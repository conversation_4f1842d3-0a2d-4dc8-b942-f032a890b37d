<template>
    <div class="step-wrapper">
        <div class="step-main">
            <el-form ref="formData" :model="formData" label-position="right" label-width="100px">
                <div>
                    <el-row>
                        <el-form-item label="公示期设置">
                            <!-- {{ detail.PublicPeriodSet | publicPeriodSetFilter }} -->
                            <span v-if="detail.PublicPeriodSet == 2">
                                {{ detail.AutoEndType | autoEndTypeFilter }}（{{ detail.AutoEndTime | dateFilter('YYYY-MM-DD') }}）自动结束考核
                            </span>
                        </el-form-item>
                        <!-- <el-col :span="24">
                            <el-form-item label="整体评价" prop="OverallEvaluation">
                                {{ formData.OverallEvaluation }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="相关附件" prop="OverallEvaluationAttachmentList">
                                <template v-if="formData.OverallEvaluationAttachmentList.length == 0">无</template>
                                <template v-else>
                                    <app-uploader
                                        :readonly="true"
                                        accept='all'
                                        :fileType='3'
                                        :max='10000'
                                        :value='formData.OverallEvaluationAttachmentList'
                                        :fileSize='1024 * 1024 * 500'
                                        :minFileSize='100 * 1024'
                                    ></app-uploader>
                                </template>
                            </el-form-item>
                        </el-col> -->
                    </el-row>

                    <el-table fit class="orderList" :data="formData.PersonalList" style="width: 100%" v-loading='listLoading'>
                        <el-table-column label="考核对象" width="auto">
                            <template slot-scope="scope">
                                <el-form-item>
                                    <span v-if="scope.row.SelfRatingEmployee">
                                        {{ scope.row.SelfRatingEmployee.Name }}
                                    </span>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="自评" width="auto">
                            <template slot-scope="scope">
                                <el-form-item>
                                    <span v-if="scope.row.SelfRatingEvaluate">
                                        {{ scope.row.SelfRatingEvaluate }}
                                    </span>
                                    <span v-else>
                                        无
                                    </span>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="集体评议" width="auto">
                            <template slot-scope="scope">
                                <el-form-item>
                                    <!-- 终审结果不等于默认值，就显示终审结果 -->
                                    <span v-if="scope.row.FinalEvaluate != 10">
                                        {{ scope.row.FinalEvaluate | finalStatusFilter }}
                                    </span>
                                    <span v-else>
                                        {{ scope.row.TeamEvaluate }}
                                    </span>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="评定说明">
                            <template slot-scope="scope">
                                <el-form-item>
                                    <el-input maxlength="100" v-model="scope.row.TeamExplain" clearable></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column> -->
                        <el-table-column label="操作" width="70px">
                            <template slot-scope="scope">
                                <app-table-row-button v-if="scope.row.SelfRatingEvaluate" @click="handleShowDetail(scope.row)" :type="2" text="自评详情"></app-table-row-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-form>
        </div>
        <!-- <div class="btn-wrapper" v-if="editable">
            <el-button type="primary" style="width: 180px;" :loading="loading" :disabled='loading' @click="handleSave">结束本次考核</el-button>
        </div> -->
    </div>
</template>


<script>
import indexPageMixin from "@/mixins/indexPage";
import empSelector from '@/views/common/empSelector'
// import { appraisePromiseStatusEnum } from "../enum"
import * as ach from "@/api/personnelManagement/achievementMgmt"
import * as myAch from "@/api/myAchievements"
import { publicPeriodSetEnum, autoEndTypeEnum } from "../enum"
import { finalStatus } from "../enum";

export default {
    name: "step5",
    mixins: [indexPageMixin],
    components: {
        empSelector,
    },
    props: {
        //跟进 才 可编辑；详细不可以
        isFollow: {
            type: Boolean,
            default: false
        },
        detail: {
            type: Object,
            required: true
        }
    },
    watch: {
        detail: {
            handler(val) {
                this.initDatas()
                this.getList()
            },
            detail: true,
            immediate: true
        }
    },
    computed: {
        editable() {
            return this.isFollow && this.detail.ProgressStatus == 5 && this.detail.PublicPeriodSet != 2
        },
    },
    created() {
        
    },
    mounted() {
    },
    filters: {
        finalStatusFilter(val) {
            let obj = finalStatus.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        // appraisePromiseStatusFilter(val) {
        //     let obj = appraisePromiseStatusEnum.find(s => s.value == val)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return ''
        // },
        publicPeriodSetFilter(val) {
            let obj = publicPeriodSetEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        autoEndTypeFilter(val) {
            let obj = autoEndTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    data() {
        return {
            loading: false,
            listLoading: false,
            tabDatas: [],
            formData: {
                Id: '',
                OverallEvaluation: '',
                OverallEvaluationAttachmentList: [],
                PersonalList: [],
            },
        };
    },
    methods: {
        handleSave() {
            this.$confirm(`结束考核后考核对象将无法进行申诉确定结束本次考核吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true
                ach.completeAndEnd([this.detail.Id]).then(res => {
                    this.loading = false
                    this.$emit('forwardSuccess')
                }).catch(err => {
                    this.loading = false
                })
            })
        },
        getList() {
            let postData = {
                PageSize: 10000,
                PageIndex: 1,
                AppraisePlanId: this.detail.Id,
                LookStatus: 1, //只查询继续考核的员工（终止考核不显示）
            }
            this.listLoading = true
            myAch.getList(postData).then(res => {
                this.listLoading = false
                this.formData.PersonalList = res.Items.map(s => {
                    return {
                        Id: s.Id,
                        SelfRatingEmployee: s.SelfRatingEmployee,
                        SelfRatingEvaluate: s.SelfRatingEvaluate,
                        TeamEvaluate: s.TeamEvaluate,
                        TeamExplain: s.TeamExplain,
                        FinalEvaluate: s.FinalEvaluate,
                    }
                }) || []
            }).catch(err => {
                this.listLoading = false
            })
        },
        initDatas() {
            let data = JSON.parse(JSON.stringify(this.detail))
            this.formData.Id = data.Id
            this.formData.OverallEvaluation = data.OverallEvaluation
            this.formData.OverallEvaluationAttachmentList = data.OverallEvaluationAttachmentList
        },
        handleShowDetail(row) {
            //1 表示中期；2：表示已自评
            this.$emit('showDetail', {Id: row.Id, Type: 2})
        },
    },
};
</script>

<style scoped>
.orderList >>> .el-form-item__content {
    margin-left: 0 !important;
}

.orderList >>> .el-form-item{
    margin-bottom: 0 !important;
}
</style>

<style lang="scss" scoped>
@import "./step.css";
</style>