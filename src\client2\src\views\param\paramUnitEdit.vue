<!--参数单位-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="600"
    >
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="120px"
        >
          <el-form-item label="单位名称" prop="Name">
            <el-input maxlength="25" v-model="formModel.Name"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="Remark">
            <el-input type="textarea" :rows="4" maxlength="1000" v-model="formModel.Remark"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <el-checkbox v-show="dialogStatus=='create'" v-model="isContinue">连续添加</el-checkbox>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
//按照以下顺序
//组件 import empSelector from "../../../../common/empSelector";
import * as param from "@/api/param";
//Api import * as demand from "@/api/projectDev/projectMgmt/demand";
//方法、属性 import { empSelector } from "../../../../common/empSelector";

export default {
  /**名称 */
  name: "param-unit-edit",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    /**弹窗类型 */
    dialogStatus: {
      type: String,
      default: "create"
    },
    /**主键Id */
    id: {
      type: String
    }
  },
  /**数据区 */
  data() {
    return {
      /**连续添加 */
      isContinue: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单模型 */
      formModel: { Name: "", Remark: "" },
      /**表单规则 */
      formRules: {
        Name: { fieldName: "单位名称", rules: [{ required: true }] }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    pageTitle() {
      let title = "";
      switch (this.dialogStatus) {
        case "create":
          title = "添加单位";
          break;
        case "update":
          title = "修改单位";
          break;
        case "detail":
          title = "单位详情";
          break;
        default:
          title = "";
          break;
      }
      return title;
    }
  },
  /**监听 */
  watch: {
    // //不等于详情页面可编辑
    // editable() {
    //   return this.dialogStatus != "detail";
    // },
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.isContinue = false;
          if (_this.dialogStatus != "create" && _this.id) {
            param.getParamUnitDetails({ Id: _this.id }).then(response => {
              _this.formModel = Object.assign({}, _this.formModel, response);
            });
          } else {
            _this.resetData();
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    /**重置 */
    resetData() {
      let _this = this;
      _this.formModel = { Name: "", Remark: "" };
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;
          if (_this.dialogStatus == "create") {
            result = param.addParamUnit(_this.formModel);
          } else if (_this.dialogStatus == "update") {
            result = param.editParamUnit(_this.formModel);
          }

          result
            .then(response => {
              _this.buttonLoading = false;
              _this.$notify({
                title: "成功",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              _this.$refs.appDialogRef.createData();
              if (_this.isContinue) {
                _this.resetData();
              } else {
                _this.handleClose();
              }
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


