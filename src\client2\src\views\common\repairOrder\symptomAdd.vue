<template>
    <div>
        <el-button type="text" @click="openInputDialog" :disabled='disabled'>新增</el-button>
        <el-dialog
                :title="optTitle"
                :visible.sync="dialogVisible"
                width="500px"
                :before-close="closeInputDialog"
                :close-on-click-modal='false'
                :append-to-body='true'
            >
            <div style="text-align: right;">
                <app-table-row-button v-show="inputMode == 1" @click="toggleInputMode(2)" :type='2' :text="`常见${this.opts[this.optType]}，从案例库中选择`"></app-table-row-button>
                <app-table-row-button v-show="inputMode == 2" @click="toggleInputMode(1)" :type='2' text="非常见，手动输入！"></app-table-row-button>
            </div>
            <el-form
                :rules="rules"
                :model="temp"
                ref="dataForm"
                label-position="right"
            >
                <el-form-item prop="desc">
                    <el-input type="textarea" :rows="3" placeholder="" v-model.trim="temp.desc" maxlength="500"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button size="mini" @click="closeInputDialog">取消</el-button>
                <el-button size="mini" type="primary" @click="handleSave" :loading="postLoading">确认</el-button>
            </div>
        </el-dialog>

        <el-dialog width="800px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="`选择${this.opts[this.optType]}`" :visible.sync='dialogAccessUsers' :append-to-body='true'>
             <!-- :condition="{'failureCaseId': pid}" -->
            <symptom-selector :optType='optType' :multiple='optType != 1' ref="accessUser" v-bind="$attrs" :existsUsers='checked' :visible.sync='dialogAccessUsers' v-show="dialogAccessUsers" @changed='handleChangeUsers'></symptom-selector>
        </el-dialog>
    </div>
</template>

<script>

import SymptomSelector from './symptomSelector'
import elDragDialog from '@/directive/el-dragDialog'
export default {
    name: 'symptom-add',
    components: {
        SymptomSelector,
    },
    directives: {
      elDragDialog
    },
    props: {
        //整条记录主要（故障现象输入方式）输入方式；如果为输入（2），那么原因分析、解决方法都只能输入；否则可输可选
        // inputMode: 1：输入；2：选择
        mainInputMode: {
            type: Object,
            default: null
        },
        optType: {
            type: Number, //1: 故障现象；2：原因分析；3：解决方法
            default: 1,
        },
        disabled: {
            type: Boolean,
            default: false
        },
        //已存在的人员
        checkedList: {
            type: Array,
            default: () => {
                return []
            },
        },
        pid: {
            type: String,
            default: ''
        },
    },
    computed: {
        optTitle() {
            if(this.optType == 1 || this.optType == 2 || this.optType == 3){
                return `新增${this.opts[this.optType]}`
            }else {
                return ''
            }
        }
    },
    watch: {
        dialogVisible(val) {
            if(!val) {
                this.temp.desc = ''
                this.inputMode = 1
                this.$refs['dataForm'].clearValidate()
            }
        },
        //如果选择框关闭
        dialogAccessUsers(val) {
            if(!val) {
                this.toggleInputMode(1)
            }
        },
        inputMode(val) {
            if(val == 2){
                this.$refs['dataForm'].clearValidate()
            }
        },
        checkedList: {
            handler(val) {
                this.checked = JSON.parse(JSON.stringify(val))
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules)
        const validateText = (rule, value, callback) => {
            if (value.length <= 0) {
                let equText = this.opts[this.optType]
                callback(new Error(`${equText}不能为空`))
            } else {
                callback()
            }
        }
        if(!this.rules['desc'])
            this.rules['desc'] = []
        this.rules.desc.push({validator: validateText, trigger: 'blur'})
    },
    data() {
        return {
            opts: {
                1: '故障现象',
                2: '原因分析',
                3: '解决方法'
            },
            dialogVisible: false,
            dialogAccessUsers: false,
            inputMode: 1, //1: 输入；2：选择
            temp: {
                desc: '',//输入内容
            },
            rules: {},
            postLoading: false,
            checked: [],
        }
    },
    methods: {
        closeInputDialog() {
            this.dialogVisible = false
        },
        openInputDialog() {
            this.dialogVisible = true
        },
        handleChangeUsers(datas) {
            // let users = datas.map(u => {
            //     return {
            //         Id: u.UserId,
            //         Text: u.Name
            //     }
            // })

            this.dialogAccessUsers = false
            this.closeInputDialog()
            this.$emit('success', {
                inputMode: 2, //1: 输入；2：选择
                desc: datas
            })
        },
        handleSave() {
            let self = this;
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {

                    // let ifExists = this.checked.find(o => !o.Id && o.Text == this.temp.desc)
                    let ifExists = this.checked.find(o => o.Text == this.temp.desc)
                    if(ifExists){
                        this.$message({
                            message: `故障现象"${this.mainInputMode.Text}"所保存的"${this.opts[this.optType]}"已存在，请修改后再保存！`,
                            type: 'error'
                        })
                        return false
                    }

                    let formData = JSON.parse(JSON.stringify(self.temp))
                    this.closeInputDialog()
                    
                    this.$emit('success', {
                        inputMode: this.inputMode,
                        desc: this.temp.desc
                    })
                    // this.postLoading = true
                    // equ.add(formData).then(r => {
                    //     self.$notify({
                    //         title: '成功',
                    //         message: `${this.opts[this.optType]}保存成功`,
                    //         type: 'success',
                    //         duration: 2000
                    //     })
                    //     this.postLoading = false
                    //     self.$emit('changed')
                    // }).catch(err => {
                    //     this.postLoading = false
                    // })

                }
            })
        },
        toggleInputMode(mode) {
            this.inputMode = mode
            if(this.inputMode == 2){
                this.dialogAccessUsers = true
            }
        },

    }
}
</script>