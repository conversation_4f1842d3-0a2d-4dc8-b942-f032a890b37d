<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='800'>
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <div class="wrapper" v-loading='loading'>
            <el-row>
              <el-col :span="24">
                <el-form-item label="轮播图片封面" prop="AttachmentList">
                  <app-upload-file :max='1' :limit="1" :fileSize='1024 * 1024 * 2' :multiple="true" :value='formData.AttachmentList' :readonly="!editable" @change='handleFileListChange' :preview='true'></app-upload-file>
                  <div>建议尺寸：686px * 262px，大小不超过 2MB</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="标题名称" prop="Title">
                  <el-input :disabled="!editable" type="text" maxlength="50" v-model="formData.Title"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="跳转url" prop="RedirectUrl">
                  <el-input :disabled="!editable" type="text" maxlength="50" v-model="formData.RedirectUrl"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="备注" prop="Describe">
                  <el-input maxlength="2000" :disabled="!editable" type="textarea" :rows="8"
                    v-model="formData.Describe"></el-input>
                </el-form-item>
              </el-col>

            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <span class="fl m-r-50" v-if="dialogStatus == 'create'">
          <el-checkbox v-model="isContinue">继续添加</el-checkbox>
        </span>
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
  import * as banner from "@/api/maintenanceCenter/banner";
  export default {
    name: "banners-mgmt-create",
    directives: {},
    components: {
      // tabs,
      // tags,
    },
    mixins: [],
    props: {
      dialogStatus: {
        //create、update、detail
        type: String
      },
      id: {
        type: String,
        default: ""
      }
    },
    watch: {
      "$attrs.dialogFormVisible"(val) {
        if (!val) {
          this.isContinue = false;
        }
        if (val) {
          // this.getProjectNames();
          this.resetFormData();
          if (this.dialogStatus != "create" && this.id) {
            this.getDetail();
          }
        }
      }
    },
    computed: {
      //不等于详情页面可编辑
      editable() {
        return this.dialogStatus != "detail";
      },
      pageTitle() {
        if (this.dialogStatus == "create") {
          return "添加轮播图片";
        } else if (this.dialogStatus == "update") {
          return "编辑轮播图片";
        } else if (this.dialogStatus == "detail") {
          return "轮播图片详情";
        }
      },

    },
    created() {

      this.rules = this.initRules(this.rules);

    },
    data() {
      return {

        loading: false,
        disabledBtn: false,
        isContinue: false,
        rules: {
          AttachmentList: {
            fieldName: "轮播图片封面",
            rules: [{ required: true, trigger: 'change' }]
          },
        },
        labelWidth: "110px",
        formData: {
          Id: "", //需求ID
          AttachmentList: [],
          Title: "",
          RedirectUrl: "", //需求描述
          Describe: "", //备注
        }
      };
    },
    methods: {
      handleFileListChange(imgs) {
          if (imgs && imgs.length > 0) {
              this.formData.AttachmentList = imgs.map(s => {
                return {
                  Id: s.Id,
                  Path: s.Path
                }
              })
          } else {
              this.formData.AttachmentList = []
          }
      },
      resetFormData() {
        let temp = {
          Id: "", //需求ID
          AttachmentList: [],
          Title: "",
          RedirectUrl: "", //
          Describe: "", //备注
        };
        this.formData = Object.assign({}, this.formData, temp);
      },
      createData() {
        let validate = this.$refs.formData.validate();
        Promise.all([validate]).then(valid => {
          let postData = JSON.parse(JSON.stringify(this.formData));
          //提交数据保存

          postData = Object.assign({}, this.formData);
          if (this.dialogStatus == "create") {
            delete postData.Id;
          }

          if(postData.AttachmentList && postData.AttachmentList.length > 0) {
            postData.ImageId = postData.AttachmentList[0].Id
          }

          delete postData.AttachmentList

          this.disabledBtn = true
          let result = null;
          if (this.dialogStatus == "create") {
            delete postData.Id;
            result = banner.add(postData);
          } else if (this.dialogStatus == "update") {
            result = banner.edit(postData);
          }

          result.then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.disabledBtn = false
            if (this.isContinue) {
              this.resetFormData();
              this.$emit("reload");

            } else {
              this.$refs.appDialogRef.createData();
            }
          }).catch(err => {
            this.disabledBtn = false
          });
        });
      },
      getDetail() {
        this.loading = true
        banner.detail({ id: this.id }).then(res => {
          this.loading = false
          this.formData = Object.assign({}, this.formData, res);
        }).catch(err => {
          this.loading = false
        });
      },
      handleFilesUpChange(files) {
      },
      handleClose() {
        this.$refs.appDialogRef.handleClose();
      }
    }
  };
</script>

<style lang="scss" scoped>


</style>
