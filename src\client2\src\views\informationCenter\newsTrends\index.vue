<template>
  <div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
      <!-- <page-title
        title="新闻动态"
        :subTitle="['新闻动态的发布及管理页面']"
      ></page-title> -->

      <div class="__dynamicTabWrapper">
        <app-table
          ref="mainTable"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :tab-auth-columns="tabAuthColumns"
          :isShowAllColumn="true"
          :loading="listLoading" :isShowBtnsArea='false'
          @rowSelectionChanged="rowSelectionChanged"
          :isShowOpatColumn="true"
          :startOfTable="startOfTable"
          @sortChagned='handleSortChange'
          :layoutMode='layoutMode'
        >
          <template slot="NewsTitle" slot-scope="scope">
            <span v-if="!scope.row.IsDraft" class="span">
              <i class="tip" v-if="!scope.row.IsRead" title="未读"></i>
              <!-- <i class="el-icon-s-opportunity" v-else title="已读" style="color: green;"></i> -->
            </span>
            <el-tag v-if="scope.row.IsChoiceness">精选</el-tag>

            <!-- <el-tag v-if="scope.row.IsRead"> -->
            <!-- <img src="../../../assets/images/du.gif" width="15" height="15"  title="已读" v-if="scope.row.IsRead"/>
                <img src="../../../assets/images/weidu.gif" width="15" height="15"  title="未读" v-if="!scope.row.IsRead"/> -->
            <!-- </el-tag> -->

            {{ scope.row.NewsTitle }}
          </template>
          <template slot="IsShow" slot-scope="scope">
            <span class="item-status" :class="`status-${scope.row.IsShow}`">
              {{ scope.row.IsShow | isShowFilter }}
            </span>
          </template>
          <template slot="IsDraft" slot-scope="scope">{{
            scope.row.IsDraft | isDraftFilter
          }}</template>
          <template slot="NewsType" slot-scope="scope">{{
            scope.row.NewsType | newsTypeFilter
          }}</template>
          <template slot="CreateEmployee" slot-scope="scope">{{
            scope.row.CreateEmployee | nameFilter
          }}</template>
           <template slot="Author" slot-scope="scope">{{
            scope.row.Author
          }}</template>
          <template slot="CreateTime" slot-scope="scope">{{
            scope.row.CreateTime | dateFilter("YYYY-MM-DD HH:mm")
          }}</template>

          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form
                :layoutMode='layoutMode'
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="onResetSearch"
            >
              <template slot="NewsTitle">
                <el-input style="width: 100%;" 
                    placeholder="搜索新闻标题..."
                    @clear='handleFilter'
                    v-antiShake='{
                        time: 300,
                        callback: () => {
                            handleFilter()
                        }
                    }' 
                    clearable 
                    v-model="listQuery.NewsTitle"
                ></el-input>
            </template>

              <!-- <template slot="StartDateTime">
                <el-date-picker format="yyyy-MM-dd" style="width: 100%;" value-format="yyyy-MM-dd" v-model="listQuery.StartDateTime" type="date" placeholder></el-date-picker>
              </template>
              <template slot="EndDateTime">
                <el-date-picker format="yyyy-MM-dd" style="width: 100%;" value-format="yyyy-MM-dd" v-model="listQuery.EndDateTime" type="date" placeholder></el-date-picker>
              </template> -->

              <template slot="Range">
                <el-date-picker
                  v-model="listQuery.Range"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder
                  end-placeholder
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%;"
                ></el-date-picker>
              </template>

              <template slot="SelectNewsType">
                <el-select
                  class="sel-ipt"
                  style="width: 100%;"
                  clearable
                  v-model="listQuery.SelectNewsType"
                  placeholder="请选择新闻类型"
                >
                  <el-option
                    v-for="item in newsTypeEnum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                    <permission-btn
                    moduleName="position"
                    v-on:btn-event="onBtnClicked"
                    ></permission-btn>
                </template>
            </app-table-form>
          </template>

          <template slot="tableTopAres">
            <div style="padding: 4px 4px 4px 8px;">
              <tags :items="newsTrendsGroupTypes" v-model="listQuery.IsDraft">
                <template v-for="t in newsTrendsGroupTypes" :slot="t.value">
                  {{ t.label }}
                </template>
              </tags>
            </div>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button
              v-if="rowBtnIsExists('btnSetIsShow')"
              @click="handleSetIsShow(scope.row)"
              :text="scope.row.IsShow ? '设为无效' : '设为有效'"
            ></app-table-row-button>

            <app-table-row-button
              v-if="rowBtnIsExists('btnEdit')"
              @click="handleUpdate(scope.row)"
              :type="1"
            >
            </app-table-row-button>
            <app-table-row-button @click="handleReview(scope.row)" :type="2">
            </app-table-row-button>
            <app-table-row-button
              v-if="rowBtnIsExists('btnDel')"
              v-show="!scope.row.IsShow"
              @click="handleDelete(scope.row)"
              :type="3"
            ></app-table-row-button>
          </template>
        </app-table>
      </div>

      <pagination
        :total="total"
        :page.sync="listQuery.PageIndex"
        :size.sync="listQuery.PageSize"
        @pagination="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <create-page
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
      @reload="getList"
    ></create-page>
  </div>
</template>

<script>
import * as newsTrends from "@/api/informationCenter/newsTrends";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import { newsTypeEnum } from "../enums";
import createPage from "./create";

export default {
  name: "newsTrends",
  components: {
    createPage
  },
  directives: {
    elDragDialog
  },
  mixins: [indexPageMixin],
  created() {
    this.getList();
  },
  watch: {
    "listQuery.IsDraft"() {
      this.getList();
    }
  },
  filters: {
     authorFilter(author){
       if (author) {
        return author[0].Name;
      }
      return "";
    },
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    isShowFilter(isShow) {
      if (isShow) {
        return "有效";
      }
      return "无效";
    },
    isDraftFilter(isDraft) {
      if (isDraft) {
        return "是";
      }
      return "否";
    },
    newsTypeFilter(status) {
      const statusObj = newsTypeEnum.find(s => s.value == status);
      if (statusObj) {
        return statusObj.label;
      }
      return "";
    }
  },
  mounted() {},
  data() {
    return {
            layoutMode: 'simple',
      newsTrendsGroupTypes: [
        { value: 0, label: "新闻列表" },
        { value: 1, label: "草稿箱" }
      ],

      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,

      newsTypeEnum: newsTypeEnum,

      multipleSelection: [],
      tableSearchItems: [
        { prop: "NewsTitle", label: "新闻标题", mainCondition: true },
        { prop: "Range", label: "发布时间" },
        { prop: "SelectNewsType", label: "新闻类型" }
      ],
      tabColumns: [
        {
          attr: { prop: "NewsTitle", label: "标题", showOverflowTooltip: true },
          slot: true
        },
        {
          attr: { prop: "IsShow", label: "状态", sortable: 'custom' },
          slot: true
        },
        {
          attr: { prop: "NewsType", label: "类型" },
          slot: true
        },
        {
          attr: { prop: "CreateEmployee", label: "发布人" },
          slot: true
        },
         {
          attr: { prop: "Author", label: "作者"},
          slot: true
        },
        {
          attr: { prop: "CreateTime", label: "发布时间", sortable: 'custom' },
          slot: true
        }
      ],
      tabDatas: [],
      listLoading: false,
      listQuery: {
        // 查询条件
        NewsTitle: "",
        Range: [],
        StartDateTime: null,
        EndDateTime: null,
        NewsType: 0,
        SelectNewsType: null,
        IsDraft: 0,
        WebOrApp: "Web"
      },
      total: 0
    };
  },

  methods: {
    onResetSearch() {
      this.listQuery.Range = [];
      this.listQuery.NewsTitle = "";
      this.listQuery.StartDateTime = null;
      this.listQuery.EndDateTime = null;
      this.listQuery.NewsType = 0;
      this.listQuery.SelectNewsType = null;

      this.getList(); //刷新列表
    },

    //查看详情
    handleReview(row) {
      this.$router.push(`/informationCenter/newsTrends/detail/${row.Id}?newsType=${row.NewsType}`);
    },

    onBtnClicked: function(domId) {
      switch (domId) {
        //我要发布
        case "btnAdd":
          this.handleDialog("create");
          break;
        //批量删除
        case "btnBatchDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error"
            });
            return;
          }
          this.handleDelete(this.multipleSelection);
          break;
        //设为精选
        case "btnChoiceness":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少选中一个",
              type: "error"
            });
            return;
          }
          this.setChoiceness(this.multipleSelection);
          break;
        //取消精选
        case "btnCancelChoiceness":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少选中一个",
              type: "error"
            });
            return;
          }
          this.cancelChoiceness(this.multipleSelection);
          break;
        //置顶
        case "btnStick":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少选中一个",
              type: "error"
            });
            return;
          }
          this.setStick(this.multipleSelection);
          break;
        default:
          break;
      }
    },

    // 设置 精选
    setChoiceness(rows, state) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      newsTrends.setChoiceness(ids).then(() => {
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000
        });
        this.getList();
      });
    },

    // 取消 精选
    cancelChoiceness(rows, state) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      newsTrends.cancelChoiceness(ids).then(() => {
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000
        });
        this.getList();
      });
    },

    //设置置顶
    setStick(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }
      newsTrends.setStick(ids).then(() => {
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000
        });
        this.getList();
      });
    },

    // 弹出编辑框
    handleUpdate(row, optType = "update") {
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.getList();
      this.closeDialog();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.getList()
    },    
    getList() {
      this.listLoading = true;

      let selectNewsType = this.listQuery.SelectNewsType;

      this.listQuery.NewsType =
        selectNewsType === null || selectNewsType === "" ? 0 : selectNewsType;

      if (this.listQuery.Range && this.listQuery.Range.length == 2) {
        this.listQuery.StartDateTime = this.listQuery.Range[0];
        this.listQuery.EndDateTime = this.listQuery.Range[1];
      }
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      newsTrends.getList(postData).then(response => {
        this.tabDatas = response.Items;
        this.total = response.Total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    //设置有效/无效
    handleSetIsShow(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      var isShow = rows.IsShow;
      var message = isShow ? "设置为无效" : "设置为有效";
      this.$confirm("是否确认" + message + "?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        newsTrends.setIsShow(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "设置成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    // 多行删除
    handleDelete(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        newsTrends.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}
.span {
  position: relative;
  padding: 5px;
}
.tip {
  display: block;
  background: #f00;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  top: 8px;
  position: absolute;
}

/* 有效 */
.status-true {
  background-color: green;
}

/* 无效 */
.status-false {
  background-color: red;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
