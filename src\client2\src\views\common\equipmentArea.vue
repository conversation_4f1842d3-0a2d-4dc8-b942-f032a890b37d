<template>
  <el-form
      label-width="10px"
      label-position="right"
      v-loading='loading'
  >
    <div>
      <!-- 设备行 -->
      <el-collapse v-model="firstActiveName" accordion>
        <el-collapse-item
          v-for="(EquipmentCategoryType, EquipmentCategoryTypeIndex) in datas"
          :name="EquipmentCategoryType.EquipmentCategoryTypeName"
          :key="EquipmentCategoryTypeIndex"
          :label="EquipmentCategoryType.EquipmentCategoryTypeName"
        >
          <template slot="title">
            <span
              style="font-size:16px;font-waight:700;"
            >{{EquipmentCategoryType.EquipmentCategoryTypeName}}</span>
          </template>

          <el-collapse v-model="secondActiveNames" accordion>
            <el-collapse-item
              v-for="EquipmentType in EquipmentCategoryType.GroupDetails"
              :key="EquipmentType.EquipmentTypeIdKey"
              :name="EquipmentType.EquipmentTypeIdKey"
              :label="EquipmentType.Title"
              class="equipment-area-title"
              style="margin-left:20px;"
            >
              <template slot="title">
                <span style="font-size:14px;">{{EquipmentType.Title}}</span>
              </template>
              <!-- 属性标题 -->
              <el-form-item :prop="EquipmentType.Title">
                <!-- 属性值选择 -->
                <template label-width="10px" v-if="EquipmentType.ControlType!=2">
                  <el-checkbox-group
                    @change="handleChanged"
                    v-model="EquipmentType[EquipmentType.EquipmentTypeIdKey]"
                    :disabled="uneditable"
                  >
                    <el-checkbox
                      v-for="item in EquipmentType.Details"
                      :key="item.EquipmentTypeSubtypeRelationId"
                      :label="item.EquipmentTypeSubtypeRelationId"
                    >{{item.EquipmentSubtypeName}}</el-checkbox>
                  </el-checkbox-group>
                </template>
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-form>
</template>

<script>
//  <el-row
//     class="group-wrapper"
//     v-for="(EquipmentCategoryType, EquipmentCategoryTypeIndex) in datas"
//     :key="EquipmentCategoryTypeIndex"
//     :label="EquipmentCategoryType.EquipmentCategoryTypeName"
//     :name="EquipmentCategoryType.EquipmentCategoryTypeName"
//   >
//     <div class="group-title">
//       <!-- 设备标题 -->
//       <h3>{{ EquipmentCategoryType.EquipmentCategoryTypeName }}</h3>
//     </div>
//     <!-- 属性行 -->
//     <el-col
//       :span="24"
//       v-for="(EquipmentType, EquipmentTypeIndex) in EquipmentCategoryType.GroupDetails"
//       :key="EquipmentTypeIndex"
//       :title="EquipmentType.Title"
//       class="equipment-area-title"
//     >
//       <!-- 属性标题 -->
//       <el-form-item :label="EquipmentType.Title" :prop="EquipmentType.Title">
//         <!-- 属性值选择 -->
//         <template v-if="EquipmentType.ControlType!=2">
//           <el-checkbox-group
//             @change="handleChanged"
//             v-model="EquipmentType[EquipmentType.EquipmentTypeIdKey]"
//             :disabled="editable"
//           >
//             <el-checkbox
//               v-for="item in EquipmentType.Details"
//               :key="item.EquipmentTypeSubtypeRelationId"
//               :label="item.EquipmentTypeSubtypeRelationId"
//             >{{item.EquipmentSubtypeName}}</el-checkbox>
//           </el-checkbox-group>
//         </template>
//       </el-form-item>
//     </el-col>
//   </el-row>

import * as equipments from "@/api/equipments";
export default {
  name: "equipment-area",
  props: {
    //选中（设置）的值
    checked: {
      type: Array,
      default: null
    },
    //是否不可编辑
    uneditable: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.getEquList();
  },
  data() {
    return {
      /**动态属性列表
       * 设备-属性-属性值
       */
      firstActiveName: "",
      secondActiveNames: "",
      datas: [],
      loading:false,
    };
  },
  methods: {
    /** */
    handleChanged() {
      this.$emit("changed", this.getCheckEquList());
    },
    /**获取动态属性列表 */
    getEquList() {
      this.loading=true;
      equipments.getList().then(res => {
        this.loading=false;
        console.log(this.checked);
        this.initEquListModel(res);
        this.datas = res;
        this.setCheckedAttr();
      }).catch(err => {
        this.loading=false;
      });
    },
    /**初始化动态表单型号 */
    initEquListModel(details) {
      if (details && details.length > 0) {
        //设备
        details.forEach(EquipmentCategoryType => {
          if (
            EquipmentCategoryType.GroupDetails &&
            EquipmentCategoryType.GroupDetails.length > 0
          ) {
            //属性
            EquipmentCategoryType.GroupDetails.forEach(EquipmentType => {
              //如果为下拉选择控件
              let itemKey = this.getModelName(EquipmentType.EquipmentTypeId);
              //主键
              EquipmentType["EquipmentTypeIdKey"] = itemKey;
              //对应选中值
              EquipmentType[itemKey] = [];
            });
          }
        });
      }
    },
    /**设置用户已选中（设置）的值 */
    setCheckedAttr() {
      if (this.datas && this.datas.length > 0) {
        this.datas.forEach(EquipmentCategoryType => {
          if (
            EquipmentCategoryType.GroupDetails &&
            EquipmentCategoryType.GroupDetails.length > 0
          ) {
            EquipmentCategoryType.GroupDetails.forEach(EquipmentType => {
              let itemKey = this.getModelName(EquipmentType.EquipmentTypeId);
              if (EquipmentType.ControlType != 2) {
                //如果是下拉
                EquipmentType.Details.forEach(a => {
                  //设置动态绑定key
                  EquipmentType.EquipmentTypeIdKey == itemKey;
                  //为动态绑定key赋值
                  if (this.checked && this.checked.length > 0) {
                    let ifExists = this.checked.findIndex(
                      b => a.EquipmentTypeSubtypeRelationId == b
                    );
                    if (ifExists > -1) {
                      EquipmentType[itemKey].push(
                        a.EquipmentTypeSubtypeRelationId
                      );
                    }
                  }
                });
              }
            });
          }
        });
      }
    },
    /**获取用户选择的“属性值”列表 */
    getCheckEquList() {
      let list = [];
      if (this.datas && this.datas.length > 0) {
        this.datas.forEach(EquipmentCategoryType => {
          if (
            EquipmentCategoryType.GroupDetails &&
            EquipmentCategoryType.GroupDetails.length > 0
          ) {
            EquipmentCategoryType.GroupDetails.forEach(EquipmentType => {
              let itemVal = EquipmentType[EquipmentType.EquipmentTypeIdKey];
              if (EquipmentType.Details && EquipmentType.Details.length > 0) {
                if (itemVal) {
                  list = list.concat(itemVal);
                }
              }
            });
          }
        });
      }
      return list;
    },
    /**获取模块名称 */
    getModelName(equipmentTypeId) {
      if (equipmentTypeId) {
        return "a_" + equipmentTypeId.replace(/-/g, "_");
      }
    }
  }
};
</script>

<style scoped>
/* .equipment-area-title >>> .el-form-item__label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-wrapper {
  margin-bottom: 10px;
}

.group-title h3 {
  position: relative;
  padding-left: 10px;
  height: 18px;
  margin-top: 0;
}

.group-title {
  padding-left: 14px;
}

.group-title h3::before {
  content: " ";
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -3px;
  background-color: #409eff;
}

.el-select {
  width: 100%;
} */
</style>