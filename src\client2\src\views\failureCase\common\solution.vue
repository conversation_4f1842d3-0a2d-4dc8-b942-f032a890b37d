<template>
    <div class="accessories">
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <div class="temBody">
                    <el-form
                        :rules="rules"
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="110px">
                        <div>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="解决方法代码" prop="FailureSolutionCode">
                                        <el-input class="elInput" @input="handleInput" v-model.trim="formData.FailureSolutionCode" maxlength="10" placeholder="" :disabled="editable"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="解决方法" prop="Solution">
                                        <el-input class="elInput" v-model.trim="formData.Solution" maxlength="100" placeholder="" :disabled="editable"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="相关附件" prop="AttachmentList">
                                        <app-uploader
                                          :readonly='editable'
                                          accept='all'
                                          :fileType='3'
                                          :max='10000'
                                          :value='formData.AttachmentList'
                                          :fileSize='1024 * 1024 * 500'
                                          :minFileSize='100 * 1024'
                                          @change='(files) => handleFilesUpChange(files)'
                                      ></app-uploader>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </div>
            </template>
            <template slot="footer">
                <div class="fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <el-button @click="handleClose" v-show="editable" size="mini">关闭</el-button>
                <el-button @click="handleClose" v-show="!editable" size="mini">取消</el-button>
                <app-button @click="handleSuccess" v-show="!editable" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog> 
    </div>
</template>
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
export default{
    name:'accessories',
    // mixins: [indexPageMixin],
    components: {
        
    },
    props:{
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: 'create'
        },
        solutionData:{
            type: Object,
            default: null
        }
    },
    data(){
        var caseValidate = (rule, value, callback) => {
            if (this.smsMsg) {
                callback(new Error('代码编号已存在!'));
            }else{
                callback();
            }
        };
        return{
            smsMsg:null,
            saveData:[],
            saveCauseData:[],
            saveCode:'',
            disabledBtn:false,
            goOn:false,
            formData:{
                "id": "",
                "FailureCaseId": "",
                "FailureSolutionCode": "",
                "Solution": "",
                "AttachmentList": [],
            },
            rules: {
                FailureSolutionCode:[{ required: true, message: '解决方法代码不能为空'},{validator: caseValidate}],
                Solution:[{ required: true, message: '解决方法不能为空'}],
            },
        }
    },
    filters: {
        
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val){
                this.goOn=false;
                if(this.dialogStatus == 'create'){
                    this.resetData();
                }else{
                    this.formData=this.solutionData;
                }
            }
      
        },
        "formData.FailureSolutionCode"(val) {
          this.formData.FailureSolutionCode = this.formData.FailureSolutionCode.replace(
            /[\W]/g,
            ""
          );
        },
    },
    computed:{
        title(){
            if(this.dialogStatus == 'create'){
                return "添加解决方法"
            }else if(this.dialogStatus == 'edit'){
                return "编辑解决方法"
            }else{
                return "解决方法"
            }
        },
        editable() {
            //详细和审批模式都不可编辑
            return this.dialogStatus == "detail"
        },
    },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        handleInput(){
            if(this.formData.FailureSolutionCode.length>0){
                this.smsMsg=false;
            }
        },
        promptRepeat(){
            this.smsMsg=true;
            this.$refs['formData'].validateField("FailureSolutionCode");
        },
        giveData(d,d1,c){
            this.saveData=d;
            this.saveCauseData=d1;
            this.saveCode=c;
        },
        resetData(){
            this.formData={
                "id": "",
                "FailureCaseId": "",
                "FailureSolutionCode": "",
                "Solution": "",
                "AttachmentList": []
            }
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        handleSuccess(){
            this.disabledBtn=true;
            this.smsMsg=false;
            let listResult = this.$refs.formData.validate();
            Promise.all([listResult]).then(valid => {
                this.formData.AttachmentIdList = this.formData.AttachmentList && this.formData.AttachmentList.map(s => s.Id);
                if(this.dialogStatus == "create"){
                    let a=this.saveData.find(v => v.FailureSolutionCode == this.formData.FailureSolutionCode);
                    if(!a){
                        a=this.saveCauseData.find(v => v.FailureResonCode == this.formData.FailureSolutionCode);
                    }
                    if(!a){
                        if(this.formData.FailureSolutionCode == this.saveCode){
                            a=true;
                        }
                    }
                    if(a){
                        // this.$message.error('代码编号已存在!');
                        this.smsMsg=true;
                        this.$refs['formData'].validateField("FailureSolutionCode");
                    }else{
                        this.$emit('saveSuccess',JSON.parse(JSON.stringify(this.formData)),this.goOn);
                        if(this.goOn){
                            this.resetData();
                            this.$refs['formData'].resetFields();
                        }
                    }  
                }else{
                    this.$emit('editSuccess',JSON.parse(JSON.stringify(this.formData)));
                }
                this.disabledBtn=false;
                
            }).catch(err => {
                this.disabledBtn=false;
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }

}
</script>
<style lang="scss" scoped>
.temBody{
    padding:10px;
}
</style>