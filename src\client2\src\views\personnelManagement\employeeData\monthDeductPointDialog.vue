<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1000' :maxHeight="800" >
            <template slot="body">
                <div class="body-content" v-loading="formLoading">
                    <el-form ref="form" label-width="80px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="规则名称">
                                    <span>{{ formData.RuleName }}</span>
                                    <el-button type="text" style="margin-left: 5px;" @click="openRateDetailDialog()">查看评分标准</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="评价人">
                                    <template v-if="formData.AppraiserEmployee">
                                        {{ formData.AppraiserEmployee.Name }}
                                    </template>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <div class="section-title">系统扣分项</div>

                    
                    <app-table-core 
                        ref="mainTable" 
                        :isShowBtnsArea='false' 
                        :tab-columns="tabColumns1" 
                        :tab-datas="formData.RuleList" 
                        :isShowAllColumn="true" 
                        :isShowOpatColumn="false"
                        :multable="false" 
                    >
                        <template slot="RateId" slot-scope="scope">
                            <span v-if="getRateParentId(scope.row.RateId)">
                                {{ getRateParentId(scope.row.RateId) | rateTypeFilter }} / 
                            </span>
                            {{ scope.row.RateId | rateTypeFilter }}
                        </template>
                        <template slot="DeductionReasonList" slot-scope="scope">
                            <div v-for="(i, idx) in scope.row.DeductionReasonList" :key="idx">
                                {{ i.DeductionReason }}
                            </div>
                        </template>
                        <template slot="DeductionValue" slot-scope="scope">
                            {{ scope.row.DeductionValue }}
                        </template>
                    </app-table-core>
                </div>
            </template>
            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
            </template>
        </app-dialog>

        <rateDetail 
            v-if="rateDetailDialogVisible && formData.Id"
            :dialogFormVisible="rateDetailDialogVisible"
            @closeDialog="closeRateDetailDialog"
            dialogStatus="review" 
            :id="formData.Id"
        ></rateDetail>

    </div>
</template>

<script>
import rateDetail from '../employeeDataSetting/create'
import * as employeePointsRule from "@/api/personnelManagement/employeePointsRule";
import { vars } from '../common/vars'

export default {
    name: 'deductPointDialog',
    components: {
        rateDetail,
    },
    filters: {
        rateTypeFilter(val) {
            let temp = [{
                RateId: -1000,
                ParentId: null,
                Title: '周得分结果平均',
                Desc: '',
                Controls: [],
                ScoringCycle: 2,
            }]
            
            let enums = JSON.parse(JSON.stringify(vars.rateEnum2)).concat(temp)

            let obj = enums.find(s => s.RateId == val)
            if(obj) {
                return obj.Title
            }
            return ''
        },
    },
    props: {
        dialogStatus: {
            type: String
        },
        //被评价员工编号
        emp: {
            type: Object,
            required: true
        },
        year: {
            type: [Number, String],
            required: true
        },
        month: {
            type: Number,
            required: true
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(val) {
                    this.getWeekSalesPoint()
                }
            },
            immediate: true
        }
    },
    computed: {
        pageTitle() {
            return `月扣分项`
        },
    },
    data() {
        return {
            
            formLoading: false,
            rateDetailDialogVisible: false,

            tabColumns1: [
                {attr: {prop: "RateId", label: "扣分项", width: 180, align: 'center'}, slot: true},
                {attr: {prop: "DeductionReasonList", label: "扣分原因"}, slot: true},
                {attr: {prop: "DeductionValue", label: "扣分", width: 170}, slot: true},
            ],
            formData: {
                Id: '',
                Employee: null,
                RuleList: [],
                RuleName: '',
            },


        }
    },
    methods: {
        getRateParentId(rateId) {
            let obj = vars.rateEnum2.find(s => s.RateId == rateId)
            if(obj) {
                return obj.ParentId
            }
            return ''
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        getWeekSalesPoint() {
            let postDatas = {
                Year: this.year,
                Month: this.month,
                EmployeeId: this.emp.EmployeesId,
            }
            this.formLoading = true
            employeePointsRule.getMonthSalesPointDetails(postDatas).then(res => {
                this.formLoading = false
                this.formData = res
            }).catch(err => {
                this.formLoading = false
            })
        },
        openRateDetailDialog() {
            this.rateDetailDialogVisible = true
        },
        closeRateDetailDialog() {
            this.rateDetailDialogVisible = false
        },
    },
}
</script>

<style lang="scss" scoped>
.body-content{
    /deep/.el-form-item{
        margin-bottom: 0;
    }
}

.section-title{
    font-size: 12px;
    color: #606266;
    font-weight: 700;
    margin-top: 20px;
    padding: 0 6px;
}

</style>