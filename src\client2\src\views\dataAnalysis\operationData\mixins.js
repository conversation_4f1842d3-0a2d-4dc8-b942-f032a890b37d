import { result } from "lodash";

export default {
    data() {
        return {

        }
    },
    methods: {
        //判断饼图是否有数据（所有的数据全部为空，也表示没有数据）
        hasDataOfPie(series) {
            if(!series || series.length == 0 || !series[0].data || series[0].data.length == 0 || series[0].data.every(s => s.value == 0 || !s.value)) {
                return false
            }
            return true
        },
        getFontsizeByNum(num) {
            num += ''
            var counta = num.length;
            if(counta <= 4){
                return '50px';
            }else if(counta <= 6){
                return '32px';
            }else if(counta <= 8){
                return '30px';
            }else if(counta <= 10){
                return '20px';
            }else{
                return '14px';
            }

        },
    },
};
