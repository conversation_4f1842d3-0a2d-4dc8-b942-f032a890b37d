export const websitEnv = {
    //站点名称
    websiteTitle: '佳运通协同办公平台',
    mainPage: {
        ////首页
        // routerName: 'dashboard',
        // url: '/dashboard',
        // title: '首页',
        // filePath: '/dashboard/index',

        // // ////我的工作台
        // routerName: 'myWorkbench',
        // url: '/workbench/myWorkbench',
        // title: '我的工作台',
        // filePath: '/workbench/myWorkbench/index',

        // ////我的工作台
        routerName: 'myWorkbench',
        url: '/workbench/myWorkbenchNew',
        title: '我的工作台',
        filePath: '/workbench/myWorkbenchNew',

    },
    whiteList: [
        '/login',
        '/view-summary',
        '/register',
        '/dynamicFormAnalysis',
        '/adminlogin',
        '/view-summary2',
        '/betalogin',
    ],
    //admin 独有菜单，模块管理里面，编辑时不能修改“是否为菜单”、“是否为系统菜单”；角色管理里面，不能分配这部分菜单；
    adminMenus: ['/enterprise/index', '/apps/index', '/feedback/index', '/modulemanager/index']

    // //文件上传默认最大3M
    // upload_max_size_default: 3 * 1024 * 1024,

    // //分片上传切片并发数量
    // simultaneousUploads: 10,
    // //分片上传功能，设置分片大小（1 * 1024 * 1024（1M））
    // //轻易修改该值，修改此值，会导致同一个文件上传的切片数量不等，会影响“秒传”、“断点续传”的准确性
    // //如果一定要修改，请先清空服务端相关数据库表 和 已保存的切片文件
    // chunkSize: 50 * 1024,
    // //维修单
    // repairOrder: {
    //     img_min_size: 10 * 1024, //维修单图片上传最小值
    //     img_max_size: 10 * 1024 * 1024,// 维修单图片上传图片最大10M
    //     video_min_size: 100 * 1024,//维修单视频上传最小值
    //     video_max_size: 500 * 1024 * 1024,//维修单视频上传最大值
    // },
    // //公告管理
    // notice: {
    //     max_size: 30 * 1024 * 1024,//附件上传最大值30M
    // },



}