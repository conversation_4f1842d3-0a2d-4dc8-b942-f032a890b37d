import Cookies from 'js-cookie'
import dayjs from 'dayjs'
/*  token 信息  */
const TokenKey = 'jyt_token'
export function getToken() {
    return Cookies.get(TokenKey) 
}

export function setToken(token) {
    return Cookies.set(TokenKey, token)
}

export function removeToken() {
    return Cookies.remove(TokenKey)
}

/* 用户信息 */
const userInfoKey = '_userinfo'
export function setUserInfo(userinfo) {
    return Cookies.set(userInfoKey, JSON.stringify(userinfo))
}

export function removeUserInfo() {
    return Cookies.remove(userInfoKey)
}

export function getUserInfo() {
    let userinfo = Cookies.get(userInfoKey)
    if(!userinfo) return ''
    return JSON.parse(userinfo)
}

export function setCookie({key= '', value= '', expires=-1}) {
    if(key){
        return Cookies.set(key, JSON.stringify(value), {expires})
    }
}

export function getCookie(key){
    let account = Cookies.get(key)
    if(!account){
        return ''
    }
    return JSON.parse(account)
}
export function getDateTimeRange(month) {
    var myDate = new Date();
    var tYear = myDate.getFullYear();
    var startDate = dayjs(tYear+'-'+month+'-'+'20' + ' 23:59:00');
    var endDate = startDate.clone().subtract(0, 'month');
    endDate=dayjs(dayjs(endDate).year()+'-'+dayjs(endDate).month()+'-'+'20'+' 00:00:00')
    console.log(startDate,endDate)
    return [endDate,startDate];
}
// export function setUserInfo(userinfo) {
//   Cookies.set(TokenKey, JSON.stringify(userinfo))
// }

// export function getUserInfo(){
//   return JSON.parse(Cookies.get(TokenKey));
// }

/**
 * 根据页面url设置需要隐藏的列
 */
// const hideColumnsKey = 'hide_columns_key'
// export function setHideColumnsOfPage(url, columns) {
//     if (url) {
//         try {
//             let columnsOfPageObj = JSON.parse(localStorage.getItem(hideColumnsKey)) || {}
//             columnsOfPageObj[url] = columns
//             localStorage.setItem(hideColumnsKey, JSON.stringify(columnsOfPageObj))
//         }catch(e) {}
//     }
// }

// /**
//  * 根据页面url获取需要隐藏的列
//  */
// export function getHideColumnsOfPage(url) {
//     if (url) {
//         try {
//             let columnsOfPageObj = JSON.parse(localStorage.getItem(hideColumnsKey)) || {}
//             return columnsOfPageObj[url] || []
//         } catch (e) {}
//     }
//     return []
// }