<template>
  <div>
    <app-dialog
      title="指派人员"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='600'
    >
      <template slot="body" v-loading="loading">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          :label-width="'100px'"
        >
            <el-row>
              <el-col :span="24">
                    <el-form-item label="实施人员" prop="HandlerEmployee">
                        <!-- <emp-selector :multiple='true' :showType='2' :list='formData.HandlerEmployee' key='service-users' @change='handleChangeUsers'></emp-selector> -->
                        <normar-emp-selector 
                          listSelectorTitle='选择人员' 
                          :listSelectorUrl='serviceArea.business + "/ImplementerManagement/GetListPage"' 
                          :multiple='true' :showType='2' :list='formData.HandlerEmployee' 
                          key='service-users' 
                          :columns='empColumns'
                          @change='handleChangeUsers'
                          :condition='{RegionalId: rootId}'
                          :pageSize='100'
                          :isAutocomplete='true'
                          ></normar-emp-selector>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="使用车辆" prop="UseVehicle" :title="formData.UseVehicle">
                        <!-- <el-input maxlength="50" v-model="formData.UseVehicle"></el-input> -->

                          <el-select
                            style="width: 100%;"
                            v-model="formData.UseVehicle"
                            :filterable='true'
                            remote
                            placeholder=""
                            allow-create
                            :clearable='true'
                            :remote-method="getVehicle"
                            :loading="vehicleLoading"
                            @change.native='selectBlur'
                            @blur.native='selectBlur'
                            >
                            <el-option
                              v-for="item in options"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                            </el-option>
                          </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" v-show="agentShow">
                    <el-form-item label="代理授权人" prop="AgentIdsNames">
                        {{formData.AgentIdsNames}}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
// import empSelector from '../../../common/empSelector'
import * as maintenOrderMgmtApi from "@/api/maintenanceCenter/maintenOrderMgmt"
import normarEmpSelector from '../../common/normarEmpSelector'
import * as carMgt from '@/api/personnelManagement/carCoordinator'
import { serviceArea } from "@/api/serviceArea"

export default {
  name: "mainten-order-mgmt-assign",
  components: {
    // tabs,
    // tags,
    // empSelector,
    normarEmpSelector,

  },
  mixins: [],
  props: {
    row: {
        type: Object,
        required: true
    },
    rootId: {
        type: String,
        default: ''
    },
    IdList: {
      type: Array,
      default: ()=>[]
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
        this.resetFormData();
        this.agentShow=false;
    },
    row(val) {
        if(val) {
            this.formData = Object.assign({}, this.formData, val)
        }
    },
  },
  created() {
    console.log(312312312)
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      loading:false,
        agentShow:false,
        serviceArea,
        disabledBtn: false,
        rules: {
            HandlerEmployee: { fieldName: "实施人员", rules: [{ required: true, trigger: 'change' }]},
            UseVehicle: { fieldName: "使用车辆", rules: [{ required: true }]},
        },
        formData: {
            Id: this.row.Id, //
            IdList: this.IdList, //
            HandlerEmployee: [], 
            UseVehicle: '',
            AgentIds:[],
            AgentIdsNames:null,
        },
        empColumns: [
          {
          attr: { prop: "Name", label: "姓名", width: '100' },
          },
          {
          attr: { prop: "Number", label: "工号", width: '100' },
          },
          {
          attr: { prop: "Gender", label: "性别", width: '60' },
          },
          {
          attr: { prop: "RegionalName", label: "负责地区" },
          },
          {
          attr: { prop: "Phone", label: "手机", width: '120' },
          },
          {
          attr: { prop: "OrgName", label: "部门" },
          }
      ],
      options: [],
      vehicleLoading: false,
    };
  },
  methods: {
    selectBlur(e) {
      this.formData.UseVehicle = e.target.value
    },
    getVehicle(query) {
      let postDatas = {
        CarState: [],
        KeyWords: query,
        PageIndex: 1,
        PageSize: 20
      }
      let _this = this
      _this.vehicleLoading = true
      carMgt.getList(postDatas).then(response => {
        _this.vehicleLoading = false
        _this.options = response.Items.map(s => {
          return{
            value: s.CarNumber, 
            label: s.CarNumber
          }
        });
      }).catch(err => {
        _this.vehicleLoading = false
      });
    },
    handleChangeUsers(users) {
        this.formData.HandlerEmployee = users;
        this.formData.AgentIdsNames=[];
        this.formData.AgentIds=[];
        users.forEach(v => {
          if(v.AgentList && v.AgentList.length>0){
            v.AgentList.forEach(v1 => {
              this.formData.AgentIdsNames.push(v1.Name);
              this.formData.AgentIds.push(v1.EmployeeId);
            })
          }
        })
        this.formData.AgentIdsNames=this.unique(this.formData.AgentIdsNames);
        this.formData.AgentIdsNames=this.formData.AgentIdsNames.join('、');
        if(this.formData.AgentIds.length>0) this.agentShow=true;
        else this.agentShow=false;
    },
    unique(arr) {
      return Array.from(new Set(arr))
    },
    resetFormData() {
      let temp = {
        HandlerEmployee: [], 
        UseVehicle: '', 
        AgentIds:[],
        AgentIdsNames:null,
      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    createData() {
        // this.loading=true;
        let validate = this.$refs.formData.validate();
        Promise.all([validate]).then(valid => {
            let postData = JSON.parse(JSON.stringify(this.formData));
            //提交数据保存
            postData = Object.assign({}, this.formData);
            postData.HandlerEmployeeIds = postData.HandlerEmployee.map(s => s.EmployeeId)
            postData.UseVehicle = [postData.UseVehicle] //后台为数组
            console.log(postData)
            let result = null;
            if (this.IdList.length>0) {
              delete postData.Id
              result = maintenOrderMgmtApi.AssignList(postData);
            } else {
              delete postData.IdList
              result = maintenOrderMgmtApi.assign(postData);
            }
            result.then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.loading=false;
                this.$refs.appDialogRef.createData();
            }).catch(err => {
              this.loading=false;
            })
        });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
