<template>
    <div>
        <app-dialog title="服务评价详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='1000'>
            <template slot="body">
                <div class="info-wrapper" v-loading='loading'>
                    <div class="lft">
                        <el-card class="box-card">
                            <div slot="header" class="clearfix">
                                <div class="header-wrapper">
                                    <span class="header-title">报修单信息</span>
                                    <span>报修时间：{{ formData.MaintenanceDetails.CreateTime && formData.MaintenanceDetails.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="row">
                                    <div class="title">报修单号：</div>
                                    <div class="content">{{ formData.MaintenanceDetails.Code || '无' }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">报修站点：</div>
                                    <div class="content">{{ formData.MaintenanceDetails.RegionalName || '无' }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">现场人员：</div>
                                    <div class="content">{{ formData.MaintenanceDetails.HandlerEmployeeList | nameFilter }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">服务单号：</div>
                                    <div class="content">{{ formData.MaintenanceDetails.ServiceNo || '无' }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">站内签字：</div>
                                    <div class="content">{{ formData.MaintenanceDetails.StationSign || '无' }}</div>
                                </div>
                            </div>
                        </el-card>

                        <el-card class="box-card">
                            <div slot="header" class="clearfix">
                                <div class="header-wrapper">
                                    <span class="header-title">客户评价</span>
                                    <span>提交时间：{{ formData.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="row">
                                    <div class="title">报修人姓名：</div>
                                    <div class="content">{{ formData.ReportEmployee || '无' }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">值班电话：</div>
                                    <div class="content">{{ formData.ReporterNumber || '无' }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">手机号：</div>
                                    <div class="content">{{ formData.PhoneNumber }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">服务态度：</div>
                                    <div class="content">
                                        <el-rate disabled v-model="formData.AttitudeScore"></el-rate>
                                    </div>
                                </div>
                                 <div class="row">
                                    <div class="title">维修技术：</div>
                                    <div class="content">
                                        <el-rate disabled v-model="formData.SkillScore"></el-rate>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="title">时效性：</div>
                                    <div class="content">
                                        <el-rate disabled v-model="formData.TimelinessScore"></el-rate>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="title">投诉/建议：</div>
                                    <div class="content">{{ formData.Remark }}</div>
                                </div>
                                <div class="row">
                                    <div class="title">评价标签：</div>
                                    <div class="content">
                                        <div class="tag-wrapper" v-for="(tag, idx) in formData.TagList" :key="idx">
                                            <el-tag type="info">{{ tag.Name }}</el-tag>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </div>
                    <div class="rht">
                        <el-card class="box-card">
                            <div slot="header" class="clearfix">
                                <div class="header-wrapper">
                                    <span class="header-title">检修进度</span>
                                </div>
                            </div>
                            <div class="step-wrapper">
                                <div class="step-node-wrapper" v-for="(item, idx) in formData.MaintenanceScheduleList" :key="idx">
                                    <div class="step-number-wrapper">
                                        <div class="step-logo-wrapper" :class="formData.MaintenanceScheduleList[idx].Time ? 'finished' : 'not-finished'">
                                            <!-- {{ formData.MaintenanceScheduleList[idx].Time ? '完' : '未' }} -->
                                            {{ formData.MaintenanceScheduleList.length - idx }}
                                        </div>
                                        <div class="step-line" :class="{active: formData.MaintenanceScheduleList[idx + 1].Time}" v-if="formData.MaintenanceScheduleList.length - idx > 1"></div>
                                    </div>
                                    <div class="step-content">
                                        <div class="cell-wrapper">
                                            <div class="cell-title">
                                                {{ formData.MaintenanceScheduleList[idx].StepKeyCN }}
                                            </div>
                                            <div class="time">
                                                {{ formData.MaintenanceScheduleList[idx].Time }}
                                            </div>
                                        </div>
                                        <div class="cell-content-wrapper" v-for="(t, idx2) in formData.MaintenanceScheduleList[idx].Values" :key="idx2">
                                            <div class="cell-title">
                                                {{ formData.MaintenanceScheduleList[idx].Values[idx2].StepValue1 || '无' }}
                                            </div>
                                            <div v-if="formData.MaintenanceScheduleList[idx].StepKey == 'ToRepair'">
                                                <span
                                                    class="item-status"
                                                    :style="{
                                                        backgroundColor: gethandlingResultStatusObj(formData.MaintenanceScheduleList[idx].Values[idx2].StepValue2)
                                                        .color
                                                    }"
                                                    >
                                                    {{ gethandlingResultStatusObj(formData.MaintenanceScheduleList[idx].Values[idx2].StepValue2).label }}
                                                </span>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as commentMgmt from "@/api/maintenanceCenter/commentMgmt";
import { vars } from './vars'
export default {
    name: "comment-mgmt-create",
    directives: {},
    components: {},
    mixins: [],
    filters: {
        nameFilter(list) {
            if(list && list.length > 0) {
                return list.map(s => s.Name).join('、')
            }
            return '无'
        }
    },
    props: {
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val && this.id) {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
    },
    data() {
        return {
            loading: false,
            formData: {
                MaintenanceDetails: {},
                MaintenanceScheduleList: []
            }
        };
    },
    methods: {
        getDetail() {
            this.loading = true
            commentMgmt.detail({ id: this.id }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, res);
                if(this.formData.MaintenanceScheduleList) {
                    this.formData.MaintenanceScheduleList.reverse()
                }
            }).catch(err => {
                this.loading = false
            });
        },
        gethandlingResultStatusObj(val) {
            let obj = vars.handlingResultStatusEnum.find(s => s.value == val) || {}
            return obj
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>


<style lang="scss" scoped>

.info-wrapper{
    display: flex;
    padding: 10px 0;
    .lft{
        flex: 1;
        box-sizing: border-box;
        margin-right: 10px;
        .row{
            display: flex;
            .title{
                width: 85px;
                text-align: right;
                font-weight: 700;
            }
            .content{
                flex: 1;
                .tag-wrapper{
                    display: inline-block;
                    margin-right: 4px;
                }
            }
        }
        .box-card:not(:last-child), .row:not(:last-child) {
            margin-bottom: 10px;
        }
        .header-wrapper{
            padding-right: 10px;
        }
    }
    .rht{
        width: 400px;
        .step-wrapper{
            .step-node-wrapper{
                display: flex;
                .step-number-wrapper{
                    position: relative;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    .step-logo-wrapper{
                        width: 18px;
                        height: 18px;
                        // margin-top: 6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        border-radius: 50%;
                        color: #fff;
                        padding: 2px;
                        box-sizing: border-box;
                    }
                    .not-finished{
                        background: #00B050;
                    }
                    .finished{
                        background: #409EFF;
                    }
                    .step-line{
                        flex: 1;
                        border-left: 2px dashed #e3e3e3;
                        box-sizing: border-box;
                        margin: 4px auto;
                        &.active{
                            border-left: 2px solid #0371EE;
                        }
                    }
                }
                .step-content{
                    flex: 1;
                    padding: 0 10px;
                    padding-bottom: 20px;
                    .cell-wrapper{
                        display: flex;
                        .cell-title{
                            flex: 1;
                            font-size: 16px;
                            font-weight: bold;
                            margin-bottom: 10px;
                        }
                        .time{
                            font-size: 14px;
                            color: #9DA0A4;
                        }
                    }
                    .cell-content-wrapper{
                        display: flex;
                        // align-items: center;
                        .cell-title{
                            flex: 1;
                            margin-bottom: 10px;
                        }
                    }
                }

            }
        }
        .box-card{
            height: 100%;
        }
    }
}

.header-wrapper{
    display: flex;
    .header-title{
        flex: 1;
    }
}


.item-status {
    font-size: 10px;
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}


</style>
