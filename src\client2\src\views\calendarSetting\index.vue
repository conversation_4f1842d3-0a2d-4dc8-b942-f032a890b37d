<template>
    <div class="app-container">
        <div class="bg-white">
            <!-- <page-title title="日历维护" :subTitle="['任务管理的日历维护管理页面']"></page-title> -->
            <div class="year-btns-wrapper">
                <el-radio-group v-model="currentYear">
                    <el-radio-button v-for="(y, idx) in years" :key="idx" :label="y">{{ y }}年</el-radio-button>
                </el-radio-group>
            </div>
            <div class="calendar-container" v-loading='listLoading'>
                <el-row :gutter="10">
                    <el-col v-for="(idx, y) in 12" :key="idx" :xs="12" :sm="12" :md="8" :lg="8" :xl="6">
                        <div class="calendar-wrapper">
                            <calendar 
                                height="400px"
                                :render-content="renderContent"
                                :week-title="weekTitle"
                                :border="false"
                                :before-render="beforeRender"
                                @year-change="changeHandle"
                                @month-change="changeHandle"
                                :render-title="renderTitle"
                                @date-click='handleClick'
                                :default-date="`${currentYear}-${y + 1}`"
                            />
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <el-dialog v-el-drag-dialog class="dialog-mini" width="400px" title="设置"
            :visible.sync="dialogFormVisible" :close-on-click-modal='false' :append-to-body='true'>
            <el-form ref="dataForm" :model="temp" label-position="right" label-width="100px">
                <el-row>
                    <el-form-item :label="'日期'" prop="Date">
                        {{ temp.Date }}
                    </el-form-item>
                    <el-form-item :label="'方式'" prop="Type">
                        <el-radio-group v-model="temp.Type" size="small">
                            <el-radio-button :label="1">休</el-radio-button>
                            <el-radio-button :label="2">班</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                </el-row>
            </el-form>
            <div slot="footer">
                <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
                <el-button size="mini" type="primary" :loading='postLoading' @click="createData">确认</el-button>
                <el-button size="mini" type="danger" v-show="isCanClear" :loading='postLoading' @click="clearSetting">取消设置</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import Calendar from 'himmas-vue-calendar'
import elDragDialog from '@/directive/el-dragDialog'
import * as cs from '@/api/calendarSetting'
import dayjs from 'dayjs'

export default {
    name: 'calendar-setting',
    components: {
        Calendar,
    },
    directives: {
        elDragDialog
    },
    watch: {
        currentYear(val) {
            this.getSettingByYear()    
        }
    },
    computed: {
        isCanClear() {
            return this.dates.find(s => s.date == this.temp.Date) != null
        }
    },
    created() {
        this.getSettingByYear()
    },
    data() {
        return {
            years: [
                new Date().getFullYear(),
                new Date().getFullYear() + 1,
                new Date().getFullYear() + 2,
            ],
            currentYear: new Date().getFullYear(), 
            weekTitle: ['日', '一', '二', '三', '四', '五', '六'],
            mouseOverCurrentDate: null,
            dialogFormVisible: false,
            postLoading: false,
            listLoading: false,
            temp: {
                Date: null,
                Type: 1,//1：休；2：班；3：默认（按正常工作日和休息计算）
            },
            dates: []
        }
    },
    methods: {
        clearSetting() {
            this.$confirm(`是否取消${this.temp.Date}的设置?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.dialogFormVisible = false
                let postDatas = JSON.parse(JSON.stringify(this.temp))
                postDatas.Type = 3
                
                cs.edit(postDatas).then(res => {
                    this.$message({
                        message: '取消成功',
                        type: 'success'
                    })

                    let delIdx = this.dates.findIndex(s => s.date == this.temp.Date)
                    if (delIdx > -1) {
                        this.dates.splice(delIdx, 1)
                    }
                })
            })
        },
        getSettingByYear(year) {
            this.listLoading = true
            // let f = this.$options.filters['dateFilter']
            // f(s.Date, 'YYYY-MM-DD'),
            cs.getSettingByYear({year: this.currentYear}).then(res => {
                this.listLoading = false
                if (res && res.length > 0) {
                    this.dates = res.map(s => {
                        return {
                            date: dayjs(s.Date).format('YYYY-MM-DD'),
                            type: s.Type
                        }
                    })
                }
            }).catch(err => {
                this.listLoading = false
            })
        },
        createData() {
            let postDatas = JSON.parse(JSON.stringify(this.temp))
            let old = this.dates.find(s => s.date == this.temp.Date)
            this.postLoading = true
            if (old) {
                cs.edit(postDatas).then(res => {
                    old.type = this.temp.Type
                    this.postLoading = false
                    this.$message({
                        message: '设置成功',
                        type: 'success'
                    })
                }).catch(err => {
                    this.postLoading = false
                })
            }else{
                cs.add(postDatas).then(res => {
                    this.postLoading = false
                    this.dates.push({
                        date: this.temp.Date,
                        type: this.temp.Type
                    })
                    this.$message({
                        message: '设置成功',
                        type: 'success'
                    })
                }).catch(err => {
                    this.postLoading = false
                })
            }
            this.dialogFormVisible = false
        },
        twoDigit: function(num){ return ('000'+num).slice(-2) },
        renderTitle(h,year,month){
            return h('div', {
            class: {
                'title-box': true
            }
            },[
                h('span',{},year+'年'),
                h('span',{},month+'月')
            ])
        },
        renderContent(h, data) {
            var {isToday, isWeekend, isOtherMonthDay, year, day, month, renderYear, renderMonth, lunar, weekDay, festival, term} = data

            // lunar对象中存有农历数据
            var {lunarDayChiness} = lunar

            //第二行展示的数据的优先级为 节日>节气>农历日
            var secondInfo = festival ?
            festival : (term ? term : (lunarDayChiness || ''))

            var dateStr = `${year}-${this.twoDigit(month)}-${this.twoDigit(day)}`

            var isHoliday = (!!~this.dates.filter(s => s.type == 1).map(s => s.date).indexOf(dateStr))

            var isOvertime = (!!~this.dates.filter(s => s.type == 2).map(s => s.date).indexOf(dateStr))

            return (
                <div on-mouseout={ () => this.handleMouseOut(data) } on-mouseover={ () => this.handleMouseOver(data) } class={{ 'date-box': true, 'today': isToday && !isOtherMonthDay, 'weekend': isWeekend, 'holiday': isHoliday, 'overtime': isOvertime, 'other-month': isOtherMonthDay}}>
                    <div class={{ 'first-info': true }}>{ day }</div>
                    <div class={{'second-info': true, 'festival':festival}}>{ secondInfo }</div>
                    <div class="sign" v-show={ isHoliday && !isOtherMonthDay }>休</div>
                    <div class="sign" v-show={ isOvertime && !isOtherMonthDay }>班</div>
                    <div class="setting" v-show={ !isOtherMonthDay && this.mouseOverCurrentDate == data.date }>
                        <i slot="reference" class="el-icon-edit" on-click={ () => this.setting(data) } ></i>
                    </div>
                </div>
            )
        },
        beforeRender(year,month,next){
            next()
        },
        changeHandle(year,month){
        },
        handleClick(data) {
            //   data.isWeekend = !data.isWeekend
        },
        setting(data) {
            let f = this.$options.filters['dateFilter']
            this.temp.Date = f(data.date, 'YYYY-MM-DD')

            let currentType = data.isWeekend ? 1 : 2

            var dateStr = `${data.year}-${this.twoDigit(data.month)}-${this.twoDigit(data.day)}`

            let currentDate = this.dates.find(s => s.date == dateStr)
            if (currentDate) {
                currentType = currentDate.type
            }

            this.temp.Type = currentType

            this.dialogFormVisible = true
        },
        handleMouseOver(data) {
            this.mouseOverCurrentDate = data.date
        },
        handleMouseOut(data) {
            this.mouseOverCurrentDate = null
        }
    }
}
</script>

<style scoped>

.calendar-wrapper >>> .kl-calendar_body-week-title > div{
    text-align: center!important;
}

.calendar-wrapper >>> .kl-calendar_body-week-title-item{
    padding-right: 0!important;
}

.calendar-container{
    padding: 10px;
}

.calendar-wrapper{
    padding-bottom: 10px;
}

.year-btns-wrapper{
    padding: 10px 0 0 10px;
}

.calendar-wrapper >>> .date-box {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.calendar-wrapper >>> .first-info{
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
}
.calendar-wrapper >>> .second-info{
    flex: 1;
    display: flex;
    justify-content: center;
    color: #999;
    font-size: 12px;
}
.calendar-wrapper >>> .second-info.festival{
    color: #f43;
}
.calendar-wrapper >>> .sign, 
.calendar-wrapper >>> .setting{
    position: absolute;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
}
.calendar-wrapper >>> .sign{
    color: #fff;
    display: none;
    top: 0;
    left: 0;
}
.calendar-wrapper >>> .sign{
    background: #f43;
}
.calendar-wrapper >>> .overtime .sign{
    background: #409EFF;
}
.calendar-wrapper >>> .setting{
    right: 0;
    bottom: 0;
}
.calendar-wrapper >>> .date-box.today{
    background: #fb0;
    color: #fff;
}
.calendar-wrapper >>> .date-box.today .second-info{
    color: #fff;
}
.calendar-wrapper >>> .weekend{
    background: #f6f8fa;
}

.calendar-wrapper >>> .holiday .sign, 
.calendar-wrapper >>> .overtime .sign{
    display: block;
}

.calendar-wrapper >>> .date-box.other-month .second-info,
.calendar-wrapper >>> .date-box.other-month .first-info{
    color: #bababa;
}

.calendar-wrapper >>> .date-box:hover{
    border: 3px solid #fb0;
}

.calendar-wrapper >>> .title-box{
    font-size: 20px;
}
</style>