<!--实施模板-->
<template>
<!--组件内容区-->
<div class="app-container">
    <div class="appContainer">
        <!-- <page-title title="项目/工程模板管理" :subTitle="['为创建多个站点提供工序、事项的模板创建维护页面']"></page-title> -->
        <div class="body-wrapper">
            <div class="product-list">
                <div style="padding:10px;">
                    <span class="title">模板列表（{{ implementationTemplateList.length }}）</span>
                    <el-button type="text" v-if="btnMaintain=='btnMaintain'" @click="handleCreate('create')">创建模板</el-button>
                    <el-button type="text" v-if="btnMaintain=='btnMaintain'" @click="handleSortDialog">排序</el-button>
                </div>
                <div class="versions-list" v-loading="implementationTemplateListLoading">
                    <no-data v-show="implementationTemplateList.length == 0"></no-data>
                    <tags v-show="implementationTemplateList.length > 0" class="tagsList" mode="list" :items="implementationTemplateList" v-model="implementationTemplateId">
                        <template v-for="(v, idx) in implementationTemplateList" :slot="v.value" slot-scope="scope">
                            <div class="item-warpper" :key="idx">
                                <div class="item-title omit" :title="v.label">{{(idx+1)+'、'+(v.label ? v.label : '无')}}</div>
                                <div>
                                    <span :style="'line-height: 26px;color:#fff;background-color:'+(v.IsUsed?'green':'red')+';padding: 2px 4px;border-radius: 10%;'">{{v.IsUsed?"启用":"停用"}}</span>
                                </div>
                            </div>
                        </template>
                    </tags>
                </div>
            </div>
            <div class="content-wrapper">
                <no-data v-show="!implementationTemplateId"></no-data>
                <div v-show="implementationTemplateId" style="padding: 10px;border-bottom:1px solid #DCDFE6">
                    <span style="font-size:16px;font-weight: 600;">{{implementationTemplateData ? implementationTemplateData.Name:""}}</span>
                    <span style="margin-left:100px;">创建时间:{{ implementationTemplateData.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</span>
                    <span style="margin-left:100px;">创建人:{{ implementationTemplateData&&implementationTemplateData.CreateEmployee ? implementationTemplateData.CreateEmployee.Name:""}}</span>
                    <span class="btns" v-show="implementationTemplateData">
                        <el-button v-if="btnMaintain=='btnMaintain'" type="primary" @click="editImplementationTemplate">修改模板</el-button>
                        <el-button v-if="btnMaintain=='btnMaintain'" type="primary" @click="procedureTemplateManage">工序管理</el-button>
                        <el-button v-if="btnMaintain=='btnMaintain'" type="primary" v-show="implementationTemplateData&&!implementationTemplateData.IsUsed" @click="useImplementationTemplate">启用</el-button>
                        <el-button v-if="btnMaintain=='btnMaintain'" type="danger" v-show="implementationTemplateData&&implementationTemplateData.IsUsed" @click="stopImplementationTemplate">停用</el-button>
                        <el-button v-if="btnMaintain=='btnMaintain'" type="danger" @click="deleteImplementationTemplate">删除模板</el-button>
                    </span>
                </div>
                <div v-show="implementationTemplateId" style="height:calc(100% - 40px);" v-loading='implementationProcedureTemplateListLoading'>
                    <no-data v-show="!implementationProcedureTemplateId"></no-data>
                    <div v-show="implementationProcedureTemplateId" class="content-tabs">
                        <el-tabs type="card" v-model="implementationProcedureTemplateId">
                            <el-tab-pane v-for="(item,index) in implementationProcedureTemplateList" :key="item.Id" :label="(index+1)+'、'+item.Name" :name="item.Id">
                                <span slot="label">
                                    <div style="display: flex;">
                                        <span slot="label" class="tab-title" :title="item.label">{{ (index+1)+'、'+item.Name }}</span>
                                    </div>
                                </span>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                    <div v-show="implementationProcedureTemplateId" style="padding:10px 10px 0 10px;height:calc(100% - 56px);">
                        <div class="summary" style="height:100%;">
                            <div class="region-title-wrapper">
                                <div class="region-title">工序负责人：{{implementationProcedureTemplate&&implementationProcedureTemplate.EmployeeList? implementationProcedureTemplate.EmployeeList.map(t=>{ return t.Name;}).join(','):""}}</div>
                                <div class="region-btns">
                                    <el-button v-if="btnMaintain=='btnMaintain'" type="primary" @click="itemTemplateManage">事项管理</el-button>
                                </div>
                            </div>
                            <div style="height:calc(100% - 64px);overflow-y:auto;">
                                <el-table fit :data="implementationItemTemplateList" style="width: 100%" v-loading='implementationItemTemplateListLoading'>
                                    <el-table-column type="index" label="序号"></el-table-column>
                                    <el-table-column prop="Name" label="实施事项"></el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <div></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--弹窗组件区-->

    <!--模板-->
    <app-dialog
        :title="(dialogStatus=='create'?'添加':'编辑')+'模板'"
        ref="accessUserDlg"
        @closeDialog="dialogVisible = false"
        :dialogFormVisible="dialogVisible"
        :maxHeight="600"
        :width="600"
    >
        <template slot="body">
            <el-form :rules="formRules" ref="formRef" :model="formModel" label-position="right" label-width="120px">
                <el-form-item label="模板名称" prop="Name">
                    <el-input maxlength="50" v-model="formModel.Name"></el-input>
                </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <div>
                <app-button @click="dialogVisible = false" :buttonType="2"></app-button>
                <app-button @click="dialogButtonClick" :buttonType="1"></app-button>
            </div>
        </template>
    </app-dialog>


    <!-- <el-dialog width="895px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" :title="(dialogStatus=='create'?'添加':'编辑')+'模板'" :visible.sync="dialogVisible" :append-to-body="true">
        <el-form :rules="formRules" ref="formRef" :model="formModel" label-position="right" label-width="120px">
            <el-form-item label="模板名称" prop="Name">
                <el-input maxlength="50" v-model="formModel.Name"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button slot="footer" @click="dialogVisible = false">取消</el-button>
            <el-button slot="footer" type="primary" @click="dialogButtonClick">确认</el-button>
        </div>
    </el-dialog> -->

    <!--工序管理-->
    <procedureManage :templateId="implementationTemplateId" :dialogStatus="procedureManageDialogStatus" :dialogFormVisible="procedureManageDialogFormVisible" @closeDialog="procedureManageCloseDialog" @saveSuccess="procedureManageSaveSuccess"></procedureManage>

    <!--事项管理-->
    <itemManage :procedureTemplateId="implementationProcedureTemplateId" :dialogStatus="itemManageDialogStatus" :dialogFormVisible="itemManageDialogFormVisible" @closeDialog="itemManageCloseDialog" @saveSuccess="itemManageSaveSuccess"></itemManage>

        <!-- 地区树排序 -->
    <tree-sort
        @closeDialog="closeTreeSortDialog"
        @saveSuccess="handleTreeSortSaveSuccess"
        :dialogFormVisible="dialogTreeSortFormVisible"
        :treeDatas='treeDatasNode'
        :defaultProps='defaultProps'
        :rowKey='rowKey'
        :businessType='8'
        :defaultExpandedKeys="[]"
        @reload="getImplementationTemplateList"
    >
    </tree-sort>
</div>
</template>

<!--组件脚本区-->

<script>
/**引用区 */
//按照以下顺序
//组件 import empSelector from "../../../../common/empSelector";
//Api import * as demand from "@/api/projectDev/projectMgmt/demand";
//方法、属性 import { empSelector } from "../../../../common/empSelector";
import noData from "../../common/components/noData";
import * as implementationTemplate from "@/api/implementation/implementationTemplate";
import procedureManage from "./procedureManage";
import itemManage from "./ItemManage";
import treeSort from '../../common/treeSort'

export default {
    /**名称 */
    name: "template-index",
    /**组件声明 */
    components: {
        noData,
        procedureManage,
        itemManage,
        treeSort,
    },
    /**参数区 */
    props: {},
    /**数据区 */
    data() {
        return {
            /**模板加载 */
            implementationTemplateListLoading: false,
            btnMaintain: '',
            /**选择的模板Id */
            implementationTemplateId: "",
            /**选择的模板 */
            implementationTemplateData: {
                Name: "",
                CreateTime: "",
                CreateEmployee: {
                    Name: ""
                },
            },
            /**模板列表 */
            implementationTemplateList: [],
            /**选择的工序 */
            implementationProcedureTemplate: {
                Name: ""
            },
            /**选择的工序Id */
            implementationProcedureTemplateId: "",
            /**工序列表 */
            implementationProcedureTemplateList: [],
            implementationProcedureTemplateListLoading: false,
            /**事项列表 */
            implementationItemTemplateList: [],
            implementationItemTemplateListLoading: false,

            /********************************* 弹窗 *********************************/
            /**表单模型 */
            formModel: {
                Name: "",
                Level: 1
            },
            /**表单规则 */
            formRules: {
                Name: {
                    fieldName: "模板名称",
                    rules: [{
                        required: true
                    }]
                },
            },
            dialogStatus: "create",
            dialogVisible: false,
            procedureManageDialogStatus: "create",
            procedureManageDialogFormVisible: false,
            itemManageDialogStatus: "create",
            itemManageDialogFormVisible: false,

            dialogTreeSortFormVisible: false,
            treeDatasNode: [],
            defaultProps: {
                children: 'children',
                label: 'label',
            },
            rowKey: 'Id',
        };
    },
    /**计算属性---响应式依赖 */
    computed: {},
    /**监听 */
    watch: {
        /**选中模板Id */
        implementationTemplateId(val) {
            if (val) {
                this.implementationTemplateData = this.implementationTemplateList.find(
                    (t) => t.Id == val
                );
                this.implementationProcedureTemplateId = "";
                if (this.implementationTemplateData) {
                    this.getImplementationProcedureTemplateList();
                } else {
                    this.implementationProcedureTemplateList = [];
                }
            }
        },
        /**选中工序Id */
        implementationProcedureTemplateId(val) {
            if (val) {
                this.implementationProcedureTemplate = this.implementationProcedureTemplateList.find(
                    (t) => t.Id == val
                );
                if (this.implementationProcedureTemplate) {
                    this.getImplementationItemTemplateList();
                } else {
                    this.implementationItemTemplateList = [];
                }
            }
        },
    },
    /**渲染前 */
    created() {
        this.getImplementationTemplateList();
        this.btnTextValue()
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        //排序
        handleSortDialog() {
            let datas = JSON.parse(JSON.stringify(this.implementationTemplateList))
            if(datas && datas.length > 0) {
                this.epKeys = []
                this.treeDatasNode = datas.map(s => {
                    s.Id = s.value
                    s.ParentId = null
                    delete s.children
                    return s
                }) || []
                this.dialogTreeSortFormVisible = true
            }
        },
        closeTreeSortDialog() {
            this.dialogTreeSortFormVisible = false
        },
        handleTreeSortSaveSuccess() {
            this.closeTreeSortDialog();
        },
        
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },
        /**获取模板列表 */
        getImplementationTemplateList() {
            this.implementationTemplateListLoading = true
            implementationTemplate
                .getImplementationTemplateListByCondition({})
                .then((response) => {
                    this.implementationTemplateListLoading = false
                    this.implementationTemplateList = response.map((s) => {
                        s.value = s.Id;
                        s.label = s.Name;
                        return s;
                    });
                    if (
                        this.implementationTemplateList &&
                        this.implementationTemplateList.length > 0
                    ) {
                        let tempData = this.implementationTemplateList.find(
                            (t) => t.Id == this.implementationTemplateId
                        );
                        if (tempData) {
                            this.implementationTemplateData = tempData;
                        } else {
                            this.implementationTemplateId = this.implementationTemplateList[0].value;
                        }
                    } else {
                        this.implementationTemplateData = {
                            Name: "",
                            CreateTime: "",
                            CreateEmployee: {
                                Name: ""
                            },
                        };
                        this.implementationTemplateId = "";
                    }
                }).catch(err => {
                    this.implementationTemplateListLoading = false
                });
        },
        /**获取工序列表 */
        getImplementationProcedureTemplateList() {
            if (!this.implementationTemplateId) {
                return;
            }
            this.implementationProcedureTemplateListLoading = true
            implementationTemplate
                .getImplementationProcedureTemplateListByCondition({
                    ImplementationTemplateId: this.implementationTemplateId,
                })
                .then((response) => {
                    this.implementationProcedureTemplateListLoading = false
                    this.implementationProcedureTemplateList = response.map((s) => {
                        s.value = s.Id;
                        s.label = s.Name;
                        return s;
                    });
                    if (
                        this.implementationProcedureTemplateList &&
                        this.implementationProcedureTemplateList.length > 0 &&
                        !this.implementationProcedureTemplateList.find(
                            (t) => t.Id == this.implementationProcedureTemplateId
                        )
                    ) {
                        this.implementationProcedureTemplateId = this.implementationProcedureTemplateList[0].Id;
                    }

                    this.implementationProcedureTemplate = this.implementationProcedureTemplateList.find(
                        (t) => t.Id == this.implementationProcedureTemplateId
                    );
                }).catch(err => {
                    this.implementationProcedureTemplateListLoading = false
                });
        },
        /**获取事项列表 */
        getImplementationItemTemplateList() {
            if (!this.implementationProcedureTemplateId) {
                return;
            }
            this.implementationItemTemplateListLoading = true
            implementationTemplate
                .getImplementationItemTemplateListByCondition({
                    ImplementationProcedureTemplateId: this
                        .implementationProcedureTemplateId,
                })
                .then((response) => {
                    this.implementationItemTemplateListLoading = false
                    this.implementationItemTemplateList = response;
                }).catch(err => {
                    this.implementationItemTemplateListLoading = false
                });
        },
        /**模板弹框 */
        handleCreate(activeName) {
            this.formModel.Id = null;
            this.formModel.Name = "";
            this.dialogStatus = activeName;
            this.dialogVisible = true;
        },
        /**修改模板 */
        editImplementationTemplate() {
            this.formModel.Id = this.implementationTemplateId;
            this.formModel.Name = this.implementationTemplateData.Name;
            this.dialogStatus = "update";
            this.dialogVisible = true;
            // implementationTemplate.editImplementationTemplate().then();
        },
        /**模板保存 */
        dialogButtonClick() {
            var result = null;
            switch (this.dialogStatus) {
                case "create":
                    result = implementationTemplate.addImplementationTemplate(
                        this.formModel
                    );
                    break;
                case "update":
                    result = implementationTemplate.editImplementationTemplate(
                        this.formModel
                    );
                    break;
                default:
                    return;
            }
            result.then((response) => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000,
                });
                this.dialogVisible = false;
                this.getImplementationTemplateList();
            });
        },
        /**启用模板 */
        useImplementationTemplate() {
            if (this.implementationTemplateData.AllowUse) {
                implementationTemplate
                    .setImplementationTemplateStatus({
                        id: this.implementationTemplateId,
                        status: true,
                    })
                    .then((response) => {
                        this.$notify({
                            title: "提示",
                            message: "成功",
                            type: "success",
                            duration: 2000,
                        });
                        this.getImplementationTemplateList();
                    });
            } else {
                this.$notify({
                    title: "提示",
                    message: "该模板下不存在工序或某个工序负责人全部不存在",
                    type: "error",
                    duration: 2000,
                });
            }
        },
        /**停用模板 */
        stopImplementationTemplate() {
            implementationTemplate
                .setImplementationTemplateStatus({
                    id: this.implementationTemplateId,
                    status: false,
                })
                .then((response) => {
                    this.$notify({
                        title: "提示",
                        message: "成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getImplementationTemplateList();
                });
        },
        /**删除模板 */
        deleteImplementationTemplate() {
            this.$confirm("是否确认删除当前模板?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                implementationTemplate
                    .deleteImplementationTemplate([this.implementationTemplateId])
                    .then((response) => {
                        this.$notify({
                            title: "提示",
                            message: "成功",
                            type: "success",
                            duration: 2000,
                        });
                        this.getImplementationTemplateList();
                    });
            });
        },
        /**工序管理 */
        procedureTemplateManage() {
            this.procedureManageDialogFormVisible = true;
        },
        /**工序保存成功 */
        procedureManageSaveSuccess() {
            this.getImplementationTemplateList();
            this.getImplementationProcedureTemplateList();
            this.getImplementationItemTemplateList();
            this.procedureManageCloseDialog();
        },
        /**工序关闭 */
        procedureManageCloseDialog() {
            this.procedureManageDialogFormVisible = false;
        },
        /**事项管理 */
        itemTemplateManage() {
            this.itemManageDialogFormVisible = true;
        },
        /**事项保存成功 */
        itemManageSaveSuccess() {
            this.getImplementationItemTemplateList();
            this.itemManageCloseDialog();
        },
        /**事项关闭 */
        itemManageCloseDialog() {
            this.itemManageDialogFormVisible = false;
        },
    },
};
</script>

<!--组件样式区-->

<style lang="scss" scoped>
.versionWrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    background: white;
}

.versions-list {
    height: calc(100% - 48px);
    width: 250px;
    overflow: auto;
}

.region-btns {
    padding: 10px 0 10px 0;
}

.tab-title {
    float: 1;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.appContainer {
    position: absolute;
    padding: 0;
    top: 0px;
    width: 100%;
    height: 100%;

    .body-wrapper {
        display: flex;
        background: #fff;
        // min-height: calc(100% - 10px)!important;
        margin-bottom: 0;
        position: absolute;
        width: 100%;
        // height: calc(100% - 50px);
        height: 100%;

        .product-list {
            border-right: 1px solid #dcdfe6;

            // border-top: 1px solid #dcdfe6;
            >div:first-child {
                display: flex;
                // justify-content: space-between;
                align-items: center;
                padding: 0 10px;
                .title{
                    flex: 1;
                }
            }

            .tagsList {
                margin-top: 10px;
            }

            .item-warpper {
                display: flex;
                justify-content: space-between;

                .item-title {
                    display: inline;
                    line-height: 26px;
                    padding-right: 10px;
                    position: relative;

                    >span {
                        position: absolute;
                        right: 0;
                    }
                }

                // .item-opts{
                //     width: 60px;
                // }
            }
        }

        .content-wrapper {
            flex: 1;

            .content {
                padding: 0 10px 10px 10px;
                padding-right: 0;

                // border-top: 1px solid #dcdfe6;
                >div:first-child {
                    display: flex;

                    span {
                        margin-right: 30px;
                    }

                    span:not(:first-child) {
                        white-space: nowrap;
                    }
                }

                .editionDetail {
                    line-height: 18px;
                    margin-top: 10px;
                    word-break: break-all;
                }

                .opt-wrapper {
                    box-sizing: border-box;
                    // border-bottom: 1px solid #DCDFE6;
                    padding-bottom: 10px;
                }

                .total-wrapper {
                    height: 40px;
                    line-height: 40px;
                    background: #dcdfe6;
                    margin: 10px 0;
                    padding-left: 10px;
                    cursor: pointer;
                }
            }
            .btns{
                float:right;margin-top:-5px;
                button{
                    margin-left: 4px;
                }
            }
        }
    }
}
</style><style scoped>
.content-tabs>>>.el-tabs__header {
    margin-bottom: 0;
}

.content-tabs>>>.el-tabs__nav-scroll {
    margin: 4px;
    margin-bottom: 0;
}
</style>
