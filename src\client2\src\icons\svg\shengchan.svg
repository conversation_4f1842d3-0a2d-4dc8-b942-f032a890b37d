﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="70px" height="70px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="32px" y="673px" width="70px" height="70px" filterUnits="userSpaceOnUse" id="filter61">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.349019607843137 0  " in="shadowComposite" />
    </filter>
    <g id="widget62">
      <image preserveAspectRatio="none" style="overflow:visible" width="60" height="60" xlink:href="data:image/png;base64,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" x="37px" y="678px" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -32 -673 )">
    <use xlink:href="#widget62" filter="url(#filter61)" />
    <use xlink:href="#widget62" />
  </g>
</svg>