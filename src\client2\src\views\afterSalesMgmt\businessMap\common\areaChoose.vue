<template>
    <div class="areaChoose">
        <app-dialog title="地区选择" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <div class="bodyBox">
                    <slot name="tip-area"></slot>
                    <!-- 弹框的树形选择不支持单击选中，必须通过选中checkbox -->
                    <v-tree class="vtree" :queryRegionID='queryRegionID' :defaultExpandLevel="defaultExpandLevel" @checkChangeNode='handleCheckChangeNode' @checkChangeNodeList='handleCheckChangeNodeList' v-bind="$attrs" :isCheckbox='true'></v-tree>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>
<script>
import vTree from './tree'
export default{
    name:'areaChoose',
    components: {   
        vTree
    },
    props:{
        // areaId:{
        //     type:String,
        //     default:''
        // },
        beforeConfirm: Function,
        queryRegionID:{
            type:String,
            default:''
        },
        defaultExpandLevel:{
            type: Number,
            default: 0
        }
    },
    data(){
        return{
           disabledBtn:false,
           checkData:null
        }
    },
    watch: {
        
    },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        async createData(){
            let result = true
            if(this.beforeConfirm) {
                result = await this.beforeConfirm(this.checkData)
            }

            if(result) {
                // this.disabledBtn = true;
                // //单选
                // if(!this.$attrs.multiple) {
                //     if(this.checkData){
                //         this.$emit('electedRegionalData',this.checkData);
                //         this.handleClose();
                //     }else{
                //         this.$message({
                //           message: '请先选择一个地区!',
                //           type: 'warning'
                //         });
                //     }
                // }else{ //多选
                //     this.$emit('electedRegionalData',this.checkData);
                //     this.handleClose();
                // }
                // this.disabledBtn = false;
                console.log(777,this.checkData,result)
                this.$emit('electedRegionalData',this.checkData);
                this.handleClose();
            }
        },
        // handleNodeClick(d){
        //     if(this.$attrs.is)
        //     this.checkData = d;
        // },
        handleCheckChangeNode(d) {
            this.checkData = d;
        },
        handleCheckChangeNodeList(list) {
            this.checkData = list
        },
        // changeTreeNode(d){
        //     this.checkData=d;
        // }
    }

}
</script>
<style lang="scss" scoped>
.bodyBox{
    width:100%;
    height:536px;
    display: flex;
    flex-direction: column;
    .vtree{
        flex: 1;
        overflow-y: hidden;
    }
}
</style>