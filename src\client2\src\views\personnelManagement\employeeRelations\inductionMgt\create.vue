<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData"
                label-position="right" label-width="60px">
                    <el-row class="wrapper" v-loading='loading'>
                        <el-col :span="9">
                            <el-form-item label="姓名" prop="Name">
                                <el-input maxlength="50" :disabled="!editable" v-model="formData.Name" placeholder="请输入姓名"></el-input>
                            </el-form-item>
                            <el-form-item label="手机" prop="Mobile">
                                <el-input maxlength="11" type="text" :disabled="!editable" placeholder="请输入手机号" v-model="formData.Mobile"></el-input>
                            </el-form-item>
                            <el-form-item label="职位" prop="JobId">
                                <div class="selector-wrapper">
                                    <job-selector v-if="editable" key="btnAddSelector" :showType="2" :multiple="false"
                                    :list="formData.JobId ? [{JobId: formData.JobId, Name: formData.JobName}] : []"
                                    @change="handleJobChange" :disabledList="[]">
                                        <el-button slot="reference" type="text" size="mini" class="filter-item">选择职位</el-button>
                                    </job-selector>
                                    <div class="cl">
                                        <span class="omit fl" :title="formData.JobName" v-if="editable">{{formData.JobName}}</span>
                                        <span class="omit fl" :title="formData.JobName" v-else>{{formData.JobName || '无'}}</span>
                                        <i v-show="formData.JobName && editable" class="el-icon-close btn-close" @click="handleJobChange()"></i>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item label-width="120px" label="性别" prop="Sex">
                                <el-radio-group v-model="formData.Sex" :disabled='!editable'>
                                    <el-radio :label="1">男</el-radio>
                                    <el-radio :label="2">女</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label-width="120px" label="所属部门" prop="DepartmentId" :title="formData.DepartmentName">
                                <treeselect :normalizer="normalizer" class="treeselect-common" :disabled="!editable" :options="DepartmentList" :default-expand-level="3"
                                :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.DepartmentId"
                                placeholder="请选择所属部门" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"
                                :append-to-body="true" zIndex='9999'>
                                    <div slot="value-label" slot-scope="{ node }" :title="node.raw.ParentName">{{ node.raw.ParentName }}</div>
                                </treeselect>
                            </el-form-item>
                            <el-form-item label-width="120px" label="预计入职时间" prop="EntryTimePlan">
                                <el-date-picker v-model="formData.EntryTimePlan" type="date" :disabled="!editable" v-if="editable"
                                format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                <template v-else>
                                    <el-date-picker v-model="formData.EntryTimePlan" type="date" :disabled="!editable" v-if="formData.EntryTimePlan"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                    <template v-else>无</template>
                                </template>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import { regs } from "@/utils/regs";
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import jobSelector from "../../../personnelManagement/systemJob/jobSelector"
import * as EntryManagementApi from "@/api/personnelManagement/EntryManagement";
export default {
    name: "inductionMgt-create",
    directives: {},
    components: {
        Treeselect,
        jobSelector
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建待入职";
            } else if (this.dialogStatus == "update") {
                return "编辑待入职";
            } else if (this.dialogStatus == "detail") {
                return "待入职详情";
            }
            return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.goOn = false;
                this.DepartmentList = [];
                this.resetFormData();
                this.getSystemDepartmentList();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.DepartmentName,
                    id: node.Id,
                    children: node.children
                };
            },
            DepartmentList: [],

            
            disabledBtn: false,
            goOn: false,
            loading: false,
            rules: {
                Name: {fieldName: "姓名",rules: [{ required: true, max: 50 }]},
                Sex: {fieldName: "性别",rules: [{ required: true }]},
                Mobile: {fieldName: "手机号",rules: [{ required: true }, { reg: regs.phone }]},
                DepartmentId: {fieldName: "所属部门",rules: [{ required: true }]},
            },
            formData: {
                // Id: '',
                Name: '',
                Sex: 1,// 0.女 1.男
                Mobile: '',
                DepartmentId: '',
                DepartmentName: '',
                JobId: '',
                JobName: '',
                // Status: null,
                EntryTimePlan: '',
                // EntryTime: '',
            }
        };
    },
    methods: {
        handleJobChange(list) {
            if(list && list.length > 0) {
                this.formData.JobName = list[0].Name
                this.formData.JobId = list[0].JobId
            }else{
                this.formData.JobName = ''
                this.formData.JobId = ''
            }
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    console.log(postData)
                    let result = null;
                    self.disabledBtn = true;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = EntryManagementApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = EntryManagementApi.edit(postData);
                    }

                    result.then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        if (self.goOn) {
                            self.resetFormData();
                            self.$refs['formData'].resetFields();
                        }
                        self.$emit('saveSuccess', self.goOn);
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            EntryManagementApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        //获取部门信息下拉框
        getSystemDepartmentList() {
            systemDepartment.getListByCondition({}).then(res => {
                this.DepartmentList = listToTreeSelect(res, undefined, undefined, undefined, 'DepartmentName');
            });
        },
    }
};
</script>
<style scoped>
.wrapper >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>
<style lang='scss' scoped>
.wrapper{
    .selector-wrapper{
        display: flex;
        .cl{
            flex: 1;
            padding: 0 6px;
            .btn-close{
                margin-left: 6px;
                cursor: pointer;
                &:hover{
                    cursor: pointer;
                    color: red;
                }
            }
        }
    }
}
</style>