<!--添加/修改-->
<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
        <template slot="body">
            <div class="create_body">
                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth">
                    
                    <el-card shadow="never" header="试卷设置">
                        <el-form-item label="分类" prop="ClassifyId">
                            <span v-if="!editable" v-html="formData.ClassifyName"></span>
                            <treeselect v-else :normalizer="normalizer" class="treeselect-common" :disabled="!editable" :options="ClassifyList" :default-expand-level="3"
                            :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId"
                            placeholder="请选择分类" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" @input="hadnleChange"></treeselect>
                        </el-form-item>

                        <el-form-item label="试卷名称" prop="ExaminationPaperName">
                            <el-input :disabled="!editable" maxlength="30" v-model="formData.ExaminationPaperName"></el-input>
                        </el-form-item>
                        <el-form-item label="作答时间" prop="isResponseTime" style="margin-bottom:0">
                            <span style="display: inline-block;">
                            <el-form-item label-width="0">
                                <el-radio-group v-model="formData.isResponseTime" :disabled="!editable" @change="isResponseTimeChange">
                                <el-radio :label="false">不限</el-radio>
                                <el-radio :label="true">限时</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            </span>
                            <span style="display: inline-block;">
                            <el-form-item label="设置时长" prop="ResponseTime" v-if="formData.isResponseTime" class="inputLeft">
                                <el-input-number style="width:75px;" :disabled="!editable" v-model="formData.ResponseTime" :min="1" :max="999" label="描述文字"
                                :controls="false" :step="1" step-strictly>
                                </el-input-number>分钟
                            </el-form-item>
                            </span>
                        </el-form-item>
                        <el-form-item label="及格正确率" prop="PassMark" class="inputLeft">
                            <el-input-number :disabled="!editable" v-model="formData.PassMark" :min="0" :max="999" label="描述文字"
                            :controls="false" :step="1" step-strictly></el-input-number>%
                        </el-form-item>
                        <el-form-item label="积分" prop="Integral" class="inputLeft">
                            <el-input-number :disabled="!editable" v-model="formData.Integral" :precision="1" :step="0.5" :min="0" :max="10" label="描述文字"
                            :controls="false" step-strictly></el-input-number>
                        </el-form-item>
                        <el-form-item label="出卷人" prop="OwnerEmployees">
                            <emp-selector key="btnAddSelector2" :readonly="!editable" :showType="2" :multiple="true"
                            :beforeConfirm='employeeListBeforeConfirm' :list="formData.OwnerEmployees"
                            @change="employeeListChange"></emp-selector>
                        </el-form-item>
                        <el-form-item label="关联题库" prop="RelationList">
                            <el-row>
                            <el-button type="text" @click="dialogQueryVisible=true">选择题库</el-button>
                            <span class="textTips1">题目总数：{{numTotal}}</span>
                            </el-row>
                            <div class="textTips2">提示：选为关联题库的分类，将按照随机提取数量比例随机生成试卷。</div>
                            <el-table :data="formData.RelationList" stripe max-height="400">
                                <el-table-column type="index" label="序号" width="50">
                                </el-table-column>
                                <el-table-column prop="QuestionBankClassifyName" label="分类" show-overflow-tooltip></el-table-column>
                                <!-- <el-table-column prop="ExtractNumber" label="随机提取数量" width="210">
                                    <template slot-scope="scope">
                                    <template v-if="!editable">{{scope.row.ExtractNumber}}</template>
                                    <el-form-item class="inputLeft" style="margin-bottom: 13px;margin-top: 13px;" label-width="0" :prop="'RelationList.'+(scope.$index)+'.ExtractNumber'" :rules="{required: true, validator: (rule, value, callback) => validatorExtractNumber(rule, value, callback, scope.row), trigger: ['change', 'blur']}" v-else>
                                        <el-input-number :disabled="!editable" v-model="scope.row.ExtractNumber" :min="0" :max="99999" label="描述文字"
                                        :controls="false" :step="1" step-strictly>
                                        </el-input-number>
                                    </el-form-item>
                                    </template>
                                </el-table-column> -->
                                <el-table-column prop="ExtractNumber" label="提取设置" width="420">
                                    <template slot-scope="scope">
                                        <div class="flexWarp" style="margin-top: 13px;">
                                            <el-select @change="ExtractTypeChange(scope.$index)" v-model="scope.row.ExtractType" placeholder="请选择" :disabled="!editable" style="width:120px;margin-right:10px;">
                                                <el-option v-for="item in ExtractTypeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                            </el-select>
                                            <div class="flexColumn">
                                                <template v-if="scope.row.ExtractType==1">
                                                    <!-- <el-input-number :disabled="!editable" v-model="scope.row.ExtractNumber" :min="0" :max="99999" label="描述文字"
                                                    :controls="false" :step="1" step-strictly></el-input-number> -->
                                                    <el-form-item class="inputLeft" style="margin-bottom: 13px;" label-width="0"
                                                    :prop="'RelationList.'+(scope.$index)+'.ExtractNumber'"
                                                    :rules="{required: true, validator: (rule, value, callback) => validatorExtractNumber(rule, value, callback, scope.row), trigger: ['change', 'blur']}">
                                                        <span style="margin-right:10px;font-size:12px;">提取数量</span>
                                                        <el-input-number :disabled="!editable" v-model="scope.row.ExtractNumber" :min="0" :max="99999" label="描述文字"
                                                        :controls="false" :step="1" step-strictly>
                                                        </el-input-number>
                                                    </el-form-item>
                                                </template>
                                                <template v-if="scope.row.ExtractType==2">
                                                    <!-- <div style="margin-bottom: 13px;">
                                                        <el-button type="text" @click="handelScaleSetRow(scope.row)">比例设置</el-button>
                                                        <span style="margin-left:10px;">{{scope.row.ExtractNumber||''}}</span>
                                                    </div> -->
                                                    <el-form-item class="inputLeft" style="margin-bottom: 13px;" label-width="0"
                                                    :prop="'RelationList.'+(scope.$index)+'.ExtractNumber'"
                                                    :rules="{required: true, message: '比例设置不能为空', trigger: 'change'}">
                                                        <el-button type="text" @click="handelScaleSetRow(scope.row)" :disabled="!editable">比例设置</el-button>
                                                        <span style="margin-left:10px;font-size:12px;">{{scope.row.ExtractNumber||''}}</span>
                                                    </el-form-item>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    <!-- "key:'低级',value:'123',current: ''" -->
                                </el-table-column>
                                <el-table-column label="操作" width="70">
                                    <template slot-scope="scope">
                                    <el-button type="text" class="danger" @click="delRow(scope.$index)">移除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-card>
                    <el-card shadow="never" header="考试设置">
                        <el-form-item label="是否必考">
                            <el-row class="rowBox">
                                <span style="width: 200px;">
                                    <el-select v-model="formData.TrainSettingOperationModel.ExamType" placeholder="请选择">
                                        <el-option v-for="item in examTypeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </span>
                            </el-row>
                        </el-form-item>
                        <!-- <el-form-item label="关联人员" prop="TrainSettingOperationModel.ExamTypeKey" v-if="formData.TrainSettingOperationModel.ExamType != 1"
                        :rules="{validator: (rule, value, callback) => validatorExamTypeKey(rule, value, callback, formData), trigger: ['change', 'blur']}">
                            <div class="cl">
                                <el-select :disabled="!editable" class="fl" @change='() => formData.TrainSettingOperationModel.ExamTypeValues = null' style="margin-right:10px;" v-model="formData.TrainSettingOperationModel.ExamTypeKey">
                                    <el-option v-for="item in viewRangeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <div class="fl" v-if="formData.TrainSettingOperationModel.ExamTypeKey == 2">
                                    <el-button :disabled="!editable" type="text" @click="handleShowTree('ExamTypeDepartmentList', 'ExamTypeKey')">选择部门</el-button>
                                </div>
                                <el-row class="fl" v-if="formData.TrainSettingOperationModel.ExamTypeKey == 3">
                                    <div class="fl">入职时间为</div>
                                    <div class="fl" style="padding:0 10px">
                                        <el-date-picker v-model="formData.TrainSettingOperationModel.ExamTypeValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 130px;"></el-date-picker>
                                    </div>
                                    <div class="fl">以后的</div>
                                </el-row>
                                <emp-selector
                                    class="fl"
                                    style="width:300px;"
                                    v-if="formData.TrainSettingOperationModel.ExamTypeKey == 4"
                                    :readonly="!editable"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :beforeConfirm='handleBeforeConfirm'
                                    :list="formData.TrainSettingOperationModel.ExamTypeEmployeeList"
                                    @change="handleViewRange($event, 'ExamTypeEmployeeList', 'ExamTypeKey')"
                                ></emp-selector>
                            </div>
                            <ul v-if="formData.TrainSettingOperationModel.ExamTypeKey == 2 && formData.TrainSettingOperationModel.ExamTypeDepartmentList.length>0" class="dUl">
                                <li class="omit" :title="dn.DepartmentName" v-for="(dn,dnI) in formData.TrainSettingOperationModel.ExamTypeDepartmentList" :key="dnI">{{dn.DepartmentName}}</li>
                            </ul>
                        </el-form-item> -->
                        <el-form-item label="循环周期">
                            <el-row class="rowBox">
                                <span style="width: 200px;">
                                    <el-radio-group v-model="formData.TrainSettingOperationModel.ExamCycleTime">
                                        <el-radio :label="1">不循环</el-radio>
                                        <el-radio :label="2">循环</el-radio>
                                    </el-radio-group>
                                </span>
                                <template v-if="formData.TrainSettingOperationModel.ExamCycleTime==2">
                                    <span style="padding-left: 40px;">
                                        <el-form-item label="选择周期" style="margin-bottom:0" prop="TrainSettingOperationModel.ExamCycleTimeKey" :rules="{required: true, message:'周期不能为空',trigger: ['change', 'blur']}">
                                            <el-select v-model="formData.TrainSettingOperationModel.ExamCycleTimeKey" placeholder="请选择">
                                                <el-option v-for="item in studyCycleTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </span>
                                    <span>
                                        <el-form-item label="开始时间" prop="TrainSettingOperationModel.ExamCycleTimeValues" :rules="{required: true, message:'开始时间不能为空',trigger: ['change', 'blur']}" style="margin-bottom:0">
                                            <el-date-picker v-model="formData.TrainSettingOperationModel.ExamCycleTimeValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                        </el-form-item>
                                    </span>
                                </template>
                            </el-row>
                        </el-form-item>
                    </el-card>
                </el-form>
            </div>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary" :loading='disabledBtn'></app-button>
        </template>
        </app-dialog>
        <!--选择题库 弹窗组件区-->
        <query-question-bank @closeDialog="dialogQueryVisible=false" @saveSuccess="handleSaveSuccess"
        :dialogFormVisible="dialogQueryVisible" :selectIds="selectIds"></query-question-bank>
        
        <!-- 按难易度提取 比例设置 -->
        <scale-set @closeDialog="dialogScaleSetVisible=false" @saveSuccess="handleScaleSetSuccess" dialogStatus="update"
        :dialogFormVisible="dialogScaleSetVisible" :node="ScaleSetRow"></scale-set>

        <v-tree
            v-if="dialogTreeVisible"
            @saveSuccess="handleTreeSaveSuccess"
            @closeDialog="handleTreeCloseDialog"
            :dialogFormVisible="dialogTreeVisible"
            :checkedList='checkedList'>
        </v-tree>
    </div>
</template>
<script> 
import { listToTreeSelect } from "@/utils";
import * as examinationPaperManagementApi from '@/api/knowledge/ExaminationPaperManagement'
import * as questionBankManagementApi from '@/api/knowledge/questionBankManagement'

import { vars } from '../common/vars'
import approvalMixins from '@/mixins/approvalPatch'
import * as classifyApi from '@/api/classify';
import empSelector from '../../common/empSelector'
import queryQuestionBank from './queryQuestionBank'
import scaleSet from './scaleSet'

import vTree from '../train/tree';
export default {
    name: "test-paper-create",
    mixins: [approvalMixins],
    components: {
        empSelector,
        vTree,
        queryQuestionBank,
        scaleSet,
    },
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        selectClassifyId: {
            type: String,
            default: ""
        },
    },
    filters: {
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (!val) {
                this.selectIds = []
                this.ClassifyList = [];
                this.QuestionImgFileList = [];
            }
            if (val) {
                this.formData = this.$options.data().formData;
                this.getClassifyList();
                this.getQuestionBank()
            }
        }
    },
    computed: {
        // 题库列表提取题目的总数
        numTotal() {
            return this.formData.RelationList.map(s=>{
                if(s.ExtractType==1){
                    return s.ExtractNumber
                }
                if(s.ExtractType==2){
                    return s.KeyValuePairs&&s.KeyValuePairs.length>0?s.KeyValuePairs.map(j=>j.value).reduce(function(a, b){
                        let num = Number(a||0) + Number(b||0);
                        if(!isNaN(parseFloat(num))) {
                            num = num.toFixed(0)
                        }
                        return num
                    }, 0):0;
                }
                return 0
            }).reduce(function(a, b){
                let num = Number(a||0) + Number(b||0);
                if(!isNaN(parseFloat(num))) {
                    num = num.toFixed(0)
                }
                return num
            }, 0);
        },
        editable() {
            return this.dialogStatus != "detail"
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建试卷";
            } else if (this.dialogStatus == "update") {
                return "编辑试卷";
            } else if (this.dialogStatus == 'detail') {
                return '试卷详情'
            }
            return ''
        },
    },
    mounted() { },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            /** 选择部门 */
            depKeyName: '',
            depValidName: '',
            checkedList:[],
            dialogTreeVisible:false,

                
            viewRangeTypes: vars.trainEnum.viewRangeTypes, // 可见范围 1:所有人 2:按部门 3:按入职时间 4:自定义
            examTypeTypes: vars.trainEnum.examTypeTypes, // 是否必考 1:不考 2:必考 3:选考
            studyCycleTypes: vars.trainEnum.studyCycleTypes, // 学习循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
            ExtractTypeTypes: vars.trainEnum.ExtractTypeTypes, // 提取类型 1.随机提取(默认) 2.按难易度提取
            dialogQueryVisible: false,
            selectIds: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                label: node.label.split(",")[0],
                id: node.Id,
                children: node.children
                };
            },
            QuestionImgFileList: [],

            disabledBtn: false,
            ClassifyList: [],

            formLoading: false,
            rules: {
                ClassifyId: { fieldName: "分类", rules: [{ required: true, trigger: 'change' }] },
                ExaminationPaperName: { fieldName: "试卷名称", rules: [{ required: true }] },
                ResponseTime: { fieldName: "时长", rules: [{ required: true }] },
                PassMark: { fieldName: "及格正确率", rules: [{ required: true }] },
                Integral: { fieldName: "积分", rules: [{ required: true }] },
                OwnerEmployees: { fieldName: "出卷人", rules: [{ required: true }] },
                RelationList: { fieldName: "关联题库", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            formData: {
                Id: "",
                ClassifyId: null,
                ExaminationPaperName: "", // 试卷名称
                isResponseTime: false, // 是否限时
                ResponseTime: 60, // 限时 时长
                PassMark: 60, // 及格正确率
                Integral: 1, // 积分
                OwnerEmployees: [], // 出卷人 集合
                OwnerEmployeeIds: '', // 出卷人 集合
                RelationList: [],// 题库集合
                TrainSettingOperationModel: {
                    ExamType: 1, // 考试类型 1:不考 2:必考 3:选考
                    ExamTypeKey: 1, // 是否必学-人员名单 1:所有人 2:按部门 3:按入职时间 4:自定义 
                    ExamTypeValues: '', // 是否必学-人员名单 的值   为空  或 id集合 字符串
                    ExamTypeEmployeeList: [],
                    ExamTypeDepartmentList: [],

                    ExamCycleTime:  1, // 考试循环周期 1:不循环 2:循环
                    ExamCycleTimeKey: 1, // 考试循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                    ExamCycleTimeValues: '', // 考试循环周期Value

                },
            },
            
            questionBankList: [], // 题库分类集合

            ScaleSetRow: {},
            dialogScaleSetVisible: false,
        };
    },
    methods: {
        handelScaleSetRow(row){
            this.ScaleSetRow = row;
            this.dialogScaleSetVisible = true;
        },
        // 设置 课程设置的默认值
        setTrainSettingOperationModel(){
            let formModel = this.formData.TrainSettingOperationModel;
            if (formModel) {
                /** 
                 *  考试类型 类型和值 
                 *  ExamType 考试类型 1:不考 2:必考 3:选考
                 *  ExamTypeKey 考试  人员名单类型 1:所有人 2:按部门 3:按入职时间 4:自定义
                 *  ExamTypeValue 考试  试卷
                 *  ExamTypeValues 考试  人员名单值
                */
                if (formModel.ExamType == 1) {
                    formModel.ExamTypeKey =  1
                }
                /** 
                 *  考试循环周期 类型和值 
                 *  ExamCycleTime 考试循环周期 1:不循环 2:循环
                 *  ExamCycleTimeKey 考试循环周期类型 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                */
                if (formModel.ExamCycleTime == 1) {
                    formModel.ExamCycleTimeKey = 1
                }
            } else {
                this.formData.TrainSettingOperationModel = this.$options.data().formData.TrainSettingOperationModel
            }
        },
        // 提交时 课程设置的值转换 对应类型时  值需要清空
        formatTrainSettingOperationModel(formData){
            let formModel = JSON.parse(JSON.stringify(formData)).TrainSettingOperationModel, Obj = {};

            /** 
             *  考试类型 类型和值 
             *  ExamType 考试类型 1:不考 2:必考 3:选考
             *  ExamTypeKey 考试  人员名单类型 1:所有人 2:按部门 3:按入职时间 4:自定义
             *  ExamTypeValue 考试  试卷
             *  ExamTypeValues 考试  人员名单值
            */
            if (formModel.ExamType == 1) {
                formModel.ExamTypeKey =  null
                formModel.ExamTypeValue = ''
                formModel.ExamTypeValueName = ''
                formModel.ExamTypeValues = ''
            }
            if (formModel.ExamType == 2 || formModel.ExamType == 3) {
                if (formModel.ExamTypeKey == 1) {
                    formModel.ExamTypeValues = ''
                }
                if (formModel.ExamTypeKey == 2) {
                    formModel.ExamTypeValues = formModel.ExamTypeDepartmentList.map(s=>s.DepartmentId).toString()
                }
                if (formModel.ExamTypeKey == 4) {
                    formModel.ExamTypeValues = formModel.ExamTypeEmployeeList.map(s=>s.EmployeeId).toString()
                    console.log(formModel.ExamTypeValues, formModel.ExamTypeEmployeeList)
                }
            }
            delete formModel.ExamTypeEmployeeList
            delete formModel.ExamTypeDepartmentList

            /** 
             *  考试循环周期 类型和值 
             *  ExamCycleTime 考试循环周期 1:不循环 2:循环
             *  ExamCycleTimeKey 考试循环周期类型 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
             *  ExamCycleTimeValues 考试循环周期 值
            */
            if (formModel.ExamCycleTime == 1) {
                formModel.ExamCycleTimeKey = null
                formModel.ExamCycleTimeValues = ''
            }

            formData.TrainSettingOperationModel = formModel
            return formData
        },
        // 可见范围 选择人员/选择部门
        handleViewRange(users, keyName, validName) {
            // this.depKeyName = keyName;
            // this.depValidName = validName;
            if (users && users.length > 0) {
                this.formData.TrainSettingOperationModel[`${keyName}`] = users;
            } else {
                this.formData.TrainSettingOperationModel[`${keyName}`] = [];
            }
            this.$refs["formData"].validateField(`TrainSettingOperationModel.${validName}`);
        },
        // 显示选择部门弹窗
        handleShowTree(keyName, validName){
            this.depKeyName = keyName;
            this.depValidName = validName;
            let list = this.formData.TrainSettingOperationModel[keyName]
            if(list) {
                this.checkedList = list.map(s => s.DepartmentId) || []
            }
            this.dialogTreeVisible=true;
        },
        // 关闭选择部门弹窗
        handleTreeCloseDialog(){
            this.dialogTreeVisible=false;
        },
        // 选择部门弹窗 确定
        handleTreeSaveSuccess(d){
            this.formData.TrainSettingOperationModel[`${this.depKeyName}`]=[];
            this.checkedList=[];
            if(d.length>0){
                d.forEach(v => {
                    this.formData.TrainSettingOperationModel[`${this.depKeyName}`].push({
                        DepartmentId: v.Id,
                        DepartmentName: v.ParentName,
                    });
                })
                this.checkedList = this.formData.TrainSettingOperationModel[`${this.depKeyName}`].map(s=>s.DepartmentId)
                this.$refs.formData.validateField(`TrainSettingOperationModel.${this.depValidName}`);
            }
            console.log(this.formData)
            this.dialogTreeVisible=false;
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1000) {
            this.$message({
                message: '不得超过1000个',
                type: 'error'
            })
            return false
            }
            return true
        },
        // 校验 可见范围 相关 
        validatorExamTypeKey(rule, value, callback, formData) {
            if (value==2&&!formData.TrainSettingOperationModel.ExamTypeDepartmentList.length>0) {
                return callback(new Error('请选择部门'));
            }
            if (value==3&&!formData.TrainSettingOperationModel.ExamTypeValues) {
                return callback(new Error('请选择入职时间'));
            }
            if (value==4&&!formData.TrainSettingOperationModel.ExamTypeEmployeeList.length>0) {
                return callback(new Error('请选择自定义人员'));
            }
            return callback();
        },
        isResponseTimeChange(){
            this.formData.ResponseTime = 60
        },
        // 移除已选择的题库
        delRow(index){
            this.formData.RelationList.splice(index, 1)
            this.selectIds = this.formData.RelationList.map(s=>s.QuestionBankClassifyId)
            this.$refs.formData.validateField("RelationList");
        },
        // 校验题库提取的题目数量
        validatorExtractNumber(rule, value, callback, row) {
            if(row.ExtractType==1){
                if (!value) {
                    return callback(new Error('请输入有效值'));
                }
                if (value > row.maxExtractNumber) {
                    return callback(new Error('超出分类下题目数量上限'));
                }
            }
            return callback();
        },
        ExtractTypeChange(index){
            this.formData.RelationList[index].ExtractNumber = ''
            this.formData.RelationList[index].KeyValuePairs = null
            this.$refs.formData.clearValidate(`RelationList.${index}.ExtractNumber`)
            this.$refs.formData.clearValidate(`RelationList.${index}.KeyValuePairs`)
        },
        handleScaleSetSuccess(val){
            let self = this;
            if(val){
                // ScaleSetRow
                // ExtractNumber
                let index = self.formData.RelationList.findIndex(s=>s.QuestionBankClassifyId == self.ScaleSetRow.QuestionBankClassifyId),str = '';
                val.map((s,i)=>{
                    if(s.isChecked){
                        if(i!=0) str +='；';
                        str += `${s.key}（${s.value}）`
                    }
                })
                self.formData.RelationList[index].ExtractNumber = str;
                self.formData.RelationList[index].KeyValuePairs = val;
                // console.log(val,self.formData.RelationList)
                self.$nextTick(()=>{
                    self.$refs.formData.validateField(`RelationList.${index}.ExtractNumber`);
                })
            }
            this.dialogScaleSetVisible = false
        },
        // 选中题目确认
        handleSaveSuccess(arr){
            arr.map(s=> { 
                if(this.formData.RelationList.every(q=>q.QuestionBankClassifyId != s.Id)) {
                this.formData.RelationList.push({
                    ExtractNumber: '',
                    maxExtractNumber: s.count,
                    QuestionBankClassifyId: s.Id,
                    QuestionBankClassifyName: s.label,
                    Sort: this.formData.RelationList.length>0?(this.formData.RelationList[this.formData.RelationList.length - 1].Sort + 1): 1,
                })
                }
            })
            this.selectIds = this.formData.RelationList.map(s=>s.QuestionBankClassifyId)
            this.dialogQueryVisible = false
            this.$refs.formData.validateField("RelationList");
        },
        employeeListBeforeConfirm(users) {
            if (users && users.length > 5) {
                this.$message({
                message: '考情组负责人不得超过5人',
                type: 'error'
                })
                return false
            }
            return true
        },
        employeeListChange(users) {
            this.formData.OwnerEmployees = users
            this.$refs.formData.validateField("OwnerEmployees");
        },
        hadnleChange() {
            this.$refs.formData.validateField("ClassifyId");
        },
        // 获取题库分类集合 用于比对 选择的题库的单项的数量的最大值
        getQuestionBank(){
            questionBankManagementApi.GetQuestionBankClassifyList({}).then(res => {
                this.questionBankList = res || []
            });
        },
        //获取分类下拉框
        getClassifyList() {
            this.formLoading = true;
            classifyApi.getListByCondition({ BusinessType: 10 }).then(res => {
                var departments = res.Items.map(function (item, index, input) {
                return {
                    Id: item.Id,
                    label: item.Name,
                    ParentId: item.ParentId
                };
                });
                
                this.ClassifyList = listToTreeSelect(departments);
                if (this.selectClassifyId) {
                this.formData.ClassifyId = this.selectClassifyId;
                }
                if (this.dialogStatus != "create" && this.id) {
                this.$nextTick(() => {
                    this.getDetail();
                });
                } else {
                this.formLoading = false;
                }
            }).catch(err => {
                this.formLoading = false;
            });
        },
        //清理表单
        resetFormData() {
        let temp = {
            // Id: "",
            // ClassifyId: null,
            // QuestionType: 1,
            // DifficultyLevel: 1,
        };
        this.formData = Object.assign({}, this.formData, temp);
        },

        //获取详情
        getDetail() {
            this.formLoading = true;
            examinationPaperManagementApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.formData.isResponseTime = this.formData.ResponseTime > 0;
                this.formData.RelationList.map(s=>{
                    s.maxExtractNumber = this.questionBankList.find(q=>q.value == s.QuestionBankClassifyId).count || 0
                })
                this.selectIds = this.formData.RelationList.map(s=>s.QuestionBankClassifyId)
                this.setTrainSettingOperationModel();
                this.formLoading = false;
            }).catch(err => {
                this.formLoading = false;
            });
        },
        //提交
        handleSave() {
            // this.disabledBtn = true;
            let listResult = this.$refs.formData.validate();
            let postData = JSON.parse(JSON.stringify(this.formData));
            if(postData.OwnerEmployees && postData.OwnerEmployees.length > 0) {
                postData.OwnerEmployeeIds = postData.OwnerEmployees.map(e => e.EmployeeId).toString()
            }
            delete postData.OwnerEmployees
            postData.ResponseTime = postData.isResponseTime ? postData.ResponseTime : 0
            delete postData.isResponseTime
            postData = this.formatTrainSettingOperationModel(postData)
            Promise.all([listResult]).then(valid => {
                postData.RelationList.map(s=>{  
                    s.ExtractNumber += '';
                    if(s.ExtractType==2){
                        s.ExtractNumber = JSON.stringify(s.KeyValuePairs);
                    }
                })
                console.log('postData',postData)
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = examinationPaperManagementApi.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = examinationPaperManagementApi.edit(postData);
                }
                this.disabledBtn = true;
                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    this.$emit('saveSuccess', false);
                }).catch(err => {
                    this.disabledBtn = false
                })
            }).catch(err => {
                this.disabledBtn = false;
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style scoped>
.notLabel >>> .el-radio__label{
  padding: 0 !important;
}
.inputLeft >>> .el-input-number{
  width: 75px;
}
.inputLeft >>> .el-input-number .el-input__inner{
  text-align: left;
  width: 70px;
}
.upDiv >>> .el-icon-circle-close{
  color:white;
}
.elUpload >>> .el-upload-list{
  display:none;
}
.create_body >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>
<style lang="scss" scoped>
.el-card{
    margin-bottom: 20px;
}
.upDiv{
    ul{
        li{
            width:159px;
            height:100px;
            position: relative;
            margin:5px;
            overflow: hidden;
            background:#282828;
        >i{
            display:none;
            z-index: 10;
            color:#ccc;
        }
        .elImg{
            width:100%;
            height:100%;
        }
        }
        li:hover{
            >i{
                display:block;
                position: absolute;
                right:0;
                top:0;
            }
        }
    }
}
.textTips1{
  font-size: 12px;
  float: right;
}
.textTips2{
  font-size: 12px;
  color: #f59a23;
}
.rowBox{
  display: flex;
//   padding: 5px 0;
  &_left {
    // width: 80px;
  }
  &_input{
    flex: 1;
    padding-right: 10px;
  }
  &_right {
    width: 170px;
  }
}
.wrapper {
  display: flex;

  .left {
    flex: 1;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
.danger{
    color: #F56C6C;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
</style>
