<!--软件实施指南管理-->
<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="pageWrapper">
        <!--左侧树-->
        <div class="product-list"><br v-if="btnAddChildren==''" />
          <el-button v-if="btnAddChildren=='btnAddChildren'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">创建分类</el-button>
          <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <div class="treeBox" v-loading='treeLoading'>
            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                <span v-if="data.Level>0" class="node-btn-area">
                  <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                      <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
        <!--右侧内容-->
        <div class="content-wrapper __dynamicTabContentWrapper">
          <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" @rowSelectionChanged="rowSelectionChanged" :tab-columns="tabColumns" :tab-datas="dataSourceList" :isShowAllColumn="true" :loading="tableLoading" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="true" :layoutMode='layoutMode' :isShowBtnsArea='false' @sortChagned="handleSortChange">

              <template slot="Status" slot-scope="scope">
                <span class="item-status" :style="{backgroundColor: getHandlerStatusObj(scope.row.Status).color}">
                  {{scope.row.Status | statusFilter }}
                </span>
              </template>

              <template slot="CreateTime" slot-scope="scope">
                {{scope.row.CreateTime | dateFilter("YYYY-MM-DD HH:mm")}}
              </template>

              <template slot="CreateEmployee" slot-scope="scope">
                <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
              </template>

              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                  <template slot="KeyWords">
                    <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索实施指南名称/创建人"></el-input>
                  </template>

                  <template slot="Status">
                    <div class="month-range-wrapper">
                      <div class="start-month">
                        <el-select style="width: 100%;" clearable v-model="listQuery.Status" placeholder="请选择状态">
                          <el-option v-for="item in StatusList" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                  </template>

                  <template slot="SelectTime">
                    <el-date-picker style="width: 100%;" v-model="listQuery.SelectTime" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" :clearable="false"></el-date-picker>
                  </template>

                  <!-- 表格批量操作区域 -->
                  <template slot="btnsArea">
                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                      <el-dropdown slot="btnBatchOpt" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                        <el-button type="primary">
                            {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="updateType">修改分类</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </permission-btn>
                  </template>
                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button @click="handleEdit(scope.row,'detail')" :type="2"></app-table-row-button>

                <app-table-row-button v-if="rowBtnIsExists('btnValidInvalid')" v-show="scope.row.Status == 1" @click="handleValidInvalid(scope.row, 2)" :type="3" text="无效"></app-table-row-button>

                <app-table-row-button v-if="rowBtnIsExists('btnValidInvalid')" v-show="scope.row.Status == 2" @click="handleValidInvalid(scope.row, 1)" text="有效"></app-table-row-button>

                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleEdit(scope.row, 'update')" :type="1"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
              </template>

            </app-table>
          </div>
          <!----------------------------------------- 分页 ------------------------------------------->
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>
    <!--添加/修改 弹窗组件区-->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="implementationGuideId" :selectClassifyId="selectClassifyId" @reload="getList"></create-page>

    <!--添加/修改 分类 弹窗组件区-->
    <classify-page :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyDialogFormVisible" @closeDialog="classifyCloseDialog" @saveSuccess="classifySaveSuccess"></classify-page>

    <batch-update v-if="dialogbatchVisible && multipleSelection.length > 0" :batchType='1' @closeDialog="()=>dialogbatchVisible=false" @saveSuccess="handleSaveBatchSuccess" :dialogFormVisible="dialogbatchVisible" :ids="multipleSelection.map(s => s.Id)"></batch-update>


  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as guide from '@/api/informationCenter/implementationGuide'
import * as classify from '@/api/classify'
import elDragDialog from "@/directive/el-dragDialog";
import noData from "@/views/common/components/noData"
import createPage from './create'
import classifyPage from "./classify";
import { StatusList } from "@/utils/commonEnum";
import { listToTreeSelect } from "@/utils";
import batchUpdate from '../../failureCase/batchUpdate'

export default {
  /**名称 */
  name: "implementation-guide",
  mixins: [indexPageMixin],
  directives: {
    elDragDialog
  },
  /**组件声明 */
  components: {
    noData,
    createPage,
    classifyPage,
    batchUpdate,
  },
  /**参数区 */
  props: {
    /**主键Id */
    keyId: {
      type: String
    }
  },
  filters: {
    statusFilter(status) {
      let obj = StatusList.find(
        s => s.value == status
      );
      if (obj) {
        return obj.label;
      }
      return status;
    },

    toStringFilter(item) {
      if (item) {
        return item;
      }
      return '无'
    },

  },
  /**数据区 */
  data() {
    return {
      implementationGuideId: '',

      /******************* 弹窗 *******************/
      dialogStatus: 'create',
      dialogFormVisible: false,
      selectClassifyId: "",

      /******************* 表格 *******************/
      layoutMode: 'simple',
      tableLoading: false,
      dataSourceList: [],
      total: 0,

      listQuery: {
        PageIndex: 1,
        PageSize: 20,
        KeyWords: '',
        ClassifyId: null,
        SelectTime: [],
        Status: ""
      },

      StatusList: StatusList,

      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "SelectTime", label: "创建时间" },
        { prop: "Status", label: "反馈状态" },
      ],

      tabColumns: [
        {
          attr: { prop: "GuideName", label: "实施指南名称", showOverflowTooltip: true, width: 500 }
        },
        {
          attr: { prop: "Status", label: "状态", sortable: 'Status' }, slot: true
        },
        {
          attr: { prop: "ClassifyName", label: "分类", }
        },
        {
          attr: { prop: "PageView", label: "阅读量", sortable: 'PageView' }
        },
        {
          attr: { prop: "CreateEmployee", label: "创建人", }, slot: true
        },
        {
          attr: { prop: "CreateTime", label: "创建时间", width: 150, sortable: 'CreateTime' }, slot: true
        },
      ],


      /******************* 权限 *******************/
      btnAddChildren: '',
      btnAdd: '',
      btnEdit: '',


      /******************* 树 *******************/
      /**树节点弹窗 */
      classifyDialogFormVisible: false,
      classifyDialogStatus: "create",
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },
      
      multipleSelection: [],
      dialogbatchVisible: false,


    };
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.ClassifyId = val.Id;
          this.listQuery.PageIndex = 1;
          this.getList();
        }
      },
      immediate: true
    }
  },
  created() {
    this.btnTextValue();
  },
  mounted() {
    this.loadTreeData();
  },
  methods: {
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleBatchDialog() {
      this.dialogbatchVisible = true
    },
    handleSaveBatchSuccess() {
        this.dialogbatchVisible = false
        this.getList();
    },

    getHandlerStatusObj(status) {
      return (this.StatusList.find(s => s.value == status) || {});
    },

    /******************* 表格事件 *******************/
    /**加载数据 */
    getList() {
      let _this = this;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      if (_this.checkedNode.Id) {
        _this.listQuery.ClassifyId = _this.checkedNode.Id;
      }
      if (postData.SelectTime.length > 0) {
        postData.StartTime = postData.SelectTime[0]
        postData.EndTime = postData.SelectTime[1]
      }
      postData.PageType = 'management';
      postData = this.assignSortObj(postData);
      _this.tableLoading = true
      guide.getList(postData).then(response => {
        _this.tableLoading = false
        _this.total = response.Total;
        _this.dataSourceList = response.Items;
      }).catch(err => {
        _this.tableLoading = false
      });
    },

    /**添加 */
    handleAdd(activeName) {
      this.selectClassifyId = this.checkedNode.Id;
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },

    /**修改 */
    handleEdit(row, optType) {
      this.selectClassifyId = "";
      this.implementationGuideId = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    /**设置状态 */
    handleValidInvalid(row, status) {
      let postData = JSON.parse(JSON.stringify(row));
      postData.status = status;
      guide.edit(postData).then(res => {
        this.$notify({
          title: '成功',
          message: '设置成功！',
          type: 'success'
        });
        this.getList();
      });
    },

    /**删除 */
    handleDelete(row) {
      let ids = [];
      ids.push(row.Id);
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        guide.del(ids).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    handleSortChange({ column, prop, order }) {
      this.sortObj = { prop, order, };
      this.getList();
    },

    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    onResetSearch() {
      this.listQuery.SelectTime = []
      this.listQuery.Status = ""
      this.listQuery.KeyWords = ""
    //   this.listQuery.ClassifyId = null
      this.getList();
    },

    btnTextValue() {
      let btns = this.topBtns
      btns.forEach(item => {
        if (item["DomId"] == "btnAddChildren") {
          this.btnAddChildren = "btnAddChildren"
        }
        if (item["DomId"] == "btnAdd") {
          this.btnAdd = "btnAdd"
        }
        if (item["DomId"] == "btnEdit") {
          this.btnEdit = "btnEdit"
        }
      })
    },

    handleFilterBtn(btns) {
      if (btns && btns.length > 0) {
        return btns.filter(s => s.DomId != 'btnAddChildren')
      }
      return []
    },

    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleAdd("create");
          break;
        case "updateType":
          if(this.multipleSelection && this.multipleSelection.length == 0) {
            this.$message({
                message: "至少选择一条",
                type: "error"
            });
            return;
          }
          this.handleBatchDialog()
          break;
      }
    },

    checkPremissBtns(domId) {
      return this.rowBtns.findIndex(b => b.DomId == domId) > -1
    },


    /******************* 弹窗相关 *******************/
    closeDialog() {
      this.dialogFormVisible = false
    },
    handleSaveSuccess(d) {
      if (!d) {
        this.closeDialog();
      }
      this.getList();
    },

    classifySaveSuccess(d) {
      if (!d) {
        this.classifyCloseDialog();
      }
      this.loadTreeData();
    },
    classifyCloseDialog() {
      this.classifyDialogFormVisible = false;
    },


    /******************* 树事件 *******************/
    loadTreeData() {
      let _this = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 1
      };
      _this.treeLoading = true
      classify.getListPage(paramData).then(response => {
        _this.treeLoading = false
        response.Items.unshift({
          Id: "",
          Name: "全部",
          Level: 0,
          ParentId: null
        });
        _this.treeData = listToTreeSelect(response.Items);

        if (_this.treeData && _this.treeData.length > 0) {
          if (
            !(
              _this.checkedNode &&
              response.Items.find(t => {
                return t.Id == _this.checkedNode.Id;
              })
            )
          ) {
            _this.checkedNode = _this.treeData[0];
          }
        } else {
          _this.checkedNode = null;
        }
        if (_this.checkedNode) {
          _this.$nextTick(() => {
            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
          });
        }
      }).catch(err => {
        _this.treeLoading = false
      });
    },
    /**添加顶级节点 */
    addTopLevel() {
      this.paramNode = {
        Id: null,
        Name: "",
        Level: 0
      };
      this.classifyDialogStatus = "create";
      this.classifyDialogFormVisible = true;
    },
    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    /**树下拉事件 */
    handleCommand(optType, node, data) {
      switch (optType) {
        case "create":
          this.paramNode = data;
          this.classifyDialogStatus = "create";
          this.classifyDialogFormVisible = true;
          break;
        case "update":
          this.paramNode = data;
          this.classifyDialogStatus = "update";
          this.classifyDialogFormVisible = true;
          break;
        case "delete":
          this.handleDeleteArea(data);
          break;
        default:
          break;
      }
    },
    /**删除树节点 */
    handleDeleteArea(data) {
      if (data.children && data.children.length > 0) {
        this.$notify({
          title: "提示",
          message: "请先删除子级",
          type: "error",
          duration: 2000
        });
        return;
      }
      let paramData = { ids: [data.Id], businessType: 1 };
      this.$confirm(`是否确认删除${data.Name}?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        classify.del(paramData).then(res => {
          if (this.checkedNode && this.checkedNode.Id == data.Id) {
            this.checkedNode = null;
          }
          this.loadTreeData();
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
        });
      });
    },
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.bg-white {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      //   padding: 10px;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
