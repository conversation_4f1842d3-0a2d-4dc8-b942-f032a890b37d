<template>
    <div>
        <!-- <div style="border: 1px solid red;">xxxxxxxxxxxxx{{ excludeChildren(item) }}</div> -->
        <!-- Container 容器 -->
        <!-- {{ excludeChildren(ctrlStruct) }} -->
        <template v-if="ctrlStruct && ctrlStruct.type == 1" class="level11111111111">
            <div style="border: 2px solid red; padding: 10px; overflow: hidden;">
                <el-row>
                    <dynamic-ctrl :ctrl='ctrlStruct' v-model="ctrlStruct.value"></dynamic-ctrl>
                    <tree-item
                        class="item"
                        v-for="(child, idx) in item.children"
                        :key="idx"
                        :item="child"
                    ></tree-item>
                </el-row>
            </div>
        </template>
        <!-- form 表单 -->
        <template v-else-if="ctrlStruct && ctrlStruct.type == 2" class="level22222222222222">
            <div style="border: 2px solid yellow; padding: 10px; overflow: hidden;" v-show="isVisible">
                <!-- formname={{ ctrlName }} -->
                <el-form :ref="ctrlName" label-position="right" label-width="100px">
                    <el-row>
                        <dynamic-ctrl :ctrl='ctrlStruct' v-model="ctrlStruct.value"></dynamic-ctrl>
                        <tree-item
                            class="item"
                            v-for="(child, idx) in item.children"
                            :key="idx"
                            :item="child"
                        ></tree-item>
                    </el-row>
                </el-form>
            </div>
        </template>
        <template v-else-if="ctrlStruct" class="level3333333333">
            <!-- ctrlName={{ ctrlName }} -->
            <dynamic-ctrl :ctrl='ctrlStruct' v-model="ctrlStruct.value"></dynamic-ctrl>
            <tree-item
                class="item"
                v-for="(child, idx) in item.children"
                :key="idx"
                :item="child"
            ></tree-item>
        </template>
    </div>
</template>

<script>
import * as df from '@/api/dynamicForm'
import mixin from '../dynamicFormCommon/mixins'
import DynamicCtrl from './dynamicCtrl'
export default {
    name: 'tree-item',
    mixins: [mixin],    
    components: {
        DynamicCtrl,
    },
    props: {
        item: Object,

    },
    // watch: {
    //     item: {
    //         handler(val, oldVal) { 
    //             // this.attrObj = this.initAttr(JSON.parse(JSON.stringify(val)))
    //             // this.attrObj = JSON.parse(JSON.stringify(val))
    //         },
    //         deep: true,
    //         immediate: true,
    //     },
    // },
    
    computed: {
        ctrlName() {
            return this.ctrlStruct.attrs.find(a => a.attrName == 'name').value
        },
        isVisible() {
            if(this.ctrlStruct.type == 2) {
                let isvis = this.ctrlStruct.attrs.find(s => s.attrName == 'isVisible')
                if(!isvis) {
                    return true
                }
                return isvis.value == 'true'
            }
            return false
        },
    },
    mounted() {
    },
    data() {
        return {
            //表单控件数据结构
            ctrlStruct: this.item,
            viewModel: {

            },
            temp: {

            }
        }
    },
    methods: {
        handleTest() {
            alert(JSON.stringify(this.viewModel))
            return false
        },
        excludeChildren(item) {
            let result = JSON.parse(JSON.stringify(item))
            result.children && delete result.children
            return result
        },

    }
}
</script>

<style scoped>


</style>