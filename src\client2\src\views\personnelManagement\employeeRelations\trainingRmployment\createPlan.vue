<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100" :maxHeight="700">
            <template slot="body">
                <!-- 第一步  选择人员 -->
                <el-row class="wrapperBody __dynamicTabContentWrapper">
                    <template v-if="pageStep==1">
                        <page-title :showBackBtn="false">
                            <div class="tagBox" slot="def">
                                <tags :items='EntryTimeEnum' v-model="listQuery.EntryTimeEnum" @change="handleTagsChange">
                                    <template v-for="t in EntryTimeEnum" :slot="t.value">
                                        {{ t.label }}
                                    </template>
                                </tags>
                            </div>
                        </page-title>
                        <div class="__dynamicTabWrapper">
                            <app-table ref="mainTable1" :tab-columns="tabAllColumns" :tab-datas="tableData" :loading="listLoading"
                                :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable"
                                :multable="true" layoutMode='simple' :isShowBtnsArea='false' :isShowConditionArea="false"
                                @sortChagned="handleSortChange" @rowSelectionChanged="rowSelectionChanged">
                                <template slot="EntryTime" slot-scope="scope">{{ scope.row.EntryTime | dateFilter('YYYY-MM-DD') }}</template>
                            </app-table>
                        </div>
                    </template>
                    <template v-if="pageStep==2">
                        <el-form :rules="rules" ref="formData" :model="formData" style="padding-top:0; height: 100%; display: flex; flex-direction: column;"
                        label-position="right" label-width="60px">
                            <div class="__dynamicTabWrapper flex-1">
                                <app-table ref="mainTable2" :tab-columns="tabAllColumns1" :tab-datas="formData.dataSourceList"
                                    :isShowAllColumn="true" :isShowOpatColumn="true" :optColWidth="60"
                                    :multable="false" layoutMode='simple' :isShowBtnsArea='false' :isShowConditionArea="false">
                                    <template slot="EntryTime" slot-scope="scope">{{ scope.row.EntryTime | dateFilter('YYYY-MM-DD') }}</template>
                                    <template slot="AvatarPath" slot-scope="scope">
                                        <img :src="scope.row.AvatarPath || require('../../../../assets/images/avatar3.png')"
                                        style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 75px; height: 75px;" />
                                    </template>
                                    <template slot='header_PositiveTimePlan' slot-scope="scope">
                                        <span>
                                            <span>{{scope.column.column.label}}</span>
                                            <span style="color:red;">*</span>
                                        </span>
                                    </template>
                                    <template slot="PositiveTimePlan" slot-scope="scope">
                                        <el-form-item label-width="0" style="margin-top: 15px;"
                                        :prop="`dataSourceList.${scope.index - 1}.PositiveTimePlan`"
                                        :rules="{required: true, message: '预计转正时间不能为空', trigger: 'change'}">
                                            <el-date-picker v-model="scope.row.PositiveTimePlan" type="date"
                                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                        </el-form-item>
                                    </template>
                                    <template slot='header_TutorEmployeeId' slot-scope="scope">
                                        <span>
                                            <span>{{scope.column.column.label}}</span>
                                            <span style="color:red;">*</span>
                                        </span>
                                    </template>
                                    <template slot="TutorEmployeeId" slot-scope="scope">
                                        <el-form-item label-width="0" style="margin-top: 15px;"
                                        :prop="`dataSourceList.${scope.index - 1}.TutorEmployeeId`"
                                        :rules="{required: true, message: '导师不能为空', trigger: 'change'}">
                                            <emp-selector
                                                :ref="`selector${scope.index - 1}`"
                                                :class="`fl ${scope.index}`"
                                                style="width:100%;"
                                                :key="`ccusers${scope.index - 1}`"
                                                :showType="2"
                                                :multiple="false"
                                                :list="scope.row.TutorEmployeeIdList||[]"
                                                :beforeConfirm="handleBeforeConfirm"
                                                @change="handleViewRange($event, scope.index - 1, 'TutorEmployeeId')"
                                            ></emp-selector>
                                        </el-form-item>
                                    </template>
                                    

                                    <template slot='header_ImmediateSuperiorId' slot-scope="scope">
                                        <span>
                                            <span>{{scope.column.column.label}}</span>
                                            <span style="color:red;">*</span>
                                        </span>
                                    </template>
                                    <template slot="ImmediateSuperiorId" slot-scope="scope">
                                        <el-form-item label-width="0" style="margin-top: 15px;"
                                        :prop="`dataSourceList.${scope.index - 1}.ImmediateSuperiorId`"
                                        :rules="{required: true, message: '直属上级不能为空', trigger: 'change'}">
                                            <emp-selector
                                                :ref="`selector${scope.index - 1}`"
                                                :class="`fl ${scope.index}`"
                                                style="width:100%;"
                                                :key="`ccusers${scope.index - 1}`"
                                                :showType="2"
                                                :multiple="false"
                                                :list="scope.row.ImmediateSuperiorIdList||[]"
                                                :beforeConfirm="handleBeforeConfirm"
                                                @change="handleViewRange($event, scope.index - 1, 'ImmediateSuperiorId')"
                                            ></emp-selector>
                                        </el-form-item>
                                    </template>


                                    <template slot='header_DepartmentManagerId' slot-scope="scope">
                                        <span>
                                            <span>{{scope.column.column.label}}</span>
                                            <span style="color:red;">*</span>
                                        </span>
                                    </template>
                                    <template slot="DepartmentManagerId" slot-scope="scope">
                                        <el-form-item label-width="0" style="margin-top: 15px;"
                                        :prop="`dataSourceList.${scope.index - 1}.DepartmentManagerId`"
                                        :rules="{required: true, message: '部门经理不能为空', trigger: 'change'}">
                                            <emp-selector
                                                :ref="`selector${scope.index - 1}`"
                                                :class="`fl ${scope.index}`"
                                                style="width:100%;"
                                                :key="`ccusers${scope.index - 1}`"
                                                :showType="2"
                                                :multiple="false"
                                                :list="scope.row.DepartmentManagerIdList||[]"
                                                :beforeConfirm="handleBeforeConfirm"
                                                @change="handleViewRange($event, scope.index - 1, 'DepartmentManagerId')"
                                            ></emp-selector>
                                        </el-form-item>
                                    </template>

                                    <!-- 表格行操作区域 -->
                                    <template slot-scope="scope">
                                        <app-table-row-button @click="handleDelRow(scope.index - 1)" :type="3" text="移除"></app-table-row-button>
                                    </template>
                                </app-table>
                            </div>
                        </el-form>
                    </template>
                </el-row>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <template v-if="pageStep==1">
                    <!-- 下一步 -->
                    <app-button @click="toNext" :buttonType="999" text="下一步"></app-button>
                </template> 
                <template v-if="pageStep==2">
                    <!-- 确认 -->
                    <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
                </template>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import empSelector from '@/views/common/empSelector'
import indexPageMixin from "@/mixins/indexPage";
import * as EmployeeTrainingPlanApi from "@/api/personnelManagement/EmployeeTrainingPlan";
import { vars } from '../common/vars'
export default {
    name: "trainingRmployment-createPlan",
    directives: {},
    components: {
        empSelector
    },
    mixins: [indexPageMixin],
    computed: {
        pageTitle() {
            // 1 添加新人  2 新人培养计划
            return this.pageStep == 1 ? "添加新人": (this.pageStep == 2 ? "新人培养计划": '');
        }
    },
    props: {
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();
                this.getList();
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            pageStep: 1, // 1 添加新人  2 新人培养计划
            EntryTimeEnum: vars.EntryTimeEnum,

            listQuery: {
                EntryTimeEnum: 1,
                PageSize: 99999,
            },
            listLoading: false,
            // 学习记录 表格头
            tabAllColumns: [
                {attr: {prop: "Name",label: "姓名"}},
                {attr: {prop: "DepartmentName",label: "部门"}},
                {attr: {prop: "Number",label: "工号",sortable: "custom"}},
                {attr: {prop: "EntryTime",label: "入职时间",sortable: "custom"},slot: true},
            ],
            tabAllColumns1: [
                {attr: {prop: "AvatarPath",label: "头像", width: '100'},slot: true},
                {attr: {prop: "Name",label: "姓名", width: '100'}},
                {attr: {prop: "DepartmentName",label: "部门", width: '100'}},
                {attr: {prop: "Number",label: "工号", width: '80'}},
                {attr: {prop: "EntryTime",label: "入职时间", width: '100'},slot: true},
                {attr: {prop: "PositiveTimePlan",label: "预计转正时间",width: '160px'},slot: true, customHeader: true,},
                {attr: {prop: "TutorEmployeeId",label: "导师",width: '220px'},slot: true, customHeader: true,},

                {attr: {prop: "ImmediateSuperiorId",label: "直属上级",width: '220px'},slot: true, customHeader: true,},
                {attr: {prop: "DepartmentManagerId",label: "部门经理",width: '220px'},slot: true, customHeader: true,},
            ],



            tableData: [],
            disabledBtn: false,
            loading: false,
            rules: {
                Name: {fieldName: "姓名",rules: [{ required: true, max: 50 }]},
                Sex: {fieldName: "性别",rules: [{ required: true }]},
                // Mobile: {fieldName: "手机号",rules: [{ required: true }, { reg: regs.phone }]},
                DepartmentId: {fieldName: "所属部门",rules: [{ required: true }]},
            },
            formData: {
                dataSourceList: [],
            },
        };
    },
    methods: {
        handleDelRow(index){
            let self = this;
            self.$confirm(`是否确认移除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                console.log(index)
                self.formData.dataSourceList.splice(index-1, 1)
                self.$forceUpdate();
            });
        },
        // 导师  选择人员
        handleViewRange(users, index, key) {
            if (users && users.length > 0) {
                this.formData.dataSourceList[index][key] = users.map(s=>s.EmployeeId)[0]
                this.formData.dataSourceList[index][`${key}List`] = users
            } else {
                this.formData.dataSourceList[index][key] = ''
                this.formData.dataSourceList[index][`${key}List`] = []
            }
            this.$refs["formData"].validateField(`formData.dataSourceList.${index}.${key}`);
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1) {
            this.$message({
                message: '不得超过1个',
                type: 'error'
            })
            return false
            }
            return true
        },
        // 表格行选择
        rowSelectionChanged(rows) {
            this.formData.dataSourceList = rows;
        },
        // 下一步
        toNext(){
            if (this.formData.dataSourceList.length>0) {
                this.pageStep = 2
            }else {
                this.$message({
                    message: '请先选择人员',
                    type: 'error'
                })
                return false
            }
        },
        // 查询新入职人员列表
        handleTagsChange(){
            this.getList();
        },
        // 表格排序按钮搜索
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
        getList(){
            let self = this, postData = JSON.parse(JSON.stringify(self.listQuery));
            postData = self.assignSortObj(postData);
            console.log(postData)
            self.listLoading = true;
            EmployeeTrainingPlanApi.GetEmployeeList(postData).then(res => {
                self.tableData = res;
                self.listLoading = false;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        // 重置表单
        resetFormData() {
            this.pageStep = 1;
            this.listQuery = this.$options.data().listQuery;
            this.formData = this.$options.data().formData;
        },
        // 表单确认按钮
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData.dataSourceList));
                    console.log(postData)
                    self.disabledBtn = true;
                    EmployeeTrainingPlanApi.add({EmployeeTrainingPlanList:postData}).then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        // 获取表单详情
        getDetail() {
            // this.loading = true
            // EmployeeTrainingPlanApi.detail({ id: this.id }).then(res => {
            //     this.formData = Object.assign({}, this.formData, res);
            //     this.loading = false
            // }).catch(err => {
            //     this.loading = false
            // });
        },
        // 关闭弹窗
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapperBody{
    height: 400px;
}
</style>