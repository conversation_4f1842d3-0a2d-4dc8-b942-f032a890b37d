export const employeeWorkingStateEnum = [
    { label: '在职', value: 1, color: '#409EFF' },
    { label: '离职', value: 2, color: '#FF0000' },
    { label: '停薪留职', value: 3, color: '#FF9900' },
]


export const statusEnum = [
    { label: '启用', value: 1 },
    { label: '停用', value: 2 },
]



export const isMarriedEnum = [
    { label: '未婚', value: 1 },
    { label: '已婚', value: 2 },
    { label: '离异', value: 3 },
    { label: '丧偶', value: 4 },
]

export const educationBackgroundEnum = [
    { label: '初中', value: 1 },
    { label: '中专', value: 2 },
    { label: '高中', value: 3 },
    { label: '大专', value: 4 },
    { label: '本科', value: 5 },
    { label: '硕士', value: 6 },
    { label: '博士', value: 7 },
    { label: '博士后', value: 8 },
]


export const laborContractStateEnum = [
    { label: '有效', value: 1, color: '#70B603' },
    { label: '已到期', value: 2, color: '#F59A23' },
    { label: '已终止', value: 3, color: '#FF5757' },
    { label: '未签', value: 4, color: '#AAAAAA' },
    { label: '快到期', value: 5, color: '#027DB4' },
]

export const politicCountenanceEnum = [
    { label: '群众', value: 1 },
    { label: '党员', value: 2 },
]
export const birthEnum = [
    { label: '未育', value: 1 },
    { label: '已育', value: 2 },
]
export const educationEnum = [
    { label: '全日制', value: 1 },
    { label: '非全日制', value: 2 },
]
export const confidentialityAgreementEnum = [
    { label: '未签', value: 1 },
    { label: '已签', value: 2 },
    { label: '已终止', value: 3 },
]
export const cycleFrequencyEnum = [
    { label: '每隔5分钟', value: 1, second: 5 * 60},
    { label: '每隔15分钟', value: 2, second: 15 * 60},
    { label: '每隔30分钟', value: 3, second: 30 * 60 },
    { label: '每隔1小时', value: 4, second: 60 * 60 },
    { label: '每隔3小时', value: 5, second: 3 * 60 * 60 },
    { label: '每隔6小时', value: 6, second: 6 * 60 * 60 },
    { label: '每隔12小时', value: 7, second: 12 * 60 * 60 },
    { label: '每隔24小时', value: 8, second: 24 * 60 * 60 },
]
export const SurveyStateEnum = [
    { value: 1, label: '进行中', color: '#70B603', bgColor: 'rgba(112, 182, 3, 0.2)' }, // 绿色
    { value: 2, label: '已完成', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
    { value: 0, label: '全部'}, // 全部  查询的时候用  
]
export const SubmitStateEnum = [
    { value: 1, label: '未提交', color: '#F56C6C', bgColor: 'rgba(254, 240, 240, 1)' }, // 红色
    { value: 2, label: '已提交', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
    { value: null, label: '全部'}, // 全部  查询的时候用  
]
export const SurveyTypeEnum = [
    { label: '360度调查', value: 1},
]
export const viewRangeEnum = [
    { label: '所有人', value: 1},
    { label: '按部门', value: 2},
    { label: '自定义', value: 3},
]

export const contractTypeEnum = [
    { label: '固定期限', value: 1},
    { label: '无固定期限', value: 2},
]

export const QuestionAnswerType = [
    { label: '胸怀品德', value: 1},
    { label: '格局远见', value: 2},
    { label: '执行担当', value: 3},
    { label: '学习超越', value: 4},
]
export const QuestionAnswerData = [
    //Type: 1  胸怀品德
    { OptionIndex: 0, SubmitItemType: 1, Value: null, Title: '诚信正直，对待事情讲信用、讲道理。'},
    { OptionIndex: 1, SubmitItemType: 1, Value: null, Title: '面对问题、面对困难不抱怨不计较，永远保持正向阳光的初心。'},
    { OptionIndex: 2, SubmitItemType: 1, Value: null, Title: '有奉献精神，不计付出。'},
    { OptionIndex: 3, SubmitItemType: 1, Value: null, Title: '具有授权意识，管理活动中恰当处理授权和责任的关系，在授权前后进行有力的控制和管理。'},

    // Type: 2 格局远见
    { OptionIndex: 4, SubmitItemType: 2, Value: null, Title: '不局限于自己的业务线和部门，也有战略思维，综合考虑市场、公司、客户的需求，放眼中长期。'},
    { OptionIndex: 5, SubmitItemType: 2, Value: null, Title: '以客户为中心，客户的问题第一时间解决。'},
    { OptionIndex: 6, SubmitItemType: 2, Value: null, Title: '指愿意作为群体中的一个成员，与群体中的其他人一起协作完成任务，而不是单独地或采取竞争的方式从事工作。'},
    { OptionIndex: 7, SubmitItemType: 2, Value: null, Title: '为员工制定发展性绩效目标、对员工的工作进行恰当考核并积极反馈，采取激励制度。'},

    // Type: 3  执行担当
    { OptionIndex: 8, SubmitItemType: 3, Value: null, Title: '计划性强，保证工作的效率和效果。'},
    { OptionIndex: 9, SubmitItemType: 3, Value: null, Title: '具备较强的领导能力。'},
    { OptionIndex: 10, SubmitItemType: 3, Value: null, Title: '对问题、风险及时准确的分析判断。'},
    { OptionIndex: 11, SubmitItemType: 3, Value: null, Title: '注重共赢，善于在上下级之间、部门之间、企业内外间运用协调技巧周转运营。'},
    { OptionIndex: 12, SubmitItemType: 3, Value: null, Title: '决策能力强，当机立断，并对员工授权尺度松紧得当。'},
    { OptionIndex: 13, SubmitItemType: 3, Value: null, Title: '具有成本意识，并主动对影响成本的因素和条件进行预防和调节。'},
    { OptionIndex: 14, SubmitItemType: 3, Value: null, Title: '具有风险防范意识，对风险保持敏锐性，并且对风险有应对措施，提前规避风险，降低风险影响。'},
    { OptionIndex: 15, SubmitItemType: 3, Value: null, Title: '说到做到，承诺的事情，会自己克服困难达成目标。'},
    { OptionIndex: 16, SubmitItemType: 3, Value: null, Title: '以身作则，勇于担当，在员工中树立榜样。'},
    
    // 学习超越
    { OptionIndex: 17, SubmitItemType: 4, Value: null, Title: '有效的培养人，对佳运通的精神和知识的传承做出贡献，不断提升自己的专业知识的同时，善于和乐于分享知识，打造知识创新型企业。'},
    { OptionIndex: 18, SubmitItemType: 4, Value: null, Title: '喜欢尝试新事物，从新的角度去认识、组织信息，并形成对工作有改进和推动作用的新观点和新方法。推行必要的调整与改善，发现并落实解决组织内存在的不良问题，促进组织发展。'},
    { OptionIndex: 19, SubmitItemType: 4, Value: null, Title: '具备演讲技能，汇报、营销PPT设计和制作、推广、宣讲等。'},
]