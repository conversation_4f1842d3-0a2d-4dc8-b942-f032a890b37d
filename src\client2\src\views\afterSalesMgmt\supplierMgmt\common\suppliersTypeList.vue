<template>
<div class="product-list" v-loading='supplierLoading' style="width: 100%;">
    <no-data v-show="suppliersTypes.length == 0"></no-data>
    <tags mode='list' :items='suppliersTypes' v-model="currSuppliersId" style="padding-right: 5px;"></tags>
</div>
</template>

<script>
import noData from '../../../common/components/noData'
export default {
    name: 'suppliersType-list',
    components: {
        noData,
    },
    props: {
        currentSuppliersTypeId: {
            type: [Number, String],
            default: ''
        },
    },
    created() {
        this.currProductId = 1
        this.currSuppliersId = this.suppliersTypes[0].value
        //this.getSuppliersTypes()
    },
    watch: {
        // currProductId(val) {
        //     this.$emit('changed', this.currProductId, this.suppliersTypes.find(s => s.value == this.currProductId))
        // },
    },
    data() {
        return {
            filterText: '',
            currSuppliersId: this.currentSuppliersTypeId,
            supplierLoading: false,
            suppliersTypes: [{
                value: 1,
                label: "结构配件供应商"
            }], //集合
        }
    },
    methods: {

        getSuppliersTypes() {
            this.supplierLoading = true
            prod.getAllProducts({}).then(res => {
                this.supplierLoading = false
                this.products = res && res.map(p => {
                    return {
                        value: p.ProductId,
                        label: p.ProductName
                    }
                })
                //如果没选中，默认选中第一项
                if (!this.currProductId && this.products.length > 0) {
                    this.currSuppliersId = this.suppliersTypes[0].value
                }
            })

        },
    },
}
</script>

<style lang="scss" scoped>
.product-list {
    width: 100%;

    //text-align: center;
    // border-right: 1px solid #DCDFE6;
    .list-wrapper .tag {
        padding: 4px;
        cursor: pointer;
        border-radius: 5px;
        margin: 5px;
        height: 50px;
        padding-top: 15px;padding-left: 20px;
    }
}
</style>
