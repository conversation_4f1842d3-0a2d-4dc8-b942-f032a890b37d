<template>
    <div v-loading='loading'>
        <el-row class="group-wrapper" v-for="(i, idx) in datas" :key="idx" :label="i.EquipmentCategoryTypeName" :name="i.EquipmentCategoryTypeName">
            <div class="group-title">
                <h3>{{ i.EquipmentCategoryTypeName }}</h3>
            </div>
            <el-col :span='12' v-for="(c, idx) in i.GroupDetails" :key="idx" :title="c.Title" class="dyn-title">
                <el-form-item :label="c.Title" :prop="c.Title">
                    <template v-if="c.ControlType==1">
                        <el-select @change='handleChanged' :disabled="editable" v-model="c[c.bindKey]" filterable clearable placeholder="请选择">
                            <el-option
                                v-for="item in c.Details"
                                :key="item.EquipmentTypeSubtypeRelationId"
                                :label="item.EquipmentSubtypeName"
                                :value="item.EquipmentTypeSubtypeRelationId">
                            </el-option>
                        </el-select>
                    </template>
                    <template v-if="c.ControlType==2">
                        <el-date-picker @change='handleChanged' format='yyyy-MM-dd' value-format='yyyy-MM-dd' class="dat-ipt" v-model="c[c.bindKey]" type="date" placeholder="" :disabled="editable"></el-date-picker>
                    </template>
                </el-form-item>
            </el-col>
        </el-row>        
    </div>
</template>

<script>
import * as repair from '@/api/repairOrder'
import { JsonHubProtocol } from '@aspnet/signalr'
export default {
    name: 'dynamic-list',
    props: {
        //选中（设置）的值
        checked: {
            type: Object,
            default: null
        },
        //是否看编辑
        editable: {
            type: Boolean,
            default: true
        },
    },
    created() {
        this.getEquList()
    },
    data() {
        return {
            //动态属性列表
            datas: [],
            loading:false,
        }
    },
    methods: {
        handleChanged() {
            this.$emit('changed', this.getCheckEquList())
        },
        //获取动态属性列表
        getEquList() {
            this.loading=true;
            repair.getEquList().then(res => {
                this.loading=false;
                this.initEquListModel(res)
                this.datas = res
                this.setCheckedAttr()
            }).catch(err => {
                this.loading=false;
            })
        },
        //初始化动态表单型号
        initEquListModel(details) {
            if(details && details.length > 0){
                details.forEach(d => {
                    if(d.GroupDetails && d.GroupDetails.length > 0){
                        d.GroupDetails.forEach(c => {
                            //如果为下拉选择控件
                            let itemKey = this.getModelName(c.EquipmentTypeId)
                            // if(c.Details && c.Details.length > 0){
                                c['bindKey'] = itemKey
                                c[itemKey] = ''
                            // }
                        })
                    }
                });
            }
            let temp = JSON.stringify(details)
        },
        //设置用户已选中（设置）的值
        setCheckedAttr() {
            if(this.datas && this.datas.length > 0){
                this.datas.forEach(d => {
                    if(d.GroupDetails && d.GroupDetails.length > 0){
                        d.GroupDetails.forEach(c => {
                            let bindKeyTmp = this.getModelName(c.EquipmentTypeId)

                            if(c.ControlType == 2) { //如果是日期框
                                c[bindKeyTmp] = ''
                                if(this.checked && this.checked.EquipmentIdAndValueList && this.checked.EquipmentIdAndValueList.length > 0){
                                    let obj = this.checked.EquipmentIdAndValueList.find(o => o.EquipmentPropId == c.EquipmentTypeId)
                                    if(obj){
                                        c[bindKeyTmp] = obj.Value
                                    }
                                }
                            }

                            if(c.ControlType == 1 && c.Details && c.Details.length > 0){ //如果是下拉
                                c.Details.forEach(a => {
                                    //设置动态绑定key
                                    c.bindKey == bindKeyTmp 
                                    //为动态绑定key赋值
                                    if(this.checked && this.checked.EquipmentPropValueIds && this.checked.EquipmentPropValueIds.length > 0){
                                        let ifExists = this.checked.EquipmentPropValueIds.findIndex(b => a.EquipmentTypeSubtypeRelationId == b)
                                        if(ifExists > -1){
                                            c[bindKeyTmp] = a.EquipmentTypeSubtypeRelationId
                                            return false
                                        }
                                    }

                                })
                            }
                        })
                    }
                })
            }
        },
        //获取用户选择的“属性值”列表
        getCheckEquList() {
            // let list = []
            let list = {
                'EquipmentPropValueIds': [],
                "EquipmentIdAndValueList": [
                    // {"EquipmentPropId":"12321","Value":"1233"}
                ]
            }
            
            if(this.datas && this.datas.length > 0){
                this.datas.forEach(d => {
                    if(d.GroupDetails && d.GroupDetails.length > 0){
                        d.GroupDetails.forEach(c => {
                            let itemVal = c[c.bindKey]
                            if(c.ControlType == 1 && c.Details && c.Details.length > 0){
                                if(itemVal){
                                    list.EquipmentPropValueIds.push(itemVal)
                                }
                            }else if(c.ControlType == 2){
                                list.EquipmentIdAndValueList.push({
                                    "EquipmentPropId": c.EquipmentTypeId,
                                    "Value": itemVal
                                })
                            }
                        })
                    }
                });
            }
            return list
        },
        getModelName(equipmentTypeId) {
            if(equipmentTypeId){
                return 'a_' + equipmentTypeId.replace(/-/g,'_')
            }
        },  
    },
}
</script>

<style scoped>

.dyn-title >>> .el-form-item__label{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-wrapper{
    margin-bottom: 10px;
}

.group-title h3{
    position: relative;
    padding-left: 10px;
    height: 18px;
    margin-top: 0;
}

.group-title{
    padding-left: 14px;
}

.group-title h3::before{
    content: ' ';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -3px;
    background-color: #409eff;
}

.el-select{
    width: 100%;
}

</style>