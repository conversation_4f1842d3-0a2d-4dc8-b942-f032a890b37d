<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="部门管理" :subTitle="['企业组织结构及人员的管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>

                    <el-tree class="elTree" 
                    ref="tree" 
                    v-loading="treeLoading" 
                    :data="treeDatas" 
                    :node-key="rowKey" 
                    :default-expanded-keys="epKeys"
                    :filter-node-method="filterNode" 
                    :props="defaultProps" 
                    :expand-on-click-node="false" 
                    :highlight-current="true" 
                    :check-on-click-node="true"
                    @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <!-- el-tag 责任部门 宽度 60px -->
                            <span class="node-title" :title="node.label" :style="{width: getNodeWidth(node.level) + 'px'}">
                                <span class="title" :style="{width: `calc(100% - ${node.data.IsDutyDepartment=='1' ? 60 : 0}px)`}">
                                    {{ node.label }}
                                </span>
                                <span v-if="node.data.IsDutyDepartment=='1'">
                                    <el-tag type="warning">责任部门</el-tag>
                                </span>
                            </span>

                            <span class="node-btn-area">
                                <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                                        <el-dropdown-item v-show="node.level <= 5 && node.level != 2 && node.level != 1  && node.data.IsDutyDepartment=='0'" command="setDuty">设为责任部门</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 5 && node.level != 2 && node.level != 1  && node.data.IsDutyDepartment=='1'" command="unsetDuty">取消责任部门</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level < 5" command="add">添加子部门</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 5 && node.level != 1" command="update">修改名称</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 5 && node.level != 1" command="delete">删除</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level == 1" command="sort">排序调整</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <page-title :title="departmentInfo"></page-title>
                <div class="tagBox">
                    <tags :items='types' v-model="listQuery.WorkingState" @change="handleTagsChange">
                        <template v-for="t in types" :slot="t.value">
                            {{ t.label }}
                        </template>
                    </tags>
                </div>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                        <template slot="AvatarPath" slot-scope="scope">
                            <!-- <div style="width: 100%; height: 100%; display: flex; justify-content: space-between; align-items:center; height: 80px;">
                                <img :src="scope.row.AvatarPath || defavatar" style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 75px; height: 75px;" />
                            </div> -->

                            <div class="avatar-wrapper">
                                <el-avatar :key="scope.row.EmployeesId" fit='cover' :size="50" :src="scope.row.AvatarPath || defavatar"></el-avatar>
                            </div>
                        </template>

                        <template slot="Name" slot-scope="scope">
                            <div>
                                {{ scope.row.Name }}
                            </div>
                            <div v-if="scope.row.IsPrincipal">
                                <el-tag type="warning">负责人</el-tag>
                            </div>
                        </template>

                        <template slot="WorkingState" slot-scope="scope">
                            <span :style="{color: getStatus(scope.row.WorkingState)}">
                                {{ scope.row.WorkingState | workingStateFilter}}
                            </span>
                        </template>

                        <template slot="Status" slot-scope="scope">
                            <el-tag type="success" v-if="scope.row.Status == 1">{{ scope.row.Status | statusFilter}}</el-tag>
                            <el-tag type="danger" v-if="scope.row.Status == 2">{{ scope.row.Status | statusFilter}}</el-tag>
                        </template>

                        <template slot="Sex" slot-scope="scope">{{ scope.row.Sex | sexFilter}}</template>
                        
                        <template slot='DepartmentName' slot-scope="scope">
                            <div>
                                <!-- <app-tag-pure style="margin-right: 2px;" effect="dark" color="#FFAA00" text="主"></app-tag-pure> -->
                                <span>{{ scope.row.DepartmentName }}</span>
                            </div>
                            <!-- <div v-if="scope.row.DepartmentMinorName && scope.row.DepartmentMinorName.length > 0">
                                <span v-for="(item, idx) in scope.row.DepartmentMinorName">
                                    {{ getPartName(item) }}
                                    <template v-if="idx < scope.row.DepartmentMinorName.length - 1">、</template>
                                </span>
                            </div> -->
                        </template>

                        <template slot="DepartmentMinorName" slot-scope="scope">
                          <div v-for="(item, index) in scope.row.DepartmentMinorName" :key="index">
                            {{ item }}
                          </div>
                        </template>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="FuzzySearchString">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入姓名、手机号、工号、职位进行过滤"
                                        @clear='getList'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.FuzzySearchString"
                                    ></el-input>
                                </template>
                                <template slot="Sex">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.Sex" placeholder="">
                                        <el-option v-for="item in genders" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template>
                                <template slot="Status">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.Status" placeholder="">
                                        <el-option v-for="item in accStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template>

                                

                                <!-- <template slot="WorkingState">
                                    <el-select class="filter-item" style="width:100%" v-model="listQuery.WorkingState" placeholder="" clearable>
                                        <el-option v-for="item in employeeWorkingStateEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template> -->

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                                    <div class="btns-wrapper">
                                        <el-button v-if="btnAdd=='btnAdd'" type="primary" class="elButton" @click="handleDialog('create')">创建人员</el-button>

                                        <el-button v-if="btnSetPrincipal=='btnSetPrincipal'" type="primary" class="elButton" @click="handleSetPrincipal()">负责人设置</el-button>

                                        <el-button v-if="btnCancelPrincipal=='btnCancelPrincipal'" type="primary" class="elButton" @click="handleCancelPrincipal()">负责人取消</el-button>

                                        <el-button v-if="btnImport=='btnImport'" type="primary" class="elButton" @click="handleImport()">批量导入</el-button>

                                        <el-button v-if="btnExport=='btnExport'" type="primary" class="elButton" @click="handleExport()">批量导出</el-button>

                                        <el-button v-if="btnAdjustDepartment=='btnAdjustDepartment'" type="primary" class="elButton" @click="handleAdjustDepartment()">调整部门</el-button>

                                        <el-button v-if="btnBatchDel=='btnBatchDel'" type="primary" class="elButton" @click="handleTableDelete()">批量删除</el-button>
                                    </div>

                                </template>


                            </app-table-form>
                        </template>

                        

                        

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleReview(scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnResetPassword')" @click="handleResetPassword(scope.row)" :type="2" text="重置密码"></app-table-row-button>

                            <!-- <el-button v-show="btnMaintain=='btnMaintain'" type="text" @click="handleUpdate(scope.row)">编辑</el-button>
                                   <el-button v-show="btnMaintain=='btnMaintain'" type="text" @click="handleReview(scope.row)">详情</el-button>
                                <el-button v-show="btnMaintain=='btnMaintain'" type="text" @click="handleDelete(scope.row)" style="color:#F56C6C;">重置密码</el-button> -->
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 创建部门 -->
    <create-folder-page v-if="currentOptNode" @closeDialog="closeFolderDialog" @saveSuccess="handleFolderSaveSuccess" :dialogFormVisible="dialogFolderFormVisible" :dialogStatus="dialogFolderStatus" :node="currentOptNode"></create-folder-page>

    <!-- 添加/修改 成员 -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList" :selectTypeId="selectTypeId"></create-page>

    <!-- 补充人员资料 -->
    <supplement-page @closeDialog="closeSupplementDialog" @saveSuccess="handleSupplementSaveSuccess" :dialogFormVisible="dialogSupplementFormVisible" :dialogStatus="'update'" :id="supplementEmpId"></supplement-page>

    <!-- 调整部门 -->
    <adjust-department @closeDialog="departmentTreeClose" @saveSuccess="departmentTreeSave" :dialogFormVisible="dialogAdjustDepartmentVisible" :node="selectEmployeesIdList"></adjust-department>

    <!-- 导入 -->
    <import-page @closeDialog="importCloseDialog" @saveSuccess="importSaveSuccess" :dialogFormVisible="dialogImportVisible" @reload="getList" :selectTypeId="selectTypeId"></import-page>

    <!-- 部门树排序 -->
    <tree-sort
        @closeDialog="closeTreeSortDialog"
        @saveSuccess="handleTreeSortSaveSuccess"
        :dialogFormVisible="dialogTreeSortFormVisible"
        :treeDatas='treeDatas'
        :defaultProps='defaultProps'
        :rowKey='rowKey'
        :businessType='1'
        :defaultExpandedKeys="epKeys"
        @reload="getDepartments"
    >
    </tree-sort>
</div>
</template>

<script>
import {
    listToTreeSelect
} from "@/utils";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import {
    downloadFile
} from "@/utils/index";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as systemEmployee from "@/api/personnelManagement/systemEmployee";
import createFolderPage from "./createFolder";
import createPage from "./create";
import adjustDepartment from "./adjustDepartment";
import importPage from "./import";
import {
    employeeWorkingStateEnum,
    statusEnum
} from "../enum";
import treeSort from '../../common/treeSort'
import supplementPage from '../../personnelManagement/employeeRecord/create'
import { vars } from '../../salesMgmt/common/vars'


export default {
    name: "systemDepartment-setting",
    mixins: [indexPageMixin],
    components: {
        createFolderPage,
        createPage,
        adjustDepartment,
        importPage,
        treeSort,
        supplementPage,
    },
    props: {},
    filters: {
        workingStateFilter(status) {
            const statusObj = employeeWorkingStateEnum.find((s) => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "";
        },
        statusFilter(status) {
            const statusObj = statusEnum.find((s) => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "";
        },
        sexFilter(value) {
            let obj = vars.common.genders.find(s => s.value == value)
            if(obj) {
                return obj.label
            }
            return ''
        },

        // nodutyFilter(value) {
        //     let duty = value.split(",");
        //     return duty[0];
        // },

    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
        types() {
          let result = [
            { value: 1, label: "在职人员" },
          ]
          if(this.topBtns.find(s => s.DomId == 'view_resign')) {
            result.push({ value: 2, label: "离职人员" })
          }
          if(this.topBtns.find(s => s.DomId == 'view_all')) {
            result.push({ value: 0, label: "全部" })
          }
          return result;
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.DepartmentId = val.Id;
                    this.selectDepartmentName = val.DepartmentName;
                    this.getList();
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getDepartments();
        this.btnTextValue()
    },
    data() {
        return {
            genders: vars.common.genders,
            accStatus: [
                {value: 1, label: '启用'},
                {value: 2, label: '停用'},
            ],
            layoutMode: 'simple',
            rowKey: 'Id',

            defavatar: require("../../../assets/images/avatar3.png"),
            btnAddChildren: '',
            btnAdd: '',
            btnCancelPrincipal: '',
            btnSetPrincipal: '',
            btnImport: '',
            btnExport: '',
            btnAdjustDepartment: '',
            btnBatchDel: '',
            departmentInfo: "",
            selectDepartmentName: "",

            dialogAdjustDepartmentVisible: false,
            selectEmployeesIdList: [],

            dialogImportVisible: false,

            employeeWorkingStateEnum: employeeWorkingStateEnum,
            statusEnum: statusEnum,

            filterText: "",

            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",

            },
            tableSearchItems: [{
                    prop: "FuzzySearchString",
                    label: "",
                    mainCondition: true
                },
                {
                    prop: "Sex",
                    label: "性别",
                },
                {
                    prop: "Status",
                    label: "账号状态",
                },
                
                // {
                //     prop: 'WorkingState',
                //     label: '在职状态'
                // }
            ],
            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            dialogFolderFormVisible: false,
            dialogFolderStatus: "create",
            currentOptNode: null, //当前操作的文件夹节点（新增、编辑、删除）
            selectTypeId: "", //当前选中的类型ID

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "AvatarPath",
                        label: "头像",
                        width: 100
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Name",
                        label: "姓名",
                        showOverflowTooltip: true,
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Sex",
                        label: "性别",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Number",
                        label: "工号",
                        sortable: "custom",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "DepartmentName",
                        label: "主要部门",
                        showOverflowTooltip: true,
                        width: '160'
                    },
                    slot: true
                },

                {
                    attr: {
                        prop: "DepartmentMinorName",
                        label: "其它部门",
                        showOverflowTooltip: true,
                        width: '160'
                    },
                    slot: true
                },

                {
                    attr: {
                        prop: "JobName",
                        label: "职位",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Mobile",
                        label: "手机",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Email",
                        label: "邮箱",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "WorkingState",
                        label: "在职状态",
                        showOverflowTooltip: true,
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Status",
                        label: "状态",
                    },
                    slot: true,
                },
            ],
            listQuery: {
                DepartmentId: "",
                FuzzySearchString: "",
                WorkingState: 1,
                Sex: null,
                Status: null,
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,
            epKeys:[],

            dialogTreeSortFormVisible: false,

            dialogSupplementFormVisible: false,
            supplementEmpId: '',
        };
    },
    methods: {
        getPartName(val) {
            if(val && val.indexOf('/') > -1) {
                let idx = val.lastIndexOf('/')
                return val.substring(idx + 1)
            }
            return val
        },
        handleSupplementSaveSuccess() {
            this.getList()
            this.closeSupplementDialog()
        },
        closeSupplementDialog() {
            this.dialogSupplementFormVisible = false
        },
        handleSupplementDialog(id) {
            this.supplementEmpId = id
            this.dialogSupplementFormVisible = true
        },
        getStatus(status) {
            let obj =  this.employeeWorkingStateEnum.find((s) => s.value == status)
            if(obj) {
                return obj.color
            }
            return ''
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        handleTagsChange(){
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        getNodeWidth(level) {
            let def_width = 190
            let result = def_width - (level - 1) * 20
            return result <= 0 ? 50 : result
        },
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnAddChildren") {
                    this.btnAddChildren = "btnAddChildren"
                }

                if (item["DomId"] == "btnAdd") {
                    this.btnAdd = "btnAdd"
                }
                if (item["DomId"] == "btnSetPrincipal") {
                    this.btnSetPrincipal = "btnSetPrincipal"
                }
                if (item["DomId"] == "btnCancelPrincipal") {
                    this.btnCancelPrincipal = "btnCancelPrincipal"
                }
                if (item["DomId"] == "btnImport") {
                    this.btnImport = "btnImport"
                }
                if (item["DomId"] == "btnExport") {
                    this.btnExport = "btnExport"
                }
                if (item["DomId"] == "btnAdjustDepartment") {
                    this.btnAdjustDepartment = "btnAdjustDepartment"
                }

                if (item["DomId"] == "btnBatchDel") {
                    this.btnBatchDel = "btnBatchDel"
                }

            })
        },
        //获取成员列表
        getList() {
            if (this.checkedNode) {
                this.listLoading = true;
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                postData = this.assignSortObj(postData);
                systemEmployee
                    .getList(postData)
                    .then((res) => {
                        this.tabDatas = res.Items;
                        this.total = res.Total;
                        this.listLoading = false;
                        // this.departmentInfo = this.selectDepartmentName+ "（" + this.total + "）";
                        this.departmentInfo = this.selectDepartmentName;
                    })
                    .catch((err) => {
                        this.listLoading = false;
                    });
                
                this.getStatistics()
            }
        },

        getStatistics() {
            systemEmployee.getListByType({DepartmentId: this.listQuery.DepartmentId}).then(res => {
                this.types.forEach(item => {
                    let count = 0;
                    let tmp = res.find(s => s.value == item.value);
                    if (tmp) {
                        count = tmp.total;
                    }
                    if (item.label.indexOf("(") > -1) {
                        item.label = item.label.replace(/[0-9]+/gi, count);
                    } else {
                        item.label += `(${count})`;
                    }
                });
            })
        },

        //表格为按钮事件
        // onBtnClicked: function (domId) {
        //     switch (domId) {
        //         //添加
        //         case "btnAdd":
        //             this.handleDialog("create");
        //             break;
        //             //调整部门
        //         case "btnAdjustDepartment":
        //             if (this.multipleSelection.length < 1) {
        //                 this.$message({
        //                     message: "至少选择一个",
        //                     type: "error",
        //                 });
        //                 return;
        //             }
        //             this.handleAdjustDepartment(this.multipleSelection);
        //             break;
        //             //设为负责人
        //         case "btnSetPrincipal":
        //             if (this.multipleSelection.length < 1) {
        //                 this.$message({
        //                     message: "至少选择一个",
        //                     type: "error",
        //                 });
        //                 return;
        //             }
        //             this.handleSetPrincipal(this.multipleSelection);
        //             break;

        //             //取消负责人
        //         case "btnCancelPrincipal":
        //             if (this.multipleSelection.length < 1) {
        //                 this.$message({
        //                     message: "至少选择一个",
        //                     type: "error",
        //                 });
        //                 return;
        //             }
        //             this.handleCancelPrincipal(this.multipleSelection);
        //             break;
        //             //批量导入
        //         case "btnImport":
        //             this.selectTypeId = this.checkedNode.Id;
        //             this.dialogImportVisible = true;
        //             break;

        //             //批量导出
        //         case "btnExport":
        //             this.handleExport();
        //             break;

        //             //批量删除
        //         case "btnBatchDel":
        //             if (this.multipleSelection.length < 1) {
        //                 this.$message({
        //                     message: "至少删除一个",
        //                     type: "error",
        //                 });
        //                 return;
        //             }
        //             this.handleTableDelete(this.multipleSelection);
        //             break;
        //         default:
        //             break;
        //     }
        // },

        //重置密码
        handleResetPassword(rows) {
            this.$confirm("是否确认重置密码?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                systemEmployee
                    .resetPassword({
                        id: rows.EmployeesId,
                    })
                    .then(() => {
                        this.$notify({
                            title: "成功",
                            message: "重置成功",
                            type: "success",
                            duration: 2000,
                        });
                        this.getList();
                    });
            });
        },

        //调整部门
        handleAdjustDepartment() {
            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少选择一个",
                    type: "error",
                });
                return;
            }
            let ids = [];
            if (_.isArray(this.multipleSelection)) {
                ids = this.multipleSelection.map((u) => u.EmployeesId);
            } else {
                ids.push(this.multipleSelection.EmployeesId);
            }
            this.selectEmployeesIdList = ids;
            this.dialogAdjustDepartmentVisible = true;
        },

        //设为负责人
        handleSetPrincipal() {
            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少选择一个",
                    type: "error",
                });
                return;
            }

            let ids = [];
            if (_.isArray(this.multipleSelection)) {
                ids = this.multipleSelection.map((u) => u.EmployeesId);
            } else {
                ids.push(this.multipleSelection.EmployeesId);
            }

            systemEmployee
                .setPrincipal({
                    ids: ids,
                    isSet: true,
                })
                .then(() => {
                    this.$notify({
                        title: "成功",
                        message: "设置成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
        },

        //取消负责人
        handleCancelPrincipal() {
            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少选择一个",
                    type: "error",
                });
                return;
            }
            let ids = [];
            if (_.isArray(this.multipleSelection)) {
                ids = this.multipleSelection.map((u) => u.EmployeesId);
            } else {
                ids.push(this.multipleSelection.EmployeesId);
            }

            systemEmployee
                .setPrincipal({
                    ids: ids,
                    isSet: false,
                })
                .then(() => {
                    this.$notify({
                        title: "成功",
                        message: "取消成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
        },
        //批量导入
        handleImport() {
            this.selectTypeId = this.checkedNode.Id;
            this.dialogImportVisible = true;
        },
        //批量导出
        handleExport() {
             let postData = JSON.parse(JSON.stringify(this.listQuery));
                postData = this.assignSortObj(postData);
                postData.DepartmentId= this.checkedNode.Id;            
            systemEmployee           
                .startExportExcel(postData
                //     {
                //     id: this.checkedNode.Id,
                //     workingState:this.listQuery.WorkingState
                // }
                )
                .then((res) => {
                    downloadFile(res.Url);
                });
        },

        // 多行删除
        handleTableDelete() {
            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少删除一个",
                    type: "error",
                });
                return;
            }
            let ids = [];
            if (_.isArray(this.multipleSelection)) {
                ids = this.multipleSelection.map((u) => u.EmployeesId);
            } else {
                ids.push(this.multipleSelection.EmployeesId);
            }

            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                systemEmployee.del(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            });
        },

        // 弹出添加框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
            this.selectTypeId = this.listQuery.DepartmentId;
        },

        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.EmployeesId;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        //弹出详情框
        handleReview(row, optType = "detail") {
            this.id = row.EmployeesId;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        onResetSearch() {
            this.listQuery.FuzzySearchString = "";
            this.listQuery.Sex = null
            this.listQuery.Status = null
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        departmentTreeClose() {
            this.dialogAdjustDepartmentVisible = false;
        },
        departmentTreeSave() {
            this.getList();
            this.dialogAdjustDepartmentVisible = false;
        },

        importCloseDialog() {
            this.getList();
            this.dialogImportVisible = false;
        },
        importSaveSuccess() {
            this.getList();
            this.dialogImportVisible = false;
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(dialogStatus, empId) {
            this.getList();
            this.closeDialog();
            if(dialogStatus == 'create') {
                this.$confirm("是否继续完善个人档案信息？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.handleSupplementDialog(empId)
                });
            }
        },
        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            systemDepartment
                .getListByCondition(this.departmentListQuery)
                .then((res) => {
                    this.treeDatas = listToTreeSelect(res);
                    
                    if(this.treeDatas && this.treeDatas.length>0){
                        this.treeDatas.forEach(v => {
                            this.epKeys.push(v.Id);
                            // if(v.children.length>0){
                            //     v.children.forEach(v1 => {
                            //         this.epKeys.push(v1.Id);
                            //     })
                            // }

                        })
                    }
                    // epKeys
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },

        //左侧树操作菜单
        handleCommand(optType, node, data) {
            if (optType == "add") {
                this.handleCreateFolder(data);
            } else if (optType == "update") {
                this.handleCreateFolder(data, "update");
            } else if (optType == "delete") {
                this.handleDelete(node, data);
            } else if (optType == "setDuty") {
                this.handleSetDuty(node, optType);
            } else if (optType == "unsetDuty") {
                this.handleSetDuty(node, optType);
            } else if (optType == 'sort') {
                this.handleTreeSortDialog()
            }
        },
        // handleUNSetDuty(data, optType = "unsetDuty") {
        //     var id = data.data.Id
        //     systemDepartment
        //         .setDuty({
        //             id: id,
        //             type: 0
        //         })
        //         .then((res) => {
        //             this.$notify({
        //                 title: "提示",
        //                 message: "取消成功",
        //                 type: "success",
        //                 duration: 2000,
        //             });
        //             this.getDepartments();
        //         })
        //         .catch((err) => {});
        // },
        handleSetDuty(node, optType = "setDuty") {
            var id = node.data.Id
            let type = optType == "setDuty" ? 1 : 0
            this.treeLoading = true
            systemDepartment
                .setDuty({
                    id: id,
                    type: type
                })
                .then((res) => {
                    this.treeLoading = false
                    this.$notify({
                        title: "提示",
                        message: "设置成功",
                        type: "success",
                        duration: 2000,
                    });
                    node.data.IsDutyDepartment = type
                })
                .catch((err) => {
                    this.treeLoading = false
                });
        },

        // 添加/修改 子分类
        handleCreateFolder(data, optType = "create") {
            this.dialogFolderStatus = optType;

            this.currentOptNode = data;
            this.dialogFolderFormVisible = true;
        },

        //删除分类
        handleDelete(node, data) {

            this.$confirm(`是否删除部门?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                systemDepartment
                    .del({
                        id: data.Id,
                    })
                    .then((res) => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }

                        //前端删除，维持树节点展开状态
                        const parent = node.parent;
                        const children = parent.data.children || parent.data;
                        const index = children.findIndex(d => d.Id === data.Id);
                        children.splice(index, 1);
                        
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000,
                        });
                    });
            });
        },

        //关闭树菜单的操作层
        closeFolderDialog() {
            this.dialogFolderFormVisible = false;
        },

        //树菜单操作保存
        handleFolderSaveSuccess(resultData) {
            if(resultData) {
                if(this.currentOptNode) {
                    if(resultData.dialogStatus == 'update') {
                        this.currentOptNode.DepartmentName = resultData.data.DepartmentName
                    }

                    if(resultData.dialogStatus == 'create') {
                        const newChild = resultData.data
                        newChild.Id = newChild.DepartmentId

                        if (!this.currentOptNode.children) {
                            this.$set(this.currentOptNode, 'children', []);
                        }
                        this.currentOptNode.children.push(newChild);
                    }
                }
            }
            this.closeFolderDialog();
            
            ////改为前端动态添加/修改，不刷新树，保持树原有状态
            // this.getDepartments();
        },

        /**
         * 设备参数设置
         */
        handleTreeSortDialog() {
            this.dialogTreeSortFormVisible = true
        },
        closeTreeSortDialog() {
            this.dialogTreeSortFormVisible = false
        },
        handleTreeSortSaveSuccess() {
            this.closeTreeSortDialog();
        },
    },
};
</script>

<style lang="scss" scoped>
.content{
    padding-top: 0!important;
    .btns-wrapper{
        button{
            margin-left: 4px;
        }
    }
    .avatar-wrapper{
        width: 50px;
        height: 50px;
        /deep/img{
            width: 100%;
            height: 100%;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}
.treeBox {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        flex: 1;
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;
    min-width: 1400px;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;


        .content {
            padding: 10px;
            padding-right: 0;
            padding-left: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: flex;
            .title{
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

</style>
