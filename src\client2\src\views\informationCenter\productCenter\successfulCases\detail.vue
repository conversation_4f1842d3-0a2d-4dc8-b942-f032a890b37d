<template>
<div class="news-wrapper">
    <!-- <div class="opt">
        <i class="el-icon-back" @click="back">返回</i>
    </div> -->
    <page-title :showBackBtn='true' @goBack="back">
        <div slot="def">
            
        </div>
    </page-title>
    <div style="display:flex;height:calc(100% - 40px);overflow-y: auto;">
        <div style="width:65%; padding-left: 10px;" v-loading="loading">
            <div class="news-container">
                <header style="font-size: 25px;padding-top: 5px;">{{ newDetail.CaseName }}</header>
                <div style="padding-left: 20px;padding-bottom: 5px;">
                    <span>{{newDetail.CreateEmployee | nameFilter}}</span>&nbsp;&nbsp;
                    <span>{{newDetail.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div class="hr" style="margin: 0px;">
                    <span class="hrl"></span>
                    <span class="hrc"></span>
                    <span class="hrr"></span>
                </div>
                <div class="divUeditor ql-editor" v-html="newDetail.CaseDescribe"></div>
            </div>

        </div>

        <el-row style="margin-left: 6%; padding-top: 10px;flex:1;" v-loading="successfulCasesListLoading">
            <!-- 成功案例 -->
            <relate-list :list='successfulCasesDatas' @click="handleSuccessfulCasesReview"></relate-list>
        </el-row>

    </div>

</div>
</template>

<script>
import * as successfulCases from '@/api/informationCenter/successfulCases'
import relateList from '../../common/relateList'
import dayjs from 'dayjs'
export default {
    name: 'successfulCases-detail',
    components: {
        relateList,
    },
    data() {
        return {
            newDetail: {},
            successfulCasesDatas: [],
            successfulCasesListLoading: false,
            successfulCaseslistQuery: {
                id: null,
            },
        }
    },
    created() {
        this.getNewDetail();
        this.getSuccessfulCasesList();
    },
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
    },
    methods: {
        handleSuccessfulCasesReview(row) {
            //查看详情
            successfulCases.detail({
                id: row.Id
            }).then(res => {
                this.newDetail = res
                //获取
                this.successfulCasesListLoading = true
                this.successfulCaseslistQuery.id = row.Id
                successfulCases.getList(this.successfulCaseslistQuery).then(response => {
                    this.successfulCasesDatas = response.Items.map(s => {
                        s.Text = s.CaseName
                        return s
                    })
                    this.successfulCasesListLoading = false
                })
            })
        },
        getSuccessfulCasesList() {
            this.successfulCasesListLoading = true
            let id = this.$route.params && this.$route.params.id
            this.successfulCaseslistQuery.id = id;
            successfulCases.getList(this.successfulCaseslistQuery).then(response => {
                this.successfulCasesDatas = response.Items.map(s => {
                    s.Text = s.CaseName
                    return s
                })
                this.successfulCasesListLoading = false
            })
        },
        getNewDetail() {
            let id = this.$route.params && this.$route.params.id
            successfulCases.detail({
                id: id
            }).then(res => {
                this.newDetail = res
            })
        },
        back() {
            this.$router.go(-1)
        },
    }
}
</script>

<style scoped>

.divUeditor >>> pre{
    white-space: pre-wrap!important; /* css-3 */
    white-space: -moz-pre-wrap!important; /* Mozilla, since 1999 */
    white-space: -pre-wrap!important; /* Opera 4-6 */
    white-space: -o-pre-wrap!important; /* Opera 7 */
    word-wrap: break-word!important; /* Internet Explorer 5.5+ */
}

.news-wrapper {
    padding: 60px 40px 0;
    overflow: hidden;
    background: #ffffff;
    margin: 10px;
    position: absolute;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    background: white;
    /* overflow: auto; */
}

.news-container {
    /*max-width: 1080px;*/
    margin: 0 auto;
    padding-bottom: 20px;
    /* text-align: center; */
}

img,
video {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-top: 0.4rem;
}

header {
    font-size: 36px;
    color: #373c41;
    font-weight: bold;
    /* text-align: center; */
    margin-bottom: 10px;
}

aside {
    font-size: 16px;
    color: #8d8f91;
    margin-top: 30px;
}

article p {
    font-size: 0.42rem;
    color: #373c41;
    margin-top: 0.4rem;
    line-height: 0.67rem;
    text-indent: 2em;
}

.hr {
    width: 100%;
    margin: 30px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hr span {
    background-color: #e3e3e5;
}

.hrl,
.hrr {
    width: calc(50% - 5px);
    height: 1px;
}

.hrc {
    width: 3px;
    height: 3px;
    border-radius: 3px;
}

@media screen and (min-width: 1080px) {
    body {
        width: 1080px;
        margin: 0 auto;
    }
}

.divUeditor {
    overflow: hidden;
    display: block;
    width: 100%;
    min-width: 90%;
    position: relative;
    word-wrap: break-word;
    padding: 12px 20px;
    padding-bottom: 20px;
}

.divUeditor img {
    border: 0;
    max-width: 100%;
    margin-top: 10px;
}



.down-wrapper a {
    color: #409eff;
    line-height: 140%;
}

.down-wrapper p {
    margin: 0;
    margin-bottom: 10px;
}

.opt {
    top: 0;
    left: 0;
    right: 0;
    position: absolute;
    /* cursor: pointer;

    i {
        font-size: 14px;
    } */
}

/* .opt:hover {
    color: rgb(64, 158, 255);
} */
</style>
