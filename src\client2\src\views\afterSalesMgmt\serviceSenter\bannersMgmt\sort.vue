<template>
    <div>
        <app-dialog title="调整广告排序" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='800'>
            <template slot="body">
                <div class="list-wrapper" v-loading='loading'>
                    <div class="item-wrapper" 
                        v-for="(item, idx) in list" :key="idx" 
                        v-dragging="{ item: item, list: list, group: 'idx' }"
                    >
                        <div class="img">
                            <div class="img-wrapper" v-viewer>
                                <img class="banner-img" :src="item.ImageUrl" :alt="item.Title">
                            </div>
                        </div>
                        <div class="content">
                            <div class="title">
                                标题：{{ item.Title }}
                            </div>
                            <div>
                                跳转URL：{{ item.RedirectUrl }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
  import * as banner from "@/api/maintenanceCenter/banner";
  export default {
    name: "banners-sort",
    directives: {},
    components: {
      // tabs,
      // tags,
    },
    mixins: [],
    props: {

    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList();
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
    },
    created() {
        this.getList()

    },
    mounted() {
        this.$dragging.$on('dragged', ({ value }) => {
            console.log(value.item)
            console.log(value.list)
            console.log(value.otherData)
        })
        this.$dragging.$on('dragend', () => {

        })
    },
    data() {
        return {
            list: [],
            loading: false,
            disabledBtn: false,
        };
    },
    methods: {
        getList() {
            this.loading = true
            // banner.getList({PageIndex: 1, PageSize: 20, Status: 1}).then(res => {
            //     this.loading = false
            //     this.list = res.Items
            // }).catch(err => {
            //     this.loading = false
            // })

            banner.getListByStatus({}).then(res => {
                this.loading = false
                this.list = res
            }).catch(err => {
                this.loading = false
            })
        },
        createData() {
            let len = this.list.length
            let postDatas = this.list.map((s, idx) => {
                return {
                    Id: s.Id,
                    OrderIndex: (len - idx) * 10
                }
            })

            this.disabledBtn = true
            banner.sort(postDatas).then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.disabledBtn = false
                this.$refs.appDialogRef.createData();
            }).catch(err => {
                this.disabledBtn = false
            });
        },
        handleFilesUpChange(files) {
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
  };
</script>

<style lang="scss" scoped>

.list-wrapper{
    min-height: 500px;
    padding: 10px 0;
    .item-wrapper{
        // height: 50px;
        border: 1px solid #EBEEF5;
        display: flex;
        .img{
            width: 200px;
            box-sizing: border-box;
            padding: 10px;
            .img-wrapper{
                overflow: hidden;
                height: 0;
                // width: 200px;
                padding-bottom: 38%; /* 图片的高/宽 */
                .banner-img{
                    width: 100%
                }
            }
        }
        .content{
            flex: 1;
            padding: 10px 10px 10px 0;
            display: flex;
            flex-direction: column;
            .title{
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 10px;
            }
        }
    }
    .item-wrapper:not(:last-child){
        margin-bottom: 20px;
    }
}

</style>
