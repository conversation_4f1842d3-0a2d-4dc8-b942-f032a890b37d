<template>
  <div>
    <div style="display: flex; box-shadow: 0 1px 130px #f1f2f4;">
      <el-menu
        class="navbar clearfix"
        mode="horizontal"
        :default-active="currentMenu"
        @select="handleSelect"
      >
        <!-- 隐藏、显示左侧菜单按钮 -->
        <!-- <hamburger style="display: none;" class="hamburger-container" :toggleClick="toggleSideBar" :isActive="sidebar.opened"></hamburger> -->

        <!-- 还原把隐藏代码去掉 -->
        <hamburger
          v-show="showSidebar"
          class="hamburger-container"
          :toggleClick="toggleSideBar"
          :isActive="sidebar.opened"
        ></hamburger>

        <!-- <div class="sidebar-logo">
            <router-link to="/">
                <img class="user-avatar" :src="logo"><span class="sidebar-company-name">{{ websiteTitle }}</span>
            </router-link>
            </div>-->
        <!-- <div style="display: flex;">

            </div> -->
        <el-menu-item :index="menu" v-for="(menu, idx) in menus" :key="idx">
          {{ menu }}
        </el-menu-item>

        <!-- <breadcrumb></breadcrumb> -->
        <!-- <el-dropdown class="lang-container" trigger="click">
            <div class="avatar-wrapper">

        {{ currentLang }}
                <i class="el-icon-caret-bottom"></i>
            </div>
            <el-dropdown-menu class="user-dropdown" slot="dropdown">

        <el-dropdown-item>

        <span @click="switchLang('cn')" style="display:block;">{{ $t('common.chinese') }}</span>

        </el-dropdown-item>
                <el-dropdown-item divided>
                <span @click="switchLang('en')" style="display:block;">{{ $t('common.english') }}</span>
                </el-dropdown-item>
            </el-dropdown-menu>
            </el-dropdown>-->

        <!-- 消息中心 -->
        <!-- <div class="avatar-container">
            <div class="avatar-wrapper">
                <router-link to="/workbench/myMessage/index">
                <el-badge :value="badgeNumber">
                    <img src="../../../assets/images/message.png" @click="messageClick()" />
                </el-badge>
                </router-link>
            </div>
            </div>-->
        <!-- 创建 -->
        <create-page
          @closeDialog="closeDialog"
          @saveSuccess="handleSaveSuccess"
          :dialogFormVisible="dialogFormVisible"
          :dialogStatus="dialogStatus"
          :id="id"
        ></create-page>
      </el-menu>

      <div style="flex: 1;">
        <div style="display: flex; justify-content: flex-end; align-items: center; height: 100%;">
          
          <span class="common-msg">
            
            <span class="more-btn-wrapper">
              <el-dropdown 
                :hide-on-click="false"
                trigger="hover"
                placement="bottom"
                @command="handleCommand2($event)"
                @visible-change="moreButtonChagned"
              >
                <div class="more-btn-inner-wrapper el-badge item">
                  <i 
                  style="margin-top: 2px;"
                  :class="
                    isShowMoreButtonFlag > 0 && isShowMoreButtonFlag % 2 == 1
                      ? 'active'
                      : isShowMoreButtonFlag > 0 && isShowMoreButtonFlag % 2 == 0
                      ? 'unactive'
                      : ''
                  "
                  class="more-btn el-icon-circle-plus color-primary"></i>

                  <!-- <svg-icon 
                    icon-class="tianjia1" 
                    style="margin-top: 4px;"
                    :class="
                      isShowMoreButtonFlag > 0 && isShowMoreButtonFlag % 2 == 1
                        ? 'active'
                        : isShowMoreButtonFlag > 0 && isShowMoreButtonFlag % 2 == 0
                        ? 'unactive'
                        : ''
                    "
                   className="more-btn color-primary"
                  ></svg-icon> -->

                   
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="item in hypertrunkTaskTypes"
                    :key="item.value"
                    :command="item.value"
                    :divided="item.value >= 100 ? true : false"
                  >
                    <div class="drop-item-wrapper">
                      
                      <taskTypeIcon :taskType="item.value" v-if="item.value <= 60"></taskTypeIcon>

                      <svg-icon
                        icon-class="renwumoban"
                        v-else-if="item.value == 100"
                        className="icon-mini btn-icon color-purple"
                      ></svg-icon>
                      <svg-icon
                        icon-class="renwuzhipaidan"
                        v-else-if="item.value == 200"
                        className="icon-mini btn-icon color-primary"
                      ></svg-icon>
                      <svg-icon
                        icon-class="复盘管理"
                        v-else-if="item.value == 300"
                        className="icon-mini btn-icon color-primary"
                      ></svg-icon>
                      <span style="margin-left: 5px;">{{ item.label }}</span>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>


            <el-popover title="请用微信扫码下载APP" width="240" trigger="hover" placement="bottom">
              <div>
                <!-- 二维码生成地址：https://cli.im/url -->
                <!-- 字符：https://oa.jiayuntong.com:7777/#/download -->
                <img
                  style="width: auto; height: auto; max-width: 100%; max-height: 100%; "
                  src="../../../assets/images/app-download-logo-175.png"
                />
              </div>
              <div
                slot="reference"
                title="扫码下载"
                class="el-badge item"
                style="color: #909399; font-size: 18px;"
              >
                <svg-icon icon-class="app-download" style="margin-top: 4px;"></svg-icon>
              </div>
            </el-popover>

            <span @click="handleNavApproval" title="待我审批">
              <el-badge
                :value="waitApprovalNum > 99 ? '99+' : waitApprovalNum"
                class="item"
                style="color: #909399; font-size: 18px;"
              >
                <svg-icon icon-class="我的审批" style="margin-top: 4px;"></svg-icon>
              </el-badge>
            </span>
            <span @click="handleNavMessage" title="未读消息">
              <el-badge
                :value="unreadNum > 99 ? '99+' : unreadNum"
                class="item"
                style="color: #909399; font-size: 18px;"
              >
                <svg-icon icon-class="我的消息" style="margin-top: 4px;"></svg-icon>
              </el-badge>
            </span>
          </span>
        </div>
      </div>

      <el-dropdown class="avatar-container" trigger="click" @command="handleSelect2">
        <div class="avatar-wrapper">
          <!-- <img class="user-avatar" :src="logo"> -->
          <img class="user-avatar" :src="_avatar" />
          {{ _username }}
          <i class="el-icon-caret-bottom"></i>
        </div>
        <el-dropdown-menu class="user-dropdown" slot="dropdown" style="text-align: center;">
          <!-- <router-link class="inlineBlock" to="/">
            <el-dropdown-item>首页</el-dropdown-item>
            </router-link>-->

          <!-- <router-link to="/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>-->
          <!-- <router-link to="/profile/profiles">
                    
                </router-link> -->

          <el-dropdown-item :command="{ path: '/profile/profiles', text: '个人信息' }">
            个人信息
          </el-dropdown-item>

          <!-- <el-dropdown-item :command='{path: "/card", text: "名片管理"}'>名片管理</el-dropdown-item> -->

          <!-- <el-dropdown-item style="padding: 0px 10px;height: 42px;" divided>
                    <span @click="handleCreate" style="display:block;">意见反馈</span>
                </el-dropdown-item> -->

          <el-dropdown-item :command="{ name:'productGuide',text: '产品指南'}">产品指南</el-dropdown-item>
          <el-dropdown-item :command="{ path: '/version', text: '关于平台' }">关于平台</el-dropdown-item>

          <!-- <router-link to="/version">
                </router-link> -->

          <el-dropdown-item style="padding: 0px 10px;height: 42px;" divided>
            <span @click="logout" style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";

// import logo from '@/assets/logo.png?imageView2/1/w/80/h/80'
import {getUserInfo, getCookie, getToken} from "@/utils/auth";
import { websitEnv } from "@/utils/env";
import createPage from "../../systemManagement/FeedbacksManagement/create";
import * as mainLineVars from "../../workbench/mainLineMgmt/vars";
// import * as systemMessage from "@/api/systemMessage/systemMessage";
// import * as approval from "@/api/projectDev/projectMgmt/approvalSetting";
import taskTypeIcon from '@/views/workbench/mainLineMgmt/components/taskTypeIcon'

export default {
  data: function() {
    return {
      // unreadNum: 0, //未读
      // approvalNum: 0, //待审批
      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,
      // logo: logo
      websiteTitle: websitEnv.websiteTitle,
      logo: require("@/assets/logo.png?imageView2/1/w/80/h/80"),
      badgeNumber: 0,
      currentMenu: "首页",
      isShowMoreButtonFlag: 0,
      hypertrunkTaskTypes: mainLineVars.vars.hypertrunkTaskTypes.concat([
        { value: 100, label: "模板创建任务" },
        { value: 200, label: "任务指派单" },
        { value: 300, label: "创建复盘" },
      ]),
    };
  },
  components: {
    Breadcrumb,
    Hamburger,
    createPage,
    taskTypeIcon,
  },
  created() {
    // this.getSubordinateModuleList();
    // this.getProjectGroupTypeCount();
    this.$store.dispatch("RefreshUnreadNum");
    this.$store.dispatch("RefreshWaitApprovalNum");
    this.badgeNumber = this.$store.state.BadgeNumber;
  },
  mounted() {
    let currentRoute = this.$route.matched.find(s => !s.path);
    if (currentRoute) {
      this.currentMenu = currentRoute.name;
      this.$store.commit("SET_CURRENT_MENU_GROUP_NAME", this.currentMenu);
    }

    // let menus = this.permission_routers.filter(s => s.hidden === false)
  },
  watch: {
    $route(to, from) {
      if (to.matched && to.matched.length > 0) {
        let groupName = to.matched.find(s => !s.path);
        if (groupName) {
          let name = groupName.name;
          this.currentMenu = name;
          this.$store.commit("SET_CURRENT_MENU_GROUP_NAME", this.currentMenu);
        } else {
          this.$store.commit("SET_CURRENT_MENU_GROUP_NAME", this.currentMenu);
        }
      }
    },
    //监听消息中心未读消息数量
    "$store.state.BadgeNumber": function(params) {
      this.badgeNumber = this.$store.state.BadgeNumber;
    },
  },
  computed: {
    ...mapGetters([
      "sidebar",
      "permission_routers",
      "currentMenuGroupName",
      "waitApprovalNum",
      "unreadNum",
    ]),
    menus() {
      return this.permission_routers.filter(s => s.hidden === false && s.isMenu).map(s => s.name);
    },
    _avatar() {
      let userinfo = this.$store.getters.userInfo;
      var avatarImgUrl = require("../../../assets/images/avatar3.png");
      if (userinfo) {
        return userinfo.avatar ? userinfo.avatar : avatarImgUrl;
      } else {
        var imgUrl = getUserInfo().avatar;
        return imgUrl ? imgUrl : avatarImgUrl;
      }
    },
    _username() {
      let userinfo = this.$store.getters.userInfo;
      if (userinfo) {
        return userinfo.username;
      } else {
        return getUserInfo().username;
      }
      // let user = this.$store.getters.userInfo.username
      // let userinfo = user || getUserInfo();
      // return userinfo;
    },
    currentLang() {
      if (this.$i18n.locale == "en") {
        return this.$t("common.english");
      } else {
        return this.$t("common.chinese");
      }
    },
  },
  methods: {
    moreButtonChagned(show) {
      this.isShowMoreButtonFlag++;
    },
    // 点击开始复盘
    handleCreateRet() {
      const dialogParams = {
        autoOpen: true, //自动打开弹框
        isHandle: false, //当前弹框是否已经处理（点击查看）——用于防止关闭后，再次点击查看，打开相同弹框
      };
      const navUrl = "/workbench/retrospective/taskRetrospective?selectRet=1";
      this.$router.push({ path: navUrl });
      this.$store.commit("SET_autoOpenDialogList", { navUrl, list: [dialogParams] });
    },

    // getSubordinateModuleList() {
    //     systemMessage.getSubordinateModuleList().then((res) => {
    //         let obj = res.find((s) => s.value == 0);
    //         if (obj) {
    //             this.$store.commit('SET_unreadNum', obj.unreadNum)
    //         }
    //     });
    // },
    // getProjectGroupTypeCount() {
    //     approval.getStatistics({}).then((res) => {
    //         let obj = res.find((s) => s.Type == 1);
    //         if (obj) {
    //             this.$store.commit('SET_waitApprovalNum', obj.Tota)
    //         }
    //     });
    // },
    handleNavApproval() {
      this.$router.push({
        path: "/workbench/myApproval?returnUrl=" + encodeURI("/workbench/myWorkbench"),
      });
    },
    handleNavMessage() {
      this.$store.commit("setIsRefreshMessage", 1);
      // this.$router.push({
      //     path:
      //     "/workbench/myMessage?returnUrl=" +
      //     encodeURI("/workbench/myWorkbench"),
      // });
    },
    //创建
    handleCreate() {
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.closeDialog();
    },
    handleSelect2(indexObj) {
      if (indexObj) {
        // 产品指南
        if(indexObj.name === 'productGuide'){
          const href = 'https://oa.jiayuntong.com:86?token='+encodeURIComponent(getToken())
          window.open(href, '_blank');
        }else{
          this.$router.push(indexObj.path);
          this.currentMenu = indexObj.text;
          this.$store.commit("SET_CURRENT_MENU_GROUP_NAME", this.currentMenu);
        }
      }
    },
    handleSelect(index, indexPath) {
      this.currentMenu = index;
      this.$store.commit("SET_CURRENT_MENU_GROUP_NAME", this.currentMenu);
    },
    //消息图标点击事件
    // messageClick() {
    //     // if (this.$route.path == "/message/index") {
    //     //修改消息图标点击标识 触发刷新页面的监听事件

    //     this.$nextTick(function () {
    //         //修改消息图标点击标识 触发刷新页面的监听事件
    //         this.$store.commit("setIsRefreshMessage", 1);
    //     });
    //     // }
    // },

    toggleSideBar() {
      this.$store.dispatch("ToggleSideBar");
    },
    logout() {
      this.$store.dispatch("FedLogOut").then(() => {
        location.reload(); // 为了重新实例化vue-router对象 避免bug
      });
    },
    switchLang(lang) {
      localStorage.lang = lang;
      this.$i18n.locale = lang;
    },

    handleCommand2(taskType) {
      switch (taskType) {
        case 10:
        case 20:
        case 30:
        // case 40:
        case 50:
        case 60:
          this.$store.commit("TASK_SET_OPEN_DIALOG", {
            busType: "task", //任务弹框
            optType: "create", //创建任务
            params: {
              hypertrunkTaskType: taskType,
            },
          });
          break;
        case 100:
          this.$store.commit("TASK_SET_OPEN_DIALOG", {
            busType: "temp", //任务模板弹框
            optType: "create", //从模板创建任务
          });
          break;
        case 200:
          this.$store.commit("TASK_SET_OPEN_DIALOG", {
            busType: "taskAssign", //任务指派单弹框
            optType: "create", //创建任务指派单
          });
          break;
        case 300:
          this.handleCreateRet();
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  border-bottom: 0 !important;
  // background-color: #333;
  // display: flex;

  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  /deep/.el-menu-item {
    padding: 0 15px;
  }
  .hamburger-container {
    line-height: 64px;
    height: 64px;
    // float: left;
    // padding: 0 10px;
    // color: white;
    // flex: 1;
    float: left;
  }

  /deep/.el-menu-item.is-active {
    font-weight: bold;
    color: $text-link-color;
    border-bottom: 2px solid $text-link-color;
  }
  // .screenfull {
  //     position: absolute;
  //     right: 90px;
  //     top: 16px;
  //     // color: red;
  // }
}

.avatar-container,
.lang-container {
  height: 64px;
  display: inline-block;
  // position: absolute;
  // float: right;
  line-height: 64px;

  // right: 65px;
  // color: white;
  .avatar-wrapper {
    cursor: pointer;
    position: relative;
    margin-left: 10px;
    // .el-icon-caret-bottom {
    //     position: absolute;
    //     right: -22px;
    //     top: 22px;
    //     font-size: 16px;
    // }
  }
}

.avatar-container {
  margin-right: 20px;
  float: right;
}

.user-avatar {
  width: 40px;
  height: 40px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
}
.user-dropdown {
  overflow: hidden !important;
}
.user-dropdown ::v-deep .el-dropdown-menu__item,
a .el-dropdown-menu__item {
  line-height: 32px !important;
  font-size: 14px;
  //padding-right: 30px;
}

.common-msg {
  font-weight: 300;
  font-size: 14px;
  span {
    cursor: pointer;
    display: inline-block;
    line-height: 16px;
    margin-right: 20px;
    padding: 2px;
  }
  // span:hover {
  //     background: #f2f8fe;
  // }
}
</style>

<style scoped lang="scss">
.avatar-wrapper >>> .el-badge__content.is-fixed {
  position: absolute !important;
  top: 15px !important;
  right: 10px !important;
  -webkit-transform: translateY(-50%) translateX(100%) !important;
  transform: translateY(-50%) translateX(100%) !important;
}

.drop-item-wrapper {
  border: none;
  user-select: none;
  display: flex;
  align-items: center;
}

.more-btn-wrapper {
  cursor: pointer;
  .more-btn-inner-wrapper {
    cursor: pointer;

    .more-btn {
      outline: none;
      font-size: 18px;
      transition: all 0.3s;
      border-radius: 100%;
      // color: #fff;
      &.active {
        transform: rotate(45deg);
      }
      &.unactive {
        transform: rotate(-90deg);
      }
      &:focus {
        outline: none;
      }
    }
  }
}
</style>
