<template>
    <div class="news-wrapper">
        <div class="news-container">
            <header>{{ newDetail.Title }}</header>
            <aside>{{ newDetail.UpdateTime }} 日更新</aside>
            <div class="hr">
                <span class="hrl"></span>
                <span class="hrc"></span>
                <span class="hrr"></span>
            </div>
            <!-- <img :src="newDetail.Image"> -->
            <div id="videoPlay" style="width: 100%; height: 100%;"></div>
            <div class="divUeditor" v-html="newDetail.Contents"></div>
        </div>
    </div>
</template>

<script>
import * as news from '@/api/news'
import dayjs from 'dayjs'
export default {
    name: 'new-detail',
    data() {
        return { 
            newDetail: {}
        }
    },
    created() {
        this.getNewDetail()
    },
    methods: {
        getNewDetail() {
            let id = this.$route.params && this.$route.params.id
            news.detail({newId: id}).then(res => {
                this.newDetail = res
                if(this.newDetail && this.newDetail.UpdateTime){
                    this.newDetail.UpdateTime = dayjs(this.newDetail.UpdateTime).format('YYYY-MM-DD')
                }

                this.$nextTick(() => {
                    var vidoes = $('video')
                    if(vidoes && vidoes.length > 0){
                        for (var v = 0; v < vidoes.length; v++) {
                            let video_obj = $(vidoes[v])
                            if(video_obj && video_obj.length > 0){
                                video_obj.get(0).setAttribute('width', '100%');   
                            }
                        }
                    }

                    var imgs = $('img')
                    if(imgs && imgs.length > 0){
                        for (var v = 0; v < imgs.length; v++) {
                            let img_obj = $(imgs[v])
                            if(img_obj && img_obj.length > 0){
                                img_obj.get(0).setAttribute('width', '100%');   
                            }
                        }
                    }                    
                })
            })
        }
    }
}
</script>

<style scoped>

.news-wrapper{
    padding: 60px 40px 0;
    /* overflow: hidden; */
    background: #ffffff;
    margin: 10px;
}

.news-container{
    max-width: 1080px;
    margin: 0 auto;
    padding-bottom: 20px;
}

img, video{
	display: inline-block;
	width:100%;
	height:auto;
	margin-top:0.4rem;
}
header{
	font-size: 36px;
	color:#373c41;
	font-weight:bold;
}
aside{
	font-size: 16px;
	color:#8d8f91;
	margin-top: 30px;
}

article p{
	font-size: 0.42rem;
	color:#373c41;
	margin-top:0.4rem;
	line-height: 0.67rem;
	text-indent:2em;
}
.hr{
	width:100%;
	margin: 30px 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.hr span{
	background-color: #e3e3e5;
}
.hrl,.hrr{
	width:calc(50% - 5px);
	height:1px;
}
.hrc{
	width:3px;
	height:3px;
	border-radius: 3px;
}
@media screen and (min-width: 1080px) {
    body {
        width:1080px;
        margin:0 auto;
    }
}
.divUeditor {
    overflow: hidden;
    display: block;
    width: 100%;
    min-width: 90%;
    position: relative;
    word-wrap: break-word;
    
}

.divUeditor img {
    border: 0;
    max-width: 100%;
    margin-top: 10px;
}

#videoPlay {
    margin-top: 0.4rem;
}
</style>
