<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1000' :maxHeight="800" >
        <template slot="body">
            <el-row class="wrapper">
                <div class="left">
                    <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading"
                    label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                        <!-- <el-form-item label="课程封面" prop="Logo">
                            <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.Logo"></el-input>
                        </el-form-item> -->
                        <el-form-item label="实物图片" prop="CoverFileList">
                            <app-upload-file :max='1' :fileSize="1024 * 1024 * 2" :fileType='1'
                            :value='formData.CoverFileList' :readonly="!editable" @change='handleUpChange' :preview='true'></app-upload-file>
                        </el-form-item>
                        <el-form-item label="物料名称" prop="Name">
                            <el-input :disabled="!editable" maxlength="100" type="text" v-model="formData.Name"></el-input>
                        </el-form-item>
                        <el-form-item label="物料长代码" prop="LongCode">
                            <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.LongCode"></el-input>
                        </el-form-item>
                        <el-form-item label="规格型号" prop="SpecificationsModel">
                            <el-input :disabled="!editable" maxlength="300" type="textarea" :rows="4" v-model="formData.SpecificationsModel"></el-input>
                        </el-form-item>
                        <el-form-item label="制造商" prop="Manufacturer">
                            <el-input :disabled="!editable" maxlength="30" type="text" v-model="formData.Manufacturer"></el-input>
                        </el-form-item>
                        <el-form-item label="品牌" prop="BrandName">
                            <el-input :disabled="!editable" maxlength="30" type="text" v-model="formData.BrandName"></el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="Describe">
                            <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="5" v-model="formData.Describe"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="right">
                    <div class="panel-title">相关说明附件</div>
                    <div class="attachmentBox">
                        <div class="notData" v-if="formData.AttachmentList.length == 0 && dialogStatus == 'detail'">暂无内容</div>
                        <app-uploader v-else
                        ref="appUploaderRef"
                        :readonly="!editable"
                        accept="all"
                        :fileType="3"
                        :max="10000"
                        :value="formData.AttachmentList"
                        :fileSize="1024 * 1024 * 500"
                        :minFileSize="100 * 1024"
                        @change="handleFilesUpChange"
                        ></app-uploader>
                    </div>
                </div>
            </el-row>
        </template>
        <template slot="footer">
            <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                <el-checkbox v-model="goOn">继续添加</el-checkbox>
            </div>
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>

</div>
</template>

<script>

import * as materialDataApi from "@/api/procureInventory/materialData";
export default {
    name: "material-data-create",
    components: {
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (!val) {
                    this.formData.CoverFileList = [];
                }
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建物料";
            } else if (this.dialogStatus == "update") {
                return "编辑物料";
            } else if (this.dialogStatus == "detail") {
                return "物料详情";
            }
        }
    },

    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            goOn: false,
            defImgUrl: require('../../../assets/images/materialData_notData.png'),
            formLoading: false,
            disabledBtn: false,
            rules: {
                CoverFileList: { fieldName: "实物图片", rules: [{ required: true }] },
                Name: { fieldName: "物料名称", rules: [{ required: true }] },
                LongCode: { fieldName: "物料长代码", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            formData: {
                Id: "",
                Logo: "", // 实物图片 id
                LogoPath: "", // 实物图片
                CoverFileList: [],
                LongCode: "", // 物料长代码
                Name: "", // 物料名称
                SpecificationsModel: "", // 规格型号
                Manufacturer: "", // 制造商
                BrandName: "", // 品牌
                Describe: "", // 备注
                AttachmentList: []
            }
        };
    },
    methods: {
        handleUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.LogoPath = imgs[0].Path;
                this.formData.Logo = imgs[0].Id;
            } else {
                this.formData.Logo = "";
                this.formData.LogoPath = "";
            }
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        resetFormData() {
            let temp = {
                Id: "",
                Logo: "", // 实物图片 id
                LogoPath: "", // 实物图片
                LongCode: "", // 物料长代码
                Name: "", // 物料名称
                SpecificationsModel: "", // 规格型号
                Manufacturer: "", // 制造商
                BrandName: "", // 品牌
                Describe: "", // 备注
                AttachmentList: [],
                CoverFileList: [{
                    Id: 'materialData_notData.png',
                    Path: this.defImgUrl
                }]
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            let self = this;
            self.formLoading = true;
            materialDataApi.detail({id: self.id}).then(res => {
                self.formLoading = false;
                self.formData = Object.assign({}, self.formData, res);
                if (self.formData.LogoPath) {
                    self.formData.CoverFileList = [{
                        Id: res.Logo,
                        Path: res.LogoPath,
                    }]
                }
                
            })
            .catch(err => {
                self.formLoading = false;
            });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                delete postData.CoverFileList
                console.log(postData)
                //提交数据保存
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = materialDataApi.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = materialDataApi.edit(postData);
                }
                this.disabledBtn = true;
                result.then(res => {
                    this.disabledBtn = false;
                    if (this.goOn) {
                        this.resetFormData();
                        this.$refs.appUploaderRef.clearFiles();
                        this.$refs.formData.resetFields();
                        this.formData.AttachmentList = []
                        this.$emit("reload");
                    } else {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData();
                    }
                }).catch(err => {
                    this.disabledBtn = false;
                });
            });
        },
        handleSave() {
            this.createData();
        }
    }
};
</script>
<style lang="scss" scoped>
.wrapper {
    display: flex;

    .left {
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }

    .right {
        width: 40%;
        border-left: 1px solid #DCDFE6;
        
        
        .panel-title{
            padding-top: 10px;
            font-size: 14px;
        }
        .attachmentBox{
                position: relative;
            width: 100%;
            height: calc(100% - 41px);
            padding-left: 10px;
            overflow: hidden;
            overflow-y: auto;
            .notData{
                color: #999;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
}
</style>
