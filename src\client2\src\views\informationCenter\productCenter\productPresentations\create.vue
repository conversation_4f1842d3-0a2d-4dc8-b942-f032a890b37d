<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="636">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth">

          <el-form-item label="产品类型" prop="ProductClassificationPath">
            <el-cascader v-model="formData.ProductClassificationPath" :options="productClassificationList" :props="{ checkStrictly: true ,emitPath:false}" clearable></el-cascader>
          </el-form-item>

          <el-form-item label="产品封面" prop="ProductCoverPath">
            <app-upload-file :max='1' :fileSize='1024 * 1024 * 2' :value='fileList' @change='handleUpChange' :preview='true'></app-upload-file>
          </el-form-item>

          <el-form-item label="产品特点">
            <el-button style="width:10%" icon="el-icon-circle-plus-outline" @click="addProductFeature"></el-button>
            <el-select v-model="selectProductFeatureList" multiple placeholder="请添加产品特点" style="width:89%" @change="selectProductFeatureChange">
              <el-option v-for="item in productFeatureList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="产品标题" prop="ProductTitle">
            <el-input maxlength="30" type="text" v-model="formData.ProductTitle"></el-input>
          </el-form-item>

          <el-form-item label="产品描述" prop="ProductDescribe">
            <!-- <el-input type="textarea" :rows="8" v-model="formData.ProductDescribe"></el-input> -->
            <!-- <editor-bar v-model="formData.ProductDescribe" :isClear="isClear"></editor-bar> -->
            <editor-bar :value="formData.ProductDescribe" @edit="formData.ProductDescribe = arguments[0]"></editor-bar>

          </el-form-item>

          <el-form-item label="是否显示" prop="IsShow">
            <el-radio v-model="formData.IsShow" :label="true">有效</el-radio>
            <el-radio v-model="formData.IsShow" :label="false">无效</el-radio>
          </el-form-item>
            <el-form-item label="相关附件">
                <app-uploader accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList"
                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
            </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <!-- <el-button v-if="this.dialogStatus == 'create'" @click="handleDraft">存为草稿</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSave" type="primary">确认</el-button> -->

        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- <app-button @click="handleDraft" v-if="this.dialogStatus == 'create'" text="存为草稿"></app-button> -->
        <app-button @click="handleSave" text="保存" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as productPresentations from '@/api/informationCenter/productPresentations'
// import EditorBar from '../../../../components/WangEditor/index.vue'
import EditorBar from '@/components/QuillEditor/index.vue'
import * as productClassification from '@/api/informationCenter/productClassification'
export default {
  name: "productPresentations-create",
  directives: {},
  components: {    EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    selectTypeId: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      this.getproductClassificationList();
      this.selectProductFeatureList = [];
      this.productFeatureList = [];
      if (!val) {
        this.fileList = [];
        this.isContinue = false;
      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加产品介绍";
      } else if (this.dialogStatus == "update") {
        return "编辑产品介绍";
      }
    },
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      selectProductFeatureList: [],
      productFeatureList: [],
      isClear: false,
      productClassificationList: [],
      fileList: [], //图像信息[{Id: '', Path: ''}]

      formLoading: false,
      isContinue: false,
      rules: {
        ProductClassificationPath: {
          fieldName: "产品类型",
          rules: [{ required: true }]
        },
        ProductCoverPath: {
          fieldName: "产品封面",
          rules: [{ required: true }]
        },
        ProductTitle: {
          fieldName: "产品标题",
          rules: [{ required: true }]
        },
        ProductDescribe: {fieldName: "产品内容",rules: [{ required: true }, {max: 20000, trigger: "blur"}]}
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        ProductClassificationId: "",
        ProductClassificationPath: "",
        ProductCover: "",
        ProductCoverPath: "",
        IsShow: true,
        ProductFeature: "",
        ProductTitle: "",
        ProductDescribe: "",
        IsDraft: false,
        AttachmentList: [],
      },
      disabledBtn: false,
    };
  },
  methods: {
        // 附件 上传 赋值
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },

    //添加产品特点
    addProductFeature() {
      this.$prompt('', '添加产品特点', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入产品特点',
        inputPattern: /^[\u4e00-\u9fffa-zA-Z0-9]{1,10}$/,
        inputErrorMessage: '只能输入汉字、字母、数字，并且不能超过10个字符'
      }).then(({ value }) => {
        if (this.productFeatureList.length >= 5) {
          this.$message.error('最多只能添加5个产品特点');
        } else {
          this.productFeatureList.push({ label: value, value: value });
          this.selectProductFeatureList.push(value);
        }
      }).catch(() => { });
    },

    //用于删除产品介绍时,同步删除list中的数据
    selectProductFeatureChange() {
      var newList = [];
      this.selectProductFeatureList.forEach(element => {
        newList.push({ label: element, value: element });
      });
      this.productFeatureList = newList;
    },

    //获取产品类型下拉框
    getproductClassificationList() {
      productClassification.getTreeList().then(res => {

        //产品类型下拉框数据源处理
        this.deleteChildrens(res);
        this.productClassificationList = res;

        if (this.dialogStatus == "create") {
          this.formData.ProductClassificationPath = this.selectTypeId;
          //   //添加时, 默认选中 未分类
          //   var obj = res.find(s => s.label == "未分类");
          //   if (obj) {
          //     this.formData.ProductClassificationPath = [obj.value];
          //   }
        }
      });
    },

    //递归删除children为空的属性
    deleteChildrens(res) {
      res.forEach(element => {
        if (element.children.length === 0) {
          delete element.children
        } else {
          this.deleteChildrens(element.children)
        }
      });
    },


    handleUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.ProductCoverPath = imgs[0].Path
        this.formData.ProductCover = imgs[0].Id
      } else {
        this.formData.ProductCoverPath = ''
        this.formData.ProductCover = ''
      }
    },

    resetFormData() {
      let temp = {
        Id: "",
        ProductClassificationId: "",
        ProductClassificationPath: "",
        ProductCover: "",
        ProductCoverPath: "",
        IsShow: true,
        ProductFeature: "",
        ProductTitle: "",
        ProductDescribe: "",
        IsDraft: false,
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      this.formLoading = true;
      this.selectProductFeatureList = [];
      this.productFeatureList = [];
      productPresentations.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);

        //把产品特点转换成下拉框需要的数据源
        if (this.formData.ProductFeature) {
          var featureList = this.formData.ProductFeature.split(',');

          this.selectProductFeatureList = featureList;
          featureList.forEach(element => {
            this.productFeatureList.push({ label: element, value: element });
          });
        }

        //绑定所属分类
        this.formData.ProductClassificationId = [this.formData.ProductClassificationId];
        // //用于定位cascader控件的值
        // this.formData.ProductClassificationPath = this.formData.ProductClassificationPath.split(',');

        //反向绑定封面图片
        this.fileList = [];
        if (this.formData.ProductCoverPath) {
          this.fileList = [
            { Id: this.formData.ProductCover, Path: this.formData.ProductCoverPath }
          ];
        }

        this.formLoading = false;
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //保存
    createData() {
        this.disabledBtn=true;
      let validate = this.$refs.formData.validate();

      Promise.all([validate]).then(valid => {

        let postData = JSON.parse(JSON.stringify(this.formData));
        postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
        delete postData.AttachmentList

        postData.ProductClassificationId = postData.ProductClassificationPath;

        //获取产品特点
        if (this.selectProductFeatureList.length > 0) {
          postData.ProductFeature = this.selectProductFeatureList.join(',');
        } else {
          postData.ProductFeature = null;
        }

        //提交数据保存
        let result = null;
        if (this.dialogStatus == "create") {
          delete postData.Id;
          result = productPresentations.add(postData);
        } else if (this.dialogStatus == "update") {
          result = productPresentations.edit(postData);
        }

        result.then(res => {
            this.disabledBtn=false;
            if (this.isContinue) {
                this.resetFormData();
                this.$emit("reload");
            } else {
                this.$refs.appDialogRef.createData();
            }
        }).catch(err => {
            this.disabledBtn=false;
        });
      });
    },

    handleSave() {
      this.formData.IsDraft = false;
      this.createData();
    },

    handleDraft() {
      this.formData.IsDraft = true;
      this.createData();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
