<template>
<div class="page-content">
    <app-dialog title="编辑合同主体" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='400'>
        <template slot="body">
            <el-form ref="formData" :rules="rules" :model="formData" label-position="right" label-width="100px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="合同主体" prop="LaborContractSubjectId">
                            <el-select id="cus-sel" class="sel-ipt" style="width:100%" placeholder="请选择合同主体" clearable v-model="formData.LaborContractSubjectId">
                                <el-option style="width: 280px" :title="item.label" v-for="item in laborContractSubjects" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="handleSave" :buttonType="1" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as systemEmployee from '@/api/personnelManagement/systemEmployee'
import * as laborContractSubject from '@/api/personnelManagement/laborContractSubject'
export default {
    name: "contract-change",
    directives: {},
    components: {
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        ids: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    filters: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getAllLaborContractSubject();        
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    mounted() {
    },
    data() {
        return {
            disabledBtn: false,
            laborContractSubjects: [],
            formData: {
                LaborContractSubjectId: null
            },
            rules: {
                LaborContractSubjectId:{
                    fieldName: "合同主体",
                    rules: [{ required: true, trigger: 'change' }]
                },
            }

        };
    },
    methods: {
        getAllLaborContractSubject() {
            laborContractSubject.getAllLaborContractSubject({}).then(res => {
                this.laborContractSubjects = res.map(s => {
                    return {
                        value: s.Id,
                        label: s.LaborContractSubjectName
                    }
                })
            })
        },
        handleSave() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    let postDatas = {
                        SystemEmployeeRecordIds: this.ids,
                        LaborContractSubjectId: this.formData.LaborContractSubjectId
                    }
                    this.disabledBtn = true
                    systemEmployee.modifyLaborContractSubject(postDatas).then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData()
                    }).catch(err => {
                        this.disabledBtn = false
                    })

                    // laborContractSubject.addOrUpdateOrDelete(this.tabDatas || []).then(res => {
                    //     this.disabledBtn = false
                    //     this.$notify({
                    //         title: "提示",
                    //         message: "保存成功",
                    //         type: "success",
                    //         duration: 2000
                    //     });
                    //     this.$refs.appDialogRef.createData()
                    // }).catch(err => {
                    //     this.disabledBtn = false
                    // })

                }
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>


<style lang="scss" scoped>
.page-content{
    padding: 10px 0;
    min-height: 300px;
    .list-wrapper{
        margin-top: 10px;
    }
}

</style>
