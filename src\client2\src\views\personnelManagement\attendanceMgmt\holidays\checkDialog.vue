<template>
    <div>
        <app-dialog title="调休异常校正" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <div class="wrapper">
                    <el-form
                        :rules="rules"
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="100px"
                        >
                        <div class="wrapper">
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="执行人">
                                        {{ currentUser.empName }}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="日期范围" prop="Month">
                                        <el-date-picker v-model="formData.Month" value-format="yyyy-MM" type="month" placeholder="" :picker-options="pickerOptions"></el-date-picker>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="执行人员" prop="CorrectEmployeeList">
                                        <emp-selector
                                            key="ccusers"
                                            :showType="2"
                                            :multiple="true"
                                            :list="formData.CorrectEmployeeList"
                                            @change="handleChangeOwnerUsers"
                                        ></emp-selector>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </div>
            </template>

            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>

    </div>
</template>

<script>
import * as timecardCorrectOvertimeRest from '@/api/personnelManagement/timecardCorrectOvertimeRest'
import { getUserInfo } from "@/utils/auth";
import indexPageMixin from "@/mixins/indexPage";
import empSelector from "../../../common/empSelector";
import dayjs from 'dayjs'

export default {
    name: "check-dialog",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [indexPageMixin],
    computed: {
        currentUser() {
            return getUserInfo()
        }
    },
    props: {

    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();
            }
        }
    },
    filters: {
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    mounted() {

    },
    data() {
        return {
            pickerOptions: {
                //只能选择 2021-6 月及以后的月份
                disabledDate(time) {
                    let startYear = dayjs(time).year()
                    let startMonth = dayjs(time).month() + 1

                    let endYear = 2021
                    let endMonth = 6

                    if(startYear > endYear || (startYear == endYear && startMonth >= endMonth)) {
                        return false
                    }
                    return true
                }
            },
            loading: false,
            disabledBtn: false,
            rules: {
                Month: {
                    fieldName: "日期范围",
                    rules: [{ required: true, trigger: "change" }]
                },
                CorrectEmployeeList: {
                    fieldName: "执行人员",
                    rules: [{ required: true, trigger: "change" }]
                },
            },
            formData: {
                Month: dayjs().format('YYYY-MM'),
                CorrectEmployeeList: [],
            },
        };
    },
    methods: {
        handleChangeOwnerUsers(users) {
            this.formData.CorrectEmployeeList = users;
        },
        resetFormData() {
            this.formData = {
                Month: dayjs().format('YYYY-MM'),
                CorrectEmployeeList: [],
            };
        },
        //编辑
        createData() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    
                    const h = this.$createElement;
                    this.$msgbox({
                        title: '提示',
                        message: h('p', null, [
                            h('div', null, '说明：确定开始执行后将会对调休类加班申请人因考勤异常缘故而导致调休无法正常累加的加班单重新进行计算，并累加相关调休余额。'),
                            h('div', { style: 'color: red' }, '提示：开始前请确认是否完成人员的考勤数据校正？')
                        ]),
                        showCancelButton: true,
                        closeOnClickModal: false,
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                    }).then(action => {

                        let startDate = dayjs(dayjs(this.formData.Month).format('YYYY-MM') + '-01')
                        let endDate = startDate.add(1, 'month')
                        let postDatas = {
                            CorrectEmployeeIdList: this.formData.CorrectEmployeeList.map(s => s.EmployeeId) || [],
                            StartTime: startDate.format('YYYY-MM-DD'),
                            EndTime: endDate.format('YYYY-MM-DD')
                        }

                        this.disabledBtn = true
                        timecardCorrectOvertimeRest.add(postDatas).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            //新增记录，返回新增记录id
                            this.$refs.appDialogRef.createData(res);
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    });
                }
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>


<style lang='scss' scoped>
.wrapper{
    
}
</style>