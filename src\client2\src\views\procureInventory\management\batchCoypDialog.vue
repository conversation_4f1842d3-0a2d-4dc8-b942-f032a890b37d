<template>
    <div>
        <app-dialog
            :title="'粘贴填入'"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :width="600"
            >
            <template slot="body">
                <div>
                    <div style="margin: 10px 0;">请将需要填入的内容复制到以下区域，以换行匹配，填入内容必须和要查找的保持一致</div>
                    <el-input style="margin-bottom: 10px;" type="textarea" :rows="8" placeholder="请输入内容" v-model="batchStr" @change="handleChange"></el-input>
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
            </template>
            </app-dialog>

    </div>
</template>

<script>
export default {
    name: 'batchCoypDialog',
    props: {
        copyType: {
            type: String,
            required: true
        },
    },
    data() {
        return {
            disabledBtn: false,
            batchStr: '',
        }
    },
    methods: {
        handleChange(val) {
            if(val) {
                if(this.copyType == 'MaterialCount') {
                    let list = val.split(/[(\r\n\s)\r\n\s]+/)
                    let result = list.map(s => parseInt(s))
                    this.batchStr = result.join('\n')
                }
            }
        },
        createData() {

            let str = this.batchStr.trim()

            if(!str) {
                this.$message({
                    message: '请输入内容',
                    type: 'error'
                });
                return false
            }

            let list = str.split('\n').filter(s => s)

            let flag = true
            let reg = /^\+?[1-9][0-9]*$/
            if(this.copyType == 'MaterialCount') {
                for(let i = 0; i < list.length; i++) {
                    if(!reg.test(list[i])) {
                        flag = false
                        break;
                    }
                }
            }

            if(!flag) {
                this.$message({
                    message: '请输入非0正整数',
                    type: 'error'
                });
                return false
            }

            this.$refs.appDialogRef.createData({
                copyType: this.copyType,
                list: list
            });
            
            // this.disabledBtn = true;
            // materialTransferApi.addList(postData).then((res) => {
            //     this.disabledBtn = false;
            //     this.$notify({
            //         title: "提示",
            //         message: "保存成功",
            //         type: "success",
            //         duration: 2000,
            //     });
            //     this.$refs.appDialogRef.createData();
            // })
            // .catch((err) => {
            //     this.disabledBtn = false;
            // });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
}
</script>