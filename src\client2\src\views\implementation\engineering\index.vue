<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="page-wrapper">
        <div class="content-left">
          <v-tree @changeNode='changeTreeNode' :isAll='true' :isSubset='true'></v-tree>
        </div>
        <div class="content-right __dynamicTabContentWrapper">
          <div class="__dynamicTabWrapper" style="padding-top: 10px;">
            <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" @rowSelectionChanged="rowSelectionChanged" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :isShowOpatColumn="true" :startOfTable="startOfTable" :serial='false' :loading='listLoading'>
              <template slot="idx" slot-scope="scope">
                <span>
                  <svg-icon v-if="scope.row.IsConcern" style="font-size: 12px;" icon-class="bookmark-fill"></svg-icon>
                  {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index) }}
                </span>
              </template>

              <template slot="ImplementStatus" slot-scope="scope">
                <span :style="{color: getStatusObj(scope.row.ImplementStatus).color}">
                  {{ scope.row.ImplementStatus | findState }}
                </span>
              </template>
              <template slot="OverallProgress" slot-scope="scope">
                <div style="max-width: 85%;">
                  <el-progress :percentage="scope.row.OverallProgress" :color="getEngStatus(scope.row.ImplementStatus).color"></el-progress>
                </div>
              </template>
              <template slot="ImplementPrincipalPeople" slot-scope="scope">
                {{ scope.row.ImplementPrincipalPeople || '无' }}
              </template>

              <template slot="ContractNumber" slot-scope="scope">
                {{ scope.row.ContractNumber || "无" }}
              </template>

              <template slot="Year" slot-scope="scope">
                {{ scope.row.Year || "无" }}
              </template>

              <template slot="EngineeringNumber" slot-scope="scope">
                {{ scope.row.EngineeringNumber || "无" }}
              </template>

              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="handleResetSearch" :layoutMode='layoutMode'>
                  <template slot="MultiCondition">
                    <el-input style="width: 100%;" @clear="getList" v-antiShake='{time: 300,callback: () => {getList()}}' clearable v-model.trim="listQuery.MultiCondition" placeholder="搜索单号/项目名称/合同编号"></el-input>
                  </template>

                  <template slot="ImplementStatus">
                    <el-select style="width: 100%;" v-model="listQuery.ImplementStatus" placeholder="" clearable>
                      <el-option v-for="item in ImplementStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </template>
                  <template slot="Year">
                    <el-date-picker key="xxxyear" style="width: 100%;" v-model="listQuery.Year" type="year" placeholder="" format='yyyy' value-format='yyyy'></el-date-picker>
                  </template>

                  <!-- 表格批量操作区域 -->
                  <template slot="btnsArea">
                    <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                    <div style="margin-left:5px">
                      <el-button @click="handleSetFollow" type="primary">设为关注</el-button>
                      <el-button @click="handleQuitFollow" type="primary">取消关注</el-button>
                    </div>
                  </template>

                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button v-if="rowBtnIsExists('btnWorkbench')" @click="handleWorkbench(scope.row)" text="工作台"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
              </template>

            </app-table>
          </div>
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>
    <!-- 创建实施工程 -->
    <v-create v-if="dialogCreateVisible" @closeDialog='closeCreateDialog' @saveSuccess='handleCreateSuccess' :dialogFormVisible='dialogCreateVisible' :dialogStatus='dialogCreateStatus' :regionalId='""' :id="id">
    </v-create>
    <!-- 实施记录/问题 -->
    <v-record-problem v-if="dialogRecordVisible" @closeDialog='closeRecordDialog' :dialogFormVisible='dialogRecordVisible' :siteList="siteList">
    </v-record-problem>
    <!-- 导出 -->
    <v-export @saveSuccess="handleSuccessExport" @closeDialog="handleCloseExport" :dialogFormVisible="dialogExportVisible" :rData="rData" :cData='cData'>
    </v-export>
  </div>
</template>

<script>
import * as impManagement from "@/api/implementation/impManagement";
import vTree from '../../afterSalesMgmt/businessMap/common/tree'
import indexPageMixin from "@/mixins/indexPage";
import vCreate from './create';
import vRecordProblem from './recordAndProblem';
import { vars } from '../common/vars';
import vExport from "@/components/Export/index";
import NoData from "@/views/common/components/noData";
import mixins from "../common/mixins";

export default {
  name: "data-dashborad",
  mixins: [indexPageMixin, mixins],
  components: {
    vTree,
    vCreate,
    vRecordProblem,
    vExport,
    NoData
  },
  filters: {
    findState(d) {
      let result = vars.implementationSatus.find(v => v.value == d);
      return result.label;
    },
  },
  created() { },
  computed: {
    returnUrl() {
      let url = decodeURI(this.$route.query.returnUrl || '')
      return url
    },
  },
  watch: {},

  data() {
    return {
      layoutMode: 'simple',
      templates: [],
      statusList: vars.regionalStatus,
      total: 0,
      listLoading: false,
      listQuery: {
        RegionalId: '',
        MultiCondition: "",
        ImplementStatus: null,
        Year: ''
      },
      multipleSelection: [],

      ImplementStatusList: vars.implementationSatus,

      tableSearchItems: [
        { prop: "MultiCondition", label: "", mainCondition: true },
        { prop: "ImplementStatus", label: "状态" },
        { prop: "Year", label: "年份" },
      ],

      tabColumns: [
        {
          attr: { prop: "idx", label: "序号", width: "50" },
          slot: true
        },
        {
          attr: { prop: "EngineeringNumber", label: "单号" },
          slot: true
        },
        {
          attr: { prop: "Name", label: "项目名称（实施工程）", showOverflowTooltip: true }
        },
        {
          attr: { prop: "Year", label: "年份" },
          slot: true
        },
        {
          attr: { prop: "ImplementStatus", label: "工程状态" },
          slot: true
        },
        {
          attr: { prop: "OverallProgress", label: "项目/工程进度" },
          slot: true
        },
        {
          attr: { prop: "ContractNumber", label: "合同编号", showOverflowTooltip: true },
          slot: true
        },
        {
          attr: { prop: "ImplementPrincipalPeople", label: "工程负责人" },
          slot: true
        },
      ],
      tabDatas: [],
      rData: null,
      cData: [],
      cloumnData: [],
      templateId: "",


      dialogExportVisible: false,
      dialogRecordVisible: false,
      dialogCreateVisible: false,
      dialogCreateStatus: 'create',

      siteList: [],

      dialogFieldShowsVisible: false,
      notRowspanColumns: [], //不需要合并的列
      /**
       * 详细
       */
      id: '',
      regionalId: '',
      dialogStatus: '',
      dialogFormVisible: false,

      checkList: [],
    };
  },
  beforeRouteLeave(to, from, next) {
    next();
  },
  methods: {


    closeRecordDialog() {
      this.dialogRecordVisible = false;
    },
    handleCreateAccep() {
      this.dialogCreateVisible = true;
    },
    closeCreateDialog() {
      this.dialogCreateVisible = false;
    },
    handleCreateSuccess() {
      this.getList();
      this.dialogCreateVisible = false;
    },


    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSuccessExport() { },
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },

    // 弹出编辑框
    handleUpdate(row, optType = "update") {
      this.id = row.Id;
      this.dialogCreateStatus = optType;
      this.handleCreateAccep();
    },

    //工作台
    handleWorkbench(item) {
      let targetUrl = "/implementation/engineering/workbench/index?id=" + item.Id + '&pageIndex=' + this.listQuery.PageIndex + '&rId=' + this.listQuery.RegionalId
      if (!!this.returnUrl) {
        targetUrl += `&returnUrl=${this.returnUrl}&returnUrlUseType=2`
      }
      this.$router.push(targetUrl)
    },


    /**表格行删除 */
    handleDelete(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        impManagement.del(ids).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.loadTableData();
        });
      });
    },

    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.dialogCreateStatus = 'create';
          this.handleCreateAccep();
          break;
        case "btnExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },

    /**设为关注 */
    handleSetFollow() {
      var rows = this.multipleSelection;
      if (rows.length < 1) {
        this.$message({
          message: "至少选择一条",
          type: "error"
        });
        return;
      }
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }
      this.$confirm("是否确认设为关注?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var temp = {};
        temp.Code = 2;
        temp.Type = 1;
        temp.BusinessId = this.listQuery.ImplementationTemplateId;
        temp.Content = JSON.stringify(ids);
        let formData = JSON.parse(JSON.stringify(temp));
        impManagement.addPersonalDataCenter(formData).then(() => {
          this.$notify({
            title: "成功",
            message: "设置成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    /**取消关注 */
    handleQuitFollow() {
      var rows = this.multipleSelection;
      if (rows.length < 1) {
        this.$message({
          message: "至少选择一条",
          type: "error"
        });
        return;
      }
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }
      this.$confirm("是否确认取消关注?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var temp = {};
        temp.Code = 2;
        temp.Type = 1;
        temp.BusinessId = this.listQuery.ImplementationTemplateId;
        temp.Content = JSON.stringify(ids);
        let formData = JSON.parse(JSON.stringify(temp));
        impManagement.delPersonalDataCenter(formData).then(() => {
          this.$notify({
            title: "成功",
            message: "取消成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },


    /**导出 */
    handleExport() {
      this.rData = {
        "exportSource": 7,
        "columns": [],
        "searchCondition": this.listQuery
      }
      this.cData = [{
        label: '序号',
        value: 'Number'
      }, {
        label: '单号',
        value: 'EngineeringNumber'
      }, {
        label: '项目名称（实施工程）',
        value: 'Name'
      }, {
        label: '年份',
        value: 'Year'
      }, {
        label: '工程状态',
        value: 'ImplementStatus'
      }, {
        label: '项目/工程进度',
        value: 'OverallProgress'
      }, {
        label: '合同编号',
        value: 'ContractNumber'
      }, {
        label: '工程负责人',
        value: 'ImplementPrincipalEmployeeString'
      }]
      this.dialogExportVisible = true;
    },
    getStatusObj(status) {
      return vars.implementationSatus.find(s => s.value == status) || {};
    },
    getRegionalStatus(val) {
      return vars.regionalStatus.find(s => s.value == val) || {};
    },
    getProcesStatus(val) {
      return vars.processStatus.find(s => s.value == val) || {};
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        RegionalId: this.listQuery.RegionalId,
        Year: '',
        MultiCondition: "",
        ImplementStatus: null
      };
      this.getList(); //刷新列表
    },


    getList() {
      this.listLoading = true;
      impManagement.getList(this.listQuery).then(res => {
        this.listLoading = false;
        res.Items.forEach(v => {
          v.OverallProgress = Math.round(v.OverallProgress * 100) / 100;
          v.RegionalManagementList.unshift({ RegionalName: '整体进度', RegionalId: v.RegionalId });
          v.region = v.RegionalId;
          v.Completed = 0;
          v.CompletedRate = 0;
          v.Total = 0;
          v.ImplementPrincipalPeople = '';
          v.secondListShow = false;
          v.activeNames = [];
          //   result = vars.implementationSatus.find(s => v.ImplementStatus == s.value);
          //   v.color = result.color;
          if (v.ImplementPrincipalEmployeeList && v.ImplementPrincipalEmployeeList.length > 0) {
            v.ImplementPrincipalPeople = [];
            v.ImplementPrincipalEmployeeList.forEach(v1 => {
              v.ImplementPrincipalPeople.push(v1.Name);
            })
            v.ImplementPrincipalPeople = v.ImplementPrincipalPeople.join('、')
          }
        })
        this.tabDatas = res.Items
        this.total = res.Total;
      })
        .catch(err => {
          this.listLoading = false;
        });
    },

    changeTreeNode(d) {
      if (d.Id == -1) {
        this.listQuery.RegionalId = null;
      } else {
        this.listQuery.RegionalId = d.Id;
      }
      this.listQuery.PageIndex = 1
      this.getList();
    },

    /**
     * 使用span标签包裹内容，然后计算span的宽度 width： px
     * @param valArr
     */
    getTextWidth(str) {
      let width = 0;
      let html = document.createElement("span");
      html.innerText = str;
      html.className = "getTextWidth";
      //字体大小需要和表格中显示字体保持一致，否则宽度计算不合
      html.style =
        "display: inline-block; overflow-x: auto; color: red; white-space:nowrap; font-size: 12px;";
      document.querySelector("body").appendChild(html);
      width = document.querySelector(".getTextWidth").offsetWidth;
      document.querySelector(".getTextWidth").remove();
      return width;
    }
  }
};
</script>

<style scoped>
.page-wrapper >>> .el-progress-bar {
  margin-right: -60px;
  padding-right: 55px;
}

/* 去掉table hover 样式 */
/* .page-wrapper >>> .el-table tbody tr:hover > td {
  background-color: #fff !important;
} */
.page-wrapper >>> .el-table .el-table__body tr.current-row > td {
  background-color: #fff !important;
}

.page-wrapper >>> a:hover {
  text-decoration: underline;
}
</style>

<style lang="scss" scoped>
// .main-page-content {
//   display: flex;
//   flex-direction: column;
//   height: 100%;
//   .table-wrapper {
//     flex: 1;
//   }
// }

// .list-wrapper{
//     .list-item{
//         // white-space: nowrap;
//         a:hover{
//             text-decoration: underline;
//         }
//     }
//     .list-item:not(:last-child) {
//         margin-bottom: 10px;
//     }
// }

.page-wrapper {
  // min-height: calc(100% - 40px)!important;
  min-height: 100% !important;
  margin-bottom: 0 !important;
  .content-left {
    position: absolute;
    top: 0;
    left: 0;
    width: 250px;
    height: 100%;
    border-right: 1px solid #dcdfe6;
  }
  .content-right {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    right: 0;
    width: calc(100% - 250px);
    height: 100%;
    overflow-y: auto;
    > div:first-child {
      margin-top: 4px;
      // margin-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
      padding: 0 8px;
    }

  }
}

.conditionArea-wrap {
  padding: 10px;
  // padding-top: 18px;
  border-bottom: 1px solid #ebeef5;
}

.btns-area {
  text-align: left;
  padding: 10px;
  // padding-right: 10px;
}
</style>