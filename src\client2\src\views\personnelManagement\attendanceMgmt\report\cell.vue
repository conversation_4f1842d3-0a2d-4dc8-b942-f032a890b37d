<template>
  <div class="elCell" v-if="cellData.dataObj">
    <!-- 当前天 -->
    <div class="cell-title-wrapper">
      <div class="day-wrapper">
        {{ cellData.currentDate | dateFilter('DD') }}
        <el-popover v-if="cellData.dataObj.TimecardRecordData && cellData.dataObj.TimecardRecordData.length > 0" title="打卡记录" width="200" trigger="hover" placement="bottom">
          <div v-for="(time, idx) in cellData.dataObj.TimecardRecordData" :key="idx">
            <div>{{ time.Date | dateFilter('HH:mm:ss') }}</div>
            <div>{{ time.OutworkType | outworkTypeFilter }}</div>
            <div>{{time.CardDescription}}</div>

          </div>
          <i class="el-icon-time" slot="reference"></i>
        </el-popover>
      </div>

      <span class="fr" v-show="cellData.dataObj.IsAppeal">申诉中</span>
      <!-- 申诉按钮仅在考勤异常情况下显示，异常情况包含：迟到、早退、缺勤、缺卡（休息日不显示）超过当天的日期，也不显示 -->
      <!-- 补充：打卡状态（迟到、早退、缺勤、缺卡）且 当天没有流程数据————有流程数据说明走流程了（请假等） -->
      <!-- <el-button class="elButSS" v-show="!cellData.dataObj.IsAppeal && isShowButton() && cellData.isComplaint && isBeforeToday(cellData.currentDate)" style="padding:4px 6px;" type="warning" @click="handleAcShow()">申诉</el-button> -->
      
      <!-- 休息日，不支持“申诉”操作（增加逻辑 cellData.dataObj.DateType != 2） -->
      <el-button class="approval-btn" v-show="cellData.dataObj.CanAppeal && cellData.isComplaint && cellData.dataObj.DateType != 2" type="warning" @click="handleAcShow()">申诉</el-button>

      <span class="tip" :class="cellData.dataObj.DateType == 2 ? 'tip-1' : 'tip-2'">
        {{ cellData.dataObj.DateType == 2 ? '休' : '班' }}
      </span>
    </div>

    <div v-if="!cellData.dataObj">

    </div>
    <div v-else>
      <div style="margin-bottom: 4px;" v-if="!isAfterToday(cellData.currentDate)">
        <span>上午：</span>
        <!-- <span v-if="cellData.dataObj.MornFirstCardStatus" class="tag" :style="{backgroundColor: getStatusColor(cellData.dataObj.MornFirstCardStatus)}">
          {{cellData.dataObj.MornFirstCardStatus | timecardStatusFilter}}
        </span> -->

        <app-tag-pure v-if="cellData.dataObj.MornFirstCardStatus" effect="dark" :color="getStatusColor(cellData.dataObj.MornFirstCardStatus)" :text="cellData.dataObj.MornFirstCardStatus | timecardStatusFilter"></app-tag-pure>
        <span v-else>
          {{cellData.dataObj.MornFirstCardStatus | timecardStatusFilter}}
        </span>
      </div>
      <div style="margin-bottom: 4px;" v-if="!isAfterToday(cellData.currentDate)">
        <span>下午：</span>
        <div style="display: inline-block; position: relative;">
             <!-- <span class="tag" v-if="cellData.dataObj.NightLastCardStatus" :style="{backgroundColor: getStatusColor(cellData.dataObj.NightLastCardStatus)}">
                  {{cellData.dataObj.NightLastCardStatus | timecardStatusFilter}}
             </span> -->
             <app-tag-pure v-if="cellData.dataObj.NightLastCardStatus" effect="dark" :color="getStatusColor(cellData.dataObj.NightLastCardStatus)" :text="cellData.dataObj.NightLastCardStatus | timecardStatusFilter"></app-tag-pure>
             <span v-else>
                  {{cellData.dataObj.NightLastCardStatus | timecardStatusFilter}}
             </span>
             <span v-if="cellData.dataObj.NightLastCardStatus === 6" style="top: -4px; right: -4px; width: 8px; height: 8px;  border-radius: 50%; background-color: #F59A23;  position: absolute;"></span>
        </div>
      </div>
      <!-- <div>
                {{ cellData.dataObj.ProcessData }}
            </div> -->
      <div style="margin-bottom: 4px;" v-for="(proc, idx) in cellData.dataObj.ProcessData" :key="idx">
        <div style="padding: 1px 4px; border: 1px solid #409eff; color: #409eff; border-radius: 4px;">
          <span>{{proc.LeaveType | levelTypeFilter}}：</span>
          <span v-for="(t, idx2) in proc.ProcessNodeTimeList" :key="idx2">
            <template v-if="proc.LeaveType != 11">
              {{t | morningOrAfternoonFilter}}
              <template v-if="idx2 < proc.ProcessNodeTimeList.length - 1">、</template>
            </template>
            <template v-else>
              {{ t }}
            </template>
          </span>
        </div>
      </div>
    </div>

  </div>
</template>

<script>

import dayjs from "dayjs"
import { vars } from '../vars'
export default {
  name: 'cell',
  props: {

    cellData: {
      type: Object,
      required: true
    }
  },
  filters: {
    outworkTypeFilter(val){
      let result = ''
      if (val) {
        let obj = vars.outworkType.find(s => s.value == val)
        if (obj) {
          result = obj.label
        }
      }
      return result
    },
    levelTypeFilter(val) {
      let obj = vars.leaveTypes.find(s => s.value === val)
      if (obj) {
        return obj.label
      }
      return '无'
    },
    timecardStatusFilter(val) {
      let result = '-'
      if (val) {
        let obj = vars.timecardStatus.find(s => s.value == val)
        if (obj) {
          result = obj.label
        }
      }
      return result
    },
    morningOrAfternoonFilter(val) {
      let result = '无'
      if (val) {
        let obj = vars.morningOrAfternoon.find(s => s.value == val)
        if (obj) {
          result = obj.label
        }
      }
      return result
    },
  },
  data() {
    return {

    }
  },
  methods: {
    getStatusColor(val) {
      let obj = vars.timecardStatus.find(s => s.value == val)
      if (obj) {
        return obj.color
      }
      return ''
    },
    // isShowButton(){
    //     let havaProcessMorn = this.cellData.dataObj.ProcessData && this.cellData.dataObj.ProcessData.find(s => s.ProcessNodeTime == 1) //上午
    //     let havaProcessNight = this.cellData.dataObj.ProcessData && this.cellData.dataObj.ProcessData.find(s => s.ProcessNodeTime == 2) //下午
    //     return (this.cellData.dataObj.MornFirstCardStatus>1 && this.cellData.dataObj.MornFirstCardStatus<6 && !havaProcessMorn) || (this.cellData.dataObj.NightLastCardStatus>1 && this.cellData.dataObj.NightLastCardStatus<6 && !havaProcessNight);
    // },
    //申诉
    handleAcShow() {
      this.$emit('handleAcShow', JSON.parse(JSON.stringify(this.cellData)));
    },
    /**
     * 获取流程数据
     * date: 年-月-日
     * upOrDown：1 上午；2 下午
     */
    // getProcessDetail(date, upOrDown) {
    //     if(this.cellData && this.cellData.processRecord) {
    //         let obj = this.cellData.processRecord.find(s => s.Date == date && s.UpDown == upOrDown) || null
    //         return obj
    //     }
    //     return null
    // },
    //是否为当天之前（不包含当天）
    // isBeforeToday(day) {
    //     if(dayjs(dayjs().format('YYYY-MM-DD')).diff(day,'day') > 0) {
    //         return true
    //     }
    //     return false
    // },
    //如果是当前日期之后的日期
    /// day: 年月日
    isAfterToday(day) {
      // if(day == '2021-01-15') {
      // }
      // let aa = dayjs().format('YYYY-MM-DD')
      // let bb = dayjs('2021-01-14').diff(dayjs('2021-01-15'),'day')
      // let cc = dayjs(dayjs().format('YYYY-MM-DD')).diff(dayjs('2021-01-15'),'day')

      // let temp1 = dayjs()
      // let temp2 = dayjs(day)
      // let temp3 = temp1.diff(temp2,'day')
      // let temp4 = temp1.diff(day,'day')

      if (dayjs(dayjs().format('YYYY-MM-DD')).diff(day, 'day') < 0) {
        return true
      }
      return false
    },
  }
}
</script>
<style lang="scss" scoped>
.elCell {
  width: 100%;
  height: 100%;
  font-size: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  .cell-title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    .day-wrapper {
      flex: 1;
    }
    .elButSS {
      // position:absolute;
      // right:0;
      // bottom:0px;
    }
    .tip {
      color: #fff;
      line-height: 20px;
      text-align: center;
      display: block;
      height: 20px;
      width: 20px;
      border-radius: 5px;
      margin-left: 6px;
    }
    .tip-1 {
      background: #409eff;
      box-shadow: 1px 1px 5px #409eff;
    }
    .tip-2 {
      background: #70b603;
      box-shadow: 1px 1px 5px #70b603;
    }
    .approval-btn {
      padding: 0;
      width: 40px;
      height: 20px;
      border-radius: 5px;
      box-shadow: 1px 1px 5px #e6a23c;
    }
  }

  // .tag {
  //   display: inline-block;
  //   padding: 1px 4px;
  //   border-radius: 2px;
  //   color: #fff;
  // }
}

.fr {
  border: 1px solid #ff0000;
  color: #ff0000;
  padding: 0px 2px;
  border-radius: 4px;
  height: 20px;
  line-height: 20px;
}
</style>
