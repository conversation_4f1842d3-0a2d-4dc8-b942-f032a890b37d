<template>
    <div>
        <el-button type="text" @click="handleAdd">配置自定义选择器</el-button>
        <el-dialog title="配置自定义选择器" :visible.sync="dialogVisible" width="800px" :before-close="handleClose" :append-to-body="true">
            <!-- {{ formStruct }} -->
            <el-form ref="dataForm" :rules="rules" :model="formStruct" label-width="120px" v-loading='loading'>
                <el-form-item label="表单接口地址" prop="url">
                    <el-input @keyup.native="changeStr" v-model="formStruct.url" placeholder="请输入表单结构api地址（格式：/Business/user/getlist）"></el-input>
                </el-form-item>
                <el-form-item label="展示列" prop="showColumns">
                    <el-checkbox-group v-model="formStruct.showColumns">
                        <el-checkbox v-for="(item, idx) in formStruct.columns" :label="item.value" :key="idx">{{ item.label }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label='查询条件' prop="conditions">
                    <el-checkbox-group v-model="formStruct.conditions">
                        <el-checkbox v-for="(item, idx) in formStruct.columns" :label="item.value" :key="idx">{{ item.label }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label='选中查询条件' prop="checkedShowColumns">
                    <el-checkbox-group v-model="formStruct.checkedShowColumns">
                        <el-checkbox v-for="(item, idx) in formStruct.columns" :label="item.value" :key="idx">{{ item.label }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="展示风格" prop="showStyle">
                    <el-select v-model="formStruct.showStyle" style="width:100%" placeholder="请选择">
                        <el-option v-for="item in selectorShowStyle" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button size="mini" @click="dialogVisible = false">取消</el-button>
                <el-button size="mini" type="primary" @click="createData">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import mixin from '../../dynamicFormCommon/mixins'
import request from '@/utils/request'
import indexPageMixin from "@/mixins/indexPage"
export default {
    name: 'selector',
    model: {
        prop: 'list',
        event: 'change'
    },
    mixins: [mixin, indexPageMixin],
    props: {
        list: {
            type: Object,
            // default: []
        }
    },
    created() {
        this.rules = this.initRules(this.rules)
        let tmp = []
        if(this.list) {
            tmp = JSON.parse(JSON.stringify(this.list))
        }
        this.formStruct = tmp
    },
    data () {
        return {
            rules: {
                url: { url: "表单接口地址", rules: [{ required: true }] },
                showColumns: { fieldName: "展示列", rules: [{ required: true }] },
                checkedShowColumns: { fieldName: "选中展示列", rules: [{ required: true }] },
                showStyle: { fieldName: "展示风格", rules: [{ required: true }] }
            },
            changeTime: new Date().getTime(),
            // interval: null,
            // datas: [],
            dialogVisible: false,
            formStruct: {
                // url: '',
                // columns: [], //表单中所有的列
                // showColumns: [], //展示的列
                // checkedShowColumns: [],//选中展示的列
                // conditions: [], //查询条件列
                // showStyle: '1'
            },
            loading: false

        }
    },
    methods: {
        handleClose() {

        },
        handleAdd() {
            this.openDialog()
        },
        createData() {
            this.$refs["dataForm"].validate(valid => {
                if (valid) {
                    let emitDatas = JSON.parse(JSON.stringify(this.formStruct))
                    this.$emit('change', emitDatas)
                    this.closeDialog()
                }
            });
        },
        openDialog() {
            this.dialogVisible = true
        },
        closeDialog() {
            this.dialogVisible = false
        },  
        changeStr() {
            this.handleTimeout()
        },
        handleTimeout() {
            this.changeTime = new Date().getTime()
            setTimeout(() => {
                if (new Date().getTime() - this.changeTime >= 500) {
                    this.getDatas()
                }
            }, 500)
        },
        getDatas() {
            this.loading = true
            this.formStruct.columns = []
            this.formStruct.showColumns = []
            this.formStruct.checkedShowColumns = []
            this.formStruct.conditions = []
            
            request({
                url: this.formStruct.url,
                method: 'get'
            }).then(res => {
                this.loading = false
                let columns = res.formContent
                if(columns && columns.length > 0) {
                    this.formStruct.columns = columns.filter(s => s.type != 0 && s.type != 1 && s.type != 2).map(s => {
                        let label = s.attrs.find(t => t.attrName == 'label').value
                        let name = s.attrs.find(t => t.attrName == 'name').value
                        return {
                            label,
                            name,
                            value: s.id
                        }
                    })
                }
                this.formStruct.showColumns = []
                this.formStruct.checkedShowColumns = []
                this.formStruct.conditions = []
            }).catch(err => {
                this.loading = false
            })
        }  
    },

}
</script>

<style scoped>

</style>