<template>
  <div>
    <el-button v-show="multipleNode&&!readonly" type="text" @click="handleAdd">新增</el-button>
    <el-table :data="NodeList" border style="width: 100%" empty-text="暂无审批人">
      <el-table-column type="expand" v-if="showResult">
        <template slot-scope="scope">
          <el-table
            :data="scope.row.ApprovalEmployees"
            border
            style="width: 100%"
            empty-text="暂无审批人"
          >
            <el-table-column prop="Name" label="审批人" width="200"></el-table-column>
            <el-table-column prop="ApprovalState" label="审批结果" width="100">
              <template
                slot-scope="slot"
              >{{stateCollectionTypeLabel['label'+slot.row.ApprovalState] }}</template>
            </el-table-column>
            <el-table-column prop="ApprovalOpinion" label="审批意见">
              <template slot-scope="slot">
                <span :title="slot.row.ApprovalOpinion">{{slot.row.ApprovalOpinion}}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column prop="NodeIndex" label="审批节点" width="70" v-if="multipleNode">
        <template slot-scope="scope">{{scope.$index+1}}</template>
      </el-table-column>
      <el-table-column prop="ApprovalEmployees" label="审批人">
        <template slot-scope="scope">
          <emp-selector
            :showType="2"
            :readonly="readonly||scope.row.NotAllowEdit"
            :multiple="multipleEmployee"
            :list="scope.row.ApprovalEmployees"
            @change="handleChangeEmployee($event,scope.row)"
          ></emp-selector>
        </template>
      </el-table-column>
      <el-table-column prop="ApprovalType" label="审批类型" width="200">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.ApprovalType"
            @change="handleChangeApprovalType($event,scope.row)"
            :disabled="readonly||scope.row.NotAllowEdit||defaultApprovalType>0"
          >
            <el-option
              v-for="item in approvalType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="50" v-if="!readonly&&multipleNode">
        <template slot-scope="scope">
          <app-table-row-button
            @click="handleRemove(scope.$index)"
            :type="3"
            v-show="!scope.row.NotAllowEdit"
          ></app-table-row-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import empSelector from "./empSelector";
import { approvalType, stateCollectionTypeLabel } from "../../utils/commonEnum";
export default {
  name: "approval-module",
  directives: {},
  components: {
    empSelector
  },
  props: {
    /**传递的数据 */
    list: {
      type: Array,
      default: () => {
        return [];
      }
    },
    /**展示审批信息 */
    showResult: {
      type: Boolean,
      default: false
    },
    /**只读 */
    readonly: {
      type: Boolean,
      default: false
    },
    /**节点人员是否多选 */
    multipleEmployee: {
      type: Boolean,
      default: true
    },
    /**是否多级节点 */
    multipleNode: {
      type: Boolean,
      default: true
    },
    defaultApprovalType: {
      type: Number,
      default: 0
    }
  },
  computed: {},
  data() {
    return {
      approvalType: approvalType,
      stateCollectionTypeLabel: stateCollectionTypeLabel,
      NodeList: []
    };
  },
  watch: {
    list: {
      handler(val) {
        console.log(val);
        if (!val || val.length == 0) {
          val = [
            {
              Nodeindex: 1,
              ApprovalEmployees: [],
              ApprovalType:this.defaultApprovalType > 1 ? this.defaultApprovalType : undefined
            }
          ];
        } else if (!this.multipleNode) {
          val = [val[0]];
        }
        this.NodeList = JSON.parse(JSON.stringify(val));
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    /**新增审批节点 */
    handleAdd() {
      var node = {
        Nodeindex: this.NodeList.length + 1,
        ApprovalEmployees: [],
        ApprovalType:
          this.defaultApprovalType > 1 ? this.defaultApprovalType : undefined
      };
      this.NodeList.push(node);
    },

    /**移除审批节点 */
    handleRemove(idx) {
      // this.$confirm("是否确认删除当前行?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning"
      // }).then(() => {
      this.NodeList.splice(idx, 1);
      this.returnChanged();
      // });
    },
    handleChangeEmployee(employees, row) {
      if (employees && employees.length > 0) {
        row.ApprovalEmployees = employees;
      } else {
        row.ApprovalEmployees = [];
      }
      this.returnChanged();
    },
    handleChangeApprovalType(val, row) {
      row.ApprovalType = val;
      this.returnChanged();
    },
    returnChanged() {
      var temp = this.NodeList.filter((item, index) => {
        return (
          item.ApprovalEmployees &&
          item.ApprovalEmployees.length > 0 &&
          item.ApprovalType &&
          item.ApprovalType > 0
        );
      });
      temp.forEach((item, index) => {
        item.Nodeindex = index + 1;
      });
      this.$emit("change", temp);
    }
  }
};
</script>
