<template>
    <div>
        <app-dialog title="选择提醒时间" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
            <template slot="body">
                <div class="wrapper">
                    <div class="list-wrapper">
                        <el-checkbox-group class="group-list" v-model="checkList">
                            <div class="omit" v-for="i in remindTypes" :key="i.value">
                                <el-checkbox :label="i.value">{{ i.label }}</el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import { vars } from './enum'
export default {
    name: "report-selector",
    directives: {},
    components: {
        noData,
    },
    mixins: [],
    computed: {
    },
    watch: {

    },
    props: {
        checked: {
            type: Array, 
            default: () => {
                return []
            }
        },
        //创建时必须
        // workPlanId: {
        //     type: String,
        //     default: ''
        //     // validator: (val) => {
        //     //     if(this.dialogStatus == 'create') {
        //     //         return !!val
        //     //     }
        //     //     return true
        //     // }
        // },
        // //周报主键，查看详情时必填
        // id: {
        //     type: String,
        //     default: ''
        // }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(this.checked) {
                    this.checkList = JSON.parse(JSON.stringify(this.checked || []))
                }
            },
            immediate: true
        },
    },
    created() {
    },
    data() {
        return {
            remindTypes: vars.remindTypes,
            disabledBtn: false,
            checkList: [],


        };
    },
    methods: {
        createData() {
            let result = JSON.parse(JSON.stringify(this.checkList))
            this.$refs.appDialogRef.createData(result);
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
}
</script>


<style lang='scss' scoped>


.list-wrapper{
    height: 100%;
    min-height: 500px;
    .group-list{
        >div{
            padding: 10px;
            &:not(:last-child) {
                border-bottom: 1px solid #DCDFE6;
            }
        }
    }
}
</style>