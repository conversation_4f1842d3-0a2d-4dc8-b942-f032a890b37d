<template>
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <!-- <page-title title="系统菜单管理" :subTitle="['系统菜单维护管理页面']"></page-title> -->
        <div class="filter-container" style="background: white;">
            <el-button v-if="btnMaintain=='btnMaintain'" style="width: 121px;" type="primary" @click='handleCreate'>添加</el-button>
            <el-button v-if="btnMaintain=='btnMaintain'" style="width: 121px;" type="primary" @click='handleUpdate'>编辑</el-button>
            <el-button v-if="btnMaintain=='btnMaintain'" style="width: 121px;" type="primary" @click='handleDelete'>删除</el-button>
            <el-button v-if="btnMaintain=='btnMaintain'" style="width: 121px;" type="primary" @click='handleAddMenu'>添加按钮</el-button>
            <el-button v-if="btnMaintain=='btnMaintain'" style="width: 121px;" type="primary" @click='handleEditMenu'>编辑按钮</el-button>
            <el-button v-if="btnMaintain=='btnMaintain'" style="width: 121px;" type="primary" @click='handleDelMenus'>删除按钮</el-button>
            <!-- <sticky :className="'sub-navbar'">
               
            </sticky> -->
        </div>
        <div class="module-body">
            <el-row :gutter="4" style="height: 100%;">
                <el-col :span="10" style="height: 100%;">
                    <!-- <el-card shadow="never" class="card-body-none">
                        <div slot="header" class="clearfix">
                        </div>
                    </el-card> -->

                    <el-button type="text" @click="getAllMenus">所有菜单</el-button>

                    <el-table v-loading='menuLoading' highlight-current-row :data="modulesTree" @row-click="treeClick" border row-key="uniqueId" :height="tabHeight2 - 50">
                        <el-table-column prop="Name" label="模块名称"></el-table-column>
                        <el-table-column prop="IconName" label="Icon"></el-table-column>
                        <el-table-column prop="Url" label="URL"></el-table-column>
                        <el-table-column prop="IsSys" label="是否为系统菜单" width="120">
                            <template slot-scope="scope">
                                <span v-show="scope.row.Url">
                                    <span v-show="scope.row.IsSys">是</span>
                                    <span v-show="!scope.row.IsSys">否</span>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="14" style="height: 100%;">
                    <div style="height: 100%;" id="__dynamicTabContentWrapper" class="bg-white __dynamicTabContentWrapper">
                        <div class="__dynamicTabWrapper">
                            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="isShowAllColumn" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="rowBtns.length > 0" :isShowBtnsArea="false" :isShowConditionArea="false">
                                <template slot="ButtonShowType" slot-scope="scope">
                                    <span :class="scope.row.ButtonShowType">{{ btnTypes.find(o => o.value == scope.row.ButtonShowType).label }}</span>
                                </template>
                            </app-table>
                        </div>

                        <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" /> -->
                    </div>
                </el-col>
            </el-row>

            <!--模块编辑对话框-->
            <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false" :append-to-body="true">
                <el-form :rules="rules2" ref="dataForm" :model="temp" label-position="right" label-width="110px">
                    <el-form-item size="small" :label="'名称'" prop="Name">
                        <el-input v-model="temp.Name"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'是否系统'" prop="IsSys">
                        <el-switch v-model="temp.IsSys" active-color="#13ce66" :disabled="dialogStatus == 'update' && canEdit(temp.Url.toLowerCase())" inactive-color="#ff4949"></el-switch>
                    </el-form-item>
                    <el-form-item size="small" :label="'模块标识'" prop="Code">
                        <el-input v-model="temp.Code"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'Icon'" prop="IconName">
                        <el-input v-model="temp.IconName"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'Url'" prop="Url">
                        <el-input v-model.trim="temp.Url"></el-input>
                    </el-form-item>
                    <el-form-item size="small" label>
                        <el-checkbox v-model="temp.IsMenu" :disabled="dialogStatus == 'update' && canEdit(temp.Url.toLowerCase())">是否为菜单</el-checkbox>
                    </el-form-item>
                    <el-form-item size="mini" :label="'上级菜单'">
                        <treeselect :append-to-body="true" zIndex='9999' class="treeselect-common" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" ref="modulesTree" :normalizer="normalizer" :disabled="treeDisabled" :options="modulesTree" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="dpSelectModule" placeholder></treeselect>
                        <el-checkbox v-model="isRoot">根节点</el-checkbox>
                    </el-form-item>
                    <el-form-item size="small" :label="'排序号(升序)'" prop="SortNo">
                        <el-input-number v-model="temp.SortNo" :min="0" :max="999999999" label></el-input-number>
                    </el-form-item>
                     <el-form-item size="small" :label="'数据权限枚举值'" prop="BusinessRoleType">
                        <el-input-number v-model="temp.BusinessRoleType" :min="0" :max="999999999" label></el-input-number>
                    </el-form-item>
                    <el-form-item size="small" label="描述" prop="Remark">
                        <el-input v-model="temp.Remark"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
                    <el-button size="mini" v-if="dialogStatus=='create'" type="primary" :loading="postLoading" @click="createData">确认</el-button>
                    <el-button size="mini" v-else type="primary" :loading="postLoading" @click="updateData">确认</el-button>
                </div>
            </el-dialog>
            <!--菜单编辑对话框-->
            <el-dialog v-el-drag-dialog :close-on-click-modal="false" class="dialog-mini" width="500px" :title="textMap[dialogMenuStatus]" :visible.sync="dialogMenuVisible" :append-to-body="true">
                <el-form :rules="rules" ref="menuForm" :model="menuTemp" label-position="right" label-width="100px">
                    <!-- <el-form-item size="small" :label="'Id'" prop="Id" v-show="dialogMenuStatus=='update'">
            <span>{{menuTemp.Id}}</span>
            </el-form-item>-->

                    <el-form-item size="small" :label="'名称'" prop="Name">
                        <el-input v-model="menuTemp.Name"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'DOM ID'" prop="DomId">
                        <el-input v-model="menuTemp.DomId"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'ActionName'" prop="ApiActionName">
                        <el-cascader placeholder :options="actionGroup" filterable style="width: 100%;" v-model="selectedOptions" @change="handleChange"></el-cascader>
                    </el-form-item>
                    <el-form-item size="small" :label="'样式'" prop="Class">
                        <el-input v-model="menuTemp.Class"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'图标'" prop="Icon">
                        <el-input v-model="menuTemp.Icon"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'所属模块'">
                        <treeselect :append-to-body="true" class="treeselect-common" zIndex='9999' :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" ref="menuModulesTree" :normalizer="normalizer" :options="modulesTree" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" placeholder :clear-on-select="true" v-model="menuTemp.ModuleId"></treeselect>
                    </el-form-item>
                    <el-form-item size="small" :label="'按钮类型'">
                        <el-select v-model="menuTemp.ButtonShowType" placeholder="按钮类型" style="width: 100%;">
                            <el-option v-for="item in btnTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item size="small" :label="'终端类型'">
                        <el-checkbox-group v-model="menuTemp.TerminalType" :min="1">
                            <el-checkbox v-for="t in TerminalTypes" :label="t.value" :key="t.value">{{t.label}}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item size="small" :label="'排序'">
                        <el-input-number v-model="menuTemp.Sort" :max="999999999" :min="-999999999"></el-input-number>
                    </el-form-item>

                    <el-form-item size="small" :label="'关联URL'" prop="RelevanceUrl">
                        <el-input v-model.trim="menuTemp.RelevanceUrl"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'关联URL标题'" prop="RelevanceTitle">
                        <el-input v-model.trim="menuTemp.RelevanceTitle"></el-input>
                    </el-form-item>
                    <el-form-item size="small" :label="'描述'" prop="Remark">
                        <el-input v-model="menuTemp.Remark"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button size="mini" @click="dialogMenuVisible = false">取消</el-button>
                    <el-button size="mini" v-if="dialogMenuStatus=='create'" type="primary" :loading="postLoading" @click="addMenu">确认</el-button>
                    <el-button size="mini" v-else type="primary" :loading="postLoading" @click="updateMenu">确认</el-button>
                </div>
            </el-dialog>
        </div>

    </div>
</div>

</template>

<script>
// import treeTable from '@/components/TreeTable'
import Pagination from "@/components/Pagination";
import {
    listToTreeSelect
} from "@/utils";
import * as modules from "@/api/modules";
import * as login from "@/api/login";

import waves from "@/directive/waves"; // 水波纹指令
import Sticky from "@/components/Sticky";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import {
    websitEnv
} from "@/utils/env";
export default {
    name: "modulemanager",
    components: {
        Sticky,
        // treeTable,
    },
    directives: {
        waves,
        elDragDialog,
    },
    mixins: [indexPageMixin, tabDynamicHeightMixins],
    data() {
        return {
            tabHeight2: 300,
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children,
                };
            },
            btnMaintain: '',
            columns: [{
                    text: "模块名称",
                    value: "Name",
                },
                {
                    text: "模块标识",
                    value: "Code",
                },
                {
                    text: "URL",
                    value: "Url",
                },
            ],
            tabColumns: [
                // {
                //     attr: { prop: 'Id', label: '编号' },
                // },
                {
                    attr: {
                        prop: "DomId",
                        label: "DOM ID"
                    },
                },
                {
                    attr: {
                        prop: "Name",
                        label: "名称"
                    },
                },
                {
                    attr: {
                        prop: "ButtonShowType",
                        label: "按钮类型"
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Class",
                        label: "样式"
                    },
                },
                {
                    attr: {
                        prop: "Icon",
                        label: "ICON"
                    },
                },
                {
                    attr: {
                        prop: "Remark",
                        label: "描述"
                    },
                },
            ],
            // tabAuthColumns: [], //已授权的列
            tabDatas: [],
            selectMenus: [], // 菜单列表选中的值
            tableKey: 0,
            total: 0,
            currentModule: null, // 左边模块treetable当前选中的项
            listLoading: true,
            postLoading: false,
            listQuery: {
                // 查询条件
                PageIndex: 1,
                PageSize: 20,
                orgId: "",
                key: undefined,
            },
            apps: [],
            btnTypes: [{
                    value: 1,
                    label: "表格顶部显示"
                },
                {
                    value: 2,
                    label: "表格行内显示"
                },
                {
                    value: 3,
                    label: "都显示"
                },
            ],
            TerminalTypes: [{
                    value: 1,
                    label: "PC端"
                },
                {
                    value: 2,
                    label: "APP移动端"
                },
                {
                    value: 3,
                    label: "WPF客户端"
                },
            ],

            modules: [], // 用户可访问到的模块列表
            modulesTree: [], // 用户可访问到的所有模块组成的树
            menuLoading: false,
            actionGroup: [], //api 可选树
            temp: {
                // 模块临时值
                Id: undefined,
                CascadeId: "",
                IconName: '',
                Url: "",
                IsMenu: true,
                Code: "",
                ParentId: null,
                Name: "",
                Status: 0,
                IsSys: false,
                Remark: "",
                SortNo: 0,
                BusinessRoleType: 0,
            },
            menuTemp: {
                // 菜单临时值
                Id: undefined,
                Url: "",
                IsMenu: true,
                Code: "",
                ApiActionName: "",
                ModuleId: null,
                Name: "",
                Status: 0,
                Sort: 0,
                ButtonShowType: 1,
                RelevanceUrl: "",
                RelevanceTitle: "",
                Remark: "",
                TerminalType: [1],
            },
            dialogFormVisible: false, // 模块编辑框
            dialogStatus: "",
            dialogMenuVisible: false, // 菜单编辑框
            dialogMenuStatus: "",

            chkRoot: false, // 根节点是否选中
            treeDisabled: true, // 树选择框时候可用
            textMap: {
                update: "编辑",
                create: "添加",
            },
            selectedOptions: [],
            //菜单
            rules: {
                Name: {
                    fieldName: "名称",
                    rules: [{
                        required: true
                    }, {
                        max: 18
                    }]
                },
                DomId: {
                    fieldName: "DomId",
                    rules: [{
                        max: 20
                    }]
                },
                ApiActionName: {
                    fieldName: "ApiActionName",
                    rules: [{
                        max: 200
                    }]
                },
                Class: {
                    fieldName: "Class",
                    rules: [{
                        max: 20
                    }]
                },
                Icon: {
                    fieldName: "Icon",
                    rules: [{
                        max: 20
                    }]
                },
                RelevanceUrl: {
                    fieldName: "关联URL",
                    rules: [{
                        max: 100
                    }]
                },
                RelevanceTitle: {
                    fieldName: "关联URL标题",
                    rules: [{
                        max: 100
                    }]
                },
                Remark: {
                    fieldName: "备注",
                    rules: [{
                        max: 200
                    }]
                },
            },
            //按钮
            rules2: {
                Name: {
                    fieldName: "名称",
                    rules: [{
                        required: true
                    }, {
                        max: 18
                    }]
                },
                // IconName: {
                //     fieldName: "Icon",
                //     rules: [{
                //         required: true
                //     }]
                // },
                Code: {
                    fieldName: "模块标识",
                    rules: [{
                        max: 50
                    }]
                },
                Url: {
                    fieldName: "Url",
                    rules: [{
                        max: 100
                    }]
                },
            },
            downloadLoading: false,
        };
    },
    computed: {
        
        isRoot: {
            get() {
                return this.chkRoot;
            },
            set(v) {
                this.chkRoot = v;
                if (v) {
                    this.temp.ParentName = "根节点";
                    this.temp.ParentId = null;
                }
                this.treeDisabled = v;
            },
        },
        dpSelectModule: {
            // 模块编辑框下拉选中的模块
            get: function () {
                if (this.dialogStatus === "update") {
                    return this.temp.ParentId;
                } else {
                    return null;
                }
            },
            set: function (v) {
                // console.log('set org:' + v)
                if (v === undefined || v === null) {
                    // 如果是根节点
                    this.temp.ParentName = "根节点";
                    this.temp.ParentId = null;
                    this.isRoot = true;
                    return;
                }
                this.isRoot = false;
                this.temp.ParentId = v;
                var parentname = this.modules.find((val) => {
                    return v === val.Id;
                }).name;
                this.temp.parentName = parentname;
            },
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
        this.rules2 = this.initRules(this.rules2);
        this.getList();

        this.btnTextValue()

    },
    mounted() {
        this.getModulesTree();
        this.getActionTree();
        this.$nextTick(() => {
            this.setTabHeight()
        })
        window.addEventListener('resize', throttle(this.setTabHeight2, 100));
    },

    beforeDestroy() {
        window.removeEventListener("resize", this.setTabHeight2);
    }, 
    
    methods: {
        setTabHeight() {
            let wrapperObj = document.getElementById(`__dynamicTabContentWrapper`)
            if(wrapperObj) {
                let tabHeightTemp = wrapperObj.clientHeight || wrapperObj.offsetHeight;
                this.$nextTick(() => {
                    //减去分页高度
                    this.tabHeight2 = tabHeightTemp
                })
            }
        },
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },
        rowClick(row) {
            this.$refs.mainTable.clearSelection();
            this.$refs.mainTable.toggleRowSelection(row);
        },
        rowSelectionChanged(rows) {
            this.selectMenus = rows;
        },
        treeClick(data) {
            // 左侧treetable点击事件
            this.currentModule = data;
            this.currentModule.parent = null;
            this.getList();
        },
        getAllMenus() {
            this.currentModule = null;
            this.getList();
        },
        handleSelectionChange(val) {
            this.selectMenus = val;
        },
        onBtnClicked: function (domId) {
            // console.log('you click:' + domId)
            switch (domId) {
                case "btnAdd":
                    this.handleCreate();
                    break;
                case "btnEdit":
                    if (this.currentModule === null) {
                        this.$message({
                            message: "只能选中一个进行编辑",
                            type: "error",
                        });
                        return;
                    }
                    this.handleUpdate(this.currentModule);
                    break;
                case "btnDel":
                    if (this.currentModule === null) {
                        this.$message({
                            message: "至少删除一个",
                            type: "error",
                        });
                        return;
                    }
                    this.handleDelete(this.currentModule);
                    break;
                case "btnAddMenu":
                    this.handleAddMenu();
                    break;
                case "btnEditMenu":
                    if (this.selectMenus.length !== 1) {
                        this.$message({
                            message: "只能选中一个进行编辑",
                            type: "error",
                        });
                        return;
                    }
                    this.handleEditMenu(this.selectMenus[0]);
                    break;
                case "btnDelMenu":
                    if (this.selectMenus.length < 1) {
                        this.$message({
                            message: "至少删除一个",
                            type: "error",
                        });
                        return;
                    }
                    this.handleDelMenus(this.selectMenus);
                    break;
                default:
                    break;
            }
        },
        //编辑时不能修改“是否为系统”、“是否为菜单”属性
        canEdit(path) {
            if (websitEnv.adminMenus.find((s) => s == path.trim().toLowerCase())) {
                return true;
            }
            return false;
        },
        getList() {
            this.listLoading = true;
            var moduleId = this.currentModule === null ? null : this.currentModule.Id;
            modules.loadMenus(moduleId).then((response) => {
                this.tabDatas = response;
                // this.total = response.count
                // this.total = response.result.length
                this.listLoading = false;
            });
        },
        getActionTree() {
            let options = [{
                value: "zhinan",
                label: "指南",
                children: [{
                    value: "shejiyuanze",
                    label: "设计原则",
                }, ],
            }, ];

            modules.getAuthActionTree().then((res) => {
                let _actionGroup = [];

                res.forEach((a) => {
                    if (_actionGroup.findIndex((o) => o.label == a.ParentName) > -1) {
                        let oldGroup = _actionGroup.find(
                            (old) => old.label == a.ParentName
                        );
                        if (oldGroup) {
                            oldGroup.children.push({
                                value: a.Name,
                                label: a.Name,
                            });
                        }
                    } else {
                        _actionGroup.push({
                            value: a.ParentName,
                            label: a.ParentName,
                            children: [{
                                value: a.Name,
                                label: a.Name,
                            }, ],
                        });
                    }
                });

                this.actionGroup = _actionGroup;
            });
        },
        getModulesTree() {
            var _this = this; // 记录vuecomponent
            _this.menuLoading = true
            login.getModules().then((response) => {
                _this.menuLoading = false
                _this.modules = response.map(function (item, index, input) {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId,
                        Code: item.Code,
                        Url: item.Url,
                        IconName: item.IconName,
                        IsMenu: item.IsMenu,
                        CascadeId: item.CascadeId,
                        IsSys: item.IsSys,
                        Remark: item.Remark,
                        SortNo: item.SortNo,
                        uniqueId: index + 1,
                        BusinessRoleType: item.BusinessRoleType,
                    };
                });
                var modulestmp = JSON.parse(JSON.stringify(_this.modules));
                _this.modulesTree = listToTreeSelect(modulestmp);
            }).catch(err => {
                _this.menuLoading = false
            });
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            // console.log(val)
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        resetTemp() {
            this.temp = {
                Id: undefined,
                CascadeId: "",
                Url: "",
                IconName: '',
                IsMenu: true,
                Code: "",
                ParentId: null,
                Name: "",
                Status: 0,
                SortNo: 0,
            };
        },
        resetMenuTemp() {
            this.menuTemp = {
                Id: undefined,
                CascadeId: "",
                Url: "",
                IsMenu: true,
                Code: "",
                ModuleId: this.currentModule ? this.currentModule.Id : null,
                ApiActionName: "",
                Name: "",
                Status: 0,
                Sort: 0,
                ButtonShowType: 1,
                RelevanceUrl: "",
                RelevanceTitle: "",
                TerminalType: [1],
            };
            this.selectedOptions = [];
        },

        // #region 模块管理
        handleCreate() {
            // 弹出添加框
            this.resetTemp();
            this.dialogStatus = "create";
            this.dialogFormVisible = true;
            this.dpSelectModule = null;
            this.$nextTick(() => {
                this.$refs["dataForm"].clearValidate();
            });
        },
        createData() {
            // 保存提交
            this.postLoading = true;
            this.$refs["dataForm"].validate((valid) => {
                if (!valid) {
                    this.postLoading = false;
                }
                if (valid) {
                    modules
                        .add(this.temp)
                        .then((response) => {
                            // 需要回填数据库生成的数据
                            this.postLoading = false;
                            this.getList();
                            // this.temp.id = response.result.id
                            // this.temp.cascadeId = response.result.cascadeId
                            // this.list.unshift(this.temp)
                            this.dialogFormVisible = false;
                            this.$notify({
                                title: "成功",
                                message: "创建成功",
                                type: "success",
                                duration: 2000,
                            });
                            this.getModulesTree();
                        })
                        .catch((err) => {
                            this.postLoading = false;
                        });
                }
            });
        },
        handleUpdate() {
            var row = this.currentModule;
            if (this.currentModule === null) {
                this.$message({
                    message: "只能选中一个进行编辑",
                    type: "error",
                });
                return;
            }
            // 弹出编辑框
            this.temp = Object.assign({}, row); // copy obj
            if (!this.temp.Url) {
                this.temp.Url = "";
            }
            if (this.temp.children) {
                // 点击含有子节点树结构时，带有的children会造成提交的时候json死循环
                this.temp.children = null;
            }

            this.dialogStatus = "update";
            this.dialogFormVisible = true;
            this.dpSelectModule = this.temp.ParentId;
            this.$nextTick(() => {
                this.$refs["dataForm"].clearValidate();
            });
        },
        updateData() {
            // 更新提交
            this.postLoading = true;
            this.$refs["dataForm"].validate((valid) => {
                if (!valid) {
                    this.postLoading = false;
                }
                if (valid) {
                    const tempData = Object.assign({}, this.temp);
                    modules
                        .update(tempData)
                        .then(() => {
                            this.postLoading = false;
                            this.dialogFormVisible = false;
                            this.$notify({
                                title: "成功",
                                message: "更新成功",
                                type: "success",
                                duration: 2000,
                            });
                            this.resetMenuTemp();
                            this.getModulesTree();
                            this.getList();
                        })
                        .catch((err) => {
                            this.postLoading = false;
                        });
                }
            });
        },
        handleDelete() {
            var rows = this.currentModule;
            if (this.currentModule === null) {
                this.$message({
                    message: "至少删除一个",
                    type: "error",
                });
                return;
            }
            // 多行删除

            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                modules.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getModulesTree();
                });
            });
        },
        // #end region

        // #region 菜单管理
        handleAddMenu() {
            // 弹出添加框
            this.resetMenuTemp();
            this.dialogMenuStatus = "create";
            this.dialogMenuVisible = true;
            this.$nextTick(() => {
                this.$refs["menuForm"].clearValidate();
            });
        },
        addMenu() {
            // 保存提交
            this.postLoading = true;
            this.$refs["menuForm"].validate((valid) => {
                if (!valid) {
                    this.postLoading = false;
                }
                if (valid) {
                    if (this.selectedOptions && this.selectedOptions.length == 2) {
                        this.menuTemp.ApiActionName = this.selectedOptions.join(".");
                    }
                    if (
                        this.menuTemp.TerminalType &&
                        this.menuTemp.TerminalType.length > 0
                    ) {
                        this.menuTemp.TerminalType = this.menuTemp.TerminalType.join(",");
                    }
                    modules
                        .addMenu(this.menuTemp)
                        .then((response) => {
                            this.postLoading = false;
                            // 需要回填数据库生成的数据
                            this.getList();
                            // this.menuTemp.id = response.result.id
                            // this.list.unshift(this.menuTemp)
                            this.dialogMenuVisible = false;
                            this.$notify({
                                title: "成功",
                                message: "创建成功",
                                type: "success",
                                duration: 2000,
                            });
                        })
                        .catch((err) => {
                            this.postLoading = false;
                        });
                }
            });
        },
        handleEditMenu() {
            var row = this.selectMenus[0];
            if (this.selectMenus.length !== 1) {
                this.$message({
                    message: "只能选中一个进行编辑",
                    type: "error",
                });
                return;
            }
            // 弹出编辑框
            this.menuTemp = Object.assign({}, row); // copy obj
            let apiName = this.menuTemp.ApiActionName;
            this.selectedOptions = [];
            if (apiName) {
                if (apiName.indexOf(".") > -1) {
                    let ctrlName = apiName.substring(0, apiName.lastIndexOf("."));
                    let actionName = apiName.substring(apiName.lastIndexOf(".") + 1);
                    if (ctrlName && actionName) {
                        this.selectedOptions = [ctrlName, actionName];
                    }
                }
            }
            if (this.menuTemp.TerminalType) {
                this.menuTemp.TerminalType = this.menuTemp.TerminalType.split(
                    ","
                ).map((item) => parseInt(item));
            }
            this.dialogMenuStatus = "update";
            this.dialogMenuVisible = true;
            this.$nextTick(() => {
                this.$refs["menuForm"].clearValidate();
            });
        },
        updateMenu() {
            // 更新提交
            if (this.selectedOptions && this.selectedOptions.length == 2) {
                this.menuTemp.ApiActionName = this.selectedOptions.join(".");
            }
            if (this.menuTemp.TerminalType && this.menuTemp.TerminalType.length > 0) {
                this.menuTemp.TerminalType = this.menuTemp.TerminalType.join(",");
            }
            this.postLoading = true;
            this.$refs["menuForm"].validate((valid) => {
                if (!valid) {
                    this.postLoading = false;
                }
                if (valid) {
                    const tempData = Object.assign({}, this.menuTemp);
                    modules
                        .updateMenu(tempData)
                        .then(() => {
                            this.postLoading = false;
                            this.dialogMenuVisible = false;
                            this.$notify({
                                title: "成功",
                                message: "更新成功",
                                type: "success",
                                duration: 2000,
                            });
                            this.getList();
                        })
                        .catch((err) => {
                            this.postLoading = false;
                        });
                }
            });
        },
        handleDelMenus() {
            var rows = this.selectMenus;
            if (this.selectMenus.length < 1) {
                this.$message({
                    message: "至少删除一个",
                    type: "error",
                });
                return;
            }
            // 多行删除
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                modules.delMenu(rows.map((u) => u.Id)).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            });
        },
        handleChange(val) {
            this.selectedOptions = val;
        },
        // #end region
    },
};
</script>

<style lang='scss' scoped>
/* .app-container {
    top: 60px;
    min-height: calc(100% - 70px);
} */

.filter-container{
    padding: 10px;
    text-align: right;
    button{
        margin-left: 4px;
    }
}

.module-body {
    // overflow-y: hidden;
    // overflow-x: hidden;
    padding: 10px;
    height: 100%;
}

.text {
    font-size: 14px;
}

.item {
    margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both;
}

.el-card__header {
    padding: 12px 20px;
}
</style>
