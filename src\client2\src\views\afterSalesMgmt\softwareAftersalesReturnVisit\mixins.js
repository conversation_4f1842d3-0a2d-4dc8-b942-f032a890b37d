let Dec = require("decimal.js");

export default {
    data() {
        return {

        }
    },
    filters: {
        totalTimeFilter(val) {
            if(!val) {
                return '无记录'
            }

            // let h = (new Dec(val).div(new Dec(60))).toFixed(0, Decimal.ROUND_DOWN)
            // let m = val / 60
            let h =  Math.floor(val / 60);
            let m = val % 60;
            return `${h}小时${m}分钟`
        }
    },
    methods: {
        getTotalTime(empId, list) {
            if(empId && list && list.length > 0) {
                let timeObj = list.find(s => s.EmployeeId == empId)
                if(timeObj) {
                    return timeObj.TotalTime
                }
            }
            return ''
        },
    },
};
