export const ViewRangeEnum = [
    { label: '所有人', value: 1},
    { label: '按部门', value: 2},
    { label: '自定义', value: 3},
]

// 问题状态 1 未解决 2 已解决 3 已放弃
export const ProductQuestionStatusEnum = [
    { label: '未解决', value: 1, color: '#f56c6c', bgColor: 'rgb(254, 240, 240)'}, // 红色
    { label: '已解决', value: 2, color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)'}, // 蓝色
    { label: '已放弃', value: 3, color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)'}, // 橙色
]


// 效果评定 1 效果显著 2 效果一般 3 效果甚微 4 毫无效果
export const EffectEvaluationTypeEnum = [
    { label: '效果显著', value: 1, color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)'}, // 蓝色
    { label: '效果一般', value: 2, color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)'}, // 蓝色
    { label: '效果甚微', value: 3, color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)'}, // 蓝色
    { label: '毫无效果', value: 4, color: '#909399', bgColor: 'rgba(244, 244, 245, 1)'}, // 灰色
]
// 采纳建议 1 已采纳  2 未采纳
export const AdoptSuggestionTypeEnum = [
    { label: '已采纳', value: 1, color: '#70B603', bgColor: 'rgba(112, 182, 3, 0.2)'}, // 绿色
    { label: '未采纳', value: 2, color: '#f56c6c', bgColor: 'rgb(254, 240, 240)'}, // 红色
]