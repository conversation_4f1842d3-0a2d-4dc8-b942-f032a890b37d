<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">

                <emp-selector
                       key="ccusers"
                       :showType="2"
                       :multiple="true"
                       :list="pers"
                       @change="handleChangeUsers"
                       :beforeOpen='getEmps'
                       :condition="{SourceType:2}"
                        >
                      <el-button style="margin-left: 10px; margin-top:10px" slot="reference" type="primary">评审人员设置</el-button>
                      <el-button style="margin-left: 10px; margin-top:10px" slot="reference" type="primary" @click="addFileType()">文件类型设置</el-button>
                </emp-selector>

                <div class="treeBox">
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入产品/项目" v-model="filterText">
                    </el-input>
                    <el-tree class="elTree" ref="tree" :data="treeDatas" node-key="Id" v-loading='treeLoading' :filter-node-method="filterNode" :props="defaultProps"  :highlight-current='true' :check-on-click-node='true' @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label" :style="{width: getNodeWidth(node.level) + 'px'}">{{ node.label }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :layoutMode='layoutMode' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :multable='false' :startOfTable="startOfTable" @sortChagned='handleSortChange'>
                        <template slot="Status" slot-scope="scope">
                           <div class="status-wrapper">
                                 <div class="item-status" :style="{
                                        backgroundColor: getWorkStatusObj(scope.row.FileStatus).color
                                       }">
                                       {{ getWorkStatusObj(scope.row.FileStatus).label }}
                                  </div>
                             </div>
                            <!-- <el-tag :type="getStatus(scope.row) == 1 ? 'info' : getStatus(scope.row) == 2 ? 'warning' : 'success'">{{ getStatus(scope.row) | statusFilter }}</el-tag> -->
                        </template>

                    <template slot="ReadNum" slot-scope="scope">
                        <span style="margin-right: 4px;">{{ scope.row.ReadNum }} / {{ scope.row.SendNum }}</span>

                        <template v-if="!listLoading">
                            <el-popover :key="scope.row.Id" :open-delay='300' trigger="hover" width="386" placement="bottom">
                                <div v-loading='symptomDetailLoading' class="popover-wrapper">
                                    <template v-if="scope.row && scope.row.symptoms">
                                        <div class="title" style="">已评审（{{ scope.row.symptoms.ReadEmployeeList.length }}）</div>
                                        <div class="cl">
                                            <noData v-if="scope.row.symptoms.ReadEmployeeList == 0"></noData>
                                            <div class="fl item-wrapper" v-for="(item, idx) in scope.row.symptoms.ReadEmployeeList" :key="idx">
                                                <div class="avatar-wrapper">
                                                    <el-avatar :src="item.AvatarPath"></el-avatar>
                                                </div>
                                                <div class="omit name">{{ item.Name }}</div>
                                            </div>
                                        </div>
                                        <div class="title" style="margin-top: 10px;">未评审（{{ scope.row.symptoms.SendEmployeeList.length }}）</div>
                                        <div class="cl">
                                            <noData v-if="scope.row.symptoms.SendEmployeeList == 0"></noData>
                                            <div class="fl item-wrapper" v-for="(item, idx2) in scope.row.symptoms.SendEmployeeList" :key="idx2 + 'list2'">
                                                <div class="avatar-wrapper">
                                                    <el-avatar :src="item.AvatarPath"></el-avatar>
                                                </div>
                                                <div class="omit name">{{ item.Name }}</div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                                <img src="../../../assets/images/more2.png" style="width: 16px; height: 16px; position: absolute; top: 12px;" alt="" slot="reference" @mouseover.prevent="handleShowSymptomDetail(scope.row, scope.row.Id)" v-if="scope.row.SendNum > 0">
                            </el-popover>
                        </template>
                    </template>
                        
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch' :layoutMode='layoutMode'>

                                <template slot='Keywords'>
                                    <el-input style="width: 100%;" placeholder="搜索文件名称/创建人" @clear='getList' v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }' clearable v-model.trim="listQuery.Keywords"></el-input>
                                </template>

                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button v-if="scope.row.IsReviewBtn" @click="handleReview(scope.row)" :type="2" text="评审"></app-table-row-button>
                            <app-table-row-button @click="handleDetailDialog('detail', scope.row)" :type="2"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 详情 -->
    <detail-page
        v-if="currentRow"
        @closeDialog='closeDetailDialog'
        @saveSuccess='handleDetailSaveSuccess'
        :dialogFormVisible='dialogDetailFormVisible'
        :dialogStatus='dialogDetailStatus'
        :project='{}'
        :id='currentRow.Id'
    ></detail-page>


     <review-page
        v-if="currentReviewRow"
        @closeDialog='closeReviewDialog'
        @saveSuccess='handleReviewSuccess'
        :dialogFormVisible='dialogReviewVisible'
        :dialogStatus='dialogReviewStatus'
        :id='currentReviewRow.Id'
    ></review-page>

    <type-page
        v-if="dialogTypeVisible"
        @closeDialog='closeTypeDialog'
        @saveSuccess='handleTypeSuccess'
        :dialogFormVisible='dialogTypeVisible'
        :dialogStatus='dialogTypeStatus'
    ></type-page>


</div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import indexPageMixin from '@/mixins/indexPage'
import * as configurationFile from '@/api/projectDev/projectMgmt/configurationFile'
import { vars } from '../../projectDev/common/vars';
import detailPage from '../../projectDev/projectMgmt/workbench/conf/detail'
import reviewPage from '../../projectDev/projectMgmt/workbench/conf/review'
import empSelector from '@/views/common/empSelector'
import typePage from './type'

export default {
    name: 'configMgmt-index',
    mixins: [indexPageMixin],
    components: {
        detailPage,
        reviewPage,
        empSelector,
        typePage,
    },
    props: {},
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
    },
    computed: {

    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1
                   
                    if(val.Level == 0){
                        //全部
                         this.listQuery.ProjectId = null
                         this.listQuery.ProductId = null
                    }else{
                         //有项目id就传项目id，有产品id就传产品id
                        if(val.ProductId) {
                         this.listQuery.ProjectId = null
                         this.listQuery.ProductId = val.Id
                        }else if(val.ProjectId) {
                         this.listQuery.ProductId = null
                         this.listQuery.ProjectId = val.Id
                      }
                    }
                  
                    this.getList()
                }
            },
            immediate: true
        },
    },
    created() {
        this.getProductClassification()
    },
    data() {
        return {
            fileNewStatus: vars.fileNewStatus,
            symptomDetailLoading: false,

            pers: [],
            status: vars.fileStatus,
            filterText: '',
            treeDatas: [],
            treeLoading: false,
            defaultProps: { //树默认结构
                children: 'ProjectListList',
                label: 'ProductName'
            },
            layoutMode: 'simple',
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
            ],
            checkedNode: null, //当前单击选中的节点

            // dialogFolderFormVisible: false,
            dialogFolderStatus: 'create',
            currentOptNode: null, //当前操作的文件夹节点（新增、编辑、删除）

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: 'ProjectName',
                        label: '项目名称',
                        showOverflowTooltip: true
                    },
                },{
                    attr: {
                        prop: 'FileName',
                        label: '文件名称',
                        showOverflowTooltip: true
                    },
                },{
                    attr: {
                        prop: 'ClassifyName',
                        label: '文件类型',
                        showOverflowTooltip: true
                    },
                },{
                    attr: {
                        prop: 'OrderName',
                        label: '关联订单',
                        showOverflowTooltip: true
                    },
                },{
                    attr: {
                        prop: 'RegionalName',
                        label: '使用地区',
                        showOverflowTooltip: true
                    },
                },{
                    attr: {
                        prop: 'FileVersionNumber',
                        label: '文件版本号',
                    },
                },
                {
                    attr: { prop: "Status", label: "文件状态" },
                    slot: true
                },
                
                {
                    attr: { prop: "ReadNum", label: "评审情况" },
                    slot: true
                },,
                
                {
                    attr: {
                        prop: 'Remark',
                        label: '文件说明',
                        showOverflowTooltip: true
                    },
                },{
                    attr: {
                        prop: 'CreateEmployeeName',
                        label: '创建人',
                    },
                }
            ],
            listQuery: {
                KeyWords: '',
                ProductId: null,
                ProjectId: null,
                // WebOrApp: 'Web'
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

            dialogDetailFormVisible: false,
            dialogDetailStatus: 'detail',
            currentRow: null,

            dialogReviewVisible:false,
            dialogReviewStatus: 'create',
            currentReviewRow: null,

            dialogTypeVisible: false,
            dialogTypeStatus: 'create',
        }
    },
    methods: {
        addFileType(){
            this.dialogTypeVisible = true
        },
        getWorkStatusObj(status) {
            return this.fileNewStatus.find(s => s.value == status) || {};
        },
        handleShowSymptomDetail(row, id) {

            this.symptomDetailLoading = true

            configurationFile.getRecordDetails({ id: id }).then(res => {
                this.$set(row, 'symptoms', res)
                this.symptomDetailLoading = false
            }).catch(err => {
                this.symptomDetailLoading = false
            })
        },
        getStatus(row) {
            /**
             * 1: 未同步
             * 2：同步中
             * 3：已同步
             */
            let status = 1
            if(row.SendNum <= 0) {
                status = 1
            }else if(row.SendNum == row.ReadNum) {
                status = 3
            }else{
                status = 2
            }
            return status
        },
        /**
         * 详情
         */
        closeDetailDialog() {
            this.dialogDetailFormVisible = false
        },
        handleDetailSaveSuccess() {
            this.getList()
            this.closeDetailDialog()
        },
        handleDetailDialog(optType, row) {
            this.currentRow = row
            this.dialogDetailStatus = optType
            this.dialogDetailFormVisible = true
        },

        closeReviewDialog(){
            this.dialogReviewVisible = false
        },
        handleReviewSuccess(){
           this.getList()
           this.closeReviewDialog()
        },

        closeTypeDialog(){
            this.dialogTypeVisible = false
        },

        handleTypeSuccess(){
          this.closeTypeDialog()
        },

        handleReview(row){
            this.currentReviewRow = row,
            this.dialogReviewVisible = true
        },

        getNodeWidth(level) {
            let def_width = 190
            let result = def_width - (level - 1) * 20
            return result <= 0 ? 50 : result
        },
        onResetSearch() {
            this.listQuery.Keywords = ''
            
            this.getList() //刷新列表
        },

        handleSortChange({ column, prop, order }) {
            this.sortObj = {prop, order}
            this.getList()
        },
        //获取产品列表
        getList() {
            if (this.checkedNode) {
                this.listLoading = true
                let postData = JSON.parse(JSON.stringify(this.listQuery))
                postData = this.assignSortObj(postData)

                configurationFile.getProjectConfigManagementList(postData).then(res => {
                    this.tabDatas = res.Items
                    this.total = res.Total
                    this.listLoading = false
                }).catch(err => {
                    this.listLoading = false
                })
            }
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1
            this.getList()
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size
            this.getList()
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page
            this.listQuery.PageSize = val.size
            this.getList()
        },

        /***************************************左侧树菜单操作***************************************/

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.ProductName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getProductClassification() {
            this.treeLoading = true
            configurationFile.getProductList({}).then(res => {
                this.treeLoading = false
                this.treeDatas = res.map(s => {
                    s.Id = s.ProductId
                    //固定两层（最多）
                    if(s.ProjectListList && s.ProjectListList.length > 0) {
                        //数据结构，不统一
                        s.ProjectListList.map(c => {
                            c.Id = c.ProjectId
                            c.ProductName = c.ProjectName
                            return c
                        })
                    }
                    return s
                })
                this.treeDatas.unshift({
                      Id: "",
                      ProductName: "全部",
                      ProductId:null,
                      Level: 0,
                      ParentId: null
                 });
                //如果首次加载问价夹树（没有选中），默认选中根节点
                if (!this.checkedNode) {
                    this.setDefaultChecked()
                }
            }).catch(err => {
                this.treeLoading = false
            })
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0]
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode
                }
            });
        },


        /******************* 弹窗 *******************/
        /**人员变更 */
        handleChangeUsers(users) {
            this.pers = []

            let postDatas = {
                employeeIdList: users.map(s => s.EmployeeId) || []
            }

            configurationFile.settingReviewersEmployee(postDatas).then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
            })
        },

        async getEmps() {
            let result = false
            await configurationFile.getSettingReviewersEmployee({}).then(res => {
                this.pers = res
                result = true
            })
            return result
        },

        //左侧树操作菜单
        // handleCommand(optType, node, data) {
        //     if (optType == 'add') {
        //         this.handleCreateFolder(data)
        //     } else if (optType == 'update') {
        //         this.handleCreateFolder(data, 'update')
        //     } else if (optType == 'delete') {
        //         this.handleDelete(node, data)
        //     }
        // },


        // 添加/修改 子分类
        // handleCreateFolder(data, optType = 'create') {
        //     this.dialogFolderStatus = optType
        //     this.currentOptNode = data
        //     this.dialogFolderFormVisible = true
        // },

        //删除分类
        // handleDelete(node, data) {
        //     this.$confirm(`是否确认删除${data.ProductClassificationName}所包含的所有产品吗?`, "提示", {
        //         confirmButtonText: "确认",
        //         cancelButtonText: "取消",
        //         type: "warning"
        //     }).then(() => {
        //         productClassification.del([data.Id]).then(res => {
        //             if (this.checkedNode && this.checkedNode.Id == data.Id) {
        //                 this.checkedNode = null
        //             }
        //             this.getProductClassification()
        //             this.$notify({
        //                 title: '成功',
        //                 message: '删除成功',
        //                 type: 'success',
        //                 duration: 2000
        //             })
        //         })
        //     });
        // },


    },
}
</script>

<style lang="scss" scoped>
.el-tag{
    margin-right: 5px;
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
        .treeBox {
            width: 100%;
            flex: 1;
            overflow-y: auto;
            // height: calc(100% - 48px);
            padding-top: 10px;

            .elInput {
                width: 230px;
                margin-left: 10px;
            }

            .elTree {
                height: calc(100% - 42px);
                overflow: auto;
                margin-top: 10px;
                padding-bottom: 10px;
            }
        }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        // padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

    }
}

/* 有效 */
.status-true {
    background-color: green;
}

/* 无效 */
.status-false {
    background-color: red;
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
    width: 44px;
    height: 22px;
}

.popover-wrapper{
    .title{
        font-size: 14px; font-weight: 600; margin-bottom: 10px;
    }
    .item-wrapper{
        padding: 5px; display: inline-block; width: 60px;
        .avatar-wrapper{
            display: flex; align-items: center; justify-content: center;
        }
        .name{
            text-align: center;
        }
    }
}


.item-status {
    color: #fff;
    border-radius: 8%;
    width: 60px;
    height: 22px;
    text-align: center;
    margin-right: 10px;
    padding: 1px;
}

 .status-wrapper{
     display: flex;
     align-items: center;
    .principal-progress{
         flex: 1;
         width: 100px;
     }
}

</style>
