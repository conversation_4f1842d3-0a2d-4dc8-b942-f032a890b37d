<!--专利软著编辑-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog title="产品类型管理" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
            <template slot="body">
                <el-row style="padding:10px 0;">
                    <el-button type="primary" @click="addTopLevel">创建产品类型</el-button>
                </el-row>
                <el-table :data="ClassifyList" height="300" v-loading="listLoading">
                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                    <el-table-column prop="Name" label="产品类型名称"></el-table-column>
                    <el-table-column label="操作" width="100">
                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <!-- 编辑 -->
                            <app-table-row-button @click="handleUpdate(scope.row, 'update')" :type="1"></app-table-row-button>
                            <!-- 删除 -->
                            <app-table-row-button @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <template slot="footer">
                <app-button :buttonType="2" @click="handleClose" text="关闭"></app-button>
            </template>
        </app-dialog>
    <classify-create :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyCreateVisible" @closeDialog="classifyCreateVisible=false" @saveSuccess="classifyCreateSaveSuccess"></classify-create>
    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as classify from '@/api/classify'
import { listToTreeSelect } from "@/utils";
import classifyCreate from './classifyCreate';

export default {
    /**名称 */
    name: "test-certificate-classify",
    /**组件声明 */
    components: {
        classifyCreate
    },
    /**参数区 */
    props: {
    },
    /**数据区 */
    data() {
        return {
            paramNode: {
                Id: null,
                Name: "",
                Level: 0
            },
            classifyDialogStatus: '',
            classifyCreateVisible: false,
            listLoading: false,
            ClassifyList: [], // 产品类型集合
            tabColumns: [
                { attr: { prop: "Name", label: "产品类型名称", showOverflowTooltip: true} },
            ],
        };
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                let self = this;
                self.listLoading = false
                self.ClassifyList = []
                self.getClassifyList();
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
    },
    /**渲染后 */
    mounted() {

    },
    /**方法区 */
    methods: {
        classifyCreateSaveSuccess(){
            this.classifyCreateVisible = false;
            this.getClassifyList();
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.classifyDialogStatus = "create";
            this.classifyCreateVisible = true;
        },
        handleUpdate(row, optType){
            this.paramNode.Id = row.Id
            this.paramNode.Name = row.Name
            this.paramNode.Level = row.Level
            console.log(this.paramNode)
            this.classifyDialogStatus = optType;
            this.classifyCreateVisible = true;
        },
        handleDelete(rows){
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                classify.del({
                    ids: [rows.Id],
                    businessType: 8
                }).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getClassifyList();
                });
            });
        },
        getClassifyList(){
            let self = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: 8
            };
            self.listLoading = true;
            classify.getListPage(paramData).then(response => {
                self.listLoading = false;
                self.ClassifyList = listToTreeSelect(response.Items);
            }).catch(err => {
                self.listLoading = false
            });
        },
        /**关闭 */
        handleClose() {
            this.$emit('saveSuccess');
        },
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
