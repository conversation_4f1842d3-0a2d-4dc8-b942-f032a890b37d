<!--参数节点-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="600"
    >
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="120px"
        >
          <el-form-item label="节点名称" prop="Name">
            <el-input maxlength="25" v-model="formModel.Name"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <el-checkbox v-show="dialogStatus=='create'" v-model="isContinue">连续添加</el-checkbox>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as param from "@/api/param";

export default {
  /**名称 */
  name: "param-node-edit",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    /**弹窗类型 */
    dialogStatus: {
      type: String,
      default: "create"
    },
    /**层级 */
    level: {
      type: Number
    },
    /**父级Id */
    parentId: {
      type: String
    },
    /**主键Id */
    id: {
      type: String
    }
  },
  /**数据区 */
  data() {
    return {
      /**连续添加 */
      isContinue: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单模型 */
      formModel: { Name: "" },
      /**表单规则 */
      formRules: {
        Name: { fieldName: "节点名称", rules: [{ required: true }] }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    pageTitle() {
      let title = "";
      switch (this.dialogStatus) {
        case "create":
          title = "添加参数节点";
          break;
        case "update":
          title = "修改参数节点";
          break;
        case "detail":
          title = "参数节点详情";
          break;
        default:
          title = "";
          break;
      }
      return title;
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.isContinue = false;
          if (_this.dialogStatus != "create" && _this.id) {
            param.getParamNodeDetails({ Id: _this.id }).then(response => {
              _this.formModel = Object.assign({}, _this.formModel, response);
            });
          } else {
            _this.resetData();
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    /**重置 */
    resetData() {
      let _this = this;
      _this.formModel = {
        Name: "",
        ParentId: _this.parentId,
        Level: _this.level
      };
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;
          if (_this.dialogStatus == "create") {
            result = param.addParamNode(_this.formModel);
          } else if (_this.dialogStatus == "update") {
            result = param.editParamNode(_this.formModel);
          }

          result
            .then(response => {
              _this.buttonLoading = false;
              _this.$notify({
                title: "成功",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              _this.$refs.appDialogRef.createData();
              if (_this.isContinue) {
                _this.resetData();
              } else {
                _this.handleClose();
              }
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


