<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth">

          <el-form-item label="案例名称" prop="CaseName">
            <el-input maxlength="30" type="text" v-model="formData.CaseName"></el-input>
          </el-form-item>

          <el-form-item label="案例封面" prop="CaseCoverPath">
            <app-upload-file :max='1' :fileSize='1024 * 1024 * 2' :value='fileList' @change='handleUpChange' :preview='true'></app-upload-file>
          </el-form-item>

          <el-form-item label="案例描述" prop="CaseDescribe">
            <!-- <el-input type="textarea" :rows="8" v-model="formData.CaseDescribe"></el-input> -->
            <!-- <editor-bar v-model="formData.CaseDescribe" :isClear="isClear"></editor-bar> -->
            <editor-bar :value="formData.CaseDescribe" @edit="formData.CaseDescribe = arguments[0]"></editor-bar>

          </el-form-item>

        </el-form>
      </template>
      <template slot="footer">
        <!-- <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSave" type="primary">发布</el-button> -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" text="发布"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as successfulCases from '@/api/informationCenter/successfulCases'
// import EditorBar from '../../../../components/WangEditor/index.vue'
import EditorBar from '@/components/QuillEditor/index.vue'

export default {
  name: "successfulCases-create",
  directives: {},
  components: {
    EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.fileList = [];
        this.isContinue = false;
      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加成功案例";
      } else if (this.dialogStatus == "update") {
        return "编辑成功案例";
      }
      return "";
    },
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      formLoading: false, isClear: false,
      fileList: [], //图像信息[{Id: '', Path: ''}]
      isContinue: false,
      rules: {
        CaseName: {
          fieldName: "案例标题",
          rules: [{ required: true }]
        },
        CaseCoverPath: {
          fieldName: "案例封面",
          rules: [{ required: true }]
        },
        CaseDescribe: {fieldName: "案例内容",rules: [{ required: true }, {max: 20000, trigger: "blur"}]}
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        CaseName: "",
        CaseCover: "",
        CaseCoverPath: "",
        CaseDescribe: "",
      }
    };
  },
  methods: {
    handleUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.CaseCoverPath = imgs[0].Path
        this.formData.CaseCover = imgs[0].Id
      } else {
        this.formData.CaseCover = ''
        this.formData.CaseCoverPath = ''
      }
    },
    resetFormData() {
      let temp = {
        Id: "",
        CaseName: "",
        CaseCover: "",
        CaseCoverPath: "",
        CaseDescribe: "",
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      this.formLoading = true;
      successfulCases.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        this.fileList = [];
        if (this.formData.CaseCoverPath) {
          this.fileList = [
            { Id: this.formData.CaseCover, Path: this.formData.CaseCoverPath }
          ];
        }
        this.formLoading = false;
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //保存
    createData() {
      let validate = this.$refs.formData.validate();

      Promise.all([validate]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        //提交数据保存
        let result = null

        if (this.dialogStatus == "create") {
          delete postData.Id;
          result = successfulCases.add(postData);
        } else if (this.dialogStatus == "update") {
          result = successfulCases.edit(postData);
        }

        result.then(res => {
          if (this.isContinue) {
            this.resetFormData();
            this.$emit("reload");
          } else {
            this.$refs.appDialogRef.createData();
          }
        });
      });
    },

    handleSave() {
      this.createData();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
