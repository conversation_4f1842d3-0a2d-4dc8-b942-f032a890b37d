<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="aside_container">
        <el-row class="tabs_box">
          <div
            class="tabs_box_item"
            v-for="item in leftTabs"
            :key="item.value"
            :class="{ active: listQuery.ApprovalTotalEnum == item.value }"
            @click="changeLeftTab(item)"
          >
            {{ item.label }}
            <span v-if="[1, 4].includes(item.value)" class="tabs_box_item_icons">
              {{ item.Total ? (item.Total > 99 ? "99+" : item.Total) : 0 }}
            </span>
          </div>
        </el-row>
      </div>
      <div class="main_container">
        <div class="content __dynamicTabWrapper">
          <app-table
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :isShowOpatColumn="true"
            :isShowAllColumn="true"
            :loading="loading"
            :multable="false"
            :startOfTable="startOfTable"
            :serial="true"
            :isShowBtnsArea="false"
            :optColWidth="150"
            style="flex: 1"
          >
            <!-- 筛选 -->
            <template slot="conditionArea">
              <app-table-form
                label-width="100px"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="onResetSearch"
                layoutMode="simple"
                style="padding-top: 10px"
              >
                <template slot="Keywords">
                  <el-input
                    style="width: 100%"
                    placeholder="搜索标题、摘要、编号"
                    v-antiShake="{
                      time: 300,
                      callback: () => {
                        handleFilter();
                      },
                    }"
                    @clear="handleFilter"
                    clearable
                    v-model.trim="listQuery.Keywords"
                  ></el-input>
                </template>
                <template slot="ApprovalModuleSubtypeType">
                  <el-select
                    style="width: 100%"
                    filterable
                    clearable
                    v-model="listQuery.ApprovalModuleSubtypeType"
                    placeholder="类型"
                    @change="handleFilter"
                  >
                    <el-option
                      v-for="t in approvalTypeOptions"
                      :label="t.label"
                      :value="t.value"
                      :key="t.value"
                    />
                  </el-select>
                </template>
                <template slot="Date">
                  <el-date-picker
                    class="date_picker"
                    v-model="listQuery.Date"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    clearable
                    style="width: 100%"
                    @change="changeDate"
                  />
                </template>
                <template slot="ApprovalState">
                  <el-select
                    clearable
                    style="width: 100%"
                    v-model="listQuery.ApprovalState"
                    placeholder=""
                  >
                    <el-option
                      v-for="item in approvalStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </template>
                <template slot="PaymentState">
                  <el-select
                    clearable
                    style="width: 100%"
                    v-model="listQuery.PaymentState"
                    placeholder=""
                  >
                    <el-option
                      v-for="item in PAYMENT_OPERATION"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </template>
                <template slot="KingdeeDepartmentNumber">
                  <CompanySelect v-model="listQuery.KingdeeDepartmentNumber" :disabled="false" />
                </template>
                <template slot="btnsArea">
                  <el-button
                    v-if="listQuery.ApprovalTotalEnum !== 5"
                    type="primary"
                    style="margin-left: 5px"
                    :loading="exportLoading"
                    @click="handleBatchExport"
                  >
                    批量导出
                  </el-button>
                </template>
              </app-table-form>
            </template>
            <!-- 表格数据 -->
            <template slot="Title" slot-scope="{ row }">
              <span
                class="a-link"
                @click="handleApproval('detail', row)"
                v-if="listQuery.ApprovalTotalEnum !== 5"
              >
                {{ getObjData(row, "Title") }}
              </span>
              <span v-else>{{ getObjData(row, "Title") }}</span>
            </template>
            <template slot="Summary" slot-scope="{ row }">
              <span>{{ getObjData(row, "Summary") }}</span>
            </template>
            <template slot="Total" slot-scope="{ row }">
              <span>{{ formatThousands(getObjData(row, "Total")) }}</span>
            </template>
            <template slot="ApprovalModuleSubtypeType" slot-scope="{ row }">
              <span>{{ approvalTypeText(row.ApprovalModuleSubtypeType) }}</span>
            </template>
            <template slot="KingdeeDepartmentName" slot-scope="{ row }">
              <span>{{ getObjData(row, "KingdeeDepartmentName") }}</span>
            </template>
            <template slot="SubmitEmployeeList" slot-scope="{ row }">
              <span>{{ employeeText(getObjData(row, "SubmitEmployeeList")) }}</span>
            </template>
            <template slot="PayeeEmployeeList" slot-scope="{ row }">
              <span>{{ employeeText(getObjData(row, "PayeeEmployeeList")) }}</span>
            </template>
            <template slot="CreateTime" slot-scope="{ row }">
              <span>
                {{ getObjData(row, "CreateTime") | dateFilter("YYYY/MM/DD HH:mm") }}
              </span>
            </template>
            <template slot="FBillNo" slot-scope="{ row }">
              <span>{{ getObjData(row, "FBillNo") }}</span>
            </template>
            <template slot="ApprovalState" slot-scope="{ row }">
              <app-tag-pure
                v-if="Object.keys(getStatusObj(row.ApprovalState)).length"
                effect="dark"
                :color="getStatusObj(row.ApprovalState).color"
                :text="getStatusObj(row.ApprovalState).label"
              />
            </template>
            <template slot="PaymentState" slot-scope="{ row }">
              <app-tag-pure
                v-if="Object.keys(getPaymentStateObj(row.PaymentState)).length"
                effect="dark"
                :color="getPaymentStateObj(row.PaymentState).color"
                :text="getPaymentStateObj(row.PaymentState).label"
              />
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="{ row }">
              <template v-if="listQuery.ApprovalTotalEnum === 5">
                <app-table-row-button :type="1" text="编辑" @click="handleDraftEdit(row)" />
                <app-table-row-button :type="3" text="删除" @click="handleDelete(row)" />
              </template>
              <template v-else>
                <app-table-row-button
                  v-if="isShowApprovalBtn(row)"
                  :type="1"
                  text="审批"
                  @click="handleApproval('approval', row)"
                />
                <app-table-row-button
                  v-else
                  :type="1"
                  text="详情"
                  @click="handleApproval('detail', row)"
                />
                <el-dropdown
                  v-if="isShowPaymentBtn(row)"
                  trigger="click"
                  @command="handlePayment($event, row)"
                >
                  <app-table-row-button :type="1" text="付款操作" />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="1">已付款</el-dropdown-item>
                    <el-dropdown-item :command="2">抵借款</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <app-table-row-button
                  :type="1"
                  text="导出附件"
                  @click="handleExportAttachment(row)"
                />
                <app-table-row-button :type="1" text="打印" @click="handlePrint(row)" />
              </template>
            </template>
          </app-table>
        </div>
        <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 所有审批类弹框 -->
    <approvalCore ref="approvalCoreRef" @approvalSuccess="handleApprovalSuccess" />
    <!-- 导出 -->
    <v-export
      @closeDialog="dialogExportVisible = false"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData="cData"
    />
    <!-- 打印借款申请 -->
    <LoanApplicationPrint ref="loanApplicationPrintRef" />
    <!-- 打印费用报销 -->
    <ExpenseRbtPrint ref="expenseRbtPrintRef" />
    <!-- 打印付款申请 -->
    <PaymentReqPrint ref="paymentReqPrintRef" />
    <!-- 打印差旅费报销 -->
    <TravelExpensesPrint ref="travelExpensesPrintRef" />
    <!-- 费用报销弹框 -->
    <ExpenseRbtDialog
      v-if="showExpenseRbtDialog"
      :dialogFormVisible="showExpenseRbtDialog"
      dialogStatus="editDraft"
      :id="currentItem.Id"
      :processId="currentItem.HRApprovalProcessId"
      :isOnlyViewDetail="false"
      @closeDialog="showExpenseRbtDialog = false"
      @reload="getList"
    />
    <!-- 付款申请弹框 -->
    <PaymentReqDialog
      v-if="showPaymentReqDialog"
      :dialogFormVisible="showPaymentReqDialog"
      dialogStatus="editDraft"
      :id="currentItem.Id"
      :processId="currentItem.HRApprovalProcessId"
      :isOnlyViewDetail="false"
      @closeDialog="showPaymentReqDialog = false"
      @reload="getList"
    />
    <!-- 差旅费报销弹框 -->
    <TravelExpensesDialog
      v-if="showTravelExpensesDialog"
      :dialogFormVisible="showTravelExpensesDialog"
      dialogStatus="editDraft"
      :id="currentItem.Id"
      :processId="currentItem.HRApprovalProcessId"
      :isOnlyViewDetail="false"
      @closeDialog="showTravelExpensesDialog = false"
      @reload="getList"
    />
    <!-- 借款申请弹框 -->
    <LoanApplicationDialog
      v-if="showLoanApplicationDialog"
      :dialogFormVisible="showLoanApplicationDialog"
      dialogStatus="editDraft"
      :id="currentItem.Id"
      :processId="currentItem.HRApprovalProcessId"
      :isOnlyViewDetail="false"
      @closeDialog="showLoanApplicationDialog = false"
      @reload="getList"
    />
  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import approvalCore from "@/views/workbench/myApproval/approvalCore";
import CompanySelect from "@/views/financialManagement/reimbursement/components/CompanySelect";
import vExport from "@/components/Export/index";
import LoanApplicationPrint from "./components/print/LoanApplicationPrint";
import ExpenseRbtPrint from "./components/print/ExpenseRbtPrint";
import PaymentReqPrint from "./components/print/PaymentReqPrint";
import TravelExpensesPrint from "./components/print/TravelExpensesPrint";
import { getFinanceListApi } from "@/api/approval.js";
import { vars as projectDevVars } from "@/views/projectDev/common/vars.js";
import { formatThousands } from "@/utils/money.js";
import { getStatistics, putRead } from "@/api/projectDev/projectMgmt/approvalSetting.js";
import { packDownloadFinanceApi } from "@/api/businessMap.js";
import { downloadFile } from "@/utils";
import * as approvalManagement from "@/api/approvalManagement.js";
import { PAYMENT_OPERATION } from "@/views/financialManagement/reimbursement/vars.js";
import { getUserInfo } from "@/utils/auth";

export default {
  name: "ReimbursementView",
  mixins: [indexPageMixin],
  components: {
    approvalCore,
    CompanySelect,
    vExport,
    LoanApplicationPrint,
    ExpenseRbtPrint,
    PaymentReqPrint,
    TravelExpensesPrint,
    ExpenseRbtDialog: () =>
      import("@/views/financialManagement/reimbursement/components/ExpenseRbtDialog"),
    PaymentReqDialog: () =>
      import("@/views/financialManagement/reimbursement/components/PaymentReqDialog"),
    TravelExpensesDialog: () =>
      import("@/views/financialManagement/reimbursement/components/TravelExpensesDialog"),
    LoanApplicationDialog: () =>
      import("@/views/financialManagement/reimbursement/components/LoanApplicationDialog"),
  },
  data() {
    return {
      PAYMENT_OPERATION,
      approvalStatus: projectDevVars.approvalStatuObj.approvalStatus,
      tabDatas: [],
      listQuery: {
        ApprovalTotalEnum: 1,
        Keywords: "",
        ApprovalModuleSubtypeType: null,
        Date: [],
        StartTime: null,
        EndTime: null,
        ApprovalState: null,
        PaymentState: null,
        KingdeeDepartmentNumber: null,
      },
      total: 0,
      loading: false,
      dialogStatus: "approval",
      id: "",
      optType: -1,
      approvalId: "", // 审批编号
      currentItem: {},
      isOnlyViewDetail: false,
      dialogExportVisible: false,
      leftTabs: [...projectDevVars.approvalStatuObj.searchType, { value: 5, label: "待提交审批" }],
      exportLoading: false,
      rData: {},
      cData: [],
      showExpenseRbtDialog: false, // 费用报销弹框
      showPaymentReqDialog: false, // 付款申请弹框
      showTravelExpensesDialog: false, // 差旅费报销弹框
      showLoanApplicationDialog: false, // 借款申请弹框
    };
  },
  created() {
    this.getList();
  },
  computed: {
    // 流程类型
    approvalTypeText() {
      return value => {
        const map = projectDevVars.approvalStatuObj.approvalModuleSubtypeTypes;
        return map.find(t => t.value === value)?.label || "";
      };
    },
    // 人员数据处理
    employeeText() {
      return list => {
        return list.map(t => t.Name).join(",");
      };
    },
    // 流程状态
    getStatusObj() {
      return value => {
        return projectDevVars.approvalStatuObj.approvalStatus.find(s => s.value == value) || {};
      };
    },
    // 付款状态
    getPaymentStateObj() {
      return value => {
        return PAYMENT_OPERATION.filter(t => t.value !== 3).find(s => s.value == value) || {};
      };
    },
    // 审批类型
    approvalTypeOptions() {
      const options = projectDevVars.approvalStatuObj.approvalModuleSubtypeTypes.filter(t =>
        [68, 69, 70, 71].includes(t.value)
      );
      return [{ value: null, label: "全部" }, ...options];
    },
    // 审批按钮
    isShowApprovalBtn() {
      return row => {
        return row.IsCurrentNodeApprovalOperatorEmployee && this.listQuery.ApprovalTotalEnum == 1;
      };
    },
    /// 付款状态操作按钮
    isShowPaymentBtn() {
      return row => {
        // 出纳人
        const isCashierEmployee = !!row?.CashierEmployeeIdList?.find(
          t => t == getUserInfo().employeeid
        );
        return row.ApprovalState === 2 && isCashierEmployee && [null, 3].includes(row.PaymentState);
      };
    },
    tabColumns() {
      const arr = [
        { attr: { prop: "Title", label: "标题", width: 170, overflow: true }, slot: true },
        {
          attr: { prop: "Summary", label: "摘要", width: 200, showOverflowTooltip: true },
          slot: true,
        },
        { attr: { prop: "Total", label: "合计金额", width: 120 }, slot: true },
        { attr: { prop: "ApprovalModuleSubtypeType", label: "流程类型", width: 100 }, slot: true },
        { attr: { prop: "KingdeeDepartmentName", label: "公司名称", minWidth: 140 }, slot: true },
        { attr: { prop: "SubmitEmployeeList", label: "发起人", width: 100 }, slot: true },
        { attr: { prop: "PayeeEmployeeList", label: "领款人", width: 100 }, slot: true },
        { attr: { prop: "CreateTime", label: "提审时间", width: 130 }, slot: true },
        { attr: { prop: "FBillNo", label: "编号", width: 160 }, slot: true },
        { attr: { prop: "ApprovalState", label: "流程状态", width: 100 }, slot: true },
        { attr: { prop: "PaymentState", label: "付款状态", width: 100 }, slot: true },
      ];

      const ApprovalTotalEnum = this.listQuery.ApprovalTotalEnum;
      return arr.filter(t => {
        if (ApprovalTotalEnum === 1) {
          return !["PaymentState"].includes(t.attr.prop);
        } else if (ApprovalTotalEnum === 5) {
          return !["ApprovalState", "FBillNo", "PaymentState"].includes(t.attr.prop);
        }

        return true;
      });
    },
    tableSearchItems() {
      const arr = [
        { prop: "Keywords", label: "关键词", mainCondition: true, showMainLabel: true },
        {
          prop: "ApprovalModuleSubtypeType",
          label: "类型",
          mainCondition: true,
          showMainLabel: true,
        },
        { prop: "Date", label: "提审时间" },
        { prop: "ApprovalState", label: "审批状态" },
        { prop: "PaymentState", label: "付款状态" },
        { prop: "KingdeeDepartmentNumber", label: "公司" },
      ];
      const ApprovalTotalEnum = this.listQuery.ApprovalTotalEnum;
      return arr.filter(t => {
        if (ApprovalTotalEnum === 1) {
          return !["PaymentState"].includes(t.prop);
        } else if (ApprovalTotalEnum === 5) {
          return !["ApprovalState", "PaymentState"].includes(t.prop);
        }
        return true;
      });
    },
  },
  methods: {
    formatThousands,
    // 安全获取列表深层数据
    getObjData(row, key, defaultValue = null) {
      return row?.FinanceApprovalObj?.[key] ?? defaultValue;
    },
    getList() {
      this.loading = true;
      this.getProjectGroupTypeCount();
      getFinanceListApi(this.listQuery)
        .then(res => {
          this.tabDatas = res.Items;
          this.total = res.Total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 审批
    handleApproval(diaStatus = "approval", row) {
      this.id = row.CurrentBusinessId;
      this.dialogStatus = diaStatus;
      this.optType = row.ApprovalModuleSubtypeType;

      // 需要根据状态来显示"审批模式"或"详情模式"
      if (this.listQuery.ApprovalTotalEnum == 1) {
        if (row.ApprovalState == 1) {
          //待审批
          this.dialogStatus = "approval";
        } else {
          //已经审批
          this.dialogStatus = "detail";
        }
      }

      if (this.listQuery.ApprovalTotalEnum == 1) {
        this.isOnlyViewDetail = false;
      } else {
        this.isOnlyViewDetail = true;
      }

      this.approvalId = row.Id;

      //打开审批弹框
      this.$refs.approvalCoreRef.handleApproval(
        this.dialogStatus,
        row.Id,
        row.CurrentBusinessId,
        row.ApprovalModuleSubtypeType,
        this.isOnlyViewDetail
      );

      if (this.dialogStatus == "detail" && this.listQuery.ApprovalTotalEnum == 4 && !row.IsRead) {
        this.setRead(row.Id);
      }
    },
    //审批成功
    handleApprovalSuccess({ approvalId, id, optType }) {
      this.getList();
    },
    setRead(id) {
      let params = {
        IdList: [id],
        BusinessReadType: 1,
        IsReadAll: false,
      };
      putRead(params).then(() => {
        this.getProjectGroupTypeCount();
      });
    },
    //获取统计数据
    getProjectGroupTypeCount() {
      getStatistics({ key: "IsFinance" }).then(res => {
        this.leftTabs = this.leftTabs.map(item => {
          const findItem = res.find(s => s.Type == item.value);
          if (findItem) {
            item.Total = findItem.Total;
          }
          return item;
        });
      });
    },
    // 导出附件
    handleExportAttachment(row) {
      packDownloadFinanceApi({
        BusinesIdList: [row.CurrentBusinessId],
      }).then(res => {
        downloadFile(res.Path);
      });
    },
    // 批量导出表格
    handleBatchExport() {
      this.rData = {
        exportSource: 37,
        columns: [],
        searchCondition: this.listQuery,
      };
      this.cData = [
        {
          label: "标题",
          value: "Title",
        },
        {
          label: "摘要",
          value: "Summary",
        },
        {
          label: "合计金额",
          value: "Total",
        },
        {
          label: "流程类型",
          value: "ApprovalModuleSubtypeType",
        },
        {
          label: "公司名称",
          value: "KingdeeDepartmentName",
        },
        {
          label: "发起人",
          value: "SubmitEmployeeName",
        },
        {
          label: "领款人",
          value: "PayeeEmployeeName",
        },
        {
          label: "提审时间",
          value: "CreateTime",
        },
        {
          label: "编号",
          value: "FBillNo",
        },
        {
          label: "审批状态",
          value: "ApprovalState",
        },
        {
          label: "付款状态",
          value: "PaymentState",
        },
      ];
      this.dialogExportVisible = true;
    },
    // 打印
    handlePrint(data) {
      const keyMap = {
        68: "expenseRbtPrintRef", // 费用报销单
        69: "paymentReqPrintRef", // 付款申请单
        70: "travelExpensesPrintRef", // 差旅费报销单
        71: "loanApplicationPrintRef", // 借款申请单
      };
      const refKey = keyMap[data.ApprovalModuleSubtypeType];
      if (refKey) {
        this.$refs[refKey].handlePrint(data);
      } else {
        this.$message.error("暂未支持打印");
      }
    },
    // 草稿点击编辑
    handleDraftEdit(row) {
      this.currentItem = row;
      const map = {
        68: () => (this.showExpenseRbtDialog = true), // 费用报销单
        69: () => (this.showPaymentReqDialog = true), // 付款申请单
        70: () => (this.showTravelExpensesDialog = true), // 差旅费报销单
        71: () => (this.showLoanApplicationDialog = true), // 借款申请单
      };
      map[row.ApprovalModuleSubtypeType]();
    },
    // 付款操作
    handlePayment(type, row) {
      const typeLabel = this.getPaymentStateObj(type)?.label || "";
      this.$confirm(`此操作将付款状态设为${typeLabel}, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        approvalManagement
          .editPaymentStateApi({
            Id: row.CurrentBusinessId,
            PaymentState: type,
          })
          .then(() => {
            this.$message.success("操作成功");
            this.getList();
          })
          .catch(err => {
            this.$message.error("操作失败");
          });
      });
    },
    handleDelete(row) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        approvalManagement.delPersonalInfo([row.Id]).then(res => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },
    changeLeftTab(item) {
      this.listQuery.ApprovalTotalEnum = item.value;
      this.getList();
    },
    changeDate(val) {
      this.listQuery.StartTime = val?.[0] ? `${val[0]} 00:00:00` : null;
      this.listQuery.EndTime = val?.[1] ? `${val[1]} 23:59:59` : null;
    },
    onResetSearch() {
      this.listQuery = {
        ...this.$options.data().listQuery,
        ApprovalTotalEnum: this.listQuery.ApprovalTotalEnum,
      };
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .bg-white {
    display: flex;
    .aside_container {
      display: flex;
      flex-direction: column;
      width: 150px;
      height: 100%;
      border-right: 1px solid $border-color-light;
      .tabs_box {
        &_item {
          background-color: #fff;
          padding: 10px;
          height: 40px;
          line-height: 20px;
          cursor: pointer;
          &_icons {
            background-color: #f56c6c;
            color: #fff;
            border-radius: 10px;
            font-size: 12px;
            padding: 2px 6px;
          }
          .svg-icon {
            float: left;
            margin-right: 10px;
          }
          &.active {
            background-color: #f0f7ff;
          }
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
    .main_container {
      flex: 1;
      width: 0;
      display: flex;
      flex-direction: column;
    }
  }
}
/deep/.el-table .cell {
  line-height: normal;
}
</style>
