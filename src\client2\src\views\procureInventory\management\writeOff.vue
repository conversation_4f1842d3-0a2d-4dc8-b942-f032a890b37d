<template>
    <div>
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='1400'
            :maxHeight='810'
        >
            <template slot="body">

            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="110px" v-loading="loading">
                <div>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="ERP账号" prop="erpList">
                                <erp-selector 
                                    listSelectorTitle='选择ERP账号'
                                    :listSelectorUrl='serviceArea.business + "/ERPAccount/GetListPage"'
                                    :multiple='false' :showType='2'
                                    :list='formData.erpList'
                                    :columns='erpColumns'
                                    key='service-erp'
                                    @change='handleChangeErp'
                                    :readonly="!editable"
                                ></erp-selector>
                                </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="销库时间"  prop="InventoryClosingTime">
                                <el-date-picker :disabled="!editable" format='yyyy-MM-dd HH:mm' style="width: 100%;" value-format='yyyy-MM-dd HH:mm'  v-model="formData.InventoryClosingTime" type="datetime" placeholder=""></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注">
                                <el-input :disabled="!editable" type="textarea" :rows="1" maxlength="500"  v-model="formData.Remark"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>


                     <el-card class="card sp-card" shadow="hover" style="margin-bottom:10px;"> 

                        <el-row style="border-bottom: 0.01px solid #dadada;padding-bottom:10px;margin-bottom:10px;">
                            <el-col :span="12"> <span style="color:black;font-size:14px;font-weight: bold;">报修单信息</span> </el-col>
                            <el-col :span="12" style="display:flex; justify-content: flex-end;">
                                <span @click="handleDetail()" style="color:#66b1ff;font-size:14px;font-weight: bold; cursor: pointer;">查看详情</span>
                                <i :class="!showDetailPanel ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" style="margin-left: 20px; cursor: pointer;" @click="toggle"></i>
                            </el-col>
                        </el-row>
                         
                         <el-row>
                            <el-col :span="12">
                                <el-form-item label="报修单编号">
                                    <span>{{formData.Code}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="报修设备">
                                    <span>{{formData.MaintenanceEquipmentStructPartList | equipmentFilter}}</span>
                                </el-form-item>
                            </el-col>

                            <div v-show="showDetailPanel">
                                <el-col :span="12">
                                    <el-form-item label="实施人员">
                                        <span>{{formData.HandlerEmployeeList | handlerEmployeeListFilter}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="报修时间">
                                        <span>{{formData.ReportTime | dateFilter('YYYY-MM-DD HH:mm', '无')}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="报修地区">
                                        <span>{{formData.RegionalName}}</span>
                                    </el-form-item>
                                </el-col>
                            </div>
                        </el-row>

                        <!-- <el-row>
                            <el-col :span="12">
                                <el-form-item label="配件名称">
                                    <span>{{formData.StructPartName}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="规格型号">
                                    <span>{{formData.SpecificationModel}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="使用数量">
                                    <span>{{formData.Count}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row> -->

                        <!-- <el-row>
                             <el-col :span="12">
                                <el-form-item label="用途分类">
                                    <span> {{ formData.ProcessMode | processModeFilter }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                     </el-card>


                     <el-card class="card" shadow="hover" style="margin-bottom:10px;">
                        
                        
                        <el-row>
                            <el-col :span="24" style="border-bottom: 1px solid #dadada; padding-bottom:10px"> <span style="color:black;font-size:14px;font-weight: bold;">用料核销</span> </el-col>
                        </el-row>

                        <app-table-core 
                            ref="mainTable" 
                            :isShowBtnsArea='false' 
                            :tab-columns="tabColumns" 
                            :tab-datas="formData.MaintenanceEquipmentStructPartList" 
                            :isShowAllColumn="true" 
                            :isShowOpatColumn="false"
                            :multable="false" 
                            :serial="false"
                            :span-method="objectSpanMethod"
                            height="400px"
                        >
                            <template slot="Name" slot-scope="scope">
                                {{ scope.row.Name }}
                            </template>
                            <template slot="IsWarranty" slot-scope="scope">
                                <span :style="'color:'+ isInsurance.find(s => s.value == scope.row.IsWarranty).color+';'">{{isInsurance.find(s => s.value == scope.row.IsWarranty).label}}</span>
                            </template>
                            <template slot="StructPartName" slot-scope="scope">
                                {{ scope.row.StructPartName }}
                            </template>
                            <template slot="SpecificationModel" slot-scope="scope">
                                {{ scope.row.SpecificationModel }}
                            </template>
                            <template slot="ProcessMode" slot-scope="scope">
                                {{ scope.row.ProcessMode | processModeFilter }}
                            </template>
                            <template slot="Count" slot-scope="scope">
                                {{ scope.row.Count }}
                            </template>
                            <template slot="IsChecked" slot-scope="scope">
                                <el-checkbox :disabled="!editable" v-model="scope.row[`check_id_${scope.row.MaintenancStructPartId}`]"></el-checkbox>
                            </template>
                            <template slot="UseMaterialEmployee" slot-scope="scope">
                                <div v-show="scope.row[`check_id_${scope.row.MaintenancStructPartId}`]">
                                    <normar-emp-selector 
                                        :readonly="!editable" 
                                        listSelectorTitle="选择用料人" 
                                        :listSelectorUrl='serviceArea.business + "/ImplementerManagement/GetListPage"' 
                                        :multiple="false" 
                                        :showType="2" 
                                        :list="scope.row.UseMaterialEmployee" 
                                        :condition="{RegionalId: RegionalId || null, ReverseCheckLevel: true}"
                                        key="service-users" 
                                        :isAutocomplete='true'
                                        @change="(users) => handleChangeUsers(users, scope.row)">
                                    </normar-emp-selector>
                                </div>
                            </template>
                            <template slot="MaterialInStockName" slot-scope="scope">
                                <div v-show="scope.row[`check_id_${scope.row.MaintenancStructPartId}`]">
                                    <el-autocomplete
                                        class="inline-input"
                                        style="width: 100%;"
                                        v-model.trim="scope.row.MaterialInStockName"
                                        :fetch-suggestions="querySearchIn"
                                        :placeholder="'请输入调入仓库'"
                                        :trigger-on-focus="false"
                                        :disabled="!editable"
                                        @select="(target) => handleSelectIn(target, scope.row)"
                                        @change="(val) => changeTransferInID(val, scope.row)"
                                    ></el-autocomplete>
                                </div>
                            </template>
                            <template slot="MaterialList" slot-scope="scope">
                                <div v-show="scope.row[`check_id_${scope.row.MaintenancStructPartId}`]">
                                    <select-material 
                                        v-if="editable"
                                        :ref="`selectmaterialRef_${scope.index}`"
                                        listSelectorTitle='选择物料'
                                        :listSelectorUrl='serviceArea.business + "/MaterialPersonalStock/GetListPage"'
                                        :multiple='true' 
                                        :showType='2'
                                        :list='scope.row.MaterialList'
                                        :columns='materialColumns'
                                        :condition="{MaterialEmployeeId: scope.row.UseMaterialEmployeeId}"
                                        key='service-erp'
                                        @change='(target) => handleChangeMaterial(target, scope.row)'
                                        :readonly="!editable || !scope.row.UseMaterialEmployeeId"
                                    >
                                        <el-button :disabled='!scope.row.UseMaterialEmployeeId' type="text" style="margin-left: 4px; border-radius: 3px" @click="chooseSelectmaterial(scope.index)" slot="reference">选择物料</el-button>
                                    </select-material>
                                    <div v-for="(ss, idx4) in scope.row.MaterialList" :key="`ss_${idx4}`">
                                        {{ ss.MaterialCode }} / {{ ss.MaterialName }}（{{ ss.UseMaterialCount }}）
                                    </div>
                                </div>
                            </template>
                        </app-table-core>
                     </el-card>
                      
                </div>
            </el-form>
                 
            </template>

            <template slot="footer">
        
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' v-if="editable" :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>

      <!-- 创建 -->
    <create-page
      v-if="dialogFormVisible && formData.Id"
      @closeDialog="closeDialog"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogPageStatus"
      :id="formData.Id"
    ></create-page>

    </div>
</template>

<script>

import { serviceArea } from "@/api/serviceArea"
import erpSelector from './erpSelector'
import normarEmpSelector from '../../afterSalesMgmt/common/normarEmpSelector'
import selectMaterial from './selectMaterial'
import * as odc from "@/api/operatingDataCenter";
import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";
import * as maintenOrderMgmt from '@/api/maintenanceCenter/maintenOrderMgmt'
import * as commonVars from "../common/vars";
import { vars } from '../../afterSalesMgmt/maintenCenter/common/vars'
import createPage from "../../afterSalesMgmt/maintenCenter/maintenOrderMgmt/create";
import * as erp from '@/api/erpManagement/erp' 
import { json } from "body-parser";
import dayjs from "dayjs"

  export default {
    name: "write-off-create",
     components: {
        erpSelector,
        normarEmpSelector,
        selectMaterial,
        createPage,
    },
    computed: {
        title() {
            if(this.dialogStatus == 'create') {
                return '核销'
            }else if(this.dialogStatus == 'update') {
                return '编辑核销'
            }else if(this.dialogStatus == 'detail') {
                return '详情'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail";
        },
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        RegionalId: {
            type: String,
            default: ''
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if(this.dialogStatus == 'create' || this.dialogStatus == 'detail') {
                        this.getDetail();
                    }

                    if (this.dialogStatus == "create") {
                        this.getCacheAccount()
                    }
                }
            },
            immediate: true
        },
    },
    filters: {
        handlingResultStatusFilter(status) {
            let obj = vars.maintenOrderMgmt.handlingResultStatus.find(
                s => s.value == status
            );
            if (obj) {
                return obj.label;
            }
            return status;
        },
        processModeFilter(model){
             let tmp = commonVars.vars.ProcessMode.find(s => s.value == model);
             //console.log(tmp)
            if (tmp) {
                return tmp.label;
            }
            return "无";
        },

        equipmentFilter(equipmentList){
            let list = (equipmentList || []).filter(s => s.rowspan > 0)
            if(list.length > 0){
                return list.map(v=>v.Name).toString()
            }
            return "无"
        },
        handlerEmployeeListFilter(employeeList){
    
            if(employeeList){
               return employeeList.map(v=>v.Name).toString()
            }
            return "无"
        },
    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            showDetailPanel: false,
            isInsurance: vars.maintenOrderMgmt.isInsurance,
            dialogPageStatus:"detail",
            dialogFormVisible:false,
            serviceArea,
            erpColumns: [
                {
                    attr: {
                        prop: "Account",
                        label: "ERP账号",
                    },
                },
                {
                    attr: {
                            prop: "RegionalName",
                            label: "所属地区",
                    },
                },
            ],

            materialColumns:[
                {
                    attr: {
                        prop: "MaterialCode",
                        label: "物料编码",
                    },
                },
                {
                    attr: {
                        prop: "MaterialName",
                        label: "物料名称",
                    },
                },
                 {
                    attr: {
                        prop: "Specifications",
                        label: "规格型号",
                    },
                },
                {
                    attr: {
                        prop: "MaterialStockName",
                        label: "仓库",
                    },
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "使用地区",
                    },
                },
                {
                    attr: {
                        prop: "MaterialCount",
                        label: "库存数量",
                    },
                },
                {
                    attr: {
                        prop: "ThisUse",
                        label: "本次使用",
                    },
                    slot: true
                },
            ],
            tabColumns: [
                {attr: {prop: "Idx", label: "序号", width: 50}},
                {attr: {prop: "Name", label: "设备名称", width: 150}, slot: true},
                {attr: {prop: "IsWarranty", label: "是否在保", width: 80}, slot: true},
                {attr: {prop: "StructPartName", label: "配件名称"}, slot: true},
                {attr: {prop: "SpecificationModel", label: "规格型号"}, slot: true},
                {attr: {prop: "ProcessMode", label: "用途分类", width: 100}, slot: true},
                {attr: {prop: "Count", label: "数量", width: 50}, slot: true},
                {attr: {prop: "IsChecked", label: "是否核销", width: 80}, slot: true},
                {attr: {prop: "UseMaterialEmployee", label: "用料人", renderHeader: this.renderHeaderCommonCol}, slot: true},
                {attr: {prop: "MaterialInStockName", label: "调入仓库", renderHeader: this.renderHeaderCommonCol}, slot: true},
                {attr: {prop: "MaterialList", label: "使用物料", renderHeader: this.renderHeaderCommonCol}, slot: true},
            ],
            applyNum:0,
            disabledBtn: false,
            rules: {
                erpList: { fieldName: "ERP账号", rules: [{ required: true }] },
                InventoryClosingTime:{ fieldName: "销库时间", rules: [{ required: true }] },
            },
            loading: false,
            formData: {
                erpList:[],
                InventoryClosingTime: '',
                Remark: '',
                Code: '',
                HandlerEmployeeList: [], //实施人员
                ReportTime: null, //报修时间
                RegionalName: '', //报修地点

                MaintenanceEquipmentStructPartList: [], //设备

            },
        };
    },
    methods: {
        toggle() {
            this.showDetailPanel = !this.showDetailPanel
        },
        renderHeaderCommonCol(h, { column }) {
            return (
                <div style='display: flex;'>
                    <div>
                    {
                        <span class='red'> *</span>
                    }
                    { 
                        <span style='margin: 0 2px;'>{column.label}</span>
                    }
                    </div>
                    {
                        column.property == "MaterialList" ? '' :
                        <div>
                            <el-button type="text" style='padding: 0; color: #67c23a;' disabled={!this.formData.MaintenanceEquipmentStructPartList || this.formData.MaintenanceEquipmentStructPartList.length == 0 ? true : false} on-click={() => this.batchCoverage(column)}>批量覆盖</el-button>
                        </div>
                    }
                </div>
            )
        },
        batchCoverage(prop) {
            let propName = prop.property
            this.formData.MaintenanceEquipmentStructPartList.forEach((row, idx) => {
                let temp = this.formData.MaintenanceEquipmentStructPartList[0]

                if(idx > 0) {
                    if(propName == 'UseMaterialEmployee') {
                        row.UseMaterialEmployee = temp.UseMaterialEmployee
                        row.UseMaterialEmployeeId = temp.UseMaterialEmployeeId
                    }else if(propName == 'MaterialInStockName') {
                        row.MaterialInStockName = temp.MaterialInStockName
                        row.MaterialInStock = temp.MaterialInStock
                        row.MaterialInStockFNumber = temp.MaterialInStockFNumber
                    }
                }
            })
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if(column.property == 'Idx' || column.property == 'Name' || column.property == 'IsWarranty') {
                return {
                    rowspan: row.rowspan,
                    colspan: 1
                }
            }
            return {
                rowspan: 1,
                colspan: 1
            }
        },
        getCacheAccount() {
            erp.getCacheAccount({}).then(res => {
                if(res) {
                    this.handleChangeErp([res])
                }
            })
        },
        handleDetail(){
            console.log(*********)
           this.dialogFormVisible = true;
        },

        closeDialog() {
           this.dialogFormVisible = false;
        },
        chooseSelectmaterial(idx) {
            let refs = this.$refs[`selectmaterialRef_${idx}`]
            refs && refs.handleShow()
        },
         handleChangeErp(erps) {
            this.$refs.formData.clearValidate('erpList');
            this.formData.ERPAccountId = erps[0].Id
            this.formData.erpList = erps
        },


        getDetail(){
            this.loading = true
            maintenOrderMgmt.detail({'id':this.id}).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);

                if(this.dialogStatus == 'create') {
                    if(!this.formData.InventoryClosingTime) {
                        this.formData.InventoryClosingTime = `${dayjs().format('YYYY-MM-DD HH:mm')}:00`
                    }
                }

                this.formData.MaintenanceEquipmentStructPartList = []


                //设备列表
                if(this.formData.MaintenanceEquipmentList && this.formData.MaintenanceEquipmentList.length > 0) {

                    this.formData.MaintenanceEquipmentList = this.formData.MaintenanceEquipmentList.map((s, index0) => {
                        s.label = s.Name;
                        s.value = s.Code;
                        //s.map(ss => ss.FaultPhenomenonList.map(sss => sss.MaintenancStructPartList))

                        //配件列表
                        let list = s.FaultPhenomenonList.reduce((prev, curr) => {
                            prev = prev.concat(curr.MaintenancStructPartList);
                            return prev;
                        }, []);

                        if(this.dialogStatus == 'create') {
                            list.map((ss, idx) => {
                                // 默认选中第一个“实施人员”
                                // if(this.formData.HandlerEmployeeList && this.formData.HandlerEmployeeList.length > 0) {
                                //     this.$set(ss, 'UseMaterialEmployeeId', this.formData.HandlerEmployeeList[0].EmployeeId)
                                // }else{
                                    // }
                                    
                                
                                ss.rowspan = idx == 0 ? list.length : 0

                                ss.Idx = index0 + 1

                                ss.Name = s.Name
                                ss.IsWarranty = s.IsWarranty
                                
                                ss.MaintenancStructPartId = ss.Id
                                

                                this.$set(ss, `check_id_${ss.Id}`, true)

                                this.$set(ss, 'UseMaterialEmployee', [])
                                this.$set(ss, 'UseMaterialEmployeeId', undefined)
                                
                                this.$set(ss, 'MaterialInStockName', '')
                                this.$set(ss, 'MaterialInStock', '')
                                this.$set(ss, 'MaterialInStockFNumber', '')
    
                                this.$set(ss, 'MaterialList', [])
                                this.$set(ss, 'MaterialCodeList', [])
               
                                return ss
                            })
                        }else{
                            this.formData.erpList = [res.ERPAccountInfo] || []

                            list.map((ss, idx) => {

                                ss.rowspan = idx == 0 ? list.length : 0
                                
                                ss.Idx = index0 + 1

                                ss.Name = s.Name
                                ss.IsWarranty = s.IsWarranty

                                ss.MaintenancStructPartId = ss.Id
                                
                                let listTemp = ss.MaterialCancelVerificationList
                                let flag = listTemp && listTemp.length > 0 && !!listTemp[0].UseMaterialEmployee

                                this.$set(ss, `check_id_${ss.Id}`, flag)

                                this.$set(ss, 'UseMaterialEmployee', listTemp.length > 0 ? [ss.MaterialCancelVerificationList[0].UseMaterialEmployee] : [])
                                this.$set(ss, 'UseMaterialEmployeeId', listTemp.length > 0 ? [ss.MaterialCancelVerificationList[0].UseMaterialEmployee.EmployeeId] : [])

                                this.$set(ss, 'MaterialInStockName', listTemp.length > 0 ? listTemp[0].MaterialInStockName : '')
                                this.$set(ss, 'MaterialInStock', '')
                                this.$set(ss, 'MaterialInStockFNumber', '')

                                this.$set(ss, 'MaterialList', listTemp.length > 0 ? listTemp.map(mater => {
                                    return {
                                        MaterialCode: mater.MaterialCode,
                                        MaterialName: mater.MaterialName,
                                        UseMaterialCount: mater.MaterialCount
                                    }
                                }) : [])
                                this.$set(ss, 'MaterialCodeList', [])
               
                                return ss
                            })
                        }

                        if(list && list.length > 0) {
                            this.formData.MaintenanceEquipmentStructPartList = this.formData.MaintenanceEquipmentStructPartList.concat(JSON.parse(JSON.stringify(list)))
                        }

                        return s
                    })

                }

            }).catch(err => {
                this.loading = false
            })
        },
        
        handleChangeUsers(users, item) {
            item.UseMaterialEmployee = users || []
            item.UseMaterialEmployeeId = users && users.length > 0 ? users[0].EmployeeId : undefined
        },

        handleChangeMaterial(result, item){
            //console.log(result)
            
            if(result){
                item.MaterialList = result
                item.MaterialCodeList = item.MaterialList.map(v=>{
                    return {
                        'UseMaterialCount' : v.UseMaterialCount,
                        'MaterialCode' : v.MaterialCode
                    }
                })
            }
        },

         querySearchIn(queryString, cb){
            let keywords = queryString.trim()
            odc.getStock({keyWords:keywords,ERPAccountId:this.ERPAccountId,}).then(res => {
                let result = res.map(v=>{
                    v.value = v.FName
                    return v
                });
                cb(result);
            }).catch(err => {
                
            });
        },

        handleSelectIn(target, item){
            // MaterialInStockName

            item.MaterialInStock = target.FStockId
            item.MaterialInStockFNumber = target.FNumber

            // this.formData.MaterialInStock = target.FStockId
            // this.formData.MaterialInStockFNumber = target.FNumber
       },

        changeTransferInID(val, item){
            item.MaterialInStock = ''
            item.MaterialInStockFNumber = ''
            item.MaterialInStockName = ''
        },

        resetFormData() {
            this.formData = {
               erpList:[],
            }
        },
     
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    
                    let list = postData.MaintenanceEquipmentStructPartList.filter(s => s[`check_id_${s.Id}`])

                    if(list.length == 0) {
                        this.$message({
                            message: "请勾选需要核销行",
                            type: "error"
                        });
                        return false
                    }

                    if(list.some(s => !s.UseMaterialEmployee || s.UseMaterialEmployee.length == 0)) {
                        this.$message({
                            message: "核销行用料人不能为空",
                            type: "error"
                        });
                        return false
                    }

                    if(list.some(s => !s.MaterialInStockName)) {
                        this.$message({
                            message: "核销行调入仓库不能为空",
                            type: "error"
                        });
                        return false
                    }

                    if(list.some(s => !s.MaterialList || s.MaterialList.length == 0)) {
                        this.$message({
                            message: "核销行使用物料不能为空",
                            type: "error"
                        });
                        return false
                    }

             
                    //整理需要提交的数据
                    let temp = {
                        MaintenanceId: postData.Id,
                        ERPAccountId: postData.erpList[0].Id,
                        InventoryClosingTime: postData.InventoryClosingTime,
                        Remark: postData.Remark,
                        MaintenanceEquipmentStructPartList: list.map(s => {
                            s.MaintenanceEquipmentId = s.MaintenanceEquipmentId
                            s.MaintenancStructPartId = s.Id
                            s.UseMaterialEmployeeId = s.UseMaterialEmployeeId
                            s.MaterialInStockName = s.MaterialInStockName
                            s.MaterialInStock = s.MaterialInStock
                            s.MaterialInStockFNumber = s.MaterialInStockFNumber
                            s.MaterialList = s.MaterialList
                            s.MaterialCodeList = s.MaterialCodeList
                            return s
                        })
                    }

                    let tempSave = () => {
                        this.disabledBtn = true
                        if (this.dialogStatus == "create") {
                            materialTransferApi.writeOff(temp).then(res => {
                                this.disabledBtn = false
                                this.$notify({
                                    title: "提示",
                                    message: "保存成功",
                                    type: "success",
                                    duration: 2000
                                });
                                this.$refs.appDialogRef.createData()
                            }).catch(err => {
                                this.disabledBtn = false
                            });
                        }
                    }

                    if(list.find(s => s.ThisUse <= 0)) {
                        this.$confirm("本次使用数量存在为0的物料，是否提交？", "提示", {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                        }).then(() => {
                            tempSave()
                        });
                    }else{
                        tempSave()
                    }
        
                } 
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },   
    }
};
</script>

<style lang="scss" scoped>
 
  .card{
    /deep/.el-card__body{
        padding: 10px;
    }

    /deep/.el-table__body-wrapper{
        overflow-y: auto!important;
    }
  }

  .sp-card{
    /deep/.el-form-item{
        margin-bottom: 0;
    }
  }

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}


.status-1 {
  background-color: red;
}

.status-2 {
  background-color: #409eff;
}

.status-3 {
  background-color: #00b050;
}


</style>