<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="部门管理" :subTitle="['企业组织结构及人员的管理页面']"></page-title> -->
        <div class="pageWrapper __dynamicTabContentWrapper">
            <div class="content __dynamicTabWrapper">
                <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :multable='false' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                    <template slot="ReportTime" slot-scope="scope">
                        {{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm') }}
                    </template>

                    <template slot="Status" slot-scope="scope">
                        {{ scope.row.Status | statusFilter }}
                    </template>
                    <template slot="CreateTime" slot-scope="scope">
                        {{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
                    </template>
                    

                    <!-- 表格查询条件区域 -->
                    <template slot="conditionArea">
                        <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                            <template slot="Keywords">
                                <el-input style="width: 100%;" 
                                    placeholder="输入标签名称"
                                    @clear='getList'
                                    v-antiShake='{
                                        time: 300,
                                        callback: () => {
                                            getList()
                                        }
                                    }' 
                                    clearable 
                                    v-model="listQuery.Keywords"
                                ></el-input>
                            </template>

                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
                            </template>
                        </app-table-form>
                    </template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1" text='编辑'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleTableDelete(scope.row)" :type="3" text='删除'></app-table-row-button>
                    </template>
                </app-table>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>
    <create-page
        @closeDialog="closeDialog"
        @saveSuccess="handleSaveSuccess"
        :dialogFormVisible="dialogFormVisible"
        :dialogStatus="dialogStatus"
        :id="id"
        @reload="getList"
    ></create-page>
</div>
</template>

<script>

import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import * as tag from "@/api/maintenanceCenter/tag";
import createPage from './create'


export default {
    name: "tags-mgmt",
    mixins: [indexPageMixin],
    components: {
        createPage,
    },
    props: {},
    filters: {

        // workingStateFilter(status) {
        //     const statusObj = employeeWorkingStateEnum.find((s) => s.value == status);
        //     if (statusObj) {
        //         return statusObj.label;
        //     }
        //     return "";
        // },
        // statusFilter(status) {
        //     const statusObj = statusEnum.find((s) => s.value == status);
        //     if (statusObj) {
        //         return statusObj.label;
        //     }
        //     return "";
        // },
        // sexFilter(value) {
        //     let obj = vars.common.genders.find(s => s.value == value)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return ''
        // },

        // nodutyFilter(value) {
        //     let duty = value.split(",");
        //     return duty[0];
        // },

    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
    },
    watch: {


    },
    created() {
        this.getList()
    },
    data() {
        return {
            layoutMode: 'simple',

            tableSearchItems: [{
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },
            ],

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "Name",
                        label: "标签名称",
                    },
                },
                {
                    attr: {
                        prop: "UsageCount",
                        label: "被使用次数",
                        sortable: "custom",
                    },
                },
                {
                    attr: {
                        prop: "NickName",
                        label: "创建人",
                    },
                },
                {
                    attr: {
                        prop: "CreateTime",
                        label: "创建时间",
                        sortable: "custom",
                    },
                    slot: true,
                },
            ],
            listQuery: {
                Keywords: "",
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

        };
    },
    methods: {
        onBtnClicked: function(domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog("create");
                break;
                case "btnExport":
                break;
                case "btnParamSetting":
                break;
                default:
                break;
            }
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },

        //获取成员列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            tag
                .getList(postData)
                .then((res) => {
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                    this.listLoading = false;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
        },

        // 多行删除
        // 多行删除
        handleTableDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id)
            } else {
                ids.push(rows.Id)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                tag.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },

        // 弹出添加框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        onResetSearch() {
            this.listQuery.Keywords = "";
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },


    },
};
</script>

<style lang="scss" scoped>
.content{
    padding-top: 0!important;
    .btns-wrapper{
        button{
            margin-left: 4px;
        }
    }
}

// .treeBox {
//     width: 100%;
//     height: calc(100% - 10px);
//     margin-top: 10px;

//     .elInput {
//         width: 230px;
//         margin-left: 10px;
//     }

//     .elTree {
//         height: calc(100% - 38px);
//         overflow: auto;
//         margin-top: 10px;
//         padding-bottom: 10px;
//     }
// }

.pageWrapper {
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .content {
        padding: 10px;
        padding-right: 0;
        padding-left: 0;

        .opt-wrapper {
            box-sizing: border-box;
            border-bottom: 1px solid #dcdfe6;
            padding-bottom: 10px;
        }
    }

}

</style>
