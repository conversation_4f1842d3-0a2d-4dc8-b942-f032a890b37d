<template>
<div class="app-container equipmentParts">
    <div class="bg-white">
        <!-- <page-title title="结构配件模板" :subTitle="['设备配件清单模板管理页面']"></page-title> -->
        <main class="cl">
            <aside class="fl" style="padding-bottom: 46px; position: relative;" v-loading="loading">
                <div>
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="paramModel.Name">
                        <!-- <el-button slot="append" type="primary" icon="el-icon-search" @click="handleFilter"></el-button> -->
                    </el-input>
                </div>
                <div>
                    <el-button v-if="btnMaintain=='btnMaintain'" type="primary" class="elButton" @click="addAccess()">创建设备配件模板</el-button>
                </div>
                <div>
                    <tags mode="list" v-if="taskList.length>0" :items="taskList" v-model="taskId" @change="handleTagsChange">
                        <template v-for="(task,idx) in taskList" :slot="task.value">
                            <div class="item_warpper" :key="idx">
                                <div>
                                    <span class="omit" :title="task.Name">{{task.Name}}</span>
                                    <span>
                                        <i class="el-icon-edit-outline" style="color:#409EFF;" v-if="btnMaintain=='btnMaintain'" @click.stop="edit(task)"></i>
                                        <i class="el-icon-delete" style="color:#F56C6C;" v-if="btnMaintain=='btnMaintain'" @click.stop="del(task.Id)"></i>
                                    </span>
                                </div>
                            </div>
                        </template>
                    </tags>
                    <no-data v-else></no-data>
                </div>
                <div style="position: absolute; left: 0; right: 0; bottom: 0;">
                    <pagination :total="total" small background :page.sync="paramModel.PageIndex" :size.sync="paramModel.PageSize" @pagination="handleCurrentChange" layout="prev, pager, next" :pager-count="5" />
                </div>
            </aside>
            <section class="fl __dynamicTabContentWrapper" v-loading="loading1">
                <page-title :title="departmentInfo+'('+listQuery.total1+')'"></page-title>
                <div v-show="taskList.length>0">
                    <el-button v-if="btnMaintain=='btnMaintain'" type="primary" class="elButton" @click="addSpec()">添加配件</el-button>
                </div>
                <div id="__dynamicTabCoreWrapper">
                    <app-table-core ref="mainTable" :tab-columns="tabColumns" :tab-datas="tableDatas" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable='false'>
                        <template slot='operation' slot-scope="scope">
                            <el-button v-if="btnMaintain=='btnMaintain'" type="text" @click="getPartDetail(scope.row.StructPartsSpecificationId,'equipmentDetail')">详情</el-button>
                            <el-button v-if="btnMaintain=='btnMaintain'" type="text" style="color:#F56C6C;" @click="deleter(scope.row.Id)">移除</el-button>
                        </template>
			<template slot='SupplierName' slot-scope='scope'>
                            {{scope.row.SupplierName ? scope.row.SupplierName : "无"}}
                        </template>
                    </app-table-core>
                </div>
                <pagination :total="listQuery.total1" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" layout="total, prev, pager, next, jumper" @pagination="handleCurrentChange1" @size-change="handleSizeChange" />
            </section>
        </main>
    </div>
    <create-device @closeDialog="closeDialog" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogAStatus" @saveSuccess="handleSaveSuccess" :msgs="cdMsg">
    </create-device>
    <!-- <add-accessories @closeDialog="closeAcceDialog" :dialogFormVisible="dialogAcceFormVisible" @saveSuccess="handleAcceSaveSuccess" v-loading="loading2" :fromData="accessoriesList">
    </add-accessories> -->
    <add-structural @closeDialog="closeAcceDialog" :dialogFormVisible="dialogAcceFormVisible" @handleChange="handleChangeSuccess">
        </add-structural>        
	<v-specifications @closeDialog="closeSpecDialog" :dialogFormVisible="dialogSpecFormVisible" :dialogStatus="dialogSpecStatus" :id="pId">
    </v-specifications>
</div>
</template>

<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
import NoData from "@/views/common/components/noData";
import indexPageMixin from "@/mixins/indexPage";
import createDevice from "./createDevice";
// import addAccessories from "../businessMap/addAccessories";
import addStructural from "../maintenCenter/maintenOrderMgmt/addStructural";
import vSpecifications from "../structuralParts/specifications";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import {
    vars
} from "../structuralParts/vars";

export default {
    name: "equipmentParts",
    /**组件 */
    components: {
        createDevice,
        // addAccessories,
        addStructural,        
	NoData,
        vSpecifications,
    },
    mixins: [indexPageMixin, tabDynamicHeightMixins],
    props: {

    },
    data() {
        return {
            dialogSpecFormVisible: false,
            btnMaintain: '',
            pId: '',
            leftId: '',
            loading: false,
            loading1: false,
            loading2: false,
            departmentInfo: '结构配件清单',
            dialogAStatus: 'create',
            dialogSpecStatus: 'equipmentDetail',
            dialogFormVisible: false,
            dialogAcceFormVisible: false,
            // keyParts:vars.keyParts,
            // riskCategories:vars.riskCategories,
            paramModel: {
                Name: '',
                PageIndex: 1,
                PageSize: 20,
            },
            total: 0,
            taskId: '',
            taskList: [],

            listQuery: {
                total1: 0,
                PageIndex: 1,
                PageSize: 20,
            },
            tabColumns: [{
                    attr: {
                        prop: "Name",
                        label: "配件名称",
                    }
                },
                {
                    attr: {
                        prop: "SpecificationModel",
                        label: "规格型号",
                    },
                },
                {
                    attr: {
                        prop: "SupplierName",
                        label: "供应商",
                    },
slot: true
                },
                {
                    attr: {
                        prop: "operation",
                        label: "操作",
                    },
                    slot: true
                },
            ],
            tableDatas: [],
            accessoriesList: [],
            cdMsg: null,
        };
    },
    watch: {
        'paramModel.Name': {
            handler: function (val) {
                let that = this;
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    that.handleFilter(val)
                }, 500)
            },
        }
    },
    created() {
        this.btnTextValue()
    },
    /**渲染后 */
    mounted() {
        this.getList(true);
    },
    methods: {
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },
        edit(d) {
            this.cdMsg = d;
            this.dialogAStatus = "edit";
            this.dialogFormVisible = true;
        },
        getPartDetail(id, dss) {
            this.pId = id;
            this.dialogSpecStatus = dss;
            this.dialogSpecFormVisible = true;
        },
        getList(t) {
            this.loading = true;
            let postData = {
                "pageIndex": this.paramModel.PageIndex,
                "pageSize": this.paramModel.PageSize,
                "name": this.paramModel.Name,
            }
            accessories.tempList(postData).then(res => {
                this.loading = false;
                // console.log(111, res)
                this.total = res.Total;
                this.taskList = res.Items;
                if (this.taskList.length > 0) {
                    this.taskId = this.taskList[0].Id;
                    this.leftId = this.taskList[0].Id;
                    if (t) {
                        this.getConditionData();
                    } else {
                        this.getPartSpecificationData();
                    }
                    this.taskList.forEach(v => {
                        v.value = v.Id;
                    })
                }else{
                    this.taskList = [];
                    this.tableDatas=[];
                }
                
            }).catch(err => {
                this.loading = false;
            })
        },
        closeSpecDialog() {
            this.dialogSpecFormVisible = false;
        },
        getConditionData() {
            this.loading1 = true;
            accessories.getListByCondition().then(res => {
                this.supplierList = res;
                this.getPartSpecificationData();
            })
        },
        handleChangeSuccess(cd,val,rId){
            console.log(777,cd,val)
            this.listQuery.PageIndex = 1;
            this.closeAcceDialog();
            let postData = {
                currentBusinessId: this.leftId,
                structPartsSpecificationIdList: [rId]
            };
            accessories.addPartSpecification(postData).then(res => {
                this.$notify({
                    title: '成功',
                    message: '添加成功！',
                    type: 'success'
                });
                this.loading1 = true;
                this.getPartSpecificationData();
            })
        },
        handleAcceSaveSuccess(d) {
            this.listQuery.PageIndex = 1;
            this.closeAcceDialog();
            let postData = {
                currentBusinessId: this.leftId,
                structPartsSpecificationIdList: []
            };
            d.forEach(v => {
                if (v.children.length > 0) {
                    v.children.forEach(v1 => {
                        postData.structPartsSpecificationIdList.push(v1.id);
                    })
                }
            })
            console.log(666, postData)
            accessories.addPartSpecification(postData).then(res => {
                this.$notify({
                    title: '成功',
                    message: '添加成功！',
                    type: 'success'
                });
                this.loading1 = true;
                this.getPartSpecificationData();
            })
        },
        getPartSpecificationData() {
            if (this.leftId) {
                let postData = {
                    "pageIndex": this.listQuery.PageIndex,
                    "pageSize": this.listQuery.PageSize,
                    "id": this.leftId
                }
                accessories.getPartSpecification(postData).then(res => {
                    this.loading1 = false;
                    this.listQuery.total1 = res.Total;
                    this.tableDatas = res.Items;
                }).catch(err => {
                    this.loading1 = false;
                })
            } else {
                this.listQuery.total1 = 0;
                this.tableDatas = [];
                this.loading1 = false;
            }
        },
        handleSaveSuccess(d) {
            if (!d) {
                this.closeDialog();
            }
            this.paramModel.PageIndex = 1;
            this.getList();
        },
        addAccess() {
            this.cdMsg = null;
            this.dialogAStatus = 'create';
            this.dialogFormVisible = true;
        },
        addSpec() {
	this.dialogAcceFormVisible = true;
           
	    // this.getAllListData();
        },
        getAllListData() {
            let arr = [];
            accessories.getAllList().then(res => {
                // console.log(333, res)
                res.forEach((v, index) => {
                    if (!v.ParentId) {
                        arr.push({
                            id: v.Id,
                            pid: index,
                            label: v.Name,
                            tId: (new Date()).valueOf().toString() + Math.random().toString().slice(-6),
                            children: []
                        })
                    }
                })
                arr.forEach((v, i) => {
                    res.forEach((v1, i1) => {
                        if (v.id == v1.ParentId) {
                            v.children.push({
                                id: v1.Id,
                                pid: v1.ParentId,
                                label: v1.Name,
                                tId: (new Date()).valueOf().toString() + Math.random().toString().slice(-6),
                            })
                        }
                    })
                })
                this.accessoriesList = arr.filter(v => v.children.length > 0);
                this.dialogAcceFormVisible = true;
                console.log(444, this.accessoriesList)
            })
        },
        /**分页页码切换 */
        handleCurrentChange(val) {
            this.paramModel.PageIndex = val.page;
            this.paramModel.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange1(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getPartSpecificationData();
        },
        handleSizeChange(val) {

        },
        changeParamModel() {
            this.paramModel.PageIndex = 1;
            // this.loadTaskList();
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        closeAcceDialog() {
            this.dialogAcceFormVisible = false;
        },
        del(d) {
            this.$confirm('确定删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                accessories.deltemp([d]).then(res => {
                    this.$notify({
                        title: '成功',
                        message: '移除成功！',
                        type: 'success'
                    });
                    this.getList();
                })
            }).catch(() => {

            });

        },
        deleter(d) {
            this.$confirm('确定移除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                accessories.deletePartSpecification([d]).then(res => {

                    this.$notify({
                        title: '成功',
                        message: '移除成功！',
                        type: 'success'
                    });
                    this.getPartSpecificationData();
                })
            }).catch(() => {

            });

        },
        handleTagsChange(d) {
            this.leftId = d;
            // this.taskData=this.taskList.find(v => v.Id == d);
            this.listQuery.PageIndex = 1;
            this.getPartSpecificationData();
        },
        handleFilter() {
            this.paramModel.PageIndex = 1;
            this.getList();
        },
    }
};
</script>

<style lang="scss" scoped>
.item_warpper {
    >div:nth-child(1) {
        display: flex;
        justify-content: space-between;

        >span:nth-child(1) {
            width: 156px;
        }
    }

    >div:nth-child(2) {
        display: flex;
        margin-top: 16px;

        span {
            border: 1px solid #aaaaaa;
            color: #aaaaaa;
            padding: 2px;
            border-radius: 6px;
        }

        >span:first-child {
            margin-right: 5px;
        }
    }
}

main {
    // height: calc(100% - 40px);
    height: 100%;

    >aside {
        width: 250px;
        height: 100%;
        border-right: 1px solid #DCDFE6;

        >div:nth-child(1) {
            padding: 10px 10px 0 10px;
        }

        >div:nth-child(2) {
            padding: 10px 0;
            text-align: center;
            border-bottom: 1px solid #DCDFE6;

            button {
                width: 180px;
            }
        }

        >div:nth-child(3) {
            height: calc(100% - 99px);
            overflow: auto;

            >div {
                margin-top: 10px;
            }
        }

        >div:nth-child(4) {
            width: 100%;
            border-top: 1px solid #dcdfe6;
        }
    }

    >section {
        width: calc(100% - 250px);
        height: 100%;

        >div:nth-child(2) {
            padding: 10px;
        }

        >div:nth-child(3) {
            padding: 0 10px;
            height: calc(100% - 92px);
            overflow: auto;
        }
    }
}

.el-icon-edit-outline {
    color: #409EFF;
}

.el-icon-delete {
    color: red;
}
</style>
