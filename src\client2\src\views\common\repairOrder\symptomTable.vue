<template>
    <div>
        <app-table-row-button @click="handleAddRow" text='新增故障现象' v-show="editable" :type='2'></app-table-row-button>
        <el-table border :data="datas" style="width: 100%">
            <el-table-column label="故障现象" :render-header="renderHeader">
                <template slot-scope="scope">
                    <!-- <el-tag size="mini" :type="scope.row.Symptom.InputMode == 1 ? '' : 'warning'" v-show="scope.row.Symptom.Text"></el-tag> -->
                    <app-tag :text='scope.row.Symptom.Text' :closable='false' size="mini" :type="scope.row.Symptom.InputMode == 1 ? '' : 'warning'" v-show="scope.row.Symptom.Text"></app-tag>
                    <symptom-add :checkedList='getChecked(scope.row, 1)' v-show="editable && !scope.row.Symptom.InputMode || scope.row.Symptom.InputMode == 0" key="symptom" :optType='1' @success='data => handleSaveSymptom(data, scope.row)'></symptom-add>
                </template>
            </el-table-column>
            
            <el-table-column label="原因分析" :render-header="renderHeader">
                <template slot-scope="scope">
                    <app-tag :text='ana.Text' size="mini" :type="ana.InputMode == 1 ? '' : 'warning'" :closable='editable' @close='handleCloseAnalysis(scope.row.Analysis, anaIdx)' v-for="(ana, anaIdx) in scope.row.Analysis" :key="anaIdx"></app-tag>
                    <!-- <el-tag size="mini" :type="ana.InputMode == 1 ? '' : 'warning'" :closable='editable' @close='handleCloseAnalysis(scope.row.Analysis, anaIdx)' v-for="(ana, anaIdx) in scope.row.Analysis" :key="anaIdx">{{ ana.Text }}</el-tag> -->
                    <symptom-add :pid='scope.row.Symptom.Id' v-show="editable" :mainInputMode='scope.row.Symptom' :checkedList='getChecked(scope.row, 2)' :disabled='scope.row.Symptom.InputMode == 0' key="analysis" :optType='2' @success='data => handleSaveAnalysis(data, scope.row, 2)'></symptom-add>
                </template>
            </el-table-column>
            <el-table-column label="解决方法" :render-header="renderHeader">
                <template slot-scope="scope">
                    <app-tag :text='sol.Text' size="mini" :type="sol.InputMode == 1 ? '' : 'warning'" :closable='editable' @close='handleCloseSolutions(scope.row.Solutions, solIdx)' v-for="(sol, solIdx) in scope.row.Solutions" :key="solIdx"></app-tag>
                    <!-- <el-tag size="mini" :type="sol.InputMode == 1 ? '' : 'warning'" :closable='editable' @close='handleCloseSolutions(scope.row.Solutions, solIdx)' v-for="(sol, solIdx) in scope.row.Solutions" :key="solIdx">{{ sol.Text }}</el-tag> -->
                    <symptom-add :pid='scope.row.Symptom.Id' v-show="editable" :mainInputMode='scope.row.Symptom' :checkedList='getChecked(scope.row, 3)' :disabled='scope.row.Symptom.InputMode == 0' key="solutions" :optType='3' @success='data => handleSaveAnalysis(data, scope.row, 3)'></symptom-add>
                </template>
            </el-table-column>
            <!-- <el-table-column label="" width="50">
                <template slot-scope="scope">
                    <el-button type="text" @click="() => removeRow(scope.$index)" class="danger">删除</el-button>
                </template>
            </el-table-column> -->
            <!-- 操作列 -->
            <el-table-column width="100" :fit='true' fixed="right" label="操作" v-if="isShowOpatColumn">
                <template slot-scope="scope">
                    <!-- 将作用域插槽返回的对象scope继续通过作用域插槽暴露出去 -->
                    <slot :obj="{row: scope.row, index: scope.$index}"></slot>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import SymptomAdd from './symptomAdd'
import * as fc from '@/api/failurecase'
export default {
    name: 'smptom-table-list',
    components: {
        SymptomAdd,
    },
    props: {
        //控制组件内部的操作按钮（新增行、新增单元格内容、删除单元格内容——操作按钮列由外部控制（内部只控制是否显示操作列））
        editable: {
            type: Boolean,
            default: true
        },
        //控制是否显示操作列
        isShowOpatColumn: {
            type: Boolean,
            default: true
        },
        list: {
            type: Array,
            default: function() {
                return []
                // return [
                //     //  {
                //     //     Symptom: {
                //     //         InputMode: 1, //1： 输入；2：选择
                //     //         Id: '',
                //     //         Text: "", //  Type=1，存输入字符串；type=2，存guid
                //     //     },
                //     //     Solutions: [ //解决方案，支持多选
                //     //         {
                //     //             InputMode: 1, //1: 输入字符，2：选择guid
                //     //             Id: '',
                //     //             Text: "" // Type=1，存输入字符串；type=2，存guid
                //     //         }
                //     //     ], 
                //     //     Analysis: [ //原因分析
                //     //         {
                //     //             InputMode: 1, //1: 输入字符，2：选择guid
                //     //             Id: '',
                //     //             Text: "" // Type=1，存输入字符串；type=2，存guid
                //     //         }
                //     //     ]
                //     // }
                // ]
            }
        }
    },
    watch: {
        list: {
            handler (val) {
                this.datas = JSON.parse(JSON.stringify(val))
            },
            deep: true
        },
    },
    mounted() {
        this.datas = JSON.parse(JSON.stringify(this.list))
    },
    data() {
        return {
            datas: [],
        }
    },
    methods: {
        removeRow(idx) {
            this.$confirm('是否确认删除当前行?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if(this.datas && this.datas.length > 0){
                    this.datas.splice(idx, 1)
                }
            })
        },
        handleAddRow() {
            if(!this.datas){
                this.datas = []
            }

            this.datas.push({
                Symptom: {
                    InputMode: 0, 
                    Text: '',
                },
                Solutions: [],
                Analysis: []
            })

            this.$emit('changed', this.datas)
        },
        getChecked(item, optType) {
            if(optType == 1){
                if(item.InputMode == 2) {
                    return [item.Symptom.Text.map(o => {
                        return {
                            Id: o.Id,
                            Text: o.Text
                        }
                    })]
                }
            }else if(optType == 2){
                return item.Analysis.map(o => {
                    return {
                        Id: o.Id,
                        Text: o.Text
                    }
                })
            }else if(optType == 3){
                return item.Solutions.map(o => {
                    return {
                        Id: o.Id,
                        Text: o.Text
                    }
                })
            }
            return []
        },
        handleSaveSymptom({inputMode, desc}, item) {
            // let exists = this.datas.filter(i => i.Symptom.InputMode == inputMode)
            

            let exists = this.datas
            if(inputMode == 2){
                // exists = exists.filter(i => i.Symptom.Id == desc[0].Id)
                exists = exists.filter(i => i.Symptom.Text == desc[0].Text)
            }else if(inputMode == 1) {
                exists = exists.filter(i => i.Symptom.Text == desc)
            }

            if(exists && exists.length > 0){
                let err = inputMode == 2 ? desc[0].Text : desc
                this.$message({
                    message: `${err}已经存在，请勿重复添加`,
                    type: 'error'
                })
                return false
            }

            if(inputMode == 1) {
                item.Symptom.InputMode = inputMode
                item.Symptom.Text = desc
            }else if(inputMode == 2) {
                if(desc && desc.length > 0){
                    let newObj = {
                        InputMode: inputMode,
                        Id: desc[0].Id,
                        Text: desc[0].Text
                    }
                    if(item.Symptom && item.Symptom.Id == newObj.Id) {
                    }else{
                        item.Symptom.InputMode = inputMode
                        item.Symptom.Text = newObj.Text
                        item.Symptom.Id = newObj.Id
                    }
                    
                    fc.getFailureAnalysises({failureCaseId: desc[0].Id}).then(res => {
                        let anas = res.map(f => {
                            return { 
                                InputMode: inputMode,
                                Id: f.FailureAnalysisId,
                                Text: f.FailureReson
                            }
                        })

                        item.Analysis = item.Analysis.concat(anas)
                    })
                    fc.getFailureSolutions({failureCaseId: desc[0].Id}).then(res => {
                        let sols = res.map(f => {
                            return { 
                                InputMode: inputMode,
                                Id: f.FailureSolutionId, 
                                Text: f.FailureSolution
                            }
                        })
                        item.Solutions = item.Solutions.concat(sols)
                    })
                }
            }
            this.$emit('changed', this.datas)
        },
        renderHeader (h,{column}) {
            return h('span', [
                h('span', column.label),
                h('span', {
                    style: 'color: red'
                }, ' *')
            ])
            // return h('div',[ 
            //         h('span', column.label),
            //         h('i', {
            //         class:'el-icon-location',
            //         style:'color:#409eff;margin-left:5px;'
            //     })
            // ],);
        },        
        handleSaveAnalysis({inputMode, desc}, item, optType) {
            if(inputMode == 1){ //输入
                if(optType == 2) {
                    item.Analysis.push({
                        InputMode: inputMode,
                        Text: desc
                    })
                }else if(optType == 3){
                    item.Solutions.push({
                        InputMode: inputMode,
                        Text: desc
                    })
                }
            }else if(inputMode == 2){//选择
                if(desc.length > 0){
                    let tmps = desc.map(o => {
                        return {
                            InputMode: inputMode,
                            Id: o.Id,
                            Text: o.Text
                        }
                    })

                    let arr = tmps.map(i => i.Text)
                    let err = ''
                    if(optType == 2){
                        err += '原因分析'
                    }else if(optType == 3){
                        err += '解决方法'
                    }
                    if((new Set(arr)).size != arr.length){
                        this.$message({
                            message: `${err}存在重复，请重新选择`,
                            type: 'error'
                        })
                        return false
                    }

                    if(optType == 2){
                        tmps.forEach(i => {
                            let ifExists = item.Analysis.findIndex(u => u.Id == i.Id) == -1
                            if(ifExists){
                                item.Analysis.push(i)
                            }
                        })
                        // item.Analysis = item.Analysis.concat(tmps)
                    }else if(optType == 3){
                        tmps.forEach(i => {
                            let ifExists = item.Solutions.findIndex(u => u.Id == i.Id) == -1
                            if(ifExists){
                                item.Solutions.push(i)
                            }
                        })
                        // item.Solutions = item.Solutions.concat(tmps)
                    }

                    //还需要删除
                    
                }
            }
            this.$emit('changed', this.datas)
        },    
        handleCloseAnalysis(analysis, idx) {
            analysis.splice(idx, 1)
            this.$emit('changed', this.datas)
        },
        handleCloseSolutions(soluteions, idx) {
            soluteions.splice(idx, 1)
            this.$emit('changed', this.datas)
        },    
    }
}
</script>

<style scoped>
.danger{
    color: #F56C6C;
}
</style>