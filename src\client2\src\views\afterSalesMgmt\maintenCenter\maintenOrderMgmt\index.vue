<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title
        title="报修单管理"
        :subTitle="['设备报修单的创建、处理、管理页面']"
        :showBackBtn="!!returnUrl"
        @goBack="handleGoBack"
      ></page-title> -->
      <div style="height: 100%;" class="__dynamicTabContentWrapper">
        <div class="content __dynamicTabWrapper">
          <app-table
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="[]"
            :isShowAllColumn="true"
            :loading="listLoading"
            @rowSelectionChanged="rowSelectionChanged"
            :isShowOpatColumn="true"
            :startOfTable="startOfTable"
            :multable="true"
            @sortChagned='handleSortChange'
            fit
          >
            <template slot="MaintenanceStatus" slot-scope="scope">
              <maintenOrderStatus :status="scope.row.MaintenanceStatus"></maintenOrderStatus>
            </template>
            <!-- <template slot="ApprovalStatus" slot-scope="scope">
              <span
                v-if="getApprovalObj(scope.row.ApprovalStatus).label"
                class="item-status"
                :style="{
                  backgroundColor: getApprovalObj(scope.row.ApprovalStatus).color
                }"
              >
                <span>{{ getApprovalObj(scope.row.ApprovalStatus).label }}</span>
              </span>
              <span v-else>无</span>
            </template> -->
            <template slot="IsSignBill" slot-scope="scope">
              <span :style="'color:'+(scope.row.IsSignBill ? serviceListStatus[scope.row.IsSignBill-1].color : '#909399')+';'">{{scope.row.IsSignBill ? serviceListStatus[scope.row.IsSignBill-1].label : '无'}}</span>
            </template>
            <template slot="ServiceNo" slot-scope="scope">{{
              scope.row.ServiceNo ? scope.row.ServiceNo : '无'
            }}</template>
            <template slot="AfterContractCode" slot-scope="scope">{{
              scope.row.AfterContractCode ? scope.row.AfterContractCode : '无'
            }}</template>
            <template slot="ReportTime" slot-scope="scope">{{
              scope.row.ReportTime | dateFilter("YYYY-MM-DD HH:mm")
            }}</template>
            <template slot="ReportEmployee" slot-scope="scope">{{
              scope.row.ReportEmployee | nameFilter
            }}</template>

            <template slot="TotalTimeEmployeeList" slot-scope="scope">
                <span v-for="(emp, idx) in scope.row.EmployeeList" :key="idx">
                    {{ getTotalTime(emp.EmployeeId, scope.row.TotalTimeEmployeeList) | totalTimeFilter }}
                    <template v-if="idx < scope.row.EmployeeList.length - 1">/</template>
                </span>
            </template>

            <template slot="HandlerEmployeeList" slot-scope="scope">
              <span v-if="scope.row.HandlerEmployeeList">{{
                scope.row.HandlerEmployeeList.map(s => s.Name).join(",")
              }}</span>
              <span v-else>无</span>
            </template>

            <!-- 表格查询条件区域 -->
            <template slot="conditionArea">
              <div
                style="margin-bottom: 10px; border-bottom: 1px solid #EBEEF5;"
              >
                <tags
                  :items="handlerEmployeeIds"
                  v-model="listQuery.TableActive"
                >
                  <template v-for="t in handlerEmployeeIds" :slot="t.label">{{
                    t.label
                  }}</template>
                </tags>
                <div style="position: absolute; top: 4px; right: 20px;">
                  <span style="margin-right: 10px;">切换地区</span>
                  <el-select v-model="RId" placeholder="请选择">
                    <el-option
                    @click.native="handleSelectChange(item.Id)"
                      v-for="item in firstAreaTree"
                      :key="item.Id"
                      :label="item.RegionalName"
                      :value="item.Id">
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div
                style="border-bottom: 1px solid #EBEEF5;"
              >
                <tags :items="daysBetweens" v-model="status">
                  <template v-for="t in daysBetweens" :slot="t.label">{{
                    t.label
                  }}</template>
                </tags>
              </div>
              <app-table-form
                :label-width="'100px'"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="handleResetSearch"
                @collapseOrExpand="collapseOrExpand"
              >
                <!-- :default-expand-level="3" -->
                <template slot="RegionalId">
                  <!-- <treeselect
                    :normalizer="normalizer"
                    key="type1"
                    v-model="listQuery.RegionalId"
                    :options="listToTreeSelect(treeOptions)"
                    :multiple="false"
                    placeholder
                    :show-count="true"
                    :noResultsText="noResultsTextOfSelTree"
                    :noOptionsText="noOptionsTextOfSelTree"
                  ></treeselect> -->
                  <div class="el-input el-input--mini">
                    <div style="display: flex; height: 32px;line-height: 32px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; ">
                      <div style="padding-left: 10px;">
                        <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">选择</span>
                      </div>
                      <div style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="listQuery.areaName">{{ listQuery.areaName }}</div>
                      <div style="width: 32px; text-align: center;">
                        <i style="cursor: pointer;" title="删除" @click="handleClearRegional" v-show="listQuery.RegionalId" class="el-icon-close"></i>
                      </div>
                    </div>
                  </div>
                  
                </template>
                <!-- <template slot="ReportTimeStart">
                              <el-date-picker style="width: 100%;" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="listQuery.ReportTimeStart" type="date"></el-date-picker>
                          </template>
                          <template slot="ReportTimeEnd">
                              <el-date-picker style="width: 100%;" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="listQuery.ReportTimeEnd" type="date"></el-date-picker>
                </template>-->
                <template slot="ReportRange">
                  <el-date-picker
                    style="width: 100%;"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm"
                    v-model="listQuery.ReportRange"
                    type="datetimerange"
                    :picker-options="pickerOptions"
                    :default-time="['00:00:00', '23:59:00']"
                  ></el-date-picker>
                </template>
                <template slot="IsSignBill">
                  <el-select
                    class="sel-ipt"
                    style="width:100%"
                    placeholder="请选择"
                    clearable
                    v-model="listQuery.IsSignBill"
                  >
                    <el-option
                      v-for="item in serviceListStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </template>
                <!-- <template slot="MaintenanceStatus">
                  <el-select
                    class="sel-ipt"
                    style="width:100%"
                    placeholder
                    clearable
                    multiple
                    collapse-tags
                    v-model="listQuery.MaintenanceStatus"
                  >
                    <el-option
                      v-for="item in status"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </template> -->
                
                <template slot="FaultTypeList">
                  <el-select
                    class="sel-ipt"
                    style="width:100%"
                    placeholder="请选择"
                    clearable
                    v-model="listQuery.FaultTypeList"
                    multiple
                    collapse-tags
                  >
                  
                    <el-option
                      v-for="item in faultTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </template>
                <template slot="IsReplacePart">
                  <el-select
                    class="sel-ipt"
                    style="width:100%"
                    placeholder="请选择"
                    clearable
                    v-model="listQuery.IsReplacePart"
                  >
                    <el-option label="有" :value="true"></el-option>
                    <el-option label="无" :value="false"></el-option>
                  </el-select>
                </template>

                
                <template slot="AfterContractCode">
                  <el-input
                    style="width: 100%;"
                    v-model.trim="listQuery.AfterContractCode"
                    placeholder
                  ></el-input>
                </template>
                <template slot="EmployeeName">
                  <el-input
                    style="width: 100%;"
                    v-model.trim="listQuery.EmployeeName"
                    placeholder
                  ></el-input>
                  <!-- <el-select
                    v-model="listQuery.HandlerEmployeeId"
                    style="width: 100%;"
                    placeholder
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in  implementerList"
                      :key="item.EmployeeId"
                      :label="item.Name"
                      :value="item.EmployeeId"
                    ></el-option>
                  </el-select> -->
                </template>
                <template slot="ServiceNo">
                  <el-input
                    style="width: 100%;"
                    v-model.trim="listQuery.ServiceNo"
                    placeholder
                  ></el-input>
                </template>
                <template slot="Code">
                  <el-input v-model.trim="listQuery.Code"></el-input>
                </template>
                <template slot="IsRelatedContract">
                  <el-checkbox v-model="listQuery.IsRelatedContract">未关联售后合同</el-checkbox>
                </template>
                <template slot="StructPartKeyWords">
                  <el-input v-model.trim="listQuery.StructPartKeyWords"></el-input>
                </template>
              </app-table-form>
            </template>

            <!-- 表格批量操作区域 -->
            <template slot="btnsArea">
              <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
              <app-table-row-button v-if=" rowBtnIsExists('btnAssign')&& scope.row.ShowAssignBtn && scope.row.MaintenanceStatus != 3" @click="handleAssign(scope.row)" :type="1" text="指派"></app-table-row-button>
              <!-- (scope.row.MaintenanceStatus == 4 || scope.row.MaintenanceStatus == 1) -->
              <app-table-row-button v-if="rowBtnIsExists('btnUpdate') && (scope.row.MaintenanceStatus != 3)" @click="handleUpdate(scope.row, 'update')" :type="1"></app-table-row-button> 
              <!-- 1{待审批；2：已审批 的 报修单，不能再次处理（已审批 表示报修单 已处理） -->
              <app-table-row-button v-if="isHandleable(scope.row) && (scope.row.MaintenanceStatus == 1 || scope.row.MaintenanceStatus == 2 || scope.row.MaintenanceStatus == 3 || scope.row.MaintenanceStatus == 5)" @click="handleUpdate(scope.row, 'handle')" :type="2" text="处理"></app-table-row-button>
              <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
            </template>
          </app-table>

        </div>
        <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="listQuery.RegionalId ? [listQuery.RegionalId] : []"
      :queryRegionID='RId'
    ></v-area-choose>
    <!-- 创建 -->
    <create-page
      v-if="dialogFormVisible"
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
      @reload="getList"
      :declareNewCases="declareNewCases"
      :queryRegionID='RId'
    ></create-page>

    <!-- 指派 -->
    <assign-page
      v-if="dialogAssignFormVisible"
      :row="currentRow"
      @closeDialog="closeAssignDialog"
      @saveSuccess="handleAssignSaveSuccess"
      :dialogFormVisible="dialogAssignFormVisible"
      :rootId="rootId" :IdList="IdList"
    ></assign-page>
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>

    <batch-update-page
      v-if="dialogBatchUpdateFormVisible"
      @closeDialog="closeBatchDialog"
      @saveSuccess="handleBatchSaveSuccess"
      :dialogFormVisible="dialogBatchUpdateFormVisible"
      :IdList='multipleSelection.map(s => s.Id)'
    ></batch-update-page>
  </div>
</template>

<script>
// import tags from '../common/tags'
import indexPageMixin from "@/mixins/indexPage";
import * as mo from "@/api/maintenanceCenter/maintenOrderMgmt";
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import { listToTreeSelect } from "@/utils";
import createPage from "./create";
import assignPage from "./assign";
import { vars } from "../common/vars";
import { getUserInfo,getDateTimeRange } from "@/utils/auth";
import { mapGetters } from "vuex";
// import * as approvalVars from '../../../salesMgmt/common/vars'
import vExport from "@/components/Export/index";
import vAreaChoose from "../../../afterSalesMgmt/businessMap/common/areaChoose";
import batchUpdatePage from './batchUpdate'
import mixins from '../../../afterSalesMgmt/softwareAftersalesReturnVisit/mixins'
import maintenOrderStatus from './maintenOrderStatus'

export default {
  name: "mainten-order-mgmt",
  mixins: [indexPageMixin, mixins],
  components: {
    // tags,
    createPage,
    assignPage,
    vExport,
    vAreaChoose,
    batchUpdatePage,
    maintenOrderStatus,

  },
  computed: {
    ...mapGetters(["autoOpenDialogList"]),
    returnUrl() {
      let url = decodeURI(this.$route.query.returnUrl || "");
      return url;
    }
  },
  created() {
    // this.loadImplementerList();
    this.getSRM();
    this.getAreas();
    this.currentEmployeeId = getUserInfo().employeeid;
  },
  watch: {
    autoOpenDialogList: {
      handler(val) {
        this.checkAutoDialog();
      },
      deep: true
    },
    "status"(val) {
      if(val == 1){
        this.listQuery.MaintenanceStatus=[1,2,4,5];
      }else if(val == 2){
        this.listQuery.MaintenanceStatus=[3];
      }else{
        this.listQuery.MaintenanceStatus=null;
      }
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    "listQuery.HandlerEmployeeId"() {
      this.getList();
    },
    "listQuery.TableActive"(val) {
      this.listQuery.PageIndex = 1;
      if (val == "my") {
        delete this.listQuery.EmployeeName;
        let idx = this.tableSearchItems.findIndex(
          s => s.prop == "EmployeeName"
        );
        if (idx > -1) {
          this.tableSearchItems.splice(idx, 1);
        }
      } else {
        this.$set(this.listQuery, "EmployeeName", "");
        this.tableSearchItems.splice(3, 0, {
          prop: "EmployeeName",
          label: "实施人员"
        });
      }
      this.getList();
      this.getNumber();
    }
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    // saintenanceStatusFilter(status) {
    //   let obj = vars.maintenOrderMgmt.maintenanceStatus.find(
    //     s => s.value == status
    //   );
    //   if (obj) {
    //     return obj.label;
    //   }
    //   return status;
    // },
  },
  mounted() {
    // this.getNumber();
  },
  data() {
    return {
      faultTypeOptions: vars.maintenOrderMgmt.FaultTypeOptions,
      pickerOptions: {
          shortcuts: [{
            text: '今年1月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('01');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年2月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('02');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年3月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('03');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },{
            text: '今年4月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('04');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年5月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('05');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年6月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('06');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },{
            text: '今年7月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('07');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年8月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('08');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年9月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('09');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },{
            text: '今年10月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('10');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年11月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('11');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年12月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('12');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }]
        },
      RId:'',
      serviceListStatus:vars.maintenOrderMgmt.serviceListStatus,
      saveFirstArea:'',
      firstAreaTree:[],
      rData:null,
      cData:[],
      dialogExportVisible:false,
      declareNewCases:false,
      status: 1,
      daysBetweens: vars.maintenOrderMgmt.tagStatus,
      handlerEmployeeIds: [
        { value: "", label: "全部报修单" },
        { value: "my", label: "指派我的" }
      ],
      normalizer(node) {
        return {
          id: node.Id,
          label: node.RegionalName,
          children: node.children
        };
      },
      listToTreeSelect,
      treedata: [],
      // treeOptions: [],
      dialogBatchUpdateFormVisible: false,

      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,
      currentEmployeeId: "",
      total: 0,
      listLoading: false,
      listQuery: {
        PageSize: 100,
        IsSignBill:"",
        ServiceNo:"",
        TableActive: "",
        // DaysBetween: 7,
        ReportRange: [],
        // ReportTimeStart: null,
        // ReportTimeEnd: null,
        MaintenanceStatus: null, //状态
        RegionalId: '', //报修地区
        EmployeeName: "", //实施人员
        Code: "", //报修单号
        IsRelatedContract: false,
        StructPartKeyWords: '', // 配件/规格
        FaultTypeList: [], //故障类型
        IsReplacePart: null, //更换配件
        AfterContractCode: '', //售后合同编号
      },
      multipleSelection: [],
      tableSearchItems: [
        // { prop: "ReportTimeStart", label: "报修时间" },
        // { prop: "ReportTimeEnd", label: "至" },
        { prop: "ReportRange", label: "报修时间" },
        // { prop: "MaintenanceStatus", label: "状态" },
        
        { prop: "RegionalId", label: "地区" },
        { prop: "ServiceNo", label: "服务单号"},
        { prop: "IsSignBill", label: "是否签单" },
        { prop: "FaultTypeList", label: "故障类型" },
        { prop: "IsReplacePart", label: "更换配件" },
        { prop: "AfterContractCode", label: "售后合同编号" },
        { prop: "EmployeeName", label: "实施人员" },
        { prop: "Code", label: "报修单号" },
        { prop: "IsRelatedContract", label: "售后合同" },
        { prop: "StructPartKeyWords", label: "配件/规格" },
        
      ],
      //      implementerList: [],
      tabColumns: [
        {
          attr: { prop: "Code", label: "报修编号",width:'125' }
        },
        {
          attr: { prop: "RegionalName", label: "报修地区", width:'280' }
        },
        {
          attr: { prop: "MaintenanceStatus", label: "状态", sortable: 'custom',width:'90' },
          slot: true
        },
        // {
        //   attr: { prop: "ApprovalStatus", label: "审批状态" },
        //   slot: true
        // },
        {
          attr: { prop: "ReportEmployee", label: "记录人",width:'90' },
          slot: true
        },
        {
          attr: {
              prop: "TotalTimeEmployeeList",
              label: "总耗时",
          },
          slot: true
        },
        {
          attr: { prop: "ReportTime", label: "报修时间", sortable: 'custom' ,width:'125'},
          slot: true
        },
        // {
        //   attr: { prop: "ShouldDealWithCount", label: "应处理", width: '90'}
        // },
        {
          attr: { prop: "ProcessedCount", label: "已处理", width: '90' }
        },
        {
          attr: { prop: "TotalPrice", label: "总收费（元）",width:'100' }
        },
        {
          attr: { prop: "IsSignBill", label: "是否签单",width:'120' },
          slot:true
        },
        {
          attr: { prop: "ServiceNo", label: "服务单号" ,width:'95'},
          slot:true
        },
        {
          attr: { prop: "AfterContractCode", label: "售后合同"},
          slot:true
        },
        {
          attr: { prop: "HandlerEmployeeList", label: "实施人员"},
          slot: true
        }
      ],
      tabDatas: [],
      currentRow: null,
      dialogAssignFormVisible: false, //指派弹框
      rootId: "", //当前行订单报修地区的最上级节点（油条id）
      IdList: [],
      dialogRegionalVisible: false
    };
  },
  methods: {
    collapseOrExpand(val){
      this.$nextTick(()=>{
        this.$refs.mainTable.setTabHeight()
      })
    },
    // disabledFn(data, nodeType) {
    //     //禁选一级节点
    //     if(data.level <= 1) {
    //         return true
    //     }
    //     return false
    // },
    getSRM(){
      mo.getSaveRegionalManagements().then(res => {
        this.listQuery.RegionalId=res ? res : '';
        this.RId=res ? res : '';
        this.saveFirstArea = this.RId
        this.listQuery.MaintenanceStatus=[1,2,4,5];
        this.getNumber();
        this.getList();
      })
    },
    getNumber(){
      let postData=JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      if (postData.TableActive == "my") {
        postData.HandlerEmployeeId = getUserInfo().employeeid;
      }
      mo.getStatisticalStatesNumber(postData).then(res => {
        this.daysBetweens[0].label='未完成('+(res[0].Count+res[1].Count)+')';
        this.daysBetweens[1].label='已处理('+res[2].Count+')';
        this.daysBetweens[2].label='全部('+(res[0].Count+res[1].Count+res[2].Count)+')';
      }).catch(err => {
      });
    },
    handleSelectChange(val){
        let a=this.firstAreaTree.find(v => v.Id == this.RId);
        this.$confirm('确定要切换至'+a.RegionalName+'吗?', '提示', {
           confirmButtonText: '确定',
           cancelButtonText: '取消',
           type: 'warning'
         }).then(() => {
            this.listQuery.IsSignBill="";
            this.listQuery.ServiceNo="";
            this.listQuery.TableActive= "";
            this.listQuery.ReportRange= [];
            this.listQuery.areaName='';
            this.listQuery.EmployeeName= ""; //实施人员
            this.listQuery.FaultTypeList = [] //故障类型
            this.listQuery.IsReplacePart = null // 更换配件
            this.listQuery.AfterContractCode = '' //售后合同
            this.listQuery.IsRelatedContract = false
            this.listQuery.StructPartKeyWords = ''
            this.listQuery.Code= ""; //报修单号
            this.saveFirstArea=this.RId;
            this.listQuery.RegionalId=this.RId;
            this.listQuery.PageIndex = 1;
            this.saveRM();
         }).catch(() => {
            this.RId=this.saveFirstArea;
         });
      
    },
    saveRM(){
      mo.saveRegionalManagements({RegionalId:this.listQuery.RegionalId ? this.listQuery.RegionalId : null}).then(res => {
        this.getList();
        this.getNumber();
      }).catch(err => {
      })
    },
    handleClearRegional() {
      this.listQuery.RegionalId = ''
      this.listQuery.areaName = ''
      this.getList();
      this.getNumber();
    },
    electedRegionalData(data){
        if(data) {
          this.listQuery.RegionalId=data.Id;
          this.listQuery.areaName=data.ParentName;
        }else{
          this.listQuery.RegionalId='';
          this.listQuery.areaName='';
        }
        this.getList();
        this.getNumber();
    },
    closeRegionalDialog() {
      this.dialogRegionalVisible=false
    },
    handleRegionalDialog() {
      if(!this.listQuery.RegionalId){
        this.listQuery.RegionalId=this.RId;
      }
      this.dialogRegionalVisible=true;
    },
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    // getApprovalObj(status) {
    //   return approvalVars.vars.orderMgmt.approvalStatus.find(s => s.value == status) || {};
    // },
    //检查并打开需要自动弹框
    checkAutoDialog() {
      let currNavUrl = this.$route.path //'/afterSalesMgmt/maintenCenter/maintenOrderMgmt'
      let oldList = this.autoOpenDialogList[currNavUrl]

      let waitList = oldList.filter(s => !s.isHandle);
      if (waitList && waitList.length > 0) {
        let firstObj = waitList[0];
        this.handleUpdate(
          {
            Id: firstObj.id
          },
          "handle"
        );
        firstObj.isHandle = true;
        //只需要帮用户打开一次点击的”查看“审批弹框
        this.$store.commit("SET_autoOpenDialogList", currNavUrl, oldList);
      }
    },
    getStatusObj(status) {
      return (
        vars.maintenOrderMgmt.maintenanceStatus.find(s => s.value == status) ||
        {}
      );
    },
    onBtnClicked: function(domId) {
      switch (domId) {
        case "btnAdd":
          this.handleDialog("create");
          break;
        case "btnUpdateBatch":
          this.handleDialogBatchUpdate("create");
          break;
        case "btnExport":
          this.handleExport();
          break;
        case "btnBatchAssign":
          this.handleBatchAssign();
          break;
        default:
          break;
      }
    },
    
    handleExport(){
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      if (this.listQuery.TableActive == "my") {
        postData.HandlerEmployeeId = getUserInfo().employeeid;
      }
      if (postData.ReportRange && postData.ReportRange.length == 2) {
        postData.ReportTimeStart = postData.ReportRange[0];
        postData.ReportTimeEnd = postData.ReportRange[1];
        delete postData.ReportRange;
      }
      postData.ExportType = 1; // 1.报修单导出 2.售后结算导出
      this.rData={
          "exportSource": 18,
          "columns": [],
          "searchCondition": postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'报修人',
        value:'TroubleShooter'
      },{
        label:'报修时间',
        value:'ReportTimeString'
      },
      {
        label:'到站/离站时间',
        value:'ArrivalAndDepartureTime'
      },
      {
        label:'一级地区',
        value:'RegionalName1'
      },{
        label:'二级地区',
        value:'RegionalName2'
      },
      {
        label:'三级地区',
        value:'RegionalName3'
      },{
        label:'四级地区',
        value:'RegionalName4'
      },{
        label:'五级地区',
        value:'RegionalName5'
      },{
        label:'报修电话',
        value:'ReporterNumber'
      },
      {
        label:'故障记录',
        value:'ReportFailureRecord'
      },
      {
        label:'加热炉/锅炉',
        value:'Name'
      },{
        label:'炉号',
        value:'HeatNumber'
      },{
        label:'供风方式',
        value:'EquipmentWorkModeName'
      },{
        label:'故障现象',
        value:'Phenomenon'
      },{
        label:'故障原因',
        value:'CauseAnalysis'
      },{
        label:'解决方式',
        value:'Solution'
      },
      {
        label:'实施人员',
        value:'HandlerEmployeeListString'
      },
      {
        label:'故障分类',
        value:'FaultType'
      },{
        label:'更换配件',
        value:'StructPartName'
      },
      {
        label:'规格型号',
        value:'SpecificationModel'
      },{
        label:'配件数量',
        value:'Count'
      },{
        label:'单价',
        value:'UnitPrice'
      },{
        label:'总价',
        value:'TotalMoney'
      },{
        label:'是否在保',
        value:'IsWarrantyName'
      },
      {
        label:'故障处理结果',
        value:'HandlingResultStatus'
      },
      {
        label:'备注',
        value:'Remarks'
      },{
        label:'站内签字',
        value:'StationSign'
      },{
        label:'是否签单',
        value:'IsSignBill'
      },{
        label:'加班/夜勤',
        value:'OvertimeNightWork'
      },{
        label:'工时',
        value:'WorkingHours'
      },{
        label:'单号',
        value:'ServiceNo'
      },
      {
        label:'录入时间',
        value:'EntryTimeString'
      },
      {
        label:'服务费用',
        value:'OtherExpenses'
      },
      
      ]
      this.dialogExportVisible=true;
    },
    // // 导出使用 售后结算列表的导出
    // handleExport(){
    //   let postData = JSON.parse(JSON.stringify(this.listQuery));
    //   postData = this.assignSortObj(postData)
    //   if (this.listQuery.TableActive == "my") {
    //     postData.HandlerEmployeeId = getUserInfo().employeeid;
    //   }
    //   if (postData.ReportRange && postData.ReportRange.length == 2) {
    //     postData.ReportTimeStart = postData.ReportRange[0];
    //     postData.ReportTimeEnd = postData.ReportRange[1];
    //     delete postData.ReportRange;
    //   }
    //   this.rData={
    //       "exportSource": 14,
    //       "columns": [],
    //       "searchCondition": postData
    //   }
    //   this.cData=[{
    //     label:'序号',
    //     value:'Number'
    //   },{
    //     label:'报修编号',
    //     value:'Code'
    //   },{
    //     label:'报修地区',
    //     value:'RegionalName'
    //   },{
    //     label:'状态',
    //     value:'MaintenanceStatus'
    //   },
    //   // {
    //   //   label:'审批状态',
    //   //   value:'ApprovalStatus'
    //   // },
    //   {
    //     label:'记录人',
    //     value:'ReportEmployeeString'
    //   },{
    //     label:'报修时间',
    //     value:'ReportTimeString'
    //   },
    //   // {
    //   //   label:'应处理',
    //   //   value:'ShouldDealWithCount'
    //   // },
    //   {
    //     label:'已处理',
    //     value:'ProcessedCount'
    //   },{
    //     label:'总收费（元）',
    //     value:'TotalPrice'
    //   },{
    //     label:'是否签单',
    //     value:'IsSignBill'
    //   },{
    //     label:'服务单号',
    //     value:'ServiceNo'
    //   },{
    //     label:'售后合同',
    //     value:'AfterContractCode'
    //   },{
    //     label:'实施人员',
    //     value:'HandlerEmployeeString'
    //   }]
    //   this.dialogExportVisible=true;
    // },
    handleDialogBatchUpdate(activeName) {
      if(this.multipleSelection.length == 0) {
        this.$message({
          message: '请选择需要调整的报修单',
          type: 'error'
        })
        return false
      }
      this.dialogBatchUpdateFormVisible = true;
    },
    handleBatchSaveSuccess() {
      this.getList()
      this.closeBatchDialog()
    },
    closeBatchDialog() {
      this.dialogBatchUpdateFormVisible = false;
    },
    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      // this.listQuery.PageIndex = 1
      this.getList();
      this.closeDialog();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
      this.getNumber();
    },

    handleNav(type, proj) {
      this.$router.push({
        path: `/projectDev/projectMgmt/${type}/index?productId=${proj.productId}&projectId=${proj.projectId}`
      });
    },
    getRootId(districtId) {
      let curr = this.treedata.find(s => s.Id == districtId);
      if (curr && curr.ParentId) {
        this.getRootId(curr.ParentId);
      } else {
        this.rootId = districtId;
      }
    },
    handleUpdate(row, optType = "update") {
      // 弹出编辑框
      if(optType == 'detail'){
        this.declareNewCases=true;
      }else{
        this.declareNewCases=false;
      }
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },
    isHandleable(row) {
      //当前用户为实施人员才能”处理“报修单
      let currentEmpId = getUserInfo().employeeid
      let result = ((row.HandlerEmployeeList || []).map(s => s.EmployeeId).findIndex(s => s == currentEmpId) > -1 
                  || (row.AgentEmployeeList || []).map(s => s.EmployeeId).findIndex(s => s == currentEmpId) > -1)
                  || (row.ReportEmployee && row.ReportEmployee.EmployeeId == currentEmpId)
                  || (row.CreateEmployee && row.CreateEmployee.EmployeeId == currentEmpId);
      return result;
      //return (scope.row.AgentEmployeeList.find(s=>s.EmployeeId==currentEmployeeId) || scope.row.HandlerEmployeeList.find(s=>s.EmployeeId==currentEmployeeId))
    },
    // loadImplementerList() {
    //   mo.getImplementerList({}).then((response) => {
    //     this.implementerList = response;
    //     // if (
    //     //   this.listQuery.HandlerEmployeeId &&
    //     //   !response.find(t => t.EmployeeId == this.listQuery.HandlerEmployeeId)
    //     // ) {
    //     //   this.listQuery.HandlerEmployeeId = null;
    //     // }
    //   });
    // },

    /**
     * 批量指派
     */
    handleBatchAssign(row) {
      // this.getRootId(row.RegionalId);
      if(this.multipleSelection.length == 0) {
        this.$message({
          message: '请选择需要调整的报修单',
          type: 'error'
        })
        return false
      }
      if(this.multipleSelection.length > 20) {
        this.$message({
          message: '批量操作的报修单不可超过20条',
          type: 'error'
        })
        return false
      }
      this.IdList = this.multipleSelection.map(s=>s.Id);
      this.currentRow = {};
      this.rootId = '';
      this.dialogAssignFormVisible = true;
      console.log(123)
    },
    /**
     * 指派
     */
    handleAssign(row) {
      // this.getRootId(row.RegionalId);
      this.IdList = [];
      this.currentRow = row;
      this.rootId = row.TopRegionalId;
      this.dialogAssignFormVisible = true;
    },
    closeAssignDialog() {
      this.dialogAssignFormVisible = false;
    },
    handleAssignSaveSuccess() {
      this.getList();
      this.closeAssignDialog();
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        TableActive: this.listQuery.TableActive,
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        RegionalId: null,
        ImplementerId: null,
        DaysBetween: this.listQuery.DaysBetween,
        EmployeeName: "",
        ReportRange: [],
        IsRelatedContract: false,
        StructPartKeyWords: '',
        MaintenanceStatus:[1,2,4,5],
        FaultTypeList: [],
        IsReplacePart: null,
        AfterContractCode: ''

      };
      if(this.status == 1){
        this.listQuery.MaintenanceStatus=[1,2,4,5];
      }else if(this.status == 2){
        this.listQuery.MaintenanceStatus=[3];
      }else{
        this.listQuery.MaintenanceStatus=null;
      }
      this.getList(); //刷新列表
      this.getNumber();
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.getList()
    },
    //获取项目列表
    getList() {
      
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      if (this.listQuery.TableActive == "my") {
        postData.HandlerEmployeeId = getUserInfo().employeeid;
      }
      if (postData.ReportRange && postData.ReportRange.length == 2) {
        postData.ReportTimeStart = postData.ReportRange[0];
        postData.ReportTimeEnd = postData.ReportRange[1];
        delete postData.ReportRange;
      }
      postData = this.assignSortObj(postData)
      this.listLoading = true;
      mo.getList(postData).then(res => {
        this.listLoading = false;
        this.tabDatas = res.Items;
        this.total = res.Total;
      }).catch(err => {
        this.listLoading = false;
      });
    },
    handleDel(row) {
      this.$confirm(`是否确认删除 ${row.Code}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        mo.del([row.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },
    getAreas() {
      regionalManagement.getPoorListByCondition({}).then(res => {
        this.treedata = res;
        this.firstAreaTree=res.filter(v => !v.ParentId);
        this.firstAreaTree.unshift({
          Id: '',
          ParentId: null,
          RegionalName: "全部",
          RegionalPhone: "",
        })

        // this.treeOptions = res.filter(s => !s.ParentId) || []
      });
    },
    handleGoBack() {
      this.$router.push({ path: this.returnUrl });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  overflow-x: auto;
  .bg-white {
    .content {
      padding-right: 0;
      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }
      .list {
      }
    }
  }
}

</style>
