<template>
    <div>
        <app-dialog title="新员工欢迎公告" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" v-loading="loading"
                label-position="right" label-width="80px">
                    <el-row class="wrapper">
                        <el-form-item label="公告内容" prop="Content">
                            <div>提示：固定开头不可修改（亲爱的 员工姓名：）</div>
                            <editor-bar :value="formData.Content" @edit="formData.Content = arguments[0]"></editor-bar>
                        </el-form-item>
                        <el-form-item label="相关附件">
                            <app-uploader accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList"
                            :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import EditorBar from "@/components/QuillEditor/index.vue";
import * as WelcomeAnnouncementApi from "@/api/personnelManagement/WelcomeAnnouncement";
export default {
    name: "inductionMgt-welcomeNotice",
    directives: {},
    components: {
        EditorBar
    },
    mixins: [],
    computed: {
    },
    props: {
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();
                this.getDetail();
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {
                Content: {fieldName: "公告内容",rules: [{ required: true, max: 50 }]},
            },
            formData: {
                Content: '',
                AttachmentList: [],
            }
        };
    },
    methods: {
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    postData.AttachmentIdList = postData.AttachmentList.map((s) => s.Id);
                    console.log(postData)
                    let result = null;
                    self.disabledBtn = true;
                    if (postData.Id) {
                        result = WelcomeAnnouncementApi.edit(postData);
                    } else {
                        result = WelcomeAnnouncementApi.add(postData);
                    }

                    result.then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.handleClose();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            WelcomeAnnouncementApi.detail({}).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style lang='scss' scoped>
</style>