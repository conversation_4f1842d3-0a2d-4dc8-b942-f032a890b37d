<!--组件名称-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog title="文件类型设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="500">
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="12px"
          v-loading='loading'
        >
          <div class="wrapper">
            <div class="title-wrapper">
                <span class="title">类型列表</span>
                <el-button @click="handleAdd" type="text">添加类别</el-button>
            </div>
            <el-row>
                <el-col :span="24" v-for="(item, idx) in formModel.List" :key="idx" style="display: flex;">
                    <el-form-item style="flex: 1;" label="　" :prop="'List.' + idx + '.Name'" :rules="{required: true, message: '类型名称不能为空', trigger: 'change'}">
                        <el-input placeholder="请输入类型名称" maxlength="50" v-model.trim="formModel.List[idx].Name"></el-input>
                    </el-form-item>
                    <i class="el-icon-delete-solid" style="font-size: 20px; color: red; margin-left: 4px; cursor:pointer" title="删除" @click="handleRemove(item, idx)"></i>
                </el-col>
            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button v-show="editable" :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as classify from "@/api/classify";

export default {
  /**名称 */
  name: "type",
  /**组件声明 */
  /**参数区 */
  props: {
    //弹窗类型
    dialogStatus: {
      type: String,
      default: "create",
    },

  },
  /**数据区 */
  data() {
    return {
      loading: false,
      buttonLoading: false,
      /**表单模型 */
      formModel: {
        List: [

        ],
        
      },
      
      /**表单规则 */
      formRules: {
      },
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },

  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if(val) {
            this.getList()
        }
      },
      immediate: true,
    },
  },
  /**渲染前 */
  created() {
    let _this = this;
    _this.formRules = _this.initRules(_this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    handleRemove(row, idx) {
      this.$confirm(`是否确认删除类别?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formModel.List.splice(idx, 1)
      })
    },
    handleAdd() {
        this.formModel.List.push({
            Id: null,
            Name: '',
            businessType:20
        })
    },
    getList() {
        let postDatas = {
            PageSize: 1000,
            PageIndex: 1,
            businessType:20
        }
        this.loading = true
        classify.getListPage(postDatas).then(res => {
            this.loading = false
            this.formModel.List = res.Items || []
        }).catch(err => {
            this.loading = false
        })
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
    
      _this.$refs.formRef.validate((valid) => {
        if (valid) {
            _this.buttonLoading = true;
            let postDatas = JSON.parse(JSON.stringify(_this.formModel.List || []))
            classify.editAllList(postDatas).then((response) => {
              _this.buttonLoading = false;
              _this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000,
              });
              _this.$refs.appDialogRef.createData();
            })
            .catch((err) => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**关闭 */
    handleClose() {
      let _this = this;
      _this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.wrapper {
    min-height: 300px;
    .title-wrapper{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .title{
            font-weight: 600;
        }
    }

}
</style>


