<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='400'>
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <div class="wrapper" v-loading='loading'>
            <el-row>
              <el-col :span="24">
                <el-form-item label="标签名称" prop="Name">
                  <el-input :disabled="!editable" maxlength="10" type="text" v-model="formData.Name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <span style="margin-right: 50px;" v-if="dialogStatus == 'create'">
          <el-checkbox v-model="isContinue">继续添加</el-checkbox>
        </span>
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
  import * as tag from "@/api/maintenanceCenter/tag";
  export default {
    name: "tag-create",
    directives: {},
    components: {
      // tabs,
      // tags,
    },
    mixins: [],
    props: {
      dialogStatus: {
        //create、update、detail
        type: String
      },
      id: {
        type: String,
        default: ""
      }
    },
    watch: {
      "$attrs.dialogFormVisible"(val) {
        if (!val) {
          this.isContinue = false;
        }
        if (val) {
          // this.getProjectNames();
          this.resetFormData();
          if (this.dialogStatus != "create" && this.id) {
            this.getDetail();
          }
        }
      }
    },
    computed: {
      //不等于详情页面可编辑
      editable() {
        return this.dialogStatus != "detail";
      },
      pageTitle() {
        if (this.dialogStatus == "create") {
          return "添加标签";
        } else if (this.dialogStatus == "update") {
          return "编辑标签";
        } else if (this.dialogStatus == "detail") {
          return "标签详情";
        }
      },

    },
    created() {

      this.rules = this.initRules(this.rules);

    },
    data() {
      return {
        loading: false,
        disabledBtn: false,
        isContinue: false,
        rules: {
          Name: {
            fieldName: "标签名称",
            rules: [{ required: true }]
          },
        },
        labelWidth: "110px",
        formData: {
          Id: "", 
          Name: "",
        }
      };
    },
    methods: {
      resetFormData() {
        let temp = {
          Id: "", //需求ID
          Name: "", //
        };
        this.formData = Object.assign({}, this.formData, temp);
      },
      createData() {
        let validate = this.$refs.formData.validate();
        Promise.all([validate]).then(valid => {
          let postData = JSON.parse(JSON.stringify(this.formData));
          //提交数据保存

          postData = Object.assign({}, this.formData);
          if (this.dialogStatus == "create") {
            delete postData.Id;
          }


          this.disabledBtn = true
          let result = null;
          if (this.dialogStatus == "create") {
            delete postData.Id;
            result = tag.add(postData);
          } else if (this.dialogStatus == "update") {
            result = tag.edit(postData);
          }

          result.then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.disabledBtn = false

            if (this.isContinue) {
              this.resetFormData();
              this.$emit("reload");

            } else {
              this.$refs.appDialogRef.createData();
            }
          }).catch(err => {
            this.disabledBtn = false
          });
        });
      },
      getDetail() {
        this.loading = true
        tag.detail({ id: this.id }).then(res => {
          this.loading = false
          this.formData = Object.assign({}, this.formData, res);
        }).catch(err => {
          this.loading = false
        });
      },
      handleFilesUpChange(files) {
      },
      handleClose() {
        this.$refs.appDialogRef.handleClose();
      }
    }
  };
</script>

<style lang="scss" scoped>
.wrapper{
  height: 100px;
}

</style>
