<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div style="width: 100%; padding: 10px;">
                    <app-table-form :layoutMode='layoutMode'
                    :label-width="'100px'"
                    :items="tableSearchItems"
                    @onSearch="handleFilter"
                    @onReset="handleResetSearch"
                    @collapseOrExpand="collapseOrExpand"
                    >
                        <template slot="ServiceNo">
                            <el-input style="width: 100%;" 
                                placeholder="搜索服务单号..."
                                @clear='handleFilter'
                                v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        handleFilter()
                                    }
                                }' 
                                clearable 
                                v-model="listQuery.ServiceNo"
                            ></el-input>
                        </template>
                    <template slot="HandlingResultStatus">
                            <el-select v-model="listQuery.HandlingResultStatus" placeholder="请选择" multiple style="width: 100%;">
                                <el-option v-for="(hrs,hrsI) in handlingResultStatus" :key="hrsI"  :label="hrs.label" :value="hrs.value"></el-option>
                            </el-select>
                        </template>
                        <template slot="IsWarranty">
                            <el-select v-model="listQuery.IsWarranty" placeholder="请选择" clearable style="width: 100%;">
                                <el-option v-for="(wl,wlI) in warrantyList" :key="wlI"  :label="wl.label" :value="wl.value"></el-option>
                            </el-select>
                        </template>
                    <template slot="RegionalId">
                        <div class="el-input el-input--mini">
                        <div style="display: flex; height: 28px;line-height: 28px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; ">
                            <div style="padding-left: 10px;">
                            <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">选择</span>
                            </div>
                            <div style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="listQuery.areaName">{{ listQuery.areaName }}</div>
                            <div style="width: 28px; text-align: center;">
                            <i style="cursor: pointer;" title="删除" @click="handleClearRegional" v-show="listQuery.RegionalId" class="el-icon-close"></i>
                            </div>
                        </div>
                        </div>
                        
                    </template>
                    <template slot="ReportRange">
                        <el-date-picker
                        style="width: 100%;"
                        format="yyyy-MM-dd HH:mm"
                        value-format="yyyy-MM-dd HH:mm"
                        v-model="listQuery.ReportRange"
                        type="datetimerange"
                        :picker-options="pickerOptions"
                        :default-time="['00:00:00', '23:59:00']"
                        ></el-date-picker>
                    </template>
                    
                    <template slot="EmployeeName">
                        <el-input
                        style="width: 100%;"
                        v-model.trim="listQuery.EmployeeName"
                        placeholder
                        ></el-input>
                    </template>
                    
                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                        <div class="btns-area">
                            <span>结算清单</span>
                            <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                        </div>
                    </template>
                    </app-table-form>
                </div>
                <div class="content" id="__dynamicTabCoreWrapper">
                    <app-table-core
                        ref="mainTable"
                        :tab-columns="tabColumns"
                        :tab-datas="tabDatas"
                        :tab-auth-columns="[]"
                        :isShowAllColumn="true"
                        :loading="listLoading"
                        @rowSelectionChanged="rowSelectionChanged"
                        :isShowOpatColumn="false"
                        :startOfTable="startOfTable"
                        :multable="false"
                        fit
                        :height='tabHeight'
                        border
                        :serial='false'
                        :span-method="objectSpanMethod"
                    >
                        <template slot="ReportTime" slot-scope="scope">
                            {{ scope.row.ReportTime | dateFilter("YYYY-MM-DD HH:mm")}}
                        </template>
                        <template slot="ArrivalTime" slot-scope="scope">
                            {{scope.row.ArrivalTime ? (getDay(scope.row.ArrivalTime)+' - '+getDay(scope.row.DepartureTime)) : '无'}}
                        </template>
                        <template slot="Name" slot-scope="scope">
                            <div class="omit" :title="scope.row.Name">{{ scope.row.Name ? scope.row.Name : '无'}}</div>
                        </template>
                        <template slot="HeatNumber" slot-scope="scope">
                            <div class="omit" :title="scope.row.HeatNumber">{{ scope.row.HeatNumber ? scope.row.HeatNumber : '无'}}</div>
                        </template>
                        <template slot="ReporterNumber" slot-scope="scope">
                            <div class="omit" :title="scope.row.ReporterNumber">{{ scope.row.ReporterNumber ? scope.row.ReporterNumber : '无'}}</div>
                        </template>
                        <template slot="RegionalName" slot-scope="scope">
                            <div class="omit" :title="scope.row.RegionalName">{{ scope.row.RegionalName ? scope.row.RegionalName : '无'}}</div>
                        </template>
                        <template slot="TroubleShooter" slot-scope="scope">
                            <div class="omit" :title="scope.row.TroubleShooter">{{ scope.row.TroubleShooter}}</div>
                        </template>
                        
                        <!-- <template slot="RegionalName1" slot-scope="scope">
                            {{ scope.row.RegionalName1 ? scope.row.RegionalName1 : '无'}}
                        </template>
                        <template slot="RegionalName2" slot-scope="scope">
                            {{ scope.row.RegionalName2 ? scope.row.RegionalName2 : '无'}}
                        </template>
                        <template slot="RegionalName3" slot-scope="scope">
                            {{ scope.row.RegionalName3 ? scope.row.RegionalName3 : '无'}}
                        </template>
                        <template slot="RegionalName4" slot-scope="scope">
                            {{ scope.row.RegionalName4 ? scope.row.RegionalName4 : '无'}}
                        </template> -->
                        <template slot="RegionalPhone" slot-scope="scope">
                            <div class="omit" :title="scope.row.RegionalPhone">{{ scope.row.RegionalPhone ? scope.row.RegionalPhone : '无'}}</div>
                        </template>
                        <template slot="EquipmentWorkModeName" slot-scope="scope">
                            <div class="omit" :title="scope.row.EquipmentWorkModeName">{{ scope.row.EquipmentWorkModeName ? scope.row.EquipmentWorkModeName : '无'}}</div>
                        </template>
                        <template slot="Phenomenon" slot-scope="scope">
                            <div class="omit" :title="scope.row.Phenomenon">{{ scope.row.Phenomenon ? scope.row.Phenomenon : '无'}}</div>
                        </template>
                        <template slot="CauseAnalysis" slot-scope="scope">
                            <div class="omit" :title="scope.row.CauseAnalysis">{{ scope.row.CauseAnalysis ? scope.row.CauseAnalysis : '无'}}</div>
                        </template>

                        <template slot="Solution" slot-scope="scope">
                            <div class="omit" :title="scope.row.Solution">{{ scope.row.Solution ? scope.row.Solution : '无'}}</div>
                        </template>
                        <template slot="SpecificationModel" slot-scope="scope">
                            <div class="omit" :title="scope.row.SpecificationModel">{{ scope.row.SpecificationModel ? scope.row.SpecificationModel : '无'}}</div>
                        </template>
                        <template slot="Count" slot-scope="scope">
                            <div class="omit" :title="scope.row.Count">{{ scope.row.Count ? scope.row.Count : '无'}}</div>
                        </template>
                        <template slot="UnitPrice" slot-scope="scope">
                            <div class="omit" :title="scope.row.UnitPrice">{{ scope.row.UnitPrice ? scope.row.UnitPrice : '无'}}</div>
                        </template>
                        <template slot="TotalMoney" slot-scope="scope">
                            <div class="omit" :title="scope.row.TotalMoney">{{ scope.row.TotalMoney ? scope.row.TotalMoney : '无'}}</div>
                        </template>
                        <template slot="HandlingResultStatus" slot-scope="scope">
                            <div class="omit" :style="'color:'+getColor(scope.row.HandlingResultStatus)+';'">{{filterResult(scope.row.HandlingResultStatus)}}</div>
                        </template>
                        <template slot="HandlerEmployeeList" slot-scope="scope">
                            {{scope.row.HandlerEmployeeList | filterEmp}}
                        </template>
                        <template slot="FaultType" slot-scope="scope">
                            {{filterFault(scope.row.FaultType)}}
                        </template>


                        <template slot="IsWarranty" slot-scope="scope">
                            <span
                                :style="'color:' + (scope.row.IsWarranty > 1 ? (scope.row.IsWarranty == 4 ? '#F59A23' : '#00cc00') : '#F56C6C')"
                                >{{ scope.row.IsWarranty == 1 ? "否" : (scope.row.IsWarranty == 2 ? '是' : (scope.row.IsWarranty == 3 ? '是(未知有效期)' : '过保'))}}</span
                            >
                        </template>
                        <template slot="Remarks" slot-scope="scope">
                            <div class="omit" :title="scope.row.Remarks">{{ scope.row.Remarks ? scope.row.Remarks : '无'}}</div>
                        </template>
                        <template slot="StationSign" slot-scope="scope">
                            <div class="omit" :title="scope.row.StationSign">{{ scope.row.StationSign ? scope.row.StationSign : '无'}}</div>
                        </template>
                        <template slot="IsSignBill" slot-scope="scope">
                            <span :style="'color:'+(scope.row.IsSignBill ? serviceListStatus[scope.row.IsSignBill-1].color : '#909399')+';'">{{scope.row.IsSignBill ? serviceListStatus[scope.row.IsSignBill-1].label : '无'}}</span>
                        </template>


                        <template slot="OvertimeNightWork" slot-scope="scope">
                            {{ scope.row.IsSignBill == 2 ? '无' : (scope.row.OvertimeNightWork ? workTypes[scope.row.OvertimeNightWork - 1].label : '无')}}
                        </template>
                        <template slot="WorkingHours" slot-scope="scope">
                            {{ scope.row.IsSignBill == 2 ? '无' : (scope.row.WorkingHours ? scope.row.WorkingHours : '无')}}
                        </template>
                        <template slot="ServiceNo" slot-scope="scope">
                            {{ scope.row.IsSignBill == 2 ? '无' : (scope.row.ServiceNo ? scope.row.ServiceNo : '无')}}
                        </template>
                        <template slot="EntryTime" slot-scope="scope">
                            {{ scope.row.IsSignBill == 2 ? '无' : (scope.row.EntryTime ? getDay(scope.row.EntryTime) : '无')}}
                        </template>
                        <template slot="OtherExpenses" slot-scope="scope">
                            {{ scope.row.OtherExpenses ? scope.row.OtherExpenses : '无'}}
                        </template>
                        
                        <template slot="StructPartName" slot-scope="scope">
                            <div class="omit" :title="scope.row.StructPartName">{{ scope.row.StructPartName ? scope.row.StructPartName : '无'}}</div>
                        </template>
                        <template slot="ReportFailureRecord" slot-scope="scope">
                            <div class="omit" :title="scope.row.ReportFailureRecord">{{ scope.row.ReportFailureRecord ? scope.row.ReportFailureRecord : '无'}}</div>
                        </template>
                    </app-table-core>
                </div>
                <pagination
                    :total="total"
                    :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize"
                    @pagination="handleCurrentChange"
                    @size-change="handleSizeChange"
                />
            </div>
        </div>
    </div>

    <v-export
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>
    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="listQuery.RegionalId ? [listQuery.RegionalId] : []"
    ></v-area-choose>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as mo from "@/api/maintenanceCenter/maintenOrderMgmt";
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import { getDateTimeRange } from "@/utils/auth";
import { listToTreeSelect, throttle } from "@/utils";
import { vars } from "../maintenCenter/common/vars";
import vExport from "@/components/Export/index";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import dayjs from 'dayjs'
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"

export default {
  name: "saleSett",
  mixins: [indexPageMixin, tabDynamicHeightMixins],
  components: {
    vExport,
    vAreaChoose,
  },
  computed: {
   
  },
  created() {
    
  },
  watch: {
    
  },
  filters: {
    filterEmp(val){
        if(val){
            return val.map(v => v.Name).join(',');
        }else{
            return '无';
        }
    },
    
  },
  data() {
    return {
            layoutMode: 'simple',
        faultTypeOptions: vars.maintenOrderMgmt.FaultTypeOptions,
        handlingResultStatus:vars.maintenOrderMgmt.handlingResultStatus,
        serviceListStatus:vars.maintenOrderMgmt.serviceListStatus,
        warrantyList:vars.maintenOrderMgmt.isInsurance,
        workTypes: vars.maintenOrderMgmt.workTypes,
        dialogRegionalVisible:false,
        total:0,
        dialogExportVisible:false,
        pickerOptions: {
          shortcuts: [{
            text: '今年1月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('01');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年2月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('02');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年3月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('03');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },{
            text: '今年4月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('04');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年5月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('05');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年6月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('06');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },{
            text: '今年7月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('07');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年8月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('08');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年9月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('09');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },{
            text: '今年10月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('10');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年11月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('11');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }, {
            text: '今年12月',
            onClick(picker) {
              let [start,end]=getDateTimeRange('12');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          }]
        },
      listLoading: false,
      listQuery: {
        PageIndex:1,
        PageSize:20,
        ServiceNo:"",
        ReportRange: [],//服务单号
        RegionalId: '', //报修地区
        areaName:'',
        EmployeeName: "", //实施人员
        IsWarranty:'',
        HandlingResultStatus:[]
      },
      tableSearchItems: [
        { prop: "ReportRange", label: "报修时间" },
        { prop: "RegionalId", label: "地区" },
        { prop: "HandlingResultStatus", label: "故障处理结果" },
        {prop: "ServiceNo", label: "服务单号", mainCondition: true},
        { prop: "EmployeeName", label: "实施人员" },
        { prop: "IsWarranty", label: "是否在保" },
        
      ],
      tabColumns: [
        {
          attr: { prop: 'idx', label: '序号', width: 48, fixed: 'left'}
        },
        {
            attr: { prop: "ReportTime", label: "报修时间",width:120, fixed: 'left'},
            slot: true
        },
        {
            attr: { prop: "ArrivalTime", label: "到站/离站时间",width:240, fixed: 'left'},
            slot: true
        },
        {
            attr: { prop: "RegionalName", label: "地区", width:200, fixed: 'left'},
        },
        {
            attr: { prop: "TroubleShooter", label: "报修人"},
        },
        // {
        //     attr: { prop: "RegionalName1", label: "一级地区",},
        //     slot: true
        // },
        // {
        //     attr: { prop: "RegionalName2", label: "二级地区",},
        //     slot: true
        // },
        // {
        //     attr: { prop: "RegionalName3", label: "三级地区",},
        //     slot: true
        // },
        // {
        //     attr: { prop: "RegionalName4", label: "四级地区",},
        //     slot: true
        // },
        {
          attr: { prop: "ReporterNumber", label: "报修电话",},
          slot: true
        },
        {
          attr: { prop: "ReportFailureRecord", label: "故障记录",},
          slot: true
        },
        {
            attr: { prop: "Name", label: "加热炉/锅炉",width:90},
             slot: true
        },
        {
            attr: { prop: "HeatNumber", label: "炉号",width:90},
             slot: true
        },
        {
            attr: { prop: "EquipmentWorkModeName", label: "供风方式",},
            slot: true
        },
        {
          attr: { prop: "IsWarranty", label: "是否在保",width:110},
          slot: true
        },
        {
          attr: { prop: "HandlingResultStatus", label: "故障处理结果",width:100},
          slot: true
        },
        {
          attr: { prop: "Remarks", label: "备注",},
          slot: true
        },
        {
          attr: { prop: "StationSign", label: "站内签字",},
          slot: true
        },
        {
          attr: { prop: "IsSignBill", label: "是否签单",width:110 },
          slot:true
        },
        {
          attr: { prop: "OvertimeNightWork", label: "加班/夜勤",},
          slot: true
        },
        {
          attr: { prop: "WorkingHours", label: "工时",},
          slot: true
        },
        {
          attr: { prop: "ServiceNo", label: "单号",},
          slot: true
        },
        {
          attr: { prop: "EntryTime", label: "录入时间",width:120},
          slot: true
        },
        {
          attr: { prop: "HandlerEmployeeList", label: "实施人员" },
          slot: true
        },
        
        {
            attr: { prop: "Phenomenon", label: "故障现象",},
            slot: true
        },
        {
            attr: { prop: "CauseAnalysis", label: "故障原因",},
            slot: true
        },
        {
            attr: { prop: "Solution", label: "解决方式",},
            slot: true
        },
        {
            attr: { prop: "FaultType", label: "故障分类",},
            slot: true
        },
        {
            attr: { prop: "StructPartName", label: "更换配件",},
            slot: true
        },
        {
          attr: {prop: "SpecificationModel",label: "规格型号",width:100},
          slot:true
        },
        {
          attr: { prop: "Count", label: "配件数量",},
          slot: true
        },
        {
          attr: { prop: "UnitPrice", label: "单价",},
          slot: true
        },
        {
          attr: { prop: "TotalMoney", label: "总价",},
          slot: true
        },
        {
          attr: { prop: "OtherExpenses", label: "服务费用",},
          slot: true
        },
        
        
      ],
      // moneyData:{
      //     TotalMoney:0,
      //     StructPartTotalMoney:0,
      //     OtherExpensesTotalMoney:0

      // },
      tabDatas: [],
      rData:null,
      cData:[],
      multipleSelection: [],
      notRowspanColumns: ['Phenomenon','CauseAnalysis','Solution','FaultType','StructPartName','SpecificationModel','Count','UnitPrice'] //不需要合并的列

    };
  },
  methods: {
      collapseOrExpand(val){
        this.$nextTick(()=>{
          this.setTabHeight()
        })
      },
      // getMoney(){
      //     let postData = JSON.parse(JSON.stringify(this.listQuery));
      //     if (postData.ReportRange && postData.ReportRange.length == 2) {
      //         postData.ReportTimeStart = postData.ReportRange[0];
      //         postData.ReportTimeEnd = postData.ReportRange[1];
      //         delete postData.ReportRange;
      //     }
      //     postData.PageSize=100000;
      //     this.listLoading=true;
      //     mo.getAfterSalesSettlementCountMoney(postData).then(res => {
      //         this.moneyData=res;
      //         this.listLoading=false;
      //     })
      // },
      filterFault(val){
       let a= this.faultTypeOptions.find(v => v.value == val);
       if(a) return a.label;
       else return '无';
    },
    filterResult(val){
        let a= this.handlingResultStatus.find(v => v.value == val);
       if(a) return a.label;
       else return '无';
    },
    getColor(val){
        let a= this.handlingResultStatus.find(v => v.value == val);
       if(a) return a.color;
       else return '';
    },
      getDay(val){
          return dayjs(val).format('YYYY-MM-DD HH:mm');
      },
      rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
      onBtnClicked: function(domId) {
      switch (domId) {
        case "btnExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },
      closeRegionalDialog() {
      this.dialogRegionalVisible=false
    },
    electedRegionalData(data){
        if(data) {
          this.listQuery.RegionalId=data.Id;
          this.listQuery.areaName=data.ParentName;
        }else{
          this.listQuery.RegionalId='';
          this.listQuery.areaName='';
        }
    },
      handleRegionalDialog() {
      
      this.dialogRegionalVisible=true;
    },
    handleClearRegional() {
      this.listQuery.RegionalId = ''
      this.listQuery.areaName = ''
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {

      if (column.property && this.notRowspanColumns.findIndex(s => s.toLowerCase() == column.property.toLowerCase()) == -1) {
        if (row.rowspan > 0) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
      getList(){
          let postData = JSON.parse(JSON.stringify(this.listQuery));
          if (postData.ReportRange && postData.ReportRange.length == 2) {
              postData.ReportTimeStart = postData.ReportRange[0];
              postData.ReportTimeEnd = postData.ReportRange[1];
              delete postData.ReportRange;
          }
          this.listLoading=true;
          mo.getAfterSalesSettlementNew(postData).then(res => {
            this.listLoading=false;
            let resultDatas = []
            let tempDatas = JSON.parse(JSON.stringify(res.Items))

            this.total = res.Total;
            tempDatas.forEach((s, index) => {

              let temp = JSON.parse(JSON.stringify(s))
              temp.rowspan = 1 //默认不跨行(跨一行)
              this.notRowspanColumns.forEach(colName => {
                temp[colName] = ''
              })

              //因为有合并行，所有需要需要处理（多行同一个序号）
              temp.idx = ((postData.PageIndex - 1) * postData.PageSize) + (index + 1)

              let tempList = s.FaultPhenomenonList

              if(tempList && tempList.length > 0) {
                tempList.forEach((ele, idx2) => {
                  if(idx2 == 0) {
                    temp.rowspan = tempList.length //判断需要跨行数量
                  }else{
                    temp.rowspan = 0
                  }
                  temp = Object.assign({}, temp, ele)
                  resultDatas.push(JSON.parse(JSON.stringify(temp)))
                });
              }else{
                resultDatas.push(JSON.parse(JSON.stringify(temp)))
              }
            })
            this.tabDatas=resultDatas
          }).catch(err => {
              this.listLoading=false;
          })
      },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
      // this.getMoney();
    },
    handleResetSearch() {
      this.listQuery =  {
          PageIndex:1,
          PageSize:20,
        ServiceNo:"",
        ReportRange: [],//服务单号
        RegionalId: '', //报修地区
        areaName:'',
        EmployeeName: "", //实施人员
        IsWarranty:'',
        HandlingResultStatus:[],
      };
      this.getList(); //刷新列表
      // this.getMoney();
    },
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    handleExport(){
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      if (this.listQuery.TableActive == "my") {
        postData.HandlerEmployeeId = getUserInfo().employeeid;
      }
      if (postData.ReportRange && postData.ReportRange.length == 2) {
        postData.ReportTimeStart = postData.ReportRange[0];
        postData.ReportTimeEnd = postData.ReportRange[1];
        delete postData.ReportRange;
      }
      postData.ExportType = 1; // 1.报修单导出 2.售后结算导出
      this.rData={
          "exportSource": 18,
          "columns": [],
          "searchCondition": postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'报修人',
        value:'TroubleShooter'
      },{
        label:'报修时间',
        value:'ReportTimeString'
      },
      {
        label:'到站/离站时间',
        value:'ArrivalAndDepartureTime'
      },
      {
        label:'一级地区',
        value:'RegionalName1'
      },{
        label:'二级地区',
        value:'RegionalName2'
      },
      {
        label:'三级地区',
        value:'RegionalName3'
      },{
        label:'四级地区',
        value:'RegionalName4'
      },{
        label:'五级地区',
        value:'RegionalName5'
      },{
        label:'报修电话',
        value:'ReporterNumber'
      },
      {
        label:'故障记录',
        value:'ReportFailureRecord'
      },
      {
        label:'加热炉/锅炉',
        value:'Name'
      },{
        label:'炉号',
        value:'HeatNumber'
      },{
        label:'供风方式',
        value:'EquipmentWorkModeName'
      },{
        label:'故障现象',
        value:'Phenomenon'
      },{
        label:'故障原因',
        value:'CauseAnalysis'
      },{
        label:'解决方式',
        value:'Solution'
      },
      {
        label:'实施人员',
        value:'HandlerEmployeeListString'
      },
      {
        label:'故障分类',
        value:'FaultType'
      },{
        label:'更换配件',
        value:'StructPartName'
      },
      {
        label:'规格型号',
        value:'SpecificationModel'
      },{
        label:'配件数量',
        value:'Count'
      },{
        label:'单价',
        value:'UnitPrice'
      },{
        label:'总价',
        value:'TotalMoney'
      },{
        label:'是否在保',
        value:'IsWarrantyName'
      },
      {
        label:'故障处理结果',
        value:'HandlingResultStatus'
      },
      {
        label:'备注',
        value:'Remarks'
      },{
        label:'站内签字',
        value:'StationSign'
      },{
        label:'是否签单',
        value:'IsSignBill'
      },{
        label:'加班/夜勤',
        value:'OvertimeNightWork'
      },{
        label:'工时',
        value:'WorkingHours'
      },{
        label:'单号',
        value:'ServiceNo'
      },
      {
        label:'录入时间',
        value:'EntryTimeString'
      },
      {
        label:'服务费用',
        value:'OtherExpenses'
      },
      
      ]
      this.dialogExportVisible=true;
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
  }
};
</script>

<style lang="scss" scoped>

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;


        .content {
            // padding: 0 10px;
            // padding-right: 0;


            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}


.btns-area {
    margin-left: 4px;
    button{
        margin-right: 4px;
    }
}
</style>
