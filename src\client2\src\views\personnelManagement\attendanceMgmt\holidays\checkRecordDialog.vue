<template>
    <div>
        <app-dialog title="异常校正记录" ref="appDialogSel" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <div class="wrapper">
                    <app-table-core 
                        :isShowBtnsArea='false' 
                        ref="mainTable" 
                        :tab-columns="tabColumns" 
                        :tab-datas="tabDatas" 
                        :tab-auth-columns="[]" 
                        :isShowAllColumn="true" 
                        :loading="listLoading" 
                        :isShowOpatColumn="true" 
                        :startOfTable="startOfTable" 
                        :multable="false" 
                        :isShowConditionArea='true'
                    >
                        <template slot="CreateEmployee" slot-scope="scope">
                            {{ scope.row.CreateEmployee ? scope.row.CreateEmployee.Name : '无' }}
                        </template>
                        <template slot="StartTime" slot-scope="scope">
                            {{ scope.row.StartTime | dateFilter('YYYY-MM-DD') }} - {{ scope.row.EndTime | dateFilter('YYYY-MM-DD') }}
                        </template>
                        <template slot="CreateTime" slot-scope="scope">
                            {{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}
                        </template>
                        
                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleCheckRecordDetailDialog(scope.row)" :type="2"></app-table-row-button>
                        </template>
                    </app-table-core>

                    <pagination 
                        v-show="total>0" 
                        :total="total" 
                        :page.sync="listQuery.PageIndex" 
                        :size.sync="listQuery.PageSize" 
                        @pagination="handleCurrentChange" 
                        @size-change="handleSizeChange" />
                </div>
            </template>

            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
            </template>
        </app-dialog>

        <checkRecordDetailDialog 
            v-if="dialogFormCheckRecordDetailVisible && id"
            @closeDialog="closeCheckRecordDetailDialog" 
            :dialogFormVisible="dialogFormCheckRecordDetailVisible"
            :id='id'
        ></checkRecordDetailDialog>
    </div>
</template>

<script>
import * as timecardCorrectOvertimeRest from '@/api/personnelManagement/timecardCorrectOvertimeRest'
import indexPageMixin from "@/mixins/indexPage";
import AppTableCore from '../../../../components/AppTable/AppTableCore.vue';
import checkRecordDetailDialog from './checkRecordDetailDialog'

export default {
    name: "check-record-dialog",
    directives: {},
    components: {
        AppTableCore,
        checkRecordDetailDialog,
    },
    mixins: [indexPageMixin],
    computed: {

    },
    props: {
        
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList()
                }
            },
            immediate: true
        }
    },
    filters: {
    },
    created() {
    },
    mounted() {

    },
    data() {
        return {
            id: '',
            dialogFormCheckRecordDetailVisible: false,
            total: 0,

            listLoading: false,
            tabDatas: [],
            tabColumns: [
                {
                    attr: {
                        prop: "CreateEmployee",
                        label: "执行人",
                    },
                    slot: true,
                },{
                    attr: {
                        prop: "StartTime",
                        label: "日期范围",
                        width: '200'
                    },
                    slot: true,
                },{
                    attr: {
                        prop: "CreateTime",
                        label: "执行时间",
                    },
                    slot: true,
                },{
                    attr: {
                        prop: "CorrectCount",
                        label: "校正数量",
                    },
                },
            ],
        };
    },
    methods: {
        getList() {
            let postDatas = JSON.parse(JSON.stringify(this.listQuery))
            this.listLoading = true
            timecardCorrectOvertimeRest.getList(postDatas).then(res => {
                this.listLoading = false
                this.total = res.Total
                this.tabDatas = res.Items
            }).catch(err => {
                this.listLoading = true
            })
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCheckRecordDetailDialog(row) {

            this.id = row.Id
            this.dialogFormCheckRecordDetailVisible = true
        },
        closeCheckRecordDetailDialog() {
            this.dialogFormCheckRecordDetailVisible = false
        },
        handleClose() {
            this.$refs.appDialogSel.handleClose();
        }
    }
};
</script>


<style lang='scss' scoped>
.wrapper{
    padding: 10px 0;
}
</style>