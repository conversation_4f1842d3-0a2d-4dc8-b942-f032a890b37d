<template>
<div class="createEmployee">
    <app-dialog title="合同主体管理" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
        <template slot="body">
            <div class="page-content">
                <el-button type="primary" @click="handleCreate">创建合同主体</el-button>
                <div class="list-wrapper">
                    <app-table-core ref="mainTable" 
                        :isShowBtnsArea='false' 
                        :tab-columns="tabColumns" 
                        :tab-datas="tabDatas" 
                        :tab-auth-columns="[]" 
                        :isShowAllColumn="true" 
                        :loading="loading" 
                        :isShowOpatColumn="true" 
                        :startOfTable="0" 
                        :multable="false" 
                        :layoutMode='layoutMode'>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleTableUpdate(scope.row, scope.index)" :type="1"></app-table-row-button>
                            <app-table-row-button @click="handleRemove(scope.index - 1)" :type="3"></app-table-row-button>
                        </template>
                    </app-table-core>
                </div>
            </div>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="handleSave" :buttonType="1" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>

    <create-page 
        @closeDialog="closeCreatePageDialog" 
        @saveSuccess="handleCreatePageSaveSuccess" 
        :dialogFormVisible="dialogCreatePageFormVisible" 
        :dialogStatus="dialogCreatePageStatus" 
        :row='currentRow'
        :rowIdx='currentRowIdx'
    ></create-page>
    

</div>
</template>

<script>
// import * as systemEmployee from '@/api/personnelManagement/systemEmployee'
import * as laborContractSubject from '@/api/personnelManagement/laborContractSubject'
import createPage from './contractCreate'
export default {
    name: "contract-list",
    directives: {},
    components: {
        createPage
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
    },
    filters: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList();        
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
    },
    mounted() {
    },
    data() {
        return {
            disabledBtn: false,
            tabColumns: [{
                    attr: {
                        prop: "LaborContractSubjectName",
                        label: "合同主体名称",
                        width: '300',
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "Remark",
                        label: "备注",
                        width: '520',
                        showOverflowTooltip: true,
                    },
                },
            ],
            layoutMode: 'simple',
            loading: false,
            tabDatas: [],
            dialogCreatePageStatus: 'create',
            dialogCreatePageFormVisible: false,
            currentRow: null,
            currentRowIdx: -1,
        };
    },
    methods: {
        handleCreatePageSaveSuccess({dialogStatus, formObj, idx}) {
            if(dialogStatus == 'create') {
                this.tabDatas.splice(0, 0, formObj)
            }else{
                this.tabDatas.splice(idx, 1, formObj)
            }
            this.closeCreatePageDialog();
        },
        handleRemove(idx) {
            this.$confirm(`是否确认删除当前行?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tabDatas.splice(idx, 1)
            })
        },
        handleTableUpdate(row, rowIdx) {
            this.currentRow = row
            this.currentRowIdx = rowIdx - 1
            this.dialogCreatePageStatus = 'update'
            this.dialogCreatePageFormVisible = true
        },
        closeCreatePageDialog() {
            this.dialogCreatePageFormVisible = false
        },
        handleCreate() {
            this.dialogCreatePageStatus = 'create'
            this.dialogCreatePageFormVisible = true
        },
        getList() {
            this.loading = true
            laborContractSubject.getAllLaborContractSubject({}).then(res => {
                this.loading = false
                this.tabDatas = res
            }).catch(err => {
                this.loading = true
            })
        },
        handleSave() {
            this.disabledBtn = true
            laborContractSubject.addOrUpdateOrDelete(this.tabDatas || []).then(res => {
                this.disabledBtn = false
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.$refs.appDialogRef.createData()
            }).catch(err => {
                this.disabledBtn = false
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.page-content{
    padding: 10px 0;
    min-height: 400px;
    .list-wrapper{
        margin-top: 10px;
    }
}

</style>
