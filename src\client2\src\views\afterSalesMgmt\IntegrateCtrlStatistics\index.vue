<template>
<div class="app-container bg-white">
    <div class="lft">
        <div class="panel-title">油田列表</div>
        <div class="panel-content">
            <noData v-if="!relationsLoading && relations.length == 0"></noData>
            <tags v-loading='relationsLoading' mode="list" :items="relations" v-model="currentRelation"></tags>
        </div>
    </div>
    <div class="mid">
        <div class="panel-title">
            <span>厂级列表</span>
            <el-button type="text" @click="handleCheckedAll()">{{ checkedAll ? '全不选' : '全选' }}</el-button>
        </div>
        <div class="panel-content">
            <noData v-if="!relationListLoading && relationList.length == 0"></noData>
            <tags v-else v-loading='relationListLoading' mode="list" :items="relationList">
                <template v-for="(item) in relationList" :slot="item.value">
                    <div :key="item.Id" class="item">
                        <el-checkbox v-model="item.checked">{{ item.label }}</el-checkbox>
                    </div>
                </template>
            </tags>
        </div>
    </div>
    <div class="rht">
        <div class="panel-title">
            <span>整体统计</span>
            <el-button type="text" @click="handleShowEcharts()">{{ showCharts ? '收起' : '展开' }} <i :class="showCharts ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i> </el-button>
        </div>
        <div>
            <el-collapse-transition>
                <div class="charts-panel" v-show="showCharts" v-loading='echartLoading'>
                    <div id="chart1">
                        <div class="chart-title">安装站点（{{ totalTuoGuan }}）</div>
                        <noData v-if="!totalTuoGuan"></noData>
                        <app-charts-basic v-else ref="pieEchart1" :option='pieEchartOption1'></app-charts-basic>
                    </div>
                    <div id="chart2">
                        <div class="chart-title">已托管设备（{{ tuoGuanTotal }}）</div>
                        <noData v-if="!tuoGuanTotal"></noData>
                        <app-charts-basic v-else ref="pieEchart2" :option='pieEchartOption2'></app-charts-basic>
                    </div>
                    <div id="chart3">
                        <div class="chart-title">未托管设备（{{ weiTuoGuanTotal }}）</div>
                        <noData v-if="!weiTuoGuanTotal"></noData>
                        <app-charts-basic v-else ref="pieEchart3" :option='pieEchartOption3'></app-charts-basic>
                    </div>
                </div>
            </el-collapse-transition>
        </div>
        <div class="panel-content __dynamicTabContentWrapper">
            <div class="opt-row">
                <span class="title">地区详情统计</span>
                <div class="mid">
                    <el-radio-group v-model="currentType" @change='getList()'>
                        <el-radio-button v-for="(r, idx) in types" :key="idx" :label="r.value">{{ r.label }}</el-radio-button>
                    </el-radio-group>
                </div>

                <el-button type="primary" @click="handleExport">导出数据</el-button>
            </div>
            <div id="__dynamicTabCoreWrapper">
                <app-table-core :loading='listLoading' @sortChagned="handleSortChange" ref="mainTable" :cell-style='{verticalAlign: "top"}' :span-method="objectSpanMethod" :height='tabHeight' :tab-columns="tabColumns" :serial='false' :tab-datas="tabDatas" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable='false'>
                    <template slot="CustodyNote" slot-scope="scope">
                        {{ scope.row.CustodyNote | emptyFilter }}
                    </template>
                    <template slot="Usage" slot-scope="scope">
                        {{ scope.row.Usage | emptyFilter }}
                    </template>
                    <template slot="Name" slot-scope="scope">
                        {{ scope.row.Name | emptyFilter }}
                    </template>

                    <template slot="RegionalName" slot-scope="scope">
                        <div style="height: 100%;">
                            <div>
                                {{ scope.row.RegionalName }}
                            </div>
                            <div class="item-status" style="margin-right: 10px; display: inline-block; width: calc(50% - 10px); color: rgb(64, 158, 255);">已托管设备：{{ scope.row.HostingCount }}</div>
                            <div class="item-status" style="margin-right: 10px; display: inline-block; width: calc(50% - 10px); color: rgb(245, 108, 108);">未托管设备：{{ scope.row.NotHostingCount }}</div>
                        </div>
                    </template>
                    <template slot="LastTime" slot-scope="scope">
                        <span @click="handleRecordDialog(scope.row.RegionalBusinessRelationId)" style="cursor: pointer;" class="el-button--text el-button--mini">
                            {{ scope.row.LastTime | dateFilter('YYYY-MM-DD') }}（{{ scope.row.EmployeeName }}）
                        </span>
                    </template>
                    <template slot="WhetherHosting" slot-scope="scope">
                        <span>
                            <span style="color: rgb(64, 158, 255);" v-if="scope.row.WhetherHosting === true">是</span>
                            <span style="color: rgb(245, 108, 108);" v-else-if="scope.row.WhetherHosting === false">否</span>
                            <span v-else>{{ '' | emptyFilter }}</span>
                        </span>
                    </template>
                    
                    <!-- <template slot='operation' slot-scope="scope">
                        <el-button v-if="btnMaintain=='btnMaintain'" type="text" @click="getPartDetail(scope.row.StructPartsSpecificationId,'equipmentDetail')">详情</el-button>
                        <el-button v-if="btnMaintain=='btnMaintain'" type="text" style="color:#F56C6C;" @click="deleter(scope.row.Id)">移除</el-button>
                    </template> -->
                </app-table-core>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" layout="total, prev, pager, next, jumper" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>

    <record v-if="dialogRecordFormVisible && id" :regionalBusinessRelationId='id' @closeDialog="closeRecordDialog" :dialogFormVisible="dialogRecordFormVisible"></record>

    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>
</div>
</template>

<script>
import * as regionalBusinessRelation from "@/api/afterSalesMgmt/regionalBusinessRelation"
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import noData from "@/views/common/components/noData"
import record from '../softwareAftersalesReturnVisit/record'
import vExport from "@/components/Export/index"
// import pieChart from './pieChart'
import indexPageMixin from "@/mixins/indexPage"
import * as equipmentParameter from "@/api/afterSalesMgmt/equipmentParameter"

let pieDatas = [
    { value: 100, name: '托管', color: '#6dcbcb' },
    { value: 200, name: '未托管', color: '#629fff' },
]

let colors = ['#3aa1ff', '#88d1ea', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2']

let pieEchartOptionTemp = {
    // title: {
    //     text: '',
    //     left: '10',
    //     textStyle: {
    //         fontSize: 14,
    //         color: "rgba(109, 109, 109, 1)",
    //         fontWeight: "600",
    //         fontStyle: "normal",
    //         lineHeight: 40
    //     },
    // },
    color: colors,
    tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)'
    },
    legend: {
        bottom: 0,
        left: 'center',
        data: [],
    },
    series: [
        {
            type: 'pie',
            radius: ['30%', '60%'],
            minShowLabelAngle: 10,
            avoidLabelOverlap: false,
            center: ['50%', '45%'],
            animation: true,
            label: {
                normal: {
                    show: true,
                    color: "#1D2129",
                    padding: [0, 0, 0, 0],
                    fontSize: 13,
                    formatter: '{b}',
                    // formatter: '{b} : {c} ({d}%)'
                }
            },
            data: []
        }
    ]
};
export default {
    name: "",
    mixins: [indexPageMixin, tabDynamicHeightMixins],
    components: {
        noData,
        // pieChart,
        record,
        vExport,
    },
    watch: {
        currentRelation(val) {
            if(val) {
                // this.checkedAll = false
                this.currentType = 1
                this.getRelationList()
            }
        },
        relationList: {
            handler(val) {
                this.getChartsDatas()
                this.getList()
            },
            deep: true
        }
    },
    computed: {
    },
    created() {
        this.getRelations()
    },
    filters: {
    },
    mounted() {
        
    },
    data() {
        return {
            //油田
            relationsLoading: false,
            relations: [],
            currentRelation: null,

            //长
            relationListLoading: false,
            relationList: [],
            checkedAll: false,
            pieEchartOption1: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption2: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption3: JSON.parse(JSON.stringify(pieEchartOptionTemp)),

            totalTuoGuan: 0,
            tuoGuanTotal: 0,
            weiTuoGuanTotal: 0,
            
            showCharts: true,

            currentType: 1,
            types: [
                {value: 1, label: '全部'},
                {value: 2, label: '已托管设备'},
                {value: 3, label: '未托管设备'},
            ],
            echartLoading: false,
            tabColumns: [
                {
                    attr: { prop: 'idx', label: '序号', width: 48, fixed: 'left'},
                },
                {
                    attr: { prop: "RegionalName", width: 300, label: "地区名称", fixed: 'left'},
                    slot: true
                },
                {
                    attr: { prop: "LastTime", label: "最近回访时间", width: 200, fixed: 'left', showOverflowTooltip: true},
                    slot: true
                },
                {
                    attr: {
                        prop: "Name",
                        label: '加热炉',
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "WhetherHosting",
                        label: "是否托管",
                        showOverflowTooltip: true,
                        // sortable: 'custom'
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "Usage",
                        label: "用途",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "CustodyNote",
                        label: "托管备注",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
            ],

            notRowspanColumns: ["Name", "Usage", "EquipmentModel", "WhetherHosting", "CustodyNote"], //不需要合并的列
            tabDatas: [],
            total: 0,
            listLoading: false,

            dialogRecordFormVisible: false,
            id: '',

            rData:null,
            cData:[],
            dialogExportVisible: false,
        }
    },
    methods: {
        handleRecordDialog(id) {
            this.id = id
            this.dialogRecordFormVisible = true
        },
        closeRecordDialog() {
            this.dialogRecordFormVisible = false
        },
        //获取油田
        getRelations() {
            let postDatas = {
                regionalBusinessType: 1,
                parentId: null
            }
            this.relationsLoading = true
            regionalBusinessRelation.getList2(postDatas).then(res => {
                this.relationsLoading = false
                this.relations = res.map(s => {
                    return {
                        label: s.RegionalName,
                        value: s.Id
                    }
                })

                if(!this.currentRelation && this.relations.length > 0) {
                    this.currentRelation = this.relations[0].value
                }
            }).catch(err => {
                this.relationsLoading = false
            })
        },
        
        //获取厂级列表
        getRelationList() {
            let postDatas = {
                regionalBusinessType: 1,
                parentId: this.currentRelation
            }
            this.relationListLoading = true
            regionalBusinessRelation.getList2(postDatas).then(res => {
                this.relationListLoading = false
                this.relationList = res.map(s => {
                    return {
                        label: s.RegionalName,
                        value: s.Id,
                        checked: true,
                    }
                })
                this.checkedAll = true;
                // this.relationList = this.relationList.concat(temp)
                
            }).catch(err => {
                this.relationListLoading = false
            })
        },
        //全选
        handleCheckedAll() {
            this.checkedAll = !this.checkedAll
            this.relationList.forEach(e => {
                e.checked = this.checkedAll
            });
        },
        //获取charts数据
        getChartsDatas() {
            this.echartLoading = true
            let postDatas = this.relationList.filter(s => s.checked).map(s => s.value)
            equipmentParameter.chartOverallStatistical(postDatas).then(res => {
                this.echartLoading = false

                let hostingCount = res.HostingCount
                let notHostingCount = res.NotHostingCount
                let totalTuoGuan = hostingCount + notHostingCount
                let tuoGuanTotal = res.HostingList.map(s => s.Value).reduce(function(prev, curr, idx, arr){
                    return prev + curr;
                }, 0);
                let weiTuoGuanTotal = res.NotHostingList.map(s => s.Value).reduce(function(prev, curr, idx, arr){
                    return prev + curr;
                }, 0);

                this.totalTuoGuan = totalTuoGuan
                this.tuoGuanTotal = tuoGuanTotal
                this.weiTuoGuanTotal = weiTuoGuanTotal
                
                // this.pieEchartOption1.title.text = `回访站点（${totalTuoGuan}）`
                // this.pieEchartOption2.title.text = `已托管（${tuoGuanTotal}）`
                // this.pieEchartOption3.title.text = `未托管（${weiTuoGuanTotal}）`

                let noData = hostingCount == 0 && notHostingCount == 0
                let targetOption1 = {
                    legend: {
                        data: noData ? [] : ['已托管', '未托管'],
                    },
                    series: [
                        {
                            data: noData ? [] : [
                                {value: res.HostingCount, name: '已托管'},
                                {value: res.NotHostingCount, name: '未托管'},
                            ],
                        }
                    ],
                }

                let targetOption2 = {
                    legend: {
                        data: res.HostingList.map(s => s.Key)   //pieDatas.map(s => s.name),
                    },
                    series: [
                        {
                            data: res.HostingList.map(s => {
                                return {
                                    value: s.Value,
                                    name: s.Key
                                }
                            }),
                        }
                    ],
                }

                let targetOption3 = {
                    legend: {
                        data: res.NotHostingList.map(s => s.Key) //pieDatas.map(s => s.name),
                    },
                    series: [
                        {
                            data: res.NotHostingList.map(s => {
                                return {
                                    value: s.Value,
                                    name: s.Key
                                }
                            }),
                        }
                    ],
                }

                this.pieEchartOption1 = _.merge({}, this.pieEchartOption1, targetOption1)

                this.pieEchartOption2 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption2)
                this.pieEchartOption3 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption3)
            }).catch(err => {
                this.echartLoading = false
            })
        },
        handleShowEcharts() {
            this.showCharts = !this.showCharts;
            setTimeout(() => {
                this.setTabHeight()
            }, 300)
        },
        handleSuccessExport() {},
        handleCloseExport() {
            this.dialogExportVisible = false;
        },
        handleExport() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData)
            postData.IdList = this.relationList.filter(s => s.checked).map(s => s.value)

            this.rData={
                "exportSource": 28,
                "columns": [],
                "searchCondition": postData
            }

            let notExportColumns = ['idx', 'LastTime']
            let exportColumns = JSON.parse(JSON.stringify(this.tabColumns))
                            .filter(s => notExportColumns.findIndex(c => c == s.attr.prop) == -1)
                            .map(s => {
                                return {
                                    label: s.attr.label,
                                    value: s.attr.prop
                                }
                            })
            this.cData = exportColumns
            
            this.dialogExportVisible=true;
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (column.property && this.notRowspanColumns.findIndex(s => s.toLowerCase() == column.property.toLowerCase()) == -1) {
                if (row.rowspan > 0) {
                    return {
                        rowspan: row.rowspan,
                        colspan: 1
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0
                    };
                }
            }
        },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        getList() {
            this.listLoading = true
            let postData = JSON.parse(JSON.stringify(this.listQuery))
            postData = this.assignSortObj(postData);

            postData.IdList = this.relationList.filter(s => s.checked).map(s => s.value)

            if(this.currentType == 1) {
                postData.WhetherHosting = null
            }else if(this.currentType == 2) {
                postData.WhetherHosting = true
            }else if(this.currentType == 3) {
                postData.WhetherHosting = false
            }

            equipmentParameter.districtStatistics(postData).then(res => {
                this.listLoading = false
                
                let resultDatas = []
                let tempDatas = JSON.parse(JSON.stringify(res.Items))

                this.total = res.Total;
                tempDatas.forEach((s, index) => {

                    let temp = JSON.parse(JSON.stringify(s))
                    temp.rowspan = 1 //默认不跨行(跨一行)
                    this.notRowspanColumns.forEach(colName => {
                        temp[colName] = ''
                    })

                    //因为有合并行，所有需要需要处理（多行同一个序号）
                    temp.idx = ((postData.PageIndex - 1) * postData.PageSize) + (index + 1)

                    let tempList = s.EquipmentParameterList

                    if(tempList && tempList.length > 0) {
                        tempList.forEach((ele, idx2) => {
                        if(idx2 == 0) {
                            temp.rowspan = tempList.length //判断需要跨行数量
                        }else{
                            temp.rowspan = 0
                        }
                        temp = Object.assign({}, temp, ele)
                        resultDatas.push(JSON.parse(JSON.stringify(temp)))
                        });
                    }else{
                        resultDatas.push(JSON.parse(JSON.stringify(temp)))
                    }
                })
                this.tabDatas = resultDatas

            }).catch(err => {
                this.listLoading = false;
            })
        },
    },
};
</script>

<style lang="scss" scoped>
.app-container{
    // overflow-y: hidden;
    display: flex;
    .lft, .mid{
        width: 200px;
        flex-shrink: 0;
        box-sizing: border-box;
        border-right: 1px solid #dcdfe6;
    }
    .lft, .mid, .rht{
        display: flex;
        flex-direction: column;
    }
    .lft{

    }
    .mid{

    }
    .rht{
        flex: 1;
        width: 100%;
        overflow-x: hidden;
        .panel-title{
            margin-bottom: 0;
        }
    }
    .panel-title{
        font-size: 14px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        line-height: 30px;
        padding: 0 10px;
    }
    .panel-content{
        flex: 1;
        height: calc(100% - 30px);
        overflow-y: auto;
        ::v-deep .list-wrapper{
            height: 100%;
            padding-left: 10px;
        }
    }
}

.charts-panel{
    display: flex;
    margin-bottom: 10px;
    >div{
        flex: 1;
        // flex-shrink: 0;
        // display: inline-block;
        height: 340px;
        width: 33.3%;
    }
    .chart-title{
        font-weight: 600;
        padding: 10px;
    }
}

.opt-row{
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
    .title{
        margin-right: 20px;
        font-size: 14px;
        font-weight: 600;
    }
    .mid{
        flex: 1;
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
    
}
</style>
