<template>
<div class="news-wrapper">
    <!-- <div class="opt">
        <i class="el-icon-back" @click="back">返回</i>
    </div> -->
    <page-title :showBackBtn='true' @goBack="back">
        <div slot="def">
            
        </div>
    </page-title>
    <div style="display:flex;height:calc(100% - 40px);overflow-y: auto;">
        <div style="width:70%; padding-left: 10px;" v-loading="loading">
            <div class="news-container">
                <header style="font-size: 25px;padding-top: 5px;">{{ newDetail.ProductTitle }}</header>
                <div style="padding-left: 20px;padding-bottom: 5px;">
                    <el-tag style="margin-right:15px;">{{newDetail.ProductClassificationName}}</el-tag>

                    <el-tag type="info" style="margin-right:5px;" v-for="item in ProductFeatureList" :key="item">{{item}}</el-tag>
                </div>
                <div class="hr" style="margin: 0px;">
                    <span class="hrl"></span>
                    <span class="hrc"></span>
                    <span class="hrr"></span>
                </div>
                <div class="divUeditor ql-editor" v-html="newDetail.ProductDescribe"></div>
                <div>
                </div>
            </div>
        </div>

        <el-row style="margin-top:40px;flex:1;border-left:1px solid #eee;" v-loading="productPresentationsListLoading">
            <div style="height:40%;">
                <div style="font-size: 20px;line-height:40px;border-bottom:1px solid #eee;padding-left:5%;">相关附件</div>
                <div style="height:calc(100% - 50px);overflow-y:auto;padding-left:5%;">
                    <app-uploader v-if="newDetail.AttachmentList&&newDetail.AttachmentList.length>0" accept="all" :fileType="3" :max="10000" :value="newDetail.AttachmentList" readonly
                    :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"></app-uploader>
                    <no-data v-else></no-data>
                </div>
            </div>
            <div style="height:60%;">
                <div style="font-size: 20px;line-height:40px;border-bottom:1px solid #eee;padding-left:5%;">相关内容</div>
                <div style="height:calc(100% - 50px);overflow-y:auto;padding-left:5%;">
                    <!-- 新闻动态 -->
                    <relate-list :list='productPresentationsDatas' @click="handleProductPresentationsReview" :show-title="false"></relate-list>
                </div>
            </div>
        </el-row>
    </div>

</div>
</template>

<script>
import * as productPresentations from '@/api/informationCenter/productPresentations'
import relateList from '../../common/relateList'
import NoData from "@/views/common/components/noData";
export default {
    name: 'productPresentations-detail',
    components: {
        relateList,
        NoData,
    },
    data() {
        return {
            newDetail: {},
            productPresentationsListLoading: false,
            productPresentationsListQuery: {
                ProductClassificationId: '',
                id: null,
                IsShow: null,
                ProductTitle: '',
                WebOrApp: 'Web'
            },
            productPresentationsDatas: [],
            ProductFeatureList: []

        }
    },
    created() {
        this.getNewDetail();
        this.getProductPresentationsList();
    },

    methods: {
        handleProductPresentationsReview(row) { //查看详情
            productPresentations.detail({
                id: row.Id
            }).then(res => {
                this.newDetail = res
                this.ProductFeatureList = []
                if (res.ProductFeature != null) {
                    this.ProductFeatureList = res.ProductFeature.split(',')
                }
                //获取
                this.productPresentationsListLoading = true
                this.productPresentationsListQuery.id = row.Id
                this.productPresentationsListQuery.IsShow = 1;
                let productClassificationId = this.$route.query.productClassificationId
                this.productPresentationsListQuery.ProductClassificationId = productClassificationId;
                productPresentations.getList(this.productPresentationsListQuery).then(response => {
                    this.productPresentationsDatas = response.Items.map(s => {
                        s.Text = s.ProductTitle
                        return s
                    })
                    this.productPresentationsListLoading = false
                })
            })
        },
        back() {
            this.$router.go(-1)
        },
        getProductPresentationsList() {
            this.productPresentationsListLoading = true
            let id = this.$route.params && this.$route.params.id
            let productClassificationId = this.$route.query.productClassificationId
            this.productPresentationsListQuery.id = id;
            this.productPresentationsListQuery.IsShow = 1;
            this.productPresentationsListQuery.ProductClassificationId = productClassificationId;
            productPresentations.getList(this.productPresentationsListQuery).then(response => {
                this.productPresentationsDatas = response.Items.map(s => {
                    s.Text = s.ProductTitle
                    return s
                })
                this.productPresentationsListLoading = false
            })
        },
        getNewDetail() {
            let id = this.$route.params && this.$route.params.id
            productPresentations.detail({
                id: id
            }).then(res => {
                this.newDetail = res
                if (res.ProductFeature != null) {
                    this.ProductFeatureList = res.ProductFeature.split(',')
                }
            })
        },
    }
}
</script>

<style scoped>
.news-wrapper {
    padding: 60px 40px 0;
    overflow: hidden;
    background: #ffffff;
    margin: 10px;
    position: absolute;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    background: white;
    /* overflow: auto; */
}

.news-container {
    max-width: 1080px;
    margin: 0 auto;
    padding-bottom: 20px;
    /* text-align: center; */
}

img,
video {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-top: 0.4rem;
}

header {
    font-size: 36px;
    color: #373c41;
    font-weight: bold;
    margin-bottom: 10px;
}

aside {
    font-size: 16px;
    color: #8d8f91;
    margin-top: 30px;
}

article p {
    font-size: 0.42rem;
    color: #373c41;
    margin-top: 0.4rem;
    line-height: 0.67rem;
    text-indent: 2em;
}

.hr {
    width: 100%;
    margin: 30px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hr span {
    background-color: #e3e3e5;
}

.hrl,
.hrr {
    width: calc(50% - 5px);
    height: 1px;
}

.hrc {
    width: 3px;
    height: 3px;
    border-radius: 3px;
}

@media screen and (min-width: 1080px) {
    body {
        width: 1080px;
        margin: 0 auto;
    }
}

.divUeditor {
    overflow: hidden;
    display: block;
    width: 100%;
    min-width: 90%;
    position: relative;
    word-wrap: break-word;
    padding: 12px 20px;
    padding-bottom: 20px;
}

.divUeditor img {
    border: 0;
    max-width: 100%;
    margin-top: 10px;
}

.down-wrapper a {
    color: #409eff;
    line-height: 140%;
}

.down-wrapper p {
    margin: 0;
    margin-bottom: 10px;
}


.opt {
    top: 0;
    left: 0;
    right: 0;
    position: absolute;
    /* cursor: pointer;

    i {
        font-size: 14px;
    } */
}
/* 
.opt:hover {
    color: rgb(64, 158, 255);
} */
</style>
