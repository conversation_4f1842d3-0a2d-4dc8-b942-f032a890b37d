<template>
    <div class="accessories">
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <div class="temBody">
                    <el-form
                        :rules="rules"
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="110px">
                        <div>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="故障原因代码" prop="FailureResonCode">
                                        <el-input class="elInput" @input="handleInput" v-model.trim="formData.FailureResonCode" maxlength="10" placeholder="" :disabled="editable"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="故障原因" prop="FailureReson">
                                        <el-input class="elInput" v-model.trim="formData.FailureReson" maxlength="100" placeholder="" :disabled="editable"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </div>
            </template>
            <template slot="footer">
                <div class="fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <el-button @click="handleClose" v-show="editable" size="mini">关闭</el-button>
                <el-button @click="handleClose" v-show="!editable" size="mini">取消</el-button>
                <app-button @click="handleSuccess" v-show="!editable" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog> 
    </div>
</template>
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
export default{
    name:'accessories',
    // mixins: [indexPageMixin],
    components: {
        
    },
    props:{
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: 'create'
        },
        causeData:{
            type: Object,
            default: null
        }
    },
    data(){
        var caseValidate = (rule, value, callback) => {
            if (this.smsMsg) {
                callback(new Error('代码编号已存在!'));
            }else{
                callback();
            }
        };
        return{
            smsMsg:false,
            disabledBtn:false,
            goOn:false,
            formData:{
                "id": "",
                "FailureCaseId": "",
                "FailureResonCode": "",
                "FailureReson": ""
            },
            rules: {
                FailureResonCode:[{ required: true, message: '故障原因代码不能为空'},{validator: caseValidate}],
                FailureReson:[{ required: true, message: '故障原因不能为空'}],
            },
            saveData:[],
            saveSolutionData:[],
            saveCode:''
        }
    },
    filters: {
        
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val){
                this.goOn=false;
                if(this.dialogStatus == 'create'){
                    this.resetData();
                }else{
                    // this.formData.FailureResonCode=this.msgs.FailureResonCode;
                    this.formData=this.causeData;
                }
            }
      
        },
        "formData.FailureResonCode"(val) {
          this.formData.FailureResonCode = this.formData.FailureResonCode.replace(
            /[\W]/g,
            ""
          );
        },
    },
    computed:{
        title(){
            if(this.dialogStatus == 'create'){
                return "添加故障原因"
            }else if(this.dialogStatus == 'edit'){
                return "编辑故障原因"
            }else{
                return "故障原因"
            }
        },
        editable() {
            //详细和审批模式都不可编辑
            return this.dialogStatus == "detail"
        },
    },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        handleInput(){
            if(this.formData.FailureResonCode.length>0){
                this.smsMsg=false;
            }
        },
        promptRepeat(){
            this.smsMsg=true;
            this.$refs['formData'].validateField("FailureResonCode");
        },
        giveData(d,d1,c){
            this.saveData=d;
            this.saveSolutionData=d1;
            this.saveCode=c;
        },
        resetData(){
            this.formData={
                "id": "",
                "FailureCaseId": "",
                "FailureResonCode": "",
                "FailureReson": ""
            }
        },
        handleSuccess(){
            this.disabledBtn=true;
            this.smsMsg=false;
            let listResult = this.$refs.formData.validate();
            
                Promise.all([listResult]).then(valid => {
                    if(this.dialogStatus == "create"){
                        
                        let a=this.saveData.find(v => v.FailureResonCode == this.formData.FailureResonCode);
                        if(!a){
                            a=this.saveSolutionData.find(v => v.FailureSolutionCode == this.formData.FailureResonCode);
                        }
                        if(!a){
                            if(this.formData.FailureResonCode == this.saveCode){
                                a=true;
                            }
                        }
                        if(a){
                            this.smsMsg=true;
                            this.$refs['formData'].validateField("FailureResonCode");
                            // this.$message.error('代码编号已存在!');
                        }else{
                            this.$emit('saveSuccess',JSON.parse(JSON.stringify(this.formData)),this.goOn);
                            if(this.goOn){
                                this.resetData();
                                this.$refs['formData'].resetFields();
                            }
                        }
                            
                        
                    }else{
                        this.$emit('editSuccess',JSON.parse(JSON.stringify(this.formData)));
                    }
                    this.disabledBtn=false;
                }).catch(err => {
                    this.disabledBtn=false;
                })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }

}
</script>
<style lang="scss" scoped>
.temBody{
    padding:10px;
}
</style>