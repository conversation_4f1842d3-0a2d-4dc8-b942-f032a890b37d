<template>
  <div>
    <app-dialog
      title="实施地区管理"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="700"
    >
      <template slot="body">
        <el-form
          :rules="{}"
          ref="formData"
          :model="formData"
          label-position="right"
          :label-width="labelWidth"
        >
          <div class="wrapper" v-loading="loading">
            <div>
              <div class="info">所属地区：{{ engDetail.TopRegionalName }}</div>
              <div class="btn-area">
                <el-button type="primary" @click="handleDialog">添加地区详情</el-button>
                
                <emp-selector
                  key="ccusers"
                  :showType="2"
                  :multiple="true"
                  :list="[]"
                  @change="handleChangeUsers"
                  :beforeConfirm='handleBeforeClick'
                >
                  <el-button slot="reference" type="primary">设置站点负责人</el-button>
                </emp-selector>

                <!-- <el-button type="primary" @click="handleOpt('temp')">导入模板</el-button> -->
                <el-button type="danger" @click="handleOpt('del')">批量删除</el-button>
              </div>
              <div>
                <app-table-core
                  ref="mainTable"
                  :tab-columns="tabColumns"
                  :tab-datas="formData.RegionalList"
                  :tab-auth-columns="[]"
                  :isShowAllColumn="true"
                  @rowSelectionChanged="rowSelectionChanged"
                  :isShowOpatColumn="false"
                  :startOfTable="0"
                  :multable="true"
                >
                  <template slot="EmployeeIdList" slot-scope="scope">
                    <emp-selector
                      :readonly="false"
                      :showType="2"
                      :multiple="true"
                      :list="scope.row.EmployeeIdList"
                      @change="(users) => handleChangeOwnerUsers(scope.row, users)"
                    ></emp-selector>
                  </template>
                  <!-- <template slot="ImplementationTemplateId" slot-scope="scope">
                    <el-select v-model="scope.row.ImplementationTemplateId" placeholder="请选择">
                      <el-option key="tip-select" label="不导入" value></el-option>
                      <el-option
                        v-for="item in templates"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </template> -->
                  <!-- 表格行操作区域 -->
                  <!-- <template slot-scope="scope">
                                    <app-table-row-button :disabled="isHandle" @click="handleRemoveReplace(item, scope.index - 1)" :type="3"></app-table-row-button>
                  </template>-->
                </app-table-core>
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <div>
          <div style="display: inline-block; float: left;">已选中: ({{ multipleSelection.length }})</div>
          <!-- 取消 -->
          <app-button @click="handleClose" :buttonType="2"></app-button>
          <!-- 确认 -->
          <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
        </div>
      </template>
    </app-dialog>

    <!-- 地区选择器 -->
    <!-- <region-selector
      v-if="engDetail && dialogFormVisible"
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      dialogStatus="create"
      :regionalId="engDetail.TopRegionalId"
      :checkedList="checkedList"
      :disabledList="engDetail.ImplementationRegionalList.map(s => s.RegionalId)"
    ></region-selector> -->

    <v-area-choose
      v-if="engDetail && dialogFormVisible"
      @closeDialog="closeDialog"
      @electedRegionalData="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :condition='{RegionalId: engDetail.TopRegionalId}'
      :multiple='true'
      :checkedList="checkedList"
      :disabledFn="disabledFn"
      :defaultExpandLevel='1'
    ></v-area-choose>


    <!-- 导入模板 -->
    <!-- <set-temp
      @closeDialog="closeTempDialog"
      @saveSuccess="handleTempSaveSuccess"
      :dialogFormVisible="dialogTempFormVisible"
      dialogStatus="create"
      :selectedCount="multipleSelection.length"
    ></set-temp> -->
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-dragDialog";
import * as impMgmt from "@/api/implementation/impManagement2";
import empSelector from "../../../common/empSelector";
// import regionSelector from "./regionSelector";
import vAreaChoose from "../../../afterSalesMgmt/businessMap/common/areaChoose";
// import EmpTable from "../../../common/empTable";
// import setTemp from "./setTemp";
import mixins from "../../common/mixins";

export default {
  name: "engineering-workbench-region",
  directives: {
    elDragDialog
  },
  components: {
    empSelector,
    // regionSelector,
    // EmpTable,
    // setTemp,
    vAreaChoose,
  },
  mixins: [mixins],
  props: {
    dialogStatus: {
      type: String
    },
    //工程id
    id: {
      type: String,
      required: true
    },
    //模板id，之前模板和地区关联，现在改为和工程关联
    implementationTemplateId: {
      type: String,
      required: true
    },
    //工程详情
    engDetail: {
      type: Object,
      default: null
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
      }
    }
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    checkedList() {
      return this.formData.RegionalList && this.formData.RegionalList.map(s => s.RegionalId) || [];
    }
  },
  created() {
    this.getTemplates({ IsUsed: true });
  },
  data() {
    return {
      loading: false,
      disabledBtn: false,
      labelWidth: "100px",
      multipleSelection: [],
      formData: {
        Id: "", //
        RegionalList: [
          // {
          //     RegionalId: "",
          //     RegionalName: "xxx/xxx/xxx",
          //     EmployeeIdList: [],
          //     ImplementationTemplateId: ''
          // }
        ]
      },
      tabColumns: [
        {
          attr: {
            prop: "RegionalName",
            label: "地区详情",
            width: "240",
            renderHeader: this.renderHeader
          }
        },
        {
          attr: {
            prop: "EmployeeIdList",
            label: "站点负责人",
            renderHeader: this.renderHeader
          },
          slot: true
        },
        // {
        //   attr: {
        //     prop: "ImplementationTemplateId",
        //     label: "导入模板",
        //     width: "200"
        //   },
        //   slot: true
        // }
      ],
      templates: [], //模板列表
      /**
       * 地区选择器
       */
      dialogFormVisible: false, //地区选择器弹框

      /**
       * 导入模板
       */
      // dialogTempFormVisible: false
    };
  },
  methods: {
    disabledFn(data, nodeType) {
      if(this.engDetail.ImplementationRegionalList.map(s => s.RegionalId).findIndex(r => r == data.Id) > -1) {
        return true
      }
      //只能选择第2、3、4、5级
      if(data.level <= 1) {
        return true
      }
      return false
    },
    resetFormData() {
      let temp = {
        Id: "", //
        RegionalList: []
      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    handleChangeOwnerUsers(row, users) {
      row.EmployeeIdList = users;
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    createData() {
      if (
        !this.formData.RegionalList ||
        this.formData.RegionalList.length == 0
      ) {
        this.$message.error("请选择地区");
        return false;
      }
      if (
        this.formData.RegionalList.some(
          s => !s.EmployeeIdList || s.EmployeeIdList.length == 0
        )
      ) {
        this.$message.error("各地区站点负责人不能为空");
        return false;
      }

      let postData = JSON.parse(JSON.stringify(this.formData.RegionalList));
      postData = postData.map(s => {
        return {
          Id: "",
          RegionalId: s.RegionalId, //地区id
          EmployeeIdList: s.EmployeeIdList.map(p => p.EmployeeId), //员工id
          ImplementId: this.id, //工程id
          ImplementationTemplateId: s.ImplementationTemplateId //模板id
        };
      });
      impMgmt
        .addRegionList(postData)
        .then(res => {
          this.$notify({
            title: "提示",
            message: "保存成功",
            type: "success",
            duration: 2000
          });
          this.disabledBtn = false;
          this.$refs.appDialogRef.createData();
        })
        .catch(err => {
          this.disabledBtn = false;
        });

      // let validate = this.$refs.formData.validate();
      // let descPanelValidate = this.$refs.descPanel.validate();

      // Promise.all([validate, descPanelValidate]).then(valid => {
      //     let postData = JSON.parse(JSON.stringify(this.formData));
      //     //提交数据保存

      //     let descObjData = this.$refs.descPanel.getData();
      //     postData = Object.assign({}, this.formData, descObjData);
      //     if (this.dialogStatus == "create") {
      //         delete postData.Id;
      //     }

      //     this.disabledBtn = true
      //     let result = null;
      //     if (this.dialogStatus == "create") {
      //         delete postData.Id;
      //         result = impMgmt.add(postData);
      //     } else if (this.dialogStatus == "update") {
      //         result = impMgmt.edit(postData);
      //     }

      //     result.then(res => {
      //         this.$notify({
      //             title: "提示",
      //             message: "保存成功",
      //             type: "success",
      //             duration: 2000
      //         });
      //         this.disabledBtn = false
      //         this.$refs.appDialogRef.createData();
      //     }).catch(err => {
      //         this.disabledBtn = false
      //     });
      // });
    },

    handleOpt(optType) {
      if (!this.multipleSelection || this.multipleSelection.length == 0) {
        this.$message.error("请选择需要批量操作的行");
        return false;
      }
      if (optType == "temp") {
        // this.handleTempDialog();
      } else if (optType == "del") {
        this.$confirm(`是否确认删除选中项？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.handleRemove();
        });
      }
    },
    handleRemove() {
      this.multipleSelection.forEach(s => {
        let idx = this.formData.RegionalList.findIndex(
          i => i.RegionalId == s.RegionalId
        );
        this.formData.RegionalList.splice(idx, 1);
      });
    },
    /**
     * 地区选择器弹框
     */
    handleDialog(row, optType = "update") {
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(rows) {
      if (rows && rows.length > 0) {
        let list = rows.map(s => {
          return {
            RegionalId: s.Id,
            RegionalName: s.ParentName,
            EmployeeIdList: [],
            //之前模板和地区关联，所以添加地区后，需要导入模板
            //现在改为和工程关联，为了不改变太多逻辑，所以直接设置工程、地区为相同模板
            ImplementationTemplateId: this.implementationTemplateId 
          };
        });
        this.formData.RegionalList = list;
      }else{
        this.formData.RegionalList = [];
      }
      this.closeDialog();
    },

    /**
     * 批量设置站点负责人
     */
    handleBeforeClick() {
      if (!this.multipleSelection || this.multipleSelection.length == 0) {
        this.$message.error("请选择需要批量操作的行");
        return false;
      }
      return true
    },
    handleChangeUsers(users) {
      this.multipleSelection.forEach(s => {
        s.EmployeeIdList = users || [];
      });
    },
    /**
     * 导入模板
     */
    // handleTempDialog(row) {
    //   this.dialogTempFormVisible = true;
    // },
    // closeTempDialog() {
    //   this.dialogTempFormVisible = false;
    // },
    // handleTempSaveSuccess(tempId) {
    //   this.multipleSelection.forEach(s => {
    //     s.ImplementationTemplateId = tempId;
    //   });
    //   this.closeTempDialog();
    // },
    
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    /**
     * 自定义表头
     */
    renderHeader(h, { column }) {
      return h("span", [
        h("span", column.label),
        h(
          "span",
          {
            style: "color: red"
          },
          " *"
        )
      ]);
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  .info {
    border-bottom: 1px solid #dcdfe6;
    padding-bottom: 5px;
  }
  .btn-area {
    padding: 5px 0;
    border-bottom: 1px solid #dcdfe6;
    button {
      margin-right: 4px;
    }
  }
}
</style>
