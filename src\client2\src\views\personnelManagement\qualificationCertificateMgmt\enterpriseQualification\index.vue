<template>
  <!-- 企业资质 -->
  <div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
      <div class="pageWrapper __dynamicTabWrapper">
        <app-table
          ref="mainTable"
          :layoutMode="layoutMode"
          :multable="true"
          :isShowOpatColumn="true"
          :isShowBtnsArea="false"
          :isShowAllColumn="true"
          :optColWidth="120"
          :loading="listLoading"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :tab-auth-columns="tabAuthColumns"
          :startOfTable="startOfTable"
          @rowSelectionChanged="rowSelectionChanged"
          @sortChagned="handleSortChange"
        >
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="onResetSearch"
              :layoutMode="layoutMode"
            >
              <template slot="Keywords">
                <el-input
                  style="width: 100%"
                  placeholder="搜索资质名称/证书编号"
                  @clear="getList"
                  v-antiShake="{
                    time: 300,
                    callback: () => {
                      getList();
                    },
                  }"
                  clearable
                  v-model.trim="listQuery.Keywords"
                ></el-input>
              </template>
              <template slot="Status">
                <el-select
                  style="width: 100%"
                  clearable
                  v-model="listQuery.Status"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in statusTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
              <template slot="IssueDate">
                <el-date-picker
                  v-model="listQuery.IssueDate"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder
                  end-placeholder
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                ></el-date-picker>
              </template>
              <template slot="EffectiveDate">
                <el-date-picker
                  v-model="listQuery.EffectiveDate"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder
                  end-placeholder
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                ></el-date-picker>
              </template>
              <!-- 表格批量操作区域 -->
              <template slot="btnsArea">
                <permission-btn v-on:btn-event="onBtnClicked">
                  <el-dropdown
                    slot="customDomId"
                    trigger="click"
                    slot-scope="scope"
                    style="margin-left: 4px"
                    @command="onBtnClicked"
                  >
                    <el-button type="primary">
                      {{ scope.data.Name }}
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="batchTermOfValidity">修改有效期</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </permission-btn>
              </template>
            </app-table-form>
          </template>

          <template slot="EffectiveDate" slot-scope="scope">
            <span
              :style="{
                color:
                  scope.row.Status == 2 || scope.row.Status == 3
                    ? getStatusObj(scope.row.Status).color
                    : '',
              }"
            >
              {{ scope.row.EffectiveDate | dateFilter("YYYY-MM-DD") }}
            </span>
          </template>
          <template slot="AnnualDate" slot-scope="scope">
            <span
              :style="{
                color:
                  scope.row.EffectiveState == 2 || scope.row.EffectiveState == 3
                    ? getStatusObj(scope.row.EffectiveState).color
                    : '',
              }"
            >
              {{ scope.row.AnnualDate | dateFilter("YYYY-MM-DD") }}
            </span>
          </template>
          <template slot="IssueDate" slot-scope="scope">
            {{ scope.row.IssueDate | dateFilter("YYYY-MM-DD") }}
          </template>
          <template slot="Status" slot-scope="scope">
            <span
              class="item-status"
              v-if="!!scope.row.Status"
              :style="{
                backgroundColor: getStatusObj(scope.row.Status).bgColor,
                color: getStatusObj(scope.row.Status).color,
              }"
            >
              {{ getStatusObj(scope.row.Status).label }}
            </span>
            <template v-else>无</template>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <!-- 详情 -->
            <app-table-row-button
              @click="handleUpdate(scope.row, 'detail')"
              text="详情"
              :type="2"
            ></app-table-row-button>
            <!-- 编辑 -->
            <app-table-row-button
              v-if="rowBtnIsExists('btnEdit')"
              @click="handleUpdate(scope.row, 'update')"
              :type="1"
            ></app-table-row-button>
            <!-- 删除 -->
            <app-table-row-button
              v-if="rowBtnIsExists('btnDel')"
              @click="handleDelete(scope.row)"
              :type="3"
            ></app-table-row-button>
          </template>
        </app-table>
      </div>
      <pagination
        :total="total"
        :page.sync="listQuery.PageIndex"
        :size.sync="listQuery.PageSize"
        @pagination="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 创建/修改 列表 -->
    <create-page
      v-if="createDialogFormVisible"
      :id="selectId"
      :dialogStatus="createDialogStatus"
      :dialogFormVisible="createDialogFormVisible"
      @closeDialog="createDialogFormVisible = false"
      @saveSuccess="createSaveSuccess"
    ></create-page>
    <!-- 批量修改有效期 -->
    <term-of-validity
      v-if="batchTermOfValidityVisible"
      :component-type="1"
      :ids="multipleSelectionIds"
      :dialogFormVisible="batchTermOfValidityVisible"
      @closeDialog="batchTermOfValidityVisible = false"
      @saveSuccess="termOfValiditySaveSuccess"
    ></term-of-validity>
    <!-- 修改有效时段（提醒设置） -->
    <effective-period
      v-if="batchEffectivePeriodVisible"
      :component-type="1"
      :dialogFormVisible="batchEffectivePeriodVisible"
      @closeDialog="batchEffectivePeriodVisible = false"
    ></effective-period>
  </div>
</template>
<script>
import * as enterpriseQualificationApi from "@/api/personnelManagement/enterpriseQualification";
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import termOfValidity from "../common/termOfValidity";
import effectivePeriod from "../common/effectivePeriod";

import { vars } from "../common/vars";
export default {
  name: "enterprise-qualification",
  mixins: [indexPageMixin],
  components: {
    createPage,
    termOfValidity,
    effectivePeriod,
  },
  filters: {
    patentWorkTypeFilter(val) {
      let obj = vars.patentWorkTypes.find(s => s.value == val);
      if (obj) {
        return obj.label;
      }
      return "";
    },
  },
  created() {
    this.getList();
  },
  data() {
    return {
      patentWorkTypes: vars.patentWorkTypes, // 著作类型 s
      statusTypes: vars.statusTypes, // 列表中状态  s

      selectId: "",
      createDialogStatus: "create",
      createDialogFormVisible: false,

      /**修改提醒设置 */
      batchEffectivePeriodVisible: false,

      /** 批量修改 有效时间 */
      batchTermOfValidityVisible: false,
      statusList: [],
      total: 0,
      listQuery: {},
      layoutMode: "simple",
      listLoading: false,
      tableSearchItems: [
        { prop: "Keywords", label: "", mainCondition: true },
        { prop: "Status", label: "状态" },
        { prop: "IssueDate", label: "发证时间" },
        { prop: "EffectiveDate", label: "有效期至" },
      ],
      multipleSelection: [],
      tabDatas: [],
      tabColumns: [
        { attr: { prop: "QualificationName", label: "资质名称", showOverflowTooltip: true } },
        { attr: { prop: "CertificateNumber", label: "证书编号", showOverflowTooltip: true } },
        { attr: { prop: "Status", label: "状态", sortable: "custom", width: "100" }, slot: true },
        {
          attr: { prop: "IssueDate", label: "发证时间", sortable: "custom", width: "110" },
          slot: true,
        },
        {
          attr: { prop: "EffectiveDate", label: "有效期至", sortable: "custom", width: "110" },
          slot: true,
        },
        {
          attr: { prop: "AnnualDate", label: "年审时间", sortable: "custom", width: "110" },
          slot: true,
        },
        { attr: { prop: "Remark", label: "备注", showOverflowTooltip: true } },
      ],
    };
  },
  methods: {
    getList() {
      let self = this;
      self.listLoading = true;
      let postDatas = JSON.parse(JSON.stringify(self.listQuery));
      postDatas = self.assignSortObj(postDatas);
      if (postDatas.IssueDate && postDatas.IssueDate.length == 2) {
        postDatas.issueDateStartTime =
          postDatas.IssueDate[0] == postDatas.IssueDate[1]
            ? postDatas.IssueDate[0] + " 00:00:00"
            : postDatas.IssueDate[0];
        postDatas.issueDateEndTime =
          postDatas.IssueDate[0] == postDatas.IssueDate[1]
            ? postDatas.IssueDate[1] + " 23:59:59"
            : postDatas.IssueDate[1];
      }
      if (postDatas.EffectiveDate && postDatas.EffectiveDate.length == 2) {
        postDatas.EffectiveDateStartTime =
          postDatas.EffectiveDate[0] == postDatas.EffectiveDate[1]
            ? postDatas.EffectiveDate[0] + " 00:00:00"
            : postDatas.EffectiveDate[0];
        postDatas.EffectiveDateEndTime =
          postDatas.EffectiveDate[0] == postDatas.EffectiveDate[1]
            ? postDatas.EffectiveDate[1] + " 23:59:59"
            : postDatas.EffectiveDate[1];
      }
      delete postDatas.IssueDate;
      delete postDatas.EffectiveDate;
      if (!postDatas.Status) postDatas.Status = 0;
      enterpriseQualificationApi
        .getList(postDatas)
        .then(res => {
          self.listLoading = false;
          self.tabDatas = res.Items;
          self.total = res.Total;
        })
        .catch(err => {
          self.listLoading = false;
        });
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {
        prop,
        order,
      };
      this.getList();
    },
    onResetSearch() {
      // this.listQuery.Keywords = "";
      this.listQuery = this.$options.data().listQuery;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    // 表格顶部按钮点击事件
    onBtnClicked(domId) {
      switch (domId) {
        case "btnAdd":
          this.handleUpdate(null, "create");
          break;
        case "batchTermOfValidity":
          this.handleTermOfValidity();
          break;
        case "btnEffectivePeriod":
          this.handleEffectivePeriod();
          break;
        default:
          break;
      }
    },
    /** 弹出 编辑有效时段 */
    handleEffectivePeriod() {
      this.multipleSelectionIds = this.multipleSelection.map(s => {
        return s.Id;
      });
      this.batchEffectivePeriodVisible = true;
    },
    /** 弹出 批量编辑编辑有效期 */
    handleTermOfValidity() {
      if (this.multipleSelection.length > 0) {
        this.multipleSelectionIds = this.multipleSelection.map(s => {
          return s.Id;
        });
        this.batchTermOfValidityVisible = true;
      } else {
        this.$message({
          message: "至少选择一条",
          type: "error",
        });
      }
    },
    /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
    handleUpdate(row, optType = "update") {
      this.selectId = optType === "create" ? "" : row.Id;
      this.createDialogStatus = optType;
      this.createDialogFormVisible = true;
    },
    /** 编辑框 点确定后 关闭 并刷新列表 */
    createSaveSuccess(d) {
      console.log("d", d);
      if (!d) {
        this.createDialogFormVisible = false;
      }
      // this.listQuery.PageIndex = 1;
      this.getList();
    },
    termOfValiditySaveSuccess() {
      this.batchTermOfValidityVisible = false;
      this.getList();
    },
    /** 删除 */
    handleDelete(rows) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        enterpriseQualificationApi.del([rows.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      });
    },
    /**分页页大小变更 */
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    /**分页页码变更 */
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    getStatusObj(val) {
      return vars.statusTypes.find(s => s.value == val) || {};
    },
  },
};
</script>
