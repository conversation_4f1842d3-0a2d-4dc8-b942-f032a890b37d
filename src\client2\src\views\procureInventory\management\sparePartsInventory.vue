<template>
  <div class="app-dialog-wrapper">
    <app-dialog
      :title="title"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1600"
      className="clear-default-height-auto dialog-auto-height"
    >
      <template slot="body">
        <!-- <div>
          
        </div> -->
        <app-table
              ref="mainTable"
              :isShowBtnsArea='false' :isShowConditionArea="false" :serial="false" :tab-columns="tabColumns" :tab-datas="formData" :isShowAllColumn="true" :loading="listLoading"  :isShowOpatColumn="false"  :multable="false" :layoutMode='layoutMode'
            >
                <template slot="Opt" slot-scope="scope">
                  <i class="el-icon-remove opt-icon" style="color: #F56C6C;" @click="handleTableDelete(scope.index - 1)"></i>
                  <!-- <app-table-row-button v-if="editable" @click="handleTableDelete(scope.index -1)" :type="3"></app-table-row-button> -->
                </template>
              
                <template slot="Idx" slot-scope="scope">
                  {{ scope.index }}
                </template>
                <template slot="MaterialCode" slot-scope="scope">
                  <div style="position: relative;">
                    <el-autocomplete
                        :disabled="!editable"
                        class="inline-input"
                        v-model.trim="scope.row.MaterialCode"
                        :fetch-suggestions="querySearch"
                        placeholder="请输入物料编码"
                        :trigger-on-focus="false"
                        @select="(item) => handleSelectMaterial(item, scope.index - 1)"
                        @change="(val) => changeMaterialCode(val, scope.index - 1)"
                        @input='handleMaterialCodeInput'
                        popper-class="my-fixed-width-popper"
                    >
                      <template slot-scope="{item}">
                        <materialOption :opt="item"></materialOption>
                      </template>
                    </el-autocomplete>
                  </div>
                </template>

                <template slot="MaterialCount" slot-scope="scope">
                  <el-input-number style="width: 100px;" v-model="scope.row.MaterialCount" :min="0"></el-input-number>
                </template>

                <template slot="MaterialInStockName" slot-scope="scope">
                  <el-autocomplete
                    :disabled="!editable"
                    class="inline-input"
                    v-model.trim="scope.row.MaterialInStockName"
                    :fetch-suggestions="querySearchIn"
                    :trigger-on-focus="false"
                    @select="(item) => handleSelectIn(item, scope.index - 1)"
                    @change="(val) => changeTransferInID(val, scope.index - 1)"
                    @input='handleMaterialOutStock'
                  >
                    <template slot-scope="{item}">
                      <div :title="item.value" class="omit">{{ item.value }}</div>
                    </template>
                  </el-autocomplete>
                </template>

                <template slot="RegionalName" slot-scope="scope">
                  <div class="_regional_detail_wrapper">
                    <div class="btn_wrapper">
                        <el-button :disabled="!editable" type="text" @click="handleRegionDialog(scope.index - 1)">选择</el-button>
                    </div>
                    <div class="regional_text" :title="scope.row.RegionalName">{{ scope.row.RegionalName }}</div>
                    <div class="close_wrapper" v-show="scope.row.RegionalName && editable">
                        <div class="i_wrapper">
                            <el-button icon="el-icon-close" class="btn" circle @click="handleClear(scope.index - 1)"></el-button>
                        </div>
                    </div>
                </div>
                </template>



                <template slot="MaterialEmployeeList" slot-scope="scope">
                  <!-- {{ scope.row.MaterialEmployeeList | nameFilter }} -->
                  <normar-emp-selector 
                      :readonly='(scope.row.RegionalId ? false : true) || !editable '
                      listSelectorTitle='选择人员' 
                      :listSelectorUrl='serviceArea.business + "/ImplementerManagement/GetListPage"' 
                      :multiple='false' :showType='2'
                      :list='scope.row.MaterialEmployeeList' 
                      key='service-users' 
                      :columns='empColumns'
                      @change='(users) => handleChangeUsers(users, scope.index - 1)'
                      :condition='{regionalId: scope.row.RegionalId,ReverseCheckLevel:true}'
                      :pageSize='100'
                      :isAutocomplete='true'
                  ></normar-emp-selector>
                </template>

                 <template slot="Remark" slot-scope="scope">
                   <!-- <el-input :style="scope.row.isRemarkFocus ? 'position: absolute; height: 100px; top: 0; right: 0px; width: 300px; z-idex: 999;' : ''" :rows="scope.row.isRemarkFocus ? 5 : 1" :disabled="!editable" type="textarea" maxlength="500" @blur="handleBlur(scope.index - 1)" @focus="handleFocus(scope.index - 1)" v-model="scope.row.Remark"></el-input> -->

                   <!-- <el-input v-if="scope.index - 1 == 0" :style="scope.row.isRemarkFocus ? 'position: absolute; height: 100px; top: 0; right: 0px; width: 300px; z-index: 999;' : 'position: absolute; height: 100px; top: 0; right: 0px; width: 300px; z-idex: 999;'" :rows="scope.row.isRemarkFocus ? 5 : 5" :disabled="!editable" type="textarea" maxlength="500" @blur="handleBlur(scope.index - 1)" @focus="handleFocus(scope.index - 1)" v-model="scope.row.Remark"></el-input> -->

                   <!-- 未获取焦点，弄你 input 输入框样式 -->
                   <div style="padding: 6px 0; height: 40px;">
                    <div style="width: 100%; border: 1px solid #DCDFE6; background: #fff; cursor: text; height: 28px; line-height: 28px; overflow: hidden;" class="el-input__inner" @click="handleHelpFocus(scope.index - 1)">{{ scope.row.Remark }}</div>
                   </div>
                   <!-- 获取焦点 -->
                   <el-input v-show="scope.row.isRemarkFocus" :style="scope.row.isRemarkFocus ? 'position: absolute; height: 100px; top: 10px; right: 10px; width: 300px; z-index: 999;' : ''" :rows="scope.row.isRemarkFocus ? 5 : 1" :ref="`remark_input_${scope.index - 1}`" :disabled="!editable" type="textarea" maxlength="500" @blur="handleBlur(scope.index - 1)" v-model="scope.row.Remark"></el-input>
                 </template>
                 


             <!-- 表格行操作区域 -->
             <!-- <template slot-scope="scope">
                  <app-table-row-button @click="handleTableDelete(scope.index -1)" :type="3"></app-table-row-button>
            </template> -->

             <!-- <template slot="btnsArea">
                  <el-button type="primary"  @click="addItem()">添加物料</el-button>
             </template> -->
             
         </app-table>
           
         <!-- <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" /> -->

         <!-- <add-item @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogItemStatus" :id="id"></add-item> -->
      </template>

      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button
          @click="createData"
          :buttonType="1"
          :disabled="disabledBtn"
        ></app-button>
      </template>
    </app-dialog>


    <!-- 选择地区 -->
    <v-area-choose
    @closeDialog="closeRegionDialog"
    @electedRegionalData="electedRegionalData"
    :dialogFormVisible="dialogRegionFormVisible"
    :checkedList="formData[currentRowIdx] ? [formData[currentRowIdx].RegionalId] : []"
    :disabledFn="disabledFn"
    :defaultExpandLevel='1'
    ></v-area-choose>

    <batchCoypDialog @closeDialog="closeBatchCoypDialog" :copyType="copyType" @saveSuccess="handleBatchCoypSaveSuccess" v-if="dialogFormBatchCoypVisible" :dialogFormVisible="dialogFormBatchCoypVisible"></batchCoypDialog>
  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
// import addItem from "./addItem";
import { serviceArea } from "@/api/serviceArea"
import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";
import * as odc from "@/api/operatingDataCenter";
import materialOption from './materialOption'
import batchCoypDialog from './batchCoypDialog'
import normarEmpSelector from '../../afterSalesMgmt/common/normarEmpSelector'
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import * as regionalManagement from "@/api/systemManagement/regionalManagement";

export default {
  name: "sparePartsInventory-create",
  mixins: [indexPageMixin],
  components: {
    // addItem,  
    materialOption,
    batchCoypDialog,
    vAreaChoose,
    normarEmpSelector,


 },
  computed: {
    title() {
      if (this.dialogStatus == "create") {
        return "新增配件库存";
      }
    },
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail";
    },
  },
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
    },
    id: {
      type: String,
      default: "",
    },
  },
  watch: {
    // "$attrs.dialogFormVisible": {
    //   handler(val) {
    //     if (val) {
    //       this.resetFormData();
    //     }
    //   },
    // },
  },
  filters: {
      nameFilter(employeeList) {
        // console.log(employeeList)
         return employeeList.map(v=> v.Name).toString()
      },
  },
  created() {
    this.getPoorListByCondition()
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      serviceArea,
      dialogFormVisible:false,
      layoutMode: "simple",
      dialogItemStatus: "sparePartsInventory-create",
      listLoading: false,
      tabColumns: [
        {
          attr: {
            prop: "Opt",
            label: "操作",
            width: 50,
            renderHeader: this.renderHeader
          },
          slot: true
        },
        {
          attr: {
            prop: "Idx",
            label: "序号",
            width: 50
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialCode",
            label: "物料编码",
            renderHeader: this.renderHeaderMaterialCodeCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialName",
            label: "物料名称",
          },
        },
        {
          attr: {
            prop: "Specifications",
            label: "规格型号",
          },
        },
        {
          attr: {
            prop: "MaterialCount",
            label: "申请数量",
            renderHeader: this.renderHeaderMaterialCountCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialUnit",
            label: "单位",
            width: 80,
            renderHeader: this.renderHeaderRequiredCol
          },
        },
        {
          attr: {
            prop: "MaterialInStockName",
            label: "仓库",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true
        },
        {
          attr: {
            prop: "RegionalName",
            label: "使用地区",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialEmployeeList",
            label: "领料人",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true,
        },
        {
          attr: {
            prop: "Remark",
            label: "备注",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true,
        },
      ],
      empColumns: [
            {
            attr: { prop: "Name", label: "姓名", width: '100' },
            },
            {
            attr: { prop: "Number", label: "工号", width: '100' },
            },
            {
            attr: { prop: "Gender", label: "性别", width: '60' },
            },
            {
            attr: { prop: "RegionalName", label: "负责地区" },
            },
            {
            attr: { prop: "Phone", label: "手机", width: '120' },
            },
            {
            attr: { prop: "OrgName", label: "部门" },
            }
        ],
      
      total: 0,
      disabledBtn: false,
      rules: {
      },
      loading: false,
      formData: [],

      dialogRegionFormVisible: false,
      currentRowIdx: -1,
      dialogFormBatchCoypVisible: false,
      
      regs: [],

      isSelectedMaterialCode: false,

      isSelectedMaterialOutStock: false,


    };
  },
  methods: {
    disabledFn(data, nodeType) {
      //禁选一级节点
      if(data.level <= 1) {
          return true
      }
      return false
    },
    renderHeader(h, { column }) {
        return (
            <span>
                <i style='font-size: 18px; color: #409EFF; cursor: pointer;' class='el-icon-circle-plus' on-click={() => this.addItem()}></i>
            </span>
        )
    },
    renderHeaderMaterialCodeCol(h, { column }) {
      return (
        <div>
          <div>{ column.label } <span class='red'> *</span></div>
          <div>
            <el-button type="text" style='padding: 0;' on-click={() => this.openBatchCoypDialog(column.property)}>粘贴填入</el-button>
          </div>
        </div>
      )
    },
    renderHeaderMaterialCountCol(h, { column }) {
        return (
          <div>
            <div>
              { column.label }
               <span class='red'> *</span>
            </div>
            <div>
              <el-button type="text" style='padding: 0;' disabled={this.formData.length == 0 ? true : false} on-click={() => this.openBatchCoypDialog(column.property)}>粘贴填入</el-button>
            </div>
          </div>
        )
    },
    renderHeaderCommonCol(h, { column }) {
        return (
          <div>
            <div>
              { column.label } 
              {
                column.property != 'Remark' ? <span class='red'> *</span> : ''
              }
            </div>
            <div>
              <el-button type="text" style='padding: 0; color: #67c23a;' disabled={this.formData.length == 0 ? true : false} on-click={() => this.batchCoverage(column)}>批量覆盖</el-button>
            </div>
          </div>
        )
    },
    openBatchCoypDialog(type) {
      this.copyType = type
      this.dialogFormBatchCoypVisible = true;
    },
    closeBatchCoypDialog() {
      this.dialogFormBatchCoypVisible = false;
    },
    handleBatchCoypSaveSuccess(obj) {
      let _copyType = obj.copyType
      let _list = obj.list || []

      if(_list.length == 0) {
        return false
      }

      let diffLen = _list.length - this.formData.length
      if(diffLen > 0) {
        for(let i = 0; i < diffLen; i++) {
          this.addItem()
        }
      }


      if(_copyType == 'MaterialCode') {
        this.getMaterialListDetails(_list)
      }else if(_copyType == 'MaterialCount') {
        this.formData.forEach((row, idx) => {
          row.MaterialCount = _list[idx] || 0
        })
      }

      this.closeBatchCoypDialog();
    },

    //批量复制填入“物料编号”——单条数据返回逻辑 = 选择下拉 + 选择下拉后调用接口的所有（赋值）逻辑
    getMaterialListDetails(list) {

      let postDatas = {
        KeywordList: list,
        ERPAccountId: this.formData.ERPAccountId,
      }
      this.listLoading = true
      odc.getMaterialListDetails(postDatas).then(res => {
        this.listLoading = false
        this.formData.forEach((row, idx) => {
          let target = res[idx]
          if(target) {
            row.MaterialCode = target.MaterialCode //物料编码
            row.MaterialName = target.MaterialName //物料名称
            row.Specifications = target.Specifications //规格型号
            row.ErpMaterialId = target.ErpMaterialId

            this.handleSelectUnit(target, idx)

          }
        });
      }).catch(err => {
        this.listLoading = false
      })
    },

    handleTableDelete(index){
      this.$confirm(`是否确认删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData.splice(index,1)
      })
    },
    handleSelectMaterial(item, idx){
      
      this.isSelectedMaterialCode = true

      if(this.formData && this.formData[idx]) {
        let obj = this.formData[idx]
        obj.MaterialCode = item.FNumber //物料编码
        obj.MaterialName = item.FName //物料名称
        obj.Specifications = item.FSpecification //规格型号
        obj.ErpMaterialId = item.FMATERIALID


        this.getMaterialDetails(obj.MaterialCode, idx)
      }
        // this.formData.MaterialCode = item.FNumber
        // this.formData.MaterialName = item.FName
        // this.formData.Specifications = item.FSpecification
        // this.formData.ErpMaterialId = item.FMATERIALID

        // this.getMaterialDetails(this.formData.MaterialCode)
    },
    getMaterialDetails(val, idx) {
        odc.getMaterialDetails({keyWords:val, ERPAccountId:this.formData.ERPAccountId,}).then(res => {
            if(res) {
                this.handleSelectUnit(res, idx)
            }
        })
    },
    handleSelectUnit(item, idx){
      
      if(this.formData && this.formData[idx]) {
        let obj = this.formData[idx]
        obj.MaterialUnitId = ''
        obj.MaterialUnitFNumber = ''
        
        obj.MaterialUnitId = item.FUNITID
        obj.MaterialUnitFNumber = item.FNumber
        obj.MaterialUnit = item.FNumber

      }

    },
    changeMaterialCode(val, idx){
      // if(!val){
      // }
      if(!this.isSelectedMaterialCode) {
        if(this.formData && this.formData[idx]) {
          let obj = this.formData[idx]
          obj.MaterialCode = ''
          obj.MaterialName = ''
          obj.Specifications = ''
          obj.ErpMaterialId = ''
          obj.MaterialUnitId = ''
          obj.MaterialUnit = ''
          obj.MaterialUnitFNumber = ''
        }
      }
      this.isSelectedMaterialCode = false
    },
    handleMaterialCodeInput() {
      this.isSelectedMaterialCode = false

    },
    handleBlur(idx) {
      let row = this.formData[idx]
      if(row) {
        this.$set(row, 'isRemarkFocus', false)
      }
    },
    querySearch(queryString, cb) {
      let keywords = queryString.trim()
      odc.getMaterial({keyWords:keywords,ERPAccountId:'',}).then(res => {
        let result = res.map(v=>{
          v.value = v.FNumber
          return v
        });
        cb(result);
      }).catch(err => {
            
      });
    },
    batchCoverage(prop) {
      let propName = prop.property
      this.formData.forEach((row, idx) => {
        let temp = this.formData[0]

        if(idx > 0) {
          if(propName == 'MaterialInStockName') {
            row.MaterialInStockName = temp.MaterialInStockName
            row.MaterialStockName = temp.MaterialInStockName
            row.MaterialStock = temp.MaterialStock
            // row.MaterialStockName = temp.MaterialStockName
            // row.MaterialOutstockName = temp.MaterialStockName
            // row.Materialoutstock = temp.MaterialStock
            row.MaterialInStock = temp.MaterialInStock
            row.MaterialInStockFNumber = temp.MaterialInStockFNumber
            row.MaterialStockFNumber = temp.MaterialStockFNumber
          }else if(propName == 'RegionalName') {
            row.RegionalId = temp.RegionalId
            row.RegionalName = temp.RegionalName
          }else if(propName == 'MaterialEmployeeList') {
            let isSame = this.isSameRootNode(temp.RegionalId, row.RegionalId)
            if(isSame) {
              row.MaterialEmployeeIdList = temp.MaterialEmployeeIdList
              row.MaterialEmployeeList = temp.MaterialEmployeeList
            }
          }else if(propName == 'Remark') {
            row.Remark = temp.Remark
          }
        }
      })
    },
    //判断 tId 是不是和 pId 属于同一个根节点
    isSameRootNode(pId, tId) {
      //找到第一个节点的根节点
      let pObj = this.regs.find(s => s.Id == pId)
      while(pObj && pObj.ParentId) {
        pObj = this.regs.find(s => s.Id == pObj.ParentId)
      }
      
      //找到第二个节点的根节点
      let pObj2 = this.regs.find(s => s.Id == tId)
      while(pObj2 && pObj2.ParentId) {
        pObj2 = this.regs.find(s => s.Id == pObj2.ParentId)
      } 

      return pObj.Id && pObj.Id == pObj2.Id

    },
    handleHelpFocus(idx) {
      let row = this.formData[idx]
      this.$set(row, 'isRemarkFocus', true)

      this.$nextTick(() => {
        if(row) {
          this.$refs[`remark_input_${idx}`].focus()
        }
      })
      // setTimeout(() => {
      // }, 10)
    },
    getPoorListByCondition() {
      let postDatas = {"RegionalName":"","RegionalId":""}
      regionalManagement.getPoorListByCondition(postDatas).then(res => {
        this.regs = res
      })
    },
    querySearchIn(queryString, cb){
      let keywords = queryString.trim()
      odc.getStock({keyWords:keywords,ERPAccountId:'',}).then(res => {
              let result = res.map(v=>{
                v.value = v.FName
                return v
            });
            cb(result);
      }).catch(err => {
            
      });
    },
    handleSelectIn(item, idx){
      this.isSelectedMaterialOutStock = true
      if(this.formData && this.formData[idx]) {
        let obj = this.formData[idx]
        obj.MaterialStockName = item.FName
        obj.MaterialStock = item.FStockId

        // obj.MaterialOutstockName = item.FName
        // obj.Materialoutstock = item.FStockId
        obj.MaterialInStock = item.FStockId
        obj.MaterialInStockFNumber = item.FNumber
        obj.MaterialStockFNumber = item.FNumber
      }
    },
    changeTransferInID(val, idx){
      if(!this.isSelectedMaterialOutStock) {
        if(this.formData && this.formData[idx]) {
          let obj = this.formData[idx]
          obj.MaterialStockName = ''
          obj.MaterialStock = ''
          obj.MaterialInStockName = ''
  
          // obj.MaterialOutstockName = ''
          // obj.Materialoutstock = ''
          obj.MaterialInStock = ''
          obj.MaterialInStockFNumber = ''
          obj.MaterialStockFNumber = ''
        }
      }

      this.isSelectedMaterialOutStock = false
    },
    
    handleMaterialOutStock() {
      this.isSelectedMaterialOutStock = false
    },
    handleChangeUsers(users, idx) {

      // this.$refs.formData.clearValidate('MaterialEmployeeList');

      if(this.formData && this.formData[idx]) {
          let obj = this.formData[idx]
          obj.MaterialEmployeeList = users;
          obj.MaterialEmployeeIdList = users.map(s => s.EmployeeId) || []
      }

    },
    //地区选择
    closeRegionDialog() {
      this.dialogRegionFormVisible = false;
    },
    
    handleRegionDialog(rowIdx){
      this.currentRowIdx = rowIdx
      this.dialogRegionFormVisible=true;
    },
    electedRegionalData(data){
        // this.$refs.formData.clearValidate('RegionalId');
        if(this.formData && this.formData[this.currentRowIdx]) {
            let obj = this.formData[this.currentRowIdx]
            if(data){
                obj.RegionalId=data.Id;
                obj.RegionalName=data.ParentName;
            }else{
                obj.RegionalId='';
                obj.RegionalName='';
            }
            obj.MaterialEmployeeList=[];
            obj.MaterialEmployeeIdList=[];
        }
    },
    handleClear(idx) {
      if(this.formData && this.formData[idx]) {
        let obj = this.formData[idx]
        obj.RegionalId='';
        obj.RegionalName='';
        obj.MaterialEmployeeList=[];
        obj.MaterialEmployeeIdList=[];
      }
    },
    addItem(){
        // this.dialogFormVisible = true;
        //// xxxxxxxxxxxxxxxxx
        this.formData.push({
          MaterialEmployeeIdList: [], 
          MaterialEmployeeList: [], 
          // AgentIds:[],
          // AgentIdsNames:null,
          RegionalId:null,
          RegionalName:"",
          Remark:"",
          MaterialCount:1,
          
          MaterialInStockName:"",
          MaterialInStock:"",
          // MaterialStock:"",
          // MaterialStockName:"",
          MaterialUnitId:"",
          MaterialUnit:"",
          MaterialCode:"",
          ErpMaterialId:"",
          MaterialName: "",
          MaterialInStockFNumber:"",
          MaterialUnitFNumber:"",
          MaterialStockFNumber:"",
        })
    },

    //  closeDialog() {
    //     this.dialogFormVisible = false;
    //  },
    //  handleSaveSuccess(_formData) {
    //     console.log(_formData)
    //     this.formData.push(_formData)
    //     this.closeDialog();
    //  },

    // resetFormData() {
    //   this.formData = [];
    // },

    handleCurrentChange(){

    },
    handleSizeChange(){

    },
    createData() {

        if(this.formData.length === 0){
              this.$message({
                  message: '配件库存记录不能为空',
                  type: 'error'
                });
        }else{
           let postData = JSON.parse(JSON.stringify(this.formData));



            let requiredColumns = ['MaterialCode', 'MaterialCount', 'MaterialUnit', 'MaterialInStockName', 'RegionalName', 'MaterialEmployeeList',]
            //验证通过
            let flag = true
            for(let i = 0; i < requiredColumns.length; i++) {
              let prop = requiredColumns[i]
              let idx = postData.findIndex(s => !s[prop] || (prop == 'MaterialEmployeeList' && s[prop].length == 0))
              if(idx > -1) {
                flag = false //验证不通过
                let obj = this.tabColumns.find(s => s.attr.prop == prop)
                if(obj) {
                  this.$message({
                    message: `${obj.attr.label}不能为空`,
                    type: 'error'
                  });
                }
                break;
              }
            }
            
            if(!flag){
              return false
            }

        
            delete postData.MaterialEmployeeList

            this.disabledBtn = true;
            if (this.dialogStatus == "create") {
              materialTransferApi.addPersonalList(postData)
                .then((res) => {
                  this.disabledBtn = false;
                  this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000,
                  });
                  this.$refs.appDialogRef.createData();
                })
                .catch((err) => {
                  this.disabledBtn = false;
                });
            }
        }

    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>


<style lang="scss" scoped>
.wrapper{
  /deep/.el-form-item{
    margin-bottom: 0;
  }
}

.opt-icon{
    font-size: 18px!important;
    cursor: pointer;
}

</style>