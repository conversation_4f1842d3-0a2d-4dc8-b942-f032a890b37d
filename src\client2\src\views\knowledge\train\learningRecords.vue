<!--分类 添加/编辑-->
<template>
  <div>
    <app-dialog title="学习记录" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100" :maxHeight="600">
      <template slot="body">
            <div class="wrapperBody __dynamicTabContentWrapper">
                <el-form ref="formData" label-position="right" label-width="120px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="课程名称">{{formModel.TrainsName||'无'}}</el-form-item>
                            <el-form-item label="学时">{{formModel.Period||'无'}}</el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="课程分类">{{formModel.TrainsClassificationName||'无'}}</el-form-item>
                            <el-form-item label="课程负责人">
                                <span v-if="formModel.PrincipalEmployeeList&&formModel.PrincipalEmployeeList.length>0">{{formModel.PrincipalEmployeeList.map(s => s.Name).join(",")}}</span>
                                <span v-else>无</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <page-title :showBackBtn='false'>
                        <div slot="def">
                            <tags :items="searchTypesData" v-model="listQuery.type">
                                <template v-for="t in searchTypesData" :slot="t.value">{{ t.label }}</template>
                            </tags>
                        </div>
                    </page-title>
                </el-form>
                <div class="__dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabAllColumns" :tab-datas="dataSourceList" :loading="listLoading"
                        :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable"
                        :multable="false" layoutMode='simple' :isShowBtnsArea='false' :isShowConditionArea="false"
                        @sortChagned="handleSortChange">
                        <!-- 学时 -->
                        <template slot="Period" slot-scope="scope">+{{scope.row.Period}}</template>
                        <!-- 状态 -->
                        <template slot="State" slot-scope="scope">
                            <span class="item-status" :style="{backgroundColor: getStateObj(scope.row.State).bgColor,
                            color: getStateObj(scope.row.State).color}">
                            {{ getStateObj(scope.row.State).label }}
                            </span>
                        </template>
                        <!-- 考试类型 -->
                        <template slot="TestModeType" slot-scope="scope">
                            <span class="item-status" :style="{backgroundColor: getTestModeTypeObj(scope.row.TestModeType).bgColor,
                            color: getTestModeTypeObj(scope.row.TestModeType).color}">
                            {{ getTestModeTypeObj(scope.row.TestModeType).label }}
                            </span>
                        </template>
                        <!-- 学习时间 -->
                        <template slot="LearningTime" slot-scope="scope">{{scope.row.LearningTime | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                        <!-- 考试成绩 -->
                        <template slot="ExamResults" slot-scope="scope">{{scope.row.ExamResults||'无'}}</template>
                        <!-- 是否及格 -->
                        <template slot="IsPass" slot-scope="scope">
                            <span class="item-status" :style="{color: getIsPassObj(scope.row.IsPass).color}">
                            {{ getIsPassObj(scope.row.IsPass).label }}
                            </span>
                        </template>
                        <!-- 积分 -->
                        <template slot="Integral" slot-scope="scope">{{((scope.row.Integral||scope.row.Integral==0)&&scope.row.Integral!='无')?'+'+scope.row.Integral:'无'}}</template>
                        <!-- 考试时间 -->
                        <template slot="ExamTime" slot-scope="scope">{{scope.row.ExamTime | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                        <!-- 用时 -->
                        <template slot="When" slot-scope="scope">{{scope.row.When||'0'}}分钟</template>
                    </app-table>
                </div>
                <!----------------------------------------- 分页 ------------------------------------------->
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange"
                @size-change="handleSizeChange" />
            </div>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose" text="关闭"></app-button>
      </template>
    </app-dialog>
  </div>
</template>
<script>
import indexPageMixin from "@/mixins/indexPage";
import * as StudyRecordApi from '@/api/knowledge/StudyRecord'
import { vars } from '../common/vars'
export default {
    name: "learning-records",
    components: {},
    mixins: [indexPageMixin],
    props: {
        checkedNode: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            stateTypes: vars.learningRecordsEnum.stateTypes,
            isPassTypes: vars.learningRecordsEnum.isPassTypes,
            testModeTypeTypes: vars.learningRecordsEnum.testModeTypeTypes,
            searchTypesData: [
                { label: "学习记录", value: 1 },
                { label: "考试记录", value: 2 },
            ],
            listLoading: false,
            formModel: {
                TrainsName: '', // 课程名称
                TrainsClassificationName: '', // 课程分类
                Period: '', // 学时
                PrincipalEmployeeList: [], // 课程负责人
            },
            total: 0,
            listQuery: {
                PageIndex: 1,
                PageSize: 10,
                trainId: '',
                type: 1,
            },
            tabAllColumns:[],
            // 学习记录 表格头
            tabColumns1: [
                {attr: {prop: "EmployeeName",label: "姓名", showOverflowTooltip: true,}},
                {attr: {prop: "EmployeeNo",label: "工号", showOverflowTooltip: true,}},
                {attr: {prop: "EmployeeJobTitle",label: "职位", showOverflowTooltip: true,}},
                {attr: {prop: "Period",label: "学时",sortable: "custom"},slot: true},
                {attr: {prop: "State",label: "状态",sortable: "custom"},slot: true},
                {attr: {prop: "LearningTime",label: "学习时间",sortable: "custom"},slot: true},
            ],
            // 考试记录 表格头
            tabColumns2: [
                {attr: {prop: "EmployeeName",label: "姓名", showOverflowTooltip: true,}},
                {attr: {prop: "EmployeeNo",label: "工号", showOverflowTooltip: true,}},
                {attr: {prop: "EmployeeJobTitle",label: "职位", showOverflowTooltip: true,}},
                {attr: {prop: "TestModeType",label: "考试类型",sortable: "custom"},slot: true},
                {attr: {prop: "ExaminationPaperName",label: "考卷名称", showOverflowTooltip: true,}},
                // {attr: {prop: "ExamResults",label: "考试成绩",sortable: "custom"},slot: true},
                {attr: {prop: "IsPass",label: "是否及格",sortable: "custom"},slot: true},
                {attr: {prop: "Integral",label: "积分",sortable: "custom"},slot: true},
                {attr: {prop: "ExamTime",label: "考试时间",sortable: "custom", width: 120},slot: true},
                {attr: {prop: "When",label: "用时",sortable: "custom"},slot: true},
            ],
            dataSourceList: [],
        };
    },
    computed: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.setDetail();
                }
            },
            immediate: true
        },
        "listQuery.type": {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1
                    this.listQuery.PageSize = 10
                    this.getList();
                }
            },
            immediate: true
        },
    },
    created() {
    },
    mounted() { },
    methods: {
        getTestModeTypeObj(val) {
            return this.testModeTypeTypes.find(
                s => s.value == val
            ) || {};
        },
        getStateObj(val) {
            return this.stateTypes.find(
                s => s.value == val
            ) || {};
        },
        getIsPassObj(val) {
            return this.isPassTypes.find(
                s => s.value == val
            ) || {};
        },
        // 表格排序按钮搜索
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
        setDetail(){
            this.formModel = Object.assign({}, this.formModel, this.checkedNode);
            this.dataSourceList = []
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        /**获取详情 */
        getList() {
            let self = this, postData = JSON.parse(JSON.stringify(self.listQuery));
            if (!self.checkedNode || !self.checkedNode.Id) {
                return false;
            }
            postData.trainId = self.checkedNode.Id;
            postData = self.assignSortObj(postData);
            self.listLoading = true;
            StudyRecordApi.getList(postData).then(res => {
                let typeName = self.listQuery.type == 1 ? 'StudyRecordDtoModelList' : 'ExamRecordDtoModelList';
                self.dataSourceList = res[typeName].Items;
                self.tabAllColumns = self[`tabColumns${self.listQuery.type}`]
                self.total = res[typeName].Total;
                self.listLoading = false;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapperBody{
    height: 590px;
}
</style>


