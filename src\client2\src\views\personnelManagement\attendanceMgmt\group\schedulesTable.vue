<template>
    <div class="wrapper __dynamicTabContentWrapper">
        <div class="row-wrapper">
            <div class="lft">
                <div style="margin-bottom: 10px; color: #abaaaa;" v-if="editable">
                    <span>切换月份前，必须保存当前月份的修改。</span>
                </div>
                排班月份：<el-date-picker :disabled='!changeMonth' :title='!changeMonth ? changeMonthTip : ""' v-model="currentMonth" type="month" style="width: 120px;" :clearable="false" value-format="yyyy-MM" placeholder="选择月"></el-date-picker>
                <span style="margin-left: 20px;">
                    <el-button type="text" :disabled='!changeMonth' :title='!changeMonth ? changeMonthTip : ""' @click="prevMonth">上个月</el-button>
                </span>
                <span style="margin: 0 20px;">
                    <el-button type="text" :disabled='!changeMonth' :title='!changeMonth ? changeMonthTip : ""' @click="nextMonth">下个月</el-button>
                </span>
                <span>
                    <el-input style="width: 300px;" v-antiShake='{
                        time: 300,
                        callback: () => {
                            PageIndex = 1;
                            getList();
                        }
                    }' clearable v-model="Keyword" placeholder='请输入名称或工号' @clear='getList'></el-input>
                </span>
                <span style="margin-left: 20px;">
                    <el-checkbox v-model="chooseException" @change='handleChanged'>查看应出勤异常</el-checkbox>
                </span>
            </div>
            <div class="rht">
                <slot name="btnArea"></slot>
            </div>
        </div>
        <div style="margin-bottom: 10px; color: #abaaaa;" v-if="editable">
            <span>操作说明：双击格子内的班/休即可进行调整。</span>
        </div>
        <!-- <u-table
            ref="plTreeTable"
            fixed-columns-roll
            header-drag-style
            :height="600"
            use-virtual
            row-id="EmployeeId"
            v-loading="loading"
            :border='false'
            :row-height='30'
            >

                <template slot="empty">暂无数据</template>
                <u-table-column type="index" label="序号" width='60'></u-table-column>
                <u-table-column v-for="(c, idx) in tabColumns" :prop="c.attr.prop" :label="c.attr.label" :key="idx"></u-table-column>

                <u-table-column 
                    v-for="(item, idx) in tabColumnsDynamic"
                    :width='item.attr.width' 
                    :key="item.attr.prop" 
                    :prop="item.attr.prop" 
                    :label="item.attr.label"
                >
                    <template slot-scope="scope" v-if="scope.row && scope.row.DateList && getCellData(scope.row.DateList, `${currentMonth}-${idx + 1}`)">
                        <span 
                            :key="idx"
                            @dblclick='handleToggleType(scope.row.EmployeeId, getCellData(scope.row.DateList, `${currentMonth}-${idx + 1}`))' 
                            :class="[`tip-${getCellData(scope.row.DateList, `${currentMonth}-${idx + 1}`).DateType}`]" 
                        >
                            {{ getCellData(scope.row.DateList, `${currentMonth}-${idx + 1}`).DateType == 2 ? '休' : '班' }}
                        </span>
                        <i v-show="isChanged(getCellData(scope.row.DateList, `${currentMonth}-${idx + 1}`))" class="tip-changed" :key="`row${scope.index}-${idx}`"></i>
                    </template>
                </u-table-column>

        </u-table> -->
        <div v-loading='loading' id="__dynamicTabCoreWrapper">
            <app-table-core
                class="orderList"
                ref="mainTable"
                :isForceFlush="false"
                :tab-columns="tabColumns.concat(tabColumnsDynamic)"
                :tab-datas="tabDatas"
                :tab-auth-columns="[]"
                :isShowAllColumn="false"
                :isShowOpatColumn="false"
                :startOfTable="0"
                :max-height='700'
                :serial='false'
                border
                :multable='false'
                @rowSelectionChanged="() => {}"
                @cell-mouse-enter='handleMouseEnter' 
                @cell-mouse-leave='handleMouseLeave'
                :cell-class-name='setCellClass'
                stripe
                :loading='loading'
                :height='!tableHeight ? tabHeight : tableHeight'
            >
                <template slot="Idx" slot-scope="scope">
                    {{ ((PageIndex - 1) * PageSize) + scope.index }}
                </template>
                <template slot="Attendance" slot-scope="scope">
                    <!-- 默认值 attendanceOfMonth = 0 表示接口调用未成功，不用全部显示红色 -->
                    <span :style="'color:' + (attendanceOfMonth > 0 && computedAttendance(scope.row) != attendanceOfMonth ? 'red' : '')">
                        {{ computedAttendance(scope.row) }}
                    </span>
                </template>
                
                <template v-for="(m, idx) in tabColumnsDynamic" :slot="m.attr.prop" slot-scope="scope">
                    <div :key="idx" @dblclick.stop='handleToggleType(scope.row.EmployeeId, idx + 1, scope.row[m.attr.prop])'>
                        <div 
                            class="cell-wrapper" 
                            v-if='isChanged(scope.row.EmployeeId, idx + 1)'
                            :class="`tip-${scope.row[m.attr.prop] == 1 ? 2 : 1}`"
                        >
                            {{ (scope.row[m.attr.prop] == 1 ? 2 : 1) | typeFilter }}
                            <i class="tip-changed"></i>
                        </div>
                        <div v-else class="cell-wrapper" :class="`tip-${scope.row[m.attr.prop]}`">
                            {{ scope.row[m.attr.prop] | typeFilter }}
                        </div>
                    </div>
                </template>
            </app-table-core>
        </div>

        <pagination
            :total="total"
            :page.sync="PageIndex"
            :size.sync="PageSize"
            layout="total, prev, pager, next"
            @pagination="handleCurrentChange"
            @size-change="() => {}"
        />

    </div>
</template>

<script>
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
// import cell from './cell'
import dayjs from 'dayjs'
export default {
    name: "schedules-table",
    directives: {},
    components: {
        // cell,
    },
    mixins: [tabDynamicHeightMixins],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        changeMonth() {
            //可切换月份
            return !this.histories || this.histories.length == 0
        },
        changeMonthTip() {
            return '当前月份存在排班调整，请保存后再切换月份'
        }
    },
    filters: {
        typeFilter(val) {
            if(val == 1) {
                return '班'
            }else if(val == 2) {
                return '休'
            }
            return ''
        }
    },
    props: {
        //临时引入，因为在弹框组件 schedulesDialog 引入时出现双滚动条
        tableHeight: {
            type: Number,
            default: null
        },
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        //考勤组编号（必填）
        timecardDepartmentId: {
            type: String,
            required: true
        },
        //默认当前月份 （2021-03）
        month: {
            type: String,
            default: `${dayjs().year()}-${dayjs().month() + 1}`
        },
        //老的排班数据
        oldSchedules: {
            type: Array,
            default: () => {
                return []
            }
        },
        //需要获取排班的用户
        empIds: {
            type: Array,
            default: () => {
                return []
            }
        },

        

    },
    watch: {
        timecardDepartmentId(val) {
            if(val) {
                this.tabDatas = []
                this.getList()
            }
        },
        currentMonth: {
            handler(val) {
                this.getAttendance()

                this.tabColumnsDynamic.splice(0, this.tabColumnsDynamic.length)

                this.$nextTick(() => {
                    let monthTemp = val
                    this.tabColumnsDynamic = Array.from(Array(dayjs(monthTemp).daysInMonth()), (v,k) => {
                        let weeks = ['日', '一', '二', '三', '四', '五', '六']
                        let tempDate = dayjs(`${monthTemp}-${k + 1}`).day()
                        // label: 日期（几号）
                        // week: 周几 （1-0）（一-日）
                        return {
                            attr: { prop: this.genPropName(k + 1), label: `${k + 1}${weeks[tempDate]}`, value: k + 1, week: tempDate, renderHeader: (h, { column }) => this.renderHeader(h, { column }, k + 1, weeks[tempDate]), width: '36', align: 'center', resizable: false },
                            slot: true
                        }
                    })
                    this.PageIndex = 1
                    this.getList();
                })
            },
            immediate: true
        },
        
    },
    created() {
    },
    data() {
        return {
            attendanceOfMonth: 0, //当前月份的“应出勤”天数
            chooseException: false,
            currentMonth: this.month,
            PageIndex: 1,
            PageSize: 20,
            total: 0,
            Keyword: '',

            histories: JSON.parse(JSON.stringify(this.oldSchedules)) || [], //变更历史记录

            tabColumns: [
                {   
                    attr: { prop: "Idx", label: "序号", fixed: 'left', width: '50', renderHeader: this.renderHeader2 },
                    slot: true
                },
                {   
                    attr: { prop: "Name", label: "姓名", fixed: 'left', renderHeader: this.renderHeader2 },
                },
                {   
                    attr: { prop: "Number", label: "工号", fixed: 'left', renderHeader: this.renderHeader2 },
                },
                {   
                    attr: { prop: "Attendance", label: "应出勤", fixed: 'left', align: 'center', renderHeader: this.renderHeader2 },
                    slot: true
                },
                
            ],
            tabColumnsDynamic: [],
            loading: false,

            tabDatas: [],

        };
    },
    methods: {
        handleChanged() {
            this.PageIndex = 1
            this.getList()
        },
        setCellClass({row, column, rowIndex, columnIndex}) {
            if(this.editable && column.property.indexOf("day_") > -1) {
                return 'work-cell'
            }
        },
        handleCurrentChange(val) {
          this.PageIndex = val.page;
          this.PageSize = val.size;
          this.getList();
        },
        prevMonth() {
            let temp = dayjs(this.currentMonth).subtract(1, 'month').format('YYYY-MM')
            this.currentMonth = temp
        },
        nextMonth() {
            let temp = dayjs(this.currentMonth).add(1, 'month').format('YYYY-MM')
                this.currentMonth = temp
        },
        isChanged(empId, day) {
            let date = dayjs(`${this.currentMonth}-${day}`).format('YYYY-MM-DD')
            let result = this.histories.find(s => s.employeeId === empId && s.date === date)
            return result
        },
        handleMouseEnter(row, column, cell, event) {
            // if(this.editable && column.property.indexOf('day_') > -1 && !cell.classList.contains('active-cell')) {
            //     console.log('----添加active-cell')
            //     cell.classList.add('active-cell')
            // }
        },
        handleMouseLeave(row, column, cell, event) {
            // if(this.editable && cell.classList.contains('active-cell')){
            //     console.log('----移除移除active-cell')
            //     cell.classList.remove('active-cell')
            // }
        },
        //获取应出勤天数
        getAttendance() {
            let monthTemp = this.currentMonth

            if(monthTemp) {
                //缺少接口获取应出勤天数
                let params = monthTemp.split('-')
                let postDatas = {
                    Year: parseInt(params[0]), 
                    Month: parseInt(params[1])
                }
    
                timecardDepartment.getAttendanceOfMonth(postDatas).then(res => {
                    this.attendanceOfMonth = res
                })

            }
        },
        computedAttendance(row) {
            //当前月的总天数
            let daysCount = dayjs(this.currentMonth).daysInMonth()
            //出勤天数（type == 1（班））的总数
            let attendance = 0
            for(let i = 0; i < daysCount; i++) {
                let day = i + 1
                //如果有修改记录，那么就以修改记录中的类型计算
                let changedObj = this.isChanged(row.EmployeeId, day)
                if(changedObj) {
                    if(changedObj.type == 1) {
                        attendance++
                    }
                }else{
                    if(row[this.genPropName(day)] == 1) {
                        attendance++
                    }
                }
            }
            return attendance
        },
        handleToggleType(empId, day, type) {

            if(this.editable) {
                let date = dayjs(`${this.currentMonth}-${day}`).format('YYYY-MM-DD')
                let idx = this.histories.findIndex(s => s.employeeId == empId && s.date == date)
    
                if(idx > -1) {
                    this.histories.splice(idx, 1)
                }else{
                    this.histories.push({
                        employeeId: empId,
                        date: date,
                        type: type == 1 ? 2 : 1
                    })
                }
            }
        },
        getCellData(scheduleList, date) {
            if(scheduleList && scheduleList.length > 0 && date) {
                return scheduleList.find(s => dayjs(s.Date).format('YYYY-MM-DD') == dayjs(date).format('YYYY-MM-DD'))
            }
            return null
        },

        getDatas() {
            return JSON.parse(JSON.stringify(this.histories.map(s => {
                s.dateType = s.type
                return s
            })) || [])
        },
        renderHeader2(h, { column }) {
            let label = column.label

            let isAttendColumn = column.property == 'Attendance' //是否为应出勤列

            let columnInnerHtmlObj = [
                h("div", {style: isAttendColumn ? 'text-align: center;' : ''}, label)
            ]
            
            if(isAttendColumn) {
                columnInnerHtmlObj.push(h("div", {style: isAttendColumn ? 'text-align: center;' : ''}, `(${this.attendanceOfMonth}天)`))
            }
            return h("div", {class: isAttendColumn ? 'weekend' : '', style: 'padding: 0 10px;'}, columnInnerHtmlObj);
        },
        /**
         * 自定义表头
         */
        renderHeader(h, { column }, day, week) {
            let tempDay = column.property.replace('day_', '')

            return h("div", {class: [new Date().getDate() == tempDay ? 'current-day' : week == '六' || week == '日' ? 'weekend' : '', 'week-title'] }, [
                h("div", day),
                h(
                    "div", 
                    {
                        // style: "color: red"
                    },
                    week
                )
            ]);

            // return h("span", {
            //     style: column.label.indexOf('日') > -1 || column.label.indexOf('六') > -1 ? "color: #F59A23" : "",
            //     class: 'aaaa'
            // }, column.label);

        },
        //生存属性名称，如：day_01、day_02
        genPropName(day) {
            if(day && day.length == 1) {
                day = `0${day}`
            }
            return `day_${day}`
        },
        getList() {
            // let mockDatas = [
            //     {Number: '000110', Name: '张三', DateList: [
            //         {Date:"2021-03-12", DateType: 2},
            //         {Date:"2021-03-13", DateType: 2},
            //         {Date:"2021-03-14", DateType: 1},
            //         {Date:"2021-03-15", DateType: 1},
            //         {Date:"2021-03-16", DateType: 1},
            //     ]}
            // ]
            let postDatas = {
                TimecardDepartmentId: this.timecardDepartmentId,
                EmployeeIdList: this.empIds,
                Year: dayjs(this.currentMonth).year(),
                Month: dayjs(this.currentMonth).month() + 1,
                PageSize: this.PageSize,
                PageIndex: this.PageIndex,
                Keyword: this.Keyword,
                ChooseException: this.chooseException,
                List: this.getDatas()
            }
            this.loading = true
            timecardDepartment.getTimecardScheduling(postDatas).then(result => {
                this.loading = false

                let res = result.Items
                
                this.total = result.Total
                if(res && res.length > 0) {
                    res = res.map(s => {
                        if(s.DateList && s.DateList.length > 0) {
                            s.DateList.forEach((n, idx) => {
                                let propName = this.genPropName((idx + 1))
                                s[propName] = n.DateType
                            });
                            delete s.DateList
                        }
                        return s
                    })
                    this.tabDatas = res || []
                }else{
                    this.tabDatas = []
                }
            }).catch(err => {
                this.loading = false
            })
        },

    }
};
</script>

<style scoped>

.orderList >>> .active-cell{
    background-color: yellow!important;
    color: red;
}

.orderList >>> .work-cell .cell{
    padding: 0;
    cursor: pointer!important;
}

.orderList >>> .el-table__header .cell{
    padding: 0;
}

.orderList >>> .current-day{
    background: #409eff;
    color: #fff;
}

.orderList >>> .weekend{
    color: #F59A23;
}

</style>

<style lang='scss' scoped>
.wrapper{
    height: 100%;
    user-select: none;

    .table-wrapper{

    }
    .row-wrapper{
        padding: 0 10px 10px;
        // margin-bottom: 10px;
        display: flex;

        .lft{
            flex: 1;
        }
        .rht{
            padding-right: 10px;
        }
    }
}
.tip-1{
    color: #409eff;
}
.tip-2{
    color: #70b603;
}

.tip-1, .tip-2 {
    font-weight: 900;
}

.tip-changed{
    position: absolute;
    height: 6px;
    width: 6px;
    background: red;
    top: 0;
    right: 0;
    border-radius: 50%;
}


</style>