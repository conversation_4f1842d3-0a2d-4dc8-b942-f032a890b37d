<template>
    <el-dialog v-el-drag-dialog class="dialog-mini" width="1000px" :title="dialogTitle" :visible.sync="isVisible"
        :before-close="handleClose" :close-on-click-modal="false" :append-to-body="true">
        <!-- {{ temp }} -->
        <!-- {{ tempStruct }} -->
        <!-- {{ formStruct }} -->
        <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="100px">
            <el-row>
                <el-col :span="12" v-for="(item, idx) in tempStruct" :key="idx" v-show="getShowConditionOfCtrl(item)">
                    <dynamic-ctrl :ctrl='item' v-model="temp[item.name]"></dynamic-ctrl>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer">
            <el-button size="mini" @click="handleClose">取消</el-button>
            <el-button size="mini" type="primary" :loading='postLoading' @click="saveChange">确认</el-button>
        </div>
    </el-dialog>
</template>

<script>
// import TreeItem from './treeItem'
import * as ec from '@/api/enterpriseConfigure'
import elDragDialog from "@/directive/el-dragDialog"
import DynamicCtrl from './dynamicCtrl'
import * as cdc from '@/api/componentDatasourceConfiguration'
import * as df from '@/api/dynamicForm'
import request from '@/utils/request'

export default {
    name: 'dynamic-form-analysis-edit',
    directives: {
        elDragDialog
    },
    components: {
        // TreeItem,
        DynamicCtrl,
    },
    computed: {
        dialogTitle() {
            if (this.dialogStatus == "create") return "添加";
            else if (this.dialogStatus == "update") return "编辑";
            else if (this.dialogStatus == "detail") return "详情";
            else return "";
        }
  
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑)
        dialogStatus: {
            required: true,
            type: String
        },
        //新增(编辑)弹框是否显示
        dialogFormVisible: {
            required: true,
            type: Boolean
        },
        formObj: {
            type: Object,
            required: true,
            default: null
        },        
        formDatas: {
            type: Object,
            default: null
        },
        
    },
    created() {
        this.isVisible = this.dialogFormVisible;
    },
    // watch: {
    //     isVisible(val) {
    //         // if(this.id && val) {
    //         //     ec.detail({key: this.id}).then(res => {
    //         //     })
    //         // }
    //     }
    // },
    data() {
        return {
            formStruct: JSON.parse(JSON.stringify(this.formObj)),//表单结构
            isVisible: false,
            dialogSubFormVisible: false,
            rules: {},
            postLoading: false,
            tempStruct: [],
            temp: {
            },
        }
    },
    mounted() {
        this.initFormObj()
    },
    methods: {
        getLabelText(attrName) {

        },
        //必须当作Array对待
        isNeedToArray(ctrl) {
            if(ctrl.type === 10002){
                return true
            }else if(ctrl.type === 5) {// 是 Select，并且支持多选
                let mulAttr = ctrl.attrs.find(s => s.attrName == 'isMultiple')
                if(mulAttr && mulAttr.value && mulAttr.value.length > 0) {
                    return mulAttr.value[0] == 'true'
                }
            }

            return false
        },
        initFormObj() {
            
            let ctrls = this.formStruct.formContent
                                .filter(s => s.type != 1 && s.type != 2 && s.type != 0) //type=0为新增表单时，后台默认添加的控件；1、2为容器控件

            let columnsList = ctrls.map(c => {

                let fieldNameObj = c.attrs.find(o => o.attrName == 'name')
                let fieldLabelObj = c.attrs.find(o => o.attrName == 'label')
                
                c.label = fieldLabelObj.value
                c.name = fieldNameObj.value
                c.value = ''

                if(c.type == 5 || c.type == 6) {
                    let datasourceAttr = c.attrs.find(a => a.attrName == 'dataSource')
                    if(datasourceAttr && datasourceAttr.value) {
                        
                        let sourceId = datasourceAttr.value

                        //需要更替接口
                        cdc.detail({configurationId: sourceId}).then(res => {
                            if(res.DataSourceType == 1) { //手动创建的下拉选项
                                datasourceAttr.options = JSON.parse(res.ConfigurationValues)
                            }else{ //需要通过接口获取
                                //需要补充接口（待完善）
                                let api = res.ConfigurationValues
                                // request({
                                //     url: api,
                                //     method: 'get'
                                // }).then(res => {
                                // })
                            }
                        })
                    }
                }
                
                if(this.isNeedToArray(c)) { //值为数组，需要转换
                    c.value = []
                }

                if(this.dialogStatus != 'create' && this.formDatas) {
                    let ctrlValue = this.formDatas[c.name].value
                    if(this.isNeedToArray(c)) { //值为数组，需要转换
                        c.value = JSON.parse(ctrlValue)
                    }else{
                        c.value = ctrlValue
                    }
                }

                return c
            })

            
            columnsList.forEach(e => {
                let valueOfCtrl = this.dialogStatus == 'create' ? '' : this.formDatas[e.name]
                this.$set(this.temp, e.name, valueOfCtrl)
            });
            this.tempStruct = columnsList
        },
        // edit(data) {
        //     this.temp = Object.assign({}, this.temp, data)
        //     this.openDialog()
        // },
        // openDialog() {
        //     this.dialogSubFormVisible = true
        // },
        // closeDialog() {
        //     this.dialogSubFormVisible = false
        // },
        // createData() {
        //     let currentNode = this.$refs.tree.getCurrentNode()
        //     currentNode.text = this.temp.text
        //     this.$refs.tree.setCurrentNode(currentNode)
        //     this.closeDialog()
        // },
        saveChange() {
            let postDatas = {
                FormId: this.formStruct.id,
                ComponentRowId: this.dialogStatus == 'create' ? '' : this.formDatas.componentRowId,
                ComponentInfos: []
            }

            Object.keys(this.temp).forEach((key) => {
                let _key = key
                let _val = this.temp[_key]
                let _id = ''
                let ctrl = this.formStruct.formContent.find(s => s.name == _key)

                if(ctrl) {
                    _id = ctrl.id
                }

                postDatas.ComponentInfos.push({
                    componentId: _id,//控件id，目前没用到
                    ctrlType: ctrl.type + '',
                    fieldName: key,
                    componentValue: this.isNeedToArray(ctrl) ? JSON.stringify(_val) : _val
                })

            });

            let result = null
            if(this.dialogStatus == 'create') {
                result = df.addData(postDatas)
            } else {
                result = df.editData(postDatas)
            }

            if(result) {
                result.then(res => {
                    this.$notify({
                        title: '成功',
                        message: '保存成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.$emit("reloading");
                    this.$emit("closeDialog");
                }).catch(err => {

                })
            }
        },
        getShowConditionOfCtrl(item) {
            let isshow = true
            let conditions = item.attrs.find(s => s.attrName == 'constraints')
            if(conditions && conditions.value) {
                let tmp = conditions.value.find(s => s.consName == 'vshow').consList
                if(tmp.length > 0) {
                    tmp.forEach(c => {
                        if(isshow) {
                            isshow = this.temp[c.key] == c.value
                            if(!isshow) {
                                isshow = false
                                return false
                            }
                        }
                    });
                }
            }
            return isshow
        },        
        handleClose() {
            this.cancel()
        },
        cancel() {
            this.$emit("closeDialog");
        },
    }
}
</script>


<style scoped>

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

.node-label{
    display: inline-block;
    max-width: 440px;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.custom-tree-node input{
  width: 580px;
  border: 1px solid #DCDFE6;
  color: #606266;
  padding:0 15px;
  height: 24px;
  border-radius:4px;
}
</style>
