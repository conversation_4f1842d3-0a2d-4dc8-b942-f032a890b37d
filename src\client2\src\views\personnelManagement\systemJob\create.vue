<!--客服编辑-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
            {{ dialogStatus }}
            <template slot="body">
                <el-form
                    :rules="formRules"
                    ref="formRef"
                    :model="formModel"
                    label-position="right"
                    label-width="100px"
                    v-loading='loading'
                >
                <el-form-item label="职位分类" prop="CategoryId">
                    <el-select v-model="formModel.CategoryId" filterable placeholder="" clearable style="width: 100%;">
                        <el-option v-for="item in categories" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="职位名称" prop="Name">
                    <el-input :disabled="!editable" maxlength="30" type="text" v-model="formModel.Name"></el-input>
                </el-form-item>
                <el-form-item label="描述" prop="Description">
                    <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="5" v-model="formModel.Description"></el-input>
                </el-form-item>
                </el-form>
            </template>
            <template slot="footer">
                <app-button :buttonType="2" @click="handleClose"></app-button>
                <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as systemJob from '@/api/personnelManagement/systemJob'

export default {
    /**名称 */
    name: "systemJob-setting-edit",
    /**组件声明 */
    components: {},
    /**参数区 */
    props: {
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        }
    },
    /**数据区 */
    data() {
        return {
            loading:false,
            categories: [],
            /**按钮在执行，不允许点击 */
            buttonLoading: false,

            /**表单模型 */
            formModel: { 
                CategoryId: null,
                JobId: null,
                Name: '',
                Description: ''
            },
            /**表单规则 */
            formRules: {
                CategoryId: { fieldName: "职位分类", rules: [{ required: true }] },
                Name: { fieldName: "职位名称", rules: [{ required: true }] },
            }
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '创建职位'
            }else if(this.dialogStatus == 'update') {
                return '编辑职位'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail" && this.dialogStatus != 'approval'
        },
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                let _this = this;
                this.formModel = {
                    CategoryId: null,
                    JobId: null,
                    Name: '',
                    Description: ''
                };
                this.getCategories();

                if(val && this.dialogStatus != 'create' && this.id) {
                    this.getDetail()
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let _this = this;
            _this.$refs.formRef.validate(valid => {
                if (valid) {
                    let result = null;
                    _this.buttonLoading = true;

                    if(this.dialogStatus == 'create') {
                        result = systemJob.add(this.formModel)
                    }else if(this.dialogStatus == 'update') {
                        result = systemJob.edit(this.formModel)
                    }

                    result.then(response => {
                        _this.buttonLoading = false;
                        _this.$refs.appDialogRef.createData();
                    }).catch(err => {
                        _this.buttonLoading = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            systemJob.detail({id: this.id}).then(response => {
                this.loading = false
                this.formModel = Object.assign({}, this.formModel, response)
            }).catch(err => {
                this.loading = false
            })
        },
        getCategories() {
            let paramData = {
                PageSize: 1000,
                PageIndex: 1
            };
            systemJob.getCategoryListPage(paramData).then(response => {
                this.categories = response.Items.map(s => {
                    return {
                        value: s.CategoryId,
                        label: s.CategoryName
                    }
                }) || []
                
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


