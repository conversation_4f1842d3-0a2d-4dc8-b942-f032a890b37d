@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";

// 工作台里程碑详情弹出框样式
.eilepostBox .elForm .el-dialog__body {
    padding: 0;
  }
.multistageFoldingPanel .el-collapse-item > div:first-child {
    border-bottom: 1px solid #ebeef5;
  }
  .multistageFoldingPanel .el-collapse-item__wrap {
    border-bottom: 0;
  }
  .multistageFoldingPanel .el-collapse-item__header {
    border-bottom: 0;
    position: relative;
  }
  
  .el-popover {
    max-width: 400px;
    min-width: 80px;
  }
  
  //项目管理版本样式更改
  .versionWrapper .elContainer .main {
    width: calc(100% - 330px);
  }
  
  //富文本样式
  .w-e-text {
    height: 380px !important;
    overflow-y: auto !important;
  }
  
  // .w-e-toolbar{
  //   flex-wrap:wrap;
  // }
  // .v-modal{
  //     z-index: 30006!important;
  // }
  // .el-dialog__wrapper{
  //     z-index: 30007!important;
  // }
  .w-e-text-container {
    z-index: 10 !important;
  }
  .w-e-menu {
    z-index: 11 !important;
  }
  
  .uploader-file-list .uploader-list {
    margin-top: 10px;
  }
  
  // 滚动条样式
  ::-webkit-scrollbar {
    cursor: pointer;
    width: 10px;
    height: 10px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #c1c1c1;
  }
  ::-webkit-scrollbar-track {
    // border-radius: 10px;
    // background: #f1f1f1;
  }
  
  //// 影响了所有下拉（el-select）样式，导致下拉option最后一个没有显示完整；
  // .el-select-dropdown__wrap {
  //   margin-bottom: -18px !important;
  // }
  .createRole .tab {
    border-bottom: 0 !important;
  }
  .mobile .el-card__body {
    height: calc(100% - 34px);
    padding: 10px;
  }
  .mobile .el-card__body .el-checkbox {
    margin-right: 20px;
  }
  #app .cus-el-main {
    height: calc(100vh - 117px + 52px); //52px 去掉了原来的 logo 行
  }
  .appContainer .page-wrapper {
    min-height: calc(100% - 10px);
  }
  .editor li {
    list-style: disc;
  }
  .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f0f7ff;
  }
  // .el-tree-node.is-current > .el-tree-node__content {
  //     background-color: #2F8DFB !important;
  //     color: white;
  // }
  // .businessElForm .el-form-item{
  //     margin-bottom: 0!important;
  // }
  .treeTable td .cell,
  .treeTable .el-table__body-wrapper,
  .treeTable .el-table {
    overflow: visible !important;
  }
  .configDialog .el-dialog__body {
    min-height: 600px;
  }
  .panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
  }
  
  .quillEditor .ql-editor {
    min-height: 380px;
    overflow-y: auto;
    // padding-right:0;
  }
  
  .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    line-height: 28px !important;
  }
  
  // .quillEditor .ql-video {
  //   display: none;
  // }

  .ql-toolbar{
    border-radius: $border-radius-base $border-radius-base 0 0;
  }
  .ql-container{
    border-radius: 0 0 5px 5px!important;
  }

  .ql-toolbar.ql-snow .ql-formats {
    margin-right: 0 !important;
  }
  
  .engineering .el-card .el-card__body {
    padding: 0 !important;
  }
  // .engineering .el-card .el-collapse{
  //     border-top: 0;
  // }
  
  .engineering .el-collapse-item__header {
    width: calc(100% - 10px);
  }
  
  .engineering .proBox .el-progress__text {
    display: none;
  }
  
  .afterSalesContainer .btns-area .el-button-group {
    width: 100%;
  }
  
  .quillEditor .ql-toolbar .ql-formats:first-child .ql-picker-label {
    padding-right: 20px;
  }
  .quillEditor .ql-toolbar .ql-formats:first-child .ql-font {
    width: auto;
    min-width: 108px;
  }
  
  .tagsBox .list-wrapper .tag {
    display: block !important;
  }
  
  .engineering .elProgress {
    width: 160px;
  }
  .engineering .el-progress-bar {
    padding-right: 60px;
    margin-right: -60px;
  }
  .ql-align-right {
    text-align: right;
  }
  .ql-align-center {
    text-align: center;
  }
  .ql-align-left {
    text-align: left;
  }
  .ql-align-justify {
    text-align: justify;
  }
  
  .ql-video {
    display: block;
    width: 68%;
    height: 400px;
    background:black;
    margin: 0 auto !important;
  }
  input.ql-video {
    display: none !important;
  }
  .divUeditor .ql-video video {
    width: 100% !important;
  }
  .el-dialog__wrapper .ql-video {
    height: 300px;
  }
  
  .engineering .el-collapse {
    border-top: 0;
    border-bottom: 0;
  }
  .engineering .el-collapse-item__header {
    border-bottom: 1px solid #ebeef5;
    width: 100%;
  }
  .insideColl .el-collapse:not(:last-child) {
    border-bottom: 1px solid #ebeef5;
  }
  
  .repairAnalysis .el-table--border {
    border-top: 0;
  }
  .repairAnalysis .el-table--border th,
  .repairAnalysis .el-table--border td {
    border-right: 0;
  }
  
  .businessAnalysis .elCollapseBox .el-collapse-item__header {
    padding-left: 10px;
  }
  
  .repairAnalysis .app-table thead tr th:first-child {
    text-align: left !important;
  }
  
  .dashboard-editor-container {
    position: relative;
  }
  .engineering .el-card.is-always-shadow{
    box-shadow: 0 0 5px 1px rgba(0,0,0,0.1);
  }
  .engineering .main{
    padding-top:10px;
  }
  
  pre{
    font-family: PingFang SC,Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif;
    font-size: 14px;
  }
  
  .bMBody .el-table--border{
    border-top: none;
  }
  .structuralParts .list-wrapper,.equipmentParts .list-wrapper, .supplierParts .list-wrapper{
    padding-left:10px!important;
  }
  .structuralParts .list-wrapper .tag,.equipmentParts .list-wrapper .tag,.supplierParts .list-wrapper .tag{
    padding:10px 6px!important;
    margin:0!important;
    // margin-top: 10px!important;
  }
  .equipmentParts .list-wrapper .tag, .supplierParts .list-wrapper .tag{
    padding:8px!important;
  }
  .specifications .el-select{
    width:100%;
  }
  .divUeditor img{
    max-width: 100%;
  }
  
  .accessories .el-checkbox .el-checkbox__label{
    font-weight: normal!important;
  }
  
  .equipmentParts .tip, .supplierParts .tip{
    margin-top: 0!important;
  }
  
  
  .lossStatistical .el-card__body{
    padding:10px 0;
  }
  .lossStatistical .el-collapse{
    border:0;
  }
  .lossStatistical .el-collapse-item__wrap{
    border:0;
    padding:0 10px;
  }
  .lossStatistical .el-collapse-item__header{
    height:auto;
    line-height: 26px;
    padding-left:10px;
    border:0;
  }
  .lossStatistical .lsMain .el-collapse-item__header{
    padding-left:0;
  }
  .lossStatistical .lsMain2 .el-collapse-item__wrap{
    padding-left:0;
  }
  .lossStatistical .ulBot .el-collapse-item__wrap{
    padding-left:0;
  }
  .el-collapse-item__header{
    color:#303133!important;
  }
  
  .newReport .el-card__body{
    position: relative;
  }
  
  
  .intBor input{
    border-color:red;
  }
  
  .elMarginSlider .el-slider{
    margin-top: -5px;
  }
  .elMarginSlider .el-slider__input{
    margin-top:-2px;
  }
  
  .failureCase .conditionArea-wrap{
    border-bottom: 0!important;
  }
  .failureCase .btns-area{
    border-top:1px solid #EBEEF5!important;
  }
  .failureCase .el-radio__label{
    font-weight: bold!important;
  }
  .gzxxxq{
    width:30%;
  }
  .gzgjz .btns-area{
    display: none!important;
  }
  .newCasesForm .b6.el-form-item{
    margin-bottom: 6px!important;
    }
   
  .newCasesForm .el-card__body{
    padding:0;
  }
  .newCasesForm .el-form{
    padding-top:0!important;
  }
  .newCasesForm .bm .el-form-item:first-child{
    position: relative;
  }
  .newCasesForm .el-checkbox{
    position: absolute;
    top: -1px;
    left: -88px;
  }
  //.newCasesForm .bm .el-input{
    //width:90%;
  //}
  .bxdcl .el-input{
    width:96%;
  }
  
  .classificationLabel label:not(.el-radio) {
    font-weight: normal;
  }
  .classificationLabel .el-checkbox{
    margin-bottom: 6px;
  }
  .bxdcl .elCol,.bxdcl .elCol .el-form-item--mini.el-form-item{
    margin-bottom: 6px;
  } 
  .bxdcl .createMainten.el-form-item{
    margin-bottom: 14px!important;
  }
  .item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
  }
  .failureCase .hideMoreCondition{
    float:right;
    text-align: right!important;
  }
  .elCreateTime .el-range__close-icon{
    width:10px;
  }
  
  
  //地区选择器样式
  ._regional_detail_wrapper{
    display: flex;
  }
  
  ._regional_detail_wrapper .btn_wrapper{
    padding-right: 5px;
  }
  
  ._regional_detail_wrapper .regional_text{
    overflow: hidden;white-space: nowrap;text-overflow: ellipsis; word-break:break-all;
  }
  
  ._regional_detail_wrapper .close_wrapper{
    flex: 1;
  }
  
  ._regional_detail_wrapper .close_wrapper .i_wrapper{
    width: 28px; text-align: center;
  }
  
  ._regional_detail_wrapper .close_wrapper .i_wrapper .btn{
    // cursor: pointer;
    padding: 4px!important;
  }
  .el-input-number .el-input{
    width:100%!important;
  }
  
  .saleSettlement .el-button-group{
    width:100%;
  }
  .el-calendar thead th:first-child{
    border-left: 1px solid #EBEEF5;
  }
  .el-calendar thead th{
    border-right: 1px solid #EBEEF5;
    border-top: 1px solid #EBEEF5;
  }
  .attendance .el-calendar-day{
    height:100px;
  }

 .processManagement .el-switch.is-disabled {
    opacity: 1;
}
.processManagement .el-switch.is-disabled .el-switch__core, .processManagement .el-switch.is-disabled .el-switch__label {
    cursor: pointer !important;;
}

.elCalendar .el-calendar__header{
  display: none;
}
.elCalendar .el-calendar__body{
  padding-left:0;
  padding-right:0;
}