<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="部门管理" :subTitle="['企业组织结构及人员的管理页面']"></page-title> -->
        <div class="pageWrapper __dynamicTabContentWrapper">
            <div class="tagBox">
                <tags :items='displayTypes' v-model="listQuery.Status" @change="handleTagsChange">
                    <template v-for="t in displayTypes" :slot="t.value">
                        {{ t.label }}
                    </template>
                </tags>
            </div>
            <div class="content __dynamicTabWrapper">
                <app-table ref="mainTable" :isShowBtnsArea='false' :multable="false" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                    <div slot="tableTopAres">
                        <div style="color: #909399; margin-left: 10px; margin-top: 10px;">
                            提示：服务中心Banner至多显示5张轮播图
                        </div>
                    </div>
                    <template slot="ImageUrl" slot-scope="scope">
                        <div class="img">
                            <div class="img-wrapper">
                                <img class="banner-img" :src="scope.row.ImageUrl" :alt="scope.row.Title">
                            </div>
                        </div>
                    </template>

                    <template slot="Status" slot-scope="scope">
                        <span
                            class="item-status"
                            :style="{
                                backgroundColor: getStatusObj(scope.row.Status)
                                .color
                            }"
                            >
                            {{ scope.row.Status | displayFilter }}
                        </span>
                    </template>

                    <!-- 表格查询条件区域 -->
                    <template slot="conditionArea">
                        <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                            <template slot="Keywords">
                                <el-input style="width: 100%;" 
                                    placeholder="搜索标题名称"
                                    @clear='getList'
                                    v-antiShake='{
                                        time: 300,
                                        callback: () => {
                                            getList()
                                        }
                                    }' 
                                    clearable 
                                    v-model="listQuery.Keywords"
                                ></el-input>
                            </template>

                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
                            </template>
                        </app-table-form>
                    </template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button v-if="rowBtnIsExists('btnCloseAndOpen')" @click="handleChangeStatus(scope.row)" :type="1" :text='scope.row.Status == 1 ? "关闭" : "启用"'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1" text='编辑'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleTableDelete(scope.row)" :type="3" text='删除'></app-table-row-button>
                    </template>
                </app-table>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>
    <create-page
        @closeDialog="closeDialog"
        @saveSuccess="handleSaveSuccess"
        :dialogFormVisible="dialogFormVisible"
        :dialogStatus="dialogStatus"
        :id="id"
        @reload="getList"
    ></create-page>

    <sort-page
        v-if="dialogSortFormVisible"
        @closeDialog="closeSortDialog"
        @saveSuccess="handleSortSaveSuccess"
        :dialogFormVisible="dialogSortFormVisible"
        @reload="getList"
    ></sort-page>
</div>
</template>

<script>

import indexPageMixin from "@/mixins/indexPage";
import * as banner from "@/api/maintenanceCenter/banner";
import createPage from './create'
import sortPage from './sort'
import { vars } from './vars'


export default {
    name: "banners-mgmt",
    mixins: [indexPageMixin],
    components: {
        createPage,
        sortPage,
    },
    props: {},
    filters: {
        displayFilter(status) {
            let obj = vars.status.find(s => s.value == status)
            if(obj) {
                return obj.label
            }
            return status
        }
    },
    computed: {
    },
    watch: {
    },
    created() {
        this.getList()
    },
    data() {
        return {
            layoutMode: 'simple',
            displayTypes: vars.status,

            tableSearchItems: [{
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },
            ],

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            dialogSortFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "ImageUrl",
                        label: "轮播缩略图",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "Title",
                        label: "标题名称",
                    },
                },
                {
                    attr: {
                        prop: "Status",
                        label: "状态",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "RedirectUrl",
                        label: "跳转URL",
                        // sortable: "custom",
                    },
                },
                {
                    attr: {
                        prop: "Describe",
                        label: "备注",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "ReadCount",
                        label: "阅读量",
                    },
                },
            ],
            listQuery: {
                Keywords: "",
                Status: 1,
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

        };
    },
    methods: {
        getStatusObj(status) {
            return vars.status.find(s => s.value == status) || {};
        },
        onBtnClicked: function(domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog("create");
                break;
                case "btnSort":
                    this.handleSortDialog()
                break;
                case "btnExport":
                break;
                case "btnParamSetting":
                break;
                default:
                break;
            }
        },
        handleSortChange({column, prop, order}) {
            // this.sortObj = {
            //     prop,
            //     order,
            // };
            this.getList();
        },
        handleTagsChange(){
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        //获取成员列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            // postData = this.assignSortObj(postData);
            banner
                .getList(postData)
                .then((res) => {
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                    this.listLoading = false;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
        },

        // 多行删除
        handleTableDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id)
            } else {
                ids.push(rows.Id)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                banner.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },

        // 弹出添加框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        handleChangeStatus(row) {
            let tip = ''
            if(row.Status == 1) {
                tip = '关闭'
            }else{
                tip = '启用'
            }
            this.$confirm(`是否确认${tip}?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let postDatas = {
                    Id: row.Id,
                    Status: row.Status == 1 ? 2 : 1 //1：启用；2：关闭
                }
                banner.changeStatus(postDatas).then(() => {
                this.$notify({
                    title: "成功",
                    message: "修改成功",
                    type: "success",
                    duration: 2000
                });
                this.getList();
                });
            });
        },
        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        onResetSearch() {
            this.listQuery.Keywords = "";
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },

        handleSortDialog() {
            this.dialogSortFormVisible = true;
        },
        closeSortDialog() {
            this.dialogSortFormVisible = false;
        },
        handleSortSaveSuccess(_formData) {
            this.getList();
            this.closeSortDialog();
        },

    },
};
</script>

<style lang="scss" scoped>
.content{
    padding-top: 0!important;
    .btns-wrapper{
        button{
            margin-left: 4px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}
// .treeBox {
//     width: 100%;
//     height: calc(100% - 10px);
//     margin-top: 10px;

//     .elInput {
//         width: 230px;
//         margin-left: 10px;
//     }

//     .elTree {
//         height: calc(100% - 38px);
//         overflow: auto;
//         margin-top: 10px;
//         padding-bottom: 10px;
//     }
// }

.pageWrapper {
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .content {
        padding: 10px;
        padding-right: 0;
        padding-left: 0;

        .opt-wrapper {
            box-sizing: border-box;
            border-bottom: 1px solid #dcdfe6;
            padding-bottom: 10px;
        }
    }

}

.img{
    width: 200px;
    box-sizing: border-box;
    padding: 10px;
    .img-wrapper{
        overflow: hidden;
        height: 0;
        // width: 200px;
        padding-bottom: 38%; /* 图片的高/宽 */
        .banner-img{
            width: 100%
        }
    }
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

</style>
