<template>
    <div>
        <el-button type="text" @click="handleAdd">编辑规则</el-button>
        <el-dialog title="规则" :visible.sync="dialogVisible" width="800px" :before-close="handleClose" :append-to-body="true">
            <div v-for="(con, idx) in datas" :key="idx">
                {{ datas[idx].consName }}<el-button type="text" @click="handleCons(datas[idx].consName)">新增</el-button>
                <el-table :data="datas[idx].consList" border style="width: 100%">
                    <el-table-column prop="key" label="关联控件名称">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.key" placeholder="请输入关联控件名称"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="value" label="关联控件值">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.value" placeholder="请输入关联控件值"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120px">
                        <template slot-scope="scope">
                            <!-- <el-input v-model="scope.row.value" placeholder="请输入关联控件值"></el-input> -->
                            <el-button type="text" @click="handleRemove(datas[idx].consList, scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div slot="footer">
                <el-button size="mini" @click="dialogVisible = false">取消</el-button>
                <el-button size="mini" type="primary" @click="createData">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'key-value-list',
    model: {
        prop: 'list',
        event: 'change'
    },
    props: {
        list: {
            type: [Array, String],
            // default: []
        }
    },
    created() {
        let tmp = []
        if(this.list) {
            tmp = JSON.parse(JSON.stringify(this.list))
        }
        this.datas = tmp
    },
    data () {
        return {
            datas: [],
            dialogVisible: false,

        }
    },
    methods: {
        handleClose() {

        },
        handleCons(consName) {
            let list = this.datas.find(s => s.consName == consName)
            list.consList.push({key: '', value: ''})
        },
        handleAdd() {
            this.openDialog()
        },
        createData() {
            let emitDatas = JSON.parse(JSON.stringify(this.datas))
            this.$emit('change', emitDatas)
            this.closeDialog()
        },
        handleRemove(list, idx) {
            list.splice(idx, 1)
        },
        openDialog() {
            this.dialogVisible = true
        },
        closeDialog() {
            this.dialogVisible = false
        },        
    },
}
</script>

<style scoped>

</style>