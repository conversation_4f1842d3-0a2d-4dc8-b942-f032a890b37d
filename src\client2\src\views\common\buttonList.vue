<template>
  <div class="buttonBox cl" :class="theme">
    <span
      class="fl pointer"
      :class="{ active: activeIndex == index }"
      v-for="(bld, index) in btnListData"
      @click="handleClick(bld, index, true)"
      :key="index"
    >
      {{ bld.label }}
      <slot :obj="bld"></slot>
    </span>
  </div>
</template>

<script>
export default {
  model: {
    prop: "modelVal", //指向props的参数名
    event: "change", //事件名称
  },
  props: ["btnListData", "modelVal", "theme"],
  name: "",
  components: {},
  filters: {},
  watch: {
    modelVal(val) {
      let idx = this.btnListData.findIndex(s => s.value == val);
      if (idx > -1) {
        this.setMonth(idx);
      }
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.btnListData.forEach((v, i) => {
      if (v.value == this.modelVal) {
        this.handleClick(v, i);
      }
    });
  },
  methods: {
    //isManual 是否手动点击触发
    handleClick(d, index, isManual = false) {
      this.activeIndex = index;
      this.$emit("change", d.value, isManual);
    },
    setMonth(d) {
      this.activeIndex = d;
    },
  },
};
</script>

<style lang="scss" scoped>
.buttonBox {
  span {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    line-height: 28px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    padding: 0 8px;
    margin-right: 6px;
  }
  span.active {
    background: $color-primary;
    border-color: $color-primary;
    color: white;
  }
  span:hover {
    border-color: $color-primary;
  }

  &.dark {
    color: $text-primary;
    span {
      background: $border-color-base;
      border: 1px solid $bg-color-3;
    }

    span.active {
      background: $color-white;
      color: $text-primary;
    }
  }
}
</style>
