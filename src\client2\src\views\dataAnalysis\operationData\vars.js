let colorsTemp = ['#3D73DD', '#E32B06', '#88d1ea', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2']
export const colors = colorsTemp

//饼状
export const pieEchartOptionTemp = {
    tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)',
        // formatter: function (params, ticket, callback) {

        //     var showHtm="";
        //     var index = params.name.lastIndexOf('(');
        //     if(index == -1){
        //         index=params.name.length;
        //     }
        //     var name =params.name.substring(0,index)
        //     showHtm=name+': '+params.value+' ('+params.percent+'%)';
        //     return showHtm;
        // }
    },
    legend: {
        left: 'center',
        bottom: '0',
        data: [], //['姓名', '身份证号', '手机号', '家庭住址', 'MEID', 'IMSI']
        // selected: {
        //   '姓名': false
        // },
    },
    calculable: true,
    series: [{
        name: '',
        type: 'pie',
        minAngle: 15,//最小角度
        startAngle: 270, //起始角度
        radius: '50%',
        center: ['50%', '40%'],
        data: [], // { value: 100, name: '大庆', label:{show:true,position:'inside'} }
        emphasis: {
        itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
        },
        labelLine: {
            normal: {
                // show: false,
                length: 3
            },
        },
        // 设置值域的标签
        label: {
            normal: {
                position: 'inner',  // 设置标签位置，默认在饼状图外 可选值：'outer' ¦
                formatter: "{d}%",
            },
        }
    }],
    color: [],
    
}

//进度圆环
export const progressEchartOptionTemp = {
    title: {
        show: true, //显示策略，默认值true,可选为：true（显示） | false（隐藏）
        text: '', //主标题文本，'\n'指定换行
        x: 'center',
        y: 'bottom',
        textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
            fontFamily: 'Arial, Verdana, sans...',
            fontSize: 12,
            fontStyle: 'normal',
            fontWeight: 'normal',
        },
    },
    tooltip: {
        trigger: "item",
        formatter: "{d}%",
        show: false,
    },
    legend: {
        selectedMode: false,
        show: false,
        left: 'center',
        bottom: '10',
    },
    series: [
    {
        name: '进度',
        type: 'pie',
        radius: ['50%', '65%'],
        center: ["50%", "50%"],
        avoidLabelOverlap: true,
        hoverAnimation: false,
        label: {
            show: true,
            position: 'center'
        },
        labelLine: {
            normal: {
                show: false,
            },
        },
        data: [
        {
            // value: data,
            // name: `${data}%`,
            selected: false,
            itemStyle: {
                color: "#FF974C",
            },
        },
        {
            // value: 100-data,
            name: "",
            itemStyle: {
                color: "#E9EEF4",
            },
            label:{
                normal:{
                    show:false,

                }
            }
        },
        ]
    }
    ]
}

//柱状
export const barOption = {
    legend: {
        data: [],
        top: 10,
        show: false,
    },
    grid:{
        top: 20,
        right: 20,
        bottom: 40,
        left: 40
    },
    xAxis: {
        type: 'category',
        axisLine: {
            lineStyle: {
            color: '#ccc', // 颜色
            }
        },
        axisLabel: {
            color:'black',
            interval:0,
            rotate: 25,
        },
        data: []
    },
    yAxis: {
        type: 'value',
        splitLine:{
            show: true,
            lineStyle:{
                type:'dashed'
            }
        },//去除网格线
        axisLine: {
            lineStyle: {
            color: '#ccc', // 颜色
            }
        },
        axisLabel: {
            color:'black'
        },
    },
    series: [
        {
            name: '',
            data: [],//120, 200, 150, 80, 70, 110, 130
            type: 'bar',
            barWidth: 20,
            // showBackground: true,
            label: {
                show: true,
                position: 'outside'
            },
            itemStyle:{
                normal:{
                    color:'#3D73DD'
                }
            }
        }
    ]
}

//词云图
export const wordCloudOption = {
    series: [{
        type: 'wordCloud',
        sizeRange: [12, 40],
        rotationRange: [-45, 90],//数据翻转范围
        rotationStep: 45,
        gridSize: 8,
        shape: 'pentagon',
        width: '100%',
        height: '100%',
        textStyle: {
            normal: {
                color: function() {
                    let colors2 = ['#fda67e', '#81cacc', '#cca8ba', "#88cc81", "#82a0c5", '#fddb7e', '#735ba1', '#bda29a', '#6e7074', '#546570', '#c4ccd3'];
                    return colors2[parseInt(Math.random() * 10)];
                }
            },
            emphasis: {
                shadowBlur: 10,
                // shadowColor: '#333'
            }
        },
        data: [] //[{"name":"男神","value":2.64}]
    }]
}

export const dateTypeEnum1 = [{
    value: 1, label: '近三月',
},{
    value: 2, label: '上月',
},{
    value: 3, label: '本月',
}]

export const dateTypeEnum2 = [{
    value: 4, label: '去年',
},{
    value: 5, label: '今年',
}]

