<template>
  <div>
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="600"
      :width="600"
    >
      <template slot="body">
        <el-form
          ref="formData"
          :rules="rules"
          :model="formData"
          label-position="right"
          :label-width="labelWidth"
        >
          <div class="wrapper" v-loading="loading">
            <div style="margin-bottom: 10px;">
              <el-button size="mini" type="primary" @click="handleAdd">添加新工序</el-button>
            </div>
            <div class="item" v-for="(item, idx) in formData.list" :key="idx">
              <div class="item-title-wrapper">
                <div class="item-title">工序{{ idx + 1 }}</div>
                <div class="item-btns">
                  <i v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></i>
                  <i
                    v-show="idx < formData.list.length - 1"
                    class="el-icon-bottom"
                    title="下移"
                    @click="move('down', idx)"
                  ></i>
                  <i class="el-icon-delete-solid" title="删除" @click="handleRemove(item)"></i>
                </div>
              </div>
              <el-row class="item-content">
                <el-col :span="24" class="cus-textarea-wrapper">
                  <el-form-item
                    label="工序名称"
                    :prop="'list.' + idx + '.Name'"
                    :rules="{required: true, message: '工序名称不能为空', trigger: 'blur'}"
                  >
                    <el-input
                      maxlength="50"
                      v-model.trim="formData.list[idx].Name"
                      clearable
                      :disabled="!editable"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="工期设置">
                    <el-date-picker
                      v-model="formData.list[idx].Range"
                      type="datetimerange"
                      align="right"
                      unlink-panels
                      range-separator="-"
                      start-placeholder
                      end-placeholder
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 100%;"
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item
                    label="工序负责人"
                    :prop="'list.' + idx + '.EmployeeList'"
                    :rules="{required: true, message: '工序负责人不能为空', trigger: 'change'}"
                  >
                    <emp-selector
                      :isShowClearButton="true"
                      :sourceType="'onlyAuthUsers'"
                      :multiple="true"
                      :showType="2"
                      key="service-users"
                      :list="formData.list[idx].EmployeeList"
                      @change="(users) => handleChangeUsers(item, users)"
                    ></emp-selector>
                  </el-form-item>

                  <el-form-item
                    label="重点关注"
                    :prop="'list.' + idx + '.IsFoucs'"
                  >
                    <el-switch v-model="formData.list[idx].IsFoucs"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as impMgmt from "@/api/implementation/impManagement2";
import empSelector from "../../../common/empSelector";
export default {
  name: "procedure-page",
  directives: {},
  components: {
    empSelector
  },
  props: {
    dialogStatus: {
      //create、update、detail
      type: String
    },
    //实施地区id（新增必传）
    implementationRegionalId: {
      type: String,
      required: true
    }
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail" && this.dialogStatus != "approval";
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "工序管理";
      } else if (this.dialogStatus == "update") {
        return "工序管理";
      } else if (this.dialogStatus == "detail") {
        return "工序管理";
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.resetFormData();
          if (this.implementationRegionalId) {
            this.getProcedures();
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      rules: {},
      loading: false,
      disabledBtn: false,
      labelWidth: "100px",
      formData: {
        list: [
          // {
          //     Id: null,
          //     Name: '',
          //     ImplementationRegionalId: this.implementationRegionalId,
          //     EmployeeList: [],
          //     Range: [],
          //     StartTime: null,
          //     EndTime: null,
          // }
        ]
      }
    };
  },
  methods: {
    handleAdd() {
      this.formData.list.push({
        Id: null,
        Name: "",
        ImplementationRegionalId: this.implementationRegionalId,
        EmployeeList: [],
        Range: [],
        StartTime: null,
        EndTime: null,
        IsFoucs: false
      });
    },
    move(direction, currIdx) {
      if (
        (direction == "up" && currIdx > 0) ||
        (direction == "down" && currIdx < this.formData.list.length - 1)
      ) {
        let currRow = JSON.parse(JSON.stringify(this.formData.list[currIdx]));
        let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
        this.formData.list.splice(currIdx, 1);
        this.formData.list.splice(targetIdx, 0, currRow);
      }
    },
    handleRemove(row) {
      if(row) {
        if(row.ImplementationEquipmentCount > 0) {
          this.$message.error({
            title: '提示',
            message: '请将该工序下的设备转移到其他工序',
            duration: 2000
          })
        }else{
          this.$confirm("是否确认删除当前工序?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            var index = this.formData.list.indexOf(row);
            if (index !== -1) {
              this.formData.list.splice(index, 1);
            }
          });
        }
      }
    },
    resetFormData() {
      let temp = {
        // Id: '', //项目ID
        formData: {
          list: []
        }
      };
      this.formData.list = Object.assign({}, this.formData.list, temp);
    },
    handleChangeUsers(row, users) {
      row.EmployeeList = users;
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData.list));
          postData = postData.map((s, idx) => {
            s.StartTime = null
            s.EndTime = null
            if(s.Range && s.Range.length == 2) {
              s.StartTime = s.Range[0];
              s.EndTime = s.Range[1];
            }
            s.Name = s.Name.trim()           
            s.EmployeeIdList = s.EmployeeList.map(p => p.EmployeeId);
            s.OrderIndex = (idx + 1) * 10;
            delete s.Range;
            delete s.EmployeeList;
            return s;
          });

          let names = postData.map(s => s.Name)
          if((new Set(names)).size != names.length) {
            this.$message.error(`工序名称不能重复!`);
            return false
          }

          let tmep = {
            ImplementationRegionalId: this.implementationRegionalId,
            ImplementationProcedureEditRequestModels: postData
          }

          this.disabledBtn = true;
          impMgmt
            .AddOrEditProcedureList(tmep)
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        }
      });
    },
    //获取工序列表
    getProcedures() {
      this.loading = true;
      this.formData.list = [];
      impMgmt
        .getProcedureList({
          ImplementationRegionalId: this.implementationRegionalId
        })
        .then(res => {
          this.loading = false;
          this.formData.list = res.map(s => {
            if (s.StartTime && s.EndTime) {
              s.Range = [s.StartTime, s.EndTime];
            } else {
              s.Range = [];
            }
            return s;
          });
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  min-height: 100px;
  .item {
    border: 1px solid #ebeef5;
    border-radius: 10px;
    margin-bottom: 10px;
    // padding-top: 10px;
    .item-title-wrapper {
      display: flex;
      height: 24px;
      line-height: 24px;
      margin-bottom: 4px;
      padding: 0 10px;
      border-bottom: 1px solid #ebeef5;
      .item-title {
        flex: 1;
        margin-bottom: 4px;
      }
      .item-btns i {
        margin-left: 5px;
        cursor: pointer;
      }
    }
    .item-content {
      padding-right: 10px;
    }
  }
}
</style>
