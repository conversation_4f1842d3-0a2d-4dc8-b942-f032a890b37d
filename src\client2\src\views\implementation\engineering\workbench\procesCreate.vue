<template>
    <div>
        <app-dialog
        :title="pageTitle"
        ref="appDialogRef"
        v-bind="$attrs"
        v-on="$listeners"
        :maxHeight="700"
        :width='600'
        >
            <template slot="body">
                <el-form
                    :rules="rules"
                    ref="formData"
                    :model="formData"
                    label-position="right"
                    :label-width="labelWidth"
                >
                <div class="wrapper" v-loading='loading'>
                    <div class="up-panel">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="事项名称" prop="Name">
                                    {{ formData.Name }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="进度" prop="Progress" class="slider-label">
                                    <el-slider :disabled="!editable" v-model="formData.Progress" :show-input="true"></el-slider>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="备注" prop="Remark">
                                    <el-input :disabled="!editable" type="textarea" maxlength="1000" :rows="5" v-model="formData.Remark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div>
                        <div>附件:</div>
                        <div>
                            <app-uploader
                                accept="all"
                                :readonly="!editable"
                                :fileType="3"
                                :max="10000"
                                :value="formData.AttachmentList"
                                :fileSize="1024 * 1024 * 500"
                                :minFileSize="100 * 1024"
                                @change="handleFilesUpChange"
                                ref="appuploader"
                            ></app-uploader>
                        </div>
                    </div>
                </div>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as impMgmt from "@/api/implementation/impManagement2"
export default {
    name: "proces-create",
    directives: {},
    components: {
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        procesId: {
            type: String,
            required: true
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.procesId) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "跟进实施事项";
            } else if (this.dialogStatus == "detail") {
                return "详情";
            }
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            loading: false,
            disabledBtn: false,
            rules: { 
                DemandName: { fieldName: "需求名称", rules: [{ required: true }]},
                AcceptanceDescription: { fieldName: "验收标准", rules: [{ required: true }]}
            },
            labelWidth: "80px",
            formData: {
                Id: "", //
                Name: "", //
                Progress: 0, //进度
                Remark: '',//备注
                AttachmentList: [],
            }
        };
    },
    methods: {
        resetFormData() {
            let temp = {
                Id: "", //
                Name: "", //
                Progress: 0, //进度
                Remark: '',//备注
                AttachmentList: [],
            };
            this.formData = Object.assign({}, this.formData, temp);
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    //提交数据保存
                    postData.AttachmentIdList = postData.AttachmentList.map(s => s.Id);

                    this.disabledBtn = true
                    impMgmt.editImpItem(postData).then(res => {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.disabledBtn = false
                        this.$refs.appDialogRef.createData();
                    }).catch(err => {
                        this.disabledBtn = false
                    });
                }
            })
        },
        getDetail() {
            this.loading = true
            impMgmt.getImpItemDetails({ id: this.procesId }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);
            }).catch(err => {
                this.loading = false
            });
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="css" scoped>
.wrapper >>> .el-form-item{
    /* margin-bottom: 6px; */
}

.up-panel{
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
}

.slider-label >>> .el-form-item__label{
    line-height: 38px!important;
}
</style>
