<template>
  <div class="wrap">
    <div class="reg-form-wrap">
      <h3 class="title" @dblclick="adminLogin()">
        <div class="comp-name">
          {{ websiteTitle }}
          <div class="comp-name-tag">beta</div>
        </div>
      </h3>
      <div style="display: flex; align-items: center; margin: 14px 0 20px 0;">
        <h4 class="sub-title" style="margin: 0; margin-right: 5px; font-size: 20px;">欢迎登录</h4>
        <el-tag effect="dark" size="small" v-show="isBetaVersion">体验版</el-tag>
      </div>
      <div class="form-wrap">
        <el-form :rules="rules" ref="dataForm" :model="loginForm" label-position="top" label-width="100px">
          <!-- <el-form-item size="small" :label="'所属企业'" prop="EnterpriseId">
            <el-select style="width: 100%;" v-model="loginForm.EnterpriseId" placeholder="请选择企业" v-loading="selectEnterpriseLoading" element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)" :loading="selectEnterpriseLoading">
              <el-option v-for="item in enterprises" :key="item.EnterpriseID" :label="item.EnterpriseName" :value="item.EnterpriseID"></el-option>
            </el-select>
          </el-form-item> -->

          <el-row :gutter="2">
            <!-- <el-col :span="9">
              <el-form-item size="small" :label="'用户名'" prop="enterpriseCode">
                <el-input placeholder="请输入企业编号" maxlength="20" v-model.trim="loginForm.enterpriseCode" ref="enterpriseCode" type="text"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item size="small" :label="'用户名'" prop="username">
                <el-input v-model.trim="loginForm.username" maxlength="50" ref="username" type="text" placeholder="请输入工号/邮箱/手机号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item size="small" :label="'密码'" prop="password">
            <el-input v-model.trim="loginForm.password" @keyup.enter.native="handleLogin" ref="password" type="password"></el-input>
          </el-form-item>
        </el-form>
        <div class="btns">
          <el-button type="primary" class="btn-login" :loading="postLoading" @click="handleLogin">登 录</el-button>
        </div>
        <div class="sub-btns">

          <app-table-row-button :underline="false" :text="isBetaVersion ? '返回正式版' : '进入体验版'" @click="handleNav"></app-table-row-button>

          <!-- <el-button style="color: #409eff; font-weight: 600; font-size: 14px;" @click="handleNav" type="text">
            {{ isBetaVersion ? '返回正式版' : '进入体验版' }} 
          </el-button> -->

          <el-checkbox v-model="rememberAccount">记住账号</el-checkbox>
          <!-- <el-button @click="handleToReg" type="text">没账号？去注册</el-button> -->

        </div>
        <!-- <div style="margin-top: 10px;">
          
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { websitEnv } from "@/utils/env";
import { setCookie, getCookie } from "@/utils/auth";
import { getUserInfo } from "@/utils/auth";
import indexPageMixin from "@/mixins/indexPage";
import loginPageMixins from "@/mixins/loginPageMixins";

import * as systemEmployee from '@/api/personnelManagement/systemEmployee'
import { regs } from "@/utils/regs";
import * as ent from "@/api/enterprise";

export default {
  name: "login",
  mixins: [indexPageMixin, loginPageMixins],
  data() {
    return {
      accountKey: "account",
      rememberAccount: true,
      websiteTitle: websitEnv.websiteTitle,
      loginForm: {
        username: "",
        password: "",
        experiencePage: false, // 默认非体验版
        // enterpriseCode: "esn001"
        // EnterpriseId: ""
      },
      rules: {
        username: { fieldName: "用户名", rules: [{ required: true }] },
        password: { fieldName: "密码", rules: [{ required: true }] },
        // enterpriseCode: { fieldName: "企业编号", rules: [{ required: true }, { reg: regs.LettersAndNumbers }] }
        // EnterpriseId: { fieldName: "企业", rules: [{ required: true }] }
      },
      enterprises: [],
      selectEnterpriseLoading: false,
      postLoading: false,
      pwdType: "password"
    };
  },
  created() {
    // this.getEnts();
    this.rules = this.initRules(this.rules);

    console.log(
      "你在电脑前看这段文字，\r\n写文字的人在佳运通等你。\r\nN年前你来到了这个世界，\r\nN年后你想改变世界。\r\n\r\n期待你脚踏祥云，\r\n与佳运通一起改变世界。"
    );
    console.log(
      "%c%s",
      "color: red;",
      "\r\n\r\n佳运通官网：http://www.jiayuntong.com\r\n\r\n"
    );
  },
  mounted() {
    this.loginForm.username = getCookie(this.accountKey) || "";
  },
  methods: {
    handleNav() {
      if(this.isBetaVersion) {
        this.$router.push('/login')
      }else{
        this.$router.push('/betalogin')
      }
    },
    // getEnts() {
    //   this.selectEnterpriseLoading = true;
    //   ent.getAllEnterprise().then(res => {
    //     this.enterprises = res.map(e => {
    //       return {
    //         EnterpriseID: e.EnterpriseID,
    //         EnterpriseName: e.EnterpriseName
    //       };
    //     });
    //     this.selectEnterpriseLoading = false;
    //   });
    // },
    adminLogin() {
      this.$router.push({ path: "/adminlogin" });
    },
    showPwd() {
      if (this.pwdType === "password") {
        this.pwdType = "";
      } else {
        this.pwdType = "password";
      }
    },
    handleToReg() {
      this.$router.push({ name: "register" });
    },
    handleLogin() {
      this.postLoading = true;
      this.$refs["dataForm"].validate(valid => {
        if (!valid) {
          this.postLoading = false;
        }

        if (valid) {
          
          this.loginForm.experiencePage = this.isBetaVersion

          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.postLoading = false;
              //登录成功，如果是员工，则获取员工信息
              let currentUser = getUserInfo();
              if (currentUser.employeeid) {
                systemEmployee.detail({ id: currentUser.employeeid }).then(res => {
                  currentUser.empName = res.Name;
                  currentUser.empNumber = res.Number;
                  currentUser.empAvatar = res.Avatar;
                  currentUser.phone = res.Mobile
                  currentUser.deptName = res.DepartmentName
                  currentUser.deptId = res.DepartmentId

                  this.$store.commit("SET_USERINFO", currentUser);
                  this.$router.push(websitEnv.mainPage.url)
                });
              }

              if (this.rememberAccount) {
                setCookie({
                  key: this.accountKey,
                  value: this.loginForm.username,
                  expires: 999
                });
              } else {
                setCookie({ key: this.accountKey, value: "" });
              }
              
              this.$router.push({ path: websitEnv.mainPage.url });
            })
            .catch(() => {
              this.postLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style src="./login-and-reg.css" scoped></style>
<style scoped>
.wrap {
  /* margin-top: 120px; */
}

.form-wrap >>> .el-form-item__label:before {
  content: "" !important;
}

.sub-btns >>> .el-checkbox {
  margin-right: 0 !important;
}

.sub-btns{
  display: flex;
  justify-content: space-between;
}
</style>

<style>
.wrap .el-loading-spinner {
  top: 50% !important;
  margin-top: -15px !important;
  padding-right: 10px !important;
  /* float: right; */
  text-align: right !important;
  width: 100% !important;
  /* text-align: center; */
  position: absolute !important;
}
.wrap .el-loading-mask {
  position: absolute !important;
  z-index: 2000 !important;
  background-color: rgba(247, 247, 247, 0.9) !important;
  margin: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  -webkit-transition: opacity 0.3s !important;
  transition: opacity 0.3s !important;
  border-radius: 5px !important;
  border: 1px solid #dddfe6 !important;
}
.wrap .el-loading-spinner i {
  color: #737373 !important;
  font-size: 16px !important;
}

.comp-name{
  font-size: 30px;
  position: relative;
  display: inline-block;
}

.comp-name-tag{
  position: absolute;
  top: -10px;
  right: -30px;
  background: #e3e3e3;
  color: #fff;
  font-size: 12px;
  padding: 2px;
  border-radius: 2px;
}

</style>
