
<template>
  <div class="b-tree" ref="scroller" :style="{ height: option.height + 'px' }" @scroll="handleScroll">
    <div class="b-tree__phantom" :style="{ height: contentHeight }"></div>
    <div class="b-tree__content" :style="{ transform: `translateY(${offset}px)` }">
      <div v-for="(item, index) in visibleData" :key="item.Id" class="el-tree-node__content" :style="{paddingLeft: 18 * (item.level - 1) + 'px', height: option.itemHeight + 'px'}">
        <i :class="item.expand ? 'expanded el-tree-node__expand-icon el-icon-caret-right' : 'el-tree-node__expand-icon el-icon-caret-right'" @click="toggleExpand(item)" v-if="item.children && item.children.length"/>
        <span v-else style="margin-right: 5px"></span>
        <label class="el-checkbox">
                <!-- is-checked -->
                <!-- is-disabled -->
                <!-- is-indeterminate -->
            <span class="el-checkbox__input" :class="[{ 'is-checked': item.checked, 'is-disabled': disabledFn(item) }]">
                <span class="el-checkbox__inner" @click.stop="disabledFn(item) ? () => {} : handleCheckedNode(item)"></span>
                <input type="checkbox" class="el-checkbox__original" :value="item.Id">
            </span>
        </label>
        <slot :item="item" :index="index"></slot>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 实现步骤：
 * 1：虚拟滚动，参考：https://github.com/jayZOU/vue-big-tree；
 * 2：dom结构、css样式、api提供 参考 element el-tree 组件；
 */
let lastTime = 0;
export default {
  name: "vueBigTree",
  props: {
    tree: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    defaultExpand: {
      type: Boolean,
      required: false,
      default: false
    },
    timeout: { //刷新频率
      type: Number,
      default: 17
    },
    //默认选中项
    defaultCheckedKeys: {
        type: Array,
        default: () => {
            return []
        }
    },
    disabledList: {
        type: Array,
        default: () => {
            return []
        }
    },
    option: {
      // 配置对象
      type: Object,
      required: true,
      default() {
        return {
          height: 500, //滚动容器的高度
          itemHeight: 25 // 单个item的高度
        }
      }
    }
  },
  watch: {
    defaultCheckedKeys: {
        handler(val) {
            this.checkeds = JSON.parse(JSON.stringify(val || []))
        },
        immediate: true
    }
  },
  data() {
    return {
      offset: 0, // translateY偏移量
      contentHeight: "0px",
      visibleData: [],
      checkeds: [],
      checkedNodes: [],
    };
  },
  computed: {
    flattenTree() {
      const flatten = (list, childKey = "children", level = 1, parent = null, defaultExpand = true) => {
        let arr = [];
        list.forEach(item => {
          item.level = level;
          if (item.expand === undefined) {
            item.expand = defaultExpand;
          }
          if (item.visible === undefined) {
            item.visible = true;
          }
          if(this.checkeds.findIndex(s => s == item.Id) > -1) {
              item.checked = true
          }else{
              item.checked = false
          }
          if (!parent.visible || !parent.expand) {
            item.visible = false;
          }
          // if(this.checkeds.findIndex(s => s == parent.Id) > -1) {
          //     item.checked = true
          // }else{
          //     item.checked = false
          // }

          item.parent = parent;
          arr.push(item);
          if (item[childKey]) {
            arr.push(
              ...flatten(
                item[childKey],
                childKey,
                level + 1,
                item,
                defaultExpand
              )
            );
          }
        });
        return arr;
      };
      return flatten(this.tree, "children", 1, {
        level: 0,
        visible: true,
        expand: true,
        children: this.tree,
        disabled: false
      });
    },
    visibleCount() {
      return Math.floor(this.option.height / this.option.itemHeight);
    }
  },
  mounted() {
    this.updateView();
  },
  methods: {
    updateView() {
      this.getContentHeight();
      this.$emit("update", this.tree);
      this.handleScroll();
    },
    handleScroll() {
      let currentTime = +new Date()
      if(currentTime - lastTime > this.timeout) {
        this.updateVisibleData(this.$refs.scroller.scrollTop);
        lastTime = currentTime
      }
    },
    updateVisibleData(scrollTop = 0) {
      let start = Math.floor(scrollTop / this.option.itemHeight) - Math.floor(this.visibleCount / 2);
      start = start < 0 ? 0 : start;
      const end = start + this.visibleCount * 2;
      const allVisibleData = (this.flattenTree || []).filter(
        item => item.visible
      );
      this.visibleData = allVisibleData.slice(start, end);
      this.offset = start * this.option.itemHeight;
    },
    getContentHeight() {
      this.contentHeight =
        (this.flattenTree || []).filter(item => item.visible).length *
          this.option.itemHeight +
        "px";
    },
    toggleExpand(item) {
      const isExpand = item.expand;
      if (isExpand) {
        this.collapse(item, true); // 折叠
      } else {
        this.expand(item, true); // 展开
      }
      this.updateView();
    },
    //展开节点
    expand(item) {
      item.expand = true;
      this.recursionVisible(item.children, true);
    },
    //折叠节点
    collapse(item) {
      item.expand = false;
      this.recursionVisible(item.children, false);
    },
    //折叠所有
    collapseAll(level = 1) {
      this.flattenTree.forEach(item => {
        item.expand = false;
        if (item.level != level) {
          item.visible = false;
        }
      });
      this.updateView();
    },
    //展开所有
    expandAll() {
      this.flattenTree.forEach(item => {
        item.expand = true;
        item.visible = true;
      });
      this.updateView();
    },
    //递归节点
    recursionVisible(children, status) {
      children.forEach(node => {
        node.visible = status;
        if (node.children) {
          this.recursionVisible(node.children, status);
        }
      });
    },
    recursionChecked(children, status) {
      children.forEach(node => {
        node.checked = status;
        if (node.children) {
          this.recursionChecked(node.children, status);
        }
      });
    },
    handleCheckedNode(item) {
        item.checked = !item.checked
        // if(item.checked) {
        //     this.checkeds.push(item.Id)
        // }else{
        //     let idx = this.checkeds.findIndex(s => s == item.Id)
        //     this.checkeds.splice(idx, 1)
        // }
        this.checkedChildrenNodes(item.children, item.checked);
        this.updateView();
    },
    checkedChildrenNodes(children, isChecked) {
        if(children && children.length > 0) {
            children.forEach(node => {
                node.checked = isChecked;
                // let idx = this.checkeds.findIndex(s => s == node.Id)
                
                // console.log(JSON.stringify(this.checkeds));

                // if(isChecked) {
                //     if(idx == -1) {
                //         this.checkeds.push(node.Id)
                //     }
                // }else{
                //     if(idx > -1){
                //         this.checkeds.splice(idx, 1)
                //     }
                // }
                if(node.children && node.children.length > 0) {
                    this.checkedChildrenNodes(node.children, isChecked)
                }
            });
            
        }
    },
    disabledFn(data, nodeType) {
      if(this.disabledList.findIndex(r => r == data.Id) > -1) {
        return true
      }
      //只能选择第2、3、4、5级
      if(data.level <= 1) {
        return true
      }
      return false
    },


    // element el-tree 同名方法
    getCheckedNodes() {
        this.checkedNodes = []
        this._filterCheckedNodes(this.flattenTree)
        return this.checkedNodes || []
    },
    _filterCheckedNodes(children) {      
        if(children && children.length > 0) {
            children.forEach(node => {
                if(node.checked){
                    this.checkedNodes.push(node)
                }
                // if(node.children && node.children.length > 0) {
                //     this._filterCheckedNodes(node.children)
                // }
            });
            
        }
    },

  }
};
</script>


<style scoped>
.b-tree {
  position: relative;
  overflow-y: scroll;
}
.b-tree__phantom {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: -1;
}
.b-tree__content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  min-height: 100px;
}
.b-tree__list-view {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.b-tree__content__item {
  padding: 5px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: center;
  cursor: pointer;
}
.b-tree__content__item:hover,
.b-tree__content__item__selected {
  background-color: #d7d7d7;
}
.b-tree__content__item__icon {
  position: absolute;
  left: 0;
  color: #c0c4cc;
  z-index: 10;
}
.b-tree__close {
  display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  margin-right: 5px;
  border-width: 5px;
  border-color: transparent transparent transparent #c0c4cc;
  border-style: dashed dashed dashed solid;
}
.b-tree__expand {
    transform: rotate(90deg);
  /* display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  margin-right: 5px;
  border-width: 5px;
  border-color: #c0c4cc transparent transparent transparent;
  border-style: solid dashed dashed dashed; */
}
</style>