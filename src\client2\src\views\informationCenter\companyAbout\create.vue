<template>
  <div>
    <app-dialog title="关于我们" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <el-form-item label="内容描述" prop="AboutAs">
            <!-- <el-input maxlength="2000" type="textarea" :rows="8" v-model="formData.AboutAs"></el-input>
             -->
            <!-- <editor-bar v-model="formData.AboutAs" :isClear="isClear"></editor-bar> -->
            <editor-bar ref="editor" :value="formData.AboutAs" @edit="formData.AboutAs = arguments[0]"></editor-bar>

          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">

        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" type="primary" :buttonType='1'></app-button>
        <!-- <el-button @click="handleSave" type="primary">保存</el-button> -->
      </template>

    </app-dialog>
  </div>
</template>

<script>
import * as companyIntroduction from '@/api/informationCenter/companyIntroduction'
//   import EditorBar from '../../../components/WangEditor/index.vue'
import EditorBar from '@/components/QuillEditor/index.vue'

export default {
  name: "create",
  directives: {},
  components: {
    EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {

        this.isContinue = false;
      }
      if (val) {
        this.setEditorHeight()
        this.resetFormData();

        this.getDetail();

      }
    }
  },
  computed: {

  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      isClear: false,
      isContinue: false,
      rules: {

        // Honors: { fieldName: "内容", rules: [{ required: true }]},
        AboutAs: { fieldName: "内容描述", rules: [{ required: true }, {max: 20000, trigger: "blur"}]},
      },
      labelWidth: "100px",
      formData: {
        AboutAs: "",
      }
    };
  },
  methods: {
    setEditorHeight() {
      this.$nextTick(() => {
        this.$refs.editor && this.$refs.editor.setHeight(600)
      })
    },
    resetFormData() {
      let temp = {
        Id: this.id,
        AboutAs: "",
      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    handleSave() {

      this.createData();
    },
    //保存
    createData() {
      let validate = this.$refs.formData.validate();
      Promise.all([validate]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        //提交数据保存
        let result = null;

        result = companyIntroduction.edit(postData);
        result.then(res => {
          if (this.isContinue) {
            this.resetFormData();
            this.$emit("reload");
          } else { //
            this.$notify({
              //  title: '成功',
              message: '保存成功',
              type: 'success',
              duration: 2000
            })
            this.$refs.appDialogRef.createData();
          }
        });



      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    getDetail() {

      companyIntroduction.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
      });


    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
