$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;
$errorColor: #F56C6C;

// sidebar
// $menuBg:#304156;
// $subMenuBg:#1f2d3d;
// $menuHover:#001528;
$menuBg: transparent;
$subMenuBg: transparent;//子菜单背景颜色
$menuHover: transparent;//子菜单 hover 背景色

$menu-width: 180px;

$text-link-del-color: #EF6467;
$text-link-color: #1C81FD;
$text-label-color: #BBC3CD;
$text-main-color: #333333;
$text-second-color: #666666;

/*
图表颜色二十个选择
#3D78F9
#7B94FD
#A07BFD
#FF5B91
#FA5A5A
#FF7742
#FFAA00
#5AD59E
#0AC2AA
#00B7FD
#8BAEFB
#B0BFFE
#C6B0FE
#FF9DBD
#FC9C9C
#FFAD8E
#FFCC66
#9CE6C5
#6CDACC
#66D4FE
 */




/******** 安装ele主题变量定色 *********/

//主要颜色
$color-primary: #267FEB; //主色
$color-success: #32B372; //安全
$color-warning: #FFAA00; //警告
$color-danger: #F53F3F; //危险
$color-info: #909399; //信息
$color-purple: #8156EE; //紫色
$color-primary-2: #00B7FD;//青蓝色
$color-xxx: #030C33;

$color-darkred: #C31426; //暗红
$color-danger-primary: #DB0016;
$color-danger-1: #FF5A3E;
//文字颜色
$text-primary: #262C3A; //强调
$text-regular: #4E5969; //次强调
$text-secondary: #86909C;
$placeholder: #C9CDD4;

//边框颜色
$border-color-base: #F2F3F5; //浅
$border-color-light: #E5E6EB; //常规
$border-color-lighter: #DCE0E7; //深
$border-color-extra-light: #C9CDD4; //强调

//背景颜色
$color-black: #000000;
$color-white: #FFFFFF;

$bg-color-1: #F7F8FA; //浅
$bg-color-2: #F2F3F5; //常规
$bg-color-3: #E5E6EB; //深
$bg-color-4: #AFB7C5; //重
$bg-color-5: #4E5969; //强调

$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-circle: 100%;
$border-radius-zero: 0;

$box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);


//画布背景色
$bg-canvas: #f7f9fb;


/** 安装ele主题变量定色 end **/




