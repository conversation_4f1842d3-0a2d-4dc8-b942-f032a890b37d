<template style="width: 103%;margin-left: -13px;">
<el-container class="seleEmpCon">
    <div style=" width: 36%;text-align: center;">
        <div style=" border-bottom: 1px solid #ebeef5;border-right: 1px solid #ebeef5;height: 36px;padding-top: 4px;">
            <tags :items="types" v-model="TableType">  
                <template v-for="(t,  idx) in types" :slot="t.value">
                    <span :class="t.value == 1 ? 'paddingRight' : 'paddingLeft'" :key="idx">
                        {{ t.label }}    
                    </span>             
                </template>
            </tags>
        </div>
        <el-container class="treeCon" v-show='TableType=="1"' style="height: calc(100% - 36px);">
            <span style="width: 100%;padding-top: 20%;">已列出近期常用联系人列表</span>
        </el-container>

        <el-container class="treeCon" v-show='TableType=="2"' style="height: 93%;">
            <el-header class="treehead">
                <!--搜索 -->

                <!-- <el-input style="width: 100%;" placeholder="请输入员工姓名或工号" v-model='listQuery.Name' @input="valueChange($event)">
            <div slot='append'>
                <el-button type="primary" class="el-button el-button--primary el-button--mini" @click="query">查询</el-button>
            </div>
        </el-input> -->

                <div class="input-wrapper">
                    <el-input placeholder="请输入员工姓名或工号" v-model='listQuery.Name' @input="valueChange($event)"></el-input>
                    <el-button type="primary" class="el-button el-button--primary el-button--mini search-btn" @click="query">查询</el-button>
                </div>
                <!-- <el-input class="treeName" placeholder="请输入员工姓名或工号" v-model='listQuery.Name' @input="valueChange($event)">
        </el-input>
        <el-button type="primary" @click="query">查询</el-button> -->
            </el-header>
            <el-main class="treeMain">
                <!--部门树 -->
                <el-tree v-show="treeHide==false" :default-checked-keys="checkedDeptList" class="tree" :data="orgsTree" show-checkbox node-key="Id" @check='checkOrg' :default-expanded-keys="defaultExpandedKey" :props="defaultProps" ref="orgsTree">
                </el-tree>
                <span v-show="treeHide==true" class="tag-group__title treeTips">已为您查询</span>
            </el-main>
        </el-container>
    </div>

    <el-container class="empCon" style="width: 81%;">
        <!--人员table -->
        <el-main class="empMain">
            <el-table class="empTab" border fit :data="tabDatas" name="CommonEmpTab" @select-all="selectAll" @select="select" v-loading="listLoading" ref="mainTable2">
                <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable">
                </el-table-column>
                <el-table-column prop="Name" label="姓名" width="103" align="center"></el-table-column>
                <el-table-column prop="Number" label="工号" width="80" align="center"></el-table-column>
                <el-table-column prop="Sex" label="性别" width="60" align="center">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.Sex == 1" type="success">男</el-tag>
                        <el-tag v-if="scope.row.Sex == 2" type="info">女</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="Mobile" label="手机" align='center'></el-table-column>
                <el-table-column prop="DepartmentName" label="部门" align="center" show-overflow-tooltip></el-table-column>
                <el-table-column prop="JobName" label="职位" align="center" show-overflow-tooltip></el-table-column>
            </el-table>
        </el-main>
        <el-footer class="empFoot">
            <!--确认返回数据给父组件 -->
            <div class="btns-wrapper">
                <el-button class="empSub" type="primary" @click="getCheckRow">确认</el-button>
            </div>
        </el-footer>
    </el-container>
</el-container>
</template>

<script>
import {
    listToTreeSelect
} from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as systemEmployee from '@/api/personnelManagement/systemEmployee'
import * as systemTopContact from '@/api/personnelManagement/systemTopContact'
export default {
    name: 'emp-table',
    props: {
        existsUsers: { //父级组件传入和当前已选人员
            type: Array,
            default: () => {
                return []
            }
        },
        multiple: { //父级附件设置:multiple="false" ，即可设置为单选
            type: Boolean,
            default: true
        },
        visible: { //当前组件展示与否
            type: Boolean,
            default: false
        },
        condition: {
            type: Object,
            default: null
        },
        disabledList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    created() {
        var _this = this; //暂时没有找到更好的办法来判断展示单选提示和全选按钮，如果有更好的办法请联系曹思平，谢谢。
        _this.$nextTick(function () { //初次打开组件在渲染结束后初始化
            $('div[name="CommonEmpTab"] th .el-checkbox').parent().attr('name', 'checkboxDiv'); //给全选框上级元素增加标识
            _this.elCheckbox = $('div[name="CommonEmpTab"] th .el-checkbox'); //默认加载全选框，将全选框存入变量重复加载时根据选择模式进行dom操作
            if (!_this.multiple)
                $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //将全选框改成单选提示
        })
    },
    watch: {
        visible: { //展示当前组件刷新列表并根据父组件传入的已选人员进行页面选中
            handler() {
                var _this = this;
                if (_this.multiple) //展示当前组件后判断单选和多选模式
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html(_this.elCheckbox); //将全选框放入table表头
                else
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //修改单选框提示
                _this.checkedUsers = JSON.parse(JSON.stringify(_this.existsUsers)) || [];
                _this.getList();
            },
            immediate: true
        },
        "TableType"() {
            this.getList();
        },
    },
    data() {
        return {
            types: [{
                    value: 1,
                    label: "常用联系人",
                },
                {
                    value: 2,
                    label: "组织结构",
                }
            ],
            TableType: "1",
            elCheckbox: null,
            checkedUsers: JSON.parse(JSON.stringify(this.existsUsers)) || [],
            checkedDeptList: [],
            treeHide: false, //是否显示部门树结构
            orgsTree: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'label'
            },
            defaultExpandedKey: [], //部门树默认展开节点
            employeeIds: [], //员工IS
            tabDatas: [], //员工列表
            listQuery: { // 查询条件
                Name: '',
                OrgIds: []
            },
            listLoading: false
        }
    },
    methods: {
        setCheckRow() { //根据父级传入的已选人员设置选中
           var _this = this;
            if (_this.checkedUsers && _this.checkedUsers.length > 0) {
                var checkedUsers = _this.tabDatas.filter(s => _this.checkedUsers.map(u => u.EmployeeId).some(o => o == s.EmployeeId)) || []
                checkedUsers.forEach(u => {
                    _this.$nextTick(() => {
                        if (_this.$refs.mainTable2)
                            _this.$refs.mainTable2.toggleRowSelection(u);
                    })
                })
            } else {
                _this.$refs.mainTable2.clearSelection();
            }
        },
        getCheckRow() { //返回已选中的人员给父级组件

            var _this = this;
            var tmpCheckRow = _this.$refs.mainTable2.selection; //获取当前已选择人数
            if (tmpCheckRow.length > _this.checkedUsers.length) { //全选时会出现当前已选中人数大于 _this.checkedUsers中记录的人数，进行同步
                tmpCheckRow = tmpCheckRow.filter(o => _this.checkedUsers.map(m => m.EmployeeId).every(employeeId => employeeId !== o.EmployeeId));
                tmpCheckRow.forEach(m => {
                    _this.checkedUsers.push(m);
                })
            }
            _this.$emit('changed', _this.checkedUsers);
        },
        valueChange(e) { //查询框值改变
            var _this = this;
            if (e == '' && _this.treeHide) { //如查询框清空内容
                _this.treeHide = false; //展示树形菜单
                _this.getList(); //刷新人员列表  
            }
        },
        selectHandel(row) { //选择框处理
            var _this = this;
            var tmpRow = _this.checkedUsers.some(m => m.EmployeeId == row.EmployeeId);
            if (tmpRow) { //选中的数据是否已包含在_this.checkedUsers内，包含则移除，不包含则添加
                _this.checkedUsers = _this.checkedUsers.filter(m => m.EmployeeId != row.EmployeeId);
            } else {
                _this.checkedUsers.push(row);
            }
        },
        selectAll(rows) { //全选框事件
            var _this = this;
            let oldSelectedList = JSON.parse(JSON.stringify(this.checkedUsers)) //已选中的
            // 已选中的，且不在当前列表中（不管怎么操作都不会被影响）
            oldSelectedList = oldSelectedList.filter(s => this.tabDatas.findIndex(n => n.EmployeeId == s.EmployeeId) == -1)
            // 合并上当前选中的
            oldSelectedList = oldSelectedList.concat(rows)
            //最后结果
            this.checkedUsers = oldSelectedList

            // _this.tabDatas.forEach(row => {//循环当前table数据做选择框事件相应处理
            //   if(this.disabledList.findIndex(s => s == row.EmployeeId) == -1) {
            //     _this.selectHandel(row);
            //   }
            // });
        },
        select(selection, row) { //单选框事件
            var _this = this;
            if (!_this.multiple) {
                //单选处理
                _this.$refs.mainTable2.clearSelection();
                _this.$refs.mainTable2.toggleRowSelection(row, true);
                _this.checkedUsers = [row];
            } else {
                //多选处理
                _this.selectHandel(row);
            }
        },
        getOrgTree() { //查询并加载部门树
            var _this = this;
            systemDepartment.getListByCondition({}).then(response => { //调用公共组件API获取部门数据
                _this.list = response.map(function (item) {
                    return {
                        Id: item.Id,
                        label: item.DepartmentName,
                        ParentId: item.ParentId
                    }
                })
                var orgstmp = JSON.parse(JSON.stringify(_this.list));
                var tempOrgsTree = listToTreeSelect(orgstmp); //将部门数据转换成树形结构
                _this.defaultExpandedKey.push(tempOrgsTree[0]['Id']); //设置默认展开
                _this.orgsTree = tempOrgsTree;
                
                //默认选中部门逻辑
                if(_this.existsUsers || _this.existsUsers.length > 0) {
                    let ids = _this.existsUsers.map(s => s.DepartmentId) || []
                    ids = ids.filter(s => !!s)
                    _this.checkedDeptList = ids

                    let selectedDeptList = JSON.parse(JSON.stringify(_this.checkedDeptList))
                    _this.defaultExpandedKey = _this.defaultExpandedKey.concat(selectedDeptList)
                    _this.listQuery.OrgIds = selectedDeptList
                }

            })
        },
        query() { //查询按钮单击事件
            var _this = this;
            if (this.listQuery.Name) {
                _this.treeHide = true; //隐藏部门树
                _this.listQuery.OrgIds = []; //查询按钮只根据输入框条件进行查询员工，清空当前已选中部门
                _this.$refs.orgsTree.setCheckedKeys([]);
                _this.getList();
            }
        },
        checkOrg() { //部门复选框发生变化触发事件
            var _this = this;

            _this.listQuery.OrgIds = []; //清空之前已选部门
            _this.$refs.orgsTree.getCheckedNodes().forEach((item) => _this.listQuery.OrgIds.push(item.Id)); //循环当前已选部门并将部门ID添加到_this.listQuery.OrgIds
            _this.getList();
        },
        getList() {
            var _this = this;
            _this.listLoading = true;
            var result = null;
            
            if (_this.TableType == "1") {
                let newlist = [];
                //常用联系人      
                result = systemTopContact.getListByCondition({
                    SortNo: 0,
                });
                result.then(response => {                    
                    response.Items.forEach((item) => newlist.push(item.SystemEmployeeEdo))
                    _this.tabDatas = newlist.map(s => {
                        let temp = s.DepartmentName
                        if(temp.indexOf('/') > -1) {
                            temp = temp.substring(temp.lastIndexOf('/') + 1)
                        }
                        s.DepartmentName = temp
                        return s
                    });
                    _this.listLoading = false;
                    _this.setCheckRow(); //根据已选人员设置tableRow默认选中
                })
            }
            if (_this.TableType == "2") {
                if (_this.listQuery.OrgIds.length > 0 || !!_this.listQuery.Name) {
                    let postData = JSON.parse(JSON.stringify(_this.listQuery))
                    if (this.condition) {
                        postData = Object.assign({}, postData, this.condition)
                    }
                    result = systemEmployee.getAllEmployees(postData); //根据勾选部门或者输入框调用公共组件API获取人员
                    result.then(response => {
                        _this.tabDatas = response;
                        _this.listLoading = false;
                        _this.setCheckRow(); //根据已选人员设置tableRow默认选中
                    })
                } else {
                    _this.tabDatas = [];
                    _this.listLoading = false;
                }
            }
        },
        checkSelectable(row) {
            if (this.disabledList && this.disabledList.length > 0) {
                return this.disabledList.findIndex(s => s == row.EmployeeId) == -1
            }
            return true
        },
    },
    mounted() {
        this.getOrgTree();
    }
}
</script>

<style scoped>
.treehead>>>.el-input-group__append {
    background: red;
    color: #fff;
}

.input-wrapper>>>.el-input__inner {
    border-radius: 4px 0 0 4px;
}

.seleEmpCon {
    height: 550px;
    border: 1px solid #eee;
}

.treeCon {
    border-right: 1px solid #ebeef5;
}

.treehead {
    height: 40px !important;
    padding-top: 5px;
}

.treehead .input-wrapper {
    width: 100%;
    position: relative;
    padding-right: 56px;
}

.input-wrapper .search-btn {
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0 4px 4px 0;
}

.treeName {
    display: inline-block;
    width: 153px;
}

.treeMain {
    padding: 0px !important;
}

.tree {
    height: 458px;
}

.treeTips {
    text-align: center;
    width: 255px;
    display: block;
    margin-top: 5px;
}

.empCon{
    display: flex;
    flex-direction: column;
}

.empMain {
    /* padding-bottom: 0px; */
    width: 100%;
    padding: 5px;
    flex: 1;
}

.empTab {
    width: 100%;
}
.empFoot {
    margin-top: 10px;
    height: 30px !important;
}

.btns-wrapper{
    text-align: right;
}

/* 

.empSub {
    float: right;
} */

.paddingRight{
    padding: 20px;
}

.paddingLeft{
    padding: 30px;
}
</style>
