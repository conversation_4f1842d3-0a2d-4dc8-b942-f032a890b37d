<template>
  <div class="avatar-warpper clearfix">
    <div>
      <div class="ipt-wrapper" v-if="isAutocomplete" :title="names">
        <el-select 
          style="width: 100%;"
          class="ipt" 
          :value-key='listSelectorKeyName' 
          @change='handleChange' 
          :value="listSelectorCheckedDataIds" 
          :multiple='multiple' 
          filterable 
          remote 
          collapse-tags
          placeholder="" 
          :remote-method="getList" 
          :loading="loading"
          :disabled="readonly"
          :clearable='isShowClearButton'
          >
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button class="ipt-btn" :disabled='readonly' style="" icon="el-icon-more" @click="handleShow"></el-button>
      </div>
    </div>
    
    <el-input v-if="!isAutocomplete" placeholder="" :value="names" :title="names" :disabled="readonly" :readonly='true' unselectable='on' class="input-with-select">
      <i slot="suffix" v-if="isShowClearButton" v-show="!readonly" @click="handleClear" class="el-input__icon el-icon-close icon-close"></i>
      <div slot='append'>
        <el-button :disabled='readonly' style="padding: 7px 12px;" icon="el-icon-more" @click="handleShow"></el-button>
      </div>
    </el-input>

    <listSelector 
      :checkedData="listSelectorCheckedData" 
      :getListUrl="listSelectorUrl" 
      :multiple="multiple" 
      :pageTitle="listSelectorTitle" 
      :topMessage="listSelectorTopMessage" 
      :selectKeyName="listSelectorKeyName" 
      :condition="listQueryParams" 
      :columnData="listSelectorColumnData" 
      :dialogFormVisible="listSelectorDialogFormVisible" 
      @closeDialog="listSelectorCloseDialog" 
      @saveSuccess="listSelectorSaveSuccess"
      :beforeConfirm='beforeConfirm'
      :pageSize="pageSize"
      ref="listSelector"
    >
      <!-- <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='Name'>
            <el-input style="width: 100%;" v-model="listQueryParams.Name" placeholder=""></el-input>
          </template>
          <template slot='Number'>
            <el-input style="width: 100%;" v-model="listQueryParams.Number" placeholder=""></el-input>
          </template>
        </app-table-form>
      </template> -->
    </listSelector>
  </div>
</template>

<script>
import elDragDialog from '../../../directive/el-dragDialog'
import listSelector from '../../common/listSelector'
import request from "../../../utils/request";

export default {
  name: 'user-list',
  directives: {
    // waves,
    elDragDialog
  },
  components: {
    // EmpTable,
    listSelector,
  },
  props: {
    columns: {
      type: Array,
      default: null
    },
    //已存在的人员
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    readonly: {
      type: Boolean,
      default: false
    },
    isShowClearButton: {
      type: Boolean,
      default: true
    },
    listSelectorUrl: {
      type: String,
      required: true
    },
    listSelectorTitle: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      default: true
    },
    condition: {
      type: Object,
      default: null
    },
    //是否自动完成（支持输入框查询）
    isAutocomplete: {
      type: Boolean,
      default: false
    },
    beforeConfirm: Function,
    pageSize:{
      type: Number,
      default: 20
    }
  },
  created() {
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },
  computed: {
    listSelectorCheckedDataIds() {
      if(this.multiple) {
        return this.listSelectorCheckedData.map(s => s[this.listSelectorKeyName]) || []
      }else{
        if(this.listSelectorCheckedData && this.listSelectorCheckedData.length > 0) {
          return this.listSelectorCheckedData[0][this.listSelectorKeyName]
        }else{
          return null
        }
      }
    },
    names() {
      if (this.listSelectorCheckedData && this.listSelectorCheckedData.length > 0) {
        // return this.listSelectorCheckedData.map(p => `${p.Name}(${p.Number})`).join(',')
        let tmp = this.listSelectorCheckedData.map(p => {
          if (p.Account && p.RegionalName) {
            return `${p.Account}(${p.RegionalName})`
          }
          return ''
        })
        return tmp.join(',')
      }
      return ''
    }
  },
  data() {
    return {
      loading: false,
      //当前下拉选项集合
      options: [],
      //最全options，已被用
      optionsTemp: [],
      delIdx: -1,
      defavatar: require('../../../assets/images/avatar.png'),
      listSelectorType: "",
      listSelectorCheckedData: [],
      listQueryParams: {
        Id: '',
        Account: '',
        RegionalName: '',
      },
    //   tableSearchItems: [
    //     { prop: "Name", label: "姓名" },
    //     { prop: "Number", label: "工号" },
    //   ],
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",
      listSelectorColumnData: this.columns ? JSON.parse(JSON.stringify(this.columns)) : [
        {
             attr: {
                 prop: "Account",
                 label: "ERP账号",
              },
        },
        {
              attr: {
                    prop: "RegionalName",
                    label: "所属地区",
               },
        },
      ],
      listSelectorDialogFormVisible: false
    }
  },
  watch: {
    list: {
      handler(val, oldVal) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val)) || []
        let existsList = this.listSelectorCheckedData.map(s => {
          return {
            value: s[this.listSelectorKeyName],
            label: `${s.Account}(${s.RegionalName})`,
            obj: s
          }
        })
        existsList = JSON.parse(JSON.stringify(existsList))
        this.options = existsList
        this.optionsTemp = existsList
        this.appendOptions(val)
        
        //首页进入页面（一般为编辑），清空select的options属性
        // if(oldVal === undefined) {
        // }
        setTimeout(() => {
          this.options = []
        }, 10);
      },
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
    //   this.listQueryParams.Name = ''
    //   this.listQueryParams.Number = ''
      this.handleFilter()
    },
    handleRemove(idx) {
      this.listSelectorCheckedData.splice(idx, 1)
      this.usersChanged()
    },
    listSelectorSaveSuccess(users) {
      this.listSelectorCheckedData = users
      this.appendOptions(users, true)
      this.usersChanged()
      this.listSelectorCloseDialog()
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false
    },
    usersChanged() {
      this.$emit("change", this.listSelectorCheckedData)
    },
    handleClear() {
      this.listSelectorCheckedData = []
      this.usersChanged();
    },
    handleShow() {
      this.listSelectorDialogFormVisible = true
    },
    handleChange(vals) {
      let _this = this
      //select值改变时候，事实更新“已选中数据集合”变量
      _this.listSelectorCheckedData = _this.optionsTemp.filter(s => vals.indexOf(s.obj[_this.listSelectorKeyName]) > -1).map(s => s.obj)
      // //select值改变时候，需要确保已选中的值不会出现在 options 中出现
      // _this.options = _this.options.filter(item => {
      //   return _this.listSelectorCheckedDataIds.indexOf(item.value) > -1;
      // });
      _this.usersChanged()
    },
    getList(query) {
      var _this = this;
      let keywords = query.trim()
      if(keywords) {
        _this.loading = true;
  
        let data = JSON.parse(JSON.stringify(_this.listQueryParams));
        data.PageIndex = 1
        data.PageSize = 20
        data.SearchText = keywords
        request({
          url: _this.listSelectorUrl,
          method: "POST",
          data
        }).then(response => {
          _this.loading = false;
          _this.appendOptions(response.Items)
          //匹配option的label，并且排除已选中的项
          _this.options = _this.optionsTemp.filter(item => {
            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1 
              && ((_this.multiple && _this.listSelectorCheckedDataIds.indexOf(item.value) == -1) || (!_this.multiple && [_this.listSelectorCheckedDataIds].indexOf(item.value) == -1));
          });
        });
      }

    },   
    appendOptions(list, appendCurrentOptions) {
      let _this = this
      if(list && list.length > 0) {
          let notExixts = list.filter(s => _this.optionsTemp.map(a => a.value).indexOf(s[_this.listSelectorKeyName]) == -1).map(s => {
            return {
              value: s[_this.listSelectorKeyName],
              label: `${s.Account}(${s.RegionalName})`,
              obj: s
            }
          })
          _this.optionsTemp = _this.optionsTemp.concat(notExixts)

          //当点击弹框，选择的项目不存在于 options 中时
          if(appendCurrentOptions) {
            let notExixts2 = list.filter(s => _this.options.map(a => a.value).indexOf(s[_this.listSelectorKeyName]) == -1).map(s => {
              return {
                value: s[_this.listSelectorKeyName],
                label: `${s.Account}(${s.RegionalName})`,
                obj: s
              }
            })
            _this.options = _this.options.concat(notExixts2)

            //点击弹框，选中，保存后，清空 options
            setTimeout(() => {
              _this.options = []
            }, 10);
          }
      }
    }, 
  }
}
</script>

<style scoped>
.ipt-wrapper >>> .el-input__inner{
  border-radius: 4px 0 0 4px;
}

.ipt-wrapper >>> .el-button--small, .el-button--mini{
  border-radius: 0 4px 4px 0;
  height: 28px;
  border-left: none;
}


</style>

<style lang='scss' scoped>
.avatar-warpper {
}

.avatar-warpper .avatar-item {
  position: relative;
  float: left;
  text-align: center;
  margin: 0 8px;
  width: 50px;
}

.avatar-warpper .avatar-item .avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: url(../../../assets/images/avatar.png) no-repeat;
}

.avatar-warpper .avatar-item .avatar img {
  width: 50px;
  height: 50px;
}

.btn-remove {
  position: absolute;
  top: -5px;
  right: -5px;
  cursor: pointer;
}

.btn-remove:hover {
  transition: all 0.3s;
  color: red;
}

.icon-plus {
  line-height: 50px;
  width: 50px;
  font-size: 24px;
  cursor: pointer;
  /* border: 1px solid red; */
}

.username {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon-close {
  cursor: pointer;
}

.dialog_wrapper ::v-deep .el-dialog__body {
  padding-top: 15px;
}

.ipt-wrapper{
  display: flex;
  .ipt{
    flex: 1;
  }
  .ipt-btn{
    padding: 7px 12px;
  }
}
</style>