<template>
    <div class="pageWarp">
        <div class="tabsBox">
            <div class="tabsBox_item" :class="{'active':pageTabTypes==1}" @click="pageTabTypes=1">
                全部问题
            </div>
            <div class="tabsBox_item" :class="{'active':pageTabTypes==2}" @click="pageTabTypes=2">
                我的方案（{{total}}）
            </div>
        </div>
        <div class="content-wrapper">
            <!-- 公司门户下面的 问题改进 - 全部问题 -->
            <indexPage @reload="getList" :pageType="2" v-if="pageTabTypes==1" />

            <!-- 公司门户下面的 问题改进 - 我的方案 -->
            <myProgrammePage @reload="getList" v-if="pageTabTypes==2" />
        </div>
    </div>
</template>

<script>

import indexPage from "@/views/knowledge/problemImprove/index.vue"
import myProgrammePage from "@/views/knowledge/problemImprove/myProgramme.vue"
import * as productQuestionSolutionImprovementApi from '@/api/knowledge/productQuestionSolutionImprovement.js'

export default {
    name: "questionnaire",
    components: {
        indexPage,
        myProgrammePage
    },
    created() {
        this.getList();
    },
    mounted() {},
    data() {
        return {
            pageTabTypes: 1,
            total: 0,
        }
    },
    methods: {
        getList(){
            let self = this;
            productQuestionSolutionImprovementApi.getMyListPage({
                PageSize: 999999,
                PageIndex: 1,
            }).then(res => {
                self.total = res.Total;
            })
            .catch(err => {
                self.total = 0;
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.pageWarp{
    background: white;
    display: flex;
    width: 100%;
    height: 100%;
    .tabsBox{
        width: 200px;
        height: 100%;
        border-right: 1px solid #dcdfe6;
        &_item{
            background-color: #fff;
            padding: 10px;
            height: 40px;
            line-height: 20px;
            cursor: pointer;
            .svg-icon{
                float: left;
                margin-right: 7px;
            }
            &.active{
                background-color: #f0f7ff;
            }
            &:hover{
                background-color: #F5F7FA;
            }
        }
    }
    .content-wrapper {
        position: relative;
        width: calc(100% - 200px);
        height: 100%;
        flex: 1;
    }

}
</style>