<template>
    <div>
        <app-dialog title="选择地区" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600" :maxHeight="800">
            <template slot="body">
                <div class="wrapper">
                    <div v-loading='listLoading' class="item-list">
                        <noData v-if="!list && list.length == 0"></noData>
                        <template v-else>
                            <div class="searchBox">
                                <el-input prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" 
                                    v-model="filterText" v-antiShake='{time: 300,callback: () => {getList()}}' 
                                ></el-input>
                            </div>
                            <div class="list-wrapper">
                                <div class="omit" v-for="i in list" :key="i.value">
                                    <el-radio v-model="checkId" :label="i.value" @change='handleChagne'>{{ i.label }}</el-radio>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import * as regionalBusinessRelation from "@/api/afterSalesMgmt/regionalBusinessRelation";
export default {
    name: "report-selector",
    directives: {},
    components: {
        noData,
    },
    mixins: [],
    computed: {
    },
    watch: {
    },
    props: {
        checked: {
            type: Object, //{ value: '', label: ''}
            default: () => {
                return null
            }
        },
        //创建时必须
        // workPlanId: {
        //     type: String,
        //     default: ''
        //     // validator: (val) => {
        //     //     if(this.dialogStatus == 'create') {
        //     //         return !!val
        //     //     }
        //     //     return true
        //     // }
        // },
        // //周报主键，查看详情时必填
        // id: {
        //     type: String,
        //     default: ''
        // }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(this.checked) {
                    this.checkId = this.checked.value
                    this.checkObj = JSON.parse(JSON.stringify(this.checked))
                }else{
                    this.checkId = null
                    this.checkObj = null
                }
                this.filterText = '';
                this.getList();
            },
            immediate: true
        },
    },
    created() {
    },
    data() {
        return {
            filterText: "",
            
            disabledBtn: false,

            list: [],
            listLoading: false,

            checkId: '',
            checkObj: null


        };
    },
    methods: {
        createData() {
            let result = JSON.parse(JSON.stringify(this.checkObj))
            this.$refs.appDialogRef.createData(result);
        },
        // getList() {
        //     this.listLoading = true
        //     let postDatas = {
        //         PageSize: 10000,
        //         PageIndex: 1,
        //         TemplateType: this.isDeviceParams ? 1 : 2
        //     }

        //     if(this.checkedNode) {
        //         postDatas.ClassifyId = this.checkedNode.Id;
        //     }

        //     deviceParams.getList(postDatas).then(res => {
        //         this.listLoading = false
        //         this.list = res.Items.map(s => {
        //             return {
        //                 label: s.Name,
        //                 value: s.Id
        //             }
        //         });
        //         // this.total = res.Total;

        //     }).catch(err => {
        //         this.listLoading = false
        //     })
        // },
        getList() {
            let postDatas = {
                PageIndex: 1,
                PageSize: 10000,
                RegionalBusinessType: 1,
                Keywords: this.filterText
            }
            this.listLoading = true
            regionalBusinessRelation.getList(postDatas).then(res => {
                this.listLoading = false
                let list = res.Items.map(s => {
                    s.value = s.Id
                    s.label = s.RegionalName
                    return s
                })

                this.list = list

                // if(list && list.length > 0 && !this.currentregionalId) {
                //     this.currentregionalId = list[0].value
                // }
            }).catch(err => {
                this.listLoading = false
            })
        },
        handleChagne(val) {
            let obj = this.list.find(s => s.value == val)
            if(obj) {
                this.checkObj = obj
            }
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
}
</script>


<style lang='scss' scoped>

.item-list{
    height: 100%;
    .searchBox{
        padding: 10px 0;
    }
    .list-wrapper{
        height: 100%;
        min-height: 500px;
        >div{
            display: flex;
            align-items: center;
            padding: 10px;
            &:not(:last-child) {
                border-bottom: 1px solid #DCDFE6;
            }
        }
    }
}
</style>