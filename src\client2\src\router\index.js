import Vue from 'vue'
import Router from 'vue-router'

// import router from './index';
// import store from './store';
import NProgress from 'nprogress' // Progress 进度条
import 'nprogress/nprogress.css' // Progress 进度条样式
// import { Message } from 'element-ui';
import { getToken } from '@/utils/auth' // 验权

import Layout from '../views/layout/Layout'
import LoginAndRegLayout from '../views/login/layout'
import { websitEnv } from "@/utils/env"

// in development-env not use lazy-loading, because lazy-loading too many pages will cause webpack hot update too slow. so only in production use lazy-loading;
// detail: https://panjiachen.github.io/vue-element-admin-site/#/lazy-loading


const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(error=> error)
}


Vue.use(Router)

/* Layout */

/**
* hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
* alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
*                                if not set alwaysShow, only more than one route under the children
*                                it will becomes nested mode, otherwise not show the root menu
* redirect: noredirect           if `redirect:noredirect` will no redirct in the breadcrumb
* name:'router-name'             the name is used by <keep-alive> (must set!!!)
* meta : {
    title: 'title'               the name show in submenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar,
  }
**/

// const whiteList = [
//     '/login', 
//     '/view-summary', 
//     '/register',
//     '/dynamicFormAnalysis',
//     '/adminlogin',
//     '/view-summary2'
// ] // 不重定向白名单

export const constantRouterMap = [
    // {
    //     //手机查看甘特图
    //     path: '/view-summary2',
    //     name: 'view-summary2',
    //     component: () => import('@/views/situation/view-of-app2'),
    //     hidden: true
    // },

    {
        //手机查看甘特图
        path: '/view-summary', name: 'view-summary', component: () => import ('@/views/situation/view-of-app'), hidden: true
    },
    {
        //手机查看甘特图
        path: '/view-summary2', name: 'view-summary2', component: () => import ('@/views/situation/view-of-app'), hidden: true
    },
    {
        path: '/401', component: () => import ('@/views/errorPage/401'), hidden: true
    },
    {
        path: '/500', component: () => import ('@/views/errorPage/404'), hidden: true
    },
    {
        path: '/404', component: () => import ('@/views/errorPage/404'), hidden: true
    },
    {
        path: '/LoginAndRegLayout',
        component: LoginAndRegLayout,
        hidden: true,
        redirect: 'login',
        children: [{
                path: '/login',
                name: 'login',
                component: () =>
                    import ('@/views/login/index'),
                hidden: true
            },
            {
                path: '/betalogin',
                name: 'betalogin',
                component: () =>
                    import ('@/views/login/index'),
                hidden: true
            },
            {
                path: '/adminlogin',
                name: 'adminlogin',
                component: () =>
                    import ('@/views/login/adminIndex'),
                hidden: true
            },
            {
                path: '/register',
                name: 'register',
                component: () =>
                    import ('@/views/register/index'),
                hidden: true
            },
        ]
    },
    {
        path: '/carddefault',
        component: Layout,
        redirect: '/card',
        children: [
            // {
            //     path: 'dashboard',
            //     name: 'dashboard',
            //     meta: { title: '首页', icon: 'dashboard', noCache: true },
            //     component: () => import('@/views/dashboard/index')
            // },
            // {
            //     path: '/profile',
            //     name: 'profile',
            //     meta: { title: '个人信息' },
            //     component: () => import('@/views/profile/index'),
            //     hidden: true
            // },
            {
                path: '/profile/profiles',
                name: 'profile',
                meta: { title: '个人信息', showSidebar: false },
                component: () =>
                    import ('@/views/profile/profiles/index'),
                hidden: true
            },

            // {
            //     path: '/card',
            //     name: 'card',
            //     meta: { title: '名片管理', showSidebar: false },
            //     component: () =>
            //         import ('@/views/card/index'),
            //     hidden: true
            // },

            {
                path: '/version',
                name: 'version',
                meta: { title: '关于平台', showSidebar: false },
                component: () =>
                    import ('@/views/version/index'),
                hidden: true
            },

            // {
            //     path: '/workbench/myMessage/index',
            //     name: 'myMessage',
            //     meta: { title: '消息中心', showSidebar: false },
            //     component: () =>
            //         import ('@/views/workbench/myMessage/index'),
            //     hidden: true
            // },
            // {
            //     path: '/dynamicFormAnalysis/:id',
            //     name: 'dynamicFormAnalysis',
            //     meta: { title: '表单详情' },
            //     component: () => import('@/views/dynamicFormAnalysis/index'),
            //     hidden: true
            // },
        ]
    },
]

const router = new Router({
    // mode: 'history', //后端支持可开
    mode: 'hash',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRouterMap
})

router.beforeEach((to, from, next) => {

    NProgress.start()
    if (getToken()) {
        if (to.path === '/login' || to.path === '/register' || to.path == '/adminlogin' || to.path == '/betalogin') {
            next({ path: websitEnv.mainPage.url })
            NProgress.done()
        } else {
            next()
        }
    } else if (websitEnv.whiteList.indexOf(to.path) !== -1) {
        next()
    } else {
        next('/login')
        NProgress.done()
    }
})

router.afterEach(() => {
    NProgress.done() // 结束Progress
})

export default router

// export const asyncRouterMap = [
//   {
//     path: '/modulemanager',
//     component: Layout,
//     children: [{
//       path: 'index',
//       component: () => import('@/views/modulemanager/index'),
//       name: 'modulemanager',
//       meta: { title: '模块管理', icon: 'excel' }
//     }]
//   },
//   {
//     path: '/orgmanager',
//     component: Layout,
//     children: [{
//       path: 'index',
//       component: () => import('@/views/orgmanager/index'),
//       name: 'orgmanager',
//       meta: { title: '部门管理', icon: 'tree' }
//     }]
//   },
//   {
//     path: '/usermanager',
//     component: Layout,
//     children: [{
//       path: 'index',
//       component: () => import('@/views/usermanager/index'),
//       name: 'usermanager',
//       meta: { title: '用户管理', icon: 'people' }
//     }]
//   },
//   {
//     path: '/rolemanager',
//     component: Layout,
//     children: [{
//       path: 'index',
//       component: () => import('@/views/rolemanager/index'),
//       name: 'rolemanager',
//       meta: { title: '角色管理', icon: 'peoples' }
//     }]
//   },
//   {
//     path: '/resources',
//     component: Layout,
//     children: [{
//       path: 'index',
//       component: () => import('@/views/resources/index'),
//       name: 'resources',
//       meta: { title: '资源管理', icon: 'chart' }
//     }]
//   },
//   {
//     path: '/forms',
//     component: Layout,
//     redirect: 'noredirect',
//     name: 'forms',
//     meta: {
//       title: '表单设计',
//       icon: 'form'
//     },
//     children: [
//       { path: 'index', component: () => import('@/views/forms/index'), name: 'formIndex', meta: { title: '表单列表', noCache: true, icon: 'table' }},
//       { path: 'add', component: () => import('@/views/forms/add'), name: 'formAdd', meta: { notauth: true, title: '添加表单', noCache: true, icon: 'star' }},
//       { path: 'edit/:id', component: () => import('@/views/forms/edit'), name: 'formEdit', hidden: true, meta: { notauth: true, title: '编辑表单', noCache: true, icon: 'list' }}
//     ]
//   },
//   {
//     path: '/flowschemes',
//     component: Layout,
//     redirect: 'noredirect',
//     name: 'flowschemes',
//     meta: {
//       title: '流程设计',
//       icon: 'eye'
//     },
//     children: [
//       { path: 'index', component: () => import('@/views/flowschemes/index'), name: 'schemeIndex', meta: { title: '流程模板', noCache: true, icon: 'table' }},
//       { path: 'add', component: () => import('@/views/flowschemes/add'), name: 'schemeAdd', meta: { notauth: true, title: '添加模板', noCache: true, icon: 'star' }},
//       { path: 'edit/:id', component: () => import('@/views/flowschemes/edit'), name: 'schemeEdit', hidden: true, meta: { notauth: true, title: '编辑流程', noCache: true, icon: 'list' }}

//     ]
//   },
//   {
//     path: '/flowinstances',
//     component: Layout,
//     redirect: 'noredirect',
//     name: 'flowinstances',
//     meta: {
//       title: '流程中心',
//       icon: 'guide'
//     },
//     children: [
//       { path: 'index', component: () => import('@/views/flowinstances/index'), name: 'index', meta: { title: '我的流程', noCache: true, icon: 'table' }},
//       { path: 'detail/:id', component: () => import('@/views/flowinstances/detail'), name: 'flowinstanceDtl', hidden: true, meta: { notauth: true, title: '流程详情', noCache: true, icon: 'list' }},
//       { path: 'verify/:id', component: () => import('@/views/flowinstances/verify'), name: 'verifyFlowinstance', hidden: true, meta: { notauth: true, title: '处理流程', noCache: true, icon: 'list' }},

//       { path: 'add', component: () => import('@/views/flowinstances/add'), name: 'instanceAdd', meta: { notauth: true, title: '发起流程', noCache: true, icon: 'star' }},
//       { path: 'disposed', component: () => import('@/views/flowinstances/dispose'), name: 'disposed', meta: { title: '已处理流程', noCache: true, icon: 'star' }},
//       { path: 'wait', component: () => import('@/views/flowinstances/wait'), name: 'wait', meta: { title: '待处理流程', noCache: true, icon: 'list' }}
//     ]
//   },
//   { path: '*', redirect: '/404', hidden: true }
// ]
