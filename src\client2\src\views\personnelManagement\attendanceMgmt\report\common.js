import dayjs from 'dayjs'
export default function initCellData (datas) {
    if(datas) {

        let temp = datas.map(s => {
          s.Date = dayjs(s.Date).format('YYYY-MM-DD')
          s.ProcessData = s.ProcessData.reduce((acc, cur) => {
            
            let procType = cur.LeaveType
            let accTemp = acc && acc.find(s => s.LeaveType == procType)
            // 11 外出显示时间段；其他流程显示：上午、下午
            if(accTemp) {
              accTemp.ProcessNodeTimeList.push(procType == 11 ? `${dayjs(cur.StartTime).format('HH:mm')}-${dayjs(cur.EndTime).format('HH:mm')}` : cur.ProcessNodeTime)
            }else{
              accTemp = {
                LeaveType: procType,
                ProcessNodeTimeList: [procType == 11 ? `${dayjs(cur.StartTime).format('HH:mm')}-${dayjs(cur.EndTime).format('HH:mm')}` : cur.ProcessNodeTime]
              }
              acc.push(accTemp)
            }
            return acc
          }, []);

          return s
        }) || []
        return temp
    }
    return []
}
  