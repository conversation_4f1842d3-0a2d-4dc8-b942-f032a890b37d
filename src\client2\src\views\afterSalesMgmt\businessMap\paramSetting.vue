<template>
    <div>
        <app-dialog title="设备参数设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600" :width='600'>
            <template slot="body">
                <div class="tags-wrapper">
                    <tags :items='types' v-model="currentType">
                        <template v-for="t in types" :slot="t.value">
                            {{ t.label }}
                        </template>
                    </tags>
                </div>
                <div style="height: 500px;">
                    <app-table
                        ref="mainTableUse"
                        :tab-columns="tabColumns"
                        :tab-datas="tabDatas"
                        :tab-auth-columns="tabAuthColumns"
                        :isShowAllColumn="true"
                        :isShowConditionArea='false'
                        :loading="loading"
                        :isShowOpatColumn="true"
                        :startOfTable="startOfTable"
                        :multable="false"
                        :optColWidth='160'
                    >
                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <el-button type="primary" @click="handleAddDialog('create')">
                                {{ currentType == 1 ? '添加用途' : '添加供风方式' }}
                            </el-button>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleAddDialog('update', scope.row)"></app-table-row-button>
                            <app-table-row-button :type="3" @click="handleRemove(scope.row)"></app-table-row-button>
                            <app-table-row-button v-show="scope.index - 1 > 0" text='上移' @click="move('up', scope.index - 1)"></app-table-row-button>
                            <app-table-row-button v-show="scope.index - 1 < tabDatas.length - 1" text='下移' @click="move('down', scope.index - 1)"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
            </template>
        </app-dialog>

        <app-dialog
            :title="subDialogTitle"
            ref="appDialogRef2"
            @closeDialog="closeAddDialog"
            @saveSuccess="handleAddSaveSuccess"
            :dialogFormVisible="dialogAddFormVisible"
            :maxHeight="600"
            :width="600"
            >
            <template slot="body">
                <el-form
                    :rules="rules"
                    ref="formData"
                    :model="formData"
                    label-width="120px"
                >
                    <div class="prev-dialog-wrapper">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item :label="currentType == 1 ? '用途名称' : '供风方式名称'" prop="Name">
                                    <el-input maxlength="20" type="text" v-model.trim="formData.Name"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <div>
                    <span style="margin-right: 50px;" v-if="dialogStatus == 'create'">
                        <el-checkbox v-model="isContinue">继续添加</el-checkbox>
                    </span>
                    <!-- 取消 -->
                    <app-button @click="closeAddDialog" :buttonType="2"></app-button>
                    <!-- 确认 -->
                    <app-button @click="handleAddSaveSuccess" :buttonType="1" :disabled="disabledBtn"></app-button>
                </div>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as equUse from "@/api/equipmentUse";
import * as equMode from "@/api/equipmentWorkMode";
import * as common from "@/api/common";
import indexPageMixin from "@/mixins/indexPage";

export default {
    name: "param-setting",
    directives: {},
    components: {
    },
    mixins: [indexPageMixin],
    props: {

    },
    watch: {
      "$attrs.dialogFormVisible"(val) {
      },
      currentType: {
          handler() {
                this.getList()
          },
          immediate: true
      },
    },
    computed: {
        subDialogTitle() {
            let pageType = ''
            let busType = ''
            if(this.dialogStatus == 'create') {
                pageType = '添加'
            }else if(this.dialogStatus == 'update') {
                pageType = '编辑'
            }

            if(this.currentType == 1) {
                busType = '设备用途'
            }else if(this.currentType == 2) {
                busType = '设备供风方式'
            }

            return pageType + busType

        },

    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            currentType: 1,
            types: [
                {label: '设备用途', value: 1},
                {label: '设备供风方式', value: 2},
            ],
            loading: false,
            tabDatas: [],
            tabColumns: [
                {
                    attr: { 
                        prop: "Name", 
                        label: "用途",
                        renderHeader: this.renderHeader
                    }
                }
                // ,{
                //     attr: {
                //         prop: "OrderIndex",
                //         label: '排序号'
                //     }
                // }
            ],
            isContinue: false,
            dialogStatus: 'create',
            disabledBtn: false,
            rules: {
                Name: { 
                    fieldName: "名称", rules: [{ required: true, trigger: "change" }]
                }
            },
            dialogAddFormVisible: false,
            formData: {
                Name: ''
            },


            
        };
    },
    methods: {
        handleCreate() {

        },
        resetFromData() {
            this.formData = {
                Name: ''
            }
        },
        getList() {
            this.loading = true
            let postDatas = {PageIndex: 1, PageSize: 10000}
            let result = null
            if(this.currentType == 1) {
                result = equUse.getList(postDatas)
            }else{
                result = equMode.getList(postDatas)
            }
            result.then(res => {
                this.tabDatas = res.Items || []
                this.loading = false
            }).catch(err => {
                this.tabDatas = []
                this.loading = false
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleRemove(item) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let result = null
                if(this.currentType == 1) {
                    result = equUse.del([item.Id])
                }else{
                    result = equMode.del([item.Id])
                }
                result.then(res => {
                    this.getList()
                    this.$notify({
                        title: "提示",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                })
            });
        },
        handleAddDialog(optType = 'create', item) {
            if(optType == 'create') {
                this.resetFromData()
            }
            this.dialogStatus = optType
            this.formData = Object.assign({}, this.formData, item)
            this.dialogAddFormVisible = true
        },
        closeAddDialog() {
            this.dialogAddFormVisible = false
        },
        move(direction, currIdx) {
            if (
                (direction == "up" && currIdx > 0) ||
                (direction == "down" && currIdx < this.tabDatas.length - 1)
            ) {
                let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
                let currRow = this.tabDatas[currIdx]
                // let targetRow = this.tabDatas[targetIdx]

                //准备提交真个列表集合数据（因为必须先提交保存成功，前端才能改变顺序，所以使用该临时变量）
                let tempNodes = this.tabDatas.map(s => {
                    return s.Id
                })
                let tempCurrRow = tempNodes[currIdx]
                tempNodes.splice(currIdx, 1);
                tempNodes.splice(targetIdx, 0, tempCurrRow);

                let postDatas = {
                    setKeyValue: tempNodes.map((s, idx) => {
                        return {
                            Key: s,
                            OrderIndex: (idx + 1) * 10
                        }
                    }),
                    setSort: this.currentType == 1 ? 2 : 3 // 2: 设备用途；3：设备供风方式
                }

                this.loading = true
                common.sort(postDatas).then(res => {
                    // 排序接口成功，直接修改 OrderIndex 属性值，并且排序（不需要重新getList）
                    this.loading = false
                    
                    this.tabDatas.splice(currIdx, 1);
                    this.tabDatas.splice(targetIdx, 0, currRow);
                }).catch(err => {
                    this.loading = false
                })
            }
        },
        handleAddSaveSuccess() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));

                    let result = null
                    this.disabledBtn = true
                    if(this.dialogStatus == 'create') {
                        postData.Id && delete postData.Id
                        let maxOrderIndex = 0
                        let orders = this.tabDatas.map(s => s.OrderIndex)
                        if(orders && orders.length > 0) {
                            maxOrderIndex = Math.max(...orders)
                        }
                        postData.OrderIndex = maxOrderIndex + 10
                        result = this.currentType == 1 ? equUse.add(postData) : equMode.add(postData)
                    }else if(this.dialogStatus == 'update') {
                        result = this.currentType == 1 ? equUse.edit(postData) : equMode.edit(postData)
                    }

                    if(result) {
                        result.then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.getList()
                            if(this.isContinue && this.dialogStatus == 'create') {
                                this.resetFromData()
                                this.$nextTick(() => {
                                    this.$refs.formData.clearValidate();
                                });
                            }else{
                                this.closeAddDialog()
                            }
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    }else{
                        this.disabledBtn = false
                    }
                }
            });
        },

        renderHeader(h, {
            column
        }) {
            return h("span", this.currentType == 1 ? '用途' : '供风方式');
        },
    }
};
</script>

<style lang="scss" scoped>
  .wrapper {
    display: flex;

    .left {
      flex: 1;
      // border-right: 1px solid red;
      padding-right: 14px;
    }

    .right {
      width: 40%;
    }
  }

  .panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
  }

  .tags-wrapper{
      border-bottom: 1px solid #EBEEF5;
  }
</style>
