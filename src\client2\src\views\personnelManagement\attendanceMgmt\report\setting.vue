<template>
    <div>
        <app-dialog title="异常提醒设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <div class="wrapper" style="height: 500px;">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="loading" :isShowOpatColumn="false" :isShowBtnsArea='false' :startOfTable="startOfTable" :multable="false" :isShowConditionArea='false'>
                        <template slot="IsEnable" slot-scope="scope">
                            <span style="margin-right: 80px;">
                                <el-switch v-model="scope.row.IsEnable" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
                            </span>
                            <template v-if="scope.row.IsEnable">
                                <el-select style="width: 100px;" v-model="scope.row.Operator" placeholder="">
                                    <el-option v-for="item in operators" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                值
                                <el-input-number v-model="scope.row.ItemValue" :min="1" :max="999999" label=""></el-input-number>
                                视为异常
                            </template>
                        </template>
                    </app-table>
                </div>
            </template>

            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>

import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import indexPageMixin from "@/mixins/indexPage";
import { vars } from '../vars' 

export default {
    name: "attendanceMgmt-report-setting",
    directives: {},
    components: {

    },
    mixins: [indexPageMixin],
    computed: {
        
    },
    props: {
        
    },
    watch: {
        "$attrs.dialogFormVisible": {
        handler(val) {
            if (val) {
                this.getList();
            }
        },
        immediate: true
        }
    },
    filters: {
    },
    created() {
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            operators: vars.operators,
            keys: [],
            defaultTabDatas: vars.execptionSettingTypes.filter(s => s.setable !== false).map(s => {
                return  {
                    ItemName: s.attr.label,
                    ItemKey: s.attr.prop,
                    IsEnable: false,
                    Operator: '>',
                    ItemValue: 1,
                }
            }),
            tabDatas: [], //原始数据
            tabColumns: [
                {
                    attr: { prop: "ItemName", label: '项目名称', width: '200'}
                },
                {
                    attr: { prop: "IsEnable", label: '是否提醒设置', },
                    slot: true
                },
                // {
                //     attr: { prop: "Operator", label: '操作符号', },
                //     slot: true
                // },
                // {
                //     attr: { prop: "ItemValue", label: '项目值', },
                //     slot: true
                // }
            ],
        };
    },
    methods: {
        getList() {
            this.loading = true
            timecardDepartment.getTimecardSetException({}).then(res => {
                this.loading = false
                let defs = JSON.parse(JSON.stringify(this.defaultTabDatas))
                //覆盖原始值
                if(res && res.length > 0) {
                    defs.forEach(ele => {
                        let obj = res.find(s => s.ItemKey == ele.ItemKey)
                        if(obj) {
                            ele.IsEnable = obj.IsEnable
                            ele.Operator = obj.Operator
                            ele.ItemValue = obj.ItemValue
                        }
                    });
                }
                this.tabDatas = defs
            }).catch(err => {
                this.loading = false
            });
        },
        createData(){
            let postData = JSON.parse(JSON.stringify(this.tabDatas));

            this.disabledBtn = true
            timecardDepartment.timecardSetException(postData).then(res => {
                this.disabledBtn = false
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.$emit('saveSuccess');
            }).catch(err => {
                this.disabledBtn = false
            })

            
            // this.$refs.formData.validate(valid => {
            //     if(valid) {
                    
            //     }
            // });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="css" scoped>
.wrapper >>> .el-table__row{
    height: 35px!important;
}
</style>

<style lang='scss' scoped>
.wrapper{
  min-height: 400px;
  .det{
    margin-bottom: 10px;
  }
}
</style>