<!--添加/修改-->
<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">
          <el-form-item label="分类" prop="ClassifyId">
            <span v-if="!editable" v-html="formData.ClassifyName"></span>
            <treeselect v-else class="treeselect-common" :normalizer="normalizer" :disabled="!editable" :options="ClassifyList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择分类" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" @input="hadnleChange"></treeselect>
          </el-form-item>

          <el-form-item label="题型" prop="QuestionType">
            <el-radio-group v-model="formData.QuestionType" :disabled="!editable" @change="questionTypesChange">
              <el-radio v-for="item in questionTypes" :key="item.value" :label="item.value">{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="难易等级" prop="DifficultyLevel">
            <el-radio-group v-model="formData.DifficultyLevel" :disabled="!editable">
              <el-radio v-for="item in searchTypes.filter(s=>s.value!=0)" :key="item.value" :label="item.value">{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="试题题目" prop="QuestionDescription">
            <el-input v-model="formData.QuestionDescription" :maxlength="500" :rows="4" type="textarea" placeholder="请输入试题题目" :disabled="!editable"></el-input>
          </el-form-item>
          <el-form-item label="图片上传">
            <div style="float: right;width: 100%;border: 1px solid #fcfcfc;text-align: right;">({{QuestionImgFileList.length}} / 5)</div>
            <app-upload-file :max='5' :limit="5" :fileSize='1024 * 1024 * 10' :multiple="true" :value='QuestionImgFileList' :readonly="!editable" @change='handleActivityPhotoUpChange' :preview='true'></app-upload-file>
          </el-form-item>
          <!-- <el-form-item label="得分设置" prop="Score" class="inputLeft">
            <el-input-number style="text-align: left;" :disabled="!editable" v-model="formData.Score" :min="0" :max="99" label="描述文字" :controls="false" :step="1" step-strictly></el-input-number>
          </el-form-item> -->
          <el-form-item label="答案设置">
            <!--  QuestionType  1 单选题   2  多选题   3 判断题  -->
            <div v-if="editable">
              <el-button type="text" @click="addQuestionItem" :disabled="formData.QuestionType==3">增加选项</el-button>
              <span class="textTips" v-show="formData.QuestionType!=3">提示：选项上限为5个</span>
            </div>
            <template v-for="(questionItem, questionItemIndex) in formData.QuestionBankAnswerList">
              <div :key="questionItem.Sort" class="rowBox" v-if="formData.QuestionType==3?questionItem.Sort<2:true">
                <span class="rowBox_left">
                  <el-radio :value="false" disabled class="notLabel" v-if="formData.QuestionType==1"></el-radio>
                  <el-checkbox :value="false" disabled class="notLabel" v-if="formData.QuestionType==2"></el-checkbox>
                  {{ questionItem.Sort | questionItemFilter(formData.QuestionType) }}
                </span>
                <span class="rowBox_input" v-if="formData.QuestionType!=3">
                  <el-form-item label-width="0" :prop="'QuestionBankAnswerList.'+questionItemIndex+'.QuestionAnswer'" :rules="{required: true, message: '内容不能为空', trigger: 'change'}" class="inputLeft">
                    <el-input :disabled="!editable" maxlength="200" v-model="questionItem.QuestionAnswer"></el-input>
                  </el-form-item>
                </span>
                <span class="rowBox_right">
                  <el-form-item label-width="0" :prop="'QuestionBankAnswerList.'+questionItemIndex+'.IsRight'" :rules="{required: true, validator: validatorAnswer, trigger: 'change'}" class="inputLeft">
                  <el-radio style="margin-right: 30px;" v-if="formData.QuestionType==1||formData.QuestionType==3" :disabled="!editable" v-model="questionItem.IsRight" :label="true" @change="questionItemChange(questionItem.Sort)">设为正确答案</el-radio>
                  <el-checkbox style="margin-right: 30px;font-weight: normal !important;" v-if="formData.QuestionType==2" :disabled="!editable" v-model="questionItem.IsRight" :label="true" @change="validatorRow">设为正确答案</el-checkbox>
                    <app-table-row-button v-show="editable&&isShowDel(questionItem)" :type="3" @click="delQuestionItem(questionItemIndex)" text="删除"></app-table-row-button>
                  </el-form-item>
                </span>
              </div>
            </template>
          </el-form-item>
          <el-form-item label="答案解析" prop="IsAnswerParse">
              <el-switch v-model="formData.IsAnswerParse"></el-switch>
          </el-form-item>
          <el-form-item label="解析内容" prop="ParseContent" v-if="formData.IsAnswerParse">
            <el-input v-model="formData.ParseContent" :maxlength="500" :rows="4" type="textarea" placeholder="请输入解析内容" :disabled="!editable"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary" :loading='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script> 
import { listToTreeSelect } from "@/utils";
import * as questionBankApi from '@/api/knowledge/questionBankManagement'
import { vars } from '../common/vars'
import approvalMixins from '@/mixins/approvalPatch'
import * as classifyApi from '@/api/classify';
export default {
  name: "question-bank-create",
  mixins: [approvalMixins],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    selectClassifyId: {
      type: String,
      default: ""
    },
  },
  filters: {
    questionItemFilter(type, QuestionType) {
      let name = ''
      switch (type) {
        case 0:
          name = 'A：'
          break
        case 1:
          name = 'B：'
          break
        case 2:
          name = 'C：'
          break
        case 3:
          name = 'D：'
          break
        case 4:
          name = 'E：'
          break
      }
      if (QuestionType == 3) {
        switch (type) {
          case 0:
            name = '正确：'
            break
          case 1:
            name = '错误：'
            break
        }
      }
      return name;
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.ClassifyList = [];
        this.QuestionImgFileList = [];
      }
      if (val) {
        this.goOn = false;
        this.formData = this.$options.data().formData;
        if (this.dialogStatus == 'create') {
          this.formData.QuestionBankAnswerList = [
            { Sort: 0, QuestionAnswer: '', IsRight: true },
            { Sort: 1, QuestionAnswer: '', IsRight: false },
            { Sort: 2, QuestionAnswer: '', IsRight: false },
            { Sort: 3, QuestionAnswer: '', IsRight: false },
          ]
        }
        this.getClassifyList();
      }
    }
  },
  computed: {
    editable() {
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建题目";
      } else if (this.dialogStatus == "update") {
        return "编辑题目";
      } else if (this.dialogStatus == 'detail') {
        return '题目详情'
      }
      return ''
    },
  },
  mounted() { },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label.split(",")[0],
          id: node.Id,
          children: node.children
        };
      },
      QuestionImgFileList: [],


      searchTypes: vars.questionBankEnum.searchTypes, // 顶部筛选条件
      questionTypes:  vars.questionBankEnum.questionTypes, // 顶部筛选条件


      disabledBtn: false,
      goOn: false,
      ClassifyList: [],

      formLoading: false,
      rules: {
        ClassifyId: { fieldName: "分类", rules: [{ required: true, trigger: 'change' }] },
        QuestionDescription: { fieldName: "试题题目", rules: [{ required: true }] },
        Score: { fieldName: "得分设置", rules: [{ required: true }] },
        ParseContent: { fieldName: "解析内容", rules: [{ required: true }] },
      },
      labelWidth: "150px",
      formData: {
        Id: "",
        ClassifyId: null,
        QuestionType: 1,
        DifficultyLevel: 1,
        QuestionDescription: '',
        QuestionImg: '',
        Score: 1,
        QuestionBankAnswerList: [],
        IsAnswerParse: false,
        ParseContent: '',
      }
    };
  },
  methods: {
    isShowDel(row){
      // QuestionType  1 单选题   2  多选题   3 判断题
      // 支持答案选项删除，单选/多选题首个答案不显示删除按钮；判断题不支持删除
      let i = row.Sort, {QuestionType} = this.formData;
      if (QuestionType===3) {
        return false
      }
      if (i==0) {
        return false
      }
      return true
    },
    validatorAnswer(rule, value, callback) {
      if (!this.formData.QuestionBankAnswerList.some(s=>s.IsRight)) {
        return callback(new Error('请设置正确答案'));
      }
      return callback();
    },
    hadnleChange() {
      this.$refs.formData.validateField("ClassifyId");
    },
    //获取分类下拉框
    getClassifyList() {
      this.formLoading = true;
      classifyApi.getListByCondition({ BusinessType: 9 }).then(res => {
        var departments = res.Items.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.Name,
            ParentId: item.ParentId
          };
        });
        this.ClassifyList = listToTreeSelect(departments);
        if (this.selectClassifyId) {
          this.formData.ClassifyId = this.selectClassifyId;
        }
        if (this.dialogStatus != "create" && this.id) {
          this.$nextTick(() => {
            this.getDetail();
          });
        } else {
          this.formLoading = false;
        }
      }).catch(err => {
        this.formLoading = false;
      });
    },
    // 顶部切换题型 重置答案为第一个
    questionTypesChange(){
      this.formData.QuestionBankAnswerList.map((s, i)=> {
        s.IsRight = i == 0
      })
    },
    // 图片上传事件
    handleActivityPhotoUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.QuestionImg = imgs.map(s => s.Id).toString()
      } else {
        this.formData.QuestionImg = ''
      }
    },
    // 删除答案  创建时  可以删除添加的第5条答案
    delQuestionItem(index){
      // if (this.formData.QuestionBankAnswerList.length<5) {
      //   this.$message({
      //       message: "答案最少4条",
      //       type: "error"
      //   });
      // } else {
        // console.log(index)
        if (this.formData.QuestionBankAnswerList[index].IsRight) {
          this.formData.QuestionBankAnswerList[index - 1].IsRight = true
        }
        this.formData.QuestionBankAnswerList.splice(index, 1);
        this.formData.QuestionBankAnswerList.map((s,i)=>{
          s.Sort = i
          return s
        })
        // console.log(this.formData.QuestionBankAnswerList) 
      // }
    },
    // 添加答案
    addQuestionItem(){
      if (this.formData.QuestionBankAnswerList.length>4) {
        this.$message({
            message: "答案最多5条",
            type: "error"
        });
      } else {
        this.formData.QuestionBankAnswerList.push({
          Sort: this.formData.QuestionBankAnswerList.length, QuestionAnswer: '', IsRight: false
        })
      }
      console.log(this.formData.QuestionBankAnswerList)
    },
    // 答案选择事件
    questionItemChange(index){
      // console.log(index)
      this.formData.QuestionBankAnswerList.map((s, i)=> {
        if(index != i) {
          s.IsRight = false
        }
      })
      this.validatorRow()
    },
    // 问题  单行选择答案时  校验每行问题答案是否选择
    validatorRow(){
      this.formData.QuestionBankAnswerList.map((s, i)=> {
        this.$refs.formData.validateField("QuestionBankAnswerList."+i+".IsRight");
      })
    },
    //清理表单
    resetFormData() {
      let temp = {
        // Id: "",
        // ClassifyId: null,
        // QuestionType: 1,
        // DifficultyLevel: 1,
        QuestionDescription: '',
        QuestionImg: '',
        Score: 1,
        QuestionBankAnswerList: [
          { Sort: 0, QuestionAnswer: '', IsRight: true },
          { Sort: 1, QuestionAnswer: '', IsRight: false },
          { Sort: 2, QuestionAnswer: '', IsRight: false },
          { Sort: 3, QuestionAnswer: '', IsRight: false },
        ]
      };
      this.formData = Object.assign({}, this.formData, temp);
      this.QuestionImgFileList = []
    },

    //获取详情
    getDetail() {
      this.formLoading = true;
      questionBankApi.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);

        
        if (this.formData.QuestionImgPaths) {
          var QuestionImg = this.formData.QuestionImg.split(',')
          var QuestionImgPaths = this.formData.QuestionImgPaths.split(',')

          QuestionImg.forEach((element, index) => {
            var photoPath = QuestionImgPaths[index];
            this.QuestionImgFileList.push({ Id: element, Path: photoPath });
          });
        }


        this.formLoading = false;
      }).catch(err => {
        this.formLoading = false;
      });
    },
    //提交
    handleSave() {
      // this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      let postData = JSON.parse(JSON.stringify(this.formData));
      Promise.all([listResult]).then(valid => {
        if (postData.QuestionType==3) {
          postData.QuestionBankAnswerList.filter((s, i)=> i<2)
        }
        let result = null;
        if (this.dialogStatus == "create") {
            delete postData.Id;
            result = questionBankApi.add(postData);
        } else if (this.dialogStatus == "update") {
            result = questionBankApi.edit(postData);
        }
        console.log(postData)
        this.disabledBtn = true;
        result.then(res => {
            this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
            });
            this.disabledBtn = false
            if (this.goOn) {
              this.resetFormData();
              this.$refs.formData.resetFields();
            }
            this.$emit('saveSuccess', this.goOn);
        }).catch(err => {
            this.disabledBtn = false
        })
        // if (this.dialogStatus == "create") {
        //   delete postData.Id;
        
        //   postData.ClassifyId = this.selectClassifyId
        //   questionBankApi.add(postData).then(res => {
        //     this.disabledBtn = false;
        //     this.$notify({
        //       title: '成功',
        //       message: '创建成功！',
        //       type: 'success'
        //     });
        //     if (this.goOn) {
        //       this.resetFormData();
        //       this.$refs['formData'].resetFields();
        //     }
        //     this.$emit('saveSuccess', this.goOn);
        //   }).catch(err => {
        //     this.disabledBtn = false;
        //   })
        // } else {
        //   questionBankApi.edit(postData).then(res => {
        //     this.disabledBtn = false;
        //     this.$notify({
        //       title: '成功',
        //       message: '编辑成功！',
        //       type: 'success'
        //     });
        //     this.$emit('saveSuccess', false);
        //   }).catch(err => {
        //     this.disabledBtn = false;
        //   })
        // }
      }).catch(err => {
        this.disabledBtn = false;
      })
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style scoped>
.notLabel >>> .el-radio__label{
  padding: 0 !important;
}
.inputLeft >>> .el-input-number .el-input__inner{
  text-align: left;
  width: 70px;
}
.upDiv >>> .el-icon-circle-close{
  color:white;
}
.elUpload >>> .el-upload-list{
  display:none;
}
</style>
<style lang="scss" scoped>
.upDiv{
    ul{
        li{
            width:159px;
            height:100px;
            position: relative;
            margin:5px;
            overflow: hidden;
            background:#282828;
        >i{
            display:none;
            z-index: 10;
            color:#ccc;
        }
        .elImg{
            width:100%;
            height:100%;
        }
        }
        li:hover{
            >i{
                display:block;
                position: absolute;
                right:0;
                top:0;
            }
        }
    }
}
.textTips{
  font-size: 12px;
  color: #f59a23;
  padding-left: 30px;
}
.rowBox{
  display: flex;
  padding: 5px 0;
  &_left {
    // width: 80px;
  }
  &_input{
    flex: 1;
    padding-right: 10px;
  }
  &_right {
    width: 170px;
  }
}
.wrapper {
  display: flex;

  .left {
    flex: 1;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
