<!--案例类型 添加/编辑-->
<template>
    <div>
        <app-dialog title="奖励积分" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="500">
            <template slot="body">
                <div style="padding:20px 23px 10px 23px;color:#409EFF;">提示：该操作将写入历史记录，请谨慎操作</div>
                <el-form :rules="formRules" ref="formData" :model="formModel" label-position="right" label-width="90px">
                    <el-form-item label="调整理由" prop="AdjustReason">
                        <el-input maxlength="100" :rows="3" type="textarea" v-model="formModel.AdjustReason"></el-input>
                    </el-form-item>
                    <el-form-item label="选择操作" prop="Operate">
                        <el-radio-group v-model="formModel.Operate">
                            <el-radio :label="1">增加</el-radio>
                            <el-radio :label="2">扣除</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="输入数值" prop="Value" class="inputLeft">
                        <el-input-number :disabled="!editable" v-model="formModel.Value"
                        :precision="0" :step="1" :min="1" :max="999" label="描述文字"
                        :controls="false" step-strictly></el-input-number>
                    </el-form-item>
                </el-form>
            </template>
            <template slot="footer">
                <app-button :buttonType="2" @click="handleClose"></app-button>
                <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as StudyRecordApi from '@/api/knowledge/StudyRecord'

export default {
    name: "learning-points-adjustment-integral",
    components: {},
    props: {
        dialogStatus: {
            type: String,
            default: "create"
        },
        id: {
            type: String,
            default: "",
            required: true
        }
    },
    data() {
        return {
            buttonLoading: false,
            formModel: {
                EmployeeId: '',
                Value: 0,
                Operate: null,
                AdjustReason: '',
            },
            formRules: {
                Value: { fieldName: "数值", rules: [{ required: true }] },
                Operate: { fieldName: "操作", rules: [{ required: true }] },
                AdjustReason: { fieldName: "调整理由", rules: [{ required: true }] },
            }
        };
    },
    computed: {
        editable() {
            return this.dialogStatus != "detail";
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormModel();
                }
            },
            immediate: true
        }
    },
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    mounted() { },
    methods: {
        /**清理表单 */
        resetFormModel() {
            this.formModel = this.$options.data().formModel
            this.formModel.EmployeeId = this.id;
        },

        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    self.buttonLoading = true;
                    StudyRecordApi.AdjustingIntegration(self.formModel).then(response => {
                        self.buttonLoading = false;
                        self.$notify({
                            title: "成功",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.buttonLoading = false;
                    });
                } else {
                    return false;
                }
            });
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style scoped>
.inputLeft >>> .el-input-number .el-input__inner{
  text-align: left;
}
</style>


