<template>
  <div class="ceda_div">
    <app-dialog title="故障现象详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :Height="650" :width="1200">
      <template slot="body">
        <el-form class="businessElForm" ref="formData" :model="formData" label-position="right" label-width="100px" v-loading="loading">
          <el-collapse v-model="activeNames">
            <el-collapse-item name="1">
              <template slot="title">
                <div class="collspaseTitle">
                  基本信息
                </div>
              </template>
               <el-form-item label="所属分类">
                {{formData.ClassifyName}}
              </el-form-item>
              <el-form-item label="故障代码" prop="FailureCaseCode">
                {{formData.FailureCaseCode}}
              </el-form-item>
              <el-form-item label="故障名称" prop="FailureSymptom">
                {{formData.FailureSymptom}}
              </el-form-item>
              <el-form-item label="故障关键字" prop="name2">
                <div v-if="keyList.length>0" class="cl">
                  <span class="elSpan fl" v-for="(kl, index) in keyList" :key="index">
                    {{ kl.FaultKeyWordName }}
                  </span>
                </div>
                <span v-else>暂无数据</span>
              </el-form-item>

              <el-form-item label="故障分类标签">
                <ul class="elUl" v-if="selectedMsg">
                  <li>故障类型：{{ selectedMsg.Name }}</li>
                  <li v-for="(sd, index) in selectedData" v-show="sd.checkList.length > 0" :key="index">
                    {{ sd.Name }}：{{ sd.checkList.join("、") }}
                  </li>
                </ul>
                <span v-else>暂无数据</span>
              </el-form-item>

              <el-form-item label="相关附件">
                <app-uploader :readonly="true" v-if="formData.AttachmentList.length>0" accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"></app-uploader>
                <span v-else>暂无数据</span>
              </el-form-item>
            </el-collapse-item>

            <el-collapse-item name="2">
              <template slot="title">
                <div class="collspaseTitle">
                  故障原因({{ formData.FailureAnalysiseList.length }})
                </div>
              </template>
              <app-table-core ref="mainTable" :tab-columns="failureAnalysiseColumns" :tab-datas="formData.FailureAnalysiseList" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="0" :multable="false">
                <template slot="op" slot-scope="scope">
                  <el-popover placement="right" width="400" trigger="click">
                    <el-card class="box-card detailCard">
                      <div slot="header" class="clearfix">
                        <span>故障原因详情</span>
                      </div>
                      <div class="text item">
                        <el-row class="detailRow">
                          <el-col :span="8">
                            <label style="float: right;">故障原因代码：</label>
                          </el-col>
                          <el-col :span="16">
                            {{scope.row.FailureResonCode}}
                          </el-col>
                        </el-row>
                        <el-row class="detailRow">
                          <el-col :span="8">
                            <label style="float: right;">故障原因：</label>
                          </el-col>
                          <el-col :span="16">
                            {{scope.row.FailureReson}}
                          </el-col>
                        </el-row>
                      </div>
                    </el-card>
                    <el-button slot="reference" type="text">详情</el-button>
                  </el-popover>
                  <!-- <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button> -->
                </template>
              </app-table-core>
            </el-collapse-item>

            <el-collapse-item name="3">
              <template slot="title">
                <div class="collspaseTitle">
                  解决方法({{ formData.SolutionList.length }})
                </div>
              </template>
              <app-table-core ref="mainTable" :tab-columns="solutionColumns" :tab-datas="formData.SolutionList" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="0" :multable="false">
                <template slot="op" slot-scope="scope">
                  <el-popover placement="right" width="400" trigger="click">
                    <el-card class="box-card detailCard">
                      <div slot="header" class="clearfix">
                        <span>解决方法详情</span>
                      </div>
                      <div class="text item">
                        <el-row class="detailRow">
                          <el-col :span="8">
                            <label style="float: right;">解决方法代码：</label>
                          </el-col>
                          <el-col :span="16">
                            {{scope.row.FailureSolutionCode}}
                          </el-col>
                        </el-row>
                        <el-row class="detailRow">
                          <el-col :span="8">
                            <label style="float: right;">解决方法：</label>
                          </el-col>
                          <el-col :span="16">
                            {{scope.row.Solution}}
                          </el-col>
                        </el-row>
                        <el-row class="detailRow">
                          <el-col :span="8">
                            <label style="float: right;">相关附件：</label>
                          </el-col>
                          <el-col :span="16">
                            <app-uploader :readonly='true' v-if="scope.row.AttachmentList.length>0" accept='all' :fileType='3' :max='10000' :value='scope.row.AttachmentList' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024'></app-uploader>
                            <span v-else>暂无数据</span>
                          </el-col>
                        </el-row>
                      </div>
                    </el-card>
                    <el-button slot="reference" type="text">详情</el-button>
                  </el-popover>
                  <!-- <el-button size="mini" type="text" @click="handleSLDetail(scope.row)">详情</el-button> -->
                </template>
              </app-table-core>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </template>
      <template slot="footer">
        <el-button @click="handleClose" v-show="editable" size="mini">关闭</el-button>
        <el-button @click="handleClose" v-show="!editable" size="mini">取消</el-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import NoData from "@/views/common/components/noData";
import * as failurecase from "@/api/failurecase";
import * as equipmentSetting from "@/api/equipmentSetting";
import { listToTreeSelect } from "@/utils";

export default {
  name: "mainten-order-mgmt-assign",
  components: {
    NoData,
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String,
      default: "create"
    },
    id: {
      type: String,
      defalult: ""
    }
  },
  data() {
    return {
      activeNames: ['1'],

      listData: [],
      causeData: null,
      solutionData: null,
      loading: false,

      keyList: [],

      dialogCauseFormVisible: false,
      dialogSolutionFormVisible: false,

      formData: {
        FailureCaseCode: "",
        FailureSymptom: "",
        FaultKeywordIdList: [],
        EquipmentSettingIds: [],
        AttachmentList: [],
        FailureAnalysiseList: [],
        SolutionList: [],
        ClassifyName:"",
        Id: ""
      },

      failureAnalysiseColumns: [
        {
          attr: { prop: "FailureResonCode", label: "故障原因代码", width: 500 }
        },
        {
          attr: { prop: "FailureReson", label: "故障原因", width: 500 }
        },
        {
          attr: { prop: "op", label: "操作" }, slot: true
        }
      ],

      solutionColumns: [
        {
          attr: { prop: "FailureSolutionCode", label: "解决方法代码", width: 500 }
        },
        {
          attr: { prop: "Solution", label: "解决方法", width: 500 }
        },
        {
          attr: { prop: "op", label: "操作" }, slot: true
        }
      ],
    };
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetData();
        if (this.dialogStatus == "detail") {
          this.getDetail();
        }
      }
    },
  },
  computed: {
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus == "detail";
    },
    selectedMsg() {
      return this.$store.state.communication.selectedMsg;
    },
    selectedData() {
      return this.$store.state.communication.selectedData;
    }
  },
  created() { },
  methods: {

    getDetail() {
      this.loading = true;
      let a = failurecase.getDetails({ id: this.id, addHot: true });
      let b = equipmentSetting.getListPage({ PageIndex: 1, PageSize: 9999 });
      Promise.all([a, b]).then(res => {
        this.listData = listToTreeSelect(res[1].Items);
        this.formData.FailureCaseCode = res[0].FailureCaseCode;
        this.formData.FailureSymptom = res[0].FailureSymptom;
        this.formData.AttachmentList = res[0].AttachmentList;
        this.formData.FailureAnalysiseList = res[0].FailureAnalysiseList;
        this.formData.SolutionList = res[0].FailureSolutionList;
        this.formData.ClassifyName = res[0].ClassifyName;
        this.formData.Id = res[0].Id;
        this.keyList = res[0].FaultKeywordList;
        if (res[0].EquipmentSettingIds.length > 0) {
          let c = this.listData.find(s => s.Id == res[0].EquipmentSettingIds[0]);
          let d = null;
          this.$store.commit("getSelectedMsg", c);
          c.children.forEach(v => {
            v.checkList = [];
            v.children.forEach(v1 => {
              d = res[0].EquipmentSettingIds.find(s => s == v1.Id);
              if (d) {
                v.checkList.push(v1.Name);
              }
            });
          });
          this.$store.commit("getselectedData", c.children);
        }
        this.loading = false;
      });
    },
    resetData() {
      this.keyList = [];
      this.$store.commit("getselectedData", []);
      this.$store.commit("getSelectedMsg", null);
      this.formData = {
        FailureCaseCode: "",
        FailureSymptom: "",
        FaultKeywordIdList: [],
        EquipmentSettingIds: [],
        AttachmentList: [],
        FailureAnalysiseList: [],
        SolutionList: [],
        ClassifyName:"",
        Id: ""
      };
    },


    handleDetail(d) {
      this.causeData = d;
      this.dialogCauseFormVisible = true;
    },

    handleSLDetail(d) {
      this.solutionData = d;
      this.dialogSolutionFormVisible = true;
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    closeCauseDialog() {
      this.dialogCauseFormVisible = false;
    },

    closeSolutionDialog() {
      this.dialogSolutionFormVisible = false;
    },

  }
};
</script>


<style lang="scss" scoped>
.detailRow {
  width: 100%;
  padding-top: 5px;
  padding-bottom: 5px;
}
.detailCard {
  > label {
    font-weight: normal !important;
  }
}

.nav {
  margin-bottom: 8px;
}

.collspaseTitle {
  font-size: 16px;
  font-weight: 700;
}

main {
  ul {
    li {
      margin-bottom: 8px;
    }
  }
}
.el_Button {
  margin-left: 20px;
}
.elSpan {
  height: 24px;
  line-height: 24px;
  color: #aaa;
  padding: 0 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  position: relative;
  margin-right: 10px;
  .el-icon-error {
    position: absolute;
    right: -5px;
    top: -5px;
    color: #aaa;
    cursor: pointer;
  }
}
.elUl {
  li {
    margin-bottom: 8px;
    padding-left: 6px;
  }
}
</style>
