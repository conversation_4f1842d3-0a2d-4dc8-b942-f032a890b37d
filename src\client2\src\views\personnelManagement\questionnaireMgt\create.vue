<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="750">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" style="padding-top: 0;" v-loading='loading'
                label-position="right" label-width="86px" class="wrapperMain">
                    <el-row class="wrapperBox">
                        <el-form-item label="问卷名称" prop="Name">
                            <el-input v-if="editable" :disabled="!editable" maxlength="50" type="text" v-model="formData.Name"></el-input>
                            <div v-else>{{formData.Name}}</div>
                        </el-form-item>
                        <el-form-item label="负责人" prop="PrincipalEmployeeList">
                            <emp-selector v-if="editable" :readonly="!editable" :beforeConfirm='handleFinalBeforeConfirm' :showType="2"
                            :multiple="true" :list="formData.PrincipalEmployeeList" @change="handleChangeManager"></emp-selector>
                            <div v-else>{{formData.PrincipalEmployeeList.map(s=>s.Name).toString()}}</div>
                        </el-form-item>
                        <el-form-item label="截止时间" prop="DeadlineTime">
                            <el-date-picker v-if="editable" :disabled="!editable" style="width: 100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            v-model="formData.DeadlineTime" type="date" placeholder></el-date-picker>
                            <div v-else>{{formData.DeadlineTime | dateFilter('YYYY-MM-DD')}}</div>
                        </el-form-item>
                        <el-form-item label="组别设置">
                            <span>已添加（{{formData.SurveyGroupList.length}}）</span>
                            <el-button type="text" @click="addSurveyGroupList" v-if="editable">添加组</el-button>
                        </el-form-item>
                    </el-row>
                    <div class="wrapperBox_main">
                        <el-card shadow="hover" :body-style="{ padding: '20px 15px 20px 5px' }"
                            v-for="(item,index) in formData.SurveyGroupList" :key="index">
                            <div slot="header" class="clearfix">
                                <span>组别{{index+1}}</span>
                                <el-button v-if="index!=0&&editable" style="font-size: 18px;float: right;color: #F56C6C;margin-right: 12px;"
                                type="text" icon="el-icon-delete" @click="delSurveyGroupList(index)"></el-button>
                            </div>
                            <el-form-item label-width="80px" label="组别名称"
                                :prop="`SurveyGroupList.${index}.Name`"
                                :rules="{required: true, message: '组别名称不能为空', trigger: 'blur'}">
                                <el-input v-if="editable" :disabled="!editable" maxlength="50" type="text" v-model="item.Name"></el-input>
                                <div v-else>{{item.Name}}</div>
                            </el-form-item>
                            <el-form-item label-width="80px" label="参与人员"
                            :prop="`SurveyGroupList.${index}.ParticipantEmployeeList`"
                            :rules="{required: true, message: '参与人员不能为空', trigger: 'change'}">
                                <emp-selector v-if="editable" :readonly="!editable" :showType="2"
                                :multiple="true" :list="item.ParticipantEmployeeList" @change="handleEmployeesChange($event,index)"></emp-selector>
                                <div v-else>{{item.ParticipantEmployeeList.map(s=>s.Name).toString()}}</div>
                            </el-form-item>
                        </el-card>
                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 关闭 -->
                <app-button v-if="dialogStatus == 'detail'" @click="handleClose" text="关闭" type></app-button>
                <!-- 取消 -->
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as SurveyApi from '@/api/personnelManagement/survey.js'
import empSelector from "@/views/common/empSelector";
import { SurveyTypeEnum } from "../enum.js";
export default {
    name: "questionnaireMgt-create",
    directives: {},
    components: {
        empSelector
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return `创建${SurveyTypeEnum.find(s => s.value == this.formData.SurveyType).label}`;
            }else if(this.dialogStatus == 'update'){
                return `编辑${SurveyTypeEnum.find(s => s.value == this.formData.SurveyType).label}`;
            }
            return "详情";
        },
    },
    filters: {
        SurveyTypeFilter(val) {
            let obj = SurveyTypeEnum.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "无";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        id: {
            type: String,
            default: "",
        },
        surveyType: { // 选择的分类
            type: Number,
            default: 1,
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {
                Name: {fieldName: "问卷名称",rules: [{ required: true }]},
                PrincipalEmployeeList: {fieldName: "负责人",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                // Id: '',
                SurveyType: 1,
                Name: '',// 问卷名称
                DeadlineTime: '',//截止时间
                PrincipalEmployee: '',// 负责人员名称
                PrincipalEmployeeIdList: [],//负责人id集合
                PrincipalEmployeeList: [],//负责人集合
                SurveyGroupList: [ // 问卷调查 组别集合
                    {
                        Name: '',// 组别名称
                        ParticipantEmployee: '',// 参与人员名称
                        ParticipantEmployeeIdList: [],// 参与人员id集合
                        ParticipantEmployeeList: [],// 参与人员集合
                    }
                ],
            },
        };
    },
    methods: {
        addSurveyGroupList(){
            this.formData.SurveyGroupList.unshift({
                Name: '',// 组别名称
                ParticipantEmployee: '',// 参与人员名称
                ParticipantEmployeeIdList: [],// 参与人员id集合
                ParticipantEmployeeList: [],// 参与人员集合
            })
            this.$refs['formData'].clearValidate()
        },
        delSurveyGroupList(index){
            this.formData.SurveyGroupList.splice(index,1)
            this.$refs['formData'].clearValidate()
        },
        handleFinalBeforeConfirm(users) {
            if(users && users.length > 5) {
                this.$message({
                    message: '负责人不得超过5人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleChangeManager(users) {
            // console.log(users)
            if (users && users.length > 0) {
                this.formData.PrincipalEmployeeList = users;
            } else {
                this.formData.PrincipalEmployeeList = [];
            }
            this.$refs["formData"].validateField("PrincipalEmployeeList");
        },
        handleEmployeesChange(users, index){
            if (users && users.length > 0) {
                this.formData.SurveyGroupList[index].ParticipantEmployeeList = users;
            } else {
                this.formData.SurveyGroupList[index].ParticipantEmployeeList = [];
            }
            this.$refs["formData"].validateField(`SurveyGroupList.${index}.ParticipantEmployeeList`);
        },
        resetFormData() {
            this.formData = this.$options.data().formData
            if (this.dialogStatus == "create") {
                this.formData.SurveyType = this.surveyType;
            }
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    postData.PrincipalEmployeeIdList = postData.PrincipalEmployeeList.map(s=>s.EmployeeId)
                    delete postData.PrincipalEmployeeList
                    postData.SurveyGroupList.map(s=>{
                        s.ParticipantEmployeeIdList = s.ParticipantEmployeeList.map(s=>s.EmployeeId)
                        delete s.ParticipantEmployeeList
                    })
                    console.log(postData)
                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = SurveyApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = SurveyApi.edit(postData);
                    }

                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 查询 基本信息
        getDetail() {
            this.isOneLoad = true;
            this.loading = true
            SurveyApi.detail({ id: this.id }).then(res => {
                this.formData = res;
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
</style>
<style lang='scss' scoped>
.wrapperBox{
    padding-top: 10px;
    &_main{
        max-height: 420px;
        overflow-y: auto;
    }
}
.el-card{
    margin-bottom: 15px;
}
.omit{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tips_title{
    float: left;
    max-width: 100%;
}
</style>