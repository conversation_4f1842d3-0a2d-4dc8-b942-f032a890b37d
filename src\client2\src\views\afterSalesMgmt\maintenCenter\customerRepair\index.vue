<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="部门管理" :subTitle="['企业组织结构及人员的管理页面']"></page-title> -->
        <div class="pageWrapper __dynamicTabContentWrapper">
            <div class="tagBox">
                <tags :items='orderTypes' v-model="listQuery.AfterSaleMaintenanceStatus" @change="handleTagsChange">
                    <template v-for="t in orderTypes" :slot="t.value">
                        {{ t.label }}
                    </template>
                </tags>
            </div>
            <div class="content __dynamicTabWrapper">
                <app-table ref="mainTable" :multable="false" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" @sortChagned="handleSortChange" :layoutMode='layoutMode'>

                    <template slot="ReportTime" slot-scope="scope">
                        {{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm') }}
                    </template>

                    <template slot="ReportContent" slot-scope="scope">
                        {{ scope.row.ReportContent || '无' }}
                    </template>

                    <template slot="AfterSaleMaintenanceStatus" slot-scope="scope">
                        <span class="item-status" :style="{backgroundColor: getStatusObj(scope.row.AfterSaleMaintenanceStatus).color}">
                            {{ scope.row.AfterSaleMaintenanceStatus | statusFilter }}
                        </span>
                    </template>

                    <!-- 表格查询条件区域 -->
                    <template slot="conditionArea">
                        <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                            <template slot="Keywords">
                                <el-input style="width: 100%;" 
                                    placeholder="输入报修人名称/值班电话"
                                    @clear='getList'
                                    v-antiShake='{
                                        time: 300,
                                        callback: () => {
                                            getList()
                                        }
                                    }' 
                                    clearable 
                                    v-model="listQuery.Keywords"
                                ></el-input>
                            </template>

                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
                            </template>
                        </app-table-form>
                    </template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button v-if="rowBtnIsExists('btnTrans') && (scope.row.AfterSaleMaintenanceStatus == 1 || scope.row.AfterSaleMaintenanceStatus == 2)" @click="handleTrans(scope.row)" :type="1" text='转为工单'></app-table-row-button>
                        <app-table-row-button v-if="scope.row.AfterSaleMaintenanceStatus == 3" @click="handleReview(scope.row)" :type="1" text='查看报修单'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnHandle') && scope.row.AfterSaleMaintenanceStatus == 1" @click="notHandle(scope.row)" :type="3" text='不处理'></app-table-row-button>
                        <!-- <app-table-row-button v-if="scope.row.AfterSaleMaintenanceStatus == 2" @click="handleDel(scope.row)" :type="3"></app-table-row-button> -->
                    </template>
                </app-table>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>
    <create-page
      v-if="dialogFormVisible"
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :transId="transId"
      :id='id'
      @reload="getList"
    ></create-page>
    

</div>
</template>

<script>

import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";

import * as customerRepair from "@/api/maintenanceCenter/customerRepair";
// import { employeeWorkingStateEnum, statusEnum } from "../enum";
import { vars } from '../common/vars'
import createPage from '../maintenOrderMgmt/create'
export default {
    name: "customer-repair",
    mixins: [indexPageMixin],
    components: {
        createPage,
    },
    props: {},
    filters: {
        statusFilter(status) {
            let obj = vars.orderTypes.find(s => s.value == status)
            if(obj) {
                return obj.label
            }
            return status
        }
        // workingStateFilter(status) {
        //     const statusObj = employeeWorkingStateEnum.find((s) => s.value == status);
        //     if (statusObj) {
        //         return statusObj.label;
        //     }
        //     return "";
        // },
        // statusFilter(status) {
        //     const statusObj = statusEnum.find((s) => s.value == status);
        //     if (statusObj) {
        //         return statusObj.label;
        //     }
        //     return "";
        // },
        // sexFilter(value) {
        //     let obj = vars.common.genders.find(s => s.value == value)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return ''
        // },

        // nodutyFilter(value) {
        //     let duty = value.split(",");
        //     return duty[0];
        // },

    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
    },
    watch: {
    },
    created() {
        this.getList()
    },
    data() {
        return {
            layoutMode: 'simple',
            orderTypes: vars.orderTypes.concat({value: 0, label: '全部'}),

            tableSearchItems: [{
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },
            ],

            id: "",
            transId: '',
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "RegionalName",
                        label: "报修地区",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "AfterSaleMaintenanceStatus",
                        label: "状态",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "ReportEmployee",
                        label: "报修人姓名",
                    },
                },
                {
                    attr: {
                        prop: "ReporterNumber",
                        label: "值班电话",
                        // sortable: "custom",
                    },
                },
                {
                    attr: {
                        prop: "ReportContent",
                        label: "报修内容",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ReportTime",
                        label: "报修时间",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "PhoneNumber",
                        label: "手机号",
                    },
                },
            ],
            listQuery: {
                Keywords: "",
                AfterSaleMaintenanceStatus: 1,
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,


        };
    },
    methods: {
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                customerRepair.del([row.Id]).then(() => {
                this.$notify({
                    title: "成功",
                    message: "删除成功",
                    type: "success",
                    duration: 2000
                });
                    this.getList();
                });
            });
        },
        getStatusObj(val) {
            let obj = vars.orderTypes.find(s => s.value == val)
            return obj || {}
        },
        onBtnClicked: function(domId) {
            // switch (domId) {
            //     case "btnAdd":
            //     break;
            //     case "btnExport":
            //     break;
            //     case "btnParamSetting":
            //     break;
            //     default:
            //     break;
            // }
        },
        handleSortChange({column, prop, order}) {
            // this.sortObj = {
            //     prop,
            //     order,
            // };
            this.getList();
        },
        handleTagsChange(){
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        //获取成员列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            // postData = this.assignSortObj(postData);
            customerRepair
                .getList(postData)
                .then((res) => {
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                    this.listLoading = false;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
            
        },






        // 多行删除
        // handleTableDelete() {
        //     if (this.multipleSelection.length < 1) {
        //         this.$message({
        //             message: "至少删除一个",
        //             type: "error",
        //         });
        //         return;
        //     }
        //     let ids = [];
        //     if (_.isArray(this.multipleSelection)) {
        //         ids = this.multipleSelection.map((u) => u.EmployeesId);
        //     } else {
        //         ids.push(this.multipleSelection.EmployeesId);
        //     }

        //     this.$confirm("是否确认删除?", "提示", {
        //         confirmButtonText: "确认",
        //         cancelButtonText: "取消",
        //         type: "warning",
        //     }).then(() => {
        //         customerRepair.del(ids).then(() => {
        //             this.$notify({
        //                 title: "成功",
        //                 message: "删除成功",
        //                 type: "success",
        //                 duration: 2000,
        //             });
        //             this.getList();
        //         });
        //     });
        // },

        // 弹出添加框
        handleTrans(row) {
            this.transId = row.Id;
            this.dialogStatus = 'create'
            this.dialogFormVisible = true;
        },
        handleReview(row) {
            this.id = row.MaintenanceId
            this.dialogStatus = 'detail'
            this.dialogFormVisible = true;
        },
        // 弹出编辑框
        notHandle(row) {
            this.$confirm(`是否确认不处理?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                //项目创建审批
                customerRepair.noProcess({id: row.Id}).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "处理成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            })
        },
        onResetSearch() {
            this.listQuery.Keywords = "";
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },


    },
};
</script>

<style lang="scss" scoped>
.content{
    padding-top: 0!important;
    .btns-wrapper{
        button{
            margin-left: 4px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}
// .treeBox {
//     width: 100%;
//     height: calc(100% - 10px);
//     margin-top: 10px;

//     .elInput {
//         width: 230px;
//         margin-left: 10px;
//     }

//     .elTree {
//         height: calc(100% - 38px);
//         overflow: auto;
//         margin-top: 10px;
//         padding-bottom: 10px;
//     }
// }

.pageWrapper {
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .content {
        padding: 10px;
        padding-right: 0;
        padding-left: 0;

        .opt-wrapper {
            box-sizing: border-box;
            border-bottom: 1px solid #dcdfe6;
            padding-bottom: 10px;
        }
    }

}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
