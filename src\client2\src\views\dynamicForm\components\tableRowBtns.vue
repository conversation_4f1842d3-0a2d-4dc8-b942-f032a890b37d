<template>
    <div>
        <el-button type="text" @click="handleAdd">配置表格行内按钮</el-button>
        <el-dialog title="按钮列表" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false" :before-close="handleClose" :append-to-body="true">
            <el-button type="text" @click="handleCreate">新增</el-button>
            <el-table :data="datas" border style="width: 100%">
                <el-table-column prop="id" label="按钮id"></el-table-column>
                <el-table-column prop="label" label="显示文字"></el-table-column>
                <el-table-column label="操作" width="120px">
                    <template slot-scope="scope">
                        <el-button type="text" @click="handleUpdate(scope.$index)">编辑</el-button>
                        <el-button type="text" @click="handleRemove(datas, scope.$index)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div slot="footer">
                <el-button size="mini" @click="dialogVisible = false">取消</el-button>
                <el-button size="mini" type="primary" @click="createData">确认</el-button>
            </div>
        </el-dialog>

        <el-dialog title="按钮详情" :visible.sync="editPageDialogVisible" width="800px" :close-on-click-modal="false" :before-close="handleCloseEditPage" :append-to-body="true">
            <el-form ref="dataForm" :rules="rules" :model="temp" label-width="80px">
                <el-form-item label="按钮id" prop="id">
                    <el-input v-model="temp.id"></el-input>
                </el-form-item>
                <el-form-item label="显示文字" prop="label">
                    <el-input v-model="temp.label"></el-input>
                </el-form-item>
                <el-form-item label="执行代码" prop="code">
                    <el-input type="textarea" :rows="5" v-model="temp.code"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button size="mini" @click="editPageDialogVisible = false">取消</el-button>
                <el-button size="mini" type="primary" @click="createButtom">确认</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
export default {
    name: 'table-row-btns',
    model: {
        prop: 'list',
        event: 'change'
    },
    mixins: [indexPageMixin],
    props: {
        list: {
            type: [Array, String],
            // default: []
        }
    },
    watch: {
        editPageDialogVisible(val) {
            if(!val) {
                this.temp = {
                    id: '',
                    label: '',
                    code: ''
                }
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
        let tmp = []
        if(this.list) {
            tmp = JSON.parse(JSON.stringify(this.list))
        }
        this.datas = tmp
    },
    data () {
        return {
            datas: [],
            dialogVisible: false,
            editPageDialogVisible: false,
            temp: {
                id: '',
                label: '',
                code: ''
            },
            rules: {
                id: { fieldName: "按钮id", rules: [{ required: true }, { max: 50 }] },
                label: { fieldName: "显示文字", rules: [{ required: true }, { max: 50 }] },
            },
            editDialogStatus: 'create',
            editIdx: -1,
        }
    },
    methods: {
        handleClose() {

        },
        handleCreate() {
            this.editDialogStatus = 'create'
            this.openEditPage()
        },
        handleAdd() {
            this.openDialog()
        },
        createData() {
            let emitDatas = JSON.parse(JSON.stringify(this.datas))
            this.$emit('change', emitDatas)
            this.closeDialog()
        },
        createButtom() {
            this.$refs["dataForm"].validate(valid => {
                if(valid) {
                    if(this.editDialogStatus == 'create') {
                        this.datas.push(this.temp)
                    }else if(this.editDialogStatus == 'update') {
                        this.datas.splice(this.editIdx, 1, this.temp)
                    }
                    this.handleCloseEditPage()
                }
            })
        },
        handleUpdate(idx) {
            this.editIdx = idx
            let old = this.datas[idx]
            this.editDialogStatus = 'update'
            this.temp = Object.assign({}, old)
            this.openEditPage()
        },
        handleRemove(list, idx) {
            list.splice(idx, 1)
        },
        openDialog() {
            this.dialogVisible = true
        },
        closeDialog() {
            this.dialogVisible = false
        },   
        openEditPage() {
            this.editPageDialogVisible = true
        },
        handleCloseEditPage() {
            this.editPageDialogVisible = false
        },     
    },
}
</script>

<style scoped>

</style>