<template>
  <div class="main-page-content">
    <app-table-form
      :label-width="'140px'"
      :items="tableSearchItems"
      @onSearch="handleFilter"
      @onReset="handleResetSearch"
    >
      <template slot="ImplementationName">
        <el-input v-model.trim="listQuery.ImplementationName"></el-input>
      </template>
      <template slot="ContractNumber">
        <el-input v-model.trim="listQuery.ContractNumber"></el-input>
      </template>
      <template slot="Status">
        <el-select
          style="width: 100%;"
          v-model="listQuery.Status"
          placeholder=""
          clearable
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </template>
    </app-table-form>
    
    <div style="padding: 10px 0;">
      <span>显示字段：</span
      ><el-checkbox v-model="impChecked">重点关注</el-checkbox>&emsp;
      <!-- <permission-btn v-on:btn-event="onBtnClicked"></permission-btn> -->
    </div>

    <div class="table-wrapper" ref="tableWrapper">
      <el-table
        :data="tabDatas"
        style="width: 100%"
        v-loading="listLoading"
        :max-height="tableWrapperHeight"
      >
        <!-- <el-table-column v-for='(item, idx) in columns' :key='idx'
                    :fixed='item.prop == "address" ? false : true'
                    :prop="item.prop"
                    :label="item.label"
                    :width="item.width">
                </el-table-column> -->
        <el-table-column fixed prop="No" label="序号" width="48">
          <template slot-scope="scope">
            {{
              (listQuery.PageIndex - 1) * listQuery.PageSize +
                (scope.$index + 1)
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          prop="Name"
          label="项目名称（实施工程）"
          min-width="350"
        ></el-table-column>
        <el-table-column prop="Status" label="状态" min-width="120">
          <template slot-scope="scope">
            <span :style="{ color: getStatusObj(scope.row.Status).color }">
              {{ scope.row.Status | findState }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="ContractNumber" label="合同编号" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.ContractNumber || "无" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="ContractPrice"
          label="合同金额"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="IsFinished" label="整体进度" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.IsFinished }}/{{ scope.row.EquipmentCount }}
          </template>
        </el-table-column>
        <el-table-column
          prop="RegionalFoucsContents"
          label="地区进度/重点关注"
          :width="dynamicWidth"
          v-if="impChecked"
        >
          <template slot-scope="scope">
            <div
              style="white-space: nowrap;"
              v-for="(item, idx) in scope.row.RegionalFoucsContents"
              :key="idx"
            >
              【
              {{ item.RegionalName }}：<span
                :style="{ color: `${getRegionalStatus(item.Status).color}` }"
                >{{ item.Progress }}% ({{
                  getRegionalStatus(item.Status).label
                }})</span
              >
              <span
                v-if="
                  item.FoucsContentItemModels &&
                    item.FoucsContentItemModels.length > 0
                "
              >
                重点关注：
                <span
                  v-for="(pro, idx2) in item.FoucsContentItemModels"
                  :key="idx2"
                  >【{{ pro.ItemName }}
                  <span
                    :style="{ color: `${getProcesStatus(pro.Status).color}` }"
                    >{{ pro.Progress }}% ({{
                      getProcesStatus(pro.Status).label
                    }})</span
                  >】</span
                >
              </span>
              】
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- <app-table-core
                ref="mainTable"
                :tab-columns="tabColumns"
                :tab-datas="tabDatas"
                :tab-auth-columns="tabAuthColumns"
                :isShowAllColumn="true"
                :loading="listLoading"
                :isShowOpatColumn="false"
                :startOfTable="startOfTable"
                :serial='false'
                :multable='false'
                :height='tableWrapperHeight'
            >
                <template slot="No" slot-scope="scope">
                    <span>
                        {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index + 1) }}
                    </span>
                </template>
                <template slot="Status" slot-scope="scope">
                    <span :style="{color: getStatusObj(scope.row.Status).color}">
                        {{ scope.row.Status | findState }}
                    </span>
                </template>
                <template slot="IsFinished" slot-scope="scope">
                    {{ scope.row.IsFinished }}/{{ scope.row.EquipmentCount }}
                </template>
                <template slot="RegionalFoucsContents" slot-scope="scope" v-if="impChecked">
                    <div style="white-space: nowrap;" v-for="(item, idx) in scope.row.RegionalFoucsContents" :key="idx">
                        【
                            {{item.RegionalName}}：<span :style="{color: `${getRegionalStatus(item.Status).color}`}">{{item.Progress}}% ({{ getRegionalStatus(item.Status).label }})</span> 
                            <span v-if="item.FoucsContentItemModels && item.FoucsContentItemModels.length > 0">
                                重点关注：
                                <span v-for="(pro, idx2) in item.FoucsContentItemModels" :key="idx2">
                                    【{{pro.ItemName}} <span :style="{color: `${getProcesStatus(pro.Status).color}`}">{{pro.Progress}}% ({{getProcesStatus(pro.Status).label}})</span>】
                                </span>
                            </span>
                        】
                    </div>
                </template>
                
            </app-table-core> -->
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageIndex"
      :size.sync="listQuery.PageSize"
      @pagination="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData="cData"
    >
    </v-export>
  </div>
</template>



<script>
import indexPageMixin from "@/mixins/indexPage";
import * as afterService from "@/api/afterSalesMgmt/afterService";
import { getUserInfo } from "@/utils/auth";
import * as impManagement from "@/api/implementation/impManagement";
import { vars } from "../common/vars";
import layoutVue from "../../login/layout.vue";
import vExport from "@/components/Export/index";
export default {
  name: "after-service",
  mixins: [indexPageMixin],
  components: { vExport },
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    findState(d) {
      let result = vars.implementationSatus.find(v => v.value == d);
      if(result) {
        return result.label;
      }
      return ''
    }
  },
  created() {
    this.getList();
  },
  watch: {
    isShow(val) {
      if (val) {
        let that = this;
        that.dynamicComputedHeight();
        window.onresize = () => {
          if (that.resizeFlag) {
            clearTimeout(that.resizeFlag);
          }
          that.resizeFlag = setTimeout(() => {
            that.dynamicComputedHeight();
            that.resizeFlag = null;
          }, 100);
        };
      }
    },
    impChecked(val) {
      // let comumnName = 'RegionalFoucsContents'
      // let dynamicColumn = {
      //     attr: { prop: comumnName, label: "地区进度/重点关注" },
      //     slot: true
      // }
      // if(val) {
      //     if(this.tabColumns.findIndex(s => s.attr.prop == comumnName) == -1) {
      //         this.tabColumns.push(dynamicColumn)
      //     }
      // }else{
      //     let idx = this.tabColumns.findIndex(s => s.attr.prop == comumnName)
      //     if(idx > -1) {
      //         this.tabColumns.splice(idx, 1)
      //     }
      // }
      // this.dynamicComputedHeight()
    },
    /**
     * 监控表格的数据data，自动设置表格宽度
     * @param valArr
     */
    tabDatas(valArr) {
      let colName = "RegionalFoucsContents";
      let arr = valArr.reduce((prev, curr) => {
        prev = prev.concat(curr[colName].map(a => a.text));
        return prev;
      }, []);
      let dynamicWidth = this.getMaxLength(arr) + 20; // 每列内容最大的宽度 + 表格的内间距(依据实际情况而定)
      this.dynamicWidth = dynamicWidth < 180 ? 180 : dynamicWidth; //最小宽度（标题）

      // const _this = this
      // let colName = 'RegionalFoucsContents'
      // let cusCol = this.tabColumns.find(s => s.attr.prop == colName)
      // if(cusCol) {

      //     let arr = valArr.reduce((prev, curr) => {
      //         prev = prev.concat(curr[colName].map(a => a.text))
      //         return prev
      //     }, [])

      //     let dynamicWidth = _this.getMaxLength(arr) + 20 // 每列内容最大的宽度 + 表格的内间距(依据实际情况而定)
      //     cusCol.attr.width = dynamicWidth < 180 ? 180 : dynamicWidth //最小宽度（标题）

      //     let idx = this.tabColumns.findIndex(s => s.attr.prop == colName)
      //     this.tabColumns.splice(idx, 1, cusCol)
      // }
    }
  },
  mounted() {
    let that = this;
    that.dynamicComputedHeight();
  },
  data() {
    return {
      dynamicWidth: 0,
      resizeFlag: null,
      tableWrapperHeight: 400,
      impChecked: true,
      statusList: vars.regionalStatus,
      total: 0,
      listLoading: false,
      listQuery: {
        ImplementationName: "",
        ContractNumber: "",
        Status: null
      },
      multipleSelection: [],
      tableSearchItems: [
        { prop: "ImplementationName", label: "项目名称(实施工程)" },
        { prop: "ContractNumber", label: "合同编号" },
        { prop: "Status", label: "状态" }
      ],
      tabColumns: [
        {
          attr: { prop: "No", label: "序号", width: "48", fixed: "left" },
          slot: true
        },
        {
          attr: {
            prop: "Name",
            label: "项目名称（实施工程）",
            minWidth: "320",
            fixed: "left"
          }
        },
        {
          attr: { prop: "Status", label: "状态", width: "120", fixed: "left" },
          slot: true
        },
        {
          attr: {
            prop: "ContractNumber",
            label: "合同编号",
            width: "120",
            fixed: "left"
          },
          slot: true
        },
        {
          attr: {
            prop: "ContractPrice",
            label: "合同金额",
            width: "120",
            fixed: "left"
          }
        },
        {
          attr: {
            prop: "IsFinished",
            label: "整体进度",
            width: "100",
            fixed: "left"
          },
          slot: true
        },
        {
          attr: { prop: "RegionalFoucsContents", label: "地区进度/重点关注" },
          slot: true
        }
      ],
      tabDatas: [],
      rData: null,
      cData: [],
      dialogExportVisible: false
    };
  },
  beforeRouteLeave(to, from, next) {
    //离开组件的时候触发
    if (this.resizeFlag) {
      clearTimeout(this.resizeFlag);
    }

    window.onresize = null;
    next();
  },
  methods: {
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    onBtnClicked() {
      this.handleExport();
    },
    handleExport() {
      this.rData = {
        exportSource: 6,
        columns: [],
        searchCondition: this.listQuery
      };
      this.cData = [
        {
          label: "序号",
          value: "Number"
        },
        {
          label: "项目名称（实施工程）",
          value: "Name"
        },
        {
          label: "状态",
          value: "Status"
        },
        {
          label: "合同编号",
          value: "ContractNumber"
        },
        {
          label: "合同金额",
          value: "ContractPrice"
        },
        {
          label: "整体进度",
          value: "TotalProgress"
        },
        {
          label: "地区进度/重点关注",
          value: "RegionalFoucsContentString"
        }
      ];
      this.dialogExportVisible = true;
    },
    dynamicComputedHeight() {
      let tableWrapper = this.$refs.tableWrapper;
      if (tableWrapper) {
        this.tableWrapperHeight = tableWrapper.clientHeight;
      }
    },
    getStatusObj(status) {
      return vars.implementationSatus.find(s => s.value == status) || {};
    },
    getRegionalStatus(val) {
      return vars.regionalStatus.find(s => s.value == val) || {};
    },
    getProcesStatus(val) {
      return vars.processStatus.find(s => s.value == val) || {};
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        ImplementationName: "",
        ContractNumber: "",
        Status: null
      };
      this.getList(); //刷新列表
    },
    //获取项目列表
    getList() {
      this.listLoading = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      let that = this;
      impManagement
        .getImplementationStatement(postData)
        .then(res => {
          this.listLoading = false;
          //用户动态计算宽度（不用于显示，所以需要和显示的格式保持一致）
          this.tabDatas = res.Items.map(s => {
            if (
              s &&
              s.RegionalFoucsContents &&
              s.RegionalFoucsContents.length > 0
            ) {
              s.RegionalFoucsContents.map(item => {
                let loopText = "";
                if (
                  item.FoucsContentItemModels &&
                  item.FoucsContentItemModels.length > 0
                ) {
                  loopText += "重点关注：";
                  for (let i = 0; i < item.FoucsContentItemModels.length; i++) {
                    let pro = item.FoucsContentItemModels[i];
                    loopText += `【${pro.ItemName} ${pro.Progress}% (${
                      that.getProcesStatus(pro.Status).label
                    })】`;
                  }
                }
                item.text = `【${item.RegionalName}：${item.Progress}% (${
                  that.getRegionalStatus(item.Status).label
                }) ${loopText}】`;
                return item;
              });
            }
            return s;
          });
          this.total = res.Total;
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    getMaxLength(arr) {
      return arr.reduce((acc, item) => {
        if (item) {
          let calcLen = this.getTextWidth(item);
          if (acc < calcLen) {
            acc = calcLen;
          }
        }
        return acc;
      }, 0);
    },
    /**
     * 使用span标签包裹内容，然后计算span的宽度 width： px
     * @param valArr
     */
    getTextWidth(str) {
      let width = 0;
      let html = document.createElement("span");
      html.innerText = str;
      html.className = "getTextWidth";
      //字体大小需要和表格中显示字体保持一致，否则宽度计算不合
      html.style =
        "display: inline-block; overflow-x: auto; color: red; white-space:nowrap; font-size: 12px;";
      document.querySelector("body").appendChild(html);
      width = document.querySelector(".getTextWidth").offsetWidth;
      document.querySelector(".getTextWidth").remove();
      return width;
    }
  }
};
</script>


<style lang="scss" scoped>
.main-page-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .table-wrapper {
    flex: 1;
  }
}
</style>