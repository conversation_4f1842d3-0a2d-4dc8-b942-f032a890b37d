<template>
    <div>
        <app-dialog :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :width='1000'
            :maxHeight="630"
        >
            <template slot="body">
                <div class="temBody newCasesForm" v-loading="loading">
                    <el-form
                        :rules="rules"
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="110px">
                        <div>
                            <el-card class="box-card">
                                <div class="bm">
                                    <el-form-item label="故障现象" prop="Name">
                                        <div>
                                            <span class="nSpan" v-show="formData.IsNew">新</span>
                                            <span>{{formData.FailureSymptom}}</span>
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="故障现象代码" prop="FailureCaseCode" class="b0" v-if="formData.IsNew">
                                        <el-input v-model.trim="formData.FailureCaseCode" @blur="handleBlur" :disabled="!editable" maxlength="10" :placeholder="placeholderText" onkeyup="value=value.replace(/[^\w/]/ig,'')" :rules="formData.IsNew ? rules.FailureCaseCode : null"></el-input>
                                    </el-form-item>
                                    <el-form-item label="故障关键字" prop="EquipmentSettingIds" class="b6" v-show="formData.IsNew">
                                        <div class="cl">
                                            <el-button class="elButton fl" type="text" v-show="editable" @click="addKey()">添加</el-button>
                                            <span class="elSpan fl" v-for="(kl,index) in keyList" :key="index">
                                            {{kl.FaultKeyWordName}}
                                            <i class="el-icon-error" v-show="editable" @click="delKey(index)"></i>
                                            </span>
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="故障分类标签" prop="FaultKeywordIdList" class="b6" v-show="formData.IsNew">
                                        <el-button type="text" v-show="editable" @click="addLabel()">添加</el-button>
                                        <ul class="elUl" v-if="selectedMsg">
                                            <li>故障类型：{{selectedMsg.Name}}</li>
                                            <li v-for="(sd,index) in selectedData" v-show="sd.checkList.length>0" :key="index">{{sd.Name}}：{{sd.checkList.join('、')}}</li>
                                        </ul>
                                    </el-form-item>
                                    <el-form-item label="相关附件" prop="AttachmentList" class="b6" v-show="formData.IsNew">
                                        <!-- <app-upload-big-file :limitTotalSize="1024 * 1024 * 1024" :max="10000" accept="all" :fileType="4" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" @change="handleFilesUpChange"></app-upload-big-file> -->
                                        <app-uploader
                                        :readonly="!editable"
                                        accept='all'
                                        :fileType='3'
                                        :max='10000'
                                        :value='formData.AttachmentList'
                                        :fileSize='1024 * 1024 * 500'
                                        :minFileSize='100 * 1024'
                                        @change='handleFilesUpChange'
                                    ></app-uploader>
                                    </el-form-item>
                                </div>
                            </el-card>
                            <el-card class="box-card">
                                <div v-for="(cal,index) in formData.FailureAnalysiseList" :key="index" class="bm">
                                    <el-form-item label="故障原因" :prop="'FailureAnalysiseList.'+index+'.FailureReson'" class="b6">
                                        <el-checkbox class="elCheckBox" :disabled="!editable" v-model="cal.checked" @change="handleCheck(cal)"></el-checkbox>
                                        <div>
                                            <span class="nSpan" v-show="cal.IsNew">新</span>
                                            <span>{{cal.FailureReson}}</span>
                                        </div>
                                    </el-form-item>
                                    <!-- <el-form-item label="故障原因代码" class="b0" :prop="'FailureAnalysiseList.'+index+'.FailureResonCode'" :rules="cal.checked ? rules.FailureResonCode : [{ required: false}]"> -->
                                        <el-form-item label="故障原因代码" class="b0" :prop="'FailureAnalysiseList.'+index+'.FailureResonCode'" :rules="cal.rules">
                                        <el-input v-model.trim="cal.FailureResonCode" :disabled="!editable" maxlength="10" :placeholder="placeholderCaseText" onkeyup="value=value.replace(/[^\w/]/ig,'')"></el-input>
                                    </el-form-item>
                                </div>
                            </el-card>
                            <el-card class="box-card">
                                <div v-for="(s,index) in formData.SolutionList" :key="index"  class="bm">
                                    <el-form-item label="解决方法" :prop="'SolutionList.'+index+'.Solution'" class="b6">
                                        <el-checkbox class="elCheckBox" :disabled="!editable" v-model="s.checked" @change="handleCheck(s)"></el-checkbox>
                                        <div>
                                            <span class="nSpan" v-show="s.IsNew">新</span>
                                            <span>{{s.Solution}}</span>
                                        </div>
                                    </el-form-item>
                                    <!-- <el-form-item label="解决方法代码" class="b0" :prop="'SolutionList.'+index+'.FailureSolutionCode'" :rules="s.checked ? rules.FailureSolutionCode : [{ required: false}]"> -->
                                        <el-form-item label="解决方法代码" class="b0" :prop="'SolutionList.'+index+'.FailureSolutionCode'" :rules="s.rules">
                                        <el-input v-model.trim="s.FailureSolutionCode" :disabled="!editable" maxlength="10" :placeholder="placeholderSolutionText" onkeyup="value=value.replace(/[^\w/]/ig,'')"></el-input>
                                    </el-form-item>
                                    <el-form-item label="相关附件" prop="Name3" class="b6">
                                        <!-- <app-upload-big-file :limitTotalSize="1024 * 1024 * 1024" :max="10000" accept="all" :fileType="4" :value="s.AttachmentList" :fileSize="1024 * 1024 * 500" @change="handleFilesUpChange"></app-upload-big-file> -->
                                        <app-uploader
                                        :readonly="!editable"
                                        accept='all'
                                        :fileType='3'
                                        :max='10000'
                                        :value='s.AttachmentList'
                                        :fileSize='1024 * 1024 * 500'
                                        :minFileSize='100 * 1024'
                                        @change='(files) => handleFilesUpChange1(files,s)'
                                    ></app-uploader>
                                    </el-form-item>
                                </div>
                            </el-card>
                        </div>
                        <div>
                            <!-- <div class="panel-title">审批</div> -->
                            <div>
                                    <approval-panel v-if="dialogStatus == 'create'" ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                                    <approval-detail v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                            </div>
                        </div>
                    </el-form>
                </div>
            </template>
            <template slot="footer">
                <el-button @click="handleClose" size="mini">取消</el-button>
                <app-button @click="handleSuccess" v-show="dialogStatus == 'create'" :buttonType='1' :disabled='disabledBtn'></app-button>
                <el-button @click="handleApproval" type="primary" size="mini" v-show="dialogStatus == 'approval' && !isOnlyViewDetail">审批</el-button>
            </template>
        </app-dialog>
        <fault-key
            @closeDialog="closeKeyDialog"
            @saveSuccess="handleKeySuccess"
            :dialogFormVisible="dialogKeyFormVisible"
            :keyList="keyList">
        </fault-key>

        <faultLabel
            @closeDialog="closeLabelDialog"
            @saveSuccess="handleLabelSuccess"
            :dialogFormVisible="dialogLabelFormVisible">
        </faultLabel>
    </div>
</template>
<script>
import approvalPanel from "../../projectDev/projectMgmt/common/approvalPanel";
import approvalDetail from "../../projectDev/projectMgmt/workbench/common/approvalDetail";
import faultKey from '../../failureCase/common/faultKey'
import faultLabel from '../../failureCase/common/faultLabel'
import * as failurecase from '@/api/failurecase';
import * as equipmentSetting from '@/api/equipmentSetting';
import { listToTreeSelect } from '@/utils';
import { vars } from "../../projectDev/common/vars";
export default{
    name:'',
    // mixins: [indexPageMixin],
    components: {
        approvalPanel,
        approvalDetail,
        faultKey,
        faultLabel
    },
    props:{
        specialPageTitle: {
            type: String
        },
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: 'create'
        },
        caseData:{
            type:Object,
            default:null
        },
        id: {
      type: String,
      default: "",
    },
    approvalId: {
      // 审批编号，从审批列表中弹出该页面时需要
      type: String,
      default: "",
    },
    //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
    isOnlyViewDetail: {
      type: Boolean,
      default: false,
    },
    },
    computed:{

    },
    data(){
        var caseValidate = (rule, value, callback) => {
            if (this.smsMsg) {
                callback(new Error('故障现象代码不可重复!'));
            }else{
                callback();
            }
            };
        return{
            smsMsg:null,
            placeholderText:'',
            placeholderCaseText:'',
            placeholderSolutionText:'',
            loading:false,
            keyList:[],
            dialogLabelFormVisible:false,
            dialogKeyFormVisible:false,
            disabledBtn:false,
            rules: {
                FailureCaseCode:[{ required: true, message: '故障现象代码不能为空'},{validator: caseValidate}],
                FailureResonCode:[{ required: true, message: '故障原因代码不能为空'}],
                FailureSolutionCode:[{ required: true, message: '解决方法代码不能为空'}],
            },
            tabDatas:[],
            formData:{
                IsNew:false,
                "MaintenanceEquipmentId": "",
                "FailureCaseId": "",
                Approval: {
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
                "Id": "",
                "FailureCaseCode": "",
                "FailureSymptom": "",
                "EquipmentSettingIds": [],
                "FaultKeywordIdList": [],
                "AttachmentIdList": [],
                "SolutionList": [],
                "FailureAnalysiseList": []
                },
        }
    },
    filters: {

    },
    watch: {
        "$attrs.dialogFormVisible":{
            handler(val) {
                if(val){
                    this.$store.commit('getselectedData',[]);
                    this.$store.commit('getSelectedMsg',null);
                    this.keyList=[];
                    this.placeholderText='';
                    this.placeholderCaseText='';
                    this.placeholderSolutionText='';
                    if(this.dialogStatus == 'create'){
                        failurecase.getCode().then(res => {
                            console.log(res)
                            this.placeholderText='最近一次添加代码：'+res.FailureAnalysiseCode;
                            this.placeholderCaseText='最近一次添加代码：'+res.FailureCaseCode;
                            this.placeholderSolutionText='最近一次添加代码：'+res.FailureSolutionCode;
                        });
                        this.getList()
                        this.createData();
                    }

                    if (this.dialogStatus != "create" && this.id) {
                        this.resetFormData();
                        this.getDetail();
                    }
                }
            },
            immediate: true
        },
    },
    computed:{
        editable() {
            //详细和审批模式都不可编辑
            return this.dialogStatus == "create"
        },
        selectedMsg(){
            return this.$store.state.communication.selectedMsg;
        },
        selectedData(){
            return this.$store.state.communication.selectedData;
        },
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            if (this.dialogStatus == "create") {
                return "申报新案例";
            } else if (this.dialogStatus == "approval") {
                return "申报新案例审批";
            }else{
                return '申报新案例详情';
            }
        },
    },
    created(){

    },
    mounted(){

    },
    methods:{
        handleBlur(){

            this.smsMsg=this.tabDatas.find(s => s.FailureCaseCode == this.formData.FailureCaseCode);
            // if(a){
            //     this.$message.error('故障现象代码不可重复!');
            //     this.$refs[formName].validateField("FailureCaseCode");
            // }
        },
        getList() {
            this.loading = true;
            let listQuery={
                FailureCaseCode: "",
                FailureSymptom: "",
                Keywords: "",
                PageIndex: 1,
                PageSize: 99999,
                EquipmentSettingIdList:[]
            }
            failurecase.getListPage(listQuery).then(res => {
                this.tabDatas = res.Items;
                this.loading = false;
            }).catch(err => {
                this.loading = false
            });
        },
        handleCheck(d){
            if(d.checked){
                d.rules[0].required=true;
            }else{
                d.rules[0].required=false;
            }
            this.$refs.formData.clearValidate();
            this.$refs.formData.validate()
        },
        resetFormData(){
             this.formData={
                IsNew:false,
                "MaintenanceEquipmentId": "",
                "FailureCaseId": "",
                Approval: {
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
                "Id": "",
                "FailureCaseCode": "",
                "FailureSymptom": "",
                "EquipmentSettingIds": [],
                "FaultKeywordIdList": [],
                "AttachmentIdList": [],
                "SolutionList": [],
                "FailureAnalysiseList": []
                }
        },
       getDetail(){
      this.loading=true;
      let a=failurecase.licationGetDetails({ id: this.id, approvalId: this.approvalId }),
          b=equipmentSetting.getListPage({"PageIndex":1,"PageSize": 9999,});
      Promise.all([a,b]).then((res) => {
        this.listData = listToTreeSelect(res[1].Items)
        this.formData=Object.assign(this.formData,res[0]);
        this.formData.SolutionList=res[0].FailureSolutionList;
        this.keyList=res[0].FaultKeywordList;
        if(res[0].EquipmentSettingIds.length>0){
          let c=this.listData.find(s => s.Id == res[0].EquipmentSettingIds[0]),d=null;
          this.$store.commit('getSelectedMsg',c);
          c.children.forEach(v => {
            v.checkList=[];
            v.children.forEach(v1 => {
              d=res[0].EquipmentSettingIds.find(s => s == v1.Id);
              if(d){
                v.checkList.push(v1.Name);
              }
            })
          })
          this.$store.commit('getselectedData',c.children);
        }
        this.loading=false;
      })
    },
        getTreeList(){
            this.loading=true;
            equipmentSetting.getListPage({"PageIndex":1,"PageSize": 9999,}).then(res => {
                this.loading=false;
            })
        },
        delKey(index){
            this.keyList.splice(index, 1);
        },
        addKey(){
            this.dialogKeyFormVisible=true;
        },
        addLabel(){
            this.dialogLabelFormVisible=true;
        },
        closeLabelDialog(){
            this.dialogLabelFormVisible=false;
          },
          handleLabelSuccess() {
      this.closeLabelDialog();
    },
          closeKeyDialog(){
            this.dialogKeyFormVisible=false;
          },
          handleKeySuccess(d) {
      this.keyList = d;
    },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        handleFilesUpChange1(files,s){
            s.AttachmentList = files
        },
        createData(){
            let data=JSON.parse(JSON.stringify(this.caseData));
                        data.SolutionList.forEach(s => {
                            s.rules=[{ required: false, message: '故障原因代码不能为空'}];
                            s.checked=false;
                            s.FailureCaseId=s.FailureCaseSolutionId;
                            s.FailureSolutionCode='';
                            s.Solution=s.SolutionContent;
                            s.AttachmentIdList=[];
                        });
                        data.SolutionList=data.SolutionList.filter(s => s.IsNew);
                        data.CauseAnalysisList.forEach(s => {
                            s.rules=[{ required: false, message: '解决方法代码不能为空'}];
                            s.checked=false;
                            s.FailureCaseId=s.FailureCaseAnalysisId;
                            s.FailureResonCode='';
                            s.FailureReson=s.AnalysisContent;
                        });
                        data.CauseAnalysisList=data.CauseAnalysisList.filter(s => s.IsNew);
                        this.formData={
                            IsNew:data.IsNew,
                            "MaintenanceEquipmentId": data.MaintenanceEquipmentId,
                            "FailureCaseId": data.FailureCaseId,
                            Approval: {
                                ApprovalEmployeeList: [[]],
                                ApprovalType: 1,
                                ApprovalOperatorEmployeeList: [], //已审批人员
                                NoApprovalEmployeeList: [], //未审批人员
                                CCEmployeeList: [], //抄送人
                                ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                                ApprovalState: 1, //1: 进行中; 2: 已完成
                                ApprovalResult: 1, //1: 通过； 2：不通过
                            },
                            "Id": data.Id,
                            "FailureCaseCode": "",
                            "FailureSymptom": data.Phenomenon,
                            "EquipmentSettingIds": [],
                            "FaultKeywordIdList": [],
                            "AttachmentIdList": [],
                            "SolutionList":data.SolutionList,
                            "FailureAnalysiseList": data.CauseAnalysisList
                        }
        },
        handleSuccess(){
            this.disabledBtn=true;
            let listResult = this.$refs.formData.validate();
           let approvalPanelValidate = this.$refs.approvalPanel.validate();
           let t=true;
           this.formData.FailureAnalysiseList.forEach(v => {
               if(!v.checked){
                   v.FailureResonCode="";
               }
           })
           this.formData.SolutionList.forEach(v => {
               if(!v.checked){
                   v.FailureSolutionCode="";
               }
           })
           if(this.dialogStatus == 'create'){

               this.smsMsg=this.tabDatas.find(s => s.FailureCaseCode == this.formData.FailureCaseCode);
               
                if(this.smsMsg){
                    // this.$message.error('故障现象代码不可重复!');
                    this.disabledBtn=false;
                    return;
                }
           }
           if(!this.formData.IsNew){
               if(this.formData.FailureAnalysiseList.length>0){
                   let a=this.formData.FailureAnalysiseList.some(v => v.checked);
                   if(!a){
                       t=false;
                       this.$message({
                        message: '请选择一条故障原因!',
                        type: 'warning'
                        });
                        this.disabledBtn=false;
                        return;
                   }
               }
               if(this.formData.SolutionList.length>0){
                   let a=this.formData.SolutionList.some(v => v.checked);
                   if(!a){
                       t=false;
                       this.$message({
                        message: '请选择一条解决方法!',
                        type: 'warning'
                        });
                        this.disabledBtn=false;
                        return;
                   }
               }
           }
           if(t){
               Promise.all([listResult,approvalPanelValidate]).then(valid => {
                   this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块
                   if(this.formData.IsNew){
                       this.keyList.forEach(v => {
                           this.formData.FaultKeywordIdList.push(v.FaultKeyWordId);
                       })
                       if(this.selectedMsg){
                           let a=null;
                           this.formData.EquipmentSettingIds.push(this.selectedMsg.Id);
                           this.selectedData.forEach(v => {
                               if(v.checkList.length>0){
                               this.formData.EquipmentSettingIds.push(v.Id);
                               v.children.forEach(v1 => {
                                   a=null;
                                   a=v.checkList.find(s => s == v1.Name);
                                   if(a){
                                   this.formData.EquipmentSettingIds.push(v1.Id);
                                   }
                               })
                               }
                           })
                       }
                       this.formData.AttachmentIdList = this.formData.AttachmentList && this.formData.AttachmentList.map(s => s.Id);
                   }
                   if(this.formData.SolutionList.length>0){
                       this.formData.SolutionList.forEach(v => {
                           if(v.checked){
                               v.AttachmentIdList = v.AttachmentList && v.AttachmentList.map(s => s.Id);
                           }
                       })
                   }
                   this.formData.Approval.ApprovalEmployeeIdList = this.formData.Approval.ApprovalEmployeeList.map(
                       (s) => s.map((e) => e.EmployeeId)
                   );
                   this.formData.Approval.CCEmployeeIdList = this.formData.Approval.CCEmployeeList.map(
                       (s) => s.EmployeeId
                   );
                   if(this.dialogStatus == "create"){
                       failurecase.caseCreate(this.formData).then(res => {
                           this.disabledBtn=false;
                           this.$notify({
                             title: '成功',
                             message: '申报成功！',
                             type: 'success'
                           });
                           this.$emit('saveSuccess');
                       }).catch(err => {
                           this.disabledBtn=false;
                       })
                   }
               }).catch(err => {
                   this.disabledBtn=false;
               })
           }else{
               this.disabledBtn=false;
           }
        },
        handleApproval() {

            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData();
                    postData.BusinessId = this.id;
                    let approvalLabel = vars.approvalResult.find(
                        (s) => s.value == postData.ApprovalResultState
                    ).label;
        
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(() => {
                        //项目创建审批
                        this.disabledBtn = true;
                        failurecase
                        .approval(postData)
                        .then((res) => {
                            this.disabledBtn = false;
                            this.$notify({
                            title: "提示",
                            message: "审批成功",
                            type: "success",
                            duration: 2000,
                            });
                            this.$emit('saveSuccess');
                        })
                        .catch((err) => {
                            this.disabledBtn = false;
                        });
                    });
                }
            })

        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }

}
</script>
<style lang="scss" scoped>
.b0{

}
.elSpan {
  height: 24px;
  line-height: 24px;
  color: #aaa;
  padding: 0 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  position: relative;
  margin-left: 10px;
  .el-icon-error {
    position: absolute;
    right: -5px;
    top: -5px;
    color: #aaa;
    cursor: pointer;
  }
}
.panel-title{
    margin-top:10px;
}
.nSpan{
    display: inline-block;
    border: 1px solid #F59A23;
    color: #F59A23;
    padding: 0 6px;
    border-radius: 6px;
    height: 18px;
    line-height: 18px;
}
.box-card{
    margin-top: 10px;
    .bm{
        padding:10px;
    }
    .bm:not(:first-child){
        border-top: 1px solid #dcdfe6;
    }
}
</style>
