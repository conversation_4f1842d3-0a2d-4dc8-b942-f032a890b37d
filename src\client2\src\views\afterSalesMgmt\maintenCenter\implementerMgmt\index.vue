<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="实施人员管理" :subTitle='["实施人员的管理页面"]'></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <!-- <el-input class="elInput" placeholder="输入关键字进行过滤" v-model="filterText">
                    </el-input> -->

                    <!-- <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current='true' :check-on-click-node='true' @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                        </span>
                    </el-tree> -->

                    <v-tree
                        @changeNode="changeTreeNode"
                        :isAll="true"
                        :level='1'
                    ></v-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :isShowBtnsArea='false' :layoutMode='layoutMode' :isShowConditionArea='true' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable">
                        <template slot='AgentList' slot-scope="scope">
                            {{scope.row.AgentList | nameFilter}}
                        </template>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :layoutMode='layoutMode'
                            :label-width="'80px'"
                            :items="tableSearchItems"
                            @onSearch="handleFilter"
                            @onReset="handleResetSearch"
                            >
                                <template slot="Name">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索人员姓名..."
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Name"
                                    ></el-input>
                                </template>
                                <template slot="Number">
                                    <el-input style="width: 100%;" v-model="listQuery.Number"></el-input>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn moduleName="position" v-on:btn-event="onBtnClicked">
                                        <template slot="btnAdd" slot-scope="scope">
                                            <emp-selector
                                                key="btnAddSelector"
                                                :showType="2"
                                                :multiple="true"
                                                :list="pers"
                                                :beforeOpen='handleAdd'
                                                :beforeConfirm='handleAddBeforeConfirm'
                                                @change="handleChangeUsers"
                                                :disabledList="disabledList"
                                                >
                                                <el-button slot="reference" icon="el-icon-edit" type="primary" class="filter-item" style="margin-left: 4px;">{{ scope.data.Name }}</el-button>
                                            </emp-selector>
                                        </template>
                                        <template slot="btnAgentSet" slot-scope="scope">
                                            <emp-selector
                                                key="btnAgentSetSelector"
                                                :showType="2"
                                                :multiple="true"
                                                :list="[]"
                                                :beforeOpen='handleAgentSetBeforeOpen'
                                                :beforeConfirm='handleAgentSetBeforeConfirm'
                                                @change="handleAgentUsers"
                                                :disabledList="disabledList"
                                                >
                                                <el-button slot="reference" icon="el-icon-edit" type="primary" class="filter-item" style="margin-left: 4px;">{{ scope.data.Name }}</el-button>
                                            </emp-selector>
                                        </template>
                                    </permission-btn>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" text='移除' :type='3'></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 添加员工 -->
    <!-- <el-dialog width="1000px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="'添加员工'" :visible.sync='dialogAccessUsers' :append-to-body='true'>
        <emp-table ref="accessUser" v-bind="$attrs" :existsUsers='pers' v-if="dialogAccessUsers" v-show="dialogAccessUsers" @changed='handleChangeUsers' :disabledList="disabledList"></emp-table>
    </el-dialog> -->
    <!-- 设置代理授权人 -->
    <!-- <el-dialog width="1000px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="'设置代理授权人'" :visible.sync='dialogAgentUsers' :append-to-body='true'>
        <emp-table ref="accessUser" v-bind="$attrs" :existsUsers='pers' v-if="dialogAgentUsers" v-show="dialogAgentUsers" @changed='handleAgentUsers' :disabledList="disabledList"></emp-table>
    </el-dialog> -->
    <!-- 调整区域 -->
    <adjust-regional @closeDialog='regionalTreeClose' @saveSuccess='regionalTreeSave' :dialogFormVisible='dialogAdjustRegionalVisible' :node='selectEmployeesIdList' :regionalName='selectRegionalName'>
    </adjust-regional>

    <!-- 审批设置 -->
    <approval-set @closeDialog='regionalApprovalSetClose' @saveSuccess='regionalApprovalSetSave' :dialogFormVisible='dialogApprovalSetVisible'>
    </approval-set>

</div>
</template>

<script>
// import {
//     listToTreeSelect
// } from '@/utils'
import elDragDialog from '@/directive/el-dragDialog'
import indexPageMixin from '@/mixins/indexPage'
// import {
//     downloadFile
// } from "@/utils/index"
// import * as regionalManagement from '@/api/systemManagement/regionalManagement'
import * as implementerManagement from '@/api/maintenanceCenter/implementerManagement'
// import EmpTable from '../../../common/empTable'
import empSelector from '../../../common/empSelector'
import adjustRegional from "./adjustRegional";
import approvalSet from "./approvalSet";
import vTree from "../../businessMap/common/tree";

export default {
    name: 'implementerManagement',
    mixins: [indexPageMixin],
    directives: {
        // waves,
        elDragDialog
    },
    components: {
        // EmpTable,
        empSelector,
        adjustRegional,
        approvalSet,
        vTree
        
    },
    props: {},
    filters: {
        nameFilter(list) {
            if(list && list.length > 0) {
                return list.map(s => s.Name).join(',')
            }
            return '无'
        }
    },
    computed: {
        fildids() {
            return this.multipleSelection.map(s => s.Id) || []
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1
                    this.listQuery.RegionalId = val.Id
                    this.selectTypeId = val.Id
                    this.selectTypeName = val.ParentName;
                    this.getList()
                }
            },
            immediate: true
        },
    },
    created() {
        // this.getRegionals()
    },
    data() {
        return {
            layoutMode: 'simple',
            tableSearchItems: [
                { prop: "Name", label: "人员姓名", mainCondition: true },
                { prop: "Number", label: "工号" },
            ],
            dialogApprovalSetVisible: false,

            dialogAdjustRegionalVisible: false,
            selectEmployeesIdList: [],
            selectRegionalName: "",

            pers: [],
            disabledList: [],
            // dialogAccessUsers: false,
            // dialogAgentUsers: false,

            filterText: '',

            // treeLoading: false,
            treeDatas: [],
            defaultProps: { //树默认结构
                children: 'children',
                label: 'RegionalName'
            },

            checkedNode: null, //当前单击选中的节点
            regionalListQuery: {
                RegionalName: ""
            },

            dialogFolderFormVisible: false,
            dialogFolderStatus: 'create',
            currentOptNode: null, //当前操作的文件夹节点（新增、编辑、删除）
            selectTypeId: '', //当前选中的类型ID
            selectTypeName: '',

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: 'Name',
                        label: '人员名称',
                    }
                },
                {
                    attr: {
                        prop: 'Number',
                        label: '工号',
                    }
                },
                {
                    attr: {
                        prop: 'Mobile',
                        label: '手机',
                        showOverflowTooltip: true
                    }
                },
                {
                    attr: {
                        prop: 'DepartmentName',
                        label: '所属部门',
                        showOverflowTooltip: true
                    }
                },
                {
                    attr: {
                        prop: 'RegionalName',
                        label: '负责区域',
                        showOverflowTooltip: true
                    }
                },
                {
                    attr: {
                        prop: "AgentList",
                        label: "代理授权人",
                    },
                    slot: true
                },
            ],
            listQuery: {
                RegionalId: '',
                AgentList: null,
                Name: '',
                Number: '',
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

        }
    },
    methods: {
        changeTreeNode(d) {
            this.checkedNode = d
        },
        //获取列表
        getList() {
            if (this.checkedNode) {
                if (this.listQuery.RegionalId == -1) {
                    this.listQuery.RegionalId = null;
                }
                this.listLoading = true
                let postData = JSON.parse(JSON.stringify(this.listQuery))
                implementerManagement.getList(postData).then(res => {
                    this.tabDatas = res.Items
                    this.total = res.Total
                    this.listLoading = false
                }).catch(err => {
                    this.listLoading = false
                })
            }
        },

        //移除行
        handleDelete(row) {
            this.$confirm('是否确认移除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                implementerManagement.del({
                    id: row.Id
                }).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '移除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        async handleAdd() {
            let result = false
            if (this.selectTypeId != -1) {
                await implementerManagement.detail({
                    regionalId: this.selectTypeId
                }).then(res => {
                    this.pers = res.EmployeeDtoList
                    // this.disabledList = res.DisabledList
                    // this.dialogAccessUsers = true;
                    result = true
                })
            } else {
                this.$message({
                    message: "该节点不可添加人员",
                    type: "error"
                });
            }
            return result
        },

        async handleAgentSetBeforeOpen() {
            let result = false
            // if (this.multipleSelection.length != 1) {
            //     this.$message({
            //         message: "只能选择一个代理授权人",
            //         type: "error"
            //     });
            //     return result;
            // }

            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少选择一个",
                    type: "error"
                });
                return result;
            }
            let ids = []
            if (_.isArray(this.multipleSelection)) {
                ids = this.multipleSelection.map(u => u.EmployeeId)
            } else {
                ids.push(this.multipleSelection.EmployeeId)
            }
            await implementerManagement.getDetailsList(
                ids
            ).then(res => {
                this.pers = res.EmployeeDtoList
                // this.disabledList = res.DisabledList
                // this.dialogAgentUsers = true;
                result = true
            })
            return result
        },

        //表格为按钮事件
        onBtnClicked: function (domId) {
            switch (domId) {
                //添加人员
                case "btnAdd":
                    
                    break;
                    //人员调整
                case "btnEdit":
                    if (this.multipleSelection.length < 1) {
                        this.$message({
                            message: "至少选择一个",
                            type: "error"
                        });
                        return;
                    }
                    this.handleAdjustRegional(this.multipleSelection);
                    break;
                    //审批设置
                case "btnApprovalSet":
                    this.dialogApprovalSetVisible = true;
                    break;

                    //设置代理授权人
                case "btnAgentSet":
                    

                    break;

                    //取消代理授权人
                case "btnAgentSetCancel":
                    if (this.multipleSelection.length < 1) {
                        this.$message({
                            message: "至少选择一个",
                            type: "error"
                        });
                        return;
                    }

                    let id = this.multipleSelection.map(u => u.Id)
                    // if (_.isArray(this.multipleSelection)) {
                    //     id = this.multipleSelection.map(u => u.Id)
                    // } else {
                    //     id.push(this.multipleSelection.Id)
                    // }
                    implementerManagement.cancelAgentSet(
                        id
                    ).then(res => {
                        this.$notify({
                            title: "提示",
                            message: "取消成功",
                            type: "success",
                            duration: 2000
                        });
                        this.getList()
                    })

                    break;

                default:
                    break;
            }
        },
        usersChanged() {
            this.$emit("change", this.pers)
        },
        // 弹出添加人员框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        async handleAddBeforeConfirm(users) {
            this.pers = users
            var employeeIdList = users.map(s => s.EmployeeId);
            let param = {
                RegionalId: this.selectTypeId,
                EmployeeIdList: employeeIdList
            };
            let result = false
            await implementerManagement.add(param).then(res => {
                result = true
            })
            return result
        },
        //添加员工
        handleChangeUsers(users) {
            this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
            });
            this.usersChanged()
            this.getList()
        },
        //设置代理授权人
        async handleAgentSetBeforeConfirm(users) {
            if(users && users.length > 5) {
                this.$message({
                    message: '代理授权人数不得超过5人',
                    type: 'error'
                })
                return false
            }

            this.pers = users
            var employeeIdList = users.map(s => s.EmployeeId);

            this.usersChanged()
            var multipleSelectionList = this.multipleSelection.map(u => u.Id)
            let param = {
                EmployeeIdList: multipleSelectionList,
                SelectEmployeeIdList: employeeIdList
            };
            let result = false
            await implementerManagement.setAgent(param).then(res => {
                result = true
            })
            return result
        },
        handleAgentUsers(users) {
            this.$notify({
                title: "提示",
                message: "设置成功",
                type: "success",
                duration: 2000
            });
            // this.dialogAgentUsers = false
            this.getList()
            
        },
        // 人员调整
        handleAdjustRegional(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.EmployeeId)
            } else {
                ids.push(rows.EmployeeId)
            }
            this.selectEmployeesIdList = ids
            this.selectRegionalName = this.selectTypeName
            this.dialogAdjustRegionalVisible = true;
        },

        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },

        handleSizeChange(val) {
            this.listQuery.PageSize = val.size
            this.getList()
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page
            this.listQuery.PageSize = val.size
            this.getList()
        },

        regionalTreeClose() {
            this.dialogAdjustRegionalVisible = false;
        },
        regionalTreeSave() {
            this.getList();
            this.dialogAdjustRegionalVisible = false;
        },

        regionalApprovalSetClose() {
            this.dialogApprovalSetVisible = false;
        },
        regionalApprovalSetSave() {
            this.getList();
            this.dialogApprovalSetVisible = false;
        },

        /***************************************左侧树菜单操作***************************************/

        // listToTreeSelect,

        //按关键字过滤树菜单
        // filterNode(value, data) {
        //     if (!value) return true;
        //     return data.RegionalName.indexOf(value) !== -1;
        // },

        //获取左侧树菜单
        // getRegionals() {
        //     this.treeLoading = true;
        //     regionalManagement.getListByCondition(this.regionalListQuery).then(res => {

        //         res.unshift({
        //             Id: -1,
        //             ParentId: null,
        //             ParentName: '全部地区',
        //             RegionalName: "全部地区",
        //             RegionalLevel: 0
        //         })

        //         this.treeDatas = listToTreeSelect(res)

        //         //如果首次加载问价夹树（没有选中），默认选中根节点
        //         if (!this.checkedNode) {
        //             this.setDefaultChecked()
        //         }
        //         this.treeLoading = false;
        //     })
        // },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        // setDefaultChecked() {
        //     this.$nextTick(() => {
        //         if (this.treeDatas && this.treeDatas.length > 0) {
        //             let rootNode = this.treeDatas[0]
        //             this.$refs.tree.setCurrentKey(rootNode.Id);
        //             this.checkedNode = rootNode
        //         }
        //     });
        // },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleResetSearch() {
            this.listQuery.PageIndex=this.listQuery.PageIndex;
            this.listQuery.PageSize=this.listQuery.PageSize;
            this.listQuery.Name="";
            this.listQuery.Number="";
            this.getList() //刷新列表
        },        
    },
}
</script>

<style lang="scss" scoped>
@import "../../../../styles/empSelectorDialogCommon.css";
.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            padding: 10px;
            padding-top: 0;
            padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
</style>
