<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title :title="pageTitle" :subTitle="[pageDesc]" :showBackBtn="!!returnUrl" @goBack="handleGoBack"></page-title> -->
        <div class="page-wrapper">
            <div class="product-list">
                <el-button type="primary" v-if="hasTreeOpertAuth" style="width: 180px; margin-top: 10px;margin-left:35px;" @click="addTopLevel">创建模板分类</el-button>
                <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label">{{ node.label }}</span>
                            <span v-if="data.Level>0 && hasTreeOpertAuth && data.Id != '00000000-0000-0000-0000-000000000000'" class="node-btn-area">
                                <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <!-- :default-sort = "sortObj" -->
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" @sortChagned="handleSortChange" :layoutMode='layoutMode'>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索模板名称"
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked"></permission-btn>
                                </template>

                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    
    <customerServiceDepartmentEdit v-if="customerServiceDepartmentEditDialogFormVisible" :dialogStatus="customerServiceDepartmentEditDialogStatus" :node="paramNode" :dialogFormVisible="customerServiceDepartmentEditDialogFormVisible" @closeDialog="customerServiceDepartmentEditCloseDialog" @saveSuccess="customerServiceDepartmentEditSaveSuccess"></customerServiceDepartmentEdit>

    <create-page :chooseUnitId='checkedNode ? checkedNode.Id : null' v-if="dialogFormVisible" @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList"></create-page>


</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import commMixin from './mixins'
import { listToTreeSelect, treeFilter } from '@/utils'
import * as classify from "@/api/classify";
import * as afterVistTemplate from "@/api/afterSalesMgmt/afterVistTemplate";

import createPage from "./create";
import customerServiceDepartmentEdit from "./departmentEdit";

// import { vars } from "../common/vars";
import { getUserInfo } from "@/utils/auth";

export default {
    name: "",
    mixins: [indexPageMixin, commMixin],
    components: {
        createPage,
        customerServiceDepartmentEdit,
    },
    computed: {
        
        hasTreeOpertAuth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnMaintain') > -1
        },

        tabColumns() {
            return [
                {
                    attr: {
                        prop: "Name",
                        label: this.isDeviceParams ? "设备参数模板名称" : '回访记录模板名称',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "ClassifyName",
                        label: "模板类型",
                        showOverflowTooltip: true,
                    },
                },
            ]
        },

    },
    created() {
        // this.getSalesman()
        this.loadTreeData();
        this.getList();

    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.ClassifyId = val.Id;
                    
                    this.getList();
                }
            },
        },
    },
    filters: {
        valFilter(val) {
            if (val) {
                return val;
            }
            return "无";
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "无";
        },

    },
    mounted() {},
    data() {
        return {
            layoutMode: 'simple',

            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
            /**树节点添加弹窗 */
            customerServiceDepartmentEditDialogFormVisible: false,
            customerServiceDepartmentEditDialogStatus: "create",

            

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,
            total: 0,
            listLoading: false,
            listQuery: {
                Keywords: '',
                ClassifyId: null,
            },
            multipleSelection: [],
            tableSearchItems: [
                {
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },


            ],
            
            tabDatas: [],

        };
    },
    methods: {
        handleBeforeOpen() {
            this.$refs.tagSelectorRef.pers = []
            return true
        },

        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId != 'btnMaintain')
            }
            return []
        },

        resetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize,
                ClassifyId: this.listQuery.ClassifyId,
                Keywords: "",
            };
            this.getList();
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let _this = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: this.isDeviceParams ? 5 : 6,
                TemplateType: this.isDeviceParams ? 1 : 2,
            };
            _this.treeLoading = true
            classify.getListPage(paramData).then(res => {
                _this.treeLoading = false
                let response = res.Items
                response.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });
                _this.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

                if (_this.treeData && _this.treeData.length > 0) {
                    if (
                        !(
                            _this.checkedNode &&
                            response.find(t => {
                                return t.Id == _this.checkedNode.Id;
                            })
                        )
                    ) {
                        _this.checkedNode = _this.treeData[0];
                    }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        }
                    });
                }
            }).catch(err => {
                _this.treeLoading = false
            });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.customerServiceDepartmentEditDialogStatus = "create";
            this.customerServiceDepartmentEditDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "create";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "update";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "delete":
                    this.handleDeleteDepartment(data);
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteDepartment(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let postDatas = {
                    Ids: [data.Id],
                    BusinessType: this.isDeviceParams ? 5 : 6,
                }
                classify
                    .del(postDatas)
                    .then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }
                        this.loadTreeData();
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
            });
        },
        /**地区弹窗保存成功 */
        customerServiceDepartmentEditSaveSuccess(isContinue) {
            if(isContinue !== true) {
                this.customerServiceDepartmentEditCloseDialog();
            }
            this.loadTreeData();
        },
        /**地区弹窗关闭 */
        customerServiceDepartmentEditCloseDialog() {
            this.customerServiceDepartmentEditDialogFormVisible = false;
        },
        handleSuccessExport() {},

        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                default:
                    break;
            }
        },
        

        handleDialog(activeName, row) {
            if(row) {
                this.id = row.Id
            }
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData, isContinue) {
            // this.listQuery.PageIndex = 1

            this.getList();
            if(!isContinue) {
                this.closeDialog();
            }
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        // handleNav(type, proj) {
        //   this.$router.push({
        //     path: `/projectDev/projectMgmt/${type}/index?productId=${proj.productId}&projectId=${proj.projectId}`
        //   });
        // },
        handleUpdate(row, optType = "update") {
            // 弹出编辑框
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        // handleResetSearch() {
        //     this.listQuery = {
        //         // 否则手动重置查询条件
        //         PageIndex: this.listQuery.PageIndex,
        //         PageSize: this.listQuery.PageSize,
        //         Name: "",
        //     };
        //     this.getList(); //刷新列表
        // },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        //获取项目列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData.TemplateType = this.isDeviceParams ? 1 : 2
            postData = this.assignSortObj(postData);
            afterVistTemplate.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            });
        },
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                afterVistTemplate.del([row.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            });
        },


    },
};
</script>

<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            // top: 40px;
            top: 0;
            right: 0;
            bottom: 0;

            .product-list {
                width: 250px;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }

                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        height: 100%;
                        overflow: auto;
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 200px);
                flex: 1;
                overflow-y: auto;

                .content {

                    // padding: 10px;
                    // padding-right: 0;
                    .opt-wrapper {
                        box-sizing: border-box;
                        border-bottom: 1px solid #dcdfe6;
                        padding-bottom: 10px;
                    }

                    .list {}
                }
            }
        }
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}

.statisticsDivClass {
    width: 100%;
    height: 75px;
    border-radius: 8px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
    cursor: pointer;

    .statisticsChildDiv {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px 0 0 8px;
        width: 26%;
        height: 75px;
        background-color: rgba(255, 108, 96, 1);
        float: left;
    }

    .statisticsChildDiv2 {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 0 8px 8px 0;
        width: 74%;
        height: 75px;
        background-color: rgba(255, 255, 255, 1);
        float: left;

        .label1 {
            color: #999999;
            margin: 0;
            margin-top: 12px;
            font-weight: 500 !important;
        }

        div {
            margin-top: 5px;
        }

        .label2 {
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            font-size: 20px;
            color: $text-second-color;
            margin: 0;
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

.statisticsDivClass:hover {
    transform: translate(-3px, -3px);
    box-shadow: 0px 0px 3px 0px #dcdfe6;
}


.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
