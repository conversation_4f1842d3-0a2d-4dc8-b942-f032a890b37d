<template>
    <div>
        <app-dialog title="异常校正记录" ref="appDialogSel" v-bind="$attrs" v-on="$listeners" :width="800" :maxHeight='700'>
            <template slot="body">
                <div class="wrapper" v-loading='loading'>
                    <template v-if="!onlyShowList">
                        <el-row>
                            <el-col :span="12">
                                <div class="row-wrapper">
                                    <span class="title">执行人：</span>
                                    <span class="content">{{ detail.CreateEmployee ? detail.CreateEmployee.Name : '无' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="row-wrapper">
                                    <span class="title">执行时间：</span>
                                    <span class="content">{{ detail.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</span>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <div class="row-wrapper">
                                    <span class="title">日期范围：</span>
                                    <span class="content">{{ detail.StartTime | dateFilter('YYYY-MM-DD') }} - {{ detail.EndTime | dateFilter('YYYY-MM-DD') }}</span>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <div class="row-wrapper">
                                    <span class="title">执行人员：</span>
                                    <span class="content">{{ detail.CorrectEmployeeList ? detail.CorrectEmployeeList.map(s => s.Name).join('、') : '无' }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </template>
                    <div class="list-wrapper">
                        <div class="title" v-if="!onlyShowList">执行结果：</div>
                        <div v-else>共完<span style="color: #409EFF; font-weight: bold; padding: 0 2px;">{{ detail.CorrectOvertimeRestDetailsList ? detail.CorrectOvertimeRestDetailsList.length : 0 }}</span>条加班申请单的处理，结果如下：</div>
                        <div style="margin-bottom: 12px;" v-for="(item, idx) in detail.CorrectOvertimeRestDetailsList" :key="idx">
                            <div>{{ item.Employee ? item.Employee.Name : '无' }}（{{ item.Employee ? item.Employee.Number : '无' }}）</div>
                            <div>
                                <span style="color: #409EFF;">{{ item.Code || '无' }}</span> 加班申请 {{ item.StartTime | dateFilter('YYYY-MM-DD') }} {{ item.StartUpDown == 1 ? '上午' : '下午' }} - {{ item.EndTime | dateFilter('YYYY-MM-DD') }} {{ item.EndUpDown == 1 ? '上午' : '下午' }} （共{{ item.TimeTotal }}天）
                            </div>
                           <div>补算：（+{{ item.CalculateTimeTotal }}天调休）</div>
                        </div>
                        <no-data v-if="detail.CorrectOvertimeRestDetailsList.length == 0 && !loading"></no-data>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import AppTableCore from '../../../../components/AppTable/AppTableCore.vue';
import * as timecardCorrectOvertimeRest from '@/api/personnelManagement/timecardCorrectOvertimeRest'
import noData from '../../../common/components/noData.vue';

export default {
    name: "check-record-detail-dialog",
    directives: {},
    components: {
        AppTableCore,
        noData
    },
 
    mixins: [indexPageMixin],
    computed: {

    },
    props: {
        id: {
            type: String,
            required: true
        },
        //是否只显示列表
        onlyShowList: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val && this.id) {
                    this.getDetail()
                }
            },
            immediate: true
        }
    },
    filters: {
    },
    created() {
    },
    mounted() {

    },
    data() {
        return {
            loading: false,
            detail: {},
        };
    },
    methods: {
        getDetail() {
            this.loading = true
            timecardCorrectOvertimeRest.detail({id: this.id}).then(res => {
                this.loading = false
                this.detail = Object.assign({}, this.detail, res)
            }).catch(err => {
                this.loading = false
            })
        },
        handleReview() {

        },
        handleClose() {
            this.$refs.appDialogSel.handleClose();
        }
    }
};
</script>


<style lang='scss' scoped>
.wrapper{
    padding: 10px 0;
    .row-wrapper{
        padding: 6px 0;
        display: flex;
        .title{
            font-weight: 600;
            width: 80px;
            text-align: left;
        }
        .content{
            flex: 1;
        }
    }

    .list-wrapper{
        margin-top: 10px;
        .title{
            font-weight: 600;
            margin-bottom: 10px;
        }
    }
}
</style>