<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="项目管理" :subTitle='["项目管理、设置、工作台的管理页面"]' @goBack="handleGoBack2"></page-title> -->

        <div class="content __dynamicTabContentWrapper">
            <div class="opt-wrapper">
                <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                <!-- <el-button type="primary" size="mini" @click="onBtnClicked('btnAddDept')">添加考勤部门</el-button> -->
            </div>

            <!-- <div class="condition-wrapper clearfix">
                <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='handleResetSearch'>
                    <template slot='Status'>

                    </template>
                </app-table-form>
            </div> -->
            <div class="list" v-loading='projectLoading'>
                <no-data v-show="tabDatas.length == 0"></no-data>
                <div class="card-wrapper" v-for="p in tabDatas" :key="p.Id">
                    <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                        <div slot="header" class="clearfix title-wrapper">
                            <span class="omit" :title="p.Name">{{ p.Name }}</span>
                            <span class="svg-wrapper" :title="p.ConnectionStatus == 1 ? '正常' : '异常'">
                                <svg-icon style="font-size: 20px;" v-if="p.ConnectionStatus == 1" icon-class="success"></svg-icon>
                                <svg-icon style="font-size: 20px;" v-if="p.ConnectionStatus == 2" icon-class="fail"></svg-icon>
                            </span>
                        </div>
                        <div class="item-wrapper">
                            <div class="item omit">
                                <span class="item-title">机器号：</span>
                                <span>{{ p.MachineNo }}</span>
                            </div>
                            <el-row>
                                <el-col :span="12">
                                    <div class="item omit">
                                        <span class="item-title">上班时间：</span>
                                        <span>{{ p.WorkStartTime }}</span>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="item omit">
                                        <span class="item-title">下班时间：</span>
                                        <span>{{ p.WorkEndTime }}</span>
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <div class="item omit">
                                        <span class="item-title">考勤人数：</span>
                                        <span>{{ p.PeopleNumber }}</span>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="item omit">
                                        <span class="item-title">考勤组数：</span>
                                        <span>{{ p.DepartmentNumber }}</span>
                                    </div>
                                </el-col>
                            </el-row>
                            <div class="item omit" style="color: #aaaaaa;">
                                <span class="item-title">最后连接时间：</span>
                                <span>{{ p.LastReceivingTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}</span>
                            </div>
                        </div>
                        <div class="split-line"></div>
                        <div class="footer">
                            <el-button v-if="rowBtnIsExists('btnGroupMgmt')" type="text" class="button" @click="handleNav('workbench', p)">考勤组管理</el-button>
                            <el-button v-if="rowBtnIsExists('btnEditDept')" type="text" class="button" @click="handleCommand('update', p)">编辑</el-button>
                            <el-button v-if="rowBtnIsExists('btnDelDept')" type="text" class="button" @click="handleCommand('delete', p)">删除</el-button>
                        </div>
                    </el-card>
                </div>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>

    <!-- 创建项目 -->
    <create-page @closeDialog='closeDialog' :id='id' @saveSuccess='handleSaveSuccess' :dialogFormVisible='dialogFormVisible' :dialogStatus='dialogStatus'>
    </create-page>


</div>
</template>

<script>
// import pageTitle from '../common/pageTitle'
// import tags from '../common/tags'
import noData from '../../../common/components/noData'
import createPage from './create'
import * as attendanceMgmt from "@/api/personnelManagement/attendanceMgmt";
import {
    getUserInfo
} from '@/utils/auth'

export default {
    name: 'attendanceMgmt-group-index',
    components: {
        // pageTitle,
        // tags,
        noData,
        createPage,
    },
    filters: {
    },
    watch: {
    },
    created() {
        this.getList()
    },
    computed: {
        returnUrl() {
            let url = decodeURI(this.$route.query.returnUrl || '')
            return url
        },

    },
    watch: {
        
    },
    mounted() {

    },
    data() {
        return {
            total: 0,
            tabDatas: [],
            id: '',


            dialogFormVisible: false,
            dialogStatus: 'create',



            projectLoading: false,

            listQuery: {
                Status: null,
            },
            tableSearchItems: [{
                    prop: 'Status',
                    label: '状态'
                },
            ],
            
        }
    },
    methods: {

        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAddDept":
                    this.handleDialog("create");
                    break;
                default:
                    break;
            }
        },
        //选择项目类型弹框
        handleDialog(activeName, item) {
            if(item) {
                this.id = item.Id
            }else{
                this.id = ''
            }
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogStatus = ''
            this.dialogFormVisible = false
        },
        handleSaveSuccess() {
            this.closeDialog()
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1
            this.getList()
        },
        handleNav(type, item) {
            let targetUrl = `/personnelManagement/attendanceMgmt/group/list/${item.Id}`

            this.$router.push(
                targetUrl
            );
        },
        handleResetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize,
                Status: null,
            };
            this.getList()
        },
        //获取项目列表
        getList() {
            this.projectLoading = true
            let postData = JSON.parse(JSON.stringify(this.listQuery))
            attendanceMgmt.getList(postData).then(res => {
                this.projectLoading = false
                this.tabDatas = res.Items
                this.total = res.Total
            })
        },
        del(item) {
            this.$confirm(`是否确认删除 ${item.Name}?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                attendanceMgmt.del([item.Id]).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        handleCommand(activeName, item) {
            if (activeName == 'update') {
                this.handleDialog(activeName, item)
            } else if (activeName == 'delete') {
                if(item.DepartmentNumber > 0) {
                    this.$message({
                        message: '请先删除考情组',
                        type: 'error'
                    })
                }else{
                    this.del(item)
                }
            }
        },
        checkPremissBtns(domId, proj) {
            //立项不通过
            return (this.rowBtns.findIndex(b => b.DomId == domId) > -1)
        },

        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList()
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList()
        },
        handleGoBack() {
            this.$router.push({
                path: this.returnUrl
            })
        },


    }

}
</script>

<style lang="scss" scoped>
.clearfix {
    >span:first-child {
        width: calc(100% - 30px);
        display: inline-block;
    }
}

.app-container {
    .bg-white {
        .content {
            padding: 10px;
            height: 100%;
            // padding-right: 0;

            .opt-wrapper {
                //, .condition-wrapper
                box-sizing: border-box;
                border-bottom: 1px solid #DCDFE6;
                padding-bottom: 10px;
            }

            .list {
                flex: 1;
                overflow-y: auto;
                .card-wrapper {
                    padding: 5px;
                    display: inline-block;
                    width: 25%;

                    .card {
                        width: 100%;
                    }

                    .item-wrapper {
                        padding: 14px;
                        padding-bottom: 2px;

                        .item {
                            height: 25px;
                            line-height: 25px;

                            div {
                                display: inline-block;
                            }

                            .item-title {
                                width: 90px;
                            }

                            .item-status {
                                color: #fff;
                                padding: 2px 4px;
                                border-radius: 10%;
                            }
                        }
                    }

                    .split-line {
                        height: 1px;
                        background: #DCDFE6;
                        margin-top: 10px;
                    }

                    .footer {
                        height: 28px;
                        padding-right: 14px;
                        text-align: right;
                    }
                }
            }
        }
    }
}


.title-wrapper{
    display: flex;
    align-items: center;
    .oimt{
        flex: 1;
    }
    .svg-wrapper{
        display: flex;
        align-items: center;
    }
}




// { value: 1, label: '正常' },
// { value: 2, label: '已延期' },
// { value: 3, label: '已交付' },
// { value: 4, label: '已终止' },
// { value: 5, label: '立项待审批' },
// { value: 6, label: '验收待审批' },
// { value: 7, label: '立项不通过' },
// { value: 8, label: '验收不通过' },

// .proj-status-1{
//     background-color: green;
// }

// .proj-status-2{
//     background-color: gray;
// }

// .proj-status-3{
//     background-color: blue;
// }

// .proj-status-4, .proj-status-5, .proj-status-6{
//     background-color: red;
// }

// .proj-status-7, .proj-status-8{
//     background-color: orange;
// }
</style>
