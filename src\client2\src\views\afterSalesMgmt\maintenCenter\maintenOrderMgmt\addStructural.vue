<template>
    <div class="addStructural">
        <app-dialog title="添加配件" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='1200'
            height="600"
        >
            <template slot="body">
                <div class="bodyBox cl">
                    <div class="dLeft fl" v-loading="loadingLeft">
                        <el-input class="elInput" placeholder="结构部件查询" v-model="leftVal"></el-input>
                        <div class="tagBox" v-if="taskList.length>0">
                            <tags mode="list" :items="taskList" v-model="taskId" @change="handleTagsChange">
                                <template v-for="(task,idx) in taskList" :slot="task.value">
                                    <div class="item_warpper" :key="idx">
                                        <div>
                                            <span class="omit" :title="task.Name">{{task.Name}}</span>
                                        </div>
                                    </div>
                                </template>
                            </tags>
                        </div>
                        <no-data v-else></no-data>
                    </div>
                    <div class="dRight fr" v-loading="loadingRight">
                        <el-input class="elInput" placeholder="规格型号查询" v-model="rightVal"></el-input>
                        <div class="tagBox">
                            <div class="sHead cl">
                                <span class="fl" style="width:28px;height:12px;"></span>
                                <span class="sRadio fl">规格型号</span>
                                <span class="sRadio fl">物资编号</span>
                                <span class="sRadio fl">供应商</span>
                                <span class="sRadio fl">备注</span>
                            </div>
                            <el-radio-group class="elR" v-model="radio" v-if="tableDatas.length>0">
                                <el-radio class="elRadio" :label="td.Id" v-for="(td,index) in tableDatas" :key="index">
                                    <span class="omit sRadio" :title="td.SpecificationModel">{{td.SpecificationModel ? td.SpecificationModel : '无'}}</span>
                                    <span class="omit sRadio" :title="td.MaterialNo">{{td.MaterialNo ? td.MaterialNo : '无'}}</span>
                                    <span class="omit sRadio" :title="td.SupplierName">{{td.SupplierName ? td.SupplierName : '无'}}</span>
                                    <span class="omit sRadio" :title="td.Remark">{{td.Remark ? td.Remark : '无'}}</span>
                                </el-radio>
                            </el-radio-group>
                            <no-data v-else></no-data>
                        </div>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1'></app-button>
            </template>
        </app-dialog>
    </div>
</template>
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
import NoData from "@/views/common/components/noData";
import indexPageMixin from "@/mixins/indexPage";
export default{
    name:'addStructural',
    components: {
        NoData
    },
    mixins: [indexPageMixin],
    props:{
        // areaId:{
        //     type:String,
        //     default:''
        // }
        
    },
    data(){
        return{
            loadingLeft:false,
            loadingRight:false,
           leftVal:'',
           rightVal:'',
           taskId:'',
           taskList:[],
           tableDatas:[],
           timeout:null,
           id:'',
           radio:'',
        }
    },
    watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if(val){
            this.leftVal='';
            this.rightVal='';
            this.getLeftList();
        }
      },
      immediate: true
    },
    'leftVal': {
            handler: function (val) {
                let that = this;
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    that.handleFilter(val)
                }, 500)
            },
        },
        'rightVal': {
            handler: function (val) {
                let that = this;
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    that.handleFilterRight(val)
                }, 500)
            },
        },
        
  },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        handleFilter(){
            this.getLeftList();
        },
        handleFilterRight(){
            this.getPartList();
        },
        handleTagsChange(d){
            this.id = d;
            this.getPartList();
        },
        getLeftList() {
            this.loadingLeft = true;
            this.loadingRight = true;
            let postData = {
                "pageIndex": 1,
                "pageSize": 100000,
                "name": this.leftVal,
                "isPivotal": null,
                "structPartRisk": null
            }
            console.log(666, postData)
            accessories.getStructuralList(postData).then(res => {
                if (res.Items && res.Items.length > 0) {
                    this.taskId = res.Items[0].Id;
                    this.id=res.Items[0].Id;
                    this.getPartList();
                    res.Items.forEach(v => {
                        v.value = v.Id;
                    })
                    this.taskList = res.Items;
                } else {
                    this.taskList = [];
                    this.tableDatas=[];
                    this.loadingRight = false;
                }
                this.loadingLeft = false;
            }).catch(err => {
                this.loadingLeft = false;
            })
        },
        getPartList() {
            let postData = {
                "pageIndex": 1,
                "pageSize": 100000,
                "structPartId": this.id,
                "SpecificationModel":this.rightVal
            }
            accessories.getPartList(postData).then(res => {
                console.log(666,res)
                this.loadingRight = false;
                this.tableDatas = res.Items;
                // this.tableDatas.forEach(v => {
                //     v.SupplierIder = this.supplierList.find(s => s.Id == v.SupplierId);
                //     v.SupplierIder = v.SupplierIder.Name;
                // })
            }).catch(err => {
                this.loadingRight = false;
            })
        },
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        createData(){
            if(this.radio){
                let a=this.tableDatas.find(s => s.Id == this.radio);
                this.$emit('handleChange',this.taskId,a,this.radio);
                this.handleClose();
            }else{
                this.$message({
                  message: '请先选择一个规格型号!',
                  type: 'warning'
                });
            }
        },
        handleNodeClick(d){
            this.checkData=d;
        },
    }

}
</script>
<style lang="scss" scoped>
.sHead{
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;
    >span{
        display:inline-block;
    }
}
.sRadio{
    width:166px!important;
}
.bodyBox{
    width:100%;
    height:536px;
    >div{
        height:100%;
        padding:10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
        .elInput{
            margin-bottom:10px;
        }
    }
    .dLeft{
        width:calc(40% - 35px);
        .tagBox{
            height:calc(100% - 30px);
            overflow-y:auto;
            .item_warpper{
                padding: 4px 0 2px 2px;
            }
        }
    }
    .dRight{
        width:calc(60% + 25px);
        .tagBox{
            height:calc(100% - 30px);
            overflow-y:auto;
            .elR{
                width:100%;
                .elRadio{
                    width:100%;
                    margin:0 0 10px 0;
                    padding-bottom:8px;
                    border-bottom: 1px solid #EBEEF5;
                }
            }
        }
    }
}
span.omit{
    width: 100%;
    display: inline-block;
}
</style>