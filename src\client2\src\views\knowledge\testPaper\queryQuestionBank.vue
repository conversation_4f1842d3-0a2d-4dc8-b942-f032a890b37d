<!--分类 添加/编辑-->
<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRefs" v-bind="$attrs" v-on="$listeners" :width="600">
      <template slot="body">
        <el-tree class="tree" v-loading="loading" :data="questionBankList" show-checkbox node-key="Id" @check='checkOrg' :check-strictly="true"
        default-expand-all :default-checked-keys="defaultCheckedKey" :props="defaultProps" ref="orgsTree">
            <span class="custom-tree-node" slot-scope="node">{{ node.data.label }}（{{ node.data.count }}）</span>
        </el-tree>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import {
    listToTreeSelect
} from '@/utils'
import * as questionBankApi from '@/api/knowledge/questionBankManagement'

export default {
  name: "test-paper-classify",
  components: {},
  props: {
    selectIds: {
      type: Array,
      default: ()=>[]
    }
  },
  data() {
    return {
        loading: false,
        questionBankList: [],
        defaultProps: { //树默认结构
            children: 'children',
            label: 'label'
        },
        defaultCheckedKey: [], //部门树默认选中节点
    };
  },
  computed: {
    // editable() {
    //   return this.dialogStatus != "detail";
    // },
    pageTitle() {
    //   if (this.dialogStatus == "create") {
    //     return "选择题库";
    //   } else if (this.dialogStatus == "update") {
    //     return "编辑分类";
    //   } else if (this.dialogStatus == "detail") {
    //     return "分类详情";
    //   }
        return "选择题库";
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
            this.defaultCheckedKey = []; //部门树默认选中节点
            this.getDetail();
        }
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    /**获取详情 */
    getDetail() {
        let self = this;
        self.loading = true
        questionBankApi.GetQuestionBankClassifyList({}).then(response => {
            // self.questionBankList = res || []
            self.list = response.map(item => {
                return {
                    Id: item.value,
                    label: item.label,
                    ParentId: item.parentId,
                    allLabel: item.allLabel,
                    count: item.count
                }
            })
            var orgstmp = JSON.parse(JSON.stringify(self.list));
            var tempOrgsTree = listToTreeSelect(orgstmp); //将部门数据转换成树形结构
            self.defaultCheckedKey = self.selectIds //设置默认选中
            self.checkedKey = self.selectIds //设置默认选中
            self.questionBankList = tempOrgsTree;
            console.log(tempOrgsTree)
            self.loading = false
        }).catch(()=>{
            self.loading = false
        });
    },
    // 部门复选框发生变化触发事件
    checkOrg() {
        var _this = this;
        // _this.listQuery.OrgIds = []; //清空之前已选部门
        // _this.$refs.orgsTree.getCheckedNodes().forEach((item) => _this.listQuery.OrgIds.push(item.Id)); //循环当前已选部门并将部门ID添加到_this.listQuery.OrgIds
        // _this.getList();
        this.checkedKey = this.$refs.orgsTree.getCheckedNodes().map(s=>s.Id)
    },
    /**提交方法 */
    handleButtonClick() {
        let arr = this.checkedKey.map(s=> {
            return {
                Id: s,
                label: this.list.find(q=>q.Id == s).allLabel,
                count: this.list.find(q=>q.Id == s).count
            }
        })
        // console.log(arr)
        this.$emit('saveSuccess', arr);
    },

    /**关闭 */
    handleClose() {
      this.$refs.appDialogRefs.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.treehead ::v-deep .el-input-group__append {
    background: red;
    color: #fff;
}

.input-wrapper ::v-deep .el-input__inner {
    border-radius: 4px 0 0 4px;
}

.seleEmpCon {
    height: 550px;
    border: 1px solid #eee;
}

.treeCon {
    border-right: 1px solid #ebeef5;
}

.treehead {
    height: 40px !important;
    padding-top: 5px;
}

.treehead .input-wrapper {
    width: 100%;
    position: relative;
    padding-right: 56px;
}

.input-wrapper .search-btn {
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0 4px 4px 0;
}

.treeName {
    display: inline-block;
    width: 153px;
}

.treeMain {
    padding: 0px !important;
}

.tree {
    height: 458px;
}

.treeTips {
    text-align: center;
    width: 255px;
    display: block;
    margin-top: 5px;
}

.empCon{
    display: flex;
    flex-direction: column;
}

.empMain {
    /* padding-bottom: 0px; */
    width: 100%;
    padding: 5px;
    flex: 1;
}

.empTab {
    width: 100%;
}
.empFoot {
    margin-top: 10px;
    height: 30px !important;
}

.btns-wrapper{
    text-align: right;
}

/* 

.empSub {
    float: right;
} */

.paddingRight{
    padding: 20px;
}

.paddingLeft{
    padding: 30px;
}
</style>

