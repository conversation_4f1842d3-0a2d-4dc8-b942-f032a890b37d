<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">

          <el-form-item label="活动封面" prop="ActivityCoverPath">
            <app-upload-file :max='1' :fileSize='1024 * 1024 * 10' :value='ActivityCoverFileList' :readonly="!editable" @change='handleActivityCoverUpChange' :preview='true'></app-upload-file>
          </el-form-item>

          <el-form-item label="活动时间" prop="Range">
            <el-date-picker v-model="formData.Range" :disabled="!editable" type="daterange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
          </el-form-item>

          <el-form-item label="活动标题" prop="ActivityTitle">
            <el-input maxlength="50" type="text" :disabled="!editable" v-model="formData.ActivityTitle"></el-input>
          </el-form-item>

          <el-form-item label="详情内容" prop="ActivityContent">
            <editor-bar :value="formData.ActivityContent" @edit="formData.ActivityContent = arguments[0]"></editor-bar>
          </el-form-item>

          <el-form-item label="活动照片" prop="ActivityPhotos">
            <div style="float: right;width: 100%;border: 1px solid #fcfcfc;text-align: right;">({{ActivityPhotoFileList.length}} / 100)</div>
            <app-upload-file :max='100' :limit="100" :fileSize='1024 * 1024 * 10' :multiple="true" :value='ActivityPhotoFileList' :readonly="!editable" @change='handleActivityPhotoUpChange' :preview='true'></app-upload-file>
          </el-form-item>

        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>

import * as systemLeagueConstruction from '@/api/personnelManagement/systemLeagueConstruction'
import EditorBar from '@/components/QuillEditor/index.vue'

export default {
  name: "demand-pool-create",
  directives: {},
  components: {
    EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.ActivityCoverFileList = [];
        this.ActivityPhotoFileList = [];

      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建团建活动";
      } else if (this.dialogStatus == "update") {
        return "编辑团建活动";
      } else if (this.dialogStatus == 'detail') {
        return '详情'
      }
      return ''
    },
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      formLoading: false,
      ActivityCoverFileList: [], //图像信息[{Id: '', Path: ''}]
      ActivityPhotoFileList: [],
      rules: {
        ActivityCoverPath: {
          fieldName: "活动封面",
          rules: [{ required: true }]
        },
        Range: {
          fieldName: "活动时间",
          rules: [{ required: true }]
        },
        ActivityTitle: {
          fieldName: "活动标题",
          rules: [{ required: true }]
        },
        ActivityContent: {fieldName: "详情内容",rules: [{ required: true }, {max: 20000, trigger: "blur"}]}
      },
      labelWidth: "100px",
      formData: {
        LeagueConstructionId: "",
        ActivityCover: "",
        ActivityCoverPath: "",
        Range: [],
        ActivityStartTime: "",
        ActivityEndTime: "",
        ActivityTitle: "",
        ActivityContent: "",
        ActivityPhotos: "",
        ActivityPhotoPaths: "",
      }
    };
  },
  methods: {

    resetFormData() {
      let temp = {
        LeagueConstructionId: "",
        ActivityCover: "",
        ActivityCoverPath: "",
        Range: [],
        ActivityStartTime: "",
        ActivityEndTime: "",
        ActivityTitle: "",
        ActivityContent: "",
        ActivityPhotos: "",
        ActivityPhotoPaths: "",
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      this.formLoading = true;
      systemLeagueConstruction.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);

        this.formData.Range = [this.formData.ActivityStartTime, this.formData.ActivityEndTime]

        this.ActivityCoverFileList = [];
        if (this.formData.ActivityCoverPath) {
          this.ActivityCoverFileList = [
            { Id: this.formData.ActivityCover, Path: this.formData.ActivityCoverPath }
          ];
        }
        this.ActivityPhotoFileList = [];
        if (this.formData.ActivityPhotoPaths) {
          var activityPhotos = this.formData.ActivityPhotos.split(',')
          var activityPhotoPaths = this.formData.ActivityPhotoPaths.split(',')

          activityPhotos.forEach((element, index) => {
            var photoPath = activityPhotoPaths[index];
            this.ActivityPhotoFileList.push({ Id: element, Path: photoPath });
          });
        }

        this.formLoading = false;
      });
    },


    //保存
    createData() {
      let validate = this.$refs.formData.validate();

      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));

          if (postData.Range && postData.Range.length == 2) {
            postData.ActivityStartTime = postData.Range[0];
            postData.ActivityEndTime = postData.Range[1];
          }
          //提交数据保存
          let result = null;
          if (this.dialogStatus == "create") {
            delete postData.LeagueConstructionId;
            result = systemLeagueConstruction.add(postData);
          } else if (this.dialogStatus == "update") {
            result = systemLeagueConstruction.edit(postData);
          }
          result.then(res => {
            if (this.isContinue) {
              this.resetFormData();
              this.$emit("reload");
            } else {
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.$refs.appDialogRef.createData();
            }
          });
        }
      });
    },

    handleActivityCoverUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.ActivityCoverPath = imgs[0].Path
        this.formData.ActivityCover = imgs[0].Id
      } else {
        this.formData.ActivityCover = ''
        this.formData.ActivityCoverPath = ''
      }
    },

    handleActivityPhotoUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.ActivityPhotoPaths = imgs.map(s => s.Path).toString()
        this.formData.ActivityPhotos = imgs.map(s => s.Id).toString()
      } else {
        this.formData.ActivityPhotos = ''
        this.formData.ActivityPhotoPaths = ''
      }
    },

    handleSave() {
      this.createData();
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
