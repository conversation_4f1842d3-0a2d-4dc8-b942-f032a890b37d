<!--维修单设备配置-->
<template>
<!--组件内容区-->
<div class="app-container">
    <div class="bg-white" v-loading='loading'>
        <!-- <page-title title="故障分类标签" :subTitle="['设备相关参数的添加、管理页面']"></page-title> -->
        <div style="position:absolute;top:0;bottom:0px;width:100%;">
            <el-col :span="4" style="border-right:1px solid #eee;position:relative;height:100%;">
                <div style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                    <span style="line-height:30px;">设备列表({{equipmentList1?equipmentList1.length:0}})</span>
                    <span class="node-btn-area">
                        <el-button v-if="btnMaintain=='btnMaintain'" style="float: right;" type="primary" @click="handleAdd(1)">添加</el-button>
                    </span>
                </div>
                <div style="height:calc(100% - 50px);overflow-y:auto;width:100%;">
                    <tags mode="list" class="tagsList" :items="equipmentList1" v-model="equipmentChecked1">
                        <template v-for="(v, idx) in equipmentList1" :slot="v.value" slot-scope="scope">
                            <div class="item-warpper" :key="idx">
                                <div class="item-title omit" :title="v.label">{{ v.label }}</div>
                                <div class="item-opts">
                                    <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, v)">
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="edit">修改名称</el-dropdown-item>
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="delete">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </div>
                        </template>
                    </tags>

                    <no-data v-show="equipmentList1.length == 0"></no-data>
                </div>
            </el-col>
            <el-col :span="4" style="border-right:1px solid #eee;position:relative;height:100%;">
                <div style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                    <span style="line-height:30px;">属性列表({{equipmentList2?equipmentList2.length:0}})</span>
                    <span class="node-btn-area">
                        <el-button style="float: right;" type="primary" :disabled="!(equipmentList1&&equipmentList1.length>0)" @click="handleAdd(2)" v-if="btnMaintain=='btnMaintain'">添加</el-button>
                    </span>
                </div>
                <div style="height:calc(100% - 50px);overflow-y:auto;width:100%;">
                    <tags mode="list" class="tagsList" :items="equipmentList2" v-model="equipmentChecked2">
                        <template v-for="(v, idx) in equipmentList2" :slot="v.value" slot-scope="scope">
                            <div class="item-warpper" :key="idx">
                                <div class="item-title omit" :title="v.label">{{ v.label }}</div>
                                <div class="item-opts">
                                    <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, v)">
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="edit">修改名称</el-dropdown-item>
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="delete">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </div>
                        </template>
                    </tags>

                    <no-data v-show="equipmentList2.length == 0"></no-data>
                </div>
            </el-col>
            <el-col :span="16" style="border-right:1px solid #eee;position:relative;height:100%;">
                <div style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                    <span style="line-height:30px;">值列表({{equipmentList3?equipmentList3.length:0}})</span>
                    <span class="node-btn-area">
                        <el-button style="float: right;" type="primary" :disabled="!(equipmentList2&&equipmentList2.length>0)" @click="handleAdd(3)" v-if="btnMaintain=='btnMaintain'">添加</el-button>
                    </span>
                </div>
                <div style="height:calc(100% - 50px);overflow-y:auto;width:100%;">
                    <tags mode="list" class="tagsList" :items="equipmentList3" v-model="equipmentChecked3">
                        <template v-for="(v, idx) in equipmentList3" :slot="v.value" slot-scope="scope">
                            <div class="item-warpper" :key="idx">
                                <div class="item-title omit" :title="v.label">{{ v.label }}</div>
                                <div class="item-opts">
                                    <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, v)">
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="edit">修改名称</el-dropdown-item>
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="delete">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </div>
                        </template>
                    </tags>
                    <no-data v-show="equipmentList3.length == 0"></no-data>
                </div>
            </el-col>
        </div>
    </div>

    <!--弹窗组件区-->
    <add :equipmentSettingType="equipmentSettingType" :parentId="parentId" :id="addId" :dialogStatus="addDialogStatus" :dialogFormVisible="addDialogFormVisible" @closeDialog="addCloseDialog" @saveSuccess="addSaveSuccess"></add>
</div>
</template>

<!--组件脚本区-->

<script>
/**引用区 */
//按照以下顺序
//组件 import empSelector from "../../../../common/empSelector";
import * as equipmentSetting from "@/api/equipmentSetting";
import noData from "@/views/common/components/noData";
import add from "./add";
//方法、属性 import { empSelector } from "../../../../common/empSelector";

export default {
    /**名称 */
    name: "attr-conf-index",
    /**组件声明 */
    components: {
        add,
        noData
    },
    /**参数区 */
    props: {
        /**主键Id */
        keyId: {
            type: String
        }
    },
    /**数据区 */
    data() {
        return {
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            btnMaintain: '',
            equipmentSettingType: 1,
            /**第一列设备类型 */
            equipmentList1: [],
            equipmentChecked1: "",
            /**第二列属性 */
            equipmentList2: [],
            equipmentChecked2: "",
            /**第三列值 */
            equipmentList3: [],
            equipmentChecked3: "",
            /**弹窗 */
            addDialogStatus: "create",
            addDialogFormVisible: false,
            /**父级ID */
            parentId: "",
            /**add组件Id */
            addId: ""
        };
    },
    /**计算属性---响应式依赖 */
    computed: {},
    /**监听 */
    watch: {
        equipmentChecked1(val) {
            this.loadData(2);
        },
        equipmentChecked2(val) {
            this.loadData(3);
        }
    },
    /**渲染前 */
    created() {
        this.btnTextValue()
    },
    /**渲染后 */
    mounted() {
        this.loadData(1);
    },
    /**方法区 */
    methods: {
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },
        loadData(type) {
            this.loading=true;
            let _this = this;
            let paramData = {
                EquipmentSettingType: type,
                parentId: type == 1 ?
                    null :
                    type == 2 ?
                    this.equipmentChecked1 :
                    this.equipmentChecked2
            };
            if (!paramData.parentId) {
                if (type == 2) {
                    _this.equipmentList2 = [];
                    _this.equipmentChecked2 = "";
                    _this.equipmentList3 = [];
                    _this.equipmentChecked3 = "";
                    this.loading=false;
                    return;
                } else if (type == 3) {
                    _this.equipmentList3 = [];
                    _this.equipmentChecked3 = "";
                    this.loading=false;
                    return;
                }
            }
            equipmentSetting.getListByCondition(paramData).then(response => {
                this.loading=false;
                let tempData = response.map(t => {
                    t.label = t.Name;
                    t.value = t.Id;
                    return t;
                });
                switch (type) {
                    case 1:
                        _this.equipmentList1 = tempData;
                        if (!tempData.find(t => t.Id == _this.equipmentChecked1)) {
                            _this.equipmentChecked1 = tempData[0] ? tempData[0].Id : "";
                        }
                        break;
                    case 2:
                        _this.equipmentList2 = tempData;
                        if (!tempData.find(t => t.Id == _this.equipmentChecked2)) {
                            _this.equipmentChecked2 = tempData[0] ? tempData[0].Id : "";
                        }
                        break;
                    case 3:
                        _this.equipmentList3 = tempData;
                        if (!tempData.find(t => t.Id == _this.equipmentChecked3)) {
                            _this.equipmentChecked3 = tempData[0] ? tempData[0].Id : "";
                        }
                        break;
                    default:
                        _this.equipmentList1 = tempData;
                        if (!tempData.find(t => t.Id == _this.equipmentChecked1)) {
                            _this.equipmentChecked1 = tempData[0] ? tempData[0].Id : "";
                        }
                        break;
                }
                /**递归渲染 */
                if (type < 3) {
                    _this.loadData(type + 1);
                }
            }).catch(err => {
                this.loading=false;
            });
        },
        //左侧树操作菜单
        handleCommand(optType, data) {
            if (optType == "edit") {
                this.handleEdit(data);
            } else if (optType == "delete") {
                this.handleDelete(data);
            }
        },
        /**新增 */
        handleAdd(type) {
            let _this = this;
            _this.equipmentSettingType = type;
            switch (type) {
                case 1:
                    _this.parentId = null;
                    break;
                case 2:
                    _this.parentId = _this.equipmentChecked1;
                    break;
                case 3:
                    _this.parentId = _this.equipmentChecked2;
                    break;
                default:
                    _this.parentId = null;
                    break;
            }
            _this.addDialogStatus = "create";
            _this.addDialogFormVisible = true;
        },
        /**编辑 */
        handleEdit(data) {
            this.addDialogStatus = "update";
            this.addId = data.Id;
            this.equipmentSettingType = data.EquipmentSettingType;
            console.log(data);

            this.addDialogFormVisible = true;
        },
        /**删除 */
        handleDelete(data) {
            let _this = this;
            equipmentSetting.getChildrenNumber({
                id: data.Id
            }).then(response => {
                if (response.Count > 0) {
                    _this.$message.error("该内容下存在未删除的内容");
                } else {
                    _this
                        .$confirm(`是否确认删除${data.Name}吗?`, "提示", {
                            confirmButtonText: "确认",
                            cancelButtonText: "取消",
                            type: "warning"
                        })
                        .then(() => {
                            equipmentSetting.del([data.Id]).then(response => {
                                _this.loadData(1);
                                _this.$notify({
                                    title: "成功",
                                    message: "删除成功",
                                    type: "success",
                                    duration: 2000
                                });
                            });
                        });
                }
            });
        },
        /**新增弹窗保存成功 */
        addSaveSuccess() {
            this.loadData(1);
            this.addCloseDialog();
        },
        /**新增弹窗关闭 */
        addCloseDialog() {
            this.addDialogFormVisible = false;
        }
    }
};
</script>

<!--组件样式区-->

<style lang="scss" scoped>
.item-warpper {
    display: flex;
    justify-content: space-between;

    .item-title {
        display: inline;
        line-height: 26px;
        position: relative;

        >span {
            position: absolute;
            right: 0;
        }
    }

    // .item-opts{
    //     width: 60px;
    // }
}
</style>
