<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="500">
            <template slot="body">
                <div class="wrapperBox">
                    <el-form :rules="rules" ref="formData" :model="formData" style="padding-top: 0;" v-loading='productLoading'
                    label-position="right" label-width="100px" class="wrapperMain">
                        <el-form-item label="产品名称" prop="Name" v-if="dialogType == 1">
                            <el-input class="wrapperBox_input" :disabled="!editable" maxlength="50" type="text" v-model.trim="formData.Name"></el-input>
                        </el-form-item>
                        <template v-else>
                            <el-form-item label="所属产品" prop="ProductImprovementId">
                                <el-select v-model="formData.ProductImprovementId" placeholder="请选择所属产品" class="wrapperBox_input" clearable>
                                    <el-option v-for="(item,index) in productData" :key="index" :label="item.Name" :value="item.Id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="型号名称" prop="Specification">
                                <el-input class="wrapperBox_input" :disabled="!editable" maxlength="50" type="text" v-model.trim="formData.Specification"></el-input>
                            </el-form-item>
                        </template>
                    </el-form>
                </div>
            </template>
            <template slot="footer">
                <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 （产品管理）-->
                <app-button v-if="dialogType == 1" @click="createProductData" :buttonType="1" :disabled="disabledBtn"></app-button>
                <!-- 确认 （产品型号管理）-->
                <app-button v-if="dialogType == 2" @click="createProductModelData" :buttonType="1" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
// 产品管理
import * as productImprovementApi from '@/api/knowledge/productImprovement.js'
// 产品型号管理
import * as productSpecificationImprovementApi from '@/api/knowledge/productSpecificationImprovement.js'

export default {
    name: "product-model-create",
    directives: {},
    components: {
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            let name = this.dialogType == 1 ? '产品' : '产品型号';
            if (this.dialogStatus == "create") {
                return `添加${name}`;
            }
            if (this.dialogStatus == "update") {
                return `编辑${name}`;
            }
            return `${name}详情`;
        },
    },
    filters: {
    },
    props: {
        //编辑还是新增(create:  新增;    update ： 编辑  detail: 详情
        dialogStatus: {
            type: String,
            default: 'detail',
        },
        // 1  产品管理  2 产品型号管理
        dialogType: {
            type: Number,
            require: true,
            default: 1,
        },
        node: {
            type: Object,
            default: ()=>{},
        },
        parentNode: {
            type: Object,
            default: ()=>{},
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                }
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            
            goOn: false,
            loading: false,
            rules: {
                Name: {fieldName: "产品名称",rules: [{ required: true }]},
                ProductImprovementId: {fieldName: "所属产品",rules: [{ required: true, trigger: 'change' }]},
                Specification: {fieldName: "型号名称",rules: [{ required: true }]},
            },
            formData: {
                Id: '',
                Name: '',// 产品名称
                ProductImprovementId: '', //
                Specification: '', // 型号名称
            },

            
            productLoading: false,
            productData: [],
        };
    },
    methods: {
        resetFormData() {
            this.formData = this.$options.data().formData
            if (this.dialogType == 1) {
                this.formData.Id = '';
                this.formData.Name = '';
                if (this.dialogStatus == 'update') {
                    this.formData.Id = this.node.Id || '';
                    this.formData.Name = this.node.Name || '';
                }
            }
            if (this.dialogType == 2) {
                this.getProduct();// 查询 产品列表
                // this.formData.ProductImprovementId = this.parentNode.Id
                this.formData.Name = this.parentNode.Name
                if (this.dialogStatus == 'update') {
                    this.formData.Id = this.node.Id
                    this.formData.Specification = this.node.Specification
                    // this.formData.ProductImprovementId = this.node.ProductImprovementId
                    this.formData.Name = this.node.ProductImprovementName
                }
            }
        },
        // 产品管理  确定(新增/编辑)
        createProductData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = productImprovementApi.add({Name: postData.Name});
                    } else if (self.dialogStatus == "update") {
                        result = productImprovementApi.edit({Id: postData.Id, Name: postData.Name});
                    }

                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        if(self.goOn){
                            self.$refs['formData'].resetFields();
                            self.resetFormData();
                            self.$emit('reload');
                        } else {
                            self.$refs.appDialogRef.createData();
                        }
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 产品型号管理  确定(新增/编辑)
        createProductModelData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = productSpecificationImprovementApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = productSpecificationImprovementApi.edit(postData);
                    }

                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        if(self.goOn){
                            self.$refs['formData'].resetFields();
                            self.resetFormData();
                            self.$emit('reload');
                        } else {
                            self.$refs.appDialogRef.createData();
                        }
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 查询 产品列表
        getProduct() {
            let self = this;
            self.productLoading = true
            productImprovementApi.getList({
                PageIndex: 1,
                PageSize: 99999,
            }).then(res => {
                self.productData = res.Items;

                // ProductImprovementId  避免加载 产品数据时候  会闪动显示id  在查询出结果后 再赋值
                self.formData.ProductImprovementId = self.parentNode.Id
                if (self.dialogStatus == 'update') {
                    self.formData.ProductImprovementId = self.node.ProductImprovementId
                }
                self.productLoading = false
            }).catch(err => {
                self.productLoading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
.wrapperBox >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>
<style lang='scss' scoped>
.wrapperBox{
    width: 100%;
    padding-top: 15px;
    &_input{
        width: 85%;
    }
}
</style>