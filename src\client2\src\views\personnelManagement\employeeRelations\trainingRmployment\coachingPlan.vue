<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-row class="wrapper __dynamicTabContentWrapper">
                    <div class="__dynamicTabWrapper">
                        <app-table ref="mainTable1" :tab-columns="tabAllColumns" :tab-datas="CoachPlanData" :loading="CoachPlanLoading"
                            :isShowAllColumn="true" :isShowOpatColumn="editable" :optColWidth="80"
                            :multable="false" layoutMode='simple' :isShowBtnsArea='editable' :isShowConditionArea="false">
                            <template slot="AccomplishTime" slot-scope="scope">{{ scope.row.AccomplishTime | dateFilter('YYYY-MM-DD') }}</template>
                            
                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <el-button type="primary" @click="handleAdd">添加计划</el-button>
                            </template>
                            
                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button @click="handleReview(scope.row, 'update')" :type="1"></app-table-row-button>
                                <app-table-row-button @click="handleDelRow(scope.row)" :type="3"></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                </el-row>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="999" type text="关闭"></app-button>
                <!-- 确认 -->
                <!-- <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button> -->
            </template>
        </app-dialog>
        <!-- 添加/编辑/查看 辅导计划 -->
        <coaching-plan-create v-if="dialogCreatePlanFormVisible" @closeDialog="closeCreatePlanDialog"
        @saveSuccess="handleCreatePlanSaveSuccess" :id="selectRow.Id" :employeeId="employeeId"
        :dialogFormVisible="dialogCreatePlanFormVisible" :dialogStatus="dialogCreatePlanStatus"></coaching-plan-create>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as EmployeeCoachPlanApi from "@/api/personnelManagement/EmployeeCoachPlan";
import coachingPlanCreate from "./coachingPlanCreate"
export default {
    name: "trainingRmployment-coachingPlan",
    directives: {},
    components: {
        coachingPlanCreate
    },
    mixins: [indexPageMixin],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "辅导计划";
            } else if (this.dialogStatus == "detail") {
                return "计划详情";
            }
            return "";
        },
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            default: "update",
        },
        id: {
            type: String,
            default: "",
        },
        employeeId: {
            type: String,
            default: "",
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    if (this.dialogStatus != "create" && this.employeeId) {
                        this.getCoachPlan();// 查询 辅导计划
                    }
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            dialogCreatePlanFormVisible: false,
            selectRow: {},
            dialogCreatePlanStatus: 'create',

            loading: false,
            // 辅导计划
            CoachPlanData: [],
            CoachPlanLoading: false,
            tabAllColumns: [
                {attr: {prop: "CoachPlan",label: "辅导计划"}},
                {attr: {prop: "Target",label: "衡量标准/目标"}},
                {attr: {prop: "AccomplishTime",label: "完成时间",width: "100px"}, slot: true},
            ],
        };
    },
    methods: {
        handleDelRow(row){
            let self = this;
            self.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                EmployeeCoachPlanApi.del([row.Id]).then(res => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getCoachPlan();
                });
            });
        },
        // 查询 辅导计划
        getCoachPlan(){
            this.CoachPlanLoading = true
            EmployeeCoachPlanApi.getList({ EmployeeId: this.employeeId }).then(res => {
                this.CoachPlanData = res;
                this.CoachPlanLoading = false
            }).catch(err => {
                this.CoachPlanLoading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 添加弹窗
        handleAdd() {
            this.selectRow = {};
            this.dialogCreatePlanStatus = 'create';
            this.dialogCreatePlanFormVisible = true
        },
        // 编辑/查看弹窗
        handleReview(row, optType) {
            this.selectRow = row;
            this.dialogCreatePlanStatus = optType;
            this.dialogCreatePlanFormVisible = true
        },
        // 添加新人培养计划 成功回调
        handleCreatePlanSaveSuccess(){
            this.getCoachPlan()
            this.closeCreatePlanDialog()
        },
        // 关闭 添加新人培养计划弹窗
        closeCreatePlanDialog(){
            this.dialogCreatePlanFormVisible = false
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
    height: 360px;
}
</style>