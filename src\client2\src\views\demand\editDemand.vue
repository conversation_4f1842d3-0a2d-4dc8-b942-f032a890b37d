<template>
  <el-dialog class="dialog-mini" width="1000px" :title="textMap[dialogEditStatus]" :visible.sync="isVisible" :before-close="handleClose" :close-on-click-modal='false' :append-to-body='true'>
    <el-form ref="dataForm" :model="dataForm" :rules="dataFormRules" label-position="right" label-width="100px" v-loading="dialogFormLoading">
      <el-row>
        <el-col :span="12">
          <el-form-item label="需求编号" prop='DemandNumber'>
            <el-input style="width: 100%;" :disabled="true" v-model="dataForm.DemandNumber" placeholder></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop='Status'>
            <el-select style="width: 100%;" class="sel-ipt" v-model="dataForm.Status" placeholder clearable :disabled="true">
              <el-option v-for="item in ProjectDemandStatusEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人" prop='CreatorEmployeeName'>
            <el-input style="width: 100%;" :disabled="true" v-model="dataForm.CreatorEmployeeName" placeholder></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建日期" prop='CreateDateTime'>
            <el-input style="width: 100%;" :disabled="true" :value='dataForm.CreateDateTime | dateFilter("YYYY-MM-DD HH:mm:ss")' placeholder></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop='Priority'>
            <el-select class="sel-ipt" style="width: 100%;" v-model="dataForm.Priority" placeholder clearable :disabled="!editable">
              <el-option v-for="item in ProjectDemandPriorityEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isResearchAndDevelopment">
          <el-form-item label="终端类型" prop='TerminalType'>
            <el-select style="width: 100%;" class="sel-ipt" v-model="dataForm.TerminalType" clearable :disabled="!editable" placeholder="请选择终端类型">
              <el-option v-for="item in ProjectDemandTerminalTypeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="isResearchAndDevelopment">
          <el-form-item label="迭代" prop='ProjectManagementIterationPlanId'>
            <treeselect :zIndex='9999' class="treeselect-common" :normalizer="normalizer" key='type1' v-model="dataForm.ProjectManagementIterationPlanId" :default-expand-level="3" :options="iterationTreeData" :multiple="false" placeholder='' :show-count="false" :disable-branch-nodes="true" :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree" :disabled="!editable">
            </treeselect>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经办人" prop='ResponsiblePersonEmployee'>
            <emp-selector :multiple="false" :showType="2" :list="dataForm.ResponsiblePersonEmployee" @change="handleChangeEmployee" :readonly="!editable"></emp-selector>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="需求标题" prop='DemandTitle'>
            <el-input style="width: 100%;" v-model="dataForm.DemandTitle" placeholder maxlength="100" :disabled="!editable"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 需求描述富文本相关 -->
      <el-row>
        <el-col>
          <el-form-item>
            <el-tabs v-model="activeCompetitorDynamicAttributeName">
              <el-tab-pane v-for="CompetitorDynamicAttribute in CompetitorDynamicAttributeList" :key="CompetitorDynamicAttribute.ID" :label="CompetitorDynamicAttribute.Name" :name="CompetitorDynamicAttribute.ID"></el-tab-pane>
            </el-tabs>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-show="activeCompetitorDynamicAttributeName == '1'">
          <el-form-item>
            <Ueditor ref="ue1" id="ue1" v-show="editable" v-bind:content="dataForm.DemandDescribe" @ready="ueReady"></Ueditor>
            <div class="reset-rich" v-if="dataForm.DemandDescribe" v-html="dataForm.DemandDescribe" v-show="!editable"></div>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-show="activeCompetitorDynamicAttributeName == '2'">
          <el-form-item>
            <Ueditor ref="ue2" id="ue2" v-show="editable" v-bind:content="dataForm.DemandSceneDescribe"></Ueditor>
            <div class="reset-rich" v-if="dataForm.DemandSceneDescribe" v-html="dataForm.DemandSceneDescribe" v-show="!editable"></div>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-show="activeCompetitorDynamicAttributeName == '3'">
          <el-form-item>
            <Ueditor ref="ue3" id="ue3" v-show="editable" v-bind:content="dataForm.DemandAnalysis"></Ueditor>
            <div class="reset-rich" v-if="dataForm.DemandAnalysis" v-html="dataForm.DemandAnalysis" v-show="!editable"></div>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-show="activeCompetitorDynamicAttributeName == '4'">
          <el-form-item>
            <Ueditor ref="ue4" id="ue4" v-show="editable" v-bind:content="dataForm.DemandSolution"></Ueditor>
            <div class="reset-rich" v-if="dataForm.DemandSolution" v-html="dataForm.DemandSolution" v-show="!editable"></div>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item :label="'附件'" prop="DemandAttachmentResourceList">
            <app-upload-big-file :limitTotalSize="1024 * 1024 * 1024" :max="10000" accept="all" :fileType="4" :value="dataForm.DemandAttachmentResourceList" :fileSize="1024 * 1024 * 500" @change="handleFilesUpChange" :readonly="!editable"></app-upload-big-file>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="this.dialogEditStatus == 'detail'">
        <comment-list :ref="'commentList'" businessType='project' :keyType='3' :id="id" :optType='0'></comment-list>
      </el-row>
      <div style="height: 10px; width: 100%;"></div>
      <el-row v-if="this.dialogEditStatus == 'detail'">
        <opt-list :keyType='3' :id="id"></opt-list>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button size="mini" v-if="dialogEditStatus != 'detail'" type="primary" :loading="postLoading" @click="submission">保存</el-button>
      <el-button @click="onComment" type="primary" v-show="dialogEditStatus == 'detail'">评论</el-button>
    </div>
  </el-dialog>
</template>

<script>
import * as projectDemandApi from "@/api/projectManagementDemand"
import CommentList from "../task/commentList"
import empSelector from "../common/empSelector"
import commentMixins from '../viewProjectInformation/commentMixins'
import localMixins from "../question/localMixins"
import OptList from "../task/optList"
import { ProjectDemandStatusEnum, ProjectDemandPriorityEnum, ProjectDemandTerminalTypeEnum } from "./enums"
import Ueditor from '@/components/Ueditor'
import { getToken, getUserInfo } from "@/utils/auth"
export default {
  name: 'demand-edit',
  props: {
    dialogEditStatus: {
      type: String,
      default: ''
    },
    dialogEditVisible: {
      type: Boolean,
      default: false
    },
    id: {
      required: true,
      default: ""
    },
    ProjectId: {
      type: String,
      default: undefined
    },
    isResearchAndDevelopment: {
      type: Boolean,
      default: false
    },
  },
  mixins: [commentMixins, localMixins],
  components: {
    CommentList,
    OptList,
    empSelector,
    Ueditor,
  },
  watch: {
    dialogEditStatus(val) {
      if (val == 'create') {
        this.dataForm.CreatorEmployeeName = getUserInfo().empName;
      }
    },
    dialogEditVisible: {
      handler(val) {
        this.isVisible = val
      },
      immediate: true
    },
  },
  computed: {
    editable() {
      return this.dialogEditStatus != 'detail'
    },
  },
  created() {
    this.dataFormRules = this.initRules(this.dataFormRules);
  },
  data() {
    return {
      activeCompetitorDynamicAttributeName: "1",
      CompetitorDynamicAttributeList: [
        {
          ID: "1",
          Name: "问题描述"
        },
        {
          ID: "2",
          Name: "场景分析"
        },
        {
          ID: "3",
          Name: "问题分析"
        },
        {
          ID: "4",
          Name: "解决方案"
        }
      ],

      normalizer(node) {
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        }
      },
      ProjectDemandStatusEnum: ProjectDemandStatusEnum,
      ProjectDemandPriorityEnum: ProjectDemandPriorityEnum,
      ProjectDemandTerminalTypeEnum: ProjectDemandTerminalTypeEnum,
      postLoading: false,
      isVisible: false,
      dialogFormLoading: false,
      textMap: {
        update: "编辑需求",
        create: "添加需求",
        detail: "查看需求详情"
      },
      dataForm: {
        ProjectManagementDemandId: this.id,
        CreatorEmployeeName: '',
        Priority: '',
        ResponsiblePersonEmployeeId: '',
        ResponsiblePersonEmployee: [],
        DemandTitle: '',
        DemandDescribe: "",
        DemandSceneDescribe: "",
        DemandAnalysis: "",
        DemandSolution: "",
        DemandAttachmentResourceList: [],
        DemandAttachmentResourceIdList: [],
        ProjectManagementIterationPlanId: undefined,
        TerminalType: '',
      },
      dataFormRules: {
        Priority: { fieldName: "优先级", rules: [{ required: true }] },
        DemandTitle: { fieldName: "需求标题", rules: [{ required: true }, { max: 100 }] },
        TerminalType: { fieldName: "终端类型", rules: [{ required: true }] },
        ResponsiblePersonEmployee: { fieldName: "经办人", rules: [{ required: true }] },
      },
    }
  },
  methods: {
    // 保存提交
    submission() {
      let self = this;
      self.postLoading = true;
      self.$refs["dataForm"].validate(valid => {
        if (!valid) {
          self.postLoading = false
        }
        if (valid) {
          let formData = JSON.parse(JSON.stringify(self.dataForm));
          formData.DemandDescribe = this.$refs.ue1.getObj().content;
          formData.DemandSceneDescribe = this.$refs.ue2.getObj().content;
          formData.DemandAnalysis = this.$refs.ue3.getObj().content;
          formData.DemandSolution = this.$refs.ue4.getObj().content;
          formData.ProjectId = self.ProjectId

          //经办人信息
          if (formData.ResponsiblePersonEmployee && formData.ResponsiblePersonEmployee.length > 0) {
            formData.ResponsiblePersonEmployeeId = formData.ResponsiblePersonEmployee[0].EmployeeId
          } else {
            formData.ResponsiblePersonEmployeeId = ''
          }

          //终端类型
          formData.TerminalType = formData.TerminalType == "" ? 0 : formData.TerminalType;

          //设置附件ID集合
          if (formData.DemandAttachmentResourceList && formData.DemandAttachmentResourceList.length > 0) {
            formData.DemandAttachmentResourceIdList = formData.DemandAttachmentResourceList.map(s => s.Id);
            let ids = JSON.parse(JSON.stringify(formData.DemandAttachmentResourceList.map(o => o.FileMd5value)));
            if (new Set(ids).size != ids.length) {
              this.$message({
                message: "请勿上传重复附件",
                type: "error"
              });
              self.postLoading = false
              return false;
            }
          }
          delete formData.DemandAttachmentResourceList;

          //判断当前操作 add or edit
          let response = null;
          if (self.dialogEditStatus == "create") {
            response = projectDemandApi.add(formData);
          } else if (self.dialogEditStatus == "update") {
            response = projectDemandApi.edit(formData);
          }

          if (response) {
            response.then(response => {
              // 需要回填数据库生成的数据
              self.postLoading = false;
              self.isVisible = false;
              self.$notify({
                title: "成功",
                message: "操作成功",
                type: "success",
                duration: 2000
              });
              self.isVisible = false;
              self.handleClose()
              self.$emit('saveSuccess')
            }).catch(err => {
              self.postLoading = false
            });
          }
        }
      }
      );
    },
    //选择经办人
    handleChangeEmployee(emp) {
      if (emp && emp.length > 0) {
        this.dataForm.ResponsiblePersonEmployee = emp;
      } else {
        this.dataForm.ResponsiblePersonEmployee = [];
      }
      this.$refs["dataForm"].validateField("ResponsiblePersonEmployee");
    },

    //选择附件
    handleFilesUpChange(files) {
      this.dataForm.DemandAttachmentResourceList = files;
    },

    ueReady() {
      if (this.dialogEditStatus != "create") {
        this.getDetail();
      }
    },
    //获取详情
    getDetail() {
      projectDemandApi.detail({ projectManagementDemandId: this.id }).then(response => {
        let detail = response;
        this.dataForm = Object.assign({}, detail);
        if (detail.ResponsiblePersonEmployeeDto) {
          this.dataForm.ResponsiblePersonEmployee = [detail.ResponsiblePersonEmployeeDto]
        }
        //Ueditor 插件不支持 传入null,会报错
        if (this.dataForm.DemandDescribe == null) {
          this.dataForm.DemandDescribe = "";
        }
        this.dataForm.TerminalType = detail.TerminalType == 0 ? "" : detail.TerminalType;
        this.dialogFormLoading = false;
      });
    },
    //清理表单
    resetTemp() {
      this.dataForm = {
        ProjectManagementDemandId: this.id,
        DemandNumber: '',
        Status: '',
        CreatorEmployeeName: '',
        CreateDateTime: '',
        Priority: '',
        ResponsiblePersonEmployeeId: '',
        ResponsiblePersonEmployee: [],
        DemandTitle: '',
        DemandDescribe: "",
        DemandAttachmentResourceList: [],
        DemandAttachmentResourceIdList: [],
        ProjectManagementIterationPlanId: undefined,
        TerminalType: '',
      };
    },
    handleClose() {
      this.cancel()
    },
    cancel() {
      this.$emit("closeDialog");
    },

  },


}
</script>