<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='Code'>
            <el-input style="width: 100%;" v-model="listQueryParams.Code" placeholder=""></el-input>
          </template>
          <template slot='CustomerName'>
            <el-input style="width: 100%;" v-model="listQueryParams.CustomerName" placeholder=""></el-input>
          </template>
          <template slot='EmployeeName'>
            <el-input style="width: 100%;" v-model="listQueryParams.EmployeeName" placeholder=""></el-input>
          </template>
        </app-table-form>
      </template>

      <template slot="CustomerStatusType" slot-scope="scope">
        <span
          class="item-status"
          :style="{backgroundColor: getStatusObj(scope.row.CustomerStatusType).color}"
        >{{ scope.row.CustomerStatusType | customerStatusTypeFilter}}</span>
      </template>
      <!-- <template slot="Score" slot-scope="scope">{{ scope.row.Score | scoreFilter }}</template> -->
      <template
        slot="SalesmanEmployee"
        slot-scope="scope"
      >{{ scope.row.SalesmanEmployee | nameFilter }}</template>
      <template
        slot="LastUpdateTime"
        slot-scope="scope"
      >{{ scope.row.LastUpdateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>
    </listSelector>
  </div>
</template>

<script>
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
import { vars } from "@/views/salesMgmt/common/vars";

export default {
  name: "customer-selector",
  components: {
    listSelector,
  },
  created() {
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },
  filters: {
    /**名称 */
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    /**状态 */
    customerStatusTypeFilter(status) {
      const statusObj = vars.customeMgmt.customerStatusTypes.find(
        (s) => s.value == status
      );
      if (statusObj) {
        return statusObj.label;
      }
      return "";
    },
    /**评分 */
    // scoreFilter(score) {
    //   const scoreObj = vars.customeMgmt.scores.find((s) => s.value == score);
    //   if (scoreObj) {
    //     return scoreObj.label;
    //   }
    //   return "无";
    // },
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList(val) {
      if (val && val.length > 0) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
      }else{
        this.listSelectorCheckedData = []
      }
    },
  },
  data() {
    return {
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/Customer/GetListPage",
      listSelectorMultiple: this.multiple,
      listQueryParams: {
        Code: '',
        CustomerName: '',
        EmployeeName: '',
      },
      tableSearchItems: [
        { prop: "Code", label: "客户编号" },
        { prop: "CustomerName", label: "客户名称" },
        { prop: "EmployeeName", label: "负责人" },
      ],
      listSelectorTitle: "选择关联客户",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",

      listSelectorColumnData: [
        {
          attr: { prop: "Code", label: "客户编号" },
        },
        {
          attr: { prop: "ClientUnits", label: "客户单位" },
        },
        {
          attr: { prop: "CustomerName", label: "客户名称" },
        },
        {
          attr: {
            prop: "CustomerStatusType",
            label: "客户状态",
          },
          slot: true,
        },
        // {
        //   attr: { prop: "Score", label: "综合评分" },
        //   slot: true,
        // },
        {
          attr: { prop: "SalesmanEmployee", label: "负责人" },
          slot: true,
        },
        {
          attr: {
            prop: "LastUpdateTime",
            label: "最后跟进时间",
            align: "center",
          },
          slot: true,
        },
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  methods: {
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      this.listQueryParams.Code = ''
      this.listQueryParams.CustomerName = ''
      this.listQueryParams.EmployeeName = ''
      this.handleFilter()
    },
    getStatusObj(status) {
      return (
        vars.customeMgmt.customerStatusTypes.find((s) => s.value == status) ||
        {}
      );
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let list =
        data.map((s) => {
          s.Code = s.OrderNumber;
          return s;
        }) || [];
      this.$emit("changed", JSON.parse(JSON.stringify(list)));
      this.listSelectorCloseDialog();
    },
  },
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>