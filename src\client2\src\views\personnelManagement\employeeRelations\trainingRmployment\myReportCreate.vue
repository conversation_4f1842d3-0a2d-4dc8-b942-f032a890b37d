<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="700">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData"
                label-position="right" label-width="100px">
                    <el-row class="wrapper" v-loading='loading'>
                        <el-form-item label="汇报时间" prop="ReportTime">
                            <el-date-picker :disabled="!editable" v-model="formData.ReportTime" type="datetime"
                            format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="汇报类型" prop="ReportType">
                            <el-radio-group :disabled="!editable" v-model="formData.ReportType">
                                <el-radio v-for="item in ReportTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="汇报概述" prop="ReportContent">
                            <el-input :disabled="!editable" maxlength="1000" type="textarea" :rows="10" v-model="formData.ReportContent"></el-input>
                        </el-form-item>
                        <el-form-item label="相关附件">
                            <template v-if="editable">
                                <app-uploader accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList"
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            </template>
                            <template v-else>
                                <app-uploader v-if="formData.AttachmentList.length>0" accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList" readonly
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                <template v-else>无</template>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import { vars } from '../common/vars'
import empSelector from '@/views/common/empSelector'
import * as EmployeeReportApi from "@/api/personnelManagement/EmployeeReport";
export default {
    name: "trainingRmployment-myReportCreate",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "添加汇报";
            } else if (this.dialogStatus == "update") {
                return "编辑汇报";
            } else if (this.dialogStatus == "detail") {
                return "汇报详情";
            }
            return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
            default: "create",
        },
        id: {
            type: String,
            default: "",
        },
        employeeId: {
            type: String,
            default: "",
        },
        
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            ReportTypeEnum: vars.ReportTypeEnum,

            disabledBtn: false,
            loading: false,
            rules: {
                ReportTime: {fieldName: "汇报时间",rules: [{ required: true }]},
                ReportContent: {fieldName: "汇报概述",rules: [{ required: true }]},
            },
            formData: {
                // Id: '',
                ReportType: 1, // 计划类型
                ReportContent: '', // 汇报概述
                ReportTime: '', // 汇报时间
                AttachmentList: [], // 附件
            }
        };
    },
    methods: {
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        resetFormData() {
            this.formData = this.$options.data().formData
            if(this.dialogStatus == "create"){
                this.formData.ReportTime = dayjs().format('YYYY-MM-DD HH:mm')
            }
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    console.log(postData)
                    postData.AttachmentIdList = postData.AttachmentList.map((s) => s.Id);
                    self.disabledBtn = true;
                    let result = null;
                    if (self.dialogStatus == 'create') {
                        postData.EmployeeId = self.employeeId;
                        result = EmployeeReportApi.add(postData)
                    }
                    if (self.dialogStatus == 'update') {
                        result = EmployeeReportApi.edit(postData)
                    }
                    result.then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            EmployeeReportApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
}
</style>