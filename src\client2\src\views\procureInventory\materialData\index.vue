<template>
<div class="app-container">
    <!-- <page-title title="在线培训" :subTitle="['在线培训管理页面']"></page-title> -->
    <div class="pageWrapper __dynamicTabContentWrapper">
        <template v-if="pageTypes===1">
            <page-title title="材料资料库" :showBackBtn='true' @goBack="back" text-bold></page-title>
            <page-title :showBackBtn='false'>
                <div slot="def">
                    <tags :items="searchTypesData" v-model="listQuery.myFavorite">
                        <template v-for="t in searchTypesData" :slot="t.value">{{ t.label }}</template>
                    </tags>
                </div>
            </page-title>
        </template>
        <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading"
            :isShowOpatColumn="true" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false'  :serial='false'>
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch"
                    :layoutMode='layoutMode'>
                        <template slot="KeyWords">
                            <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable
                            v-model.trim="listQuery.KeyWords" placeholder="搜索物料长代码/物料名称/规格型号"></el-input>
                        </template>
                        <template slot="Manufacturer">
                            <el-input style="width: 100%;" clearable v-model.trim="listQuery.Manufacturer" placeholder="搜索制造商"></el-input>
                        </template>
                        <template slot="BrandName">
                            <el-input style="width: 100%;" clearable v-model.trim="listQuery.BrandName" placeholder="搜索品牌"></el-input>
                        </template>
                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked">
                                <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                                    <el-button type="primary">
                                        {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                                    </el-button>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="batchEditClassify">修改分类</el-dropdown-item>
                                        <el-dropdown-item command="batchVisibleTime">设置时段</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </permission-btn>
                        </template>
                    </app-table-form>
                </template>
                <template slot="Idx" slot-scope="scope">
                    {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index + 1) }}
                    <i v-if="pageTypes===1&&scope.row.IsFavorite" class="el-icon-star-on" style="font-size: 20px;color: #ffb81c;vertical-align: middle;"></i>
                </template>
                <template slot="LogoPath" slot-scope="scope">
                    <img :src="scope.row.LogoPath||defImgUrl" width="110" height="60" style="float: left;" v-viewer />
                </template>

                <template slot="SpecificationsModel" slot-scope="scope">{{scope.row.SpecificationsModel||'无'}}</template>
                <template slot="Manufacturer" slot-scope="scope">{{scope.row.Manufacturer||'无'}}</template>
                <template slot="BrandName" slot-scope="scope">{{scope.row.BrandName||'无'}}</template>
                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 查看详情 -->
                    <app-table-row-button @click="handleReview(scope.row)" :type="1" text="详情"></app-table-row-button>
                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1"></app-table-row-button>

                    <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button>

                    <!-- pageTypes===1 业务百科那边使用 -->
                    <template v-if="pageTypes===1">
                        <!-- 收藏/取消收藏 -->
                        <app-table-row-button v-if="!scope.row.IsFavorite" @click="SetFavorite(scope.row)" :type="1" text="添加收藏"></app-table-row-button>
                        <app-table-row-button v-if="scope.row.IsFavorite" @click="SetFavorite(scope.row)" :type="3" text="取消收藏"></app-table-row-button>
                    </template>
                </template>
            </app-table>

        </div>
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <!-- 创建/编辑/详情 -->
    <create-page v-if="dialogFormVisible"
        @closeDialog="closeDialog"
        @saveSuccess="handleSaveSuccess"
        :dialogFormVisible="dialogFormVisible"
        :dialogStatus="dialogStatus"
        :id="selectId"
        @reload="getList"
    ></create-page>

    <!-- 批量导入 -->
    <importDialog
        v-if="dialogImportFormVisible"
        @closeDialog="dialogImportFormVisible=false"
        @saveSuccess="handleImportSaveSuccess"
        :dialogFormVisible="dialogImportFormVisible"
        :dialogStatus="'import'"
    ></importDialog>
    <!-- 批量导出 -->
    <v-export
        @closeDialog="handleCloseExport"
        :dialogFormVisible="dialogExportVisible"
        :rData="rData" :cData="cData"
    ></v-export>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as materialDataApi from "@/api/procureInventory/materialData";
import createPage from "./create";
import importDialog from "./importDialog";
import vExport from "@/components/Export/index";

export default {
    name: "material-data",
    mixins: [indexPageMixin],
    components: {
        // createFolderPage,
        createPage,
        // createClassificationPage,
        vExport,
        importDialog
    },
    props: {
        pageTypes: {
            type: Number,
            default: 0, // 0 默认页面  1 业务百科那边使用(有收藏功能)
        }
    },
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
        isShowFilter(isShow) {
            if (isShow) {
                return "有效";
            }
            return "无效";
        },
    },
    watch: {
        "listQuery.myFavorite"() {
            this.listQuery.PageIndex = 1
            this.getList();
        },
    },
    created() {
        // this.gettrainsClassification();
        // this.btnTextValue();
        this.getList();
    },
    data() {
        return {
            searchTypesData: [
                { label: "全部", value: 0 },
                { label: "我的收藏", value: 1 },
            ],


            defImgUrl: require('../../../assets/images/materialData_notData.png'),

            // 创建/编辑/详情
            dialogStatus: '',
            dialogFormVisible: false,
            selectId: '',

            // 批量导入
            dialogImportFormVisible: false,

            listLoading: false,
            layoutMode: 'simple',
            tabColumns: [
                {attr: {prop: "Idx",label: "序号", width: "80"},slot: true},
                {attr: {prop: "LogoPath",label: "实物图片"},slot: true},
                {attr: {prop: "LongCode",label: "物料长代码", showOverflowTooltip: true}},
                {attr: {prop: "Name",label: "物料名称", showOverflowTooltip: true}},
                {attr: {prop: "SpecificationsModel",label: "规格型号", showOverflowTooltip: true},slot: true},
                {attr: {prop: "Manufacturer",label: "制造商", showOverflowTooltip: true},slot: true},
                {attr: {prop: "BrandName",label: "品牌", showOverflowTooltip: true},slot: true},
            ],
            listQuery: {
                KeyWords: '',
                myFavorite: 0,
                Manufacturer: '',
                BrandName: '',
            },
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                { prop: "Manufacturer", label: "制造商" },
                { prop: "BrandName", label: "品牌" },
            ],
            tabDatas: [], //原始数据
            total: 0,

            
            dialogExportVisible: false,
            rData: null,
            cData: [],
        };
    },
    methods: {
        // 收藏/取消收藏 
        SetFavorite(row){
            let self = this,str = row.IsFavorite?'取消收藏':'添加收藏';

            self.$confirm(`确定要${str}吗?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                materialDataApi.SetFavorite({id: row.Id}).then(() => {
                    self.$notify({
                        title: "成功",
                        message: "操作成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getList();
                });
            });
        },
        // 批量导入
        handleImportSaveSuccess() {
            this.dialogImportFormVisible = false;
            this.getList();
        },
        onResetSearch() {
            this.listQuery.KeyWords = ''
            this.listQuery.Manufacturer = ''
            this.listQuery.BrandName = ''
            this.getList(); //刷新列表
        },

        //查看详情
        handleReview(row, optType = "detail") {
            this.selectId = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        //获取列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            this.listLoading = true;
            materialDataApi.getList(postData).then(res => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch(err => {
                this.listLoading = false;
            });
        },
        onBtnClicked(domId) {
            console.log(domId)
            switch (domId) {
                    //添加
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                    //批量导入
                case "btnImport":
                    this.dialogImportFormVisible = true;
                    break;
                    //批量导出
                case "btnExport":
                    this.handleExport();
                    break;
                default:
                    break;
            }
        },
        //弹出添加框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },

        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.selectId = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        // 多行删除
        handleTableDelete(rows) {
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }

            this.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                materialDataApi.del(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },
        handleExport() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            this.rData = {
                exportSource: 26,
                columns: [],
                searchCondition: postData,
            };
            this.cData = [
                // { label: "实物图片",value: "LogoPath",},
                { label: "物料长代码",value: "LongCode",},
                { label: "物料名称",value: "Name",},
                { label: "规格型号",value: "SpecificationsModel",},
                { label: "制造商",value: "Manufacturer",},
                { label: "品牌",value: "BrandName",},
            ];
            this.dialogExportVisible = true;
        },
        handleCloseExport() {
            this.dialogExportVisible = false;
        },
        back() {
            this.$router.go(-1)
        }
    }
};
</script>

<style lang="scss" scoped>
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}

</style>
