<template>
<div>
    <app-dialog
        :title="pageTitle"
        ref="appDialogRef"
        v-bind="$attrs"
        v-on="$listeners"
        :width="1000"
        :maxHeight='800'
    >
        <template slot="body">
            <el-row class="dialogWapper">
                <div class="product-list">
                    <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                    <div class="treeBox" v-loading='treeLoading'>
                        <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node, data }" :style="{paddingRight: data.Level == 1 ? '60px' : ''}">
                                <span class="node-title" :title="node.label">{{ node.label }}</span>
                            </span>
                        </el-tree>
                    </div>
                </div>
                <div class="content-wrapper">
                    <page-title :showBackBtn='false'>
                        <div slot="def">
                            <tags :items="searchTypes" v-model="listQuery.IsMine">
                                <template v-for="t in searchTypes" :slot="t.value">{{ t.label }}</template>
                            </tags>
                        </div>
                    </page-title>
                    <el-row class="searchBox">
                        <el-input style="width: 300px;" 
                            placeholder="搜索客户名称/职务/标签"
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQuery.Keywords"
                        ></el-input>
                    </el-row>
                    <el-table
                        fit
                        :data="tableData"
                        style="width: 100%"
                        v-loading="tableLoading"
                        max-height="560"
                        :header-cell-style="{'text-align':'left'}"
                        highlight-current-row row-key="Id"
                        @selection-change="handleSelectionChange"
                        ref="mainTable"
                    >
                        <el-table-column type="selection" width="55" :reserve-selection="true" :selectable="checkSelectable"></el-table-column>
                        <el-table-column type="index" label="序号" :index="indexMethod" width="55"></el-table-column>
                        <!-- <el-table-column width="55" v-if="!multiple">
                        <template slot-scope="scope">
                            <el-radio v-model="checkedRadioValue" @change='handleRadioChange' :label="scope.row[selectKeyName]"></el-radio>
                        </template>
                        </el-table-column> -->
                        <el-table-column label="客户名称" prop="CustomerName"></el-table-column>
                        <el-table-column label="客户单位" prop="CustomerUnitName"></el-table-column>
                        <el-table-column label="职务" prop="Position">
                            <template slot-scope="scope">
                                {{ scope.row.Position | valFilter }}
                            </template>
                        </el-table-column>
                        <el-table-column label="手机" prop="Telephone">
                            <template slot-scope="scope">
                                {{ scope.row.Telephone | valFilter }}
                            </template>
                        </el-table-column>
                        <el-table-column label="负责人" prop="SalesmanEmployee">
                            <template slot-scope="scope">
                                {{ scope.row.SalesmanEmployee | nameFilter }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </el-row>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>
</div>
</template>
<script>
import { listToTreeSelect } from "@/utils";
import * as customerUnit from "@/api/salesMgmt/customerUnit";
import * as customMgmt from "@/api/salesMgmt/customMgmt";
export default {
    name: "selector-customer",
    components: {
    },
    props: {
        // 是否多选
        multiple: { //create、update、detail
            type: Boolean,
            default: true
        },
        pageTitle: {
            type: String,
            default: '选择客户'
        },
        ids: {
            type: Array,
            default: () => {
                return []
            }
        },
        disableList:{// 禁止选择项
          type:Array,
          default:()=>[]
        }
    },
    computed: {
    },
    watch: {
        '$attrs.dialogFormVisible': {
            handler(val) {
                if(val) {
                    this.loadTreeData();
                    this.getList();
                }
            },
            immediate: true
        },
        "listQuery.IsMine"(val) {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.CustomerUnitId = val.Id;
                    this.getList();
                }
            },
        },
    },
    filters: {
        valFilter(val) {
            if (val) {
                return val;
            }
            return "无";
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "无";
        },
    },
    mounted() {
    },
    data() {
        return {
            searchTypes: [ // 顶部筛选条件
                { value: 1, label: "我负责的" },
                { value: 2, label: "全部客户" }
            ],
            disabledBtn: false,
            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            multipleSelection: [],
            tableData: [],
            tableLoading: false,
            listQuery: {
                Keywords: '',
                IsMine: 1,
                CustomerUnitId: null,
                PageIndex: 1,
                PageSize: 20
            },
            total: 0,
        }
    },
    methods: {
        indexMethod(idx) {
            return (this.listQuery.PageIndex - 1) * this.listQuery.PageSize + (idx + 1)
        },
        // 提交
        createData() {
            if(this.multipleSelection.length>0){
                this.$refs.appDialogRef.createData(this.multipleSelection);
            }else{
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
                return;
            }
        },
        // 取消
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let self = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1
            };
            self.treeLoading = true
            customerUnit.getListPage(paramData).then(res => {
                self.treeLoading = false
                let response = res.Items
                response.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });
                self.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构
            }).catch(err => {
                self.treeLoading = false
            });
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        //获取项目列表
        getList() {
            this.tableLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            // postData = this.assignSortObj(postData);
            customMgmt.getList(postData).then((res) => {
                this.tableLoading = false;
                this.tableData = res.Items;
                this.total = res.Total;
                this.setCheckedTable()
            });
        },
        // 列表有数据时  默认选中外层传过来的已选中id行
        setCheckedTable(){
            if(this.ids.length===0) return false
            this.ids.map(row => {
                if (this.tableData.length!=0&&this.multipleSelection.every(q=>q.Id !== row.Id)) {
                    this.$refs.mainTable.toggleRowSelection(row)
                }
            });
        },
        // 选中行事件
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 禁止选择项
        checkSelectable(row){
          const ids = this.disableList.map(t=>t.Id)
          return !ids.includes(row.Id)
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
    }
}
</script>
<style lang="scss" scoped>
.dialogWapper{
    display: flex;
    height: 700px;
    .product-list {
        width: 250px;
        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        .treeBox {
            flex: 1;
            overflow-y: auto;
            width: 100%;
            .elInput {
                width: 230px;
                margin-left: 10px;
            }
            .elTree {
                height: 100%;
                overflow: auto;
            }
        }
    }
    .content-wrapper {
        width: calc(100% - 250px);
        flex: 1;
        overflow-y: auto;
        .searchBox{
            padding: 10px;
        }
    }
}
</style>