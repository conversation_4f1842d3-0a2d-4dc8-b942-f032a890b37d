<template>
  <app-dialog title="详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='800' :width="1208">
    <template slot="body">
      <div class="wrapper22">
        
        <!-- <ul class="ulTagBox2 cl">
          <li
            @click="handleChangeChildren(ut.value)"
            class="fl"
            :class="{'active': ut.value == activeIndex}"
            v-for="ut in ulTag"
            :key="ut.value"
          >{{ut.label}}({{ut.amount}})</li>
        </ul> -->

        <!-- <div class="bt elCollBt" v-if="tabDatas.length>0">
        </div> -->
        <div class="el-card card" style="margin: 10px 0; padding: 10px;">
          故障现象：{{ keyworks }}
        </div>

        <div class="content22">
          <div class="lft el-card card">
            <div class="ipt-wrapper">
              <el-input style="width: 100%;" 
                  placeholder="搜索原因分析"
                  @clear='getAnsList'
                  v-antiShake='{
                      time: 300,
                      callback: () => {
                          listAnsQuery.PageIndex = 1
                          getAnsList()
                      }
                  }' 
                  clearable 
                  v-model="listAnsQuery.AnalysisContent"
              ></el-input>
            </div>
            <div class="list-wrapper">
              <no-data v-if="listDatas.length == 0 && !tagsListLoading" style="height:80px;"></no-data>
              <tags v-else v-loading='tagsListLoading' mode="list" class="tagsList" :items="listDatas" v-model="listQuery.FaultPhenomenonAnalysiId">
                <template v-for="(v, idx) in listDatas" :slot="v.value" slot-scope="scope">
                  <div :key="v.value" class="tag-item">
                    <div :title="v.label" class="tag-title" :key="idx">
                      {{ (listAnsQuery.PageIndex - 1) * listAnsQuery.PageSize + (idx + 1) }}、{{ v.label }}
                    </div>
                    <div>
                      ({{ v.Count }})
                    </div>
                  </div>
                </template>
              </tags>
            </div>
            <div>
              <pagination
                v-show="listTotal > 0"
                :total="listTotal"
                small
                background
                :page.sync="listAnsQuery.PageIndex"
                :size.sync="listAnsQuery.PageSize"
                @pagination="handleAnsCurrentChange"
                layout="prev, pager, next"
                :pager-count="5"
              />
            </div>
          </div>
          <div class="rht el-card card" v-loading='loading'>
            <div class="ipt-wrapper">
              <el-input style="width: 100%;" 
                  placeholder="搜索配件名称"
                  @clear='getList'
                  v-antiShake='{
                      time: 300,
                      callback: () => {
                          listQuery.PageIndex = 1
                          getList()
                      }
                  }' 
                  clearable 
                  v-model="listQuery.Keywords"
              ></el-input>
            </div>
            <div class="list-wrapper">
              <div v-if="tabDatas.length>0">
                <el-collapse v-for="(am,i2) in tabDatas" :key="i2">
                  <el-collapse-item name="1">
                    <template slot="title">
                      <div class="collHead2 cl">
                        <div class="fl cl">
                          <span class="omit fl" :title="am.StructPartName">{{am.StructPartName}}</span>
                          <span class="fl">({{am.Count}})</span>
                        </div>
                        <div class="fr">
                          <span>配件占比：{{am.Percent}}%</span>&emsp;
                          <div style="display: flex; align-items: center; padding-left: 10px;">
                            <el-progress :percentage="am.Percent" :color="am.Percent >= 50 ? '#FF9900' : '#5B9CFF'" :show-text="false"></el-progress>
                          </div>
                        </div>
                      </div>
                    </template>
                    <div class="lsMain2" v-if="am.SpecificationModelList && am.SpecificationModelList.length>0">
                      <div class="collHead2 cl bpt" v-for="(sm,i3) in am.SpecificationModelList" :key="i3">
                        <div class="fl cl">
                          <span
                            class="omit fl pl30"
                            style="font-weight:normal;"
                          :title="sm.SpecificationModel">{{sm.SpecificationModel}}</span>
                          <span class="fl">({{sm.Count}})</span>
                        </div>
                        <div class="fr">
                          <span>配件占比：{{sm.Percent}}%</span>&emsp;
                          <div style="display: flex; align-items: center; padding-left: 10px;">
                            <el-progress :percentage="sm.Percent" :color="sm.Percent >= 50 ? '#FF9900' : '#5B9CFF'" :show-text="false"></el-progress>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="lsMain2" v-if="!am.SpecificationModelList || am.SpecificationModelList.length<=0">
                      <no-data style="height:80px;"></no-data>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <no-data v-if="tabDatas.length<=0" class="bt" style="height:80px;"></no-data>
            </div>
            <div class="pagination-wrapper">
              <pagination
                v-if="total > 0"
                :total="total"
                :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize"
                @pagination="handleCurrentChange"
                @size-change="handleSizeChange"
              />
            </div>

          </div>
        </div>
        

      </div>
    </template>

    <template slot="footer">
      <!-- 取消 -->
      <app-button @click="handleClose" :buttonType="2"></app-button>
    </template>
  </app-dialog>
</template>

<script>
import * as maintenOrderMgmt from "@/api/maintenanceCenter/maintenOrderMgmt";
import noData from "@/views/common/components/noData";
import { vars } from '../common/vars'

export default {
  name: "",
  directives: {},
  components: {
    noData,
  },
  mixins: [],
  computed: {

  },
  props: {
    //编辑还是新增(create: 新增; update: 编辑; detail：详情)
    // dialogStatus: {
    //   required: true,
    //   type: String
    // },
    keyworks: {
      type: String,
      default: ""
    },
    //  = row.Phenomenon:
    year: {
      Number: String,
      required: true
    },
    regionalId: {
      Number: String,
      default: ''
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          // this.getSummary()
          // this.resetFormData();
          // if (this.dialogStatus != "create" && this.id) {
          //   this.getDetail();
          // }
          this.getAnsList()
        }
      },
      immediate: true
    },
    "listQuery.FaultPhenomenonAnalysiId": {
      handler(val) {
        if(val) {
          this.getList()
        }
      }
    }
    // activeIndex: {
    //   handler(val) {
    //     if(val) {
    //       //基于 activeIndex 计算，所以有主从关系
    //       this.getList()
    //     }
    //   }
    // }
  },
  created() {
  },
  data() {
    return {
      handling: vars.handling,
      loading: false,
      // activeIndex: 0,
      // ulTag: vars.handling.map(s => {
      //   s.amount = 0
      //   return s
      // }),
      // ulTag: [
      //   { label: "更换", value:4, amount:0, },
      //   { label: "维修", value:1, amount:0, },
      //   { label: "拆除", value:2, amount:0, },
      //   // {
      //   //   label: "被替换",
      //   //   value:3,
      //   // },
      //   { label: "新增", value:5, amount:0, },
      // ],

      tagsListLoading: false,
      currentAns: '',
      listDatas: [],
      listTotal: 0,
      listAnsQuery: {
        PageSize: 30,
        PageIndex: 1,
        AnalysisContent: '',
      },

      tabDatas: [],
      total: 0,
      listQuery: {
        PageSize: 20,
        PageIndex: 1,
        FaultPhenomenonAnalysiId: '',
        Keywords: '',
      }
    };
  },
  methods: {
    // getSummary() {
    //   let postData = Object.assign(this.listQuery)
    //   postData.year = this.year
    //   postData.phenomenon = this.keyworks
    //   postData.regionalId = this.regionalId

    //   maintenOrderMgmt.getFaultChartCount(postData).then(res => {
    //     //表示统计数据获取完成
    //     if(this.ulTag && this.ulTag.length > 0) {
    //       this.activeIndex = this.ulTag[0].value
    //     }
    //     this.ulTag.forEach(e => {
    //       let obj = res.find(s => s.ProcessMode == e.value)
    //       if(obj) {
    //         e.amount = obj.Count
    //       }
    //     })
    //   })
    // },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleAnsCurrentChange(val) {
      this.listAnsQuery.PageSize = val.size;
      this.getAnsList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    getAnsList() {
      let postData = JSON.parse(JSON.stringify(this.listAnsQuery))
      postData.Phenomenon = this.keyworks
      postData.regionalId = this.regionalId
      postData.year = this.year
      this.tagsListLoading = true
      maintenOrderMgmt.getFaultPhenomenonAnalysis(postData).then(res => {
        this.tagsListLoading = false
        this.listDatas = res.Items.map(s => {
          s.value = s.Id
          s.label = s.AnalysisContent
          return s
        }) || [];

        if(!this.listQuery.FaultPhenomenonAnalysiId && this.listDatas.length > 0) {
          this.listQuery.FaultPhenomenonAnalysiId = this.listDatas[0].value
        }

        this.listTotal = res.Total
      }).catch(err => {
        this.tagsListLoading = false
      })
    },
    getList() {
      let postData = JSON.parse(JSON.stringify(this.listQuery))
      postData.year = this.year
      postData.phenomenon = this.keyworks
      // postData.ProcessMode = this.activeIndex
      postData.regionalId = this.regionalId

      this.loading = true
      maintenOrderMgmt.getFaultChart(postData).then(res => {
        this.loading=false;
        // let obj = this.ulTag.find(e => e.value == this.activeIndex)
        // res.Items.forEach(v1 => {
        //   if(obj && obj.amount > 0) {
        //     v1.percent=v1.Count / obj.amount;
        //   }
        //   v1.percent=Math.round(v1.percent*10000)/100;
        //   if(v1.SpecificationModelList && v1.SpecificationModelList.length > 0) {
        //     v1.SpecificationModelList.forEach(v2 => {
        //       v2.percent=v2.Count/v1.Count;
        //       v2.percent=Math.round(v2.percent*10000)/100;
        //     })
        //   }
        // })
        this.tabDatas=res.Items || [];
        this.total=res.Total
      }).catch(err => {
        this.loading=false;
      })
    },
    // handleChangeChildren(val){
    //   this.activeIndex=val;
    // },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang='scss' scoped>
.wrapper22{
  display: flex;
  flex-direction: column;
  min-height: 500px;
  margin-bottom: 10px;
}

// .elCollBt{
//   >div:not(:first-child){
//     border-top:1px solid #EBEEF5;
//   }
// }

// .elCollBt{
//   flex: 1;
// }

.ulTagBox2 {
  padding: 6px 0;
  border-top: 1px solid #dcdfe6;
  li {
    padding: 2px 4px;
    cursor: pointer;
    border-radius: 5px;
    margin: 2px;
    margin-right: 10px;
    margin-left: 0;
  }
  li:hover {
    background: #f5f7fa;
  }
  li.active {
    background: #ecf5ff;
  }
}

.content22{
  flex: 1;
  display: flex;
  max-height: 730px;
  .lft{
    width: 260px;
    margin-right: 10px;
    
  }
  .rht{
    flex: 1;
  }
  .lft, .rht{
    display: flex;
    flex-direction: column;
    .list-wrapper{
      flex: 1;
      overflow-y: auto;
      .tag-item{
        display: flex;
        .tag-title{
          flex: 1;
          white-space: nowrap; 
          word-break: break-all; 
          overflow: hidden; 
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.bpt {
  padding-left: 0;
  padding-right: 10px;
}

.collHead2 {
  width: 100%;
  padding: 6px 0;
  > div {
    width: 50%;
  }
  > div:nth-child(1) {
    > span:nth-child(1) {
      max-width: 70%;
      font-weight: 700;
    }
  }
  > div:nth-child(2) {
    display: flex;
    > span {
      width: 110px;
      text-align: right;
    }
    > div {
      width: calc(100% - 130px);
      > div {
        // margin-top: 8px;
        width: 100%;
      }
    }
  }
}
.pl30 {
  padding-left: 30px;
}
.bt {
  border-top: 1px solid #dcdfe6;
}

.ipt-wrapper{
  box-sizing: border-box;
  padding: 10px;
}
// .card{
//   border: 1px solid #EBEEF5;
//   background-color: #FFF;
//   color: #303133;
//   transition: .3s;
//   box-shadow: 0 2px 12px 0 rgb(0,0,0);
//   margin: 10px;
//   padding: 10px;
// }

</style>