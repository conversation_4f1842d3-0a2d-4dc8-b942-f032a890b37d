<template>
    <div>
        <app-dialog
            :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :maxHeight='820'
            :width='1200'
        >
            <template slot="body">
                <el-form style="height: 700px;" :rules="rules" ref="formData" :model="formData" label-position="right" label-width="100px" v-loading='loading'>
                    <div class="wrapper">
                        <div class="desc-panel">
                            <el-row>
                                <el-col :span="24">
                                    <div class="row-desc">
                                        <!-- 访问类型：{{ formData.Type | playTypeFilter }} -->
                                        计划名称：{{ formData.PlanName }}
                                    </div>
                                </el-col>
                                <el-col :span="24">
                                    <div class="row-desc">
                                        客服人员：<span v-if="formData.EmployeeList && formData.EmployeeList.length > 0">{{ formData.EmployeeList.map(s => s.Name).join(',') }}</span><span v-else>无</span>
                                    </div>
                                </el-col>
                                <el-col :span="8">
                                    <div class="row-desc">
                                        开始时间：{{ formData.StartTime | dateFilter('YYYY-MM-DD HH:mm') }}
                                    </div>
                                </el-col>
                                <el-col :span="8">
                                    <div class="row-desc">
                                        截止时间：{{ formData.EndTime | dateFilter('YYYY-MM-DD HH:mm') }}
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                        
                        <div class="middle-panel">
                            <div class="list-wrapper2">
                                <div style="margin-bottom: 10px; font-weight: 700;">回访地区列表</div>
                                <tags mode='list' :items='formData.ReturnVisitsDetailList' v-model="selectGroup">
                                    <template v-for="(t, idx) in formData.ReturnVisitsDetailList" :slot="t.value">
                                        <div :key="t.value + '1'" style="display: flex;">
                                            <div :key="t.value + '2'" style="flex: 1; width: 0;" class="omit" :title="t.data.RegionalName">{{ t.data.RegionalName }}</div>
                                            <div :key="t.value + '3'" class="btn-wrapper">
                                                <span
                                                    class="item-status"
                                                    :class="`status-${t.Situation}`"
                                                >
                                                    {{ t.Situation ? '已回访' : '未回访' }}
                                                </span>
                                            </div>
                                        </div>
                                    </template>
                                </tags>
                            </div>
                            <div style="flex: 1;">
                                <!-- <div>报修设备信息</div> -->
                                <tags style="padding: 10px; padding-top: 0;" :items='types' v-model="tagType">
                                    <template v-for="t in types" :slot="t.value">
                                        {{ t.label }}<span :key="t.value" v-if="t.value == 2">（{{ total }}）</span>
                                    </template>
                                </tags>
                                
                                <template v-if="tagType == 1">
                                    <div v-for="(item, idx) in formData.ReturnVisitsDetailList" :key="idx">
                                        <div v-show="item.Code == selectGroup">
                                            <el-row>
                                                <el-col :span="24">
                                                    <el-form-item label="回访情况" prop="Situation">
                                                        <el-radio :disabled="!editable" v-model="item.Situation" :label="false">未回访</el-radio>
                                                        <el-radio :disabled="!editable" v-model="item.Situation" :label="true">已回访</el-radio>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-form-item label="回访客户" :prop="'ReturnVisitsDetailList.' + idx + '.VisitCustomerName'" :rules="rules.VisitCustomerName">
                                                        <el-input :disabled="!editable" maxlength="20" type="text" v-model="item.VisitCustomerName"></el-input>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-form-item label="联系电话" :prop="'ReturnVisitsDetailList.' + idx + '.VisitCustomerPhone'" :rules="rules.VisitCustomerPhone">
                                                        <el-input :disabled="!editable" maxlength="20" type="text" v-model="item.VisitCustomerPhone"></el-input>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="24">
                                                    <el-form-item label="回访时间" :prop="'ReturnVisitsDetailList.' + idx + '.Time'" :rules="rules.Time">
                                                        <el-date-picker :disabled="!editable" style="width: 100%;" format='yyyy-MM-dd HH:mm' value-format='yyyy-MM-dd HH:mm' v-model="item.Time" type="datetime" placeholder=""></el-date-picker>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="24">
                                                    <el-form-item label="故障维修" :prop="'ReturnVisitsDetailList.' + idx + '.Record'" :rules="rules.Record">
                                                        <el-input :disabled="!editable" maxlength="3000" type="textarea" :rows="3" v-model="item.Record"></el-input>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="24">
                                                    <el-form-item label="产品建议" :prop="'ReturnVisitsDetailList.' + idx + '.ProductSuggestion'">
                                                        <el-input :disabled="!editable" maxlength="3000" type="textarea" :rows="3" v-model="item.ProductSuggestion"></el-input>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-form-item label="服务及时性" :prop="'ReturnVisitsDetailList.' + idx + '.TimelinessService'" :rules="rules.TimelinessService">
                                                        <div style="padding-top: 4px;">
                                                            <el-rate :disabled="!editable" v-model="item.TimelinessService" @change='(score) => handleChange(score, `"ReturnVisitsDetailList.${idx}.TimelinessService"`)'></el-rate>
                                                        </div>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-form-item label="服务态度" :prop="'ReturnVisitsDetailList.' + idx + '.ServiceAttitude'" :rules="rules.ServiceAttitude">
                                                        <div style="padding-top: 4px;">
                                                            <el-rate :disabled="!editable" v-model="item.ServiceAttitude" @change='(score) => handleChange(score, `"ReturnVisitsDetailList.${idx}.ServiceAttitude"`)'></el-rate>
                                                        </div>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="24">
                                                    <el-form-item label="备注" prop="Remarks">
                                                        <el-input :disabled="!editable" maxlength="3000" type="textarea" :rows="3" v-model="item.Remarks"></el-input>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="tagType == 2">
                                    <div style="width: 98%;">
                                        <app-table-core
                                            ref="mainTable"
                                            :tab-columns="tabColumns"
                                            :tab-datas="tabDatas"
                                            :tab-auth-columns="[]"
                                            :isShowAllColumn="false"
                                            :loading="listLoading"
                                            :isShowOpatColumn="true"
                                            :startOfTable="startOfTable"
                                            :multable="false"
                                            @sortChagned='handleSortChange'
                                            :height='380'
                                            fit
                                        >
                                            <template slot="ServiceNo" slot-scope="scope">{{ scope.row.ServiceNo ? scope.row.ServiceNo : '无' }}</template>
                                            <template slot="ReportTime" slot-scope="scope">{{
                                                scope.row.ReportTime | dateFilter("YYYY-MM-DD HH:mm")
                                            }}</template>
                                            <template slot="HandlerEmployeeList" slot-scope="scope">
                                                <span v-if="scope.row.HandlerEmployeeList">{{
                                                    scope.row.HandlerEmployeeList.map(s => s.Name).join(",")
                                                }}</span>
                                                <span v-else>无</span>
                                            </template>
                                            <template slot="IsReplacePart" slot-scope="scope">{{ scope.row.IsReplacePart ? '是' : '否' }}</template>

                                            <!-- 表格行操作区域 -->
                                            <template slot-scope="scope">
                                                <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
                                            </template>
                                        </app-table-core>

                                        <pagination
                                            layout="total, prev, pager, next, jumper"
                                            :total="total"
                                            :page.sync="listQuery.PageIndex"
                                            :size.sync="listQuery.PageSize"
                                            @pagination="handleCurrentChange"
                                            @size-change="handleSizeChange"
                                        />
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div v-if="formData.Approval">
                        <!-- <div class="panel-title">审批</div> -->
                        <div>
                            <approval-panel v-if="dialogStatus == 'track'" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                            <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                        </div>
                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button style="margin-right: 2px;" @click="handleClose" :buttonType='2'></app-button>
                <!-- 保存 -->
                <app-button style="margin-right: 2px;" v-if="!isHandle && dialogStatus != 'approval'" @click="createData(1)" v-show="editable" :disabled='disabledBtn' text='保存'></app-button>&nbsp;
                <!-- 确认 -->
                <app-button v-if="dialogStatus != 'approval'" @click="createData(2)" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>

                <!-- 审批模式下才显示审批 -->
                <el-button @click="handleApproval" type="primary" :disabled='disabledBtn' v-show="(dialogStatus == 'approval' && !isOnlyViewDetail) && isApprovalor">审批</el-button>
            </template>
        </app-dialog>

        <create-page
            v-if="dialogDetailFormVisible"
            @closeDialog="closeDialog"
            @saveSuccess="() => {}"
            :dialogFormVisible="dialogDetailFormVisible"
            :dialogStatus="dialogDetailStatus"
            :id="orderId"
            :declareNewCases="true"
        ></create-page>
    </div>
</template>

<script>
import tabs from '../../../projectDev/projectMgmt/common/tabs'
// import empSelector from '../../../common/empSelector'
import approvalPanel from '../../../projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '../../../projectDev/projectMgmt/workbench/common/approvalDetail'
import * as afterService from "@/api/afterSalesMgmt/afterService"
import { getUserInfo } from "@/utils/auth"
import { vars } from "../common/vars"
import * as projVars from "../../../projectDev/common/vars"
import approvalMixins from '../../../../mixins/approvalPatch'
import indexPageMixin from "@/mixins/indexPage"
import * as mo from "@/api/maintenanceCenter/maintenOrderMgmt"
import createPage from '../../../afterSalesMgmt/maintenCenter/maintenOrderMgmt/create'

  export default {
    name: "after-service-track",
    directives: {
    },
    components: {
        tabs,
        // empSelector,
        approvalPanel,
        approvalDetail,
        createPage,
    },
    mixins: [indexPageMixin, approvalMixins],
    props: {
        specialPageTitle: {
            type: String
        },
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
    },
    filters: {
        // playTypeFilter(type) {
        //     let obj = vars.afterService.planTypes.find(s => s.value == type)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return type
        // }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail" && this.dialogStatus != 'approval'
        },
        isHandle() {
            return this.dialogStatus != 'track'
        },
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            if(this.dialogStatus == 'track') {
                return '跟进回访计划'
            }else if(this.dialogStatus == 'detail') {
                return '回访计划详情'
            }
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(val) {
                    this.disabledBtn = false
                    this.resetFormData()
                    if(this.dialogStatus != 'create' && this.id) {
                        this.getDetail()
                    }
                }
            },
            immediate: true
        },
        selectGroup: {
            handler(val) {
                if(val) {
                    this.getList()
                }
            },
            immediate: true
        },
        tagType: {
            handler(val) {
                if(val && this.selectGroup) {
                    this.getList()
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules)

        const validateTimelinessService = (rule, value, callback) => {
            if(!value || value == 0) {
                callback(new Error(`请打分`));
            }else{
                callback();
            }
        };
        if (!this.rules["TimelinessService"])
            this.rules["TimelinessService"] = [];
        this.rules.TimelinessService.push({
            validator: validateTimelinessService,
            trigger: "change"
        });

        const validateServiceAttitude = (rule, value, callback) => {
            if(!value || value == 0) {
                callback(new Error(`请打分`));
            }else{
                callback();
            }
        };
        if (!this.rules["ServiceAttitude"])
            this.rules["ServiceAttitude"] = [];
        this.rules.ServiceAttitude.push({
            validator: validateServiceAttitude,
            trigger: "change"
        });
    },
    data() {
        return {
            tabColumns: [
                {
                    attr: { prop: "ServiceNo", label: "服务单号" ,width:'95'},
                    slot:true
                },
                {
                    attr: { prop: "ReportTime", label: "报修时间", sortable: 'custom' ,width:'125'},
                    slot: true
                },
                {
                    attr: { prop: "TotalPrice", label: "总收费（元）" }
                },
                {
                    attr: { prop: "HandlerEmployeeList", label: "实施人员" },
                    slot: true
                },
                {
                    attr: { prop: "IsReplacePart", label: "更换配件",width:'100'},
                    slot: true
                },
            ],
            tabDatas: [],
            listLoading: false,
            total: 0,

            orderId: '',
            dialogDetailStatus: 'detail',
            dialogDetailFormVisible: false,
            
            tagType: 1,
            types: [
                {value: 1, label: '回访单信息'},
                {value: 2, label: '相关报修单'},
            ],
            selectGroup: '',
            oldDistrictId: null,
            isTip: true,
            loading: false,
            disabledBtn: false,
            normalizer(node) {
                return {
                    id: node.Id,
                    label: node.RegionalName,
                    children: node.children
                }
            },
            rules: {
                Time: { fieldName: "回访时间", rules: [{ required: true }] },
                Record: { fieldName: "故障维修", rules: [{ required: true }] },
                VisitCustomerName: { fieldName: "回访客户", rules: [{ required: true }] },
                VisitCustomerPhone: { fieldName: "联系电话", rules: [{ required: true }] },
                TimelinessService: { fieldName: "服务及时性", rules: [{ required: true }] },
                ServiceAttitude: { fieldName: "服务态度", rules: [{ required: true }] },
                
            },
            formData: {
                Id: "", //需求ID
                PlanName: '',//计划名称
                EmployeeList: [], //客服人员
                StartTime: null, //开始时间
                EndTime: null,//截至时间
                Type: 1, //回访类型
                ReturnVisitsDetailList: [],

                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },


            },
            selectGroup: '',

            //创建订单
            dialogOrderCreateFormVisible: false, 
            dialogOrderCreateStatus: 'detail',

            
        };
    },
    methods: {
        handleSortChange({ column, prop, order }) {
            this.sortObj = {prop, order}
            this.getList()
        },
        //获取项目列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            let obj = this.formData.ReturnVisitsDetailList.find(s => s.value == this.selectGroup)
            if(!obj) {
                return false
            }
            
            postData.RegionalId = obj.data.RegionalId

            postData = this.assignSortObj(postData)
            this.listLoading = true;
            mo.getList(postData).then(res => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            }).catch(err => {
                this.listLoading = false;
            });
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleUpdate(row, optType = "update") {
            // // 弹出编辑框
            // if(optType == 'detail'){
            //     this.declareNewCases=true;
            // }else{
            //     this.declareNewCases=false;
            // }
            this.orderId = row.Id;
            this.dialogDetailStatus = optType;
            this.dialogDetailFormVisible = true;
        },
        closeDialog() {
            this.dialogDetailFormVisible = false;
        },

        handleChange(score, validateKey) {
            this.$refs["formData"].validate();
        },
        resetFormData() {
            let temp = {
                Id: "", //需求ID
                PlanName: '',//计划名称
                EmployeeList: [], //客服人员
                StartTime: null, //开始时间
                EndTime: null,//截至时间
                Type: 1, //回访类型
                ReturnVisitsDetailList: [
                    // {
                    //     PlanId: '',
                    //     Time: null,
                    //     Situation: false,
                    //     Record: '',
                    //     Remarks: '',
                    // }
                ],

                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },

                
            }
            this.formData = Object.assign({}, this.formData, temp)
        },
        //报修单处理审批
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = projVars.vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
        
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        //项目创建审批
                        this.disabledBtn = true
                        afterService.createApproval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })
                }
            })
            
        },
        //根据验证错误，定位到对应的设备列表中的某一项设备
        trackErrors() {
            this.$refs.formData.validate((res, errors) => {
                //当”设备列表“下有多条记录时，需要根据错误提示切换到不同的”设备两目“下，方便用户看到错误提示信息
                //当所有非”设备列表“下的验证都通过后，才执行逻辑
                let errFields = JSON.parse(JSON.stringify(errors))
                let errorKeys = Object.keys(errFields)

                let isToggle = errorKeys.length > 0 && errorKeys.every(s => s !== 'ReturnVisitsDetailList' && s.indexOf('ReturnVisitsDetailList') > -1)
                if(isToggle) {
                    //存在错误的”设备列表“索引
                    let errorKeysIdx = errorKeys.map(s => {
                        s = s.substring(s.indexOf('.') + 1).substring(0, s.indexOf('.'))
                        return parseInt(s)
                    })
                    errorKeysIdx = Array.from(new Set(errorKeysIdx)) //去重
                    //当前选中的设备索引
                    let idx = this.formData.ReturnVisitsDetailList.findIndex(s => s.Code == this.selectGroup)
                    //如果当前选中的设备中不存在校验失败的字段，那么就需要切换到第一组存在错误的设备条目中
                    if(errorKeysIdx.findIndex(s => s === idx) == -1) {
                        if(errorKeysIdx.length > 0) {
                            this.selectGroup = this.formData.ReturnVisitsDetailList[errorKeysIdx[0]].Code
                        }
                    }
                }

            })
        },
        /**
         * optType: 1: 保存（不校验数据）；2：提交：
         */
        createData(optType) {
            let flagVAlid = new Promise((resolve, reject) => {
                return resolve(true)
            })
            let validList = [flagVAlid]

            let validate = this.$refs.formData.validate()

            // if(this.dialogStatus == 'track') {
            //     validList.push(validate)
            // }
            if(optType == 2 && this.dialogStatus == 'track') {
                
                validList.push(validate)

                //如果是”处理“报修单，需要验证”审批“区块信息
                if(this.dialogStatus == 'track') {
                    let approvalPanelValidate = this.$refs.approvalPanel.validate()
                    validList.push(approvalPanelValidate)
                }
            }

            Promise.all(validList).then(valid => {
                let postData = {
                    Id: this.id,
                    List: this.formData.ReturnVisitsDetailList.map(s => {
                        return {
                            Time: s.Time,
                            PlanId: s.PlanId,
                            ReturnVisitsId: s.ReturnVisitsId,
                            Situation: s.Situation,
                            Record: s.Record,
                            ProductSuggestion: s.ProductSuggestion,
                            Remarks: s.Remarks,
                            TimelinessService: s.TimelinessService,
                            ServiceAttitude: s.ServiceAttitude,
                            VisitCustomerName: s.VisitCustomerName,
                            VisitCustomerPhone: s.VisitCustomerPhone,
                        }
                    })
                }

                if(!this.isHandle) {
                    postData.Approval = this.$refs.approvalPanel.getData() //审批层区块
                    postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                    postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                }

                this.disabledBtn = true
                let result = null
                if(optType == 2 && postData.List.some(s => !s.Situation)) {
                    this.$confirm(`检测到有未完成的回访订单，是否继续提交?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        afterService.follow(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    }).catch(err => {
                        this.disabledBtn = false
                    })
                }else{
                    if(optType == 2) { //提交
                        result = afterService.follow(postData)
                    }else{ //保存
                        result = afterService.save(postData)
                    }
                    result.then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData()
                    }).catch(err => {
                        this.disabledBtn = false
                    })
                }

            }).catch(err => {
                this.trackErrors()
            })

        },
        getDetail() {
            this.loading = true
            afterService.detail({ id: this.id }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);
                this.formData.ReturnVisitsDetailList = this.formData.ReturnVisitsDetailList.map(s => {
                    let result = Object.assign({}, s, {
                        Id: s.PlanId,
                        Code: s.Number,
                        label: s.Number,
                        value: s.Number,
                        PlanId: s.PlanId,
                        Situation: (s.Situation === true || s.Situation === false) ? s.Situation : false,
                        data: s
                    })
                    return result
                })
                
                if(this.dialogStatus == 'track' && !this.formData.Approval) {
                    this.formData.Approval = {//审批信息
                        ApprovalEmployeeList: [[]],
                        ApprovalType: 1,
                        ApprovalOperatorEmployeeList: [], //已审批人员
                        NoApprovalEmployeeList: [], //未审批人员
                        CCEmployeeList: [], //抄送人
                        ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                        ApprovalState: 1, //1: 进行中; 2: 已完成
                        ApprovalResult: 1, //1: 通过； 2：不通过
                    }
                }
                if(this.formData.ReturnVisitsDetailList && this.formData.ReturnVisitsDetailList.length > 0) {
                    this.selectGroup = this.formData.ReturnVisitsDetailList[0].Code
                }
            }).catch(err => {
                this.loading = false
            });
        },
        /**
         * 订单创建
         */
        handleOrderCreateDialog() {
            this.dialogOrderCreateFormVisible = true
        },
        closeOrderCreateDialog() {
            this.dialogOrderCreateFormVisible = false
        },
        handleOrderCreateSaveSuccess() {
            // this.getList()
            this.closeOrderCreateDialog()
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        equsInit(datas) {

            if(datas && datas.length > 0) {
                return datas.map(s => {
                    s.label = s.Name
                    s.value = s.Code
                    return s
                })
            }
            return []
        },
        handleAddReplace(item) {
            item.ReplaceAccessoryList.push({
                MaintenanceEquipmentId: '',//维修设备Id
                Name: '',//配件名称
                SpecificationModel: '',//规格型号
                UsageAmount: 0, //使用数量
                UnitPrice: 0,//单价
                Remarks: '', //备注
            })
        },
        handleRemoveReplace(item, idx) {
            item.ReplaceAccessoryList.splice(idx, 1)
        },
        handleAddFault(item) {
            item.FaultPhenomenonList.push({
                MaintenanceEquipmentId: '',//维修设备Id
                Phenomenon: '',//故障现象
                CauseAnalysis: '',//原因分析
                Solution: '',//解决方法
            })
        },
        handleRemoveFault(item, idx) {
            item.FaultPhenomenonList.splice(idx, 1)
        },
        handleFilesUpChange(item, files) {
            item.AttachmentList = files
        },
    }
};
</script>


<style lang="scss" scoped>

.middle-panel{
    display: flex; 
    min-height: 200px;
    border-top: 1px solid #DCDFE6;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
    .list-wrapper2{
        width: 300px;
        border-right: 1px solid #DCDFE6;
    }
    >div{
        padding: 10px 0;
    }

}



.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}

.row-desc{
    margin-bottom: 2px;
}

.desc-panel{
    padding-bottom: 10px;
}


.status-false {
  background-color: red;
}

.status-true {
  background-color: #409EFF;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
