<template>
  <div>
    <app-dialog title="选择供应商" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1000'>
      <template slot="body">
        <main class="cl" v-loading="loading">
          <aside class="fl">
            <tags mode="list" v-if="taskList.length>0" :items="taskList" v-model="taskId" @change="handleTagsChange" style="margin-top: 10px;">
              <template v-for="(task,idx) in taskList" :slot="task.value">
                <div class="item_warpper" :key="idx">
                  <div>
                    <span class="omit" :title="task.Name">{{task.Name}}</span>
                  </div>
                </div>
              </template>
            </tags>
            <no-data v-else></no-data>
          </aside>
          <section class="fr">
            <app-table-core ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="0" :multable='false' :serial='false'>
              <template slot="Choice" slot-scope="scope">
                <el-radio class="elRadio" v-model="radio" @change="handleChange(scope.row)" :label="scope.row.Id"></el-radio>
              </template>
              <template slot="Settlement" slot-scope="scope">
                <span>{{ scope.row.Settlement | SettlementFilter}}</span>
              </template>
            </app-table-core>
          </section>
        </main>
        <!-- <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          :label-width="'100px'"
        >
            <el-row>
                <el-col :span="24">
                    <el-form-item label="问题名称">
                        <span v-if="row">{{ row.Name }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="处理人" prop="TargetEmployee">
                        <emp-selector :multiple='false' :showType='2' :list='formData.TargetEmployee' key='service-users' @change='handleChangeUsers'></emp-selector>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form> -->
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as supplierManagement from "@/api/afterSalesMgmt/supplierManagement";
import NoData from "@/views/common/components/noData";
import {
  SettlementEnum,
} from "../enum";
export default {
  name: "ques-assign",
  components: {
    NoData

  },
  mixins: [],
  props: {
    id: {
      type: String,
      default: ''
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.saveData = null;
        // this.radio=this.id;
        this.radio = '';
        this.getTypeList();
      }
    },
  },
  filters: {
    SettlementFilter(status) {

      const statusObj = SettlementEnum.find((s) => s.value == status);
      if (statusObj) {
        return statusObj.label;
      }
      return "";
    },

  },
  created() {

  },
  data() {
    return {
      saveData: null,
      radio: '',
      disabledBtn: false,
      loading: false,
      taskList: [],
      tabDatas: [],
      taskId: '',
      listQuery: {
        // 查询条件
        Name: "",
        PageIndex: 1,
        PageSize: 9999,
        Code: "", //SuppliersTypeId:null,
        Contacts: "",
        SuppliersTypeId: '',
        // Settlement: 0,
        // Phone: "",
      },
      tabColumns: [{
        attr: {
          prop: "Choice",
          label: "选择",
          width: '80'
        },
        slot: true,
      }, {
        attr: {
          prop: "Code",
          label: "供应商编号",
        }
      },

      {
        attr: {
          prop: "Name",
          label: "供应商名称",
          showOverflowTooltip: true
        }
      },
      {
        attr: {
          prop: "Contacts",
          label: "联系人1",
        }
      },
      {
        attr: {
          prop: "Phone",
          label: "联系人电话1",
        }
      },
      {
        attr: {
          prop: "Email",
          label: "供应商邮箱",
          showOverflowTooltip: true
        }
      },
      {
        attr: {
          prop: "Settlement",
          label: "结算方式",
        },
        slot: true,
      }
      ],
    };
  },
  methods: {
    handleChange(d) {
      console.log(1, d)
      this.saveData = d;
    },
    getTypeList() {
      this.loading = true;
      let postData = {
        "pageIndex": 1,
        "pageSize": 9999,

      }
      supplierManagement.getListType(postData).then(res => {
        console.log(1, res)
        this.taskList = res.Items;
        this.loading = false;
        if (this.taskList.length > 0) {
          this.cid = this.taskList[0].Id;
          this.taskId = this.taskList[0].Id;
          this.listQuery.SuppliersTypeId = this.taskList[0].Id;
          this.getList();
        }

        this.taskList.forEach(v => {
          v.value = v.Id;
        })
      }).catch(err => {
        this.loading = false;
      })
    },
    getList() {
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      this.loading = true;
      supplierManagement.getList(postData).then(res => {
        console.log(2, res)
        this.loading = false;
        this.tabDatas = res.Items;
      }).catch(err => {
        this.loading = false;
      });
    },
    handleTagsChange(d) {
      this.listQuery.SuppliersTypeId = d;
      this.getList();
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
    createData() {
      if (this.saveData) {
        console.log(666, this.saveData)
        this.$emit('saveSuccess', this.saveData);
      } else {
        this.handleClose();
      }

    }
  }
};
</script>
<style scoped>
.elRadio >>> .el-radio__label {
  display: none;
}
</style>
<style lang="scss" scoped>
aside {
  height: 590px;
  overflow-y: atuo;
  width: 250px;
  border-right: 1px solid #ebeef5;
}
section {
  height: 590px;
  overflow-y: atuo;
  width: calc(100% - 250px);
}
.omit {
  display: inline-block;
  width: 96%;
}
</style>
