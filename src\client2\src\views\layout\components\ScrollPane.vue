<template>
<div class="scroll-wrapper">
  <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.native.prevent="handleScroll">
    <slot />
  </el-scrollbar>
  <div class="move-btn move-left" @mousedown="moveLeft" @mouseup="clearTimer">
    <i class="el-icon-arrow-left"></i>
  </div>
  <div class="move-btn move-right" @mousedown="moveRight" @mouseup="clearTimer">
    <i class="el-icon-arrow-right"></i>
  </div>
</div>
</template>

<script>
const tagAndTagSpacing = 4 // tagAndTagSpacing

export default {
  name: 'ScrollPane222',
  data() {
    return {
      left: 0,
      timer: null
    }
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap
    }
  },
  methods: {
    clearTimer() {
      clearInterval(this.timer)
    },
    moveLeft() {
      this.clearTimer()
      this.timer = setInterval(() => {
        this.scrollWrapper.scrollLeft -= 10
      }, 10)
      
    },
    moveRight() {
      this.clearTimer()
      this.timer = setInterval(() => {
        this.scrollWrapper.scrollLeft += 10
      }, 10)
    },
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const $scrollWrapper = this.scrollWrapper
      

      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4
    },
    moveToTarget(currentTag) {
      const $container = this.$refs.scrollContainer.$el
      const $containerWidth = $container.offsetWidth
      const $scrollWrapper = this.scrollWrapper
      const tagList = this.$parent.$refs.tag

      let firstTag = null
      let lastTag = null

      // find first tag and last tag
      if (tagList.length > 0) {
        firstTag = tagList[0]
        lastTag = tagList[tagList.length - 1]
      }

      if (firstTag === currentTag) {
        $scrollWrapper.scrollLeft = 0
      } else if (lastTag === currentTag) {
        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth
      } else {
        // find preTag and nextTag


        const currentIndex = tagList.findIndex(item => item === currentTag)


        const prevTag = tagList[currentIndex - 1]
        const nextTag = tagList[currentIndex + 1]

        // the tag's offsetLeft after of nextTag
        const afterNextTagOffsetLeft = nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing

        // the tag's offsetLeft before of prevTag
        const beforePrevTagOffsetLeft = prevTag.$el.offsetLeft - tagAndTagSpacing
        if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {
          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth
        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {
          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft
        }
      }
    }
  },
  destroyed() {
    this.clearTimer()
  },
}
</script>

<style lang="scss" scoped>
.scroll-container {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  width: 100%;
  ::v-deep {
    .el-scrollbar__bar {
      bottom: 0px;
    }
    .el-scrollbar__wrap {
      height: 49px;
    }
  }
}

.scroll-wrapper{
  position: relative;
  padding: 0 20px;
  box-sizing: border-box;
  .move-btn{
    position: absolute;
    box-sizing: border-box;
    background: #d8dce5;
    top: 4px;
    width: 20px;
    height: 26px;
    cursor: pointer;
    line-height: 26px;
    opacity: .4;
    transition: .3s;
    text-align: center;
    &:hover{
      background: #d2d3d6;
    }
  }


  .move-left{
    left: 0;
  }

  .move-right{
    right: 0;
  }
}

// .scroll-container:hover {
  
// }
</style>
