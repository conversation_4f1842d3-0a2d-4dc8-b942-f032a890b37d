/**
 * Created by ji<PERSON><PERSON><PERSON> on 16/11/18.
 */
import { CodeToText } from "element-china-area-data";
import Compressor from 'compressorjs';


export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (("" + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    if (key === "a") return ["一", "二", "三", "四", "五", "六", "日"][value - 1];
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

export function formatTime(format) {
  const now = new Date();
  var o = {
    "M+": now.getMonth() + 1, // month
    "d+": now.getDate(), // day
    "h+": now.getHours(), // hour
    "m+": now.getMinutes(), // minute
    "s+": now.getSeconds(), // second
    "q+": Math.floor((now.getMonth() + 3) / 3), // quarter
    S: now.getMilliseconds(), // millisecond
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (now.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return format;
}

// 将list转成tree，使用前注意把array进行深拷贝
export function listToTreeSelect(array, parent, tree, model, keyOfPath) {
  let defaultKeys = {
    key: "Id",
    parentKey: "ParentId",
  };

  if (model) {
    defaultKeys = Object.assign({}, defaultKeys, model);
  }

  let rootNode = {};
  rootNode[defaultKeys.key] = null;
  rootNode["level"] = 0;
  if (keyOfPath) {
    rootNode["ParentName"] = "";
  }

  tree = typeof tree !== "undefined" ? tree : [];
  parent = typeof parent !== "undefined" ? parent : rootNode;

  var children = array.filter((val, index) => {
    return val[defaultKeys.parentKey] === parent[defaultKeys.key];
  });

  if (children.length > 0) {
    children.forEach(item => {
      item["level"] = parent["level"] + 1;
      if (keyOfPath) {
        let parentNameTemp = parent["ParentName"];
        item["ParentName"] = parentNameTemp + (parentNameTemp ? " / " : "") + item[keyOfPath];
      }
    });
    if (parent[defaultKeys.key] === null) {
      tree = children;
    } else {
      parent["children"] = children;
    }

    children.forEach((val, index) => {
      listToTreeSelect(array, val, null, model, keyOfPath);
    });
  }

  return tree;
}

export function downloadFile(url, filename) {
  let _filename = filename || url.substr(url.lastIndexOf("/") + 1);

  // var element = document.createElement('a');
  // element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(url));
  // element.setAttribute('download', _filename);
  // element.setAttribute('target', '_blank')
  // element.style.display = 'none';
  // document.body.appendChild(element);
  // element.click();
  // document.body.removeChild(element);

  const downloadFileA = document.createElement("a");
  document.body.appendChild(downloadFileA);
  downloadFileA.href = url;
  downloadFileA.target = "_blank";
  downloadFileA.download = _filename;
  /**
   * 超链接 target="_blank" 要增加 rel="noopener noreferrer" 来堵住钓鱼安全漏洞。
   * 如果你在链接上使用 target="_blank"属性，并且不加上rel="noopener"属性，
   * 那么你就让用户暴露在一个非常简单的钓鱼攻击之下。(摘要)
   */
  downloadFileA.rel = "noopener noreferrer";
  downloadFileA.click();
  document.body.removeChild(downloadFileA);
}

export function comImg(file) {
  return new Promise((resolve, reject) => {
    new Compressor(file, {
      //压缩质量, 0-1
      quality: 0.3,
      // //转换的类型，默认为 image/png
      // convertTypes: ['image/jpeg'],
      // //需要压缩的起始大小，默认5M, 5 * 1000 * 1000
      // convertSize: 5000000,
      //压缩成功后处理
      success(result) {
        resolve(result);
      },
      error(err) {
        console.log(err.message);
        reject();
      },
    });
  });
}



// // web端压缩性能不好，暂时放弃
// export async function comVideo(file) {
//   // 参考：
//   // https://www.wikimoe.com/post/65b49075f103a1ddcb83deb1

//   const ffmpeg = createFFmpeg({ log: true });

//   let inputFile = file;

  
//   // let ffmpeg = createFFmpeg({ corePath: "./ffmpeg-core.js", log: true });

//   return new Promise(async (resolve, reject) => {
//     try {
      
//       let ffmpeg = createFFmpeg({ log: true });
          
//       if (!ffmpeg.isLoaded()) {
//         await ffmpeg.load();
//       }

//       // 将输入视频写入 ffmpeg 内部文件系统
//       ffmpeg.FS('writeFile', 'input.mp4', await fetchFile(inputFile));

//       // 使用 h264 编码压缩视频
//       await ffmpeg.run(
//         '-i', 'input.mp4',
//         '-vcodec', 'libx264',
//         '-crf', 23, // 控制视频质量（0-51，默认23，数值越大压缩率越高）
//         '-preset', 'fast',
//         'output.mp4'
//       );

//       // 读取输出文件
//       const data = ffmpeg.FS('readFile', 'output.mp4');
//       const compressedVideo = new Blob([data.buffer], { type: 'video/mp4' });
      
//       let videoUrl = URL.createObjectURL(compressedVideo);
      
//       resolve(videoUrl); // 成功时，返回压缩后的视频 URL

//     } catch (error) {
//       reject(error); // 发生错误时，返回错误
//     }
//   });








//   // // 确保 FFmpeg 已加载
//   // if (!ffmpeg.loaded) {
//   //   await ffmpeg.load({
//   //     coreURL: 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/ffmpeg-core.js', // 核心文件 URL
//   //   });
//   // }

//   // 将文件写入 FFmpeg 的文件系统
//   // await ffmpeg.FS(fileName, await fetchFile(file));

//   // // 使用 FFmpeg 进行视频压缩
//   // await ffmpeg.exec([
//   //   '-i', fileName, // 输入文件
//   //   '-vf', 'scale=640:-1', // 缩放视频宽度为 640，高度按比例调整
//   //   '-c:v', 'libx264', // 使用 H.264 编码
//   //   '-crf', '28', // 压缩质量，数值越小质量越高
//   //   'output.mp4', // 输出文件名
//   // ]);

//   // // 读取压缩后的视频文件
//   // const data = await ffmpeg.readFile('output.mp4');

//   // // 返回压缩后的视频 URL
//   // return URL.createObjectURL(new Blob([data], { type: 'video/mp4' }));

// }

/**图片压缩，默认同比例压缩
 * @param {Object} fileObj
 * 图片对象
 * 回调函数有一个参数，base64的字符串数据
 */
export function compress(fileObj) {
  return new Promise((resolve, reject) => {
    // ....
    // const newFile = convertBase64UrlToBlob(data);
    // resolve(newFile);
    // ....
    // catch (e) {
    //     console.log("error!");
    //     reject(e);
    // }

    try {
      const image = new Image();
      image.src = URL.createObjectURL(fileObj);
      image.onload = function() {
        const that = this; // 默认按比例压缩
        let w = that.width;
        let h = that.height;
        const scale = w / h;
        w = fileObj.width || w;
        h = fileObj.height || w / scale;
        let quality = 0.6; // 默认图片质量为0.7 // 生成canvas
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d"); // 创建属性节点
        const anw = document.createAttribute("width");
        anw.nodeValue = w;
        const anh = document.createAttribute("height");
        anh.nodeValue = h;
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);
        ctx.drawImage(that, 0, 0, w, h); // 图像质量
        if (fileObj.quality && fileObj.quality <= 1 && fileObj.quality > 0) {
          quality = fileObj.quality;
        } // quality值越小，所绘制出的图像越模糊
        const data = canvas.toDataURL("image/jpeg", quality); // 压缩完成执行回调
        const newFile = convertBase64UrlToBlob(data);
        resolve(newFile);
        // return newFile
      };
    } catch (e) {
      // console.log("压缩失败!");
      reject(e);
    }
  });
}

function convertBase64UrlToBlob(urlData) {
  const bytes = window.atob(urlData.split(",")[1]); // 去掉url的头，并转换为byte // 处理异常,将ascii码小于0的转换为大于0
  const ab = new ArrayBuffer(bytes.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ia], { type: "image/png" });
}

/**
 *
 * @param {Array} treeData 需要过滤的树形对象
 * @param {String} value 匹配的字符串
 * @param {String} matchProp 被匹配的节点属性
 */
export function treeFilter(treeData, value, matchProp) {
  if (!treeData) {
    return null;
  }

  let getNewTreeData = (list, val, prop) => {
    var newTreeData = new Array();
    var node = null;
    var children = null;
    var text = "";
    for (var i = 0; i < list.length; i++) {
      //多个根节点开始遍历
      node = list[i];
      if (node.children) {
        children = node.children;
      }
      text = node[prop];
      if (text.indexOf(val) > -1) {
        // let tempChildren = node.children || []
        // delete node.children //匹配到的节点需要包含子节点
        newTreeData.push(node);
        continue;
      } else {
        if (children) {
          var newNodes = getNewTreeData(node.children || [], val, prop);
          if (newNodes && newNodes.length > 0) {
            node.children = newNodes;
            newTreeData.push(node);
          }
        }
      }
    }
    return newTreeData;
  };

  return getNewTreeData(treeData, value, matchProp);
}

//节流
export function throttle(fn, wait) {
  var timer = null;
  return function() {
    var context = this;
    var args = arguments;
    if (!timer) {
      timer = setTimeout(function() {
        fn.apply(context, args);
        timer = null;
      }, wait);
    }
  };
}

// //组织IE下按 BackSpace 键返回上一个页面（浏览记录）
// export function banBackSpace(e){
//   var ev = e || window.event;
//   //各种浏览器下获取事件对象
//   var obj = ev.relatedTarget || ev.srcElement || ev.target || ev.currentTarget;
//   //按下Backspace键
//   if(ev.keyCode == 8){
//     var tagName = obj.nodeName //标签名称
//     //如果标签不是input或者textarea则阻止Backspace
//     if(tagName!='INPUT' && tagName!='TEXTAREA'){
//       return stopIt(ev);
//     }
//     var tagType = obj.type.toUpperCase();//标签类型
//     //input标签除了下面几种类型，全部阻止Backspace
//     if(tagName=='INPUT' && (tagType!='TEXT' && tagType!='TEXTAREA' && tagType!='PASSWORD')){
//       return stopIt(ev);
//     }
//     //input或者textarea输入框如果不可编辑则阻止Backspace
//     if((tagName=='INPUT' || tagName=='TEXTAREA') && (obj.readOnly==true || obj.disabled ==true)){
//       return stopIt(ev);
//     }
//   }
// }
// function stopIt(ev){
//   if(ev.preventDefault ){
//     //preventDefault()方法阻止元素发生默认的行为
//     ev.preventDefault();
//   }
//   if(ev.returnValue){
//     //IE浏览器下用window.event.returnValue = false;实现阻止元素发生默认的行为
//     ev.returnValue = false;
//   }
//   return false;
// }

export function newGuid() {
  var guid = "";
  for (var i = 1; i <= 32; i++) {
    var n = Math.floor(Math.random() * 16.0).toString(16);
    guid += n;
    if (i == 8 || i == 12 || i == 16 || i == 20) guid += "-";
  }
  return guid;
}

//递归处理
export function recursive(children, cb) {
  for (let i = 0; i < children.length; i++) {
    let sub = children[i];
    if (cb) {
      cb(sub, children);
    }
    if (sub.children) {
      recursive(sub.children, cb);
    }
  }
}

//十六进制颜色转成 rgba（默认带 0.15 透明度）
export function olorRgb(color, opacity = 0.15) {
  var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  var sColor = color.toLowerCase();
  if (sColor && reg.test(sColor)) {
    if (sColor.length === 4) {
      var sColorNew = "#";
      for (var i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
      }
      sColor = sColorNew;
    }
    //处理六位的颜色值
    var sColorChange = [];
    for (var i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
    }
    return "rgba(" + sColorChange.join(",") + ", " + opacity + ")";
  } else {
    return sColor;
  }
}

export function remoteLoad(url, doc = document) {
  return new Promise(function(resolve) {
    let script_ = doc.createElement("script");
    script_.type = "text/javascript";
    script_.src = url;
    script_.onload = function() {
      console.log(url);
      resolve();
    };
    doc.body.appendChild(script_);
  });
}

export function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  let clone = Array.isArray(obj) ? [] : {};

  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key]);
    }
  }

  return clone;
}

export function getTextByCode(val) {
  if (val) {
    return CodeToText[val];
  }
  return val;
}

/**
 * 将比较大的字节数值转换成哼合适的单位大小
 * @param {*} limit 需要转换的字节数值
 * @returns 返回 xxGB、xxMB、xxKB 等
 */
export function sizeUnitConversion(limit) {
  var size = "";
  if (limit < 0.1 * 1024) {
    //如果小于0.1KB转化成B
    size = limit.toFixed(2) + "B";
  } else if (limit < 0.1 * 1024 * 1024) {
    //如果小于0.1MB转化成KB
    size = (limit / 1024).toFixed(2) + "KB";
  } else if (limit < 1 * 1024 * 1024 * 1024) {
    //如果小于1GB转化成MB
    size = (limit / (1024 * 1024)).toFixed(2) + "MB";
  } else {
    //其他转化成GB
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  }

  var sizestr = size + "";
  var len = sizestr.indexOf(".");
  var dec = sizestr.substr(len + 1, 2);
  if (dec == "00") {
    //当小数点后为00时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
  }
  return sizestr;
}

// 源字符串，要高亮的片段
//str 要传入的字符串  key 代表要高亮的字符串
export function heightLight(str, key) {
  try {
    const reg = new RegExp(key, "ig");
    return str.replace(reg, val => {
      return `<span style="color:red">${val}</span>`;
    });
  } catch (e) {
    return str;
  }
}

const pagination_key = "pagination_"; //后面加上表格索引（对应 viewBusinessType 枚举）

let _getPaginationObj = viewBusinessType => {
  let obj = localStorage.getItem(`${pagination_key}${viewBusinessType}`);
  if (obj) {
    obj = JSON.parse(obj);
  }
  return obj;
};

export function getPaginationObj(viewBusinessType) {
  return _getPaginationObj(viewBusinessType);
}

export function setPaginationObj(viewBusinessType, paginationObj) {
  if (paginationObj) {
    localStorage.setItem(
      `${pagination_key}${viewBusinessType}`,
      JSON.stringify(paginationObj || {})
    );
  }
}

/**
 * 设置 Pagination 对象某个属性的值
 * @param {*} viewBusinessType 业务枚举，主要能确保唯一性
 * @param {*} paginationObj 分页对象
 * @param {*} key 分页对象属性
 */
export function initPagination(viewBusinessType, paginationObj, key) {
  //表示设置了 viewBusinessType，需要存储分页信息
  if (viewBusinessType > 0 && paginationObj) {
    let pageObj = _getPaginationObj(viewBusinessType);
    if (pageObj && pageObj[key]) {
      paginationObj[key] = pageObj[key];
    }
  }
}
