<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750">
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="站点信息" prop="RegionalBusinessRelationId">
                                <!-- <span>
                                    <span v-if="formData.RegionalName">{{formData.RegionalName}}&nbsp;</span>
                                    <el-button type="text"  :disabled="!editable" @click="handleDialog()">选择</el-button>
                                    &nbsp;<i style="color:rgb(159 166 181);">(提示：请先选中设备所在地区，下一步进行设备添加操作)</i>
                                </span> -->

                                <div class="_regional_detail_wrapper">
                                    <div class="btn_wrapper">
                                        <el-button :disabled="!editable" type="text" @click="handleRegionDialog">选择</el-button>
                                    </div>
                                    <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                    <div class="close_wrapper" v-show="formData.RegionalName && editable">
                                        <div class="i_wrapper">
                                            <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="加热炉名称" prop="Name">
                                <el-input maxlength="50" :disabled='!editable' v-model="formData.Name"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="用途" prop="Usage">
                                <el-input maxlength="50" :disabled='!editable' v-model="formData.Usage"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="类型" prop="EquipmentModel">
                                <el-select
                                  :disabled="!editable"
                                  class="sel-ipt"
                                  style="width:100%"
                                  placeholder=""
                                  clearable
                                  v-model="formData.EquipmentModel"
                                >
                                  <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="PLC型号" prop="PLCType">
                                <el-input maxlength="50" :disabled='!editable' v-model="formData.PLCType"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="启炉/停炉" prop="StartOrStopFurnace">
                                <el-select
                                  :disabled="!editable"
                                  class="sel-ipt"
                                  style="width:100%"
                                  placeholder=""
                                  clearable
                                  v-model="formData.StartOrStopFurnace"
                                >
                                  <el-option v-for="item in startOrStopFurnaceTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="是否托管" prop="WhetherHosting">
                                <el-radio :disabled="!editable" v-model="formData.WhetherHosting" :label="false">否</el-radio>
                                <el-radio :disabled="!editable" v-model="formData.WhetherHosting" :label="true">是</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="托管备注" prop="CustodyNote">
                                <el-input maxlength="50" :disabled='!editable' v-model="formData.CustodyNote"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="厂家" prop="Manufacturers">
                                <el-input maxlength="50" :disabled='!editable' v-model="formData.Manufacturers"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="参数模板" prop="SalesAfterVistTemplateId">
                                <!--  -->
                                <div class="_regional_detail_wrapper">
                                    <div class="btn_wrapper">
                                        <el-button :disabled="dialogStatus != 'create'" type="text" @click="handleParamsTempReport">选择</el-button>
                                    </div>
                                    <div class="regional_text" :title="formData.SalesAfterVistTemplateName">{{ formData.SalesAfterVistTemplateName }}</div>
                                    <div class="close_wrapper" v-show="formData.SalesAfterVistTemplateName && dialogStatus == 'create'">
                                        <div class="i_wrapper">
                                            <el-button icon="el-icon-close" class="btn" circle @click="handleParamsTempSaveSuccess(null)"></el-button>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div v-loading='tempListLoading' style="min-height: 80px;">
                    <noData v-if="!tempListLoading && formData.SalesAfterVistTemplate.length == 0"></noData>
                    <el-collapse class="cus-collapse" v-model='activeNames' v-else>
                        <el-collapse-item :name="t.Id" v-for="(t, idx) in formData.SalesAfterVistTemplate" :key="idx">
                            <template slot="title">
                                {{ t.Name }}
                            </template>
                            <el-row>
                                <el-col :span="12" v-for="tt in t.FieldList" :key="tt.FieldId">
                                    <div class="col-wrapper">
                                        <div class="col-title el-form-item__label omit" :title="tt.Name">
                                            {{ tt.Name }}
                                        </div>
                                        <div class="col-content">
                                            <el-select
                                            :disabled="!editable"
                                            class="sel-ipt"
                                            style="width:100%"
                                            placeholder=""
                                            clearable
                                            v-model="tt.FieldValue"
                                            >
                                            <el-option v-for="item in paramTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    
                                </el-col>
                            </el-row>
                        </el-collapse-item>
                    </el-collapse>
                </div>

            </el-form>
        </template>
        <template slot="footer">
            <span class="fl m-r-50" v-if="dialogStatus == 'create'">
                <el-checkbox v-model="isContinue">继续添加</el-checkbox>
            </span>
            
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>

    <regionalSelector
        @closeDialog="closeRegionDialog"
        @saveSuccess="electedRegionalData"
        :dialogFormVisible="dialogRegionFormVisible"
        :checked="formData.RegionalBusinessRelationId ? {value: formData.RegionalBusinessRelationId, label: formData.RegionalName} : null"
    ></regionalSelector>

    <paramsTempSelector
        v-if="dialogParamsTempFormVisible"
        @closeDialog='closeParamsTempDialog' 
        @saveSuccess='handleParamsTempSaveSuccess'
        :dialogFormVisible='dialogParamsTempFormVisible'
        :dialogStatus='dialogParamsTempStatus' 
        :isDeviceParams='true'
    ></paramsTempSelector>

</div>
</template>

<script>
import * as classify from "@/api/classify";
import * as afterVistTemplate from "@/api/afterSalesMgmt/afterVistTemplate";
import { listToTreeSelect } from "@/utils";
import * as metadata from '@/api/systemManagement/metadata'
import noData from "@/views/common/components/noData";
import regionalSelector from "./regionalSelector";
import paramsTempSelector from './paramsTempSelector'
import * as equipmentParameter from "@/api/afterSalesMgmt/equipmentParameter";
import { vars } from './enum'


export default {
    name: "custom-mgmt-pool-create",
    directives: {},
    components: {
        noData,
        regionalSelector,
        paramsTempSelector,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [],
    props: {
        dialogStatus: {
            //create、update、detail、follow（跟进）
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        checkedRegional: {
            type: Object,
            default: null
        },
    },
    watch: {

        "$attrs.dialogFormVisible": {
            
            handler(val) {
                if (!val) {
                this.isContinue = false;
                }
                
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }

                    if(this.dialogStatus == 'create') {

                    }

                    if(this.dialogStatus == 'create' && this.checkedRegional) {
                        this.formData.RegionalBusinessRelationId = this.checkedRegional.value
                        this.formData.RegionalName = this.checkedRegional.label
                    }

                }
            },
            immediate: true
        },
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            let mainTitle = '设备参数'
            if (this.dialogStatus == "create") {
                return `创建${mainTitle}`
            } else if (this.dialogStatus == "update") {
                return `编辑${mainTitle}`
            } else if (this.dialogStatus == "detail") {
                return `${mainTitle}详情`
            } 
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            isContinue: false,
            types: vars.types,
            paramTypes: vars.paramTypes,
            startOrStopFurnaceTypes: vars.startOrStopFurnaceType,
            treeDatas: [],
            loading: false,
            disabledBtn: false,
            rules: {
                RegionalBusinessRelationId: { fieldName: "站点信息", rules: [{ required: true }] },
                Name: { fieldName: "加热炉名称", rules: [{ required: true }] },
                SalesAfterVistTemplateId: { fieldName: "参数模板", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            activeNames: [],
            formData: {
                Id: "", //
                RegionalBusinessRelationId: null,
                RegionalName: '',
                Name: '', //名称
                Usage: '', //用途
                EquipmentModel: null,//类型
                PLCType: '', //plc 类型
                StartOrStopFurnace: null,
                WhetherHosting: false, //是否托管
                CustodyNote: '',//托管备注
                Manufacturers: '',//厂家
                SalesAfterVistTemplateId: null,
                SalesAfterVistTemplateName: '',
                SalesAfterVistTemplate: [
                ],
            },
            tempListLoading: false,

            dialogRegionFormVisible: false,

            dialogParamsTempFormVisible: false,
            dialogParamsTempStatus: "create",
        };
    },

    methods: {
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        resetFormData() {
            let temp = {
                Id: "", //
                Name: '', //名称
                Usage: '', //用途
                EquipmentModel: null,//类型
                PLCType: '', //plc 类型
                StartOrStopFurnace: null,
                WhetherHosting: false, //是否托管
                CustodyNote: '',//托管备注
                Manufacturers: '',//厂家
            };
            if(this.formData.SalesAfterVistTemplate) {
                this.formData.SalesAfterVistTemplate.forEach(t => {
                    if(t.FieldList) {
                        t.FieldList.forEach(tt => {
                            tt.FieldValue = null
                        })
                    }
                });
            }
            this.formData = Object.assign({}, this.formData, temp);
        },
        async getDetail() {
            this.loading = true
            equipmentParameter.detail({
                id: this.id
            }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);
                if(this.formData.SalesAfterVistTemplate && this.formData.SalesAfterVistTemplate.length > 0) {
                    this.activeNames = this.formData.SalesAfterVistTemplate.map(s => s.Id)
                }
            }).catch(err => {
                this.loading = false
            });
        },

        //地区选择
        closeRegionDialog() {
          this.dialogRegionFormVisible = false;
        },
        handleRegionDialog(){
            this.dialogRegionFormVisible=true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('RegionalBusinessRelationId');
            if(data){
                this.formData.RegionalBusinessRelationId=data.value;
                this.formData.RegionalName=data.label;
            }else{
                this.formData.RegionalBusinessRelationId='';
                this.formData.RegionalName='';
            }
            this.closeRegionDialog()
        },
        getTempList() {
            if(this.formData.SalesAfterVistTemplateId) {
                this.tempListLoading = true
                afterVistTemplate.detail({id: this.formData.SalesAfterVistTemplateId}).then(res => {
                    this.tempListLoading = false
                    this.formData.SalesAfterVistTemplate = res.TypeList
                    if(this.formData.SalesAfterVistTemplate && this.formData.SalesAfterVistTemplate.length > 0) {
                        this.activeNames = this.formData.SalesAfterVistTemplate.map(s => s.Id)
                    }
                }).catch(err => {
                    this.tempListLoading = false
                })
            }
        },
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                //提交数据保存

                postData = Object.assign({}, this.formData);
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                }


                this.disabledBtn = true
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = equipmentParameter.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = equipmentParameter.edit(postData);
                }

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    if (this.isContinue) {
                        this.resetFormData();
                        this.$emit("reload");
                    } else {
                        this.$refs.appDialogRef.createData();
                    }
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },
        handleParamsTempReport(row, activeName = 'create') {
            this.dialogParamsTempStatus = activeName;
            this.dialogParamsTempFormVisible = true;
        },
        closeParamsTempDialog() {
            this.dialogParamsTempFormVisible = false
        },
        handleParamsTempSaveSuccess(checkedObj) {
            if(checkedObj) {
                this.formData.SalesAfterVistTemplateId = checkedObj.value
                this.formData.SalesAfterVistTemplateName = checkedObj.label

                this.formData.SalesAfterVistTemplate = []
                this.getTempList()
            }else{
                this.formData.SalesAfterVistTemplateId = null
                this.formData.SalesAfterVistTemplateName = ''
            }
            this.$refs.formData.clearValidate('SalesAfterVistTemplateId');
            this.closeParamsTempDialog()
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
.cus-collapse >>> .el-collapse-item__header{
    background: #fbfbfb!important;
    height: 36px;
}
</style>

<style lang='scss' scoped>

.col-wrapper{
    display: flex;
    align-items: center;
    padding: 5px 0;
    .col-title{
        width: 100px;
        text-align: right;
        padding-right: 12px;
    }
    .col-content{
        flex: 1;
    }
}
</style>
