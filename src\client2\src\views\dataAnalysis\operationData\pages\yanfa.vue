<template>
    <div class="block-content" v-loading='loading'>
      
     <!-- <div style="margin: 0 10px 10px 0; text-align: right; border-bottom: 1px solid #dcdfe6; padding-top: 10px;">
                 研发
                <el-radio-group  v-model="period" size="mini" @change='getResearchDetailsChart'>
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
     </div> -->

     <el-row style="width:100%; margin-top:19px; margin-bottom:5px;">
            <el-col :span=12 style="display:flex;">
                <span style="margin-left:30px; width: 5px; height: 20px; background: #3D73DD;"></span>
                <span style="margin-top:2px; font-size:16px; color: #1D2129; margin-left:11px; font-weight:bold;">研发</span>
            </el-col>
            <el-col :span=12 style="display:flex; justify-content:flex-end;">
               <el-radio-group v-model="period" size="mini" @change='getResearchDetailsChart' style="margin-right:15px;">
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </el-col>
     </el-row>
       
      
        <div class="inner-content">
            <el-row style="width: 100%; height: 1px; background: #DCDFE6;"></el-row>
            <div class="top" style="margin-top: 30px;">


                 <div style="display:flex;">
                    <span class="shape"></span>
                    <div class="top-item" >
                        <span class="top-title">产品数量</span>
                        <span class="top-value">
                            {{ formData.ProductCount }}
                        </span>
                    </div>
                 </div>
               


               
                <div style="margin-left:130px;  display:flex;">
                      <span class="shape"></span>
                    <div class="top-item">
                        <span class="top-title">项目数量</span>
                        <span class="top-value">
                            {{ formData.ProjectCount }}
                        </span>
                    </div>
                   
                </div>

                 
                 <div style="margin-left:130px; display:flex;">
                    <span class="shape"></span>
                     <div class="top-item">
                        <span class="top-title">待提取需求数量</span>
                         <span class="top-value">
                            {{ formData.AwaitDemandCount }}
                        </span>
                     </div>
                </div>
               
                
                <!-- <div class="flex-dire-column-wrapper">
                    <div class="chart-title">集控装置情况</div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption2.series || pieEchartOption2.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart2" :option='pieEchartOption2'></app-charts-basic>
                    </div>
                </div> -->
            </div>

            


           
            <div class="bottom">

                 <div class="flex-dire-column-wrapper">
                    <span class="chart-title">项目情况</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption1.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart1" :option='pieEchartOption1'></app-charts-basic>
                    </div>
                 </div>
                <div class="flex-dire-column-wrapper">
                    <span class="chart-title">需求情况</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption2.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption2'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <span class="chart-title">任务情况</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption3.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart4" :option='pieEchartOption3'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <span class="chart-title">问题数量</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption4.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart5" :option='pieEchartOption4'></app-charts-basic>
                    </div>
                </div>

            </div>

        </div>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import blockTitle from '../blockTitle'
import * as odc from "@/api/operatingDataCenter";
import { pieEchartOptionTemp, colors, dateTypeEnum1 } from "../vars";
import mixins from '../mixins'

export default {
    name: 'yanfa',
    mixins: [mixins],
    components: {
        noData,
        blockTitle,
    },
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    mounted() {
        this.getResearchDetailsChart()
    },
    data() {
        return {
            period: 3,
            dateTypeEnum: dateTypeEnum1,
            loading: false,
            chartsHeight: '270px',
            pieEchartOption1: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption2: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption3: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption4: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            
            formData: {
                ProductCount: 0, //产品数据
                ProjectCount: 0, //项目数量
                AwaitDemandCount: 0, //待提取需求数量

            },
        }
    },
    methods: {
        getResearchDetailsChart() {
            let that = this
            that.loading = true
            odc.getResearchDetailsChart({Period: that.period}).then(res => {
                that.loading = false

                that.formData = {
                    ProductCount: res.ProductCount, //产品数据
                    ProjectCount: res.ProjectCount, //项目数量
                    AwaitDemandCount: res.AwaitDemandCount, //待提取需求数量
                }

// AwaitDemandCount: 15
// ChartData: [{Value: 20, Label: "产品数量", Total: 0}, {Value: 62, Label: "项目数量", Total: 0}]
// DemandChartData: [{Value: 15, Label: "未完成", Total: 0}, {Value: 0, Label: "已完成", Total: 0}]
// MonthData: []
// ProductCount: 20
// ProjectChartData: [{Value: 20, Label: "正常", Total: 0}, {Value: 62, Label: "已支付", Total: 0},…]
// ProjectCount: 62
// QuestionChartData: [{Value: 2, Label: "未完成", Total: 0}, {Value: 0, Label: "已完成", Total: 0}]
// TaskDetailChartData: [{Value: 51, Label: "未完成", Total: 0}, {Value: 9, Label: "已完成", Total: 0}]

                that.formData.MaintenanceTotal = res.MaintenanceTotal
                that.formData.MonthRepairFee = res.MonthRepairFee

                that.pieEchartOption1 = that._initProjectPieChartDatas(res.ProjectChartData || []) //项目情况
                that.pieEchartOption2 = that._initPieChartDatas(res.DemandChartData || []) //需求情况
                that.pieEchartOption3 = that._initPieChartDatas(res.TaskDetailChartData || []) //任务情况
                that.pieEchartOption4 = that._initPieChartDatas(res.QuestionChartData || []) //问题情况
            }).catch(err => {
                that.loading = false
            })

        },

        _initProjectPieChartDatas(list) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                targetOption = {
                    legend: {
                        // type: 'scroll',
                        // icon:"circle",
                        // orient: 'vertical',
                        // left: '62%',
                        // top: 'middle',
                        data: chartDatas.map(s => s.Label)//chartDatas.map(s => s.Label)
                    },
                    series: [{
                        radius: ['50%', '65%'],
                        center: ['50%', '50%'],
                        label: {
                            normal: {
                                show: false,
                                position: 'inner'
                            }
                        },
                        data: chartDatas.map(s => {
                            return {
                                value: s.Value === 0 ? null : s.Value,
                                name: s.Label
                            }
                        })
                    }],
                    color: ['#3C9EFF', '#8AEC5A', '#A3ABBC', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2']
                }
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption)

            return targetOption

        },

        _initPieChartDatas(list) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                targetOption = {
                    legend: {
                        // type: 'scroll',
                        // icon:"circle",
                        // orient: 'vertical',
                        // left: '62%',
                        // top: 'middle',
                        data: chartDatas.map(s => s.Label)//chartDatas.map(s => s.Label)
                    },
                    series: [{
                        radius: ['50%', '65%'],
                        center: ['50%', '50%'],
                        label: {
                            normal: {
                                show: false,
                                position: 'inner'
                            }
                        },
                        data: chartDatas.map(s => {
                            return {
                                value: s.Value === 0 ? null : s.Value,
                                name: s.Label
                            }
                        })
                    }],
                    color: ['#E32B06', '#3D73DD', '#88d1ea', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2']
                }
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption)

            return targetOption

        },
    },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
.block-content{
  //  height: 100%;
    display: flex;
    flex-direction: column;
    .inner-content{
    
        flex: 1;
        display: flex;
        flex-direction: column;
        .bottom{
            flex: 1;
            display: flex;
            margin-top: 40px;
            .flex-dire-column-wrapper{
                flex: 1;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
    }
}

.chart-title{
    text-align: center;
    color: $text-main-color;
    font-size: 16px;
    font-weight: bold;
}

// .flex-1, .flex-2{
//     box-sizing: border-box;
//     margin: 5px;
//     border-radius: 5px;
//     display: flex;
//     flex-direction: column;
//     >div:last-child{
//         flex: 1;
//     }
// }

// .flex-1{
//     flex: 1;
// }

.top{
   flex: 1;
   display: flex;
   margin-left: 30px;
}

.top-item{
   display: flex; flex-direction: column; margin-left: 11px;
}

.top-title{
   color:#A0A1A3; font-size:14px;
}
.top-value{
    color:$text-primary; font-weight: bold; font-size:30px; margin-top:3px;
}

.text-content{
    text-align: center; flex: 1; font-weight: bold; display: flex; justify-content: center; align-items: center; word-break: break-all; white-space: normal; word-break: break-all;
}

.shape{
   width: 10px;height: 50px;background: #E9EAEF;
}

</style>