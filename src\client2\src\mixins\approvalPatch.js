
//补丁：用于修复审批系列弹框——如果是非预选（自选），且当前用户不处于审批层中(且未审批)，则不显示什么按钮
import { getUserInfo } from "@/utils/auth";
export default {
    computed: {
        isApprovalor() {
            if(this.formData && this.formData.Approval) {
                return this.__isApprovalor(this.formData.Approval)
            }
            return false
        },
        //是否是最近一个审批层中，未审批人员（如果是，表示可以审批；否则表示不可以审批——审批需要满足审批层先后顺序）
        isCurrentNodeApprovalOperatorEmployee() {
            if(this.formData && this.formData.Approval) {
                return this.formData.Approval.IsCurrentNodeApprovalOperatorEmployee
            }
            return false
        },
        // 是否可编辑审批人
        isEditApprove() {
          return this?.formData?.Approval?.ApprovalSet === 1
        },
    },
    data() {
        return {
            
        }
    },
    watch: {
        
    },
    created() {
        
    },
    methods: {
        __isApprovalor(approvalObj) {
            if(approvalObj.ApprovalSet == 1) {
                return true
            }
         
            if(approvalObj.ApprovalNodeList) {
                let approvalList = approvalObj.ApprovalNodeList.reduce((a, b) => { return a.concat(...b.NoApprovalEmployeeList || [])}, [])

                // let approvalList = approvalObj.ApprovalEmployeeList.reduce(function (a, b) { return a.concat(b)} );
                let ids = approvalList.map(s => s.EmployeeId)
                let currentEmpId = getUserInfo().employeeid
                let isApprovalor = ids.findIndex(s => s == currentEmpId) > -1
                return isApprovalor
            }

            return false
        }
    }
}