import { vars } from './vars'
import * as impMgmt from "@/api/implementation/impManagement2"

export default {
    filters: {
        engStatusFilter(val) {
            let obj = vars.implementationSatus.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return val
        },
        regionalStatusFilter(val) {
            let obj = vars.regionalStatus.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return val
        },
        procesStatusFilter(val) {
            let obj = vars.processStatus.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return val
        },
    },
    data() {
        return {
            templates: []
        }
    },   
    methods: {
        getEngStatus(val) {
            return vars.implementationSatus.find(s => s.value == val) || {}
        },
        getRegionalStatus(val) {
            return vars.regionalStatus.find(s => s.value == val) || {}
        },
        getEquStatus(val) {
            return vars.equStatus.find(s => s.value == val) || {}
        },
        getProcesStatus(val) {
            return vars.processStatus.find(s => s.value == val) || {}
        },
        getQuestionStatus(val) {
            return vars.questionStatus.find(s => s.value == val) || {}
        },
        getTemplates(data) {
            let postData = Object.assign({}, data)
            impMgmt.getTemplates(postData).then(res => {
                this.templates = res.map(s => {
                    s.value = s.Id
                    s.label = s.Name
                    return s
                })
            })
        }
    },
       
};
