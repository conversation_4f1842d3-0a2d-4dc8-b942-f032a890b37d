<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='1000'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <div>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="计划名称" prop="PlanName">
                                    <el-input maxlength="50" :disabled="!editable" v-model="formData.PlanName"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="客服人员" prop="EmployeeList">
                                    <!-- <emp-selector :readonly="!editable" :showType="2" :multiple="true" :list="formData.EmployeeList" @change="handleChangeOwnerUsers"></emp-selector> -->
                                    <normar-emp-selector listSelectorTitle='选择人员' :listSelectorUrl='serviceArea.business + "/CustomerServiceManage/GetListPage"' :multiple='true' :showType='2' :list='formData.EmployeeList' :columns='empColumns' key='service-users' @change='handleChangeUsers'></normar-emp-selector>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item :label="'计划起止时间'" prop="Range">
                                    <el-date-picker v-model="formData.Range" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" style="width: 100%;" :disabled="!editable">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="24">
                                <el-form-item label="回访类型" prop='Type'>
                                    <el-radio :disabled="dialogStatus != 'create'" v-for="(item, idx) in planTypes" :key="idx" v-model="formData.Type" :label="item.value">{{ item.label }}</el-radio>
                                </el-form-item>
                            </el-col> -->
                        </el-row>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <el-button type="primary" @click="showListSelector">添加报修单</el-button>
                         <el-button type="danger" @click="handleRemoveList">批量删除</el-button>
                    </div>
                    <app-table-core class="treeTable" ref="mainTable" :tab-columns="tabColumns" :tab-datas="formData.List" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="0" :multable='true' @rowSelectionChanged="rowSelectionChanged">
                        <template slot="HandlerEmployeeList" slot-scope="scope">
                            <span v-if="scope.row.HandlerEmployeeList">{{ scope.row.HandlerEmployeeList | names }}</span>
                            <span v-else>无</span>
                        </template>
                        <template slot="ReportTime" slot-scope="scope">
                            {{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm') }}
                        </template>
                        <template slot="IsReplacePart" slot-scope="scope">
                            {{ scope.row.IsReplacePart ? '有' : '无' }}
                        </template>

                        <!-- 表格行操作区域 -->
                        <!-- <template slot-scope="scope">
                            <app-table-row-button @click="handleRemove(scope.index - 1)" :type="3"></app-table-row-button>
                        </template> -->

                    </app-table-core>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>

    <!-- 报修单、订单 弹框 -->
    <listSelector :checkedData="listSelectorCheckedData" :width='1200' :getListUrl="listSelectorUrl" :multiple="listSelectorMultiple" :pageTitle="listSelectorTitle" :topMessage="listSelectorTopMessage" :selectKeyName="listSelectorKeyName" :condition="formData.Type == 1 ? listQueryOrder : listQueryRep" :columnData="listSelectorColumnData" :dialogFormVisible="listSelectorDialogFormVisible" @closeDialog="listSelectorCloseDialog" @saveSuccess="listSelectorSaveSuccess" ref="listSelector">
        <template slot="conditionArea">
            <app-table-form v-if="formData.Type == 1" style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
                <template slot='OrderNumber'>
                    <el-input style="width: 100%;" v-model="listQueryOrder.OrderNumber" placeholder=""></el-input>
                </template>
                <template slot='EmployeeName'>
                    <el-input style="width: 100%;" v-model="listQueryOrder.EmployeeName" placeholder=""></el-input>
                </template>
            </app-table-form>

            <app-table-form v-if="formData.Type == 2" style="padding-top: 10px;" :label-width="'80px'" :items='tableSearchItemsRep' @onSearch='handleFilter' @onReset='onResetSearch'>
                <template slot="ReportRange">
                    <el-date-picker
                      style="width: 100%;"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm"
                      v-model="listQueryRep.ReportRange"
                      type="datetimerange"
                      :picker-options="pickerOptions"
                      :default-time="['00:00:00', '23:59:00']"
                    ></el-date-picker>
                </template>
                <template slot="RegionalId">
                    <div class="el-input el-input--mini">
                      <div style="display: flex; height: 28px;line-height: 28px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; ">
                        <div style="padding-left: 10px;">
                          <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">选择</span>
                        </div>
                        <div style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="listQueryRep.areaName">{{ listQueryRep.areaName }}</div>
                        <div style="width: 28px; text-align: center;">
                          <i style="cursor: pointer;" title="删除" @click="handleClearRegional" v-show="listQueryRep.RegionalId" class="el-icon-close"></i>
                        </div>
                      </div>
                    </div>
                </template>
                <template slot="ServiceNo">
                    <el-input
                      style="width: 100%;"
                      v-model.trim="listQueryRep.ServiceNo"
                      placeholder
                    ></el-input>
                </template>
                <!-- <template slot='Code'>
                    <el-input style="width: 100%;" v-model="listQueryRep.Code" placeholder=""></el-input>
                </template> -->
                <template slot='EmployeeName'>
                    <el-input style="width: 100%;" v-model="listQueryRep.EmployeeName" placeholder=""></el-input>
                </template>
                <template slot="IsReplacePart">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      placeholder="请选择"
                      clearable
                      v-model="listQueryRep.IsReplacePart"
                    >
                      <el-option label="有" :value="true"></el-option>
                      <el-option label="无" :value="false"></el-option>
                    </el-select>
                </template>
                
                <template slot="IsWarranty">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      placeholder="请选择"
                      clearable
                      v-model="listQueryRep.IsWarranty"
                    >
                        <el-option
                            v-for="item in warranty"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </template>
                <template slot="ChargeTotalType">
                    <div style="display: flex;">
                        <div style="width:80px;">
                            <el-select
                            class="sel-ipt"
                            placeholder="请选择"
                            clearable
                            v-model="listQueryRep.ChargeTotalType"
                            @change='handleChange'
                            >
                                <el-option
                                    v-for="item in chargeTypes"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                        <div style="flex: 1; margin-left: 4px;">
                            <el-input @keyup.native="changeAmount('Charge1')" v-model.number="listQueryRep.Charge1" maxlength="9" placeholder=""></el-input>
                        </div>
                        <div style="flex: 1; margin-left: 4px;" v-if="listQueryRep.ChargeTotalType == 3">
                            <el-input @keyup.native="changeAmount('Charge2')" v-model.number="listQueryRep.Charge2" maxlength="9" placeholder=""></el-input>
                        </div>
                    </div>
                </template>


            </app-table-form>
        </template>

        <!-- <template slot="ReceivedPayment" slot-scope="scope">
            {{ scope.row.ReceivedPayment | receivedPayment }}
        </template>
        <template slot="MaintenanceStatus" slot-scope="scope">
            {{ scope.row.MaintenanceStatus | saintenanceStatusFilter }}
        </template> -->
        <template slot="Employee" slot-scope="scope">
            <span v-if="scope.row.Employee">{{ scope.row.Employee.Name }}</span>
        </template>

        <template slot="HandlerEmployeeList" slot-scope="scope">
            <span v-if="scope.row.HandlerEmployeeList">{{ scope.row.HandlerEmployeeList | names }}</span>
            <span v-else>无</span>
        </template>
        <template slot="ReportTime" slot-scope="scope">
            {{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm') }}
        </template>
        <template slot="IsReplacePart" slot-scope="scope">
            {{ scope.row.IsReplacePart ? '有' : '无' }}
        </template>
        
    </listSelector>

    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="listQueryRep.RegionalId ? [listQueryRep.RegionalId] : []"
    ></v-area-choose>
</div>
</template>

<script>
// import empSelector from "../../../common/empSelector"
import vAreaChoose from "../../../afterSalesMgmt/businessMap/common/areaChoose";
import {
    vars
} from "../common/vars"
import * as afterService from "@/api/afterSalesMgmt/afterService"
import listSelector from '../../../common/listSelector'
import {
    serviceArea
} from "@/api/serviceArea"
import normarEmpSelector from '../../common/normarEmpSelector'
import * as orderVars from '../../../salesMgmt/common/vars'
import * as maintenCenterVars from '../../maintenCenter/common/vars'
import { getUserInfo,getDateTimeRange } from "@/utils/auth";
export default {
    name: "after-service-create",
    directives: {},
    components: {
        // empSelector,
        listSelector,
        normarEmpSelector,
        vAreaChoose,

    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    filters: {
        names(list) {
            if (list && list.length > 0) {
                return list.map(s => s.Name).join(',')
            }
            return ''
        },
        receivedPayment(status) {
            const statusObj = orderVars.vars.orderMgmt.remittanceStatus.find(s => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "";
        },
        saintenanceStatusFilter(status) {
            let obj = maintenCenterVars.vars.maintenOrderMgmt.maintenanceStatus.find(s => s.value == status)
            if (obj) {
                return obj.label
            }
            return status
        },
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        },
        'formData.Type'(val, oldVal) {
            this.oldType = oldVal
            if (this.isTip) {
                if (oldVal && oldVal != val && this.formData.List && this.formData.List.length > 0) {
                    this.$confirm('重新选择回访类型将清空回访单列表信息, 是否继续操作？', "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    }).then(() => {
                        this.isTip = true
                        this.formData.List = []
                    }).catch(() => {
                        this.isTip = false
                        this.formData.Type = this.oldType
                    });
                }
            } else {
                this.isTip = true
            }
        },
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建回访计划";
            } else if (this.dialogStatus == "update") {
                return "编辑回访计划";
            } else if (this.dialogStatus == "detail") {
                return "回访计划详情";
            }
        },
    },
    created() {
        if (this.condition) {
            this.listQueryOrder = Object.assign({}, this.listQueryOrder, this.condition)
        }
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            chargeTypes: [
                {value: 1, label: '大于'},
                {value: 2, label: '小于'},
                {value: 3, label: '区间'},
            ],
            warranty: orderVars.vars.business.warranty,
            dialogRegionalVisible: false,
            pickerOptions: {
                shortcuts: [{
                    text: '今年1月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('01');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年2月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('02');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年3月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('03');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                },{
                    text: '今年4月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('04');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年5月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('05');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年6月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('06');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                },{
                    text: '今年7月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('07');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年8月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('08');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年9月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('09');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                },{
                    text: '今年10月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('10');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年11月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('11');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }, {
                    text: '今年12月',
                    onClick(picker) {
                    let [start,end]=getDateTimeRange('12');
                    picker.$emit('pick', [start.toDate(), end.toDate()]);
                    }
                }]
            },
            serviceArea,
            oldType: null,
            isTip: true,
            loading: false,
            disabledBtn: false,
            planTypes: vars.afterService.planTypes,
            listQueryOrder: {
                OrderNumber: '',
                EmployeeName: '',
                IsSelectList: true,
                ReturnVisitsOrderNumber: [],
                
            },
            tableSearchItems: [{
                    prop: "OrderNumber",
                    label: "订单编号"
                },
                {
                    prop: "EmployeeName",
                    label: "业务员"
                },
            ],
            listQueryRep: {
                ReportRange: [],
                RegionalId: '', //报修地区
                // Code: '',
                EmployeeName: '',
                IsSelectList: true,
                MaintenanceStatus: [3],
                IsReplacePart: null,
                IsWarranty: null,
                ChargeTotalType: null,
                Charge1: '',
                Charge2: ''
            },
            tableSearchItemsRep: [
                { prop: "ReportRange", label: "报修时间" },
                { prop: "RegionalId", label: "地区" },
                { prop: "ServiceNo", label: "服务单号"},
                // {
                //     prop: "Code",
                //     label: "报修单号"
                // },
                {
                    prop: "EmployeeName",
                    label: "实施人员"
                },
                { prop: "IsReplacePart", label: "更换配件" },
                { prop: "IsWarranty", label: "是否在保" },
                { prop: "ChargeTotalType", label: "总共收费" },
                
            ],
            empColumns: [{
                    attr: {
                        prop: "Name",
                        label: "姓名",
                    },
                },
                {
                    attr: {
                        prop: "Number",
                        label: "工号",
                    },
                },
                {
                    attr: {
                        prop: "Gender",
                        label: "性别",
                    },
                },
                {
                    attr: {
                        prop: "CustomerServiceDepartmentName",
                        label: "负责地区"
                    },
                },
                {
                    attr: {
                        prop: "Phone",
                        label: "手机",
                    },
                },
                {
                    attr: {
                        prop: "OrgName",
                        label: "部门"
                    },
                }
            ],
            tabColumns: [
                // {
                //     attr: {
                //         prop: "Code",
                //         label: '订单号',
                //         width: '440',
                //         renderHeader: this.renderHeader
                //     }
                // },
                {
                    attr: { prop: "RegionalName", label: "报修地区", showOverflowTooltip: true, width:'280' }
                },
                {
                    attr: { prop: "ServiceNo", label: "服务单号" },
                },
                {
                    attr: { prop: "ReportTime", label: "报修时间", sortable: 'custom'},
                    slot: true
                },
                {
                    attr: { prop: "TotalPrice", label: "总收费（元）" }
                },
                {
                    attr: {
                        prop: "HandlerEmployeeList",
                        label: "实施人员",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "IsReplacePart",
                        label: "更换配件",
                    },
                    slot: true
                }
            ],
            rules: {
                PlanName: {
                    fieldName: "计划名称",
                    rules: [{
                        required: true
                    }]
                },
                EmployeeList: {
                    fieldName: "客服人员",
                    rules: [{
                        required: true,
                        trigger: 'change'
                    }]
                },
                AcceptanceDescription: {
                    fieldName: "验收标准",
                    rules: [{
                        required: true
                    }]
                },
                Range: {
                    fieldName: "计划起止时间",
                    rules: [{
                        required: true
                    }]
                },
                List: {
                    fieldName: "回访单",
                    rules: [{
                        required: true
                    }]
                },
            },
            labelWidth: "110px",
            formData: {
                Id: "", //需求ID
                PlanName: '', //计划名称
                EmployeeList: [], //客服人员
                EmployeeIds: [],
                StartTime: null, //开始时间
                EndTime: null, //截至时间
                Range: [],
                Type: 2, //回访类型
                List: [],
                Ids: [], //提交id集合
            },

            listSelectorType: "",
            listSelectorCheckedData: [],
            listSelectorUrl: "",
            listSelectorMultiple: true,
            listSelectorCondition: {},
            listSelectorTitle: "添加报修单",
            listSelectorTopMessage: "",
            listSelectorKeyName: "",
            listSelectorColumnData: [],
            listSelectorDialogFormVisible: false,

            multipleSelection: []
        };
    },
    methods: {
        changeAmount(type) {
            this.listQueryRep[type] = this.listQueryRep[type].replace(/[^\d.]/g, '')
        },
        handleChange(val) {
            if(!val) {
                this.listQueryRep.Charge1 = ''
                this.listQueryRep.Charge2 = ''
            }
        },
        rowSelectionChanged(rows) {
          this.multipleSelection = rows;
        },
        handleClearRegional() {
            this.listQueryRep.RegionalId = ''
            this.listQueryRep.areaName = ''
            this.handleFilter();
        },
        electedRegionalData(data){
            if(data) {
                this.listQueryRep.RegionalId=data.Id;
                this.listQueryRep.areaName=data.ParentName;
            }else{
                this.listQueryRep.RegionalId='';
                this.listQueryRep.areaName='';
            }
            this.handleFilter();
        },
        closeRegionalDialog() {
            this.dialogRegionalVisible=false
        },
        handleRegionalDialog() {
            this.dialogRegionalVisible=true;
        },
        handleFilter() {
            if(this.listQueryRep.ReportRange && this.listQueryRep.ReportRange.length == 2) {
                this.listQueryRep.ReportTimeStart = this.listQueryRep.ReportRange[0];
                this.listQueryRep.ReportTimeEnd = this.listQueryRep.ReportRange[1];
            }else{
                this.listQueryRep.ReportTimeStart = null
                this.listQueryRep.ReportTimeEnd = null
            }

            this.$refs.listSelector.getDatas()
        },
        onResetSearch() {
            // this.listQueryOrder.PageIndex = 1

            if (this.formData.Type == 1) {
                this.listQueryOrder.OrderNumber = ''
                this.listQueryOrder.EmployeeName = ''
            } else {
                this.listQueryRep.ReportRange = []
                this.listQueryRep.RegionalId = ''
                this.listQueryRep.ServiceNo = ''
                this.listQueryRep.areaName = ''
                // this.listQueryRep.Code = ''
                this.listQueryRep.EmployeeName = ''
                this.listQueryRep.IsReplacePart = null
                this.listQueryRep.IsWarranty = null
                this.listQueryRep.ChargeTotalType = null
                this.listQueryRep.Charge1 = ''
                this.listQueryRep.Charge2 = ''
                

            }

            this.handleFilter()
        },
        handleChangeUsers(users) {
            this.formData.EmployeeList = users
        },
        /**展示设备选择器 */
        showListSelector() {
            let _this = this;
            if (_this.dialogStatus == "create") {
                _this.listQueryOrder.ReturnVisitsOrderNumber = []
            }
            _this.listSelectorCheckedData = _this.formData.List
            if (_this.formData.Type == 1) { //订单
                _this.listSelectorUrl = serviceArea.business + "/Order/GetListPage"
            } else { //报修单
                _this.listSelectorUrl = serviceArea.business + "/Maintenance/GetListPage"
            }
            _this.listSelectorCondition = {}
            _this.listSelectorMultiple = true
            _this.listSelectorKeyName = "Id"
            // console.log(333,this.formData,this.listSelectorCheckedData)
            if (_this.formData.Type == 1) { //订单
                _this.listSelectorColumnData = [{
                        attr: {
                            prop: "OrderNumber",
                            label: "订单编号",
                        },
                    },
                    // {
                    //     attr: {
                    //         prop: "ClientUnits",
                    //         label: "客户单位"
                    //     }
                    // },
                    // {
                    //     attr: {
                    //         prop: "RegionalName",
                    //         label: "地区"
                    //     }
                    // },
                    {
                        attr: {
                            prop: "OrderSum",
                            label: "合同金额(元)",
                        }
                    },
                    {
                        attr: {
                            prop: "ReceivedPayment",
                            label: "回款情况",
                        },
                        slot: true
                    },
                    {
                        attr: {
                            prop: "Employee",
                            label: "业务员",
                        },
                        slot: true
                    }
                ]
            } else { //报修单
                _this.listSelectorColumnData = [
                    {
                        attr: { prop: "RegionalName", label: "报修地区", showOverflowTooltip: true, width:'280' }
                    },
                    // {
                    //     attr: {
                    //         prop: "Code",
                    //         label: "报修单号",
                    //     }
                    // },
                    // {
                    //     attr: {
                    //         prop: "MaintenanceStatus",
                    //         label: "状态",
                    //     },
                    //     slot: true
                    // },
                    // {
                    //     attr: {
                    //         prop: "RegionalName",
                    //         label: "安装区域"
                    //     }
                    // },
                    {
                        attr: { prop: "ServiceNo", label: "服务单号" ,width:'95'},
                    },
                    {
                        attr: { prop: "ReportTime", label: "报修时间", sortable: 'custom' ,width:'125'},
                        slot: true
                    },
                    {
                        attr: { prop: "TotalPrice", label: "总收费（元）",width:'100' }
                    },
                    {
                        attr: {
                            prop: "HandlerEmployeeList",
                            label: "实施人员",
                        },
                        slot: true
                    },
                    {
                        attr: {
                            prop: "IsReplacePart",
                            label: "更换配件",
                        },
                        slot: true
                    }
                ]
            }
            _this.listSelectorDialogFormVisible = true
        },
        resetFormData() {
            let temp = {
                Id: "", //需求ID
                PlanName: '', //计划名称
                EmployeeList: [], //客服人员
                EmployeeIds: [],
                StartTime: null, //开始时间
                EndTime: null, //截至时间
                Range: [],
                Type: 2, //回访类型
                List: [],
                Ids: [], //提交id集合
            };
            this.formData = Object.assign({}, this.formData, temp);
        },
        createData() {
            let validate = this.$refs.formData.validate();

            Promise.all([validate]).then(valid => {
                //提交数据保存
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.EmployeeIds = postData.EmployeeList.map(s => s.EmployeeId)
                postData.StartTime = postData.Range[0]
                postData.EndTime = postData.Range[1]
                postData.Ids = postData.List.map(s => s.Id)

                delete postData.List
                delete postData.EmployeeList
                delete postData.Range

                if (this.dialogStatus == "create") {
                    delete postData.Id;
                }

                this.disabledBtn = true
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = afterService.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = afterService.edit(postData);
                }

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },
        getDetail() {
            this.loading = true
            afterService.detail({
                id: this.id
            }).then(res => {
                this.loading = false
                this.isTip = false
                this.formData = Object.assign({}, this.formData, res);
                this.formData.Range = [this.formData.StartTime, this.formData.EndTime]
                this.formData.List = this.formData.ReturnVisitsDetailList.map(s => {
                    return {
                        Id: s.PlanId,
                        Code: s.Number,
                        OrderNumber: s.Number,

                        RegionalName: s.RegionalName,
                        ServiceNo: s.ServiceNo,
                        ReportTime: s.ReportTime,
                        TotalPrice: s.TotalPrice,
                        IsReplacePart: s.IsReplacePart,
                        HandlerEmployeeList: s.HandlerEmployeeList
                    }
                })
                this.listQueryOrder.ReturnVisitsOrderNumber = [];
                this.formData.List.forEach((item) => this.listQueryOrder.ReturnVisitsOrderNumber.push(item.OrderNumber))
            }).catch(err => {
                this.loading = false
            });
        },
        // handleRemove(idx) {
        //     this.formData.List.splice(idx, 1)
        // },
        handleRemoveList() {
            if(this.multipleSelection.length == 0) {
                this.$message({
                    message: '请选择需要删除的项',
                    type: 'error'
                })
                return false
            }

            this.$confirm('是否确认删除？', "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let checkeds = this.multipleSelection.map(s => s.Code)
                for (let i = this.formData.List.length - 1; i >= 0; i--) {
                    if(checkeds.find(s => s == this.formData.List[i].Code)) {
                        this.formData.List.splice(i, 1);
                    }
                }
                this.multipleSelection = []

                this.$notify({
                    title: "提示",
                    message: "删除成功",
                    type: "success",
                    duration: 2000
                });
            })
        },
        listSelectorSaveSuccess(data) {
            if (this.formData.Type == 1) {
                this.formData.List = data.map(s => {
                    s.Code = s.OrderNumber
                    return s
                })
            } else {
                this.formData.List = data
            }

            this.listSelectorCloseDialog()
        },
        listSelectorCloseDialog() {
            this.onResetSearch()
            this.listSelectorDialogFormVisible = false
        },
        renderHeader(h, {
            column
        }) {
            return h("span", this.formData.Type == 1 ? '订单号' : '报修单号');
        },
        handleChangeOwnerUsers(users) {
            this.formData.EmployeeList = users
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}
</style>