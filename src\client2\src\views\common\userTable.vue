<template>
  <div v-if="visible">
    <div class="conditionArea-wrap clearfix">
      <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
        <template slot='Name'>
          <el-input style="width: 100%;" v-model="listQuery.Name" placeholder=""></el-input>
        </template>
        <template slot='phone'>
          <el-input style="width: 100%;" v-model="listQuery.phone" placeholder=""></el-input>
        </template>
        <template slot="other-btns">
          <el-button type="success" @click="handleSave">确认</el-button>
        </template>
      </app-table-form>
    </div>
    <el-table fit :data="tabDatas" style="width: 100%" v-loading="listLoading" @selection-change='rowSelectionChanged' ref="mainTable2" :highlight-current-row='!multiple' @current-change='currentChanged' max-height="500">
      <el-table-column type="selection" width="55" v-if="multiple"></el-table-column>
      <el-table-column type="index" :index="indexMethod" label="编号">
        <!-- <template slot="header" slot-scope="scope">编号</template> -->
      </el-table-column>
      <el-table-column prop="Name" label="姓名" width="120"></el-table-column>
      <el-table-column prop="Gender" label="性别" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.Gender == 1" type="success">男</el-tag>
          <el-tag v-if="scope.row.Gender == 2" type="info">女</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="Phone" label="手机" width="140"></el-table-column>
      <el-table-column prop="Tel" label="电话" width="120"></el-table-column>
      <el-table-column prop="Email" label="邮箱"></el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />

  </div>
</template>

<script>
import indexPageMixin from '@/mixins/indexPage'
export default {
  name: 'user-list',
  mixins: [indexPageMixin],
  props: {
    existsUsers: {
      type: Array,
      default: () => {
        return []
      }
    },
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    condition: {
      type: Object,
      default: null
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.checkedUsers = JSON.parse(JSON.stringify(this.existsUsers))
          if (this.condition) {
            this.listQuery = Object.assign({}, this.listQuery, this.condition)
          }
          this.getList()
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      checkedUsers: [],
      // userSelectorDlg: false,
      tableSearchItems: [
        { prop: 'Name', label: '姓名' },
        { prop: 'phone', label: '手机' },
      ],
      multipleSelection: [], // 列表checkbox选中的值
      total: 0,
      listLoading: false,
      listQuery: { // 查询条件
        Name: '',
        phone: '',

      },
      tabKey: 0,
      tabDatas: [],
    }
  },

  methods: {
    onResetSearch() {
      this.listQuery = {
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize
      };
      if (this.condition) {
        this.listQuery = Object.assign({}, this.listQuery, this.condition)
      }
    },
    currentChanged(currentRow, oldCurrentRow) {
      if (!this.multiple && currentRow) { //某页选中一个用户后，点击翻页会触发该事件，所以要加该判断
        this.multipleSelection = [currentRow]
        this.checkedUsers = [currentRow]
      }
    },
    //当表格行中所有 checkbox 选中状态项改变时（返回所有选中的行数据）
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
      this.tabDatas.forEach(r => {
        let idx = this.checkedUsers.findIndex(u => u.UserId == r.UserId)
        if (idx > -1) {
          this.checkedUsers.splice(idx, 1)
        }
      })

      if (rows && rows.length > 0) {
        rows.forEach(r => {
          if (!this.checkedUsers.some(s => s == r.UserId)) {
            this.checkedUsers.push(r)
          }
        })
      }
    },
    getList() {
      this.listLoading = true
      // users.getList222(this.listQuery).then(response => {
      //     this.tabDatas = response.Items
      //     this.total = response.Total
      //     this.listLoading = false
      //     this.setCheckedusers()
      // })
    },
    handleFilter() {
      this.listQuery.PageIndex = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page
      this.listQuery.PageSize = val.size

      this.getList()
    },
    setCheckedusers() {

      if (!this.multiple) { //单选
        if (this.checkedUsers.length > 0) {
          let checkUser = this.tabDatas.find(p => p.UserId == this.checkedUsers[0].UserId)
          checkUser && this.$refs.mainTable2.setCurrentRow(checkUser);
        }
      } else { //多选

        if (this.checkedUsers.length == 0) {
          this.$refs.mainTable2.clearSelection();
        } else {

          let checkedUsers = this.tabDatas.filter(s => this.checkedUsers.map(u => u.UserId).some(o => o == s.UserId)) || []
          checkedUsers.forEach(u => {
            this.$nextTick(() => {
              if (this.$refs.mainTable2) {
                this.$refs.mainTable2.toggleRowSelection(u)
              }
            })
          })
        }
      }
    },
    handleSave() {
      this.$emit('changed', this.checkedUsers)
      // this.userSelectorDlg = false

    },

    indexMethod(index) {
      return (index += 1) + this.listQuery.PageSize * (this.listQuery.PageIndex - 1)
    },
  }
}
</script>


<style scoped>
.title,
.sub-title {
  margin-bottom: 10px;
}

.sub-title {
  display: flex;
  line-height: 28px;
}

.sub-title-info {
  flex: 1;
}

.sub-title-btns {
  margin-right: 10px;
}

.tab-users {
  max-height: 480px;
  padding: 10px;
  overflow-y: scroll;
}

.tab-item-wrap {
  float: left;
  width: 20%;
  padding: 5px;
  position: relative;
}

.tab-item {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  height: 80px;
  width: 100%;
  border: 1px solid #dcdfe6;
  padding: 10px;
}

.opt {
  cursor: pointer;
  font-size: 28px;
  color: #dcdfe6;
  text-align: center;
  line-height: 80px;
  vertical-align: middle;
  padding: 0;
}

.conditionArea-wrap {
  padding: 0 10px;
}

.btns-area {
  text-align: left;
  padding: 10px 0;
  padding-right: 10px;
}

.file-btn-del {
  background: transparent;
  display: block;
  cursor: pointer;
  position: absolute;
  font-size: 18px;
  top: -3px;
  right: -3px;
}

.file-btn-del:hover {
  transition: all 0.3s;
  color: #f56c6c;
}

.color-danger,
.color-danger:hover {
  color: #f56c6c;
}
</style>