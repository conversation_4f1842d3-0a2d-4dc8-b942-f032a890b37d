<template style="width: 103%;margin-left: -13px;">
<el-container class="seleEmpCon">
    <div class="lft">
        <div class="tags-wrapper">
            <tags :items="types" v-model="TableType">  
                <template v-for="(t,  idx) in types" :slot="t.value">
                    <span :class="t.value == 1 ? 'paddingRight' : 'paddingRight'" :key="idx">
                        {{ t.label }}    
                    </span>             
                </template>
            </tags>
        </div>
        <!-- <el-container class="treeCon" style="height: calc(100% - 36px);">
        </el-container> -->
        <div class="lft-content" v-show='TableType=="1"' style="display: flex; align-items: center; justify-content: center;">已列出近期常用联系人列表</div>

        <div class="lft-content" v-show='TableType=="2"'>
            <!--搜索 -->
            <div class="input-wrapper">
                <el-input placeholder="请输入员工姓名或工号" v-model='listQuery.Name' @input="valueChange($event)"></el-input>
                <el-button type="primary" class="el-button el-button--primary el-button--mini" style="margin-left: 5px;" @click="query">查询</el-button>
            </div>
            <!-- <el-header class="treehead">
            </el-header> -->
            <!-- <el-main class="treeMain">
            </el-main> -->
            <!--部门树 -->
            <div>
                <el-tree v-show="treeHide==false" class="tree" :data="orgsTree" show-checkbox node-key="Id" @check='checkOrg' :default-expanded-keys="defaultExpandedKey" :props="defaultProps" ref="orgsTree">
                </el-tree>
                <span v-show="treeHide==true" class="tag-group__title treeTips">已为您查询</span>
            </div>
        </div>
        <!-- <el-container class="treeCon" style="height: 93%;">
        </el-container> -->
    </div>

    <el-container class="empCon" style="width: 81%;">
        <!--人员table -->
        <el-main class="empMain">
            <el-table class="empTab" row-class-name="cur-row-class" height="550" border fit :data="tabDatas" name="CommonEmpTab" @select-all="selectAll" @select="select" @row-click="handleRowClick" v-loading="listLoading" ref="mainTable2">
                <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable">
                </el-table-column>
                <el-table-column prop="Name" label="姓名" width="103" align="center"></el-table-column>
                <el-table-column prop="Number" label="工号" width="80" align="center"></el-table-column>
                <el-table-column prop="Sex" label="性别" width="60" align="center">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.Sex == 1" type="success">男</el-tag>
                        <el-tag v-if="scope.row.Sex == 2" type="info">女</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="Mobile" label="手机" align='center'></el-table-column>
                <el-table-column prop="DepartmentName" label="部门" align="center" show-overflow-tooltip></el-table-column>
                <el-table-column prop="JobName" label="职位" align="center" show-overflow-tooltip></el-table-column>
            </el-table>
            <!-- <el-drawer
                title="我是标题" :append-to-body="false" :modal="false" :modal-append-to-body="false" wrapperClosable
                :visible.sync="drawerOpen"
                :with-header="false">
                <span>我来啦!</span>
            </el-drawer> -->
            <div class="drawerBox_mode" @click.self="drawerOpen=false" :class="drawerOpen?'show':'hidden'">
                <el-row class="drawerBox">
                    <div class="drawerBox_openBtn" @click="drawerOpen=!drawerOpen">
                        已选<div class="drawerBox_openBtn_tags">{{checkedUsers.length>99?'99+':checkedUsers.length}}</div>
                    </div>
                    <div class="drawerBox_title">
                        <div class="drawerBox_title_wrapper">
                            <span class="el-icon-circle-check"></span>
                            已选中（{{checkedUsers.length}}）
                        </div>
                        <el-button type="text" @click="delDrawer(false)">清空所有</el-button>
                    </div>
                    <el-table max-height="515" border fit :data="checkedUsers">
                        <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                        <el-table-column prop="Name" label="姓名"></el-table-column>
                        <el-table-column label="操作" width="60" align="center">
                            <template slot-scope="scope">
                                <el-button type="text" class="drawer_delIcon" :disabled="!!disabledList.find(s => s == scope.row.EmployeeId)" @click="delDrawer(scope.$index,scope.row)">
                                    <span class="el-icon-circle-close" title="移除"></span>
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>
            </div>
        </el-main>
        <!-- <el-footer class="empFoot">
            确认返回数据给父组件
            <div class="btns-wrapper">
                <el-button class="empSub" type="primary">确认</el-button>
                <el-button class="empSub" type="primary" @click="getCheckRow">确认</el-button>
            </div>
        </el-footer> -->
    </el-container>
</el-container>
</template>

<script>
import {
    listToTreeSelect
} from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as systemEmployee from '@/api/personnelManagement/systemEmployee'
import * as systemTopContact from '@/api/personnelManagement/systemTopContact'
export default {
    name: 'emp-table',
    props: {
        existsUsers: { //父级组件传入和当前已选人员
            type: Array,
            default: () => {
                return []
            }
        },
        multiple: { //父级附件设置:multiple="false" ，即可设置为单选
            type: Boolean,
            default: true
        },
        visible: { //当前组件展示与否
            type: Boolean,
            default: false
        },
        condition: {
            type: Object,
            default: null
        },
        disabledList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    created() {
        var _this = this; //暂时没有找到更好的办法来判断展示单选提示和全选按钮，如果有更好的办法请联系曹思平，谢谢。
        _this.$nextTick(function () { //初次打开组件在渲染结束后初始化
            $('div[name="CommonEmpTab"] th .el-checkbox').parent().attr('name', 'checkboxDiv'); //给全选框上级元素增加标识
            _this.elCheckbox = $('div[name="CommonEmpTab"] th .el-checkbox'); //默认加载全选框，将全选框存入变量重复加载时根据选择模式进行dom操作
            if (!_this.multiple)
                $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //将全选框改成单选提示
        })
    },
    watch: {
        visible: { //展示当前组件刷新列表并根据父组件传入的已选人员进行页面选中
            handler() {
                var _this = this;
                if (_this.multiple) //展示当前组件后判断单选和多选模式
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html(_this.elCheckbox); //将全选框放入table表头
                else
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //修改单选框提示
                _this.checkedUsers = JSON.parse(JSON.stringify(_this.existsUsers)) || [];
                _this.getList();
            },
            immediate: true
        },
        "TableType"() {
            this.getList();
        },
    },
    data() {
        return {
            drawerOpen: false,
            types: [{
                    value: 1,
                    label: "常用联系人",
                },
                {
                    value: 2,
                    label: "组织结构",
                }
            ],
            TableType: "1",
            elCheckbox: null,
            checkedUsers: JSON.parse(JSON.stringify(this.existsUsers)) || [],
            treeHide: false, //是否显示部门树结构
            orgsTree: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'label'
            },
            defaultExpandedKey: [], //部门树默认展开节点
            employeeIds: [], //员工IS
            tabDatas: [], //员工列表
            listQuery: { // 查询条件
                Name: '',
                OrgIds: []
            },
            listLoading: false
        }
    },
    methods: {
        setCheckRow() { //根据父级传入的已选人员设置选中
           var _this = this;
            if (_this.checkedUsers && _this.checkedUsers.length > 0) {
                var checkedUsers = _this.tabDatas.filter(s => _this.checkedUsers.map(u => u.EmployeeId).some(o => o == s.EmployeeId)) || []
                checkedUsers.forEach(u => {
                    _this.$nextTick(() => {
                        if (_this.$refs.mainTable2)
                            _this.$refs.mainTable2.toggleRowSelection(u);
                    })
                })
            } else {
                _this.$refs.mainTable2.clearSelection();
            }
        },
        // 删除已选中的
        delDrawer(index, row){
            if (index!==false) {
                let item = this.tabDatas.find(s=>s.EmployeeId==row.EmployeeId);
                this.$refs.mainTable2.toggleRowSelection(item);
                this.checkedUsers.splice(index,1)
            } else {
                this.$confirm('确定要清空所有已选中的人员吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // this.$refs.mainTable2.clearSelection()

                    let emps = this.tabDatas.filter(s => this.checkSelectable(s) && this.checkedUsers.find(e => e.EmployeeId == s.EmployeeId))
                    emps.forEach(emp => {
                        this.$refs.mainTable2.toggleRowSelection(emp)
                    })

                    this.checkedUsers = this.checkedUsers.filter(s => !this.checkSelectable(s))
                })
            }
            this.$forceUpdate()
        },
        getCheckRow() { //返回已选中的人员给父级组件

            var _this = this;
            var tmpCheckRow = _this.$refs.mainTable2.selection.filter(s => !!s); //获取当前已选择人数
            if (tmpCheckRow.length > _this.checkedUsers.length) { //全选时会出现当前已选中人数大于 _this.checkedUsers中记录的人数，进行同步
                tmpCheckRow = tmpCheckRow.filter(o => _this.checkedUsers.map(m => m.EmployeeId).every(employeeId => employeeId !== o.EmployeeId));
                tmpCheckRow.forEach(m => {
                    _this.checkedUsers.push(m);
                })
            }
            _this.$emit('changed', _this.checkedUsers);
        },
        valueChange(e) { //查询框值改变
            var _this = this;
            if (e == '' && _this.treeHide) { //如查询框清空内容
                _this.treeHide = false; //展示树形菜单
                _this.getList(); //刷新人员列表  
            }
        },
        selectHandel(row) { //选择框处理
            var _this = this;
            var tmpRow = _this.checkedUsers.some(m => m.EmployeeId == row.EmployeeId);
            if (tmpRow) { //选中的数据是否已包含在_this.checkedUsers内，包含则移除，不包含则添加
                _this.checkedUsers = _this.checkedUsers.filter(m => m.EmployeeId != row.EmployeeId);
            } else {
                _this.checkedUsers.push(row);
            }
        },
        selectAll(rows) { //全选框事件
            var _this = this;
            let oldSelectedList = JSON.parse(JSON.stringify(this.checkedUsers)) //已选中的
            // 已选中的，且不在当前列表中（不管怎么操作都不会被影响）
            oldSelectedList = oldSelectedList.filter(s => this.tabDatas.findIndex(n => n.EmployeeId == s.EmployeeId) == -1)
            // 合并上当前选中的
            oldSelectedList = oldSelectedList.concat(rows)
            //最后结果
            this.checkedUsers = oldSelectedList

            // _this.tabDatas.forEach(row => {//循环当前table数据做选择框事件相应处理
            //   if(this.disabledList.findIndex(s => s == row.EmployeeId) == -1) {
            //     _this.selectHandel(row);
            //   }
            // });
        },
        // handleRowClick(row, column, event) {
        //     debugger
        //     debugger
        //     this.select(null, row)

        // },

        //单击表格行选中
        handleRowClick(row, column, event) {
            let _this = this
            let refsElTable = _this.$refs.mainTable2; // 获取表格对象
            let findRow = _this.checkedUsers.find(c => c.EmployeeId == row.EmployeeId);  //找到选中的行
            if (findRow) {
                refsElTable.toggleRowSelection(row, false);  //如过重复选中，则取消选中
                _this.select(null, row)
                return;
            }
            refsElTable.toggleRowSelection(row, true); // 实现选中行中选中事件
            _this.select(null, row)
        },

        select(selection, row) { //单选框事件
            var _this = this;
            if (!_this.multiple) {
                //单选处理
                _this.$refs.mainTable2.clearSelection();
                _this.$refs.mainTable2.toggleRowSelection(row, true);
                _this.checkedUsers = [row];
            } else {
                //多选处理
                _this.selectHandel(row);
            }
        },
        getOrgTree() { //查询并加载部门树
            var _this = this;
            systemDepartment.getListByCondition({}).then(response => { //调用公共组件API获取部门数据
                _this.list = response.map(function (item) {
                    return {
                        Id: item.Id,
                        label: item.DepartmentName,
                        ParentId: item.ParentId
                    }
                })
                var orgstmp = JSON.parse(JSON.stringify(_this.list));
                var tempOrgsTree = listToTreeSelect(orgstmp); //将部门数据转换成树形结构
                _this.defaultExpandedKey.push(tempOrgsTree[0]['Id']); //设置默认展开
                _this.orgsTree = tempOrgsTree;
            })
        },
        query() { //查询按钮单击事件
            var _this = this;
            if (this.listQuery.Name) {
                _this.treeHide = true; //隐藏部门树
                _this.listQuery.OrgIds = []; //查询按钮只根据输入框条件进行查询员工，清空当前已选中部门
                _this.$refs.orgsTree.setCheckedKeys([]);
                _this.getList();
            }
        },
        checkOrg() { //部门复选框发生变化触发事件
            var _this = this;
            _this.listQuery.OrgIds = []; //清空之前已选部门
            _this.$refs.orgsTree.getCheckedNodes().forEach((item) => _this.listQuery.OrgIds.push(item.Id)); //循环当前已选部门并将部门ID添加到_this.listQuery.OrgIds
            _this.getList();
        },
        getList() {
            var _this = this;
            _this.listLoading = true;
            var result = null;
            
            if (_this.TableType == "1") {
                let newlist = [];
                //常用联系人      
                result = systemTopContact.getListByCondition({
                    SortNo: 0,
                });
                result.then(response => {                    
                    response.Items.forEach((item) => newlist.push({...item.SystemEmployeeEdo,...{EmployeeLevelType:item.Employee.EmployeeLevelType}}))
                    _this.tabDatas = newlist.map(s => {
                        let temp = s.DepartmentName
                        if(temp.indexOf('/') > -1) {
                            temp = temp.substring(temp.lastIndexOf('/') + 1)
                        }
                        s.DepartmentName = temp
                        return s
                    });
                    _this.listLoading = false;
                    _this.setCheckRow(); //根据已选人员设置tableRow默认选中
                })
            }
            if (_this.TableType == "2") {
                if (_this.listQuery.OrgIds.length > 0 || !!_this.listQuery.Name) {
                    let postData = JSON.parse(JSON.stringify(_this.listQuery))
                    if (this.condition) {
                        postData = Object.assign({}, postData, this.condition)
                    }
                    result = systemEmployee.getAllEmployees(postData); //根据勾选部门或者输入框调用公共组件API获取人员
                    result.then(response => {
                        _this.tabDatas = response;
                        _this.listLoading = false;
                        _this.setCheckRow(); //根据已选人员设置tableRow默认选中
                    })
                } else {
                    _this.tabDatas = [];
                    _this.listLoading = false;
                }
            }
        },
        checkSelectable(row) {
            if (this.disabledList && this.disabledList.length > 0) {
                return this.disabledList.findIndex(s => s == row.EmployeeId) == -1
            }
            return true
        },
    },
    mounted() {
        this.getOrgTree();
    }
}
</script>

<style lang="scss" scoped>
/* .treehead>>>.el-input-group__append {
    background: red;
    color: #fff;
} */

// .input-wrapper>>>.el-input__inner {
//     border-radius: 4px 0 0 4px;
// }

.seleEmpCon {
    height: 550px;
    border: 1px solid $border-color-light;
    .lft{
        width: 36%;
        display: flex;
        flex-direction: column;
        border-right: 1px solid $border-color-light;
        .tags-wrapper{
            border-bottom: 1px solid $border-color-light;
            /deep/.list-wrapper{
                display: flex;
                padding: 0 20px;
                .tag-common{
                    flex: 1;
                    text-align: center;
                    padding-top: 11px;
                    padding-bottom: 10px;
                }
            }
        }
        
        .lft-content{
            flex: 1;
            .input-wrapper{
                display: flex;
                padding: 10px;
                border-bottom: 1px solid #ebeef5;
                >input{
                    flex: 1;
                }
            }
        }
    }
}

.treeCon {
    border-right: 1px solid #ebeef5;
}

/* .treehead {
    height: 40px !important;
    padding-top: 5px;
}

.treehead .input-wrapper {
    width: 100%;
    position: relative;
    padding-right: 56px;
} */

// .input-wrapper .search-btn {
//     position: absolute;
//     top: 0;
//     right: 0;
//     border-radius: 0 4px 4px 0;
// }

.treeName {
    display: inline-block;
    width: 153px;
}

/* .treeMain {
    padding: 0px !important;
} */

.tree {
    height: 450px;
    overflow-y: auto;
}

.treeTips {
    text-align: center;
    display: block;
    margin-top: 5px;
}

.empCon{
    display: flex;
    flex-direction: column;
}

.empMain {
    /* padding-bottom: 0px; */
    width: 100%;
    padding: 0;
    flex: 1;
    overflow: hidden;
}

.empTab {
    width: 100%;
    border: none;
}
.empFoot {
    margin-top: 10px;
    height: 30px !important;
}

.btns-wrapper{
    text-align: right;
}

/* 

.empSub {
    float: right;
} */

.paddingRight{
    padding: 20px;
}

/* .paddingLeft{
    padding: 30px;
} */
</style>
<style lang="scss" scoped>
.seleEmpCon{
    position: relative;
    overflow-x: hidden;
    .drawerBox_mode{
        position: absolute;
        width: 0;
        height: 100%;
        right: 0;
        top: 0;
        z-index: 1;
        &.show{
            width: 100%;
            .drawerBox{
                right: 0;
                &_openBtn{
                    background: rgba(242, 242, 242, 1);
                }
            }
        }
        &.hidden{
            width: 0;
            .drawerBox{
                right: -30%;
                &_openBtn{
                    background: rgba(242, 242, 242, 0.7);
                }
            }
        }
        .drawerBox{
            position: absolute;
            width: 30%;
            top: 0;
            bottom: 0;
            right: -30%;
            height: 100%;
            box-sizing: border-box;
            background-color: #FFF;
            display: flex;
            flex-direction: column;
            transition: right .3s;
            box-shadow: 0 8px 10px -5px #00000033, 0 16px 24px 2px #00000024, 0 6px 30px 5px #0000001f;
            .drawer_delIcon{
                padding: 0;
                vertical-align: middle;
                .el-icon-circle-close{
                    font-size: 18px;
                    color: $color-danger;
                }
            }
            &_openBtn{
                width: 35px;
                padding: 10px 5px;
                position: absolute;
                left: -35px;
                top: 50%;
                transform: translateY(-50%);
                background: rgba(242, 242, 242, 0.7);
                color: #409EFF;
                text-align: center;
                cursor: pointer;
                border-bottom-left-radius: 5px;
                border-top-left-radius: 5px;
                &_tags{
                    width: 25px;
                    height: 25px;
                    overflow: hidden;
                    border-radius: 50%;
                    background-color: #409EFF;
                    color: #FFF;
                    line-height: 25px;
                    margin-top: 5px;
                    font-size: 12px;
                }
            }
            &_title{
                display: flex;
                height: 35px;
                line-height: 35px;
                padding: 0 10px;
                &_wrapper{
                    flex: 1;
                }
            }
        }
    }
}

.is-disabled{
    .el-icon-circle-close{
        color: rgba($color: $color-danger, $alpha: .5)!important;
    }
}


</style>