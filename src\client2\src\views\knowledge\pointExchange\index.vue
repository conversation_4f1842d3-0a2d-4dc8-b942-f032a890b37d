<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title :title="pageTitle" :subTitle="[pageDesc]" :showBackBtn="!!returnUrl" @goBack="handleGoBack"></page-title> -->
        <div class="page-wrapper">
            <div class="product-list">
                <el-button type="primary" v-if="hasTreeOpertAuth" style="width: 180px; margin-top: 10px;margin-left:35px;" @click="addTopLevel">创建兑换分类</el-button>
                <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label">{{ node.label }}</span>
                            <span v-if="data.Level>0 && hasTreeOpertAuth && data.Id != '00000000-0000-0000-0000-000000000000'" class="node-btn-area">
                                <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" :optColWidth="160"
                    @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable"
                    :multable="true" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                        
                        <template slot="LogoPath" slot-scope="scope">
                            <img :src="scope.row.LogoPath" width="45px" height="45px" style="padding-top: 8px;" />
                        </template>
                        <template slot="PrizeStatus" slot-scope="scope">
                            <span class="item-status" :style="{background: getPrizeStatuObj(scope.row.PrizeStatus).bgColor, color: getPrizeStatuObj(scope.row.PrizeStatus).color}">
                                {{ getPrizeStatuObj(scope.row.PrizeStatus).label }}
                            </span>
                        </template>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索商品名称/商品编号"
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                                        <template slot="btnBatchOpt" slot-scope="scope">
                                            <el-dropdown style="margin-left: 4px; height: 28px;" @command="handleSelect">
                                                <el-button type="primary">
                                                    批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                                                </el-button>
                                                <el-dropdown-menu slot="dropdown">
                                                    <el-dropdown-item command='handleChangeType'>调整标签分类</el-dropdown-item>
                                                    <el-dropdown-item command='handleUp'>批量上架</el-dropdown-item>
                                                    <el-dropdown-item command='handleDown'>批量下架</el-dropdown-item>
                                                </el-dropdown-menu>
                                            </el-dropdown>
                                        </template>
                                    </permission-btn>
                                </template>

                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleDialog('detail', scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button @click="handleRecored(scope.row)" text='兑换记录' :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    
    <customerServiceDepartmentEdit v-if="customerServiceDepartmentEditDialogFormVisible" :dialogStatus="customerServiceDepartmentEditDialogStatus" :node="paramNode" :dialogFormVisible="customerServiceDepartmentEditDialogFormVisible" @closeDialog="customerServiceDepartmentEditCloseDialog" @saveSuccess="customerServiceDepartmentEditSaveSuccess"></customerServiceDepartmentEdit>
    
    <create-page :selectTypeId='checkedNode.Id' v-if="dialogFormVisible" @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList"></create-page>

    <!-- 奖品类型调整 -->
    <typeChange
        v-if="dialogTagTypeChangeVisible"
        @closeDialog="closeTagTypeChangeDialog"
        :dialogFormVisible="dialogTagTypeChangeVisible"
        @saveSuccess="handleTagTypeChangeSuccess"
        :ids='multipleSelection.map(s => s.Id) || []'
    ></typeChange>

    <!-- 积分兑换审批设置 -->
    <approvalSetting
        v-if="dialogApprovalSettingStatus"
        @closeDialog='closeApprovalSettingDialog' 
        @saveSuccess='handleApprovalSettingSaveSuccess'
        :dialogFormVisible='dialogApprovalSettingFormVisible'
        :dialogStatus='dialogApprovalSettingStatus' 
    >
    </approvalSetting>
    <!-- 兑换记录 -->
    <exchange-records v-if="exchangeRecordsVisible" :redeemPrizeId='id' @closeDialog="exchangeRecordsVisible=false" :dialogFormVisible="exchangeRecordsVisible" />
</div>
</template>

<script>
// import pageTitle from '../common/pageTitle'
// import tags from '../common/tags'
import indexPageMixin from "@/mixins/indexPage";
import * as pointExchange from "@/api/knowledge/pointExchange";
import * as classify from '@/api/classify'
import createPage from "./create";
import typeChange from "./typeChange";
import approvalSetting from './approvalSetting'
import {
    vars
} from "../common/vars";
import {
    getUserInfo
} from "@/utils/auth";
import layoutVue from "../../login/layout.vue";
import { listToTreeSelect } from "@/utils";
import customerServiceDepartmentEdit from "./departmentEdit";
import { prizeStatus } from './enum'
import exchangeRecords from "./exchangeRecords";

export default {
    name: "",
    mixins: [indexPageMixin],
    components: {
        // pageTitle,
        // tags,
        createPage,
        typeChange,
        customerServiceDepartmentEdit,
        approvalSetting,
        exchangeRecords,
    },
    computed: {
        hasTreeOpertAuth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnMaintain') > -1
        },
    },
    created() {
        // this.getSalesman()
        this.loadTreeData();
        this.getList();

    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.ClassifyId = val.Id;
                    
                    this.getList();
                }
            },
        },
    },
    filters: {
        valFilter(val) {
            if (val) {
                return val;
            }
            return "无";
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "无";
        },

    },
    mounted() {},
    data() {
        return {
            layoutMode: 'simple',

            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
            /**树节点添加弹窗 */
            customerServiceDepartmentEditDialogFormVisible: false,
            customerServiceDepartmentEditDialogStatus: "create",

            

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,
            total: 0,
            listLoading: false,
            listQuery: {
                Keywords: '',
                ClassifyId: null,
            },
            multipleSelection: [],
            tableSearchItems: [
                {
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },
            ],
            tabColumns: [
                {
                    attr: {
                        prop: "LogoPath",
                        label: "奖品封面",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "Name",
                        label: "标签名称",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Code",
                        label: "奖品编号",
                    },
                },
                {
                    attr: {
                        prop: "PrizeStatus",
                        label: "状态",
                        sortable: 'custom'
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "PointsExchangesValue",
                        label: "兑换积分",
                        sortable: 'custom'
                    },
                },
                {
                    attr: {
                        prop: "Inventory",
                        label: "剩余库存",
                        sortable: 'custom'
                    },
                },
            ],
            tabDatas: [],
            currentProduct: null,

            // // salesman: [],
            // tagType: 0, //标签类型
            // dialogTagTypeFormVisible: false,

            // 奖品类型调整
            dialogTagTypeChangeVisible: false,

            //积分兑换弹框
            dialogApprovalSettingFormVisible: false,
            dialogApprovalSettingStatus: 'update',

            //兑换记录
            exchangeRecordsVisible: false,

        };
    },
    methods: {
        getPrizeStatuObj(val) {
            return prizeStatus.find(s => s.value == val) || {}
        },
        handleBeforeOpen() {
            this.$refs.tagSelectorRef.pers = []
            return true
        },
        /**选择标签 */
        handleTagChange(list) {
            // if(list && list.length > 0) {
            //     let postDatas = list.map(s => s.Id)
            //     pointExchange.executeFactTag(postDatas).then(res => {
            //         this.$notify({
            //             title: "提示",
            //             message: "执行成功",
            //             type: "success",
            //             duration: 2000
            //         });
            //         this.getList()
            //     })
            // }
        },
        handleSelect(cmd) {
            if (this.multipleSelection.length <= 0) {
                this.$message({
                    message: '请选择需要操作的行',
                    type: 'error'
                })
                return
            }
            switch(cmd) {
                case "handleChangeType":
                    this.dialogTagTypeChangeVisible = true
                    break;
                case "handleUp":
                    this.modifyStatus('up')
                    break;
                case "handleDown":
                    this.modifyStatus('down')
                    break;
            }
        },
        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId != 'btnMaintain')
            }
            return []
        },
        resetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize,
                ClassifyId: this.listQuery.ClassifyId,
                Keywords: "",
            };
            this.getList();
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let _this = this;
            let paramData = {
                BusinessType: 11,
            };
            _this.treeLoading = true
            classify.getListPage(paramData).then(res => {
                _this.treeLoading = false

                let response = res.Items
                response.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });
                _this.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

                if (_this.treeData && _this.treeData.length > 0) {
                    if (
                        !(
                            _this.checkedNode &&
                            response.find(t => {
                                return t.Id == _this.checkedNode.Id;
                            })
                        )
                    ) {
                        _this.checkedNode = _this.treeData[0];
                    }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        }
                    });
                }
            }).catch(err => {
                _this.treeLoading = false
            });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.customerServiceDepartmentEditDialogStatus = "create";
            this.customerServiceDepartmentEditDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "create";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "update";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "delete":
                    this.handleDeleteDepartment(data);
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteDepartment(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                customerTagClassify
                    .del([data.Id])
                    .then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }
                        this.loadTreeData();
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
            });
        },
        /**地区弹窗保存成功 */
        customerServiceDepartmentEditSaveSuccess(isContinue) {
            if(isContinue !== true) {
                this.customerServiceDepartmentEditCloseDialog();
            }
            this.loadTreeData();
        },
        /**地区弹窗关闭 */
        customerServiceDepartmentEditCloseDialog() {
            this.customerServiceDepartmentEditDialogFormVisible = false;
        },



        handleSuccessExport() {},

        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                case "btnApproval":
                    this.handleApprovalSettingDialog();
                    break;
                default:
                    break;
            }
        },
        

        // closeTagTypeDialog() {
        //     this.dialogTagTypeFormVisible = false
        // },
        // handleTagTypeSaveSuccess() {
        //     this.closeTagTypeDialog()
        //     this.handleDialog("create");
        // },
        handleDialog(activeName, row) {
            if(row) {
                this.id = row.Id
            }
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData, isContinue) {
            // this.listQuery.PageIndex = 1

            this.getList();
            if(!isContinue) {
                this.closeDialog();
            }
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        // handleNav(type, proj) {
        //   this.$router.push({
        //     path: `/projectDev/projectMgmt/${type}/index?productId=${proj.productId}&projectId=${proj.projectId}`
        //   });
        // },
        handleUpdate(row, optType = "update") {
            // 弹出编辑框
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        // handleResetSearch() {
        //     this.listQuery = {
        //         // 否则手动重置查询条件
        //         PageIndex: this.listQuery.PageIndex,
        //         PageSize: this.listQuery.PageSize,
        //         Name: "",
        //     };
        //     this.getList(); //刷新列表
        // },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        //获取项目列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            pointExchange.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            });
        },
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                pointExchange.del([row.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            });
        },
        closeTagTypeChangeDialog() {
            this.dialogTagTypeChangeVisible = false
        },
        handleTagTypeChangeSuccess() {
            this.getList()
            this.closeTagTypeChangeDialog()
        },
        
        modifyStatus(direc) {
            let tip = direc == 'up' ? '上架' : '下架'
            this.$confirm("是否批量" + tip, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let postDatas = {
                    PrizeStatus: direc == 'up' ? 1 : 2,
                    IdList: this.multipleSelection.map(s => s.Id)
                }

                pointExchange.modifyStatus(postDatas).then(res => {
                    this.getList()
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                })

            });
        },
        handleApprovalSettingDialog() {
            this.dialogApprovalSettingFormVisible = true
        },
        closeApprovalSettingDialog() {
            this.dialogApprovalSettingFormVisible = false
        },
        handleApprovalSettingSaveSuccess() {
            this.closeApprovalSettingDialog()
        },
        handleRecored(row) {
            if(row && row.Id) {
                this.id = row.Id
                this.exchangeRecordsVisible = true
            }
        },
    },
};
</script>

<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            // top: 40px;
            top: 0;
            right: 0;
            bottom: 0;

            .product-list {
                width: 250px;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }

                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        height: 100%;
                        overflow: auto;
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 200px);
                flex: 1;
                overflow-y: auto;

                .content {

                    // padding: 10px;
                    // padding-right: 0;
                    .opt-wrapper {
                        box-sizing: border-box;
                        border-bottom: 1px solid #dcdfe6;
                        padding-bottom: 10px;
                    }

                    .list {}
                }
            }
        }
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}

.statisticsDivClass {
    width: 100%;
    height: 75px;
    border-radius: 8px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
    cursor: pointer;

    .statisticsChildDiv {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px 0 0 8px;
        width: 26%;
        height: 75px;
        background-color: rgba(255, 108, 96, 1);
        float: left;
    }

    .statisticsChildDiv2 {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 0 8px 8px 0;
        width: 74%;
        height: 75px;
        background-color: rgba(255, 255, 255, 1);
        float: left;

        .label1 {
            color: #999999;
            margin: 0;
            margin-top: 12px;
            font-weight: 500 !important;
        }

        div {
            margin-top: 5px;
        }

        .label2 {
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            font-size: 20px;
            color: $text-second-color;
            margin: 0;
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

.statisticsDivClass:hover {
    transform: translate(-3px, -3px);
    box-shadow: 0px 0px 3px 0px #dcdfe6;
}


.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
