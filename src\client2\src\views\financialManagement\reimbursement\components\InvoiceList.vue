<template>
  <!-- 发票列表(包含上传和识别发票文字) -->
  <div class="invoice_list">
    <div class="invoice_list_top">
      <el-button type="text" @click="uploadInvoice" v-if="!disabled">上传发票</el-button>
      <div class="placeholder-div" v-else></div>
      <span class="placeholder">({{ list.length }}/{{ limit }})</span>
    </div>
    <div class="invoice_item" v-for="(item, index) in list" :key="index" @click="uploadInvoice">
      <div class="cell top">
        <span class="flex-1 omit" :title="item.Payee">{{ item.Payee }}</span>
        <span class="flex-0">¥{{ item.InvoiceAmount }}</span>
      </div>
      <div class="cell omit" :title="item.Payer">{{ item.Payer }}</div>
      <div class="cell">{{ item.InvoiceNumber }}</div>
      <div class="cell">{{ item.InvoiceDate | dateFilter("YYYY/MM/DD") }}</div>
      <svg-icon
        v-if="!disabled"
        icon-class="delete-icon"
        class="icon danger-icon delete_icon"
        title="删除"
        @click.stop.native="deleteInvoice(index)"
      />
      <svg-icon
        v-if="disabled"
        icon-class="download-icon"
        class="icon download_icon com-icon"
        title="下载"
        @click.stop.native="handleDownload(item)"
        />
    </div>
    <!-- 上传发票弹窗 -->
    <UploadInvoiceDialog
      v-if="uploadInvoiceVisible"
      v-model="list"
      :dialogFormVisible="uploadInvoiceVisible"
      :disabled="disabled"
      :limit="limit"
      @closeDialog="uploadInvoiceClose"
      @saveSuccess="uploadInvoiceSuccess"
    />
  </div>
</template>

<script>
import UploadInvoiceDialog from "@/views/financialManagement/reimbursement/components/UploadInvoiceDialog.vue";
import { serviceArea } from '@/api/serviceArea'

export default {
  name: "InvoiceList",
  components: {
    UploadInvoiceDialog,
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      list: [],
      limit:20,//限制上传数量
      uploadInvoiceVisible: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.list = this.$_.cloneDeep(val);
      },
      immediate: true,
    },
  },
  methods: {
    uploadInvoice() {
      this.uploadInvoiceVisible = true;
    },
    uploadInvoiceSuccess(list) {
      this.list = list;
      this.uploadInvoiceClose();
      this.change();
    },
    uploadInvoiceClose() {
      this.uploadInvoiceVisible = false;
    },
    change() {
      const list = this.$_.cloneDeep(this.list);
      this.$emit("input", list);
      this.$emit("change", list);
    },
    handleDownload(item) {
      const Id = item?.AttachmentList?.[0]?.Id;
      if(Id){
        const dowFileUrl = `api${serviceArea.resource}/FileUpload/Download?id=${Id}`;
        console.log(this.$_.cloneDeep(item),dowFileUrl);
        window.open(dowFileUrl)
      }else{
        this.$message.warning("发票附件不存在");
      }
    },
    deleteInvoice(index) {
      this.list.splice(index, 1);
      this.change();
    },
  },
};
</script>

<style lang="scss" scoped>
.invoice_list {
  .invoice_list_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .invoice_item {
    position: relative;
    min-height: 85px;
    font-size: 12px;
    border: 1px solid $border-color-light;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    line-height: normal;
    cursor: pointer;
    .cell {
      color: $text-secondary;
      &:not(:first-child) {
        margin-top: 4px;
      }
      &.top {
        color: $text-main-color;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    .delete_icon {
      position: absolute;
      right: 10px;
      bottom: 10px;
    }
    .download_icon {
      position: absolute;
      right: 10px;
      bottom: 10px;
    }
  }
}
</style>
