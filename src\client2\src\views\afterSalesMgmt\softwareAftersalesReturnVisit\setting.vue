<template>
<div>
    <app-dialog title="设置回访提醒" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750" :width='800'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="循环设置" prop="IsLoopSetting">
                                <el-switch :disabled='!editable' v-model="formData.IsLoopSetting"></el-switch>
                            </el-form-item>
                        </el-col>

                        <template v-if="formData.IsLoopSetting">
                            <el-col :span="24">
                                <el-form-item label="提醒对象" prop="EmployeeList">
                                    <emp-selector :readonly="!editable" :beforeConfirm='handleFinalBeforeConfirm' :showType="2" :multiple="true" :list="formData.EmployeeList" @change="handleChangeManager"></emp-selector>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="开始日期" prop="StartTime">
                                    <el-date-picker style="width:100%;" :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.StartTime" type="datetime"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="时间设置" prop="Timeset">
                                    <el-time-picker style="width: 100px;" v-model="formData.Timeset" format="HH:mm" value-format="HH:mm" placeholder=""></el-time-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="循环周期" prop="CyclePeriodType">
                                    <el-select
                                    :disabled="!editable"
                                    class="sel-ipt"
                                    placeholder=""
                                    clearable
                                    v-model="formData.CyclePeriodType"
                                    >
                                    <el-option v-for="item in cyclePeriodTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>

                                    <span v-if="formData.CyclePeriodType == 6" style="margin-left: 20px;">
                                        每<el-input-number style="margin: 0 6px;" v-model="formData.CustomLoopSetting" :min="1" :max="99"></el-input-number>天循环1次
                                    </span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24" class="sp-row">
                                <el-form-item label="提前提醒设置" prop="IsAdvanceReminderSettings">
                                    <el-switch :disabled='!editable' v-model="formData.IsAdvanceReminderSettings"></el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24" v-if="formData.IsAdvanceReminderSettings">
                                <el-form-item label="" prop="">
                                    <el-button @click="handleRegionDialog" type="text">添加提醒时间</el-button>
                                    <div v-if="formData.RemindSetValueList && formData.RemindSetValueList.length > 0">
                                        <span v-for="(t, idx) in formData.RemindSetValueList" :key="idx">
                                            {{ t | typeFilter }}
                                            <template v-if="idx < formData.RemindSetValueList.length - 1">,</template>
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </template>
                        

                    </el-row>
                </div>

            </el-form>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>

    
    <remindTypesSelector
        @closeDialog="closeRemindTypesDialog"
        @saveSuccess="electedRemindTypesData"
        :dialogFormVisible="dialogRemindTypesFormVisible"
        :checked="formData.RemindSetValueList || []"
    ></remindTypesSelector>

</div>
</template>

<script>
import * as salesAfterVist from "@/api/afterSalesMgmt/salesAfterVist";
import noData from "@/views/common/components/noData";
import empSelector from '../../common/empSelector'
import remindTypesSelector from './remindTypesSelector'
import { vars } from './enum'
import dayjs from 'dayjs'

export default {
    name: "setting",
    directives: {},
    components: {
        noData,
        empSelector,
        remindTypesSelector,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [],
    filters: {
        typeFilter(val) {
            let obj = vars.remindTypes.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return '无'
        }
    },
    props: {
        dialogStatus: {
            //create、update、detail、follow（跟进）
            type: String
        },
        regionalBusinessRelationId: {
            type: String,
            required: true
        },

    },
    watch: {

        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val && this.regionalBusinessRelationId) {
                    this.getDetail()
                }
            },
            immediate: true
        },
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            cyclePeriodTypes: vars.cyclePeriodTypes,
            remindTypes: vars.remindTypes,
            loading: false,
            disabledBtn: false,
            rules: {
                EmployeeList: { fieldName: "提醒对象", rules: [{ required: true }] },
                StartTime: { fieldName: "开始日期", rules: [{ required: true }] },
                Timeset: { fieldName: "时间设置", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            activeNames: [],
            formData: {
                Id: "", //
                IsLoopSetting: false,
                EmployeeList: [],
                StartTime: dayjs().format('YYYY-MM-DD'),
                CyclePeriodType: 1,
                CustomLoopSetting: 1,
                IsAdvanceReminderSettings: false,
                Timeset: '',
                RemindSetValueList: [],


            },

            dialogRemindTypesFormVisible: false,
        };
    },

    methods: {
        handleFinalBeforeConfirm(users) {
            if(users && users.length > 10) {
            this.$message({
                message: '提醒对象不得超过10人',
                type: 'error'
            })
            return false
            }
            return true
        },
        handleChangeManager(users) {
            if (users && users.length > 0) {
                this.formData.EmployeeList = users;
            } else {
                this.formData.EmployeeList = [];
            }
            this.$refs["formData"].validateField("EmployeeList");
        },
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        getDetail() {
            this.loading = true
            salesAfterVist.getVistReminderSetting({id: this.regionalBusinessRelationId}).then(res => {
                this.loading = false
                if(res) {
                    this.formData = Object.assign({}, this.formData, res)
                }
            }).catch(err => {
                this.loading = false
            })
        },
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                //提交数据保存
                postData = Object.assign({}, this.formData);
                postData.RegionalBusinessRelationId = this.regionalBusinessRelationId

                if(postData.EmployeeList && postData.EmployeeList.length > 0) {
                    postData.EmployeeIdList = postData.EmployeeList.map(s => s.EmployeeId)
                }
                delete postData.EmployeeList

                if(!postData.IsAdvanceReminderSettings) {
                    postData.RemindSetValueList = []
                }else{
                    if(!postData.RemindSetValueList || postData.RemindSetValueList.length == 0) {
                        this.$message({
                            message: '请添加提醒时间',
                            type: 'error'
                        })
                        return false
                    }
                }

                this.disabledBtn = true
                let result = salesAfterVist.editVistReminderSetting(postData);
                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },

         //地区选择
        closeRemindTypesDialog() {
          this.dialogRemindTypesFormVisible = false;
        },
        handleRegionDialog(){
            this.dialogRemindTypesFormVisible=true;
        },
        electedRemindTypesData(data){
            this.formData.RemindSetValueList = JSON.parse(JSON.stringify(data || []))
            this.closeRemindTypesDialog()
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
.sp-row >>> .el-form-item{
    margin-bottom: 0;
}
</style>

<style lang='scss' scoped>
.wrapper{
    min-height: 300px;
}
.col-wrapper{
    padding: 5px 0;
    min-height: 400px;
}
</style>
