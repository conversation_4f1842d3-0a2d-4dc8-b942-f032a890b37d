<template>
<div class="app-container">
    
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <el-card v-loading='loading' class="dialogWarp_card" header="人员列表" shadow="never" :body-style="{ padding: '0',  display: 'flex', flex: '1', flexDirection: 'column' }">
                        <div style="width: 100%; padding: 10px 10px 0;">
                            <el-input class="elInput" style="margin-bottom: 4px;" prefix-icon="el-icon-search" placeholder="输入关键字" clearable="" v-model="filterText"></el-input>
                        </div>
                        <el-tree v-if="tableData.length>0" :check-strictly='true' :default-expanded-keys="epKeys" :show-checkbox='true' class="tagsList" ref="treeRef" :data="tableData" :props="defaultProps" node-key="Id" :filter-node-method="filterNode"  :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="handleNodeClick">
                            <span class="custom-tree-node" slot-scope="{ node, data }">
                                <span class="node-title" :title="node.label">
                                    <div class="avatar-wrapper">
                                        <el-avatar v-if="data.Type == 2" fit='cover' :size="18" :src="data.Avatar || defavatar"></el-avatar>
                                    </div>
                                    <span style="margin-left: 4px; padding-bottom: 2px;">{{ node.label }}</span>
                                </span>
                            </span>
                        </el-tree>
                        <div v-else class="tagsList">
                            <no-data></no-data>
                        </div>
                </el-card>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper" v-loading="detailFormDataLoading">
              
                <div v-show="type==0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
                    请点击左侧文件查看详情
                </div>
                <div v-show="type!=0" class="persion-wrapper">
                        <div class="avatar">
                            <el-avatar  fit='cover' :size="50" :src="basicsFormData.AvatarPath || defavatar"></el-avatar>
                        </div>
                        <div class="info-wrapper">
                            <div class="info">
                                <div class="name omit">{{ basicsFormData.Name }}</div>
                                <div class="title omit">
                                    <span style="color:gray;">
                                        {{ basicsFormData.DepartmentName }} 
                                    </span>
                                    <span style="margin-left:10px;color:gray;">
                                        {{ basicsFormData.JobName }}
                                    </span>
                                    
                                </div>
                            </div>
                        </div>
                </div>
                
                <div class="content __dynamicTabWrapper">
                    
                    <el-row style="height:100%;">
                        <el-col :span="3" style="height:100%">
                           <el-tabs :tab-position="tabNewsPosition" style="height: 100%; margin-top: 20px; margin-left:50px" :items='typesData' v-model="type">
                            <el-tab-pane v-for="(itemTab,index) in typesData" :name='itemTab.value' :label="itemTab.label" :key="index"></el-tab-pane>
                           </el-tabs>
                        </el-col>
                        <el-col :span="21" style="height:100%; display: flex; flex-direction: column;" v-loading="commonLoading">
                            <div v-if="type == 6 && isShowSalesPoint">
                                <el-radio-group v-model="currentReviewType" style="margin: 10px 0;">
                                    <el-radio-button v-for='r in reviewTypes' :label="r.value" :key="r.value">{{ r.label }}</el-radio-button>
                                </el-radio-group>
                            </div>
                            <div class="tagBox" v-if="type==2 || type==3 || (type==6 && currentReviewType == 1) || type==7">
                                <div class="year-wrapper">
                                    切换年份：
                                    <el-select v-model="listQuery.Year" placeholder="请选择" @change='freshData'>
                                        <el-option v-for="item in years" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </div>
                                <div v-if="type!=7" class="btn-list">
                                    <v-button-list :btnListData='monList' v-model="listQuery.Month" @change="freshData"></v-button-list>
                                </div>
                            </div>


                            <div v-if="type==1" key='block_1' class="page-content">

                                <el-form class="form" style="padding-top:10px;" ref="basicsFormData"  label-position="right" :label-width="labelWidth">


                                      <div class="text item">

                                            <div style="display:flex; padding-top:19px;">
                                                <span class="title-left"></span>
                                                <span class="title-bg">基本信息</span>
                                            </div>

                                            <el-row style="margin-top:10px">
                                                <el-col :span="8">
                                                    <el-form-item label="姓名" prop="Name">
                                                        <label class="basicsLabel">{{basicsFormData.Name}}</label>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-form-item label="性别" prop="Sex">
                                                        <label class="basicsLabel">{{basicsFormData.Sex == 1 ? '男':'女'}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="手机号" prop="Mobile">
                                                        <label class="basicsLabel">{{basicsFormData.Mobile}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>

                                             <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="工号" prop="Name">
                                                        <label class="basicsLabel">{{basicsFormData.Number}}</label>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-form-item label="所属部门" prop="Sex">
                                                        <label class="basicsLabel">{{basicsFormData.DepartmentNameLastLevel}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="座机" prop="Mobile">
                                                        <label class="basicsLabel">{{basicsFormData.SpecialPlane}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="所属职位" prop="JobName">
                                                        <label class="basicsLabel">{{basicsFormData.JobName}}</label>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-form-item label="微信" prop="WeChat">
                                                        <label class="basicsLabel">{{basicsFormData.WeChat}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="邮箱" prop="Email">
                                                        <label class="basicsLabel">{{basicsFormData.Email}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="在职状态" prop="WorkingState">
                                                        <label class="basicsLabel">{{basicsFormData.WorkingState | workingStateFilter}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                             <div style="display:flex; padding-top:19px;">
                                                <span class="title-left"></span>
                                                <span class="title-bg">个人资料</span>
                                             </div>


                                              <el-row style="margin-top:10px">
                                                <el-col :span="8">
                                                    <el-form-item label="身份证号" prop="IdCard">
                                                        <label class="basicsLabel">{{detailFormData.IdCard}}</label>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-form-item label="籍贯" prop="NativePlace">
                                                        <label class="basicsLabel">{{detailFormData.NativePlace}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="民族" prop="Nationality">
                                                        <label class="basicsLabel">{{detailFormData.Nationality}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>

                                             <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="政治面貌" prop="PoliticCountenance">
                                                        <label class="basicsLabel">{{detailFormData.PoliticCountenance | politicCountenanceFilter}}</label>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-form-item label="婚姻" prop="IsMarried">
                                                        <label class="basicsLabel">{{detailFormData.IsMarried | marriedFilter}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="生育" prop="Birth">
                                                        <label class="basicsLabel">{{detailFormData.Birth | birthFilter}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="学历" prop="EducationBackground">
                                                        <label class="basicsLabel">{{detailFormData.EducationBackground | educationBackgroundFilter}}</label>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-form-item label="学历性质" prop="Education">
                                                        <label class="basicsLabel">{{detailFormData.Education | educationFilter}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="毕业学校" prop="University">
                                                        <label class="basicsLabel">{{detailFormData.University}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="毕业时间" prop="UniversityTime">
                                                        <label class="basicsLabel">{{detailFormData.UniversityTime | dateFilter('YYYY-MM-DD')}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="专业" prop="Specialty">
                                                        <label class="basicsLabel">{{detailFormData.Specialty}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="技能" prop="TechnicalAbility">
                                                        <label class="basicsLabel">{{detailFormData.TechnicalAbility}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="入职时间" prop="EntryTime">
                                                        <label class="basicsLabel">{{detailFormData.EntryTime | dateFilter('YYYY-MM-DD')}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="转正时间" prop="RegularizationTime">
                                                        <label class="basicsLabel">{{detailFormData.RegularizationTime | dateFilter('YYYY-MM-DD')}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="紧急联系人" prop="EmergencyContact">
                                                        <label class="basicsLabel">{{detailFormData.EmergencyContact}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="紧急联系人电话" prop="EmergencyContactPhone">
                                                        <label class="basicsLabel">{{detailFormData.EmergencyContactPhone}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="紧急联系人2" prop="EmergencyContact1">
                                                        <label class="basicsLabel">{{detailFormData.EmergencyContact1}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="紧急联系人电话2" prop="EmergencyContactPhone1">
                                                        <label class="basicsLabel">{{detailFormData.EmergencyContactPhone1}}</label>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>


                                            <el-row>
                                                <el-col :span="8">
                                                    <el-form-item label="现居住地址" prop="Address">
                                                        <label class="basicsLabel">{{detailFormData.Address}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="开始工作时间" prop="WorkTime">
                                                        <label class="basicsLabel">{{detailFormData.WorkTime | dateFilter('YYYY-MM-DD')}}</label>
                                                    </el-form-item>
                                                </el-col>

                                            </el-row>

                                            <div style="display:flex; padding-top:19px;">
                                                <span class="title-left"></span>
                                                <span class="title-bg">其他</span>
                                            </div>

                                            <el-row style="margin-top:10px">
                                                <el-col :span="8">
                                                    <el-form-item label="生日" prop="Birthday">
                                                        <label class="basicsLabel">{{detailFormData.Birthday | dateFilter('YYYY-MM-DD')}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="兴趣爱好" prop="HobbiesAndInterests">
                                                        <label class="basicsLabel">{{detailFormData.HobbiesAndInterests}}</label>
                                                    </el-form-item>
                                                </el-col>

                                            </el-row>

                                            <div style="display:flex; padding-top:19px;">
                                                <span class="title-left"></span>
                                                <span class="title-bg">剩余假期</span>
                                            </div>


                                             <el-row style="margin-top:10px">
                                                <el-col :span="8">
                                                    <el-form-item label="剩余年假(天)">
                                                        <label class="basicsLabel">{{empInfo.LeaveBalance.AnnualLeave || 0}}</label>
                                                    </el-form-item>
                                                </el-col>

                                                 <el-col :span="8">
                                                    <el-form-item label="剩余调休(天)">
                                                        <label class="basicsLabel">{{empInfo.LeaveBalance.CompensatoryLeave || 0 }}</label>
                                                    </el-form-item>
                                                </el-col>

                                            </el-row>

                                        </div>            
                                       
                                </el-form>

                                
                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">附件信息</span>
                                </div>
                                 <app-uploader :readonly="true" accept="all" :fileType="3" :max="10000" :value="detailFormData.AttachmentIdList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"></app-uploader>
                                 <div v-if="detailFormData.AttachmentIdList == null">
                                        暂无附件
                                 </div>
                            </div>

                            <div v-if="type==2" key='block_2' class="page-content">
                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">总览信息</span>
                                </div>

                                <div style="display: flex; flex-direction: row; flex-flow: wrap; width:95%;">
                                    <div v-for="(t, idx) in summaryList" :key="idx" style="width:14%;"> 
                                        <el-card shadow="hover" :key="`kaoqing-zonglan-${idx}`" style="margin-top:20px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">{{ t.label }}</span>
                                            <span class="card-value">{{ t.value }}</span>
                                        </el-card>
                                    </div>
                                </div>

                                <div style="display:flex; padding-top:19px;" v-show="listQuery.Month != 0">
                                    <span class="title-left"></span>
                                    <span class="title-bg">考勤月历</span>
                                </div>

                                <!-- <el-calendar class="elCalendar" v-model="currentDate" v-show="listQuery.Month != 0">
                                    <template
                                    slot="dateCell"
                                    slot-scope="{date, data}">
                                        <div v-if="!onMonth(data.day)">
                                            <div>{{ data.day | dateFilter('DD') }}</div>
                                        </div>
                                        <cell 
                                            v-if="onMonth(data.day)"
                                            :cellData="{
                                                currentDate: data.day,
                                                dataObj: getDatas(data.day),
                                                isComplaint:true
                                            }"
                                            @handleAcShow='(currCellData) => handleAcShow(data, currCellData)'>
                                        </cell>
                                    </template>
                                </el-calendar> -->

                                <div class="calendar-wrapper">
                                    
                                        <el-calendar class="elCalendar" v-show="listQuery.Month != 0" v-model="currentDate">
                                        <template
                                            slot="dateCell"
                                            slot-scope="{date, data}">
                                            <div v-if="!onMonth(data.day)">
                                                <div>{{ data.day | dateFilter('DD') }}</div>
                                            </div>
                                            <cell v-if="onMonth(data.day)" :cellData="{
                                                currentDate: data.day,
                                                dataObj: getDatas(data.day)
                                            }"></cell>
                                        </template>
                                    </el-calendar>
                                </div>

                                <div style="display:flex;margin-top:10px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">流程记录</span>
                                </div>
                                
                                <app-table-core style="margin-left:25px;margin-bottom:30px;" ref="mainTable"  :tab-columns="tabColumns" :serial='false' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true" :isShowBtnsArea='true' :startOfTable="1" :multable="false" :isShowConditionArea='false'>
                                    <template slot="Idx" slot-scope="scope">
                                        {{ scope.index + 1 }}
                                    </template>
                                    <template slot="ApprovalState" slot-scope="scope">
                                        <span class="item-status" :style="{color:getStatusObj(scope.row.ApprovalState).bgColor,
                                        backgroundColor: getStatusObj(scope.row.ApprovalState).color}">{{ getStatusObj(scope.row.ApprovalState).label }}</span>
                                    </template>
                                    <template slot="ApprovalModuleSubtypeType" slot-scope="scope">
                                        {{ getOptTypeDesc(scope.row.ApprovalModuleSubtypeType) }}
                                        <span v-if="scope.row.LeaveType">
                                        ({{ scope.row.LeaveType | levelTypeFilter }})
                                        </span>
                                    </template>
                                    <template slot="CreateEmployee" slot-scope="scope">
                                        <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
                                    </template>
                                    <template slot="TimeSpan" slot-scope="scope">
                                        <span v-if="scope.row.TimeSpan">
                                        <!-- 外出 或者 外出申请撤销 x小时x分钟 -->
                                        <span v-if="scope.row.ApprovalModuleSubtypeType == 32 || scope.row.ApprovalModuleSubtypeType == 36">
                                            {{ transTime(scope.row.TimeSpan) }}
                                        </span>
                                        <span v-else>
                                            {{ scope.row.TimeSpan }}天
                                        </span>
                                        </span>
                                        <span v-else>
                                        无
                                        </span>
                                        
                                    </template>
                                    <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>

                                    <!-- 表格行操作区域 -->
                                    <template slot-scope="scope">
                                        <app-table-row-button @click="handleShowDetail(scope.row)" :type="2"></app-table-row-button>
                                        <app-table-row-button v-if="scope.row.ApprovalModuleSubtypeType == 42 || scope.row.ApprovalModuleSubtypeType == 43" @click='handlePrintRow(scope.row)' text="打印"></app-table-row-button>
                                        
                                    </template>
                                </app-table-core>

                            </div>

                            <div v-if="type==3" key='block_3' class="page-content">
                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">总览信息</span>
                                </div>

                                 <div style="margin-top:20px; display:flex; flex-direction: row;">

                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="all-task" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">全部任务</span>
                                                <span class="card-content">{{ taskSummary.Total }}</span>
                                            </div>
                                        </div>
                                    </el-card>

                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="general-task" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">常规任务</span>
                                                <span class="card-content">{{ taskSummary.RoutineTaskCount }}</span>
                                            </div>
                                        </div>
                                    </el-card>

                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="develop-task" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">研发任务</span>
                                                <span class="card-content">{{ taskSummary.ProjectTaskCount }}</span>
                                            </div>
                                        </div>
                                    </el-card>

                                 </div> 

                                <div v-show="listQuery.Month === 0"  style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">年度任务趋势</span>
                                </div>

                                <div v-show="listQuery.Month == 0">
                                    <div id="myChart" ref="myChart" style="width:96%; height: 280px; margin-top:15px; margin-left:15px; margin-right:35px;"></div>
                                </div>
                                
                                 <div style="display:flex; padding-top:19px;" v-show="listQuery.Month != 0">
                                    <span class="title-left"></span>
                                    <span class="title-bg">任务详情</span>
                                </div>

                                <FullCalendar v-show="listQuery.Month != 0" ref="fullCalendar" id="fullCalendar" v-loading='taskLoading' :options="calendarOptions" class="app-calendar"></FullCalendar>
                            </div>

                            <div v-if="type==4" key='block_4' class="page-content" style="overflow-y: hidden;">
                                <div style="display: flex; padding-top: 19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">总览信息</span>
                                </div>

                                <div style="margin-top: 20px; display: flex;">

                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="shouldShared" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">应分享</span>
                                                <span class="card-content">{{ sharedPeopleStatistics.ShouldShare }}</span>
                                            </div>
                                        </div>
                                    </el-card>

                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="shared" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">已分享</span>
                                                <span class="card-content">{{ sharedPeopleStatistics.HaveBeenShared }}</span>
                                            </div>
                                        </div>
                                    </el-card>

                                </div> 

                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">分享详情</span>
                                </div>

                                <div class="share-content">
                                    <div class="mid">
                                        <div class="lft">
                                            <div class="top">分享列表</div>
                                            <div class="inner-mid">
                                                <div v-for="(d, idx) in classList" :key="idx">
                                                <el-button @click="changeClass(d.Id)" :type="d.Id == trainId ? 'primary' : ''">{{ d.TrainsName }}</el-button>
                                                </div>
                                            </div>
                                            <div class="btm">
                                                {{ query.PageIndex }} / {{ pageTotal }}
                                                <pagination
                                                    :total="total"
                                                    small
                                                    background
                                                    :page.sync="query.PageIndex"
                                                    :size.sync="query.PageSize"
                                                    @pagination="handleCurrentChange"
                                                    layout="prev, next"
                                                    :pager-count="5"
                                                />
                                            </div>
                                        </div>
                                        <div class="rht">
                                            <div class="top"></div>
                                            <div class="inner-mid">
                                                <div v-if="sharedContent">
                                                    <div style="font-size: 16px;font-weight: 700;padding: 0px 10px;">{{sharedContent.Title}}</div>
                                                    <div class="divUeditor ql-editor" v-html="sharedContent.Content" v-viewer></div>
                                                </div>
                                                <no-data v-else></no-data>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div v-if="type==5" key='block_5' class="page-content" style="height:100%;">
                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">绩效详情</span>
                                </div>

                                <div v-show="selfRatingDatas.length == 0" style="text-align: center; padding: 20px;">未参与绩效考核</div>
                                
                                <div v-show="selfRatingDatas.length > 0" style="display:flex; flex-wrap: wrap; padding-top:10px;">
                                    <el-button style="margin-left:20px; margin-bottom: 5px;" v-for="item in selfRatingDatas" :key="item.Id" :type="currentYearId===item.Id?'primary':''" @click="handleRating(item.Id)"> {{ item.Year }} / {{ item.HalfYearType | halfyearTypeFilter }}</el-button>
                                </div>
                                <el-form
                                    v-show="selfRatingDatas.length > 0"
                                        style="margin-top:10px;"
                                        ref="ratingFormData"
                                        :model="ratingFormData"
                                        label-position="right"
                                        label-width="100px"
                                        >
                                        <div class="bt">
                                            <el-form-item label="考核状态">
                                                <span>{{ ratingFormData.appraisePlanDetailsResponseModel.ProgressStatus | progressStatusfilter }}</span>
                                            </el-form-item>
                                            <el-form-item label="部门名称">
                                                <span>{{ ratingFormData.appraisePlanDetailsResponseModel.DepartmentName }}</span>
                                            </el-form-item>
                                            <el-form-item label="考核主管">
                                                <span>{{ratingFormData.appraisePlanDetailsResponseModel.PrincipalEmployeeList | namesFilter}}</span>
                                            </el-form-item>
                                        </div>
                                    
                                        <div class="bt">
                                            <el-form-item label="自评" prop="SelfRatingEvaluate">
                                                <span>{{ ratingFormData.SelfRatingEvaluate }}</span>
                                            </el-form-item>
                                            <el-form-item label="评价说明" prop="SelfRatingExplain">
                                                <span>{{ ratingFormData.SelfRatingExplain }}</span>
                                            </el-form-item>
                                            <el-form-item label="备注">
                                                <span>{{ ratingFormData.SelfRatingDescribe }}</span>
                                            </el-form-item>
                                            <el-form-item label="PBC" prop="MyAttachmentList">
                                                <app-uploader 
                                                    :readonly="true" 
                                                    accept="all" 
                                                    :fileType="3" 
                                                    :max="10000" 
                                                    :value="ratingFormData.MyAttachmentList"
                                                    :fileSize="1024 * 1024 * 500" 
                                                    :minFileSize="100 * 1024" 
                                                    >
                                                </app-uploader>
                                                <!--    @change="handleFilesMyChange"  -->
                                            </el-form-item>
                                        </div>
                                        <div>
                                            <el-form-item label="评定结果">
                                                <!-- 如果存在“终审结果”，那么用该值覆盖“集体评定” -->
                                                <template v-if="ratingFormData.FinalEvaluate && ratingFormData.FinalEvaluate != 10">
                                                    <span v-if="ratingFormData.FinalEvaluate">
                                                        {{ getFinalStatus(ratingFormData.FinalEvaluate) }}
                                                    </span>
                                                </template>
                                                <template v-else>
                                                    <span v-if="ratingFormData.TeamEvaluate">
                                                        {{ ratingFormData.TeamEvaluate }}
                                                    </span>
                                                </template>
                                            </el-form-item>
                                            <el-form-item label="评议说明">
                                                <span v-if="ratingFormData.FinalEvaluate && ratingFormData.FinalEvaluate != 10">{{ ratingFormData.FinalRatingExplain }}</span>
                                                <span v-else>{{ratingFormData.TeamExplain}}</span>
                                            </el-form-item>
                                        </div>
                                </el-form>
                            </div>
                             
                            <div v-if="type==6 && currentReviewType == 1" key='block_6' class="page-content" style="height:100%;">
                                <div style="display:flex; padding-top:19px;" v-if="listQuery.Month > 0 && isShowSalesPoint">
                                    <span class="title-left"></span>
                                    <span class="title-bg">销售评分</span>
                                </div>
                                
                                <div v-if="listQuery.Month > 0 && isShowSalesPoint">
                                    <div class="grade-wrapper">
                                        <div class="item-wrapper" 
                                            v-for="(r, idx) in rates" :key='idx'
                                        >
                                            <div class="title">第{{ weeks[r.WeekIndex - 1] }}周评分</div>
                                            <div class="num" :class="r.Point < 60 ? 'l1' : r.Point < 80 ? 'l2' : 'l3'">{{ r.Point }}</div>
                                            <div class="opt">
                                                <el-button v-if="r.Evaluated" type="primary" size="mini" @click="deductPointDialogOpen(r.WeekIndex)">扣分项</el-button>
                                                <el-button type="text" v-if="r.Evaluated && r.IsShowScoreButton" size="mini" style="color: #F59A23;" @click="gradeDialogOpen(r.WeekIndex, 'update')">修改</el-button>
                                                <span class="tip" v-if="!r.Evaluated && !r.IsShowScoreButton">待主管评分</span>
                                                <el-button type="text" v-if="!r.Evaluated && r.IsShowScoreButton" @click="gradeDialogOpen(r.WeekIndex, 'create')">评分</el-button>
                                            </div>
                                        </div>

                                        <div style="flex: 1;"></div>

                                        <!-- <div>{{ monthData }}</div> -->
                                        <div class="item-wrapper" style="padding: 0 40px; border-left: 1px solid #E4E7ED;">
                                            <div class="title">月度评分</div>
                                            <div class="num" :class="monthData.Point < 60 ? 'l1' : monthData.Point < 80 ? 'l2' : 'l3'">{{ monthData.Point }}</div>
                                            <div class="opt">
                                                <el-button v-if="monthData.Evaluated" type="primary" size="mini" @click="monthDeductPointDialogOpen">扣分项</el-button>
                                                <el-button type="text" v-if="monthData.Evaluated && monthData.IsShowScoreButton" size="mini" style="color: #F59A23;" @click="monthGradeDialogOpen('update')">修改</el-button>
                                                <span class="tip" v-if="!monthData.Evaluated && !monthData.IsShowScoreButton">待主管评分</span>
                                                <el-button type="text" v-if="!monthData.Evaluated && monthData.IsShowScoreButton" @click="monthGradeDialogOpen('create')">评分</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">客户数据</span>
                                </div>

                                <el-row>
                                    <el-col class="info-left" :span="4">
                                        <el-card shadow="hover" style="margin-top:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">新增客户</span>
                                            <span class="card-value">{{customerDataStatisticsDatas.CustomerCount}}</span>
                                        </el-card>

                                        <el-card shadow="hover" style="margin-top:20px; margin-bottom:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">拜访客户</span>
                                            <span class="card-value">{{customerDataStatisticsDatas.VisitRecordManagementCount}}</span>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="20">
                                        
                                        <el-row>
                                            <el-col :span="24" class="item-top">
                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">客户总数</span>
                                                    <span class="card-value">{{customerPortraitDatas.Total}}</span>
                                                </el-card>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">友好度</span>
                                                 <noData v-if="!customerPortraitDatas.FriendlinessList"></noData>
                                                 <div v-else id="lineFriendlinessChart" ref="lineFriendlinessChart" style="width:96%; height: 300px;"></div>
                                              </el-col>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">客户状态</span>
                                                 <noData v-if="!barEchartOption1.series ||barEchartOption1.series[0].data.length == 0"></noData>
                                                 <app-charts-basic
                                                    v-else
                                                    :height="chartsHeight"
                                                    :xAxisLabelTooltip="true"
                                                    :xAxisLabelLimit="5"
                                                    :rotate="25"
                                                    width='320px'
                                                    :option="barEchartOption1"
                                                 ></app-charts-basic>
                                              </el-col>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">客户来源</span>
                                                  <div>
                                                     <noData v-if="!hasDataOfPie(pieEchartOption1.series)"></noData>
                                                     <app-charts-basic v-else :height='chartsHeight' width='320px' :option='pieEchartOption1'></app-charts-basic>
                                                 </div>
                                              </el-col>
                                        </el-row>
                                        

                                    </el-col>
                                </el-row>

                                 <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">线索数据</span>
                                </div>
                                 
                                <el-row>
                                    <el-col class="info-left" :span="4">
                                        <el-card shadow="hover" style="margin-top:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">新增的线索</span>
                                            <span class="card-value">{{clueDataStatistics.CreateCount}}</span>
                                        </el-card>

                                        <el-card shadow="hover" style="margin-top:20px; margin-bottom:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">跟进的线索</span>
                                            <span class="card-value">{{clueDataStatistics.FollowCount}}</span>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="20">
                                        
                                        <el-row>
                                            <el-col :span="24" class="item-top">
                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">线索总数</span>
                                                    <span class="card-value">{{clueDataPortrait.Total}}</span>
                                                </el-card>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">线索来源</span>
                                                 <div>
                                                     <noData v-if="!hasDataOfPie(pieEchartOption3.series)"></noData>
                                                     <app-charts-basic v-else :height='chartsHeight' width='320px' :option='pieEchartOption3'></app-charts-basic>
                                                 </div>
                                              </el-col>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">线索状态</span>
                                                 <div>
                                                     <noData v-if="!hasDataOfPie(pieEchartOption2.series)"></noData>
                                                     <app-charts-basic v-else :height='chartsHeight' width='320px' :option='pieEchartOption2'></app-charts-basic>
                                                 </div>
                                              </el-col>
                                        </el-row>
                                        

                                    </el-col>
                                </el-row>

                                 <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">机会点数据</span>
                                </div>

                                <el-row>
                                    <el-col class="info-left" :span="4">
                                        <el-card shadow="hover" style="margin-top:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">新增的机会点</span>
                                            <span class="card-value">{{businessDataStatistics.CreateCount}}</span>
                                        </el-card>

                                        <el-card shadow="hover" style="margin-top:20px; margin-bottom:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">跟进的机会点</span>
                                            <span class="card-value">{{businessDataStatistics.FollowCount}}</span>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="20">
                                        
                                        <el-row>
                                            <el-col :span="24" class="item-top">
                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">机会点产值(万元)</span>
                                                    <span class="card-value">{{businessPortrait.OutputValueTotal}}</span>
                                                </el-card>

                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">已转订单(万元)</span>
                                                    <span class="card-value">{{businessPortrait.ForwardOrderAmount}}</span>
                                                </el-card>

                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">未转订单(万元)</span>
                                                    <span class="card-value">{{businessPortrait.UnforwardedOrderAmount}}</span>
                                                </el-card>

                                                 <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">已终止(万元)</span>
                                                    <span class="card-value">{{businessPortrait.TerminatedAmount}}</span>
                                                </el-card>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">产品类别</span>
                                                 <noData v-if="!barEchartOption3.series || barEchartOption3.series[0].data.length == 0"></noData>
                                                 <app-charts-basic v-else :width="chartsWidth" :height="chartsHeight" :xAxisLabelTooltip="true" :xAxisLabelLimit="5" :rotate="25" ref="pieEchart3" :option="barEchartOption3"></app-charts-basic>
                                              </el-col>
                                              <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">机会点状态</span>
                                                 <noData v-if="!hasDataOfPie(pieEchartOption4.series)"></noData>
                                                 <app-charts-basic v-else :height='chartsHeight' width='320px' :option='pieEchartOption4'></app-charts-basic>
                                              </el-col>
                                        </el-row>
                                        

                                    </el-col>
                                </el-row>

                                 <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">订单数据</span>
                                </div>
                                   
                                <el-row>
                                    <el-col class="info-left" :span="4">
                                        <el-card shadow="hover" style="margin-top:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">新增的订单</span>
                                            <span class="card-value">{{orderDataStatistics.CreateCount}}</span>
                                        </el-card>

                                        <el-card shadow="hover" style="margin-top:20px; margin-bottom:10px; margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">跟进的订单</span>
                                            <span class="card-value">{{orderDataStatistics.FollowCount}}</span>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="20">
                                        
                                        <el-row>
                                            <el-col :span="24" class="item-top">
                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">合同总金额(万元)</span>
                                                    <span class="card-value">{{orderPortrait.ContractGrandTotal}}</span>
                                                </el-card>

                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">未结算金额(万元)</span>
                                                    <span class="card-value">{{orderPortrait.UnsettledAmount}}</span>
                                                </el-card>

                                                 <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                                    <span class="card-title">已结算金额(万元)</span>
                                                    <span class="card-value">{{orderPortrait.SettlementAmount}}</span>
                                                </el-card>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                             <el-col :span="8">
                                                 <span style="color: #333333;font-size: 14px;font-weight: bold; margin-left: 31px;">产品类别</span>
                                                 <noData v-if="!barEchartOption2.series || barEchartOption2.series[0].data.length == 0"></noData>
                                                 <app-charts-basic v-else :width="chartsWidth" :height="chartsHeight" :xAxisLabelTooltip="true" :xAxisLabelLimit="5" :rotate="25" ref="pieEchart2" :option="barEchartOption2"></app-charts-basic>
                                              </el-col>
                                        </el-row>
                                        

                                    </el-col>
                                </el-row>
                                
                                <div>
                                    <div style="display:flex; padding-top:19px;">
                                        <span class="title-left"></span>
                                        <span class="title-bg">拜访数据</span>
                                    </div>
                                    <div style="margin-top:20px; margin-bottom:20px; display:flex;">
                                        <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">需要拜访的客户</span>
                                            <span class="card-value">{{visitPlanDataStatistics.UnvisitedCount}}</span>
                                        </el-card>
    
                                        <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">已拜访的客户</span>
                                            <span class="card-value">{{visitPlanDataStatistics.HaveVisitedCount}}</span>
                                        </el-card>

                                        <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px;">
                                            <span class="card-title">邀请到公司</span>
                                            <span class="card-value">{{visitPlanDataStatistics.InviteCompanyCount}}</span>
                                        </el-card>
                                        
                                    </div>
                                    <div style="padding-left: 10px;">
                                        <app-table-core ref="mainTableDetailData" 
                                            :tab-columns="tabDetailDataColumns" 
                                            :serial='true' 
                                            :tab-datas="visitPlanDataStatistics.DetailData" 
                                            :tab-auth-columns="tabAuthColumns" 
                                            :isShowAllColumn="false" 
                                            :isShowOpatColumn="false" 
                                            :isShowBtnsArea='false' 
                                            :startOfTable="0" 
                                            :multable="false" 
                                            :isShowConditionArea='false'
                                        >
                                            <template slot="VisitType" slot-scope="scope">
                                                {{ scope.row.VisitType | visitTypeFilter }}
                                            </template>
                                            <template slot="VisitTime" slot-scope="scope">
                                                {{ scope.row.VisitTime | dateFilter('YYYY-MM-DD HH:mm') }}
                                            </template>
                                        </app-table-core>
                                    </div>
                                </div>
                            </div>
                            <div v-if="type==6 && currentReviewType == 2" key='block_6' class="page-content" style="height:100%;">
                                <el-row style="margin-top: 10px; display: flex; align-items: center;">
                                    <el-col :span="6">
                                        计分周期：{{ dayCounterInfo.Month }}月的第{{ weeks[dayCounterInfo.WeekIndex - 1] }}周
                                    </el-col>
                                    <el-col :span="6">
                                        当前扣分：{{ dayCounterInfo.DeductionPoints }}
                                    </el-col>
                                    <el-col :span="6">
                                        主管评分：
                                        <el-tag v-if="!dayCounterInfo.SupervisorRating" type="warning" effect="dark">未评分</el-tag>
                                        <el-tag v-else type="success" effect="dark">已评分</el-tag>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-button type="text" @click="openRateDetailDialog()">查看评分标准</el-button>
                                    </el-col>
                                </el-row>
                                <div v-for="(i, idx) in dayCounterInfo.RuleList" :key="idx">
                                    <div style="display: flex; padding-top: 19px;">
                                        <span class="title-left"></span>
                                        <span class="title-bg">{{ i.RateId | rateTypeFilter }}</span>
                                    </div>

                                    <div style="display: flex;">
                                        <div style="width: 210px; padding: 10px 0; border-right: 1px solid #EBEEF5;">
                                            <el-card shadow="hover" style="margin-top:10px; margin-left:35px; width:126px; height:72px;" v-for="(sub, idx2) in i.List" :key="`sub_${idx2}`">
                                                <span class="card-title" v-if="sub.RateId != 10000">{{ sub.RateId | rateTypeFilter }}</span>
                                                <span class="card-title" v-else>主管扣分</span>
                                                <span class="card-value" style="text-align: left; height: 23px; line-height: 23px; padding: 0 10px;">
                                                    <span style="font-weight: bold; font-size: 12px;">扣分：</span>
                                                    <span :style="{color: sub.DeductionValue > 0 ? '#D9001B' : ''}">
                                                        {{ sub.DeductionValue }}
                                                    </span>
                                                </span>
                                            </el-card>
                                        </div>
                                        <div style="width: 360px; display: flex; flex-wrap: wrap; padding: 10px 0;" v-if="i.RateId !== 10000">
                                            <template v-for="(sub, idx) in i.List">
                                                <template v-for="(ssub, idx3) in sub.RightSidePenalty">
                                                    <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px; margin-top: 10px;" :key="`ssub_${idx}_${idx3}`">
                                                        <span class="card-title">{{ ssub.Title }}</span>
                                                        <span class="card-value" :style="{color: ssub.IsStandards ? '' : '#F59A23'}">{{ ssub.Value }}{{ ssub.Unit }}</span>
                                                    </el-card>

                                                    <!-- 为了排版，写死，没有实际意义 -->
                                                    <div v-if="sub.RateId == 4000 && idx3 == 0" style="margin-left:35px; width:126px; height:72px; margin-top: 10px;"></div>
                                                </template>
                                            </template>
                                        </div>
                                        <div style="flex: 1; padding: 20px 0; padding-left: 40px; overflow: hidden;">
                                            <div style="font-size: 14px; font-weight: bold; margin-bottom: 20px;">扣分详情</div>
                                            <template v-for="(sub, idx) in i.List">
                                                <div v-for="(ssub, idx3) in sub.DeductionReasonList" :key="`reason_${idx}_${idx3}`">
                                                    <div>{{ ssub.DeductionReason }}</div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- <el-row>
                                        <el-col :span="4" style="padding: 10px 0;">
                                            <el-card shadow="hover" style="margin-top:10px; margin-left:35px; width:126px; height:72px;" v-for="(sub, idx2) in i.List" :key="`sub_${idx2}`">
                                                <span class="card-title">{{ sub.RateId | rateTypeFilter }}</span>
                                                <span class="card-value" style="text-align: left; height: 23px; line-height: 23px; padding: 0 10px;">
                                                    <span style="font-weight: bold; font-size: 12px;">扣分：</span>
                                                    <span :class="sub.DeductionValue < 60 ? 'l1' : sub.DeductionValue < 80 ? 'l2' : 'l3'">
                                                        {{ sub.DeductionValue }}
                                                    </span>
                                                </span>
                                            </el-card>
                                        </el-col>
                                        <el-col v-if="i.RateId !== 10000" :span="8" style="display: flex; flex-wrap: wrap; padding: 10px 0;">
                                            <template v-for="(sub, idx) in i.List">
                                                <el-card shadow="hover" style="margin-left:35px; width:126px; height:72px; margin-top: 10px;" v-for="(ssub, idx3) in sub.RightSidePenalty" :key="`ssub_${idx}_${idx3}`">
                                                    <span class="card-title">{{ ssub.Title }}</span>
                                                    <span class="card-value">{{ ssub.Value }}{{ ssub.Unit }}</span>
                                                </el-card>
                                            </template>
                                        </el-col>
                                        <el-col :span="i.RateId !== 10000 ? 12 : 20" style="padding: 20px 0;">
                                            <div style="font-size: 14px; font-weight: bold; margin-bottom: 20px;">扣分详情</div>
                                            <template v-for="(sub, idx) in i.List">
                                                <div v-for="(ssub, idx3) in sub.DeductionReasonList" :key="`reason_${idx}_${idx3}`">
                                                    <div>{{ ssub.DeductionReason }}</div>
                                                </div>
                                            </template>
                                        </el-col>
                                    </el-row> -->

                                </div>
                            </div>

                            <div v-if="type==7" key='block_7' style="height:100%; margin-right: 50px;">
                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">项目信息</span>
                                </div>
                                
                                <div v-show="projectInfoStatistics.ProjectCount == 0" style="text-align: center; padding: 20px;">暂无研发数据</div>
                                <div v-show="projectInfoStatistics.ProjectCount > 0" style="margin-top:20px; display:flex;">
                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="all-task" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">参与的项目</span>
                                                <span class="card-content">{{ projectInfoStatistics.ProjectCount }}</span>
                                            </div>
                                        </div>
                                    </el-card>
                                    <el-card shadow="hover" class="card-info">
                                        <div style="display:flex;height:100%;">
                                            <div style="width:40%;margin-top: 15px; margin-left: 10px;">
                                                <svg-icon icon-class="general-task" style="height:40px;width:40px;"></svg-icon>
                                            </div>
                                            <div style="width:60%;display:flex;flex-direction: column;margin-top:5px;">
                                                <span class="card-title">研发任务数</span>
                                                <span class="card-content">{{ projectInfoStatistics.TaskCount }}</span>
                                            </div>
                                        </div>
                                    </el-card>
                                </div>


                                <div style="display:flex; padding-top:19px;">
                                    <span class="title-left"></span>
                                    <span class="title-bg">问题分布</span>
                                </div>
                                
                                <div v-show="projectInfoStatistics.ProjectCount == 0" style="text-align: center; padding: 20px;">暂无研发数据</div>
                                <div>
                                    <div id="lineChart" ref="lineChart" style="width:97%; height: 500px; margin-top:15px; margin-left:15px; margin-right:35px;"></div>
                                </div>

                            </div>

                        </el-col>
                    </el-row>  

                </div>
            </div>
        </div>
    </div>


     <!-- 打印视图 -->
    <evectionDetailPrint ref="print"></evectionDetailPrint>
     

     <!-- 考核申诉 -->
    <common-bullet :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogBulletChangeFormVisible" @closeDialog="closeBulletChangeDialog" @saveSuccess="handleBulletChangeSaveSuccess" :dialogFormVisible="dialogBulletChangeFormVisible" dialogStatus="detail" :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail" :disAB="true"></common-bullet>
    <!-- 请假申请 -->
    <vacate :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogVaVisible" @closeDialog="closeVaDialog" @saveSuccess="handleVcateSaveSuccess" :dialogFormVisible="dialogVaVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></vacate>
    <!-- 加班申请 -->
    <overtime :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogOvertimeVisible" @closeDialog="closeOveretimeDialog" @saveSuccess="handleOvertimeSaveSuccess" :dialogFormVisible="dialogOvertimeVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></overtime>
    <!-- 出差申请 -->
    <evection :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogEvVisible" @closeDialog="closeEvDialog" @saveSuccess="() => {}" :dialogFormVisible="dialogEvVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></evection>
     <!-- 考核申诉 -->
    <attendance-complaint :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogAttenVisible" @closeDialog="closeAttenDialog" @saveSuccess="handleAttenSaveSuccess" :dialogFormVisible="dialogAttenVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></attendance-complaint>
    <!-- 外出申请 -->
    <goout :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogGoOutVisible" @closeDialog="closeGoOutDialog" @saveSuccess="handleGoOutSaveSuccess" :dialogFormVisible="dialogGoOutVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></goout>
    <!-- 考勤人工审核——移动端提交 -->
    <att-approval :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogAttendanceApprovalFormVisible" @closeDialog="closeAttendanceApprovalDialog" @saveSuccess="handleAttendanceApprovalSaveSuccess" :dialogFormVisible="dialogAttendanceApprovalFormVisible" dialogStatus="detail" :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></att-approval>

    <!-- 出差结束申请 -->
    <evectionOver
      v-if="dialogEvOverVisible"
      @closeDialog="closeEvOverDialog"
      @saveSuccess="() => {}"
      :dialogFormVisible="dialogEvOverVisible"
      :dialogStatus='"detail"'
      :id="id"
      :approvalId='approvalId'
    >
    </evectionOver>

    <!-- 出差补助申请 -->
    <evectionSubsidy
      v-if="dialogEvSubSidyVisible"
      @closeDialog="closeEvSubSidyDialog"
      @saveSuccess="() => {}"
      :dialogFormVisible="dialogEvSubSidyVisible"
      :dialogStatus='"detail"'
      :id="id"
      :approvalId='approvalId'
    >
    </evectionSubsidy>


     <!-- 日程事项 -->
        <create-item-page
            @closeDialog="closeItemDialog"
            @saveSuccess="handleSaveItemSuccess"
            :dialogFormVisible="dialogItemFormVisible"
            :dialogStatus="dialogItemStatus"
            :startTime='startTime'
            :endTime='endTime'
            :id="itemId"
        ></create-item-page>

        <!-- 客户拜访 -->
        <createCustomerVisit
            @closeDialog="closeCustomerVisitDialog"
            @saveSuccess="handleSaveCustomerVisitSuccess"
            :dialogFormVisible="dialogCustomerVisitFormVisible"
            :dialogStatus="dialogCustomerVisitStatus"
            :startTime='startTime'
            :endTime='endTime'
            :id="visitId"
        ></createCustomerVisit>

        <!-- 通用任务 -->
        <template v-if="dialogFormVisible">
            <createCommonTask
                @closeDialog="closeCreateCommTask"
                :dialogFormVisible="dialogFormVisible"
                dialogStatus="detail"
                :id='taskId'
                :isSetdefUser="true"
            ></createCommonTask>
        </template>

        <!-- 项目任务 -->
        <template v-if="taskEditVisible">
            <taskEdit
                ref="taskEditRef"
                :project="{Id: currentProjectId}"
                dialogStatus="detail"
                @closeDialog="closeCreateTask"
                :dialogFormVisible="taskEditVisible"
                :taskId="taskId"
                :isSetdefUser="true"
            ></taskEdit>
        </template>

        <!-- 周 评分弹框 -->
        <gradeDialog 
            key='gradeDialog'
            v-if="gradeDialogVisible && basicsFormData"
            @closeDialog="gradeDialogClose"
            @saveSuccess="gradeDialogSaveSuccess"
            :dialogFormVisible="gradeDialogVisible"
            :dialogStatus='dialogStatus'
            :emp="basicsFormData"
            :weekIdx='weekIdx'
            :year='listQuery.Year'
            :month='listQuery.Month'
        ></gradeDialog>

        <!-- 月 评分弹框 -->
        <monthGradeDialog 
            key='monthGradeDialog'
            v-if="monthGradeDialogVisible && basicsFormData"
            @closeDialog="monthGradeDialogClose"
            @saveSuccess="monthGradeDialogSaveSuccess"
            :dialogFormVisible="monthGradeDialogVisible"
            :dialogStatus='monthGradeDialogStatus'
            :emp="basicsFormData"
            :year='listQuery.Year'
            :month='listQuery.Month'
        ></monthGradeDialog>
        
        <!-- 周 扣分项弹框 -->
        <deductPointDialog 
            v-if="deductPointDialogVisible && basicsFormData"
            @closeDialog="deductPointDialogClose"
            @saveSuccess="deductPointDialogSaveSuccess"
            :dialogFormVisible="deductPointDialogVisible"
            :emp="basicsFormData"
            :weekIdx='weekIdx'
            :year='listQuery.Year'
            :month='listQuery.Month'
        ></deductPointDialog>

        <!-- 月 扣分项弹框 -->
        <monthDeductPointDialog 
            v-if="monthDeductPointDialogVisible && basicsFormData"
            @closeDialog="monthDeductPointDialogClose"
            @saveSuccess="monthDeductPointDialogSaveSuccess"
            :dialogFormVisible="monthDeductPointDialogVisible"
            :emp="basicsFormData"
            :year='listQuery.Year'
            :month='listQuery.Month'
        ></monthDeductPointDialog>

        <!-- 评分标准 -->
        <rateDetail 
            v-if="rateDetailDialogVisible && basicsFormData.EmployeesId"
            :dialogFormVisible="rateDetailDialogVisible"
            @closeDialog="() => rateDetailDialogVisible = false"
            dialogStatus="review" 
            :empId="basicsFormData.EmployeesId"
        ></rateDetail>

        
</div>
</template>

<script>

import indexPageMixin from "@/mixins/indexPage";
import NoData from "@/views/common/components/noData";
import vButtonList from '@/views/common/buttonList'
import * as trainApi from "@/api/informationCenter/train";
import { listToTreeSelect } from "@/utils";
import * as systemEmployee from '@/api/personnelManagement/systemEmployee'
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import dayjs from "dayjs"
import initCellData from '../attendanceMgmt/report/common'
import cell from '../attendanceMgmt/report/cell'
import * as approvalVars from '../../projectDev/common/vars'
import * as approvalManagement from "@/api/approvalManagement";
import * as wordPlan from '@/api/workbench/workPlan'
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import interactionPlugin from '@fullcalendar/interaction'
import zhCnLocale from '@fullcalendar/core/locales/zh-cn'
import * as mw from "@/api/workbench/myWorkbench";
import createItemPage from "../../workbench/myWorkbench/createItem"
import createCustomerVisit from '../../workbench/mySchedule/createCustomerVisit'
import taskEdit from '../../projectDev/projectMgmt/workbench/task/taskEdit'
import createCommonTask from '../../workbench/workPlan/planList/createCommonTask'
import { vars } from '../../workbench/mySchedule/vars'
import * as varsPersonal from '../attendanceMgmt/vars'
import * as commonEnum from '../common/vars'
import * as saleMgmtEnum from '../../salesMgmt/common/vars'
import evectionDetailPrint from '../attendanceMgmt/report/evectionDetailPrint'
import { minuteFilter } from '../../../filters';
import * as myAchievements from "@/api/myAchievements";
import { appraiseTypeEnum,appraisePersonalsStatusEnum, yearTypeEnum } from "../../personnelManagement/achievementMgmt/enum";
import * as customerRelationLine from "@/api/salesMgmt/customerRelationLine";
import { pieEchartOptionTemp, colors,barOption} from "../../dataAnalysis/operationData/vars";
import mixins from '../../dataAnalysis/operationData/mixins'
import * as clueManager from "@/api/salesMgmt/clueMgmt";
import * as team from "@/api/projectDev/projectMgmt/team";
import * as businessOpportunity from "@/api/salesMgmt/businessOpportunity";
import * as orderMgmt from '@/api/salesMgmt/orderMgmt'
import * as visitPlanMgmt from '@/api/salesMgmt/visitPlanMgmt'
import * as businessManager from "@/api/salesMgmt/businessManager";
import * as employeePointsRule from "@/api/personnelManagement/employeePointsRule";
import deductPointDialog from './deductPointDialog'
import monthDeductPointDialog from './monthDeductPointDialog'
import gradeDialog from './gradeDialog'
import monthGradeDialog from './monthGradeDialog'
import * as businessRoleApi from "@/api/businessRole";
import { getUserInfo } from "@/utils/auth";
import * as commonVars from '../common/vars'
import rateDetail from '../employeeDataSetting/create'

var echarts = require('echarts');
let clickCount = 0;
let prev = ''; // 上一次点击的dom节点

import {
    isMarriedEnum,
    educationBackgroundEnum,
    employeeWorkingStateEnum,
    politicCountenanceEnum,
    birthEnum,
    educationEnum,
    confidentialityAgreementEnum,
} from "../enum";

export default {
    name: "attendanceMgmt-report-index",
    mixins: [indexPageMixin,mixins],
    components: {
        NoData,
        vButtonList,
        createItemPage,
        createCustomerVisit,
        taskEdit,
        createCommonTask,
        evectionDetailPrint,
        cell,
        FullCalendar,
        commonBullet:() => import('../../workbench/achievements/commonBullet'),
        vacate:() => import('../../workbench/myWorkbench/apply/vacate'),
        overtime:() => import('../../workbench/myWorkbench/apply/overtime'),
        evection:() => import('../../workbench/myWorkbench/apply/evection'),
        goout:() => import('../../workbench/myWorkbench/apply/goout'),
        evectionOver:() => import('../../workbench/myWorkbench/apply/evectionOver'),
        evectionSubsidy:() => import('../../workbench/myWorkbench/apply/evectionSubsidy'),
        attendanceComplaint:() => import('../../workbench/attendance/attendanceComplaint'),
        attApproval:() => import('../../workbench/myApproval/others/attendanceApproval'),
        deductPointDialog,
        monthDeductPointDialog,
        gradeDialog,
        monthGradeDialog,
        rateDetail,
    },
    props: {},
    filters: {
        visitTypeFilter(val) {
            let obj = saleMgmtEnum.vars.customeMgmt.visitTypes.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return val
        },
        rateTypeFilter(val) {
            let obj = commonVars.vars.rateEnum.find(s => s.RateId == val)
            if(obj) {
                return obj.Title
            }
            return ''
        },
        progressStatusfilter(val) {
            let obj = appraisePersonalsStatusEnum.find(s => s.value == val)
            if (obj) {
                return obj.label
            }
            return ''
        },
        halfyearTypeFilter(val) {
            let obj = yearTypeEnum.find(s => s.value == val)
            if (obj) {
                return obj.label
            }
            return ''
        },
       namesFilter(list) {
            if(list && list.length > 0) {
                return list.map(s => s.Name).join('、')
            }
            return ''
        },
        levelTypeFilter(val) {
            let obj = varsPersonal.vars.leaveTypes.find(s => s.value === val)
            if (obj) {
                return obj.label
            }
            return '无'
        },
        educationFilter(value){
            let result;
            for (let index = 0; index < educationEnum.length; index++) {
               if(educationEnum[index].value === value){
                   result =  educationEnum[index].label
                }
            }
            return result
        },
        marriedFilter(value){
            let result;
            for (let index = 0; index < isMarriedEnum.length; index++) {
                if(isMarriedEnum[index].value === value){
                   result =  isMarriedEnum[index].label
                }
            }
            return result
        },
        politicCountenanceFilter(value){
            let result;
            for (let index = 0; index < politicCountenanceEnum.length; index++) {
                 if(politicCountenanceEnum[index].value === value){
                   result =  politicCountenanceEnum[index].label
                }
            }
            return result
        },
        educationBackgroundFilter(value){
            let result;
            for (let index = 0; index < educationBackgroundEnum.length; index++) {
                if(educationBackgroundEnum[index].value === value){
                   result =  educationBackgroundEnum[index].label
                }
            }
            return result
        },
        birthFilter(value){
           let result;
           for (let index = 0; index < birthEnum.length; index++) {
               if(birthEnum[index].value === value){
                   result =  birthEnum[index].label
                }
            }
            return result
        },

        workingStateFilter(value){
           let result;
           for (let index = 0; index < employeeWorkingStateEnum.length; index++) {
               if(employeeWorkingStateEnum[index].value === value){
                   result =  employeeWorkingStateEnum[index].label
                }
            }
            return result
        },
    },
    computed: {
          pageTotal() {
            return parseInt(this.total / this.query.PageSize) + (this.total % this.query.PageSize > 0 ? 1 : 0)
        },
    },
    watch: {
        currentId: {
            handler(val) {
                if(val) {
                    this.getIsSalesman(val)
                }
            },
            immediate: true
        },
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        type(val){
            this.listQuery.Year = (new Date()).getFullYear()
            this.listQuery.Month = (new Date()).getMonth() + 1
            if(val == '3') {
                this.$nextTick(() => {
                    /** 自定义日期头 */
                    this.calendarApi = this.$refs.fullCalendar.getApi();
                    //this.title = this.calendarApi.view?.title;
                    this.freshData()
                })
            }else{
                this.freshData()
            }

        },
        'listQuery.Year'(val) {
            if(this.type == '6' && this.currentReviewType == 1) {
                this.getOpportunityDataStatistics()
                this.getOrderDataStatistics()
                this.getvisitPlan()
                this.getSalesPoint()
            }else if(this.type == '6' && this.currentReviewType == 2) {
                this.getDayCounter()
            }
        },
        'listQuery.Month'(val) {
            if(this.type == '6' && this.currentReviewType == 1) {
                this.getOpportunityDataStatistics()
                this.getOrderDataStatistics()
                this.getvisitPlan()
                this.getSalesPoint()
            }else if(this.type == '6' && this.currentReviewType == 2) {
                this.getDayCounter()
            }
        },
        currentReviewType(val) {
            if(val == 2) {
                this.getDayCounter()
            }
        },
    },
    mounted() {

    },
    created() {
        let currentYear = (new Date()).getFullYear()
        this.years = Array.from(Array(5), (v,k) => { 
            return {
                value: currentYear - k,
                label: currentYear - k
            }
        })

        this.getTree()
    },
    data() {
        return { 
            picColors: ['#178ffd', '#13c0c0', '#2ec059', '#f7ca13'],
            chartsWidth: "350px", 
            visitPlanDataStatistics:{}, 
            tabDetailDataColumns: [
                {
                    attr: { prop: "VisitRecordName", label: "拜访计划" },
                },
                {
                    attr: { prop: "CustomersName", label: "客户姓名" },
                },
                {
                    attr: { prop: "CustomerUnit", label: "客户单位" },
                },
                {
                    attr: { prop: "Position", label: "职务" },
                },
                {
                    attr: { prop: "VisitType", label: "拜访方式" },
                    slot: true
                },
                {
                    attr: { prop: "VisitTime", label: "实际拜访时间" },
                    slot: true
                },
                {
                    attr: { prop: "VisitContent", label: "拜访内容" },
                },
            ],
           businessDataStatistics:{}, 
           businessPortrait:{}, 
           orderDataStatistics:{},
           orderPortrait:{},
           lineHeight: "400px",
           linewidth: "900px",
           projectInfoStatistics:{}, 
           clueDataPortrait:{}, 
           clueDataStatistics:{}, 
           chartsHeight: "300px",
           barEchartOption1: JSON.parse(JSON.stringify(barOption)),
           barEchartOption2: JSON.parse(JSON.stringify(barOption)),
           barEchartOption3: JSON.parse(JSON.stringify(barOption)),
           pieEchartOption1: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
           pieEchartOption2: JSON.parse(JSON.stringify(pieEchartOptionTemp)), 
           pieEchartOption3: JSON.parse(JSON.stringify(pieEchartOptionTemp)), 
           pieEchartOption4: JSON.parse(JSON.stringify(pieEchartOptionTemp)), 
           chartsHeight: '300px',
           customerPortraitDatas:{},
           customerDataStatisticsDatas:{},
           currentYearId:'',
           selfRatingDatas:[],
           labelWidth: "120px",
           epKeys: [],
           sharedPeopleStatistics:{},
           calendarApi: null,
           queryStart: '',
           queryEnd: '',
           taskLoading:false,
           classLoading:false,
           sharedContentLoading:false,
           sharedContent:{},
           trainId:"",
           total: 0,
           classList:[], 
           taskSummary: {},
           isOnlyViewDetail: true,
           id: '',
           approvalId: '',
           datas: null,
           currentDate: new Date((new Date()).getFullYear(), dayjs(new Date()).month(), 1),
           summaryLoading:false,
           empInfo: {
            LeaveBalance:{
              AnnualLeave : 0,
              CompensatoryLeave : 0,
            }
           }, 
           currentId:'',
           loading:false,
           years: [],
           monList: Array.from(Array(12), (v,k) => {
                return {
                    value: k + 1,
                    label: `${k+1}月`
                }
            }).concat({value: 0, label: '全年'}),
            tabNewsPosition: 'left',
            typesData: [{
                    value: "3",
                    label: "工作数据"
                },
                {
                    value: "2",
                    label: "考勤数据"
                },
                {
                    value: "1",
                    label: "员工档案"
                },
                {
                    value: "4",
                    label: "读书分享"
                },
                 {
                    value: "5",
                    label: "绩效数据"
                },
                 {
                    value: "6",
                    label: "销售数据"
                },
                 {
                    value: "7",
                    label: "研发数据"
                },
            ],
            type: "0",
            departmentInfo: "",
            filterText: "",
            tableData: [],
            defaultProps: {
                children: "children",
                label: "EmployeeName",
                disabled: this.disabledFn,
            },
            dialogStatus: "create",
            listQuery: {
                Year: (new Date()).getFullYear(),
                Month: (new Date()).getMonth() + 1
            },

            query:{
                PageIndex:1,
                PageSize:20,
            },

            defavatar: require("../../../assets/images/avatar3.png"),

            detailFormData: {
                // SiLing: 0,
                EmployeeRecordId: "",
                EmployeesId: "",
                IdCard: "",
                NativePlace: "",
                Nationality: "",
                PoliticCountenance: null,
                Birth: null,
                IsMarried: 1,
                EducationBackground: null,
                Education: null,
                University: "",
                UniversityTime: '',
                Specialty: "",
                TechnicalAbility: "",
                WorkTime: null,
                EntryTime: null,
                RegularizationTime: null,
                EmergencyContact: "",
                EmergencyContactPhone: "",
                // WorkYears: 0,
                ContractNo: "",
                ContractStartTime: null,
                ContractEndTime: null,
                ContractEndDate: null,
                ContractEndReason: null,
                ConfidentialityAgreement: 1,
                // PersonalFileNumber: "",
                PayrollBank: "",
                BankAccount: "",
                Birthday: null,
                HobbiesAndInterests: "",
                AttachmentIdList: [], // 附件ID集合

                EmergencyContact1: '',
                EmergencyContactPhone1: '',
                Address: '',
                LaborContractSubjectId: null,
            },

             basicsFormData: {
                EmployeesId: "",
                Name: "",
                Sex: 1,
                Number: "",
                Mobile: "",
                DepartmentId: null,
                DepartmentName: "",
                JobId: "",
                JobName: "",
                Email: "",
                SpecialPlane: "",
                WeChat: "",
                Status: 1,
                WorkingState: 1
            },
            detailFormDataLoading:false,
            summaryList: [
                {label: '应出勤（天）', field: 'DueAttendanceDay', value: 0},
                {label: '实际出勤（天）', field: 'ActualAttendance', value: 0},
                {label: '迟到次数', field: 'LateNumber', value: 0},
                {label: '总迟到时长（分）', field: 'TotalLateHours', value: 0},
                {label: '早退（分）', field: 'LeaveEarly', value: 0},
                {label: '缺勤天数', field: 'AbsentDay', value: 0},
                {label: '出差（天）', field: 'BusinessTrip', value: 0},
                {label: '工作日加班（次）', field: 'WorkingOvertime', value: 0},
                {label: '加班（天）', field: 'RestdaysWorkOvertime', value: 0},
                {label: '事假（天）', field: 'CasualLeave', value: 0},
                {label: '调休（天）', field: 'WorkToRest', value: 0},
                {label: '病假（天）', field: 'SickLeave', value: 0},
                {label: '年假（天）', field: 'AnnualVacation', value: 0},
                {label: '产假（天）', field: 'MaternityLeave', value: 0},
                {label: '陪产假（天）', field: 'PaternityLeave', value: 0},
                {label: '婚假（天）', field: 'MarriageLeave', value: 0},
                {label: '丧假（天）', field: 'FuneralLeave', value: 0},
            ],

            tabDatas: [], //原始数据
            tabAuthColumns: [],
            listLoading: false,
            tabColumns: [
                {
                attr: { prop: "Code", label: "审批单号", width: '150' }
                },
                {
                attr: { prop: "ApprovalState", label: "审批状态", width: '100' },
                slot: true
                },
                {
                attr: {
                    prop: "ApprovalModuleSubtypeType",
                    label: "流程类型",
                    width: '160'
                },
                slot: true
                },
                {
                attr: {
                    prop: "StartEndTime",
                    label: "开始/结束时间",
                    width: '240'
                },
                },
                {
                attr: {
                    prop: "TimeSpan",
                    label: "时长",
                    width: '140'
                },
                slot: true
                },
                {
                attr: { prop: "CreateEmployee", label: "提审人" },
                slot: true
                },
                {
                attr: { prop: "CreateTime", label: "申请时间" },
                slot: true
                },
            ],

               //考核申诉
                dialogBulletChangeFormVisible:false,

                // 请假、加班、出差
                dialogVaVisible:false,
                dialogOvertimeVisible:false,
                dialogEvVisible:false,

                dialogGoOutVisible: false,

                //考勤申诉
                dialogAttenVisible:false,

                //考勤人工审核
                dialogAttendanceApprovalFormVisible: false,
                dialogEvOverVisible: false,

                dialogEvSubSidyVisible: false,

                lineFriendlinessChart: null,
                optionFriendlinessLine: {
                    // title: {
                    //     text: '折线图堆叠'
                    // },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        x: 'center',
                        y: 'bottom',
                        data: ['一面之缘', '初步认识', '相熟相知', '交心友人', '情如老铁']
                    },
                    color:['#67C23A', '#3D73DD',],
                    grid: {
                        top: '2%',
                        left: '30px',
                        right: '5px',
                        bottom: '0%',
                        containLabel: true
                    },
                    // toolbox: {
                    //     feature: {
                    //         saveAsImage: {}
                    //     }
                    // },
                    
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                },
                
                lineChart: null,
                optionLine: {
                    // title: {
                    //     text: '折线图堆叠'
                    // },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        x: 'center',
                        y: 'bottom',
                        data: ['已完成', '未完成']
                    },
                    color:['#67C23A', '#3D73DD',],
                    grid: {
                        top: '2%',
                        left: '1%',
                        right: '2%',
                        bottom: '12%',
                        containLabel: true
                    },
                    // toolbox: {
                    //     feature: {
                    //         saveAsImage: {}
                    //     }
                    // },
                    
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                },
                myChart: null,
                option: {
                    // title: {
                    //     text: '折线图堆叠'
                    // },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        x: 'center',
                        y: 'bottom',
                        data: ['应完成', '已完成']
                    },
                    color:['#67C23A', '#3D73DD',],
                    grid: {
                        top: '2%',
                        left: '1%',
                        right: '2%',
                        bottom: '12%',
                        containLabel: true
                    },
                    // toolbox: {
                    //     feature: {
                    //         saveAsImage: {}
                    //     }
                    // },
                    
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                },

            calendarOptions: {
                plugins: [
                    // 加载插件，V5采用插件模块方式加入
                    dayGridPlugin,
                    timeGridPlugin,
                    interactionPlugin, // needed for dateClick
                    listPlugin,
                ],
                height: 800, //日历高度
                width: 400,
                headerToolbar: false, // 隐藏头部的导航栏
                // headerToolbar: {
                //     // 头部toolba
                //     left: 'prev,next today',
                //     center: 'title',
                //     right: 'dayGridMonth,timeGridWeek,timeGridDay,list',//,listYear,listMonth,listWeek,listDay,list
                //     // right: 'dayGridMonth'
                // },
                handleWindowResize: true, //随浏览器窗口变化
                initialView: 'dayGridMonth', // 初始化插件显示
                // initialDate:""//初始化日期
                
                // initialEvents: INITIAL_EVENTS, // alternatively, use the `events` setting to fetch from a feed
                // editable: true, //是否可编辑
                // droppable: true,//可拖拽的
                // timeZone: 'local',//采用时区
                selectable: true,
                // selectMirror: true,
                dayMaxEvents: true,
                // weekends: true, // 是否显示一周七天
                // select: this.handleDateSelect,
                // eventClick: this.handleEventClick, // 日程点击事件
                eventMouseEnter: this.handleEventMouseEnter, // 用户将鼠标悬停在事件上时触发
                // eventsSet: this.handleEvents,
                dateClick: this.handleDateClick,//日期方格点击事件
                select: this.handleDateSelect, // 拖动，可传处理函数
                eventClick: this.handleEventClick, //日程点击事件
                datesSet: this.handleDatesSet,
                locale: zhCnLocale,
                nextDayThreshold: '01:00:00',
                events: [
                //日程事件的json
                // { title: 'event 1', date: '2021-04-23 12:00:00' },
                // { title: 'event 2', date: '2021-04-24 05:59:23' },
                // { title: 'event 3', date: '2021-04-25 08:23:00' },
                // { title: 'event 4', date: '2021-04-25 09:30:00' },
                // { title: 'event 5', date: '2021-04-26 12:00:00' },
                // { title: 'event 2', date: '2021-04-26 15:00:00' }
                ],
                eventDrop: this.handleEventDrop,//当拖拽完成并且时间改变时触发
                themeSystem: 'cosmo',
                initialDate: new Date('2017-10-28'),
                // timeZone: 'UTC',
                timezone:'local',
                timeZone: 'local',
                // eventDurationEditable: true, // 可以调整事件的时间
                // eventStartEditable: true, // 是否让事件在开始时就可以拖动
                // editable: true,
                nowIndicator: true, // 当前的时间线显示
                
                displayEventEnd: true, // like 08:00 - 13:00
                eventTimeFormat: { // like '14:30:00'
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: false,
                    hour12: false, // 设置时间为24小时
                },
                slotLabelFormat: { // 左侧时间格式
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: 'lowercase',
                    hour12: false, // false设置时间为24小时
                },
                
            },

            dialogTypeFormVisible: false,
            types: vars.types.filter(s => s.value != 1),
            currentType: 2, //2: 日程事项; 3: 客户拜访
            currentStarttime: '',
            currentEndtime: '',

            dialogFormVisible: false,
            taskEditVisible: false,
            taskId: '',
            currentProjectId: '',

              /** 代办事项 */
            dialogItemFormVisible: false,
            dialogItemStatus: "create",
            itemId: '',


            /** 客户拜访 */
            dialogCustomerVisitFormVisible: false,
            dialogCustomerVisitStatus: "create",
            visitId: '',

            startTime: null,
            endTime: null,

            ratingFormData:{
                AppealReason:'',
                ExpectationEvaluation:'',
                AppraisePersonalsStatus: 1,
                AppraisePlanId: "475a4653-120b-4692-8cb8-c928db76ad14",
                AppraisePromiseStatus: 1,
                ApprovalStatus: null,
                AttachmentList: [],
                CreateEmployeeId: "6a88d7fe-7e3e-463c-9567-fbe167fe70cd",
                CreateTime: "2020-12-02 09:13:30.000",
                Id: "42b8c78a-e713-4324-86b9-1a2395d58436",
                LastUpdateEmployeeId: "00000000-0000-0000-0000-000000000000",
                LastUpdateTime: "0001-01-01 00:00:00.000",
                LookStatus: null,
                MyAttachmentList: [],
                SelfRatingDescribe: null,
                SelfRatingEmployee: {
                    Name:''
                },
                SelfRatingEmployeeId: "9635815b-c61f-468b-a7d4-6d16362fa7d5",
                SelfRatingEvaluate: null,
                SelfRatingExplain: null,
                TeamEvaluate: null,
                TeamExplain: null,
                
                appraisePlanDetailsResponseModel:{
                    Year: '',
                    HalfYearType: 1,
                    // IsPublic: false,
                    PrincipalEmployeeList: [],
                    ObjectEmployeeList: [],
                    TeamGoals: '',
                    AttachmentList: [],

                    AppealObjectEmployeeIdList: null,
                    AppealObjectEmployeeList: [],
                    AppraiseDepartmentId: "6866227a-eff7-4190-8237-551fe7ebfe98",
                    AppraiseDepartmentName: "asdfadf",
                    AppraiseType: 1,
                    AttachmentIdList: null,
                    AttachmentList: [],
                    AutoEndType: 1,
                    CreateEmployeeId: "0d3a2247-8603-4676-826c-79d4a39d824e",
                    CreateTime: "2020-12-02 08:55:00.000",
                    Describe: "sdfasdf",
                    EndTime: "2020-12-18 00:00:00.000",
                    Id: "475a4653-120b-4692-8cb8-c928db76ad14",
                    LastUpdateEmployeeId: "00000000-0000-0000-0000-000000000000",
                    LastUpdateTime: "0001-01-01 00:00:00.000",
                    Name: "aaaaaadfasdfa",
                    ObjectEmployeeIdList: null,
                    ObjectEmployeeList: [],
                    OverallEvaluation: null,
                    PrincipalEmployeeIdList: null,
                    PrincipalEmployeeList: [],
                    ProgressStatus: 2,
                    PublicPeriodSet: 1,
                    StartTime: "2020-12-16 00:00:00.000",
                    TeamGoals: "afsdfasdfa",
                    AppraiseTypeLabel:'',
                    PublicPeriodSetLabel:'',
                    PrincipalEmployeeListLabel:'',
                    ObjectEmployeeListLabel:'',
                    AppealObjectEmployeeListLabel:'',
                },
            },
            weeks: commonEnum.vars.weekIdxEnum,
            ratesLoading: false,
            rates: [],
            monthData: {},
            weekIdx: 0,
            deductPointDialogVisible: false,
            gradeDialogVisible: false,

            monthGradeDialogStatus: 'create',
            monthGradeDialogVisible: false,
            monthDeductPointDialogVisible: false,


            dayCounterInfo: {},
            dayCounterInfoLoading: false,

            currentReviewType: 1,
            reviewTypes: [
                {value: 1, label: '总览'},
                {value: 2, label: '当日对标'},
            ],
            isShowSalesPoint: false,

            rateDetailDialogVisible: false,

            commonLoading: false,

        
        };
    },
    methods: {
        getIsSalesman(employeeId) {
            this.isShowSalesPoint = false
            businessManager.IsSalesman({employeeId}).then(res => {
                if(res) {
                    this.isShowSalesPoint = res.IsShowSalesPoint
                }

                let idx = this.typesData.findIndex(s => s.value == 6)
                if(res && idx == -1) { //是销售人员，且不存在“销售人员选项”，加进去
                    this.typesData.splice(this.typesData.length - 1, 0, {
                        value: "6",
                        label: "销售数据"
                    })
                }else if(!res && idx > -1){ //不是销售人员，且存在，则删除
                    this.typesData.splice(idx, 1)
                }
            })
        },

        getvisitPlan(){
           let postDatas = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }

            if(postDatas.Month == 0){
                 delete postDatas.Month
            }

            visitPlanMgmt.getDataStatistics(postDatas).then(res => {
                this.visitPlanDataStatistics = res
            });
        },

        getOpportunityDataStatistics() {
            let postDatas = {
                    Year: this.listQuery.Year,
                    Month: this.listQuery.Month,
                    EmployeeId: this.currentId
                }

            if(postDatas.Month == 0){
                 delete postDatas.Month
            }

            businessOpportunity.getDataStatistics(postDatas).then(res => {
                this.businessDataStatistics = res
            });
        },

        getBusiness(){
         
            let postDatas = {
                    Year: this.listQuery.Year,
                    Month: this.listQuery.Month,
                    EmployeeId: this.currentId
                }

            if(postDatas.Month == 0){
                 delete postDatas.Month
            }
            
            this.getOpportunityDataStatistics(postDatas)

            businessOpportunity.getPortrait(postDatas).then(res => {
                this.businessPortrait = res


                this.barEchartOption3 = this._initOpportunityBarChartDatas(
                    res.SourceTypeList || []
                );

                let targetOption1 = {}
                if(res.OpportunityStateList) {
                    let chartDatas = res.OpportunityStateList
                    targetOption1 = {
                        legend: {
                             icon: "circle",
                             top: 'bottom',
                             data: chartDatas.map(s => s.name)
                        },
                        series: [{
                            center: ['35%', '50%'],
                            data: chartDatas.map(s => {
                                return {
                                    value: s.value === 0 ? null : s.value,
                                    name: s.name
                                }
                            })
                        }],
                        color: this.picColors
                    }
                   this.pieEchartOption4 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)
                }

            });
        },

       _initOpportunityBarChartDatas(list) {
            if (!list) {
                list = [];
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []));
            let targetOption = {};
            if (chartDatas && chartDatas.length > 0) {
                targetOption = {
                grid: {
                    left: 40,
                },
                xAxis: {
                    data: chartDatas.map((s) => s.name),
                },
                yAxis: {
                    splitLine: {
                    show: true,
                    lineStyle: {
                        type: "solid",
                        color: "#E9EAEF", // 颜色
                    },
                    },
                },
                series: [
                    {
                    data: chartDatas.map((s) => s.value),
                    },
                ],
            };
          }

            targetOption = _.merge(
                {},
                JSON.parse(JSON.stringify(barOption)),
                targetOption
            );

          return targetOption;
        },
        getOrderDataStatistics() {
            let postDatas = {
                    Year: this.listQuery.Year,
                    Month: this.listQuery.Month,
                    EmployeeId: this.currentId
                }

            if(postDatas.Month == 0){
                 delete postDatas.Month
            }
            orderMgmt.getDataStatistics(postDatas).then(res => {
                this.orderDataStatistics = res
            });
        },
        getOrder(){
         
            let postDatas = {
                    Year: this.listQuery.Year,
                    Month: this.listQuery.Month,
                    EmployeeId: this.currentId
                }

            if(postDatas.Month == 0){
                 delete postDatas.Month
            }

            this.getOrderDataStatistics(postDatas)

            orderMgmt.getPortrait(postDatas).then(res => {
                this.orderPortrait = res
                this.barEchartOption2 = this._initOrderBarChartDatas(
                    res.SourceTypeList || []
                );

            });
        },

        _initOrderBarChartDatas(list) {
            if (!list) {
                list = [];
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []));
            let targetOption = {};
            if (chartDatas && chartDatas.length > 0) {
                targetOption = {
                grid: {
                    left: 40,
                },
                xAxis: {
                    data: chartDatas.map((s) => s.name),
                },
                yAxis: {
                    splitLine: {
                    show: true,
                    lineStyle: {
                        type: "solid",
                        color: "#E9EAEF", // 颜色
                    },
                    },
                },
                series: [
                    {
                    data: chartDatas.map((s) => s.value),
                    },
                ],
            };
         }

        targetOption = _.merge(
            {},
            JSON.parse(JSON.stringify(barOption)),
            targetOption
        );

      return targetOption;
    },

       _initBarChartDatas(list) {
            if (!list) {
                list = [];
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []));
            let targetOption = {};
            if (chartDatas && chartDatas.length > 0) {
                targetOption = {
                grid: {
                    left: 40,
                },
                xAxis: {
                    data: chartDatas.map((s) => s.name),
                },
                yAxis: {
                    splitLine: {
                    show: true,
                    lineStyle: {
                        type: "solid",
                        color: "#E9EAEF", // 颜色
                    },
                    },
                },
                series: [
                    {
                    data: chartDatas.map((s) => s.value),
                    },
                ],
                };
            }

            targetOption = _.merge(
                {},
                JSON.parse(JSON.stringify(barOption)),
                targetOption
            );

            return targetOption;
        },

        getCustomerDataStatistics(){
         
          let postDatas = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }

            if(postDatas.Month == 0){
               delete postDatas.Month
            }

          customerRelationLine.getCustomerDataStatistics(postDatas).then(res => {
                this.customerDataStatisticsDatas = res
            });
        },

        getGetCustomerPortrait(){
            let postDatas = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }

            if(postDatas.Month == 0){
               delete postDatas.Month
            }

          customerRelationLine.getGetCustomerPortrait(postDatas).then(res => {
            
                if(res.FriendlinessList) {
                      
                    let chartObj = this.$refs.lineFriendlinessChart
                    if(chartObj){
                         this.lineFriendlinessChart = echarts.init(chartObj);
                         this.lineFriendlinessChart.showLoading({
                                text: '加载中...',
                                textStyle: { fontSize : 30 , color: '#444' },
                                effectOption: {backgroundColor: 'rgba(0, 0, 0, 0)'}
                            });

                        this.optionFriendlinessLine.xAxis = {
                                axisLabel: {
                                    rotate: 25,
                                    color: 'black',
                                    interval: 0,
                                },
                                type: 'category',
                                boundaryGap: false,
                                data: ['一面之缘', '初步认识', '相熟相知', '交心友人', '情如老铁']
                        },
                    
                        this.optionFriendlinessLine.series = [{
                            name: '',
                            type: 'line',
                            data: res.FriendlinessList.map(s => s.value) 
                        },
                        ];
                        
                        this.lineFriendlinessChart.setOption(this.optionFriendlinessLine, true);
                        this.lineFriendlinessChart.hideLoading();
                        this.$nextTick(() => {
                            this.lineFriendlinessChart.resize();
                        })
                        window.addEventListener('resize', () => {
                            this.lineFriendlinessChart.resize();
                        })
                    }
                        
                   
                }


                this.customerPortraitDatas = res
                let targetOption1 = {}
                 if(res.StreamAnalysisChartList) {
                    let chartDatas = res.StreamAnalysisChartList
                    targetOption1 = {
                       
                        legend: {
                             icon: "circle",
                             top: 'bottom',
                             data: chartDatas.map(s => s.name)
                        },
                        series: [{
                            center: ['35%', '50%'],
                            data: chartDatas.map(s => {
                                return {
                                    value: s.value === 0 ? null : s.value,
                                    name: s.name
                                }
                            })
                        }],
                        color: this.picColors
                    }

                }
                this.pieEchartOption1 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)
                
                 if(res.IntentionAnalysisList) {
                     let chartDatas = res.IntentionAnalysisList
                     this.barEchartOption1 = this._initBarChartDatas(chartDatas)
                }
                
            });
        },


         getClueDataStatistics(){
         
            let postDatas = {
                    Year: this.listQuery.Year,
                    Month: this.listQuery.Month,
                    EmployeeId: this.currentId
                }

                if(postDatas.Month == 0){
                delete postDatas.Month
                }

            clueManager.getDataStatistics(postDatas).then(res => {
                    this.clueDataStatistics = res
            });
        },

         getClueDataPortrait(){
            let postDatas = {
                    Year: this.listQuery.Year,
                    Month: this.listQuery.Month,
                    EmployeeId: this.currentId
                }

                if(postDatas.Month == 0){
                delete postDatas.Month
                }

            clueManager.getPortrait(postDatas).then(res => {
                this.clueDataPortrait = res
                let targetOption1 = {}
                if(res.OpportunityThreadStateList) {
                    let chartDatas = res.OpportunityThreadStateList
                    targetOption1 = {
                       
                        legend: {
                             icon: "circle",
                             top: 'bottom',
                             data: chartDatas.map(s => s.name)
                        },
                        series: [{
                            center: ['35%', '50%'],
                            data: chartDatas.map(s => {
                                return {
                                    value: s.value === 0 ? null : s.value,
                                    name: s.name
                                }
                            })
                        }],
                        color: this.picColors
                    }
                   this.pieEchartOption2 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)
                }
                 
                let targetOption2 = {} 
                if(res.SourceTypeList) {
                    let chartDatas = res.SourceTypeList
                    targetOption2 = {
                       
                        legend: {
                             icon: "circle",
                             top: 'bottom',
                             data: chartDatas.map(s => s.name)
                        },
                        series: [{
                            center: ['35%', '50%'],
                            data: chartDatas.map(s => {
                                return {
                                    value: s.value === 0 ? null : s.value,
                                    name: s.name
                                }
                            })
                        }],
                        color: this.picColors
                    }
                   this.pieEchartOption3 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption2)
                }
                
            });
        },


         getProjectInfoStatistics(){
         
            let postDatas = {
                    Year: this.listQuery.Year,
                    EmployeeId: this.currentId
            }
            this.setCommonLoading(true)
            team.getProjectInfoStatistics(postDatas).then(res => {
                this.setCommonLoading(false)
                this.projectInfoStatistics = res
            }).catch(err => {
                this.setCommonLoading(false)
            });
        },

         getQuestionStatistics(){
         
            let postDatas = {
                    Year: this.listQuery.Year,
                    EmployeeId: this.currentId
            }

            team.getQuestionStatistics(postDatas).then(res => {
                   // this.QuestionStatistics = res
                   //console.log(res)
                    // let chartObj = this.$refs.lineChart
                    this.lineChart = echarts.init(document.getElementById('lineChart'));
                    let len = 12 //有多少个月
                    
                    this.lineChart.showLoading({
                        text: '加载中...',
                        textStyle: { fontSize : 30 , color: '#444' },
                        effectOption: {backgroundColor: 'rgba(0, 0, 0, 0)'}
                    });

                    if(res) {
                        this.optionLine.xAxis = {
                            type: 'category',
                            boundaryGap: false,
                            data: Array.from(Array(len), (v,k) => {
                                return `${k + 1}月`
                            })
                            // data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
                        },
                        this.optionLine.series = [{
                            name: '已完成',
                            type: 'line',
                            data: res.CompleteChartData.map(s => s.Value) 
                            // data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
                        },
                        {
                            name: '未完成',
                            type: 'line',
                            data: res.UnfinishedChartData.map(s => s.Value) 
                            // data: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
                        }];
                        
                        this.lineChart.setOption(this.optionLine);
                        this.lineChart.hideLoading();
                        this.$nextTick(() => {
                            this.lineChart.resize();
                        })
                        window.addEventListener('resize', () => {
                            this.lineChart.resize();
                        })
                    }
            });
        },
          
                                                       

        handleRating(id){
            this.currentYearId = id
            this.getRatingDetails();
        },
         
        getRatingDetails(){

            this.setCommonLoading(true)
            myAchievements.detail({id: this.currentYearId,isApprovaled:false}).then(res => {
                this.setCommonLoading(false)
                this.ratingFormData = Object.assign({}, this.ratingFormData, res)
                this.ratingFormData.ExpectationEvaluation=this.ratingFormData.ExpectationEvaluation ? this.ratingFormData.ExpectationEvaluation : '';
                //通过枚举查询值
                let a=null;
                a=appraiseTypeEnum.find(s => s.value == this.ratingFormData.appraisePlanDetailsResponseModel.AppraiseType);
                this.ratingFormData.appraisePlanDetailsResponseModel.AppraiseTypeLabel=a.label;
                if(this.ratingFormData.appraisePlanDetailsResponseModel.PublicPeriodSet == 1){
                    this.ratingFormData.appraisePlanDetailsResponseModel.PublicPeriodSetLabel=this.settingPublicityOptions[0].label;
                }else{
                    a=this.autoEndTimeOptions.find(s => s.value == this.ratingFormData.appraisePlanDetailsResponseModel.AutoEndType);
                    this.ratingFormData.appraisePlanDetailsResponseModel.PublicPeriodSetLabel= a.label+'自动结束考核';
                }
                this.ratingFormData.appraisePlanDetailsResponseModel.PrincipalEmployeeListLabel=this.pELfilter(this.ratingFormData.appraisePlanDetailsResponseModel.PrincipalEmployeeList);
                this.ratingFormData.appraisePlanDetailsResponseModel.ObjectEmployeeListLabel=this.pELfilter(this.ratingFormData.appraisePlanDetailsResponseModel.ObjectEmployeeList);
                this.ratingFormData.appraisePlanDetailsResponseModel.AppealObjectEmployeeListLabel=this.pELfilter(this.ratingFormData.appraisePlanDetailsResponseModel.AppealObjectEmployeeList);

            }).catch(err => {
                this.setCommonLoading(false)
            })
        } ,

        getSelfRatingList() {
            this.setCommonLoading(true)
            myAchievements.getList({
                PageIndex:1,
                PageSize:1000,
                "appraisePlanId": "",
                "type": 1,
                SelfRatingEmployeeId:this.currentId
            },).then(res => {
                this.setCommonLoading(false)
                this.selfRatingDatas = res.Items;
                if(this.selfRatingDatas && this.selfRatingDatas.length>0){
                    this.currentYearId = this.selfRatingDatas[0].Id
                    this.getRatingDetails()
                }
            }).catch(err => {
                this.setCommonLoading(false)
            });
        },

        getSharedPeopleStatistics(){
            trainApi
                .getSharedPeopleStatistics({EmployeeId:this.currentId,TrainsClassificationId: "2c347efb-7e2f-4c97-aacf-c43fd25096a6"})
                .then(res => {
                    this.sharedPeopleStatistics = res;
                })
                .catch(err => {
              
                });
        },

        handlePrintRow(row) {
            let type = 3
            if(row.ApprovalModuleSubtypeType == 42) {
                type = 3
            }else if(row.ApprovalModuleSubtypeType == 43) {
                type = 6
            }
            //3：出差；出差补助：6
            this.$refs.print.handlePrintRow({type: type, id: row.CurrentBusinessId, approvalId: row.Id})
        },

        transTime(val) {
            return minuteFilter(val)
        },

        freshData(){
            this.currentDate = new Date(this.listQuery.Year, this.listQuery.Month - 1, 1);
            // if(this.type === "2"){
            //     if(this.listQuery.Month != 0){
            //        this.getPersonalTimecard()
            //     }
            //     this.getProcessList()
            //     this.getSummary()
            // }else if(this.type === "3"){

            //     this.getTask()
            //     if(this.listQuery.Month != 0){
            //         this.getDailyDatas()
            //     }
            // }else if(this.type === "6"){
            //   this.getCustomerDataStatistics()
            //   this.getGetCustomerPortrait()
            //   this.getClueDataStatistics ()
            //   this.getClueDataPortrait()
            // }else if(this.type === "7"){
            //   this.getProjectInfoStatistics()
            //   this.getQuestionStatistics()
            // }

            if(this.type == '1') {
                this.getEmployeeRecordDetails() //员工档案——获取成员档案信息
            }else if(this.type == '2') {
                this.getSummary()//考情数据——总览信息
                if(this.listQuery.Month != 0){
                   this.getPersonalTimecard()//考情数据——考情月历
                }
                this.getProcessList()//考情数据——流程记录
            }else if(this.type == '3') {
                this.getTask()//工作数据——总览信息
                if(this.listQuery.Month != 0){
                    this.getDailyDatas()//工作数据——任务详情
                }
            }else if(this.type == '4') {
                this.getSharedList()//读书分享——分享详情
                this.getSharedPeopleStatistics()//读书分享——总览信息
            }else if(this.type == '5') {
                this.getSelfRatingList()//绩效数据——绩效详情
            }else if(this.type == '6' && this.currentReviewType == 1) {
                this.getCustomerDataStatistics()//销售数据——客户数据（统计）
                this.getGetCustomerPortrait()//销售数据
                this.getClueDataStatistics()//销售数据
                this.getClueDataPortrait()//销售数据
                this.getBusiness()//销售数据
                this.getOrder()//销售数据
                this.getvisitPlan()//销售数据
                this.getSalesPoint()
            }else if(this.type == '6' && this.currentReviewType == 2) {
                this.getDayCounter()
            }else if(this.type == '7') {
                this.getProjectInfoStatistics()//研发数据
                this.getQuestionStatistics()//研发数据
            }
        },

           /** 任务（常规任务、项目任务） */
        handleDialog(id) {
            this.taskId = id
            this.dialogFormVisible = true
        },
        closeCreateCommTask() {
            this.dialogFormVisible = false
        },

        handleTaskDialog(id, projId) {
            this.taskId = id
            this.currentProjectId = projId
            this.taskEditVisible = true
        },
        closeCreateTask() {
            this.taskEditVisible = false
        },

        /** 代办事项 */
        handleEditItemDialog(id) {
            this.itemId = id
            this.dialogItemStatus = 'update';
            this.dialogItemFormVisible = true;
        },
        handleAddItemDialog(startTime, endTime) {
            this.startTime = startTime
            this.endTime = endTime

            this.dialogItemStatus = 'create';
            this.dialogItemFormVisible = true;
        },
        closeItemDialog() {
            this.dialogItemFormVisible = false;
        },
        handleSaveItemSuccess(model, _formData) {
            this.closeItemDialog();
            this.getDailyDatas()
        },
        
        /** 客户拜访 */
        handleCustomerVisitDialog(id) {
            this.visitId = id
            this.dialogCustomerVisitStatus = 'update';
            this.dialogCustomerVisitFormVisible = true;
        },
        handleAddCustomerVisitDialog(startTime, endTime) {
            this.startTime = startTime
            this.endTime = endTime

            this.dialogCustomerVisitStatus = 'create';
            this.dialogCustomerVisitFormVisible = true;
        },
        closeCustomerVisitDialog() {
            this.dialogCustomerVisitFormVisible = false;
        },
        handleSaveCustomerVisitSuccess(model, _formData) {
            this.closeCustomerVisitDialog();
            this.getDatas()
        },
        handleDel(tempId) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$notify({
                    title: "成功",
                    message: "删除成功",
                    type: "success",
                    duration: 2000
                });
                mw.delSchedules([tempId]).then(res => {
                    let idx = this.calendarOptions.events.findIndex(s => s.id == tempId)
                    if(idx > -1) {
                        this.calendarOptions.events.splice(idx, 1)
                    }
                })
            });
        },

        getDailyDatas() {

        
            // CreateEmployee: null
            // CreateEmployeeId: "00000000-0000-0000-0000-000000000000"
            // CreateTime: "2022-07-06 15:16:45.000"
            // Descript: "2222222222222222"
            // EndTime: "2022-07-15 00:00:00.000"
            // Id: "d0359836-fd9e-4919-b116-be58bd14397d"
            // Name: "22222"
            // Remind: true
            // StartTime: "2022-07-06 00:00:00.000"
            // Status: 1
            // TimeType: 1
            
            let day = dayjs(this.listQuery.Year+"-"+this.listQuery.Month).format("YYYY-MM")
            //获取指定日期所在月的第一天
            this.queryStart = dayjs(day).startOf("month").format("YYYY-MM-DD");
            //获取指定日期所在月的第一天
            this.queryEnd = dayjs(day).endOf("month").format("YYYY-MM-DD");


            this.search()

            this.setCommonLoading(true)
            this.taskLoading = true
            this.calendarOptions.events = []
            // let postDatas = {
            //     EmployeeId:this.currentId,
            //     StartTime: firstDate,
            //     EndTime: lastDate
            // }
              let postDatas = {
                EmployeeId:this.currentId,
                StartTime: this.queryStart,
                EndTime: this.queryEnd,
                LoadOnlyTask: true
            }
            mw.getScheduleListByCondition(postDatas).then(res => {
                this.setCommonLoading(false)
                this.taskLoading = false

                this.calendarOptions.events = res.map(s => {
                    let result = {
                        id: s.Id,
                        title: s.Name, 
                        start: dayjs(s.StartTime).format('YYYY-MM-DD HH:mm:ss'),
                        end: dayjs(s.EndTime).format('YYYY-MM-DD HH:mm:ss'),//展示到日历上需要加1，因为入库时减了1
                        // allDay: s.ScheduleBusienssType == 3 ? false : true,
                        extendedProps: {
                            scheduleBusienssType: s.ScheduleBusienssType, //1.工作任务 2.日程事项 3.客户拜访
                            workPlanTaskType: s.WorkPlanTaskType,//工作任务分为（1：常规任务；2：项目任务）
                            projectId: s.ProjectId, //产品id
                        },
                    }

                    //日程1号-2号表示一天；工作任务（1号-1号表示一天，所以“工作任务”的结束时间需要加1天）
                    if(s.ScheduleBusienssType == 1) {
                        result.end = dayjs(result.end).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
                        result.backgroundColor = '#e6a23c'
                        result.borderColor = '#e6a23c'
                    }
                    
                    if(s.ScheduleBusienssType == 3) {
                        result.allDay = false
                    } else if(result.start.indexOf(' 00:00:00') > -1 && result.end.indexOf(' 00:00:00') > -1) { //如果是“整天”任务，allDay=true；否则为false（方便在周、日视图上展示）
                        result.allDay = true
                    }else{
                        result.allDay = false
                    }
                    return result
                })
            }).catch(err => {
                this.setCommonLoading(false)
                this.taskLoading = false
            })
        },

         handleEventMouseEnter:function (info) {//鼠标hover事件，对应也有其他鼠标事件监听
            let col = info.event.borderColor;
            let eve = info.event._def.extendedProps
            info.el.title = info.event._def.title
            // tippy 组件（info.el）
        },
        handleDatesSet(info) {
            let startTimeTemp = dayjs(info.start).format('YYYY-MM-DD')
            let endTimeTemp = dayjs(info.end).format('YYYY-MM-DD')
           // console.log(info,startTimeTemp,endTimeTemp)
            if (this.queryStart !== startTimeTemp && this.queryEnd !== endTimeTemp) {
                this.queryStart = startTimeTemp
                this.queryEnd = endTimeTemp
                this.getDatas()
            }
        },
        handleEventDrop(event, dayDelta, minuteDelta, allDay, revertFunc, jsEvent, ui, view) {

        },
        handleEventClick(info) {
            // info.el.style.borderColor = 'red';
            // console.log(JSON.stringify(info.event))

            let tempId = info.event.id

            let tempType = info.event.extendedProps.scheduleBusienssType //1.工作任务 2.日程事项 3.客户拜访

            if (tempId !== prev) {
                clickCount = 0;
            }
            clickCount += 1;
            
            prev = tempId;
            setTimeout(() => {
                if (clickCount === 2 && tempType != 1) { //双击删除（“工作任务”不能删除）
                    this.handleDel(tempId)
                } else if (clickCount === 1 || tempType == 1) {
                    if(tempType == 1) {
                        let workType = info.event.extendedProps.workPlanTaskType
                        if(workType == 1) {
                            this.handleDialog(tempId)
                        }else if(workType == 2) {
                            let projId = info.event.extendedProps.projectId
                            this.handleTaskDialog(tempId, projId)
                        }
                    }else if(tempType == 2) {
                        this.handleEditItemDialog(tempId)
                    }else if(tempType == 3) {
                        this.handleCustomerVisitDialog(tempId)
                    }
                }
                clickCount = 0;
            }, 300)
            
        },
        handleDateClick(arg) {
            // if (confirm('您是否要在' + arg.dateStr + '添加一个新的事件?')) {
            //     this.calendarOptions.events.push({ // add new event data
            //       title: '新的事件',
            //       start: arg.date,
            //       allDay: arg.allDay
            //     })
            // }
        },
        handleDateSelect(arg) {
            this.currentStarttime = dayjs(arg.startStr).format('YYYY-MM-DD HH:mm:ss')
            //结束时间会多一天，所以要减去1
            let endTime = dayjs(arg.endStr).format('YYYY-MM-DD HH:mm:ss')
            this.currentEndtime = endTime
            this.handleTypeDialog(this.currentStarttime,this.currentEndtime)
        },
        /** 自定义日期头 */
        prev() {
            this.calendarApi.prev();
            this.title = this.calendarApi.view?.title;
        },
        next() {
            this.calendarApi.next();
            this.title = this.calendarApi.view?.title;
        },
        today() {
            this.calendarApi.today();
            this.title = this.calendarApi.view?.title;
        },
        month() {
            this.calendarApi.changeView('dayGridMonth');
            this.calendarApi.today();
            this.title = this.calendarApi.view?.title;
        },
        week() {
            this.calendarApi.changeView('timeGridWeek');
            this.calendarApi.today();
            this.title = this.calendarApi.view?.title;
        },
        day() {
            this.calendarApi.changeView('timeGridDay');
            this.calendarApi.today();
            this.title = this.calendarApi.view?.title;
            this.getDatas()
        },
        search() {
            if(this.queryStart && this.calendarApi){
               this.calendarApi.gotoDate(this.queryStart);
            }
        },
        handleMore() {},
        
        /** 类型选择弹框 */
        handleTypeDialog() {
            this.dialogTypeFormVisible = true
        },
        closeTypeDialog() {
            this.currentType = 2
            this.dialogTypeFormVisible = false
        },
        handleTypeSaveSuccess() {
            let that = this
            if(that.currentType == 2) { //日程事项
                that.handleAddItemDialog(that.currentStarttime, that.currentEndtime)
                that.$nextTick(() => {
                    that.closeTypeDialog()
                })
            }else if(that.currentType == 3){ //客户拜访
                that.handleAddCustomerVisitDialog(that.currentStarttime, that.currentEndtime)
                that.$nextTick(() => {
                    that.closeTypeDialog()
                })
            }
        },
     
        getProcessList(){
            this.listLoading = true;
            let postDatas = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }

            if(this.listQuery.Month === 0){
               delete postDatas.Month
            }

            approvalManagement
                .getSignHRApprovalProcessByMonth(postDatas)
                .then(res => {
                this.listLoading = false;
                this.tabDatas = res;
                })
                .catch(err => {
                this.listLoading = false;
                });
        },
         
        getOptTypeDesc(type) {
            let tmp = approvalVars.vars.approvalStatuObj.approvalModuleSubtypeTypes.find(
                s => s.value == type
            );
            if (tmp) {
                return tmp.label;
            }
            return "";
        }, 

        handleBulletChangeDialog(){
          this.dialogBulletChangeFormVisible=true;
        },

        handleVcateChangeDialog() {
          this.dialogVaVisible = true;
        },
        closeVaDialog() {
          this.dialogVaVisible = false;
        },

        handleOvertimeChangeDialog() {
          this.dialogOvertimeVisible = true;
        },
        closeOveretimeDialog() {
          this.dialogOvertimeVisible = false;
        },

        handleEvectionChangeDialog() {
          this.dialogEvVisible = true;
        },
        closeEvDialog() {
          this.dialogEvVisible = false;
        },

        handleAttenChangeDialog(){
          this.dialogAttenVisible=true;
        },
        closeAttenDialog(){
          this.dialogAttenVisible=false;
        },


        handleGoOutChangeDialog() {
          this.dialogGoOutVisible = true;
        },
        closeGoOutDialog() {
          this.dialogGoOutVisible = false;
        },


        handleAttendanceApprovalDialog() {
          this.dialogAttendanceApprovalFormVisible = true;
        },
        closeAttendanceApprovalDialog() {
          this.dialogAttendanceApprovalFormVisible = false;
        },
        

        handleShowDetail(row) {

            this.id = row.CurrentBusinessId;
            
            // 不同模块下可能有多个操作需要审批，例如：项目研发 下 项目创建、项目验收 需要审批，他们调用的审批页面不同
            // let moduleType = row.ApprovalModuleType //模块（1：项目研发）
            // if(moduleType == 1) { //项目研发
            // }
            let optType = row.ApprovalModuleSubtypeType;
            this.optType = optType

            this.approvalId = row.Id;
            
            if(optType == 27) { //考核申诉
                this.handleBulletChangeDialog();
            }else if(optType == 28 || optType == 33) { //请假（撤销 请假申请）
                // if(optType == 33) {
                //   this.dialogVacateStatus = 'revokeApproval';
                // }
                this.handleVcateChangeDialog();
            }else if(optType == 29 || optType == 34) { //加班（撤销 加班申请）
                // if(optType == 34) {
                //   this.dialogOvertimeStatus = 'revokeApproval';
                // }
                this.handleOvertimeChangeDialog();
            }else if(optType == 30 || optType == 35) { //出差（撤销 出差申请）
                // if(optType == 35) {
                //   this.dialogEvectionStatus = 'revokeApproval';
                // }
                this.handleEvectionChangeDialog();
            }else if(optType == 31) { //考勤申诉
                this.handleAttenChangeDialog();
            }else if(optType == 32 || optType == 36) { //外出申请（撤销 外出申请）
                // if(optType == 36) {
                //   this.dialogGoOutStatus = 'revokeApproval';
                // }
                this.handleGoOutChangeDialog()
            }else if(optType == 37) {
                this.handleAttendanceApprovalDialog();
            }else if(optType == 42) {
                this.handleOverEvection()
            }else if(optType == 43) {
                this.handleEvSubSidyDialog()
            }
        },

        handleOverEvection() {
            this.dialogEvOverVisible = true
        },
        closeEvOverDialog() {
            this.dialogEvOverVisible=false;
         },
        handleEvSubSidyDialog() {
            this.dialogEvSubSidyVisible = true
         },
        closeEvSubSidyDialog() {
            this.dialogEvSubSidyVisible = false
         },

        getStatusObj(val) {
            return approvalVars.vars.approvalStatuObj.approvalStatus.find(
                s => s.value == val
            ) || {};
        },

        handleAcShow(d, curCellData){
            this.time=d.day;
            this.currentCellData = curCellData
            this.dialogAcStatus='create';
            this.id=this.employeeid;
            this.dialogAcFormVisible=true;
        },

        getPersonalTimecard(){
            let postDatas = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }
            timecardDepartment.getPersonalTimecardRecords(postDatas).then(res => {
                this.datas = initCellData(res)
            }).catch(err => {
            });
            
        },

        //当前日期是否存在当前月份中
        onMonth(day) {
            let currentDay = dayjs(day)
            if(currentDay.isValid()) { 
                return currentDay.year() === this.listQuery.Year && (currentDay.month() + 1) === this.listQuery.Month
            }
            return false
        },

        getDatas(date) {
            if(dayjs(date).isValid()) {
                let currentDate = dayjs(date).format('YYYY-MM-DD')
                if(this.datas) {
                    return this.datas.find(s => s.Date == currentDate)
                }
            }
            return null
        },


        //获取成员档案信息
        getEmployeeRecordDetails(isAll) {
            if(isAll) {
                this.detailFormDataLoading = true;
            }else{
                this.setCommonLoading(true)
            }
            systemEmployee.getEmployeeRecordDetails({
                id: this.currentId
            }).then(res => {
                if(isAll) {
                    this.detailFormDataLoading = false;
                }else{
                    this.setCommonLoading(false)
                }
                var formObj = Object.assign({}, this.detailFormData, res);
                this.detailFormData = formObj;
            }).catch(err => {
                if(isAll) {
                    this.detailFormDataLoading = false;
                }else{
                    this.setCommonLoading(false)
                }
            });

             systemEmployee.detail({
                id: this.currentId
            }).then(res => {
                var formObj = Object.assign({}, this.basicsFormData, res);
                this.basicsFormData = formObj;
            });

             systemEmployee.getOverviewDetails({id: this.currentId}).then(res => {
                this.empInfo = res
            }).catch(err => {
            })
        },


         getSummary() {
            let postData = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }
            if(postData.Month === 0) { //没有月份，表示全年
                //postData.Month = this.listQuery.Month
                delete postData.Month
            }
            this.summaryLoading = true
            this.setCommonLoading(true)
            timecardDepartment.getTimecardReport(postData).then((res) => {
                this.summaryLoading = false
                this.setCommonLoading(false)
                if(res.Items && res.Items.length == 1) {
                    let obj = res.Items[0]
                    this.summaryList.forEach(col => {
                        let fieldTemp = col.field
                        if(obj[fieldTemp]) {
                            col.value = obj[fieldTemp].Value
                        }
                    });
                }
            }).catch(err => {
                this.setCommonLoading(false)
                this.summaryLoading = false
            })
        },

        getTask(){
            //tab 不等于 3，不需要加载 myChart 对应的 echarts 图
            if(this.type != 3) {
                return false
            }

            let postData = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }

            if(this.listQuery.Month === 0){
                delete postData.Month
            }

            this.$nextTick(() => {
                let chartObj = this.$refs.myChart
                this.myChart = echarts.init(chartObj);
    
                this.myChart.showLoading({
                    text: '加载中...',
                    textStyle: { fontSize : 30 , color: '#444' },
                    effectOption: {backgroundColor: 'rgba(0, 0, 0, 0)'}
                });
                wordPlan.chartByEmployee(postData).then(res => {
                    this.taskSummary = res
                    
                    let len = res.ShouldCompleteChartData.length //有多少个月

                    if(chartObj) {
                        this.option.xAxis = {
                            type: 'category',
                            boundaryGap: false,
                            data: Array.from(Array(len), (v,k) => {
                                return `${k + 1}月`
                            })
                            // data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
                        },
                        this.option.series = [{
                            name: '应完成',
                            type: 'line',
                            data: res.ShouldCompleteChartData.map(s => s.Value) 
                            // data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
                        },
                        {
                            name: '已完成',
                            type: 'line',
                            data: res.CompleteChartData.map(s => s.Value) 
                            // data: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
                        }];
                        
                        this.myChart.setOption(this.option, true);
                        this.myChart.hideLoading();
                        this.$nextTick(() => {
                            this.myChart.resize();
                        })
                        window.addEventListener('resize', () => {
                            this.myChart.resize();
                        })
                    }
                })
            })
           

        },


        getSharedList(){

            let postData = {
                CurrentIsWillLearn: 0,
                PageIndex: this.query.PageIndex,
                PageSize: this.query.PageSize,
                PageType: "onLineTraining",
                TrainsClassificationId: "2c347efb-7e2f-4c97-aacf-c43fd25096a6"
            }
            this.setCommonLoading(true)
            this.classLoading = true
            trainApi.getList(postData).then(res => {
                this.setCommonLoading(false)
                 this.classLoading = false
                 this.classList = res.Items;
                 this.total = res.Total
                 this.trainId = this.classList[0].Id
                 this.getTrainLearningLessonByEmployeeId()
                }).catch(err => {
                  this.setCommonLoading(false)
                  this.classLoading = false
                });

        },


         handleCurrentChange(val) {
            this.query.PageIndex = val.page;
            this.query.PageSize = val.size;
            this.getSharedList();
        },


        getTrainLearningLessonByEmployeeId(){
            this.setCommonLoading(true)
            this.sharedContentLoading = true
             trainApi.getTrainLearningLessonByEmployeeId({
                      trainId:this.trainId,
                      employeeId:this.currentId
             }).then(res => {
                this.setCommonLoading(false)
                this.sharedContentLoading = false
                this.sharedContent = res
             }).catch(err => {
                this.setCommonLoading(false)
                this.sharedContentLoading = false
             });
        },


        changeClass(trainId){
          this.trainId = trainId
          this.getTrainLearningLessonByEmployeeId()
        },

      
    
        /***************************************左侧树菜单操作***************************************/
        getTree(){
            let q1 = businessRoleApi.getOnlyLookOneselfData({businessRoleTypeEnum: 9})
            let q2 = trainApi.getDepartmentEmployeeTree({EnableDataPermissions: true})

            this.loading = true
            Promise.all([q1, q2]).then(result => {
                this.loading = false
                //是否只能够查看自己
                let isOnly = result[0] || false

                let res = result[1]
                let dataList = res.reduce((acc, cur) => {
                    if(acc.findIndex(s => s.Id == cur.Id) == -1) {
                        //添加部门
                        let dept = {
                            Type: 1, //部门
                            EmployeeName: cur.DepartmentName,
                            LearningLessonId: null,
                            Avatar: '',
                            value: cur.Id,
                            Title: cur.Title,
                            Content: cur.Content,
                            Id: cur.Id,
                            GiveLikeNum: cur.GiveLikeNum,
                            // IsTheCurrentUserThumbUp: cur.IsTheCurrentUserThumbUp,
                            ParentId: cur.ParentId
                        }
                        acc.push(dept)

                        //添加部门人员
                        let list = cur.EmployeeList
                        if(list && list.length > 0) {
                            list.forEach(s => {
                                if(acc.findIndex(ss => ss.Id == s.Id) == -1) {
                                    let emp = {
                                        Type: 2, //人员
                                        LearningLessonId: s.LearningLessonId,
                                        EmployeeName: s.EmployeeDto.Name,
                                        Avatar: s.EmployeeDto.Avatar,
                                        value: s.EmployeeId,
                                        Title: s.Title,
                                        Content: s.Content,
                                        Id: s.EmployeeId,
                                        GiveLikeNum: s.GiveLikeNum,
                                        IsTheCurrentUserThumbUp: s.IsTheCurrentUserThumbUp,
                                        ParentId: dept.Id
                                    }
                                    acc.push(emp)
                                }
                            });
                        }
                    }
                    return acc
                }, []);

                let temp = dataList.filter(s => !s.ParentId).map(s => s.Id) || []
                this.epKeys = temp

                let treeDatas = listToTreeSelect(dataList);

                this.tableData = treeDatas

                //如果只能看自己，则选中当前用户
                if(isOnly) {
                    this.$nextTick(() => {
                        let empId = getUserInfo().employeeid
                        
                        //选中高亮节点
                        this.$refs.treeRef.setCurrentKey(empId);
                        this.$refs.treeRef.setCheckedKeys([empId]);

                        //默认展开节点
                        this.epKeys = [empId]

                        //模拟点击，加载右侧数据
                        let currentNode = this.$refs.treeRef.getCurrentNode()
                        this.handleNodeClick(currentNode)
                    })
                }


                // if(dataList && dataList.length > 0 && dataList.filter(s => s.Type == 2).length > 0) {
                //     //递归树结构，找到第一个子节点（人员）
                //     let findNode = (tree, func) => {
                //         for (const node of tree) {
                //             if (func(node)) return node
                //             if (node.children) {
                //                 const res = findNode(node.children, func)
                //                 if (res) return res
                //             }
                //         }
                //         return null
                //     }

                //     const fisrtChildNode = findNode(treeDatas, (node) => {
                //         return node.Type === 2
                //     })

                //     if(fisrtChildNode) {
                //         this.currentId = this.currentId || fisrtChildNode.Id
                //         this.$nextTick(() => {
                //             this.setCheckedKeys(this.currentId)
                //             let refTree = this.$refs.treeRef
                //             //高亮
                //             refTree.setCurrentKey(this.currentId)
                //             this.checkedNode = refTree.getCurrentNode()
                //             this.getEmployeeRecordDetails()
                //         })
                //     }

                // }

            }).catch(err => {
                this.loading = false
            });


        },
         
        filterNode(value, data) {
            if (!value) return true;
            return data.EmployeeName.indexOf(value) !== -1;
        },

        handleNodeClick(data) {
            this.currentReviewType = 1
            if(data && data.Type == 2) {
                this.type =  "3"
                this.currentId = data.Id
                this.checkedNode = data
                this.setCheckedKeys(this.currentId)
                this.getEmployeeRecordDetails(true)
            }else if(data && data.Type == 1) {
                //还原高亮（用户点击了不可点击的节点，那么高亮还是原来的节点）
                this.$refs.treeRef.setCurrentKey(this.currentId)
            }
        },

        setCheckedKeys(id) {
            let refTree = this.$refs.treeRef
            if(refTree) {
                refTree.setCheckedKeys([id])
                if(this.type == '1') {
                    this.getEmployeeRecordDetails() //员工档案——获取成员档案信息
                }else if(this.type == '2') {
                    this.getSummary()//考情数据——总览信息
                    if(this.listQuery.Month != 0){
                        this.getPersonalTimecard()//考情数据——考情月历
                    }
                    this.getProcessList()//考情数据——流程记录
                }else if(this.type == '3') {
                    this.getTask()//工作数据——总览信息
                    if(this.listQuery.Month != 0){
                        this.getDailyDatas()//工作数据——任务详情
                    }
                }else if(this.type == '4') {
                    this.getSharedList()//读书分享——分享详情
                    this.getSharedPeopleStatistics()//读书分享——总览信息
                }else if(this.type == '5') {
                    this.getSelfRatingList()//绩效数据——绩效详情
                }else if(this.type == '6' && this.currentReviewType == 1) {
                    this.getCustomerDataStatistics()//销售数据——客户数据（统计）
                    this.getGetCustomerPortrait()//销售数据
                    this.getClueDataStatistics()//销售数据
                    this.getClueDataPortrait()//销售数据
                    this.getBusiness()//销售数据
                    this.getOrder()//销售数据
                    this.getvisitPlan()//销售数据
                    this.getSalesPoint()
                }else if(this.type == '6' && this.currentReviewType == 2) {
                    this.getDayCounter()
                }else if(this.type == '7') {
                    this.getProjectInfoStatistics()//研发数据
                    this.getQuestionStatistics()//研发数据
                }
            }
        },

        disabledFn(data, nodeType) {
            if(data.Type == 2) {
                return false
            }
            //禁选部门节点
            return true
        },
        getSalesPoint() {
            if(this.listQuery.Month == 0) {
                return false
            }

            let postDatas = {
                Year: this.listQuery.Year,
                Month: this.listQuery.Month,
                EmployeeId: this.currentId
            }
            this.setCommonLoading(true)
            this.ratesLoading = true
            employeePointsRule.getSalesPoint(postDatas).then(res => {
                this.setCommonLoading(false)
                this.ratesLoading = false
                this.rates = res.WeekData || []
                this.monthData = res.MonthData || {}

                // // 测试
                // .map((s, idx) => {
                //     //测试数据
                //     if(idx == 0) {
                //         s.Point = 25
                //     }else if(idx == 1) {
                //         s.Point = 60
                //     }else if(idx == 2) {
                //         s.Point = 80
                //     }else if(idx == 3) {
                //         s.Point = null
                //     }
                //     return s
                // })
            }).catch(err => {
                this.setCommonLoading(false)
                this.ratesLoading = false
            })
        },
        getDayCounter() {
            let postDatas = {
                EmployeeId: this.currentId
            }
            this.setCommonLoading(true)
            this.dayCounterInfoLoading = true
            this.dayCounterInfo = {}
            employeePointsRule.getDayCounter(postDatas).then(res => {
                this.setCommonLoading(false)
                this.dayCounterInfoLoading = false

                this.dayCounterInfo = res

                let targetList = []
                
                let enums = JSON.parse(JSON.stringify(commonVars.vars.rateEnum))
                
                //分组，相同 ParentId 对象放到一组，没有 ParentId，自己一组（ParentId 设置为自己）

                let list = JSON.parse(JSON.stringify(res.RuleList)) || []
                list.forEach(item => {
                    let obj = enums.find(s => s.RateId == item.RateId)
                    if(obj) {
                        let pId = obj.ParentId || item.RateId
                        let exists = targetList.find(s => s.RateId == pId)
                        if(!exists) {
                            targetList.push({
                                RateId: pId,
                                List: [item]
                            })
                        }else{
                            exists.List.push(item)
                        }
                    }
                })

                // 10000 其他情况特殊处理
                targetList.forEach(item => {
                    if(item.RateId == 10000 && item.List.length > 0) {
                        let tempList = JSON.parse(JSON.stringify(item.List))
                        let targetObj = JSON.parse(JSON.stringify(tempList[0]))
                        targetObj.DeductionValue = _.sum(tempList.map(s => s.DeductionValue));
                        targetObj.DeductionReasonList = tempList.map(s => s.DeductionReasonList).reduce((prev, curr) => {
                            return (prev || []).concat(curr || [])
                        }, [])

                        targetObj.DeductionReasonList.map(s => {
                            s.DeductionReason = s.DeductionReason ? `${s.DeductionReason}，扣${s.DeductionValue}分` : ''
                            return s
                        })
                        item.List = [targetObj]
                    }
                })

                res.RuleList = targetList

            }).catch(err => {
                this.setCommonLoading(false)
                this.dayCounterInfoLoading = false
            })
        },
        //扣分项
        deductPointDialogOpen(weekIdx) {
            this.weekIdx = weekIdx
            this.deductPointDialogVisible = true
        },
        deductPointDialogClose() {
            this.deductPointDialogVisible = false;
        },
        deductPointDialogSaveSuccess(model, _formData) {
            this.deductPointDialogClose();
        },
        //周 评分、修改
        gradeDialogOpen(weekIdx, optType = 'detail') {
            this.dialogStatus = optType
            this.weekIdx = weekIdx
            this.gradeDialogVisible = true
        },
        gradeDialogClose() {
            this.gradeDialogVisible = false;
        },
        gradeDialogSaveSuccess(model, _formData) {
            this.getSalesPoint()
            this.gradeDialogClose();
        },
        //月 扣分项
        monthDeductPointDialogOpen() {
            this.monthDeductPointDialogVisible = true
        },
        monthDeductPointDialogClose() {
            this.monthDeductPointDialogVisible = false;
        },
        monthDeductPointDialogSaveSuccess(model, _formData) {
            this.monthDeductPointDialogClose();
        },
        
        //月 评分、修改
        monthGradeDialogOpen(optType = 'detail') {
            this.monthGradeDialogStatus = optType
            this.monthGradeDialogVisible = true
        },
        monthGradeDialogClose() {
            this.monthGradeDialogVisible = false;
        },
        monthGradeDialogSaveSuccess(model, _formData) {
            this.getSalesPoint()
            this.monthGradeDialogClose();
        },
        openRateDetailDialog() {
            this.rateDetailDialogVisible = true
        },
        setCommonLoading(val) {
            this.commonLoading = val
        },
    },  
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;
        // border-right: 1px solid #dcdfe6;
    /deep/.el-checkbox{
        display: none;
    }
    /deep/.el-tree-node{
        .is-leaf + el-checkbox .el-checkbox__inner{
            display: inline-block;
        }
    }
    /deep/.el-card{
        border-radius: 0px;
    }
    
    .dialogWarp_card{
        width: 100%;
        height: 100%;
    }

    .el-checkbox .el-checkbox__inner{
        display: none;
    }
 
    &_card{
        display: flex;
        flex-direction: column;
        .tagsList{
            flex: 1;
            // height: 446px;
            padding: 10px;
            overflow: hidden;
            overflow-y: auto;
            .noRefund {
                height: 30px;
                .avatar-wrapper{
                    display: flex;
                    align-items: center;
                    width: 18;
                    height: 18;
                    /deep/img{
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;

        .content {
            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

.node-title{
    display: flex;
    align-items: center;
}

 .tagsList{
            flex: 1;
            padding: 10px;
            overflow: hidden;
            overflow-y: auto;
        }

 .title-bg{
//    width:95%; 
   flex: 1;
   padding-top:3px;  
   padding-left:20px; 
   font-size:16px; 
   font-weight:bold; 
   background: rgba(61,115,221,0.32);
 }

 .title-left{
   margin-left:10px;
   width: 5px;
   height: 24px;
   background: #3D73DD;
 }


 .tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 10px 8px;
    display: flex;
    .year-wrapper{
        >div:first-child{
            width: 100px!important;
        }
    }
    .btn-list{
        flex: 1;
        margin-left: 20px;
        display: flex;
        align-items: center;
    }
}

.item-title{
    font-weight: bold;
    margin-left: 70px;
}
.item-content{
    margin-left: 10px;
}

.card-info{
   margin-left:30px;
   width: 146px;
   height: 72px;
}

.card-title{
    margin-top: 10px;
    margin-left: 5px;
}

.card-value{
    margin-top: 6px;
    font-weight: bold;
    font-size: 20px;
    // margin-left: 45px;
    display: inline-block;
    text-align: center;
}

.card-content{
   font-weight: bold;
   font-size: 24px;
   margin-left: 30px;
}

/deep/.el-card__body{
    padding: 0px;
    height: calc(100% - 35px);
    display: flex;
    flex-direction: column;
}

// /deep/.el-calendar__body{
//     padding: 12px 20px 15px;
// }

// .calendar-wrapper >>> .el-calendar__header{
//   display: none!important;
// }

.calendar-wrapper >>> .el-calendar__body{
  padding: 10px!important;
}
.calendar-wrapper >>> .el-calendar-table{
  height: 700px;
}
.calendar-wrapper >>> .el-calendar-table:not(.is-range) td.next{
    pointer-events: none;
}
.calendar-wrapper >>> .el-calendar-table:not(.is-range) td.prev{
    pointer-events: none;
}

.calendar-wrapper >>> .el-calendar-day{
  padding: 4px;
  overflow: auto!important;
  height: 100%!important;
}

.calendar-wrapper{
    margin-left: 10px;
    /deep/.el-calendar-table .el-calendar-day {
        box-sizing: border-box;
        padding: 8px;
        height: 125px;
        overflow-y: auto;
    }

    /deep/.el-calendar__header{
        display: none;
    }
}

.page-content{
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    padding-right: 50px;
}

// .elCalendar >>> .el-calendar-table:not(.is-range) td.next{
//     pointer-events: none;
// }
// .elCalendar >>> .el-calendar-table:not(.is-range) td.prev{
//     pointer-events: none;
// }
// .elCalendar >>> .el-calendar-table{
//   height: 700px;
// }
// .elCalendar >>> .el-calendar-day{
//   padding: 4px;
//   overflow: auto;
//   height: 100%;
// }

.app-calendar{
    padding: 10px;
    // height: 1000px;
    margin-left: 15px;

    /deep/.fc-popover-body{
        max-height: 220px;
        overflow-y: auto;
    }

    /deep/.fc-view-harness.fc-view-harness-active{
       height: 1000px;
    }
    
}

.share-content{
    height: 100%;

    .mid{
        flex: 1;
        display: flex;
        overflow-y: hidden;
        .lft{
            width: 145px;
            border-right: 1px solid #EBEEF5;
        }
        .rht{
            flex: 1;
        }
        .lft, .rht{
            height: 600px;
            display: flex;
            flex-direction: column;
            .inner-mid{
                padding: 0 10px;
                height: 0;
                flex: 1;
                overflow-y: auto;
            }
            .top{
                font-weight: bold;
                padding: 5px 10px;
                padding-bottom: 10px;
            }
        }
        .lft{
            .inner-mid{
                padding: 0 10px;
                text-align: center;
                >div:not(:last-child) {
                    margin-bottom: 5px;
                }
                >div{
                    button{
                        width: 100%;
                    }
                }
            }
            .btm{
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /deep/.el-button--mini{
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;  
        }
    }
}

.persion-wrapper{
    display: flex;
    margin-left: 45px;
    margin-top: 20px;
    border-bottom: 1px solid #DCDFE6;

    .info-wrapper{
        .info{
            margin-left: 10px;

            .name{
                margin-top: 5px;
                font-size: 16px;
                font-weight: bold;
            }

            .title{
                 margin-top: 5px;
            }
        
        }
        }
}

.basicsLabel {
    color: #606266;
    font-weight: 500 !important;
}

.form{
    /deep/.el-form-item__label{
        color: black;
    }
}

.info-left{
  border-right: 1px solid #EBEEF5;
  display: flex;
  flex-direction: column;
  margin-top: 10px;
  height: 100%;
}

.item-top{
    display: flex;
    margin-top:20px; 
    margin-bottom:10px;
}

.bt{
    border-bottom: 1px solid #dcdfe6;
    margin-bottom:16px;
}

.grade-wrapper{
    height: 123px;
    padding: 20px 40px 0;
    // padding-right: 80px;
    // padding-bottom: 0px;
    display: flex;
    align-items: center;
    .item-wrapper:not(:last-child) {
        margin-right: 40px;
    }
    .item-wrapper{
        >div{
            text-align: center;
        }
        .title{
        }
        .num{
            margin: 10px 0;
            font-weight: bold;
            font-size: 40px;
        }
        .opt{
            /deep/.el-button{
                padding: 3px 5px;
            }
            .tip{
                font-size: 12px;
                color: #C0C4CC;
            }
        }
    }
}


.content{
    /deep/.el-tabs__nav-wrap::after{
        width: 1px!important;
    }
}

.l1{
    color: #D9001B;
}
.l2{
    color: #F59A23;
}
.l3{
    color: #00CC00;
}
</style>
