<template>
    <!-- 问题改进管理 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="true"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="pageType==1?150:110"
            :loading="listLoading"
            :tab-columns="pageType==1?tabColumns:(tabColumns.filter(s=>s.attr.prop != 'SolutionCount'))"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :startOfTable="startOfTable"
            @sortChagned="handleSortChange">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'120px'" :items="pageType==1?tableSearchItems:(tableSearchItems.filter(s=>s.prop == 'Keywords'))"
                        @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索问题标题" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>
                        <!-- 状态 -->
                        <template slot="ProductQuestionStatus">
                            <el-select v-model="listQuery.ProductQuestionStatus" placeholder="请选择">
                                <el-option v-for="item in ProductQuestionStatusEnum.filter(s=>s.value!=0)" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <!-- 创建时间 -->
                        <template slot="CreateTime">
                            <el-date-picker style="width: 180px;" v-model="listQuery.CreateTime" type="date" align="right" format="yyyy-MM-dd" placeholder="请选择"
                            value-format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                        </template>
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                        </template>
                    </app-table-form>
                </template>
                <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD') }}</template>
                <template slot="ProductSpecificationImprovementName" slot-scope="scope">{{ scope.row.ProductSpecificationImprovementName || '无' }}</template>

                <template slot="ProductQuestionStatus" slot-scope="scope">
                    <span class="item-status" v-if="scope.row.ProductQuestionStatus"
                        :style="{backgroundColor: getProductQuestionStatusObj(scope.row.ProductQuestionStatus).bgColor,color: getProductQuestionStatusObj(scope.row.ProductQuestionStatus).color}"
                    >{{ getProductQuestionStatusObj(scope.row.ProductQuestionStatus).label }}</span>
                    <template v-else>无</template>
                </template>
<!-- 
                <template slot="ProductName" slot-scope="scope">
                    <span class="item-status" v-if="scope.row.ProductName"
                        :style="{backgroundColor: getProductNameObj(scope.row.ProductName).bgColor,color: getProductNameObj(scope.row.ProductName).color}"
                    >{{ getProductNameObj(scope.row.ProductName).label }}</span>
                    <template v-else>无</template>
                </template>
                
                <template slot="SurveyType" slot-scope="scope">{{ scope.row.SurveyType | SurveyTypeFilter}}</template> -->
                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <template v-if="pageType===1">
                        <!-- 详情 -->
                        <app-table-row-button @click="handleFollow(scope.row, 'detail')" text="详情" :type="2"></app-table-row-button>
                        <!-- 跟进 -->
                        <app-table-row-button v-if="rowBtnIsExists('btnFollow')" @click="handleFollow(scope.row, 'follow')" text="跟进" :type="2"></app-table-row-button>
                        <!-- 编辑 -->
                        <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row,'update')" text="编辑" :type="2"></app-table-row-button>
                        <!-- 删除 -->
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                    </template>
                    <template v-if="pageType===2">
                        <!-- 详情 -->
                        <app-table-row-button @click="handleFollow(scope.row, 'detail')" text="详情" :type="2"></app-table-row-button>
                        <!-- 填写问卷 -->
                        <app-table-row-button v-if="rowBtnIsExists('btnSubmitProgramme')&&scope.row.ProductQuestionStatus==1" @click="handleSubmitProgramme(scope.row, 'create')" text="提交方案" :type="2"></app-table-row-button>
                    </template>
                </template>
            </app-table>
        </div>
        <!----------------------------------------- 分页 ------------------------------------------->
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>

    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogFormVisible" :id="selectId" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogFormVisible"
    @closeDialog="createDialogFormVisible=false" @saveSuccess="createSaveSuccess"></create-page>

    
    <!-- 选择产品 -->
    <product-model 
        v-if="dialogProductModelVisible" @reload="getList"
        @closeDialog="dialogProductModelVisible=false" dialogStatus="create"
        :dialogFormVisible="dialogProductModelVisible" />

    <!-- 查看详情/ 跟进  -->
    <follow-page v-if="createFollowVisible" :id="selectId" :dialogStatus="createFollowStatus"
    :dialogFormVisible="createFollowVisible" @reload="getList"
    @closeDialog="createFollowVisible=false" @saveSuccess="createFollowSuccess"></follow-page>


    

    <!-- 创建/修改/详情 解决方案 -->
    <programme-create v-if="submitProgrammeVisible" :problemId="selectId" :dialogStatus="submitProgrammeStatus" :dialogFormVisible="submitProgrammeVisible"
    @closeDialog="submitProgrammeVisible=false" @saveSuccess="createSubmitProgrammeSuccess" />
</div>
</template>
<script>
import { ProductQuestionStatusEnum } from "./enum.js";
import * as productQuestionImprovementApi from '@/api/knowledge/productQuestionImprovement.js'

import indexPageMixin from "@/mixins/indexPage";
import productModel from "@/views/knowledge/problemImprove/productModel.vue";
import createPage from "./create";
import followPage from "./follow";
import programmeCreate from "./programmeCreate";



export default {
    name: 'problem-improve-index',
    mixins: [indexPageMixin],
    components: {
        createPage,
        productModel,
        followPage,
        programmeCreate,
    },
    props: {
        pageType: { // 1 人事行政管理下面的  问题改进管理   2  公司门户 下面的 问题改进
            type: Number,
            default: 1,
        },
    },
    filters: {
        // SurveyTypeFilter(val) {
        //     let obj = SurveyTypeEnum.find(
        //         s => s.value == val
        //     );
        //     if (obj) {
        //         return obj.label;
        //     }
        //     return "无";
        // }
    },
    created() {
        this.getList();
    },
    data() {
        return {
            ProductQuestionStatusEnum,


            /** 创建问题改进 */
            selectId: '',
            selectRow: {},
            createDialogStatus: 'create',
            createDialogFormVisible: false,

            /** 跟进弹窗 */
            createFollowStatus: 'detail',
            createFollowVisible: false,

            /** 选择产品弹窗 */
            dialogProductModelVisible: false,

            /** 提交解决方案 */
            submitProgrammeStatus: 'detail',
            submitProgrammeVisible: false,

            total: 0,
            listQuery: {},
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "CreateTime", label: "创建时间" },
                { prop: "ProductQuestionStatus", label: "状态" },
            ],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "Title", label: "问题标题", showOverflowTooltip: true}},
                { attr: { prop: "ProductSpecificationImprovementName", label: "相关产品", showOverflowTooltip: true}, slot: true },
                { attr: { prop: "ProductQuestionStatus", label: "状态"}, slot: true },
                { attr: { prop: "SponsorEmployeesName", label: "发起人", showOverflowTooltip: true} },
                { attr: { prop: "CreateTime", label: "创建时间", sortable: 'custom'}, slot: true },
                { attr: { prop: "SolutionCount", label: "解决方案"}},
            ],
        }
    },
    methods: {
        // 提交方案弹窗  确定返回
        createSubmitProgrammeSuccess(){
            this.submitProgrammeVisible = false;
            this.$emit('reload')
            this.getList()
        },
        // 提交方案弹窗
        handleSubmitProgramme(row, optType = "detail"){
            this.selectId = row.Id;
            this.selectRow = row;
            this.submitProgrammeStatus = optType;
            this.submitProgrammeVisible = true;
        },
        // 获取列表
        getList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            if (this.pageType === 2) {
                postDatas.PageType = "onLineTraining";
            }
            postDatas = self.assignSortObj(postDatas);
            self.listLoading = true;
            productQuestionImprovementApi.getList(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        // 列表排序
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        // 重置搜索
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        // 表格文本框果过滤
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 表格顶部按钮点击事件
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleAdd()
                    break;
                case "btnModel":
                    this.dialogProductModelVisible = true;
                    break;
                default:
                    break;
            }
        },
        // 显示跟进弹窗
        handleFollow(row, optType = "detail"){
            this.selectId = row.Id;
            this.selectRow = row;
            this.createFollowStatus = optType
            this.createFollowVisible = true
        },
        // 跟进弹窗 确定
        createFollowSuccess(val){
            this.createFollowVisible = false
            this.getList()
        },
        /** 弹出新增编辑框 */
        handleAdd() {
            this.selectId = '';
            this.selectRow = {};
            this.createDialogStatus = 'create'
            this.createDialogFormVisible = true
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.selectId = optType === 'create' ? '' : row.Id;
            this.selectRow = optType === 'create' ? {} : row;;
            this.createDialogStatus = optType
            this.createDialogFormVisible = true
        },
        /** 编辑框 点确定后 关闭 并刷新列表 */
        createSaveSuccess(d) {
            if (!d) {
                this.createDialogFormVisible = false
            }
            this.listQuery.PageIndex = 1;
            this.getList()
        },
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                productQuestionImprovementApi.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        // 获取问题状态 对象
        getProductQuestionStatusObj(val){
            return this.ProductQuestionStatusEnum.find(s => s.value == val) || {};
        }
    }
}
</script>
<style lang="scss" scoped>
.app-container{
    width: 100%;
    height: 100%;
    margin: 0;
    left: 0;
    top: 0;
}
</style>