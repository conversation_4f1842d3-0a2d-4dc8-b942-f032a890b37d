import Cookies from 'js-cookie'
import * as approval from "@/api/projectDev/projectMgmt/approvalSetting";
import * as systemMessage from "@/api/systemMessage/systemMessage";
import Vue from 'vue';

const app = {
  state: {
    sidebar: {
      opened: !+Cookies.get('sidebarStatus'),
      withoutAnimation: false
    },
    device: 'desktop',
    isNoticedExpire: false,
    currentMenuGroupName: '', //当前一级菜单名称
    currentMenuPageId: '', //当前菜单中文名称
    //我的审批页面自动打开弹框
    
    autoOpenDialogList: {
      // /xxxxx/xxxx/: [ //消息推送时，审批页面自动打开弹框数组
      //   // {
      //   //   id: '', //审批id
      //   //   approvalId: '', //主键id
      //   //   businessType: 1, //弹框业务类型
      //   //   autoOpen: true, //自动打开弹框
      //   //   ishandle: false, //当前弹框是否已经处理（点击查看）——用于防止关闭后，再次点击查看，打开相同弹框
      //   // }
      // ]
    },
    hypertrunkList: [],
    waitApprovalNum: 0,
    unreadNum: 0,

    checkHasNewMessageTaskId: '',//超级干线模块，任务跟进动态查看，设置已读后，其他模块需要跟进检查
  },
  mutations: {
    TOGGLE_SIDEBAR: state => {
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 1)
      } else {
        Cookies.set('sidebarStatus', 0)
      }
      state.sidebar.opened = !state.sidebar.opened
      state.sidebar.withoutAnimation = false
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus', 1)
      state.sidebar.opened = false
      state.sidebar.withoutAnimation = withoutAnimation
    },
    TOGGLE_DEVICE: (state, device) => {
      state.device = device
    },
    SET_NOTICED_PRIRE: (state, isExpire) => {
      state.isNoticedExpire = isExpire
    },
    SET_CURRENT_MENU_GROUP_NAME: (state, title) => {
      state.currentMenuGroupName = title
    },
    SET_CURRENT_MENU_PAGE_ID: (state, title) => {
      state.currentMenuPageId = title
    },
    SET_autoOpenDialogList: (state, {navUrl, list}) => {
      if (!state.autoOpenDialogList[navUrl]){
        Vue.set(state.autoOpenDialogList, navUrl, []);
      }
      let exList = state.autoOpenDialogList[navUrl]

      if(exList && exList.length > 0) {
        exList.splice(exList.length)
      }
      state.autoOpenDialogList[navUrl] = list
    },
    SET_waitApprovalNum: (state, num) => {
      state.waitApprovalNum = num
    },
    SET_unreadNum: (state, num) => {
      state.unreadNum = num
    },
    SET_checkHasNewMessageTaskId: (state, taskId) => {
      state.checkHasNewMessageTaskId = taskId
    },
    
  },
  actions: {
    ToggleSideBar: ({ commit }) => {
      commit('TOGGLE_SIDEBAR')
    },
    CloseSideBar({ commit }, { withoutAnimation }) {
      commit('CLOSE_SIDEBAR', withoutAnimation)
    },
    ToggleDevice({ commit }, device) {
      commit('TOGGLE_DEVICE', device)
    },
    RefreshUnreadNum({ commit }) {
      systemMessage.getSubordinateModuleList().then((res) => {
        let obj = res.find((s) => s.value == 0);
        if (obj) {
            commit('SET_unreadNum', obj.unreadNum)
        }
      });
    },
    RefreshWaitApprovalNum({ commit }) {
      approval.getStatistics({}).then((res) => {
        let obj = res.find((s) => s.Type == 1);
        if (obj) {
            commit('SET_waitApprovalNum', obj.Total)
        }
      });
    }
  }
}

export default app
