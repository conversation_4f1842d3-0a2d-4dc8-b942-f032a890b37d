<template>
  <div>
    <app-dialog :title="title" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
      <template slot="body">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          label-width="100px"
        >
          <div class="wrapper" v-loading='loading'>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="产品名称" prop="Name">
                    <el-input maxlength="30" :disabled="!editable" v-model="formData.Name"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
          </div>
        </el-form>
      </template>

      <template slot="footer">
        <!-- <el-button @click="handleClose">取消</el-button>
        <el-button @click="createData" type="primary" v-show="editable">确认</el-button>-->
        <span class="fl m-r-50" v-if="dialogStatus == 'create'">
          <el-checkbox v-model="isContinue">继续添加</el-checkbox>
        </span>
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as classify from "@/api/classify";
import { vars } from '../common/vars';
import busMixins from './mixins'
export default {
  name: "",
  directives: {},
  components: {
  },
  mixins: [busMixins],
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    title() {
      if (this.dialogStatus == "create") {
        return "创建产品";
      } else if (this.dialogStatus == "update") {
        return "编辑产品";
      } else if (this.dialogStatus == "detail") {
        return "产品详情";
      }
      return "";
    }
  },
  props: {
    //编辑还是新增(create: 新增; update: 编辑; detail：详情)
    dialogStatus: {
      required: true,
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
        handler(val) {
            if (val) {
                this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        },
        immediate: true
    }
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      productTypes: vars.productTypes,
      isClear: false,
      disabledBtn: false,
      loading: false,
      rules: {
        Name: {
          fieldName: "产品名称",
          rules: [{ required: true, max: 100 }]
        },
      },
      formData: {
        Id: "",
        Name: "", //产品名称
        
      },
      isContinue: false,

    };
  },
  methods: {
    resetFormData() {
      this.formData = {
        Id: "",
        Name: "", //产品名称
      };
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));
          postData.BusinessType = this.businessType
          this.disabledBtn = true;
          let result = null
          if (this.dialogStatus == "create") {
            delete postData.Id;
            result = classify.add(postData);
          } else if (this.dialogStatus == "update") {
            result = classify.edit(postData);
          }

          result
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              if (this.isContinue) {
                this.resetFormData();
              }
              this.$refs.appDialogRef.createData(this.isContinue);
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        }
      });
    },
    getDetail() {
      this.loading = true
      classify.detail({ id: this.id }).then(res => {
        this.loading = false
        this.formData.Id = res.Id
        this.formData.Name = res.Name
      }).catch(err => {
        this.loading = false
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang='scss' scoped>
.wrapper{
    min-height: 100px;
}
</style>