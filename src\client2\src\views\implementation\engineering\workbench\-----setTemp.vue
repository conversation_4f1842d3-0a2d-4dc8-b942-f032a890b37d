<template>
    <div>
        <app-dialog
        title="导入模板"
        ref="appDialogRef"
        v-bind="$attrs"
        v-on="$listeners"
        :maxHeight="700"
        :width='600'
        >
            <template slot="body">
                <div class="wrapper" v-loading='loading'>
                    <div class="tip">
                        选中导入地区（{{ selectedCount }}）
                    </div>
                    <div class="item-wrapper" v-for="(item, idx) in templates" :key="idx">
                        <el-radio v-model="radio" :label="item.value">{{ item.label }}</el-radio>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import mixins from '../../common/mixins'
export default {
    name: "set-owner",
    components: {
    },
    mixins: [mixins],
    props: {
        dialogStatus: {
            type: String
        },
        selectedCount: {
            type: Number,
            default: 0
        }
    },
    created() {
        this.getTemplates({ IsUsed: true })
    },
    data() {
        return {
            radio: '',
            loading: false,
            disabledBtn: false,
            templates: []
        };
    },
    methods: {
        // getList() {
        //     this.templates = [
        //         {label: '111111', value: 'a5b60381-0e20-4c04-b8b3-d697e2d21111'},
        //         {label: '222222222', value: 'a5b60381-0e20-4c04-b8b3-d697e2d22222'},
        //         {label: '33333', value: 'a5b60381-0e20-4c04-b8b3-d697e2d33333'},
        //     ]
        //     if(this.templates.length > 0) {
        //         this.radio = this.templates[0].value
        //     }
        // },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        createData() {
            this.$refs.appDialogRef.createData(this.radio);
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapper{
    .tip{
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 6px;
        margin-bottom: 6px;
    }
    .item-wrapper{
        margin-bottom: 4px;
    }
}
</style>
