<template>
    <el-dialog class="dialog-mini" width="1000px" title="查看详情" top="3vh" destroy-on-close :visible.sync="isVisible"
        :before-close="cancel" :close-on-click-modal="false" :append-to-body="true">
        <!-- AI报警 -->
        <div v-if="MessageTypeId==3" style="max-height: 600px; overflow-y: auto;">
            <el-form :rules="rules" ref="warningForm" :model="MessageDetail" label-position="right" label-width="120px"
                style="max-height: 600px; overflow-y: auto;">
                <!-- 第一行 -->
                <el-row>
                    <!-- 第一列 -->
                    <el-col :span="12">
                        <el-row>
                            <el-form-item label="报警编号">{{MessageDetail.MessageCode}}</el-form-item>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="设备名称">{{MessageDetail.FurnaceName}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="设备型号">{{MessageDetail.FurnaceModel}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="额定热负荷(KW)">{{MessageDetail.FurnaceRatedPower}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="设计热效率(%)">{{MessageDetail.DesignThermalEfficiency}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="设计压力(Mpa)">{{MessageDetail.DesignPressure}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="设计温度(℃)">{{MessageDetail.DesignTemperature}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="报警类型">AI报警</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="报警时间">{{ formatterDate(MessageDetail.CreateDateTime)}}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="报警标题">{{MessageDetail.MessageTitle}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="状态">{{taskState[MessageDetail.MessageStatus].label}}</el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>

                    <!-- 第二列 -->
                    <el-col :span="12">
                        <!-- 动态分类列 -->
                        <el-row v-for="item in MessageDetail.DeviceTypeDatas" v-bind:key="item.DeviceTypeName">
                            <el-col :span="24">
                                <el-form-item :label="item.DeviceTypeName">{{item.DeviceTypeValue}}</el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                <!-- 第二行 -->
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="报警内容">{{MessageDetail.MessageContent}}</el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">{{MessageDetail.Remark}}</el-form-item>
                    </el-col>
                </el-row>
                <!-------------------------- 分割线 ------------------------->
                <hr />
                <div class="card-container">
                    <app-tabs :styleThemes="'default'" key="flowTabs" :items="flowTabs" :active.sync="flowTabIdx">
                    </app-tabs>

                    <!-- 实时数据列表 -->
                    <div v-if="flowTabIdx == 1">
                        <el-table :data="PlcFuntionData" v-loading="PlcFuntionDataLoadings"
                            style="width: 100%;height:334px;">
                            <el-table-column prop="PointKey" label="基础参数Key"></el-table-column>

                            <el-table-column prop="PointName" label="基础参数名称"></el-table-column>

                            <el-table-column prop="PointValue" label="基础参数值"></el-table-column>
                        </el-table>
                        <el-pagination class="paging" background :total="plcFuntionDataTotal"
                            :current-page="plcFuntionDataPage" :page-size="plcFuntionDataLimit"
                            @current-change="plcFuntionDataHandleCurrentChange" />
                    </div>
                    <!-- 结构部件列表 -->
                    <div v-if="flowTabIdx == 0">
                        <el-table :data="structuralComponentSolidInfoData"
                            v-loading="StructuralComponentSolidInfosLoadings" style="width: 100%;height:334px;">
                            <el-table-column prop="SpecificationModel" label="规格型号"></el-table-column>

                            <el-table-column prop="StructuralComponentName" label="结构部件名称"></el-table-column>

                            <el-table-column prop="IsKeyDevice" label="是否属于关键部件"></el-table-column>

                            <el-table-column prop="ComponentBelong" label="部件属于"></el-table-column>

                            <el-table-column prop="StructuralComponentMadeDate" label="生产日期"></el-table-column>
                            <el-table-column prop="DeviceSuppliersName" label="供应商" width="200"></el-table-column>
                        </el-table>
                        <el-pagination class="paging" background :total="structuralComponentSolidInfoDataTotal"
                            :current-page="structuralComponentSolidInfoDataPage"
                            :page-size="structuralComponentSolidInfoDataLimit"
                            @current-change="structuralComponentSolidInfoDataHandleCurrentChange" />
                    </div>
                </div>
                <!-------------------------- 分割线 ------------------------->
                <hr />

                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'售后人员'" prop="ServiceEmployees">
                            <emp-selector :showType="2" :readonly="editable" :multiple="false"
                                :list="MessageDetail.ServiceEmployees" @change="handleChangeServiceEmployee">
                            </emp-selector>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="关联任务编号" v-show="MessageDetail.TaskSn">
                            <!-- <app-table-row-button @click="handleAddRow" text='新增故障现象' v-show="editable" :type='2'></app-table-row-button> -->
                            <el-button v-if="isViewTaskDetail" type="text" @click="showTaskDetail">
                                {{MessageDetail.TaskSn}}</el-button>
                            <span v-else>{{MessageDetail.TaskSn}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'任务起止时间'" prop="Range">
                            <el-date-picker v-model="MessageDetail.Range" type="datetimerange" align="right"
                                unlink-panels range-separator="-" start-placeholder end-placeholder
                                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%;"
                                :disabled="editable">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注" prop="Description">
                            <el-input maxlength="500" type="textarea" :rows="2" v-model="MessageDetail.Description"
                                :disabled="editable"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <!-- 客户档案 -->
        <div v-else-if="MessageTypeId==4">
            <el-form ref="dataForm" :model="customerArchiveDetail" label-position="right" label-width="120px"
                style="max-height: 600px; overflow-y: auto;">
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'客户名称'" prop="CustomerName">
                            <el-input maxlength="100" v-model="customerArchiveDetail.CustomerName" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'部门名称'" prop="DepartmentName">
                            <el-input maxlength="100" v-model="customerArchiveDetail.DepartmentName" disabled>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'单位名称'" prop="OrganizationName">
                            <el-input maxlength="100" v-model="customerArchiveDetail.OrganizationName" disabled>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'级别'" prop="CustomerLevelTypeId">
                            <el-select v-model="customerArchiveDetail.CustomerLevelTypeId" disabled placeholder="请选择级别"
                                style="width:100%;">
                                <el-option key="16" :value="16" label="正处级">正处级</el-option>
                                <el-option key="32" :value="32" label="副处级">副处级</el-option>
                                <el-option key="48" :value="48" label="正科级">正科级</el-option>
                                <el-option key="64" :value="64" label="副科级">副科级</el-option>
                                <el-option key="80" :value="80" label="主任">主任</el-option>
                                <el-option key="96" :value="96" label="科员">科员</el-option>
                                <el-option key="112" :value="112" label="办事员">办事员</el-option>
                                <el-option key="128" :value="128" label="其他">其他</el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'性别'" prop="GenderTypeId">
                            <el-select v-model="customerArchiveDetail.GenderTypeId" disabled placeholder="请选择性别"
                                style="width:100%;">
                                <el-option key="1" :value="1" label="男">男</el-option>
                                <el-option key="2" :value="2" label="女">女</el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'重要性'" prop="ImportanceTypeId">
                            <el-select v-model="customerArchiveDetail.ImportanceTypeId" disabled placeholder="请选择重要性"
                                style="width:100%;">
                                <el-option key="1" :value="1" label="高">高</el-option>
                                <el-option key="2" :value="2" label="中">中</el-option>
                                <el-option key="3" :value="3" label="低">低</el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'生日'" prop="Birthday">
                            <el-date-picker class="dat-ipt" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                v-model="customerArchiveDetail.Birthday" type="date" placeholder disabled>
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'结婚纪念日'" prop="WeddingDay">
                            <el-date-picker class="dat-ipt" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                v-model="customerArchiveDetail.WeddingDay" type="date" placeholder disabled>
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'毕业学校'" prop="School">
                            <el-input maxlength="100" v-model="customerArchiveDetail.School" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'电话'" prop="Telephone">
                            <el-input maxlength="100" v-model="customerArchiveDetail.Telephone" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'主要人脉关系'" prop="MajorRelationship">
                            <el-input maxlength="500" type="textarea" :rows="2"
                                v-model="customerArchiveDetail.MajorRelationship" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'性格特点'" prop="CharacterTrait">
                            <el-input maxlength="500" type="textarea" :rows="2"
                                v-model="customerArchiveDetail.CharacterTrait" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'兴趣爱好'" prop="Hobby">
                            <el-input maxlength="500" type="textarea" :rows="2" v-model="customerArchiveDetail.Hobby"
                                disabled>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'最近常联系的人'" prop="FrequentContacts">
                            <el-input maxlength="500" type="textarea" :rows="2"
                                v-model="customerArchiveDetail.FrequentContacts" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'最近看什么书'" prop="RecentBook">
                            <el-input maxlength="500" type="textarea" :rows="2"
                                v-model="customerArchiveDetail.RecentBook" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'备注'" prop="Description">
                            <el-input maxlength="500" type="textarea" :rows="2"
                                v-model="customerArchiveDetail.Description" disabled>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item :label="'附件'" prop="EnclosureList">
                            <app-upload-big-file :readonly="true" accept="all" :max="1" :fileType="4"
                                :value="customerArchiveDetail.EnclosureList" :fileSize="1024 * 1024 * 500"
                                @change="handleUpChange">
                            </app-upload-big-file>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <div v-if="innerVisible">
            <edit-page :isViewMessageDetail='false' :isOnlyShowCancelButton="dialogStatus == 'detail'"
                :dialogStatus="dialogStatus" :dialogFormVisible="innerVisible" :id="taskId"
                :customData="{MessageId:MessageId,TaskType:6,ServiceEmployees:MessageDetail.ServiceEmployees,Range:MessageDetail.Range,MessageCode:MessageDetail.MessageCode,Description:MessageDetail.Description}"
                @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" @reloading="handleReloading"></edit-page>
        </div>

        <div slot="footer">
            <el-button @click="cancel">取消</el-button>
            <el-button size="mini" type="primary" v-show="!MessageDetail.TaskSn" @click="validateData">派发任务</el-button>
        </div>
    </el-dialog>

</template>
<script>
    import elDragDialog from "@/directive/el-dragDialog";
    import * as messages from "@/api/messages";
    import * as customerArchive from "@/api/customerArchive";
    import EmpSelector from "../common/empSelector";

    export default {
        name: "message-detail",
        directives: {
            elDragDialog
        },
        components: {
            EditPage: () => import('../task/edit'),
            EmpSelector
        },
        props: {
            //新增(编辑)弹框是否显示
            dialogFormVisible: {
                required: true,
                type: Boolean,
                default: false
            },
            MessageId: {
                //消息id
                required: true,
                default: ""
            },
            MessageTypeId: {
                //消息id
                required: true,
                type: Number,
                default: 3
            },
            //是否可以查看任务详情（因为任务详情也关联报警详情，会无限循环嵌套）
            isViewTaskDetail: {
                type: Boolean,
                default: true
            }
        },
        data() {
            return {
                isVisible: false,
                taskState: [
                    { label: '待处理', value: 1 },
                    { label: '处理中', value: 2 },
                    { label: '已处理', value: 3 },
                ],
                //tab页
                flowTabs: ["结构部件", "实时数据"],
                flowTabIdx: 0,

                //详情页面
                editable: false,
                innerVisible: false,
                dialogStatus: "create",
                taskId: "",
                MessageDetail: {
                    MessageTitle: "", //消息标题
                    MessageContent: "", //消息内容
                    CreateDateTime: "", //发生时间
                    FurnaceName: "", //加热炉名称
                    FurnaceModel: "", //设备型号
                    DepartmentName: "", //部门名称
                    FurnaceRatedPower: "", //额定热负荷
                    DesignThermalEfficiency: "", //设计热效率
                    DesignTemperature: "", //设计温度
                    DesignPressure: "", //设计压力
                    Remark: "", //备注
                    PointDatas: [], //实时点位数据集合
                    DeviceTypeDatas: [], //分类数据集合
                    StructuralComponentSolidInfoDatas: [], //结构部件数据集合
                    Range: [], //起始时间
                    MessageCode: "", //报警CODE
                    MessageStatus: 0, //状态
                    ServiceEmployees: [], //售后人员
                    TaskDetailId: "", //关联任务主键
                    TaskSn: "", //关联任务编号
                    StartTime: "", //开始时间
                    EndTime: "", //结束时间
                    Description: "" //备注
                },

                //自定义 结构部件列表
                StructuralComponentSolidInfosLoadings: false,
                structuralComponentSolidInfoData: [],
                structuralComponentSolidInfoDataTotal: 0,
                structuralComponentSolidInfoDataPage: 1,
                structuralComponentSolidInfoDataLimit: 10,

                //自定义 实时点位列表
                PlcFuntionDataLoadings: false,
                PlcFuntionData: [],
                plcFuntionDataTotal: 0,
                plcFuntionDataPage: 1,
                plcFuntionDataLimit: 10,

                //主消息列表
                tabDatas: [],
                total: 0,
                listLoading: false,
                multipleSelection: [],
                tableSearchItems: [{ prop: "MessageContent", label: "消息关键字" }],
                tabColumns: [
                    {
                        attr: {
                            prop: "MessageTypeName",
                            label: "消息分组",
                            width: 200
                        }
                    },
                    {
                        attr: {
                            prop: "MessageTitle",
                            label: "消息标题",
                            showOverflowTooltip: true
                        }
                    },
                    {
                        attr: {
                            prop: "MessageContent",
                            label: "消息内容",
                            showOverflowTooltip: true
                        }
                    },
                    {
                        attr: {
                            prop: "SenderEmployeeName",
                            label: "消息发布人",
                            width: 100
                        }
                    },
                    {
                        attr: {
                            prop: "CreateDatetime",
                            label: "发布时间",
                            width: 200
                        }
                    }
                ],
                rules: {
                    Range: [{ required: true, message: "起止时间不能为空" }],
                    ServiceEmployees: [{ required: true, message: "售后人员不能为空" }]
                },
                customerArchiveDetail: {
                    CustomerArchiveId: "",
                    CustomerName: "",
                    DepartmentName: "",
                    OrganizationName: "",
                    CustomerLevelTypeId: "",
                    GenderTypeId: "",
                    ImportanceTypeId: "",
                    Birthday: "",
                    WeddingDay: "",
                    School: "",
                    Telephone: "",
                    MajorRelationship: "",
                    CharacterTrait: "",
                    Hobby: "",
                    FrequentContacts: "",
                    RecentBook: "",
                    Remark: "",
                    EnclosureList: []
                }
            };
        },
        created() {
            this.isVisible = this.dialogFormVisible;
            this.getDetail();
        },
        watch: {
            //监听消息列表页面 点击消息图标重复刷新 标识
            "$store.state.IsRefreshMessage": function (params) {
                if (this.$store.state.IsRefreshMessage == 1) {
                    this.getList();
                    this.$store.commit("setIsRefreshMessage", 0);
                }
            }
        },
        methods: {
            getDetail() {
                this.PlcFuntionData = [];
                this.structuralComponentSolidInfoData = [];
                this.PlcFuntionDataLoadings = true;
                this.StructuralComponentSolidInfosLoadings = true;
                switch (this.MessageTypeId) {
                    case 3:
                        this.editable = true;
                        messages.getListById({ messageId: this.MessageId }).then(response => {
                            response.Range = [];
                            this.MessageDetail = response;
                            if (!this.MessageDetail.TaskDetailId) {
                                this.editable = false;
                            }
                            if (!this.MessageDetail.ServiceEmployees) {
                                this.MessageDetail.ServiceEmployees = [];
                            }
                            if (this.MessageDetail.StartTime && this.MessageDetail.EndTime) {
                                this.$set(this.MessageDetail, "Range", [
                                    this.MessageDetail.StartTime,
                                    this.MessageDetail.EndTime
                                ]);
                            }
                            //设置 实时数据table相关参数
                            this.plcFuntionDataTotal = this.MessageDetail.PointDatas.length;
                            this.PlcFuntionDataLoadings = false;
                            this.PlcFuntionData = this.MessageDetail.PointDatas.slice(0, 10);
                            //设置 结构部件table相关参数
                            this.structuralComponentSolidInfoDataTotal = this.MessageDetail.StructuralComponentSolidInfoDatas.length;
                            this.StructuralComponentSolidInfosLoadings = false;
                            this.structuralComponentSolidInfoData = this.MessageDetail.StructuralComponentSolidInfoDatas.slice(
                                0,
                                10
                            );
                            this.$nextTick(() => {
                                this.$refs["warningForm"].clearValidate();
                            });
                            // this.rules = this.initRules(this.rules);
                        }).catch(err => {
                            if (err) {
                                this.cancel()
                            }
                        });
                        break;
                    case 4:
                        customerArchive
                            .getCustomerArchiveShareDetail({ messageId: this.MessageId }) //CustomerArchiveId
                            .then(response => {
                                console.log(response);
                                this.customerArchiveDetail = response;
                                this.dialogStatus = optType;
                                this.$nextTick(() => {
                                    this.$refs["dataForm"].clearValidate();
                                });
                            });
                        break;

                    default:
                        break;
                }
            },
            //实时数据table 分页
            plcFuntionDataHandleCurrentChange(val) {
                this.plcFuntionDataPage = val;
                this.PlcFuntionData = this.MessageDetail.PointDatas.slice(
                    val,
                    val + this.plcFuntionDataLimit
                );
            },
            //结构部件table分页
            structuralComponentSolidInfoDataHandleCurrentChange(val) {
                this.structuralComponentSolidInfoDataPage = val;
                this.structuralComponentSolidInfoData = this.MessageDetail.StructuralComponentSolidInfoDatas.slice(
                    val,
                    val + this.structuralComponentSolidInfoDataLimit
                );
            },

            formatterDate(strDate) {
                let f = this.$options.filters["dateFilter"];
                return f(strDate, "YYYY-MM-DD HH:mm:ss");
            },
            handleChangeServiceEmployee(employees) {
                if (employees && employees.length > 0) {
                    this.MessageDetail.ServiceEmployees = [employees[0]];
                } else {
                    this.MessageDetail.ServiceEmployees = [];
                }
                this.$refs["warningForm"].validateField("ServiceEmployees");
            },
            handleUpChange(files) {
                this.customerArchiveDetail.EnclosureList = files;
            },
            cancel() {
                this.$emit("closeDetail");
            },
            closeDialog() {
                this.innerVisible = false;
            },
            handleReloading() {
            },
            handleSaveSuccess(_formData) {
                this.innerVisible = false;
                this.$emit("handleDialogSuccess");
            },
            showTaskDetail() {
                this.taskId = this.MessageDetail.TaskDetailId;
                this.dialogStatus = "detail";
                this.innerVisible = true;
            },
            validateData() {
                this.$refs["warningForm"].validate(valid => {
                    if (valid) {
                        this.dialogStatus = "create";
                        this.innerVisible = true;
                    }
                });
            }
        }
    };
</script>