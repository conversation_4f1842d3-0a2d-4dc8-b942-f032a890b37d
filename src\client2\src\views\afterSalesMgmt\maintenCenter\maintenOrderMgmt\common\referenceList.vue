<template>
  <div class="referenceList">
    <app-dialog
      title="故障维修百科"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='860'
      :maxHeight="600"
    >
      <template slot="body">
        <main v-loading="loading">
          <div class="searchBox">
            <div class="omit"><span style="font-weight:bold;">故障现象：</span>{{msg ? msg.FailureSymptom : ''}}</div>
            <div class="cList cl">
              <span class="fl">{{msg ? msg.FailureCaseCode : ''}}</span>
            </div>
          </div>
          <div class="mainBox cl">
            <div class="leftBox fl">
              <div class="searchBox1">
                <el-input v-model="FailureReson" placeholder="请输入查询内容"></el-input>
                <el-button size="mini" type="primary" @click="handleSearch()">查询</el-button>
              </div>
              <p>故障原因列表({{leftList.length}})</p>
              <div v-if="leftList.length>0">
                <el-card class="box-card" v-for="(ll,index) in leftList" :key="index">
                  <div class="omit"><el-checkbox v-model="ll.checked" @change="handleChangeLeft(ll)"></el-checkbox>&nbsp;{{index+1}}、{{ll.FailureReson}}</div>
                  <div class="cList cl">
                    <span class="fl">{{ll.FailureResonCode}}</span>
                  </div>
                </el-card>
              </div>
              <no-data v-else></no-data>
            </div>
            <div class="rightBox fl">
              <div class="searchBox1">
                <el-input v-model="Solution" placeholder="请输入查询内容"></el-input>
                <el-button size="mini" type="primary" @click="handleSearch()">查询</el-button>
              </div>
              <p>解决方法列表({{rightList.length}})</p>
              <div v-if="rightList.length>0">
                <el-card class="box-card" v-for="(rl,index) in rightList" :key="index">
                  <div class="omit"><el-checkbox v-model="rl.checked" @change="handleChangeRight(rl)"></el-checkbox>&nbsp;{{index+1}}、{{rl.Solution}}</div>
                  <div class="cList cl">
                    <span class="fl">{{rl.FailureSolutionCode}}</span>
                  </div>
                </el-card>
              </div>
              <no-data v-else></no-data>
            </div>
            
          </div>
        </main>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- <el-button type="primary" size="mini">引用</el-button> -->
        <app-button text="引用" @click="handleSuccess" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as failurecase from '@/api/failurecase';
import NoData from "@/views/common/components/noData";
export default {
  name: "referenceList",
  components: {
    NoData

  },
  mixins: [],
  props: {
    msg:{
      type:Object,
      default:null
    },
    outerData:{
      type:Object,
      default:function(){
        return null
      }
    },
    oData:{
      type:Boolean,
      default:false
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
        if(val){
          this.FailureReson='';
          this.Solution='';
          this.saveLeftData=[];
          this.saveRightData=[];
          this.loadSelectData();
        }
    }
  },
  created() {
    
  },
  data() {
    return {
      loading:false,
      checked:false,
      leftList:[],
      rightList:[],
      disabledBtn:false,
      FailureReson:'',
      Solution:'',
      saveLeftData:[],
      saveRightData:[]    
    };
  },
  methods: {
    handleChangeLeft(d){
      console.log(d)
      if(d.checked){
        this.saveLeftData.push(d);
      }else{
        for (var i = 0; i < this.saveLeftData.length; i++) {
            if ((this.saveLeftData[i].Id).indexOf(d.Id) > -1) {//判断key为999的对象是否存在，
                index = i;
                this.saveLeftData.splice(index, 1);//存在即删除
            }
        }
      }
    },
    handleChangeRight(d){
      console.log(d)
      if(d.checked){
        this.saveRightData.push(d);
      }else{
        for (var i = 0; i < this.saveRightData.length; i++) {
            if ((this.saveRightData[i].Id).indexOf(d.Id) > -1) {//判断key为999的对象是否存在，
                index = i;
                this.saveRightData.splice(index, 1);//存在即删除
            }
        }
      }
    },
    handleSearch(){
      this.loadSelectData();
    },
    loadSelectData(){
      this.loading=true;
      console.log(this.FailureReson,this.Solution)
      failurecase.getSubitem({Id:this.msg.Id,FailureReson:this.FailureReson,Solution:this.Solution}).then(res => {
        console.log(res)
        this.loading=false;
        if(this.oData){
          let a=null;
          res.FailureAnalysiseList.forEach(v => {
            v.checked=false;
            a=this.outerData.CauseAnalysisList.find(s => s.AnalysisContent == v.FailureReson);
            if(a){
              v.checked=true;
            }
          })
           res.FailureSolutionList.forEach(v => {
            v.checked=false;
            a=this.outerData.SolutionList.find(s => s.SolutionContent == v.Solution);
            if(a){
              v.checked=true;
            }
          })
        }else{
          res.FailureAnalysiseList.forEach(v => {
            v.checked=false;
          })
          res.FailureSolutionList.forEach(v => {
            v.checked=false;
          })
        }
        if(this.saveLeftData.length>0){
          res.FailureAnalysiseList.forEach(v => {
            let a=this.saveLeftData.find(s => s.Id == v.Id);
            if(a) v.checked=true;
          })
        }
        if(this.saveRightData.length>0){
          res.FailureSolutionList.forEach(v => {
            let a=this.saveRightData.find(s => s.Id == v.Id);
            if(a) v.checked=true;
          })
        }
        this.leftList=res.FailureAnalysiseList;
        this.rightList=res.FailureSolutionList;
      })
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    handleSuccess(){
      this.disabledBtn=true;
      if(this.oData){
        this.leftList.forEach(v => {
          if(v.checked){
            let a=this.outerData.CauseAnalysisList.find(s => s.FailureCaseAnalysisId == v.Id);
            if(!a){
              this.outerData.CauseAnalysisList.unshift({
                AnalysisContent: v.FailureReson,
                FailureCaseAnalysisId: v.Id,
                FailureResonCode:v.FailureResonCode,
                Id: "",
                IsNew: false,
              })
            }
          }else{
            for (var i = 0; i < this.outerData.CauseAnalysisList.length; i++) {
              if ((this.outerData.CauseAnalysisList[i].FailureCaseAnalysisId).indexOf(v.Id) > -1) {
                let index = i;
                this.outerData.CauseAnalysisList.splice(index, 1);//存在即删除
              }
            }
          }
        })
        this.rightList.forEach(v => {
          if(v.checked){
            let a=this.outerData.SolutionList.find(s => s.FailureCaseSolutionId == v.Id);
            if(!a){
              this.outerData.SolutionList.unshift({
                SolutionContent: v.Solution,
                FailureCaseSolutionId: v.Id,
                FailureSolutionCode:v.FailureSolutionCode,
                AttachmentList:v.AttachmentList,
                Id: "",
                IsNew: false,
              })
            }
          }else{
            for (var i = 0; i < this.outerData.SolutionList.length; i++) {
                if ((this.outerData.SolutionList[i].FailureCaseSolutionId).indexOf(v.Id) > -1) {
                    let index = i;
                    this.outerData.SolutionList.splice(index, 1);//存在即删除
                }
            }
          }
        })
      }else{
        this.outerData.Phenomenon=this.msg.FailureSymptom;
        this.outerData.IsNew=false;
        this.outerData.FailureCaseId=this.msg.Id;
        this.outerData.CauseAnalysisList=[];
        this.outerData.SolutionList=[];
        this.leftList.forEach(v => {
          if(v.checked){
            this.outerData.CauseAnalysisList.unshift({
              AnalysisContent: v.FailureReson,
              FailureCaseAnalysisId: v.Id,
              FailureResonCode:v.FailureResonCode,
              Id: "",
              IsNew: false,
            })
          }
        })
        this.rightList.forEach(v => {
          if(v.checked){
            this.outerData.SolutionList.unshift({
              SolutionContent: v.Solution,
              FailureCaseSolutionId: v.Id,
              FailureSolutionCode:v.FailureSolutionCode,
              AttachmentList:v.AttachmentList,
              Id: "",
              IsNew: false,
            })
          }
        })
      }
      this.disabledBtn=false;
      this.$emit('saveSuccess',this.outerData);
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style scoped>
  .searchBox{
    position: relative;
    border-bottom: 1px solid #dcdfe6;
  }
  .searchBox1{
    position: relative;
    margin-top: 10px;
  }
  .searchBox1 >>> .el-input{
    width:calc(100% - 50px);
  }
  .searchBox1 >>> .el-button{
    position: absolute;
    right:0;
    top:0;
  }
  .box-card >>> .el-card__body{
    padding:10px;
  }
</style>
<style lang="scss" scoped>
.box-card{
  margin-bottom: 10px;
}
.cList{
  span{
    display: inline-block;
    color:#F59A23;
    margin-right: 6px;
    border: 1px solid;
    padding: 2px 4px;
    border-radius: 6px;
    margin-bottom:6px;
  }
}
.mainBox{
  height:548px;
  >div{
    width:50%;
    height:100%;
    overflow-y:auto;
    >p{
      margin:6px 0;
      color:#333;
      font-weight:bold;
    }
  }  
  .leftBox{
    border-right:1px solid #dcdfe6;
    padding-right:10px;
  }
  .rightBox{
    padding-left:10px;
  }
}
.omit{
  width:100%;
  margin-bottom: 6px;
}
</style>