<template>
  <div>
    <app-dialog
      title="指派人员"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='600'
    >
      <template slot="body">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          :label-width="'100px'"
        >
            <el-row>
                <el-col :span="24">
                    <el-form-item label="问题名称">
                        <span v-if="row">{{ row.Name }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="处理人" prop="TargetEmployee">
                        <emp-selector :multiple='false' :showType='2' :list='formData.TargetEmployee' key='service-users' @change='handleChangeUsers'></emp-selector>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import empSelector from "../../../common/empSelector"
import * as impMgmt from "@/api/implementation/impManagement2"

export default {
  name: "ques-assign",
  components: {
    // tabs,
    // tags,
    empSelector,

  },
  mixins: [],
  props: {
    row: {
        type: Object,
        required: true
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
        this.resetFormData();
    },
    row(val) {
        if(val) {
            this.formData = Object.assign({}, this.formData, val)
        }
    },
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
        disabledBtn: false,
        rules: {
            TargetEmployee: { fieldName: "处理人", rules: [{ required: true, trigger: 'change' }]},
        },
        formData: {
            Id: this.row.Id, //
            TargetEmployee: [], 
            EmployeeId: '', 
        },
    };
  },
  methods: {
    handleChangeUsers(users) {
        if (users && users.length > 0) {
            this.formData.TargetEmployee = [users[0]]
        } else {
            this.formData.TargetEmployee = []
        }
    },
    resetFormData() {
      let temp = {
        TargetEmployee: [], 
        EmployeeId: '', 
      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    createData() {
        let validate = this.$refs.formData.validate();
        Promise.all([validate]).then(valid => {
            let postData = JSON.parse(JSON.stringify(this.formData));
            //提交数据保存
            postData = Object.assign({}, this.formData);
            postData.EmployeeId = postData.TargetEmployee[0].EmployeeId
            impMgmt.assign(postData).then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.$refs.appDialogRef.createData();
            })
        });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
