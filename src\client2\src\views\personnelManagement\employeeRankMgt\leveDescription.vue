<template>
    <div>
        <app-dialog title="职级说明管理" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000" :height="800">
            <template slot="body">
                <div class="wrapperMain __dynamicTabContentWrapper">
                    <div class="content __dynamicTabWrapper">
                        <app-table ref="mainTable"
                        :tab-columns="tabColumns" :tab-datas="tabDatas" :optColWidth="50" :isShowConditionArea="false"
                        :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true"
                        :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode'
                        :isShowBtnsArea='false'>
                            <template slot="EmployeeLevelType" slot-scope="scope">
                                <!-- <user-leve-icon :number="scope.row.EmployeeLevelType" style="display: inline-block;" /> -->
                                T{{ scope.row.EmployeeLevelType }}
                            </template>
                            <template slot="Qualifications" slot-scope="scope">
                                {{scope.row.Qualifications|emptyFilter}}
                            </template>
                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button @click="handleEdit(scope.row,'update')"></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <!-- 关闭 -->
                <app-button @click="handleClose" text="关闭" type></app-button>
            </template>
        </app-dialog>
        <!-- 职级说明 编辑 -->
        <leve-create
            v-if="dialogLeveCreateVisible" :dialogStatus="dialogStatus" :id="selectId"
            @closeDialog="dialogLeveCreateVisible=false" @saveSuccess="handleLeveCreateSuccess"
            :dialogFormVisible="dialogLeveCreateVisible"></leve-create>
    </div>
</template>

<script>
import UserLeveIcon from "@/components/UserLeveIcon"
import indexPageMixin from "@/mixins/indexPage";
import * as EmployeeLevelApi from "@/api/personnelManagement/EmployeeLevel";

import leveCreate from "./leveCreate"
export default {
    name: "employeeRankMgt-leve-description",
    mixins: [indexPageMixin],
    components: {
        UserLeveIcon,
        leveCreate
    },
    created() {
        this.getList()
    },
    data() {
        return {
            
            listLoading: false,
            listQuery: {},
            tabDatas: [
                // { EmployeeLevelType: 1, Qualifications: ""},
                // { EmployeeLevelType: 2, Qualifications: ""},
                // { EmployeeLevelType: 3, Qualifications: ""},
                // { EmployeeLevelType: 4, Qualifications: ""},
                // { EmployeeLevelType: 5, Qualifications: ""},
                // { EmployeeLevelType: 6, Qualifications: ""},
                // { EmployeeLevelType: 7, Qualifications: ""},
                // { EmployeeLevelType: 8, Qualifications: ""},
                // { EmployeeLevelType: 9, Qualifications: ""},
                // { EmployeeLevelType: 10, Qualifications: ""},
            ], //原始数据
            /******************* 表格 *******************/
            layoutMode: 'simple',
            tabColumns: [
                { attr: { prop: "EmployeeLevelType", label: "职级", width: '70px', align: 'center' }, slot: true },
                { attr: { prop: "Qualifications", label: "任职资格说明", showOverflowTooltip: true }, slot: true },
            ],
            dialogLeveCreateVisible: false,
            selectId: '',
            dialogStatus: '',
            
        };
    },
    methods: {
        // 编辑/详情
        handleEdit(row, optType){
            this.selectId = row.Id;
            this.dialogStatus = optType;
            this.dialogLeveCreateVisible = true;
        },
        //获取成员列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            this.listLoading = true;
            EmployeeLevelApi.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        handleLeveCreateSuccess(){
            this.getList();
            this.dialogLeveCreateVisible = false;
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style lang='scss' scoped>
.colorBlue{
    color: #409EFF;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
.wrapperMain{
    height: 706px;
    display: flex;
}
</style>