<!-- 部门树状选择器 -->
<template>
  <el-cascader
    v-model="value_"
    :options="departmentTreeList"
    :loading="departmentLoading"
    :show-all-levels="false"
    :props="{
      value: 'Id',
      label: 'DepartmentName',
      emitPath: false,
      checkStrictly:true
    }"
    :disabled="disabled"
    style="width: 100%"
    clearable
    placeholder="请选择部门"
    @change="handleChange"
  />
</template>

<script>
import { getListByCondition } from "@/api/personnelManagement/systemDepartment";
import { listToTreeSelect } from "@/utils";

export default {
  props: {
    value: {
      type: [String, null],
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      value_: [],
      departmentTreeList: [],
      departmentLoading: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.value_ = val;
      },
      immediate: true,
    },
  },
  created() {
    this.getDepartmentList();
  },
  methods: {
    getDepartmentList() {
      this.departmentLoading = true;
      getListByCondition({})
        .then(res => {
          this.departmentTreeList = listToTreeSelect(res);
        })
        .finally(() => {
          this.departmentLoading = false;
        });
    },
    handleChange(val) {
      this.$emit("change", val);
      this.$emit("input", val);
    },
  },
};
</script>

<style></style>
