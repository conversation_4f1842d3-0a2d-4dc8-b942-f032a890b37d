<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='500'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="分类" prop="ClassifyId">
                                <treeselect key='type2'
                                    class="treeselect-common"
                                    :append-to-body="true"
                                    :normalizer="unitNormalizer"
                                    v-model="formData.ClassifyId" :default-expand-level="3"
                                    :options="treeDatas" :multiple="false" placeholder='' :show-count="true"
                                    :noResultsText='noResultsTextOfSelTree'
                                    :disabled="!editable"
                                    :noOptionsText="noOptionsTextOfSelTree"
                                    zIndex='9999'
                                    @input="hadnleChangeCustomerUnitId"
                                    >
                                </treeselect>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="类型名称" prop="Name">
                                <el-input v-if="editable" v-model="formData.Name" :disabled="!editable" maxlength="50"></el-input>
                                <span v-else>{{ formData.Name }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </div>

            </el-form>
        </template>
        <template slot="footer">
            <!-- <span class="fl m-r-50" v-if="dialogStatus == 'create'">
                <el-checkbox v-model="isContinue">继续添加</el-checkbox>
            </span> -->
            
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as classify from "@/api/classify";
import * as salesAfterQuestionType from "@/api/afterSalesMgmt/salesAfterQuestionType";
import { listToTreeSelect } from "@/utils";
import * as metadata from '@/api/systemManagement/metadata'
import noData from "@/views/common/components/noData";

export default {
    name: "custom-mgmt-pool-create",
    directives: {},
    components: {
        noData,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [],
    props: {
        dialogStatus: {
            //create、update、detail、follow（跟进）
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        chooseUnitId: {
            type: String,
            default: ''
        },
    },
    watch: {

        "$attrs.dialogFormVisible": {
            
            handler(val) {
                // if (!val) {
                //     this.isContinue = false;
                // }
                
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }


                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            let mainTitle = '问题类型'
            if (this.dialogStatus == "create") {
                return `创建${mainTitle}`
            } else if (this.dialogStatus == "update") {
                return `编辑${mainTitle}`
            } else if (this.dialogStatus == "detail") {
                return `${mainTitle}详情`
            } 
        },
    },
    created() {
        this.getCustomerProps()
        if(this.dialogStatus == 'create') {
            this.getTreeDatas().then(res => {
                this.initTreeDatas(res.Items)
            })
        }
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            // isContinue: false,
            customerProps: [],
            treeDatas: [],
            unitNormalizer(node) {
                // treeselect定义字段
                return {
                    id: node.Id,
                    label: node.Name,
                    children: node.children
                }
            },
            months: Array.from(Array(12), (v, k) => {
                return {
                    value: k + 1,
                    label: `${k + 1}月`
                } 
            }),
            days: Array.from(Array(31), (v, k) => {
                return {
                    value: k + 1,
                    label: `${k + 1}日`
                } 
            }),
            // ClientUnitNames: [],
            pickerOptions: {
                //禁止选择当前日期后的时间
                disabledDate(time) {
                    let currentDate = dayjs().format('YYYY-MM-DD')
                    let choiceDate = dayjs(time).format('YYYY-MM-DD')
                    let diffDay = dayjs(currentDate).diff(dayjs(choiceDate), 'day')
                    if (diffDay >= 0) {
                        return false
                    }
                    return true
                },
            },
            loading: false,
            disabledBtn: false,
            rules: {
                Name: { fieldName: "类型名称", rules: [{ required: true }] },
                ClassifyId: { fieldName: "模板分类", rules: [{ required: true, trigger: 'change' }] },

            },
            labelWidth: "80px",
            formData: {
                Id: "", //
                ClassifyId: null,
                Name: '', //设备参数模板名称
            },
            // treedata: [],
            tabsData: [{
                    name: 'customAnalysis',
                    label: '客户分析'
                },
                {
                    name: 'relPerson',
                    label: '客户关系人'
                },
                {
                    name: 'expenseRecord',
                    label: '费用记录'
                },
                {
                    name: 'visitRecord',
                    label: '拜访记录'
                },
                {
                    name: 'assocOrder',
                    label: '关联订单'
                },
            ],
        };
    },

    methods: {
        handleFocus() {
            this.collClick = this.$refs.coll.setActiveNames
            this.$refs.coll.setActiveNames = () => {}
        },
        handleBlur() {
            this.$refs.coll.setActiveNames = this.collClick
        },    
        getCtrlOptions(metadataId) {
            // PropertyDataValue options [{value: 1, label: 'xxx'}] 组成的字符串
            let metadata = this.customerProps.find(s => s.value == metadataId)
            if(!metadata || !metadata.obj.PropertyDataValueList) {
                return []
            }
            return metadata.obj.PropertyDataValueList
        },
        //获取控件类型（跟进元数据id获取元数据对应的控件卖局）
        getCtrlType(metadataId) {
            let metadata = this.customerProps.find(s => s.value == metadataId)
            if(metadata) {
                return metadata.obj.PropertyDataType
            }
            return 0
        },
        handleAdd(group) {
            if(group) {
                if(group && group.FieldList.length >= 50) {
                    this.$message({
                        message: "添加失败，不能超过50条",
                        type: "error"
                    });
                    return false
                }
                group.FieldList.push({
                    Name: ''
                })
            }
        },
        removeProp(group, idx) {
            if(group && group.FieldList) {
                this.$confirm("是否确认删除", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    group.FieldList.splice(idx, 1)
                });
            }
        },
        hadnleChangeCustomerUnitId() {
            this.$refs.formData.validateField("ClassifyId");
        },
        getTreeDatas() {
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: 19
            };
            return classify.getListPage(paramData)
        },
        initTreeDatas(list) {
            let _this = this
            _this.treeDatas = listToTreeSelect(list, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

            if(this.dialogStatus == 'create' && this.chooseUnitId && !!list.find(s => s.Id == this.chooseUnitId)) {
                this.formData.ClassifyId = this.chooseUnitId
            }
        },
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        resetFormData() {
            let temp = {
                Id: "", //
                // ClassifyId: null,
                Name: '', //设备参数模板名称
            };
            this.formData = Object.assign({}, this.formData, temp);
        },
        getCustomerProps() {
            let postData = {
                PageIndex: 1,
                PageSize: 10000
            }
            metadata.getList(postData).then(res => {
                let datas = res.Items.map((s, idx) => {
                    return{
                        value: s.Id,
                        label: s.Name,
                        obj: s
                    }
                });
                this.customerProps = datas
            });
        },
        createData() {
            let validate = this.$refs.formData.validate();

            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));

                this.disabledBtn = true

                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = salesAfterQuestionType.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = salesAfterQuestionType.edit(postData);
                }

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    this.$refs.appDialogRef.createData();

                    // if (this.isContinue) {
                    //     this.resetFormData();
                    // } 

                    // this.$refs.appDialogRef.createData(postData, this.isContinue);
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },
        async getDetail() {
            let m1 = this.getTreeDatas()
            let m2 = salesAfterQuestionType.detail({id: this.id})
            this.loading = true
            Promise.all([m1, m2]).then(res => {

                this.loading = false
                let res1 = res[0]
                let res2 = res[1]

                this.initTreeDatas(res1.Items)
                this.formData = Object.assign({}, this.formData, res2);

            }).catch(err => {
                this.loading = false
            });

        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapper {
    min-height: 150px;
}

</style>
