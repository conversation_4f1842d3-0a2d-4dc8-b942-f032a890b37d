<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title
        title="客诉管理"
        :subTitle="['客诉的创建、跟进、管理页面']"
        :showBackBtn="!!returnUrl"
        @goBack="handleGoBack"
      ></page-title> -->
      <div style="height: 100%;" class="__dynamicTabContentWrapper">
        <div class="content __dynamicTabWrapper">
          <app-table
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas" :isShowBtnsArea='false'
            :tab-auth-columns="tabAuthColumns"
            :isShowAllColumn="true"
            :loading="listLoading"
            @rowSelectionChanged="rowSelectionChanged"
            :isShowOpatColumn="true"
            :startOfTable="startOfTable"
            :multable="false"
            @sortChagned='handleSortChange'
            :layoutMode='layoutMode'
          >
            <template slot="ProblemTypes" slot-scope="scope">{{
              scope.row.ProblemTypes | problemTypesFilter
            }}</template>
            <template slot="ImpactType" slot-scope="scope">{{
              scope.row.ImpactType | impactTypeFilter
            }}</template>
            <!-- <template slot="Type" slot-scope="scope">{{ scope.row.Type | typeFilter }}</template> -->

            <template slot="Status" slot-scope="scope">
              <span
                class="item-status"
                :style="{
                  backgroundColor: getStatusObj(scope.row.Status).color
                }"
              >
                {{ scope.row.Status | customerComplaintStatusFilter }}
              </span>
            </template>
            <!-- <template slot="ApprovalStatus" slot-scope="scope">
              <span
                class="item-status"
                :class="`status-${scope.row.ApprovalStatus}`"
                >{{ scope.row.ApprovalStatus | approvalStatusFilter }}</span
              >
            </template> -->
            <template slot="ComplaintTime" slot-scope="scope">{{
              scope.row.ComplaintTime | dateFilter("YYYY-MM-DD HH:mm")
            }}</template>
            <template slot="CustomerServiceEmployee" slot-scope="scope">
              <span v-if="scope.row.CustomerServiceEmployee">{{
                scope.row.CustomerServiceEmployee.Name
              }}</span>
            </template>

            <!-- 表格查询条件区域 -->
            <template slot="conditionArea">
              <div
                style="border-bottom: 1px solid #EBEEF5;margin-bottom: 10px;"
              >
                <tags
                  :items="handlerEmployeeIds"
                  v-model="listQuery.TableActive"
                >
                  <template v-for="t in handlerEmployeeIds" :slot="t.label">
                    {{ t.label }}
                  </template>
                </tags>
              </div>
              <app-table-form
                :label-width="'80px'"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="handleResetSearch"
                :layoutMode='layoutMode'
              >
                <template slot="ComplaintTitle">
                        <el-input style="width: 100%;" 
                            placeholder="搜索投诉问题..."
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQuery.ComplaintTitle"
                        ></el-input>
                    </template>
                <template slot="CustomerName">
                  <el-input v-model.trim="listQuery.CustomerName"></el-input>
                </template>
                <template slot="ProblemTypes">
                  <el-select
                    style="width: 100%;"
                    class="sel-ipt"
                    v-model="listQuery.problemTypes"
                    placeholder
                    clearable
                  >
                    <el-option
                      v-for="item in problemTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </template>
                <template slot="RegionalId">
                  <!-- <treeselect
                    :normalizer="normalizer"
                    key="type1"
                    v-model="listQuery.RegionalId"
                    :default-expand-level="3"
                    :options="treedata"
                    :multiple="false"
                    placeholder=""
                    :show-count="true"
                    :noResultsText="noResultsTextOfSelTree"
                    :noOptionsText="noOptionsTextOfSelTree"
                  >
                  </treeselect> -->

                  <div class="el-input el-input--mini">
                    <div style="display: flex; height: 28px;line-height: 28px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; ">
                      <div style="padding-left: 10px;">
                        <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">选择</span>
                      </div>
                      <div style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="listQuery.areaName">{{ listQuery.areaName }}</div>
                      <div style="width: 28px; text-align: center;">
                        <i style="cursor: pointer;" title="删除" @click="handleClearRegional" v-show="listQuery.RegionalId" class="el-icon-close"></i>
                      </div>
                    </div>
                  </div>

                </template>
                <template slot="DutyDepartmentId">
                  <el-select
                    style="width: 100%;"
                    class="sel-ipt"
                    v-model="listQuery.DutyDepartmentId"
                    placeholder
                    clearable
                  >
                    <el-option
                      v-for="item in dutyDepartment"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </template>
                <template slot="EmployeeName">
                  <el-input v-model.trim="listQuery.EmployeeName"></el-input>
                  <!-- <el-select
                    style="width: 100%;"
                    class="sel-ipt"
                    filterable
                    v-model="listQuery.EmployeeName"
                    placeholder
                    clearable
                  >
                    <el-option
                      v-for="item in serviceEmployee"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select> -->
                </template>
                <template slot="Range">
                  <el-date-picker
                    v-model="listQuery.Range"
                    type="datetimerange"
                    align="right"
                    unlink-panels
                    range-separator="-"
                    start-placeholder
                    end-placeholder
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm"
                    style="width: 100%;"
                  ></el-date-picker>
                </template>
                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                </template>
              </app-table-form>
            </template>


            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <app-table-row-button
                @click="handleTrackDialog(scope.row, 'detail')"
                :type="2"
              ></app-table-row-button>
              <app-table-row-button v-if=" scope.row.CustomerServiceEmployeeId == currentEmployeeId && scope.row.Status != 3" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>
              <app-table-row-button v-if=" scope.row.IsCurrentEmployeeId && scope.row.Status != 3" @click="handleDisposeDialog(scope.row)" text="处理" :type="2"></app-table-row-button>
              <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
            </template>
          </app-table>
        </div>
        <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!-- 新增、编辑 -->
    <create-page
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
    ></create-page>

    <!-- 处理 -->
    <dispose-page
      @closeDialog="closeTrackDialog"
      @saveSuccess="handleTrackSaveSuccess"
      :dialogFormVisible="dialogTrackFormVisible"
      :dialogStatus="dialogTrackStatus"
      :id="id"
    ></dispose-page>
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>

    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="listQuery.RegionalId ? [listQuery.RegionalId] : []"
    ></v-area-choose>
  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as afterService from "@/api/afterSalesMgmt/customerComplaint";
import * as systemManagement from "@/api/systemManagement/regionalManagement";
import * as systemDepartment from "@/api/systemManagement/systemDepartment";
import createPage from "./create";
// import trackPage from '../track'
import { listToTreeSelect } from "@/utils";
import { vars } from "./vars";
import { getUserInfo } from "@/utils/auth";
import * as projVars from "../../projectDev/common/vars";
import disposePage from "./dispose";
import { mapGetters } from "vuex";
import * as approvalVars from '../../salesMgmt/common/vars'
import vExport from "@/components/Export/index";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";

export default {
  name: "after-service",
  mixins: [indexPageMixin],
  components: {
    createPage,
    disposePage,
    vExport,
    vAreaChoose,
    // trackPage,
  },
  computed: {
    ...mapGetters(["autoOpenDialogList"]),
    returnUrl() {
      let url = decodeURI(this.$route.query.returnUrl || "");
      return url;
    }
  },
  filters: {
    // statusFilter(val) {
    //     let obj = vars.afterService.planStatus.find(s => s.value == val)
    //     if(obj) {
    //         return obj.label
    //     }
    //     return val
    // },
    //customerComplaintStatus
    customerComplaintStatusFilter(status) {
      let tmp = vars.customerComplaintStatus.find(s => s.value == status);
      if (tmp) {
        return tmp.label;
      }
      return "无";
    },
    problemTypesFilter(status) {
      let obj = vars.problemTypes.find(s => s.value == status);
      if (obj) {
        return obj.label;
      }
      return status;
    },
    impactTypeFilter(status) {
      let obj = vars.impactType.find(s => s.value == status);
      if (obj) {
        return obj.label;
      }
      return status;
    },
    // approvalStatusFilter(status) {
    //   if (!status) {
    //     return "无";
    //   }

    //   const statusObj = approvalVars.vars.orderMgmt.approvalStatus.find(s => s.value == status);
    //   let result = "无";
    //   if (statusObj) {
    //     result = statusObj.label;
    //   }
    //   return result;
    // }
  },
  created() {
    //   this.getServiceEmployee()
    this.currentEmployeeId = getUserInfo().employeeid
    this.getAreas();
    this.getDutyDepartments();
    this.getList();
  },
  watch: {
    autoOpenDialogList: {
      handler(val) {
        this.checkAutoDialog();
      },
      deep: true
    },
    "listQuery.TableActive": {
      handler(val) {
        if (val == "my") {
          delete this.listQuery.EmployeeName;
          let idx = this.tableSearchItems.findIndex(
            s => s.prop == "DutyDepartmentId"
          );
          if (idx > -1) {
            this.tableSearchItems.splice(idx, 1);
          }

          idx = this.tableSearchItems.findIndex(
            s => s.prop == "EmployeeName"
          );
          if (idx > -1) {
            this.tableSearchItems.splice(idx, 1);
          }
        } else {
          this.$set(this.listQuery, "DutyDepartmentId", "");
          this.tableSearchItems.splice(3, 0, {
            prop: "DutyDepartmentId",
            label: "责任部门"
          });

          this.$set(this.listQuery, "EmployeeName", "");
          this.tableSearchItems.splice(3, 0, {
            prop: "EmployeeName",
            label: "客服人员"
          });
          // this.getServiceEmployee();
        }
        this.getList();
        this.collapseOrExpand()
      },
      immediate: true
    }
  },
  data() {
    return {
            layoutMode: 'simple',
      normalizer(node) {
        // treeselect定义字段
        return {
          id: node.Id,
          label: node.RegionalName,
          children: node.children
        };
      },
      treedata: [],
      dutyDepartment: [],
      // serviceEmployee: [],
      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,
      currentEmployeeId:'',
      dialogTrackFormVisible: false,
      dialogTrackStatus: "dispose",

      priorities: "",
      total: 0,
      listLoading: false,
      problemTypes: vars.problemTypes,
      listQuery: {
        TableActive: "",
        ComplaintTitle: "",
        CustomerName: null,
        RegionalId: null,
        ProblemTypes: null,
        DutyDepartmentId: "",
        EmployeeName: "",
        AgentEmployeeId: ""
      },
      multipleSelection: [],
      tableSearchItems: [
        { prop: "ComplaintTitle", label: "投诉问题", mainCondition: true },
        { prop: "CustomerName", label: "客户名称" },
        { prop: "ProblemTypes", label: "问题类型" },
        { prop: "RegionalId", label: "地区" },
        // { prop: "DutyDepartmentId", label: "责任部门" },
        // { prop: "EmployeeName", label: "客服人员" },
        { prop: "Range", label: "反应时间" }
      ],
      tabColumns: [
        {
          attr: {
            prop: "Code",
            label: "客诉单编号",
            width: "140"
          }
        },
        {
          attr: {
            prop: "ComplaintTitle",
            label: "投诉问题",
            width: "180",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "Status",
            label: "状态",
            width: "100",
            sortable: 'custom'
          },
          slot: true
        },
        // {
        //   attr: {
        //     prop: "ApprovalStatus",
        //     label: "审批状态",
        //     width: "100"
        //   },
        //   slot: true
        // },
        {
          attr: {
            prop: "ProblemTypes",
            label: "问题类型",
            width: "100"
          },
          slot: true
        },
        {
          attr: {
            prop: "CustomerName",
            label: "客户名称",
            width: "150"
          }
        },
        {
          attr: {
            prop: "RegionalName",
            label: "地区",
            width: "180",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "ComplaintTime",
            label: "反应时间",
            width: "140",
            sortable: 'custom'
          },
          slot: true
        },
        {
          attr: {
            prop: "ImpactType",
            label: "造成影响",
            width: "140",
            sortable: 'custom'
          },
          slot: true
        },
        {
          attr: {
            prop: "CustomerServiceEmployee",
            label: "客服人员",
            width: "120"
          },
          slot: true
        },
        {
          attr: { prop: "DutyDepartmentName", label: "责任部门", width: "120" }
        }
      ],
      tabDatas: [],
      employees: [],
      handlerEmployeeIds: [
        { value: "", label: "全部" },
        { value: "my", label: "我处理的" }
      ],
      rData:null,
      cData:[],
      dialogExportVisible:false,
      dialogRegionalVisible: false,
    };
  },
  methods: {
    collapseOrExpand(val){
      this.$nextTick(()=>{
        this.$refs.mainTable.setTabHeight()
      })
    },
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    //检查并打开需要自动弹框
    checkAutoDialog() {
      let currNavUrl = this.$route.path //'/afterSalesMgmt/customerComplaint'
      let oldList = this.autoOpenDialogList[currNavUrl]
      let waitList = oldList.filter(s => !s.isHandle);
      if (waitList && waitList.length > 0) {
        let firstObj = waitList[0];
        this.handleDisposeDialog({
          Id: firstObj.id
        });
        firstObj.isHandle = true;
        //只需要帮用户打开一次点击的”查看“审批弹框
        this.$store.commit("SET_autoOpenDialogList", currNavUrl, oldList);
      }
    },
    getStatusObj(status) {
      return vars.customerComplaintStatus.find(s => s.value == status) || {};
    },
    isShowTrackBtn(row) {
      if (row && row.EmployeeList && row.EmployeeList.length > 0) {
        return row.EmployeeList.map(s => s.EmployeeId).some(
          s => s == getUserInfo().employeeid
        );
      }
      return false;
    },
    onBtnClicked: function(domId) {
      switch (domId) {
        case "btnAdd":
          this.handleDialog("create");
          break;
        case "btnExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },
    handleExport(){
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      if (this.listQuery.Range && this.listQuery.Range.length == 2) {
        postData.StartComplaintTime = postData.Range[0];
        postData.EndComplaintTime = postData.Range[1];
        delete postData.Range;
      }
      if (this.listQuery.TableActive == "my") {
        postData.AgentEmployeeId = getUserInfo().employeeid;
        postData.CustomerServiceEmployeeId = getUserInfo().employeeid;
      }
      this.rData={
          "exportSource": 12,
          "columns": [],
          "searchCondition": postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'客诉单编号',
        value:'Code'
      },{
        label:'投诉问题',
        value:'ComplaintTitle'
      },{
        label:'状态',
        value:'Status'
      },
      // {
      //   label:'审批状态',
      //   value:'ApprovalStatus'
      // },
      {
        label:'问题类型',
        value:'ProblemTypes'
      },{
        label:'客户名称',
        value:'CustomerName'
      },{
        label:'地区',
        value:'RegionalName'
      },{
        label:'反应时间',
        value:'ComplaintTimeString'
      },{
        label:'造成影响',
        value:'ImpactType'
      },{
        label:'客服人员',
        value:'CustomerServiceEmployeeString'
      },{
        label:'责任部门',
        value:'DutyDepartmentName'
      },{
        label:'问题说明',
        value:'Remark'
      },{
        label:'问题回复',
        value:'QuestionResponse'
      }]
      this.dialogExportVisible=true;
    },
    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.getList();
      this.closeDialog();
    },
    handleTrackDialog(row, optType = "update") {
      // 弹出编辑框
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },
    handleDisposeDialog(row) {
      this.id = row.Id;
      this.dialogTrackStatus = "dispose";
      this.dialogTrackFormVisible = true;
    },
    handleTrackSaveSuccess(_formData) {
      this.getList();
      this.closeTrackDialog();
    },
    closeTrackDialog() {
      this.dialogTrackFormVisible = false;
    },

    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    // getEmployees() {
    //   afterService.getEmployees({}).then(res => {
    //     this.employees = res.map(s => {
    //       return {
    //         label: s.Name,
    //         value: s.EmployeeId
    //       };
    //     });
    //   });
    // },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleUpdate(row, optType = "update") {
      // 弹出编辑框
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        TableActive: this.listQuery.TableActive,
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        PlanName: null,
        PlanNo: null,
        Type: null
      };
      this.getList(); //刷新列表
    },
    getAreas() {
      systemManagement.getListByCondition({}).then(res => {
        this.treedata = listToTreeSelect(res);
      });
    },
    // getServiceEmployee() {
    //   afterService.getServiceEmployee({}).then(res => {
    //     this.serviceEmployee =
    //       res.map(s => {
    //         return {
    //           value: s.EmployeeId,
    //           label: s.Name
    //         };
    //       }) || [];
    //   });
    // },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.getList()
    },
    //获取项目列表
    getList() {
      this.listLoading = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));

      if (this.listQuery.Range && this.listQuery.Range.length == 2) {
        postData.StartComplaintTime = postData.Range[0];
        postData.EndComplaintTime = postData.Range[1];
        delete postData.Range;
      }

      if (this.listQuery.TableActive == "my") {
        postData.AgentEmployeeId = getUserInfo().employeeid;
        postData.CustomerServiceEmployeeId = getUserInfo().employeeid;
      }
      postData = this.assignSortObj(postData)
      afterService
        .getList(postData)
        .then(res => {
          this.listLoading = false;
          this.tabDatas = res.Items;
          this.total = res.Total;
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    handleDel(row) {
      this.$confirm(`是否确认删除 ${row.Code}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        afterService.del([row.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },
    handleGoBack() {
      this.$router.push({ path: this.returnUrl });
    },
    getDutyDepartments() {
      systemDepartment.getDutyDepartment().then(res => {
        this.dutyDepartment = res.map(t => {
          return { value: t.DepartmentId, label: t.DepartmentName };
        });
      });
    },

    handleClearRegional() {
      this.listQuery.RegionalId = ''
      this.listQuery.areaName = ''
    },
    electedRegionalData(data){
        if(data) {
          this.listQuery.RegionalId=data.Id;
          this.listQuery.areaName=data.ParentName;
        }else{
          this.listQuery.RegionalId='';
          this.listQuery.areaName='';
        }
    },
    closeRegionalDialog() {
      this.dialogRegionalVisible=false
    },
    handleRegionalDialog() {
      if(!this.listQuery.RegionalId){
        this.listQuery.RegionalId=this.RId;
      }
      this.dialogRegionalVisible=true;
    },
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  overflow-y: auto;
  .bg-white {
    .content {
      padding-right: 0;
      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }
      .list {
      }
    }
  }
}

.status-1,
.status-4,
.status-7 {
  background-color: red;
}

.status-2,
.status-5,
.status-8 {
  background-color: blue;
}

.status-3,
.status-6,
.status-9 {
  background-color: orange;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

.status-null {
  color: #606266;
}
</style>
