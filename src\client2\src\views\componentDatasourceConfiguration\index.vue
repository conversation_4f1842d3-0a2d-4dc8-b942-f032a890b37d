<template>
  <div>
    <div class="app-container">
      <div class="bg-white">
        <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns='tabAuthColumns' :isShowAllColumn='isShowAllColumn' :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn='rowBtns.length > 0' :startOfTable='startOfTable' :multable=false>
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='resetSearch'>
              <template slot='ConfigurationKey'>
                <el-input style="width: 100%;" v-model="listQuery.ConfigurationKey" placeholder=""></el-input>
              </template>
            </app-table-form>
          </template>

          <!-- 通过slot自定义列：参数，当前行 -->
          <template slot='DataSourceType' slot-scope="scope">
            <el-tag v-if="scope.row.DataSourceType == 1" class="gender-male">集合</el-tag>
            <el-tag v-if="scope.row.DataSourceType == 2" class="gender-female">API地址</el-tag>
          </template>

          <!-- 表格批量操作区域 -->
          <template slot="btnsArea">
            <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type='1'>
            </app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type='3'>
            </app-table-row-button>

            <app-table-row-button v-if="rowBtnIsExists('btnSetAdmin')" @click="handleSetAdmin(scope.row)" :type='2' :text="getRowBtnText('btnSetAdmin')">
            </app-table-row-button>
          </template>
        </app-table>

        <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
      </div>

      <!-- 操作窗口 -->
      <el-dialog v-el-drag-dialog class="dialog-mini" width="650px" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal='false' :append-to-body='true'>
        <el-form :rules="rules" ref="dataForm" :model="DynamicForm" label-position="right" label-width="140px">
          <el-row>
            <el-col>
              <el-form-item :label="'配置名称'" prop="ConfigurationKey">
                <el-input maxlength="50" v-model="DynamicForm.ConfigurationKey" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'数据源类型'" prop="DataSourceType">
                <el-radio v-model="DynamicForm.DataSourceType" :label=1 @change=" radioChangeHandle">集合</el-radio>
                <el-radio v-model="DynamicForm.DataSourceType" :label=2 @change=" radioChangeHandle">API地址</el-radio>
              </el-form-item>

              <el-form-item v-if="DynamicForm.DataSourceType == 1" :label="'配置数据源'" prop="ConfigurationValues">
                <el-row>
                  <el-col :span="20">
                    <el-input v-model="DynamicForm.ConfigurationValues" placeholder="请输入JSON格式数据源" :disabled="true">{{ SetConfigurationValues }}</el-input>
                  </el-col>
                  <el-col :span="4" style="text-align:right">
                    <el-button @click.prevent="addDomain()">添加</el-button>
                  </el-col>
                </el-row>

                <el-row :gutter="10" style="margin-top:3px;" v-for="(domain) in domainsInfo" :key="domain.key">
                  <el-col :span="10">
                    <el-input v-model="domain.label" placeholder="请输入Label"></el-input>
                  </el-col>
                  <el-col :span="10">
                    <el-input v-model="domain.value" placeholder="请输入Value"></el-input>
                  </el-col>
                  <el-col :span="4" style="text-align:right">
                    <el-button @click.prevent="removeDomain(domain)">删除</el-button>
                  </el-col>
                </el-row>
              </el-form-item>

              <el-form-item v-else :label="'配置数据源'" prop="ConfigurationValues">
                <el-input v-model="DynamicForm.ConfigurationValues" :disabled="editable" placeholder="请输入接口地址 (/api/Check/Sign)"></el-input>
              </el-form-item>
              <el-form-item :label="'备注'" prop="Remark">
                <el-input maxlength="2000" v-model="DynamicForm.Remark" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
          <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import * as componentDatasourceConfiguration from "@/api/componentDatasourceConfiguration";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import { regs } from "@/utils/regs";
export default {
  name: "position",
  components: {},
  directives: {
    // waves,
    elDragDialog
  },
  mixins: [indexPageMixin],
  data() {
    return {
      multipleSelection: [],
      tableSearchItems: [{ prop: "ConfigurationKey", label: "配置名称" }],
      tabColumns: [
        {
          attr: { prop: "ConfigurationKey", label: "配置名称" }
        },
        {
          attr: {
            prop: "ConfigurationValues",
            label: "配置数据源",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "DataSourceType",
            label: "数据源类型",
          },
          slot: true
        },
        // {
        //   attr: {prop: "ConfigurationState",label: "配置状态"}
        // },
        {
          attr: {
            prop: "Remark",
            label: "备注",
            showOverflowTooltip: true
          }
        }
      ],
      tabDatas: [],
      listLoading: false,
      postLoading: false,
      listQuery: {
        // 查询条件
        ConfigurationKey: ""
      },
      total: 0,
      textMap: {
        update: "编辑",
        create: "添加"
      },
      dialogFormVisible: false,
      rules: {
        ConfigurationKey: {
          fieldName: "配置名称",
          rules: [{ required: true }]
        },
        ConfigurationValues: {
          fieldName: "配置数据源",
          rules: [{ required: true }]
        }
      },
      DynamicForm: {
        ConfigurationKey: "",
        ConfigurationValues: "",
        DataSourceType: 1,
        ConfigurationState: "",
        Remark: ""
      },
      domainsInfo: []
    };
  },
  created() {
    this.rules = this.initRules(this.rules);
    this.getList();
  },
  mounted() { },
  computed: {
    SetConfigurationValues: function () {
      this.setValues();
    }
  },
  methods: {
    //实时设置 配置数据源 的值
    setValues() {
      this.DynamicForm.ConfigurationValues =
        this.domainsInfo.length == 0 ? "" : JSON.stringify(this.domainsInfo);
    },
    removeDomain(item) {
      var index = this.domainsInfo.indexOf(item);
      if (index !== -1) {
        this.domainsInfo.splice(index, 1);
      }
      this.setValues();
    },
    addDomain() {
      this.domainsInfo.push({ label: "", value: "" });
      this.DynamicForm.ConfigurationValues = JSON.stringify(this.domainsInfo);
    },

    radioChangeHandle() {
      this.DynamicForm.ConfigurationValues = "";
      this.domainsInfo = [];
    },
    resetTemp() {
      this.DynamicForm = {
        ConfigurationKey: "",
        ConfigurationValues: "",
        DataSourceType: 1,
        ConfigurationState: "",
        Remark: ""
      };
      this.domainsInfo = [];
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleCreate();
          break;
        case "btnEdit":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行编辑",
              type: "error"
            });
            return;
          }
          this.handleUpdate(this.multipleSelection[0]);
          break;
        case "btnDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error"
            });
            return;
          }
          this.handleDelete(this.multipleSelection);
          break;
        case "btnDetail":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行查看",
              type: "error"
            });
            return;
          }
          this.handleUpdate(this.multipleSelection[0], "detail");
          break;
        default:
          break;
      }
    },
    getList() {
      this.listLoading = true;
      componentDatasourceConfiguration
        .getList(this.listQuery)
        .then(response => {
          this.tabDatas = response.Items;
          this.total = response.Total;
          this.listLoading = false;
        });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCreate() {
      // 弹出添加框
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      // 保存提交
      let self = this;
      self.postLoading = true;
      this.$refs["dataForm"].validate(valid => {
        if (!valid) {
          self.postLoading = false;
        }
        if (valid) {
          let formData = JSON.parse(JSON.stringify(self.DynamicForm));

          //手动验证
          if (formData.DataSourceType == 1) {
            var configurationValues = JSON.parse(formData.ConfigurationValues);
            var newConfigurationValues = Object.assign([], configurationValues);

            newConfigurationValues.forEach(element => {
              if (element.label == "" || element.value == "") {
                var index = configurationValues.indexOf(element);
                configurationValues.splice(index, 1);
              }
            });

            //配置数据源中不存在有效数据
            if (configurationValues.length == 0) {
              self.DynamicForm.ConfigurationValues = "";
              self.$refs["dataForm"].validateField("ConfigurationValues");
              self.postLoading = false;
              return false;
            }

            formData.ConfigurationValues = JSON.stringify(configurationValues);
          }

          if (self.dialogStatus == "create") {
            delete formData.ComponentDatasourceConfigurationID;
          }

          let res = null;
          if (self.dialogStatus == "create") {
            res = componentDatasourceConfiguration.add(formData);
          } else if (self.dialogStatus == "update") {
            res = componentDatasourceConfiguration.edit(formData);
          }
          if (res) {
            res
              .then(response => {
                self.postLoading = false;
                self.dialogFormVisible = false;
                self.$notify({
                  title: "成功",
                  message: "创建成功",
                  type: "success",
                  duration: 2000
                });

                this.getList();
              })
              .catch(err => {
                self.postLoading = false;
              });
          }
        }
      });
    },
    // 弹出编辑框
    handleUpdate(row, optType = "update") {
      this.DynamicForm = Object.assign({}, row);
      //类型为 集合 时, 设置动态集合数据源用于生成控件
      if (row.DataSourceType == 1) {
        var domainList = JSON.parse(row.ConfigurationValues);
        this.domainsInfo = Object.assign([], domainList);
      }
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    handleDelete(rows) {
      // 多行删除
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.ComponentDatasourceConfigurationId);
      } else {
        ids.push(rows.ComponentDatasourceConfigurationId);
      }

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        componentDatasourceConfiguration.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}
</style>