<template>
    <div>
        <el-button type="text" @click="() => dialogAccessUsers = true" v-show="!disabled">新增</el-button>
        <span>
            <el-tag
                :key="idx"
                v-for="(tag, idx) in checked"
                :closable='!disabled'
                :disable-transitions="false"
                @close="handleRemove(checked, idx)">
                {{ tag.Text }}
            </el-tag>
        </span>
        <el-dialog width="800px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="`选择${this.opts[this.optType]}`" :visible.sync='dialogAccessUsers' :append-to-body='true'>
            <param-selector :optType='optType' ref="accessUser" v-bind="$attrs" :existsUsers='checked' :visible.sync='dialogAccessUsers' v-show="dialogAccessUsers" @changed='handleChangeUsers'></param-selector>
        </el-dialog>
    </div>
</template>

<script>

import ParamSelector from './paramSelector'
import elDragDialog from '@/directive/el-dragDialog'

export default {
    name: 'param-add',
    components: {
        ParamSelector,
    },
    directives: {
      elDragDialog
    },
    props: {
        optType: {
            type: Number, //1: 关键字；2：影响点位参数；3：关联综合报警参数
            default: 1,
        },
        disabled: {
            type: Boolean,
            default: false
        },
        //已存在的集合
        checkedList: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    computed: {
        optTitle() {
            if(this.optType == 1 || this.optType == 2 || this.optType == 3){
                return `新增${this.opts[this.optType]}`
            }else {
                return ''
            }
        }
    },
    watch: {
        checkedList: {
            handler(val) {
                this.checked = JSON.parse(JSON.stringify(val))
                // this.$nextTick(() => {
                    
                // })
            },
            immediate: true
        },
    },
    created() {
        // this.rules = this.initRules(this.rules)
        // const validateText = (rule, value, callback) => {
        //     if (value.length <= 0) {
        //         let equText = this.opts[this.optType]
        //         callback(new Error(`${equText}不能为空`))
        //     } else {
        //         callback()
        //     }
        // }
        // if(!this.rules['desc'])
        //     this.rules['desc'] = []
        // this.rules.desc.push({validator: validateText, trigger: 'blur'})
    },
    data() {
        return {
            opts: {
                1: '关键字',
                2: '影响点位参数',
                3: '关联综合报警参数'
            },
            dialogAccessUsers: false,
            temp: {
                desc: '',//输入内容
            },
            rules: {},
            checked: [],
        }
    },
    methods: {
        handleChangeUsers(datas) {
            this.dialogAccessUsers = false
            this.$emit('success', datas)
        },
        handleRemove(items, idx) {
            if(items && items.length > 0){
                items.splice(idx, 1)
                this.$emit('success', items)
            }
        },
    }
}
</script>