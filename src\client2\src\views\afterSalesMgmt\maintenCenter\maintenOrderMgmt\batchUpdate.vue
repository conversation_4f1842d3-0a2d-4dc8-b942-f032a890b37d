<template>
    <div>
        <app-dialog
        title="修改售后合同"
        ref="appDialogRef"
        v-bind="$attrs"
        v-on="$listeners"
        :width='600'
        >
        <template slot="body">
            <el-form
                :rules="rules"
                ref="formData"
                :model="formData"
                label-position="right"
                label-width="100px"
            >
                <div class="dialog-wrapper">
                    <el-form-item label-width="110px" label="已选中报修单">
                        {{ IdList.length }}
                    </el-form-item>
                    <el-form-item label-width="110px" label="关联售后合同" prop="AfterContractId">
                        <div class="_regional_detail_wrapper">
                            <div class="btn_wrapper">
                                <el-button :disabled="isHandle" type="text" @click="handleAfterContractDialog">选择</el-button>
                            </div>
                            <div class="regional_text" :title="formData.AfterContractCode">{{ formData.AfterContractCode }}</div>
                            <div class="close_wrapper" v-show="formData.AfterContractCode">
                                <div class="i_wrapper">
                                    <el-button icon="el-icon-close" class="btn" circle @click="electedAfterContractData(null)"></el-button>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                    <div style="padding: 10px 15px; color: #f59a23;">
                        提示：如未选择关联售后合同，将对选中报修单进行置空处理
                    </div>
                </div>
            </el-form>

            <saleContractSelector
                :isShow="dialogAfterContractFormVisible"
                :checkedList="[]" 
                :condition="{}"
                @changed="electedAfterContractData"
                @closed="() => (dialogAfterContractFormVisible = false)"
                :multiple="false"
            ></saleContractSelector>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
        </template>
        </app-dialog>
    </div>
</template>

<script>
import * as mo from "@/api/maintenanceCenter/maintenOrderMgmt"
import saleContractSelector from "@/views/common/saleContractSelector";

export default {
    name: "batch-update-assign",
    components: {
        saleContractSelector
    },
    mixins: [],
    props: {
        IdList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            rules: {
                // AfterContractId: { fieldName: "关联售后合同", rules: [{ required: true, trigger: 'change' }]},
            },
            formData: {
                AfterContractId: '',
                AfterContractCode: '',
            },
            dialogAfterContractFormVisible: false,
        };
    },
    methods: {
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.MaintenanceIdList = this.IdList
                this.disabledBtn = true
                mo.batchUpdateAfterContract(postData).then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false;
                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false;
                })
            });
        },
        handleAfterContractDialog() {
            this.dialogAfterContractFormVisible = true
        },
        electedAfterContractData(datas) {
            if (datas && datas.length > 0) {
                this.formData.AfterContractId = datas[0].Id;
                this.formData.AfterContractCode = datas[0].AfterContractCode;
            }else{
                this.formData.AfterContractId = null;
                this.formData.AfterContractCode = '';
            }
            this.$refs["formData"].validateField("AfterContractId");
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
.dialog-wrapper >>> .el-form-item{
    margin-bottom: 0;
}

.dialog-wrapper{
    height: 150px;
}
</style>