<template>
<div>
  <app-dialog class-name="dialog-wrapper"
    title="调整分类"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :width="500"
  >
    <template slot="body">
      <el-row class="wrapper">
        <el-form :rules="rules" ref="formData" :model="formData" :label-width="labelWidth">
          <el-form-item label="所属分类" prop="ClassifyId">
              <treeselect :normalizer="normalizer" class="treeselect-common" :options="ClassifyList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择所属分类" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"></treeselect>
          </el-form-item>
        </el-form>
      </el-row>
    </template>
    <template slot="footer">
      <!-- 取消 -->
      <el-button @click="handleClose">取消</el-button>
      <!-- 确定 -->
      <el-button @click="handleSubmit" type="primary" :disabled='disabledBtn'>确定</el-button>
    </template>
  </app-dialog>
</div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import * as approvalManagement from "@/api/approvalManagement";
import * as classify from '@/api/classify'
export default {
  name: "batch-set-classify",
  components: {
  },
  props: {
    ids: {
      type: Array,
      default() {
        return []
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.formData = this.$options.data().formData; // 重置提交表单字段为初始值
          this.formData.ids = this.ids
          this.getClassifyList()
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      disabledBtn: false,
      loading: false,
      rules: {
        ClassifyId: { fieldName: "所属分类", rules: [{ required: true, trigger: 'change' }] },
      },
      labelWidth: "100px",
      formData: {
        ids: [],
        ClassifyId: null
      },
      ClassifyList: [],
      normalizer(node) {
          // treeselect定义字段
          return {
              label: node.label.split(",")[0],
              id: node.Id,
              children: node.children
          };
      },
    };
  },
  computed: {
  },
  created() {
    this.rules = this.initRules(this.rules)
  },
  methods: {
    //获取分类下拉框
    getClassifyList() {
      this.loading = true;
        classify.getListByCondition({ BusinessType: 7 }).then(res => {
          this.loading = false;
            var departments = res.map(function (item, index, input) {
            return {
                Id: item.Id,
                label: item.Name,
                ParentId: item.ParentId
            };
            });
            this.ClassifyList = listToTreeSelect(departments);
        }).catch(err => {
          this.loading = false;
        });
    },
    // 弹窗提交
    handleSubmit() {
      let self = this;
      console.log(self.formData)
      self.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(self.formData));
          self.disabledBtn = true
          approvalManagement.ChangeClassify(postData).then(res => {
              self.$notify({
                  title: "提示",
                  message: "保存成功",
                  type: "success",
                  duration: 2000
              });
              self.disabledBtn = false
              self.$refs.appDialogRef.createData();
          }).catch(err => {
              self.disabledBtn = false
          });
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style scoped>
.wrapper {
  padding: 10px 0;
  padding-right: 30px;
  min-height: 100px;
}
</style>
<style>
.dialog-wrapper .el-dialog__body{
  overflow: inherit;
}
</style>