<!--答题详情-->
<template>
<app-dialog title="进度详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="500">
    <template slot="body">
        <div class="pageWarp">
            <page-title :showBackBtn='false'>
                <div slot="def">
                    <tags :items="SubmitStateEnum" v-model="listQuery.SubmitState">
                        <template v-for="t in SubmitStateEnum" :slot="t.value">{{ t.label }}</template>
                    </tags>
                </div>
            </page-title>
            <div class="pageWarp_main">
                <el-table
                    fit
                    :data="ParticipantEmployeeList"
                    style="width: 100%"
                    height="100%" v-loading="loading"
                    :header-cell-style="{'text-align':'left'}"
                    highlight-current-row row-key="Id"
                    ref="mainTable"
                >
                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                    <el-table-column label="参与人" prop="Name">
                        <template slot-scope="scope">
                            <div style="line-height: 40px;" v-viewer>
                                <img class="tabsBox_item_userPhoto" :src="scope.row.AvatarPath||require('../../../assets/images/avatar3.png')" />
                                <span>{{scope.row.Name}}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="提交情况" prop="SubmitState" width="110">
                        <template slot-scope="scope">
                            <span class="item-status" v-if="scope.row.SubmitState"
                                :style="{backgroundColor: getSubmitStateObj(scope.row.SubmitState).bgColor,color: getSubmitStateObj(scope.row.SubmitState).color}"
                            >{{ getSubmitStateObj(scope.row.SubmitState).label }}</span>
                            <template v-else>无</template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </template>
    <template slot="footer">
        <app-button @click="handleClose" text="关闭" type></app-button>
    </template>
</app-dialog>
</template>

<script>
import NoData from "@/views/common/components/noData";
import * as SurveyApi from '@/api/personnelManagement/survey.js'
import { SubmitStateEnum} from "../enum.js";
export default {
    name: "questionnaireMgt-progress-detail",
    components: {
        NoData
    },
    props: {
        node: {
            type: Object,
            required: true,
            default: ()=>{},
        },
    },
    filters: {
    },
    data() {
        return {
            SubmitStateEnum,

            searchTypesData: [
                { label: "全部试卷", value: 0 },
                { label: "必考试卷", value: 2 },
            ],


            listQuery: {
                SubmitState: 1,
            },
            loading: false,
            ParticipantEmployeeList: [],
        };
    },
    computed: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getDetail();// 查询 基本信息
                }
            },
            immediate: true
        },
        "listQuery.SubmitState"(val) {
            this.getDetail();// 查询 基本信息
        },
    },
    created() {
    },
    mounted() {},
    methods: {
        // 查询 基本信息
        getDetail() {
            let self = this;
            self.loading = true
            SurveyApi.ProgressDetails({
                Id: self.node.Id,
                SubmitState: self.listQuery.SubmitState,
            }).then(res => {
                self.ParticipantEmployeeList = res;
                self.loading = false
            }).catch(err => {
                self.loading = false
            });
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        getSubmitStateObj(val){
            return this.SubmitStateEnum.find(
                s => s.value == val
            ) || {};
        },
    }
};
</script>

<style lang="scss" scoped>
.pageWarp{
    height: 400px;
    display: flex;
    flex-direction: column;
    .wrapper{
        padding: 0;
    }
    &_main{
        flex: 1;
        
        .tabsBox_item_userPhoto{
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            float: left;
            margin-right: 10px;
            cursor: pointer;
        }
    }
}
</style>


