export default {

    /**
     * 用户覆盖路由默认对应的文件组件（页面）路径；
     * 假设有两个路径，对应的页面基本一样，所以不希望复制重复的代码，主要用于解决此种场景；
     * 
     * 说明：数据库路由（url）地址，也就是文件结构路径，默认情况下，路径渲染组件也处于该位置；
     */
    override: [{
        //销售管理——》我的客户（和 ”客户管理“ 通用）
        routerPath: '/salesMgmt/myCustom/index',
        file: '/salesMgmt/customMgmt/index'
    },
    {
        //销售管理——》我的客户（和 ”客户管理“ 通用）
        routerPath: '/salesMgmt/myCustom/detail/:id',
        file: '/salesMgmt/customMgmt/detail'
    },
    {
        //销售管理——》我的客户（和 ”客户管理“ 通用）
        routerPath: '/salesMgmt/myCustom/follow/:id',
        file: '/salesMgmt/customMgmt/detail'
    },
    {
        //销售管理——》机会点管理
        routerPath: '/salesMgmt/businessOpportunity/follow',
        file: '/salesMgmt/businessOpportunity/detail'
    }, {
        //销售管理——》投标管理
        routerPath: '/salesMgmt/bidManager/follow',
        file: '/salesMgmt/bidManager/detail'
    }, {
        //人事管理——》绩效管理
        routerPath: '/personnelManagement/achievementMgmt/follow/:id',
        file: '/personnelManagement/achievementMgmt/detail'
    }, {
        //库存查询——》货品信息共享
        routerPath: '/invoicingMgmt/stock/index', //库存查询
        file: '/invoicingMgmt/basic/share/index' //货品信息共享
    }, {
        routerPath: '/workbench/workPlan/create', //工作计划创建
        file: '/workbench/workPlan/planList/create' //工作计划创建
    }, {
        routerPath: '/workbench/workPlan/edit/:id', //工作计划编辑
        file: '/workbench/workPlan/planList/create' //工作计划创建
    }, {
        routerPath: '/workbench/workPlan/reCreate/:id', //工作计划编辑
        file: '/workbench/workPlan/planList/create' //工作计划创建
    }, {
        routerPath: '/workbench/workPlan/detail/:id', //工作计划详情
        file: '/workbench/workPlan/planList/detail' //工作计划创建
    }, {
        routerPath: '/personnelManagement/attendanceMgmt/group/list/add',
        file: '/personnelManagement/attendanceMgmt/group/listDetail' //工作计划创建
    }, {
        routerPath: '/personnelManagement/attendanceMgmt/group/list/edit/:id',
        file: '/personnelManagement/attendanceMgmt/group/listDetail' //工作计划创建
    }, {
        routerPath: '/personnelManagement/attendanceMgmt/group/list/detail/:id',
        file: '/personnelManagement/attendanceMgmt/group/listDetail' //工作计划创建
    }, {
        routerPath: '/afterSalesMgmt/softwareReturnVisit/visitRecord/index',
        file: '/afterSalesMgmt/softwareReturnVisit/deviceParams/index'
    },  {
        routerPath: '/workbench/onlineTraining/detail/:id',
        file: '/workbench/onlineTraining/detail' // 在线培训 考试详情
    },  {
        routerPath: '/workbench/onlineTraining/examination/:id',
        file: '/workbench/onlineTraining/examination' // 在线培训 考试详情
    },  {
        routerPath: '/workbench/onlineTraining/examinationView/:id',
        file: '/workbench/onlineTraining/examinationView' // 在线培训 考试结果页面
    },  {
        routerPath: '/personnelManagement/organizationFeedback/index', //组织建设反馈管理
        file: '/projectDev/productionFeedback/index' //产品反馈管理
    }, {
        //个人工作台——》我的会议
        routerPath: '/workbench/myMeeting/follow/:id',
        file: '/workbench/myMeeting/mettingList/detail'
    }, {
        //个人工作台——》我的会议
        routerPath: '/workbench/myMeeting/detail/:id',
        file: '/workbench/myMeeting/mettingList/detail'
    }, {
        //个人工作台——》我的会议
        routerPath: '/workbench/myWorkbenchNew/index',
        file: '/workbench/myWorkbench/index'
    },
    ],
    /**
     * 扩展路由，某些特殊页面上的相关按钮，需要添加一些路由跳转
     */
    extend: [
        {
            path: '/projectdev/projectmgmt/index',
            pages: [
            // {
            //     pageUrl: '/projectDev/projectMgmt/projectSetting/index',
            //     pageTitle: '项目设置',
            //     moduleCode: 'projectSetting'
            // },
            {
                pageUrl: '/projectDev/projectMgmt/workbench/index',
                pageTitle: '工作台',
                moduleCode: 'workbench'
            },
            ]
        },
        {
            path: '/salesMgmt/customMgmt/index',
            pages: [{
                pageUrl: '/salesMgmt/customMgmt/detail/:id',
                pageTitle: '客户管理详情',
                moduleCode: 'customMgmtDetail',
                // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
            },]
        },
        {
            //需要扩展路由的页面
            path: '/salesMgmt/myCustom/index',
            pages: [{
                pageUrl: '/salesMgmt/myCustom/detail/:id',
                pageTitle: '我的客户详情',
                moduleCode: 'myCustomerDetail',
                // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
            },
            {
                pageUrl: '/salesMgmt/myCustom/follow/:id',
                pageTitle: '我的客户跟进',
                moduleCode: 'myCustomerFollow',
                // authBtnCode: 'btnTrack' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
            },
            ]
        },
        {
            path: '/encyclopedia/index',
            pages: [
                {
                    pageUrl: '/encyclopedia/failureCase/index',
                    pageTitle: '维修故障案例详情',
                    moduleCode: 'failureCase'
                },
                {
                    pageUrl: '/encyclopedia/implementationGuide/index',
                    pageTitle: '软件实施指南详情',
                    moduleCode: 'implementationGuide'
                },
                {
                    pageUrl: '/encyclopedia/materialDatabase/index',
                    pageTitle: '材料资料库',
                    moduleCode: 'materialDatabase'
                },
                {
                    pageUrl: '/encyclopedia/knowledge/index',
                    pageTitle: '知识库',
                    moduleCode: 'knowledgeIndex',
                },
                {
                    pageUrl: '/encyclopedia/templateRepository/index',
                    pageTitle: '模板资源库',
                    moduleCode: 'templateIndex',
                },
                
                {
                    pageUrl: '/encyclopedia/knowledge/detail/:id',
                    pageTitle: '知识库详情',
                    moduleCode: 'knowledgeView',
                },
                {
                    pageUrl: '/encyclopedia/knowledge/create',
                    pageTitle: '我要投稿',
                    moduleCode: 'knowledgeCreate',
                },
                {
                    pageUrl: '/encyclopedia/implementationProblem/index',
                    pageTitle: '软件实施常见问题详情',
                    moduleCode: 'implementationProblem'
                }
            ]
        },
        {
            path: '/informationCenter/newsTrends/index',
            pages: [{
                pageUrl: '/informationCenter/newsTrends/detail/:id',
                pageTitle: '新闻动态详情',
                moduleCode: 'xxxxx'
            }]
        },
        {
            path: '/informationCenter/systemNotices/index',
            pages: [{
                pageUrl: '/informationCenter/systemNotices/detail/:id',
                pageTitle: '公告通知详情',
                moduleCode: 'xxxxx'
            }]
        },
        {
            path: '/informationCenter/productCenter/successfulCases/index',
            pages: [{
                pageUrl: '/informationCenter/productCenter/successfulCases/detail/:id',
                pageTitle: '成功案例详情',
                moduleCode: 'xxxxx'
            }]
        },
        {
            path: '/informationCenter/productCenter/productPresentations/index',
            pages: [{
                pageUrl: '/informationCenter/productCenter/productPresentations/detail/:id',
                pageTitle: '产品介绍详情',
                moduleCode: 'xxxxx'
            }]
        },
        {
            path: '/implementation/engineering/index',
            pages: [{
                pageUrl: '/implementation/engineering/workbench/index',
                pageTitle: '实施工作台',
                moduleCode: 'xxxxx'
            }]
        },
        {
            path: '/salesMgmt/businessOpportunity/index',
            pages: [{
                pageUrl: '/salesMgmt/businessOpportunity/detail',
                pageTitle: '商机-项目管理',
                moduleCode: 'businessOpportunityDetail',
            },
            {
                pageUrl: '/salesMgmt/businessOpportunity/follow',
                pageTitle: '商机-项目跟进',
                moduleCode: 'businessOpportunityFollow',
            },
            ]
        }, {
            path: '/salesMgmt/bidManager/index',
            pages: [{
                pageUrl: '/salesMgmt/bidManager/detail',
                pageTitle: '投标详情',
                moduleCode: 'bidManagerDetail',
            },
            {
                pageUrl: '/salesMgmt/bidManager/follow',
                pageTitle: '投标跟进',
                moduleCode: 'bidManagerFollow',
            },
            ]
        }, {
            path: '/personnelManagement/achievementMgmt/index',
            pages: [{
                pageUrl: '/personnelManagement/achievementMgmt/detail/:id',
                pageTitle: '绩效管理详情',
                moduleCode: 'xxxxxxxxxxxx',
            },
            {
                pageUrl: '/personnelManagement/achievementMgmt/follow/:id',
                pageTitle: '绩效管理跟进',
                moduleCode: 'xxxxxxxxxxxx',
            },
            ]
        }, {
            path: '/personnelManagement/attendanceMgmt/group/index',
            pages: [{
                pageUrl: '/personnelManagement/attendanceMgmt/group/list/add',
                pageTitle: '考勤组管理列表',
                moduleCode: 'xxxxxxxxxxxx',
            }, {
                pageUrl: '/personnelManagement/attendanceMgmt/group/list/edit/:id',
                pageTitle: '考勤组管理列表',
                moduleCode: 'xxxxxxxxxxxx',
            }, {
                pageUrl: '/personnelManagement/attendanceMgmt/group/list/detail/:id',
                pageTitle: '考勤组管理列表',
                moduleCode: 'xxxxxxxxxxxx',
            }, {
                pageUrl: '/personnelManagement/attendanceMgmt/group/list/:id',
                pageTitle: '考勤组管理列表',
                moduleCode: 'xxxxxxxxxxxx',
            }
            ]
        }, {
            path: '/workbench/workPlan/index',
            pages: [{
                pageUrl: '/workbench/workPlan/create',
                pageTitle: '创建工作计划',
                moduleCode: 'xxxxxxxxxxxx',
            },
            {
                pageUrl: '/workbench/workPlan/reCreate/:id',
                pageTitle: '引用创建工作计划',
                moduleCode: 'xxxxxxxxxxxx',
            },
            {
                pageUrl: '/workbench/workPlan/edit/:id',
                pageTitle: '编辑工作计划',
                moduleCode: 'xxxxxxxxxxxx',
            }, {
                pageUrl: '/workbench/workPlan/detail/:id',
                pageTitle: '工作计划详情',
                moduleCode: 'xxxxxxxxxxxx',
            },
            ]
        }, {
            path: '/workbench/workPlanDashboard/index',
            pages: [{
                pageUrl: '/workbench/workPlanDashboard/detail/:id',
                pageTitle: '数据看板详情',
                moduleCode: 'xxxxxxxxxxxx',
            },{
                pageUrl: '/workbench/workPlanDashboard/dailyDetail/:id',
                pageTitle: '数据看板详情2',
                moduleCode: 'xxxxxxxxxxxxxxx',
            },
            ]
        },
        {
            path: '/workbench/onlineTraining/index',
            pages: [
                {
                    pageUrl: '/workbench/onlineTraining/detail/:id',
                    pageTitle: '在线培训详情',
                    moduleCode: 'onlineTrainingDetail',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
                {
                    pageUrl: '/workbench/onlineTraining/examination/:id',
                    pageTitle: '在线考试',
                    moduleCode: 'onlineTrainingExamination',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
                {
                    pageUrl: '/workbench/onlineTraining/examinationView/:id',
                    pageTitle: '考试结束',
                    moduleCode: 'onlineTrainingExaminationView',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
            ]
        },
        {
            path: '/knowledge/knowledgeManagement/index',
            pages: [
                {
                    pageUrl: '/knowledge/knowledgeManagement/detail/:id',
                    pageTitle: '文章详情',
                    moduleCode: 'knowledgeView',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
                {
                    pageUrl: '/knowledge/knowledgeManagement/create',
                    pageTitle: '我要投稿',
                    moduleCode: 'knowledgeCreate',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
            ]
        },
        {
            path: '/salesMgmt/competitor/index',
            pages: [
                {
                    pageUrl: '/salesMgmt/competitor/follow/:id', // 竞争对手  跟进页面
                    pageTitle: '跟进竞争对手',
                    // moduleCode: 'knowledgeView',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
                {
                    pageUrl: '/salesMgmt/competitor/detail/:id', // 竞争对手  详情页面
                    pageTitle: '竞争对手详情',
                    // moduleCode: 'knowledgeView',
                    // authBtnCode: 'btnDetail' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
                },
            ]
        },
        {
            path: '/workbench/myMeeting/index',
            pages: [
            {
                pageUrl: '/workbench/myMeeting/follow/:id',
                pageTitle: '跟进我的会议',
                moduleCode: 'xxxxxxxxxxxx',
            }, {
                pageUrl: '/workbench/myMeeting/detail/:id',
                pageTitle: '我的会议详情',
                moduleCode: 'xxxxxxxxxxxx',
            },
            ]
        },
        {
            path: '/workbench/mainLineMgmt/index',
            pages: [{
                pageUrl: '/workbench/mainLineMgmt/follow/:id',
                pageTitle: '超级干线跟进',
                moduleCode: 'mainLineMgmtFollow',
                // authBtnCode: 'btnFollow' //扩展路由需要判断是否有对应按钮对应的权限（有这个按钮权限，才能有这个路由权限）
            },]
        },
        {
            //需要扩展路由的页面
            path: '/salesMgmt/clientManagement/corporateClient/index',
            pages: [{
                pageUrl: '/salesMgmt/clientManagement/corporateClient/detail/:id',
                pageTitle: '企业客户详情',
                moduleCode: 'xxxxxxxxxxxx',
                authBtnCode: 'btnDetail'
            },
            ]
        },
        {
            //需要扩展路由的页面
            path: '/workbench/meet/my/index',
            pages: [{
                pageUrl: '/workbench/meet/my/detail/:id',
                pageTitle: '会议管理',
                moduleCode: 'xxxxxxxxxxxx',
                // authBtnCode: 'btnDetail'// 不需要和权限挂钩
            },
            ]
        },
        {
            //需要扩展路由的页面
            path: '/workbench/organizationalGoal/index',
            pages: [{
                pageUrl: '/workbench/organizationalGoal/detail/:id',
                pageTitle: '目标管理',
                moduleCode: 'xxxxxxxxxxxx',
            },
            ]
        },
        
    ]
}
