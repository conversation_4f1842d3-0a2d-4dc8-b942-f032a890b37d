<template>
    <!-- 我要评论 -->
    <app-dialog title="我要评论" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='520' :maxHeight="250" >
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData"
            label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                <el-form-item label="评论内容" prop="Content">
                    <el-input maxlength="500" type="textarea" :rows="5" v-model="formData.Content" placeholder="输入评论内容..."></el-input>
                </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="createData" text="提交" :loading="buttonLoading"></app-button>
        </template>
    </app-dialog>
</template>
<script>
import * as KnowledgeA<PERSON> from '@/api/knowledge/Knowledge'
export default {
    name: 'add-comment',
    props: {
        parentId: {
            type: String,
            require: true,
        },
    },
    data() {
        return {
            labelWidth: "80px",
            buttonLoading: false,
            formData: {
                Content: '',
            },
            rules: {
                Content: {fieldName: "评论内容",rules: [{ required: true }]},
            },
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    methods: {
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        //保存
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                this.buttonLoading = true;
                KnowledgeApi.addComment({
                    CurrentBusinessId: this.parentId,
                    CommentContent: this.formData.Content,
                }).then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                    this.buttonLoading = false;
                });
            });
        },
    }
}
</script>