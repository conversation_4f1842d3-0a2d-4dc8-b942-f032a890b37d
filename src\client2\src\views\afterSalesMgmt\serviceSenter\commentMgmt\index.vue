<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="员工档案" :subTitle="['企业员工的个人档案管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <v-tree @changeNode='changeTreeNode' :isAll='true' :isSubset='false'></v-tree>
                    <!-- <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>

                    <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                        </span>
                    </el-tree> -->
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <!-- <page-title :title="departmentInfo"></page-title> -->
                <div class="tagBox">
                    <tags :items='types' v-model="listQuery.LaborContractState">
                        <template v-for="t in types" :slot="t.value">
                            {{ t.label }}
                        </template>
                    </tags>
                </div>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" @sortChagned="handleSortChange" :layoutMode='layoutMode'>

                        <template slot="ReportTime" slot-scope="scope">{{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}</template>
                        <template slot="HandlerEmployeeList" slot-scope="scope">{{ scope.row.HandlerEmployeeList.map(s => s.Name).join(',') }}</template>
                        <template slot="CollectsScore" slot-scope="scope">
                            <el-rate
                                v-model="scope.row.CollectsScore"
                                disabled
                                text-color="#ff9900">
                            </el-rate>
                        </template>
                        <template slot="WorkingState" slot-scope="scope">{{ scope.row.WorkingState}}</template>

                        <!-- <template slot="Status" slot-scope="scope">
                            <el-tag type="success" v-if="scope.row.Status == 1">{{ scope.row.Status | statusFilter}}</el-tag>
                            <el-tag type="danger" v-if="scope.row.Status == 2">{{ scope.row.Status | statusFilter}}</el-tag>
                        </template> -->
                        <template slot="LaborContractState" slot-scope="scope">
                        </template>


                        

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入报修单号/现场人员/报修人姓名/值班电话"
                                        @clear='getList'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>


                                <template slot="Range">
                                    <el-date-picker
                                        v-model="listQuery.Range"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder
                                        end-placeholder
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;"
                                    ></el-date-picker>
                                </template>

                                <template slot="AttitudeScore">
                                    <div class="month-range-wrapper">
                                        <div class="start-month">
                                            <el-select style="width: 100%;" clearable v-model="listQuery.AttitudeScore" placeholder="">
                                                <el-option
                                                    v-for="item in scores"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                >
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
<!-- 
                                    <el-date-picker
                                        v-model="listQuery.BirthdayRange"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder
                                        end-placeholder
                                        format="MM"
                                        value-format="MM"
                                        style="width: 100%;"
                                    ></el-date-picker> -->
                                </template>

                                <!-- <template slot="Name">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                                </template> -->

                            </app-table-form>
                        </template>

                        <!-- 表格批量操作区域 -->
                        <!-- <template slot="btnsArea"> -->
                        <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                        <!-- </template> -->

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleReview(scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 查看/修改 -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id"></create-page>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as commentMgmt from "@/api/maintenanceCenter/commentMgmt";
import createPage from "./create";
import { vars } from '../../../salesMgmt/common/vars'
import vTree from '../../../afterSalesMgmt/businessMap/common/tree'

export default {
    name: "comment-mgmt",
    mixins: [indexPageMixin],
    components: {
        createPage,
        vTree,
    },
    props: {},
    filters: {

        // statusFilter(status) {
        //     const statusObj = statusEnum.find((s) => s.value == status);
        //     if (statusObj) {
        //         return statusObj.label;
        //     }
        //     return "";
        // },
        sexFilter(value) {
            if (value == 2) {
                return "女";
            }
            if (value == 1) {
                return "男";
            }
        },
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
    },
    watch: {
        "listQuery.LaborContractState"() {
            this.getList();
        },

    },
    created() {
    },
    data() {
        return {
            // departmentListQuery: {
            //     DepartmentName: "",
            // },

            scores: vars.customeMgmt.scores,
            layoutMode: 'simple',
            epKeys: [],
            types: [
                { value: 0, label: "全部" },
                { value: 1, label: "近一周" },
                { value: 2, label: "近一个月" },
                { value: 3, label: "近三个月" },
            ],
            // departmentInfo: "",
            // selectDepartmentName: "",

            // statusEnum: statusEnum,

            // filterText: "",

            // treeLoading: false,
            // treeDatas: [],
            // defaultProps: {
            //     //树默认结构
            //     children: "children",
            //     label: "DepartmentName",
            // },
            tableSearchItems: [{
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },{
                    prop: "AttitudeScore",
                    label: "服务评价"
                },{
                    prop: "Range",
                    label: "报修时间范围"
                }
            ],



            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "Code",
                        label: "报修单号",
                    },
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "报修地区",
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "ReportTime",
                        label: "报修时间",
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "HandlerEmployeeList",
                        label: "现场人员",
                        showOverflowTooltip: true,
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "CollectsScore",
                        label: "服务评价",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "ReportEmployee",
                        label: "报修人姓名",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "ReporterNumber",
                        label: "值班电话",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "PhoneNumber",
                        label: "手机号",
                    },
                },
            ],
            listQuery: {
                RegionalId: "",
                Keywords: '',
                Range: [],
                AttitudeScore: null,
                LaborContractState: 0
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,
        };
    },
    methods: {
        changeTreeNode(d) {
            if (d.Id == -1) {
                this.listQuery.RegionalId = null;
            } else {
                this.listQuery.RegionalId = d.Id;
            }
            this.listQuery.PageIndex = 1
            this.getList();
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },

        //获取成员列表
        getList() {

            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);

            postData.StartTime = null
            postData.EndTime = null

            if(postData.Range && postData.Range.length == 2) {
                postData.StartTime = postData.Range[0]
                postData.EndTime = postData.Range[1]
            }

            delete postData.Range

            commentMgmt.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        handleTableDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id)
            } else {
                ids.push(rows.Id)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                commentMgmt.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        //弹出详情框
        handleReview(row, optType = "detail") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        onResetSearch() {
            this.listQuery.LaborContractState = 0
            this.listQuery.Keywords = ''
            this.listQuery.Range = []
            this.listQuery.AttitudeScore = null
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

    },
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: 100%;
    // display: flex;
    // flex-direction: column;

    // .elInput {
    //     width: 230px;
    //     margin-left: 10px;
    // }

    // .elTree {
    //     flex: 1;
    //     overflow: auto;
    //     margin-top: 10px;
    //     padding-bottom: 10px;
    // }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}

.month-range-wrapper{
    display: flex;
    .start-month, .end-month{
        flex: 1
    }
    .month-separator{
        padding: 0 6px;
    }
}
</style>
