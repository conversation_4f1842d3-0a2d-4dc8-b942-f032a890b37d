<template>
    <!-- 审核 -->
    <app-dialog title="审核" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='320' :maxHeight="200" >
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData"
            label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                <el-form-item label="请选择" prop="KnowledgeApprovalStatus">
                    <el-radio-group v-model="formData.KnowledgeApprovalStatus">
                        <el-radio :label="3">审核不通过</el-radio>
                        <el-radio :label="2">审核通过</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="createData" text="提交" :loading="buttonLoading"></app-button>
        </template>
    </app-dialog>
</template>
<script>
import * as KnowledgeApi from '@/api/knowledge/Knowledge'
export default {
    name: 'knowledge-management-examine',
    props: {
        ids: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            labelWidth: "70px",
            buttonLoading: false,
            formData: {
                KnowledgeApprovalStatus: null,
            },
            rules: {
                KnowledgeApprovalStatus: {fieldName: "审核结果",rules: [{ required: true }]},
            },
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    methods: {
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        //保存
        createData() {
            let self = this,validate = this.$refs.formData.validate();
            self.$refs.formData.validate(valid => {
                if (valid) {
                    // this.buttonLoading = true;
                    KnowledgeApi.approvalList({
                        IdList: self.ids,
                        KnowledgeApprovalStatus: self.formData.KnowledgeApprovalStatus
                    }).then(res => {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData();
                        this.buttonLoading = false;
                    });
                } else {
                    return false;
                }
            });
        },
    }
}
</script>