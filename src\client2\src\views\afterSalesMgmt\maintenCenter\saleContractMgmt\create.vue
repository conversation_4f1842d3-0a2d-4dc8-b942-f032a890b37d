<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='1200'>
            <template slot="body">
                <el-form
                    :rules="rules"
                    ref="formData"
                    :model="formData"
                    label-position="right"
                    label-width="100px"
                    >
                    <div
                        class="wrapper bxdcl"
                        v-loading="loading"
                        style="padding:0 10px;"
                    > 
                        <div class="cl" style="position:relative;">
                            <div class="fl divLeft">
                                <el-card class="box-card">
                                    <div slot="header" class="clearfix">
                                    <span>报修信息</span>
                                    </div>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="合同名称" prop="AfterContractName">
                                                <el-input
                                                    style="width: 100%;"
                                                    :disabled="!editable"
                                                    maxlength="100"
                                                    v-model="formData.AfterContractName"
                                                    :title="formData.AfterContractName"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="合同编号" prop="AfterContractCode">
                                                <el-input
                                                    style="width: 100%;"
                                                    :disabled="!editable"
                                                    maxlength="50"
                                                    v-model="formData.AfterContractCode"
                                                    :title="formData.AfterContractCode"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="服务地区" prop="RegionalId">
                                                <div class="_regional_detail_wrapper">
                                                    <div class="btn_wrapper">
                                                        <el-button :disabled="!editable" type="text" @click="handleDialog">选择</el-button>
                                                    </div>
                                                    <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                                    <div class="close_wrapper" v-show="formData.RegionalName && !(!editable) && dialogStatus != 'update'">
                                                        <div class="i_wrapper">
                                                            <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="合同金额" prop="AfterContractAmount">
                                                <el-input-number :disabled="!editable" v-model="formData.AfterContractAmount" :precision="0" :step="1" :min="0" :max="999999999"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="合同有效期" prop="ContractLifeRange">
                                                <el-date-picker
                                                    :disabled="!editable"
                                                    v-model="formData.ContractLifeRange"
                                                    type="daterange"
                                                    align="right"
                                                    unlink-panels
                                                    range-separator="-"
                                                    start-placeholder
                                                    end-placeholder
                                                    format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd"
                                                    style="width: 100%;"
                                                ></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="税率系数" prop="TaxRate">
                                                <el-input-number :disabled="!editable" v-model="formData.TaxRate" :precision="2" :step="0.01" :min="0" :max="99.99"></el-input-number>
                                                <el-popover
                                                  placement="top"
                                                  trigger="hover">
                                                  <div>例：13%税率则输入1.13</div>
                                                  <i slot="reference" class="el-icon-question" style="margin-left: 10px; font-size: 16px; color: #a7a2a2;"></i>
                                                </el-popover>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="税后金额">
                                                <span>{{ afterTaxAmount }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <div style="font-weight: bold; margin: 10px 0; color: #666;">
                                        <div class="clearfix">
                                            <span>服务内容</span>
                                        </div>
                                    </div>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="服务费" prop="ServiceCharge">
                                                <el-input
                                                    :disabled="!editable"
                                                    maxlength="30"
                                                    v-model="formData.ServiceCharge"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="服务单" prop="AfterServiceTicket">
                                                <el-input
                                                    :disabled="!editable"
                                                    maxlength="30"
                                                    v-model="formData.AfterServiceTicket"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="备注" prop="Remark">
                                                <el-input
                                                    :disabled='!editable'
                                                    maxlength="500"
                                                    type="textarea"
                                                    :rows="3"
                                                    v-model="formData.Remark"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-card>
                            </div>
                            <div class="fr cRight">
                                <el-card class="box-card">
                                <div slot="header" class="clearfix">
                                    <span>相关附件</span>
                                </div>
                                <div class="upDiv">
                                    <app-uploader :readonly='!editable' accept='all' :fileType='3' :max='10000' :value='formData.AttachmentList' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024' @change='handleFilesUpChange'></app-uploader>
                                </div>
                                </el-card>
                            </div>
                        </div>

                    </div>
            </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button v-if="editable" @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>

        <!-- 选择地区 -->
        <v-area-choose
          @closeDialog="closeDialog"
          @electedRegionalData="electedRegionalData"
          :dialogFormVisible="dialogRegionalDialogVisible"
          :checkedList="formData.RegionalId ? [formData.RegionalId] : []"
          :disabledFn="disabledFn"
        ></v-area-choose>
    </div>
</template>

<script>
import * as saleContractMgmt from "@/api/maintenanceCenter/saleContractMgmt"
import vAreaChoose from "../../businessMap/common/areaChoose"
import { Decimal } from "decimal.js"
import { vars } from './vars'
import dayjs from 'dayjs'

export default {
    name: "sale-contract-mgmt-create",
    directives: {},
    components: {
        vAreaChoose,
    },
    mixins: [],
    props: {
        id: {
            type: String,
            default: ""
        },
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: ''
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val && this.id && this.dialogStatus != 'create') {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    computed: {
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '创建售后合同'
            }else if(this.dialogStatus == 'update') {
                return '编辑售后合同'
            }else if(this.dialogStatus == 'detail') {
                return '售后合同详情'
            }
        },
        editable() {
            return this.dialogStatus != 'detail'
        },
        afterTaxAmount() {
            if(this.formData) {
              if(this.formData.TaxRate == 0) {
                return this.formData.AfterContractAmount
              }
              let temp1 = new Decimal(this.formData.AfterContractAmount)
              let temp2 = new Decimal(this.formData.TaxRate)
              //向下保留两位小数
              return temp1.div(temp2).toFixed(2, Decimal.ROUND_DOWN)
            }
            return '-'
        },
    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            formData: {
                AfterContractName: '',//合同名称
                AfterContractCode: '',//合同编号
                RegionalId: null,
                RegionalName: '',
                AfterContractAmount: 0,//合同金额
                ContractLifeRange: [],//合同有效期
                ContractLifeStartTime: null,
                ContractLifeEndTime: null,
                TaxRate: 0,//税率系数
                AfterTaxAmount: 0, //税后金额
                ServiceCharge: '', //服务费
                AfterServiceTicket: '',//服务单
                Remark: '',//备注
                AttachmentList: [],
            },
            dialogRegionalDialogVisible: false,
            rules: {
                AfterContractName:{ fieldName: "合同名称", rules: [{ required: true, trigger: 'change' }]},
                AfterContractCode:{ fieldName: "合同编号", rules: [{ required: true, trigger: 'change' }]},
                AfterContractAmount:{ fieldName: "合同金额", rules: [{ required: true, trigger: 'change' }]},
                RegionalId:{ fieldName: "服务地区", rules: [{ required: true, trigger: 'change' }]},
            },
        };
    },
    methods: {
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        disabledFn(data, nodeType) {
            //禁选一级节点
            if(data.level <= 1) {
                return true
            }
            return false
        },
        closeDialog() {
          this.dialogRegionalDialogVisible = false;
        },
        handleDialog(){
            this.dialogRegionalDialogVisible = true;
        },
        electedRegionalData(data){
          if(data){
              this.formData.RegionalId = data.Id;
              this.formData.RegionalName = data.ParentName;
          }else{
              this.formData.RegionalId = '';
              this.formData.RegionalName = '';
          }
          this.$refs["formData"].validateField("RegionalId");
        },
        getDetail() {
            this.loading = true
            saleContractMgmt.detail({ id: this.id }).then(res => {
              
                this.loading = false
                this.formData = Object.assign({}, res);

                if(res.ContractLifeStartTime && res.ContractLifeEndTime) {
                  let temp = [dayjs(res.ContractLifeStartTime).format('YYYY-MM-DD'), dayjs(res.ContractLifeEndTime).format('YYYY-MM-DD')]
                  this.$set(this.formData, 'ContractLifeRange', temp)
                }
            }).catch(err => {
              
                this.loading = false
            });
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    if(postData.ContractLifeRange && postData.ContractLifeRange.length == 2) {
                        postData.ContractLifeStartTime = postData.ContractLifeRange[0]
                        postData.ContractLifeEndTime = postData.ContractLifeRange[1]
                    }
                    delete postData.ContractLifeRange

                    postData.AfterTaxAmount = this.afterTaxAmount

                    let result = null
                    this.disabledBtn = true
                    if (this.dialogStatus == 'create') {
                        result = saleContractMgmt.add(postData)
                    } else if (this.dialogStatus == 'update') {
                        result = saleContractMgmt.edit(postData)
                    }
                    result.then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$emit('saveSuccess');
                    }).catch(err => {
                        this.disabledBtn = false
                    })
                }
            })
        },
        gethandlingResultStatusObj(val) {
            let obj = vars.handlingResultStatusEnum.find(s => s.value == val) || {}
            return obj
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="css" scoped>
.upDiv >>> .el-icon-circle-close{
  color:white;
}
.elUpload >>> .el-upload-list{
  display:none;
}
.divLeft >>> .el-card__body{
  padding-bottom:0;
}
.p10 >>> .el-card__body{
    padding:10px!important;
}
.tMoney >>> .el-input {
  width: 100% !important;
}
.elNumber >>> .el-input {
  width: 100% !important;
}
.btn-wrapper >>> .el-button {
  padding-top: 0;
  padding-bottom: 0;
}

.cus-textarea-wrapper >>> .el-form-item__content,
.case-wrapper >>> .el-form-item__content,
.list-wrapper2 >>> .el-form-item__content {
  margin-left: 0 !important;
}

.right-wrapper >>> .el-tabs__header {
  margin-bottom: 0;
}

.wrapper >>> .el-card__body{
  padding: 10px;
}

.list-wrapper2>>> .list-wrapper{
  padding-right: 0;
}
</style>

<style lang="scss" scoped>
.upDiv{
  height:100%;
  ul{
    height: calc(100% - 86px);
    width:calc(100% - 40px);
    position: absolute;
    overflow-y: auto;
    li{
      width:98%;
      height:450px;
      position: relative;
      margin:5px;
      overflow: hidden;
      background:#282828;
      >i{
        display:none;
        z-index: 10;
        color:#ccc;
      }
      .elImg{
        width:100%;
        height:100%;
        // height:auto;
        // position:absolute;
        // top:50%;
        // left:50%;
        // transform:translate(-50%,-50%);
      }
    }
    li:hover{
      >i{
        display:block;
        position: absolute;
        right:0;
        top:0;
      }
    }
  }
}
.elUpload{
  float: right;
  padding-right:10px;
}
.divLeft{
  width:calc(100% - 360px);
}

.cRight{
  position: absolute;
  right: 0;
  padding-bottom: 10px;
  width:340px;
  height:100%;
  .el-card{
    height:100%;
    overflow-y:auto;
  }
}
.boxCard{
    margin:10px;
}
.div-popover {
  padding: 6px 0;
  position: absolute;
  background: #fff;
  min-width: 250px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  z-index: 2000;
  color: #606266;
  line-height: 1.4;
  text-align: justify;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  word-break: break-all;
  margin-top: 6px;
  > div {
    padding: 0 20px;
    cursor: pointer;
    height: 34px;
    line-height: 34px;
  }
  > div:hover {
    background: #f5f7fa;
  }
}
.div-popover:before {
  box-sizing: content-box;
  width: 0px;
  height: 0px;
  position: absolute;
  top: -16px;
  left: 41px;
  padding: 0;
  border-bottom: 8px solid #ffffff;
  border-top: 8px solid transparent;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  display: block;
  content: "";
  z-index: 12;
}
.div-popover:after {
  box-sizing: content-box;
  width: 0px;
  height: 0px;
  position: absolute;
  top: -18px;
  left: 40px;
  padding: 0;
  border-bottom: 9px solid #ebeef5;
  border-top: 9px solid transparent;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  display: block;
  content: "";
  z-index: 10;
}

.elCol {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
.elFormUl {
  li {
    width: 96%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > div:nth-child(1) {
      display: flex;
      width: 690px;
      align-items: center;
      span:nth-child(2) {
        display: inline-block;
        margin-left: 6px;
      }
    }
  }
}
.nSpan {
  display: inline-block;
  border: 1px solid #f59a23;
  color: #f59a23;
  padding: 0 6px;
  border-radius: 6px;
  height: 18px;
  line-height: 18px;
}
.el-icon-more {
  position: absolute;
  right: 40px;
  top: 6px;
  font-size: 18px;
  transform: rotate(90deg);
  z-index: 100;
  color: #409eff;
  background: white;
  cursor: pointer;
}
.el-icon-success {
  position: absolute;
  right: 6px;
  top: 4px;
  font-size: 20px;
  z-index: 100;
  color: #04d919;
  cursor: pointer;
}
.elP {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  position: absolute;
  top: 18px;
  left: 82px;
}
// .tMoney {
//   padding: 10px;
//   border-bottom: 1px solid #dcdfe6;
//   margin-bottom: 10px;
// }
.box-card {
  margin-bottom: 10px;
  .el-icon-delete {
    color: red;
    float: right;
    margin: 8px;
    font-size: 18px;
  }
}
// .wrapper {
//   border-bottom: 1px solid #dcdfe6;
//   margin-bottom: 10px;
// }
.poEl {
  position: relative;
  > i:first-child {
    position: absolute;
    left: 4px;
    top: 4px;
    color: red;
  }
}
.required {
  color: red;
}

// .list-wrapper2{
//     box-sizing: border-box;
//     border-right: 1px solid #DCDFE6;
// }

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
.right-wrapper {
  flex: 1;
  box-sizing: border-box;
  margin-left: 10px;
  .tab-content-wrapper {
    border-left: 1px solid #e4e7ed;
  }
}
.title {
  font-weight: bold;
  padding-bottom: 4px;
  font-size: 14px;
}

.section {
  padding: 10px;
  .col-wrapper {
    margin-bottom: 2px;
  }
  .field-title-right {
    display: inline-block;
    width: 100px;
    text-align: right;
  }
}

// .section-first {
//   border-bottom: 1px solid #dcdfe6;
// }

.status-1 {
  background-color: red;
}

.status-2 {
  background-color: #409eff;
}

.status-3 {
  background-color: #00b050;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>

