#app {
	width:100%;
  // 主体区域
  .main-page-height {
    height: 100%;
    // min-height: 100%;
    // height: calc(100vh - 65px);//改回原来解这里
    // height: calc(100vh - 117px + 52px); //52px 去掉了原来的 logo 行
    height: 100%;
    
    // margin-left: 240px;
    position: relative;
    flex: 1;
  }

  .cus-el-main {
    transition: margin-left .28s;
  }
  
  // 侧边栏
  .sidebar-container {
    transition: width 0.28s;
    width: $menu-width !important;
    height: 100%;
    position: absolute;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    // overflow: hidden;
    background-color: rgb(48, 65, 86);
    background: linear-gradient(0deg, #55448C 0%, #213A68 100%);
    //reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }
    .scrollbar-wrapper {
      overflow-x: hidden!important;
      margin-bottom: 0!important;
      .el-scrollbar__view {
        height: 100%;
      }

      .menu{
        background: $subMenuBg;
      }
    }
    .is-horizontal {
      display: none;
    }
    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }
    .svg-icon {
      // margin-right: 16px;
      // width: 14px;
      // display: inline-block;
      // height: 100%;

    }
    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background: transparent;
      .router-link-active li{
        // font-weight: bold;
        // color: #fff!important;

        // 菜单调整
        .menu-inner-wrapper{
          position: absolute;
          width: 140px;
          height: 36px;
          background: rgba($color: #B7D7FF, $alpha: .2)!important;
          border-radius: 5px;
          // width: 160px!important;
          margin: 0 10px;
        }

      }
    }
  }
  .openSidebar{
    .sidebar-container{
      width: $menu-width !important;
    }
    .cus-el-main {
      margin-left: $menu-width;
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: 64px !important;
    }
    .cus-el-main {
      margin-left: 64px;
    }
    .submenu-title-noDropdown {
      padding-left: 10px !important;
      position: relative;
      .el-tooltip {
        padding: 0!important;
        text-align: center;
      }
    }
    .el-submenu {
      overflow: hidden;
      &>.el-submenu__title {
        padding: 0 10px !important;
        text-align: center;
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          padding: 0!important;
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }
  .sidebar-container .nest-menu .el-submenu>.el-submenu__title,
  .sidebar-container .el-submenu .el-menu-item {
    min-width: $menu-width !important;
    background-color: $subMenuBg !important;
    &:hover {
      background-color: $menuHover !important;
    }
  }
  .sidebar-container .el-menu-item, .sidebar-container .el-submenu__title {
    height: 50px;
    line-height: 50px;
    color: rgba(255, 255, 255, 1)!important;
    
    background: $menuHover !important;
    &:hover {
      background-color: $menuHover !important;
    }
  }
  .el-menu--collapse .el-menu .el-submenu {
    min-width: $menu-width !important;
  }

  //菜单调整
  .sidebar-container .el-menu-item{
    position: relative!important;
  }

  //菜单调整
  .el-submenu__title i{
    padding-top: 2px;
    font-size: 14px;
    font-weight: 600;
    color: #fff;
  }

  //菜单调整
  .menu-inner-wrapper{
    left: 0;
    right: 0;
    bottom: 0;
    top: 9px;
  }

  .menu-wrapper{
    .el-menu{
      background: rgba($color: $text-primary, $alpha: .2);
    }
  }



  //适配移动端
  .mobile {
    .cus-el-main {
      margin-left: 0px;
    }
    .sidebar-container {
      transition: transform .28s;
      width: $menu-width !important;
    }
    &.hideSidebar {
      .sidebar-container {
        transition-duration: 0.3s;
        transform: translate3d(-$menu-width, 0, 0);
      }
    }
  }
  .withoutAnimation {
    .cus-el-main,
    .sidebar-container {
      transition: none;
    }
  }

  .cus-el-main, .cus-el-main-not-sidebar{
    // background: pink;
    background: #e9eaef;
    padding: 0;
    position: relative;
    overflow: visible!important;
    // height: 100%;
    height: calc(100vh - 65px);
  }

}
