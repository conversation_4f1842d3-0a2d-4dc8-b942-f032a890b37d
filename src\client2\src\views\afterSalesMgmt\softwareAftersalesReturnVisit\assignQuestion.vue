<template>
<div>
    <app-dialog title="问题指派" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="500">
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="处理人" prop="ProcessEmployeeList">
                                <emp-selector
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="false"
                                    :list="formData.ProcessEmployeeList"
                                    @change="handleChangeUsers"
                                    :beforeConfirm='handleBeforeConfirm'
                                    >
                                </emp-selector>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="问题类型" prop="SalesAfterQuestionTypeId">
                                <!-- <el-select
                                  class="sel-ipt"
                                  style="width:100%"
                                  placeholder=""
                                  clearable
                                  v-model="formData.SalesAfterQuestionTypeId"
                                >
                                  <el-option v-for="item in treeDatas" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select> -->
                                
                                <treeselect key='type2'
                                    class="treeselect-common"
                                    :append-to-body="true"
                                    :normalizer="unitNormalizer"
                                    v-model="formData.SalesAfterQuestionTypeId" 
                                    :default-expand-level="3"
                                    :options="treeDatas" :multiple="false" placeholder='' :show-count="true"
                                    :noResultsText='noResultsTextOfSelTree'
                                    :noOptionsText="noOptionsTextOfSelTree"
                                    zIndex='9999'
                                    :flat='true'
                                    @input="hadnleChangeCustomerUnitId"
                                    >
                                </treeselect>

                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="Remark">
                                <el-input type='textarea' rows="4" v-model="formData.Remark" maxlength="1000"></el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>

                </div>


            </el-form>
        </template>
        <template slot="footer">
            
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>




</div>
</template>

<script>
import * as classify from "@/api/classify";
import { listToTreeSelect } from "@/utils";
import * as metadata from '@/api/systemManagement/metadata'
import noData from "@/views/common/components/noData";
import * as salesAfterQuestion from "@/api/afterSalesMgmt/salesAfterQuestion";
import * as salesAfterQuestionType from "@/api/afterSalesMgmt/salesAfterQuestionType";
import { vars } from './enum'
import empSelector from '@/views/common/empSelector'

export default {
    name: "assignQuestion",
    directives: {},
    components: {
        noData,
        empSelector,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [],
    props: {

        id: {
            type: String,
            default: ""
        },

    },
    watch: {

        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    this.getDetail();
                }
            },
            immediate: true
        },
    },
    computed: {

    },
    created() {

        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            unitNormalizer(node) {
                // treeselect定义字段
                if(node.QuestionType == 1){
                    node['isDisabled'] = true
                }else{
                    node['isDisabled'] = false
                }
                return {
                    id: node.Id,
                    label: node.Name,
                    children: node.children
                }
            },
            treeDatas: [],
            loading: false,
            disabledBtn: false,
            rules: {
                SalesAfterQuestionTypeId: { fieldName: "问题类型", rules: [{ required: true }] },
                ProcessEmployeeList: { fieldName: "处理人", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            formData: {
                Id: "", //
                SalesAfterQuestionTypeId: null,//类型
                ProcessEmployeeList: [],
            },



        };
    },

    methods: {
        hadnleChangeCustomerUnitId() {
            this.$refs.formData.validateField("SalesAfterQuestionTypeId");
        },
        getTreeDatas() {
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
            };
            return salesAfterQuestionType.getTreeDatas({})
        },
        handleBeforeConfirm(users) {
            if (users && users.length > 1) {
                this.$message({
                    message: '回访人不得超过1人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleChangeUsers(users) {
            this.formData.ProcessEmployeeList = users
            this.$refs.formData.clearValidate('ProcessEmployeeList');
        },
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        resetFormData() {
            let temp = {
                Id: "", //
                SalesAfterQuestionTypeId: null,//类型
                ProcessEmployeeList: [],
            };

            this.formData = Object.assign({}, this.formData, temp);
        },
        async getDetail() {
            let m1 = this.getTreeDatas()
            let m2 = salesAfterQuestion.detail({id: this.id})
            this.loading = true
            Promise.all([m1, m2]).then(res => {

                this.loading = false
                let res1 = res[0] ? res[0] : []
                let res2 = res[1]

                this.initTreeDatas(res1)

                this.formData = Object.assign({}, this.formData, {
                    Id: res2.Id, //
                    SalesAfterQuestionTypeId: res2.SalesAfterQuestionTypeId,//类型
                });

            }).catch(err => {
                this.loading = false
            });

        },
        initTreeDatas(list) {
            
            let _this = this

            // _this.treeDatas = list.map(s=> {
            //     return {
            //         label: s.Name,
            //         value: s.Id
            //     }
            // })
            _this.treeDatas = listToTreeSelect(list)

        },
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                //提交数据保存

                postData = Object.assign({}, this.formData);
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                }

                if(postData.ProcessEmployeeList && postData.ProcessEmployeeList.length > 0) {
                    postData.ProcessEmployeeIdList = postData.ProcessEmployeeList.map(s => s.EmployeeId)
                }
                delete postData.ProcessEmployeeList

                this.disabledBtn = true

                salesAfterQuestion.assign(postData).then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false

                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
.cus-collapse >>> .el-collapse-item__header{
    background: #fbfbfb!important;
    height: 36px;
}
</style>

<style lang='scss' scoped>
.wrapper{
    padding-right: 2px;
}
</style>
