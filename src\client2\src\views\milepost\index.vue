<template>
  <div class="milepost">
   <app-table
      ref="mainTable"
      :tab-columns="tabColumns"
      :multable='false'
      :tab-datas="tabDatas"
      :isShowAllColumn="isShowAllColumn"
      :loading="listLoading"
      @rowSelectionChanged="rowSelectionChanged"
      :isShowOpatColumn="true"
      :startOfTable="startOfTable"
    >

      <!-- 表格查询条件区域 -->
      <template slot="conditionArea">
        <app-table-form
          style="padding-top: 10px;"
          :label-width="'100px'"
          :items="tableSearchItems"
          @onSearch="handleFilter"
          @onReset="resetSearch"
        >
          <!-- <template slot="MilestoneDatumTypeId">
            <el-select class="sel-ipt" style="width:100%" v-model="listQuery.MilestoneDatumTypeId" placeholder clearable>
              <el-option
                v-for="item in milestoneDatumType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template> -->
          <template slot="ProjectManagementVersionPlanId" >
            <treeselect :normalizer="normalizer" class="treeselect-common" key='type1' v-model="listQuery.ProjectManagementVersionPlanId" :default-expand-level="3" :options="transIterationTreeData" :multiple="false" placeholder='' :show-count="false" :disable-branch-nodes="false" :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
            </treeselect>
          </template>
        </app-table-form>
      </template>
      <template slot="MilestoneStatusTitle" slot-scope="scope" >
        <span
          class="common-status"
          :class="'RiskFlowState-' + scope.row.MilestoneStatus"
          >{{ scope.row.MilestoneStatusTitle }}</span>
      </template>
      <template slot="ProgressValue" slot-scope="scope" >
        <span>{{ scope.row.ProgressValue }}%</span>
      </template>
      <!-- 表格批量操作区域 -->
      <template slot="btnsArea">
        <div class="pdl">
          <el-button @click="handleCreate"><span class="el-icon-circle-plus"></span> 新增</el-button>
        </div>
      </template>
      <!-- 表格行操作区域 -->
      <template slot-scope="scope">
        <!-- &&scope.row.milestoneDatumType==1 -->
        <app-table-row-button @click="handleUpdate(scope.row)" :type="2"  v-if="(scope.row.MilestoneStatus==1 || scope.row.MilestoneStatus==3)" text="编辑"></app-table-row-button>
        <app-table-row-button @click="start(scope.row)" :type="2"  v-if="scope.row.MilestoneStatus==1 || scope.row.MilestoneStatus==3" text="开始"></app-table-row-button>
        <app-table-row-button @click="startApproval(scope.row, 0)" :type="1" v-if="scope.row.MilestoneStatus==2 && scope.row.IsApproverEmployee" text="审批"></app-table-row-button>
        <app-table-row-button @click="accomplish(scope.row)" :type="2" v-if="scope.row.MilestoneStatus==4 || scope.row.MilestoneStatus==6" text="完成"></app-table-row-button>
        <app-table-row-button @click="openApproval(scope.row, 1)" :type="2" v-if="scope.row.MilestoneStatus==5 && scope.row.IsApproverEmployee" text="审批"></app-table-row-button>

        <!-- <app-table-row-button @click="changeProgress(scope.row)" v-if="scope.row.MilestoneStatus==4 || scope.row.MilestoneStatus==6" :type="2" text='调整进度'></app-table-row-button> -->
        <app-table-row-button @click="showDetails(scope.row)" :type="2"></app-table-row-button>
      </template>
    </app-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageIndex"
      :size.sync="listQuery.PageSize"
    />
     <div>
            <comment-application-or-approval @getList='getList' ref="caoa" :editDialogStatus='dialogStatus' :ProjectId="ProjectId" @closeDialog='closeDialog' @saveSuccess='handleSaveSuccess'>
            </comment-application-or-approval>
        </div>
  </div>
</template>

<script>
import {milestoneDatumType} from "../../utils/commonEnum";
import commentApplicationOrApproval from './commentApplicationOrApproval'
import * as milestoneApi from "@/api/milestone";
import empSelector from "../common/empSelector";
import dayjs from "dayjs";
import { downloadFile } from "@/utils/index";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import { getToken, getUserInfo } from "@/utils/auth";
    import questionMixins from '../question/localMixins'

export default {
  name: "milepost",
  components: {
    commentApplicationOrApproval
  },
  directives: {

  },
  props: {
    ProjectId: {
      type: String,
      default: undefined
    },
  },
  watch: {
    ProjectId: {
        handler(val) {
            this.listQuery.ProjectId = val
            this.resetSearch()
            this.getList()
        },
        immediate: true
    },
        iterationTreeData: {
            handler(val) {
                this.setTreeHead(val)
            },
            immediate: true
        },
  },
  mixins: [indexPageMixin,questionMixins],
  data() {
    return {
      normalizer(node) {
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        }
      },
      tabDatas:[],
      total:0,
      isShowAllColumn: true,
      listLoading:false,
      tableSearchItems: [
        // { prop: "MilestoneDatumTypeId", label: "基准类型" },
        { prop: "ProjectManagementVersionPlanId", label: "迭代" },

      ],
      milestoneDatumType: milestoneDatumType,
      tabColumns: [
        {
          attr: { prop: "Name", label: "里程碑名称",}
        },
        {
          attr: { prop: "PlannedStartTime", label: "计划开始时间",formatter: this.formatterDate}
        },
        {
          attr: { prop: "PlannedEndTime", label: "计划完成时间",formatter: this.formatterDate}
        },
        {
          attr: { prop: "VersionIteration", label: "版本-迭代" }
        },
        {
          attr: { prop: "Deliverable", label: "交付件" }
        },
        {
          attr: { prop: "ActualStartTime", label: "实际开始时间",formatter: this.formatterDate }
        },
        {
          attr: { prop: "ActualEndTime", label: "实际完成时间",formatter: this.formatterDate }
        },
        {
          attr: { prop: "MilestoneStatusTitle", label: "状态", width: '120px'},slot: true
        },
        {
          attr: { prop: "ProgressValue", label: "进度", width: '120px'},slot: true
        }
      ],
       listQuery: { // 查询条件
           ProjectId: this.ProjectId,
          //  MilestoneDatumTypeId:'',
      },
      dialogStatus: '',//编辑还是新增(create: 新增; update: 编辑)
      categories: [],
      transIterationTreeData:[{Id:"00000000-0000-0000-0000-000000000000",
                    ParentId: null,
                    label: "非版本/迭代"
                    }],
    };
  },
  created() {
  },
  mounted() {
  },
  methods:{
    setTreeHead(val){
      var tempData={Id:"00000000-0000-0000-0000-000000000000",
                    ParentId: null,
                    label: "非版本/迭代"
                    }
                  this.transIterationTreeData=JSON.parse(JSON.stringify(val));
                  this.transIterationTreeData.unshift(tempData);

    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    openApproval(obj,type){
      this.dialogFormVisible=true;
      if(type == 0){//申请的type
        this.$refs.caoa.childShow(obj,false);
      }else{//审批的type
        this.$refs.caoa.childShow(obj,true);
      }
    },
    changeProgress(obj) {
      this.dialogStatus = 'changeProgress'
      this.$refs.caoa.showDetails(obj.ProjectManagementMilestoneId);
    },
    showDetails(obj){
      this.dialogStatus = 'detail'
      this.$refs.caoa.showDetails(obj.ProjectManagementMilestoneId);
    },
    rowSelectionChanged(){

    },
    getList(){
      this.listQuery.ProjectId=this.ProjectId;
      milestoneApi.getList(this.listQuery).then(response=>{
          this.tabDatas = response.Items;
          console.log(888,this.tabDatas)
      });
    },
    handleCreate() {
      // this.openDialog()
      this.dialogStatus = 'create'
      this.$refs.caoa.showAddEdit("");
    },
    handleUpdate(obj){
      this.dialogStatus = 'update'
      this.$refs.caoa.showAddEdit(obj.ProjectManagementMilestoneId);
    },
     closeDialog() {
       //弹框关闭，清空弹框类型
       this.dialogStatus = ''
      this.dialogFormVisible = false
    },
    handleSaveSuccess(_formData) {

    },
    // isShowApproveBtn(row) {
    //   return row.MilestoneStatus==1 || row.MilestoneStatus==2 || row.MilestoneStatus==4
    // },
    start(obj){  // 开始
       this.$refs.caoa.childShow(obj,false);
       this.$refs.caoa.start(obj);
    },
    startApproval(obj,type){   // 开始审批业务
       this.$refs.caoa.childShow(obj,true);
       this.$refs.caoa.startApproval(obj);
    },
    accomplish(obj){   //完成
       this.$refs.caoa.childShow(obj,false);
       this.$refs.caoa.accomplish(obj);
    },
    formatterDate(row, column) {
      return this.$options.filters["dateFilter"](row[column.property], "YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang='scss' scoped>
.common-status {
  color: #fff;
  padding: 4px 8px;
  border-radius: 5px;
  /* opacity: 0.4; */
}
.RiskFlowState-1 {
  //草稿
  background: #f2913b;
}
.RiskFlowState-2 {
  //草稿
  background: #f2913b;
}

.RiskFlowState-3 {
  //待处理
  background: #3b95fa;
}

.RiskFlowState-4 {
  //待审批
  background: #67c23a;
}

.RiskFlowState-5 {
  //责任人驳回
  background: #e84c3d;
}

.RiskFlowState-6 {
  //审批人驳回
  background: #929292;
}

.RiskFlowState-7 {
  //已处理
  background: #05c4a7;
}


</style>


