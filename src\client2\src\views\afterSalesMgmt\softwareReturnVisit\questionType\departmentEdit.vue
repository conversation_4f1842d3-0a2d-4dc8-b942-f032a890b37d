<!--客服区域-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="600"
    >
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="100px"
          style="height: 150px;"
        >
          <el-form-item label="路径" v-if="fullPath">
            {{ fullPath }}
          </el-form-item>
          <el-form-item label="分类名称" prop="Name">
            <el-input maxlength="50" :disabled="!editable" v-model="formModel.Name"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <span class="fl m-r-50" v-if="dialogStatus == 'create'">
          <el-checkbox v-model="isContinue">继续添加</el-checkbox>
        </span>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
// import * as customerTagClassify from "@/api/salesMgmt/customerTagClassify";
import * as classify from "@/api/classify";
export default {
  /**名称 */
  name: "customer-service-department-edit",
  /**组件声明 */
  components: {},
  mixins: [],
  /**参数区 */
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
      default: "create"
    },
    //操作的节点，如果是新增，则为父节点；编辑为当前节点
    node: {
      type: Object,
      required: true
    }
  },
  /**数据区 */
  data() {
    return {
      isContinue: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单模型 */
      formModel: { Name: "", Level: 1 },
      /**表单规则 */
      formRules: {
        Name: { fieldName: "分类名称", rules: [{ required: true }] }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    
    fullPath() {
      if(this.node && this.node.ParentName) {

        let path = this.node.ParentName
        let nodes = path.split('/')

        if(this.dialogStatus != "create") {
          nodes.pop()
        }
        return nodes.join(' / ')
      }
      return ''
    },
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加分类";
      } else if (this.dialogStatus == "update") {
        return "编辑分类";
      } else if (this.dialogStatus == "detail") {
        return "分类详情";
      }
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.isContinue = false;
        }

        let _this = this;
        if (val) {
          if (this.dialogStatus == "create") {
            this.resetFormModel();
          } else {
            this.getDetail();
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    resetFormModel() {
      let temp = {
        Id: "",
        Level: this.node.Level ? this.node.Level + 1 : 1,
        ParentId: this.node.Id,
        Name: "" //名称
      };
      this.formModel = Object.assign({}, this.formModel, temp);
    },
    getDetail() {
      this.formModel = Object.assign({}, this.formModel, this.node);
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;
          let postDatas = JSON.parse(JSON.stringify(_this.formModel))
          postDatas.BusinessType = 19
          if (_this.dialogStatus == "create") {
            result = classify.add(postDatas);
          } else if (_this.dialogStatus == "update") {
            result = classify.edit(postDatas);
          }

          result
            .then(response => {
              _this.buttonLoading = false;
              _this.$notify({
                title: "成功",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              if (this.isContinue) {
                this.resetFormModel();
              }
              _this.$refs.appDialogRef.createData(this.isContinue);
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


