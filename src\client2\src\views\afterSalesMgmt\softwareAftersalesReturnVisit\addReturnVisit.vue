<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750">
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <div class="lft">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="站点信息" prop="RegionalBusinessRelationId">
                                    <div class="_regional_detail_wrapper">
                                        <div class="btn_wrapper">
                                            <el-button :disabled="!editable" type="text" @click="handleRegionDialog">选择</el-button>
                                        </div>
                                        <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                        <div class="close_wrapper" v-show="formData.RegionalName && editable">
                                            <div class="i_wrapper">
                                                <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                            </div>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>

                        
                            <el-col :span="24">
                                <el-form-item label="回访类型" prop='SalesAfterVisttType'>
                                    <el-radio-group v-model="formData.SalesAfterVisttType">
                                        <el-radio v-for="(item, idx) in salesAfterVisttTypes" :key="`ch_${idx}`" :label="item.value">{{ item.label }}</el-radio>
                                        <!-- <el-radio :label="1">正常回访</el-radio>
                                        <el-radio :label="2">故障处理</el-radio> -->
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="回访人" prop='EmployeeList'>
                                    <emp-selector :beforeConfirm='handleBeforeConfirm' :readonly="!editable" :showType="2" :multiple="true" :list="formData.EmployeeList" @change="handleChangeManager"></emp-selector>
                                </el-form-item>
                            </el-col>
                            
                            <el-col :span="24">
                                <el-form-item label="回访时间" prop='VistTime'>
                                    <el-date-picker style="width:100%;" :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.VistTime" type="date"></el-date-picker>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item label="参数模板" prop="SalesAfterVistTemplateId">
                                    <div class="_regional_detail_wrapper">
                                        <div class="btn_wrapper">
                                            <el-button :disabled="!editable" type="text" @click="handleUsageDefault">使用默认模板</el-button>
                                            <el-button :disabled="!editable" type="text" @click="handleParamsTempReport">选择</el-button>
                                        </div>
                                        <div class="regional_text" :title="formData.SalesAfterVistTemplateName">{{ formData.SalesAfterVistTemplateName }}</div>
                                        <div class="close_wrapper" v-show="formData.SalesAfterVistTemplateName && editable">
                                            <div class="i_wrapper">
                                                <el-button icon="el-icon-close" class="btn" circle @click="handleParamsTempSaveSuccess(null)"></el-button>
                                            </div>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row v-if="formData.SalesAfterVistTemplateId == defaultTemp.value">
                            <el-col :span="24">
                                <el-form-item label="回访内容" prop='Contents'>
                                    <el-input type="textarea" maxlength="1000" :rows="3" :disabled="!editable" v-model="formData.Contents"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="备注" prop='Remark'>
                                    <el-input type="textarea" maxlength="500" :rows="3" :disabled="!editable" v-model="formData.Remark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <div v-loading='tempListLoading' v-if="formData.SalesAfterVistTemplateId && formData.SalesAfterVistTemplateId != defaultTemp.value" style="min-height: 80px;">
                            <noData v-if="!tempListLoading && formData.SalesAfterVistTemplate.length == 0"></noData>
                            <el-collapse class="cus-collapse" v-model='activeNames' v-else>
                                <el-collapse-item :name="t.Id" v-for="(t, idx) in formData.SalesAfterVistTemplate" :key="idx">
                                    <template slot="title">
                                        {{ t.Name }}
                                    </template>
                                    <el-row>
                                        <el-col :span="24" v-for="tt in t.FieldList" :key="tt.FieldId">
                                            <div class="col-wrapper">
                                                <div class="col-title el-form-item__label omit" :title="tt.Name">
                                                    {{ tt.Name }}
                                                </div>
                                                <div class="col-content" :title="tt.FieldValue">
                                                    <el-input type="textarea" :rows="3" maxlength="500" :disabled="!editable" v-model="tt.FieldValue"></el-input>
                                                </div>
                                            </div>
                                            
                                        </el-col>
                                    </el-row>
                                </el-collapse-item>
                            </el-collapse>
                        </div>
                    </div>
                    <div class="rht">
                        <div>
                            <div class="panel-title">附件</div>
                            <div>
                                <app-uploader
                                ref="appUploaderRef"
                                :readonly="!editable"
                                accept="all"
                                :fileType="3"
                                :max="10000"
                                :value="formData.AttachmentList"
                                :fileSize="1024 * 1024 * 500"
                                :minFileSize="100 * 1024"
                                @change="handleFilesUpChange"
                                ></app-uploader>
                            </div>
                        </div>
                    </div>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <span class="fl m-r-50" v-if="dialogStatus == 'create'">
                <el-checkbox v-model="isContinue">继续添加</el-checkbox>
            </span>
            
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>

    <regionalSelector
        @closeDialog="closeRegionDialog"
        @saveSuccess="electedRegionalData"
        :dialogFormVisible="dialogRegionFormVisible"
        :checked="formData.RegionalBusinessRelationId ? {value: formData.RegionalBusinessRelationId, label: formData.RegionalName} : null"
    ></regionalSelector>

    <paramsTempSelector
        v-if="dialogParamsTempFormVisible"
        @closeDialog='closeParamsTempDialog' 
        @saveSuccess='handleParamsTempSaveSuccess'
        :dialogFormVisible='dialogParamsTempFormVisible'
        :dialogStatus='dialogParamsTempStatus' 
        :isDeviceParams='false'
    ></paramsTempSelector>

</div>
</template>

<script>
import * as afterVistTemplate from "@/api/afterSalesMgmt/afterVistTemplate";
import noData from "@/views/common/components/noData";
import regionalSelector from "./regionalSelector";
import paramsTempSelector from './paramsTempSelector'
import empSelector from '../../common/empSelector'
import * as salesAfterVist from "@/api/afterSalesMgmt/salesAfterVist";
import { getUserInfo } from "@/utils/auth";
import { vars } from './enum'

import dayjs from 'dayjs'
let defaultTemp = {value: '00000000-0000-0000-0000-000000000000', label: '默认模板'}

export default {
    name: "custom-mgmt-pool-create",
    directives: {},
    components: {
        noData,
        regionalSelector,
        paramsTempSelector,
        empSelector,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [],
    props: {
        dialogStatus: {
            //create、update、detail、follow（跟进）
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        checkedRegional: {
            type: Object,
            default: null
        },
    },
    watch: {

        "$attrs.dialogFormVisible": {
            
            handler(val) {
                if (!val) {
                    this.isContinue = false;
                }
                
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }


                    if(this.dialogStatus == 'create' && this.checkedRegional) {
                        this.formData.RegionalBusinessRelationId = this.checkedRegional.value
                        this.formData.RegionalName = this.checkedRegional.label
                    }

                    if(this.dialogStatus == 'create') {
                        this.loadLastTemplate()

                        let currUser = getUserInfo();
                        this.formData.EmployeeList = [
                            {
                                EmployeeId: currUser.employeeid,
                                Name: currUser.username,
                                Avatar: currUser.empAvatar,
                                Number: currUser.empNumber
                            }
                        ];
                    }

                }
            },
            immediate: true
        },
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            let mainTitle = '回访记录'
            if (this.dialogStatus == "create") {
                return `创建${mainTitle}`
            } else if (this.dialogStatus == "update") {
                return `编辑${mainTitle}`
            } else if (this.dialogStatus == "detail") {
                return `${mainTitle}详情`
            } 
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            salesAfterVisttTypes: vars.salesAfterVisttTypes,
            defaultTemp: defaultTemp,
            isContinue: false,
            treeDatas: [],
            loading: false,
            disabledBtn: false,
            rules: {
                RegionalBusinessRelationId: { fieldName: "站点信息", rules: [{ required: true }] },
                EmployeeList: { fieldName: "回访人", rules: [{ required: true }] },
                VistTime: { fieldName: "回访时间", rules: [{ required: true }] },
                SalesAfterVistTemplateId: { fieldName: "参数模板", rules: [{ required: true }] },
                Contents: { fieldName: "回访内容", rules: [{ required: true }] },
                SalesAfterVisttType: { fieldName: "回访类型", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            activeNames: [],
            formData: {
                SalesAfterVisttType: 1, // 回访类型 1  正常回访  2   故障处理
                Id: "", //
                EmployeeList: [],
                VistTime: dayjs().format('YYYY-MM-DD'),
                RegionalBusinessRelationId: null,
                RegionalName: '',
                SalesAfterVistTemplateId: defaultTemp.value,
                SalesAfterVistTemplateName: defaultTemp.label,
                AttachmentList: [],
                SalesAfterVistTemplate: [
                ],
            },
            tempListLoading: false,

            dialogRegionFormVisible: false,

            dialogParamsTempFormVisible: false,
            dialogParamsTempStatus: "create",
        };
    },

    methods: {
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleChangeManager(users) {
            if (users && users.length > 0) {
                this.formData.EmployeeList = users;
            } else {
                this.formData.EmployeeList = [];
            }
            this.$refs["formData"].validateField("EmployeeList");
        },
        handleBeforeConfirm(users) {
            if (users && users.length > 5) {
                this.$message({
                    message: '回访人不得超过5人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        loadLastTemplate() {
            salesAfterVist.loadLastTemplate().then(res => {
                if(res) {
                    this.formData.SalesAfterVistTemplateId = res.SalesAfterVistTemplateId
                    this.formData.SalesAfterVistTemplateName = res.Name
                    this.formData.SalesAfterVistTemplate = res.TypeList

                    if(this.formData.SalesAfterVistTemplate && this.formData.SalesAfterVistTemplate.length > 0) {
                        this.activeNames = this.formData.SalesAfterVistTemplate.map(s => s.Id)
                    }
                }
            })
        },
        resetFormData() {
            let temp = {
                SalesAfterVisttType: 1,
                Id: "", //
                EmployeeList: [],
                AttachmentList: [],
                VistTime: dayjs().format('YYYY-MM-DD'),
                Contents: '',
                Remark: '',
            };
            if(this.$refs.appUploaderRef) {
                this.$refs.appUploaderRef.clearFiles();
            }
            if(this.formData.SalesAfterVistTemplate) {
                this.formData.SalesAfterVistTemplate.forEach(t => {
                    if(t.FieldList) {
                        t.FieldList.forEach(tt => {
                            tt.FieldValue = ''
                        })
                    }
                });
            }
            this.formData = Object.assign({}, this.formData, temp);
        },
        async getDetail() {
            this.loading = true
            salesAfterVist.detail({
                id: this.id
            }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);
                if(this.formData.SalesAfterVistTemplateId == defaultTemp.value) {
                    this.formData.SalesAfterVistTemplateName = defaultTemp.label
                }

                if(this.formData.SalesAfterVistTemplate && this.formData.SalesAfterVistTemplate.length > 0) {
                    this.activeNames = this.formData.SalesAfterVistTemplate.map(s => s.Id)
                }
            }).catch(err => {
                this.loading = false
            });
        },

        //地区选择
        closeRegionDialog() {
          this.dialogRegionFormVisible = false;
        },
        handleRegionDialog(){
            this.dialogRegionFormVisible=true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('RegionalBusinessRelationId');
            if(data){
                this.formData.RegionalBusinessRelationId=data.value;
                this.formData.RegionalName=data.label;
            }else{
                this.formData.RegionalBusinessRelationId='';
                this.formData.RegionalName='';
            }
            this.closeRegionDialog()
        },
        getTempList() {
            if(this.formData.SalesAfterVistTemplateId) {
                this.tempListLoading = true
                afterVistTemplate.detail({id: this.formData.SalesAfterVistTemplateId}).then(res => {
                    this.tempListLoading = false
                    this.formData.SalesAfterVistTemplate = res.TypeList

                    if(this.formData.SalesAfterVistTemplate && this.formData.SalesAfterVistTemplate.length > 0) {
                        this.activeNames = this.formData.SalesAfterVistTemplate.map(s => s.Id)
                    }
                }).catch(err => {
                    this.tempListLoading = false
                })
            }
        },
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                //提交数据保存

                postData = Object.assign({}, this.formData);
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                }

                if(postData.EmployeeList) {
                    postData.EmployeeIdList = postData.EmployeeList.map(s => s.EmployeeId)
                }
                delete postData.EmployeeList

                if(postData.AttachmentList) {
                    postData.AttachmentIdList = postData.AttachmentList.map(s => s.Id);
                }
                delete postData.AttachmentList

                //如果不是默认模板
                if(postData.SalesAfterVistTemplateId != defaultTemp.value) {
                    postData.Contents = ''
                    postData.Remark = ''
                }

                this.disabledBtn = true
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = salesAfterVist.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = salesAfterVist.edit(postData);
                }

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    if (this.isContinue) {
                        this.resetFormData();
                        this.$emit("reload");
                    } else {
                        this.$refs.appDialogRef.createData();
                    }
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },
        handleUsageDefault() {
            this.formData.SalesAfterVistTemplateId = defaultTemp.value
            this.formData.SalesAfterVistTemplateName = defaultTemp.label
            this.validateField()
        },
        handleParamsTempReport(row, activeName = 'create') {
            this.dialogParamsTempStatus = activeName;
            this.dialogParamsTempFormVisible = true;
        },
        closeParamsTempDialog() {
            this.dialogParamsTempFormVisible = false
        },
        validateField() {
            this.$refs.formData.clearValidate('SalesAfterVistTemplateId');
        },
        handleParamsTempSaveSuccess(checkedObj) {
            if(checkedObj) {
                this.formData.SalesAfterVistTemplateId = checkedObj.value
                this.formData.SalesAfterVistTemplateName = checkedObj.label
                this.formData.SalesAfterVistTemplate = []
                this.getTempList()
            }else{
                this.formData.SalesAfterVistTemplateId = null
                this.formData.SalesAfterVistTemplateName = ''
            }
            this.validateField()
            this.closeParamsTempDialog()
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>


<style scoped>
.cus-collapse >>> .el-collapse-item__header{
    background: #fbfbfb!important;
    height: 36px;
}
</style>


<style lang='scss' scoped>

.wrapper{
    display: flex;
    .lft{
        flex: 1;
    }
    .rht{
        padding-left: 20px;
        width: 400px
    }
}

.col-wrapper{
    display: flex;
    align-items: center;
    padding: 5px 0;
    .col-title{
        width: 100px;
        text-align: right;
        padding-right: 12px;
    }
    .col-content{
        flex: 1;
    }
}


.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}
</style>
