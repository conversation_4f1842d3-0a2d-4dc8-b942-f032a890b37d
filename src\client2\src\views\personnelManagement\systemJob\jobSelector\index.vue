<template>
    <div class="sel-wrapper" :class="reference ? '' : 'avatar-warpper clearfix'" :style="{ display: reference ? 'inline-block' : 'block' }">
        <!-- <div v-if="showType == 1">
            <div class="avatar-item" v-for="(p, idx) in pers" :key="'p_' + idx" @mouseover="() => delIdx = idx"
                @mouseout="() => delIdx = -1">
                <i class="btn-remove el-icon-remove" v-if="!readonly" v-show="delIdx == idx"
                    @click.stop="handleRemove(idx)"></i>
                <div class="avatar">
                    <img :src="p.Avatar" alt="">
                </div>
                <div class="username" :title="p.Name">{{ p.Name }}</div>
            </div>

            <div class="avatar-item">
                <div class="avatar">
                    <i class="el-icon-plus icon-plus" v-show="!readonly" @click="handleShow"></i>
                </div>
                <div></div>
            </div>
        </div> -->
        <template :class="reference ? 'inline' : ''">
            <slot name="reference">
                <el-input placeholder="" :value="names" :title="names" :disabled="readonly" :readonly='true' unselectable='on' class="input-with-select">
                    <i slot="suffix" v-if="isShowClearButton" v-show="!readonly" @click="handleClear" class="el-input__icon el-icon-close icon-close"></i>
                    <div slot='append'>
                        <el-button @click="handleShow" :disabled='readonly' style="padding: 7px 12px;" icon="el-icon-more"></el-button>
                    </div>
                </el-input>
            </slot>
        </template>

        <!-- <el-dialog width="1000px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini"
            ref="accessUserDlg" v-el-drag-dialog :title="'添加员工'" :visible.sync='dialogAccessUsers'
            :append-to-body='true'>
            <commodity-table ref="accessUser" v-bind="$attrs" :existsUsers='pers' v-if="dialogAccessUsers" 
                v-show="dialogAccessUsers" @changed='handleChangeUsers'></commodity-table>
        </el-dialog> -->

        <app-dialog
            title="添加职位"
            ref="appDialogRef2"
            @closeDialog="() => dialogAccessUsers = false"
            @saveSuccess="handleAddSaveSuccess"
            :dialogFormVisible="dialogAccessUsers"
            :width="1000"
            >
            <template slot="body">
                <job-table ref="accessUser" v-bind="$attrs" :existsUsers='pers' v-if="dialogAccessUsers" v-show="dialogAccessUsers" @changed='handleChangeUsers'></job-table>
            </template>
            <template slot="footer">
                <div class="footer-btns">
                    
                    <div class="rht-btns">
                        <!-- 取消 -->
                        <app-button @click="() => dialogAccessUsers = false" :buttonType="2"></app-button>
                        <!-- 确认 -->
                        <app-button @click="handleAddSaveSuccess" :buttonType="1"></app-button>
                    </div>
                </div>
            </template>
        </app-dialog>

        <!-- <createDialog v-if="createDialogDialogFormVisible" :dialogStatus="createDialogDialogStatus" :dialogFormVisible="createDialogDialogFormVisible" @closeDialog="createDialogCloseDialog" @reload='createDialogSaveSuccess(false)' @saveSuccess="createDialogSaveSuccess"></createDialog> -->
    </div>
</template>

<script>
    import elDragDialog from '@/directive/el-dragDialog'
    import jobTable from './jobTable'
    // import createDialog from "../../basic/commodity/create";

    export default {
        name: 'user-list',
        directives: {
            // waves,
            elDragDialog
        },
        components: {
            jobTable,
            // createDialog: () => import('../../basic/commodity/create'),
        },
        mounted() {
            // this.pers = JSON.parse(JSON.stringify(this.list))
            let referenceObj = this.$slots.reference
            if (referenceObj) {
                this.reference = referenceObj[0].elm
            }
            if (this.reference) {
                this.reference.addEventListener('click', this.handleShow, false)
            }
        },
        props: {
            //已存在的人员
            list: {
                type: Array,
                default: () => {
                    return []
                },
            },
            readonly: {
                type: Boolean,
                default: false
            },
            // showType: {
            //     type: Number,
            //     default: 1, //2：头像模式；2：文本框模式
            // },
            isShowClearButton: {
                type: Boolean,
                default: true
            },
            //打开弹框之前（返回 true 会打开弹框，否则不打开）
            ////用于打开弹框之前，判断是否应该打开弹框
            beforeOpen: Function,
            //关闭弹框之前（返回 true 会关闭弹框，否则不关闭弹框）
            //保存成功之后，关闭按钮之前——默认情况下，保存调用api后，需要根据返回结果判断是否关闭弹框
            ////用于关闭弹框之前，判断是否应该关闭弹框——保存后的关闭按钮，而不是点击右上角的关闭按钮（该按钮一般也业务无关）
            beforeConfirm: Function,

        },
        computed: {
            names() {
                if (this.pers && this.pers.length > 0) {
                    // return this.pers.map(p => `${p.Name}(${p.Number})`).join(',')
                    let tmp = this.pers.map(p => {
                        if (p.Name && p.Number) {
                            return `${p.Name}(${p.Number})`
                        }
                        return ''
                    })
                    return tmp.join(',')
                }
                return ''
            }
        },
        data() {
            return {
                // delIdx: -1,
                pers: [],
                dialogAccessUsers: false,
                reference: undefined,

                // createDialogDialogFormVisible: false,
                // createDialogDialogStatus: "create",

            }
        },
        watch: {
            list: {
                handler(val) {
                    this.pers = JSON.parse(JSON.stringify(val))
                },
                immediate: true
            },
        },

        methods: {
            // 创建货品
            // handleCreate() {
            //     // this.createDialogDialogStatus = 'create';
            //     // this.createDialogDialogFormVisible = true;
            // },
            // createDialogSaveSuccess(closeDialog) {
            //     //刷新子组件列表
            //     this.$refs.accessUser.getList()
            //     if(closeDialog === false) {
            //     }else{
            //         this.createDialogCloseDialog();
            //     }
            // },
            // createDialogCloseDialog() {
            //     // this.createDialogDialogFormVisible = false;
            // },
            
            // handleRemove(idx) {
            //     this.pers.splice(idx, 1)
            //     this.usersChanged()
            // },
            handleAddSaveSuccess() {
                let selectedList = this.$refs.accessUser.getSelectedList()
                this.handleBeforeClose(selectedList)
            },
            handleChangeUsers(users) {
                this.handleBeforeClose(users)
            },
            async handleBeforeClose(users) {
                if(this.beforeConfirm) {
                    if(await this.beforeConfirm(users)) {
                        this.pers = users
                        this.usersChanged()
                        this.dialogAccessUsers = false
                    }
                }else{
                    this.pers = users
                    this.usersChanged()
                    this.dialogAccessUsers = false
                }
            },

            usersChanged() {
                let postDatas = this.pers
                this.$emit("change", postDatas)
            },
            handleClear() {
                this.pers = []
                this.usersChanged();
            },
            async handleShow() {
                if(this.beforeOpen) {
                    if(await this.beforeOpen()) {
                        this.dialogAccessUsers = true
                    }
                }else{
                    this.dialogAccessUsers = true
                }
            },
        },
        beforeDestroy() {
            if (this.reference) {
                this.reference.removeEventListener('click', this.handleShow, false)
            }
        },
    }
</script>

<style scoped>
.sel-wrapper >>> .el-dialog__body{
    padding: 0 10px!important;
}

     /* .avatar-warpper {}

    .avatar-warpper .avatar-item {
        position: relative;
        float: left;
        text-align: center;
        margin: 0 8px;
        width: 50px;
    }

    .avatar-warpper .avatar-item .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
    }

    .avatar-warpper .avatar-item .avatar img {
        width: 50px;
        height: 50px;
    }

    .btn-remove {
        position: absolute;
        top: -5px;
        right: -5px;
        cursor: pointer;
    }

    .btn-remove:hover {
        transition: all 0.3s;
        color: red;
    }

    .icon-plus {
        line-height: 50px;
        width: 50px;
        font-size: 24px;
        cursor: pointer;
    }

    .username {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .icon-close {
        cursor: pointer;
    }

    .dialog_wrapper>>>.el-dialog__body {
        padding: 10px;
    }

    */

    .inline{
        display: inline;
    } 


</style>

<style lang="scss" scoped>

.footer-btns{
    display: flex;
    .rht-btns{
        flex: 1;       
    }
}
</style>