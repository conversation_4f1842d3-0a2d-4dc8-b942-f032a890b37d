<template>
    <table class="my-tab">
        <thead>
            <tr>
                <th class="num">序号</th>
                <th v-for="c in columns" :key="c.attr.prop" v-bind="c.attr">
                    {{ c.attr.label }}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr v-show="!loading && list.length == 0">
                <td :colspan="columns.length + 1" style="border-bottom: none; text-align: center; color: #9c9faf;">
                    没有数据
                </td>
            </tr>
            <tr v-show="loading">
                <td v-loading='loading' :colspan="columns.length + 1"></td>
            </tr>
            <tr v-for="(r, idx) in list" :key="idx">
                <td class="num">{{ idx + 1 }}</td>
                <template v-for="c in columns">
                    <td v-if="c.slot" :key="c.attr.prop" v-bind="c.attr">
                        <slot :name="c.attr.prop" :row='r'></slot>
                    </td>
                    <td v-else :key="c.attr.prop" v-bind="c.attr">
                        {{ r[c.attr.prop] }}
                    </td>
                </template>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import * as flowinstances from '@/api/flowInstances'
    import request from '@/utils/request'

    export default {
        name: 'flow-tab',
        props: {
            reqType: {
                type: String,
                default: 'post'
            },
            url: {
                type: String,
                required: true
            },
            condition: {
                type: Object,
                default: null
            },
            tabColumns: {
                type: Array,
                required: true,
                validator: (cols) => {
                    return cols.length >= 1 //表格至少需要1列
                }
            },
        },
        computed: {
            //表格需要显示的列
            columns() {
                return this.headers.filter(h => h.isShow);
            },
        },
        created() {
            this._initColumns()
            this.getList()
        },
        data() {
            return {
                loading: false,
                headers: [],
                list: [],
            }
        },
        methods: {
            getList() {
                this.loading = true

                let cond = {
                    PageIndex: 1,
                    PageSize: 5,
                    type: 2, //1: 普通流程；2：维修单流程
                }

                let postData = {
                    url: this.url,
                    method: this.reqType,
                }
                if (postData.method == 'get') {
                    postData.params = Object.assign(cond, this.condition)
                } else {
                    postData.data = Object.assign(cond, this.condition)
                }

                request(postData).then(res => {
                    this.list = res.Items
                    this.loading = false
                }).catch(err => {
                    this.loading = false
                })
                // flowinstances.getList({
                //     PageIndex: 1,
                //     PageSize: 5,
                //     Status: this.status
                // }).then(response => {
                //     this.list = response.Items
                // }).catch(err => {

                // })
            },
            _initColumns() {
                let res = this.tabColumns
                // if(!this.isShowAllColumn){
                //     res = res.filter(h => this.authColumns.some(o => o == h.attr.prop))
                // }
                this.headers = res.map((c) => {
                    if (c.isShow === undefined) {
                        this.$set(c, 'isShow', true)
                    }
                    return c;
                });
            },
        }
    }
</script>

<style scoped>
    .my-tab th {
        color: #000034;
    }

    .my-tab th,
    td {
        color: #6f7799;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .my-tab td,
    th {
        border-bottom: 1px solid #E4E7ED;
        height: 54px;
        line-height: 54px;
        box-sizing: border-box;
    }

    .my-tab tr:last-of-type td {
        border-bottom: none;
    }

    .my-tab .full-time {
        min-width: 180px;
        text-align: center;
    }

    .my-tab .num {
        width: 80px;
        text-align: center;
    }

    .my-tab {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
        /* table-layout: fixed; */
        table-layout: fixed;
        width: 100%;
    }

    /* .a-l{
  text-align: left;
}

.a-c{
  text-align: center;
} */
</style>