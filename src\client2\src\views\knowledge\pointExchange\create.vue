<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1200' :maxHeight="800" >
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="奖品图片" prop="LogoPath">
                            <app-upload-file :max='1' :fileSize="1024 * 1024 * 2" :fileType='1' :value='CoverFileList' :readonly="!editable" @change='handleUpChange' :preview='true'></app-upload-file>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="奖品类型" prop="ClassifyId">
                            <treeselect :normalizer="normalizer" :disabled="!editable" :options="treeData" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择奖品类型" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" :append-to-body="true" zIndex='9999'>
                                <!-- <div slot="value-label" slot-scope="{ node }">{{ node.raw.ParentName }}</div> -->
                            </treeselect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="奖品名称" prop="Name">
                            <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.Name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="单位" prop="Unit">
                            <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.Unit"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="兑换积分" prop="PointsExchangesValue">
                            <el-input-number :disabled="!editable" v-model="formData.PointsExchangesValue" :min="0" :max="999999999" label="描述文字"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="剩余库存" prop="Inventory">
                            <el-input-number :disabled="!editable" v-model="formData.Inventory" :min="0" :max="999999999" label="描述文字"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="奖品介绍" prop="Description">
                    <editor-bar v-if="editable" :value="formData.Description" @edit="formData.Description = arguments[0]"></editor-bar>
                    <div class="divUeditor ql-editor" v-html="formData.Description" v-if="!editable"></div>
                </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable"></app-button>
        </template>
    </app-dialog>

</div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";
import EditorBar from "@/components/QuillEditor/index.vue";
import { vars } from '../common/vars'
import * as classify from '@/api/classify'
import * as pointExchange from "@/api/knowledge/pointExchange";

export default {
    name: "point-exchange-create",
    directives: {},
    components: {
        EditorBar,
        Treeselect,
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        selectTypeId: {
            type: String,
            default: ""
        },

    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建兑换奖品";
            } else if (this.dialogStatus == "update") {
                return "编辑兑换奖品";
            } else if (this.dialogStatus == "detail") {
                return "兑换奖品详情";
            }
        }
    },

    created() {
        this.getTreeData()
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            /** 选择部门 */
            treeData: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },
            departStr: '',
            checkedList:[],


            viewRangeTypes: vars.trainEnum.viewRangeTypes, // 可见范围 1:所有人 2:按部门 3:按入职时间 4:自定义
            examTypeTypes: vars.trainEnum.examTypeTypes, // 是否必考 1:不考 2:必考 3:选考
            studyCycleTypes: vars.trainEnum.studyCycleTypes, // 学习循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
            isClear: false,
            fileList: [], //图像信息[{Id: '', Path: ''}]
            CoverFileList: [],
            formLoading: false,
            isContinue: false,
            rules: {
                ClassifyId: {
                    fieldName: "奖品类型",
                    rules: [{
                        required: true
                    }]
                },
                Name: {
                    fieldName: "奖品名称",
                    rules: [{
                        required: true
                    }]
                },
                Unit: {
                    fieldName: "单位",
                    rules: [{
                        required: true
                    }]
                },
                
            },
            labelWidth: "100px",
            formData: {
                Id: "",
                ClassifyId: null,
                LogoPath: "",
                Unit: '',
                PointsExchangesValue: null,
                Inventory: null,
                Description: '',
            }
        };
    },
    methods: {
        
        // 删除关联的试卷
        handleUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.LogoPath = imgs[0].Path
                this.formData.Logo = imgs[0].Id
            } else {
                this.formData.Logo = ''
                this.formData.LogoPath = ''
            }
        },
        resetFormData() {
            let temp = {
                Id: "",
                ClassifyId: null,
                LogoPath: "",
                Unit: '',
                PointsExchangesValue: null,
                Inventory: null,
                Description: '',
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            this.formLoading = true;
            pointExchange
                .detail({
                    id: this.id
                })
                .then((res) => {

                    this.formLoading = false;
                    this.formData = Object.assign({}, this.formData, res);

                    this.CoverFileList = [];
                    if (this.formData.LogoPath) {
                        this.CoverFileList = [{
                            Id: this.formData.Logo,
                            Path: this.formData.LogoPath
                        }];
                    }

                })
                .catch((err) => {
                    this.formLoading = false;
                });

        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        createData() {

            let validate = this.$refs.formData.validate();
            console.log(this.formData)
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));

                //提交数据保存
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = pointExchange.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = pointExchange.edit(postData);
                }

                result.then(res => {
                    if (this.isContinue) {
                        this.resetFormData();
                        this.$emit("reload");
                    } else {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData();
                    }

                });
            });
        },
        


        getTreeData() {
            let postDatas = {
                PageIndex: 1,
                PageSize: 10000,
                BusinessType: 11
            }
            classify.getListPage(postDatas).then(res => {
                var list = (res.Items || []).map((item, index, input) => {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId
                    };
                });
                this.treeData = listToTreeSelect(list, undefined, undefined, undefined, 'DepartmentName');
                
                if (this.dialogStatus == "create") {
                    this.formData.ClassifyId = this.selectTypeId || null;
                }
            });
        },
        handleSave() {
            this.createData();
        }
    }
};
</script>

<style scoped>
.inputLeft >>> .el-input-number .el-input__inner{
  text-align: left;
  width: 70px;
}
</style>
<style lang="scss" scoped>
.dUl{
    min-width:300px;
    max-width:500px;
    margin-top:10px;
    max-height: 350px;
    overflow-y: auto;
    border:1px solid #DCDFE6;
    padding: 0px 10px;
    border-radius: 4px;
}
.el-card{
    margin-bottom: 20px;
}
.wrapper {
    display: flex;

    .left {
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }

    .right {
        width: 40%;
    }
}

.panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
}
.rowBox>span{
    float: left;
    padding:0 10px;
}
.rowBox>span:first-child{
    padding: 0;
}
</style>
