export const vars = {
    outworkType: [
        { value: 10, label: '【客户/现场】' },
        { value: 20, label: '【居家办公】' },
        { value: 30, label: '【异地出差】' },
        { value: 40, label: '【其他】' },
        { value: 50, label: '【范围内打卡】' },
    ],
    timecardType: [
        { value: 1, label: '密码' },
        { value: 2, label: '指纹' },
        { value: 3, label: '人脸' },
        { value: 4, label: '定位' },
        { value: 5, label: '人工审核' },
    ],
    operators: [
        { value: '>', label: '大于' },
        { value: '>=', label: '大于等于' },
        { value: '<', label: '小于' },
        { value: '<=', label: '小于等于' },
    ],
    morningOrAfternoon: [
        { value: 1, label: '上午' },
        { value: 2, label: '下午' },
    ],
    //打卡状态
    timecardStatus: [
        { value: 1, label: '正常', color: '#409eff' },
        { value: 2, label: '迟到', color: '#FF0000' },
        { value: 3, label: '早退', color: '#FF0000' },
        // { value: 4, label: '缺勤', color: '#FF0000' },
        { value: 5, label: '缺卡', color: '#FF0000' },
        { value: 6, label: '正常', color: '#409eff' },
       // { value: 6, label: '工作日加班', color: '#F59A23' }, //工作日加班
        // { value: 7, label: '加班', color: '#F59A23' }, //休息日加班
        // { value: 8, label: '审批中', color: '#FF0000' }, //审批中
    ],
    processTypes: [
        { value: 1, label: '请假', color: '#EC808D' },
        { value: 2, label: '加班', color: '#00CC00' },
        { value: 3, label: '出差', color: '#F59A23' },
        { value: 4, label: '外出', color: '#00BFBF' },
        { value: 5, label: '赠礼', color: '#409eff' },
        { value: 6, label: '出差补助', color: '#F59A23' },
        { value: 7, label: '变更联络', color: '#F59A23' },
        { value: 8, label: '项目立项' },
        { value: 9, label: '人员异动' },
        {value: 10, label: '费用报销'},
        {value: 11, label: '付款申请'},
        {value: 12, label: '差旅费报销'},
        {value: 13, label: '借款申请'},
    ],
    //流程类型
    leaveTypes: [
        { value: 1, label: '事假' },
        { value: 2, label: '调休' },
        { value: 3, label: '病假' },
        { value: 4, label: '年假' },
        { value: 5, label: '产假' },
        { value: 6, label: '陪产假' },
        { value: 7, label: '婚假' },
        { value: 8, label: '丧假' },
        { value: 9, label: '加班' },
        { value: 10, label: '出差' },
        { value: 11, label: '外出' },
        { value: 12, label: '哺乳假' },
        { value: 13, label: '产检假' },
        { value: 14, label: '育儿假' },
    ],
    //考勤申诉设置
    flowTypes: [
        { value: 1, label: '自选' },
        { value: 2, label: '预设' },
    ],
    //考勤方式
    attendanceTypes: [
        { value: 1, label: '固定班制' },
        { value: 2, label: '排班班制' },
    ],
    workOvertimeRules: [
        { value: 1, label: '记为调休' },
        { value: 2, label: '记为出勤' },
    ],
    fieldCardAssignedPlace: [
        { value: 1, label: '不限地点打卡' },
        { value: 2, label: '指定地点打卡' },
    ],
    onOffShiftTime: [
        { value: 1, label: '按正常上/下班时间' },
        { value: 2, label: '满规定时间即可' },
    ],
    rangeValue: [
        { value: 1, label: '100米内' },
        { value: 2, label: '200米内' },
        { value: 3, label: '300米内' },
        { value: 4, label: '500米内' },
        { value: 5, label: '1公里内' },
        { value: 6, label: '2公里内' },
        { value: 7, label: '3公里内' },
        { value: 8, label: '5公里内' },
    ],
    execptionSettingTypes: [{
            setable: false,
            attr: {
                prop: "Opt",
                label: "操作",
                fixed: 'left'
            },
            slot: true
        },
        {
            setable: false,
            attr: {
                prop: "Idx",
                label: "序号",
                fixed: 'left'
            },
            slot: true
        },
        {
            setable: false, //不可设置的列
            attr: {
                prop: "Name",
                label: "姓名",
                showOverflowTooltip: true,
                fixed: 'left'
            },
        },
        {
            setable: false,
            attr: {
                prop: "DepartmentName",
                label: "主要部门",
                showOverflowTooltip: true,
                fixed: 'left',
                width: 160,
            },
        },
        {
          setable: false,
          attr: {
              prop: "DepartmentNameList",
              label: "其它部门",
              showOverflowTooltip: true,
              fixed: 'left',
              width: 160,
          },
          slot: true
        },

        // {
        //     attr: {
        //         prop: "AttendanceConfirm",
        //         label: "考勤确认",
        //         showOverflowTooltip: true,
        //         fixed: 'left'
        //     },
        //     slot: true
        // },
        {
            attr: {
                prop: "DueAttendanceDay",
                label: "应出勤（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "ActualAttendance",
                label: "实际出勤（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "LateNumber",
                label: "迟到次数",
            },
            slot: true
        },
        {
            attr: {
                prop: "TotalLateHours",
                label: "总迟到时长（分钟）",
            },
            slot: true
        },
        {
            attr: {
                prop: "LeaveEarly",
                label: "早退（分钟）",
            },
            slot: true
        },
        {
            attr: {
                prop: "AbsentDay",
                label: "缺勤天数",
            },
            slot: true
        },
        {
            attr: {
                prop: "BusinessTrip",
                label: "出差（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "WorkingOvertime",
                label: "工作日加班（次）",
            },
            slot: true
        },
        {
            attr: {
                prop: "RestdaysWorkOvertime",
                label: "加班（天）", //休息日
            },
            slot: true
        },
        {
            attr: {
                prop: "CasualLeave",
                label: "事假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "WorkToRest",
                label: "调休（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "SickLeave",
                label: "病假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "AnnualVacation",
                label: "年假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "MaternityLeave",
                label: "产假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "BreastfeedingLeave",
                label: "哺乳假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "PaternityLeave",
                label: "陪产假（天",
            },
            slot: true
        },
        {
            attr: {
                prop: "MarriageLeave",
                label: "婚假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "FuneralLeave",
                label: "丧假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "PrenatalCheckLeave",
                label: "产检假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "ParentalLeave",
                label: "育儿假（天）",
            },
            slot: true
        },
    ]

}