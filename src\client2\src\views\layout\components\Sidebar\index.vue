<template>
  <div :class="isCollapse ? 'isCollapse' : ''">
    <!-- <div class="sidebar-logo">
      <router-link to="/">
        <img class="user-avatar" :src="logo"><span class="sidebar-company-name">{{ websiteTitle }}</span>
      </router-link>
    </div> -->

    <div class="log-wrapper" :class="sidebar.opened ? 'isShow' : ''">
        <div v-if="isExperience" class="exp-mask">体验版</div>
        <div style="width:100%;text-align: center; display: flex; align-items: center; justify-content: center;">
            <img v-show="!sidebar.opened" src="@/assets/logox2.png" style="width: 42px; height: 42px;">
            <div v-show="sidebar.opened" style="margin-left: 10px; flex-shrink: 0; padding-top: 6px; padding-right: 20px;">
                <img style="margin-bottom: 5px; width: 108px; height: 42px;" src="@/assets/logo-textx2.png">
            </div>
        </div>
    </div>
    <el-scrollbar ref='scrollbarRef' wrapClass="scrollbar-wrapper" style="height: calc(100% - 94px);">
      <el-menu mode="vertical" :show-timeout="200" :default-active="$route.path" :collapse="isCollapse" close="menu" text-color="#bfcbd9" active-text-color="#409EFF">
        <sidebar-item v-for="route in menus" :key="route.name" :item="route" :base-path="route.path"></sidebar-item>

      </el-menu>
    </el-scrollbar>
 
    <!-- <el-tooltip popper-class="white_Bg" class="item" effect="light" trigger="click" placement="top-start" :disabled="!!!versionRemarks">
      <div slot="content" class="version_pre">
        <pre>{{versionRemarks}}</pre>
      </div>
    </el-tooltip> -->
    
    <div style="height: 30px; display: flex; align-items: center; justify-content: center;">
      <!-- <el-popover
        placement="top-end"
        width="200"
        trigger="hover"
        content="">
        <pre style="margin: 0;">{{versionRemarks}}</pre>
        <div slot="reference" class="version_text">{{versionNumber}}</div>
      </el-popover> -->

      
      <div class="version_text">
        <a href="javascript:void(0);" @click="handleNav">{{ versionNumber }}</a>
        <!-- <app-table-row-button :text='versionNumber' @click="handleNav"></app-table-row-button>   -->
      </div>
    </div>




  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import { websitEnv } from '@/utils/env'
import { getUserInfo } from "@/utils/auth";
import * as VersionMgmtsManagement from "@/api/systemManagement/versionMgmtsManagement";

export default {
  components: { SidebarItem },
  created() {
    this.queryNewVersionDetails();
  },
  data() {
    return {
      websiteTitle: websitEnv.websiteTitle,
      // logo: require('@/assets/logo.png?imageView2/1/w/80/h/80')
      versionNumber: '',
      versionRemarks: '',
    }
  },
  watch: {
    currentMenuGroupName: {
      handler(val, oldVal) {
        if (val == '首页') {
          this.$router.push('/dashboard')
        }
        let currentRoute = this.$route
        let currentRoutePath = currentRoute.path.toLowerCase()
        //忽略路由参数匹配路由
        /**
         * 使之可以从当前地址：
         * /xxx/yyy/ccc8a6d0-3e86-465e-a255-4bf4e3735b79
         * 匹配到路由
         * /xxx/yyy/:id
         */
        if(currentRoute.params) {
          for (const key in currentRoute.params) {
            if (currentRoute.params.hasOwnProperty(key)) {
              const element = currentRoute.params[key];
              currentRoutePath = currentRoutePath.replace(element, `:${key}`)
            }
          }
        }

        let currentRouteObj = null
        for (let i = 0; i < this.menus.length; i++) {
          let currGroup = this.menus[i]
          if (currGroup.path.toLowerCase() == currentRoutePath) {
            currentRouteObj = currGroup
          }

          if (currGroup.children && currGroup.children.length > 0) {
            for (let j = 0; j < currGroup.children.length; j++) {
              if (currGroup.children[j].path && currGroup.children[j].path.toLowerCase() == currentRoutePath) {
                currentRouteObj = currGroup.children[j]
              }
            }
          }
        }

        if (currentRouteObj) {
          this.$router.push(currentRouteObj.fullpath)
        } else {
          let tempMenus = this.menus.filter(s => !s.hidden)


          if (tempMenus && tempMenus.length > 0) {
            let firstGroup = tempMenus[0]

            //补丁：由于支持扩展路由，可能导致获取到的路由第一项为详细页面（路由path以":id"结尾——所以要过滤屌）
            if(firstGroup && firstGroup.children) {
              firstGroup.children = firstGroup.children.filter(s => s.path.indexOf(':id') == -1)
            }
            
            let firstMenu = firstGroup
            if (firstGroup && firstGroup.children && firstGroup.children.length > 0) {
              firstMenu = firstGroup.children[0]
            }
            
            this.$router.push(firstMenu.path)
          }
        }
      },
      deep: true
    }
  },
  computed: {
    ...mapGetters(["sidebar", 'permission_routers', 'currentMenuGroupName']),
    //是否为体验版账号
    isExperience() {
      let userInfo = getUserInfo()
      return userInfo && userInfo.experience
    },
    menus() {
      let ms = this.permission_routers.filter(s => s.hidden === false && s.name == this.currentMenuGroupName)
      let children = []
      if (ms && ms.length > 0) {
        children = ms[0].children || []
        let mainPageIdx = children.findIndex(s => s.path.toLowerCase() == '/dashboard')
        //去掉首页（首页也用到了Layout，所有会有一级子菜单）
        if (mainPageIdx > -1) {
          children.splice(mainPageIdx, 1)
        }
      }
      // // 判断是否需要显示（打开）左侧导航——如果一级菜单下自由一个子菜单   或者   没有子菜单，则关闭菜单，否则打开
      // let isOpendMenus = (children.length == 1 && (!children[0].children || children[0].children && children[0].children.length == 0)) || children.length == 0
      // this.$store.dispatch('ToggleDevice', isOpendMenus ? 'mobile' : 'desktop')
      // if ((isOpendMenus && this.sidebar.opened) || (!(isOpendMenus) && !this.sidebar.opened)) {
      //   this.$store.dispatch("ToggleSideBar");
      // }

      //更新左侧导航菜单滚动条（没有超出高度，需要隐藏滚动条）
      this.$nextTick(() => {
        this.$refs.scrollbarRef && this.$refs.scrollbarRef.update()
      })
      return children
    },
    routes() {
      return this.$router.options.routes
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  methods: {
    handleNav() {
      this.$router.push({path: '/version'})
    },
    queryNewVersionDetails(){
        VersionMgmtsManagement.GetNewVersionDetails().then((res)=>{
            this.versionNumber = res.Name || '';
            this.versionRemarks = res.Remarks || '';
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.sidebar-logo {
  /* width: 180px; */
  text-align: center;
  height: 64px;
  line-height: 64px;
  cursor: pointer;
  /* float: left; */
  /* color: white; */
  color: #ffffff;
  font-size: 14px;
}

.user-avatar {
  width: 30px;
  height: 30px;
  vertical-align: middle;
  margin-right: 5px;
}

.openSidebar .sidebar-company-name {
  display: auto;
}

.hideSidebar .sidebar-company-name {
  display: none;
}


.isCollapse >>> .el-tooltip{
  font-size: 16px!important;
}


.version_text{
    // width: 100%;
    // text-align: center;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;
    color: rgba($color: #fff, $alpha: 0.5);
    a:hover{
      color: rgba($color: #fff, $alpha: 0.7);
      text-decoration: underline;
    }
}
.version_pre{
    width: calc(100% + 10px);
    margin-right: -10px;
    padding-right: 10px;
    max-height: 600px;
    overflow: hidden;
    overflow-y: auto
}

.log-wrapper{
    position: relative;
    background: #294983;
    color: #fff;
    width:64px; height: 64px; display: flex; align-items: center; justify-content: center;
    transition: all .28s;
    .exp-mask{
        position: absolute;
        top: 0;
        right: 0;
        bottom: -1px;
        left: 0;
        background: #0078ff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
    }
}

.isShow{
    width: 180px;
}
</style>
