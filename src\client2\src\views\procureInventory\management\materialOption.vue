<template>
    <div class="opt-wrapper">
        <div class="omit w-100" :title="opt.FNumber">{{ opt.FNumber }}</div>
        <div class="omit flex-1" style="flex: 4;" :title="opt.FName">{{ opt.FName }}</div>
        <div class="omit flex-1" style="flex: 6;" :title="opt.FSpecification">{{ opt.FSpecification }}</div>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        // {
        // "FMATERIALID": 100716,
        // "FDocumentStatus": "C",
        // "FForbidStatus": "A",
        // "FName": "十字槽沉头螺钉M5X16",
        // "FNumber": "01.0102031",
        // "FSpecification": "M5X16",
        // "value": "01.0102031"
        // } 
        opt: {
            type: Object,
            required: true
        },
    },
    data() {
        return {

        }
    },
}
</script>

<style lang="scss" scoped>
.opt-wrapper{
    display: flex;
    >div{
        flex-shrink: 0;
    }

    .w-100{
        width: 100px;
    }
    .w-80{
        width: 80px;
    }
    .flex-1{
        flex: 1;
    }
}
</style>