<template>
    <div>
        <app-dialog :title="title" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="820">
            <template slot="body">
                <el-form
                    :rules="rules"
                    ref="formData"
                    :model="formData"
                    label-position="right"
                    label-width="110px"
                >
                    <div class="wrapper" v-loading='loading'>
                        <el-form-item label="所属分类" prop="ClassifyId">
                            <treeselect :disabled="editableClassify" class="treeselect-common" :normalizer="normalizer" :options="computedClassifyList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择所属分类" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"></treeselect>
                        </el-form-item>
                        <el-form-item label="流程名称" prop="ApprovalName">
                            <el-input maxlength="20" :disabled="editable" v-model="formData.ApprovalName"></el-input>
                        </el-form-item>
                        <el-form-item label="流程类型" prop="ApprovalType">
                            <el-select :disabled="dialogStatus != 'create' && !leaveShow" v-model="formData.ApprovalType" placeholder="">
                                <el-option v-for="item in processTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="适用范围" prop="ApprovalRange">
                            <div class="cl">
                                <el-select :disabled="editable" class="fl" style="margin-right:10px;" v-model="formData.ApprovalRange" placeholder="" @change="handleChangeRange">
                                    <el-option v-for="item in scopeApplication" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <div class="fl" v-if="formData.ApprovalRange == 2">
                                    <el-button :disabled="editable" type="text" @click="handleShowTree">选择</el-button>
                                    <!-- <span v-if="departName">{{departName}}</span> -->
                                </div>
                                
                                <emp-selector
                                    class="fl"
                                    style="width:500px;"
                                    v-if="formData.ApprovalRange == 3"
                                    :readonly="editable"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :beforeConfirm='handlePrincipalEmployeeBeforeConfirm'
                                    :list="formData.ApprovalEmployeeIds"
                                    @change="handleChangePrincipalUsers"
                                ></emp-selector>
                            </div>
                            <ul v-if="formData.ApprovalRange == 2 && departName && departName.length>0" class="dUl">
                                <li class="omit" :title="dn" v-for="(dn,dnI) in departName" :key="dnI">{{dn}}</li>
                            </ul>
                        </el-form-item>
                        <el-form-item label="备注" prop="Remark">
                            <el-input maxlength="200" :rows="3" type="textarea" :disabled="editable" v-model="formData.Remark"></el-input>
                        </el-form-item>
                        <el-form-item label="审批流程设置" prop="ApprovalSet">
                            <el-radio-group v-model="formData.ApprovalSet" :disabled="editable" @change="handleRadioChange">
                                <el-radio :label="ps.label" v-for="(ps,psI) in processSet" :key="psI">{{ps.value}}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>
                </el-form>
                <div v-if="formData.ApprovalSet != 1">
                    <!-- <div class="panel-title">审批</div> -->
                    <div>
                        <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                        <approval-panel v-else ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <el-button @click="handleClose" v-show="dialogStatus == 'detail'">关闭</el-button>
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2" v-show="dialogStatus != 'detail'"></app-button>
                <!-- 确认 -->
                <app-button v-if="dialogStatus != 'detail'" @click="createData" :buttonType="1" v-show="!editable" :disabled="disabledBtn" style="margin-left:10px;"></app-button>
                <!-- <el-button @click="handleApproval"  type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval'">审批</el-button> -->
            </template>
        </app-dialog>
        <v-tree
            v-if="dialogTreeVisible"
            @saveSuccess="handleTreeSaveSuccess"
            @closeDialog="handleTreeCloseDialog"
            :dialogFormVisible="dialogTreeVisible"
            :checkedList='checkedList'>
        </v-tree>
    </div>
</template>

<script>
import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail'
import empSelector from "../../common/empSelector";
import * as approvalManagement from "@/api/approvalManagement";
import { vars } from '../../workbench/myWorkbench/vars'
import vTree from './tree';
import { getToken } from '@/utils/auth'
import {
    listToTreeSelect
} from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as classify from '@/api/classify';
export default {
    name: "vacate",
    directives: {},
    components: {
        empSelector,
        approvalPanel,
        approvalDetail,
        vTree,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "create" && this.dialogStatus != "update";
        },
        editableClassify() {
            // 流程设置 (修改)的时候 类型是基础流程  则禁用 所属分类选择
            if (this.dialogStatus === "update"&&this.formData.ApprovalCategory == 1) {
                return true;
            }
            return this.dialogStatus != "create" && this.dialogStatus != "update";
        },
        title() {
            if (this.dialogStatus == "create") {
                return "创建审批流程";
            } else if (this.dialogStatus == "update") {
                return "流程设置";
            } else if (this.dialogStatus == "detail") {
                return "流程详情";
            }else if(this.dialogStatus == "approval"){
                return "流程审批";
            }
            return "";
        },
        computedClassifyList(){
            // 创建的时候  和 流程设置(流程类型不为基础类型时)  过滤掉选项 基础
            if (this.dialogStatus == "create"||(this.dialogStatus == "update"&&this.formData.ApprovalCategory == 2)) {
                return this.ClassifyList.filter(s=>s.label!='基础')
            }
            return this.ClassifyList
        }
    },
    props: {
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        leaveShow:{
            type:Boolean,
            default:false
        },
        selectClassifyId: {
            type: String,
            default: ""
        },
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();
                this.getClassifyList();
                if (this.dialogStatus != "create" && this.id) {
                    this.loading=true;
                    systemDepartment.getListByCondition({}).then(response => { //调用公共组件API获取部门数据
                        this.loading=false;
                        let list = response.map(function (item) {
                            return {
                                Id: item.Id,
                                label: item.DepartmentName,
                                ParentId: item.ParentId,
                                ParentName:item.DepartmentName,
                            }
                        })
                        var orgstmp = JSON.parse(JSON.stringify(list));
                        var tempOrgsTree = listToTreeSelect(orgstmp, undefined, undefined, undefined, 'ParentName'); //将部门数据转换成树形结构
                        this.treeData=this.treeToArray(tempOrgsTree,'children');
                        this.getDetail();
                    })
                }
            }
        }
    },
    created() {
        
    },
    data() {
        var validateName = (rule, value, callback) => {
            // console.log(value,this.formData.ApprovalRangeDepartmentId)
            if (value == 2) {
                if(this.formData.ApprovalRangeDepartmentId.length<1){
                    callback(new Error("请选择部门!"));
                }else{
                    callback();
                }
            } else if (value == 3) {
                if(!this.formData.ApprovalEmployeeIds) this.formData.ApprovalEmployeeIds=[];
                if(this.formData.ApprovalEmployeeIds.length<1){
                    callback(new Error("请选择自定义人员!"));
                }else{
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            departName:'',
            checkedList:[],
            dialogTreeVisible:false,
            processTypes:vars.processTypes,
            scopeApplication:vars.scopeApplication,
            processSet:vars.processSet,
            imgsFilePath:[],
            imgHeaders:{token: getToken()},
            timeQuantum:vars.timeQuantum,
            disabledBtn: false,
            loading: false,
            rules: {
                ClassifyId: { required: true, message: '请选择审批流程分类', trigger: 'change' },
                ApprovalName: [{ required: true, message: '流程名称不能为空', trigger: 'blur' },],
                ApprovalType: [{ required: true, message: '请选择流程类型', trigger: 'blur' },],
                ApprovalRange: [
                    { required: true, message: '适用范围不能为空', trigger: 'change' },
                    { validator: validateName, trigger: "change" }
                ],
                ApprovalSet: [{ required: true, message: '请选择流程审批设置', trigger: 'blur' },],
            },
            formData: {
                ApprovalName:'',
                ApprovalType:1,
                ApprovalRange:1,
                ApprovalCategory: 2, // 1 基础流程， 2 自定义流程
                Remark: '',
                ApprovalRangeDepartmentId:[],
                ApprovalEmployeeIds:[],
                ApprovalSet:1,
                ClassifyId: null,
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
            },
            treeData:[],
            ClassifyList: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.label.split(",")[0],
                    id: node.Id,
                    children: node.children
                };
            },
        };
    },
    methods: {
        //获取分类下拉框
        getClassifyList() {
            classify.getListByCondition({ BusinessType: 7 }).then(res => {
                var departments = res.Items.map(function (item, index, input) {
                    return {
                        Id: item.Id,
                        label: item.Name,
                        ParentId: item.ParentId
                    };
                });
                this.ClassifyList = listToTreeSelect(departments);
                if (this.selectClassifyId) {
                    this.formData.ClassifyId = this.selectClassifyId;
                }
            }).catch(err => {
            });
        },
        handleRadioChange(d){
            if(d == 1){
                this.formData.Approval={//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                }
            }
        },
        handleShowTree(){
            this.dialogTreeVisible=true;
        },
        handleTreeCloseDialog(){
            this.dialogTreeVisible=false;
        },
        handleTreeSaveSuccess(d){
            this.checkedList=[];
            this.departName=[];
            this.formData.ApprovalRangeDepartmentId=[];
            if(d.length>0){
                d.forEach(v => {
                    this.formData.ApprovalRangeDepartmentId.push(v.Id);
                    this.checkedList.push(v.Id);
                    this.departName.push(v.ParentName);
                })
                // this.formData.ApprovalRangeDepartmentId=d[0].Id;
                // this.checkedList=d.map(v => v.Id);
                // this.departName=d[0].label;
                this.$refs.formData.validateField('ApprovalRange',(val) => {
                    return;
                }); 
            }
            this.dialogTreeVisible=false;
        },

        handleChangeRange(){

        },
        handleChangePrincipalUsers(users) {
            this.formData.ApprovalEmployeeIds = users;
        },
        handlePrincipalEmployeeBeforeConfirm(users) {
            if(users && users.length > 1000) {
            this.$message({
                message: '负责人不得超过1000人',
                type: 'error'
            })
            return false
            }
            return true
        },
        resetFormData() {
            this.checkedList=[];
            this.departName='';
            this.formData = {
                ApprovalName:'',
                ApprovalType:1,
                ApprovalRange:1,
                Remark: '',
                ApprovalRangeDepartmentId:[],
                ApprovalEmployeeIds:[],
                ApprovalSet:1,
                ClassifyId: null,
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
            };
        },
        createData() {
            this.disabledBtn=true;
            let validate = this.$refs.formData.validate()
            let approvalPanelValidate = new Promise((resolve, reject) => { resolve(true) });
            if(this.formData.ApprovalSet == 2){
                approvalPanelValidate = this.$refs.approvalPanel.validate()
            }
            Promise.all([validate, approvalPanelValidate]).then(valid => {
                if(this.formData.ApprovalSet == 2){
                    this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块
                    this.formData.Approval.ApprovalEmployeeIdList = this.formData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                    this.formData.Approval.CCEmployeeIdList = this.formData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                }
                if(this.formData.ApprovalRange == 3){
                    this.formData. ApprovalRangeEmployeeIds=[];
                    this.formData.ApprovalEmployeeIds.forEach(v => {
                       this.formData.ApprovalRangeEmployeeIds.push(v.EmployeeId);
                    })
                }

                let result=null;
                if(this.dialogStatus == 'create'){
                    result=approvalManagement.add(this.formData)
                }else{
                    result=approvalManagement.edit(this.formData)
                }
                result.then(res => {
                    this.loading=false;
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$emit('saveSuccess');
                    this.disabledBtn=false;
                }).catch(err => {
                    this.loading=false;
                    this.disabledBtn=false;
                })
                
            }).catch(e => {
                this.disabledBtn=false;
            })
        },
        getDetail() {
            this.loading = true
            approvalManagement.detail({ id: this.id }).then(res => {
                this.loading = false
                if(res.ApprovalRange == 3){
                    res.ApprovalEmployeeIds=res.ApprovalRangeEmployee;
                }else if(res.ApprovalRange == 2){
                    this.departName=[];
                    let a=null;
                    res.ApprovalRangeDepartmentIdList.forEach(v => {
                        a=this.treeData.find(s => s.Id == v);
                        this.departName.push(a.ParentName);
                    })
                    
                    this.checkedList=res.ApprovalRangeDepartmentIdList;
                }
                if(!res.Approval){
                    res.Approval={//审批信息
                        ApprovalEmployeeList: [[]],
                        ApprovalType: 1,
                        ApprovalOperatorEmployeeList: [], //已审批人员
                        NoApprovalEmployeeList: [], //未审批人员
                        CCEmployeeList: [], //抄送人
                        ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                        ApprovalState: 1, //1: 进行中; 2: 已完成
                        ApprovalResult: 1, //1: 通过； 2：不通过
                    }
                }
                this.formData = Object.assign({}, this.formData, res);
                this.formData.ApprovalRangeDepartmentId=res.ApprovalRangeDepartmentIdList;
            }).catch(err => {
                this.loading = false
            });
        },
        treeToArray(treeData, field) {
            var result = [];
            if(!field) field = "children";
            for(var key in treeData) {
                var obj = treeData[key];
                var clone = JSON.parse(JSON.stringify(obj));
                delete clone[field];
                result.push(clone);
                //
                if(obj[field]) {
                    var tmp = this.treeToArray(obj[field], field);
                    result = result.concat(tmp);
                }
            }
            return result;
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
        
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        
        
                        this.disabledBtn = true
                        //项目创建审批
                        change.createApproval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })

                }
            })
        },
    }
};
</script>

<style lang="scss" scoped>
.dUl{
    min-width:300px;
    max-width:500px;
    margin-top:10px;
    max-height: 350px;
    overflow-y: auto;
    border:1px solid #DCDFE6;
    padding: 0px 10px;
    border-radius: 4px;
}
</style>