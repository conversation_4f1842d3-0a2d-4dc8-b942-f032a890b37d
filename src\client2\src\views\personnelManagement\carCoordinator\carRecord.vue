<!--用车记录-->
<template>
  <div>
    <app-dialog title="用车记录" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :height="600" :width="1000">
      <template slot="body">

        <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false' :height="400">

          <template slot="CarServiceTime" slot-scope="scope">
            {{scope.row.CarServiceTime | dateFilter("YYYY-MM-DD HH:mm")}}
          </template>

          <template slot="CarReturnTime" slot-scope="scope">
            {{scope.row.CarReturnTime | dateFilter("YYYY-MM-DD HH:mm")}}
          </template>

          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="handleResetSearch" :layoutMode='layoutMode'>
              <template slot="KeyWords">
                <el-input style="width: 100%;" placeholder="搜索使用人/目的地" @clear='handleFilter' v-antiShake='{
                              time: 300,
                              callback: () => {
                                  handleFilter()
                              }
                          }' clearable v-model="listQuery.KeyWords"></el-input>
              </template>

              <template slot="CarServiceTimeList">
                <el-date-picker v-model="listQuery.CarServiceTimeList" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" style="width: 300px;" :clearable="false"></el-date-picker>
              </template>

              <template slot="CarReturnTimeList">
                <el-date-picker v-model="listQuery.CarReturnTimeList" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" style="width: 300px;" :clearable="false"></el-date-picker>
              </template>

            </app-table-form>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <el-popover placement="right" width="400" trigger="click">

              <el-card class="box-card detailCard">
                <div slot="header" class="clearfix">
                  <span>用车信息</span>
                </div>
                <div class="text item">
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">车牌号：</label>
                    </el-col>
                    <el-col :span="6">
                      {{obj.CarNumber}}
                    </el-col>
                    <el-col :span="6">
                      <label style="float: right;">司机：</label>
                    </el-col>
                    <el-col :span="6">
                      {{obj&&obj.DriverEmployee?obj.DriverEmployee.Name:"无"}}
                    </el-col>
                  </el-row>
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">用车时间：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.CarServiceTime}}
                    </el-col>
                  </el-row>
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">预计还车：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.CarReturnTimePlan}}
                    </el-col>
                  </el-row>
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">使用人：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.UserName}}
                    </el-col>
                  </el-row>
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">目的地：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.Destination}}
                    </el-col>
                  </el-row>
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">用车理由：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.UseReason}}
                    </el-col>
                  </el-row>
                </div>
              </el-card>

              <el-card class="box-card detailCard" style="margin-top:10px">
                <div slot="header" class="clearfix">
                  <span>还车信息</span>
                </div>
                <div class="text item">
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;">还车时间：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.CarReturnTime}}
                    </el-col>
                  </el-row>
                  <el-row class="detailRow">
                    <el-col :span="6">
                      <label style="float: right;"> 备注：</label>
                    </el-col>
                    <el-col :span="18">
                      {{scope.row.Remark}}
                    </el-col>
                  </el-row>
                </div>
              </el-card>

              <el-button slot="reference" type="text">详情</el-button>
            </el-popover>
          </template>
        </app-table>

        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />

      </template>
      <template slot="footer">
        <app-button @click="handleClose" text="关闭"></app-button>
      </template>
    </app-dialog>

  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as carRecordMgt from '@/api/personnelManagement/carCoordinatorRecord'

export default {
  mixins: [indexPageMixin],
  components: {},
  computed: {},
  filters: {},
  created() { },
  props: {
    obj: {
      type: Object,
      required: true
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.getList();
      } else {
        this.listQuery = {
          PageIndex: this.listQuery.PageIndex,
          PageSize: this.listQuery.PageSize,
          KeyWords: '',
          CarServiceTimeList: [],
          CarReturnTimeList: [],
        };
      }
    }
  },
  data() {

    return {
      labelWidth: "100px",
      layoutMode: 'simple',
      carRecordId: '',
      dialogLogStatus: 'detail',
      dialogLogFormVisible: false,
      total: 0,
      listLoading: false,
      listQuery: {
        PageIndex: 1,
        PageSize: 10,
        KeyWords: '',
        CarServiceTimeList: [],
        CarReturnTimeList: [],
      },

      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "CarServiceTimeList", label: "用车时间" },
        { prop: "CarReturnTimeList", label: "还车时间" },
      ],
      tabColumns: [
        {
          attr: {
            prop: "UserName",
            label: "使用人",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "CarServiceTime",
            label: "用车时间",
          }
        },
        {
          attr: {
            prop: "Destination",
            label: "目的地",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "UseReason",
            label: "用车理由",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "CarReturnTime",
            label: "还车时间",
          }
        },
        {
          attr: {
            prop: "TotalTime",
            label: "总时长",
          }
        },
      ],
      tabDatas: []
    };
  },
  methods: {

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //获取列表
    getList() {
      this.listLoading = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));

      postData.CarId = this.obj.Id;
      carRecordMgt.getList(postData).then(res => {
        this.listLoading = false;
        this.tabDatas = res.Items;
        this.total = res.Total;
      })
        .catch(err => {
          this.listLoading = false;
        });
    },

    handleReview(val) {
      this.carRecordId = val.Id;
      this.dialogLogFormVisible = true;
    },
    closeLogDialog() {
      this.dialogLogFormVisible = false;
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleResetSearch() {
      this.listQuery = {
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        KeyWords: '',
        CarServiceTimeList: [],
        CarReturnTimeList: [],
      };
      this.getList();
    },
  }
};
</script>

<style lang="scss" scoped>
.detailRow {
  width: 100%;
  padding-top: 5px;
  padding-bottom: 5px;
}
.detailCard {
  > label {
    font-weight: normal !important;
  }
}
</style>
