<template>
    <div>
        <div class="tab-title">周评分</div>
        <table class="tab">
            <thead>
                <tr>
                    <td v-if="!readonly"></td>
                    <td style="width: 50px;">序号</td>
                    <td style="width: 100px;">评分项</td>
                    <td style="width: 120px;">子评分项</td>
                    <td style="width: 450px;">规则说明</td>
                    <td style="width: 260px;">扣分</td>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(row, idx) in rateDatas" :key="idx">
                    <td v-if="!readonly">
                        <!-- {{ row[`${check_id}${row.RateId}`] }}{{ row.RateId }} -->
                        <el-checkbox v-model="row[`${check_id}${row.RateId}`]" @change="(val) => handleChange(val, row.RateId, 1)"></el-checkbox>
                    </td>
                    <td>{{ idx + 1 }}</td>
                    <td v-if="row.rowspan > 0" :rowspan="row.rowspan">{{ row.Title }}</td>
                    <td>{{ row.SubTitle }}</td>
                    <td>
                        <template v-for="ctrl in row.Desc">
                            <span v-if="ctrl.type == 'TEXT'">{{ ctrl.text }}</span>
                            <template v-else-if="ctrl.type == 'NUMBER'">
                                <el-input-number v-if="!readonly" style="margin: 2px 4px; width: 100px;" v-model="ctrl[ctrl.name]" label=""></el-input-number>
                                <span v-else>{{ ctrl[ctrl.name] }}</span>
                            </template>
                        </template>
                    </td>
                    <td>
                        <template v-for="(ctrl, idx2) in row.Controls">
                            <span v-if="ctrl.type == 'TEXT'">{{ ctrl.text }}</span>
                            <template v-else-if="ctrl.type == 'NUMBER'">
                                <el-input-number v-if="!readonly" style="margin: 2px 4px; width: 100px;" v-model="ctrl[ctrl.name]" label=""></el-input-number>
                                <span v-else>{{ ctrl[ctrl.name] }}<span v-if="idx2 < row.Controls.length - 1">、</span></span>
                            </template>
                        </template>
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="tab-title" style="margin-top: 20px;">月评分</div>
        <table class="tab">
            <thead>
                <tr>
                    <td v-if="!readonly"></td>
                    <td style="width: 50px;">序号</td>
                    <td style="width: 100px;">评分项</td>
                    <td style="width: 120px;">子评分项</td>
                    <td style="width: 450px;">规则说明</td>
                    <td style="width: 260px;">扣分</td>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(row, idx) in rateDatas2" :key="idx">
                    <td v-if="!readonly">
                        <!-- {{ row[`${check_id}${row.RateId}`] }}{{ row.RateId }} -->
                        <el-checkbox v-model="row[`${check_id}${row.RateId}`]" @change="(val) => handleChange(val, row.RateId, 2)"></el-checkbox>
                    </td>
                    <td>{{ idx + 1 }}</td>
                    <td v-if="row.rowspan > 0" :rowspan="row.rowspan">{{ row.Title }}</td>
                    <td>{{ row.SubTitle }}</td>
                    <td>
                        <template v-for="ctrl in row.Desc">
                            <span v-if="ctrl.type == 'TEXT'">{{ ctrl.text }}</span>
                            <template v-else-if="ctrl.type == 'NUMBER'">
                                <el-input-number v-if="!readonly" style="margin: 2px 4px; width: 100px;" v-model="ctrl[ctrl.name]" label=""></el-input-number>
                                <span v-else>{{ ctrl[ctrl.name] }}</span>
                            </template>
                        </template>
                    </td>
                    <td>
                        <template v-for="(ctrl, idx2) in row.Controls">
                            <span v-if="ctrl.type == 'TEXT'">{{ ctrl.text }}</span>
                            <template v-else-if="ctrl.type == 'NUMBER'">
                                <el-input-number v-if="!readonly" style="margin: 2px 4px; width: 100px;" v-model="ctrl[ctrl.name]" label=""></el-input-number>
                                <span v-else>{{ ctrl[ctrl.name] }}<span v-if="idx2 < row.Controls.length - 1">、</span></span>
                            </template>
                        </template>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>

import { vars } from '../common/vars'
export default {
    name: 'ruleTable',
    props: {
        readonly: {
            type: Boolean,
            default: false
        },
        values: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    created() {
        this.initDatas(1)
        this.initDatas(2)
    },
    watch: {
        values: {
            handler(val) {
                let list = val.filter(s => s.ScoringCycle == 1)
                this.backfillDatas((list || []), 1)
                let list2 = val.filter(s => s.ScoringCycle == 2)
                this.backfillDatas((list2 || []), 2)
            }
        }
    },
    data() {
        return {
            check_id: 'check_id_',
            rateDatas: [],
            rateDatas2: [],

        }
    },
    methods: {
        //获取选中行（以及填写）的值
        getDatas() {
            let datas = this.rateDatas.filter(s => s[`${this.check_id}${s.RateId}`])
            let result = datas.map(s => {
                return {
                    RateId: s.RateId,
                    DescValueStr: s.Desc.filter(c => c.type == 'NUMBER').map(c => c[c.name]).join('_'),
                    ControlsValueStr: s.Controls.filter(c => c.type == 'NUMBER').map(c => c[c.name]).join('_'),
                    ScoringCycle: s.ScoringCycle
                }
            })

            let datas2 = this.rateDatas2.filter(s => s[`${this.check_id}${s.RateId}`])
            let result2 = datas2.map(s => {
                return {
                    RateId: s.RateId,
                    DescValueStr: s.Desc.filter(c => c.type == 'NUMBER').map(c => c[c.name]).join('_'),
                    ControlsValueStr: s.Controls.filter(c => c.type == 'NUMBER').map(c => c[c.name]).join('_'),
                    ScoringCycle: s.ScoringCycle
                }
            })
            return result.concat(result2)
        },
        //初始化表格
        initDatas(scoringCycleType) {
            let enums = scoringCycleType == 1 ? vars.rateEnum : vars.rateEnum2
            let datas = JSON.parse(JSON.stringify(enums))

            //转成父子关系
            let tempList = datas.filter(s => !s.ParentId).map(s => {
                s.Children = []
                return s
            })
            datas.filter(s => s.ParentId).forEach(item => {
                let exists = tempList.find(s => s.RateId == item.ParentId)
                if(exists) {
                    exists.Children.push(item)
                }
            })

            //转成正常“行”，计算跨行
            let rows = []
            tempList.forEach(g => {
                if(g.Children.length == 0) {
                    g[`${this.check_id}${g.RateId}`] = false
                    g.SubTitle = ''
                    g.rowspan = 1
                    rows.push(g)
                }else{
                    g.Children.forEach((sub, idx) => {
                        sub[`${this.check_id}${sub.RateId}`] = false
                        sub.SubTitle= sub.Title
                        sub.Title = g.Title
                        sub.rowspan = idx == 0 ? g.Children.length : -1
                        rows.push(sub)
                    })
                }
            })

            //规则说明、扣分 转成数组，转成“控件”类型，方便视图展示
            rows.forEach(r => {
                let ctrls = r.Desc.split(/{|}/)
                let temp = []
                let flag = -1
                ctrls.forEach(c => {
                    if(c == 'NUMBER') {
                        let target = {type: 'NUMBER'}
                        flag++
                        let name = `Desc_${r.RateId}_${flag}`
                        target.name = name
                        target[name] = '' //为控件取名，用来绑定模型属性
                        temp.push(target)
                    }else{
                        temp.push({type: 'TEXT', text: c})
                    }
                })
                r.Desc = temp

                temp = []
                flag = -1
                r.Controls.forEach(c => {
                    if(c.type == 'NUMBER') {
                        let target = {type: 'NUMBER'}
                        flag++
                        let name = `Controls_${r.RateId}_${flag}`
                        target.name = name
                        target[name] = '' //为控件取名，用来绑定模型属性
                        temp.push(target)
                    }else{
                        temp.push(c)
                    }
                })
                r.Controls = temp
            })

            if(scoringCycleType == 1) {
                this.rateDatas = rows
            }else{
                this.rateDatas2 = rows
            }
        },
        //还原用户填写的数据
        backfillDatas(list, scoringCycleType) {
            if(list && list.length > 0) {
                list.forEach(old => {
                    let row = this[scoringCycleType == 1 ? 'rateDatas' : 'rateDatas2'].find(s => s.RateId == old.RateId)
                    if(row) {
                        row[`${this.check_id}${old.RateId}`] = true

                        //规则说明 列 值还原
                        if(old.DescValueStr !== '' && old.DescValueStr !== null) {
                            let descVals = old.DescValueStr.indexOf('_') > -1 ? old.DescValueStr.split('_') : [old.DescValueStr]
                            descVals.forEach((v, idx) => {
                                let ctrlName = `Desc_${old.RateId}_${idx}`
                                let ctrl = row.Desc.find(c => c.name == ctrlName)
                                if(ctrl) {
                                    ctrl[ctrlName] = parseInt(descVals[idx])
                                }
                            })
                        }

                        //扣分 列 值还原
                        if(old.ControlsValueStr !== '' && old.ControlsValueStr !== null) {
                            let descVals = old.ControlsValueStr.indexOf('_') > -1 ? old.ControlsValueStr.split('_') : [old.ControlsValueStr]
                            descVals.forEach((v, idx) => {
                                let ctrlName = `Controls_${old.RateId}_${idx}`
                                let ctrl = row.Controls.find(c => c.name == ctrlName)
                                if(ctrl) {
                                    ctrl[ctrlName] = parseInt(descVals[idx])
                                }
                            })
                        }

                    }
                })
            }
        },
        //同组的选项必须同时选中或不选中
        handleChange(checked, rateId, scoringCycleType) {
            let list = scoringCycleType == 1 ? this.rateDatas : this.rateDatas2
            let enums = vars[scoringCycleType == 1 ? 'rateEnum' : 'rateEnum2']
            let checkedObj = enums.find(s => s.RateId == rateId)
            if(checkedObj && checkedObj.ParentId) {
                let pid = checkedObj.ParentId
                let brotherList = enums.filter(s => s.ParentId == pid)

                brotherList.forEach(b => {
                    let obj = list.find(s => s.RateId == b.RateId)
                    if(obj) {
                        obj[`${this.check_id}${obj.RateId}`] = checked
                    }
                })
            }
        },
    },
}
</script>

<style lang="scss" scoped>

.tab{
    border-collapse: collapse;
    thead{
        color: #909399;
    }
    td{
        border: 1px solid #EBEEF5;
        text-align: left;
        box-sizing: border-box;
        padding: 6px 10px;
    }
}

.tab-title{
    border-left: 4px solid #409EFF;
    padding-left: 10px;
    margin-bottom: 10px;
    font-weight: bold;
}
</style>