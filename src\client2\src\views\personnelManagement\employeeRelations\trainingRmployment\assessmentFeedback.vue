<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
 
                <el-form ref="formData"  
                  :model="formData"
                  :rules="rules"
                  label-position="right" 
                  label-width="110px">

                    <el-row class="wrapper" v-loading='loading'  v-for="(item, idx) in formData.list" :key="idx">
                        <el-card style="margin-top:20px;margin-bottom:20px;" shadow="hover">
                         <el-col :span="24">
                           <h3>{{formData.list[idx].ReportTime | dateFilter('YYYY-MM-DD HH:mm')}} 月度汇报</h3>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="考核结果反馈" :prop="'list.' + idx + '.ReportFeedbackContent'" :rules="{required: true, message: '考核结果反馈不能为空', trigger: 'blur'}">
                                <el-input :disabled="!editable" maxlength="2000" type="textarea" :rows="4" v-model="formData.list[idx].ReportFeedbackContent"></el-input>
                            </el-form-item>
                        </el-col>
                         <el-col :span="24">
                            <el-form-item label="改进意见" :prop="'list.' + idx + '.Improvements'" :rules="{required: true, message: '改进意见不能为空', trigger: 'blur'}">
                                <el-input :disabled="!editable" maxlength="2000" type="textarea" :rows="4" v-model="formData.list[idx].Improvements"></el-input>
                            </el-form-item>
                        </el-col>
                        </el-card>
                    </el-row>

                </el-form>

            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import empSelector from '@/views/common/empSelector'
import * as EmployeeTrainingPlanApi from "@/api/personnelManagement/EmployeeTrainingPlan";
import * as EmployeeReportFeedbackApi from "@/api/personnelManagement/EmployeeReportFeedback";
import { getUserInfo } from "@/utils/auth";
export default {
    name: "evaluatePage",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "考核反馈";
            } else if (this.dialogStatus == "update") {
                return "更改考核反馈";
            } else if (this.dialogStatus == "detail") {
                return "考核反馈详情";
            }
            return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
            default: "create",
        },
        row: {
            type: Object,
            required: true
        },
        employeeId: {
            type: String,
            default: "",
        },

        
    },
    watch: {
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {},
            formData: {
                list: []
            },
        };
    },
    methods: {
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                let postData = JSON.parse(JSON.stringify(self.formData));

                let data =  postData.list.map(s=>{
                        return {
                              employeeReportId:s.Id,
                              reportFeedbackContent:s.ReportFeedbackContent,
                              improvements:s.Improvements,
                        }
                    });

                    self.disabledBtn = true;
                    let result = null;
                    if (self.dialogStatus == 'create') {
                        result = EmployeeReportFeedbackApi.addOrEditList(data)
                    }
                    // if (self.dialogStatus == 'update') {
                    //     result = EmployeeReportFeedbackApi.edit(data)
                    // }
                    result.then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            EmployeeTrainingPlanApi.getReportFeedbacks({ id: this.employeeId }).then(res => {

             
                this.formData.list = res.map(m =>{
                    let temp = m.EmployeeReportFeedback || {}
                    return {
                        ReportFeedbackId: temp.Id,
                        ReportFeedbackContent: temp.ReportFeedbackContent,
                        Improvements: temp.Improvements,
                        ReportTime: m.ReportTime,
                        Id:m.Id,
                    }
                    // m.EmployeeReportFeedback = m.EmployeeReportFeedback || {
                    //       Id: "",
                    //       ReportFeedbackContent: "",
                    //       Improvements: "",
                    // }

                    // return m
                })

                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    },
    mounted(){
        this.getDetail();
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
}
</style>