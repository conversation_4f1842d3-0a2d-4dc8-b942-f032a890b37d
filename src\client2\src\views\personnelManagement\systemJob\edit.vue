<!--客服编辑-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog title="分类调整" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
      <template slot="body" v-loading='loading'>
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="100px"
        >
          <el-form-item label="职位分类" prop="CategoryId">
              <el-select v-model="formModel.CategoryId" filterable placeholder="" clearable style="width: 100%;">
                  <el-option v-for="item in categories" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
          </el-form-item>


        </el-form>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */

import * as systemJob from '@/api/personnelManagement/systemJob'

export default {
  /**名称 */
  name: "systemJob-setting-edit",
  /**组件声明 */
  components: {
  },
  /**参数区 */
  props: {
    /**客服Id */
    jobIds: {
      type: Array
    }
  },
  /**数据区 */
  data() {
    return {
      curr: [ { "JobId": "a2524962-333b-4d85-bb17-13263df83648", "Name": "机械工程师" } ],

      loading:false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**区域列表 */
      categories: [],
      /**表单模型 */
      formModel: { CategoryId: null, JobIds: [] },

      /**表单规则 */
      formRules: {
        CategoryId: { fieldName: "职位分类", rules: [{ required: true }] }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {},
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        this.formModel = {
          JobIds: this.jobIds,
          CategoryId: null
        };
        this.getCategories();
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    getCategories() {
        let paramData = {
            PageSize: 1000,
            PageIndex: 1
        };
        systemJob.getCategoryListPage(paramData).then(response => {
            this.categories = response.Items.map(s => {
                return {
                    value: s.CategoryId,
                    label: s.CategoryName
                }
            }) || []
            
        })
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;

          result = systemJob.adjustmentCategory(
            this.formModel
          );

          result
            .then(response => {
              _this.buttonLoading = false;
              _this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


