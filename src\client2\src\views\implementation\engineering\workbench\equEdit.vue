<template>
  <div>
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='600'
      :maxHeight="600"
    >
      <template slot="body">
        <div class="wrapper" v-loading="loading">
          <div style="min-height: 400px;">
            <!-- <common-change-normar
              ref="commonChange"
              :dialogStatus="dialogStatus"
              :commonChangeDetail="commonChangeDetail"
              v-loading="loading"
              :isOnlyViewDetail="isOnlyViewDetail"
            >
            </common-change-normar> -->
            <el-form
            :rules="rules"
            ref="formData"
            :model="formData"
            label-position="right"
            label-width="100px"
            >
            <el-row>
                <el-col :span="24">
                <el-form-item label="设备名称" prop="Name">
                    <el-input :disabled="!editable" maxlength="50" v-model="formData.Name"></el-input>
                </el-form-item>
                </el-col>
                <el-col :span="24">
                <el-form-item label="类型" prop="ProductListManagementId">
                    <treeselect
                    :disabled="!editable"
                    :normalizer="normalizer2"
                    key="type2"
                    v-model="formData.ProductListManagementId"
                    :zIndex="99999999"
                    :default-expand-level="3"
                    :options="products"
                    :multiple="false"
                    placeholder
                    :show-count="true"
                    :noResultsText="noResultsTextOfSelTree"
                    :noOptionsText="noOptionsTextOfSelTree"
                    >
                    <label
                        :title="node.label"
                        slot="option-label"
                        slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                        :class="labelClassName"
                    >
                        <span>
                        {{ node.label }}
                        <span
                            v-if="shouldShowCount"
                            :class="countClassName"
                        >({{ count }})</span>
                        </span>
                    </label>
                    </treeselect>
                </el-form-item>
                </el-col>
                <el-col :span="24">
                <el-form-item label="备注" prop="Remark">
                    <el-input :disabled="!editable" maxlength="2000" v-model="formData.Remark"></el-input>
                </el-form-item>
                </el-col>
            </el-row>
            </el-form>
          </div>
        </div>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>

      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as change from "@/api/projectDev/projectMgmt/change";

import * as impMgmt from "@/api/implementation/impManagement2";
import commonChangeNormar from "../../../projectDev/projectMgmt/workbench/common/changeNormar";
import * as prodMgmt from "@/api/systemManagement/productListManagement";
// import createBody from './createBody'
import { vars } from "../../../projectDev/common/vars";
import { listToTreeSelect } from "@/utils";

export default {
  name: "contract-change",
  components: {
    commonChangeNormar
    // createBody,
  },
  props: {
    specialPageTitle: {
        type: String
    },
    //开始、结束操作弹框
    dialogStatus: {
      type: String,
      default: "create"
    },
    equId: {
      type: String,
      default: ""
    },

  },
  computed: {
    pageTitle() {
      if(this.specialPageTitle) {
        return this.specialPageTitle
      }
      if (this.dialogStatus == "create") {
        return "实施设备变更";
      } else if (this.dialogStatus == "update") {
        return "编辑设备";
      } else if (this.dialogStatus == "detail") {
        return "详情";
      } else if (this.dialogStatus == "approval") {
        return "变更审批";
      }
    },
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "approval" && this.dialogStatus != "detail";
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        //新增里程碑变更（获取里程碑详情）
        if (val) {
          if (this.equId) {
            this.getEquDetail();
          } 
        }
      },
      immediate: true
    }
    // row: {
    //     handler(var) {
    //     }
    //     // handler (newval) {
    //     //     this.initFiles(newval)
    //     // },
    //     // deep: true
    // }
  },
  filters: {},
  created() {
    this.getProductList();
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      products: [],
      normalizer2(node) {
        return {
          id: node.Id,
          label: node.ProductName,
          children: node.children
        };
      },
      loading: false,
      disabledBtn: false,
      rules: {
        Name: { fieldName: "设备名称", rules: [{ required: true }] },
        ProductListManagementId: {
          fieldName: "类型",
          rules: [{ required: true }]
        },
        // Remark: { fieldName: "备注", rules: [{ required: true }] }
      },
      formData: {
        Id: '',
        Name: "", //设备名称
        ProductListManagementId: undefined, //类型
        Remark: "" //备注
      },
    //   commonChangeDetail: {
    //     ChangeEditRequestModel: {
    //       ChangeReason: "", //变更原因
    //       Approval: {
    //         //审批对象
    //         ApprovalEmployeeIdList: [[]],
    //         ApprovalType: 1,
    //         CCEmployeeIdList: [],
    //         ApprovalState: 1, //1: 进行中; 2: 已完成
    //         ApprovalResult: 1 //1: 通过； 2：不通过
    //       }
    //     }
    //   }
    };
  },
  methods: {
    getProductList() {
      prodMgmt.getListByCondition({}).then(res => {
        this.products = listToTreeSelect(res);
      });
    },
    createData() {
      let createBodyValid = this.$refs.formData.validate();

      Promise.all([createBodyValid]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));

        this.disabledBtn = true;
        impMgmt
          .editEqu(postData)
          .then(res => {
            this.disabledBtn = false;
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.$refs.appDialogRef.createData();
          })
          .catch(err => {
            this.disabledBtn = false;
          });
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    //获取设备详情
    getEquDetail() {
      this.loading = true;
      impMgmt
        .getImpEquDetails({ id: this.equId })
        .then(res => {
          this.loading = false;
          this.formData = {
            Id: this.equId,
            Name: res.Name, //设备名称
            ProductListManagementId: res.ProductListManagementId, //类型
            Remark: res.Remark //备注
          }
        //   this.formData = Object.assign({}, this.formData, res);
        })
        .catch(err => {
          this.loading = false;
        });
    },

  }
};
</script>


<style lang="scss" scoped>
// .wrapper{
//     display: flex;
//     .left{
//         flex: 1;
//         padding-right: 14px;
//     }
//     .right{
//         width: 20%;
//     }
// }

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>