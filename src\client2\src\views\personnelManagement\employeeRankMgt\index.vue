<template>
    <div class="app-container">
        <div class="bg-white">
            <div class="pageWrapper">
                <div class="product-list">
                    <div class="treeBox">
                        <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                        <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node }">
                                <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                            </span>
                        </el-tree>
                    </div>
                </div>
                <div class="content-wrapper __dynamicTabContentWrapper">
                    <div class="content __dynamicTabWrapper">
                        <app-table ref="mainTable"
                        :tab-columns="tabColumns" :tab-datas="tabDatas" :optColWidth="110"
                        :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true"
                        :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode'
                        :isShowBtnsArea='false'>
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="KeyWords">
                                    <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索员工姓名/工号"></el-input>
                                </template>
                                <template slot='EmployeeLevelType'>
                                    <el-select style="width:100%;" v-model="listQuery.EmployeeLevelType" clearable placeholder="请选择">
                                        <el-option v-for="item in leveEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template>
                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                                </template>
                                </app-table-form>
                            </template>


                            <template slot="EmployeeLevelType" slot-scope="scope">
                                <!-- <user-leve-icon :number="scope.row.EmployeeLevelType" /> -->
                                T{{ scope.row.EmployeeLevelType }}
                            </template>
                            <template slot="AvatarPath" slot-scope="scope">
                                <!-- <div style="line-height: 40px;" v-viewer>
                                    <img class="userPhoto" :src="scope.row.AvatarPath||defAvatar" />
                                </div> -->

                                <div class="avatar-wrapper" v-viewer>
                                    <el-avatar fit='cover' :key="scope.row.EmployeesId" :size="50" :src="scope.row.AvatarPath || defavatar"></el-avatar>
                                </div>
                            </template>

                            <template slot="CreateTime" slot-scope="scope">
                                {{scope.row.CreateTime | dateFilter("YYYY-MM-DD HH:mm")}}
                            </template>

                            <template slot="CreateEmployee" slot-scope="scope">
                                <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
                            </template>
                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button @click="handleEdit(scope.row,'detail')" :type="2"></app-table-row-button>
                                <app-table-row-button v-if="rowBtnIsExists('btnAdjustApplication')" @click="hadnleAdjust(scope.row, 'update')" text="调整申请" :type="2"></app-table-row-button>
                            </template>

                        </app-table>
                    </div>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </div>
        </div>
        <!-- 选择人员 -->
        <emp-selector ref="selectorEmpSelector" key="btnAddSelector" :showType="3" :multiple="true"
            :beforeConfirm='handleAddEmpSelector' submit-text="下一步"
            :list="selectObj.EmployeeList" @change="handleChangeEmpSelector">
        </emp-selector>
        
        <!-- 调整详情 -->
        <batch-setLevel
            v-if="dialogBatchSetLevelVisible" :tab-data="selectObj.EmployeeList"
            @closeDialog="handleBatchSetLevelCloseDialog" @saveSuccess="handleBatchSetLevelSuccess"
            :dialogFormVisible="dialogBatchSetLevelVisible"></batch-setLevel>


        <!-- 职级详情 -->
        <rank-detail
            v-if="dialogRankDetailVisible"
            @closeDialog="dialogRankDetailVisible=false"
            :dialogFormVisible="dialogRankDetailVisible"
            :dialogStatus="dialogStatus"
            :employeeId="selectRow.EmployeesId"></rank-detail>

        <!-- 调整详情 -->
        <adjust-record
            v-if="dialogAdjustRecordVisible"
            @closeDialog="dialogAdjustRecordVisible=false"
            :dialogFormVisible="dialogAdjustRecordVisible"></adjust-record>



        <!-- 调整申请 -->
        <adjust-application
            v-if="dialogAdjustApplicationVisible"
            @closeDialog="dialogAdjustApplicationVisible=false"
            :dialogFormVisible="dialogAdjustApplicationVisible"
            :dialogStatus="dialogStatus" @saveSuccess="handleAdjustApplicationSuccess"
            :employeeId="selectRow.EmployeesId"></adjust-application>

            
        <!-- 职级说明管理 -->
        <leve-description
            v-if="dialogLeveDescriptionVisible"
            @closeDialog="dialogLeveDescriptionVisible=false" @saveSuccess="dialogLeveDescriptionVisible=false"
            :dialogFormVisible="dialogLeveDescriptionVisible"></leve-description>
        
    </div>
</template>

<script>
import {leveEnum} from "@/components/UserLeveIcon/enum.js";
import { listToTreeSelect } from "@/utils";
import indexPageMixin from "@/mixins/indexPage";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as systemEmployeeApi from "@/api/personnelManagement/systemEmployee";


import empSelector from '@/views/common/empSelector'


import noData from "@/views/common/components/noData"

import rankDetail from "./rankDetail"
import UserLeveIcon from "@/components/UserLeveIcon"

import adjustApplication from "./adjustApplication"
import adjustRecord from "./adjustRecord"
import batchSetLevel from "./batchSetLevel"
import leveDescription from "./leveDescription"


export default {
    name: "situation-index",
    mixins: [indexPageMixin],
    components: {
        UserLeveIcon,
        noData,
        rankDetail,
        adjustApplication,
        adjustRecord,
        empSelector,
        batchSetLevel,
        leveDescription,
    },
    props: {},
    filters: {
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {

    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.DepartmentId = val.Id;
                    // this.selectDepartmentName = val.DepartmentName;
                    this.handleFilter()
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getDepartments();
    },
    data() {
        return {
            leveEnum,
            defAvatar: require('../../../assets/images/avatar3.png'),
            epKeys: [],

            filterText: "",
            treeDatas: [],

            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                { prop: "EmployeeLevelType", label: "人员职级"},
                // { prop: "NoWorkArrange", label: "工作计划"},
            ],

            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            listLoading: false,
            listQuery: {
                WorkingState: 1, //  在职人员
            },
            tabDatas: [], //原始数据
            total: 0,
            /******************* 表格 *******************/
            layoutMode: 'simple',
            tabColumns: [
                { attr: { prop: "EmployeeLevelType", label: "员工职级"}, slot: true },
                { attr: { prop: "AvatarPath", label: "头像" }, slot: true },
                { attr: { prop: "Name", label: "姓名", } },
                { attr: { prop: "Number", label: "工号" } },
                { attr: { prop: "DepartmentName", label: "部门", showOverflowTooltip: true } },
                { attr: { prop: "JobName", label: "职位", showOverflowTooltip: true } },
            ],



            selectRow: {},
            dialogStatus: '',

            dialogRankDetailVisible: false,
            dialogAdjustApplicationVisible: false,
            dialogAdjustRecordVisible: false,


            dialogBatchSetLevelVisible: false,
            selectObj: {
                EmployeeList: []
            },

            // 职级说明管理
            dialogLeveDescriptionVisible: false,
            
        };
    },
    methods: {
        // 选中负责人
        handleChangeEmpSelector(users) {
            this.selectObj.EmployeeList = users.map(s=>{
                s['OldEmployeeLevelType'] = JSON.parse(JSON.stringify(s)).EmployeeLevelType || 1
                s['NewEmployeeLevelType'] = JSON.parse(JSON.stringify(s)).EmployeeLevelType
                return s
            })
            this.dialogBatchSetLevelVisible = true;
        },
        // 负责人 数量限制
        handleAddEmpSelector(users) {
            if (users && users.length == 0) {
                this.$message({
                    message: '请选择人员',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 编辑/详情
        handleEdit(row, optType){
            this.selectRow = row;
            this.dialogStatus = optType;
            this.dialogRankDetailVisible = true;
        },
        // 调整申请
        hadnleAdjust(row, optType){
            this.selectRow = row;
            this.dialogStatus = optType;
            this.dialogAdjustApplicationVisible = true;
        },
        // 调整申请  确定
        handleAdjustApplicationSuccess(){
            this.getList()
            this.dialogAdjustApplicationVisible = false
        },
        // 初始职级设置 确定
        handleBatchSetLevelSuccess(){
            this.getList()
            this.$refs.selectorEmpSelector.handleClear()
            this.dialogBatchSetLevelVisible = false
        },
        // 初始职级设置 取消
        handleBatchSetLevelCloseDialog(){
            this.$refs.selectorEmpSelector.handleClear()
            this.dialogBatchSetLevelVisible = false
        },
        /**表头部点击 */
        onBtnClicked: function (type) {
            console.log(type)
            switch (type) {
                case "btnAdjustmentRecord":
                    // 调整记录
                    this.selectRow = {};
                    this.dialogStatus = '';
                    this.dialogAdjustRecordVisible = true;
                    break;
                case "btnInitialJobLevel":
                    // 	初始职级设置
                    this.$refs.selectorEmpSelector.handleShow()
                    break;
                case "btnLeveDescription":
                    // 	职级说明管理
                    this.selectRow = {};
                    this.dialogStatus = '';
                    this.dialogLeveDescriptionVisible = true;
                    break;

                
                default:
                break;
            }
        },

        //获取成员列表
        getList() {
            if (this.checkedNode) {
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                this.listLoading = true;
                systemEmployeeApi.getList(postData).then((res) => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
            }
        },
        onResetSearch() {
            this.listQuery = this.$options.data().listQuery;
            this.getList();
        },

        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            systemDepartment
                .getListByCondition(this.departmentListQuery)
                .then((res) => {
                this.treeDatas = listToTreeSelect(res);

                if (this.treeDatas && this.treeDatas.length > 0) {
                    this.treeDatas.forEach(v => {
                        this.epKeys.push(v.Id);
                    })
                }
                //如果首次加载问价夹树（没有选中），默认选中根节点
                if (!this.checkedNode) {
                    this.setDefaultChecked();
                }
                this.treeLoading = false;
            });
        },

        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },

    },
};
</script>


<style lang="scss" scoped>
.avatar-wrapper{
    width: 50px;
    height: 50px;
    /deep/img{
        width: 100%;
        height: 100%;
    }
}

// .userPhoto{
//     width: 40px;
//     height: 40px;
//     border-radius: 50%;
//     overflow: hidden;
//     float: left;
//     margin-right: 10px;
//     cursor: pointer;
// }
.treeBox {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        flex: 1;
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

.tag {
    display: inline-block;
    padding: 1px 4px;
    border-radius: 2px;
    color: #fff;
}


</style>
