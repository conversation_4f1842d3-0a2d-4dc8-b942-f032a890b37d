<!-- 差旅费报销弹窗 -->
<template>
  <app-dialog
    ref="appDialogRef"
    :width="1000"
    className="clear-padding"
    :title="title"
    v-bind="$attrs"
    v-on="$listeners"
    :beforeClose="beforeClose"
  >
    <div slot="body" class="body_wrapper" v-loading="loading">
      <div class="main_container">
        <el-form
          :model="formData.TravelExpenseObj"
          ref="formRef"
          :rules="rules"
          label-width="110px"
          style="padding-top: 0"
          class="form_container"
        >
          <div class="left_container">
            <el-form-item
              label="撤销原因"
              prop="RevocationCause"
              v-if="
                dialogStatus == 'revoke' ||
                dialogStatus == 'revokeApproval' ||
                (dialogStatus == 'detail' && formData.RevocationStatus > 0)
              "
            >
              <el-input
                :disabled="dialogStatus !== 'revoke'"
                type="textarea"
                :rows="4"
                maxlength="500"
                v-model="formData.TravelExpenseObj.RevocationCause"
              />
            </el-form-item>
            <el-form-item label="公司名称" prop="KingdeeDepartmentNumber">
              <CompanySelect
                v-model="formData.TravelExpenseObj.KingdeeDepartmentNumber"
                :disabled="disabled"
                @change="handleChangeCompany"
              />
            </el-form-item>
            <el-form-item label="填报日期" prop="FBillDate">
              <el-date-picker
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="formData.TravelExpenseObj.FBillDate"
                type="date"
                placeholder="请选择填报日期"
                :disabled="disabled"
                :picker-options="pickerOptions"
                :clearable="false"
              />
            </el-form-item>
            <el-form-item label="部门" prop="DepartmentId">
              <DepartmentSelect
                :value="formData.TravelExpenseObj.DepartmentId"
                :disabled="disabled"
                @change="changeDepartment"
              />
            </el-form-item>
            <el-form-item label="职别" prop="ExpenseRankType">
              <div style="display: flex; align-items: center">
                <el-select
                  v-model="formData.TravelExpenseObj.ExpenseRankType"
                  :disabled="disabled"
                  placeholder="请选择职别"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in EXPENSE_RANK_TYPE"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-button type="text" @click="viewStandard" style="margin-left: 10px">
                  查看差旅标准
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="出差事由" prop="BusinessTripReasons">
              <el-input
                v-model="formData.TravelExpenseObj.BusinessTripReasons"
                placeholder="请输入出差事由"
                clearable
                :disabled="disabled"
                :maxlength="50"
              />
            </el-form-item>
            <el-divider />
            <!-- 报销明细 -->
            <div class="reimbursement_list">
              <div
                class="reimbursement_item"
                v-for="(item, index) in formData.TravelExpenseObj.TravelExpenseDetailList"
                :key="index"
              >
                <div class="reimbursement_top">
                  <span>报销明细{{ index + 1 }}</span>
                  <app-table-row-button
                    :type="3"
                    text="删除"
                    v-if="!disabled"
                    @click="deleteExpenseRbt(index)"
                  />
                </div>
                <div class="reimbursement_content">
                  <el-form-item
                    label="日期"
                    :key="`date_${index}`"
                    :prop="`TravelExpenseDetailList.${index}.Date`"
                    :rules="[{ required: true, message: '请选择日期', trigger: 'change' }]"
                  >
                    <el-date-picker
                      v-model="item.Date"
                      type="daterange"
                      align="right"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      :disabled="disabled"
                      style="width: 100%"
                      @change="handleDateChange(item, $event)"
                    />
                  </el-form-item>
                  <el-form-item label="起讫地点" required>
                    <div style="display: flex; align-items: center">
                      <el-form-item
                        label=""
                        label-width="0px"
                        :key="`TravelRouteStart_${index}`"
                        :prop="`TravelExpenseDetailList.${index}.TravelRouteStart`"
                        :rules="[
                          { required: true, message: '请输入起讫地点开始', trigger: 'blur' },
                        ]"
                        style="flex: 1; margin-bottom: 0"
                      >
                        <el-input
                          v-model="item.TravelRouteStart"
                          placeholder="请输入起讫地点开始"
                          :disabled="disabled"
                          :maxlength="30"
                          clearable
                          style="width: 100%"
                        />
                      </el-form-item>
                      <span style="margin: 0 10px" class="flex-0">-</span>
                      <el-form-item
                        label=""
                        label-width="0px"
                        :key="`TravelRouteEnd_${index}`"
                        :prop="`TravelExpenseDetailList.${index}.TravelRouteEnd`"
                        :rules="[
                          { required: true, message: '请输入起讫地点结束', trigger: 'blur' },
                        ]"
                        style="flex: 1; margin-bottom: 0"
                      >
                        <el-input
                          v-model="item.TravelRouteEnd"
                          placeholder="请输入起讫地点结束"
                          :disabled="disabled"
                          :maxlength="30"
                          clearable
                          style="width: 100%"
                        />
                      </el-form-item>
                    </div>
                  </el-form-item>
                  <el-form-item label="天数" required>
                    <el-input-number
                      v-model="item.Days"
                      :step="1"
                      :min="0"
                      :disabled="disabled"
                      style="width: 200px"
                    />
                  </el-form-item>
                  <!-- 费用明细项 -->
                  <el-form-item
                    label="费用明细"
                    :key="`TravelExpenseDetailCosts_${index}`"
                    :prop="`TravelExpenseDetailList.${index}.TravelExpenseDetailCosts`"
                    :rules="[{ required: true, message: '请添加明细项', trigger: 'change' }]"
                  >
                    <el-button @click="addExpenseParticulars(item)" v-if="!disabled" size="mini">
                      添加明细项
                    </el-button>
                    <div class="particulars_list" style="margin-top: 10px">
                      <div
                        class="particulars_item"
                        v-for="(p, pInd) in item.TravelExpenseDetailCosts"
                        :key="pInd"
                      >
                        <el-form-item label="" label-width="0">
                          <div style="display: flex; align-items: center">
                            <el-form-item
                              label=""
                              label-width="0"
                              :key="`TravelType_${index}_${pInd}`"
                              :prop="`TravelExpenseDetailList.${index}.TravelExpenseDetailCosts.${pInd}.TravelType`"
                              :rules="[
                                { required: true, message: '请选择差旅费类型', trigger: 'change' },
                              ]"
                              class="flex-1"
                              style="margin-bottom: 0"
                            >
                              <el-select
                                v-model="p.TravelType"
                                placeholder="请选择费用类型"
                                :disabled="disabled"
                                style="width: 100%"
                              >
                                <el-option
                                  v-for="(t, tInd) in TRAVEL_TYPE"
                                  :disabled="
                                    disabledTravelType(item.TravelExpenseDetailCosts, t.value)
                                  "
                                  :label="t.label"
                                  :value="t.value"
                                  :key="tInd"
                                />
                              </el-select>
                            </el-form-item>
                            <div style="margin: 0 10px" class="flex-0"></div>
                            <el-form-item
                              label=""
                              label-width="0"
                              :key="`Cost_${index}_${pInd}`"
                              :prop="`TravelExpenseDetailList.${index}.TravelExpenseDetailCosts.${pInd}.Cost`"
                              :rules="[{ required: true, message: '请输入费用', trigger: 'blur' }]"
                              style="margin-bottom: 0"
                              class="flex-1"
                            >
                              <el-input
                                v-model="p.Cost"
                                :disabled="disabled"
                                placeholder="请输入金额"
                                type="text"
                                v-thousands="true"
                                :maxlength="15"
                                clearable
                                style="width: 100%"
                              >
                                <span slot="append">元</span>
                              </el-input>
                            </el-form-item>
                            <svg-icon
                              v-if="!disabled"
                              icon-class="delete-icon"
                              class="icon danger-icon flex-0"
                              style="margin-left: 10px"
                              title="删除"
                              @click.stop.native="
                                deleteParticulars(item.TravelExpenseDetailCosts, pInd)
                              "
                            />
                          </div>
                        </el-form-item>
                        <el-form-item label="发票" label-width="40px">
                          <InvoiceList v-model="p.InvoiceList" :disabled="disabled" />
                        </el-form-item>
                        <el-form-item label="附件" label-width="40px">
                          <AttachmentList v-model="p.AttachmentList" :disabled="disabled" />
                        </el-form-item>
                        <el-divider />
                      </div>
                      <el-form-item label="小计" label-width="350px" style="margin-bottom: 0">
                        <div class="el-input is-disabled el-input--small">
                          <div class="el-input__inner">
                            {{ formatThousands(particularsTotal(item)) }} 元
                          </div>
                        </div>
                      </el-form-item>
                    </div>
                  </el-form-item>
                </div>
                <el-divider />
              </div>
              <el-button @click="addExpenseDetail" v-if="!disabled" size="mini">
                添加报销明细
              </el-button>
            </div>
            <!-- 合计 -->
            <el-form-item label="合计金额">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ formatThousands(totalAmount) }} 元</div>
              </div>
            </el-form-item>
            <el-form-item label="合计金额(大写)">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ convertToChinese(totalAmount) }}</div>
              </div>
            </el-form-item>
            <el-form-item label="预支金额">
              <el-input
                v-model="formData.TravelExpenseObj.Advance"
                :disabled="disabled"
                placeholder="请输入金额"
                type="text"
                v-thousands="true"
                :maxlength="15"
                clearable
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
            <el-form-item label="费用说明">
              <el-input
                v-model="formData.TravelExpenseObj.ExpenseDescription"
                :disabled="disabled"
                placeholder="请输入费用说明"
                clearable
                :maxlength="30"
              />
            </el-form-item>
          </div>
          <div class="right_container">
            <!-- 关联出差单 -->
            <RelationEvection
              v-model="formData.TravelExpenseObj.RelateHRpersonalInfoList"
              :disabled="disabled"
            />
            <el-divider />
            <!-- 关联研发项目 -->
            <AddProject
              :companyId="formData.TravelExpenseObj.KingdeeDepartmentNumber"
              v-model="formData.TravelExpenseObj.KingdeeProjectList"
              :disabled="disabled"
            />
            <el-divider />
            <el-form-item label="报销人">
              <empSelector
                :readonly="true"
                :showType="2"
                :multiple="true"
                placeholder="输入工号/姓名"
                :isAutocomplete="true"
                :collapseTags="false"
                :list="formData.TravelExpenseObj.SubmitEmployeeList"
                :beforeConfirm="() => true"
              />
            </el-form-item>
            <el-form-item label="领款人" prop="PayeeEmployeeList">
              <empSelector
                :readonly="disabled"
                :showType="2"
                :multiple="false"
                placeholder="输入工号/姓名"
                :isAutocomplete="true"
                :collapseTags="false"
                :list="formData.TravelExpenseObj.PayeeEmployeeList"
                :beforeConfirm="handleBeforePerson"
                @change="handleChangePerson"
              />
            </el-form-item>
            <el-form-item label="票据及附件数">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ fileTotal }}</div>
              </div>
            </el-form-item>
            <el-form-item label="单据编号" v-if="disabled">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ formData.TravelExpenseObj.FBillNo }}</div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 审批区域 -->
      <div class="body_footer">
        <approvalPanel
          v-if="!disabled"
          :editable="isEditApprove"
          ref="approvalPanel"
          :approvalPanelObj="formData.Approval"
        />
        <approvalDetail
          :isOnlyViewDetail="isOnlyViewDetail || !isApprovalor"
          v-if="['approval', 'detail', 'revoke', 'revokeApproval'].includes(dialogStatus)"
          ref="approvalDetail"
          :dialogStatus="dialogStatusTrans"
          :approvalObj="formData.Approval"
        />
      </div>
      <TravelExpensesDialog
        v-if="showTravelExpensesDialog"
        :dialogFormVisible="showTravelExpensesDialog"
        :tempObj="tempObj_"
        dialogStatus="create"
        :id="id"
        :approvalId="approvalId"
        :processId="tempObj_.HRApprovalProcessId"
        @closeDialog="closeSubDialog"
      />
    </div>
    <div slot="footer">
      <el-button
        v-if="['revokeApproval', 'detail'].includes(dialogStatus) && isCurrentUser"
        @click="handleReferenceCreate"
      >
        引用创建
      </el-button>
      <app-button @click="beforeClose" :buttonType="2" :loading="btnLoading" />
      <el-button
        v-if="['create', 'editDraft'].includes(dialogStatus)"
        @click="handleSaveDraft"
        :loading="btnLoading"
      >
        暂存草稿
      </el-button>
      <app-button
        @click="createData"
        :buttonType="1"
        v-if="dialogStatus != 'approval' && (!disabled || dialogStatus == 'revoke')"
        :loading="btnLoading"
        style="margin-left: 10px"
      />
      <el-button
        @click="handleApproval"
        type="primary"
        :loading="btnLoading"
        v-if="
          (dialogStatus == 'approval' || (dialogStatus == 'revokeApproval' && !isOnlyViewDetail)) &&
          isApprovalor
        "
        style="margin-left: 10px"
      >
        审批
      </el-button>
    </div>
  </app-dialog>
</template>

<script>
import RelationEvection from "./RelationEvection.vue";
import dayjs from "dayjs";
import Decimal from "decimal.js";
import * as approvalManagement from "@/api/approvalManagement.js";
import approvalMixins from "@/mixins/approvalPatch.js";
import { EXPENSE_RANK_TYPE, TRAVEL_TYPE } from "@/views/financialManagement/reimbursement/vars.js";
import { getUserInfo } from "@/utils/auth";
import expenseFormDialogMixins from "./expenseFormDialogMixins.js";
import { TIAN_ZONG_ID, LIU_ZONG_ID } from "@/config/employeeConfig.js";

export default {
  name: "TravelExpensesDialog",
  mixins: [approvalMixins, expenseFormDialogMixins],
  components: {
    RelationEvection,
    TravelExpensesDialog: () => import("./TravelExpensesDialog"),
  },
  data() {
    return {
      EXPENSE_RANK_TYPE,
      TRAVEL_TYPE,
      // 表单parcelKey对应后端详情数据的包裹对象mixins使用
      parcelKey: "TravelExpenseObj",
      formData: {
        TravelExpenseObj: {
          FBillNo: "",
          KingdeeDepartmentNumber: null,
          FBillDate: null,
          DepartmentId: null,
          ExpenseRankType: null,
          BusinessTripReasons: "",
          Advance: "",
          ExpenseDescription: "",
          RevocationCause: "",
          // 报销明细
          TravelExpenseDetailList: [],
          // 报销人
          SubmitEmployeeList: [],
          // 领款人
          PayeeEmployeeList: [],
          // 项目
          KingdeeProjectList: [],
          // 出差单
          RelateHRpersonalInfoList: [],
        },
        Type: 12, // 差旅费报销
        ApprovalStatus: 0, //审批状态
        HrApprovalProcessId: null,
        //审批信息
        Approval: {
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          ApprovalOperatorEmployeeList: [], //已审批人员
          NoApprovalEmployeeList: [], //未审批人员
          CCEmployeeList: [], //抄送人
          ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
          ApprovalState: 1, //1: 进行中; 2: 已完成
          ApprovalResult: 1, //1: 通过； 2：不通过
        },
      },
      showTravelExpensesDialog: false,
      tempObj_: null,
      rules: {
        KingdeeDepartmentNumber: [{ required: true, message: "请选择公司名称", trigger: "change" }],
        FBillDate: [{ required: true, message: "请选择填报日期", trigger: "change" }],
        DepartmentId: [{ required: true, message: "请选择部门", trigger: "change" }],
        PayeeEmployeeList: [{ required: true, message: "请选择领款人", trigger: "change" }],
        ExpenseRankType: [{ required: true, message: "请选择职别", trigger: "change" }],
        BusinessTripReasons: [{ required: true, message: "请输入出差事由", trigger: "blur" }],
        RevocationCause: [{ required: true, message: "请输入撤销原因", trigger: "blur" }],
      },
    };
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.closeDialog();
          return;
        }
        if (this.dialogStatus === "create") {
          if (this.tempObj) {
            this.formData = Object.assign(this.formData, this.$_.cloneDeep(this.tempObj));
          } else {
            this.formData.TravelExpenseObj.FBillDate = dayjs().format("YYYY-MM-DD");
            this.addExpenseDetail();

            const { employeeid, empName, empNumber, deptId } = getUserInfo();
            const oneself = {
              Name: empName,
              Number: empNumber,
              EmployeeId: employeeid,
            };
            this.formData.TravelExpenseObj.SubmitEmployeeList.push(oneself);
            this.formData.TravelExpenseObj.PayeeEmployeeList.push(oneself);
            this.changeDepartment(deptId);
          }
        } else {
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  created() {},
  computed: {
    title() {
      const titleMap = {
        create: "差旅费报销申请",
        detail: "差旅费报销详情",
        approval: "差旅费报销审批",
        revoke: "(撤销)差旅费报销",
        revokeApproval: "(撤销)差旅费报销",
      };
      return titleMap[this.dialogStatus] || "差旅费报销";
    },
    pickerOptions() {
      return {
        disabledDate: time => {
          return dayjs(time).isAfter(dayjs(), "day");
        },
      };
    },
    // 合计金额
    totalAmount() {
      const list = this.formData?.TravelExpenseObj?.TravelExpenseDetailList;
      if (list && list.length > 0) {
        return list.reduce((acc, curr) => {
          return acc.plus(this.particularsTotal(curr));
        }, Decimal(0));
      }
      return 0;
    },
    // 费用明细的明细项合计
    particularsTotal() {
      return item => {
        return item.TravelExpenseDetailCosts.reduce((acc, curr) => {
          return acc.plus(Decimal(this.parseThousands(curr?.Cost || 0)));
        }, Decimal(0));
      };
    },
    // 票据及附件数
    fileTotal() {
      try {
        const list = this.formData?.TravelExpenseObj?.TravelExpenseDetailList;
        if (!list || list.length === 0) return 0;
        return list.reduce((acc, curr) => {
          // 明细项票据及附件数
          const pTotal = curr.TravelExpenseDetailCosts.reduce((acc1, curr1) => {
            const InvoiceCount = curr1?.InvoiceList?.length || 0;
            const AttachmentCount = curr1?.AttachmentList?.length || 0;
            return acc1 + InvoiceCount + AttachmentCount;
          }, 0);
          return acc + pTotal;
        }, 0);
      } catch (error) {
        return 0;
      }
    },
    // 选中的报销类型禁用
    disabledTravelType() {
      return (item, value) => {
        const valueList = item.map(t => t.TravelType);
        return valueList.includes(value);
      };
    },
  },
  methods: {
    // 查看差旅标准
    viewStandard() {
      this.$viewerApi({
        images: [require("@/assets/images/travel_expenses_standard.png")],
      });
    },
    // 添加报销明细
    addExpenseDetail() {
      if (this.formData.TravelExpenseObj.TravelExpenseDetailList.length >= 5) {
        this.$message.error("报销明细最多只能添加5条");
        return;
      }
      const obj = {
        Date: [], // 日期
        StartDate: null,
        EndDate: null,
        TravelRouteStart: "",
        TravelRouteEnd: "",
        Days: 0,
        TravelExpenseDetailCosts: [],
      };
      this.formData.TravelExpenseObj.TravelExpenseDetailList.push(obj);
      // 添加明细项
      this.addExpenseParticulars(obj);
    },
    // 添加明细项
    addExpenseParticulars(item) {
      if (item.TravelExpenseDetailCosts) {
        if (item.TravelExpenseDetailCosts.length >= 6) {
          this.$message.error("明细项最多只能添加6条");
          return;
        }
        item.TravelExpenseDetailCosts.push({
          TravelType: null,
          Cost: "",
          InvoiceList: [],
          AttachmentList: [],
        });
      }
    },
    // 删除报销明细
    deleteExpenseRbt(index) {
      if (this.formData.TravelExpenseObj.TravelExpenseDetailList.length === 1) {
        this.$message.error("至少保留一条报销明细");
        return;
      }
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formData.TravelExpenseObj.TravelExpenseDetailList.splice(index, 1);
      });
    },
    // 删除报销明细的明细项
    deleteParticulars(item, pInd) {
      item.splice(pInd, 1);
    },
    /**
     * 创建报销单
     * @param temporize true:保存草稿 false:发起审批
     */
    createRequest(temporize = false) {
      // 硬编码老板id  老板不需要出差单校验
      const PayeeEmployeeIdList = this.formData.TravelExpenseObj.PayeeEmployeeList.map(
        s => s.EmployeeId
      );
      const isBoss = PayeeEmployeeIdList.some(t => [TIAN_ZONG_ID, LIU_ZONG_ID].includes(t));

      if (
        !isBoss &&
        !temporize &&
        this.formData.TravelExpenseObj.RelateHRpersonalInfoList.length === 0
      ) {
        this.$message.error("请选择关联出差单");
        return;
      }

      this.formData.Approval = this.$refs.approvalPanel.getData(); //审批层区块

      const form = this.$_.cloneDeep(this.formData);
      form.Approval.ApprovalEmployeeIdList = form.Approval.ApprovalEmployeeList.map(s =>
        s.map(e => e.EmployeeId)
      );
      form.Approval.CCEmployeeIdList = form.Approval.CCEmployeeList.map(s => s.EmployeeId);

      const params = {
        ...form,
        Temporize: temporize,
        HrApprovalProcessId: this.id,
        TravelExpenseObj: {
          ...form.TravelExpenseObj,
          Total: this.totalAmount,
          SubmitEmployeeIdList: form.TravelExpenseObj.SubmitEmployeeList.map(s => s.EmployeeId),
          PayeeEmployeeIdList: form.TravelExpenseObj.PayeeEmployeeList.map(s => s.EmployeeId),
          RelateHRpersonalInfoIdList: form.TravelExpenseObj.RelateHRpersonalInfoList.map(s => s.Id),
        },
      };
      // 明细项
      const TravelExpenseDetailList = params.TravelExpenseObj.TravelExpenseDetailList;
      TravelExpenseDetailList.forEach(s => {
        delete s.Date;
        s.TravelExpenseDetailCosts.forEach(s1 => {
          s1.AttachmentIdList = s1.AttachmentList.map(t => t.Id);
          delete s1.AttachmentList;
        });
      });

      delete params.TravelExpenseObj.PayeeEmployeeList;
      delete params.TravelExpenseObj.SubmitEmployeeList;
      delete params.TravelExpenseObj.RelateHRpersonalInfoList;

      let reqApi;
      if (this.dialogStatus == "editDraft") {
        // 草稿编辑
        reqApi = approvalManagement.temporizeApi;
      } else {
        // 普通创建和创建时暂存
        reqApi = approvalManagement.infoAdd;
      }
      this.btnLoading = true;
      reqApi(params)
        .then(res => {
          this.$emit("reload");
          this.closeDialog();
          if (temporize) {
            this.$message.success("暂存成功");
          } else {
            this.$confirm(
              "审批流程完成后，请及时将对应纸质票据（如发票、合同、收据等）提交至财务处",
              "提示",
              {
                confirmButtonText: "确定",
                showCancelButton: false,
                type: "success",
              }
            );
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    handleChangePerson(users) {
      this.formData.TravelExpenseObj.PayeeEmployeeList = users;
      this.$nextTick(() => {
        this.$refs.formRef.validateField("PayeeEmployeeList");
      });
    },
    handleBeforePerson(users) {
      if (users && users.length > 1) {
        this.$message.error("领款人不得超过1人");
        return false;
      }
      return true;
    },
    handleDateChange(item, value) {
      item.StartDate = value?.[0] || null;
      item.EndDate = value?.[1] || null;

      if (item.StartDate && item.EndDate && !this.disabled) {
        item.Days = dayjs(item.EndDate).diff(dayjs(item.StartDate), "day") + 1;
      } else {
        item.Days = 0;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.body_wrapper {
  height: 75vh;
  display: flex;
  flex-direction: column;
  .main_container {
    flex: 1;
    width: 100%;
    .form_container {
      width: 100%;
      height: 100%;
      display: flex;
      .left_container {
        padding: 10px;
        width: 65%;
        border-right: 1px solid $border-color-light;
        .reimbursement_list {
          margin-bottom: 10px;
          .reimbursement_item {
            .reimbursement_top {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .reimbursement_content {
              margin-top: 10px;
            }
          }
        }
      }
      .right_container {
        width: 35%;
        padding: 10px;
      }
    }
  }
  .body_footer {
    flex-shrink: 0;
    border-top: 1px solid $border-color-light;
  }
}
/deep/.el-divider {
  background-color: $border-color-light;
  margin: 10px 0;
}

/deep/input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  &[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
