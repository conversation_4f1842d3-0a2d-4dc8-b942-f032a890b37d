﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="70px" height="70px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="32px" y="93px" width="70px" height="70px" filterUnits="userSpaceOnUse" id="filter63">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.349019607843137 0  " in="shadowComposite" />
    </filter>
    <g id="widget64">
      <image preserveAspectRatio="none" style="overflow:visible" width="60" height="60" xlink:href="data:image/png;base64,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" x="37px" y="98px" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -32 -93 )">
    <use xlink:href="#widget64" filter="url(#filter63)" />
    <use xlink:href="#widget64" />
  </g>
</svg>