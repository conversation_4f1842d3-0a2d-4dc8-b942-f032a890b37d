<template>
    <div class="print-wrapper" ref="print" v-if="printRow">
      <div class="tab-title">
        {{ printType == 3 ? '出差申请单' : '出差补助申请单' }}
      </div>
      <div class="sub-tab-title">审批单号：{{ printRow.Code }}</div>
      <table v-if="printType == 3">
        <tr>
          <td class="title">姓名</td>
          <td class="content">
            {{ printRow.EmployeeList && printRow.EmployeeList.length > 0 ? printRow.EmployeeList[0].Name : '' }}
          </td>
          <td class="title">工号</td>
          <td class="content">
            {{ printRow.EmployeeList && printRow.EmployeeList.length > 0 ? printRow.EmployeeList[0].Number : '' }}
          </td>
          <td class="title">部门</td>
          <td class="content">
            {{ printRow.EmployeeList && printRow.EmployeeList.length > 0 && printRow.EmployeeList[0].DepartmentList && printRow.EmployeeList[0].DepartmentList.length > 0 ? printRow.EmployeeList[0].DepartmentList[0].DepartmentName : '' }}
          </td>
        </tr>
        <tr>
          <td class="title">出差事由</td>
          <td colspan="5" class="content">
            <div class="text-content">
              {{ printRow.Remarks }}
            </div>
          </td>
        </tr>
        <tr>
          <td class="title">出差地域</td>
          <td colspan="2" class="content">
            {{ printRow.BusinessTripZone | zoneFilter }}
          </td>
          <td class="title">出差地点</td>
          <td colspan="2" class="content">
            {{ printRow.TravelPlace }}
          </td>
        </tr>

        <tr>
          <td class="title">出差时间</td>
          <td colspan="2" class="content">
            {{ printRow.StartTime | dateFilter('YYYY-MM-DD') }}{{ printRow.StartUpDown | upDownFilter }} - {{ printRow.EndTime | dateFilter('YYYY-MM-DD') }}{{ printRow.EndUpDown | upDownFilter }}
          </td>
          <td class="title">出差天数</td>
          <td colspan="2" class="content">
            {{ printRow.TimeTotal }}
          </td>
        </tr>
        <tr>
          <td class="title">审批信息</td>
          <td colspan="5" class="content">
            <div class="text-content">
              <div v-for="(l, idx) in printRow.Approval.ApprovalNodeList" :key="idx">
                <div>审批层{{ idx + 1 }}：</div>
                <template v-if="l.ApprovalResultList && l.ApprovalResultList.length > 0">
                  <div v-for="(a, idx2) in l.ApprovalResultList" :key="'a-' + idx2">{{ a.ApprovalOperatorEmployee.Name }} {{ a.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }} {{ a.ApprovalResultState == 1 ? "通过" : '不通过' }}  备注：{{ a.ApprovalOpinion }}</div>
                </template>
                <div v-else>无</div>
              </div>
            </div>
          </td>
        </tr>
      </table>
      <table v-else-if="printType == 6">
        <tr>
          <td class="title">姓名</td>
          <td class="content">
            {{ printRow.EmployeeList && printRow.EmployeeList.length > 0 ? printRow.EmployeeList[0].Name : '' }}
          </td>
          <td class="title">工号</td>
          <td class="content">
            {{ printRow.EmployeeList && printRow.EmployeeList.length > 0 ? printRow.EmployeeList[0].Number : '' }}
          </td>
          <td class="title">部门</td>
          <td class="content">
            {{ printRow.EmployeeList && printRow.EmployeeList.length > 0 && printRow.EmployeeList[0].DepartmentList && printRow.EmployeeList[0].DepartmentList.length > 0 ? printRow.EmployeeList[0].DepartmentList[0].DepartmentName : '' }}
          </td>
        </tr>
        <tr>
          <td class="title">出差地点</td>
          <td colspan="5" class="content">
            <div class="text-content">
              {{ printRow.TravelPlace }}
            </div>
          </td>
        </tr>
        <tr>
          <td class="title">补助月份</td>
          <td colspan="2" class="content">
            {{ printRow.SubsidyMonth }} 月
          </td>
          <td class="title">补助金额</td>
          <td colspan="2" class="content">
            {{ printRow.SubsidyMoney }} 元
          </td>
        </tr>
        <tr>
          <td class="title">关联出差单</td>
          <td colspan="5" class="content">
            <div class="text-content">
              <div v-for="(t, idx) in printRow.RelationHRpersonalInfoList" :key="idx">
                {{ t.ApprovalCode }}（出差地点：{{ t.TravelPlace }}，开始/结束时间：{{ t.StartTime | dateFilter('YYYY-MM-DD') }} {{ t.StartUpDown | morningOrAfternoonFilter }} - {{ t.EndTime | dateFilter('YYYY-MM-DD') }} {{ t.EndUpDown | morningOrAfternoonFilter }}，共计{{ t.TimeTotal }}天）
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="title">审批信息</td>
          <td colspan="5" class="content">
            <div class="text-content">
              <div v-for="(l, idx) in printRow.Approval.ApprovalNodeList" :key="idx">
                <div>审批层{{ idx + 1 }}：</div>
                <template v-if="l.ApprovalResultList && l.ApprovalResultList.length > 0">
                  <div v-for="(a, idx2) in l.ApprovalResultList" :key="'a-' + idx2">{{ a.ApprovalOperatorEmployee.Name }} {{ a.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }} {{ a.ApprovalResultState == 1 ? "通过" : '不通过' }}  备注：{{ a.ApprovalOpinion }}</div>
                </template>
                <div v-else>无</div>
              </div>
            </div>
          </td>
        </tr>
      </table>
    </div>
</template>

<script>
import * as approvalManagement from "@/api/approvalManagement";
import { vars } from '../../../workbench/myWorkbench/vars'
import * as attendanceVars from '../../../personnelManagement/attendanceMgmt/vars'

export default {
    name: '',
    props: {
        id: {
            type: String,
            default: ''
        },
        approvalId: {
            type: String,
            default: ''
        }
    },
    filters: {
        upDownFilter(val) {
            if(val == 1) {
                return '上午'
            }
            return '下午'
        },
        zoneFilter(val) {
          let obj = vars.zones.find(s => s.value == val)
          if(obj) {
            return obj.label
          }
          return ''
        },
        morningOrAfternoonFilter(val) {
            let result = '无'
            if (val) {
                let obj = attendanceVars.vars.morningOrAfternoon.find(s => s.value == val)
                if (obj) {
                result = obj.label
                }
            }
            return result
        },
    },
    data() {
      return{
        printType: -1, //3： 打印出差；6：出差补助
        printRow: null
      }
    },
    methods: {
      // type———— 3： 打印出差；6：出差补助
        handlePrintRow({type, id, approvalId}) {
            if(type, id && approvalId) {
                this.printType = type
                approvalManagement.getPersonalDetails({ id: id, approvalId: approvalId, isApprovaled: true }).then(res => {
                    this.printRow = res
                    this.$nextTick(() => {
                        this.$print(this.$refs.print)
                    })
                })
            }
        },
    }
}
</script>

<style lang='scss' scoped>

.print-wrapper{
  padding: 10px;
  position: absolute;
  z-index: -999;
  .tab-title{
    margin-top: 40px;
    margin-bottom: 20px;
    height: 40px; 
    line-height: 40px; 
    text-align: center; 
    font-size: 24px; 
    position: relative;
  }
  .sub-tab-title{
    text-align: left;
    padding-bottom: 10px;
  }

  table, table tr th, table tr td {
    border: 1px solid $text-primary;
  }
  table {
    width: 100%;
    text-align: center; 
    border-collapse: collapse; 
    td{
      padding: 10px 0;
    }
    .title{
      width: 100px;
      font-weight: bold;
    }
    .content{
      text-align: left;
      padding: 10px;
    }
    .text-content{
      min-height: 100px;
    }
  }
}
</style>