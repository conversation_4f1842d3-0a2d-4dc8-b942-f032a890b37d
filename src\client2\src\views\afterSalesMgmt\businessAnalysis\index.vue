<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="pageWrapper">
          <div class="product-list">
              <div class="treeBox">
                <tags mode='list' :items='areaList' v-model="regionalId">
                    <template v-for="t in areaList" :slot="t.value">
                        {{ t.label }}
                    </template>
                </tags>
              </div>
          </div>
          <div class="content-wrapper">
            <template v-if="regionalId == tagDefaultVAlue">

              
              <!-- <v-analysis-title title="整体数据统计" v-loading="loading4">
               
              </v-analysis-title> -->
              <el-row style="margin-top: 10px; margin-left: 10px; margin-right: 10px;">
                <el-col :span="24" class="blockTitle pl-10 line-40 cl"><span>整体数据统计</span></el-col>
              </el-row>
               <el-card shadow="hover" style="margin-left: 10px; margin-right: 10px;">
                <ul class="chartUl cl">
                  <li class="fl" v-for="(od,odI) in overallData" :key="odI">
                    <tool-title :title="`${od.label}${od.value != 'warrantySituation' ? '（' + od.count + '）' : ''}`" :showOptArea='false'></tool-title>
                    <div :id="'myChart'+od.value" style="width:100%;height:260px;"></div>
                  </li>
                </ul>
               </el-card>
              


              <div class="tabs-content">

                <el-card shadow="hover">
                   <div class="__dynamicTabContentWrapper" style="height: 100%;">
                  
                  <tool-title :title='"生产厂家分析"' :showOptArea='false'>
                    <template slot="append">
                      <el-input 
                          style="width: 200px;"
                          placeholder="关键字搜索"
                          @clear='getLeftData'
                          v-antiShake='{
                              time: 300,
                              callback: () => {
                                  getLeftData()
                              }
                          }' 
                          clearable 
                          v-model="factoryAnalysisListQuery.Keywords"
                      ></el-input>
                    </template>
                  </tool-title>

                  <div class="__dynamicTabWrapper">
                    <app-table
                      ref="mainTable"
                      :tab-columns="tabColumns"
                      :tab-datas="tabDatas"
                      :tab-auth-columns="tabAuthColumns"
                      :isShowAllColumn="true"
                      :loading="leftLoading"
                      :isShowOpatColumn="false"
                      :startOfTable="startOfTable"
                      :multable="false"
                      :serial="false"
                      :isShowBtnsArea="false"
                      :isShowConditionArea="false"
                    >
                      <template slot="ranking" slot-scope="scope">
                          <span class="cRank" :class="{'fix-width': scope.index >= 0 && scope.index <= 3 && factoryAnalysisListQuery.PageIndex == 1}" :style="{background: getColor(scope.index), color: factoryAnalysisListQuery.PageIndex == 1 && scope.index <= 3 ? '#fff' : 'inherit'}">{{ (factoryAnalysisListQuery.PageIndex-1)*factoryAnalysisListQuery.PageSize+scope.index }}</span>
                      </template>
                      <template slot="Ratio" slot-scope="scope">
                        <div class="elBox">
                          <span>{{scope.row.Ratio}}%</span>
                          <el-progress style="width:145px;margin-top:7px;margin-left:10px;" :stroke-width='10' :percentage="scope.row.Ratio" :show-text="false"></el-progress>
                        </div>
                      </template>
                    </app-table>
                  </div>
                  <pagination
                    :total="total"
                    :page.sync="factoryAnalysisListQuery.PageIndex"
                    :size.sync="factoryAnalysisListQuery.PageSize"
                    layout="total, prev, pager, next, jumper"
                    @pagination="handleCurrentChange"
                    @size-change="handleSizeChange"
                  />
                </div>
                </el-card>   

                
              </div>
              
            </template>
            <template v-else>
              <div class="part-regional">
                <div style="position: sticky;">{{ regionalIdName }}</div>
                <div class="first-level cl">
                  <div class="lft fl">
                    <ul class="chartUl" v-loading="loading4">
                      <template v-for="(od,odI) in overallData">
                        <!-- 三个饼图，显示第一个 和 第三个 -->
                        <li class="fl shadow" :style="{marginRight: odI == 0 ? '5px' : '', marginLeft: odI == 2 ? '5px' : ''}" v-show="odI != 1" :key="odI">
                          <tool-title :title="`${od.label}${od.value != 'warrantySituation' ? '（' + od.count + '）' : ''}`" :showOptArea='false'></tool-title>
                          <div :id="'myChart'+od.value" style="width:100%;height:260px;"></div>
                        </li>
                      </template>
                    </ul>

                    <div class="shadow">
                      <tool-title title='生产厂家分析' :regionalName='factoryAnalysisListQuery.regionalName' @click="handleRegionalDialog('factoryAnalysis', factoryAnalysisListQuery.regionalId)">
                        <template slot="append">
                          <el-input 
                              style="width: 200px;"
                              placeholder="关键字搜索"
                              @clear='getLeftData'
                              v-antiShake='{
                                  time: 300,
                                  callback: () => {
                                      getLeftData()
                                  }
                              }' 
                              clearable 
                              v-model="factoryAnalysisListQuery.Keywords"
                          ></el-input>
                        </template>
                      </tool-title>
                      <div>
                        <app-table
                          ref="mainTable"
                          :tab-columns="tabColumns"
                          :tab-datas="tabDatas"
                          :tab-auth-columns="tabAuthColumns"
                          :isShowAllColumn="true"
                          :loading="leftLoading"
                          :isShowOpatColumn="false"
                          :startOfTable="startOfTable"
                          :multable="false"
                          :serial="false"
                          :isShowBtnsArea="false"
                          :isShowConditionArea="false"
                        >
                          <template slot="ranking" slot-scope="scope">
                            <span class="cRank" :class="{'fix-width': scope.index >= 0 && scope.index <= 3 && factoryAnalysisListQuery.PageIndex == 1}" :style="{background: getColor(scope.index), color: factoryAnalysisListQuery.PageIndex == 1 && scope.index <= 3 ? '#fff' : 'inherit'}">{{ (factoryAnalysisListQuery.PageIndex-1)*factoryAnalysisListQuery.PageSize+scope.index }}</span>
                          </template>
                          <template slot="Ratio" slot-scope="scope">
                            <div class="elBox">
                              <span>{{scope.row.Ratio}}%</span>
                              <el-progress style="width:145px;margin-top:7px;margin-left:10px;" :stroke-width='10' :percentage="scope.row.Ratio" :show-text="false"></el-progress>
                            </div>
                          </template>
                        </app-table>
                      </div>
                      <pagination
                        :total="total"
                        :page.sync="factoryAnalysisListQuery.PageIndex"
                        :size.sync="factoryAnalysisListQuery.PageSize"
                        layout="total, prev, pager, next, jumper"
                        @pagination="handleCurrentChange"
                        @size-change="handleSizeChange"
                      />
                    </div>
                  </div>
                  <div class="rht fr shadow">
                    <div style="margin-bottom: 10px;" v-loading='loading5'>
                      <tool-title title='设备年龄整体分析' :regionalName='ageAnalysisListQuery.regionalName' @click="handleRegionalDialog('ageAnalysis', ageAnalysisListQuery.regionalId)">
                      </tool-title>

                      <div id="myChartAgeAnalysis" style="width:100%;height:260px;"></div>
                    </div>

                    <div style="height: 492px; width: 100%;" class="__dynamicTabContentWrapper">
                      
                      <tool-title title='设备年龄分析' :showOptArea='false'>
                        <template slot="append">
                          <el-select class="elSelect" v-model="equipmentValue" placeholder="请选择" @change="handleSelectChange">
                            <el-option
                              v-for="item in equipmentAge"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                            </el-option>
                          </el-select>
                        </template>
                      </tool-title>

                      <div class="__dynamicTabWrapper" style="padding-right: 5px; overflow: hidden;">
                        <app-table
                          ref="mainTable"
                          :tab-columns="equipmentColumns"
                          :tab-datas="equipmentDatas"
                          :tab-auth-columns="tabAuthColumns"
                          :isShowAllColumn="true"
                          :loading="rightLoading"
                          :isShowOpatColumn="false"
                          :startOfTable="startOfTable"
                          :multable="false"
                          :serial="false"
                          :isShowBtnsArea="false"
                          :isShowConditionArea="false"
                          row-key="RegionalId"
                          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                        >
                          <template slot="oper" slot-scope="scope">
                            <el-button type="text" @click='handleDetail(scope.row)'>详情</el-button>
                          </template>
                        </app-table>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="second-level cl">
                  <div class="fl shadow">
                    <tool-title title='用途分类' :regionalName='equipmentUseListQuery.regionalName' @click="handleRegionalDialog('equipmentUse', equipmentUseListQuery.regionalId)">
                      <template slot="append">
                        <el-input 
                            style="width: 200px;"
                            placeholder="关键字搜索"
                            @clear='getEquipmentUse'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    getEquipmentUse()
                                }
                            }' 
                            clearable 
                            v-model="equipmentUseListQuery.Keywords"
                        ></el-input>
                      </template>
                    </tool-title>

                    <div v-loading='loading6'>
                      <div>
                        <app-table
                          ref="mainTable"
                          :tab-columns="tabColumnsEquipmentUse"
                          :tab-datas="tabDatasEquipmentUse"
                          :tab-auth-columns="tabAuthColumns"
                          :isShowAllColumn="true"
                          :loading="loading6"
                          :isShowOpatColumn="false"
                          :startOfTable="startOfTable"
                          :multable="false"
                          :serial="false"
                          :isShowBtnsArea="false"
                          :isShowConditionArea="false"
                        >
                          <template slot="Ratio" slot-scope="scope">
                            <div class="elBox">
                              <span>{{scope.row.Ratio}}%</span>
                              <el-progress style="width:145px;margin-top:7px;margin-left:10px;" :stroke-width='10' :percentage="scope.row.Ratio" :show-text="false"></el-progress>
                            </div>
                          </template>
                        </app-table>
                      </div>
                      <pagination
                        :total="totalEquipmentUse"
                        :page.sync="equipmentUseListQuery.PageIndex"
                        :size.sync="equipmentUseListQuery.PageSize"
                        layout="total, prev, pager, next, jumper"
                        @pagination="handleCurrentChangeEquipmentUse"
                        @size-change="handleSizeChangeEquipmentUse"
                      />


                    </div>
                  </div>
                  <div class="fr shadow">
                    <tool-title title='供风方式' :regionalName='equipmentWorkModeListQuery.regionalName' @click="handleRegionalDialog('equipmentWorkMode', equipmentWorkModeListQuery.regionalId)">
                      <template slot="append">
                        <el-input 
                            style="width: 200px;"
                            placeholder="关键字搜索"
                            @clear='getEquipmentWorkMode'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    getEquipmentWorkMode()
                                }
                            }' 
                            clearable 
                            v-model="equipmentWorkModeListQuery.Keywords"
                        ></el-input>
                      </template>
                    </tool-title>

                    <div v-loading='loading7'>
                      <div id="myChartEquipmentWorkMode" style="width:100%;height:452px;"></div>
                    </div>
                  </div>
                </div>

              </div>
            </template>
          </div>
      </div>

    </div>
    <v-detail
        v-if="dialogdetailVisible"
        @closeDialog="handleClosedetail"
        :dialogFormVisible="dialogdetailVisible"
        :id="detailId"
        :equipmentValue='equipmentValue'
        >
      </v-detail>

      <!-- 选择地区 -->
      <v-area-choose
          v-if="regionalSelecorVisible"
          @closeDialog="closeRegionalDialog"
          @electedRegionalData="selectedRegional"
          :dialogFormVisible="regionalSelecorVisible"
          :checkedList="targetRegionalId ? [targetRegionalId] : []"
          :queryRegionID='regionalId'
      ></v-area-choose>
  </div>
</template>

<script>
import * as businessMap from "@/api/businessMap";
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import { listToTreeSelect } from '@/utils'
import vButtonList from '@/views/common/buttonList'
import vAnalysisTitle from "@/views/common/analysisTitle";
import NoData from "@/views/common/components/noData";
import { vars } from "../maintenCenter/common/vars"
import indexPageMixin from "@/mixins/indexPage";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import vDetail from './detail';
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import toolTitle from './toolTitle'
var echarts = require('echarts');

const tagTemp = {
  value: '',
  label: '整体统计分析'
}

let lineOptionTemp = {
  // title: {
  //   text: ''
  // },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
        type: 'shadow'
    }
  },
  // legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10px',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    boundaryGap: [0, 0.01]
  },
  yAxis: {
    type: 'category',
    data: [] //'Brazil', 'Indonesia', 'USA', 'India', 'China', 'World'
  },
  color: ['#3aa1ff', '#88d1ea', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2'],
  series: [
    {
      name: '设备数量',
      type: 'bar',
      data: [] //18203, 23489, 29034, 104970, 131744, 630230
    },
  ]
};

export default {
  name: "",
  mixins: [indexPageMixin, tabDynamicHeightMixins],
  components: {
    vButtonList,
    vAnalysisTitle,
    NoData,
    vDetail,
    vAreaChoose,
    toolTitle,
  },
  computed: {
    regionalIdName() {
      let obj = this.areaList.find(s => s.value == this.regionalId)
      if(obj) {
        return obj.label
      }
      return ''
    }
  },
  filters: {


  },
  watch: {
    regionalId(val) {
      let currRegional = this.areaList.find(s => s.value == val) || {}
      let valLabel = currRegional.label || ''
      this.factoryAnalysisListQuery.regionalId = val
      this.factoryAnalysisListQuery.regionalName = valLabel
      this.equipmentUseListQuery.regionalId = val
      this.equipmentUseListQuery.regionalName = valLabel
      this.ageAnalysisListQuery.regionalId = val
      this.ageAnalysisListQuery.regionalName = valLabel
      this.equipmentWorkModeListQuery.regionalId = val
      this.equipmentWorkModeListQuery.regionalName = valLabel

      this.getOverallStatistics();
      this.getLeftData();

      if(val != this.tagDefaultVAlue) {
        this.getAgeAnalysis()
        this.getRightData()
        this.getEquipmentUse()
        this.getEquipmentWorkMode()
        // xxxxx
      }
    },
  },
  mounted() {
    this.getRegional();
    this.getOverallStatistics();
    this.getLeftData();

    // var tMonth = this.yearVal.getMonth();
    // this.$refs.mongthVBL.setMonth(tMonth);
    // this.monthVal=tMonth;
    // this.getNewEquipment();

  },
  data() {
    return {
      tagDefaultVAlue: '', //8206d472-c6d9-4e04-ab2c-3a6d95db129e
      detailId:'',
      listToTreeSelect,
      dialogdetailVisible:false,
      equipmentValue:vars.equipmentAge[0].value,
      equipmentAge:vars.equipmentAge,


      tabColumnsEquipmentUse: [
        {
          attr: { prop: "EquipmentUseName", label: "设备名称",}
        },
        {
          attr: { prop: "Count", label: "设备数量", }
        },
        {
          attr: { prop: "Ratio", label: "占比",width:230},
          slot: true
        },
      ],
      tabDatasEquipmentUse: [],
      totalEquipmentUse: 0,

      regionalId:'',

      //生产厂家分析 查询条件
      factoryAnalysisListQuery: {
        PageIndex:1,
        PageSize:15,
        regionalId:'',
        keyWords: '',
      },
      //用途分类 查询条件
      equipmentUseListQuery: {
        PageIndex:1,
        PageSize:15,
        regionalId: null,
        Keywords: '',
      },

      //设备年龄整体分析 查询条件
      ageAnalysisListQuery: {
        regionalId: null,
      },

      //供风方式 查询条件
      equipmentWorkModeListQuery: {
        regionalId: null,
        Keywords: '',
      },
      total:0,
      leftLoading:false,
      rightLoading:false,
      tabColumns: [
        {
          attr: { prop: "ranking", label: "序号",width:80, align: 'center'},
          slot:true
        },
        {
          attr: { prop: "Manufacturer", label: "生产厂家名称",}
        },
        {
          attr: { prop: "Count", label: "设备数量", }
        },
        {
          attr: { prop: "Ratio", label: "占比",width:230},
          slot:true
        },
      ],
      equipmentColumns:[
        {
          attr: { prop: "RegionalName", label: "所在地区"},
        },
        {
          attr: { prop: "Count", label: "设备数量"}
        },
        {
          attr: { prop: "oper", label: "操作" },
          slot:true
        }
      ],
      equipmentDatas:[],
      tabDatas: [{
        EquipmentWorkModeName:50
      }],
      overallData:[
        { label: '设备总数', count: 0, value: 'RegionEquipment' },
        { label: '设备报修', count: 0, value: 'MaintenanceEquipment' },
        { label: '保修情况', count: 0, value: 'warrantySituation' }
      ],
      types: [{value: 1, label: '新增设备统计'}, {value: 2, label: '设备数据分析'}],
      treeData:[],
      nationalData:0,
      topData:[],
      loading4:false,
      loading5: false,
      loading6: false,
      loading7: false,
      // myChart:null,
      // myChart1:null,
      // myChart2:null,
      // myChart3:null,
      region: '',
      regionList:[],
      areaList:[],
      devicesNum:0,
      equipmentData:[],
      typeNum:0,
      equipmentType:[],
      yearVal:new Date(),
      areaData:null,
      areaId:'',
      typeId:'',
      firstUpdate:true,
      saveOverAllId:'全部',
      // myChartOption:null,
      // myChartOption1:null,
      // myChartOption2:null,
      // quantityAnalysisList:[],
      // quantityAnalysis:0,
      // typeAnalysisList:[],
      // typeAnalysis:0,

      //地区选择器弹框
      regionalSelecorVisible: false,
      //点击弹框的目标
      targetKey: '',
      //目标地区id
      targetRegionalId: null,
      //目标地区名称
      targetRegionalName: '',
      
    };
  },

  methods:{
    getColor(index){
      let n=(this.factoryAnalysisListQuery.PageIndex-1)*this.factoryAnalysisListQuery.PageSize+index;
      return  n== 1 ? '#F59A23' : (n == 2 ? '#409EFF' : (n == 3 ? '#00CC00' : ''));
    },
    handleDetail(d){
      this.detailId=d.RegionalId;
      this.dialogdetailVisible=true;
    },
    handleClosedetail(){
      this.dialogdetailVisible=false;
    },
    handleCurrentChange(val) {
      this.factoryAnalysisListQuery.PageIndex = val.page;
      this.factoryAnalysisListQuery.PageSize = val.size;
      this.getLeftData();
    },
    handleSizeChange(val) {
      this.factoryAnalysisListQuery.PageSize = val.size;
      this.getLeftData();
    },
    // getTypeEquipment(){
    //   this.loading3=true;
    //   let params={
    //     year:new Date(this.yearVal).getFullYear(),
    //     month:this.monthVal,
    //     regionalId:this.region,
    //   }
    //   businessMap.getRegionEquipmentTypeAnalyzeChartAsync(params).then(res => {
    //     this.typeAnalysisList=res;
    //     vars.lengther=9;
    //     vars.pieOption.legend.data=[];
    //     vars.pieOption.series[0].data=[];
    //     this.myChart4 = echarts.init(document.getElementById('myChart4'));
    //     this.myChart4.hideLoading();
    //     this.typeAnalysis=0;
    //     if(res.length>0){

    //       res.forEach(v => {
    //         this.typeAnalysis+=v.Total;
    //         this.pieDataassembly(vars.pieOption.legend.data,vars.pieOption.series[0].data,v.ProductName,v.Total,true);
    //       })
    //       this.showNodata(vars.pieOption.series[0].data.length,this.myChart4);
    //     }else{
    //       this.showNodata(0,this.myChart4);
    //     }
    //     vars.pieOption.title.show=false;
    //     this.getOther(vars.pieOption);
    //     this.myChart4.setOption(vars.pieOption,true);
    //     this.boxResize(this.myChart4);
    //     this.loading3=false;
    //   }).catch(err => {
    //     this.loading3=false;
    //   })
    // },
    // getRegionEquipment(){
    //   this.loading2=true;
    //   let params={
    //     year:new Date(this.yearVal).getFullYear(),
    //     month:this.monthVal,
    //     regionalId:this.areaId,
    //   }

    //   businessMap.getRegionEquipmentCountAnalyzeChartAsync(params).then(res => {
    //     this.region=this.areaId;
    //     this.regionList=[{label:'全部',value:this.areaId}];
    //     this.$refs.toa.chioseFirst(this.areaId);
    //     this.quantityAnalysisList=res;
    //     vars.lengther=11;
    //     vars.pieOption.legend.data=[];
    //     vars.pieOption.series[0].data=[];
    //     this.myChart3 = echarts.init(document.getElementById('myChart3'));
    //     this.myChart3.hideLoading();
    //     this.quantityAnalysis=0;
    //     if(res.length>0){

    //       res.forEach(v => {
    //         this.regionList.push({label:v.RegionalName,value:v.RegionalId});
    //         this.quantityAnalysis+=v.Total;
    //         this.pieDataassembly(vars.pieOption.legend.data,vars.pieOption.series[0].data,v.RegionalName,v.Total,true);
    //       })
    //       this.showNodata(vars.pieOption.series[0].data.length,this.myChart3);
    //     }else{
    //       this.showNodata(0,this.myChart3);
    //     }
    //     vars.pieOption.title.show=false;
    //     this.getOther(vars.pieOption);
    //     this.myChart3.setOption(vars.pieOption,true);
    //     this.boxResize(this.myChart3);
    //     this.loading2=false;
    //   }).catch(err => {
    //     this.loading2=false;
    //   })
    // },
    showNodata(l,box){
      if(l<=0){
        box.showLoading({
          text: '暂无数据',
          color: '#ffffff',
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 0)',
          }
        );
      }
    },
    getOther(o){
      if(o.legend.data.length>10){
        let allValue=0;
        o.series[0].data.forEach(v => {
          allValue+=v.value;
        })
        let isOther=o.series[0].data.some(v => (v.value/allValue<0.05));
        if(isOther){
          let otherValue=0;
          o.series[0].data.forEach((v,i) => {
            if(v.value/allValue < 0.05){
              otherValue+=v.value;
            }
          })
          let sdO=o.series[0].data.filter(v => (v.value/allValue >= 0.05));
          sdO.push({
            label: {show: true, position: "inside"},
            name: "其它("+otherValue+')',
            value: otherValue
          })
          o.legend.data=[];
          sdO.forEach(v => {
            o.legend.data.push(v.name);
          })
          o.series[0].data=sdO;
        }
      }
    },
    sum(arr) {
      var len = arr.length;
      if(len == 0){
        return 0;
      } else if (len == 1){
        return arr[0];
      } else {
        return arr[0] + sum(arr.slice(1));
      }
    },

    getRegional(){
      this.leftLoading=true;
      this.rightLoading=true;
      regionalManagement.getPoorListByCondition({RegionalName:"",Level:1}).then(res => {
        // 测试
        // let first = res[0]
        // res.splice(0, 1)

        let temp = JSON.parse(JSON.stringify(tagTemp))
        // temp.value = first.Id

        let result = res.map(s => {
          return {
            value: s.Id,
            label: s.RegionalName
          }
          // s.value = s.Id
          // s.label = s.RegionalName
          // return s
        });
        this.areaList = [temp].concat(result)

        this.regionalId=this.areaList[0].value;
        this.getLeftData();
        

      })
    },
    getLeftData(){
      this.leftLoading=true;
      let postDatas = JSON.parse(JSON.stringify(this.factoryAnalysisListQuery))
      if(!postDatas.regionalId){
        delete postDatas.regionalId
      }
      businessMap.getEquipmenManufacturerAnalysisChart(postDatas).then(res => {
        this.leftLoading=false;
        this.tabDatas=res.Items;
        this.total=res.Total;
      })
    },
    getRightData(){
      this.rightLoading=true;
      let postDatas = {
        year: this.equipmentValue
      }
      let regId = this.factoryAnalysisListQuery.regionalId
      if(regId) {
        postDatas.regionalId = regId
      }

      businessMap.getEquipmenAgeAnalysisChart(postDatas).then(res => {
        this.rightLoading=false;
        this.equipmentDatas=listToTreeSelect(res,undefined,undefined,{key: 'RegionalId',parentKey: 'ParentId'},'RegionalName');
      })
    },
    // chartTypeChange2(d){
    //   this.region=d.value;
    //   this.getTypeEquipment();
    // },
    boxResize(box){
      this.$nextTick(() => {
        box.resize();
      })
      window.addEventListener('resize',function(){
        box.resize();
      })
    },
    // getEquipmentData(d){
    //   this.areaId=d.RegionalId;
    //   this.region=this.areaId;
    //   this.getRegionEquipment();
    //   this.getTypeEquipment();
    // },
    getLabelByAge(age) {
      let obj = this.equipmentAge.find(s => s.value == age)
      if(obj) {
        return obj.label
      }
      return '无'
    },
    handleSelectChange(d){
      this.getRightData();
    },
    getOverallStatistics(){
      this.loading4=true;

      let regId = this.factoryAnalysisListQuery.regionalId

      businessMap.getEquipmentDataAnalysisChartAsync({regionalId: regId ? regId : null}).then(res => {
        res.warrantySituation={
          NotWarrantyCount:res.NotWarrantyCount,
          WarrantyCount:res.WarrantyCount,
          ExpirationDateCount:res.ExpirationDateCount,
          OverCount:res.OverCount
        };
        delete res.OverCount;
        delete res.NotWarrantyCount;
        delete res.WarrantyCount;
        delete res.ExpirationDateCount;
          this.initChart(res)
          this.loading4=false;
      }).catch(err => {
        this.loading4=false;
      })
    },
    getAgeAnalysis(){
      
      let postDatas = JSON.parse(JSON.stringify(this.ageAnalysisListQuery))
      if(!postDatas.regionalId) {
        delete postDatas.regionalId
      }

      this.loading5=true;
      businessMap.getAllEquipmenAgeAnalysisChart(postDatas).then(res => {
        this.loading5=false;
        this.initChartAgeAnalysis(res)
      }).catch(err => {
        this.loading5=false;
      })
    },
    getEquipmentUse(){

      let postDatas = JSON.parse(JSON.stringify(this.equipmentUseListQuery))
      if(!postDatas.regionalId) {
        delete postDatas.regionalId
      }

      this.loading6=true;
      businessMap.getEquipmentUse(postDatas).then(res => {
        this.loading6=false;
        // this.initChartEquipmentUse(res)

        this.tabDatasEquipmentUse = res.Items
        this.totalEquipmentUse = res.Total

      }).catch(err => {
        this.loading6=false;
      })
    },
    handleCurrentChangeEquipmentUse(val) {
      this.equipmentUseListQuery.PageIndex = val.page;
      this.equipmentUseListQuery.PageSize = val.size;
      this.getEquipmentUse();
    },
    handleSizeChangeEquipmentUse(val) {
      this.equipmentUseListQuery.PageSize = val.size;
      this.getEquipmentUse();
    },

    getEquipmentWorkMode(){
      let postDatas = JSON.parse(JSON.stringify(this.equipmentWorkModeListQuery))
      if(!postDatas.regionalId){
        delete postDatas.regionalId
      }
      this.loading7=true;
      businessMap.getEquipmentWorkMode(postDatas).then(res => {
        this.loading7=false;
        this.initChartEquipmentWorkMode(res)
      }).catch(err => {
        this.loading7=false;
      })
    },
    
    pieDataassembly(ld,sd,n,t,s){
      ld.push(s ? n+'('+t+')' : n);
      sd.push({
        value: t,
        name: s ? n+'('+t+')' : n,
        label:{show:t > 0 ? true : false,position:'inside'}
      })
    },
    initChart(res,index=''){
      for(var key in res){
            vars.pieOption.legend.data=[];
            vars.pieOption.series[0].data=[];
              let n=0;
            if(key == 'RegionEquipment' || key == 'MaintenanceEquipment'){
              res[key].forEach(v => {
                n+=v.Total;
                this.pieDataassembly(vars.pieOption.legend.data,vars.pieOption.series[0].data,v.RegionalName,v.Total,true);
              })

              let temp = this.overallData.find(s => s.value == key)
              if(temp) {
                temp.count = n
              }
              // if(key == 'RegionEquipment') {
              //   vars.pieOption.title.text='设备总数('+n+')'
              // }else{
              //   vars.pieOption.title.text='设备报修('+n+')';
              // }
            }else if(key == 'warrantySituation'){
              // vars.pieOption.title.text='保修情况';
              vars.pieOption.legend.data=
              ['在保未知有效期('+res[key].ExpirationDateCount+')',
              '在保('+res[key].WarrantyCount+')',
              // '过保('+res[key].OverCount+')',
              '非在保('+res[key].NotWarrantyCount+')']
              vars.pieOption.series[0].data=[{
                  value: res[key].ExpirationDateCount,
                  name: '在保未知有效期('+res[key].ExpirationDateCount+')',
                  label:{show:res[key].ExpirationDateCount > 0 ? true : false,position:'inside'}
                },{
                  value: res[key].WarrantyCount,
                  name: '在保('+res[key].WarrantyCount+')',
                  label:{show:res[key].WarrantyCount > 0 ? true : false,position:'inside'}
                },
                // {
                //   value: res[key].OverCount,
                //   name: '过保('+res[key].OverCount+')',
                //   label:{show:res[key].OverCount > 0 ? true : false,position:'inside'}
                // },
                {
                  value: res[key].NotWarrantyCount,
                  name: '非在保('+res[key].NotWarrantyCount+')',
                  label:{show:res[key].NotWarrantyCount > 0 ? true : false,position:'inside'}
                },]
            }

            vars.pieOption.series[0].data.forEach(v => {
              v.label.show=false;
            })
            let box=echarts.init(document.getElementById('myChart'+key+index));
            box.hideLoading();
            vars.pieOption.legend.left='60%';
            vars.pieOption.series[0].radius='66%';
            vars.pieOption.series[0].center[0]='30%';
            // vars.pieOption.title.show=true;
            vars.pieOption.legend.height='200';
            this.showNodata(vars.pieOption.series[0].data.length,box);
            box.setOption(vars.pieOption,true);
            this.boxResize(box);
      }
    },
    initChartAgeAnalysis(res) {
      vars.pieOption.legend.data=[];
      vars.pieOption.series[0].data=[];
      let n = 0;
      res.forEach(v => {
        n+=v.Count;
        let tempLabel = this.getLabelByAge(v.EquipmentAge)
        this.pieDataassembly(vars.pieOption.legend.data,vars.pieOption.series[0].data,tempLabel,v.Count,true);
      })
      vars.pieOption.title.text='设备年龄整体分析('+n+')'
      vars.pieOption.series[0].data.forEach(v => {
        v.label.show=false;
      })
      let box=echarts.init(document.getElementById('myChartAgeAnalysis'));
      box.hideLoading();
      vars.pieOption.legend.left='60%';
      vars.pieOption.series[0].radius='66%';
      vars.pieOption.series[0].center[0]='30%';
      // vars.pieOption.title.show=true;
      vars.pieOption.legend.height='200';
      this.showNodata(vars.pieOption.series[0].data.length,box);
      box.setOption(vars.pieOption,true);
      this.boxResize(box);
    },


    initChartEquipmentWorkMode(res) {

      let options = JSON.parse(JSON.stringify(lineOptionTemp))

      options.yAxis.data = res.map(s => s.EquipmentWorkModeName)
      options.series[0].data = res.map(s => s.Count)

      //如果没有数据，需要隐藏坐标
      let dataLen = options.series[0].data.length
      options.xAxis.show = dataLen > 0
      options.yAxis.show = dataLen > 0

      let box=echarts.init(document.getElementById('myChartEquipmentWorkMode'));
      box.hideLoading();

      this.showNodata(dataLen,box);
      box.setOption(options,true);
      this.boxResize(box);
    },

    // 关闭 地区选择
    closeRegionalDialog() {
      this.regionalSelecorVisible = false;
    },
    // 展开 地区选择
    handleRegionalDialog(targetType, selectedRegional){
      this.targetKey = targetType
      this.targetRegionalId = selectedRegional
      this.regionalSelecorVisible = true;
    },
    // 地区选择 确定事件
    selectedRegional(data){
      if(data){
        let targetKey = this.targetKey
        let id = data.Id
        let name = data.ParentName
        this.targetRegionalId = id;
        this.targetRegionalName = name

        this[`${targetKey}ListQuery`].regionalId = id
        this[`${targetKey}ListQuery`].regionalName = name

        switch(targetKey) {
          case "factoryAnalysis":
            this.getLeftData();
            break;
          case "ageAnalysis":
            this.getAgeAnalysis()
            break;
          case "equipmentUse":
            this.getEquipmentUse()
            break;
          case "equipmentWorkMode":
            this.getEquipmentWorkMode()
            break;
        }
      }else{
        this.targetRegionalId = '';
        this.targetRegionalName = '';
      }
    },
  }
};
</script>

<style lang="scss" scoped>

.elBox{
  width:100%;
  height:100%;
  display:flex;
  align-content: center;
  >span{
    width:45px;
    text-align:right;
  }
}
.cRank{
  position:absolute;
  left:50%;
  top:50%;
  transform: translate(-50%,-50%);
  line-height:20px;
  text-align: center;
  border-radius: 50%;
  color:white;
  // background:#D7D7D7;
}

.fix-width{
  width:20px;
  height:20px;
}

// .barChartBox{
//   width:100%;
//   overflow-x: auto;
// }
// .tagBox{
//   border-bottom: 1px solid #EBEEF5;
//   padding: 4px 8px;
// }
// .app-container{
//   overflow-y:auto;
// }
// .main{
//   padding:10px;
//   padding-top: 0;

// }
// .monBottom{
//     margin-top: 10px;
//     margin-bottom: 20px;
//     border:1px solid #ccc;
//   }
//   .monBottom:last-child{
//     border:0;
//   }
// .chartBox1 .monBottom{
//   margin:0;
// }
// .chartBox{

//   >div{
//     width:50%;
//     height:400px;
//   }
// }
// .chartBox1{
//     margin-top:10px;
//   >div{
//     width:calc(50% - 10px);
//     height:455px;
//   }
//   >div:last-child{
//     margin-left:20px;
//   }
// }
.line-40{
  border-bottom: 1px solid #DCDFE6;
}
.blockTitle{
  background:#ccc;
  >span:first-child{
    font-size: 16px;
    font-weight: 600;
  }
}


// .firstPW{
//   >div:first-child{
//     display: flex;
//     align-items: center;
//     border-bottom: 1px solid #EBEEF5;
//     padding:8px 10px;
//     .vbl{
//       margin-left:10px;
//     }
//   }
// }
// .secondPW{
//   >div:first-child{
//     padding:0px 10px;
//   }
// }

.shadow{
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
}
.chartUl{
  width:100%;
  box-sizing: border-box;
  // padding: 0 10px;
  li{
    width:33.3333333%;
  }
  li:not(:last-child){
    border-right:1px solid #EBEEF5;
  }
}
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
        .treeBox {
            width: 100%;
            height: calc(100% - 10px);
            margin-top: 10px;
            padding-left: 10px;

        }
    }

    .content-wrapper {
        flex: 1;
        // padding: 0 10px;
        display: flex;
        flex-direction: column;
        ::v-deep .monBottom{
          margin: 10px;
          width: calc(100% - 20px);
        }
        /deep/.el-card__body{
          padding:5px;
        }

        .tabs-content{
          flex: 1;
          // display: flex;
          overflow: hidden;
          padding: 10px;
          .title{
            border-bottom: 1px solid #ebeef5;
            box-sizing: border-box;
            font-weight: 700;
            height: 40px;
            // padding: 0 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          // .lft, .rht{
          //   border: 1px solid #ebeef5;
          //   box-sizing: border-box;
          //   flex-shrink: 0;
          //   // margin: 10px;
          // }
          // .lft{
          //   width: 48%;
          //   margin-right: 5px;
          // }
          // .rht{
          //   // flex: 1;
          //   width: 52%;
          //   margin-left: 10px;


          // }
        }
    }
}

.part-regional{
  padding: 10px;
  overflow-y: auto;
  position: relative;
  .first-level{
    // display: flex;
    .lft, .rht{
      // border: 1px solid #ebeef5;
      box-sizing: border-box;
      // margin: 10px;
      
    }
    .lft{
      width: calc(66.6% - 5px);
      .chartUl{
        display: flex;
        padding: 10px 0;
        li{
          flex: 1;
        }
      }
    }
    .rht{
      width: calc(33.3% - 5px);
      margin-top: 10px;
      // flex: 1;
      // padding-left: 10px;
      // padding: 10px;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
    }
  }
  .second-level{
    margin-top: 10px;
    >div{
      width: calc(50% - 5px);
      min-height: 452px;
      box-sizing: border-box;
    }
    >div:first-child{
      margin-right: 5px;
    }
    >div:last-child{
      margin-left: 5px;
    }
  }


  
}
</style>