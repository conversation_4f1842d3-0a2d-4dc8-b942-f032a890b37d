<!--维修故障案例-->
<template>
  <div class="app-container">
    <div class="opt">
      <page-title title="维修故障案例" :showBackBtn='true' @goBack="back" text-bold></page-title>
    </div>
    <div class="bg-white">
      <div class="pageWrapper">
        <!--左侧树-->
        <div class="product-list">
          <el-input class="elInput" style="margin:5px 10px 5px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <div class="treeBox" v-loading='treeLoading'>
            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node }">
                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '170px' : node.level == 2 ? '180px' : '170px'}">{{ node.label }}</span>
              </span>
            </el-tree>
          </div>
        </div>
        <!--右侧内容-->
        <div class="content-wrapper __dynamicTabContentWrapper">
          <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="[]" :layoutMode='layoutMode' :isShowAllColumn="true" :isShowOpatColumn="true" :startOfTable="0" :multable='false' :isShowBtnsArea='false' :loading='listLoading' @sortChagned="handleSortChange">

              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                  <template slot="KeyWords">
                    <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索故障代码/故障名称"></el-input>
                  </template>

                  <template slot="FailureKeywords">
                    <el-input style="width: 100%;" v-model="listQuery.FailureKeywords" placeholder></el-input>
                  </template>

                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2" text="详情"></app-table-row-button>
              </template>
            </app-table>
          </div>
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>

      </div>
    </div>

    <v-ceda @closeDialog="closeCedaDialog" @saveSuccess="handleCedaSaveSuccess" :dialogFormVisible="dialogCedaFormVisible" :dialogStatus="dialogCedaStatus" :id="cedaId">
    </v-ceda>
  </div>
</template>

<script>
import * as failurecase from "@/api/failurecase";
import * as classify from '@/api/classify'
import elDragDialog from "@/directive/el-dragDialog";
import { listToTreeSelect } from "@/utils";
import indexPageMixin from "@/mixins/indexPage";
import vCeda from "./ceda";

export default {
  name: "failurecase",
  components: {
    vCeda,
  },
  directives: {
    elDragDialog
  },
  mixins: [indexPageMixin],
  computed: {
    indexSelectedMsg() {
      return this.$store.state.communication.indexSelectedMsg;
    },
    indexSelectedData() {
      return this.$store.state.communication.indexSelectedData;
    },
  },
  data() {
    return {
      layoutMode: 'simple',
      cedaId: '',
      dialogCedaFormVisible: false,
      dialogCedaStatus: 'create',
      /**查询内容 */
      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "FailureKeywords", label: "故障关键字" },
      ],
      /**列表查询参数 */
      listQuery: {
        FailureKeywords: "",
        Keywords: "",
        PageIndex: 1,
        PageSize: 20,
        EquipmentSettingIdList: []
      },
      /**列表加载中 */
      listLoading: false,
      /**列表数据 */
      tabDatas: [],
      /**列表栏目 */
      tabColumns: [
        {
          attr: { prop: "FailureCaseCode", label: "故障代码", sortable: 'FailureCaseCode' }
        },
        {
          attr: { prop: "FailureSymptom", label: "故障现象名称", showOverflowTooltip: true, width: 500 }
        },
        {
          attr: { prop: "ClassifyName", label: "分类", }
        },
        {
          attr: { prop: "Hot", label: "阅读量", sortable: 'Hot' }
        },
        {
          attr: { prop: "FaultKeywordsStr", label: "故障关键字", showOverflowTooltip: true, width: 300 }
        },
      ],
      /**总数 */
      total: 0,
      classification: false,

      /******************* 树 *******************/
      /**树节点弹窗 */
      classifyDialogFormVisible: false,
      classifyDialogStatus: "create",
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },
    };
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.ClassifyId = val.Id;
          this.listQuery.PageIndex = 1;
          this.getList();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.loadTreeData();
  },
  methods: {
    closeCedaDialog() {
      this.getList();
      this.dialogCedaFormVisible = false;
    },
    handleCedaSaveSuccess() {
      this.getList();
      this.dialogCedaFormVisible = false;
    },
    openFault() {
      this.classification = true;
    },
    closeFault() {
      this.classification = false;
    },

    /**切换页大小 */
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    /**切换页码 */
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleSortChange({ column, prop, order }) {
      this.sortObj = { prop, order, };
      this.getList();
    },

    /**查询 */
    handleFilter() {
      this.listQuery.EquipmentSettingIdList = [];
      this.listQuery.PageIndex = 1;
      if (this.indexSelectedData.length > 0) {
        let a = null;
        this.indexSelectedData.forEach(v => {
          if (v.checkList.length > 0) {
            v.children.forEach(v1 => {
              a = null;
              a = v.checkList.find(s => s == v1.Name);
              if (a) {
                this.listQuery.EquipmentSettingIdList.push(v1.Id);
              }
            })
          }
        })
      }
      this.getList();
    },
    resetSearch() {
      this.$store.commit('getIndexSelectedData', []);
      this.$store.commit('getIndexSelectedMsg', null);
      if (this.classification) {
        this.$refs.cLlabel.resetEmpty();
      }
      this.listQuery = {
        FailureKeywords: "",
        Keywords: "",
        PageIndex: 1,
        PageSize: 20,
        EquipmentSettingIdList: []
      }
      this.getList();
    },
    /**获取列表 */
    getList() {
      this.listLoading = true;
      if (this.checkedNode.Id) {
        this.listQuery.ClassifyId = this.checkedNode.Id;
      }
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData);
      failurecase.getListPage(postData).then(res => {
        this.tabDatas = res.Items;
        this.total = res.Total;
        this.listLoading = false;
      }).catch(err => {
        this.listLoading = false
      });
    },


    /**********************************弹出窗************************************/

    /**查询详情后弹窗 */
    handleUpdate(row, optType = "edit") {
      this.dialogCedaStatus = optType;
      this.cedaId = row.Id;
      this.dialogCedaFormVisible = true;
    },


    /******************* 树事件 *******************/
    loadTreeData() {
      let _this = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 3
      };
      _this.treeLoading = true
      classify.getListPage(paramData).then(response => {
        _this.treeLoading = false
        response.Items.unshift({
          Id: "",
          Name: "全部",
          Level: 0,
          ParentId: null
        });
        _this.treeData = listToTreeSelect(response.Items);

        if (_this.treeData && _this.treeData.length > 0) {
          if (
            !(
              _this.checkedNode &&
              response.Items.find(t => {
                return t.Id == _this.checkedNode.Id;
              })
            )
          ) {
            _this.checkedNode = _this.treeData[0];
          }
        } else {
          _this.checkedNode = null;
        }
        if (_this.checkedNode) {
          _this.$nextTick(() => {
            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
          });
        }
      }).catch(err => {
        _this.treeLoading = false
      });
    },

    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    back() {
      this.$router.go(-1)
    }
  }
};
</script>


<!--组件样式区-->
<style lang="scss" scoped>
.opt {
  top: 0;
  left: 0;
  right: 0;
  position: absolute;
}

.bg-white {
  position: absolute;
  width: 100%;
  height: 95%;
  overflow-y: auto;
  top: 40px !important;
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      //   padding: 10px;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
