<!--用车单位编辑-->
<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
      <template slot="body">
        <el-form :rules="formRules" ref="formData" :model="formModel" label-position="right" label-width="120px">
          <el-form-item label="用车单位名称" prop="Name">
            <el-input maxlength="30" :disabled="!editable" v-model="formModel.Name"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as carMgt from '@/api/personnelManagement/carCoordinator'

export default {
  /**名称 */
  name: "business-manager-area-edit",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
      default: "create"
    },
    //操作的节点，如果是新增，则为父节点；编辑为当前节点
    node: {
      type: Object,
      required: true
    }
  },
  /**数据区 */
  data() {
    return {
      goOn: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单模型 */
      formModel: { Name: "", Level: 1 },
      /**表单规则 */
      formRules: {
        Name: { fieldName: "用车单位名称", rules: [{ required: true }] }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加用车单位";
      } else if (this.dialogStatus == "update") {
        return "编辑用车单位";
      } else if (this.dialogStatus == "detail") {
        return "用车单位详情";
      }
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          this.goOn = false;
          if (this.dialogStatus == "create") {
            this.resetFormModel();
          } else {
            this.getDetail();
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() { },
  /**方法区 */
  methods: {
    resetFormModel() {
      let temp = {
        Id: "",
        Level: this.node.Level ? this.node.Level + 1 : 1,
        ParentId: this.node.Id,
        Name: "" //名称
      };
      this.formModel = Object.assign({}, this.formModel, temp);
    },
    getDetail() {
      this.formModel = Object.assign({}, this.formModel, this.node);
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formData.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;

          if (_this.dialogStatus == "create") {
            result = carMgt.addDepartment(_this.formModel);
          } else if (_this.dialogStatus == "update") {
            result = carMgt.editDepartment(_this.formModel);
          }

          result.then(response => {
            _this.buttonLoading = false;
            _this.$notify({
              title: "成功",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            if (this.goOn) {
              this.resetFormData();
              this.$refs['formData'].resetFields();
            }
            this.$emit('saveSuccess', this.goOn);
            // _this.$refs.appDialogRef.createData();
          }).catch(err => {
            _this.buttonLoading = false;
          });
        } else {
          return false;
        }
      });
    },

    resetFormData() {
      let temp = { Name: "", Level: 1 };
      this.formData = Object.assign({}, this.formData, temp);
    },

    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


