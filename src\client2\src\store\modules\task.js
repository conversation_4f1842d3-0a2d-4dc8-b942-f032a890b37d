const task = {
  state: {
    /**
     * 调用任务弹框消息
     * busType: 'task' //弹框业务类型，固定值（预留）
     * dialogType: 'create', //弹框状态，create、update、detail 等
     * params: {}, //弹框需要的参数，一般根据 dialogType 来定；例如：创建可能需要 parentId；详情一定要 id 等；
     * 具体参数是否支持，需要参考消费页面 App.vue，这里只是传过去。
     */
    taskMsg: null,
    /**
     * 任务弹框操作成功设置消息对象
     * 操作任务状态：获取到该对象，表示任务弹框相关操作成功
     */
    taskOptStatus: null,
  },
  mutations: {
    /**
     * 创建任务弹框消息设置
     * {
     *    busType：task（任务弹框）、temp（任务模板弹框）、taskAssign（任务指派单弹框）
     *    optType：create（目前只适配创建）
     *    params：根据 busType、optType 设置的对应参数
     * }
     * @param {*} state 
     * @param {*} param1 
     */
    TASK_SET_OPEN_DIALOG(state, { busType = 'task', optType = "create", params = {} }) {

      let obj = {
        busType: busType,
        optType: optType,
        params,
      };
      state.taskMsg = obj;

      //弹框消息发出后，立马清空（不需要额外编写维护逻辑），收到弹框消息后，消费页面会立马打开弹框；
      //若当前不处于消费页面，则说明不需要发出弹框消息（清空也符合逻辑）
      setTimeout(() => {
        state.taskMsg = null;
      }, 100);
    },

    /**
     * 任务操作成功消息对象
     * @param {*} state
     * @param {*} optType 操作类型：create
     * @param {*} params 备用参数，操作完成后，接收放可能需要参数
     */
    TASK_SET_OPT_STATUS(state, { busType = 'task', optType = "create", params = {} }) {

      let obj = {
        busType: busType,
        optType: optType,
        params,
      };
      state.taskOptStatus = obj;

      setTimeout(() => {
        state.taskOptStatus = null;
      }, 100);
    },
  },
  actions: {},
};

export default task;
