<!--软件实施指南详情-->
<template>
  <div>
    <app-dialog title="软件实施指南详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100" :maxHeight="600">
      <template slot="body">
        <el-form ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">

          <el-form-item label="实施指南分类" prop="ClassifyId">
            <span v-html="formData.ClassifyName"></span>
          </el-form-item>

          <el-form-item label="实施指南名称" prop="GuideName">
            <span v-html="formData.GuideName"></span>
          </el-form-item>

          <el-form-item label="实施指南详情" prop="GuideDetail">
            <div class="divUeditor ql-editor" style="padding-top:5px;padding-left: 0px;" v-viewer v-html="formData.GuideDetail"></div>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script> 
import * as guide from '@/api/informationCenter/implementationGuide'
import * as classify from '@/api/classify';
import { listToTreeSelect } from "@/utils";
export default {
  name: "create",
  directives: {},
  components: {
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    selectClassifyId: {
      type: String,
      default: ""
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.ClassifyList = [];
      }
      if (val) {

        this.resetFormData();
        this.getClassifyList();
      }
    }
  },
  computed: {},
  mounted() { },
  created() { },
  data() {

    return {
      disabledBtn: false,

      ClassifyList: [],

      formLoading: false,

      labelWidth: "120px",
      formData: {
        Id: "",
        ClassifyId: null,
        GuideName: "",
        GuideDetail: "",
      }
    };
  },
  methods: {

    //清理表单
    resetFormData() {
      let temp = {
        Id: "",
        ClassifyId: null,
        GuideName: "",
        GuideDetail: "",
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    //获取分类下拉框
    getClassifyList() {
      this.formLoading = true;
      classify.getListByCondition({ BusinessType: 1 }).then(res => {
        var departments = res.Items.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.Name,
            ParentId: item.ParentId
          };
        });
        this.ClassifyList = listToTreeSelect(departments);
        if (this.selectClassifyId) {
          this.formData.ClassifyId = this.selectClassifyId;
        }
        if (this.dialogStatus != "create" && this.id) {
          this.$nextTick(() => {
            this.getDetail();
          });
        } else {
          this.formLoading = false;
        }
      }).catch(err => {
        this.formLoading = false;
      });
    },

    //获取详情
    getDetail() {
      guide.detail({ id: this.id, isAddPageView: true }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        this.formLoading = false;
      }).catch(err => {
        this.formLoading = false;
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
