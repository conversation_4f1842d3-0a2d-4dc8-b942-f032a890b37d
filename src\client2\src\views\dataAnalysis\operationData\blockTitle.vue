<template>
    <div class="title-wrapper" @click="handleClick">
        <div class="title-content">
            <img class="title-img" :src="obj.imgPath" alt="" srcset="">
            <!-- <svg-icon class="title-img" :icon-class="obj.value"></svg-icon> -->
            <span>{{ obj.title }}</span>
        </div>
        <slot class="rht" name='rht'></slot>
    </div>
</template>

<script>
export default {
    name: 'block-title',
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    data() {
        return {

        }
    },
    methods: {
        handleClick() {
            this.$emit('click', this.obj.value)
        },
    },
}
</script>

<style lang="scss" scoped>
.title-wrapper{
    pointer-events: auto;
    cursor: pointer;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    margin-bottom: 10px;
    .title-content{
        display: flex;
        align-items: center;
        .title-img{
            font-size: 34px;
            margin-right: 10px;
            width: 34px;
            height: 34px;
        }
        span{
            font-weight: bold;
        }
    }
    .rht{
        flex: 1;
    }
}

</style>