<template>
    <div class="app-container">
        <div class="bg-white">
            <!-- <page-title :title="pageTitle" :subTitle="[pageDesc]" :showBackBtn="!!returnUrl" @goBack="handleGoBack"></page-title> -->
            <div class="page-wrapper">
                <div class="product-list">
                    <page-title title="物料分组"></page-title>
                    <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" clearable placeholder="输入关键字进行过滤" v-model="filterText"></el-input>

                    <div class="treeBox" v-loading='treeLoading'>
                        <div class="elTree">
                            <tags mode="list" v-if="treeData.length>0" :items="treeDataComp" v-model="listQuery.FStockId" @change="getList()"></tags>
                        </div>
                        <!-- <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node, data }">
                                <span class="node-title" :title="node.label">{{ node.label }}</span>
                            </span>
                        </el-tree> -->
                    </div>
                </div>
                <div class="content-wrapper __dynamicTabContentWrapper">
                    <page-title title="物料信息"></page-title>
                    <div class="content __dynamicTabWrapper">
                        <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable="false" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                                    <template slot="Keywords">
                                        <el-input style="width: 100%;" 
                                            placeholder="输入物料名称/编码/规格型号"
                                            @clear='handleFilter'
                                            v-antiShake='{
                                                time: 300,
                                                callback: () => {
                                                    handleFilter()
                                                }
                                            }' 
                                            clearable 
                                            v-model="listQuery.Keywords"
                                        ></el-input>
                                    </template>

                                    <!-- 表格批量操作区域 -->
                                    <template slot="btnsArea">
                                        
                                    </template>
    
                                </app-table-form>
                            </template>
    
                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button @click="handleDialog('detail', scope.row)" :type="2"></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                    <el-pagination
                      class="pagination-container"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page.sync="listQuery.PageIndex"
                      :page-sizes="[10, 20, 30, 40, 50, 100]"
                      :page-size="listQuery.PageSize"
                      layout="sizes, prev, pager, next"
                      :total="total"
                      background
                    />
                </div>
            </div>
        </div>
        
    
        <app-dialog
            title="选择创建标签类型"
            ref="appDialogRef2"
            @closeDialog="closeTagTypeDialog"
            :dialogFormVisible="dialogTagTypeFormVisible"
            :width="400"
            >
            <template slot="body">
                xxxx
            </template>
            <template slot="footer">
                <div>
                    <!-- 取消 -->
                    <app-button @click="closeTagTypeDialog" :buttonType="2"></app-button>
                </div>
            </template>
        </app-dialog>
    
    </div>
</template>
    
<script>
// import pageTitle from '../common/pageTitle'
// import tags from '../common/tags'
import indexPageMixin from "@/mixins/indexPage";
// import * as customerTagClassify from "@/api/salesMgmt/customerTagClassify";
import * as odc from "@/api/operatingDataCenter";
// import { listToTreeSelect } from "@/utils";

export default {
    name: "materialCodeQueryIndex",
    mixins: [indexPageMixin],
    components: {
        // pageTitle,
        // tags,
    },
    computed: {
        treeDataComp() {
            return this.treeData.filter(s => !this.filterText || s.label.indexOf(this.filterText) > -1)
        },
    },
    created() {
        // this.getSalesman()
        this.loadTreeData();
        this.getList();

    },
    watch: {
        // filterText(val) {
        //     this.$refs.treeRef.filter(val);
        // },
    },
    filters: {


    },
    mounted() {},
    data() {
        return {
            layoutMode: 'simple',

            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,

            id: "",
            total: 0,
            listLoading: false,
            listQuery: {
                Keywords: '',
                FStockId: '',
            },
            multipleSelection: [],
            tableSearchItems: [
                {
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },
            ],
            tabColumns: [
                {
                    attr: {
                        prop: "FNumber",
                        label: "物料编码",
                        width: "120",
                        // sortable: 'custom'
                    },
                },
                {
                    attr: {
                        prop: "FName",
                        label: "物料名称",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "FSpecification",
                        label: "规格型号",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "FBaseUnitName",
                        label: "基本单位",
                        width: "80",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "FDocumentStatusName",
                        label: "数据状态",
                        width: "80",
                        // sortable: 'custom'
                    },
                },
                {
                    attr: {
                        prop: "FMnemonicCode",
                        label: "助记码",
                        width: "120",
                    },
                },
                {
                    attr: {
                        prop: "FErpClsIDName",
                        label: "物料属性",
                        width: "80",
                    },
                },
                {
                    attr: {
                        prop: "FPurchaseUnitName",
                        label: "采购单位",
                        width: "80",
                    },
                },
                {
                    attr: {
                        prop: "FForbidStatusName",
                        label: "禁用状态",
                        width: "80",
                    },
                },
                {
                    attr: {
                        prop: "FRefStatusName",
                        label: "已使用",
                        width: "80",
                    },
                },
                
            ],
            tabDatas: [],

            // salesman: [],
            tagType: 0, //标签类型
            dialogTagTypeFormVisible: false,



        };
    },
    methods: {



        resetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize,
                FStockId: '',
                Keywords: "",
            };
            this.getList();
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let _this = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1
            };
            _this.treeLoading = true

            odc.getStock(paramData).then(res => {
                _this.treeLoading = false

                this.treeData = res.map(s => {
                    return {
                        label: s.FName,
                        value: s.FStockId,
                    }
                })

                this.treeData.splice(0, 1, {label: '全部', value: ''})
            }).catch(err => {
                _this.treeLoading = false
            })
        },

        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        handleAdd() {
            this.tagType = 1
            this.dialogTagTypeFormVisible = true
        },
        closeTagTypeDialog() {
            this.dialogTagTypeFormVisible = false
        },

        handleCurrentChange(val) {
            this.listQuery.PageIndex = val;
            this.getList();
        },
        handleSizeChange(val) {
          this.listQuery.PageIndex = 1;
          this.listQuery.PageSize = val;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        // handleNav(type, proj) {
        //   this.$router.push({
        //     path: `/projectDev/projectMgmt/${type}/index?productId=${proj.productId}&projectId=${proj.projectId}`
        //   });
        // },

        // handleResetSearch() {
        //     this.listQuery = {
        //         // 否则手动重置查询条件
        //         PageIndex: this.listQuery.PageIndex,
        //         PageSize: this.listQuery.PageSize,
        //         Name: "",
        //     };
        //     this.getList(); //刷新列表
        // },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        //获取项目列表
        getList() {

            console.log('调用列表接口————————————————————')

            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);

            odc.getMaterialListPage(postData).then(res => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                // 处理分页
                this.total = res.PageCount * res.PageSize;
            })

        },


    },
};
</script>

<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            // top: 40px;
            top: 0;
            right: 0;
            bottom: 0;
            .product-list {
                width: 250px;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }

                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;
                    

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        padding-left: 10px;
                        height: 100%;
                        overflow: auto;
                        /deep/.tag-common{
                            padding-left: 10px;
                            padding-right: 10px;
                        }
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 200px);
                flex: 1;
                overflow-y: auto;

                .content {

                    // padding: 10px;
                    // padding-right: 0;
                    .opt-wrapper {
                        box-sizing: border-box;
                        border-bottom: 1px solid #dcdfe6;
                        padding-bottom: 10px;
                    }

                    .list {}
                }
            }
        }
    }
}


.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}



</style>
    