<template>
  <div class="failureCase">
    <div class="app-container">
      <div class="bg-white">
        <div class="pageWrapper">
          <!--左侧树-->
          <div class="product-list"><br v-if="btnAddChildren==''" />
            <el-button v-if="btnAddChildren=='btnAddChildren'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">创建分类</el-button>
            <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            <div class="treeBox" v-loading='treeLoading'>
              <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                  <span v-if="data.Level>0 && node.label !='默认'" class="node-btn-area">
                    <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                      <span class="el-dropdown-link">
                        <i class="el-icon-more"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                        <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                        <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </span>
                </span>
              </el-tree>
            </div>
          </div>
          <!--右侧内容-->
          <div class="content-wrapper __dynamicTabContentWrapper">
            <div class="content __dynamicTabWrapper">
              <app-table ref="mainTable" @rowSelectionChanged="rowSelectionChanged" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="[]" :layoutMode='layoutMode' :isShowAllColumn="true" :isShowOpatColumn="true" :startOfTable="0" :multable='true' :isShowBtnsArea='false' :loading='listLoading' @sortChagned="handleSortChange">

                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                  <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                    <template slot="KeyWords">
                      <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索故障代码/故障名称"></el-input>
                    </template>

                    <template slot="FailureKeywords">
                      <el-input style="width: 100%;" v-model="listQuery.FailureKeywords" placeholder></el-input>
                    </template>

                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                      <permission-btn :filterBtn='handleFilterBtn' moduleName="failurecasetype" v-on:btn-event="onBtnClicked">
                        <el-dropdown slot="btnBatchOpt" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                          <el-button type="primary">
                              {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                          </el-button>
                          <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item command="updateType">修改分类</el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </permission-btn>
                    </template>

                  </app-table-form>
                </template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                  <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2" text="详情"></app-table-row-button>
                  <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1">
                  </app-table-row-button>
                  <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                </template>
              </app-table>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
          </div>

        </div>
      </div>
    </div>
    <v-ceda @closeDialog="closeCedaDialog" @saveSuccess="handleCedaSaveSuccess" :dialogFormVisible="dialogCedaFormVisible" :dialogStatus="dialogCedaStatus" :selectClassifyId="selectClassifyId" :id="cedaId">
    </v-ceda>

    <!--添加/修改 分类 弹窗组件区-->
    <classify-page :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyDialogFormVisible" @closeDialog="classifyCloseDialog" @saveSuccess="classifySaveSuccess"></classify-page>

    <batch-update v-if="dialogbatchVisible && multipleSelection.length > 0" :batchType='3' @closeDialog="()=>dialogbatchVisible=false" @saveSuccess="handleSaveBatchSuccess" :dialogFormVisible="dialogbatchVisible" :ids="multipleSelection.map(s => s.Id)"></batch-update>


  </div>
</template>


<script>
import * as failurecase from "@/api/failurecase";
import EquipmentArea from "../common/equipmentArea";
import ParamAdd from "../common/reptofailurecase/paramAdd";
import * as classify from '@/api/classify'
import classifyPage from "./classify";
import elDragDialog from "@/directive/el-dragDialog";
import { listToTreeSelect } from "@/utils";
import indexPageMixin from "@/mixins/indexPage";
import SymptomSelector from '../common/repairOrder/symptomSelector';
import classLabel from "./common/classificationLabel";
import vCeda from "./common/ceda";
import batchUpdate from './batchUpdate'

export default {
  name: "failurecase",
  components: {
    ParamAdd,
    EquipmentArea,
    SymptomSelector,
    classLabel,
    vCeda,
    classifyPage,
    batchUpdate,
  },
  directives: {
    elDragDialog
  },
  mixins: [indexPageMixin],
  computed: {
    indexSelectedMsg() {
      return this.$store.state.communication.indexSelectedMsg;
    },
    indexSelectedData() {
      return this.$store.state.communication.indexSelectedData;
    },
  },
  data() {
    return {
      layoutMode: 'simple',
      
      cedaId: '',
      dialogCedaFormVisible: false,
      dialogCedaStatus: 'create',
      selectClassifyId: "",
      /**查询内容 */
      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "FailureKeywords", label: "故障关键字" },
        //  { prop: "op", label: "故障分类标签" },
      ],
      /**列表查询参数 */
      listQuery: {
        FailureKeywords: "",
        Keywords: "",
        PageIndex: 1,
        PageSize: 20,
        EquipmentSettingIdList: []
      },
      /**列表加载中 */
      listLoading: false,
      /**列表数据 */
      tabDatas: [],
      /**列表栏目 */
      tabColumns: [
        {
          attr: { prop: "FailureCaseCode", label: "故障代码", sortable: 'FailureCaseCode' }
        },
        {
          attr: { prop: "FailureSymptom", label: "故障现象名称", showOverflowTooltip: true, width: 500 }
        },
        {
          attr: { prop: "ClassifyName", label: "分类", }
        },
        {
          attr: { prop: "FaultKeywordsStr", label: "故障关键字", showOverflowTooltip: true, width: 300 }
        },
      ],
      /**总数 */
      total: 0,
      classification: false,

      /******************* 权限 *******************/
      btnAddChildren: '',
      btnAdd: '',
      btnEdit: '',


      /******************* 树 *******************/
      /**树节点弹窗 */
      classifyDialogFormVisible: false,
      classifyDialogStatus: "create",
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },

      multipleSelection: [],
      dialogbatchVisible: false,

    };
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.ClassifyId = val.Id;
          this.listQuery.PageIndex = 1;
          this.getList();
        }
      },
      immediate: true
    }
  },
  created() {
    this.btnTextValue();
  },
  mounted() {
    this.loadTreeData();
  },
  methods: {
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    closeCedaDialog() {
      this.dialogCedaFormVisible = false;
    },
    handleCedaSaveSuccess() {
      this.getList();
      this.dialogCedaFormVisible = false;
    },
    openFault() {
      this.classification = true;
    },
    closeFault() {
      this.classification = false;
    },

    /**按钮组 */
    handleAdd(type) {
      this.dialogCedaStatus = type;
      this.selectClassifyId = this.checkedNode.Id;
      this.dialogCedaFormVisible = true;

    },
    /**切换页大小 */
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    /**切换页码 */
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = { prop, order, };
      this.getList();
    },
    /**查询 */
    handleFilter() {
      this.listQuery.EquipmentSettingIdList = [];
      this.listQuery.PageIndex = 1;
      console.log(this.indexSelectedMsg, this.indexSelectedData)
      if (this.indexSelectedData.length > 0) {
        let a = null;
        this.indexSelectedData.forEach(v => {
          if (v.checkList.length > 0) {
            v.children.forEach(v1 => {
              a = null;
              a = v.checkList.find(s => s == v1.Name);
              if (a) {
                this.listQuery.EquipmentSettingIdList.push(v1.Id);
              }
            })
          }
        })
      }
      console.log(111, this.listQuery.EquipmentSettingIdList)
      this.getList();
    },
    resetSearch() {
      this.$store.commit('getIndexSelectedData', []);
      this.$store.commit('getIndexSelectedMsg', null);
      if (this.classification) {
        this.$refs.cLlabel.resetEmpty();
      }
      this.listQuery = {
        FailureKeywords: "",
        Keywords: "",
        PageIndex: 1,
        PageSize: 20,
        EquipmentSettingIdList: []
      }
      this.getList();
    },
    /**获取列表 */
    getList() {
      this.listLoading = true;
      if (this.checkedNode.Id) {
        this.listQuery.ClassifyId = this.checkedNode.Id;
      }
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData);
      failurecase.getListPage(postData).then(res => {
        this.tabDatas = res.Items;
        this.total = res.Total;
        this.listLoading = false;
      }).catch(err => {
        this.listLoading = false
      });
    },


    btnTextValue() {
      let btns = this.topBtns
      btns.forEach(item => {
        if (item["DomId"] == "btnAddChildren") {
          this.btnAddChildren = "btnAddChildren"
        }
        if (item["DomId"] == "btnAdd") {
          this.btnAdd = "btnAdd"
        }
        if (item["DomId"] == "btnEdit") {
          this.btnEdit = "btnEdit"
        }
      })
    },

    handleFilterBtn(btns) {
      if (btns && btns.length > 0) {
        return btns.filter(s => s.DomId != 'btnAddChildren')
      }
      return []
    },

    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleAdd("create");
          break;
        case "updateType":
          if(this.multipleSelection && this.multipleSelection.length == 0) {
            this.$message({
                message: "至少选择一条",
                type: "error"
            });
            return;
          }
          this.handleBatchDialog()
          break;
      }
    },

    checkPremissBtns(domId) {
      return this.rowBtns.findIndex(b => b.DomId == domId) > -1
    },

    /**********************************弹出窗************************************/
    /**是否可编辑
     * （首页一般分为新增、编辑、详情）
     */
    getEditable() {
      return this.dialogStatus == "detail";
    },
    handleDelete(d) {
      this.$confirm('是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        failurecase.del([d.Id]).then(res => {
          this.$notify({
            title: '成功',
            message: '删除成功！',
            type: 'success'
          });
          this.listQuery.PageIndex = 1;
          this.getList();
        });
      }).catch(() => {

      });
    },
    /**查询详情后弹窗 */
    handleUpdate(row, optType = "edit") {
      this.selectClassifyId = "";
      this.dialogCedaStatus = optType;
      this.cedaId = row.Id;
      this.dialogCedaFormVisible = true;
    },

    classifySaveSuccess(d) {
      if (!d) {
        this.classifyCloseDialog();
      }
      this.loadTreeData();
    },
    classifyCloseDialog() {
      this.classifyDialogFormVisible = false;
    },

    /******************* 树事件 *******************/
    loadTreeData() {
      let _this = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 3
      };
      _this.treeLoading = true
      classify.getListPage(paramData).then(response => {
        _this.treeLoading = false
        response.Items.unshift({
          Id: "",
          Name: "全部",
          Level: 0,
          ParentId: null
        });
        _this.treeData = listToTreeSelect(response.Items);

        if (_this.treeData && _this.treeData.length > 0) {
          if (
            !(
              _this.checkedNode &&
              response.Items.find(t => {
                return t.Id == _this.checkedNode.Id;
              })
            )
          ) {
            _this.checkedNode = _this.treeData[0];
          }
        } else {
          _this.checkedNode = null;
        }
        if (_this.checkedNode) {
          _this.$nextTick(() => {
            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
          });
        }
      }).catch(err => {
        _this.treeLoading = false
      });
    },
    /**添加顶级节点 */
    addTopLevel() {
      this.paramNode = {
        Id: null,
        Name: "",
        Level: 0
      };
      this.classifyDialogStatus = "create";
      this.classifyDialogFormVisible = true;
    },
    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    /**树下拉事件 */
    handleCommand(optType, node, data) {
      switch (optType) {
        case "create":
          this.paramNode = data;
          this.classifyDialogStatus = "create";
          this.classifyDialogFormVisible = true;
          break;
        case "update":
          this.paramNode = data;
          this.classifyDialogStatus = "update";
          this.classifyDialogFormVisible = true;
          break;
        case "delete":
          this.handleDeleteArea(data);
          break;
        default:
          break;
      }
    },
    /**删除树节点 */
    handleDeleteArea(data) {
      if (data.children && data.children.length > 0) {
        this.$notify({
          title: "提示",
          message: "请先删除子级",
          type: "error",
          duration: 2000
        });
        return;
      }
      let paramData = { ids: [data.Id], businessType: 3 };
      this.$confirm(`是否确认删除${data.Name}?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        classify.del(paramData).then(res => {
          if (this.checkedNode && this.checkedNode.Id == data.Id) {
            this.checkedNode = null;
          }
          this.loadTreeData();
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
        });
      });
    },
    handleBatchDialog() {
      this.dialogbatchVisible = true
    },
    handleSaveBatchSuccess() {
        this.dialogbatchVisible = false
        this.getList();
    },
  }
};
</script>


<!--组件样式区-->
<style lang="scss" scoped>
.bg-white {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      //   padding: 10px;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
