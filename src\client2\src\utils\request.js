import axios from "axios";
import store from "../store";
import router from "../router";
import Cookies from "js-cookie";
import dayjs from "dayjs";

const expection_key = "expection_global";

import {
  Message,
  // MessageBox
} from "element-ui";

import { getToken } from "@/utils/auth";
import { removeToken, removeUserInfo } from "@/utils/auth";

let handleError = () => {
  //最新报错时间
  let now = dayjs().format('YYYY/MM/DD HH:mm:ss SSS');
  //两次错误时间间隔
  let dura = 0

  //上一次报错时间
  let lastTime = Cookies.get(expection_key);

  if (lastTime) {
    lastTime = dayjs(lastTime).format('YYYY/MM/DD HH:mm:ss SSS');
    dura = dayjs(now).diff(dayjs(lastTime), 'SSS');
  }

  if (!lastTime || dura > 500) {
    dura = 0

    Cookies.set(expection_key, now);

    Message({
      message: "服务器异常",
      type: "error",
    });
  }
};

// 创建axios实例
const service = axios.create({
  baseURL: "api", // api的base_url
  timeout: 20000, // 请求超时时间
});

// request拦截器
service.interceptors.request.use(
  config => {
    if (config.config && config.config.timeout) {
      config.timeout = config.config.timeout;
    }

    config.headers["Terminal"] = 1;
    config.headers["TerminalName"] = encodeURI("web端请求");
    config.headers["Token"] = getToken() || (config.token && config.token.Token); // 让每个请求携带自定义token 请根据实际情况自行修改
    config.headers["PageId"] = encodeURI(store.getters.currentMenuPageId || "");
    // router.history.current.meta.title———可以得到页面名称，单该方式不凡便特殊情况动态设置

    //解决 ie 缓存 api 请求问题
    config.url += config.url.indexOf("?") > -1 ? "&" : "?" + `t=${Date.now()}`;

    // }
    return config;
  },
  error => {
    // Do something with request error
    // console.log(error) // for debug
    Promise.reject(error);
  }
);

let errCodes = {
  0: "服务器未知错误",
  1: "成功",
  2: "没有找到",
  3: "未知错误",
  4: "参数错误",
  5: "参数格式错误",
  6: "参数值 超出 有效值范围",
  7: "未登录或登录信息已过期",
  8: "未登录或登录信息已过期",
  9: "没有权限",
  10: "操作失败",
  11: "对象已存在",
  12: "对象不存在",
  13: "数据异常",
  14: "名称已被使用",
  15: "用户名或密码错误",
  16: "账号已经被使用",
  17: "手机号码被使用",
  18: "邮箱已被使用",
  19: "账户不存在",
  20: "验证码错误",
  21: "重复请求",
  22: "短信发送已超出限制",
  23: "数据入库失败",
  24: "请为角色分配机构",
  25: "找不到审核节点",
  26: "无法寻找到下一个节点",
  27: "无法寻找到会签节点的审核者",
  28: "会签节点的审核者不能为所有人",
  29: "模板号已被使用",
  30: "流程已经结束，不能再次处理",
  31: "当前节点没有审核人",
  32: "维修单正在审批中不能修改",
  33: "维修单流程模板不存在",
  34: "流程正在运行",
  35: "父级节点无效",
  36: "节点无效",
  37: "不能设为无效,因为存在有效状态子节点",
  38: "父级节点不存在",
  39: "不能设为无效,因为存在有效状态职位节点",
  40: "不能设为无效,因为存在关联员工",
  41: "非法操作",
  42: "邮件发送失败,请确认邮件地址是否有误",
  43: "旧密码不正确", //修改密码业务
  44: "单位数据错误",
  45: "操作失败,存在关联部门",
  46: "操作失败,存在关联职位",
  47: "操作失败,存在关联员工",
  48: "服务时间格式不正确",
  49: "维修单号已存在",
  50: "操作失败,该节点被维修单使用中",
  51: "操作失败,存在子节点",
  52: "哈希验证错误",
  53: "员工创建成功，邮件发送失败",
  54: "操作失败，当前客户单位未找到",
  55: "当前故障现象已转，请勿重复提交",
  56: "员工工号已存在",
  57: "操作失败,关键字被故障案例使用中",
  58: "操作成功，短信通知推送失败",
  // 59: '操作成功，短信通知推送成功',
  60: "操作失败，存在关联任务",
  61: "操作失败，任务已被删除",
  62: "操作失败，任务已被删除",
  63: "操作失败，任务已被删除",
  64: "操作失败，任务当前状态不支持编辑",
  65: "操作失败，消息已被删除",
  66: "操作失败，当前流程已结束",
  67: "操作失败，维修单已被删除",
  68: "操作失败，app 版本号已经存在",
  69: "短信发送失败",
  70: "当前日期已存在，不能对同一个日期设置两次",
  71: "当前日期未找到",
  72: "操作失败,该节点被客户项目使用中",
  73: "操作失败,该节点被客户单位干系人使用中",
  74: "操作失败,该节点被客户单位使用中",
  75: "客户单位名称已存在",
  76: "采购预测不存在",
  77: "拜访记录不存在",
  78: "企业管理员角色下不能添加或修改用户",
  79: "角色已经被使用",
  80: "组织机构代码已存在",
  81: "当前用户已存在员工信息",
  82: "当前用户已存在生效中的员工信息",
  83: "操作失败，表单已被删除",
  84: "操作失败，表单不存在",
  85: "表单编号已存在",
  86: "暂不支持操作系统角色",
  87: "操作失败，控件不存在",
  88: "项目编码已存在",
  89: "项目名称已存在",
  90: "获取失败，项目已被删除",
  91: "项目管理风险名称已存在",
  92: "项目管理风险编码已存在",
  93: "项目管理风险明细不存在",
  94: "里程碑不存在",
  95: "里程碑在非待审批状态",
  96: "操作失败，项目已被删除",
  97: "操作失败，当前节点已审批",
  98: "操作失败，当前项目已被驳回",
  99: "操作失败，当前验收申请已被驳回",
  100: "版本计划不存在",
  101: "迭代计划不存在",
  102: "有迭代计划在进行中",
  103: "操作失败，项目验收申请不存在",
  104: "需求标题重复",
  105: "操作失败，需求已被删除",
  106: "需求历史记录不存在",
  107: "操作失败，当前流程已被处理",
  108: "版本名称已存在",
  109: "版本非规划中，不能删除",
  110: "迭代处于进行中，不能删除",
  111: "迭代被引用,不能删除",
  112: "操作失败，当前状态不支持编辑",
  113: "问题标题重复",
  114: "当前用户非经办人，无法操作",
  115: "问题不存在",
  116: "企业编号不存在",
  117: "企业编号已存在",
  118: "产品名称重复",
  119: "产品有关联的需求，不能删除",
  120: "产品有关联的项目，不能删除",
  121: "操作失败，当前需求信息不存在",
  122: "操作失败，迭代已经在进行中，不允许添加",
  123: "请先删除该实施项目的地区列表信息",
  124: "账号已停用，请联系管理员",
  125: "部门无负责人",
  126: "暂无访问权限，请联系管理员开通权限",
  127: "考核对象未提交绩效承诺",
  128: "当前申请为待审批状态，请补充“撤销原因”后重新提交",
  129: "密码校验错误",
  130: "报修单导入订单错误",
  131: "已有出差单,请结束后再次发起",
  132: "奖品剩余库存不足",
  133: "积分不足无法兑换",
  134: "材料导入出错",
  135: "超级干线任务付件不存在",
};

//不需要在此处显示错误信息的错误状态代码集合（有具体业务页面处理错误）
let notShowMessage = [129, 130, 132, 133, 135];

// respone拦截器
service.interceptors.response.use(
  response => {

    const res = response.data;
    if (res.MessageCode != 1) {
      // let isNoticed = store.getters.isNoticedExpire
      if (res.MessageCode == 7 || res.MessageCode == 8) {
        removeUserInfo();
        removeToken();
        window.location.href = "/#/login";
        // if (!store.getters.isNoticedExpire) {
        //   Message({
        //     message: errCodes[res.MessageCode],
        //     type: 'error',
        //     duration: 5 * 1000
        //   })
        // }
        return Promise.reject({
          err: "error",
          messageCode: res.MessageCode,
        });
      } else {
        let errMsg = "";
        //如果错误代码为999，表示以服务端提示错误为准。否则是否前端配置的错误提示信息。
        if (res.MessageCode == 999) {
          errMsg = res.ErrorMessage;
        } else {
          errMsg = errCodes[res.MessageCode];
        }

        //如果需要提示错误才显示错误信息（否则由具体页面处理错误）
        if (notShowMessage.findIndex(s => s == res.MessageCode) == -1) {
          Message({
            message: errMsg,
            type: "error",
            duration: 5 * 1000,
          });
        }

        return Promise.reject({
          err: "error",
          messageCode: res.MessageCode,
          errorMessage: res.ErrorMessage,
        });
      }
    } else {
      return res.TMessageData;
    }
  },
  error => {
    // console.log('err' + error)// for debug
    // console.log(error.message)

    let errMsg = errCodes["3"];
    if (error && error.message && error.message.startsWith("timeout")) {
      errMsg = "请求超时，请稍后重试！";
      Message({
        message: errMsg,
        type: "error",
        duration: 5 * 1000,
      });
    } else if (error) {
      // 请求取消
      if(error.message == 'cancelRequest') return;
      
      // console.log(JSON.stringify(error))
      //router.push({path: '/500'})

      handleError();
    }

    return Promise.reject(error);
  }
);

export default service;
