<!--车辆调度管理-->
<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="pageWrapper">
        <div class="product-list"><br v-if="btnAddChildren==''" />
          <el-button v-if="btnAddChildren=='btnAddChildren'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">创建用车单位</el-button>
          <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <div class="treeBox" v-loading='treeLoading'>
            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                <span v-if="data.Level>0" class="node-btn-area">
                  <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                      <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="content-wrapper">
          <div class="content __dynamicTabContentWrapper">
            <div class="tab-form-wrapper">
              <app-table-form :label-width="'100px'" :smWidth='18' :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='"simple"'>
                <template slot="KeyWords">
                  <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索车牌号/司机/品牌/型号"></el-input>
                </template>
                <template slot="IsExitDriverEmployee">
                  <el-select style="width: 50%;" clearable v-model="listQuery.IsExitDriverEmployee" placeholder="">
                    <el-option v-for="item in driverEmployee" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>

                <template slot="CarServiceTimeList">
                  <el-date-picker v-model="listQuery.CarServiceTimeList" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" style="width: 50%;" :clearable="false"></el-date-picker>
                </template>

                <template slot="CarReturnTimeList">
                  <el-date-picker v-model="listQuery.CarReturnTimeList" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" style="width: 50%;" :clearable="false"></el-date-picker>
                </template>

                <template slot="btnsArea">
                  <el-button style="float: left;margin-left:5px" v-if="btnAdd=='btnAdd'" type="primary" class="elButton" @click="handleAdd('create')">添加车辆</el-button>
                </template>

              </app-table-form>

            </div>
            <el-row style="margin-top: 0px;margin-left: 10px;">
              <el-col :span="24">
                <span style="float:left;margin-right:10px">车辆状态说明：</span>
                <el-checkbox-group v-model="listQuery.CarState" @change="handleCarStateChange">
                  <el-checkbox v-for="item in carStateList" :key="item.label" :label="item.value">
                    <a class="round" :style="{color: getStatusObj(item.value).color}" style="margin-left:-10px;margin-right: -5px;">●</a>
                    {{item.label}}
                  </el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-row>

            <!----------------------------------------- 内容区 ------------------------------------------->
            <div class="list-item-wrapper" v-loading='tableLoading'>
              <noData v-if="dataSourceList.length == 0"></noData>
              <el-row v-else>
                <el-col v-for="c in dataSourceList" :key="c.Id" :span="6" :sm="12" :md="8" :xl="6">
                  <div class="card-wrapper">
                    <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                      <div slot="header" class="clearfix">
                        <div>
                          <a class="round" :style="{color: getStatusObj(c.CarState).color}">●</a>
                          <label :style="{color: getStatusObj(c.CarState).color}">{{getStatusObj(c.CarState).label}}</label>
                          <label style="font-size:12px;font-weight: normal !important;" :style="{color: getStatusObj(c.CarState).color}" v-show="c.CarState==2">{{c.CarReturnTimePlan != null?"（预计"+c.CarReturnTimePlan+"结束）" : "（未知结束时间）"}}</label>
                        </div>
                        <span>
                          <!-- 用车 -->
                          <img title="用车" src="../../../assets/images/use.svg" v-if="rowBtnIsExists('btnUseReturnCar')" v-show="c.CarState == 1&&c.CarState != 5" @click="handleCarUse(c)">

                          <!-- 还车 -->
                          <img title="还车" src="../../../assets/images/return.svg" v-if="rowBtnIsExists('btnUseReturnCar')" v-show="c.CarState == 2&&c.CarState != 5" @click="handleCarReturn(c)">

                          <!-- 查看记录 -->
                          <img title="查看记录" src="../../../assets/images/editState.svg" v-if="rowBtnIsExists('btnModifyState')" @click="handleShowRecord(c)">

                          <span class="node-btn-area" style="line-height: 0;">
                            <el-dropdown trigger="click" style="float: right; " @command="handleOperationCommand($event, c.Id)" size="mini">
                              <span class="el-dropdown-link">
                                <i class="el-icon-more" style="font-size: 15px;-moz-transform:rotate(-90deg);  -webkit-transform:rotate(-90deg);"></i>
                              </span>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-if="rowBtnIsExists('btnModifyState')" :disabled="c.CarState ==2" command="stateChange">修改状态</el-dropdown-item>
                                <el-dropdown-item v-if="rowBtnIsExists('btnEdit')" command="update">修改</el-dropdown-item>
                                <el-dropdown-item v-if="rowBtnIsExists('btnDel')" command="delete">删除</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </span>
                        </span>
                      </div>
                      <div @click="handleEdit(c.Id, 'detail')" class="elMain">
                        <div class="item-wrapper">
                          <div class="row1">
                            <div class="lft img-wrapper">
                              <img v-if="c.CarImgPath" :src="c.CarImgPath">
                              <img v-else src="../../../assets/images/car.png">
                              <!-- <span>
                                <span class="text-ellipsis contentSpan">
                                  {{c.CarNumber}} &nbsp;&nbsp; &nbsp; {{c.CarBrand}} &nbsp; &nbsp;&nbsp; {{c.CarType}}
                                </span>
                                <br />
                                <span style="width: 100%; display: inline-block;" class="text-ellipsis" :title="c.CarDepartmentName">
                                  {{c.CarDepartmentName}}
                                </span>
                              </span> -->
                            </div>
                            <div class="rht card-info">
                              <div class="f-w-6 f-s-20">{{c.CarNumber | emptyFilter}}</div>
                              <div>{{c.CarBrand | emptyFilter}}</div>
                              <div>{{c.CarDepartmentName | emptyFilter}}</div>
                            </div>
                          </div>
                          <div class="row1" style="margin-top: 15px;">
                            <div class="lft">
                              <span class="title-text">GPS状态</span>
                              <span :style="{color: c.GPSStatus == '在线' ? '#70B603' : c.GPSStatus == '离线' ? '#D9001B' : '#393133'}">{{ c.GPSStatus | emptyFilter }}</span>
                            </div>
                            <div class="rht">
                              <span class="title-text">服务到期</span>
                              <span v-if="!c.EndTime">
                                {{ c.EndTime | emptyFilter }}
                              </span>
                              <span v-else :style="{color: c.EndTimeExpire ? '#D9001B' : '#027DB4'}">
                                {{ c.EndTime | dateFilter('YYYY-MM-DD') }}
                              </span>
                            </div>
                          </div>
                          <div class="row2" style="margin-top: 15px; display: flex;">
                            <span class="title-text">当前位置</span>
                            <span style="flex: 1;" :title="c.CurrentPosition">{{ c.CurrentPosition | emptyFilter }}</span>
                          </div>

                        </div>
                      </div>
                    </el-card>
                  </div>
                </el-col>
              </el-row>
              <div class="card-list-wrapper">
              </div>
            </div>

            <!----------------------------------------- 分页 ------------------------------------------->
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
          </div>
        </div>
      </div>
    </div>

    <!--修改车辆单位 弹窗组件区-->
    <carEdit :dialogStatus="departmentDialogStatus" :node="paramNode" :dialogFormVisible="departmentDialogFormVisible" @closeDialog="departmentCloseDialog" @saveSuccess="departmentSaveSuccess"></carEdit>

    <!--修改车辆状态 弹窗组件区-->
    <carEditState :obj="selectCarObj" :dialogFormVisible="stateDialogFormVisible" @closeDialog="stateCloseDialog" @saveSuccess="stateSaveSuccess"></carEditState>

    <!--添加/修改车辆 弹窗组件区-->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="carId" :selectDepartmentId="selectDepartmentId" @reload="getList"></create-page>

    <!--用车 弹窗组件区-->
    <useCar @closeDialog="useCarCloseDialog" @saveSuccess="useCarSaveSuccess" :dialogFormVisible="useCarDialogFormVisible" :obj="currentCar"></useCar>

    <!--还车 弹窗组件区-->
    <returnCar @closeDialog="returnCarCloseDialog" @saveSuccess="returnCarSaveSuccess" :dialogFormVisible="returnCarDialogFormVisible" :obj="currentCar"></returnCar>

    <!--记录 弹窗组件区-->
    <carRecord @closeDialog="carRecordCloseDialog" @saveSuccess="carRecordSaveSuccess" :dialogFormVisible="carRecordDialogFormVisible" :obj="currentCar"></carRecord>

  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as carMgt from '@/api/personnelManagement/carCoordinator'
import elDragDialog from "@/directive/el-dragDialog";
import noData from "@/views/common/components/noData"
import empSelector from "@/views/common/empSelector";
import carEdit from "./carEdit";
import carEditState from "./carEditState";
import createPage from './create'
import useCar from './useCar'
import returnCar from './returnCar'
import carRecord from './carRecord'
import { listToTreeSelect } from "@/utils";

export default {
  /**名称 */
  name: "car-coordinator",
  mixins: [indexPageMixin],
  directives: {
    elDragDialog
  },
  /**组件声明 */
  components: {
    carEdit,
    empSelector,
    noData,
    carEditState,
    createPage,
    useCar,
    returnCar,
    carRecord
  },
  /**参数区 */
  props: {
    /**主键Id */
    keyId: {
      type: String
    }
  },
  /**数据区 */
  data() {
    return {
      carId: '',
      currentCar: {},

      /******************* 添加弹窗 *******************/
      dialogStatus: 'create',
      dialogFormVisible: false,
      selectDepartmentId: "",

      /******************* 用车/还车弹窗 *******************/
      useCarDialogFormVisible: false,
      returnCarDialogFormVisible: false,
      carRecordDialogFormVisible: false,


      stateDialogFormVisible: false,
      selectCarObj: {
        Id: "",
      },

      /******************* 表格 *******************/
      tableLoading: false,
      dataSourceList: [],
      total: 0,
      carStateList: [],

      /**查询条件 */
      listQuery: {
        PageIndex: 1,
        PageSize: 20,
        KeyWords: '',
        IsExitDriverEmployee: '',
        CarState: [],
        CarDepartmentId: '',
        CarServiceTimeList: [],
        CarReturnTimeList: [],
      },
      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "IsExitDriverEmployee", label: "司机信息" },
        { prop: "CarServiceTimeList", label: "用车时间" },
        { prop: "CarReturnTimeList", label: "还车时间" },
      ],
      driverEmployee: [
        { label: '无固定司机', value: false },
        { label: '固定司机', value: true },
      ],


      /******************* 权限 *******************/

      btnAddChildren: '',
      btnAdd: '',
      btnEdit: '',



      /******************* 树 *******************/
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },


      /******************* 组件 *******************/
      /**树节点添加弹窗 */
      departmentDialogFormVisible: false,
      departmentDialogStatus: "create",

      /**选择业务员 */
      businessManagerIds: [],
      pers: [],

      CarStates: [
        { value: 1, label: '空闲', color: '#00CC00' },
        { value: 2, label: '使用中', color: '#409EFF' },
        { value: 5, label: '固定用车', color: '#837DFF' },
        { value: 3, label: '车辆维修/保养', color: '#FF9900' },
        { value: 4, label: '停用', color: '#A9ACB3' },
      ],
    };
  },
  /**计算属性 */
  computed: {},
  /**监听 */
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.CarDepartmentId = val.Id;
          this.listQuery.PageIndex = 1;
          this.getList();
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.btnTextValue();
    this.getCarStateAllData();
  },
  /**渲染后 */
  mounted() {
    this.loadTreeData();
  },
  /**方法区 */
  methods: {

    /******************* 表格事件 *******************/
    /**获取所有车辆状态对应的数据量 */
    getCarStateAllData() {
      let _this = this;
      carMgt.getCarStateAllList().then(response => {
        _this.carStateList = response
      }).catch(err => {
        _this.carStateList = []
      });
    },

    /**加载数据 */
    getList() {
      let _this = this;
      if (_this.checkedNode.Id) {
        _this.listQuery.CarDepartmentId = _this.checkedNode.Id;
      } else {
        delete this.listQuery.CarDepartmentId;
      }
      let postData = JSON.parse(JSON.stringify(_this.listQuery));
      if (postData.CarServiceTimeList && postData.CarServiceTimeList.length > 0) {
        postData.UseCarStartDateTime = postData.CarServiceTimeList[0]
        postData.UseCarEndDateTime = postData.CarServiceTimeList[1]
      }
      if (postData.CarReturnTimeList && postData.CarReturnTimeList.length > 0) {
        postData.ReturnCarStartDateTime = postData.CarReturnTimeList[0]
        postData.ReturnCarEndDateTime = postData.CarReturnTimeList[1]
      }
      _this.tableLoading = true
      carMgt.getList(postData).then(response => {
        _this.tableLoading = false
        _this.total = response.Total;
        _this.dataSourceList = response.Items;
      }).catch(err => {
        _this.tableLoading = false
      });
    },

    /**用车 */
    handleCarUse(item) {
      this.currentCar = item;
      this.useCarDialogFormVisible = true;
    },

    /**还车 */
    handleCarReturn(item) {
      this.currentCar = item;
      this.returnCarDialogFormVisible = true;
    },

    /**查看记录 */
    handleShowRecord(item) {
      this.currentCar = item;
      this.carRecordDialogFormVisible = true;
    },

    /**调整状态 */
    handleEditState(id) {
      this.selectCarObj.Id = id;
      this.stateDialogFormVisible = true;
    },

    /**添加 */
    handleAdd(activeName) {
      this.selectDepartmentId = this.checkedNode.Id;
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },

    /**修改 */
    handleEdit(id, optType = "update") {
      this.selectDepartmentId = "";
      this.carId = id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    /**删除 */
    handleDelete(id) {
      let ids = [];
      ids.push(id);
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        carMgt.del(ids).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getCarStateAllData();
          this.getList();
        });
      });
    },

    /**车辆状态转换 */
    getStatusObj(status) {
      return this.CarStates.find(s => s.value == status) || {};
    },

    handleOperationCommand(optType, id) {
      switch (optType) {
        case "stateChange":
          this.handleEditState(id);
          break;
        case "update":
          this.handleEdit(id);
          break;
        case "delete":
          this.handleDelete(id);
          break;
        default:
          break;
      }
    },


    /**切换车辆状态过滤 */
    handleCarStateChange(item) {
      this.getList();
    },

    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    onResetSearch() {
      this.listQuery.KeyWords = ""
      this.listQuery.IsExitDriverEmployee = ''
      this.listQuery.CarState = []
      this.listQuery.CarDepartmentId = ''
      this.listQuery.CarServiceTimeList = []
      this.listQuery.CarReturnTimeList = []
      this.getList();
    },

    btnTextValue() {
      let btns = this.topBtns
      btns.forEach(item => {
        if (item["DomId"] == "btnAddChildren") {
          this.btnAddChildren = "btnAddChildren"
        }
        if (item["DomId"] == "btnAdd") {
          this.btnAdd = "btnAdd"
        }
        if (item["DomId"] == "btnEdit") {
          this.btnEdit = "btnEdit"
        }
      })
    },

    /******************* 弹窗相关 *******************/
    stateSaveSuccess() {
      this.getCarStateAllData();
      this.getList();
      this.stateCloseDialog();
    },
    stateCloseDialog() {
      this.stateDialogFormVisible = false;
    },

    departmentSaveSuccess(d) {
      if (!d) {
        this.departmentCloseDialog();
      }
      this.loadTreeData();
    },
    departmentCloseDialog() {
      this.departmentDialogFormVisible = false;
    },

    closeDialog() {
      this.dialogFormVisible = false
    },
    handleSaveSuccess(d) {
      if (!d) {
        this.closeDialog();
      }
      this.getCarStateAllData();
      this.getList();
    },

    useCarCloseDialog() {
      this.useCarDialogFormVisible = false;
    },
    useCarSaveSuccess() {
      this.useCarCloseDialog();
      this.getCarStateAllData();
      this.getList()
    },


    returnCarCloseDialog() {
      this.returnCarDialogFormVisible = false;
    },
    returnCarSaveSuccess() {
      this.returnCarCloseDialog();
      this.getCarStateAllData();
      this.getList()
    },

    carRecordCloseDialog() {
      this.carRecordDialogFormVisible = false;
    },
    carRecordSaveSuccess() {
      this.carRecordCloseDialog();
    },

    /******************* 树事件 *******************/
    loadTreeData() {
      let _this = this;
      let paramData = {};
      _this.treeLoading = true
      carMgt.getDepartmentCondition(paramData)
        .then(response => {
          _this.treeLoading = false
          response.unshift({
            Id: "",
            Name: "全部",
            Level: 0,
            ParentId: null
          });
          _this.treeData = listToTreeSelect(response);

          if (_this.treeData && _this.treeData.length > 0) {
            if (
              !(
                _this.checkedNode &&
                response.find(t => {
                  return t.Id == _this.checkedNode.Id;
                })
              )
            ) {
              _this.checkedNode = _this.treeData[0];
            }
          } else {
            _this.checkedNode = null;
          }
          if (_this.checkedNode) {
            _this.$nextTick(() => {
              _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
            });
          }
        }).catch(err => {
          _this.treeLoading = false
        });
    },
    /**添加顶级节点 */
    addTopLevel() {
      this.paramNode = {
        Id: null,
        Name: "",
        Level: 0
      };
      this.departmentDialogStatus = "create";
      this.departmentDialogFormVisible = true;
    },
    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    /**树下拉事件 */
    handleCommand(optType, node, data) {
      switch (optType) {
        case "create":
          this.paramNode = data;
          this.departmentDialogStatus = "create";
          this.departmentDialogFormVisible = true;
          break;
        case "update":
          this.paramNode = data;
          this.departmentDialogStatus = "update";
          this.departmentDialogFormVisible = true;
          break;
        case "delete":
          this.handleDeleteArea(data);
          break;
        default:
          break;
      }
    },
    /**删除树节点 */
    handleDeleteArea(data) {
      if (data.children && data.children.length > 0) {
        this.$notify({
          title: "提示",
          message: "请先删除子级",
          type: "error",
          duration: 2000
        });
        return;
      }
      this.$confirm(`是否确认删除${data.Name}?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        carMgt.delDepartment([data.Id]).then(res => {
          if (this.checkedNode && this.checkedNode.Id == data.Id) {
            this.checkedNode = null;
          }
          this.loadTreeData();
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
        });
      });
    },
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.bg-white {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.round {
  width: 16px;
  height: 16px;
  display: inline-block;
  font-size: 20px;
  line-height: 16px;
  text-align: center;
  color: #f00;
  text-decoration: none;
}

.contentSpan {
  width: 100%;
  display: inline-block;
  font-size: 15px;
  margin-bottom: 5px;
  max-width: 330px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.clearfix {
  > div {
    float: left;
    padding-right: 9px;
  }
  > span {
    float: right;
    padding-right: 5px;
    img {
      margin-right: 10px;
      cursor: pointer;
    }
    i {
      font-size: 20px;
      margin-top: 8px;
      margin-right: 4px;
    }
  }
}

.elMain {
  width: 100%;
  // height: 224px;
  cursor: pointer;
  // display: flex;
  // justify-content: space-between;
  //   position: absolute;
  .item-wrapper{
    height: 100%;
    .row1{
      display: flex;
      .lft{
        width: 160px;
      }
      .rht{
        flex: 1;
        padding-left: 10px;
      }
      .img-wrapper {
        height: 100px;
        background: #e3e3e3;
        position: relative;
        text-align: center;
        overflow: hidden;
        display: flex;
        align-items: center;
        > img {
          width: 100%;
          object-fit: cover;
          object-position: center;

          //   width: auto;
          //   height: auto;
          //   max-width: 100%;
          //   max-height: 100%;
          //   width: calc(100% + 1px);
          //   height: auto;
          //   position: absolute;
          //   top: 50%;
          //   left: 50%;
          //   transform: translate(-50%, -50%);
    
          // position: absolute;
          // top: 50%;
          // left: 50%;
          // display: block;
          // width: 100%;
          // height: auto;
          // transform: translate(-50%, -50%);
        }
    
        // > span {
        //   position: absolute;
        //   bottom: 0;
        //   left: 0;
        //   width: 100%;
        //   height: 50px;
        //   //   background: #8b8b8b;
        //   //   opacity: 0.7;
        //   background: rgba(70, 70, 70, 0.7);
        //   color: white;
        //   text-align: left;
        //   padding-top: 7px;
        //   padding-left: 10px;
        //   padding-right: 10px;
        // }
      }
      .card-info{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .f-w-6{
          font-weight: 600;
        }
        .f-s-20{
          font-size: 20px;
        }
      }


    }
  }

  .title-text{
    margin-right: 10px;
  }

  // > div:last-child {
  //   width: 100%;
  //   color: #909399;
  //   background: #282828;
  // }
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
    overflow-y: auto;
    // >div:first-child{
    //     display: flex;
    //     justify-content: space-between;
    //     align-items:center;
    //     padding:0 10px;
    // }
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: hidden;

    .content {
      //   padding: 10px;
      height: 100%;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }

      .list-item-wrapper {
        flex: 1;
        overflow-y: auto;
        padding: 5px;
        .card-wrapper {
          // flex: 1;
          width: 100%;
          min-width: 350px;
          padding-top: 10px;
          padding-right: 5px;
          padding-left: 5px;
          padding-bottom: 5px;
          display: inline-block;
          overflow: hidden;
          .card {
            width: 100%;
            height: 240px;
          }

          .item-wrapper {
            cursor: pointer;
            padding: 14px;

            .item {
              padding-bottom: 4px;

              div {
                display: inline-block;
              }

              .item-title {
                width: 90px;
              }
            }
          }

          .split-line {
            height: 1px;
            background: #dcdfe6;
            margin-top: 10px;
          }

          .footer {
            padding-right: 14px;
            text-align: right;
          }
        }
        // .card-list-wrapper{
        //   display: flex;
        //   flex-wrap: wrap;
        // }
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
