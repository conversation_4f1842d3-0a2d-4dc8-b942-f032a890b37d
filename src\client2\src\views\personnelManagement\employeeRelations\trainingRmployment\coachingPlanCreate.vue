<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData"
                label-position="right" label-width="100px">
                    <el-row class="wrapper" v-loading='loading'>
                        <el-form-item label="辅导计划" prop="CoachPlan">
                            <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="4" v-model="formData.CoachPlan"></el-input>
                        </el-form-item>
                        <el-form-item label="衡量标准/目标" prop="Target">
                            <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="4" v-model="formData.Target"></el-input>
                        </el-form-item>
                        <el-form-item label="完成时间" prop="AccomplishTime">
                            <el-date-picker v-model="formData.AccomplishTime" type="date"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import empSelector from '@/views/common/empSelector'
import * as EmployeeCoachPlanApi from "@/api/personnelManagement/EmployeeCoachPlan";
export default {
    name: "trainingRmployment-coachingPlanCreate",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "添加辅导计划";
            } else if (this.dialogStatus == "update") {
                return "编辑辅导计划";
            } else if (this.dialogStatus == "detail") {
                return "辅导计划详情";
            }
            return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
            default: "create",
        },
        id: {
            type: String,
            default: "",
        },
        employeeId: {
            type: String,
            default: "",
        },
        
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {
                CoachPlan: {fieldName: "辅导计划",rules: [{ required: true }]},
            },
            formData: {
                // Id: '',
                EmployeeId: '', // 员工id
                Target: '', // 衡量标准/目标
                CoachPlan: '', // 辅导计划
                AccomplishTime: '', // 完成时间
            }
        };
    },
    methods: {
        resetFormData() {
            this.formData = this.$options.data().formData
            if(this.dialogStatus == "create"){
                this.formData.AccomplishTime = dayjs().format('YYYY-MM-DD')
            }
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    console.log(postData)
                    self.disabledBtn = true;
                    let result = null;
                    if (self.dialogStatus == 'create') {
                        postData.EmployeeId = self.employeeId;
                        result = EmployeeCoachPlanApi.add(postData)
                    }
                    if (self.dialogStatus == 'update') {
                        result = EmployeeCoachPlanApi.edit(postData)
                    }
                    result.then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            EmployeeCoachPlanApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                if (res.TutorEmployeeId) {
                    this.formData.TutorEmployeeIdList.push(res.TutorEmployee)
                }
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
}
</style>