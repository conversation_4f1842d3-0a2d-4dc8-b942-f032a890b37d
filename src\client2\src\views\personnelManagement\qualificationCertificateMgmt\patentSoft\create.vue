<!--专利软著编辑-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-row class="contentBox">
                    <div class="contentBox_left">
                        <el-form
                            :rules="formRules"
                            ref="formRef"
                            :model="formModel"
                            label-position="right"
                            label-width="120px"
                            v-loading='loading'
                        >
                            <el-form-item label="知识产权名称" prop="Name">
                                <div :title="editable?'':formModel.Name">
                                    <el-input :disabled="!editable" maxlength="100" type="text" v-model="formModel.Name"></el-input>
                                </div>
                            </el-form-item>
                            <el-form-item label="著作类型" prop="PatentWorkType">
                                <el-select :disabled="!editable" v-model="formModel.PatentWorkType" placeholder="请选择">
                                    <el-option v-for="item in patentWorkTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="关联产品" prop="ClassifyId">
                                <el-select :disabled="!editable" clearable v-model="formModel.ClassifyId" placeholder="请选择">
                                    <el-option v-for="item in relationList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="专利号/登记号" prop="PatentNumber">
                                <div :title="editable?'':formModel.PatentNumber">
                                    <el-input :disabled="!editable" maxlength="50" type="text" v-model="formModel.PatentNumber"></el-input>
                                </div>
                            </el-form-item>
                            <el-form-item label="授权公告号" prop="NoticeNumber">
                                <div :title="editable?'':formModel.NoticeNumber">
                                    <el-input :disabled="!editable" maxlength="50" type="text" v-model="formModel.NoticeNumber"></el-input>
                                </div>
                            </el-form-item>
                            <el-form-item label="授权公告日" prop="NoticeDate">
                                <el-date-picker :clearable="false" :disabled="!editable" v-model="formModel.NoticeDate" type="date" align="right" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="申请日期" prop="ApplyDate">
                                <el-date-picker @change="computedDate" :clearable="false" :disabled="!editable" v-model="formModel.ApplyDate" type="date" align="right" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                            
                            <el-form-item label="有效期至" prop="ValidUntilType" v-if="editable">
                                <el-tag v-for="btnItem in termOfValidityTypes" :key="btnItem.value" :effect='btnItem.value == formModel.ValidUntilType ? "dark" : "light"'
                                :disabled="!editable" :class="!editable?'isDisabled':''" @click="chooseTag(btnItem)">{{ btnItem.label }}</el-tag>
                            </el-form-item>
                            <el-form-item label="有效日期" prop="ValidDate">
                                <el-date-picker clearable @change="formModel.ValidUntilType=0" :disabled="!editable" v-model="formModel.ValidDate" type="date" align="right" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="备注" prop="Remark">
                                <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="5" v-model="formModel.Remark"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="contentBox_right">
                        <div class="panel-title">附件</div>
                        <div>
                            <app-uploader ref="appUploaderRef" :readonly='!editable' accept='all' :fileType='3' :max='10000' :value='formModel.AttachmentList' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024' @change='handleFilesUpChange'></app-uploader>
                        </div>
                    </div>
                </el-row>
            </template>
            <template slot="footer">
                <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <app-button :buttonType="2" @click="handleClose"></app-button>
                <app-button v-if="editable" :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as patentApi from '@/api/personnelManagement/Patent'
import * as classify from "@/api/classify";
import { vars } from "../common/vars.js"
import dayjs from 'dayjs'
import busMixins from './mixins'

export default {
    /**名称 */
    name: "patent-soft-edit",
    /**组件声明 */
    components: {},
    mixins: [busMixins],
    /**参数区 */
    props: {
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        }
    },
    /**数据区 */
    data() {
        return {
            goOn: false, // 是否继续添加
            termOfValidityTypes: vars.termOfValidityTypes,
            patentWorkTypes: vars.patentWorkTypes,
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,

            /**表单模型 */
            formModel: { 
                Name: '', // 知识产权名称
                PatentWorkType: 1, // 著作类型
                ClassifyId: null, //关联产品
                PatentNumber: '', // 专利号/登记号
                NoticeNumber: '', // 授权公告号
                NoticeDate: null, // 授权公告日
                ApplyDate: null, // 授权公告日
                ValidUntilType: 0, // 有效期至
                ValidDate: '', // 有效日期
                Remark: '', // 备注
                AttachmentList: [],
            },
            /**表单规则 */
            formRules: {
                Name: { fieldName: "知识产权名称", rules: [{ required: true }] },
                PatentNumber: { fieldName: "专利号/登记号", rules: [{ required: true }] },
                NoticeNumber: { fieldName: "授权公告号", rules: [{ required: true }] },
                ApplyDate: { fieldName: "申请日期", rules: [{ required: true, trigger: 'change' }] },
            },
            //关联产品列表
            relationList: [],
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '创建专利/软著'
            }else if(this.dialogStatus == 'update') {
                return '编辑专利/软著'
            }else if(this.dialogStatus == 'detail') {
                return '专利/软著详情'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail"
        },
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                let self = this;
                self.formModel = self.$options.data().formModel
                self.goOn = false
                if(val && self.dialogStatus != 'create' && self.id) {
                    self.getDetail()
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.getClassifyList()
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        getClassifyList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas.BusinessType = this.businessType
            classify.getListByCondition(postDatas).then(res => {
                self.relationList = res.Items.map(s => {
                    return {
                        value: s.Id,
                        label: s.Name
                    }
                });
            })
        },
        // 计算有效日期
        computedDate() {
            if (this.formModel.ApplyDate&&this.formModel.ValidUntilType) {
                let currentDate = this.formModel.ApplyDate,
                    lastNumber = this.termOfValidityTypes.find(s=>s.value == this.formModel.ValidUntilType).number || 0;
                this.formModel.ValidDate = dayjs(currentDate).add(lastNumber, 'year').format("YYYY-MM-DD")
            }
        },
        // 选择 有效期至
        chooseTag(row) {
            if (this.editable){
                this.formModel.ValidUntilType = this.formModel.ValidUntilType === row.value ? null : row.value;
                this.computedDate()
            }
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    //附件
                    self.formModel.AttachmentIdList = self.formModel.AttachmentList.map((s) => {return s.Id});
                    console.log(self.formModel)
                    let result = null;
                    self.buttonLoading = true;

                    if(this.dialogStatus == 'create') {
                        result = patentApi.add(this.formModel)
                    }else if(this.dialogStatus == 'update') {
                        result = patentApi.edit(this.formModel)
                    }
                    result.then(response => {
                        self.buttonLoading = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000,
                        });
                        if(self.goOn){
                            self.$refs.appUploaderRef.clearFiles();
                            self.$refs.formRef.resetFields();
                            self.formModel = self.$options.data().formModel;
                        }
                        console.log('goOn',self.goOn)
                        self.$emit('saveSuccess', self.goOn);
                    }).catch(err => {
                        self.buttonLoading = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            patentApi.detail({id: this.id}).then(response => {
                this.loading = false
                this.formModel = Object.assign({}, this.formModel, response)
            }).catch(err => {
                this.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleFilesUpChange(files) {
            this.formModel.AttachmentList = files
        },
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.el-tag{
    cursor: pointer;
    +.el-tag{margin-left: 15px;}
    &.isDisabled{
        cursor: not-allowed;
    }
}
.contentBox{
    display: flex;
    &_left{
        flex: 1;
        padding-right: 10px;
    }
    &_right{
        width: 34%;
        border-left: 1px solid #eee;
        padding: 13px 5px;
    }
}
</style>
