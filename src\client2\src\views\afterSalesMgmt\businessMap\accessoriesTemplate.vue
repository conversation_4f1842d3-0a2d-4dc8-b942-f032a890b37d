<template>
    <div class="accessoriesTemplate" v-loading="loading">
        <app-dialog title="批量导入设备部件模板" 
        class="configDialog"
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='446'
        >
        	<template slot="body">
                <div class="main">
                    <div>请选择需要导入的设备部件模板</div>
                    <ul>
                        <li v-for="(tl,index) in taskList" :key="index">
                            <el-radio v-model="radio" :label="tl.Id">{{tl.Name}}</el-radio>
                        </li>
                    </ul>
                </div>
                <pagination
                v-show="listQuery.total>0"
                :total="listQuery.total"
                :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize"
                layout="total, prev, pager, next, jumper"
                @pagination="handleCurrentChange"
                />
        	</template>
        	<template slot="footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button @click="handleSuccess">确定</el-button>
            </template>
    	</app-dialog>
    </div>
</template>    	
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
export default{
	name:'accessoriesTemplate',
	data(){
        return{
            loading:false,
            radio:'',
            listQuery:{
                total:0,
                PageIndex:1,
                PageSize:10,
            },
            taskList:[],
        }
    },
    components: {  },
    watch: {
        '$attrs.dialogFormVisible'(val) {
        	if(val){
                this.listQuery.PageIndex=1;
                this.radio='';
                this.taskList=[];
                this.getList();
        	}
        },
    },   
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        // change(d){
        //     this.radio=d.Id;
            
        // },
        handleCurrentChange(val){
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        getList(){
            this.loading=true;
            let postData={
                "pageIndex": this.listQuery.PageIndex,
                "pageSize":this.listQuery.PageSize,
                "name": '',
            }
            accessories.tempList(postData).then(res => {
                this.loading=false;
                this.listQuery.total=res.Total;
                this.taskList=res.Items;
            }).catch(err => {
                this.loading=false;
            })
        },
    	handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        handleSuccess(){
            this.$emit('getRadio',this.radio);;
        }
    } 
}
</script>
<style lang="scss" scoped>
.main{
    >div{
        padding:0 10px;
    }
    ul{
        height:388px;
        overflow: auto;
        padding:0 10px;
        li{
            margin-top: 10px;
        }
    }
}
</style>