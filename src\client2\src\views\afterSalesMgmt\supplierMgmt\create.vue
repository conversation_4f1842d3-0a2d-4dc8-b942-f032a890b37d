<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000" :maxHeight="600">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="100px">
          <div class="wrapper" v-loading="loading">
            <div class="left">
              <!-- <div class="panel-title"></div> -->
              <div>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="供应商类型" prop="SuppliersTypeId">
                      <el-select :disabled="!editable" style="width: 100%;" v-model="formData.SuppliersTypeId" placeholder>
                        <el-option v-for="item in SuppliersTypeNames" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <div class="panel-title">基本信息</div>
              <div>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="供应商名称" prop="Name">
                      <el-input :disabled="!editable" maxlength="30" v-model="formData.Name"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="供应商编号" prop="Code">
                      <el-input :disabled="!editable" maxlength="30" v-model="formData.Code"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="联系人1" prop="Contacts">
                      <el-input :disabled="!editable" maxlength="10" v-model="formData.Contacts"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系人电话1" prop="Phone">
                      <el-input :disabled="!editable" maxlength="20" v-model="formData.Phone"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="联系人2" prop="Contacts2">
                      <el-input :disabled="!editable" maxlength="10" v-model="formData.Contacts2"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系人电话2" prop="Phone2">
                      <el-input :disabled="!editable" maxlength="20" v-model="formData.Phone2"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="联系人3" prop="Contacts3">
                      <el-input :disabled="!editable" maxlength="10" v-model="formData.Contacts3"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系人电话3" prop="Phone3">
                      <el-input :disabled="!editable" maxlength="20" v-model="formData.Phone3"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="供应商邮箱" prop="Email">
                      <el-input :disabled="!editable" maxlength="50" v-model="formData.Email"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="结算方式" prop="Settlement">
                      <el-select :disabled="!editable" class="sel-ipt" v-model="formData.Settlement" placeholder="请选择" style="width: 211.5px;">
                        <el-option v-for="item in Settlements" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <div class="panel-title">付款信息</div>
              <div>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="开户银行" prop="BankDeposit">
                      <el-input :disabled="!editable" maxlength="20" v-model="formData.BankDeposit"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="银行账号" prop="BankAccount">
                      <el-input :disabled="!editable" maxlength="30" v-model="formData.BankAccount"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <div>
                <div class="panel-title">企业信息</div>
                <div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="企业注册代码" prop="EnterpriseRegistrationCode">
                        <el-input :disabled="!editable" maxlength="20" v-model="formData.EnterpriseRegistrationCode"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="法人代表" prop="Lega">
                        <el-input :disabled="!editable" maxlength="10" v-model="formData.Lega"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="公司规模" prop="Insize">
                        <el-select :disabled="!editable" class="sel-ipt" v-model="formData.Insize" placeholder="请选择" style="width: 211.5px;">
                          <el-option v-for="item in Insizes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="公司性质" prop="Nature">
                        <el-select :disabled="!editable" class="sel-ipt" v-model="formData.Nature" placeholder="请选择" style="width: 211.5px;">
                          <el-option v-for="item in Natures" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="执照有效期" prop="ValidityOfLicense">
                        <el-date-picker :disabled="!editable" style="width: 100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="formData.ValidityOfLicense" type="date" placeholder></el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="所属行业" prop="Industry">
                        <el-input :disabled="!editable" maxlength="50" v-model="formData.Industry"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="公司地址" prop="CompanyAddress">
                        <el-input v-model="formData.CompanyAddress" :disabled="!editable" maxlength="100"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="最大客户" prop="BiggestCustomers">
                        <el-input v-model="formData.BiggestCustomers" :disabled="!editable" maxlength="50"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="企业资质" prop="EnterpriseQualification">
                        <el-input type="textarea" rows="3" v-model="formData.EnterpriseQualification" :disabled="!editable" maxlength="500"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="经营范围" prop="BusinessScope">
                        <el-input type="textarea" rows="6" v-model="formData.BusinessScope" :disabled="!editable" maxlength="1000"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>

            <div class="right">
              <div>
                <div class="panel-title">证照附件</div>
                <div>
                  <app-uploader :readonly="!editable" accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>

        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as supplierManagement from "@/api/afterSalesMgmt/supplierManagement";
import {
  SettlementEnum,
  InsizeEnum,
  NatureEnum
} from "../enum";
export default {
  name: "create",
  directives: {},
  components: {},
  mixins: [],
  props: {
    dialogStatus: {
      type: String,
    },
    id: {
      type: String,
      default: "",
    },
    cid: {
      type: String,
      default: "",
    },
  },

  watch: {
    "$attrs.dialogFormVisible"(val) {
      this.getSuppliersTypeNames()
      if (val) {
        this.resetFormData();
        this.formData.SuppliersTypeId = this.cid
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    },
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建供应商";
      } else if (this.dialogStatus == "detail") {
        return "供应商详情";
      } else if (this.dialogStatus == "update") {
        return "编辑供应商";
      }
    },
  },
  created() {
    this.getSuppliersTypeNames()
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      loading: false,
      SuppliersTypeNames: [],
      disabledBtn: false,
      Settlements: SettlementEnum,
      Insizes: InsizeEnum,
      Natures: NatureEnum,
      rules: {
        SuppliersTypeId: {
          fieldName: "供应商类型",
          rules: [{
            required: true
          }]
        },
        Name: {
          fieldName: "供应商名称",
          rules: [{
            required: true,
          },],
        },
        Code: {
          fieldName: "供应商编号",
          rules: [{
            reg: /^[0-9a-zA-Z]+$/
          }, {
            required: true,
          },],
        },
        Contacts: {
          fieldName: "联系人",
          rules: [{
            required: true,
          },],
        },
        Phone: {
          fieldName: "联系人电话",
          rules: [{
            reg: /^((0\d{2,3}-\d{7,8})|(1[3-9]\d{9}))$/
          }, {
            required: true,
          },],
        },
        Settlement: {
          fieldName: "结算方式",
          rules: [{
            required: true,
          },],
        },
        EnterpriseRegistrationCode: {
          fieldName: "企业注册代码",
          rules: [{
            reg: /^[0-9a-zA-Z]+$/
          }],
        },
      },

      labelWidth: "110px",
      formData: {
        Id: null, //ID
        Name: "",
        Code: "",
        Contacts: "",
        Phone: "",
        Settlement: "",
        EnterpriseRegistrationCode: "",
        Lega: "",
        Insize: "",
        Nature: "",
        ValidityOfLicense: "",
        Industry: "",
        CompanyAddress: "",
        BusinessScope: "",
        AttachmentList: [],
        SuppliersTypeId: "",
      },
    };
  },
  methods: {
    getSuppliersTypeNames() {
      supplierManagement.getAll({}).then((res) => {
        this.SuppliersTypeNames = res.map((s) => {
          return {
            label: s.Name,
            value: s.Id,
          };
        });
      });
    },
    resetFormData() {
      let temp = {
        Name: "",
        SuppliersTypeId: null,
        Code: "",
        Contacts: "",
        Phone: "",
        Settlement: "",
        EnterpriseRegistrationCode: "",
        Lega: "",
        Insize: "",
        Nature: "",
        ValidityOfLicense: "",
        Industry: "",
        CompanyAddress: "",
        BusinessScope: "",
        AttachmentList: [], //
      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    createData() {
      let validate = this.$refs.formData.validate();

      Promise.all([validate]).then((valid) => {
        //提交数据保存
        let postData = JSON.parse(JSON.stringify(this.formData));
        postData.AttachmentIdList =
          postData.AttachmentList && postData.AttachmentList.map((s) => s.Id);

        if (this.dialogStatus == "create") {
          delete postData.Id;
        }

        this.disabledBtn = true;
        let result = null;
        if (this.dialogStatus == "create") {
          //    postData.SuppliersTypeId = this.cid;
          result = supplierManagement.add(postData);
        } else if (this.dialogStatus == "update") {
          result = supplierManagement.edit(postData);
        }

        result
          .then((res) => {
            this.disabledBtn = false;
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000,
            });

            this.$refs.appDialogRef.createData();
          })
          .catch((err) => {
            this.disabledBtn = false;
          });
      });
    },
    getDetail() {
      this.loading = true;
      supplierManagement.detail({
        id: this.id,
      })
        .then((res) => {
          this.loading = false;

          this.formData = Object.assign({}, this.formData, res);

        })
        .catch((err) => {
          this.loading = false;
        });
    },

    handleFilesUpChange(files) {
      this.formData.AttachmentList = files;
    },
    handleClose() {
      //location.reload();
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }

  .right {
    width: 35%;
  }
}

// .staffingBox{
//     padding-bottom: 20px;
// }
.lastDiv {
  height: calc(100% - 100px);
  overflow-y: auto;
}
</style>
