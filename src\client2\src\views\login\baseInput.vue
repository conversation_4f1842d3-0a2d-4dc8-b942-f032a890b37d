<template>
    <div class="input-wrapper">
        <input
            v-bind="$attrs"
            v-bind:value="value"
            v-on:input="$emit('input', $event.target.value)"
            placeholder="请输入*"
        >
        <div class="input-placeholder-label">{{ placeholder.text }}</div>
    </div>
</template>

<script>
export default {
    name: 'base-input',
    props: {
        value: '',
        placeholder: {
            type: Object,
            required: false
        }
    },
    data() {
        return {

        }
    },
}
</script>

<style scoped>

.input-wrapper{
    width: 100%;
    position: relative;
}

.input-placeholder-label{
    position: absolute;
    top: 1px;
    bottom: 1px;
    left: 5px;
    right: 5px;
    height: calc(100% - 2px);
    z-index: -1;
}

input{
    box-sizing: border-box;
    border: 1px solid #d9d9d9;
    width: 100%;
    border-radius: 2px;
    height: 40px;
    line-height: 40px;
    padding: 0 5px;
    background: transparent;
}

input::placeholder{
    color: blue;
}

input::placeholder:after{
    content: '*';
    color: red;
}

input:hover{
    border-color: #c0c0c0;
}

</style>