<template>
    <!-- 问题改进管理 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="true"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="110"
            :loading="listLoading"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :startOfTable="startOfTable"
            @sortChagned="handleSortChange">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'120px'" :items="tableSearchItems"
                        @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索问题标题/方案标题" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>
                    </app-table-form>
                </template>
                <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD') }}</template>

                <template slot="EffectEvaluationType" slot-scope="scope">
                    <span class="item-status" v-if="scope.row.EffectEvaluationType"
                        :style="{backgroundColor: getEffectEvaluationTypeObj(scope.row.EffectEvaluationType).bgColor,color: getEffectEvaluationTypeObj(scope.row.EffectEvaluationType).color}"
                    >{{ getEffectEvaluationTypeObj(scope.row.EffectEvaluationType).label }}</span>
                    <template v-else>无</template>
                </template>
                
                <template slot="AdoptSuggestionType" slot-scope="scope">
                    <span class="item-status" v-if="scope.row.AdoptSuggestionType"
                        :style="{backgroundColor: getAdoptSuggestionTypeObj(scope.row.AdoptSuggestionType).bgColor,color: getAdoptSuggestionTypeObj(scope.row.AdoptSuggestionType).color}"
                    >{{ getAdoptSuggestionTypeObj(scope.row.AdoptSuggestionType).label }}</span>
                    <template v-else>无</template>
                </template>
                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 详情 -->
                    <app-table-row-button @click="handleUpdate(scope.row, 'detail')" text="详情" :type="2"></app-table-row-button>
                    <!-- 编辑 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row,'update')" text="编辑" :type="2"></app-table-row-button>
                    <!-- 删除 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnDel')&&!scope.row.EffectEvaluationType" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                </template>
            </app-table>
        </div>
        <!----------------------------------------- 分页 ------------------------------------------->
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>


    <!-- 创建/修改/详情 解决方案 -->
    <programme-create v-if="submitProgrammeVisible" :id="selectRow.Id" :problemId="selectId"
    :dialogStatus="submitProgrammeStatus" :dialogFormVisible="submitProgrammeVisible"
    @closeDialog="submitProgrammeVisible=false" @saveSuccess="createSubmitProgrammeSuccess" />
</div>
</template>
<script>
import { ProductQuestionStatusEnum, EffectEvaluationTypeEnum, AdoptSuggestionTypeEnum } from "./enum.js";
import * as productQuestionSolutionImprovementApi from '@/api/knowledge/productQuestionSolutionImprovement.js'

import indexPageMixin from "@/mixins/indexPage";


import programmeCreate from "./programmeCreate";

export default {
    name: 'problem-improve-my-programme',
    mixins: [indexPageMixin],
    components: {
        programmeCreate
    },
    props: {
        pageType: { // 1 人事行政管理下面的  问题改进管理   2  公司门户 下面的 问题改进
            type: Number,
            default: 1,
        },
    },
    watch: {
    },
    filters: {
        // SurveyTypeFilter(val) {
        //     let obj = SurveyTypeEnum.find(
        //         s => s.value == val
        //     );
        //     if (obj) {
        //         return obj.label;
        //     }
        //     return "无";
        // }
    },
    created() {
        this.getList();
    },
    data() {
        return {
            ProductQuestionStatusEnum,
            EffectEvaluationTypeEnum,
            AdoptSuggestionTypeEnum,

            /** 编辑解决方案 */
            selectId: '',
            selectRow: {},
            submitProgrammeStatus: 'detail',
            submitProgrammeVisible: false,

            total: 0,
            listQuery: {},
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
            ],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "Title", label: "问题标题", showOverflowTooltip: true}},
                { attr: { prop: "ProductSpecificationImprovementName", label: "相关产品", showOverflowTooltip: true} },
                { attr: { prop: "CaseName", label: "方案标题", showOverflowTooltip: true} },
                { attr: { prop: "EffectEvaluationType", label: "实验结果"}, slot: true },
                { attr: { prop: "AdoptSuggestionType", label: "是否采纳"}, slot: true },
                { attr: { prop: "TestEmployeesName", label: "实验人", showOverflowTooltip: true} },
            ],
        }
    },
    methods: {
        // 获取列表
        getList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);
            self.listLoading = true;
            productQuestionSolutionImprovementApi.getMyListPage(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        // 列表排序
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        // 重置搜索
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        // 表格文本框果过滤
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 提交方案弹窗  确定返回
        createSubmitProgrammeSuccess(){
            this.submitProgrammeVisible = false;
            this.getList()
        },
        // 弹出编辑框 方案弹窗
        handleUpdate(row, optType = "detail"){
            this.selectId = row.ProductQuestionImprovementId;
            this.selectRow = row;
            this.submitProgrammeStatus = optType;
            this.submitProgrammeVisible = true;
        },
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                productQuestionSolutionImprovementApi.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$emit('reload')
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        // 获取  试验结果 对象
        getEffectEvaluationTypeObj(val){
            return this.EffectEvaluationTypeEnum.find(s => s.value == val) || {};
        },
        // 获取  是否采纳 对象
        getAdoptSuggestionTypeObj(val){
            return this.AdoptSuggestionTypeEnum.find(s => s.value == val) || {};
        },
    }
}
</script>
<style lang="scss" scoped>
.app-container{
    width: 100%;
    height: 100%;
    margin: 0;
    left: 0;
    top: 0;
}
</style>