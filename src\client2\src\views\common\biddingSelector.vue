<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      :width='1200'
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='BidNumber'>
            <el-input style="width: 100%;" v-model="listQueryParams.BidNumber" placeholder=""></el-input>
          </template>
          <template slot='ProjectName'>
            <el-input style="width: 100%;" v-model="listQueryParams.ProjectName" placeholder=""></el-input>
          </template>
          <template slot='EmployeeName'>
            <el-input style="width: 100%;" v-model="listQueryParams.EmployeeName" placeholder=""></el-input>
          </template>
        </app-table-form>
      </template>

      <!-- <template slot="ApprovalStatus" slot-scope="scope">
        <span
          v-if="getApprovalObj(scope.row.ApprovalStatus).label"
          class="item-status"
          :style="{
            backgroundColor: getApprovalObj(scope.row.ApprovalStatus).color
          }"
        >
          <span>{{ getApprovalObj(scope.row.ApprovalStatus).label }}</span>
        </span>
        <span v-else>无</span>
      </template> -->
      
      <template slot="BidStatus" slot-scope="scope">
        <span :class="`status-${scope.row.BidStatus}`">{{ scope.row.BidStatus | statusFilter }}</span>
      </template>

      <template slot="BidType" slot-scope="scope">
        <span>{{ scope.row.BidType | typeFilter }}</span>
      </template>
      <template slot="BidTime" slot-scope="scope">{{ scope.row.BidTime | dateFilter('YYYY-MM-DD') }}</template>
      <template slot="PrincipalEmployeeList" slot-scope="scope">
        <span
          v-if="scope.row.PrincipalEmployeeList"
        >{{ scope.row.PrincipalEmployeeList.map(s => s.Name).join(',') }}</span>
      </template>
    </listSelector>
  </div>
</template>

<script>
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
import { vars } from "@/views/salesMgmt/common/vars";

export default {
  name: "bidding-selector",
  components: {
    listSelector,

  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },  
  watch: {
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList(val) {
      if (val && val.length > 0) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
      }
    },
  },
  filters: {
    typeFilter(val) {
      let obj = vars.bidMgmt.BidTypes.find((s) => s.value == val);
      if (obj) {
        return obj.label;
      }
      return val;
    },
    statusFilter(val) {
      let obj = vars.bidMgmt.BidStatus.find((s) => s.value == val);
      if (obj) {
        return obj.label;
      }
      return val;
    },
  },
  data() {
    return {
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/Bids/GetListPage",
      listSelectorMultiple: this.multiple,
      // listSelectorCondition: this.condition ? this.condition : {},
      listQueryParams: {
        BidNumber: '',
        ProjectName: '',
        EmployeeName: '',
      },
      tableSearchItems: [
        { prop: "BidNumber", label: "招标编号" },
        { prop: "EmployeeName", label: "负责人" },
        { prop: "ProjectName", label: "投标项目" },
      ],
      listSelectorTitle: "选择关联投标",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",

      listSelectorColumnData: [
        { attr: { prop: "BidNumber", label: "招标编号", showOverflowTooltip: true } },
        // {
        //   //审批状态（订单创建、订单变更 审批记录最后一条状态）
        //   attr: { prop: "ApprovalStatus", label: "审批状态" },
        //   slot: true,
        // },
        {
          attr: { prop: "BidStatus", label: "状态" },
          slot: true,
        },
        { attr: { prop: "ProjectName", label: "项目名称", showOverflowTooltip: true } },
        { attr: { prop: "ClientUnits", label: "客户单位" } },
        {
          attr: { prop: "BidType", label: "招标类型" },
          slot: true,
        },
        {
          attr: { prop: "BidTime", label: "开标时间" },
          slot: true,
        },
        {
          attr: { prop: "PrincipalEmployeeList", label: "负责人" },
          slot: true,
        },
        {
          attr: {
            prop: "ProjectPrice",
            label: "项目金额（万）",
            align: "center",
          },
        },
        { attr: { prop: "BondPrice", label: "保证金（万）" } },
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  methods: {
    // getApprovalObj(status) {
    //     return vars.orderMgmt.approvalStatus.find(s => s.value == status) || {};
    // },
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      this.listQueryParams.BidNumber = ''
      this.listQueryParams.ProjectName = ''
      this.listQueryParams.EmployeeName = ''
      this.handleFilter()
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let list =
        data.map((s) => {
          s.Code = s.OrderNumber;
          return s;
        }) || [];
      this.$emit("changed", JSON.parse(JSON.stringify(list)));
      this.listSelectorCloseDialog();
    },
  },
};
</script>

<style lang="scss" scoped>


.status-1 {
  color: red;
}
.status-2 {
  color: rgb(83, 168, 255);
}
.status-3 {
  color: rgb(255, 153, 0);
}
</style>