<template>
    <!-- 企业资质 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="true"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="120"
            :loading="listLoading"  :startOfTable="startOfTable"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索寄语内容" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>
                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                        </template>
                    </app-table-form>
                </template>


                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 详情 -->
                    <app-table-row-button @click="handleUpdate(scope.row,'detail')" text="详情" :type="2"></app-table-row-button>
                    <!-- 编辑 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row, 'update')" :type="1"></app-table-row-button>
                    <!-- 删除 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                </template>
            </app-table>
        </div>
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogFormVisible" :id="selectId" :dialogStatus="createDialogStatus"
    :dialogFormVisible="createDialogFormVisible" @closeDialog="createDialogFormVisible=false"
    @saveSuccess="createSaveSuccess" @reload="getList"></create-page>
    
    <rule-setting v-if="ruleSettingVisible" :dialogFormVisible="ruleSettingVisible" @closeDialog="ruleSettingVisible=false"></rule-setting>
</div>
</template>
<script>
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import ruleSetting from "./ruleSetting";
import * as workMessageApi from '@/api/personnelManagement/workMessage'

export default {
    name: 'work-message',
    mixins: [indexPageMixin],
    components: {
        ruleSetting,
        createPage,
    },
    filters: {
    },
    created() {
        this.getList();
    },
    data() {
        return {
            selectId: '',
            createDialogStatus: 'create',
            createDialogFormVisible: false,

            ruleSettingVisible: false,

            total: 0,
            listQuery: {},
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                // { prop: "Status", label: "状态" },
                // { prop: "IssueDate", label: "发证时间" },
                // { prop: "EffectiveDate", label: "有效期至" },
            ],
            multipleSelection: [],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "Content", label: "寄语内容", showOverflowTooltip: true,} },
            ],
        }
    },
    methods: {
        getList() {
            let self = this;
            self.listLoading = true;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);
            workMessageApi.getList(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 表格顶部按钮点击事件
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleUpdate(null, 'create');
                    break;
                case "btnSetting":
                    this.ruleSettingVisible = true;
                    break;
                case "btnEffectivePeriod":
                    this.handleEffectivePeriod();
                    break;
                default:
                    break;
            }
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.selectId = optType === 'create' ? '' : row.Id;
            this.createDialogStatus = optType
            this.createDialogFormVisible = true
        },
        /** 编辑框 点确定后 关闭 并刷新列表 */
        createSaveSuccess(d) {
            console.log('d',d)
            if (!d) {
                this.createDialogFormVisible = false
            }
            // this.listQuery.PageIndex = 1;
            this.getList()
        },
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                workMessageApi.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    }
}
</script>
