<template>
  <div class="queryPhenomenon">
    <app-dialog
      title="故障维修百科"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='700'
      :maxHeight="600"
    >
      <template slot="body" v-loading="loading">
        <main>
          <div class="searchBox">
            <el-input v-model="searchVal" placeholder="请输入查询内容"></el-input>
            <el-button size="mini" type="primary" @click="handleSearch()">查询</el-button>
          </div>
          <div @click="dialogReferFormVisible = true">已查询到<i style="color:#409EFF;"> {{listData.length}} </i>条相关数据</div>
          <div class="mainBox" v-if="listData.length>0">
            <el-card class="box-card" v-for="(item,index) in listData" :key="index" @click.native="handleClick(item)">
              <div class="omit">{{index+1}}、{{item.FailureSymptom}}</div>
              <div class="cList cl">
                <span class="fl">{{item.FailureCaseCode}}</span>
              </div>
            </el-card>
          </div>
          <no-data v-else></no-data>
        </main>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>
    <reference-list
      @closeDialog="closeReferDialog"
      @saveSuccess="saveRefSuccess"
      :dialogFormVisible="dialogReferFormVisible"
      :msg='msg'
      :outerData="outerData"
      :oData="oData">
    </reference-list>
  </div>
</template>

<script>
import * as failurecase from '@/api/failurecase';
import referenceList from "./referenceList";
import NoData from "@/views/common/components/noData";
export default {
  name: "queryPhenomenon",
  components: {
    referenceList,
    NoData
  },
  mixins: [],
  props: {
    outerData:{
      type:Object,
      default:function(){
        return null
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
        if(val){
          this.searchVal='';
          this.loadSelectData();
        }
    }
  },
  created() {
    
  },
  data() {
    return {
      oData:false,
      loading:false,
      searchVal:'',
      dialogReferFormVisible:false,
      listData:[],
      msg:null,
    };
  },
  methods: {
    saveRefSuccess(d){
      this.$emit('saveSuccess',d);
      this.$refs.appDialogRef.handleClose();
    },
    handleClick(d){
      this.msg=d;
      this.oData=false;
      if(this.msg.FailureSymptom == this.outerData.Phenomenon){
        this.oData=true;
      }
      this.dialogReferFormVisible=true;
    },
    loadSelectData(){
      this.loading=true;
        failurecase.getListByCondition({"failureCaseCode": "","failureSymptom": this.searchVal}).then(res => {
          this.loading=false;
          this.listData=res;
        })
    },
    handleSearch(){
      this.loadSelectData();
    },
    closeReferDialog(){
      this.dialogReferFormVisible=false;
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style scoped>
  .searchBox{
    position: relative;
  }
  .searchBox >>> .el-input{
    width:calc(100% - 50px);
  }
  .searchBox >>> .el-button{
    position: absolute;
    right:0;
    top:0;
  }
  .box-card >>> .el-card__body{
    padding:10px;
  }
</style>
<style lang="scss" scoped>
.box-card{
  margin-bottom:10px;
  cursor: pointer;
}
main{
  >div:nth-child(2){
    padding:10px 0 8px 0;
  }
}
.cList{
  span{
    display: inline-block;
    color:#F59A23;
    margin-right: 6px;
    border: 1px solid;
    padding: 2px 4px;
    border-radius: 6px;
    margin-bottom:6px;
  }
}
.mainBox{
  height:534px;
  overflow-y: auto;
}
.omit{
  width:100%;
  margin-bottom: 6px;
}
</style>