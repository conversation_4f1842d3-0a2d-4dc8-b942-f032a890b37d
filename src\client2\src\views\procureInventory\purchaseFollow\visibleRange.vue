<template>
<div>
    <app-dialog title="可见范围设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='700' :width='900'>
        <template slot="body">
            <div class="wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading"
                    :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false'
                    :optColWidth="160">
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                          <app-table-form :label-width="'100px'" :items="[]" :layoutMode='layoutMode'>
                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <el-button type="primary" @click="handleDialog('create')">创建角色</el-button>
                            </template>
                          </app-table-form>
                        </template>
                        <template slot="BusinessNameList" slot-scope="scope">{{scope.row.BusinessNameList||'无'}}</template>
                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleRelation(scope.row)" :type="1" text="关联人员"></app-table-row-button>
                            <template v-if="!scope.row.IsDefault">
                                <!-- 非默认角色才可编辑/删除 -->
                                <app-table-row-button @click="handleReview(scope.row, 'update')" :type="1"></app-table-row-button>
                                <app-table-row-button @click="handleDel(scope.row)" :type="3" text="删除"></app-table-row-button>
                            </template>
                        </template>
                    </app-table>

                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" type='2' text="关闭"></app-button>
        </template>
    </app-dialog>
    <!-- 创建/编辑  角色 -->
    <add-role v-if="dialogFormVisible"
        @closeDialog="closeDialog"
        @saveSuccess="handleSaveSuccess"
        :dialogFormVisible="dialogFormVisible"
        :dialogStatus="dialogStatus"
        :id="selectId" @reload="handleReload()"
    ></add-role>
    <related-personnel v-if="dialogRelationVisible"
        @closeDialog="dialogRelationVisible=false"
        :dialogFormVisible="dialogRelationVisible"
        :id="selectId" @reload="handleReload()"
    ></related-personnel>
</div>
</template>

<script>
import * as businessRoleApi from "@/api/businessRole";
import indexPageMixin from "@/mixins/indexPage";

import addRole from './addRole.vue'
import relatedPersonnel from './relatedPersonnel.vue'
export default {
    name: "purchaseFollow-visible-range",
    mixins: [indexPageMixin],
    components: {
        addRole,
        relatedPersonnel,
    },
    props: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList();
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
    },
    data() {
        return {
            // 创建/编辑/详情
            dialogStatus: '',
            dialogFormVisible: false,
            selectId: '',


            // 关联人员
            dialogRelationVisible: false,

            listLoading: false,
            layoutMode: 'simple',
            tabColumns: [
                {attr: {prop: "Name",label: "角色名称", showOverflowTooltip: true}},
                {attr: {prop: "BusinessNameList",label: "可见范围", showOverflowTooltip: true},slot: true},
                {attr: {prop: "PeopleNumber",label: "关联人数"}},
            ],
            listQuery: {
            },
            tabDatas: [], //原始数据
            total: 0,
        };
    },
    methods: {
        handleReload() {
            this.getList()
            this.$emit('reload')
        },
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            this.listLoading = true;
            businessRoleApi.getList(postData).then(res => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch(err => {
                this.listLoading = false;
            });
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        //弹出添加框
        handleDialog(activeName) {
            this.selectId = "";
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        // 弹出编辑框 / 查看详情
        handleReview(row, optType = "detail") {
            this.selectId = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        handleSaveSuccess(_formData) {
            this.$emit('reload')
            this.getList();
            this.closeDialog();
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        handleRelation(rows){
            this.selectId = rows.Id;
            this.dialogRelationVisible = true;
        },
        handleDel(rows){
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }
            this.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                businessRoleApi.del(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.handleReload()
                });
            });
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    }
};
</script>

<style lang="scss" scoped>
</style>
