<template>
    <div>
        <app-dialog 
            title="验收记录" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
            :maxHeight='600'
        >
            <template slot="body">
                <div class="wrapper">
                    <div class="item" v-for="(a, idx) in list" :key="idx">
                        <div>
                            {{ idx + 1 }}、{{ a.CreateEmployee.Name }} {{ a.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }} 发起 验收申请
                        </div>
                        <div>
                            审批结果：{{ a.ApprovalState | approvalStatusFilter }} <el-button type="text" class="button" @click="handleDialog(a)">查看详情</el-button>
                        </div>
                        <!-- {{ a.AcceptanceConclusion }} -->
                    </div>
                </div>
                <pagination
                    v-show="total>0"
                    :total="total"
                    :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize"
                    @pagination="handleCurrentChange"
                    @size-change="handleSizeChange"
                />
            </template>
            <template slot="footer">
                <el-button @click="handleClose">关闭</el-button>
            </template>

        </app-dialog>

        <!-- <accept-page
            @closeDialog='closeAcceptDialog' 
            @saveSuccess='handleAcceptSaveSuccess'
            :dialogFormVisible='dialogAcceptFormVisible'
            :dialogStatus='dialogAcceptStatus' 
            :isContinue='isAccept'
            :isOnlyViewDetail='true'
            :id='acceptId'
            :approvalId='approvalId'
        ></accept-page> -->

    </div>
</template>

<script>
import { vars } from '../../common/vars'
// import acceptPage from '../accept'
import * as impManagement from "@/api/implementation/impManagement";
  export default {
    name: "project-mgmt-accept-list",
    directives: {
    },
    components: {
        // acceptPage,
    },
    props: {
        // dialogStatus: { //create、update、detail
        //     type: String
        // },
        project: { 
            type: Object,
            // required: true
        },
        //是否满足验收条件（列表中的项目如果没有验收操作，该列表中也不应该有）
        //如果首页中的项目可以“验收”，那么也可以从该列表中选择一条记录当默认值来验收
        isAccept: {
            type: Boolean,
            default: true
        },
    },
    filters: {
        //验收状态
        approvalStatusFilter(status) {
            let tmp = vars.approvalStatuObj.approvalStatus.find(s => s.value == status)
            if(tmp) {
                return tmp.label
            }
            return '--'
        }
    },    
    computed: {
        
    },
    watch: {
        '$attrs.dialogFormVisible'(val) {
            if(val) {
                this.getList()
            }
        },
    },
    created() {
    },
    data() {

        return {
            listQuery: {
                PageIndex: 1,
                PageSize: 10,
                ImplementId: ''
            },
            acceptId: '',
            approvalId: '',
            list: [],
            total: 0,
            dialogAcceptStatus: 'detail',
            dialogAcceptFormVisible: false,
        };
    },
    methods: {
        handleDialog(accept) {
            //待审批（审批模式）
            if(accept.ApprovalState == 1) {
                this.dialogAcceptStatus = 'approval'
            }else{
                //审批详情模式
                this.dialogAcceptStatus = 'detail'
            }
            this.acceptId = accept.Id
            this.approvalId = accept.ApprovalId
            this.dialogAcceptFormVisible = true
        },
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery))
            postData.ImplementId = this.project.Id
            impManagement.getAcceptList(postData).then(res => {
                this.list = res.Items
                this.total = res.Total
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        closeAcceptDialog() {
            this.dialogAcceptFormVisible = false
        },
        handleAcceptSaveSuccess() {
            this.closeAcceptDialog()
            this.getList()
            this.$emit('reload')
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size
            this.getList()
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page
            this.listQuery.PageSize = val.size
            this.getList()
        },
    }
};
</script>

<style lang="scss" scoped>


.wrapper{
    .item{
        margin-bottom: 6px;
        
    }
}

// .panel-title{
//     font-size: 16px;
//     font-weight: 700;
//     padding-bottom: 4px;
//     padding-left: 6px;
//     border-bottom: 1px solid #DCDFE6;
//     margin-bottom: 10px;
// }
</style>
