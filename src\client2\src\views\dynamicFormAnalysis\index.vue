<template>
    <div style="height: 100%; background: #fff; padding: 10px;">
        
        <!-- <index-dynamic :formObj='formObj' v-if="formObj"></index-dynamic> -->
        <!-- <span v-if="formObj">
            {{ formObj.formContent }}
        </span> -->
        <div>
            <el-button type="text" @click="() => showFormStruct = !showFormStruct">
                显示/隐藏表单结构
            </el-button>
            <div v-show="showFormStruct">{{ formObj }}</div>
        </div>
        <tree-item class="item" :item="formObj.formContent" v-if="formObj"></tree-item>

        <!-- <button @click="handleUpdate">saveaaa</button> -->
    </div>
</template>

<script>
    import indexPageMixin from '@/mixins/indexPage'
    import * as df from '@/api/dynamicForm'
    import mixin from '../dynamicFormCommon/mixins'
    import IndexDynamic from './index-dynamic'
    import TreeItem from './treeItem'
    import { listToTreeSelect } from '@/utils'

    export default {
        name: 'dynamic-form-analysis',
        components: {
            IndexDynamic,
            TreeItem,
        },
        mixins: [indexPageMixin, mixin],
        watch: {
            formObj: {
                handler (newval) {
                    this.$store.commit('SAVE_FORM_OBJ', newval)
                    this.formObj = this.$store.getters.formObj
                },
                deep: true
            },
        },
        data() {
            return {
                showFormStruct: false,
                formObj: null,//表单结构
                viewModel: { //表单结构对应的模型结构，所有绑定的表单必须在form里面
                    // formname: {//表单名称
                    //     // requestUrl: '',//表单请求地址
                    //     // requestMethod: '',//表单请求方式
                    //     // dialogVisible: true,//表单是否显示
                    //     // rules: [
                    //     //     {
                    //     //         ctrlName: []
                    //     //     }
                    //     // ],//表单验证规则
                    //     // isRequiredValidation: false,
                    //     // temp: {//当前表单关联的字段数据
                    //     //     ctrlName: '',//控件名称
                    //     // }
                    // }
                },
            }
        },
        created() {
            this.getFormStruct()
        },
        methods: {
            //伪代码，编辑
            handleUpdate() {
                //显示编辑框

                
                let formObj = this.$store.getters.formObj
                let editform = formObj.formContent.children[1].attrs.find(s => s.attrName == 'isVisible')
                editform.value = 'true'

                formObj.formContent.children[1].children.forEach(c => {
                    if(c.type != 0 && c.type != 1 && c.type != 2 && c.type != 7) {
                        c.value = '2'
                    }
                })


            },
            ////伪代码：清空编辑框
            // handleClearInput() {
            //     let formObj = this.$store.getters.formObj
            //     formObj.formContent.children[1].children.forEach(c => {
            //         if(c.type != 0 && c.type != 1 && c.type != 2 && c.type != 7) {
            //             c.value = ''
            //         }
            //     })
            // },
            // // 伪代码，查询
            // handleSearch() {
            //     let formObj = this.$store.getters.formObj
            //     let table = formObj.formContent.children[0].children[2]
            //     df.getDatas({
            //         formId: formObj.id
            //     }).then(response => {
            //         this.$set(table, 'value', response.Items)
            //     }).catch(err => {})
            // },
            // // 伪代码，新增
            // handleAdd() {
            //     let formObj = this.$store.getters.formObj
            //     formObj.formContent.children[1].children.forEach(c => {
            //         // if(c.type != 0 && c.type != 1 && c.type != 2 && c.type != 7) {
            //         //     c.value = '123'
            //         // }
            //     })
            //     let editform = formObj.formContent.children[1].attrs.find(s => s.attrName == 'isVisible')
            //     editform.value = 'true'
            // },
            ////新增伪代码，弹出页面第二个表单
            // handleToggle() {
            //     // let formObj = this.$store.getters.formObj
            //     // let editform = formObj.formContent.children[1].attrs.find(s => s.attrName == 'isVisible')
            //     // let editformVisible = editform.value
            //     // editformVisible == 'true' ? editform.value = 'false' : editform.value = 'true'
            // },
            //// 伪代码，使用表单保存数据
            // handleSave() {
            //     let formObj = this.$store.getters.formObj
            //     let postDatas = {
            //         FormId: formObj.id,
            //         ComponentRowId: '',
            //         ComponentInfos: []
            //     }
            //     formObj.formContent.children[1].children.forEach(c => {
            //         if(c.type != 0 && c.type != 1 && c.type != 2 && c.type != 7) {
            //             let ctrlName = c.attrs.find(a => a.attrName == 'name').value
            //             let ctrlType = c.type
            //             let ctrlVal = c.value
            //             postDatas.ComponentInfos.push({
            //                 ctrlType: ctrlType + '',
            //                 fieldName: ctrlName,
            //                 componentValue: ctrlVal
            //             })
            //         }
            //     })
            //     df.addData(postDatas).then(res => {
            //         this.$notify({
            //             title: '成功',
            //             message: '保存成功',
            //             type: 'success',
            //             duration: 2000
            //         })
            //         //隐藏新增框
            //         let editform = formObj.formContent.children[1].attrs.find(s => s.attrName == 'isVisible')
            //         let editformVisible = editform.value
            //         editformVisible == 'true' ? editform.value = 'false' : editform.value = 'true'
            //     }).catch(err => {
            //     })
            // },

            //获取表单结构
            getFormStruct() {
                let formId = this.$route.params.id
                df.detail({ formId: formId }).then(response => {
                    let datas = response

                    datas.formContent = datas.formContent.map(s => {
                        s.children && delete s.children
                        return s
                    })

                    //将表单结构中复杂属性值字符串转为对象
                    datas = this.fetchFormStructDatasToObject(datas)
                    datas.formContent = this.mergeCtrlData(datas.formContent)

                    //创建表单时，后台会默认创建一个name=id的控件，用来标识表单唯一id——需要隐藏起来，不可操作
                    datas.formContent = datas.formContent.filter(s => s.type != 0)

                    //将表单数据转成tree格式
                    let treeDatas = listToTreeSelect(datas.formContent, undefined, undefined, {key: 'id', parentKey: 'pid'})
                    if(treeDatas && treeDatas.length > 0) {
                        datas.formContent = treeDatas[0]
                    }

                    // this.formObj = datas

                    this.$store.commit('SAVE_FORM_OBJ', datas)
                    this.formObj = this.$store.getters.formObj

                    // this.generateViewModel(this.formObj.formContent)
                })
            },
            //生成所有表单需要的模型对象
            // generateViewModel(ctrlObj) {
            //     ctrlObj.children.map(f => {
            //         if(f.type == 2) {
            //             let formName = f.attrs.find(a => a.attrName == 'name').value //表单名称
            //             let action = f.attrs.find(a => a.attrName == 'action').value //请求地址
            //             let method = f.attrs.find(a => a.attrName == 'method').value //请求方式get、post
            //             let isVisible = f.attrs.find(a => a.attrName == 'isDialogForm').value == 'true' //是否显示
            //             let isRequiredValidation = f.attrs.find(a => a.attrName == 'isRequiredValidation').value == 'true' //是否需要验证
            //             if(!this.viewModel[formName]) {
            //                 this.$set(this.viewModel, formName, {})
            //             }

            //             this.$set(this.viewModel[formName], 'requestUrl', action)
            //             this.$set(this.viewModel[formName], 'requestMethod', method)
            //             this.$set(this.viewModel[formName], 'dialogVisible', isVisible)
            //             this.$set(this.viewModel[formName], 'isRequiredValidation', isRequiredValidation)

            //             if(!this.viewModel[formName]['temp']){
            //                 this.$set(this.viewModel[formName], 'temp', {})
            //             }

            //             let ctrls = (f.children || []).filter(s => s.type != 0 && s.type != 1 && s.type != 2)
            //             if(ctrls && ctrls.length > 0) {
            //                 ctrls.forEach(s => {
            //                     let ctrlName = s.attrs.find(a => a.attrName == 'name').value

            //                     this.$set(this.viewModel[formName]['temp'], ctrlName, '')
            //                 })
            //             }

            //             // requestUrl: '',//表单请求地址
            //             // requestMethod: '',//表单请求方式
            //             // dialogVisible: true,//表单是否显示
            //             // rules: [
            //             //     {
            //             //         ctrlName: []
            //             //     }
            //             // ],//表单验证规则
            //             // isRequiredValidation: false,
            //             // temp: {//当前表单关联的字段数据
            //             //     ctrlName: '',//控件名称
            //             // }
            //         }
            //         if(f.children) {
            //             this.generateViewModel(f)
            //         }
            //     })
            // },
        },
    }
</script>

<style scoped>
    .sel-ipt,
    .dat-ipt {
        width: 100%;
    }

    .avatar {
        width: 68px;
        height: 68px;
    }

    .tip-avatar {
        width: 140px;
        height: 140px;
    }

    .avatar,
    .tip-avatar {
        border-radius: 50%;
    }

    /* .cus_wdt{
        width: 200px;
    } */
</style>
