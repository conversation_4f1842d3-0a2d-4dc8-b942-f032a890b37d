<!--分类 添加/编辑-->
<template>
  <div>
    <app-dialog title="学习记录" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100" :maxHeight="700">
      <template slot="body">
            <div class="wrapperBody">
                <el-form ref="formData" label-position="right" label-width="120px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="姓名">{{formModel.EmployeeName||'无'}}</el-form-item>
                            <el-form-item label="学习课程">{{formModel.StudyTrainNum||'无'}}</el-form-item>
                            <el-form-item label="通过考试">{{formModel.ExamPassNum||'无'}}</el-form-item>
                            <el-form-item label="可用积分">{{formModel.UsableIntegral||'无'}}</el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="等级">
                                <img v-if="formModel.Level" :src="levelData[`levelImg${formModel.Level}`]" style="width:28px;height:28px;float:left;" />
                                <div v-else>无</div>
                            </el-form-item>
                            <el-form-item label="总学时">{{formModel.TotalPeriod||'无'}}</el-form-item>
                            <el-form-item label="总积分">{{formModel.TotalIntegral||'无'}}</el-form-item>
                        </el-col>
                    </el-row>
                    <page-title :showBackBtn='false'>
                        <div slot="def">
                            <tags :items="searchTypesData" v-model="listQuery.type">
                                <template v-for="t in searchTypesData" :slot="t.value">{{ t.label }}</template>
                            </tags>
                        </div>
                    </page-title>
                </el-form>
                <div style="height:356px;" class="__dynamicTabContentWrapper">
                    <div class="__dynamicTabWrapper">
                        <app-table ref="mainTable" :tab-columns="tabAllColumns" :tab-datas="dataSourceList" :loading="listLoading"
                            :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable"
                            :multable="false" layoutMode='simple' :isShowBtnsArea='false' :isShowConditionArea="false"
                            @sortChagned="handleSortChange">
                            <!-- 学时 -->
                            <template slot="Period" slot-scope="scope">+{{scope.row.Period}}</template>
                            <!-- 状态 -->
                            <template slot="State" slot-scope="scope">
                                <span class="item-status" :style="{backgroundColor: getStateObj(scope.row.State).bgColor,
                                color: getStateObj(scope.row.State).color}">
                                {{ getStateObj(scope.row.State).label }}
                                </span>
                            </template>

                            <!-- 考试类型 -->
                            <template slot="TestModeType" slot-scope="scope">
                                <span class="item-status" :style="{backgroundColor: getTestModeTypeObj(scope.row.TestModeType).bgColor,
                                color: getTestModeTypeObj(scope.row.TestModeType).color}">
                                {{ getTestModeTypeObj(scope.row.TestModeType).label }}
                                </span>
                            </template>
                            <!-- 是否必学 -->
                            <template slot="IsWillLearn" slot-scope="scope">
                                <span class="item-status" :style="{color: getIsWillLearnObj(scope.row.IsWillLearn).color}">
                                {{ getIsWillLearnObj(scope.row.IsWillLearn).label }}
                                </span>
                            </template>
                            <!-- 学习时间 -->
                            <template slot="LearningTime" slot-scope="scope">{{scope.row.LearningTime | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                            <!-- 考试成绩 -->
                            <template slot="ExamResults" slot-scope="scope">{{scope.row.ExamResults||'无'}}</template>
                            <!-- 是否及格 -->
                            <template slot="IsPass" slot-scope="scope">
                                <span class="item-status" :style="{color: getIsPassObj(scope.row.IsPass).color}">
                                {{ getIsPassObj(scope.row.IsPass).label }}
                                </span>
                            </template>
                            <!-- 积分 -->
                            <template slot="Integral" slot-scope="scope">{{((scope.row.Integral||scope.row.Integral==0)&&scope.row.Integral!='无')?'+'+scope.row.Integral:'无'}}</template>
                            <!-- 考试时间 -->
                            <template slot="ExamTime" slot-scope="scope">{{scope.row.ExamTime | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                            <!-- 用时 -->
                            <template slot="When" slot-scope="scope">{{scope.row.When||'0'}}分钟</template>
                        </app-table>
                    </div>
                    <!----------------------------------------- 分页 ------------------------------------------->
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange"
                    @size-change="handleSizeChange" />
                </div>
            </div>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose" text="关闭"></app-button>
      </template>
    </app-dialog>
  </div>
</template>
<script>
import indexPageMixin from "@/mixins/indexPage";
import * as StudyRecordApi from '@/api/knowledge/StudyRecord'
import { vars } from '../common/vars'
export default {
    name: "learning-records",
    components: {},
    mixins: [indexPageMixin],
    props: {
        checkedNode: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            stateTypes: vars.learningRecordsEnum.stateTypes,
            isPassTypes: vars.learningRecordsEnum.isPassTypes,
            isWillLearnTypes: vars.learningRecordsEnum.isWillLearnTypes,
            testModeTypeTypes: vars.learningRecordsEnum.testModeTypeTypes,
            searchTypesData: [
                { label: "学习记录", value: 1 },
                { label: "考试记录", value: 2 },
            ],
            listLoading: false,
            formModel: {
                EmployeeName: '', // 姓名
                StudyTrainNum: '', // 学习课程
                ExamPassNum: '', // 通过考试
                UsableIntegral: '', // 可用积分
                Level: '', // 等级
                TotalPeriod: '', // 总学时
                TotalIntegral: '', // 总积分
            },
            total: 0,
            listQuery: {
                PageIndex: 1,
                PageSize: 10,
                EmployeeId: '',
                type: 1,
            },
            tabAllColumns:[],
            // 学习记录 表格头
            tabColumns1: [
                {attr: {prop: "TrainName",label: "课程名称", showOverflowTooltip: true,}},
                {attr: {prop: "IsWillLearn",label: "是否必学",sortable: "custom"},slot: true},
                {attr: {prop: "TrainClassifyName",label: "课程分类", showOverflowTooltip: true,}},
                {attr: {prop: "Period",label: "学时", showOverflowTooltip: true,}},
                {attr: {prop: "State",label: "状态",sortable: "custom"},slot: true},
                {attr: {prop: "LearningTime",label: "学习时间",sortable: "custom"},slot: true},
            ],
            // 考试记录 表格头
            tabColumns2: [
                {attr: {prop: "ExaminationPaperName",label: "考卷名称", showOverflowTooltip: true,}},
                // {attr: {prop: "ExamResults",label: "考试成绩",sortable: "custom"},slot: true},
                {attr: {prop: "TestModeType",label: "考试类型",sortable: "custom"},slot: true},
                {attr: {prop: "IsPass",label: "是否及格",sortable: "custom"},slot: true},
                {attr: {prop: "Integral",label: "积分",sortable: "custom"},slot: true},
                {attr: {prop: "ExamTime",label: "考试时间",sortable: "custom"},slot: true},
                {attr: {prop: "When",label: "用时",sortable: "custom"},slot: true},
            ],
            dataSourceList: [],
        };
    },
    computed: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.setDetail();
                }
            },
            immediate: true
        },
        "listQuery.type": {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1
                    this.listQuery.PageSize = 10
                    this.getList();
                }
            },
            immediate: true
        },
    },
    created() {
    },
    mounted() { },
    methods: {
        getTestModeTypeObj(val) {
            return this.testModeTypeTypes.find(
                s => s.value == val
            ) || {};
        },
        getStateObj(val) {
            return this.stateTypes.find(
                s => s.value == val
            ) || {};
        },
        getIsPassObj(val) {
            return this.isPassTypes.find(
                s => s.value == val
            ) || {};
        },
        getIsWillLearnObj(val) {
            return this.isWillLearnTypes.find(
                s => s.value == val
            ) || {};
        },
        // 表格排序按钮搜索
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
        setDetail(){
            this.formModel = Object.assign({}, this.formModel, this.checkedNode);
            console.log(this.formModel)
            this.dataSourceList = []
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        /**获取详情 */
        getList() {
            let self = this, postData = JSON.parse(JSON.stringify(self.listQuery));
            if (!self.checkedNode || !self.checkedNode.EmployeeId) {
                return false;
            }
            postData.EmployeeId = self.checkedNode.EmployeeId;
            postData = self.assignSortObj(postData);
            self.listLoading = true;
            StudyRecordApi.getList(postData).then(res => {
                let typeName = self.listQuery.type == 1 ? 'StudyRecordDtoModelList' : 'ExamRecordDtoModelList';
                self.dataSourceList = res[typeName].Items;
                self.tabAllColumns = self[`tabColumns${self.listQuery.type}`]
                self.total = res[typeName].Total;
                self.listLoading = false;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapperBody{
    height: 590px;
}
</style>


