// import { debug } from "util";
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'

export default {
    props: {
        /**
         * 建议：不要轻易使用该属性
         * 专用于需要审批页面修改 pageTitle 属性的扩展
         * 目前：个人工作台——》我的审批——》对应的每一个审批“页面”的 pageTitle 都需要修改为 “审批类型” 列对应
         * 由于很多弹框都是共用的，所以从内部不好确认 pagetitle，所以改为外部确认
         */
        specialPageTitle: {
            type: String
        }
    },
    data() {
        return {
            levelData: {
                levelImg1: require("../assets/images/learningLevel_1.png"),
                levelImg2: require("../assets/images/learningLevel_2.png"),
                levelImg3: require("../assets/images/learningLevel_3.png"),
                levelImg4: require("../assets/images/learningLevel_4.png"),
                levelImg5: require("../assets/images/learningLevel_5.png"),
                levelImg6: require("../assets/images/learningLevel_6.png"),
                levelImg7: require("../assets/images/learningLevel_7.png"),
            },
            g_defavatar: require("../assets/images/avatar3.png"),
            listQuery: {
                // 查询条件
                PageIndex: 1,
                PageSize: 20
            },

            //vue-selecttree 组件未匹配到文字提示
            noResultsTextOfSelTree: '未匹配到', //tree-select 没有匹配到数据
            noOptionsTextOfSelTree: '没有数据', //tree-select 没有可选的options
            clearAllTextOfSelTree: '全部清空', 
            clearValueTextOfSelTree: '清空',

            targetMenu: null, //需要查找授权菜单对象

            //超级干线相关限制
            mainLine: {
                //100:（不做限制）——相关业务代码没有删除
                taskLevelLimit: 3, //超级干线任务层级最大支持 5 级别
            },
        };
    },
    computed: {
        ...mapGetters(['permission_routers']),
        //表格顶部需要的按钮
        topBtns() {
            return this._getBtnsByType([1, 3]);
        },
        //表格行内需要的按钮
        rowBtns() {
            return this._getBtnsByType([2, 3]);
        },
        showSidebar() {
            return !(this.$route.meta.showSidebar === false)
        },
    },
    methods: {
        //休眠，避免把代码写道 setTimeout 回调函数中
        async sleep(timer) {
            await new Promise(r => setTimeout(r, timer))
        },
        //判断两个时间是否完全相同（年月日时分秒）
        isSame(datetime1, datetime2) {
            let mainStartTime = dayjs(datetime1)
            let mainEndTime = dayjs(datetime2)
            return mainStartTime.isSame(mainEndTime)
        },
        //table 重置按钮
        resetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize
            };
        },
        //判断行内某个按钮是否存在
        rowBtnIsExists(domId) {
            return this._getBtnsByType([2, 3]).findIndex(b => b.DomId == domId) > -1;
        },
         //判断表格顶部某个按钮是否存在
        topBtnIsExists(domId) {
            return this._getBtnsByType([1, 3]).findIndex(b => b.DomId == domId) > -1;
        },
        //根据domId获取页面上按钮的文字
        getRowBtnText(domId) {
            let obj = this._getBtnsByType([2, 3]).find(b => b.DomId == domId)
            if (obj) {
                return obj.Name
            }
            return ''
        },
        _getBtnsByType(types) {
            if (!_.isArray(types)) return [];

            var route = this.$route;
            var elements = route.meta.elements || [];
            return elements
                .filter(b => types.indexOf(b.ButtonShowType) > -1)
                .sort((a, b) => {
                    return a.Sort - b.Sort; //升序
                });
        },
        /**
         * 自动生成表单验证规则：简化重复代码，统一管理（方便设置国际化）
         * 
         * @param {*} rules 传递过来的规则对象
         * 
         * rules 对象说明如下：
         * 
         * 例：
         * {
         *      Name: { fieldName: '账号', rules: [{ required: true }]},
         *      Phone: { fieldName: '手机', rules: [{ reg: regs.phone }]},
         * }
         * 
         * Name: 校验的表单属性
         * fieldName: '校验的表单属性的名称——用于多语言提示'
         * rules: 校验字段规则集合，目前支持：
         *      required: 必填项。注：必须写 required: true (否则字段前没法显示*)
         *      reg: 自定义验证规则，正则表达式对象（如：RegExp、/^1[3-9]\d{9}$/）
         */
        initRules(rules) {
            let old_rules = rules;
            let result_rules = {};
            for (const r in old_rules) {
                let fieldName = old_rules[r].fieldName || "";
                let rulesOfField = [];
                for (const i in old_rules[r].rules) {
                    let curRule = old_rules[r].rules[i];

                    curRule.label = fieldName

                    let errMsg = curRule.message || ''
                    if (!curRule.hasOwnProperty("message")) {
                        let ruleName = Object.keys(curRule)[0] //当前规则名称
                        switch (ruleName) {
                            case 'required':
                                errMsg = `${fieldName}不能为空`
                                break;
                            case 'reg':
                                errMsg = `${fieldName}格式不正确`
                                break;
                            case 'min':
                                errMsg = `${fieldName}不能少于${curRule.min}个字符`
                                break;
                            case 'max':
                                errMsg = `${fieldName}不能超过${curRule.max}个字符`
                                break;
                            case 'type':
                                if (curRule.type == 'number') {
                                    errMsg = `${fieldName}必须是数字`
                                }
                                break;
                        }

                        //待补充更多规则
                    }
                    
                    //如果校验规则不能为空，那么自动添加规则（不能全部是空格）
                    if(curRule.hasOwnProperty("required") && curRule.required){
                        curRule['pattern'] = '[^\x22]+'
                    }

                    if ((curRule.hasOwnProperty("required") && curRule.required)
                        || curRule.hasOwnProperty('max')
                        || curRule.hasOwnProperty('min')
                        || curRule.hasOwnProperty('type')) {
                        curRule["message"] = errMsg;
                    }

                    if (curRule.hasOwnProperty('required') && curRule.required) {
                        curRule["validator"] = (rule, value, callback) => {
                            if (!_.trim(value)) {
                                return callback(new Error(errMsg));
                            }
                            return callback();
                        };
                    }

                    if (curRule.hasOwnProperty("reg")) {
                        curRule["validator"] = (rule, value, callback) => {
                            if (value && !curRule.reg.test(value)) {
                                return callback(new Error(errMsg));
                            }
                            return callback();
                        };
                    }

                    if (!curRule.hasOwnProperty("trigger")) {
                        curRule["trigger"] = "blur";
                    }

                    rulesOfField[i] = curRule;
                }
                result_rules[r] = rulesOfField;
            }
            return result_rules
        },
        haveMenusPermission(menuPath) {
            return this.getMenu(menuPath) != null
        },
        haveBtnPermission(menuPath, btnId) {
            let menu = this.getMenu(menuPath)
            if(menu && menu.meta && menu.meta.elements && menu.meta.elements.find(s => s.DomId == btnId)) {
                return true
            }
            return false
        },
        //判断是否具有某个菜单权限
        getMenu(menuPath) {
            let targetPath = menuPath.toLowerCase()
            let allMenus = this.permission_routers
            let flat = false
            for(let i = 0; i < allMenus.length; i++) {
                let currMenu = allMenus[i]
                let currPath = currMenu.path.toLowerCase()
                if(currPath && currPath == targetPath && !flat) {
                    flat = true
                    return currMenu
                }

                if(currMenu.children && currMenu.children.length > 0 && !flat) {
                    for(let j = 0; j < currMenu.children.length; j++) {
                        currPath = currMenu.children[j].path.toLowerCase()
                        if(currPath && currPath == targetPath) {
                            flat = true
                            return currMenu.children[j]
                        }
                        
                        if(currMenu.children[j].children && currMenu.children[j].children.length > 0 && !flat) {
                            for(let k = 0; k < currMenu.children[j].children.length; k++) {
                                currPath = currMenu.children[j].children[k].path.toLowerCase()
                                if(currPath && currPath == targetPath) {
                                    flat = true
                                    return currMenu.children[j].children[k]
                                }
                            }
                        }
                    }
                }
            }
            return null
        },
        /**
         * 
         * @param {*} targetUrl 默认目标路径（必填）—— 一般用于子页面跳转（返回）至父页面
         * @param {*} returnUrl 跳转路径—— 一般用于跨业务的页面之间的跳转
         * @param {*} returnUrlUseType 跳转路径如何被使用（1：默认值，可以不传，直接使用 returnUrl；2：不能直接使用 returnUrl，只能被回传到上一个页面url）
         */
        getReturnUrl(targetUrl, returnUrl) {
            if (!returnUrl) { //如果没有returnUrl，直接返回父页面
            }else if(returnUrl && this.$route.query.returnUrlUseType == 2) {//如果有returnUrl，但是不能使用该值（等于没有），返回父页面，并且returnUrl带回父页面
                targetUrl += `?returnUrl=` + returnUrl
            }else if(returnUrl) { //如果有 returnUrl，并且可以直接使用，则跳转到 returnUrl 对应页面
                targetUrl = returnUrl
            }
            return targetUrl
        },

        // _getMenu(list, path) {
        //     let targetMenuTemp = this.targetMenu //最终返回的菜单
        //     let targetPath = path.toLowerCase() //需要查找的菜单
            
        //     if(!targetMenuTemp) {
        //         for(let i = 0; i < list.length; i++) {
        //             let currentMenu = list[i]
        //             let currentMenuPath = currentMenu.path.toLowerCase()

        //             if(currentMenuPath == targetPath) {
        //                 this.targetMenu = currentMenu
        //                 break;
        //             }

        //             if(!targetMenuTemp && currentMenu.children && currentMenu.children.length > 0) {
        //                 this._getMenu(currentMenu.children, path)
        //             }
        //         }
        //     }

        // },
    }
};
