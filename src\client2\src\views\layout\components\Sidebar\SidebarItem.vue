<template>
  <!-- <div v-if="!item.hidden&&item.children" class="menu-wrapper"> -->
  <div v-if="!item.hidden" class="menu-wrapper">
      <router-link v-if="!(item.children && item.children.length > 0)" :to="resolvePath(item.path)">
        <el-menu-item :index="resolvePath(item.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <div class="menu-inner-wrapper" :style="bgWidth"></div>
          <svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon"></svg-icon>
          <!-- <span style="padding-left: 4px;" v-if="item.meta&&item.meta.title" slot="title">{{item.meta.title}}</span> -->
          <menuTitle style="padding-left: 4px;" v-if="item.meta&&item.meta.title" slot="title" :meta="item.meta" :title="item.meta.title"></menuTitle>
        </el-menu-item>
      </router-link>

      <el-submenu v-else :index="item.name||item.path">
        <template slot="title">
          <!-- <router-link :to="resolvePath(item.path)" :key="item.name">
            <svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon"></svg-icon>
            <span v-if="item.meta&&item.meta.title">{{item.meta.title}}</span>
          </router-link> -->

          <router-link v-if="item.path" :to="resolvePath(item.path)" :key="item.name">
              <div class="menu-inner-wrapper" :style="bgWidth"></div>
              <svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon"></svg-icon>
              <!-- <span style="padding-left: 4px;" v-if="item.meta&&item.meta.title">{{item.meta.title}}</span> -->
              <menuTitle style="padding-left: 4px;" v-if="item.meta&&item.meta.title" :meta="item.meta" :title="item.meta.title"></menuTitle>
          </router-link>
          <template v-else>
            <div class="menu-inner-wrapper" :style="bgWidth"></div>
            <svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon"></svg-icon>
            <!-- <span style="padding-left: 4px;" v-if="item.meta&&item.meta.title">{{item.meta.title}}</span> -->
            <menuTitle style="padding-left: 4px;" v-if="item.meta&&item.meta.title" :meta="item.meta" :title="item.meta.title"></menuTitle>
          </template>
        </template>
        
        <template v-for="child in item.children" v-if="!child.hidden">
          <sidebar-item :is-nest="true" class="nest-menu" v-if="child.children&&child.children.length>0" :item="child" :key="child.path" :base-path="resolvePath(child.path)"></sidebar-item>
          <router-link v-else :to="resolvePath(child.path)" :key="child.name">
            <el-menu-item :index="resolvePath(child.path)">
              <div class="menu-inner-wrapper" :style="bgWidth"></div>
              <svg-icon v-if="child.meta&&child.meta.icon" :icon-class="child.meta.icon"></svg-icon>
              <!-- <span v-if="child.meta&&child.meta.title" slot="title">{{child.meta.title}}</span> -->
              <menuTitle v-if="child.meta&&child.meta.title" slot="title" :meta="child.meta" :title="child.meta.title"></menuTitle>
            </el-menu-item>
          </router-link>
        </template>
      </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import menuTitle from './menuTitle'
import _toConsumableArray from 'babel-runtime/helpers/toConsumableArray';
import { mapGetters } from "vuex";

export default {
  name: 'SidebarItem',
  components: {
    menuTitle,

  },
  computed: {
    ...mapGetters(["sidebar"]),
    bgWidth() {
      return {
        width: this.sidebar.opened ? '160px' : '160px' //45px
      }
    },
  },
  props: {
    // route配置json
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  methods: {
    hasOneShowingChildren(item, children) {
      if(children) {
        const showingChildren = children.filter(item => {
          return !item.hidden
        })
      }else{
        return false
      }
      
      //（修改bug）如果一级菜单下只有一个二级菜单，则隐藏一级菜单，只显示二级菜单
      // item.redirect == 'dashboard' 表示一级菜单为 Layout 路由
      if (showingChildren.length === 1 && item.redirect == 'dashboard') {
        return true
      }
      return false
    },
    resolvePath(...paths) {
      return path.resolve(this.basePath, ...paths)
    }
    // resolvePath() {
    //   for (var _len = arguments.length, paths = Array(_len), _key = 0; _key < _len; _key++) {
    //     paths[_key] = arguments[_key];
    //   }

    //   return path.resolve.apply(path, [this.basePath].concat(_toConsumableArray(paths)));
    // }
  }
}
</script>


<style lang="scss" scoped>



</style>