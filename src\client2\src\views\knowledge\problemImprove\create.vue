<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" style="padding-top: 0;" v-loading='loading'
                label-position="right" label-width="86px" class="wrapperMain">
                    <el-row class="wrapperBox">
                        <el-form-item label="相关产品" prop="ProductSpecificationImprovementId">
                            <el-button type="text" @click="selectProduct">选择产品</el-button>
                            <span style="padding-left: 10px;">{{formData.ProductSpecificationImprovementName}}</span>
                        </el-form-item>
                        <el-form-item label="问题标题" prop="Title">
                            <el-input v-if="editable" :disabled="!editable" maxlength="100" type="text" v-model.trim="formData.Title"></el-input>
                            <div v-else>{{formData.Title}}</div>
                        </el-form-item>
                        <el-form-item label="问题描述" prop="Describe">
                            <editor-bar :value="formData.Describe" @edit="formData.Describe = arguments[0]"></editor-bar>
                        </el-form-item>
                        <el-form-item label="发起人" prop="SponsorEmployeeList">
                            <emp-selector v-if="editable" :readonly="!editable" :beforeConfirm='handleFinalBeforeConfirm' :showType="2"
                            :multiple="true" :list="formData.SponsorEmployeeList" @change="handleChangeManager"></emp-selector>
                            <div v-else>{{formData.SponsorEmployeeList.map(s=>s.Name).toString()}}</div>
                        </el-form-item>
                        <el-form-item label="可见范围" prop="ViewRange">
                            <div class="cl">
                                <el-select :disabled="!editable" class="fl" @change="() => {formData.ViewRangeValues = '';formData.ViewRangeStrList=[]}" style="margin-right:10px;" v-model="formData.ViewRange">
                                    <el-option v-for="item in ViewRangeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <div class="fl" v-if="formData.ViewRange == 2">
                                    <el-button :disabled="!editable" type="text" @click="handleShowTree">选择部门</el-button>
                                </div>
                                <emp-selector
                                    class="fl"
                                    style="width:300px;"
                                    v-if="formData.ViewRange == 3"
                                    :readonly="!editable"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :beforeConfirm='handleBeforeConfirm'
                                    :list="formData.ViewRangeStrList"
                                    @change="handleViewRange"
                                ></emp-selector>
                            </div>
                            <ul v-if="formData.ViewRange == 2 && formData.ViewRangeStrList&&formData.ViewRangeStrList.length>0" class="dUl">
                                <li class="omit" :title="dn.DepartmentName" v-for="(dn,dnI) in formData.ViewRangeStrList" :key="dnI">{{dn.DepartmentName}}</li>
                            </ul>
                        </el-form-item>
                        <el-form-item label="相关附件">
                            <app-uploader accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList"
                            :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 关闭 -->
                <app-button v-if="dialogStatus == 'detail'" @click="handleClose" text="关闭" type></app-button>
                <!-- 取消 -->
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>

        <!-- 选择部门弹窗 -->
        <v-tree
            v-if="dialogTreeVisible"
            @saveSuccess="handleTreeSaveSuccess"
            @closeDialog="handleTreeCloseDialog"
            :dialogFormVisible="dialogTreeVisible"
            :checkedList='checkedList'>
        </v-tree>

        <!-- 选择产品 -->
        <product-model 
            v-if="dialogProductModelVisible"
            @saveSuccess="handleProductModelSaveSuccess"
            @closeDialog="handleProductModelCloseDialog"
            :dialogFormVisible="dialogProductModelVisible"
            :ids='checkedProductList' />
    </div>
</template>

<script>
import EditorBar from "@/components/QuillEditor/index.vue";
import productModel from "@/views/knowledge/problemImprove/productModel.vue";

import * as productQuestionImprovementApi from '@/api/knowledge/productQuestionImprovement.js'

import empSelector from "@/views/common/empSelector";
import vTree from "@/views/knowledge/train/tree";
import { ViewRangeEnum } from "./enum.js";
export default {
    name: "problem-improve-create",
    directives: {},
    components: {
        empSelector,
        EditorBar,
        vTree,
        productModel
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return `创建问题`;
            }else if(this.dialogStatus == 'update'){
                return `编辑问题`;
            }
            return "问题详情";
        },
    },
    filters: {
        // SurveyTypeFilter(val) {
        //     let obj = SurveyTypeEnum.find(
        //         s => s.value == val
        //     );
        //     if (obj) {
        //         return obj.label;
        //     }
        //     return "无";
        // }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        id: {
            type: String,
            default: "",
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            ViewRangeEnum,


            /** 选择部门 */
            depKeyName: '',
            depValidName: '',
            checkedList:[],
            dialogTreeVisible:false,


            disabledBtn: false,
            loading: false,
            rules: {
                Title: {fieldName: "问题标题",rules: [{ required: true }]},
                SponsorEmployeeList: {fieldName: "发起人",rules: [{ required: true }]},
                Describe: {fieldName: "问题描述",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                Id: '',
                ProductSpecificationImprovementId: '', // 相关产品 型号 id
                ProductSpecificationImprovementName: '', // 相关产品 型号 name
                Title: '',// 问题标题
                Describe: '',// 问题描述
                SponsorEmployees: '',// 发起人员名称
                SponsorEmployeeIdList: [],//发起人id集合
                SponsorEmployeeList: [],//发起人集合

                AttachmentList: [],// 相关附件

                ViewRange: 1,
                ViewRangeValues: '',
                ViewRangeStrList: [],
            },

            // 选择产品
            checkedProductList: [],
            dialogProductModelVisible: false,
        };
    },
    methods: {
        // 选择产品
        selectProduct(){
            this.dialogProductModelVisible = true
            this.$refs.formData.validateField('ProductSpecificationImprovementId');
        },
        // 选择产品 确定
        handleProductModelSaveSuccess(arr){
            if(arr&&arr.length==1){
                this.checkedProductList = JSON.parse(JSON.stringify(arr));
                this.formData.ProductSpecificationImprovementId = arr[0].Id
                this.formData.ProductSpecificationImprovementName = `${arr[0].ProductImprovementName}/${arr[0].Specification}`
                this.dialogProductModelVisible = false
            } else if(arr&&arr.length>1) {
                this.$message({
                    message: "只能选择一个",
                    type: "error"
                });
            } else {
                this.$message({
                    message: "至少选择一个",
                    type: "error"
                });
            }
        },
        // 选择产品  关闭
        handleProductModelCloseDialog(){
            this.dialogProductModelVisible = false
        },
        // 关闭选择部门弹窗
        handleTreeCloseDialog(){
            this.dialogTreeVisible=false;
        },
        // 选择部门弹窗 确定
        handleTreeSaveSuccess(d){
            this.formData.ViewRangeStrList=[];
            this.checkedList=[];
            if(d.length>0){
                d.forEach(v => {
                    this.formData.ViewRangeStrList.push({
                        DepartmentId: v.Id,
                        DepartmentName: v.ParentName,
                    });
                })
                this.checkedList = this.formData.ViewRangeStrList.map(s=>s.DepartmentId)
                this.$refs.formData.validateField('ViewRangeStrList');
            }
            this.dialogTreeVisible=false;
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1000) {
            this.$message({
                message: '不得超过1000个',
                type: 'error'
            })
            return false
            }
            return true
        },
        // 显示选择部门弹窗
        handleShowTree(){
            let list = this.formData.ViewRangeStrList
            if(list) {
                this.checkedList = list.map(s => s.DepartmentId) || []
            }
            this.dialogTreeVisible=true;
        },
        // 可见范围 选择人员/选择部门
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.ViewRangeStrList = users;
            } else {
                this.formData.ViewRangeStrList = [];
            }
            this.$refs["formData"].validateField('ViewRangeStrList');
        },
        // 附件 上传 赋值
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        // 发起人 选择校验
        handleFinalBeforeConfirm(users) {
            if(users && users.length > 10) {
                this.$message({
                    message: '发起人不得超过10人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 发起人 选择确定
        handleChangeManager(users) {
            if (users && users.length > 0) {
                this.formData.SponsorEmployeeList = users;
            } else {
                this.formData.SponsorEmployeeList = [];
            }
            this.$refs["formData"].validateField("SponsorEmployeeList");
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    postData.SponsorEmployeeIdList = postData.SponsorEmployeeList.map(s=>s.EmployeeId);
                    delete postData.SponsorEmployeeList
                    postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    delete postData.AttachmentList
                    if(postData.ViewRange==2) {
                        postData.ViewRangeValues = postData.ViewRangeStrList.length>0 ? postData.ViewRangeStrList.map(s => s.DepartmentId).toString() : '';
                    }
                    if(postData.ViewRange==3) {
                        postData.ViewRangeValues = postData.ViewRangeStrList.length>0 ? postData.ViewRangeStrList.map(s => s.EmployeeId).toString() : '';
                    }
                    delete postData.ViewRangeStrList
                    delete postData.ViewRangeDepartmentList
                    delete postData.ViewRangeEmployeeList
                    
                    // console.log(postData)
                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = productQuestionImprovementApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = productQuestionImprovementApi.edit(postData);
                    }

                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 查询 基本信息
        getDetail() {
            this.isOneLoad = true;
            this.loading = true
            productQuestionImprovementApi.detail({ id: this.id }).then(res => {
                if(res.ViewRange===2){
                    res.ViewRangeStrList = res.ViewRangeDepartmentList
                }
                if(res.ViewRange===3){
                    res.ViewRangeStrList = res.ViewRangeEmployeeList
                }
                this.formData = res;
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
</style>
<style lang='scss' scoped>
.wrapperBox{
    padding-top: 10px;
    padding-right: 20px;
    &_main{
        max-height: 420px;
        overflow-y: auto;
    }
}
.el-card{
    margin-bottom: 15px;
}
.omit{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tips_title{
    float: left;
    max-width: 100%;
}
</style>