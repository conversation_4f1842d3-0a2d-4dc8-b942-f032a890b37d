<!-- 付款申请弹窗 -->
<template>
  <app-dialog
    ref="appDialogRef"
    :width="1000"
    className="clear-padding"
    :title="title"
    v-bind="$attrs"
    v-on="$listeners"
    :beforeClose="beforeClose"
  >
    <div slot="body" class="body_wrapper" v-loading="loading">
      <div class="main_container">
        <el-form
          :model="formData.PaymentObj"
          ref="formRef"
          :rules="rules"
          label-width="110px"
          style="padding-top: 0"
          class="form_container"
        >
          <div class="left_container">
            <el-form-item
              label="撤销原因"
              prop="RevocationCause"
              v-if="
                dialogStatus == 'revoke' ||
                dialogStatus == 'revokeApproval' ||
                (dialogStatus == 'detail' && formData.RevocationStatus > 0)
              "
            >
              <el-input
                :disabled="dialogStatus !== 'revoke'"
                type="textarea"
                :rows="4"
                maxlength="500"
                v-model="formData.PaymentObj.RevocationCause"
              />
            </el-form-item>
            <el-form-item label="收款方联系人" prop="PayeeContact">
              <el-input
                v-model="formData.PaymentObj.PayeeContact"
                placeholder="请输入收款方联系人"
                :disabled="disabled"
                clearable
                :maxlength="30"
              />
            </el-form-item>
            <el-form-item label="联系方式" prop="PayeeContactNumber">
              <el-input
                v-model="formData.PaymentObj.PayeeContactNumber"
                placeholder="请输入联系方式"
                :disabled="disabled"
                clearable
                type="number"
                :maxlength="30"
              />
            </el-form-item>
            <el-form-item label="付款内容" prop="PaymentContent">
              <el-input
                v-model="formData.PaymentObj.PaymentContent"
                placeholder="请输入付款内容"
                :disabled="disabled"
                clearable
                :maxlength="100"
              />
            </el-form-item>
            <el-form-item label="是否签订合同" prop="ContractSigned">
              <el-radio
                v-model="formData.PaymentObj.ContractSigned"
                :label="true"
                :disabled="disabled"
              >
                是
              </el-radio>
              <el-radio
                v-model="formData.PaymentObj.ContractSigned"
                :label="false"
                :disabled="disabled"
              >
                否
              </el-radio>
            </el-form-item>
            <el-form-item label="附件" prop="AttachmentList">
              <AttachmentList
                v-model="formData.PaymentObj.AttachmentList"
                :disabled="disabled"
                accept="pdf"
              />
            </el-form-item>
            <el-divider />
            <p>开票情况</p>
            <el-form-item label="开票情况" prop="InvoiceStatus">
              <el-radio v-model="formData.PaymentObj.InvoiceStatus" :disabled="disabled" :label="1">
                未开票
              </el-radio>
              <el-radio v-model="formData.PaymentObj.InvoiceStatus" :disabled="disabled" :label="2">
                已开票
              </el-radio>
            </el-form-item>
            <el-form-item label="发票" prop="InvoiceList">
              <InvoiceList v-model="formData.PaymentObj.InvoiceList" :disabled="disabled" />
            </el-form-item>
            <el-divider />
            <p>付款方式</p>
            <el-form-item label="付款方式" prop="PaymentMethod">
              <el-radio
                v-model="formData.PaymentObj.PaymentMethod"
                v-for="(t, i) in PAYMENT_METHOD"
                :label="t.value"
                :key="i"
                :disabled="disabled"
              >
                {{ t.label }}
              </el-radio>
            </el-form-item>
            <el-divider />
            <p>账号信息</p>
            <el-form-item label="收款单位" prop="PayeeCompany">
              <el-autocomplete
                v-model="formData.PaymentObj.PayeeCompany"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入收款人/单位名称"
                style="width: 100%"
                clearable
                :disabled="disabled"
                value-key="PayeeCompany"
                :maxlength="30"
                :trigger-on-focus="false"
                @select="handleSelect"
              />
            </el-form-item>
            <el-form-item label="开户银行" prop="BankName">
              <el-input
                v-model="formData.PaymentObj.BankName"
                placeholder="请输入开户银行"
                :disabled="disabled"
                clearable
                :maxlength="30"
              />
            </el-form-item>
            <el-form-item label="银行账号" prop="BankAccountNumber">
              <el-input
                v-model="formData.PaymentObj.BankAccountNumber"
                placeholder="请输入银行账号"
                :disabled="disabled"
                clearable
                type="number"
              />
            </el-form-item>
            <el-divider />
            <p>合同信息</p>
            <el-form-item label="合同金额" prop="ContractAmount">
              <el-input
                v-model="formData.PaymentObj.ContractAmount"
                :disabled="disabled"
                placeholder="请输入合同金额"
                type="text"
                v-thousands="true"
                :maxlength="15"
                clearable
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
            <el-form-item label="已付金额" prop="AmountPaid">
              <el-input
                v-model="formData.PaymentObj.AmountPaid"
                :disabled="disabled"
                placeholder="请输入已付金额"
                type="text"
                v-thousands="true"
                :maxlength="15"
                clearable
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
            <el-form-item label="本次付款金额" prop="CurrentPaymentAmoun">
              <el-input
                v-model="formData.PaymentObj.CurrentPaymentAmoun"
                :disabled="disabled"
                placeholder="请输入本次付款金额"
                type="text"
                v-thousands="true"
                :maxlength="15"
                clearable
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
            <el-form-item label="人民币(大写)">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">
                  {{ convertToChinese(formData.PaymentObj.CurrentPaymentAmoun) }}
                </div>
              </div>
            </el-form-item>
            <el-form-item label="备注" prop="Remarks">
              <el-input
                v-model="formData.PaymentObj.Remarks"
                placeholder="请输入备注"
                type="textarea"
                :rows="3"
                :disabled="disabled"
                :maxlength="500"
              />
            </el-form-item>
          </div>
          <div class="right_container">
            <!-- 关联研发项目 -->
            <AddProject
              :companyId="formData.PaymentObj.KingdeeDepartmentNumber"
              v-model="formData.PaymentObj.KingdeeProjectList"
              :disabled="disabled"
            />
            <el-divider />
            <el-form-item label="申请人">
              <empSelector
                :readonly="true"
                :showType="2"
                :multiple="true"
                placeholder="输入工号/姓名"
                :isAutocomplete="true"
                :collapseTags="false"
                :list="formData.PaymentObj.SubmitEmployeeList"
                :beforeConfirm="() => true"
              />
            </el-form-item>
            <el-form-item label="联系方式" prop="ApplicantContact">
              <el-input
                v-model="formData.PaymentObj.ApplicantContact"
                placeholder="请输入联系方式"
                type="number"
                clearable
                :maxlength="30"
                :disabled="disabled"
              />
            </el-form-item>
            <el-form-item label="公司名称" prop="KingdeeDepartmentNumber">
              <CompanySelect
                v-model="formData.PaymentObj.KingdeeDepartmentNumber"
                :disabled="disabled"
                @change="handleChangeCompany"
              />
            </el-form-item>
            <el-form-item label="部门" prop="DepartmentId">
              <DepartmentSelect
                :value="formData.PaymentObj.DepartmentId"
                :disabled="disabled"
                @change="changeDepartment"
              />
            </el-form-item>
            <el-form-item label="填报日期" prop="FBillDate">
              <el-date-picker
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="formData.PaymentObj.FBillDate"
                type="date"
                placeholder="请选择填报日期"
                :disabled="disabled"
                :picker-options="pickerOptions"
                :clearable="false"
              />
            </el-form-item>
            <el-form-item label="票据及附件数">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ fileTotal }}</div>
              </div>
            </el-form-item>
            <el-form-item label="单据编号" v-if="disabled">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ formData.PaymentObj.FBillNo }}</div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 审批区域 -->
      <div class="body_footer">
        <approvalPanel
          v-if="!disabled"
          :editable="isEditApprove"
          ref="approvalPanel"
          :approvalPanelObj="formData.Approval"
        />
        <approvalDetail
          :isOnlyViewDetail="isOnlyViewDetail || !isApprovalor"
          v-if="['approval', 'detail', 'revoke', 'revokeApproval'].includes(dialogStatus)"
          ref="approvalDetail"
          :dialogStatus="dialogStatusTrans"
          :approvalObj="formData.Approval"
        />
      </div>
      <PaymentReqDialog
        v-if="showPaymentReqDialog"
        :dialogFormVisible="showPaymentReqDialog"
        :tempObj="tempObj_"
        dialogStatus="create"
        :id="id"
        :approvalId="approvalId"
        :processId="tempObj_.HRApprovalProcessId"
        @closeDialog="closeSubDialog"
      />
    </div>
    <div slot="footer">
      <el-button
        v-if="['revokeApproval', 'detail'].includes(dialogStatus) && isCurrentUser"
        @click="handleReferenceCreate"
      >
        引用创建
      </el-button>
      <app-button @click="beforeClose" :buttonType="2" :loading="btnLoading" />
      <el-button
        v-if="['create', 'editDraft'].includes(dialogStatus)"
        @click="handleSaveDraft"
        :loading="btnLoading"
      >
        暂存草稿
      </el-button>
      <app-button
        @click="createData"
        :buttonType="1"
        v-if="dialogStatus != 'approval' && (!disabled || dialogStatus == 'revoke')"
        :loading="btnLoading"
        style="margin-left: 10px"
      />
      <el-button
        @click="handleApproval"
        type="primary"
        :loading="btnLoading"
        v-if="
          (dialogStatus == 'approval' || (dialogStatus == 'revokeApproval' && !isOnlyViewDetail)) &&
          isApprovalor
        "
        style="margin-left: 10px"
      >
        审批
      </el-button>
    </div>
  </app-dialog>
</template>

<script>
import dayjs from "dayjs";
import * as approvalManagement from "@/api/approvalManagement.js";
import approvalMixins from "@/mixins/approvalPatch.js";
import { getInterlinkApi } from "@/api/finance.js";
import { getUserInfo } from "@/utils/auth";
import { PAYMENT_METHOD } from "@/views/financialManagement/reimbursement/vars.js";
import { debounce } from "lodash";
import expenseFormDialogMixins from "./expenseFormDialogMixins.js";

export default {
  name: "paymentReqDialog",
  mixins: [approvalMixins, expenseFormDialogMixins],
  components: {
    PaymentReqDialog: () => import("./PaymentReqDialog"),
  },
  data() {
    return {
      PAYMENT_METHOD,
      // 表单parcelKey对应后端详情数据的包裹对象mixins使用
      parcelKey: "PaymentObj",
      formData: {
        PaymentObj: {
          FBillNo: "",
          FBillDate: null,
          KingdeeDepartmentNumber: null,
          DepartmentId: null,
          PayeeContact: "",
          ApplicantContact: "",
          PayeeContactNumber: "",
          PaymentContent: "",
          ContractSigned: false,
          InvoiceStatus: 1,
          PaymentMethod: 1,
          PayeeCompany: "",
          BankName: "",
          BankAccountNumber: "",
          ContractAmount: "",
          AmountPaid: "",
          CurrentPaymentAmoun: "",
          Remarks: "",
          RevocationCause: "",
          AttachmentList: [],
          // 申请人
          SubmitEmployeeList: [],
          // 项目
          KingdeeProjectList: [],
          // 发票
          InvoiceList: [],
        },
        Type: 11, //付款申请
        ApprovalStatus: 0, //审批状态
        HrApprovalProcessId: null,
        //审批信息
        Approval: {
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          ApprovalOperatorEmployeeList: [], //已审批人员
          NoApprovalEmployeeList: [], //未审批人员
          CCEmployeeList: [], //抄送人
          ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
          ApprovalState: 1, //1: 进行中; 2: 已完成
          ApprovalResult: 1, //1: 通过； 2：不通过
        },
      },
      showPaymentReqDialog: false,
      tempObj_: null,
    };
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.closeDialog();
          return;
        }
        if (this.dialogStatus === "create") {
          if (this.tempObj) {
            this.formData = Object.assign(this.formData, this.$_.cloneDeep(this.tempObj));
          } else {
            this.formData.PaymentObj.FBillDate = dayjs().format("YYYY-MM-DD");
            this.defaultInfo();
          }
        } else {
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  created() {},
  computed: {
    title() {
      const titleMap = {
        create: "付款申请",
        detail: "付款申请详情",
        approval: "付款申请审批",
        revoke: "(撤销)付款申请",
        revokeApproval: "(撤销)付款申请",
      };
      return titleMap[this.dialogStatus] || "付款申请";
    },
    pickerOptions() {
      return {
        disabledDate: time => {
          return dayjs(time).isAfter(dayjs(), "day");
        },
      };
    },
    // 票据及附件数
    fileTotal() {
      const obj = this.formData.PaymentObj;
      const invoiceTotal = obj?.InvoiceList?.length || 0;
      const attachmentTotal = obj?.AttachmentList?.length || 0;
      return invoiceTotal + attachmentTotal;
    },
    rules() {
      const rules = {
        PayeeContact: [{ required: true, message: "请输入收款方联系人", trigger: "blur" }],
        PayeeContactNumber: [{ required: true, message: "请输入联系方式", trigger: "blur" }],
        PaymentContent: [{ required: true, message: "请输入付款内容", trigger: "blur" }],
        AttachmentList: [
          {
            required: this.formData.PaymentObj.ContractSigned,
            message: "请选择附件",
            trigger: "change",
          },
        ],
        ApplicantContact: [{ required: true, message: "请输入联系方式", trigger: "blur" }],
        KingdeeDepartmentNumber: [{ required: true, message: "请选择公司名称", trigger: "change" }],
        FBillDate: [{ required: true, message: "请选择填报日期", trigger: "change" }],
        DepartmentId: [{ required: true, message: "请选择部门", trigger: "change" }],
        PayeeCompany: [{ required: true, message: "请输入收款人/单位名称", trigger: "blur" }],
        BankName: [{ required: true, message: "请输入开户银行", trigger: "blur" }],
        BankAccountNumber: [
          { required: true, message: "请输入银行账号", trigger: "blur" },
          { max: 25, message: "银行账号格式不正确", trigger: "blur" },
        ],
        CurrentPaymentAmoun: [{ required: true, message: "请输入本次付款金额", trigger: "blur" }],
        InvoiceList: [
          {
            required: this.formData.PaymentObj.InvoiceStatus === 2,
            message: "请上传发票",
            trigger: "change",
          },
        ],
        RevocationCause: [{ required: true, message: "请输入撤销原因", trigger: "blur" }],
      };
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      });
      return rules;
    },
  },
  methods: {
    // 默认填入用户对应的信息
    defaultInfo() {
      const userInfo = getUserInfo();
      this.formData.PaymentObj.SubmitEmployeeList.push({
        Name: userInfo.empName,
        Number: userInfo.empNumber,
        EmployeeId: userInfo.employeeid,
      });
      this.changeDepartment(userInfo.deptId);
      this.formData.PaymentObj.ApplicantContact = userInfo.phone;
    },
    /**
     * 创建报销单
     * @param temporize true:保存草稿 false:发起审批
     */
    createRequest(temporize = false) {
      this.formData.Approval = this.$refs.approvalPanel.getData(); //审批层区块

      const form = this.$_.cloneDeep(this.formData);
      form.Approval.ApprovalEmployeeIdList = form.Approval.ApprovalEmployeeList.map(s =>
        s.map(e => e.EmployeeId)
      );
      form.Approval.CCEmployeeIdList = form.Approval.CCEmployeeList.map(s => s.EmployeeId);

      const params = {
        ...form,
        Temporize: temporize,
        HrApprovalProcessId: this.id,
        PaymentObj: {
          ...form.PaymentObj,
          SubmitEmployeeIdList: form.PaymentObj.SubmitEmployeeList.map(s => s.EmployeeId),
        },
      };
      params.PaymentObj.AttachmentIdList = params.PaymentObj.AttachmentList.map(s => s.Id);

      delete params.PaymentObj.AttachmentList;
      delete params.PaymentObj.SubmitEmployeeList;

      let reqApi;
      if (this.dialogStatus == "editDraft") {
        // 草稿编辑
        reqApi = approvalManagement.temporizeApi;
      } else {
        // 普通创建和创建时暂存
        reqApi = approvalManagement.infoAdd;
      }
      this.btnLoading = true;
      reqApi(params)
        .then(res => {
          this.$emit("reload");
          this.closeDialog();
          if (temporize) {
            this.$message.success("暂存成功");
          } else {
            this.$confirm(
              "审批流程完成后，请及时将对应纸质票据（如发票、合同、收据等）提交至财务处",
              "提示",
              {
                confirmButtonText: "确定",
                showCancelButton: false,
                type: "success",
              }
            );
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 搜索收款单位
    querySearchAsync: debounce(function (queryString, cb) {
      queryString = queryString.trim();

      if (!queryString) {
        cb([]);
        return;
      }
      getInterlinkApi({
        keywords: queryString,
      }).then(res => {
        cb(res);
      });
    }, 300),
    // 收款单位选择
    handleSelect(item) {
      this.formData.PaymentObj.PayeeCompany = item.PayeeCompany;
      this.formData.PaymentObj.BankName = item.BankName;
      this.formData.PaymentObj.BankAccountNumber = item.BankAccountNumber;
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate(["PayeeCompany", "BankName", "BankAccountNumber"]);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.body_wrapper {
  height: 75vh;
  display: flex;
  flex-direction: column;
  .main_container {
    flex: 1;
    width: 100%;
    .form_container {
      width: 100%;
      height: 100%;
      display: flex;
      .left_container {
        padding: 10px;
        width: 65%;
        border-right: 1px solid $border-color-light;
      }
      .right_container {
        width: 35%;
        padding: 10px;
      }
    }
  }
  .body_footer {
    flex-shrink: 0;
    border-top: 1px solid $border-color-light;
  }
}
/deep/.el-divider {
  background-color: $border-color-light;
  margin: 10px 0;
}

/deep/input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  &[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
