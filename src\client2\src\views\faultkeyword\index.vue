<template>
    <div class="app-container">
      <div class="bg-white __dynamicTabContentWrapper">
        <!-- <page-title title="故障关键字" :subTitle="['故障现象关键字的维护管理页面']"></page-title> -->

        <div class="__dynamicTabWrapper">
          <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns='tabAuthColumns'
            :isShowAllColumn='isShowAllColumn' :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :layoutMode='layoutMode' :isShowBtnsArea='false'
            :isShowOpatColumn='rowBtns.length > 0' :startOfTable='startOfTable'>
            <!-- 表格查询条件区域 -->
            <template slot="conditionArea">
              <app-table-form :label-width="'100px'" :items='tableSearchItems' :layoutMode='layoutMode'
                @onSearch='handleFilter' @onReset='handleResetSearch'>
                <template slot="Name">
                    <el-input style="width: 100%;" 
                        placeholder="搜索故障关键字..."
                        @clear='handleFilter'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                handleFilter()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.Name"
                    ></el-input>
                </template>
                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
                </template>
              </app-table-form>
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type='1'>
              </app-table-row-button>
              <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleUpdate(scope.row, 'detail')"
                :type='2'></app-table-row-button>
              <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type='3'>
              </app-table-row-button>
            </template>
          </app-table>
        </div>

        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange" @size-change="handleSizeChange" />
      </div>

      <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible" :close-on-click-modal='false' :append-to-body='true'>
        <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="100px">
          <el-row>
            <el-col>
              <el-form-item :label="'故障关键字'" prop="FaultKeyWordName">
                <el-input v-model="temp.FaultKeyWordName" :disabled="editable" maxlength="50"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
          <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
  import * as faultkeyword from "@/api/faultkeyword";

  // import waves from '@/directive/waves' // 水波纹指令
  // import Sticky from '@/components/Sticky'

  import elDragDialog from "@/directive/el-dragDialog";
  import indexPageMixin from "@/mixins/indexPage";
  export default {
    name: "position",
    components: {
      // Sticky,
    },
    directives: {
      // waves,
      elDragDialog
    },
    mixins: [indexPageMixin],
    data() {
      return {
            layoutMode: 'simple',
        normalizer(node) {
          // treeselect定义字段
          return {
            label: node.label,
            id: node.Id,
            children: node.children
          };
        },
        multipleSelection: [],
        tableSearchItems: [{ prop: "Name", label: "故障关键字", mainCondition: true }],
        tabColumns: [
          {
            attr: { prop: "FaultKeyWordName", label: "故障关键字" }
          }
        ],
        tabDatas: [],
        listLoading: false,
        postLoading: false,
        listQuery: {
          // 查询条件
          Name: ""
        },
        total: 0,
        textMap: {
          update: "编辑",
          create: "添加"
        },
        dialogFormVisible: false,
        rules: {
          FaultKeyWordName: { fieldName: "故障关键字", rules: [{ required: true }] }
        },
        temp: {
          FaultKeyWordName: ""
        }
      };
    },
    created() {
      this.rules = this.initRules(this.rules);
      this.getList();
    },
    mounted() { },
    methods: {
      handleResetSearch() {
        this.listQuery.PageIndex = this.listQuery.PageIndex;
        this.listQuery.PageSize = this.listQuery.PageSize;
        this.listQuery.Name = ''
        this.getList()
      },
      resetTemp() {
        this.temp = {
          FaultKeyWordName: ""
        };
      },
      rowSelectionChanged(rows) {
        this.multipleSelection = rows;
      },
      onBtnClicked: function (domId) {
        // console.log('you click:' + domId)
        switch (domId) {
          case "btnAdd":
            this.handleCreate();
            break;
          case "btnEdit":
            if (this.multipleSelection.length !== 1) {
              this.$message({
                message: "只能选中一个进行编辑",
                type: "error"
              });
              return;
            }
            this.handleUpdate(this.multipleSelection[0]);
            break;
          case "btnDel":
            if (this.multipleSelection.length < 1) {
              this.$message({
                message: "至少删除一个",
                type: "error"
              });
              return;
            }
            this.handleDelete(this.multipleSelection);
            break;
          case "btnDetail":
            if (this.multipleSelection.length !== 1) {
              this.$message({
                message: "只能选中一个进行查看",
                type: "error"
              });
              return;
            }
            this.handleUpdate(this.multipleSelection[0], "detail");
            break;
          default:
            break;
        }
      },
      getList() {
        this.listLoading = true;
        faultkeyword.getList(this.listQuery).then(response => {
          this.tabDatas = response.Items;
          this.total = response.Total;
          this.listLoading = false;
        });
      },
      handleFilter() {
        this.listQuery.PageIndex = 1;
        this.getList();
      },
      handleSizeChange(val) {
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      handleCurrentChange(val) {
        this.listQuery.PageIndex = val.page;
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      handleCreate() {
        // 弹出添加框
        this.resetTemp();
        this.dialogStatus = "create";
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
      },
      createData() {
        // 保存提交
        let self = this;
        self.postLoading = true
        this.$refs["dataForm"].validate(valid => {
          if(!valid) {
            self.postLoading = false
          }
          if (valid) {
            let formData = JSON.parse(JSON.stringify(self.temp));
            if (self.dialogStatus == "create") {
              delete formData.PositionId;
            }

            let res = null;
            if (self.dialogStatus == "create") {
              res = faultkeyword.add(formData);
            } else if (self.dialogStatus == "update") {
              res = faultkeyword.edit(formData);
            }
            if (res) {
              res.then(response => {
                self.postLoading = false
                self.dialogFormVisible = false;
                self.$notify({
                  title: "成功",
                  message: "创建成功",
                  type: "success",
                  duration: 2000
                });

                this.getList();
              }).catch(err => {
                self.postLoading = false
              });
            }
          }
        });
      },
      handleUpdate(row, optType = "update") {
        // 弹出编辑框
        this.temp = Object.assign({}, row); // copy obj
        this.dialogStatus = optType;
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
      },
      handleDelete(rows) {
        // 多行删除
        let ids = [];
        if (_.isArray(rows)) {
          ids = rows.map(u => u.FaultKeyWordId);
        } else {
          ids.push(rows.FaultKeyWordId);
        }

        this.$confirm("是否确认删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          faultkeyword.del(ids).then(() => {
            this.$notify({
              title: "成功",
              message: "删除成功",
              type: "success",
              duration: 2000
            });
            this.getList();
          });
        });
      },

      hadnleChangeOilfield() {
        this.$refs.dataForm.validateField("OrgId");
      },
      formatterDate(row, column) {
        let f = this.$options.filters["dateFilter"];
        return f(row.CreateDateTime, "YYYY-MM-DD HH:mm:ss");
      },
      formatterDate2(row, column) {
        let f = this.$options.filters["dateFilter"];
        return f(row.LastUpdateDateTime, "YYYY-MM-DD HH:mm:ss");
      }
    }
  };
</script>

<style scoped>
  .sel-ipt,
  .dat-ipt {
    width: 100%;
  }
</style>
