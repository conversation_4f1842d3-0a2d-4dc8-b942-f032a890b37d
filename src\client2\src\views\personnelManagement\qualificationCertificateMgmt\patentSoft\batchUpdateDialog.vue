<!--批量修改有效期-->
<template>
    <!--组件内容区-->
    <app-dialog title="修改关联产品" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
        <template slot="body">
            <el-form
                :rules="formRules"
                ref="formRef"
                :model="formModel"
                label-position="right"
                label-width="100px"
                v-loading='loading'
            >
                <el-form-item label="已选中">{{ids.length}}</el-form-item>
                <el-form-item v-if="batchUpdateType == 1" label="有效期至" prop="ValidDate">
                    <el-date-picker v-model="formModel.ValidDate" type="date" align="right" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                </el-form-item>
                <el-form-item v-else-if="batchUpdateType == 2" label="关联产品" prop="ClassifyId">
                    <el-select v-model="formModel.ClassifyId" style="width: 100%;" placeholder="请选择">
                        <el-option
                            v-for="item in relationList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                
            </el-form>
        </template>
        <template slot="footer">
            <app-button :buttonType="2" @click="handleClose"></app-button>
            <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
        </template>
    </app-dialog>
</template>

<!--组件脚本区-->
<script>

// 专利及软件著作
import * as patentApi from '@/api/personnelManagement/Patent'
import * as classify from "@/api/classify";
import busMixins from './mixins'

export default {
    /**名称 */
    name: "batchUpdateDialog",
    /**组件声明 */
    components: {},
    mixins: [busMixins],
    /**参数区 */
    props: {
        ids: {
            type: Array,
            default: () => {
                return []
            }
        },
        //1 修改日期；2 修改关联产品
        batchUpdateType: {
            type: Number,
            default: 1
        }
    },
    /**数据区 */
    data() {
        return {
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            /**表单模型 */
            formModel: { 
                ValidDate: null,
                ClassifyId: null,
            },
            /**表单规则 */
            formRules: {
                ValidDate: { fieldName: "有效期", rules: [{ required: true }] },
                ClassifyId: { fieldName: "关联产品", rules: [{ required: true }] },
            },
            relationList: [

            ],
        };
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(this.batchUpdateType == 2) {
                    this.getList()
                }
                this.formModel = this.$options.data().formModel
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    self.buttonLoading = true;
                    let postDatas = {
                        IdList: self.ids,
                        Type: self.batchUpdateType
                    }
                    if(this.batchUpdateType == 1) {
                        postDatas.ValidDate = self.formModel.ValidDate
                    }else if(this.batchUpdateType == 2) {
                        postDatas.ClassifyId = self.formModel.ClassifyId
                    }
                    patentApi.BatchEditValidDate(postDatas).then(response => {
                        self.buttonLoading = false;
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.buttonLoading = false
                    })
                }
            });
        },
        getList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas.BusinessType = this.businessType

            self.listLoading = true;
            classify.getListByCondition(postDatas).then(res => {
                self.listLoading = false;
                self.relationList = res.map(s => {
                    return {
                        value: s.Id,
                        label: s.Name
                    }
                });
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
