<template>
    <div class="block-content" v-loading='loading'>
        <!-- <blockTitle :obj='obj'>
            <div slot='rht'>
                <el-radio-group v-model="period" size="mini" @change='getWorkPlanDetailsChart'>
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </div>
        </blockTitle> -->

         <el-row style="width:100%; margin-top:19px; margin-bottom:5px;">
            <el-col :span=12 style="display:flex;">
                <span style="margin-left:30px; width: 5px; height: 20px; background: #3D73DD;"></span>
                <span style="margin-top:2px; font-size:16px; color: #1D2129; margin-left:11px; font-weight:bold;">工作计划</span>
            </el-col>
            <el-col :span=12 style="display:flex; justify-content:flex-end;">
                 <el-radio-group v-model="period" size="mini" @change='getWorkPlanDetailsChart' style="margin-right:15px;">
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                 </el-radio-group>
            </el-col>
         </el-row>

        
         
        <div class="inner-content">
             <el-row style="width: 100%; height: 1px; background: #DCDFE6;"></el-row>
            <div class="top">
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">周计划</div>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption1.series)"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart1" :option='pieEchartOption1'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                   <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption2.series || pieEchartOption2.series[0].data.length == 0"></noData>
                        <app-charts-basic :width='chartsWidth' :height='chartsHeight' ref="pieEchart1" :option='pieEchartOption2'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                   <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption3.series || pieEchartOption3.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart1" :option='pieEchartOption3'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                   <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption4.series || pieEchartOption4.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart2" :option='pieEchartOption4'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                   <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption5.series)"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart2" :option='pieEchartOption5'></app-charts-basic>
                    </div>
                </div>
            </div>
            <div class="bottom">
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">月计划</div>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption6.series)"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption6'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                   <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption7.series || pieEchartOption7.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart4" :option='pieEchartOption7'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                   <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption8.series || pieEchartOption8.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart5" :option='pieEchartOption8'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption9.series || pieEchartOption9.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart4" :option='pieEchartOption9'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title"></div>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption10.series)"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart5" :option='pieEchartOption10'></app-charts-basic>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import blockTitle from '../blockTitle'
import * as odc from "@/api/operatingDataCenter";
import { pieEchartOptionTemp, progressEchartOptionTemp, colors, dateTypeEnum1 } from "../vars";
import mixins from '../mixins'

export default {
    name: 'gongzojihua',
    mixins: [mixins],
    components: {
        noData,
        blockTitle,
    },
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    mounted() {
        this.getWorkPlanDetailsChart()
    },
    data() {
        return {
            period: 3,
            dateTypeEnum: dateTypeEnum1,
            loading: false,
            chartsHeight: '180px',
            chartsWidth: '157px',
            pieEchartOption1: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption2: JSON.parse(JSON.stringify(progressEchartOptionTemp)),
            pieEchartOption3: JSON.parse(JSON.stringify(progressEchartOptionTemp)),
            pieEchartOption4: JSON.parse(JSON.stringify(progressEchartOptionTemp)),
            pieEchartOption5: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption6: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption7: JSON.parse(JSON.stringify(progressEchartOptionTemp)),
            pieEchartOption8: JSON.parse(JSON.stringify(progressEchartOptionTemp)),
            pieEchartOption9: JSON.parse(JSON.stringify(progressEchartOptionTemp)),
            pieEchartOption10: JSON.parse(JSON.stringify(pieEchartOptionTemp)),

        }
    },
    methods: {
        getWorkPlanDetailsChart() {
            let that = this
            that.loading = true
            odc.getWorkPlanDetailsChart({Period: that.period}).then(res => {
                that.loading = false

                //周计划
                that.pieEchartOption1 = that._initPieChartDatas(res.WeekData || [])
                that.pieEchartOption2 = that._initProgressChartDatas(res.WeekDetailsData.CompletedCount || 0, '已完成')
                that.pieEchartOption3 = that._initProgressChartDatas(res.WeekDetailsData.ReportedCount || 0, '已汇报')
                that.pieEchartOption4 = that._initProgressChartDatas(res.WeekDetailsData.Saturability || 0, '饱和率', true)
                that.pieEchartOption5 = that._initPieChartDatas(res.WeekDetailsData.PlanInsideOutsideData || [],true)

                //月计划
                that.pieEchartOption6 = that._initPieChartDatas(res.MonthData || [])
                that.pieEchartOption7 = that._initProgressChartDatas(res.MonthDetailsData.CompletedCount || 0, '已完成')
                that.pieEchartOption8 = that._initProgressChartDatas(res.MonthDetailsData.ReportedCount || 0, '已汇报')
                that.pieEchartOption9 = that._initProgressChartDatas(res.MonthDetailsData.Saturability || 0, '饱和率', true)
                that.pieEchartOption10 = that._initPieChartDatas(res.MonthDetailsData.PlanInsideOutsideData || [],true)

            }).catch(err => {
                that.loading = false
            })
        },
        _initProgressChartDatas(saturability, title, isCus) {
            let col = '#FF974C'
            let col2 = '#3D73DD'
            let targetOption = {
                title: {
                    text: title,
                    textStyle:{
	                  color:'#AEB0BC',
	                }
                },
                series: [{
                    radius: ['50%', '65%'],
                    data: [{
                        value: saturability,
                        name: `${saturability}%`,
                    },
                    {
                        value: 100-saturability,
                    }]
                }]
            }

            //如果已完成进度超过 100%，那么只显示一个圆环即可
            if(isCus && saturability > 100) {
                targetOption.series = [{
                    radius: ['50%', '65%'],
                    data: [{
                        value: saturability,
                        name: `${saturability}%`,
                        itemStyle: {
                            color: saturability > 100 ? col2 : col
                        }
                    }]
                }]
            }
             
            if(saturability == 0){
                 targetOption.series = [{
                    radius: ['50%', '65%'],
                    data: [{
                        value: saturability,
                        name: `${saturability}%`,
                        itemStyle: {
                            color: 'E9EEF4'
                        },
                         label:{
                         normal:{
                             color: '#58F3AC'
                        }
            }
                    }]
                }]
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(progressEchartOptionTemp)), targetOption)

            return targetOption

        },
        _initPieChartDatas(list,state) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                if(state){

                    targetOption = {
                    legend: {
                        // type: 'scroll',
                        // icon:"circle",
                        // orient: 'vertical',
                        // left: '62%',
                        // top: 'middle',
                         textStyle: {                     
	                        color: '#AEB0BC'
       		             },
                        data: chartDatas.map(s => s.Label)//chartDatas.map(s => s.Label)
                    },
                    
                    series: [{
                        radius: '60%',
                        center: ['50%', '50%'],
                        label: {
                            normal: {
                                // show: false,
                                position: 'inner'
                            }
                        },
                        data: chartDatas.map(s => {
                            return {
                                value: s.Value === 0 ? null : s.Value,
                                name: s.Label
                            }
                        })
                    }],
                    color: ['#3D73DD', '#F29748', '#88d1ea', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2']
                }

                }else{
                    targetOption = {
                    legend: {
                        // type: 'scroll',
                        // icon:"circle",
                        // orient: 'vertical',
                        // left: '62%',
                        // top: 'middle',
                        textStyle: {                     
	                        color: '#AEB0BC'
       		             },
                        data: chartDatas.map(s => s.Label)//chartDatas.map(s => s.Label)
                    },
                    series: [{
                        radius: '60%',
                        center: ['50%', '50%'],
                        label: {
                            normal: {
                                // show: false,
                                position: 'inner'
                            }
                        },
                        data: chartDatas.map(s => {
                            return {
                                value: s.Value === 0 ? null : s.Value,
                                name: s.Label
                            }
                        })
                    }],
                    color: colors
                }
                }
                
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption)

            return targetOption

        },
    },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
.block-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    .inner-content{
        flex: 1;
        display: flex;
        flex-direction: column;
        .top, .bottom{
            flex: 1;
            display: flex;
            .flex-dire-column-wrapper{
                flex: 1;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
            // >div:first-child{
            //     border-right: 1px solid #dcdfe6;
            // }
        }
        .top{
            >div{
                padding-bottom: 10px;
            }
        }
        .bottom{
            >div{
                padding-top: 10px;
            }
        }
    }
}

.chart-title{
    text-align: center;
    height: 16px;
    line-height: 16px;
    color:$text-primary;
    font-weight:bold;
}

.flex-1, .flex-2{
   // box-sizing: border-box;
    margin: 5px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    // >div:last-child{
    //     flex: 1;
    // }
}

.flex-1{
    flex: 1;
    display: flex;
    align-items: center;
}

.text-content{
    text-align: center; flex: 1; font-weight: bold; display: flex; justify-content: center; align-items: center; word-break: break-all; white-space: normal; word-break: break-all;
}


</style>