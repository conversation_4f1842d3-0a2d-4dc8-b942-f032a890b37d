<template>
    <div class="configPage">
        <app-dialog title="添加配置" 
        class="configDialog"
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='446'
        >
        	<template slot="body">
                <v-config-page-body :ids="configIds" @getConfigList='getConfigList'></v-config-page-body>
        	</template>
        	<template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
    	</app-dialog>
    </div>
</template>    	
<script>
import vConfigPageBody from './configPageBody';
export default{
	name:'configPage',
    props:['configIds'],
	data(){
        return{
        	disabledBtn:false,
        	ids:[],
            listData:[],
            listChildData:[],
            radio:'',
        }
    },
    components: { vConfigPageBody },
    watch: {
        '$attrs.dialogFormVisible'(val) {
        	if(val){
        		this.finishingData();
        	}
        },
    },   
    created(){
        
    },
    mounted(){
        
    },
    methods:{
    	getConfigList(r,ld,lcd){
            this.radio=r;
            this.listData=ld;
            this.listChildData=lcd;
        },
    	handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        createData(){
            this.disabledBtn = true;
            this.saveIds();
            
        },
        saveIds(){
        	this.ids=[];
        	this.ids.push(this.radio);
        	this.listChildData.forEach(v => {
        		if(v.value.length>0){
        			this.ids.push(v.Id);
        			this.ids.push(v.value);
        		}
        	})
        	this.$emit('saveSuccess',this.ids);
        	this.disabledBtn = false;
        	this.handleClose();
        },
        finishingData(){
        	
        	
        }
    } 
}
</script>
