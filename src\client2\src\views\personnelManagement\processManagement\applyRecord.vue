<template>
  <div class="applyRecord">
    <app-dialog
      title="申请记录"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1000"
      className="clear-default-height-auto dialog-auto-height"
    >
      <template slot="body">
        <app-table
          :height="600"
          ref="mainTable"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :tab-auth-columns="[]"
          :isShowAllColumn="true"
          :loading="loading"
          :isShowOpatColumn="false"
          :startOfTable="startOfTable"
          :isShowBtnsArea="false"
          :multable="false"
        >
          <template slot="ApprovalStatus" slot-scope="scope">
            <template v-if="approvalStatus.find(s => s.value == scope.row.ApprovalStatus)">
              <span
                :style="
                  'color:' +
                  approvalStatus.find(s => s.value == scope.row.ApprovalStatus).color +
                  ';'
                "
              >
                {{ approvalStatus.find(s => s.value == scope.row.ApprovalStatus).label }}
              </span>
            </template>
            <!-- <span>{{ scope.row.RevocationStatus }}</span> -->

            <span
              v-if="scope.row.RevocationStatus"
              :style="{
                color: getRevocationStatusObj(scope.row.RevocationStatus).color,
              }"
            >
              <span>（{{ getRevocationStatusObj(scope.row.RevocationStatus).label }}）</span>
            </span>
          </template>
          <template slot="CreateTime" slot-scope="scope">
            <span>{{ scope.row.CreateTime | dateFilter("YYYY-MM-DD HH:mm") }}</span>
          </template>
          <template slot="LeaveType" slot-scope="scope">
            <span>{{ leaveTypes.find(s => s.value == scope.row.LeaveType).label }}</span>
          </template>
          <template slot="EmployeeList" slot-scope="scope">
            <span>{{ scope.row.EmployeeList | empNames }}</span>
          </template>

          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="onResetSearch"
            >
              <template slot="timeRange">
                <el-date-picker
                  style="width: 100%"
                  v-model="listQuery.timeRange"
                  type="daterange"
                  range-separator="-"
                  start-placeholder=""
                  end-placeholder=""
                  @change="handleChangeTime"
                ></el-date-picker>
              </template>
              <template slot="createEmployee">
                <el-input
                  style="width: 100%"
                  maxlength="50"
                  v-model="listQuery.createEmployee"
                  placeholder
                ></el-input>
              </template>
              <!-- <template slot="approvalState">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.approvalState" placeholder="">
                                        <el-option v-for="item in approvalStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template> -->
            </app-table-form>
          </template>
          <template slot="operation" slot-scope="scope">
            <el-button type="text" @click="handleReview(scope.row)">详情</el-button>
          </template>
        </app-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <el-button @click="handleClose">关闭</el-button>
      </template>
    </app-dialog>
    <v-vacate
      v-if="dialogVaVisible"
      @closeDialog="closeVaDialog"
      :dialogFormVisible="dialogVaVisible"
      dialogStatus="detail"
      :id="applyId"
    ></v-vacate>
    <!-- 加班申请 -->
    <overtime
      v-if="dialogOTimeVisible"
      @closeDialog="closeOTimgDialog"
      :dialogFormVisible="dialogOTimeVisible"
      dialogStatus="detail"
      :id="applyId"
    ></overtime>
    <!-- 出差申请 -->
    <evection
      v-if="dialogEvVisible"
      @closeDialog="closeEvDialog"
      :dialogFormVisible="dialogEvVisible"
      dialogStatus="detail"
      :id="applyId"
    ></evection>

    <!-- 外出申请 -->
    <goout
      v-if="dialogOutVisible"
      @closeDialog="closeOutDialog"
      :dialogFormVisible="dialogOutVisible"
      :dialogStatus="'detail'"
      :id="applyId"
    ></goout>

    <!-- 变更联络单申请 -->
    <changeContact
      v-if="dialogChangeContactVisible"
      @closeDialog="dialogChangeContactVisible = false"
      :dialogFormVisible="dialogChangeContactVisible"
      :dialogStatus="'detail'"
      :id="applyId"
    ></changeContact>

    <!-- 出差补助申请 -->
    <evectionSubsidy
      v-if="dialogEvSubSidyVisible"
      @closeDialog="dialogEvSubSidyVisible = false"
      :dialogFormVisible="dialogEvSubSidyVisible"
      :dialogStatus="'detail'"
      :id="applyId"
    ></evectionSubsidy>

    <!-- 人员异动申请 -->
    <persTurnover
      v-if="dialogPersTurnoverVisible"
      @closeDialog="dialogPersTurnoverVisible = false"
      :dialogFormVisible="dialogPersTurnoverVisible"
      :dialogStatus="'detail'"
      :id="applyId"
    ></persTurnover>

    <!-- 费用报销单 -->
    <ExpenseRbtDialog
      v-if="expenseRbtVisible"
      :dialogFormVisible="expenseRbtVisible"
      dialogStatus="detail"
      :id="applyId"
      @closeDialog="expenseRbtVisible = false"
    />
    <!-- 付款申请 -->
    <PaymentReqDialog
      v-if="paymentReqVisible"
      :dialogFormVisible="paymentReqVisible"
      dialogStatus="detail"
      :id="applyId"
      @closeDialog="paymentReqVisible = false"
    />
    <!-- 差旅费报销 -->
    <TravelExpensesDialog
      v-if="travelExpensesVisible"
      :dialogFormVisible="travelExpensesVisible"
      dialogStatus="detail"
      :id="applyId"
      @closeDialog="travelExpensesVisible = false"
    />
    <!-- 借款申请 -->
    <LoanApplicationDialog
      v-if="loanApplicationVisible"
      :dialogFormVisible="loanApplicationVisible"
      dialogStatus="detail"
      :id="applyId"
      @closeDialog="loanApplicationVisible = false"
    />
  </div>
</template>
<script>
import dayjs from "dayjs";
import * as approvalManagement from "@/api/approvalManagement";
import indexPageMixin from "@/mixins/indexPage";
import { vars } from "../../workbench/myWorkbench/vars";
import vVacate from "../../workbench/myWorkbench/apply/vacate";
import overtime from "../../workbench/myWorkbench/apply/overtime";
import evection from "../../workbench/myWorkbench/apply/evection";
import goout from "../../workbench/myWorkbench/apply/goout";
import changeContact from "../../workbench/myWorkbench/apply/changeContact";
import evectionSubsidy from "../../workbench/myWorkbench/apply/evectionSubsidy";
import persTurnover from "../../workbench/myWorkbench/apply/persTurnover";

export default {
  name: "applyRecord",
  mixins: [indexPageMixin],
  components: {
    vVacate,
    overtime,
    evection,
    goout,
    changeContact,
    evectionSubsidy,
    persTurnover,
    ExpenseRbtDialog: () =>
      import("@/views/financialManagement/reimbursement/components/ExpenseRbtDialog"), // 费用报销单
    PaymentReqDialog: () =>
      import("@/views/financialManagement/reimbursement/components/PaymentReqDialog"), // 付款申请
    TravelExpensesDialog: () =>
      import("@/views/financialManagement/reimbursement/components/TravelExpensesDialog"), // 付款申请
    LoanApplicationDialog: () =>
      import("@/views/financialManagement/reimbursement/components/LoanApplicationDialog"), // 借款申请
  },
  filters: {
    empNames(list) {
      if (list && list.length > 0) {
        return list.map(s => s.Name).join("、");
      }
      return "无";
    },
  },
  props: {
    type: {
      type: Number,
      default: 1,
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      applyId: "",
      dialogVaVisible: false,
      dialogOTimeVisible: false,
      dialogEvVisible: false,
      dialogOutVisible: false,
      dialogChangeContactVisible: false,
      dialogEvSubSidyVisible: false,
      dialogPersTurnoverVisible: false,
      expenseRbtVisible: false,
      paymentReqVisible: false,
      travelExpensesVisible: false,
      loanApplicationVisible: false,
      total: 0,
      approvalStatus: vars.approvalStatus,
      leaveTypes: vars.leaveTypes,
      loading: false,
      listQuery: {
        timeRange: "",
        // "approvalState": null,
        createEmployee: "",
        startCreateTime: null,
        endCreateTime: null,
        type: 1,
        PageIndex: 1,
        PageSize: 20,
      },
      tableSearchItems: [
        {
          prop: "timeRange",
          label: "提交时间",
        },
        {
          prop: "createEmployee",
          label: "申请人",
        },
        // {
        //     prop: "approvalState",
        //     label: "审批状态",
        // },
      ],
      tabDatas: [],
      tabColumns: [],
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.listQuery.type = this.type;
    this.tabColumns = [
      {
        attr: {
          prop: "Code",
          label: "审批单号",
        },
      },

      {
        attr: {
          prop: "ApprovalStatus",
          label: "审批状态",
        },
        slot: true,
      },
      {
        attr: {
          prop: "CreateTime",
          label: "提交时间",
        },
        slot: true,
      },
      {
        attr: {
          prop: "LeaveType",
          label: "请假类型",
        },
        slot: true,
      },
      {
        attr: {
          prop: "EmployeeList",
          label: "申请人",
          showOverflowTooltip: true,
        },
        slot: true,
      },
      {
        attr: {
          prop: "operation",
          label: "操作",
        },
        slot: true,
      },
    ];
    if (this.type != 1) {
      this.tabColumns.splice(3, 1);
    }
    this.getList();
  },
  methods: {
    getRevocationStatusObj(status) {
      return vars.revocationStatus.find(s => s.value == status) || {};
    },
    handleChangeTime(d) {
      if (!d) {
        this.listQuery.startCreateTime = "";
        this.listQuery.endCreateTime = "";
      }
    },
    closeVaDialog() {
      this.dialogVaVisible = false;
    },
    closeOTimgDialog() {
      this.dialogOTimeVisible = false;
    },
    closeEvDialog() {
      this.dialogEvVisible = false;
    },
    closeOutDialog() {
      this.dialogOutVisible = false;
    },
    getList() {
      this.loading = true;
      this.listQuery.HRApprovalProcessId = this.id;
      if (this.listQuery.timeRange) {
        this.listQuery.startCreateTime = dayjs(this.listQuery.timeRange[0]).format("YYYY-MM-DD");
        this.listQuery.endCreateTime = dayjs(this.listQuery.timeRange[1]).format("YYYY-MM-DD");
      }
      approvalManagement
        .getListByCondition(this.listQuery)
        .then(res => {
          this.loading = false;
          this.total = res.Total;
          this.tabDatas = res.Items;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList(); //刷新列表
    },
    onResetSearch() {
      this.listQuery.timeRange = "";
      this.listQuery.createEmployee = "";
      // this.listQuery.approvalState = null;
      this.listQuery.startCreateTime = "";
      this.listQuery.endCreateTime = "";
      this.getList(); //刷新列表
    },
    handleReview(d) {
      this.applyId = d.CurrentBusinessId;
      console.log(this.type);
      if (this.type == 1) {
        this.dialogVaVisible = true;
      } else if (this.type == 2) {
        this.dialogOTimeVisible = true;
      } else if (this.type == 3) {
        this.dialogEvVisible = true;
      } else if (this.type == 4) {
        this.dialogOutVisible = true;
      } else if (this.type == 6) {
        this.dialogEvSubSidyVisible = true;
      } else if (this.type == 7) {
        this.dialogChangeContactVisible = true;
      } else if (this.type == 9) {
        this.dialogPersTurnoverVisible = true;
      } else if (this.type == 10) {
        this.expenseRbtVisible = true;
      } else if (this.type == 11) {
        this.paymentReqVisible = true;
      } else if (this.type == 12) {
        this.travelExpensesVisible = true;
      } else if (this.type == 13) {
        this.loanApplicationVisible = true;
      }
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>
<style lang="scss" scoped></style>
