<template>
  <div class="attendance_time_table">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="0">
      <el-form-item label="" prop="AttendanceTimeConfig">
        <app-table-core
          ref="mainTable"
          :tab-columns="tableColumn"
          :tab-datas="formData.AttendanceTimeConfig"
          :tab-auth-columns="[]"
          :isShowOpatColumn="!disabled"
          :multable="false"
          :serial="false"
          :optColWidth="80"
          :loading="loading"
        >
          <template slot="select" slot-scope="scope">
            <el-checkbox
              :value="isSelect(scope.row)"
              :disabled="disabledSelectCol"
              @change="changeSelect(scope.row)"
            />
          </template>
          <template slot="WorkStartRange" slot-scope="scope">
            <el-form-item
              label=""
              :key="scope.index"
              style="margin-bottom:0"
              :rules="[{ required: true, message: '请选择时间', trigger: 'change' }]"
              :prop="`AttendanceTimeConfig[${scope.index - 1}].WorkStartRange`"
            >
              <el-time-picker
                :key="1"
                :disabled="disabled"
                format="HH:mm"
                value-format="HH:mm"
                is-range
                v-model="scope.row.WorkStartRange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间"
                style="width:100%"
              />
            </el-form-item>
          </template>
          <template slot="LunchBreakRange" slot-scope="scope">
            <el-form-item
              label=""
              :key="scope.index"
              style="margin-bottom:0"
              :rules="[{ required: true, message: '请选择时间', trigger: 'change' }]"
              :prop="`AttendanceTimeConfig[${scope.index - 1}].LunchBreakRange`"
            >
              <el-time-picker
                :key="1"
                :disabled="disabled"
                format="HH:mm"
                value-format="HH:mm"
                is-range
                v-model="scope.row.LunchBreakRange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间"
                style="width:100%"
              />
            </el-form-item>
          </template>
          <template slot="Remark" slot-scope="scope">
            <el-input
              v-model="scope.row.Remark"
              :maxlength="50"
              :disabled="disabled"
              type="text"
              placeholder="请输入考勤时间适用描述"
              style="width:100%"
              v-if="!disabled"
            />
            <span v-else>{{ scope.row.Remark }}</span>
          </template>
          <template slot="DailyAttendance" slot-scope="scope">
            <el-checkbox
              :value="scope.row.IsUsed"
              :disabled="disabled"
              @change="changeDailyAttendance($event, scope.row)"
            />
          </template>
          <template slot-scope="scope">
            <app-table-row-button :type="3" text="删除" @click="handleDelete(scope.index)" />
          </template>
        </app-table-core>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
const defTableObj = {
  WorkStartRange: ["09:30", "18:30"],
  LunchBreakRange: ["12:30", "14:00"],
  Remark: "",
  IsUsed: false,
  WorkStartTime: null,
  WorkEndTime: null,
  LunchBreakTime: null,
};

export default {
  name: "AttendanceTimeTable",
  props: {
    // 数据数组
    list: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否显示日常考勤列
    isShowDailyAttendance: {
      type: Boolean,
      default: false,
    },
    // 是否显示选择列
    isShowSelectCol: {
      type: Boolean,
      default: false,
    },
    // 是否禁用选择列
    disabledSelectCol: {
      type: Boolean,
      default: false,
    },
    // 是否多选
    multipleSelect: {
      type: Boolean,
      default: false,
    },
    // 选择的id数组
    selectIdList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {
        AttendanceTimeConfig: [],
      },
      // 选择的id数组
      selectIdList_: [],
      loading: false,
      rules: {
        AttendanceTimeConfig: [
          { required: true, message: "请添加考勤时间配置", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    selectIdList: {
      handler(val) {
        this.selectIdList_ = this.$_.cloneDeep(val);
      },
      deep: true,
      immediate: true,
    },
    list: {
      handler(val) {
        if (val?.length) {
          const attendanceTimeConfig = this.$_.cloneDeep(val);
          this.formData.AttendanceTimeConfig = attendanceTimeConfig.map(t => {
            t.WorkStartRange = t.WorkStartTime && t.WorkEndTime ? [t.WorkStartTime, t.WorkEndTime] : [];
            t.LunchBreakRange = t.LunchBreakTime ? t.LunchBreakTime.split("-") : [];
            return t;
          });
        }

        if (!this.formData.AttendanceTimeConfig.length && !this.disabled) {
          this.addRow();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 行是否被选择
    isSelect() {
      return item => {
        if (!item?.Id) return false;
        return this.selectIdList_.includes(item.Id);
      };
    },
    tableColumn() {
      return [
        {
          attr: { prop: "select", label: "", width: 35 },
          isShow: this.isShowSelectCol,
          slot: true,
        },
        { attr: { prop: "WorkStartRange", label: "上班时间", width: 200 }, slot: true },
        { attr: { prop: "LunchBreakRange", label: "休息时间", width: 200 }, slot: true },
        { attr: { prop: "Remark", label: "考勤时间适用描述" }, slot: true },
        {
          attr: { prop: "DailyAttendance", label: "日常考勤", width: 80 },
          isShow: this.isShowDailyAttendance,
          slot: true,
        },
      ];
    },
  },
  methods: {
    getValue() {
      const form = this.$_.cloneDeep(this.formData);
      form.AttendanceTimeConfig = form.AttendanceTimeConfig.map(t => {
        t.WorkStartTime = t.WorkStartRange?.[0] || null;
        t.WorkEndTime = t.WorkStartRange?.[1] || null;

        if (t.LunchBreakRange && t.LunchBreakRange.length === 2) {
          t.LunchBreakTime = `${t.LunchBreakRange[0]}-${t.LunchBreakRange[1]}`;
        }
        delete t.WorkStartRange;
        delete t.LunchBreakRange;

        return t;
      });

      return form;
    },
    validate() {
      // 校验是否选择日常考勤
      if (this.isShowDailyAttendance) {
        const someDailyAttendance = this.formData.AttendanceTimeConfig.some(t => t.IsUsed);
        if (!someDailyAttendance) {
          this.$message.error("请选择日常考勤");
          return new Promise.reject();
        }
      }
      return this.$refs.formRef.validate();
    },
    addRow() {
      this.formData.AttendanceTimeConfig.push(this.$_.cloneDeep(defTableObj));
      this.$refs.formRef && this.$refs.formRef.clearValidate();
    },
    handleDelete(index) {
      this.$confirm("此操作将删除该项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formData.AttendanceTimeConfig.splice(index - 1, 1);
      });
    },
    // 选择日常考勤时间(单选)
    changeDailyAttendance(value, item) {
      const AttendanceTimeConfig = this.formData.AttendanceTimeConfig;
      if (value) {
        AttendanceTimeConfig.forEach(item => {
          item.IsUsed = false;
        });
        item.IsUsed = true;
      }
    },
    // 选择改变钩子
    changeSelect(item) {
      const index = this.selectIdList_.findIndex(t => t === item.Id);
      if (this.multipleSelect) {
        // 多选
        if (index !== -1) {
          this.selectIdList_.splice(index, 1);
        } else {
          this.selectIdList_.push(item.Id);
        }
      } else {
        // 单选
        this.selectIdList_ = [];
        this.selectIdList_.push(item.Id);
      }

      this.$emit("changeSelect", this.selectIdList_);
    },
  },
};
</script>

<style lang="scss" scoped>
.attendance_time_table {
}
</style>
