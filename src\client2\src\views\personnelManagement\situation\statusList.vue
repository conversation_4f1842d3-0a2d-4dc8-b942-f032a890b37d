<template>
  <div class="tag-list" v-if="recordObj">
    <template v-if="recordObj && recordObj.DateType == 2">
      <span class="tag" style="background: #027DB4;">休息</span>
    </template>

    <el-popover
      :key="'tr_' + rowKey"
      title="打卡情况"
      width="200"
      trigger="hover"
      placement="bottom"
      :close-delay="50"
    >
      <div>
        <div style="margin-bottom: 6px;">
          <span>上午：</span>
          <span>
            <app-tag-pure
              effect="dark"
              :color="getStatusColor(recordObj.MornFirstCardStatus)"
              :text="recordObj.MornFirstCardStatus | timecardStatusFilter"
            ></app-tag-pure>
            <!-- <span style="display: inline-block; padding: 1px 4px; border-radius: 2px;" :style="{color: recordObj.MornFirstCardStatus ? '#fff' : '', backgroundColor: getStatusColor(recordObj.MornFirstCardStatus)}">
                            {{recordObj.MornFirstCardStatus | timecardStatusFilter}}
                        </span> -->
          </span>
        </div>
        <div>
          <span>下午：</span>
          <div style="display: inline-block; position: relative;">
            <!-- <span style="display: inline-block; padding: 1px 4px; border-radius: 2px;" :style="{color: recordObj.NightLastCardStatus ? '#fff' : '', backgroundColor: getStatusColor(recordObj.NightLastCardStatus)}">
                            {{recordObj.NightLastCardStatus | timecardStatusFilter}}
                        </span> -->
            <app-tag-pure
              effect="dark"
              :color="getStatusColor(recordObj.NightLastCardStatus)"
              :text="recordObj.NightLastCardStatus | timecardStatusFilter"
            ></app-tag-pure>
            <span
              v-if="recordObj.NightLastCardStatus === 6"
              style="top: -4px; right: -4px; width: 8px; height: 8px;  border-radius: 50%; background-color: #FFAA00;  position: absolute;"
            ></span>
          </div>
        </div>
      </div>
      <div
        slot="reference"
        :style="{
          display:
            recordObj && (recordObj.MornFirstCardStatus || recordObj.NightLastCardStatus)
              ? 'inline-block'
              : 'none',
        }"
      >
        <span style="margin-right: 5px;">
          <!-- 上、下午均为缺卡（上下午打卡均不正常）——相同合并 -->
          <app-tag-pure
            :key="'_ref1' + rowKey"
            v-if="
              (isShow &&
                recordObj.MornFirstCardStatus == 5 &&
                recordObj.NightLastCardStatus == 5) ||
                (recordObj.MornFirstCardStatus == 5 && recordObj.NightLastCardStatus === 0) ||
                (recordObj.MornFirstCardStatus == 0 && recordObj.NightLastCardStatus === 5)
            "
            effect="dark"
            color="#F53F3F"
            text="未出勤"
          ></app-tag-pure>
          <!-- 已出勤，改为显示上下午打开状态 -->
          <!-- 迟到且早退（上下午打卡均不正常）——不相同，上下午打卡状态都显示 -->
          <app-tag-pure
            :key="'_ref2' + rowKey"
            v-else-if="
              isShow && recordObj.MornFirstCardStatus == 2 && recordObj.NightLastCardStatus == 3
            "
            effect="dark"
            color="#F53F3F"
          >
            已出勤
            <template v-if="showDetail">
              ({{ recordObj.MornFirstCardStatus | timecardStatusFilter }}、{{
                recordObj.NightLastCardStatus | timecardStatusFilter
              }})
            </template>
          </app-tag-pure>
          <!-- 上下午打卡均正常——相同合并 -->
          <app-tag-pure
            :key="'_ref3' + rowKey"
            v-else-if="
              isShow &&
                recordObj.MornFirstCardStatus == 1 &&
                (recordObj.NightLastCardStatus == 1 || recordObj.NightLastCardStatus == 6)
            "
            effect="dark"
            color="#267FEB"
          >
            已出勤
            <template v-if="showDetail">
              ({{ recordObj.MornFirstCardStatus | timecardStatusFilter }}、{{
                recordObj.NightLastCardStatus | timecardStatusFilter
              }})
            </template>
          </app-tag-pure>
          <!-- 上、下午打卡任一正常——显示不正宗的打卡 -->
          <!-- 上午：正常、迟到；下午：正常、早退、工作日加班 都视为有正常打卡 -->
          <app-tag-pure
            :key="'_ref4' + rowKey"
            v-else-if="
              (isShow &&
                (recordObj.MornFirstCardStatus == 1 || recordObj.MornFirstCardStatus == 2)) ||
                recordObj.NightLastCardStatus == 1 ||
                recordObj.NightLastCardStatus == 3 ||
                recordObj.NightLastCardStatus == 6
            "
            effect="dark"
            color="#00B7FD"
          >
            已出勤
            <template v-if="showDetail">
              (
              <!-- 上午有打卡（ > 0），且不是正常状态（1、6、5），即显示上午打卡（异常）状态 -->
              <!-- v-if="recordObj.MornFirstCardStatus && recordObj.MornFirstCardStatus != 1 && recordObj.MornFirstCardStatus != 6 && recordObj.MornFirstCardStatus != 5" -->
              <template>
                {{ recordObj.MornFirstCardStatus | timecardStatusFilter }}、{{
                  recordObj.NightLastCardStatus | timecardStatusFilter
                }}
              </template>
              <!-- 下午有打卡（ > 0），且不是正常状态（1、6），即显示下午打卡（异常）状态 -->
              <!-- -->
              <!-- <template v-else-if="recordObj.NightLastCardStatus && recordObj.NightLastCardStatus != 1 && recordObj.NightLastCardStatus != 6"></template> -->
              )
            </template>
          </app-tag-pure>
        </span>
      </div>
    </el-popover>
    <!-- 有打卡状态 注意：状态6工作日加班视为正常1 -->

    <!-- 大流程类型 -->
    <template v-if="recordObj && recordObj.ProcessDataGroup">
      <span v-for="(p, idx2) in recordObj.ProcessDataGroup" :key="'p_' + idx2 + rowKey">
        <el-popover
          v-if="recordObj"
          width="200"
          trigger="hover"
          placement="bottom"
          :open-delay="50"
        >
          <div>
            <div v-for="(sub, idx3) in p.List" :key="'sub' + idx3">
              <!-- 小流程类型：事假、调休、病假等 -->
              <span>{{ sub.LeaveType | levelTypeFilter }}：</span>
              <!-- 外出 -->
              <span v-if="sub.LeaveType == 11">
                <template v-if="sub.ProcessData && sub.ProcessData.length > 0">
                  {{ sub.ProcessData[0].StartTime | dateFilter("HH:mm") }} -
                  {{ sub.ProcessData[0].EndTime | dateFilter("HH:mm") }}
                </template>
              </span>
              <template v-else>
                <span v-for="(pro, idx4) in sub.ProcessData" :key="'pro' + idx4">
                  {{ pro.ProcessNodeTime | morningOrAfternoonFilter }}
                </span>
              </template>
              <!-- 出差 -->
              <!-- <div v-if="sub.LeaveType == 10 && sub.ProcessData && sub.ProcessData.length > 0">
                                出差地点：{{ sub.ProcessData[0].TravelPlace || '无' }}
                            </div> -->
            </div>
          </div>

          <span slot="reference">
            <app-tag-pure effect="dark" :color="getTypeColor(p.Type)">
              {{ p.Type | typeFilter }}
            </app-tag-pure>
          </span>

          <!-- <span slot="reference" class="tag" :style="{backgroundColor: getTypeColor(p.Type)}">
                        {{ p.Type | typeFilter }}
                    </span> -->
        </el-popover>
      </span>
    </template>
  </div>
</template>

<script>
import { vars } from "../../personnelManagement/attendanceMgmt/vars";
export default {
  name: "statusList",
  props: {
    showDetail: {
      type: Boolean,
      default: true,
    },
    recordObj: {
      type: Object,
      required: true,
    },
    //主要是该组件在table中使用，popover 不显示的问题
    rowKey: {
      type: String,
    },
  },
  computed: {
    isShow() {
      let recordObj = this.recordObj;
      return recordObj && (recordObj.MornFirstCardStatus || recordObj.NightLastCardStatus);
    },
  },
  filters: {
    typeFilter(val) {
      let result = "无";
      if (val) {
        let obj = vars.processTypes.find(s => s.value == val);
        if (obj) {
          result = obj.label;
        }
      }
      return result;
    },
    timecardStatusFilter(val) {
      let result = "-";
      if (val) {
        let obj = vars.timecardStatus.find(s => s.value == val);
        if (obj) {
          result = obj.label;
        }
      }
      return result;
    },
    levelTypeFilter(val) {
      let obj = vars.leaveTypes.find(s => s.value === val);
      if (obj) {
        return obj.label;
      }
      return "无";
    },
    morningOrAfternoonFilter(val) {
      let result = "无";
      if (val) {
        let obj = vars.morningOrAfternoon.find(s => s.value == val);
        if (obj) {
          result = obj.label;
        }
      }
      return result;
    },
    levelTypeFilter(val) {
      let obj = vars.leaveTypes.find(s => s.value === val);
      if (obj) {
        return obj.label;
      }
      return "无";
    },
  },
  data() {
    return {};
  },
  methods: {
    getStatusColor(val) {
      let obj = vars.timecardStatus.find(s => s.value == val);
      if (obj) {
        return obj.color;
      }
      return "";
    },
    getTypeColor(val) {
      let obj = vars.processTypes.find(s => s.value == val);
      if (obj) {
        return obj.color;
      }
      return "";
    },
  },
};
</script>

<style lang="scss" scoped>
.tag-list {
  span.tag {
    margin: 2px 0;
    margin-right: 4px;
  }
  /deep/.el-popover__reference {
    line-height: 20px;
  }
}

.tag {
  line-height: 20px;
  display: inline-block;
  padding: 1px 4px;
  border-radius: 2px;
  color: #fff;
}
</style>
