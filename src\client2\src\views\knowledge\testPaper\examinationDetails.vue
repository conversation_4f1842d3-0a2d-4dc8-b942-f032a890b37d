<!--分类 添加/编辑-->
<template>
    <div>
        <app-dialog title="考试详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100" :maxHeight="600">
            <template slot="body">
                <div class=".__dynamicTabContentWrapper">
                    <el-form ref="formData" label-position="right" label-width="90px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="考卷名称：" style="margin-bottom: 0">
                                    <div class="text_nowrap" :title="formModel.ExaminationPaperName?formModel.ExaminationPaperName:''">{{formModel.ExaminationPaperName ||'无'}}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="出卷人：" style="margin-bottom: 0">
                                    <div class="text_nowrap" :title="formModel.OwnerEmployeeNames?formModel.OwnerEmployeeNames:''">{{formModel.OwnerEmployeeNames ||'无'}}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <div class="__dynamicTabWrapper">
                        <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="dataSourceList" :loading="listLoading"
                            :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable"
                            :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false'
                            @sortChagned="handleSortChange">

                            <template slot="LearningTime" slot-scope="scope">{{scope.row.LearningTime | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                            <!-- 考试成绩 -->
                            <template slot="ExamResults" slot-scope="scope">{{scope.row.ExamResults||'无'}}</template>
                            <!-- 是否及格 -->
                            <template slot="IsPass" slot-scope="scope">
                                <span class="item-status" :style="{color: getIsPassObj(scope.row.IsPass).color}">
                                {{ getIsPassObj(scope.row.IsPass).label }}
                                </span>
                            </template>
                            <!-- 积分 -->
                            <template slot="Integral" slot-scope="scope">{{((scope.row.Integral||scope.row.Integral==0)&&scope.row.Integral!='无')?'+'+scope.row.Integral:'无'}}</template>
                            <!-- 考试时间 -->
                            <template slot="ExamTime" slot-scope="scope">{{scope.row.ExamTime | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                            <!-- 用时 -->
                            <template slot="When" slot-scope="scope">{{scope.row.When||'0'}}分钟</template>

                            <!-- 考试类型 -->
                            <template slot="TestModeType" slot-scope="scope">
                                <span class="item-status" :style="{backgroundColor: getTestModeTypeObj(scope.row.TestModeType).bgColor,
                                color: getTestModeTypeObj(scope.row.TestModeType).color}">
                                {{ getTestModeTypeObj(scope.row.TestModeType).label }}
                                </span>
                            </template>
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                    <template slot="KeyWords">
                                        <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable
                                        v-model.trim="listQuery.KeyWords" placeholder="搜索人员姓名/工号"></el-input>
                                    </template>
                                    <!-- 表格批量操作区域 -->
                                    <template slot="isPass">
                                        <el-select v-model="listQuery.isPass" placeholder="请选择">
                                            <el-option label="及格" :value="1"></el-option>
                                            <el-option label="不及格" :value="2"></el-option>
                                        </el-select>
                                    </template>
                                    <template slot="examTime">
                                        <el-date-picker v-model="listQuery.examTime" type="daterange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                    </template>
                                    <template slot="isFiltration">
                                        <el-checkbox v-model="listQuery.isFiltration">只看必考人员名单</el-checkbox>
                                    </template>
                                </app-table-form>
                            </template>
                        </app-table>
                    </div>
                    <!----------------------------------------- 分页 ------------------------------------------->
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange"
                    @size-change="handleSizeChange" />
                </div>
            </template>
            <template slot="footer">
                <app-button :buttonType="2" @click="handleClose"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>

import indexPageMixin from "@/mixins/indexPage";
import { vars } from '../common/vars'
import * as StudyRecordApi from '@/api/knowledge/StudyRecord'
export default {
    /**名称 */
    name: "test-paper-examination-details",
    mixins: [indexPageMixin],
    props: {
        checkedNode: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            listLoading: false,
            passNotTypes: vars.testPaperEnum.passNotTypes,
            isPassTypes: vars.learningRecordsEnum.isPassTypes,
            testModeTypeTypes: vars.learningRecordsEnum.testModeTypeTypes,
            formModel: {
                ExaminationPaperName: '', // 考卷名称
                OwnerEmployeeNames: '', // 出卷人
            },

            /******************* 表格 *******************/
            layoutMode: 'simple',
            dataSourceList: [],
            total: 0,
            listQuery: {
                PageIndex: 1,
                PageSize: 10,
                KeyWords: '',
                examTime: [],
                isPass: null,
                isFiltration: null,
                type: 2,
            },
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                { prop: "isPass", label: "是否及格" },
                { prop: "examTime", label: "考试时间" },
                { prop: "isFiltration", label: "名单过滤" },
            ],
            tabColumns: [
                {attr: {prop: "EmployeeName",label: "姓名", showOverflowTooltip: true,}},
                {attr: {prop: "EmployeeNo",label: "工号", showOverflowTooltip: true,}},
                {attr: {prop: "EmployeeJobTitle",label: "职位", showOverflowTooltip: true,}},
                {attr: {prop: "TestModeType",label: "考试类型",sortable: "custom"},slot: true},
                // {attr: {prop: "ExamResults",label: "考试成绩",sortable: "custom"},slot: true},
                {attr: {prop: "IsPass",label: "是否及格",sortable: "custom"},slot: true},
                {attr: {prop: "Integral",label: "积分",sortable: "custom"},slot: true},
                {attr: {prop: "ExamTime",label: "考试时间",sortable: "custom"},slot: true},
                {attr: {prop: "When",label: "用时",sortable: "custom"},slot: true},
            ],

        };
    },
    computed: {
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "添加分类";
            } else if (this.dialogStatus == "update") {
                return "编辑分类";
            } else if (this.dialogStatus == "detail") {
                return "分类详情";
            }
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.setDetail();
                }
            },
            immediate: true
        }
    },
    created() {
    },
    mounted() { },
    methods: {
        getTestModeTypeObj(val) {
            return this.testModeTypeTypes.find(
                s => s.value == val
            ) || {};
        },
        setDetail(){
            this.formModel = Object.assign({}, this.formModel, this.checkedNode);
            this.dataSourceList = []
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        // 是否及格 文字和颜色转换
        getPassNotTypesObj(val) {
            return this.passNotTypes.find(
                s => s.value == val
            ) || {};
        },
        getIsPassObj(val) {
            return this.isPassTypes.find(
                s => s.value == val
            ) || {};
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        onResetSearch() {
            // this.listQuery.LastUpdateTime = []
            // this.listQuery.QuestionType = null
            // this.listQuery.KeyWords = ""
            this.listQuery = this.$options.data().listQuery
            this.getList();
        },
        // 表格排序按钮搜索
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
        /**获取数据 */
        getList() {
            let self = this, postData = JSON.parse(JSON.stringify(self.listQuery));
            if (!self.checkedNode || !self.checkedNode.Id) {
                return false;
            }
            if (!postData.isFiltration) {
                delete postData.isFiltration
            }
            if (!postData.isPass) {
                delete postData.isPass
            }
            postData.examinationPaperId = self.checkedNode.Id;
            if (postData.examTime.length > 0) {
                postData.examTimeStart = postData.examTime[0] == postData.examTime[1] ? postData.examTime[0] + ' 00:00:00' : postData.examTime[0]
                postData.examTimeEnd = postData.examTime[0] == postData.examTime[1] ? postData.examTime[1] + ' 23:59:59' : postData.examTime[1]
            }
            delete postData.examTime
            postData = self.assignSortObj(postData);
            self.listLoading = true;
            StudyRecordApi.getList(postData).then(res => {
                let typeName = self.listQuery.type == 1 ? 'StudyRecordDtoModelList' : 'ExamRecordDtoModelList';
                self.dataSourceList = res[typeName].Items;
                // self.tabColumns = self[`tabColumns${self.listQuery.type}`]
                self.total = res[typeName].Total;
                self.listLoading = false;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },


        /**提交方法 */
        handleButtonClick() {
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.wrapperBody{
    height: 590px;
}
.text_nowrap{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    white-space: nowrap;
}
</style>


