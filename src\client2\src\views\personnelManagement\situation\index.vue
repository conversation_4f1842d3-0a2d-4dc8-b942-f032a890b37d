<template>
    <div class="app-container">
        <div class="bg-white">
            <div class="pageWrapper">
                <div class="product-list">
                    <div class="treeBox">
                        <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                        <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node }">
                                <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                            </span>
                        </el-tree>
                    </div>
                </div>
                <div class="content-wrapper __dynamicTabContentWrapper">
                    <!-- <page-title :title="departmentInfo"></page-title> -->
                    <div class="tab-form-wrapper">
                        <app-table-form :label-width="'100px'" :smWidth='18' :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch"  :layoutMode='"simple"'>
                            <template slot="FuzzySearchString">
                                <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.FuzzySearchString" placeholder="搜索姓名/工号/职位"></el-input>
                            </template>
                            <template slot="AttendanceStatusList">
                                <el-select style="width: 500px;" clearable v-model="listQuery.AttendanceStatusList" multiple placeholder="">
                                    <el-option
                                    v-for="item in status"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                            <template slot="NoWorkArrange">
                                <el-checkbox v-model="listQuery.NoWorkArrange">暂无工作安排</el-checkbox>
                            </template>

                            <!-- <template slot="btnsArea">
                                <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
                            </template> -->
                        </app-table-form>
                    </div>
                    <div class="content">
                        <div class="list-item-wrapper" v-loading='listLoading'>
                            <noData v-if="tabDatas.length == 0"></noData>
                            <el-row>
                                <el-col :span="6" v-for="(item, idx) in tabDatas" :key="idx">
                                  
                                <el-card shadow="hover" class="item-wrapper-card">
                                    
                                  <div class="item-wrapper">
                                        <div class="persion-wrapper">
                                            <div class="avatar">
                                                <img class="img-avatar-shadow" :src="item.AvatarPath" alt="">
                                            </div>
                                            <div class="info-wrapper">
                                                <div class="info" @click="handlePersonDialog(item)">
                                                    <div class="name omit" :title="item.Name">{{ item.Name }}</div>
                                                    <div class="title omit" :title="item.JobName">{{ item.JobName }}</div>
                                                </div>
                                                <statusList v-if="item.DayPersonalTimecardRecord" :recordObj='item.DayPersonalTimecardRecord'></statusList>
                                            </div>
                                        </div>
                                        <div class="task-list-wrapper">
                                            <div class="task-list-title">工作计划</div>
                                            <div class="task-list">
                                                <noData v-if="item.WorkTaskList.length == 0" text='暂无工作安排'></noData>
                                                <div class="task-list-item" v-for="(t, idx2) in item.WorkTaskList" :key="idx2">
                                                    <div class="dot" style="background: #5c9cff;" v-if="isPrincipalEmployee(t, item.EmployeeId)">责</div>
                                                    <div class="dot" style="background: #e99a15;" v-else-if="isParticipantEmployee(t, item.EmployeeId)">参</div>
                                                    <div class="task-name omit" :title="`${idx2 + 1}、${t.TaskName}`">
                                                        <a href="javascript:void(0);" @click="handleReviewTask(t)">{{ idx2 + 1 }}、{{ t.TaskName }}</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                      
                                </el-card>

                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </div>
        </div>

        <!-- 通用任务 -->
        <createCommonTask
            v-if="dialogFormVisible && commTaskId"
            @closeDialog="() => dialogFormVisible = false"
            :dialogFormVisible="dialogFormVisible"
            dialogStatus="detail"
            :id='commTaskId'
        ></createCommonTask>

        <!-- 项目任务 -->
        <taskEdit
            v-if="taskEditVisible"
            ref="taskEditRef"
            :project="{Id: currentProjectId}"
            dialogStatus="detail"
            @closeDialog="() => taskEditVisible = false"
            :dialogFormVisible="taskEditVisible"
            :taskId="taskId"
        ></taskEdit>

        <persionDetail
            v-if='dialogPersonFormVisible'
            @closeDialog='closePersonDialog' 
            @saveSuccess='() => {}'
            :dialogFormVisible='dialogPersonFormVisible'
            :dialogStatus='dialogPersonStatus' 
            :id='empId'
            :empId='empId'
        ></persionDetail>
    </div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import vButtonList from '@/views/common/buttonList'
import * as systemEmployee from "@/api/personnelManagement/systemEmployee"
import { vars } from '../../personnelManagement/attendanceMgmt/vars'
import { getUserInfo } from "@/utils/auth";
import createCommonTask from '../../workbench/workPlan/planList/createCommonTask'
import taskEdit from "../../projectDev/projectMgmt/workbench/task/taskEdit"
import noData from "@/views/common/components/noData"
import persionDetail from './persionDetail'
import statusList from './statusList'
import mixins from './mixins'

export default {
    name: "situation-index",
    mixins: [indexPageMixin, mixins],
    components: {
        vButtonList,
        createCommonTask,
        taskEdit,
        noData,
        persionDetail,
        statusList,
    },
    props: {},
    filters: {
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },

        
        

    },
    computed: {

    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.DepartmentId = val.Id;
                    // this.selectDepartmentName = val.DepartmentName;
                    this.handleFilter()
                }
            },
            immediate: true,
        },
    },
    created() {
        let currentYear = (new Date()).getFullYear()
        this.years = Array.from(Array(5), (v, k) => {
            return {
                value: currentYear - k,
                label: currentYear - k
            }
        })
        // for(let i = currentYear - 4; i <= currentYear; i++) {
        //     this.years.push({
        //         value: i,
        //         label: i
        //     })
        // }

        this.getDepartments();
    },
    data() {
        return {
            status: [
                { label: '出勤', value: 1},
                { label: '未出勤', value: 2},
                { label: '休息', value: 3},
                { label: '请假', value: 4},
                { label: '加班', value: 5},
                { label: '出差', value: 6},
                { label: '外出', value: 7},
            ],
            dialogFormCheckRecordVisible: false,

            years: [],
            // monList: Array.from(Array(12), (v,k) => {
            //     return {
            //         value: k + 1,
            //         label: `${k+1}月`
            //     }
            // }),
            epKeys: [],

            // departmentInfo: "",
            // selectDepartmentName: "",

            filterText: "",

            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            tableSearchItems: [
                { prop: "FuzzySearchString", label: "", mainCondition: true },
                { prop: "AttendanceStatusList", label: "考勤状态"},
                { prop: "NoWorkArrange", label: "工作计划"},
                
                
                // { prop: "Year", label: "年份"},
                // { prop: "Name", label: "姓名"},
                // // {
                // //     prop: "Mobile",
                // //     label: "手机号"
                // // },
                // { prop: "Number", label: "工号" },
            ],

            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            listLoading: false,
            listQuery: {
                DepartmentId: "",
                // Name: "",
                FuzzySearchString: "",
                AttendanceStatusList: [],
                NoWorkArrange: null,
                // Mobile: "",
                // Number: '',
                // Year: (new Date()).getFullYear(),
                // Month: (new Date()).getMonth() + 1
            },
            tabDatas: [], //原始数据
            total: 0,

            currentProjectId: '', //当前项目id，用于新增任务
            taskEditVisible: false,
            taskId: '',

            //通用任务
            dialogFormVisible: false,
            commTaskId: '',

            dialogPersonStatus: 'detail',
            dialogPersonFormVisible: false,
            empId: '',

        };
    },
    methods: {
        handlePersonDialog(item) {
            this.empId = item.EmployeeId
            this.dialogPersonFormVisible = true;
        },
        closePersonDialog() {
            this.dialogPersonFormVisible = false
        },
        isPrincipalEmployee(task, empId) {
            if(task && task.PrincipalEmployeeIdList && empId) {
                return task.PrincipalEmployeeIdList.findIndex(s => s == empId) > -1
            }
            return false
        },
        isParticipantEmployee(task, empId) {
            if(task && task.ParticipantEmployeeIdList && empId) {
                return task.ParticipantEmployeeIdList.findIndex(s => s == empId) > -1
            }
            return false
        },
        handleReviewTask(task) {
            if(task.WorkPlanTaskType == 1) {
                this.commTaskId = task.TaskId
                this.dialogFormVisible = true;
            }else if(task.WorkPlanTaskType == 2) {
                this.currentProjectId = task.ProjectId
                this.taskId = task.TaskId
                this.taskEditVisible = true;
            }
        },
        // //项目任务详细
        // handleTaskDialog(projectId, taskId) {
        //     this.currentProjectId = projectId
        //     this.taskId = taskId
        //     this.taskEditVisible = true;
        // },

        getStatusColor(val) {
            let obj = vars.timecardStatus.find(s => s.value == val)
            if (obj) {
                return obj.color
            }
            return ''
        },
        auth(btnDomId) {
            return btnDomId && this.topBtns.findIndex(s => s.DomId.toLowerCase() == btnDomId.toLowerCase()) > -1
        },
        /**表头部点击 */
        // onBtnClicked: function (type) {
        //     switch (type) {
        //         default:
        //         break;
        //     }
        // },



        //获取成员列表
        getList() {
            if (this.checkedNode) {
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                // postData = this.assignSortObj(postData);

                if(postData.NoWorkArrange !== true) {
                    delete postData.NoWorkArrange
                }

                // postData.DayTime = '2021-06-16'
                // postData.DayTime = '2021-03-02'

                this.listLoading = true;
                systemEmployee.getOverviewPage(postData).then((res) => {
                    this.listLoading = false;
                    this.tabDatas = res.Items.map(s => {
                        this.initDatas(s)
                        
                        return s
                    })
                    // this.departmentInfo = this.selectDepartmentName;
                    this.total = res.Total;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
            }
        },
        
        onResetSearch() {
            // this.listQuery.Name = "";
            // // this.listQuery.Mobile = "";
            // this.listQuery.Number = ''
            this.listQuery.FuzzySearchString = ""
            this.listQuery.AttendanceStatusList = []
            this.listQuery.NoWorkArrange = null
            this.getList();
        },

        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            systemDepartment
                .getListByCondition(this.departmentListQuery)
                .then((res) => {
                this.treeDatas = listToTreeSelect(res);

                if (this.treeDatas && this.treeDatas.length > 0) {
                    this.treeDatas.forEach(v => {
                    this.epKeys.push(v.Id);
                    // if(v.children.length>0){
                    //     v.children.forEach(v1 => {
                    //         this.epKeys.push(v1.Id);
                    //     })
                    // }

                    })
                }
                //如果首次加载问价夹树（没有选中），默认选中根节点
                if (!this.checkedNode) {
                    this.setDefaultChecked();
                }
                this.treeLoading = false;
            });
        },

        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },

    },
};
</script>


<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .tab-form-wrapper{
            padding: 10px;
        }
        .content {
            flex: 1;
            overflow-y: auto;
            .list-item-wrapper{
              .item-wrapper-card{
                    height: 220px;
                   // padding: 10px;
                    margin: 10px;
                 .item-wrapper{
                    height: 220px;
                    display: flex;
                    flex-direction: column;
                   //border: 1px solid #e3e3e3;
                   // padding: 10px;
                   // margin: 10px;
                    //box-sizing: border-box;
                    //box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
                    //border-radius: 5px;
                    .persion-wrapper{
                        display: flex;
                        padding-bottom: 4px;
                        border-bottom: 1px solid #e3e3e3;
                        .avatar{
                            margin-right: 10px;
                            img{
                                width: 50px;
                                height: 50px;
                                border-radius: 50%;
                            }
                        }
                        .info-wrapper{
                            flex: 1;
                            // display: flex;
                            // flex-direction: column;
                            // justify-content: space-around;
                            // padding: 4px 0;
                            .info{
                                display: flex;
                                margin-bottom: 4px;
                                cursor: pointer;
                                .name{
                                    font-weight: bold;
                                    margin-right: 10px;
                                }
                                .title{
                                    flex: 1;
                                    width: 0;
                                }
                            }
                        }
                    }
                    .task-list-wrapper{
                        flex: 1;
                        height: 0;
                        padding: 4px;
                        margin-top: 4px;
                        display: flex;
                        flex-direction: column;
                        .task-list-title{
                            margin-bottom: 6px;
                        }
                        .task-list{
                            flex: 1;
                            overflow-y: auto;
                            .task-list-item{
                                display: flex;
                                align-items: center;
                                margin-top: 4px;
                                .dot{
                                    height: 18px;
                                    width: 18px;
                                    text-align: center;
                                    line-height: 14px;
                                    background: red;
                                    border-radius: 50%;
                                    color: #fff;
                                    padding: 2px;
                                    margin-right: 4px;
                                }
                                .task-name{
                                    flex: 1;
                                    a:hover{
                                        text-decoration: underline;
                                    }
                                }
                            }
                        }
                    }
                }

              }

                
                
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}


</style>
