<template>
    <div v-if="attrObj" style="margin-top: 5px; margin-bottom: 10px;">
        <div>
            <!-- input -->
            <div v-if='attrObj.type == 3'>
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    <el-input v-model="attrObj.value" @input="handleChange(attrObj.value)"></el-input>
                </dynamic-ctrl-wrap>
            </div>
            <!-- Text Area -->
            <div v-else-if="attrObj.type == 4">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    <el-input type="textarea" :rows="3" placeholder="" v-model="attrObj.value" @input="handleChange(attrObj.value)"></el-input>
                </dynamic-ctrl-wrap>
            </div>
            <!-- select -->
            <div v-else-if="attrObj.type == 5">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    <el-select v-model="attrObj.value" @change='handleChange(attrObj.value)' clearable>
                        <el-option v-for="item in attrObj.options" :key="item.value" :value='item.value' :label="item.label"></el-option>
                    </el-select>
                </dynamic-ctrl-wrap>
            </div>
            <!-- 单选 -->
            <div v-else-if='attrObj.type == 6'>
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    <el-radio @change='handleChange(attrObj.value)' v-model="attrObj.value" :label="i.value" v-for="(i, idx) in attrObj.options" :key="idx">{{ i.label }}</el-radio>
                    <el-button v-if="attrObj.value == 1000" type="text">添加options</el-button>
                    <el-input v-if="attrObj.value == 1001"></el-input>
                </dynamic-ctrl-wrap>
            </div>
            <!-- button -->
            <div v-else-if="attrObj.type == 7">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    <el-select v-model="attrObj.value" @change='handleChange(attrObj.value)'>
                        <el-option v-for="item in attrObj.options" :key="item.value" :value='item.value' :label="item.label"></el-option>
                    </el-select>
                </dynamic-ctrl-wrap>
            </div>
            <!-- checkbox -->
            <div v-else-if="attrObj.type == 8">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    <!-- {{attrObj.value}} -->
                    <el-checkbox-group v-model="attrObj.value" @change='handleChange(attrObj.value)'>
                        <el-checkbox v-for="(item, idx) in attrObj.options" :label="item.value" :key="idx">{{ item.label }}</el-checkbox>
                    </el-checkbox-group>
                </dynamic-ctrl-wrap>
            </div>
            <!-- table -->
            <div v-else-if="attrObj.type == 10">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    table
                </dynamic-ctrl-wrap>
            </div>
            <div v-else-if="attrObj.type == 10001">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    {{ attrObj.value }}
                    <key-value-list v-model='attrObj.value' @change='handleChange(attrObj.value)'></key-value-list>
                </dynamic-ctrl-wrap>
            </div>
            <div v-else-if="attrObj.type == 10002">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    {{ attrObj.extendConf }}
                    <selector v-model='attrObj.extendConf' @change='handleChange(attrObj.extendConf)'></selector>
                </dynamic-ctrl-wrap>
            </div>
            <div v-else-if="attrObj.type == 10003">
                <dynamic-ctrl-wrap :attrObj='attrObj'>
                    {{ attrObj.value }}
                    <table-row-btns v-model="attrObj.value" @change="handleChange(attrObj.value)"></table-row-btns>
                </dynamic-ctrl-wrap>
            </div>
            <div v-else>
                未知控件
            </div>
        </div>
    </div>
</template>

<script>
import mixin from '../dynamicFormCommon/mixins'
import DynamicCtrlWrap from './dynamicCtrlWrap'
import KeyValueList from './components/keyValueList'
import Selector from './components/selector'
import TableRowBtns from './components/tableRowBtns'
import * as cdc from '@/api/componentDatasourceConfiguration'

export default {
    name: 'dynamic-ctrl',
    mixins: [mixin],
    model: {
        prop: 'value',
        event: 'change'
    },
    components: {
        DynamicCtrlWrap,
        KeyValueList,
        Selector,
        TableRowBtns,
    },
    props: {
        value: {
            type: [Number, String, Boolean, Array, Object],
        },
        //控件类型
        ctrlObj: {
            type: Object,
            required: true
        },
        //控件属性
        attr: {
            type: Object,
            required: true
        },

    },
    computed: {
        
    },
    watch: {
        attr(val) {
            // this.attrObj = JSON.parse(JSON.stringify(val))

            // this.getDataSource()
            this.initAttr(val)
        },
    },
    // watch: {
    //     attr: {
    //         handler(val, oldVal) { 
    //             // this.attrObj = this.initAttr(JSON.parse(JSON.stringify(val)))
    //             // this.attrObj = JSON.parse(JSON.stringify(val))
    //         },
    //         deep: true,
    //         immediate: true,
    //     },
    // },
    created() {
        this.initAttr(this.attr)
    },
    data() {
        return {
            attrObj: {}
        }
    },
    methods: {
        initAttr(attr) {
            this.attrObj = JSON.parse(JSON.stringify(attr))
            this.getDataSource()
        },
        handleChange(newval) {
            this.$emit('change', newval)
        },
        getAttrObjByAttrName(attrName) {
            return this.ctrlObj.attrs.find(s => s.attrName == attrName)
        },
        getDataSource() {
            if(this.attrObj.type == 5 && this.attrObj.attrName == 'dataSource') {
                cdc.getList({}).then(res => {
                    this.attrObj.options = res.Items.map(s => {
                        return {
                            value: s.ComponentDatasourceConfigurationId,
                            label: s.ConfigurationKey
                        }
                    })
                })
            }
            
        },

    },
}
</script>