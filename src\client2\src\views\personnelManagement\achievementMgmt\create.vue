<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" className="clear-default-height-auto" v-bind="$attrs" v-on="$listeners" :width='800'>
      <template slot="body">
        <el-form :rules="rules" style="height: 100%; overflow-y: auto;" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <div class="wrapper" v-loading='loading'>
              <div style="border-bottom: 1px solid #DCDFE6;">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="考核年份" prop="Year">
                      <el-date-picker v-model="formData.Year" :disabled="!editable" type="year" value-format="yyyy" placeholder=""></el-date-picker>
                      <el-select :disabled="!editable" v-model="formData.HalfYearType" placeholder="">
                        <el-option v-for="item in yearTypeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="考核负责人" prop="PrincipalEmployeeList">
                      <emp-selector
                        :readonly="!editable"
                        key="ccusers"
                        :showType="2"
                        :multiple="true"
                        :beforeConfirm='handlePrincipalEmployeeBeforeConfirm'
                        :list="formData.PrincipalEmployeeList"
                        @change="handleChangePrincipalUsers"
                      ></emp-selector>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="年度目标" prop="YearGoals">
                      <el-input maxlength="500" :disabled="!editable" type="textarea" :rows="4" v-model="formData.YearGoals"></el-input>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="24">
                    <el-form-item label="考核部门" prop="AppraiseDepartmentId">
                      <el-cascader v-model="formData.AppraiseDepartmentId" :options="depts" :disabled="!editable" :props="{ checkStrictly: true, emitPath: false }" clearable></el-cascader>
                    </el-form-item>
                  </el-col> -->
                  <!-- <el-col :span="24">
                    <el-form-item label="团队目标" prop="TeamGoals">
                      <el-input maxlength="500" :disabled="!editable" type="textarea" :rows="4" v-model="formData.TeamGoals"></el-input>
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="24">
                    <el-form-item label="相关附件" prop="AttachmentList">
                      <span v-if="formData.AttachmentList.length == 0 && !editable">无</span>
                      <app-uploader v-else :readonly="!editable" accept="all" :fileType="3" :max="10000"
                  :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"
                  @change="handleFilesUpChange" ref="appuploader"></app-uploader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="申诉对象" prop="AppealObjectEmployeeList">
                      <emp-selector
                        :readonly="!editable"
                        key="ccusers"
                        :showType="2"
                        :multiple="true"
                        :beforeConfirm='handleAppealObjectEmployeeBeforeConfirm'
                        :list="formData.AppealObjectEmployeeList"
                        @change="handleChangeAppealObjectUsers"
                      ></emp-selector>
                    </el-form-item>
                  </el-col>
                 
                </el-row>
              </div>
              <div>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="考核部门" class="st-el-form-item st-row">
                      已添加（{{ formData.AppraisePlanList ? formData.AppraisePlanList.length : 0 }}）     <el-button v-if="editable" type="text" @click="treeOpenDialog">添加部门</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <div style="height: 200px;" v-show="!loading && formData.AppraisePlanList.length == 0">
                  <no-data></no-data>
                </div>

                <el-card v-for="(item, idx) in formData.AppraisePlanList" :key="idx">
                  <el-col :span="24">
                    <el-form-item label="部门名称">
                      <div class="dep-wrapper">
                        <div class="dep-name">
                          {{ item.DepartmentName }}
                        </div>
                        <div v-if="editable" class="del-btn-wrapper" style="color: red;" @click="handleRemove(idx)">
                          <i class="el-icon-delete"></i>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="终审人" :prop="'AppraisePlanList.' + idx + '.FinalEmployeeList'" :rules="{required: true, message: '终审人不能为空', trigger: 'change'}">
                      <emp-selector
                        :readonly="!editable"
                        key="ccusers"
                        :showType="2"
                        :multiple="true"
                        :beforeConfirm='handleFinalBeforeConfirm'
                        :list="item.FinalEmployeeList"
                        @change="(users) => handleFinalChangeObjectUsers(users, item)"
                      ></emp-selector>
                    </el-form-item>
                  </el-col>


                  <el-col :span="24">
                    <el-form-item label="考核主管" :prop="'AppraisePlanList.' + idx + '.PrincipalEmployeeList'" :rules="{required: true, message: '考核主管不能为空', trigger: 'change'}">
                      <emp-selector
                        :readonly="!editable"
                        key="ccusers"
                        :showType="2"
                        :multiple="true"
                        :beforeConfirm='handlePrincipalBeforeConfirm'
                        :list="item.PrincipalEmployeeList"
                        @change="(users) => handlePrincipalChangeObjectUsers(users, item)"
                      ></emp-selector>
                    </el-form-item>
                  </el-col>

                  <!-- <el-col :span="24">
                    <el-form-item label="团队目标">
                      <el-input :title="item.TeamGoals" type="textarea" :rows="5" maxlength="2000" v-model="item.TeamGoals" clearable :disabled="!editable"></el-input>
                    </el-form-item>
                  </el-col> -->
                  
                  <el-col :span="24">
                    <el-form-item label="被考核人" :prop="'AppraisePlanList.' + idx + '.ObjectEmployeeList'" :rules="{required: true, message: '被考核人不能为空', trigger: 'change'}">
                      <emp-selector
                        :readonly="!editable"
                        key="ccusers"
                        :showType="2"
                        :multiple="true"
                        :list="item.ObjectEmployeeList"
                        @change="(users) => handleObjectChangeObjectUsers(users, item)"
                      ></emp-selector>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="24">
                    <el-form-item label="相关附件">
                      <span v-if="item.AttachmentList.length == 0 && !editable">无</span>
                      <app-uploader v-else :readonly="!editable" accept="all" :fileType="3" :max="10000"
                  :value="item.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"
                  @change="(files) => handleRowFilesUpChange(files, item)" ref="appuploader"></app-uploader>
                    </el-form-item>
                  </el-col> -->
                  <!-- <el-col :span="24">
                    <el-form-item label="集体公示">
                      <el-switch v-model="item.IsPublic" :disabled='!editable' active-color="#13ce66" inactive-color="#ff4949"></el-switch>
                    </el-form-item>
                  </el-col> -->
                </el-card>

                <!-- <el-row v-for="(item, idx) in formData.AppraisePlanList" :key="idx">


                </el-row> -->
              </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- <el-button @click="handleClose">取消</el-button>
        <el-button @click="createData" type="primary" v-show="editable">确认</el-button> -->

        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>

    <dept-selector v-if="treeDialogFormVisible" :checkedList='chedkedList' :disabled='treeDisabledBtn' :dialogFormVisible="treeDialogFormVisible" @closeDialog="treeCloseDialog" @saveSuccess="treeSaveSuccess">

    </dept-selector>
  </div>
</template>

<script>
  import empSelector from '@/views/common/empSelector'
  import { appraiseTypeEnum, publicPeriodSetEnum, autoEndTypeEnum, yearTypeEnum } from "./enum";
  // import { listToTreeSelect } from "@/utils";
  import * as ach from "@/api/personnelManagement/achievementMgmt";
  import * as systemEmployee from "@/api/personnelManagement/systemEmployee";
  import * as appraisePlanYear from "@/api/personnelManagement/appraisePlanYear";
  import noData from "@/views/common/components/noData";
  import deptSelector from './tree'
  export default {
    name: "ach-mgmt-create",
    directives: {},
    components: {
      // tabs,
      // tags,
      empSelector,
      deptSelector,
      noData,
    },
    mixins: [],
    props: {
      dialogStatus: {
        //create、update、reCreate（再次创建）
        type: String
      },
      id: {
        type: String,
        default: ""
      },
    },
    watch: {
      "$attrs.dialogFormVisible": {
        handler(val) {
          if (val) {
            this.resetFormData();


            if (this.dialogStatus != "create" && this.id) {
              this.getDetail();
            }
          }
        },
        immediate: true
      }
    },
    computed: {
      chedkedList() {
        if(this.formData.AppraisePlanList) {
          return this.formData.AppraisePlanList.map(s => s.DepartmentId) || []
        }
        return []
      },
      //不等于详情页面可编辑
      editable() {
        return this.dialogStatus != "detail";
      },
      pageTitle() {
        if (this.dialogStatus == "create" || this.dialogStatus == "reCreate") {
          return "创建绩效考核";
        } else if (this.dialogStatus == "update") {
          return "编辑绩效考核";
        } else if (this.dialogStatus == "detail") {
          return "绩效考核详情";
        }
      },
    },
    created() {
      this.rules = this.initRules(this.rules);
      // this.getDepts()
    },
    data() {
      return {
        appraiseTypeEnum,
        publicPeriodSetEnum,
        autoEndTypeEnum,
        yearTypeEnum,
        loading: false,
        disabledBtn: false,
        treeDisabledBtn: false,
        rules: {
          // AppraiseType: {fieldName: "考核类型", rules: [{ required: true, trigger: 'change' }]},
          // AppraiseDepartmentId: {fieldName: "考核部门", rules: [{ required: true, trigger: 'change' }]},
          // TeamGoals: {fieldName: "团队目标", rules: [{ required: true }]},
          FinalEmployeeList: {fieldName: "终审人", rules: [{ required: true, trigger: 'change' }]},
          PrincipalEmployeeList: {fieldName: "负责人", rules: [{ required: true, trigger: 'change' }]},
          ObjectEmployeeList: {fieldName: "考核对象", rules: [{ required: true, trigger: 'change' }]},
          AppealObjectEmployeeList: {fieldName: "申诉对象", rules: [{ required: true, trigger: 'change' }]},
        },
        labelWidth: "100px",
        formData: {
          Id: "", //需求ID
          Year: (new Date()).getFullYear().toString(),
          HalfYearType: 1,
          AppraiseType: 1, //考核类型
          Name: "", //考核计划名称
          YearGoals: "", //考核描述
          PublicPeriodSet: 2, //公示期设置
          AutoEndType: 2,
          // AppraiseDepartmentId: '',// 考核部门
          // TeamGoals: '',//团队目标
          AttachmentIdList: [], // 附件ID列表
          AttachmentList: [],
          PrincipalEmployeeList: [],
          FinalEmployeeList: [],
          ObjectEmployeeList: [],
          AppealObjectEmployeeList: [],
          AppraisePlanList: [],
        },
        treeDialogFormVisible: false,
        // depts: [],
      };
    },
    methods: {
      handleRemove(idx) {
        this.$confirm('是否确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.formData.AppraisePlanList.splice(idx, 1)
        }).catch(() => {
       
        });
      },
      // getDepts() {
      //   let paramData = {PageSize: 100000, PageIndex: 1}
      //   ach.getListDept(paramData).then(res => {
      //     let datas = (res.Items || []).map(s => {
      //       s.value = s.Id
      //       s.label = s.DepartmentName
      //       return s
      //     })
      //     this.depts = listToTreeSelect(datas);
      //   })
      // },
      treeOpenDialog() {
        this.treeDialogFormVisible = true
      },
      treeCloseDialog() {
        this.treeDialogFormVisible = false
      },
      treeSaveSuccess(datas) {
        
        if(datas && datas.length > 0) {
          this.treeDisabledBtn = true
          //新选择的全部部门
          let newDeptIds = datas.map(s => s.Id)
          //已存在的部门
          let oldList = this.formData.AppraisePlanList.filter(s => newDeptIds.findIndex(n => n == s.DepartmentId) > -1) || []
          //需要新增的部门
          let addDeptIds = newDeptIds.filter(s => this.formData.AppraisePlanList.map(s => s.DepartmentId).findIndex(n => n == s) == -1)

          if(addDeptIds && addDeptIds.length > 0) {
            systemEmployee.systemEmployee(addDeptIds).then(res => {
              this.treeDisabledBtn = false
              let olds = JSON.parse(JSON.stringify(oldList)) || []
              let news = res.map(s => {
                return {
                  DepartmentId: s.DepartmentId,//部门名称
                  DepartmentName: s.DepartmentName,
                  PrincipalEmployeeList: s.EmployeeList.filter(s => s.IsPrincipal) || [],//考核主管
                  FinalEmployeeList: s.EmployeeList.filter(s => s.IsPrincipal) || [], //终审人
                  TeamGoals: '', //团队目标
                  ObjectEmployeeList: s.EmployeeList || [],//被考核人
                  AttachmentList: [],//相关附件
                  // IsPublic: true, //集体公示
                }
              }) || []
              this.formData.AppraisePlanList = olds.concat(news)

              this.treeCloseDialog()
            }).catch(err => {
              this.treeDisabledBtn = false
            })
          }else{
            this.treeDisabledBtn = false
            this.formData.AppraisePlanList = JSON.parse(JSON.stringify(oldList)) || []
            this.treeCloseDialog()
          }
        }else{
          this.formData.AppraisePlanList = []
          this.treeCloseDialog()
        }
      },
      handlePrincipalEmployeeBeforeConfirm(users) {
        if(users && users.length > 3) {
          this.$message({
              message: '考核负责人不得超过3人',
              type: 'error'
          })
          return false
        }
        return true
      },
      handleAppealObjectEmployeeBeforeConfirm(users) {
        if(users && users.length > 3) {
          this.$message({
              message: '申述对象不得超过3人',
              type: 'error'
          })
          return false
        }
        return true
      },
      
      handleFinalBeforeConfirm(users) {
        if(users && users.length > 3) {
          this.$message({
              message: '终审人不得超过3人',
              type: 'error'
          })
          return false
        }
        return true
      },
      handlePrincipalBeforeConfirm(users) {
        if(users && users.length > 3) {
          this.$message({
              message: '考核人不得超过3人',
              type: 'error'
          })
          return false
        }
        return true
      },
      resetFormData() {
        let temp = {
          Id: "", //ID
          Year: (new Date()).getFullYear().toString(),
          HalfYearType: 1,
          AppraiseType: 1, //考核类型
          Name: '', //考核计划名称
          YearGoals: "", //考核描述
          PublicPeriodSet: 2, //公示期设置
          AutoEndType: 2,
          // AppraiseDepartmentId: '',// 考核部门
          // TeamGoals: '',//团队目标
          AttachmentIdList: [], // 附件ID列表
          AttachmentList: [],
          PrincipalEmployeeList: [],
          FinalEmployeeList: [],
          ObjectEmployeeList: [],
          AppealObjectEmployeeList: [],
          AppraisePlanList: [],
        };
        this.formData = Object.assign({}, this.formData, temp);
      },
      createData() {
        
        this.$refs.formData.validate(valid => {
          if(valid) {
            let postData = JSON.parse(JSON.stringify(this.formData));

            if(postData.AppraisePlanList.length == 0) {
              this.$message.error(`请选择考核部门`);
              return false
            }

            //提交数据保存
            postData.AttachmentIdList = postData.AttachmentList.map(s => s.Id);
            postData.PrincipalEmployeeIdList = postData.PrincipalEmployeeList.map(s => s.EmployeeId) || []
            postData.FinalEmployeeIdList = postData.FinalEmployeeList.map(s => s.EmployeeId) || []
            postData.ObjectEmployeeIdList = postData.ObjectEmployeeList.map(s => s.EmployeeId) || []
            postData.AppealObjectEmployeeIdList = postData.AppealObjectEmployeeList.map(s => s.EmployeeId) || []

            if (this.dialogStatus == "create") {
              delete postData.Id;
            }

            postData.AppraisePlanList = postData.AppraisePlanList.map(s => {
              let result = {
                Id: s.Id,
                DepartmentId: s.DepartmentId,//部门名称
                PrincipalEmployeeIdList: s.PrincipalEmployeeList.map(n => n.EmployeeId) || [],//考核主管
                FinalEmployeeIdList: s.FinalEmployeeList.map(s => s.EmployeeId) || [], //终审人
                TeamGoals: s.TeamGoals, //团队目标
                ObjectEmployeeIdList: s.ObjectEmployeeList.map(n => n.EmployeeId) || [],//被考核人
                AttachmentIdList: s.AttachmentList.map(n => n.Id) || [],//相关附件
                // IsPublic: s.IsPublic, //集体公示
              }
              return result
            })
  
            this.disabledBtn = true
            let result = null;
            if (this.dialogStatus == "create" || this.dialogStatus == "reCreate") {
              delete postData.Id;
              result = appraisePlanYear.add(postData);
            } else if (this.dialogStatus == "update") {
              result = appraisePlanYear.edit(postData);
            }
  
            result.then(res => {
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.disabledBtn = false
              this.$refs.appDialogRef.createData();
            }).catch(err => {
              this.disabledBtn = false
            });
          }
        });
      },
      getDetail() {
        this.loading = true
        appraisePlanYear.detail({ id: this.id }).then(res => {
          this.loading = false

          this.formData = Object.assign({}, this.formData, res);
          this.formData.Year = this.formData.Year && this.formData.Year.toString()
          


          //再次创建（清空时间）
          if(this.dialogStatus == 'reCreate') {
            delete this.formData.Id
          }else{
          }

        }).catch(err => {
          this.loading = false
        });
      },
      handleChangePrincipalUsers(users) {
        this.formData.PrincipalEmployeeList = users;
      },
      handleFinalChangeObjectUsers(users, row) {
        row.FinalEmployeeList = users
      },
      handlePrincipalChangeObjectUsers(users, row) {
        row.PrincipalEmployeeList = users
      },
      handleObjectChangeObjectUsers(users, row) {
        row.ObjectEmployeeList = users
      },
      handleChangeAppealObjectUsers(users) {
        this.formData.AppealObjectEmployeeList = users;
      },
      handleFilesUpChange(files) {
        this.formData.AttachmentList = files;
      },
      handleRowFilesUpChange(files, row) {
        row.AttachmentList = files
      },
      handleClose() {
        this.$refs.appDialogRef.handleClose();
      }
    }
  };
</script>

<style scoped>
.wrapper >>> .el-card__body {
  padding: 0;
  padding-top: 10px;
  padding-right: 10px;
}

.wrapper >>> .el-card {
  margin-top: 10px;
  border-radius: 6px;
}

.wrapper >>> .st-el-form-item .el-form-item{
  margin-bottom: 0!important;
}

.wrapper >>> .st-row{
  padding: 8px 0;
  border-bottom: 1px solid #DCDFE6;
  margin: 0!important;
}
</style>

<style lang="scss" scoped>
.dep-wrapper{
  display: flex;
  .dep-name{
    flex: 1;
  }
  .del-btn-wrapper{
    // width: 40px;
    i{
      font-size: 18px;
      cursor: pointer;
    }
  }
}
</style>
