<template>
  <div class="monBottom shadow">
    <div class="blockTitle pl-10 line-40 cl">
      <span class="fl">{{title}}<i v-show="amount >= 0">({{amount}})</i></span>
      <el-select class='fr' v-model="region" v-show="regionList">
        <el-option @click.native="handleSelectChange(rl)" v-for="(rl,index) in regionList" :label="rl.label" :value="rl.value" :key="index"></el-option>
      </el-select>
    </div>
    <slot></slot>
  </div>
</template>

<script>

export default {
  props:['title','amount','aShow','regionList'],
  name: "",
  components: {
    
  },
  filters: {
    
    
  },

  data() {
    return {
      treeData:[],
      nationalData:0,
      region:''
    };
  },
  computed: {
    
  },
  
  watch: {
    
  },
  created() {
    
  },
  mounted() {
    // if(this.regionList){
    //   this.region=this.regionList[0].value;
    // }
  },
  methods:{
    chioseFirst(id){
      this.region=id;
    },
    handleSelectChange(d){
      this.$emit('selectChange',d)
    },
  }
};
</script>

<style lang="scss" scoped>
.monBottom{
  width:100%;
  margin-top: 10px;
  margin-bottom: 20px;
  border:1px solid #EBEEF5;
}
.line-40{
  border-bottom: 1px solid #EBEEF5;
}
.pl-10{
  padding-right: 10px;
}
.blockTitle{
  background:#ccc;
  >span:first-child{
    font-size: 16px;
    font-weight: 600;
  }
}
.shadow{
  box-shadow:2px 2px 5px -2px rgba(0, 0, 0, 0.5); 
}
</style>