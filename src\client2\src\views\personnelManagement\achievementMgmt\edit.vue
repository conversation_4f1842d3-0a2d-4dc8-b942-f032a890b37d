<template>
  <div>
    <app-dialog :title="title" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1008">
      <template slot="body">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          label-width="100px"
        >
          <div class="wrapper" v-loading='loading'>
                <el-row>

                    <el-col :span="24">
                        <el-form-item label="部门名称">
                            {{ formData.DepartmentName }}
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="终审人" prop="FinalEmployeeList">
                            <emp-selector
                            :readonly="!editable"
                            key="ccusers"
                            :showType="2"
                            :multiple="true"
                            :beforeConfirm='handleFinalBeforeConfirm'
                            :list="formData.FinalEmployeeList"
                            @change="handleFinalChangeObjectUsers"
                            ></emp-selector>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="考核主管" prop="PrincipalEmployeeList">
                            <emp-selector
                            :readonly="!editable"
                            key="ccusers"
                            :showType="2"
                            :multiple="true"
                            :beforeConfirm='handlePrincipalBeforeConfirm'
                            :list="formData.PrincipalEmployeeList"
                            @change="handlePrincipalChangeObjectUsers"
                            ></emp-selector>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="24">
                        <el-form-item label="被考核人" prop="ObjectEmployeeList">
                            <emp-selector
                            :readonly="!editable"
                            key="ccusers"
                            :showType="2"
                            :multiple="true"
                            :list="formData.ObjectEmployeeList"
                            @change="handleObjectChangeObjectUsers"
                            ></emp-selector>
                        </el-form-item>
                    </el-col>
                </el-row>
          </div>
        </el-form>
      </template>

      <template slot="footer">
        <!-- <el-button @click="handleClose">取消</el-button>
        <el-button @click="createData" type="primary" v-show="editable">确认</el-button>-->
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import empSelector from "../../common/empSelector";
import wangEditor from "@/components/WangEditor";
import * as ach from "@/api/personnelManagement/achievementMgmt";
export default {
  name: "",
  directives: {},
  components: {
    wangEditor,
    empSelector,
  },
  mixins: [],
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    title() {
      if (this.dialogStatus == "create") {
        return "创建";
      } else if (this.dialogStatus == "update") {
        return "编辑";
      } else if (this.dialogStatus == "detail") {
        return "详情";
      }
      return "";
    }
  },
  props: {
    //编辑还是新增(create: 新增; update: 编辑; detail：详情)
    dialogStatus: {
      required: true,
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      isClear: false,
      disabledBtn: false,
      loading: false,
      rules: {
        FinalEmployeeList: { fieldName: "终审人", rules: [{ required: true, trigger: 'change' }] },
        PrincipalEmployeeList: { fieldName: "考核主管", rules: [{ required: true, trigger: 'change' }] },
        ObjectEmployeeList: { fieldName: "被考核人", rules: [{ required: true, trigger: 'change' }] },
      },
      formData: {
        Id: "",
        FinalEmployeeList: [],
        PrincipalEmployeeList: [],
        ObjectEmployeeList: [],
      }
    };
  },
  methods: {
      handleFinalBeforeConfirm(users) {
        if(users && users.length > 3) {
          this.$message({
              message: '终审人不得超过3人',
              type: 'error'
          })
          return false
        }
        return true
      },
      handlePrincipalBeforeConfirm(users) {
        if(users && users.length > 3) {
          this.$message({
              message: '考核人不得超过3人',
              type: 'error'
          })
          return false
        }
        return true
      },
    handleFinalChangeObjectUsers(users) {
      this.formData.FinalEmployeeList = users || [];
    },
    handlePrincipalChangeObjectUsers(users) {
      this.formData.PrincipalEmployeeList = users || [];
    },
    handleObjectChangeObjectUsers(users) {
      this.formData.ObjectEmployeeList = users || [];
    },
    resetFormData() {
      this.formData = {
        Id: "",
        FinalEmployeeList: [],
        PrincipalEmployeeList: [],
        ObjectEmployeeList: [],
      };
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
            let postData = JSON.parse(JSON.stringify(this.formData));
          
            postData.PrincipalEmployeeIdList = postData.PrincipalEmployeeList.map(n => n.EmployeeId) || [] //考核主管
            postData.FinalEmployeeIdList = postData.FinalEmployeeList.map(s => s.EmployeeId) || [] //终审人
            postData.ObjectEmployeeIdList = postData.ObjectEmployeeList.map(n => n.EmployeeId) || [] //被考核人
          
            let result = null

            this.disabledBtn = true;
            if (this.dialogStatus == "create") {
                // delete postData.Id;
                // result = prod.add(postData);
            } else if (this.dialogStatus == "update") {
                result = ach.editPlan(postData);
            }

            result.then(res => {
                    this.disabledBtn = false;
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                })
                .catch(err => {
                    this.disabledBtn = false;
                });
            }
      });
    },
    getDetail() {
      this.loading = true
      ach.detailPlan({ id: this.id }).then(res => {
        this.loading = false
        this.formData = Object.assign({}, this.formData, res);
      }).catch(err => {
        this.loading = false
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang='scss' scoped>
.wrapper{

}
</style>