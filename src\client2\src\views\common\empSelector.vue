<template>
    <div :class="reference ? '' : 'avatar-warpper clearfix'" :style="{ display: reference ? 'inline-block' : 'block' }">
        <div v-if="showType == 1">
            <div class="avatar-item" v-for="(p, idx) in pers" :key="'p_' + idx" @mouseover="() => delIdx = idx"
                @mouseout="() => delIdx = -1">
                <i class="btn-remove el-icon-remove" v-if="!readonly" v-show="delIdx == idx"
                    @click.stop="handleRemove(idx)"></i>
                <div class="avatar">
                    <img :src="p.Avatar || defavatar" alt="">
                </div>
                <div class="username" :title="p.Name">{{ p.Name }}</div>
            </div>

            <div class="avatar-item">
                <div class="avatar">
                    <i class="el-icon-plus icon-plus" v-show="!readonly" @click="handleShow"></i>
                </div>
                <div></div>
            </div>
        </div>
        <template :class="reference ? 'inline' : ''" v-else-if="showType == 2">
            <slot name="reference">
                <div class="ipt-wrapper" :title="names" ref="selectRef">
                    <el-select 
                        v-if="isAutocomplete"
                        style="width: 100%;"
                        class="ipt" 
                        :value-key='listSelectorKeyName' 
                        @change='handleChange' 
                        :value="listSelectorCheckedDataIds" 
                        :multiple='multiple' 
                        filterable 
                        remote 
                        :collapse-tags="collapseTags"
                        :placeholder="placeholder" 
                        :remote-method="getList" 
                        :loading="loading"
                        :disabled="readonly"
                        :clearable='isShowClearButton'
                        v-remove-list="[pers, notRemoveList]"
                    >
                        <el-option v-for="item in options" :key="item.value" :disabled="notRemoveList.includes(item.value)" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <!-- <el-button class="ipt-btn" :disabled='readonly' style="" icon="el-icon-more" @click="handleShow"></el-button> -->
                    
                    <el-input v-if="!isAutocomplete" :placeholder="placeholder" :value="names" :title="names" :disabled="readonly" :readonly='true' unselectable='on' class="input-with-select cus-input">
                        <i slot="suffix" v-if="isShowClearButton" v-show="!readonly" @click="handleClear" class="el-input__icon el-icon-close icon-close"></i>
                        <!-- <div slot='append'>
                            <el-button :disabled='readonly' style="padding: 7px 12px;" icon="el-icon-more" @click="handleShow"></el-button>
                        </div> -->
                    </el-input>

                    <div class="btn-wrapper" :class="readonly ? 'disabled' : ''" @click="handleShow">
                        <img src="../../assets/images/per.svg" alt="">
                    </div>
                </div>

            </slot>
        </template>
        <el-dialog width="1000px" class="dialog_wrapper dialog-mini" :close-on-click-modal="false" close="dialog-mini"
            ref="accessUserDlg" v-el-drag-dialog :visible.sync='dialogAccessUsers'
            v-if="dialogAccessUsers"
            :append-to-body='true'>
            <div class="dialog_wrapper_title" slot="title">
                <!-- <svg-icon icon-class="人员-添加" style="font-size: 18px; padding-right: 4px; float: left;"></svg-icon> -->
                人员选择
            </div>
            <emp-table ref="accessUser" v-bind="$attrs" :disabledList="notRemoveList" :existsUsers='pers' @changed='handleChangeUsers'></emp-table>
            <template slot="footer">
                <!-- <el-button @click="dialogAccessUsers=false">取消</el-button>
                <el-button type="primary" @click="$refs.accessUser.getCheckRow()">{{submitText}}</el-button> -->
                <div>
                    <app-cus-button iconType="close" type="" text="取消" @click="dialogAccessUsers=false"></app-cus-button>
                    <app-cus-button iconType="save" type="primary" :text="submitText"  style="margin-left: 5px;" @click="$refs.accessUser.getCheckRow()"></app-cus-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import elDragDialog from '@/directive/el-dragDialog'
    import removeList from '@/directive/removeList/index'
    import EmpTable from './empTable'
    import * as comm from "@/api/common";
    import { serviceArea } from "@/api/serviceArea"
    import request from "@/utils/request";

    export default {
        name: 'user-list',
        directives: {
            // waves,
            elDragDialog,
            removeList,
        },
        components: {
            EmpTable,
        },
        mounted() {
            // this.pers = JSON.parse(JSON.stringify(this.list))
            let referenceObj = this.$slots.reference
            if (referenceObj) {
                this.reference = referenceObj[0].elm
            }
            if (this.reference) {
                this.reference.addEventListener('click', this.handleShow, false)
            }
        },
        props: {
            //已存在的人员
            list: {
                type: Array,
                default: () => {
                    return []
                },
            },
            readonly: {
                type: Boolean,
                default: false
            },
            showType: {
                type: Number,
                default: 1, //1：头像模式；2：文本框模式  3: 纯选择组件( 页面上不显示相关，通过this.$refs[xxx].handleShow() 显示选择人员弹框，其他不变)
            },
            submitText: { // 确定按钮的文字  默认 确认
                type: String,
                default: "确认"
            },
            isShowClearButton: {
                type: Boolean,
                default: true
            },
            //是否自动完成（支持输入框查询）
            isAutocomplete: {
                type: Boolean,
                default: false
            },
            multiple: {
                type: Boolean,
                default: true
            },
            collapseTags: {
                type: Boolean,
                default: true
            },
            //打开弹框之前（返回 true 会打开弹框，否则不打开）
            ////用于打开弹框之前，判断是否应该打开弹框
            beforeOpen: Function,
            //关闭弹框之前（返回 true 会关闭弹框，否则不关闭弹框）
            //保存成功之后，关闭按钮之前——默认情况下，保存调用api后，需要根据返回结果判断是否关闭弹框
            ////用于关闭弹框之前，判断是否应该关闭弹框——保存后的关闭按钮，而不是点击右上角的关闭按钮（该按钮一般也业务无关）
            beforeConfirm: Function,
            placeholder: {
                type: String,
                default: ''
            },
            notRemoveList: {
                type: Array,
                default: () => {
                    return []
                }
            },

        },
        computed: {
            names() {
                if (this.pers && this.pers.length > 0) {
                    // return this.pers.map(p => `${p.Name}(${p.Number})`).join(',')
                    let tmp = this.pers.map(p => {
                        if (p.Name && p.Number) {
                            return `${p.Name}(${p.Number})`
                        }
                        return ''
                    })
                    return tmp.join(',')
                }
                return ''
            },
            listSelectorCheckedDataIds() {
                if(this.multiple) {
                    return this.pers.map(s => s[this.listSelectorKeyName]) || []
                }else{
                    if(this.pers && this.pers.length > 0) {
                    return this.pers[0][this.listSelectorKeyName]
                    }else{
                    return null
                    }
                }
            }
        },
        data() {
            return {
                delIdx: -1,
                defavatar: require('../../assets/images/avatar.png'),
                pers: [],
                dialogAccessUsers: false,
                reference: undefined,

                //当前下拉选项集合
                options: [],
                //最全options，已被用
                optionsTemp: [],
                listSelectorKeyName: "EmployeeId",
                loading: false,
            }
        },
        watch: {
            list: {
                handler(val) {
                    this.pers = JSON.parse(JSON.stringify(val || []))

                    let existsList = this.pers.map(s => {
                    return {
                        value: s[this.listSelectorKeyName],
                        label: `${s.Name}(${s.Number})`,
                        obj: s
                    }
                    })
                    existsList = JSON.parse(JSON.stringify(existsList))
                    this.options = existsList
                    this.optionsTemp = existsList
                    this.appendOptions(val)
                    
                    //首页进入页面（一般为编辑），清空select的options属性
                    // if(oldVal === undefined) {
                    // }
                    setTimeout(() => {
                    this.options = []
                    }, 10);
                },
                immediate: true
            },
        },

        methods: {
            /**
             * 
             * @param {*} vals EmployeeId 集合，select 修改后的最终值
             */
            async handleChange(vals) {
                //不能删除的用户集合，如果在之前的选中集合中，但是没有在修改后的集合中（表示不应该删除的被删除了）；
                let notDelEmps = this.notRemoveList.filter(s => this.pers.find(o => o.EmployeeId == s) && !vals.find(n => n == s))

                //还原不应该删除的用户
                vals = notDelEmps.concat(vals)

                let _this = this
                if(this.beforeConfirm) {
                    if(await this.beforeConfirm(vals)) {
                        _this.pers = _this.optionsTemp.filter(s => vals.indexOf(s.obj[_this.listSelectorKeyName]) > -1).map(s => s.obj)
                    }
                }else{
                    _this.pers = _this.optionsTemp.filter(s => vals.indexOf(s.obj[_this.listSelectorKeyName]) > -1).map(s => s.obj)
                }
                _this.usersChanged()
            },
            handleRemove(idx) {
                if(this.pers[idx] && this.pers[idx].CanDelete !== false) {
                    this.pers.splice(idx, 1)
                    this.usersChanged()
                }
            },
            handleChangeUsers(users) {
                this.handleBeforeClose(users)
            },
            async handleBeforeClose(users) {
                if(this.beforeConfirm) {
                    if(await this.beforeConfirm(users)) {
                        this.pers = users
                        this.usersChanged()
                        this.dialogAccessUsers = false
                    }
                }else{
                    this.pers = users
                    this.usersChanged()
                    this.dialogAccessUsers = false
                }
            },

            usersChanged() {
                let postDatas = this.pers

                //保存为常用联系人
                let ids = postDatas.map(s => s.EmployeeId)
                if(ids && ids.length > 0) {
                    comm.saveTopContacts(ids).then(res => {})
                }

                this.$emit("change", JSON.parse(JSON.stringify(postDatas)))
            },
            handleClear() {
                this.pers = []
                this.usersChanged();
            },
            async handleShow() {
                if(!this.readonly) {
                    if(this.beforeOpen) {
                        if(await this.beforeOpen()) {
                            this.dialogAccessUsers = true
                        }
                    }else{
                        this.dialogAccessUsers = true
                    }
                }
            },
            getList(query) {
                var _this = this;
                let keywords = query.trim()
                if(keywords) {
                    _this.loading = true;
            
                    let data = {};
                    data.PageIndex = 1
                    data.PageSize = 20
                    data.Name = keywords
                    request({
                        url: serviceArea.user + "/SystemEmployee/GetAllEmployees",
                        method: "POST",
                        data
                    }).then(response => {
                        _this.loading = false;
                        _this.appendOptions(response)
                        //匹配option的label，并且排除已选中的项
                        _this.options = _this.optionsTemp.filter(item => {
                            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1 
                            && ((_this.multiple && _this.listSelectorCheckedDataIds.indexOf(item.value) == -1) || (!_this.multiple && [_this.listSelectorCheckedDataIds].indexOf(item.value) == -1));
                        });
                    });
                }

                },   
                appendOptions(list, appendCurrentOptions) {
                let _this = this
                if(list && list.length > 0) {
                    let notExixts = list.filter(s => _this.optionsTemp.map(a => a.value).indexOf(s[_this.listSelectorKeyName]) == -1).map(s => {
                        return {
                        value: s[_this.listSelectorKeyName],
                        label: `${s.Name}(${s.Number})`,
                        obj: s
                        }
                    })
                    _this.optionsTemp = _this.optionsTemp.concat(notExixts)

                    //当点击弹框，选择的项目不存在于 options 中时
                    if(appendCurrentOptions) {
                        let notExixts2 = list.filter(s => _this.options.map(a => a.value).indexOf(s[_this.listSelectorKeyName]) == -1).map(s => {
                        return {
                            value: s[_this.listSelectorKeyName],
                            label: `${s.Name}(${s.Number})`,
                            obj: s
                        }
                        })
                        _this.options = _this.options.concat(notExixts2)

                        //点击弹框，选中，保存后，清空 options
                        setTimeout(() => {
                        _this.options = []
                        }, 10);
                    }
                }
            },
        },
        beforeDestroy() {
            if (this.reference) {
                this.reference.removeEventListener('click', this.handleShow, false)
            }
        },
    }
</script>

<style lang="scss" scoped>
.ipt{
    /deep/.el-input__inner{
        border-radius: $border-radius-base 0 0 $border-radius-base!important;
    }
}

.ipt-wrapper{
    /deep/.el-button--small{
        border-radius: 0 $border-radius-base $border-radius-base 0;
        border-left: none;
    }
}


.dialog_wrapper >>> .el-dialog__body {
    /* padding-top: 15px; */
    padding: 0;
}
</style>

<style lang='scss' scoped>
    .avatar-warpper {}

    .avatar-warpper .avatar-item {
        position: relative;
        float: left;
        text-align: center;
        margin: 0 8px;
        width: 50px;
    }

    .avatar-warpper .avatar-item .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: url(../../assets/images/avatar.png) no-repeat;
    }

    .avatar-warpper .avatar-item .avatar img {
        width: 50px;
        height: 50px;
    }

    .btn-remove {
        position: absolute;
        top: -5px;
        right: -5px;
        cursor: pointer;
    }

    .btn-remove:hover {
        transition: all 0.3s;
        color: #F53F3F;
    }

    .icon-plus {
        line-height: 50px;
        width: 50px;
        font-size: 24px;
        cursor: pointer;
        /* border: 1px solid red; */
    }

    .username {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .icon-close {
        cursor: pointer;
        margin-top: 2px!important;
    }

    .inline{
        display: inline;
    }

    .ipt-wrapper{
        height: 100%;
        position: relative;
        display: flex;
        padding-right: 30px;
        .ipt{
            flex: 1;
        }
        .btn-wrapper{
            flex-shrink: 0;
            cursor: pointer;
            user-select: none;
            position: absolute;
            top: 0; 
            right: 0; 
            bottom: 0; 
            width: 30px; 
            box-sizing: border-box; 
            display: flex; 
            align-items: center; 
            justify-content: center;
            border: 1px solid $border-color-light; 
            border-left: none;
            // padding-bottom: 0.5px;
            border-radius: 0 $border-radius-base $border-radius-base 0;
            &.disabled{
                cursor: not-allowed;
                border-color: #edeff3;
            }
        }
    }
.dialog_wrapper{
    &_title{
        height: 18px;
        line-height: 18px;

        [class*=" el-icon-"], [class^=el-icon-]{
            font-size: 18px;
            float: left;
            margin-right: 5px;
        }
    }
}
.dialog_wrapper_title{
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    // color: #fff;
}

.ipt-wrapper{
    // select 多选时, tag 上的 删除按钮位置调整
    /deep/.el-tag__close.el-icon-close{
        top: 2px!important;
    }
    /deep/.el-tag.el-tag--info{
        color: $text-primary!important;
    }
}


</style>