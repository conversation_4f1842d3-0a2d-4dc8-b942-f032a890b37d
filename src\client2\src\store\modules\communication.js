const communication = {
    state: {
        selectedData:[],//故障分类标签选中数据
        selectedMsg:null,
        indexSelectedData:[],
        indexSelectedMsg:null,
    },
    mutations: {
        getselectedData(state,d){
        	state.selectedData=d;
        },
        getSelectedMsg(state,d){
        	state.selectedMsg=d;
        },
        getIndexSelectedData(state,d){
        	state.indexSelectedData=d;
        },
        getIndexSelectedMsg(state,d){
        	state.indexSelectedMsg=d;
        },
    }
}

export default communication