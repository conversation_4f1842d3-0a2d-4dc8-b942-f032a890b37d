<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title
        title="售后服务"
        :subTitle="['售后回访计划的创建、跟进、管理页面']"
        :showBackBtn="!!returnUrl"
        @goBack="handleGoBack"
      ></page-title> -->
      <div style="height: 100%;" class="__dynamicTabContentWrapper">
        <div class="content __dynamicTabWrapper">
          <app-table
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :isShowAllColumn="true"
            :loading="listLoading"
            @rowSelectionChanged="rowSelectionChanged"
            :isShowOpatColumn="true"
            :startOfTable="startOfTable"
            :multable="false"
            @sortChagned='handleSortChange'
            :isShowBtnsArea='false'
            :layoutMode='layoutMode'
          >
            <!-- <template slot="Type" slot-scope="scope">{{
              scope.row.Type | typeFilter
            }}</template> -->
            <template slot="ReturnVisitsStatus" slot-scope="scope">
              <span
                class="item-status"
                :style="{
                  backgroundColor: getStatusObj(scope.row.ReturnVisitsStatus)
                    .color
                }"
              >
                {{ scope.row.ReturnVisitsStatus | statusFilter }}
              </span>
            </template>
            <template slot="ApprovalStatus" slot-scope="scope">
              <span
                v-if="getApprovalObj(scope.row.ApprovalStatus).label"
                class="item-status"
                :style="{
                  backgroundColor: getApprovalObj(scope.row.ApprovalStatus).color
                }"
              >
                <span>{{ getApprovalObj(scope.row.ApprovalStatus).label }}</span>
              </span>
              <span v-else>无</span>
            </template>
            <template slot="EndTime" slot-scope="scope">{{
              scope.row.EndTime | dateFilter("YYYY-MM-DD HH:mm")
            }}</template>
            <template slot="EmployeeList" slot-scope="scope">
              <span v-if="scope.row.EmployeeList">{{
                scope.row.EmployeeList.map(s => s.Name).join(",")
              }}</span>
            </template>
            <template slot="ShouldDealWithCount" slot-scope="scope">
              <template v-if="scope.row.ShouldDealWithCount > 0">
                <span :style="{color: scope.row.ProcessedCount == 0 ? '#FF0000' : scope.row.ProcessedCount > 0 && scope.row.ProcessedCount < scope.row.ShouldDealWithCount ? '#00CC00' : '#53A8FF'}">
                  {{ scope.row.ShouldDealWithCount }} / {{ scope.row.ProcessedCount }}
                </span>
              </template>
              <template v-else>
                {{ scope.row.ShouldDealWithCount }} / {{ scope.row.ProcessedCount }}
              </template>
            </template>

            <!-- 表格查询条件区域 -->
            <template slot="conditionArea">
              <div
                style="border-bottom: 1px solid #EBEEF5; margin-bottom: 10px;"
              >
                <tags
                  :items="handlerEmployeeIds"
                  v-model="listQuery.SearchStatus"
                >
                  <template v-for="t in handlerEmployeeIds" :slot="t.value">
                    {{ t.label }} ({{ t.total }})
                  </template>
                </tags>
              </div>
              <app-table-form
                :label-width="'80px'"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="handleResetSearch"
                :layoutMode='layoutMode'
              >
                <template slot="Keywords">
                    <el-input style="width: 100%;" 
                        placeholder="搜索计划名称/编号"
                        @clear='getList'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                getList()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.Keywords"
                    ></el-input>
                </template>
                <!-- <template slot="Type">
                  <el-select
                    style="width: 100%;"
                    class="sel-ipt"
                    v-model="listQuery.Type"
                    placeholder
                    clearable
                  >
                    <el-option
                      v-for="item in planTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </template> -->
                <template slot="EmployeeName">
                  <el-input
                    style="width: 100%;"
                    v-model.trim="listQuery.EmployeeName"
                    placeholder
                  ></el-input>
                  <!-- <el-select style="width: 100%;" class="sel-ipt" filterable v-model="listQuery.EmployeeId" placeholder clearable>
                                          <el-option v-for="item in employees" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                      </el-select> -->
                </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                  <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                </template>
              </app-table-form>
            </template>

            

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <app-table-row-button @click="handleTrackDialog(scope.row, 'detail')" :type="2"></app-table-row-button>
              <!-- 待审批 或 已完成 不能编辑 跟进 -->
              <app-table-row-button v-if="rowBtnIsExists('btnEdit') && (!scope.row.ApprovalStatus || scope.row.ApprovalStatus == 9) && (scope.row.ReturnVisitsStatus == 1 || scope.row.ReturnVisitsStatus == 2)" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>
              <!-- 这个跟进不用授权，维修单要授权是因为特殊原因，才这么加的 -->
              <app-table-row-button v-if="isShowTrackBtn(scope.row) && (!scope.row.ApprovalStatus || scope.row.ApprovalStatus == 9) && (scope.row.ReturnVisitsStatus == 1 || scope.row.ReturnVisitsStatus == 2)" @click="handleTrackDialog(scope.row, 'track')" text="跟进" :type="2"></app-table-row-button>
              <app-table-row-button v-if="rowBtnIsExists('btnDel') && scope.row.ApprovalStatus != 7 " @click="handleDel(scope.row)" :type="3"></app-table-row-button>
            </template>
          </app-table>
        </div>
        <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!-- 新增、编辑 -->
    <create-page
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
    ></create-page>
    <!-- 跟进 -->
    <track-page
      @closeDialog="closeTrackDialog"
      @saveSuccess="handleTrackSaveSuccess"
      :dialogFormVisible="dialogTrackFormVisible"
      :dialogStatus="dialogTrackStatus"
      :id="id"
    ></track-page>
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>
  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as afterService from "@/api/afterSalesMgmt/afterService";
import createPage from "./create";
import trackPage from "./track";
import { vars } from "../common/vars";
import { getUserInfo } from "@/utils/auth";
import * as approvalVars from '../../../salesMgmt/common/vars'
import vExport from "@/components/Export/index";
export default {
  name: "after-service",
  mixins: [indexPageMixin],
  components: {
    createPage,
    trackPage,
    vExport
  },
  computed: {
    returnUrl() {
      let url = decodeURI(this.$route.query.returnUrl || "");
      return url;
    }
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    typeFilter(val) {
      let obj = vars.afterService.planTypes.find(s => s.value == val);
      if (obj) {
        return obj.label;
      }
      return val;
    },
    statusFilter(val) {
      let obj = vars.afterService.planStatus.find(s => s.value == val);
      if (obj) {
        return obj.label;
      }
      return val;
    },
  },
  created() {
    this.getStatisticalStatesNumber()
    this.getEmployees();
    this.getList();
    this.currentEmployeeId = getUserInfo().employeeid;
  },
  watch: {
    "listQuery.SearchStatus"(val) {
      // if (val == "my") {
      //   delete this.listQuery.EmployeeName;
      //   let idx = this.tableSearchItems.findIndex(
      //     s => s.prop == "EmployeeName"
      //   );
      //   if (idx > -1) {
      //     this.tableSearchItems.splice(idx, 1);
      //   }
      // } else {
      //   this.$set(this.listQuery, "EmployeeName", "");
      //   this.tableSearchItems.splice(3, 0, {
      //     prop: "EmployeeName",
      //     label: "客服人员"
      //   });
      // }
      this.getList();
    }
  },
  data() {
    return {
      layoutMode: 'simple',
      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,
      planTypes: vars.afterService.planTypes,
      planStatus: vars.afterService.planStatus,
      currentEmployeeId: "",
      dialogTrackFormVisible: false,
      dialogTrackStatus: "track",

      priorities: "",
      total: 0,
      listLoading: false,
      listQuery: {
        SearchStatus: 1,
        Keywords: '',
        // Type: null,
        EmployeeName: ""
      },
      multipleSelection: [],
      tableSearchItems: [
        {
            prop: "Keywords",
            label: "",
            mainCondition: true
        },
        // { prop: "Type", label: "类型" },
        { prop: "EmployeeName", label: "客服人员" }
      ],
      tabColumns: [
        {
          attr: { prop: "PlanNo", label: "计划编号" }
        },
        {
          attr: { prop: "PlanName", label: "计划名称", showOverflowTooltip: true }
        },
        {
          attr: {
            prop: "ReturnVisitsStatus",
            label: "状态",
            sortable: 'custom'
          },
          slot: true
        },
        {
          attr: {
            prop: "ApprovalStatus",
            label: "审批状态",
          },
          slot: true
        },
        // {
        //   attr: { prop: "Type", label: "类型", width: "100" },
        //   slot: true
        // },
        {
          attr: {
            prop: "ShouldDealWithCount",
            label: "应/已回访",
          },
          slot: true
        },
        // {
        //   attr: {
        //     prop: "ProcessedCount",
        //     label: "已回访",
        //     width: "100",
        //   }
        // },
        {
          attr: { prop: "EmployeeList", label: "客服人员" },
          slot: true
        },
        {
          attr: {
            prop: "EndTime",
            label: "截止时间",
            sortable: 'custom'
          },
          slot: true
        }
      ],
      tabDatas: [],
      employees: [],
      handlerEmployeeIds: [
        { value: 1, label: "未完成", total: 0 },
        { value: 2, label: "已完成", total: 0 },
        { value: 0, label: "全部", total: 0 },
      ],
      rData:null,
      cData:[],
      dialogExportVisible:false,
    };
  },
  methods: {
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    getApprovalObj(status) {
      return approvalVars.vars.orderMgmt.approvalStatus.find(s => s.value == status) || {};
    },
    getStatusObj(status) {
      return vars.afterService.planStatus.find(s => s.value == status) || {};
    },
    isShowTrackBtn(row) {
      if (row && row.EmployeeList && row.EmployeeList.length > 0) {
        return row.EmployeeList.map(s => s.EmployeeId).some(
          s => s == getUserInfo().employeeid
        );
      }
      return false;
    },
    onBtnClicked: function(domId) {
      switch (domId) {
        case "btnAdd":
          this.handleDialog("create");
          break;
        case "btnExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },
    handleExport(){
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      // if (this.listQuery.TableActive == "my") {
      //   postData.EmployeeId = getUserInfo().employeeid;
      // }
      this.rData={
          "exportSource": 13,
          "columns": [],
          "searchCondition": postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'计划编号',
        value:'PlanNo'
      },{
        label:'计划名称',
        value:'PlanName'
      },{
        label:'状态',
        value:'ReturnVisitsStatus'
      },{
        label:'审批状态',
        value:'ApprovalStatus'
      },
      // {
      //   label:'类型',
      //   value:'Type'
      // },
      {
        label:'应回访',
        value:'ShouldDealWithCount'
      },{
        label:'已回访',
        value:'ProcessedCount'
      },{
        label:'客服人员',
        value:'EmployeeString'
      },{
        label:'截止时间',
        value:'EndTimeString'
      }]
      this.dialogExportVisible=true;
    },
    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.getStatisticalStatesNumber()
      this.getList();
      this.closeDialog();
    },
    handleTrackDialog(row, optType = "update") {
      // 弹出编辑框
      this.id = row.Id;
      this.dialogTrackStatus = optType;
      this.dialogTrackFormVisible = true;
    },
    handleTrackSaveSuccess(_formData) {
      this.getStatisticalStatesNumber()
      this.getList();
      this.closeTrackDialog();
    },
    closeTrackDialog() {
      this.dialogTrackFormVisible = false;
    },

    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    getEmployees() {
      afterService.getEmployees({}).then(res => {
        this.employees = res.map(s => {
          return {
            label: s.Name,
            value: s.EmployeeId
          };
        });
      });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleUpdate(row, optType = "update") {
      // 弹出编辑框
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        SearchStatus: this.listQuery.SearchStatus,
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        Keywords: '',
        // Type: null
      };
      this.getList(); //刷新列表
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.getList()
    },
    //获取项目列表
    getList() {
      this.listLoading = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      // if (this.listQuery.TableActive == "my") {
      //   postData.EmployeeId = getUserInfo().employeeid;
      // }
      postData = this.assignSortObj(postData)
      afterService
        .getList(postData)
        .then(res => {
          this.listLoading = false;
          this.tabDatas = res.Items;
          this.total = res.Total;
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    handleDel(row) {
      this.$confirm(`是否确认删除 ${row.PlanNo}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        afterService.del([row.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getStatisticalStatesNumber()
          this.getList();
        });
      });
    },
    getStatisticalStatesNumber() {
      afterService.getStatisticalStatesNumber({}).then(res => {
        this.handlerEmployeeIds.forEach(e => {
          let obj = res.find(s => s.Key === e.value)
          if(obj) {
            e.total = obj.Value
          }
        })
      })
    },
    handleGoBack() {
      this.$router.push({ path: this.returnUrl });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  overflow-y: auto;
  .bg-white {
    .content {
      padding-right: 0;
      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }
      .list {
      }
    }
  }
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
