<template>
  <div>
        <app-dialog
            title="实施记录/问题"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :width='1200'
             v-loading='loading'
        >
            <template slot="body">
              <div class="wrapper" v-loading='loading'>
                  <div class="left tagsBox">
                      <tags mode='siteList' :items='siteList' v-model="selectGroup" @change="handleTagsChange1">
                    </tags>
                  </div>
                  <div class="right">
                      <div>
                        <tags :items='types' v-model="tagType" @change="handleTagsChange2">
                            <template v-for="t in types" :slot="t.value">
                                {{ t.label }}
                            </template>
                        </tags>
                      </div>
                      <div>
                        <div v-if="tagType == 1">
                          <app-table-core
                            ref="mainTable"
                            :tab-columns="tabColumns"
                            :tab-datas="tabDatas"
                            :tab-auth-columns="[]"
                            :isShowAllColumn="true"
                            :isShowOpatColumn="false"
                            :startOfTable="0"
                            :multable='false'
                          >
                            <template slot="Status" slot-scope="scope">{{ scope.row.Status | equStatusFilter }}</template>
                            <template slot="EmployeeList" slot-scope="scope">
                                <span v-if="scope.row.EmployeeList">{{ scope.row.EmployeeList.map(s => s.Name).join(',') }}</span>
                            </template>
                            <template slot="CreateEmployee" slot-scope="scope">
                                <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
                            </template>
                            <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>
                            <template slot="Operation" slot-scope="scope">
                                <el-button type="text" size="mini" @click="handleQuesDialog(scope.row)">详情</el-button>
                            </template>
                          </app-table-core>
                          <pagination
                            v-show="total>PageSize"
                            :total="total"
                            :page.sync="PageIndex"
                            :size.sync="PageSize"
                            layout="total, prev, pager, next, jumper"
                            @pagination="handleCurrentChange"
                            @size-change="handleSizeChange"
                          />
                        </div>
                        <div v-else>
                          <el-tabs type="card" v-model="tabsActive" @tab-click="handleTabClick">
                            <el-tab-pane v-for="(item,index) in tabsData" :key="item.Id" :label="(index+1)+'、'+item.Name" :name="item.Id"></el-tab-pane>
                          </el-tabs>
                          <div class="elTimeBox" v-loading='timelineLoading'>
                            <el-timeline v-if="activities.length>0">
                              <el-timeline-item
                                v-for="(activity, index) in activities"
                                :key="index"
                                :timestamp="activity.CreateTime.split('.')[0]">
                                <div class="elTimeDiv cl">
                                  <span class="fl cl">
                                    <i class="c409EFF fl">{{activity.CommentContent.Employee | emName}}&nbsp;</i>
                                    <i class="fl">将&nbsp;</i>
                                    <i class="c409EFF fl omit" :title="activity.CommentContent.EquipmentNames">{{activity.CommentContent.EquipmentNames}}</i>
                                  </span>
                                  <span class="fl">{{activity.CommentContent.Type}}<i class="c409EFF">{{activity.CommentContent.Status | varsFilter(activity.CommentContent)}}</i></span>
                                </div>
                                <div v-show="activity.CommentContent.Remark">返回原因：{{activity.CommentContent.Remark}}</div>
                              </el-timeline-item>
                            </el-timeline>
                            <!--暂无数据-->
                            <no-data v-else></no-data>
                          </div>
                        </div>
                      </div>
                  </div>
              </div>
            </template>
            <template slot="footer">
                <el-button @click="handleClose">关闭</el-button>
            </template>
        </app-dialog>
        <!-- 问题 创建、处理、详情 -->
        <ques-create
            @closeDialog="closeQuesDialog"
            @saveSuccess="handleQuesSaveSuccess"
            :dialogFormVisible="dialogQuesFormVisible"
            dialogStatus='detail'
            :id="quesId"
        ></ques-create>
    </div>
</template>

<script>
import quesCreate from './workbench/quesCreate'
import indexPageMixin from "@/mixins/indexPage";
import * as impManagement from "@/api/implementation/impManagement";
import * as impMgmt from "@/api/implementation/impManagement2"
import {vars} from '../common/vars';
import NoData from "@/views/common/components/noData";
export default {
  props:['siteList'],
  mixins: [indexPageMixin],
  name: "",
  components: {
    quesCreate,
    NoData
  },
  filters: {
    equStatusFilter(status) {
        let tmp = vars.questionStatus.find(s => s.value == status)
        if(tmp) {
            return tmp.label
        }
        return status
    },
    varsFilter(s,d) {
      if(s == 0){
        return d.ProcedureName;
      }else{
        let r=vars.processStatus.find(v => v.value == s);
        return r.label;
      }
    },
    emName(d){
      if(d){
        return d.Name
      }else{
        return ''
      }
    }
  },

  data() {
    return {
      timelineLoading:false,
      dialogQuesFormVisible:false,
      quesId:'',
      saveId:'',
      total:0,
      activities: [],
      tabsData:[],
      tabsActive:'sales',
      tagType:1,
      loading:false,
      selectGroup:'',
      list:[{value:1,label:'哈哈'},{value:2,label:'嘿嘿'},{value:3,label:'呵呵'},],
      types: [{value: 1, label: '问题处理'}, {value: 2, label: '操作记录'}],
      tabColumns: [
        {
            attr: { prop: "Name", label: "问题名称" }
        },
        {
            attr: { prop: "Status", label: "状态" },
            slot: true
        },
        {
            attr: { prop: "EmployeeList", label: "处理人" },
            slot: true
        },
        {
            attr: { prop: "CreateEmployee", label: "创建人" },
            slot: true
        },
        {
            attr: { prop: "CreateTime", label: "创建时间" },
            slot: true
        },
        {
          attr: { prop: "Operation", label: "操作" },
          slot:true
        },
      ],
      tabDatas: [],
      PageSize:15,
      PageIndex:1,
    };
  },
  computed: {
    
  },
  
  watch: {
    siteList(val){
      if(val.length>0){
        this.selectGroup=this.siteList[0].value;
      }
    },
    '$attrs.dialogFormVisible':{
        handler(val) {
            if(val){
              // console.log(222,this.siteList)
              if(this.siteList.length>0){
                this.saveId=this.siteList[0].value;
                this.getImplementData(this.siteList[0].value);
                this.getQuesList(this.siteList[0].value);
              }
            }
        },
        immediate: true
    }
  },
  created() {
      
  },
  mounted() {},
  methods: {
    getQuesList(id){
      let params={
        "PageIndex": this.PageIndex,
        "PageSize": this.PageSize,
        "ImplementationRegionalId": id,
        "IsMine": false,
      }
      impMgmt.getQues(params).then(res => {
        res.Items.forEach(v => {
          v.Operation=true;
        })
        this.tabDatas = res.Items
        this.total = res.Total
      })
    },
    getImplementData(id){
      impManagement.getImplementationProcedureListByCondition({"ImplementationRegionalId": id}).then(res => {
        this.tabsActive=res[0].Id;
        this.tabsData=res;
        this.getImplementDetail(this.tabsActive);
      })
    },
    getImplementDetail(id){
      this.timelineLoading=true;
      impManagement.getAllComment({"CurrentBusinessId": id,"Type": 11}).then(res => {
        if(res.length>0){
          res.forEach(v => {
            v.CommentContent=JSON.parse(v.CommentContent);
            v.CommentContent.EquipmentNames=[];
            v.CommentContent.EquipmentList.forEach(v1 => {
              v.CommentContent.EquipmentNames.push(v1.Name);
            })
            if(v.CommentContent.EquipmentNames.length>0){
              v.CommentContent.EquipmentNames=v.CommentContent.EquipmentNames.join('、');
            }else{
              v.CommentContent.EquipmentNames='';
            }
          })
          this.activities=res;
        }else{
          this.activities=[];
        }
        this.timelineLoading=false;
      }).catch(e => {
        this.timelineLoading=false;
      })
    },
    handleTabClick(d){
      this.getImplementDetail(this.tabsActive);
    },
    handleTagsChange1(d){
      this.saveId=d;
      this.getImplementData(d);
      this.getQuesList(d);
    },
    handleTagsChange2(d){

    },
    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
    handleCurrentChange(val) {
      this.PageIndex = val.page;
      this.getQuesList(this.saveId);
    },
    handleSizeChange(val) {
      this.PageSize = val.size;
      this.getQuesList(this.saveId);
    },
    /**
     * 问题 添加、编辑、详情
     */
    handleQuesDialog(row) {
        if(row) {
            this.quesId = row.Id
        }
        this.dialogQuesFormVisible = true;
    },
    closeQuesDialog() {
        this.dialogQuesFormVisible = false;
    },
    handleQuesSaveSuccess(newRegional) {
        this.closeQuesDialog();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper{
    height:600px;
    display: flex;
    .left{
       width: 30%;
        border-right: 1px solid #DCDFE6;
        height:100%;
        overflow-y: auto;
    }
    .right{
      padding-left:8px;
       flex: 1;
       >div:first-child{
        padding-bottom: 6px;
       }
       >div:last-child{
        height:100%;
        >div{
          height:calc(100% - 34px);
          overflow-y: auto;
          .elTimeBox{
            height:calc(100% - 90px);
            overflow-y: auto;
          }
        }
       }
    }
}
.elTimeBox{
  padding:0 20px;
}
.elTimeDiv{
  margin-bottom: 6px;
  height:20px;
  line-height: 20px;
  >span:first-child{
    margin-right: 100px;
    width:320px;
    >i:last-child{
      max-width:250px;
    }
  }
}
.c409EFF{
  color:#409EFF;

}
</style>