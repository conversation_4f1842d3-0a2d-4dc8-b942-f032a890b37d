<!--提醒设置-->
<template>
<div>
    <!--组件内容区-->
    <app-dialog title="提醒设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
        <template slot="body">
            <el-form
                :rules="formRules"
                ref="formRef"
                :model="formData"
                label-position="right"
                label-width="100px"
                v-loading='loading'>
                <el-form-item label="提醒开关">
                    <el-switch v-model="formData.IsReminding"></el-switch>
                </el-form-item>
                <!-- 提醒开关开启才显示下面的参数 -->
                <template v-if="formData.IsReminding">
                    <el-form-item label="有效期" prop="Validity">
                        <el-radio-group v-model="formData.Validity">
                            <el-radio :label="0">不足1个月</el-radio>
                            <el-radio :label="1">不足2个月</el-radio>
                            <el-radio :label="2">不足3个月</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="提醒时间" prop="RemindingTime">
                                <el-time-picker style="width: 100px;" v-model="formData.RemindingTime" format="HH:mm" value-format="HH:mm" placeholder=""></el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="提醒频率" prop="RemindingRate">
                                <el-select v-model="formData.RemindingRate" placeholder="请选择">
                                    <el-option :value="0" label="仅1次"></el-option>
                                    <el-option :value="1" label="每天1次"></el-option>
                                    <el-option :value="2" label="每3天1次"></el-option>
                                    <el-option :value="3" label="每7天1次"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="范围设置">
                        <el-button type="primary" @click="handleAddDepartment">创建推送规则</el-button>
                    </el-form-item>
                    <el-form-item label=" ">
                        <el-table :data="formData.Rules" max-height="300px" fit>
                            <el-table-column prop="RangeApplicationName" label="部门范围" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="ReceiveEmployeeList" label="推送给" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    {{scope.row.ReceiveEmployeeList.map(s=>s.Name).toString()}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="SendEmployee" width="100" label="发起人">
                                <template slot-scope="scope">
                                    {{scope.row.SendEmployee.Name}}
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="80">
                                <template slot-scope="scope">
                                    <app-table-row-button @click="handleEditDepartment(scope.row, 'update')" :type="1"></app-table-row-button>
                                    <app-table-row-button @click="handleTableDelete(scope.row, scope.$index)" :type="3"></app-table-row-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </template>
            </el-form>
        </template>
        <template slot="footer">
            <app-button :buttonType="2" @click="handleClose"></app-button>
            <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
        </template>
    </app-dialog>
    <!-- 选择部门 -->
    <selectDepartment @closeDialog="closeDepartmentDialog" :dialogFormVisible="dialogDepartmentVisible" :dialogStatus="dialogDepartmentStatus"
    @saveSuccess="handleDepartmentSaveSuccess" :computationRuleId="computationRuleId" :disbArr="disbArr"></selectDepartment>
</div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */

import selectDepartment from './selectDepartment';
import * as SystemEmployeeRecordRemindApi from "@/api/personnelManagement/SystemEmployeeRecordRemind";
export default {
    /**名称 */
    name: "employee-record-msg-reminder",
    /**组件声明 */
    components: {
        selectDepartment
    },
    /**参数区 */
    props: {
        componentType: {
            type: Number,
            require: true,
            default: 1
        }
    },
    /**数据区 */
    data() {
        return {
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            /**表单模型 */
            formData: { 
                IsReminding: false,
                Validity: 0,
                RemindingTime: '09:00',
                RemindingRate: 1, // 0 仅1次 1 每天1次 2 每3天1次 3 每7天1次
                Rules: [],
            },
            /**表单规则 */
            formRules: {
                Validity: { fieldName: "有效期", rules: [{ required: true }] },
                RemindingTime: { fieldName: "提醒时间", rules: [{ required: true }] },
                RemindingRate: { fieldName: "提醒频率", rules: [{ required: true }] },
            },

            dialogDepartmentVisible: false,
            dialogDepartmentStatus: '',
            computationRuleId: '',
            disbArr: [],
        };
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {                
                this.formData = this.$options.data().formData
                // this.formData.RemindingTime = dayjs('09:00').format('HH:MM')
                if (val) {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        handleAddDepartment(){
            let disbArr = [];
            this.formData.Rules.map(s=> {
                disbArr = disbArr.concat(s.RangeApplication)
            })
            this.disbArr = disbArr;
            this.computationRuleId = '';
            this.dialogDepartmentStatus = 'create'
            this.dialogDepartmentVisible = true;
        },
        handleEditDepartment(row, optType) {
            this.dialogDepartmentStatus = optType
            this.computationRuleId = row.Id;
            this.dialogDepartmentVisible = true;
        },
        closeDepartmentDialog() {
            this.dialogDepartmentVisible = false
        },
        handleDepartmentSaveSuccess() {
            this.getDetail()
            this.closeDepartmentDialog()
        },
        handleTableDelete(row,index) {
            let self = this;
            self.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                SystemEmployeeRecordRemindApi.DeleteRecordRemindingSettingRule({id: row.Id}).then(res => {
                    self.$notify({
                        title: "成功",
                        message: "操作成功",
                        type: "success",
                        duration: 2000
                    });
                    // self.getList();
                    self.formData.Rules.splice(index, 1);
                });
            });
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this, formData = JSON.parse(JSON.stringify(self.formData));
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    self.buttonLoading = true;
                    SystemEmployeeRecordRemindApi.SetRecordRemindingSetting(formData).then(response => {
                        self.buttonLoading = false;
                        self.handleClose();
                    }).catch(err => {
                        self.buttonLoading = false
                    })
                }
            });
        },
        // 查询数据
        getDetail() {
            let self = this;
            self.loading = true;
            SystemEmployeeRecordRemindApi.GetRecordRemindingSetting({}).then(response => {
                self.loading = false;
                self.formData = Object.assign({}, self.formData, response)
            }).catch(err => {
                self.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
