<template>
  <div class="buttonBox cl">
    <span class="fl pointer" :class="{active:activeIndex == index}" v-for="(bld,index) in btnListData" @click="handleClick(bld,index)">{{bld.RegionalName || bld}}</span>
  </div>
</template>

<script>
export default {
  props:['btnListData'],
  name: "",
  components: {
    
  },
  filters: {
    
    
  },

  data() {
    return {
      activeIndex:0,
    };
  },
  computed: {
    
  },
  
  watch: {
    btnListData(val, oldVal){
      this.$emit('buttonData',val[0]);
    },
  },
  created() {
    
      
  },
  mounted() {
    this.$emit('buttonData',this.btnListData[0]);
  },
  methods: {
    handleClick(d,index){
      this.activeIndex=index;
      this.$emit('buttonData',d);
    }
  }
};
</script>

<style lang="scss" scoped>
.buttonBox{
  span{
    border-radius: 4px;
    border:1px solid #DCDFE6;
    padding:4px 8px;
    margin-right: 20px;
  }
  span.active{
    background:#409EFF;
    border-color:#409EFF;
    color:white;
  }
  span:hover{
    border-color:#409EFF;
  }
}
</style>