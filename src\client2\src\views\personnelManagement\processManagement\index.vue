<template>
<div class="app-container processManagement">
    <div class="left_wrapper">
        <br v-if="btnAddChildren==''" />
        <el-button v-if="btnAddChildren=='btnAddChildren'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">创建分类</el-button>
        <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
        <div class="treeBox">
            <el-tree class="elTree" v-loading='treeLoading' ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                    <span v-if="data.Level>0 && node.label !='默认' && node.label !='基础'" class="node-btn-area">
                    <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                        <span class="el-dropdown-link">
                        <i class="el-icon-more"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                        <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                        <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    </span>
                </span>
            </el-tree>
        </div>
    </div>
    <div class="bg-white" v-loading='loading'>
        <div class="cl">
            <app-table-form
                :label-width="'80px'"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="onResetSearch" :layoutMode='layoutMode'
                >
                <template slot="FuzzyQueryString">
                    <el-input style="width: 100%;" 
                        placeholder="搜索流程名称/备注..."
                        @clear='handleFilter'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                handleFilter()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.FuzzyQueryString"
                    ></el-input>
                </template>
                <template slot="EmployeeQueryString">
                    <el-input v-model.trim="listQuery.EmployeeQueryString" clearable placeholder="请输入人员名称/工号"></el-input>
                </template>
                <template slot="ApprovalType">
                    <el-select
                    style="width: 100%;"
                    class="sel-ipt"
                    v-model="listQuery.ApprovalType"
                    placeholder
                    clearable
                    >
                    <el-option v-for="item in processTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </template>
                <template slot="ApprovalRange">
                    <el-select
                    style="width: 100%;"
                    class="sel-ipt"
                    v-model="listQuery.ApprovalRange"
                    placeholder
                    clearable
                    >
                    <el-option v-for="item in scopeApplication" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </template>
                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                        <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                            <el-button type="primary">
                                {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="btnBatchClassify">调整分类</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </permission-btn>
                </template>
            </app-table-form>
        </div>
        <main class="__dynamicTabContentWrapper">
            <div class="cl list">
                <no-data v-if="customData.length == 0 && !loading"></no-data>
                <el-checkbox-group v-else v-model="checkGroupList">
                    <el-card :shadow="checkShadow(cd.Id)" :body-style="{ padding: '0' }" class="elCard fl" v-for="(cd,cdI) in customData" :key="cdI">
                        <div @click="checkGroupChange(cd, true)">
                            <div class="pd">
                                <div class="cl" style="display: flex;">
                                    <el-checkbox @change="checkGroupChange(cd, false)" class="notLabelText" v-if="cd.ApprovalCategory===2" :label="cd.Id"></el-checkbox>
                                    <span class="fl omit" style="font-weight:bold;width:200px;" :title="cd.ApprovalName">{{cd.ApprovalName}}</span>
                                    <template v-if="rowBtnIsExists('btnSwitch')">
                                        <el-switch class="fr elSwitch" disabled v-model="cd.IsVisible" @click.stop.native.prevent="handleSwitchChange(cd)"></el-switch>
                                    </template>
                                </div>
                                <div>
                                    <span class="title">流程类型：</span>
                                    <span>{{ cd.ApprovalType | processTypesFilter }}</span>
                                </div>
                                <div>
                                    <span class="title">适用范围：</span>
                                    <span>{{ cd.ApprovalRange | scopeApplicationFilter }}</span>
                                </div>
                                <div>
                                    <span class="title">分类：</span>
                                    <span v-if="cd.ApprovalCategory===1">基础流程</span>
                                    <span class="desc" v-else :title="cd.ClassifyName || '无'">{{ cd.ClassifyName || "无" }}</span>
                                </div>
                                
                                <div>
                                    <span class="title">备注：</span>
                                    <span class="desc" :title="cd.Remark">{{ cd.Remark || '无'}}</span>
                                </div>
                            </div>
                            <div class="cardFooter cl">
                                <el-button v-if="rowBtnIsExists('btnDel')&&isShowDel(cd)" class="elButton" type="text fr" style="color:red;" @click.stop="handleDel(cd)"><i class="el-icon-delete"></i>删除</el-button>
                                <el-button v-if="rowBtnIsExists('btnSetting')" class="elButton" type="text fr" style="color:#409EFF;" @click.stop="handleSetUp(cd,true)"><i class="el-icon-s-tools"></i>流程设置</el-button>
                                <el-button type="text fr" style="color:#F6A031;" @click.stop="handleRecordShow(cd)"><i class="el-icon-time"></i>申请记录</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-checkbox-group>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" layout="total, prev, pager, next, jumper" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange"/>
        </main>
    </div>
    <v-create
        @saveSuccess="handleCreateSaveSuccess"
        @closeDialog="handleCreateCloseDialog"
        :dialogFormVisible="dialogCreateVisible"
        :dialogStatus="dialogCreateFormStatus"
        :id="id" :selectClassifyId="selectClassifyId"
        :leaveShow="leaveShow">
    </v-create>
    <apply-record
        v-if="dialogRecordVisible"
        @closeDialog="handleRecordCloseDialog"
        :dialogFormVisible="dialogRecordVisible"
        :dialogStatus="dialogRecordFormStatus"
        :type='type'
        :id="recordId"></apply-record>
    <!--添加/修改 分类 弹窗组件区-->
    <classify-page :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyDialogFormVisible" @closeDialog="classifyCloseDialog" @saveSuccess="classifySaveSuccess"></classify-page>
    
    <!-- 批量设置分类 -->
    <batch-set @closeDialog="()=>dialogBatchModifyClassifyVisible=false" @saveSuccess="handleBatchModifyClassifySuccess" :dialogFormVisible="dialogBatchModifyClassifyVisible" :ids="checkGroupList"></batch-set>
</div>
</template>

<script>

import * as approvalManagement from "@/api/approvalManagement";
import vButtonList from '@/views/common/buttonList';
import vCreate from './create';
import applyRecord from './applyRecord';
import noData from "@/views/common/components/noData";
import { vars } from '../../workbench/myWorkbench/vars';
import * as classify from '@/api/classify'
import classifyPage from "./classify";
import batchSet from "./batchSet";

import { listToTreeSelect } from "@/utils";
export default {
    name: '',
    components: {
        vButtonList,
        vCreate,
        applyRecord,
        noData,
        classifyPage,
        batchSet,
    },
    filters: {
        processTypesFilter(val) {
            let obj = vars.processTypes.find(s => s.value == val)
            if (obj){
                return obj.label
            }
            return ''
        },
        scopeApplicationFilter(val) {
            let obj = vars.scopeApplication.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        }
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.ClassifyId = val.Id;
                    this.listQuery.PageIndex = 1;
                    this.checkGroupList = []
                    this.getList();
                }
            },
            immediate: true
        }
    },
    created() {
        this.btnTextValue();
    },
    computed: {

    },
    mounted() {
        // this.getBasicList()
        this.loadTreeData();
        // this.getList();
    },
    data() {
        return {
            layoutMode: 'simple',
            selectClassifyId: "",
            checkGroupList: [],
            recordId:'',
            leaveShow:false,
            type:1,
            processTypes:vars.processTypes,
            scopeApplication:vars.scopeApplication,
            total:0,
            loading:false,
            dialogRecordFormStatus:'',
            dialogRecordVisible:false,
            dialogCreateFormStatus:'',
            dialogCreateVisible:false,
            listQuery:{
                PageIndex:1,
                PageSize:20,
                FuzzyQueryString: '',
                EmployeeQueryString: '',
                ApprovalType: null,
                ApprovalRange: null,
            },
            tableSearchItems: [{
                    prop: "FuzzyQueryString",
                    label: "模糊查询",
                    mainCondition: true
                },
                {
                    prop: "EmployeeQueryString",
                    label: "人员查询",
                },
                {
                    prop: "ApprovalType",
                    label: "流程类型",
                },
                {
                    prop: "ApprovalRange",
                    label: "使用范围",
                },
            ],
            basisData:[],
            customData:[],
            id:'',

            dialogBatchModifyClassifyVisible: false,


            /******************* 权限 *******************/
            btnAddChildren: '',
            /******************* 树 *******************/
            /**树节点弹窗 */
            classifyDialogFormVisible: false,
            classifyDialogStatus: "create",
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
        }
    },
    methods:{
        isShowDel(row) {
            return row.ApprovalCategory===2
        },
        checkGroupChange(row, type) {
            // console.log(111, type)
            if(row.ApprovalCategory===2){
                let index = this.checkGroupList.findIndex(s=>s === row.Id);
                if (index>-1) {
                    this.checkGroupList.splice(index, 1)
                } else {
                    this.checkGroupList.push(row.Id)
                }
            }
        },
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnAddChildren") {
                    this.btnAddChildren = "btnAddChildren"
                }
                if (item["DomId"] == "btnAdd") {
                    this.btnAdd = "btnAdd"
                }
                if (item["DomId"] == "btnEdit") {
                    this.btnEdit = "btnEdit"
                }
            })
        },
        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId != 'btnAddChildren')
            }
            return []
        },
        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleCreate();
                    break;
                case "btnBatchClassify":
                    this.handleBatchModifyClassify();
                    break;
                default:
                    break;
            }
        },
        // 显示批量修改分类弹窗
        handleBatchModifyClassify() {
            if (this.checkGroupList.length>0) {
                this.dialogBatchModifyClassifyVisible=true;
            } else {
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        handleBatchModifyClassifySuccess() {
            this.dialogBatchModifyClassifyVisible=false
            this.checkGroupList = []
            this.getList() //刷新列表
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.checkGroupList = []
            this.getList() //刷新列表
        },
        onResetSearch() {
            this.listQuery.FuzzyQueryString = '';
            this.listQuery.EmployeeQueryString = '';
            this.listQuery.ApprovalType = null;
            this.listQuery.ApprovalRange = null;
            this.checkGroupList = []
            this.getList() //刷新列表
        },
        handleDel(cd){
            this.checkGroupList = []
            this.$confirm('确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                 approvalManagement.del([cd.Id]).then(res => {
                    this.$notify({
                        title: "提示",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    // this.listQuery.PageIndex=1;
                    this.getList();
                }).catch(err => {});
            }).catch(() => {
                         
            });
           
        },
        handleSetUp(cd,t){
            this.id=cd.Id;
            this.dialogCreateFormStatus="update";
            this.leaveShow=t;
            this.checkGroupList = []
            this.selectClassifyId = ""
            this.dialogCreateVisible=true;
        },
        //获取基础流程
        getBasicList() {
            let postDatas = {
                PageSize: 1000,
                PageIndex: 1,
                ApprovalCategory: 1, //基础流程
            }
            approvalManagement.getList(postDatas).then(res => {
                this.basisData=res.Items;
                // this.total=res.Total
            }).catch(err => {
            })
        },
        getList(){
            this.loading=true;
            let postDatas = JSON.parse(JSON.stringify(this.listQuery))
            // postDatas.ApprovalCategory = 2 //自定义流程
            approvalManagement.getList(postDatas).then(res => {
                this.customData=res.Items;
                this.total=res.Total;
                this.loading=false;
            }).catch(err => {
                this.loading=false;
            })
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size
            this.getList()
        },
        handleRecordShow(d){
            this.type=d.ApprovalType;
            this.recordId=d.Id;
            this.checkGroupList = []
            this.dialogRecordVisible=true;
        },
        handleRecordCloseDialog(){
            this.dialogRecordVisible=false;
        },
        handleCreate(){
            this.selectClassifyId = ""
            if (this.checkedNode.Name!='全部'&&this.checkedNode.Name!='基础') {
                this.selectClassifyId = this.checkedNode.Id
            }
            this.dialogCreateFormStatus='create';
            this.dialogCreateVisible=true;
        },
        handleCreateCloseDialog(){
            this.dialogCreateVisible=false;
        },
        handleCreateSaveSuccess(){
            // this.listQuery.PageIndex=1;
            this.getList();
            this.handleCreateCloseDialog();
        },
        handleSwitchChange(d){
            if(d.IsVisible){
                const h = this.$createElement;
                this.$msgbox({
                    title: '',
                    message: h('p', null, [
                        h('div', { style: 'text-align: center' }, '关闭后相关人员将不可见该流程 '),
                        h('div', { style: 'text-align: center' }, '确定要关闭该流程吗？')
                    ]),
                    showCancelButton: true,
                    closeOnClickModal: false,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    beforeClose: (action, instance, done) => {
                        if (action === 'confirm') {
                            instance.confirmButtonLoading = true;
                            instance.confirmButtonText = '确认中...';
                            d.IsVisible=false;
                            approvalManagement.editVisible({id:d.Id,isVisible:d.IsVisible}).then(res => {
                                done();
                                instance.confirmButtonLoading = false;
                            })
                        } else {
                            done();
                        }
                    }
                }).then(action => {
                    console.log(action)
                });
            }else{
                d.IsVisible=true;
                approvalManagement.editVisible({id:d.Id,isVisible:d.IsVisible}).then(res => {
                    
                })
            }
        },

        classifySaveSuccess(d) {
            if (!d) {
                this.classifyCloseDialog();
            }
            this.loadTreeData();
        },
        classifyCloseDialog() {
            this.classifyDialogFormVisible = false;
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let _this = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: 7
            };
            _this.treeLoading = true
            classify.getListPage(paramData).then(response => {
                _this.treeLoading = false
                response.Items.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });
                _this.treeData = listToTreeSelect(response.Items);

                if (_this.treeData && _this.treeData.length > 0) {
                if (
                    !(
                        _this.checkedNode &&
                        response.Items.find(t => {
                            return t.Id == _this.checkedNode.Id;
                        })
                    )
                ) {
                    _this.checkedNode = _this.treeData[0];
                }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                    });
                }
            }).catch(err => {
                _this.treeLoading = false
            });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.classifyDialogStatus = "create";
            this.checkGroupList = []
            this.classifyDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.classifyDialogStatus = "create";
                    this.checkGroupList = []
                    this.classifyDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.classifyDialogStatus = "update";
                    this.checkGroupList = []
                    this.classifyDialogFormVisible = true;
                    break;
                case "delete":
                    this.checkGroupList = []
                    this.handleDeleteArea(data);
                    break;
                default:
                break;
            }
        },
        /**删除树节点 */
        handleDeleteArea(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            let paramData = { ids: [data.Id], businessType: 7 };
            this.$confirm(`是否确认删除${data.Name}?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                classify.del(paramData).then(res => {
                if (this.checkedNode && this.checkedNode.Id == data.Id) {
                    this.checkedNode = null;
                }
                this.loadTreeData();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                });
            });
        },
        checkShadow(Id){
            return this.checkGroupList.some(s=>s===Id) ? 'always' : 'hover'
        },
    }
}
</script>
<style scoped>
.processManagement >>>.el-checkbox-group{
    font-size: inherit;
}
.notLabelText >>>.el-checkbox__label{
    display: none;
}
</style>
<style lang="scss" scoped>
.processManagement{
    display: flex;
    .left_wrapper{
        height: 100%;
        display: flex;
        flex-direction: column;
        width: 249px;
        border-right: 1px solid #dcdfe6;
        .treeBox{
            flex: 1;
            overflow-y: auto;
        }
          .custom-tree-node {
            display: block;
            width: 100%;
            position: relative;
            box-sizing: border-box;
            padding-right: 24px;

            .node-title {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .node-btn-area {
                position: absolute;
                right: 0;
                top: 0;
                width: 23px;
                height: 16px;
            }
        }
    }
    .bg-white{
        flex: 1;
        width: calc(100% - 250px);
        left: 250px;
        padding-bottom: 0;
    }
}
.elCard{
    width:calc(20% - 10px);
    padding:10px 0 8px 0;
    margin-bottom:10px;
    min-width: 205px;
    .pd{
        padding:0 10px;
        // >div:nth-child(2){
        //     margin-bottom:6px;
        // }
        // >div:nth-child(1){
        //     margin-bottom:6px;
        // }
        .notLabelText{
            padding-right: 6px;
        }
        >div:not(:last-child){
            margin-bottom:6px;
        }
        .title{
            display: inline-block;
            width: 70px;
            text-align: right;
        }
        >div{
            display: flex;
            .desc{
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        
    }
    .cardFooter{
        padding:10px 10px 0 0;
        .elButton{
            margin-left:10px;
        }
    }
}
.elCard:not(:last-child){
    margin-right:10px;
}
.bg-white{
    padding:10px;
    >div:first-child{
        padding-bottom:10px;
        border-bottom: 1px solid #dcdfe6;
    }
    main{
        height:calc(100% - 50px);
        // overflow-y:auto;
        >div{
            margin-top:10px;
        }
        >div.pagination-container{
            margin-top: 0;
        }
    }
}

.list{
    flex: 1;
    overflow-y: auto;
}
</style>
