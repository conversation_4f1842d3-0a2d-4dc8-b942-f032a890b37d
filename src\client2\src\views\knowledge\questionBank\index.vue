<!--软件实施常见问题管理-->
<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="pageWrapper">
        <!--左侧树-->
        <div class="product-list"><br v-if="btnAddChildren==''" />
          <el-button v-if="btnAddChildren=='btnAddChildren'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">创建题库分类</el-button>
          <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <div class="treeBox" v-loading='treeLoading'>
            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                <span v-if="data.Level>0" class="node-btn-area">
                  <el-dropdown trigger="click" style="float: right; padding-right: 9px;" v-if="btnAddChildren=='btnAddChildren'"
                  @command="handleCommand($event, node, data)">
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
        <!--右侧内容-->
        <div class="content-wrapper __dynamicTabContentWrapper">
          <page-title :showBackBtn='false'>
              <div slot="def">
                  <tags :items="searchTypesData" v-model="listQuery.DifficultyLevel">
                      <template v-for="t in searchTypesData" :slot="t.value">{{ t.label }}</template>
                  </tags>
              </div>
          </page-title>
          <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="dataSourceList" :isShowAllColumn="true" :optColWidth='120'
            :loading="tableLoading" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="true" :layoutMode='layoutMode'
            :isShowBtnsArea='false' @sortChagned="handleSortChange" @rowSelectionChanged="rowSelectionChanged">

              <template slot="QuestionType" slot-scope="scope">
                <span class="item-status" :style="{backgroundColor: getQuestionTypeObj(scope.row.QuestionType).bgColor,
                  color: getQuestionTypeObj(scope.row.QuestionType).color}">
                  {{ getQuestionTypeObj(scope.row.QuestionType).label }}
                </span>
              </template>
              <template slot="DifficultyLevel" slot-scope="scope">
                <span class="item-status" :style="{color: getDifficultyLevelObj(scope.row.DifficultyLevel).color}">
                  {{ getDifficultyLevelObj(scope.row.DifficultyLevel).label }}
                </span>
              </template>

              <template slot="LastUpdateTime" slot-scope="scope">
                {{scope.row.LastUpdateTime | dateFilter("YYYY-MM-DD HH:mm")}}
              </template>

              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                  <template slot="KeyWords">
                    <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索题目"></el-input>
                  </template>

                  <template slot="QuestionType">
                    <div class="month-range-wrapper">
                      <div class="start-month">
                        <el-select style="width: 100%;" clearable v-model="listQuery.QuestionType" placeholder="请选择">
                          <el-option v-for="item in questionTypes" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                  </template>

                  <template slot="LastUpdateTime">
                    <el-date-picker style="width: 100%;" v-model="listQuery.LastUpdateTime" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" :clearable="false" :default-time="['00:00:00', '23:59:59']">></el-date-picker>
                  </template>

                  <!-- 表格批量操作区域 -->
                  <template slot="btnsArea">
                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                      <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                          <el-button type="primary">
                              {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                          </el-button>
                          <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item command="batchEditClassify">修改分类</el-dropdown-item>
                              <el-dropdown-item command="batchDifficultyLevel">难易等级</el-dropdown-item>
                          </el-dropdown-menu>
                      </el-dropdown>
                    </permission-btn>
                  </template>
                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button @click="handleEdit(scope.row,'detail')" :type="2"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleEdit(scope.row, 'update')" :type="1"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
              </template>

            </app-table>
          </div>
          <!----------------------------------------- 分页 ------------------------------------------->
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>
    <!--添加/修改 弹窗组件区-->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="implementationProblemId" :selectClassifyId="selectClassifyId" @reload="getList"></create-page>

    <!--添加/修改 分类 弹窗组件区-->
    <classify-page :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyDialogFormVisible" @closeDialog="classifyCloseDialog" @saveSuccess="classifySaveSuccess"></classify-page>
    
    <!-- 批量修改分类/难易等级  -->
    <batch-classify @closeDialog="batchClassifyVisible=false" @saveSuccess="handleBatchClassifySuccess" :component-type="componentType"
        :dialogFormVisible="batchClassifyVisible" :ids="multipleSelectionIds"></batch-classify>

  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as classify from '@/api/classify'
import * as questionBankApi from '@/api/knowledge/questionBankManagement'
import createPage from './create'
import classifyPage from "./classify";
import { listToTreeSelect } from "@/utils";
import { vars } from '../common/vars'
import batchClassify from "./batchClassify";

export default {
  /**名称 */
  name: "question-bank",
  mixins: [indexPageMixin],
  /**组件声明 */
  components: {
    createPage,
    classifyPage,
    batchClassify
  },
  filters: {
    // statusFilter(status) {
    //   let obj = StatusList.find(
    //     s => s.value == status
    //   );
    //   if (obj) {
    //     return obj.label;
    //   }
    //   return status;
    // },
  },
  /**数据区 */
  data() {
    return {
      // 列表多选  id集合
      multipleSelectionIds: [],

      // 批量修改分类
      batchClassifyVisible: false,
      componentType: 1,


      searchTypesData: [
        { label: "全部（0）", value: 0 },
        { label: "初级（0）", value: 1 },
        { label: "中级（0）", value: 2 },
        { label: "高级（0）", value: 3 },
      ],
      searchTypes: vars.questionBankEnum.searchTypes, // 顶部筛选条件
      questionTypes:  vars.questionBankEnum.questionTypes, // 顶部筛选条件
      
      implementationProblemId: '',

      /******************* 弹窗 *******************/
      dialogStatus: 'create',
      dialogFormVisible: false,
      selectClassifyId: "",

      /******************* 表格 *******************/
      layoutMode: 'simple',
      tableLoading: false,
      dataSourceList: [],
      total: 0,

      listQuery: {
        KeyWords: '',
        ClassifyId: null,
        LastUpdateTime: [],
        QuestionType: null, // 题目类型  (单选 多选 判断题)
        DifficultyLevel: 0, // 题目级别  (初级 中级 高级)
      },

      multipleSelection: [],
      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "QuestionType", label: "题型" },
        { prop: "LastUpdateTime", label: "最后修改时间" },
      ],

      tabColumns: [
        {
          attr: { prop: "QuestionDescription", label: "试题题目", showOverflowTooltip: true }
        },
        {
          attr: { prop: "QuestionType", label: "题型", sortable: 'QuestionType' , width: 120}, slot: true
        },
        {
          attr: { prop: "ClassifyName", label: "分类", showOverflowTooltip: true, width: 240 }
        },
        {
          attr: { prop: "DifficultyLevel", label: "难易等级", sortable: 'DifficultyLevel', width: 120 }, slot: true
        },
        // {
        //   attr: { prop: "CreateEmployeeName", label: "创建人" }
        // },
        {
          attr: { prop: "LastUpdateTime", label: "最后修改时间", width: 150, sortable: 'LastUpdateTime' }, slot: true
        },
      ],

      /******************* 权限 *******************/
      btnAddChildren: '',
      btnAdd: '',
      btnEdit: '',


      /******************* 树 *******************/
      /**树节点弹窗 */
      classifyDialogFormVisible: false,
      classifyDialogStatus: "create",
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },

    };
  },
  computed: {},
  watch: {
    // "listQuery.visitRecordTypeEnum"() {
    //     this.listQuery.PageIndex = 1
    //     this.getList();
    // },
    "listQuery.DifficultyLevel"() {
        this.listQuery.PageIndex = 1
        this.getList();
    },
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.ClassifyId = val.Id;
          this.listQuery.PageIndex = 1;
          this.GetDifficultyLevelAllData()
          this.getList();
        }
      },
      immediate: true
    }
  },
  created() {
    this.btnTextValue();
  },
  mounted() {
    this.loadTreeData();
  },
  methods: {
    // 获取顶部 难易等级数据计数
    GetDifficultyLevelAllData(){
      let self = this;
      questionBankApi.GetDifficultyLevelAllData({classifyId: self.checkedNode?self.checkedNode.Id:''}).then(response => {
        self.searchTypesData = response || self.searchTypesData;
      });
    },
    // 难易等级 文字转换
    getDifficultyLevelObj(val) {
        return this.searchTypes.filter(s=>s.value!=0).find(
            s => s.value == val
        ) || {};
    },
    // 问题类型 文字转换
    getQuestionTypeObj(val) {
        return this.questionTypes.find(
            s => s.value == val
        ) || {};
    },
    handleFilterBtn(btns) {
      if (btns && btns.length > 0) {
        return btns.filter(s => s.DomId != 'btnAddChildren')
      }
      return []
    },
    /******************* 表格事件 *******************/
    /**加载数据 */
    getList() {
      let self = this, postData = JSON.parse(JSON.stringify(self.listQuery));
      if (self.checkedNode.Id) {
        self.listQuery.ClassifyId = self.checkedNode.Id;
      }
      if (postData.LastUpdateTime.length > 0) {
        postData.LastUpdateTimeStart = postData.LastUpdateTime[0]
        postData.LastUpdateTimeEnd = postData.LastUpdateTime[1]
      }
      postData = this.assignSortObj(postData);
      delete postData.LastUpdateTime
      self.tableLoading = true
      questionBankApi.getList(postData).then(response => {
        self.tableLoading = false
        self.total = response.Total;
        self.dataSourceList = response.Items;
      }).catch(err => {
        self.tableLoading = false
      });
    },

    /**添加 */
    handleAdd(activeName) {
      this.selectClassifyId = this.checkedNode.Id;
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },

    /**修改 */
    handleEdit(row, optType) {
      this.selectClassifyId = "";
      this.implementationProblemId = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    /**删除 */
    handleDelete(row) {
      let ids = [];
      ids.push(row.Id);
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        questionBankApi.del(ids).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    handleSortChange({ column, prop, order }) {
      this.sortObj = { prop, order, };
      this.getList();
    },

    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    onResetSearch() {
      this.listQuery.LastUpdateTime = []
      this.listQuery.QuestionType = null
      this.listQuery.KeyWords = ""
      this.getList();
    },

    btnTextValue() {
      let btns = this.topBtns
      btns.forEach(item => {
        if (item["DomId"] == "btnAddChildren") {
          this.btnAddChildren = "btnAddChildren"
        }
        if (item["DomId"] == "btnAdd") {
          this.btnAdd = "btnAdd"
        }
        if (item["DomId"] == "btnEdit") {
          this.btnEdit = "btnEdit"
        }
      })
    },

    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleAdd("create");
          break;
          // 修改分类
        case "batchEditClassify":
          this.handleClassify(1);
          break;
          // 难易等级
        case "batchDifficultyLevel":
          this.handleClassify(2);
          break;
      }
    },

    handleClassify(type){
      if (this.multipleSelection.length>0) {
        this.multipleSelectionIds = this.multipleSelection.map(s=>{return s.Id});
        this.componentType = type;
        this.batchClassifyVisible=true;
      } else {
        this.$message({
            message: "至少选择一条",
            type: "error"
        });
      }
    },

    /******************* 弹窗相关 *******************/
    closeDialog() {
      this.dialogFormVisible = false
    },
    handleSaveSuccess(d) {
      if (!d) {
        this.closeDialog();
      }
      this.getList();
    },

    handleBatchClassifySuccess() {
      this.getList();
      this.batchClassifyVisible = false
    },
    classifySaveSuccess(d) {
      if (!d) {
        this.classifyCloseDialog();
      }
      this.loadTreeData();
    },
    classifyCloseDialog() {
      this.classifyDialogFormVisible = false;
    },


    // 表格行选择
    rowSelectionChanged(rows) {
        this.multipleSelection = rows;
    },
    checkPremissBtns(domId) {
      return this.rowBtns.findIndex(b => b.DomId == domId) > -1
    },
    /******************* 树事件 *******************/
    loadTreeData() {
      let self = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 9
      };
      self.treeLoading = true
      classify.getListPage(paramData).then(response => {
        self.treeLoading = false
        response.Items.unshift({
          Id: "",
          Name: "全部",
          Level: 0,
          ParentId: null
        });
        self.treeData = listToTreeSelect(response.Items);

        if (self.treeData && self.treeData.length > 0) {
          if (
            !(
              self.checkedNode &&
              response.Items.find(t => {
                return t.Id == self.checkedNode.Id;
              })
            )
          ) {
            self.checkedNode = self.treeData[0];
          }
        } else {
          self.checkedNode = null;
        }
        if (self.checkedNode) {
          self.$nextTick(() => {
            self.$refs.treeRef.setCurrentKey(self.checkedNode.Id);
          });
        }
      }).catch(err => {
        self.treeLoading = false
      });
    },
    /**添加顶级节点 */
    addTopLevel() {
      this.paramNode = {
        Id: null,
        Name: "",
        Level: 0
      };
      this.classifyDialogStatus = "create";
      this.classifyDialogFormVisible = true;
    },
    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    /**树下拉事件 */
    handleCommand(optType, node, data) {
      switch (optType) {
        case "create":
          this.paramNode = data;
          this.classifyDialogStatus = "create";
          this.classifyDialogFormVisible = true;
          break;
        case "update":
          this.paramNode = data;
          this.classifyDialogStatus = "update";
          this.classifyDialogFormVisible = true;
          break;
        case "delete":
          this.handleDeleteArea(data);
          break;
        default:
          break;
      }
    },
    /**删除树节点 */
    handleDeleteArea(data) {
      if (data.children && data.children.length > 0) {
        this.$notify({
          title: "提示",
          message: "请先删除子级",
          type: "error",
          duration: 2000
        });
        return;
      }
      let paramData = { ids: [data.Id], businessType: 9 };
      this.$confirm(`是否确认删除${data.Name}?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        classify.del(paramData).then(res => {
          if (this.checkedNode && this.checkedNode.Id == data.Id) {
            this.checkedNode = null;
          }
          this.loadTreeData();
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
        });
      });
    },
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.bg-white {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      //   padding: 10px;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
