@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";
// @import "../font-style/font.css";

body {
  margin: 0;
  border: 0;
  padding: 0;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: PingFang, PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 14px;
  color: $text-regular;
}

// 处理：el-textarea 和 input 显示文字样式不一样
.el-textarea__inner{
  font-family: PingFang, PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

// label:not(.el-radio) {
//   font-weight: 700 !important;
// }

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
ul,
li {
  list-style: none;
  padding: 0px;
  margin: 0px;
}
i {
  font-style: normal;
}
div:focus {
  outline: none;
}
.cl:after,
.clearfix:after {
  content: "\20";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
}
.cl,
.clearfix {
  zoom: 1;
}
.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}
.pl-10 {
  padding-left: 10px;
}
.pr-10 {
  padding-right: 10px;
}
.line-40 {
  height: 40px;
  line-height: 40px;
}
.red{
  color:#F56C6C;
}
.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  margin-bottom: 20px;
  display: block;
  line-height: 36px;
  font-size: 15px;
  font-family: "PingFang SC", "Source Sans Pro", "Helvetica Neue", Arial, sans-serif;
  a {
    color: #337ab7;
    cursor: pointer;
    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.warn-content {
  background: rgba(66, 185, 131, 0.1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: 0.05rem;
  a {
    color: #42b983;
    font-weight: 600;
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  // background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);
  background: #d0d0d0;
  .subtitle {
    font-size: 20px;
    color: #fff;
  }
  &.draft {
    background: #d0d0d0;
  }
  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: rgb(32, 160, 255);
  }
}

// .filter-container {
//     padding-bottom: 0px;
//     .filter-item {
//         display: inline-block;
//         vertical-align: middle;
//         margin-bottom: 0px;
//     }
// }

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

//table 头部居中
// table thead th {
//     text-align: center;
// }

// 设置树形表格收起展开图标。
//有子节点 且未展开
body .el-table tbody{
  font-size: 13px!important; //表格内容字体覆盖
  .el-icon-arrow-right:before {
    background: url("../assets/images/close.png") no-repeat;
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    background-size: 16px;
    position: absolute;
    top: 2px;
    right: 2px;
  }
}

//展开按钮
.close-img-btn{
  background: url("../assets/images/open.png") no-repeat;
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
  position: absolute;
  top: 2px;
  right: 2px;
}
// 有子节点 且已展开
body .el-table tbody{
  //el-table
  .el-table__expand-icon--expanded {
    .el-icon-arrow-right:before {
      @extend .close-img-btn;
    }
  }

  //umy-ui 中的 table，收起展开图标设置
  .tree--btn-wrapper{
    margin-top: -9px;
    .el-icon-arrow-down:before{
      @extend .close-img-btn;
    }
  }

} 

body .elx-table{
  .elx-header--row{
    th{
      height: 40px!important;
    }
  }
}

.elx-table .elx-body--column.col--ellipsis, 
.elx-table .elx-footer--column.col--ellipsis, 
.elx-table .elx-header--column.col--ellipsis, 
.elx-table .elx-editable .elx-body--column{
  height: 40px!important;
}

//干线任务，树形表格特有央视，解决冻结列，表格错乱问题
.--my-task-tab{
  .plTableBox .el-table__body-wrapper tbody td, 
  .el-table__fixed .el-table__fixed-body-wrapper td{
    height: 45px!important;

    // 注意：u-table 必须配后css设置表格属性 :row-height="45"
  }
}



body .el-table [class*=el-table__row--level] .el-table__expand-icon{
  margin-right: 0;
}


// 没有子节点
body .el-table tbody .el-table__placeholder::before {
  //   background: url("") no-repeat;
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
}
// 展开折叠时不进行动画，否则图标会旋转
body .el-table tbody .el-table__expand-icon--expanded {
  transform: none;
}

// end 设置树形表格收起展开图标。


/* 表格头部的排序图标  高度 的问题 start by 2022-01-19 */
.el-table .caret-wrapper{
  height: 23px;
}
.el-table .sort-caret.ascending{
  top: 0px;
}
.el-table .sort-caret.descending{
  bottom: 1px;
}

/* 表格头部的排序图标  高度 的问题 end */


//很多表格中会用到 app-table-row-button（基于 el-link 封装的），不能影响自定义按钮的高度（下划线离文字距离很大）
.el-table .el-link--inner{
  line-height: normal;
}


// table td 上下间距减小
.el-table td {
  padding: 3px 0;
}
.el-table th {
  // padding: 10px 0!important;
  // height: 40px!important;
  // background: #FAFBFF!important;
  font-size: 13px;
  font-weight: 600;
  // padding-top: 0!important;
  // padding-bottom: 0!important;
  background: $bg-color-1!important;

  //表格最后一列（自动生成列，如果有y轴滚动条，会生成10px宽的th）
  &:last-child.gutter{
    background-color: transparent!important;
    border-color: transparent!important;
  }
}

.plTableBox .el-table thead{
  color: $text-regular;
  th{
    .cell{
      >span:first-child{
        font-weight: 600;
      }
    }
  }
}


// .el-table::before{
//   height: 0!important;
// }

// .el-table thead .cell{
//   line-height: normal;
// }

// .el-table tbody{
//   font-size: 14px!important;
// }

.body-small .el-card__body {
  padding: 10px 5px;
}

//分页间距修改
.pagination-container {
  background: #fff;
  padding: 10px !important;
  // border-left:1px solid #efefef;
  // border-right:1px solid #efefef;
  .is-in-pagination {
    margin-left: 5px;
  }
}


.card-body-none .el-card__body {
  padding: 0;
  margin: -1px;
  padding-bottom: 10px;
}

.app-container {
  // background-color: #efefef;
  padding: 10px;
}

.el-card__header {
  height: 34px;
  line-height: 34px;
  width: 100%;
  color: #666;
  padding: 0 !important;
  font-weight: bold;
  padding-left: 9px !important;
  font-size: 14px;
}

.bg-white {
  background-color: $color-white;
}


.dialog-mini {
  .el-dialog__header {
    flex-shrink: 0;
    height: 47px;
    display: flex;
    align-items: center;
    // line-height: 47px;
    border-radius: 4px 4px 0 0;
    // border-bottom: 1px solid #eee;
    overflow: hidden;
    // background-color: #f8f8f8;
    padding: 0 10px;
    // background: linear-gradient(90deg, #5068FF 0%, #8C6DFF 100%);
    border-bottom: 1px solid $border-color-lighter;
    .el-dialog__title {
      height: 100%;
      font-size: 16px;
      font-weight: 600;
      color: $text-primary;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      display: inline-block;
      display: flex;
      align-items: center;
    }
    .el-dialog__headerbtn {
      font-size: 24px;
      top: 10px;
      right: 10px;
      cursor: pointer;
      // .el-dialog__close{
      //   color: rgba($color: $text-second-color, $alpha: .5);
      //   // &:hover{
      //   //   color: $text-second-color;
      //   // }
      // }


      // i:before{
      //   // color: #fff;
      //   color: $text-second-color;
      //   // &:hover{
      //   //   color: rgba($color: red, $alpha: .5);
      //   // }
      // }
      // i:before:hover{
      //   color: rgba($color: red, $alpha: .5);
      // }
    }
  }
  .el-dialog__footer {
    flex-shrink: 0;
    height: 60px;
    width: 100%;
    text-align: right;
    padding: 0px 10px;
    border-radius: 0 0 4px 4px;
    // border-top: 1px solid #eee;
    // background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // line-height: 50px;
    border-top: 1px solid $border-color-lighter;
    position: relative;
    >div{
      width: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .el-dialog__body {
    padding: 10px;
    padding-top: 0;
    padding-bottom: 0;
    // padding-right: 0;
    .el-form {
      // padding-right: 20px;
      padding-top: 10px;
    }
  }

  &.clear-padding {
    .el-dialog__body{
      padding: 0!important;
    }
  }

  .el-form-item__label {
    font-size: 14px;
    color: $text-secondary;
    // font-weight: normal !important;
  }
  .el-form-item.el-form-item--small {
    margin-bottom: 18px;
  }
  .el-form-item.is-error.is-required.el-form-item--small,
  .el-form-item.is-error.el-form-item--small {
    margin-bottom: 18px;
  }
}

//不显示弹框header、footer 分割线
.dialog-mini.not-show-line {
  .el-dialog__header {
    border-bottom: none;
  }
  .el-dialog__footer {
    border-top: none;
  }
}

//自定义弹框样式，不需要通用的padding等
.cus-dialog{
  .el-dialog__body {
    padding: 0;
    .el-form {
      // padding-right: 20px;
      padding: 0;
    }
  }
}

// .el-table--border{
//  border-color:#ebeef5;
// }

.compent-dialog-body {
  margin: -10px;
  .table {
    margin-top: 10px;
    border: 1px solid #ebeef5;
    border-bottom: 0;
    border-right: 0;
  }
  .content {
    padding: 10px;
  }
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.clearfix {
  display: inline-block;
}
/* Hides from IE-mac \*/
* html .clearfix {
  height: 1%;
}
.clearfix {
  display: block;
}


.gender-male {
  color: #597db7;
  background: #ebf2fe;
}

.gender-female {
  color: #ce4a93;
  background: #feecf6;
}

.gender-male,
.gender-female {
  border: none;
}

// .el-dropdown-menu--mini .el-dropdown-menu__item{
//     line-height: 36px!important;
// }

.el-button + .el-button {
  margin-left: 4px;
}

.el-picker-panel__footer .el-button {
  margin-left: 10px;
}
//el-card底部没有元素时footer会消失的问题
.wrapper
  .app-container
  .page-wrapper
  .content-wrapper
  .content
  .list
  .card-wrapper
  .footer {
  height: 28px;
}

//全局通用样式设置
.app-main {
  padding: 10px;
  box-sizing: border-box;
}
.projectListBox {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.app-container {
  position: absolute;
  left: 10px;
  top: 10px;
  background: white;
  width: calc(100% - 20px);
  min-height: calc(100% - 20px);
  padding: 0;
  margin-bottom: 10px;
}
.app-container > .bg-white {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: white;
  overflow-y: auto;
}
.app-containe > .bg-white {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  overflow-y: auto;
}
.app-containe > .bg-white .page-wrapper {
  padding-top: 0 !important;
}
.page-wrapper {
  position: absolute;
  left: 0;
  // top: 40px;
  top: 0;
  width: 100%;
  // min-height: calc(100% - 50px);
  min-height: 100%;
  padding-bottom: 0;
  margin-bottom: 10px;
}
.dataBoard .el-dialog__header {
  border-bottom: 1px solid #eee;
}
.dataBoard .el-dialog__body {
  padding: 10px 20px;
}
.dataBoard .el-dialog__footer {
  border-top: 1px solid #eee;
  text-align: left;
}
.screen .list-wrapper .tag-list {
  padding: 8px !important;
  margin-bottom: 4px !important;
}
.teamWrapper .list-wrapper .tag-list {
  padding: 8px !important;
  margin-bottom: 4px !important;
}
.list-wrapper .tag-list .item-opts {
  padding: 5px;
}
.list-wrapper .tag-list.active {
  background: #ecf5ff !important;
}
.list-wrapper .tag-list:not(.active):hover {
  background: #f5f7fa !important;
}
.elForm .list-wrapper {
  padding-right: 2px !important;
}
.foldingBox {
  border-top: 1px solid #eee;
  padding: 10px 10px 10px 30px;
}
.demand-list .tag-list,
.question-list .tag-list,
.task-list .tag-list {
  line-height: 16px;
}
.demandInfo .el-tabs__header,
.taskInfo .el-tabs__header,
.questionInfo .el-tabs__header {
  margin-bottom: 0;
}
.demandInfo .el-tabs--top,
.taskInfo .el-tabs--top,
.questionInfo .el-tabs--top {
  padding: 10px 10px 0px;
}
.demandInfo .tabsContent,
.taskInfo .tabsContent,
.questionInfo .tabsContent {
  padding: 0 10px 0 10px;
}
.demandInfo .elCollapse1,
.taskInfo .elCollapse1,
.questionInfo .elCollapse1 {
  border-top: 0;
  border-bottom: 0;
}
.demandInfo .el-collapse-item .listDiv,
.taskInfo .el-collapse-item .listDiv,
.questionInfo .el-collapse-item .listDiv {
  text-align: center;
  padding-right: 0px;
}
.demandInfo .tabsContent .el-textarea,
.taskInfo .tabsContent .el-textarea,
.questionInfo .tabsContent .el-textarea {
  margin: 10px 0;
}

.el-dropdown-menu--mini{
  padding: 0;
}

.el-dropdown-menu--mini .el-dropdown-menu__item {
  padding: 5px 14px;
}

// hover:#f5f7fa;
//active:#ecf5ff;

//超出省略
.omit {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

.page-wrapper .product-list {
  width: 250px !important;
}
.content-wrapper {
  width: calc(100% - 250px) !important;
}
.page-wrapper .product-list .item-warpper .item-title {
  display: flex;
  align-items: center;
}
.page-wrapper .product-list .item-warpper .item-title .titleLabel {
  max-width: calc(100% - 50px);
}

.custom-prompt-title-required p:before{
  content: "* ";
  color:red;
  font-weight:bold;
}

//工作台团队样式调整
// .teamWrapper .list-wrapper .tag{
//     margin:0!important;
// }
// .demand-list .list-wrapper .tag{
//     margin:0!important;
// }

.img-avatar-shadow{
  box-shadow: #A29E9E 1px 1px 3px;
}

.label-wrapper{
  display: inline-block;
  .label-wrapper-content{
      display: flex;
      align-items: center;
      position: relative;
  }
}

.sp-required-asterisk{
  color: $color-danger;
  margin-right: 4px;
}

//隐藏 date_picker 的 此刻 按钮
.date_picker_hidden_text_button > .el-picker-panel__footer > .el-button--text:first-child{
  display: none;
}

.a-hover-underline{
  &:hover{
    text-decoration: underline;
  }
}



.divPre{
  width: 100%;
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word; 
}

.__dynamicTabContentWrapper{
  display: flex;
  flex-direction: column;
}

[id*='__dynamicTabCoreWrapper'], .__dynamicTabWrapper{
  flex: 1;
  overflow-y: hidden;
}

//自定义全面屏样式
.full-screen-cus{
  background: #fff;
  z-index: 9999;
}

.card-body{
  box-shadow: none;
}
.cus-box{
  border-radius: 10px;
  margin: 10px;
  border: 1px solid #eee;
}

.cus-box-shadow{
  box-shadow: none!important;
}

.cus-box-shadow:hover{
  transition: all .3s;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1)!important;
}

/deep/.my-popper{
  width: 400px!important;
}



//下拉 option 自定义宽度

.my-fixed-width-popper, .my-fixed-width-popper-290, .my-fixed-width-popper-300, .my-fixed-width-popper-400 {
  position: absolute;
  width: auto!important;
}
.my-fixed-width-popper{
  max-width: 1400px!important;
  min-width: 400px!important;
}
.my-fixed-width-popper-290{
  width: 290px!important;
}
.my-fixed-width-popper-300{
  width: 300px!important;
}
.my-fixed-width-popper-400 {
  width: 400px !important;
}

.timeline-popper{
  padding: 0;
  min-width: 30px;
  box-shadow: none!important;
  border: none!important;
}

.popper-z-index-0{
  z-index: 0!important;
}

.popper-z-index-1{
  z-index: 1!important;
}

.filter-popper-wrapper{
  padding: 0;
  .content, .footer{
    padding: 8px;
  }
  .content{
    
  }
  .footer{
    border-top: 1px solid #E5E5E5;
    text-align: right;
  }
}

._sp-popper-tools{
  .content{
    .title{
      color: $bg-color-4;
    }
    .slider-wrapper{
      .el-slider{
        .el-slider__runway{
          margin: 0;  
          .el-slider__button-wrapper{
            // width: 20px;
            // height: 20px;
          }
        }
      }
    }
  }
}

// ._sp-select{
//   .el-scrollbar{
//     .el-scrollbar__view{
//       .el-select-dropdown__item{
//         height: 40px!important;
//         line-height: normal!important;
        
//         >div{
//             display: flex;
//             flex-direction: column;
//         }
//       }
//     }
//   }
// }

.line-clamp-2{
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 超出几行省略 */
  line-clamp: 2;
  overflow: hidden;
}

.line-clamp-3{
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 超出几行省略 */
  line-clamp: 3;
  overflow: hidden;
}

.font-12{
  font-size: 12px;
}



.common-drawer-wrapper{
  border-radius: $border-radius-base 0px 0px 0px;
  .el-drawer__header{
    box-sizing: border-box;
    height: 48px;
    // background: linear-gradient(90deg, #5068FF 0%, #8C6DFF 100%);
    font-size: 14px;
    font-weight: 600;
    // color: #fff;
    color: $text-regular;
    padding: 16px;
    padding-right: 10px;
    border-bottom: 1px solid $border-color-lighter;
    margin: 0;
    .el-icon-close{
      &:hover{
        color: $color-primary;
      }
    }
  }

  .content-body-wrapper{
    height: 100%;
    display: flex;
    flex-direction: column;
    .content-body{
      flex: 1;
      overflow-y: hidden;
    }
    .footer{
      z-index: 1;
      // height: 80px;
      background: #FFFFFF;
      box-shadow: 0px 0px 20px 0px rgba(111,111,111,0.1);
      border-radius: 0px 0px 0px 10px;
    }
  }
}


.cus-button{
  display: inline-block;
  // outline: none;
  // border-radius: 5px;
  // user-select: none;
  // border: none;
  // cursor: pointer;
  // &.disabled{
  //   cursor: not-allowed;
  // }
  // .inner-wrapper{
  //   border-radius: 5px;
  //   height: 40px;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   border: none;
  //   outline: none;
  //   flex-shrink: 0;
  //   background: rgba(175,177,187,0.2);
  //   padding: 0 10px;
  //   font-weight: 400;
  //   font-size: 16px;
  //   color: #AFB1BB;
  //   .el-dropdown-link{
  //     font-weight: 400;
  //     font-size: 16px;
  //     color: #1F82FD;
  //   }
  //   &.mini{
  //     font-size: 12px;
  //     height: 24px;
  //   }
  //   &:hover{
  //     color: #FFFFFF;
  //     background: rgba(66,69,85,0.2);
  //     .el-dropdown-link{
  //       color: #FFFFFF;
  //       background: rgba(66,69,85,0.2);
  //     }
  //   }
  //   &.disabled{
  //     cursor: not-allowed;
  //     background: #EEEEEE;
  //     color: #BFBFBF;
  //     border: 1px solid #BFBFBF;
  //     .el-dropdown-link{
  //       background: #EEEEEE;
  //       color: #BFBFBF;
  //     }
  //     &:hover{
  //       background: #EEEEEE;
  //       color: #BFBFBF;
  //       .el-dropdown-link{
  //         background: #EEEEEE;
  //         color: #BFBFBF;
  //       }
  //     }
  //   }
    
  // }
}

.btn-pri{
  .inner-wrapper{
    background: rgba(28,129,253,0.2);
    color: #1F82FD;
    &:hover{
        background: rgba($color: $text-link-color, $alpha: .8);
    }
  }
}

.btn-sec-pri{
  height: 42px;
  background: rgba(28,129,253,0.2);
  color: #0073DF;
  &:hover{
    color: #FFFFFF;
    box-shadow: 0px 0px 21px 0px rgba(28,117,253,0.23);
  }
}
.btn-danger{
  .inner-wrapper{
    background: rgba(239,100,103,0.2);
    color: #EF6467;
  }
  &:hover{
    background: #EF6467;
  }
}
.btn-warning{
  .inner-wrapper{
    background: rgba(242,145,0,0.2);
    color: #F29100;
  }
  &:hover{
      background: rgba(242,145,0,0.16);
  }
}


.btn-close{
  font-size: 18px;
  padding: 10px;
  background: rgba(175,177,187,0.2);
}


.cus-text-button{
  cursor: pointer;
  color: $text-link-color;
  user-select: none;
  &.del{
    color: $text-link-del-color;
  }
  &:hover{
    text-decoration: underline;
  }
}


.cus-tag-item{
  padding: 0 5px; user-select: none; height: 22px; line-height: 22px; border-radius: 10px; font-size: 12px;
}

.m-r-50{
  margin-right: 50px;
}


.cur-row-class{
  cursor: pointer!important;
}

//自定义表格斑马纹颜色
.cus-row-striped td.el-table__cell{
  background: rgba($color: #F9F9FB, $alpha: .5);
}

.line-height-20{
  line-height: 20px;
}

.line-height-16{
  line-height: 16px;
}


.new-message-dot{
  position: absolute;
  right: -6px;
  top: -5px;
  width: 10px;
  height: 10px;
  box-sizing: border-box;
  background: $color-danger;
  border: 2px solid $color-white;
  border-radius: 50%;
}

//特殊的 el-form-item，下边距设置为 0
.sp-el-form-item{
  margin-bottom: 0!important;
}

.sp-el-form-item-5{
  margin-bottom: 5px!important;
}

.flex-0{
  flex-shrink: 0;
}

.flex-1{
  flex: 1;
}


.icon-big{
  font-size: 48px;
}

.icon-micro{
  font-size: 24px;
}

.icon-32{
  font-size: 32px;
}

.icon-20{
  font-size: 20px;
}

.icon-18{
  font-size: 18px;
}

.icon-mini{
  font-size: 16px;
}

.icon-14{
  font-size: 14px;
}


.color-danger {
  color: #f78989;
}
.color-success {
  color: $color-success;
}

.color-white {
  color: #fff;
}

.color-warning{
  color: $color-warning;
}
.color-purple{
  color: $color-purple;
}


.text-regular{
  color: $text-regular;
}
.text-primary{
  color: $text-primary;
}

.color-info{
  color: $color-info;
}

.color-primary{
  color: $color-primary;
}

.placeholder{
  color: $placeholder;
}

.placeholder-text-color{
  color: $placeholder;
}

.bg-color-4{
  color: $bg-color-4;
}

.color-danger{
  color: $color-danger;
}
.color-danger-1{
  color: $color-danger-1;
}
.text-secondary{
  color: $text-secondary;
}

.text-main-color{
  color: $text-main-color;
}

.bg-color-1{
  background: $bg-color-1;
}

.border-color-light{
  color: $border-color-light;
}

._sp-table-first-col{
  border-right: 1px solid $border-color-light;
}

// 带图片的按钮样式调整
.custom-button-wrapper{
  .el-button{
    padding: 7px 10px!important;
  }
  .el-radio-button{
      .el-radio-button__inner{
          padding: 7px 10px!important;
          border-color: $color-primary;
      }
      &:not(.is-active){
          .el-radio-button__inner{
              color: rgba($color: $color-primary, $alpha: .8);
              &:hover{
                  color: $color-primary;
              }
          }
      }
  }
  .btn-item-wrapper{
      display: flex; 
      align-items: center;
      .icon-mini{
          margin-right: 5px;
      }
  }
}

.tab-header-filter-title{
  margin-bottom: 2px;
}

.tab-header-filter-content{
  border: 1px solid $border-color-light;
  width: 100%;
  padding: 10px;
  max-height: 240px;
  overflow-y: auto;
  border-radius: $border-radius-base;

}

.a-link:hover{
  cursor: pointer;
  color: $color-primary;
  text-decoration: underline;
}

//覆盖标签样式（影响了el-select tag样式，研发管理——》项目管理——》跟进——》任务——》全部状态多选——导致删除按钮都跑出去了）
// .el-tag--mini{
//   border-radius: 2px;
//   font-size: 11px;
//   height: 16px;
//   padding: 0 2px;
//   line-height: 15px;
// }


// 用于 flowchart 图形节点，通过搜索选中后，给一个高亮提示
._checked-tip{
  .node-inner-wrapper{
    border-color: $color-danger!important;
    box-shadow: 0px 0px 10px 0px rgba($color: $color-danger, $alpha: .2);
  }
}

.header__cell__undraggable{
  &:hover{
    .header__cell__more__button{
      display: inline-block;
    }
  }
}

.header__cell__draggable{
  &:hover{
    .header__cell__more__button{
      display: inline-block;
    }
  }
  .cell, .elx-cell{
    >span:first-child{
      cursor: move;
    }
  }
}

.header__cell__more__button{
  position: absolute; 
  right: 10px;
  display: none;
}


.__my_sp_datePicker{
  .el-date-range-picker__time-header{
    .el-date-range-picker__editors-wrap{
      padding: 5px!important;
      &.active{
        background-color: $color-primary !important;
      }
    }
    .el-date-range-picker__editors-wrap {
      &.active{
        background-color: $color-primary !important;
      }
    }

  }

  

  .sp-front-disabled-click{
    pointer-events: none!important;
    &:after{
      content: ' ';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: transparent;
      z-index: 2;
    }
  }
}
/* 毛玻璃特效 */
.ground_glass{
  position: relative;
  user-select: none;
  padding-right:3px;
  padding-left:2px;
  &:after{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    content: "";
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    //background-color: rgba(255, 255, 255, 0.25);
    //border: 1px solid rgba(255, 255, 255, 0.18);
    //box-shadow: rgba(142, 142, 142, 0.19) 0 6px 15px 0;
    //-webkit-box-shadow: rgba(142, 142, 142, 0.19) 0 6px 15px 0;
    //border-radius: 12px;
    //-webkit-border-radius: 12px;
  }
}


.highHeightSelectOption{
  li{
    height: 60px;
    .cus-option{
      height: 100%;
      display: flex;
      .rht{
        flex: 1;
        >div{
          height: 25px;
        }
      }
    }
  }
}