<template>
  <div>
    <app-dialog title="考勤详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
      <template slot="body">
          <div class="wrapper">
            <div class="det">
              <el-row>
                <el-col :span="6">
                  <span style="padding-left: 10px;">
                    姓名：{{ row.Name }}
                  </span>
                </el-col>
                <el-col :span="18">
                  考勤时间：{{ year }}-{{ month }}
                </el-col>
              </el-row>
            </div>
            <app-table ref="mainTable" class="table_container" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="loading" :isShowOpatColumn="false" :isShowBtnsArea='false' :startOfTable="startOfTable" :multable="false" :isShowConditionArea='false'>
              
              <template slot="TimecardDate" slot-scope="scope">
                  {{ scope.row.TimecardTime | dateFilter('YYYY-MM-DD') }}
              </template>
              <template slot="TimecardTime" slot-scope="scope">
                  {{ scope.row.TimecardTime | dateFilter('HH:mm:ss') }}
              </template>
              <template slot="TimecardType" slot-scope="scope">
                  {{ scope.row.TimecardType | typeFilter }}
              </template>
              
            </app-table>

          <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />

          </div>
      </template>

      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>

import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import indexPageMixin from "@/mixins/indexPage";
import { vars } from '../vars' 

export default {
  name: "attendanceMgmt-datas-create",
  directives: {},
  components: {

  },
  mixins: [indexPageMixin],
  computed: {
    
  },
  props: {
    //考勤号
    timecardNo: {
      type: String,
      required: true
    },
    //机器号
    machineNo: {
      type: String,
      required: true
    },
    year: {
      type: Number,
      required: true
    },
    month: {
      type: Number,
      required: true
    },
    row: {
      type: Object,
      default: {}
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.getList();
        }
      },
      immediate: true
    }
  },
  filters: {
    typeFilter(val) {
      let obj = vars.timecardType.find(s => s.value == val)
      if(obj) {
        return obj.label
      }
      return ''
    },
  },
  created() {
  },
  data() {
    return {
      disabledBtn: false,
      loading: false,
      tabColumns: [
            {
              attr: {
                  prop: "Name",
                  label: "姓名",
                  showOverflowTooltip: true
              },
            },
            {
                attr: {
                    prop: "MachineNo",
                    label: "机器号码",
                    showOverflowTooltip: true,
                },
            },
            {
                attr: {
                    prop: "TimecardNo",
                    label: "考勤号码",
                    showOverflowTooltip: true,
                },
            },
            {
                attr: {
                    prop: "TimecardDate",
                    label: "考勤日期",
                },
                slot: true
            },
            {
                attr: {
                    prop: "TimecardTime",
                    label: "打卡时间",
                },
                slot: true
            },
            {
                attr: {
                    prop: "TimecardType",
                    label: "打卡方式",
                },
                slot: true
            },
            {
                attr: {
                    prop: "Address",
                    label: "定位地址",
                    showOverflowTooltip: true,
                },
            },
        ],
      listQuery: {
      },
      tabDatas: [], //原始数据
      total: 0,
    };
  },
  methods: {
    getList() {
      let others = {
        TimecardNo: this.timecardNo,
        MachineNo: this.machineNo,
        Year: this.year,
        Month: this.month,
      }
      let postDatas = JSON.parse(JSON.stringify(this.listQuery))
      postDatas = Object.assign({}, postDatas, others)

      this.loading = true
      timecardDepartment.getTimecardEmployeeRecordDetail(postDatas).then(res => {
        this.loading = false
        this.tabDatas = res.Items;
        this.total = res.Total;
      }).catch(err => {
        this.loading = false
      });
    },
    handleCurrentChange(val) {
        this.listQuery.PageIndex = val.page;
        this.listQuery.PageSize = val.size;
        this.getList();
    },
    handleSizeChange(val) {
        this.listQuery.PageSize = val.size;
        this.getList();
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang='scss' scoped>
.wrapper{
  .det{
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .table_container{
    min-height: 400px;
  }
}
</style>
