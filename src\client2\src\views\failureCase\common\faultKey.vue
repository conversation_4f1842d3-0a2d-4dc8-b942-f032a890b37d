<template>
  <div>
    <app-dialog
    title="故障关键字"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :maxHeight='700'
    :width='700'
    >
    <template slot="body">
      <div class="gzgjz" v-loading="loading">
        <div>
          <span>模糊查询&emsp;</span>
          <el-input class="elInput" v-model="searchVal" placeholder=""></el-input>
          <el-button @click="handleSearch" type="primary" size="mini" style="margin-left:10px;">查询</el-button>
          <el-button @click="handleReset" size="mini">重置</el-button>
        </div>
        <!-- <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="false" :startOfTable="startOfTable">
        </app-table> -->
        <el-table
          ref="mainTable"
          :data="tabDatas"
          style="width: 100%">
          <el-table-column
            label="选择"
            width="55">
            <template slot-scope="scope">
              <el-checkbox v-model="tabDatas[scope.$index].cheked" @change="handelCheck(tabDatas[scope.$index])"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            label="序号"
            width="120">
            <template slot-scope="scope">{{ (scope.$index+1)+(listQuery.PageIndex-1)*listQuery.PageSize }}</template>
          </el-table-column>
          <el-table-column
            prop="FaultKeyWordName"
            label="故障关键字">
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" layout="total, prev, pager, next, jumper" />
      </div>
    </template>
    <template slot="footer">
        <div class="fl">
          已选中（{{selectedNum}}）
        </div>
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button @click="handleSuccess" type="primary" size="mini">确认</el-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as faultkeyword from "@/api/faultkeyword";
import indexPageMixin from "@/mixins/indexPage";
  export default {
    name: "",
    components: {

    },
    mixins: [indexPageMixin],
    props:{
      keyList: {
        type: Array,
        default: null
      }
    },
    data() {
      return {
        loading:false,
        selectedNum:0,
        listQuery: {
            PageIndex: 1,
            PageSize: 10,
        },
        total:0,
        searchVal:'',
        tabDatas:[],
        tabColumns: [
          {attr: {prop: "FaultKeyWordName",label: "故障关键字"}},
        ],
        selectedData:[],
      };
    },
    watch: {
      "$attrs.dialogFormVisible"(val) {
        if(val){
          console.log(2,this.keyList)
          this.listQuery={
            PageIndex: 1,
            PageSize: 10,
          }
          this.searchVal="";
          this.getList(true);
        }
      },
    },
    computed:{

    },
    created() {

    },
    mounted(){

    },
    methods: {
      handleSearch(){
        this.getList();
      },
      handleReset(){
        this.searchVal="";
        this.getList();
      },
      handleClose(){
        this.$refs.appDialogRef.handleClose()
      },
      handleSuccess(){
        this.$emit('saveSuccess',this.selectedData);
        this.$refs.appDialogRef.handleClose()
      },
      getList(t){
        this.loading=true;
        let params={
          "name": this.searchVal,
          "pageIndex": this.listQuery.PageIndex,
          "pageSize": this.listQuery.PageSize
        }
        console.log('p',params)
        faultkeyword.getList(params).then(res => {
          this.loading=false;
          console.log(1,res)
          this.total=res.Total;
          if(t){
            this.selectedData=JSON.parse(JSON.stringify(this.keyList));
            if(this.selectedData){
                this.selectedNum=this.selectedData.length;
            }
          }
          this.tabDatas=res.Items;
          let a=null;
          this.tabDatas.forEach(v => {
            v.cheked=false;
            a=this.selectedData.find(s => s.FaultKeyWordId == v.FaultKeyWordId);
            if(a){
              v.cheked=true;
            }
          })
        }).catch(err => {
          this.loading=false;
        })
      },
      handelCheck(val){
        console.log(val,this.tabDatas)

        if(val.cheked){
          if(!this.selectedData || this.selectedData.length<10){

            if(!this.selectedData){
              this.selectedData=[]
            }

            this.selectedData.push(val);
          }else{
            val.cheked=false;
          }
        }else{
          this.selectedData=this.selectedData.filter(s => s.FaultKeyWordId!=val.FaultKeyWordId);
        }
        this.tabDatas=JSON.parse(JSON.stringify(this.tabDatas));
        this.selectedNum=this.selectedData.length;
      },
      handleSizeChange(val) {
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      handleCurrentChange(val) {
        this.listQuery.PageIndex = val.page;
        this.listQuery.PageSize = val.size;
        this.getList();
      },
    }
  };
</script>
<style lang="scss" scoped>
  .elInput{
    width:60%;
  }
</style>
