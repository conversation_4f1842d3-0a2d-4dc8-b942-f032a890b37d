<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='700' :width='600'>
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <div class="wrapper">
            <el-row>
              <el-col :span="24">
                <el-form-item label="分类名称" prop="ProductClassificationName">
                  <el-input maxlength="30" :disabled="!editable" v-model="formData.ProductClassificationName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- <el-button @click="handleClose">取消</el-button>
        <el-button @click="createData" type="primary" v-show="editable">确认</el-button> -->

        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="createData" :buttonType='1' v-show="editable"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>

// import tags from '../common/tags'
import * as productClassification from '@/api/informationCenter/productClassification'

export default {
  name: "productClassification-createFolder",
  directives: {
  },
  components: {

  },
  mixins: [
  ],
  props: {

    dialogStatus: { //create、update、detail
      type: String,
      default: 'create'
    },
    //操作的节点，如果是新增，则为父节点；编辑为当前节点
    node: {
      type: Object,
      required: true
    },
  },
  watch: {
    product: {
      handler(val) {
        if (val) {
          this.formData.ProductId = this.product.value
          this.formData.ProductName = this.product.label
        }
      },
      immediate: true
    },
    '$attrs.dialogFormVisible'(val) {

      if (val) {
        this.resetFormData()
        if (this.dialogStatus != 'create') {
          this.getDetail()
        }
      }

    }
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == 'create') {
        return '添加分类'
      } else if (this.dialogStatus == 'update') {
        return '编辑分类'
      } else if (this.dialogStatus == 'detail') {
        return '分类详情'
      }
    },

  },
  created() {
    this.rules = this.initRules(this.rules)
  },
  data() {

    return {
      rules: {
        ProductClassificationName: { fieldName: "分类名称", rules: [{ required: true, max: 100 }] },
      },
      labelWidth: '100px',
      formData: {
        Id: '', //
        ProductClassificationName: '',//分类名称

      },
    };
  },
  methods: {
    resetFormData() {
      let temp = {
        Id: '', //
        ProductClassificationName: '',//分类名称

      }
      this.formData = Object.assign({}, this.formData, temp)
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));

          let result = null

          if (this.dialogStatus == 'create') {
            delete postData.Id
            postData.ParentId = this.node.Id
            result = productClassification.add(postData)
          } else if (this.dialogStatus == 'update') {
            result = productClassification.edit(postData)
          }

          result.then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.$refs.appDialogRef.createData()
          })
        }
      });
    },
    getDetail() {
      this.formData = Object.assign({}, this.formData, this.node)
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
