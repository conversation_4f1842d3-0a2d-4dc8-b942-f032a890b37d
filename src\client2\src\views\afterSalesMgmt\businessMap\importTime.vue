<template>
    <div class="timeTemplate">
        <app-dialog :title="'批量添加'+title" 
        class="configDialog"
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='446'
        >
        	<template slot="body">
                <div class="main">
                    <div>
                        <span>{{title}}&emsp;</span>
                        <el-select v-if="type == 0"  v-model="data" placeholder="请选择" style="width:80%;">
                            <el-option
                            v-for="item in options"
                            :key="item.Id"
                            :label="item.Name"
                            :value="item.Id">
                            </el-option>
                        </el-select>
                        <el-input v-if="type == 1" v-model="data" style="width:80%;"></el-input>
                        <el-date-picker v-if="type == 2" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="data" type="date" style="width:78%;"></el-date-picker>
                    </div>
                </div>
        	</template>
        	<template slot="footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSuccess">确定</el-button>
            </template>
    	</app-dialog>
    </div>
</template>    	
<script>
export default{
	name:'timeTemplate',
    props:{
        title:{
            type:String,
            default:''
        },
        type:{
            type:Number,
            default:0
        },
        options:{
            type:Array,
            default:[]
        }
    },
	data(){
        return{
            data:'',
        }
    },
    components: {  },
    watch: {
        '$attrs.dialogFormVisible'(val) {
        	if(val){
                this.data='';
        	}
        },
    },   
    created(){
        
    },
    mounted(){
        
    },
    methods:{
    	handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        handleSuccess(){
            this.$emit('saveSuccess',this.data);
        },
    } 
}
</script>
<style lang="scss" scoped>
.main{
    >div{
        padding:30px 10px;
    }
}
</style>