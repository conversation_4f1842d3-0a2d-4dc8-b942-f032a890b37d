<template>
    <div>
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="80px">
                    <div class="wrapper" v-loading='loading'>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="ERP账号" prop="Account">
                                    <el-input :disabled="!editable" maxlength="50" v-model="formData.Account"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="密码" prop="Password">
                                    <el-input :disabled="!editable" maxlength="100" show-password v-model="formData.Password"></el-input>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item label="角色名称" prop="ERPAccountRole">
                                    <el-select class="elSelect" :disabled="!editable" v-model="formData.ERPAccountRole" placeholder="请选择">
                                        <el-option
                                        v-for="item in roleTypes"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item label="关联人员" prop="AssociatedEmployeeList">
                                    <emp-selector 
                                        v-if="editable"
                                        :readonly="!editable"
                                        ref="empSelector1" :isAutocomplete='true'
                                        :collapseTags="false" :showType="2" :multiple="true"
                                        :list="formData.AssociatedEmployeeList"
                                        key="ccuserss"
                                        @change="handleChangeAssociatedEmployeeList"
                                    >
                                        <app-table-row-button slot="reference" :disabled="!editable" style="line-height: normal;" :type="1" text="添加"></app-table-row-button>
                                    </emp-selector>
                                    <div class="emps-wrapper">
                                        <noData v-if="!loading && formData.AssociatedEmployeeList && formData.AssociatedEmployeeList.length == 0"></noData>
                                        <template v-else>
                                            <el-tag v-for="(emp, idx) in formData.AssociatedEmployeeList || []" :key="emp.EmployeeId" size="small" :closable="editable ? true : false" @close="handleRemove(emp)">{{ emp.Name }}</el-tag>
                                        </template>
                                    </div>
                                </el-form-item>
                            </el-col>
                            
                        </el-row>

                    </div>
                </el-form>

            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn' v-if="editable"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as erpApi from '@/api/erpManagement/erp'
import empSelector from "../../common/empSelector"
import noData from "@/views/common/components/noData"
import { vars } from './vars'

export default {
    name: "erp-create",
    components: {
        empSelector,
        noData,

    },
    computed: {
        title() {
            if(this.dialogStatus == 'create') {
                return '添加账号'
            }else if(this.dialogStatus == 'update') {
                return '编辑账号'
            }else if(this.dialogStatus == 'detail') {
                return '账号详情'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail";
        },
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        // regionalId: {
        //     type: String,
        //     default: ""
        // },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }

    },
    filters: {
     
    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            roleTypes: vars.roleTypes,
            disabledBtn: false,
            rules: {
                Account: { fieldName: "ERP账号", rules: [{ required: true }] },
                Password: { fieldName: "密码", rules: [{ required: true }] },
            },
            loading: false,
            formData: {
                Account: '',
                Password: '', 
                // RegionalId: this.regionalId, 
                ERPAccountRole: 1,
                AssociatedEmployeeList: [],
            },
        };
    },
    methods: {
        resetFormData() {
            this.formData = {
                Id:'',
                Account: '',
                Password: '', 
                // RegionalId: this.regionalId,
                ERPAccountRole: 1,
                AssociatedEmployeeList: [],
            }
        },
        handleRemove(tag) {
            if(tag) {
                let idx = this.formData.AssociatedEmployeeList.findIndex(s => s.EmployeeId == tag.EmployeeId)
                if(idx > -1) {
                    this.formData.AssociatedEmployeeList.splice(idx, 1)
                }
            }
        },
        handleChangeAssociatedEmployeeList(users) {
            this.formData.AssociatedEmployeeList = users
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));

                    let emps = postData.AssociatedEmployeeList || []
                    postData.AssociatedEmployeeIdList = emps.map(s => s.EmployeeId) || []
                    delete postData.AssociatedEmployeeList
                
                    this.disabledBtn = true
                    if (this.dialogStatus == "create") {
                        erpApi.addAccount(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        });
                    }else if (this.dialogStatus == "update") {
                      erpApi.edit(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "编辑成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        });
                   }
        
                } 
            });
        },
         
        getDetail() {
            let self = this;
            self.loading = true;
            erpApi.detail({id: self.id}).then(res => {
                self.loading = false;
                self.formData = Object.assign({}, self.formData, res);
            })
            .catch(err => {
                self.loading = false;
            });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },   
    }
};
</script>

<style lang="scss" scoped>
.emps-wrapper{
    min-height: 100px;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;
    padding: 10px;
    >span{
        margin: 0 5px;
    }
}
</style>
