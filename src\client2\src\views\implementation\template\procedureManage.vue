<!--工序模板管理-->
<template>
  <!--组件内容区-->
  <div>
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="600"
      :width="600"
    >
      <template slot="body">
        <el-form
          ref="formRef"
          :rules="rules"
          :model="formModel"
          label-position="right"
          label-width="100px"
        >
          <div class="wrapper" v-loading="loading">
            <el-button size="mini" type="primary" @click="handleAdd" :disabled='formModel.list.length >= 20'>添加新工序</el-button>
            <div style="margin-top:10px;max-height:550px;overflow-y:auto;">

              <el-card class="box-card" v-for="(item, idx) in formModel.list" :key="idx">
                  <div slot="header">
                      <div class="item-title-wrapper">
                          <div class="item-title">工序{{ idx + 1 }}</div>
                          <div class="item-btns">
                              <div @click="move('up', idx)">
                                <svg-icon icon-class="arrow-circle-up" v-show="idx > 0" class="el-icon-top" title="上移"></svg-icon>
                              </div>
                              <div @click="move('down', idx)">
                                <svg-icon icon-class="arrow-circle-down" v-show="idx < formModel.list.length - 1" title="下移"></svg-icon>
                              </div>
                              <i class="el-icon-delete-solid" style="font-size: 20px; color: red; margin-left: 4px;" title="删除" @click="handleRemove(item)"></i>
                          </div>
                      </div>
                  </div>
                  <el-row class="item-content">
                      <el-col :span="24" class="cus-textarea-wrapper">
                        <el-form-item
                          label="工序名称"
                          :prop="'list.' + idx + '.Name'"
                          :rules="{required: true, message: '工序名称不能为空', trigger: 'blur'}"
                        >
                          <el-input
                            maxlength="50"
                            v-model="formModel.list[idx].Name"
                            clearable
                            :disabled="!editable"
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="工序负责人"
                          :prop="'list.' + idx + '.EmployeeList'"
                          :rules="{required: true, message: '工序负责人不能为空', trigger: 'change'}"
                        >
                          <emp-selector
                            :isShowClearButton="false"
                            :sourceType="'onlyAuthUsers'"
                            :multiple="true"
                            :showType="2"
                            key="service-users"
                            :list="formModel.list[idx].EmployeeList"
                            @change="(users) => handleChangeUsers(item, users)"
                          ></emp-selector>
                        </el-form-item>
                        <el-form-item
                          label="重点关注"
                          :prop="'list.' + idx + '.IsFoucs'"
                        >
                          <el-switch v-model="formModel.list[idx].IsFoucs"></el-switch>
                        </el-form-item>
                      </el-col>
                  </el-row>
              </el-card>

              <!-- <div class="item" v-for="(item, idx) in formModel.list" :key="idx">
                <div class="item-title-wrapper">
                  <div class="item-title">工序{{ idx + 1 }}</div>
                  <div class="item-btns">
                    <i v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></i>
                    <i
                      v-show="idx < formModel.list.length - 1"
                      class="el-icon-bottom"
                      title="下移"
                      @click="move('down', idx)"
                    ></i>
                    <i class="el-icon-delete-solid" title="删除" @click="handleRemove(item)"></i>
                  </div>
                </div>
                <el-row class="item-content">
                  
                </el-row>
              </div> -->
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
import * as implementationTemplate from "@/api/implementation/implementationTemplate";
import empSelector from "@/views/common/empSelector";
export default {
  /**名称 */
  name: "template-procedure-manage",
  directives: {},
  /**组件声明 */
  components: {
    empSelector
  },
  /**参数区 */
  props: {
    dialogStatus: {
      //create、update、detail
      type: String
    },
    //实施模板id（新增必传）
    templateId: {
      type: String,
      required: true
    }
  },
  /**数据区 */
  data() {
    return {
      rules: {},
      loading: false,
      disabledBtn: false,
      formModel: {
        list: [
          // {
          //   Id: null,
          //   Name: "",
          //   ImplementationTemplateId: this.templateId,
          //   EmployeeList: []
          // }
        ]
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail" && this.dialogStatus != "approval";
    },
    /**组件标题 */
    pageTitle() {
      return "工序管理";
      // if (this.dialogStatus == "create") {
      //   return "工序管理";
      // } else if (this.dialogStatus == "update") {
      //   return "工序管理";
      // } else if (this.dialogStatus == "detail") {
      //   return "工序管理";
      // }
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
        if (this.templateId) {
          this.getProcedures();
        }
      }
    }
  },
  /**渲染前 */
  created() {
    this.rules = this.initRules(this.rules);
  },
  /**方法区 */
  methods: {
    //获取工序列表
    getProcedures() {
      this.loading = true;
      this.formModel.list = [];
      implementationTemplate
        .getImplementationProcedureTemplateListByCondition({
          ImplementationTemplateId: this.templateId
        })
        .then(res => {
          this.formModel.list = res;
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    /**重置数据 */
    resetFormData() {
      let temp = {
        formModel: {
          list: []
        }
      };
      this.formModel.list = Object.assign({}, this.formModel.list, temp);
    },
    /**添加工序 */
    handleAdd() {
      this.formModel.list.push({
        Id: null,
        Name: "",
        ImplementationTemplateId: this.templateId,
        EmployeeList: [],
        IsFoucs: false
      });
    },
    /**移动工序 */
    move(direction, currIdx) {

      if (
        (direction == "up" && currIdx > 0) ||
        (direction == "down" && currIdx < this.formModel.list.length - 1)
      ) {
        let currRow = JSON.parse(JSON.stringify(this.formModel.list[currIdx]));
        let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
        this.formModel.list.splice(currIdx, 1);
        this.formModel.list.splice(targetIdx, 0, currRow);
      }
    },
    /**移除工序 */
    handleRemove(row) {
      this.$confirm("是否确认删除当前工序?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var index = this.formModel.list.indexOf(row);
        if (index !== -1) {
          this.formModel.list.splice(index, 1);
        }
      });
    },
    /**变更人员 */
    handleChangeUsers(row, users) {
      row.EmployeeList = users;
    },
    /**保存数据 */
    createData() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formModel.list));
          postData = postData.map((s, idx) => {
            s.EmployeeIdList = s.EmployeeList.map(p => p.EmployeeId);
            s.OrderIndex = (idx + 1) * 10;
            delete s.EmployeeList;
            return s;
          });

          let names = postData.map(s => s.Name);
          if (new Set(names).size != names.length) {
            this.$message.error(`工序名称不能重复`);
            return false;
          }

          if(postData.length > 20) {
            this.$message.error(`工序不能超过20个`);
            return false;
          }

          this.disabledBtn = true;
          implementationTemplate
            .editImplementationProcedureTemplateList(postData)
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        }
      });
    },
    /**关闭组件 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style scoped>
.wrapper >>> .el-card:not(:last-child){
    margin-bottom: 10px;
}

.wrapper >>> .el-card__body{
    padding: 10px;
}
</style>

<style lang="scss" scoped>
.wrapper {
  min-height: 100px;
  // .item {
    // border: 1px solid #ebeef5;
    // border-radius: 10px;
    // margin-bottom: 10px;
    // padding-top: 10px;
    .item-title-wrapper {
      display: flex;
      height: 34px;
      line-height: 34px;
      margin-bottom: 4px;
      padding: 0 10px;
      // border-bottom: 1px solid #ebeef5;
      .item-title {
        flex: 1;
        margin-bottom: 4px;
      }
      .item-btns{
          display: flex;
          align-items: center;
          justify-content: flex-end;
          svg {
              cursor: pointer;
              font-size: 20px;
              vertical-align: middle;
              margin-left: 4px;
          }
      }
    }
    .item-content {
      padding-right: 10px;
    }
  // }
}
</style>
