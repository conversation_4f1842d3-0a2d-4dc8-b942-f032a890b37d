<template>
    <div class="__tip" :style="{minHeight: minHeight + 'px'}">

        <div class="img-wrapper" v-if="showImg">
            <img class="img" :style="{width: imgWidth}" src="../../../assets/images/no-data.png" alt>
            <!-- <div class="placeholder-text-color">暂无数据</div> -->
        </div>
        <div class="sec">
            <span>{{ text }}</span><slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'no-data',
    
    props: {
        text: {
            type: String,
            default: '暂无数据'
        },
        minHeight: {
            type: Number,
            default: 100
        },
        showImg: {
            type: Boolean,
            default: true
        },
        size: {
            type: String,
            default: 'medium'
        },
    },
    computed: {
        imgWidth() {
            let size = this.size
            let defWidth = '72px'
            if(size == 'medium') {
            }else if(size == 'small') {
                defWidth = '72px'
            }else if(size == 'mini') {
                defWidth = '60px'
            }
            return defWidth
        },
    },
    data() {
        return {
            
        }
    },
}
</script>

<style lang="scss" scoped>
.__tip{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .sec{
        display: flex;
        align-items: center;
        justify-content: center;
        color: $placeholder;
        font-size: 14px;
    }
}

.img-wrapper{
    padding-bottom: 10px;
    .img{
        
    }
}


</style>
