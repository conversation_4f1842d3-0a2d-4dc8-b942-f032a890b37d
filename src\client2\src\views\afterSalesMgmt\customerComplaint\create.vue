<template>
    <div>
        <app-dialog
            :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :maxHeight="700"
        >
            <template slot="body">
                <el-form
                    :rules="rules"
                    ref="formData"
                    :model="formData"
                    label-position="right"
                    :label-width="labelWidth"
                >
                    <div class="wrapper" v-loading='loading'>
                        <div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="客户名称" prop="CustomerName">
                                        <el-input maxlength="10" :disabled="!editable" v-model="formData.CustomerName"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="联系电话" prop="Telephone">
                                       <el-input placeholder='区号-XXXXXXX / 186XXXXXXXX' :disabled="!editable" maxlength="20" v-model="formData.Telephone"></el-input>
                                    </el-form-item>
                                </el-col>
                               <el-col :span="12">
                                    <el-form-item label="反应时间" prop="ComplaintTime">
                                      <el-date-picker style="width: 100%;" v-model="formData.ComplaintTime" format='yyyy-MM-dd HH:mm' value-format='yyyy-MM-dd HH:mm' :disabled="!editable" type="datetime" placeholder="请选择反应时间">
                                      </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                     <el-form-item label="地区" prop='RegionalId'>
                                        <!-- <treeselect :disabled="!editable" :normalizer="normalizer" key='type1'
                                            :zIndex='99999999'
                                            v-model="formData.RegionalId" :default-expand-level="3"
                                            :options="treedata" :multiple="false" placeholder='' :show-count="true"
                                            :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
                                        </treeselect> -->

                                        <!-- <span v-if="formData.RegionalName">{{formData.RegionalName}}&nbsp;</span>
                                        <el-button type="text" :disabled="!editable" @click="handleDialog">选择</el-button> -->


                                        <div class="_regional_detail_wrapper">
                                            <div class="btn_wrapper">
                                                <el-button :disabled="!editable" type="text" @click="handleDialog">选择</el-button>
                                            </div>
                                            <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                            <div class="close_wrapper" v-show="formData.RegionalName && editable">
                                                <div class="i_wrapper">
                                                    <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="客服人员" prop="EmployeeList">
                                        <normar-emp-selector
                                            listSelectorTitle='选择人员'
                                            :listSelectorUrl='serviceArea.business + "/CustomerServiceManage/GetListPage"'
                                            :multiple='false' :showType='2'
                                            :list='formData.EmployeeList'
                                            :columns='empColumns'
                                            key='service-users'
                                            @change='handleChangeUsers'
                                             :readonly="!editable"
                                        ></normar-emp-selector>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="问题类型" prop='ProblemTypes'>
                                        <el-select v-model="formData.ProblemTypes" placeholder="请选择" :disabled="!editable">
                                              <el-option v-for="item in problemTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                          </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="造成影响" prop='ImpactType'>
                                        <el-select v-model="formData.ImpactType" placeholder="请选择" :disabled="!editable">
                                          <el-option v-for="item in impactType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="投诉问题" prop="ComplaintTitle">
                                        <el-input maxlength="200" :disabled="!editable" v-model="formData.ComplaintTitle"></el-input>
                                    </el-form-item>
                                </el-col>
                                 <el-col :span="24">
                                    <el-form-item label="问题说明" prop="Remark">
                                        <el-input type="textarea"  maxlength="2000" :rows="5" :disabled="!editable" v-model="formData.Remark"></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24">
                                    <el-form-item label="问题回复" prop="QuestionResponse">
                                        <el-input type="textarea" v-model="formData.QuestionResponse" :rows="5" :disabled="!editable" maxlength="2000"></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24">
                                    <el-form-item label="处理结果" prop="ResultType">
                                         <!-- <el-radio :disabled="!editable" v-model="formData.ResultType" :label="true">是</el-radio> -->
                                        <el-radio
                                          :disabled='!editable'
                                          v-model="formData.ResultType"
                                          v-for="(t, idx) in resultType"
                                          :key="idx"
                                          :label="t.value"
                                          >
                                            {{ t.label }}
                                        </el-radio>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="12" v-if="formData.ResultType==2">
                                    <el-form-item label="责任部门" prop='DutyDepartmentId'>
                                        <el-select v-model="formData.DutyDepartmentId" placeholder="请选择" :disabled="!editable">
                                          <el-option v-for="item in dutyDepartment" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                </el-form>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>

        <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleCloseAgeentEmploye" >
            <el-form :model="formAgentEmployee"  ref="formAgentEmployee"  :rules="agentEmployeeRules">
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="该责任部门未检测到负责人"></el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="请选择人员" prop="AgentEmployeeId">
                        <normar-emp-selector
                            listSelectorTitle='选择人员'
                            :listSelectorUrl='dutyDepartmentEmployeeUrl'
                            :multiple='false' :showType='2'
                            :list='formAgentEmployee.EmployeeList'
                            :columns='empDutyColumns'
                            key='service-users'
                            @change='handleChangeDutyUsers'
                            :readonly="!editable"
                            :condition='{DepartmentId: formData.DutyDepartmentId}'
                        ></normar-emp-selector>
                      </el-form-item>
                    </el-col>
                  </el-row>
            </el-form>

            <span slot="footer" class="dialog-footer">
              <el-button @click="dialogVisible = false">取 消</el-button>
              <el-button type="primary" @click="goSaveData">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 选择地区 -->
        <v-area-choose
          @closeDialog="closeDialog"
          @electedRegionalData="electedRegionalData"
          :dialogFormVisible="dialogRegionalDialogVisible"
          :checkedList="formData.RegionalId ? [formData.RegionalId] : []"
          :disabledFn="disabledFn"
        ></v-area-choose>
    </div>
</template>

<script>
// import empSelector from "../../../common/empSelector"
// import { vars } from "../customerServiceCenter/common/vars"
import { regs } from "@/utils/regs"
// import { listToTreeSelect } from '@/utils'
import * as afterService from "@/api/afterSalesMgmt/customerComplaint"
import listSelector from '../../common/listSelector'
import { serviceArea } from "@/api/serviceArea"
import normarEmpSelector from '../common/normarEmpSelector'
import * as orderVars from '../../salesMgmt/common/vars'
import * as maintenCenterVars from '../maintenCenter/common/vars'
// import * as systemManagement from "@/api/systemManagement/regionalManagement"
import * as systemDepartment from "@/api/systemManagement/systemDepartment"
import * as systemEmployee from "@/api/systemManagement/systemEmployee"
import { vars } from "./vars"
import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail'
import user from '../../../store/modules/user'
import vAreaChoose from "../businessMap/common/areaChoose";

export default {
    name: "after-service-create",
    directives: {},
    components: {
        // empSelector,
        listSelector,
        normarEmpSelector,
        vAreaChoose,
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    filters: {
    },

    data() {
        return {
        //    normalizer(node) {
        //       // treeselect定义字段
        //       return {
        //           id: node.Id,
        //           label: node.RegionalName,
        //           children: node.children
        //       }
        //     },
            serviceArea,
            oldType: null,
            isTip: true,
            loading: false,
            disabledBtn: false,
            dutyDepartment:[],
            problemTypes:vars.problemTypes,
            impactType:vars.impactType,
            resultType:vars.resultType,
            empColumns: [
                {
                attr: { prop: "Name", label: "姓名" },
                },
                {
                attr: { prop: "Number", label: "工号" },
                },
                {
                attr: { prop: "Gender", label: "性别" },
                },
                {
                attr: { prop: "CustomerServiceDepartmentName", label: "负责地区" },
                },
                {
                attr: { prop: "Phone", label: "手机" },
                },
                {
                attr: { prop: "OrgName", label: "部门" },
                }
            ],
            empDutyColumns: [
                {
                attr: { prop: "Name", label: "姓名" },
                },
                {
                attr: { prop: "Number", label: "工号" },
                },
                {
                attr: { prop: "Mobile", label: "手机" },
                },
                {
                attr: { prop: "DepartmentName", label: "部门" },
                }
            ],
            tabColumns: [
                {
                    attr: { prop: "Code", label: '订单号', width: '440', renderHeader: this.renderHeader }
                }
            ],
            rules: {
                CustomerName: { fieldName: "客户名称", rules: [{ required: true }]},
                Telephone: { fieldName: "联系电话", rules: [{ required: true }, { reg: regs.phoneAndTel }] },
                EmployeeList: { fieldName: "客服人员", rules: [{ required: true, trigger: 'change' }]},
                ProblemTypes: { fieldName: "问题类型", rules: [{ required: true }]},
                ImpactType: { fieldName: "造成影响", rules: [{ required: true }]},
                ComplaintTitle: { fieldName: "投诉问题", rules: [{ required: true }]},
                Remark: { fieldName: "问题说明", rules: [{ required: true }]},
                QuestionResponse: { fieldName: "问题回复", rules: [{ required: true }]},
                ComplaintTime: { fieldName: "反应时间", rules: [{ required: true }]},
                RegionalId: { fieldName: "地区", rules: [{ required: true, trigger: 'change' }]},
                DutyDepartmentId: { fieldName: "责任部门", rules: [{ required: true }]}
            },
            agentEmployeeRules:{
                AgentEmployeeId: { fieldName: "请选择人员", rules: [{ required: true }]}
            },
            labelWidth: "110px",
            formData: {
                Id: "", //需求ID
                CustomerName: '',//计划名称
                Telephone:'',   //联系电话
                EmployeeList: [], //客服人员
                EmployeeIds: [],
                CustomerServiceEmployeeId:'',
                RegionalId:null,
                ComplaintTime: null, //开始时间
                ProblemTypes:'',
                ImpactType:'',
                ComplaintTitle:'',
                Remark:'',
                QuestionResponse:'',
                ResultType:1,
                DutyDepartmentId:'',
                AgentEmployeeId:[]
            },
            // treedata:[],
            dialogVisible:false,
            formAgentEmployee : {
               AgentEmployeeId:[],
               EmployeeList: [], //客服人员
            },
            dutyDepartmentEmployeeUrl:'',
            dialogRegionalDialogVisible: false,

        };
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        },
        'formData.Type'(val, oldVal) {
            this.oldType = oldVal
            if(this.isTip) {
                if(oldVal && oldVal != val && this.formData.List && this.formData.List.length > 0) {
                    this.$confirm('重新选择回访类型将清空回访单列表信息, 是否继续操作？', "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    }).then(() => {
                        this.isTip = true
                        this.formData.List = []
                    }).catch(() => {
                        this.isTip = false
                        this.formData.Type = this.oldType
                    });
                }
            }else{
                this.isTip = true
            }
        },
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建客诉单";
            } else if (this.dialogStatus == "update") {
                return "编辑客诉单";
            } else if (this.dialogStatus == "detail") {
                return "客诉单详情";
            }
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
        this.agentEmployeeRules = this.initRules(this.agentEmployeeRules);
        // this.getAreas();
        this.getDutyDepartments();
    },
    methods: {
        //地区选择
        closeDialog() {
          this.dialogRegionalDialogVisible = false;
        },
        handleDialog(){
            this.dialogRegionalDialogVisible = true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('RegionalId');
            if(data){
                this.formData.RegionalId=data.Id;
                this.formData.RegionalName=data.ParentName;
            }else{
                this.formData.RegionalId='';
                this.formData.RegionalName='';
            }
        },
        disabledFn(data, nodeType) {
            //禁选一级节点
            if(data.level <= 1) {
                return true
            }
            return false
        },
        handleChangeUsers(users) {
            this.formData.EmployeeList = users
        },
        handleChangeDutyUsers(users){
          this.formAgentEmployee.AgentEmployeeId =[];
          if(users){
             this.formAgentEmployee.EmployeeList = users
             this.formAgentEmployee.AgentEmployeeId = users.map(s => s.EmployeeId)
          }
        },
        resetFormData() {
            let temp = {
                Id: "", //需求ID
                CustomerName: '',//计划名称
                Telephone:'',   //联系电话
                EmployeeList: [], //客服人员
                EmployeeIds: [],
                CustomerServiceEmployeeId:'',
                RegionalId:null,
                RegionalName: '',
                ComplaintTime: null, //开始时间
                ProblemTypes:'',
                ImpactType:'',
                ComplaintTitle:'',
                Remark:'',
                QuestionResponse:'',
                ResultType:1,
                DutyDepartmentId:'',
                AgentEmployeeId:[]
            };
            this.formData = Object.assign({}, this.formData, temp);

            let tempFormAgentEmployee = {
               AgentEmployeeId:[],
               EmployeeList: [], //
            };

            this.formAgentEmployee = Object.assign({}, this.formAgentEmployee, tempFormAgentEmployee);

        },
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {

                 // 移交责任部门
                if(this.formData.ResultType==2){
                    // 查询部门负责人
                    systemDepartment.isDepartmentPrincipal({id : this.formData.DutyDepartmentId}).then(res => {

                        this.formData.AgentEmployeeId = res;
                        if(!this.formData.AgentEmployeeId){
                            // 弹出选择人
                            this.dialogVisible = true;
                            this.dutyDepartmentEmployeeUrl = serviceArea.user + "/SystemEmployee/GetListPage";
                        }else{
                            this.saveData();
                        }
                    });
                }else{
                    this.saveData();
                }
            });
        },
        saveData(){
            //提交数据保存
            let postData = JSON.parse(JSON.stringify(this.formData));
            postData.EmployeeIds = postData.EmployeeList.map(s => s.EmployeeId)
            postData.CustomerServiceEmployeeId = postData.EmployeeList[0].EmployeeId

            delete postData.EmployeeList

            if (this.dialogStatus == "create") {
                delete postData.Id;
            }

            this.disabledBtn = true
            let result = null;
            if (this.dialogStatus == "create") {
                delete postData.Id;
                result = afterService.add(postData);
            } else if (this.dialogStatus == "update") {
                result = afterService.edit(postData);
            }
            result.then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.disabledBtn = false
                this.$refs.appDialogRef.createData();
            }).catch(err => {
                  // 部门没有责任人
                  if(err.messageCode==125){
                      this.dialogVisible = true
                  }

                this.disabledBtn = false
            });

        },
        getDetail() {
            this.loading = true
            afterService.detail({ id: this.id }).then(res => {
                this.loading = false
                this.isTip = false
                this.formData = Object.assign({}, this.formData, res);
                this.formData.EmployeeList.push(res.CustomerServiceEmployee);

            }).catch(err => {
                this.loading = false
            });
        },
        handleRemove(idx) {
            this.formData.List.splice(idx, 1)
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        renderHeader(h, { column }) {
            return h("span", this.formData.Type == 1 ? '订单号' : '报修单号');
        },
        handleChangeOwnerUsers(users) {
            this.formData.EmployeeList = users
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleCloseAgeentEmploye(){
          this.dialogVisible=false
        },
        // getAreas() {
        //     systemManagement.getListByCondition({}).then(res => {
        //         this.treedata = listToTreeSelect(res)
        //     })
        // },
        getDutyDepartments() {
            systemDepartment.getDutyDepartment().then(res => {
                this.dutyDepartment = res.map(t=> {
                  return { value : t.DepartmentId , label : t.DepartmentName }
                })
            })
        },
        goSaveData(){
            let validate1 = this.$refs.formAgentEmployee.validate();
            Promise.all([validate1]).then(valid => {
                this.formData.AgentEmployeeId = this.formAgentEmployee.AgentEmployeeId;
                this.saveData();
                this.dialogVisible = false
           })
        }
    }
};
</script>

<style lang="scss" scoped>
.panel-title-st {
  font-size: 16px;
  font-weight: 700;
  padding-top: 8px;
  padding-bottom: 14px;
  padding-left: 6px;
  border-top: 1px solid #dcdfe6;
}
</style>



<style lang="scss" scoped>

.wrapper{
    display: flex;
    .left{
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }
    .right{
        width: 20%;
    }
}

.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}

</style>
