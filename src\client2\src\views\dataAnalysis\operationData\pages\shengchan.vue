<template>
    <div class="block-content" v-loading='loading'>
        <!-- <blockTitle :obj='obj'>
            <div slot='rht'>
                <el-radio-group v-model="period" size="mini" @change='getImplementDetailsChart'>
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </div>
        </blockTitle> -->
        
         <el-row style="width:100%; margin-top:19px; margin-bottom:5px;">
            <el-col :span=12 style="display:flex;">
                <span style="margin-left:30px; width: 5px; height: 20px; background: #3D73DD;"></span>
                <span style="margin-top:2px; font-size:16px; color: #1D2129; margin-left:11px; font-weight:bold;">生产</span>
            </el-col>
            <el-col :span=12 style="display:flex; justify-content:flex-end;">
                <el-radio-group v-model="period" size="mini" @change='getImplementDetailsChart' style="margin-right:15px;">
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </el-col>
         </el-row>
           

        <div class="inner-content">
            <el-row style="width: 100%; height: 1px; background: #DCDFE6;"></el-row>
            <div class="top">
                <div style="display: flex; margin-top: 30px; margin-left:31px;">
                      <span class="shape"></span>
                      <div style="display: flex; flex-direction: column; margin-left:11px;">
                             <span style="color:#A0A1A3; font-size:14px;">实施工程数量</span>
                
                             <span style="color:#1D2129; font-weight: bold; font-size:30px; margin-top:3px;">
                                 {{ formData.ImplementCount }}
                             </span>
                      </div>
                </div>
                <!-- 占位 -->
                <div class="flex-dire-column-wrapper">
                </div>
            </div>
            <div class="bottom">
                <div class="flex-dire-column-wrapper">
                    <span style="color:#333333; font-size:14px; font-weight: bold; margin-left:31px;">实施工程完成率</span>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption1.series || pieEchartOption1.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart1" :xAxisLabelTooltip='true' :xAxisLabelLimit='5' :rotate='25' :option='pieEchartOption1'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <span style="color:#333333; font-size:14px; font-weight: bold; margin-left:31px;">实施工程数量分布</span>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption2.series || pieEchartOption2.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :width='chartsWidth' :height='chartsHeight' ref="pieEchart2" :xAxisLabelTooltip='true' :xAxisLabelLimit='5' :rotate='25' :option='pieEchartOption2'></app-charts-basic>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import blockTitle from '../blockTitle'
import * as odc from "@/api/operatingDataCenter";
import { barOption, colors, dateTypeEnum2 } from "../vars";

export default {
    name: 'shengchan',
    components: {
        noData,
        blockTitle,
    },
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    mounted() {
        this.getImplementDetailsChart()
    },
    data() {
        return {
            chartsWidth: '90%',
            dateTypeEnum: dateTypeEnum2,
            loading: false,
            period: 5,
            chartsHeight: '180px',
            pieEchartOption1: JSON.parse(JSON.stringify(barOption)),
            pieEchartOption2: JSON.parse(JSON.stringify(barOption)),
            
            formData: {
                ImplementCount: 0, 
            },
        }
    },
    methods: {
        getImplementDetailsChart() {
            let that = this
            that.loading = true
            odc.getImplementDetailsChart({Period: that.period}).then(res => {
                that.loading = false

                that.formData.ImplementCount = res.ImplementCount || 0

                that.pieEchartOption1 = that._initCompletionRateBarChartDatas(res.CompletionRateData || [])
                that.pieEchartOption2 = that._initBarChartDatas(res.QuantityDistributionData || [])
            }).catch(err => {
                that.loading = false
            })

        },

         _initCompletionRateBarChartDatas(list) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                targetOption = {
                    grid:{
                        left: 50
                    },
                    xAxis: {
                         type: 'category',
                         boundaryGap: false,
                        //  axisLabel: {//x轴文字的配置
                        //     show: true,
                        //     textStyle: {
                        //     color: "#1D2129",
                        //     fontSize: '12',
                        //     fontWeight: 'bold',
                        //     }
                        //  },

                        data: chartDatas.map(s => s.Label)
                    },

                    yAxis: {
                       type: 'value',
                       splitLine: {
                              show: true,
                              lineStyle: {
                                 type: 'solid',
                                 color: '#E9EAEF', // 颜色
                                 }
                        },

                       axisLine: {
                            lineStyle: {
                                 color: '#E9EAEF', // 颜色
                               }
                       },
                    //    axisLabel: {
                    //         textStyle: {
                    //           show:true,
                    //             color: "#1D2129",
                    //             fontSize: '12',
                    //             fontWeight: 'bold',
                    //         },                           
                    //     },

                       data: chartDatas.map(s => s.Label)
                    },
                    
                    series: [
                        {   
                            type: 'line',
                            smooth: true,
                            areaStyle: {},
                            lineStyle: {
                                  color: '#3D73DD',
                                  width: 4
                            },
                            symbolSize:10,
                            data: chartDatas.map(s => s.Value)
                        }
                    ]
                }
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(barOption)), targetOption)

            return targetOption

        },

        _initBarChartDatas(list) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                targetOption = {
                    xAxis: {
                        //  axisLabel: {//x轴文字的配置
                        //     show: true,
                        //     textStyle: {
                        //        color: "#1D2129",
                        //        fontSize: '12',
                        //        fontWeight: 'bold',
                        //     }
                        //  },

                        data: chartDatas.map(s => s.Label)
                    },

                    yAxis: {
                       splitLine: {
                              show: true,
                              lineStyle: {
                                 type: 'solid',
                                 color: '#E9EAEF', // 颜色
                                 }
                        },

                       axisLine: {
                            lineStyle: {
                                 color: '#E9EAEF', // 颜色
                               }
                       },
                    //    axisLabel: {
                    //         textStyle: {
                    //           show:true,
                    //             color: "#1D2129",
                    //             fontSize: '12',
                    //             fontWeight: 'bold',
                    //         },                           
                    //     },

                       data: chartDatas.map(s => s.Label)
                    },
                    
                    series: [
                        {
                            data: chartDatas.map(s => s.Value)
                        }
                    ]
                }
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(barOption)), targetOption)

            return targetOption

        },
    },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
.block-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    .inner-content{
        flex: 1;
        display: flex;
        flex-direction: column;
        .bottom{
            flex: 1;
            display: flex;
            margin-top: 33px;
            .flex-dire-column-wrapper{
                flex: 1;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
    }
}

.chart-title{
    text-align: center;
}

.flex-1, .flex-2{
    box-sizing: border-box;
    margin: 5px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    >div:last-child{
        flex: 1;
    }
}

.flex-1{
    flex: 1;
}

.text-content{
    text-align: center; flex: 1; font-weight: bold; display: flex; justify-content: center; align-items: center; word-break: break-all; white-space: normal; word-break: break-all;
}

.shape{
   width: 10px;height: 50px;background: #E9EAEF;
}

</style>