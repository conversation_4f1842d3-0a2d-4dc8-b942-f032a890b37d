<template>
<div class="container">
  <div class="err-page-container">
    <img class="lft" :src="errPageInfo.errImg" alt="">
    <div class="rgt">
      <div class="err-code">{{ errPageInfo.errCode }}</div>
      <div class="err-msg">{{ errPageInfo.errMsg }}</div>
      <div class="btn-wrap">
        <el-button type="primary" @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</div>
</template>

<script>

import { websitEnv } from "@/utils/env";
export default {
  name: 'page404',
  computed: {
    errPageInfo() {
      let key = this.$route.path.toLowerCase()
      return this.errPages[key] || {}
    }
  },
  data() {
    return {
      errPages: {
        '/404': {
          errCode: '404',
          errMsg: '页面丢失了',
          errImg: require('../../assets/images/404.png')
        },
        '/500': {
          errCode: '500',
          errMsg: '服务器无响应',
          errImg: require('../../assets/images/500.png')
        }
      }
    }
  },
  methods: {
    goHome() {
      this.$router.push({path: websitEnv.mainPage.url})
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.container{
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
	justify-content: center;

}

.err-page-container{
  // width: 400px;
  display: flex;
  .lft{
    flex: 1;
    height: 160px;
    margin-right: 50px;
  }
  .rgt{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 15px 0;
    .err-code, .err-msg{
      font-size: 24px;
      font-weight: 700;
    }
  }

}


</style>
