<template>
  <div>
    <app-dialog
      title="故障现象详情"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='800'
    >
      <template slot="body" v-loading="loading">
        <main>
            <div class="bd">
                <div class="panel-title">基本信息</div>
                <div class="dp"><span>故障代码：</span>{{data.FailureCaseCode}}</div>
                <div class="dp"><span>故障名称：</span>{{data.FailureSymptom}}</div>
                <div class="dp"><span>故障关键字：</span>{{data.keyList | filterList}}</div>
            </div>
            <div class="bd">
                <div class="panel-title">故障分类标签</div>
                <ul class="elUl dp" v-if="data.EquipmentSettingIds.length>0">
                    <li><span style="font-weight:bold;">故障类型：</span>{{data.Name}}</li>
                    <li v-for="(sd,index) in data.selectedData" v-show="sd.checkList.length>0" :key="index"><span style="font-weight:bold;">{{sd.Name}}：</span>{{sd.checkList.join('、')}}</li>
                </ul>
                <no-data v-else></no-data>
            </div>
            <div class="bd">
                <div class="panel-title">相关附件</div>
                <app-uploader
                :readonly='true'
                accept='all'
                :fileType='3'
                :max='10000'
                :value='data.AttachmentList'
                :fileSize='1024 * 1024 * 500'
                :minFileSize='100 * 1024'
            ></app-uploader>
            <no-data v-show="data.AttachmentList.length<=0"></no-data>
            </div>
        </main>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" text="关闭"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as failurecase from '@/api/failurecase';
import * as equipmentSetting from '@/api/equipmentSetting';
import { listToTreeSelect } from '@/utils';
import NoData from "@/views/common/components/noData";
export default {
  name: "",
  components: {
      NoData

  },
  mixins: [],
  props: {
    id:{
        type:String,
        default:''
    }
  },
  filters:{
      filterList(a){
          if(a.length>0){
              return a.join('、');
          }else{
              return '';
          }
      }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
        if(val){
            this.getData();
        }
    },
  },
  created() {
    
  },
  data() {
    return {
        loading:false,
       data:{
           FailureCaseCode:'',
           FailureSymptom:'',
           Name:'',
           keyList:[],
           EquipmentSettingIds:[],
           selectedData:[],
           AttachmentList:[],
       },
    };
  },
  methods: {
      getData(){
          let a=failurecase.getDetails({Id:this.id}),
          b=equipmentSetting.getListPage({"PageIndex":1,"PageSize": 9999,});
          this.loading=true;
          Promise.all([a,b]).then((res) => {
              this.loading=false;
              console.log(res)
              res[0].keyList=[];
              res[0].FaultKeywordList.forEach(v => {
                  res[0].keyList.push(v.FaultKeyWordName);
              })
              res[0].selectedData=[];
              let listData = listToTreeSelect(res[1].Items)
              if(res[0].EquipmentSettingIds.length>0){
                    let c=listData.find(s => s.Id == res[0].EquipmentSettingIds[0]),d=null;
                    res[0].Name=c.Name;
                    c.children.forEach(v => {
                        v.checkList=[];
                        v.children.forEach(v1 => {
                            d=res[0].EquipmentSettingIds.find(s => s == v1.Id);
                            if(d){
                                v.checkList.push(v1.Name);
                            }
                        })
                    })
                    res[0].selectedData=c.children;
                }
            this.data=res[0];
          })
      },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style lang="scss" scoped>
    .dp{
        padding:0 10px 10px 6px;
        >span{
            font-weight: bold;
        }
    }
    .bd{
        border-bottom:1px solid #ebeef5;
        margin-bottom:10px;
    }
    .elUl{
        li{
            margin-bottom: 6px;
        }
    }
</style>
