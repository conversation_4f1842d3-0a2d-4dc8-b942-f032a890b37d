<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                    <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <page-title :title="departmentInfo"></page-title>
                <div class="tagBox">
                    <div class="year-wrapper">
                        切换年份：
                        <el-select v-model="listQuery.Year" placeholder="请选择" @change='getList'>
                            <el-option v-for="item in years" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="btn-list">
                        <v-button-list :btnListData='monList' v-model="listQuery.Month" @change="getList"></v-button-list>
                    </div>
                </div>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :isShowBtnsArea='false' :startOfTable="startOfTable" :multable="false">

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch">
                                <template slot="Name">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                                </template>
                                <!-- <template slot="Mobile">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Mobile" placeholder></el-input>
                                </template> -->
                                <template slot="Number">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Number" placeholder></el-input>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格批量操作区域 -->
                        <!-- <template slot="btnsArea"> -->
                        <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                        <!-- </template> -->

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row)" :type="2" text='考勤详情'></app-table-row-button>
                        </template>
                    </app-table>

                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 查看/修改 -->
    <create-page v-if="currentRow" @closeDialog="closeDialog" :row='currentRow' :month='listQuery.Month' :year='listQuery.Year' :timecardNo='timecardNo' :machineNo='machineNo' :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus"></create-page>
</div>
</template>

<script>
import {
    listToTreeSelect
} from "@/utils";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import createPage from "./create";
import vButtonList from '@/views/common/buttonList'
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"

export default {
    name: "attendanceMgmt-datas-index",
    mixins: [indexPageMixin],
    components: {
        createPage,
        vButtonList,
    },
    props: {},
    filters: {
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.DepartmentId = val.Id;
                    this.selectDepartmentName = val.DepartmentName;
                    this.getList();
                }
            },
            immediate: true,
        },
    },
    created() {
        let currentYear = (new Date()).getFullYear()
        this.years = Array.from(Array(5), (v,k) => { 
            return {
                value: currentYear - k,
                label: currentYear - k
            }
        })
        // for(let i = currentYear - 4; i <= currentYear; i++) {
        //     this.years.push({
        //         value: i,
        //         label: i
        //     })
        // }

        this.getDepartments();
    },
    data() {
        return {
            currentRow: null,
            // currentMonth: 1,
            years: [],
            monList: Array.from(Array(12), (v,k) => {
                return {
                    value: k + 1,
                    label: `${k+1}月`
                }
            }),
            epKeys: [],

            departmentInfo: "",
            selectDepartmentName: "",

            // statusEnum: statusEnum,

            filterText: "",

            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            tableSearchItems: [{
                    prop: "Name",
                    label: "姓名"
                },
                // {
                //     prop: "Mobile",
                //     label: "手机号"
                // },
                {
                    prop: "Number",
                    label: "工号"
                },
            ],

            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            
            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "Name",
                        label: "姓名",
                        showOverflowTooltip: true
                    },
                },
                // {
                //     attr: {
                //         prop: "Sex",
                //         label: "性别",
                //     },
                //     slot: true,
                // },
                {
                    attr: {
                        prop: "Number",
                        label: "工号",
                    },
                },
                {
                    attr: {
                        prop: "DepartmentName",
                        label: "部门",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MachineNo",
                        label: "机器号码",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "TimecardNo",
                        label: "考勤号码",
                        showOverflowTooltip: true,
                    },
                },
            ],
            listQuery: {
                DepartmentId: "",
                Name: "",
                // Mobile: "",
                Number: '',
                Year: (new Date()).getFullYear(),
                Month: (new Date()).getMonth() + 1
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

            machineNo: '',
            timecardNo: '',
        };
    },
    methods: {
        closeDialog() {
            this.dialogFormVisible = false;
        },
        // handleSaveSuccess(_formData) {
        //     this.getList();
        //     this.closeDialog();
        // },

        //获取成员列表
        getList() {
            if (this.checkedNode) {
                this.listLoading = true;
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                // postData = this.assignSortObj(postData);
                timecardDepartment
                    .getTimecardEmployeeRecords(postData)
                    .then((res) => {
                        this.tabDatas = res.Items;
                        this.total = res.Total;
                        this.listLoading = false;
                        this.departmentInfo = this.selectDepartmentName;
                    })
                    .catch((err) => {
                        this.listLoading = false;
                    });
            }
        },
        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.EmployeesId;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        //弹出详情框
        handleReview(row, optType = "detail") {
            this.currentRow = row
            this.id = row.EmployeesId;
            this.machineNo = row.MachineNo
            this.timecardNo = row.TimecardNo
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        onResetSearch() {
            this.listQuery.Name = "";
            // this.listQuery.Mobile = "";
            this.listQuery.Number = ''
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            systemDepartment
                .getListByCondition(this.departmentListQuery)
                .then((res) => {
                    this.treeDatas = listToTreeSelect(res);
                    
                    if(this.treeDatas && this.treeDatas.length>0){
                        this.treeDatas.forEach(v => {
                            this.epKeys.push(v.Id);
                            // if(v.children.length>0){
                            //     v.children.forEach(v1 => {
                            //         this.epKeys.push(v1.Id);
                            //     })
                            // }

                        })
                    }
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        flex: 1;
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 10px 8px;
    display: flex;
    .year-wrapper{
        >div:first-child{
            width: 100px!important;
        }
    }
    .btn-list{
        flex: 1;
        margin-left: 20px;
        display: flex;
        align-items: center;
    }
}
</style>
