<template>
  <div>
    <app-dialog
      :title="pageItlte"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="600"
    >
      <template slot="body">
        <el-form
          ref="formData"
          :rules="rules"
          :model="formData"
          label-position="right"
          :label-width="labelWidth"
        >
          <div class="wrapper">
            <div class="left">
              <el-row class="item-content">
                <el-col :span="24">
                  <el-form-item label="处理人" prop="EmployeeList">
                    <emp-selector
                      :readonly="dialogStatus != 'create'"
                      :isShowClearButton="false"
                      :sourceType="'onlyAuthUsers'"
                      :multiple="false"
                      :showType="2"
                      key="service-users"
                      :list="formData.EmployeeList"
                      @change="handleChangeUsers"
                    ></emp-selector>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="问题名称" prop="Name">
                    <el-input
                      :disabled="dialogStatus != 'create'"
                      type="text"
                      maxlength="50"
                      v-model="formData.Name"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="问题描述" prop="Description">
                    <el-input
                      :disabled="dialogStatus != 'create'"
                      type="textarea"
                      maxlength="2000"
                      :rows="8"
                      v-model="formData.Description"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="dialogStatus != 'create'">
                  <el-form-item label="处理状态" prop="Status">
                    <el-radio
                      :disabled="!editable"
                      v-show="item.value != 1"
                      v-for="(item, idx) in status"
                      :key="idx"
                      v-model="formData.Status"
                      :label="item.value"
                    >{{ item.label }}</el-radio>
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="dialogStatus != 'create'">
                  <el-form-item label="处理结果" prop="Result">
                    <el-input
                      :disabled="!editable"
                      type="textarea"
                      maxlength="2000"
                      :rows="8"
                      v-model="formData.Result"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="right">
              <div>
                <div class="panel-title">附件</div>
                <div>
                  <app-uploader
                    :readonly="dialogStatus != 'create'"
                    accept="all"
                    :fileType="3"
                    :max="10000"
                    :value="formData.AttachmentList"
                    :fileSize="1024 * 1024 * 500"
                    :minFileSize="100 * 1024"
                    @change="handleFilesUpChange"
                    ref="appuploader"
                  ></app-uploader>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button
          v-if="dialogStatus != 'detail'"
          @click="createData"
          :buttonType="1"
          :disabled="disabledBtn"
        ></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as impMgmt from "@/api/implementation/impManagement2";
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import empSelector from "../../../common/empSelector";
import { vars } from "../../common/vars";

export default {
  name: "edit-region",
  props: {
    dialogStatus: {
      //create、update、detail
      type: String
    },
    // region: {
    //     type: Object,
    //     required: true
    // },
    //新增必须
    impRegionalId: {
      type: String,
      default: ""
    },
    //编辑、详情必须
    id: {
      type: String,
      default: ""
    }
  },
  computed: {
    pageItlte() {
      if (this.dialogStatus == "create") {
        return "创建问题";
      } else if (this.dialogStatus == "detail") {
        return "问题详情";
      } else if (this.dialogStatus == "update") {
        return "问题处理";
      }
    },
    editable() {
      return this.dialogStatus != "detail";
    }
  },
  components: {
    empSelector
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.isContinue = false;
      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
    // region: {
    //     handler(val) {
    //         this.formData = Object.assign({}, this.formData, JSON.parse(JSON.stringify(val)))
    //     },
    //     // deep: true,
    //     immediate: true
    // }
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      status: vars.questionStatus,
      rules: {
        EmployeeList: { fieldName: "处理人", rules: [{ required: true, trigger: 'change' }] },
        Name: { fieldName: "问题名称", rules: [{ required: true }] },
        Description: { fieldName: "问题描述", rules: [{ required: true }] },
        tDescription: { fieldName: "问题描述", rules: [{ required: true }] },
        Status: { fieldName: "处理状态", rules: [{ required: true }] },
        Result: { fieldName: "处理结果", rules: [{ required: true }] },
      },
      disabledBtn: false,
      labelWidth: "100px",
      formData: {
        Id: "",
        Name: "", //问题名称
        ImplementationRegionalId: "", //实施地区ID
        Description: "", //问题描述
        EmployeeList: [],
        AttachmentList: [],
        Status: 2, //处理状态
        Result: "" //处理结果
      }
    };
  },
  methods: {
    resetFormData() {
      this.formData = {
        Id: "",
        Name: "", //问题名称
        ImplementationRegionalId: "", //实施地区ID
        Description: "", //问题描述
        EmployeeList: [],
        AttachmentList: [],
        Status: 2, //处理状态
        Result: "" //处理结果
      };
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));
          postData.EmployeeIdList = postData.EmployeeList.map(
            s => s.EmployeeId
          );
          postData.AttachmentIdList = postData.AttachmentList.map(s => s.Id);

          if (this.dialogStatus == "create") {
            postData.ImplementationRegionalId = this.impRegionalId;
          } else if (this.dialogStatus == "update") {
            postData.Id = this.id;
          }

          this.disabledBtn = true;
          let result = null;
          if (this.dialogStatus == "create") {
            result = impMgmt.addQues(postData);
          } else if (this.dialogStatus == "update") {
            result = impMgmt.editQues(postData);
          }
          result
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        }
      });
    },
    getDetail() {
      if (this.id) {
        impMgmt.getQeus({ id: this.id }).then(res => {
          this.formData = Object.assign({}, this.formData, res);
          //如果是处理问题，那么设置状态默认值为 2 处理中
          if (this.dialogStatus == "update" && this.formData.Status < 2) {
            this.formData.Status = 2;
          }
        });
      }
    },
    handleFilesUpChange(files) {
      this.formData.AttachmentList = files;
    },
    handleChangeUsers(users) {
      this.formData.EmployeeList = users;
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  max-height: 500px;
  // .left, .right {
  //     display: inline-block;
  // }
  .left {
    width: calc(66% - 10px) !important;
    padding-right: 5px;
    // float: left;
  }
  .right {
    width: 34% !important;
    // float: right;
    // height: 636px;
    // padding-left:10px;
    // overflow-y: auto;
    border-left: 1px solid #dcdfe6;
    padding-left: 5px;
  }
}
</style>
