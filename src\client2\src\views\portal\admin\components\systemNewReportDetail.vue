<template>
  <div class="newReportDiv">
    <app-dialog title="新人详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600" :destroy-on-close="true" :close-on-press-escape="false" :modal-append-to-body="false">
      <template slot="body">
        <el-form ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" class="newReportForm">
          <div class="form-wrapper">
            <el-row>
              <el-col :span="12">
                <el-form-item label="姓名：" prop="Name">
                  {{formData.Name}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别：" prop="Sex">
                  {{formData.Sex == 1 ?"男":"女"}}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="年龄：" prop="Age">
                  {{formData.Age}}岁
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工号：" prop="Number">
                  {{formData.Number}}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="星座：" prop="Constellation">
                  {{ ConstellationOptions.find(s=>s.value == formData.Constellation ).label }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="入职时间：" prop="EntryTime">
                  {{formData.EntryTime  | dateFilter('YYYY-MM-DD')}}
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="自我介绍：" prop="SelfIntroduction">
              {{formData.SelfIntroduction}}
            </el-form-item>

            <el-form-item label="兴趣爱好：" prop="HobbiesAndInterests">
              {{formData.HobbiesAndInterests}}
            </el-form-item>

            <el-form-item label="新人生活照：" prop="LifePhotos">
              <viewer :images="formData.LifePhotoPathList">
                <img v-for="(item, index) in formData.LifePhotoPathList" :src="item.src" :key="index" class="viewerImg">
              </viewer>
            </el-form-item>

            <!-- 评论区 -->
            <div class="commentInfoClass">
              <b>评论 ({{commentListCount}}) </b>&nbsp;
              <el-button type="primary" size="mini" @click="dialogCommentVisible = true">我要评论</el-button>
               <!-- :style="{ color: formData.IsTheCurrentUserThumbUp ? 'red' : 'black' }" -->
              <a @click="giveLike" :title="formData.IsTheCurrentUserThumbUp ? '取消点赞':'点赞'">
                <svg-icon :icon-class="formData.IsTheCurrentUserThumbUp ? 'zan-active' : 'zan'"></svg-icon>&nbsp;{{formData.GiveLikeNum}}
              </a>
            </div>

            <br />

            <!-- 评论列表 -->
            <div v-loading="commentLoading">
              <div class="commentListClass" v-for="comment in commentList" :key="comment.id">
                <span>{{comment.CreateEmployeeName}}</span> &nbsp;&nbsp;
                <span>{{comment.CreateTime  | dateFilter('YYYY-MM-DD HH:mm')}}</span>
                <a @click="withdraw(comment)" v-if="IsShowRecall(comment)" title="撤回评论">撤回</a>
                <p style="margin-top: 5px;" v-html="comment.CommentContent"></p>
                <hr />
              </div>
            </div>
          </div>

        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>

    <!-- 我要评论 -->
    <el-dialog title="我要评论" ref="commentDialogRef" :visible.sync="dialogCommentVisible" :close-on-click-modal="false">
      <el-input maxlength="500" type="textarea" :rows="4" placeholder="请输入内容" v-model="commentContent"></el-input>
      <template slot="footer">
        <app-button @click="handleCommentSave" :buttonType='1'></app-button>
        <app-button @click="handleCommentClose" :buttonType='2'></app-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import * as systemNewReport from '@/api/personnelManagement/systemNewReport'
import { getUserInfo } from "@/utils/auth";

export default {
  name: "systemNewReport-Detail",
  directives: {},
  components: {
  },
  mixins: [],
  props: {
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
        if (this.id) {
          this.getDetail();
          this.getCommentList();
        }
      }
    },
    "dialogCommentVisible"() {
      this.commentContent = ""
    }
  },
  computed: {
  },

  created() {

  },
  data() {
    return {
      commentLoading: false,
      commentContent: "",//评论内容
      dialogCommentVisible: false,
      commentListCount: 0,
      commentList: [],
      ConstellationOptions: [
        { value: 0, label: '白羊座' },
        { value: 1, label: '金牛座' },
        { value: 2, label: '双子座' },
        { value: 3, label: '巨蟹座' },
        { value: 4, label: '狮子座' },
        { value: 5, label: '处女座' },
        { value: 6, label: '天秤座' },
        { value: 7, label: '天蝎座' },
        { value: 8, label: '射手座' },
        { value: 9, label: '摩羯座' },
        { value: 10, label: '水瓶座' },
        { value: 11, label: '双鱼座' }
      ],
      formLoading: false,
      labelWidth: "100px",
      formData: {
        NewReportId: "",
        DepartmentId: null,
        Cover: "",
        CoverPath: "",
        Name: "",
        Sex: "",
        Age: "",
        Constellation: "",
        Number: "",
        EntryTime: "",
        SelfIntroduction: "",
        HobbiesAndInterests: "",
        LifePhotos: "",
        LifePhotoPaths: "",
        LifePhotoPathList: [],
        GiveLikeNum: 0
      },
    };
  },
  methods: {
    resetFormData() {
      this.commentListCount = 0;
      this.commentList = [];
      let temp = {
        NewReportId: "",
        DepartmentId: null,
        Cover: "",
        CoverPath: "",
        Name: "",
        Sex: "",
        Age: "",
        Constellation: "",
        Number: "",
        EntryTime: "",
        SelfIntroduction: "",
        HobbiesAndInterests: "",
        LifePhotos: "",
        LifePhotoPaths: "",
        LifePhotoPathList: [],
        GiveLikeNum: 0
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    //判断是否显示 撤回
    IsShowRecall(comment) {
      return getUserInfo().employeeid == comment.CreateEmployeeId
    },

    //获取详情
    getDetail() {
      this.formLoading = true;
      systemNewReport.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        if (this.formData.LifePhotos) {
          this.formData.LifePhotos = this.formData.LifePhotos.split(',')
        }

        if (this.formData.LifePhotoPaths) {
          var photoPathList = this.formData.LifePhotoPaths.split(',')

          var imgList = [];
          photoPathList.forEach(element => {
            var imgObj = {
              msrc: element,
              src: element,
            };
            imgList.push(imgObj);
          });

          this.formData.LifePhotoPathList = imgList;
        }

        this.formLoading = false;
        // if (res != null) {
        //   this.getCommentList();
        // }
      });
    },

    //获取评论集合
    getCommentList() {
      this.commentLoading = true;
      systemNewReport.getComment({ businessId: this.id }).then(res => {
        this.commentList = res
        this.commentListCount = res.length;
        this.commentLoading = false;
      })
    },

    //点赞 or 撤回
    giveLike() {
      systemNewReport.setGiveLike({ businessId: this.id }).then(res => {
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
        this.getDetail();
      })
    },

    //撤回评论
    withdraw(comment) {
      systemNewReport.recall({ businessId: comment.Id }).then(res => {
        this.$notify({
          title: '成功',
          message: '撤回成功',
          type: 'success',
          duration: 2000
        })
        this.getCommentList();
      })
    },

    //提交评论
    handleCommentSave() {
      if (this.commentContent.trim() == "") {
        this.$message.error('请输入评论内容');
        return;
      }
      var obj = {
        CurrentBusinessId: this.id,
        CommentContent: this.commentContent,
        Type: 12,
      };

      systemNewReport.addComment(obj).then(() => {
        this.$notify({
          title: '成功',
          message: '评论成功',
          type: 'success',
          duration: 2000
        })
        this.commentContent = ""
        this.dialogCommentVisible = false
        this.getCommentList();
      })
    },

    handleCommentClose() {
      this.$refs.commentDialogRef.handleClose();
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<style scoped>
.viewer-backdrop {
  background-color: rgba(0, 0, 0, 1);
}
.viewerImg {
  height: auto;
  width: 100px;
  margin-left: 10px;
}

.form-wrapper >>> .el-form-item__label {
  padding-right: 0!important;
}
</style>

<style lang="scss" scoped>
.newReportForm {
  margin-right: 40px;
  label {
    font-weight: 100 !important;
  }
}
.wrapper {
  display: flex;
  .left {
    flex: 1;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}

.commentInfoClass {
  margin-left: 20px;
  border-top: 1px solid silver;
  border-bottom: 1px solid silver;
  // height: 40px;
  line-height: 40px;
  width: 100%;
  b {
    font-size: 15px;
    float: left;
  }
  a {
    float: right;
    margin-right: 15px;
    display: flex;
    align-items: center;
  }
}

.commentListClass {
  margin-left: 20px;
  p {
    word-wrap: break-word;
    word-break: break-all;
  }
  hr {
    background-color: #e1e1e1;
    height: 1px;
    border: none;
  }
  a {
    color: red;
    float: right;
    margin-right: 15px;
  }
}
</style>
