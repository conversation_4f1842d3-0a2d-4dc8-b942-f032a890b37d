<template>
	<div class="app-wrapper" :class="classObj">
        <div class="main-container">

            <div class="main-page-height">

                
                <el-aside style="width: auto;" v-show="showSidebar">
                    <sidebar class="sidebar-container"></sidebar>
                </el-aside>
                <el-main :class="showSidebar ? 'cus-el-main' : 'cus-el-main-not-sidebar'">


                    <el-header>
                        <!-- <div class="headDiv">
                            <img src="@/assets/logo.png">
                            <span class="comp-name">
                            佳运通办公协同平台
                            <div class="comp-name-tag">beta</div>
                            </span>
                        </div> -->
                        <div style="display: flex; align-items:center;">
                            
                            <navbar style="flex: 1;"></navbar>
                        </div>
                        <!-- <tags-view></tags-view> -->
                    </el-header>

                    <app-main></app-main>
                </el-main>
            </div>
            <!-- <el-container> -->
            
            <!-- <el-container>
                
            </el-container> -->
            <!-- </el-container> -->
        </div>
	</div>
</template>

<script>

import { Navbar, Sidebar, AppMain, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'//还原放出这里


export default {
    name: 'layout',
    components: {
        Navbar,
        Sidebar,
        AppMain,
        TagsView,
    },
    mixins: [ResizeMixin],//还原放出这里
    computed: {

        sidebar() {
            return this.$store.state.app.sidebar
        },
        device() {
            return this.$store.state.app.device
        },
        classObj() {
            return {
                hideSidebar: !this.sidebar.opened,
                openSidebar: this.sidebar.opened,
                withoutAnimation: this.sidebar.withoutAnimation,
                mobile: this.device === 'mobile'
            }
        }
    },
    data() {
        return {

        }
    },
    created(){
        
    },
    methods: {

        handleClickOutside() {
            this.$store.dispatch('CloseSideBar', { withoutAnimation: false })
        }
    }
}
</script>
<style>
.white_Bg.el-tooltip__popper.is-light{
    border: 1px solid #DCDFE6 !important;
}
.white_Bg.el-tooltip__popper.is-light .popper__arrow{
        border-bottom-color: #DCDFE6 !important;
}
.white_Bg.el-tooltip__popper{
    background: rgba(255, 255, 255, 1) !important;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/mixin.scss';
// .headDiv{
//   display: flex;
//   align-items: center;
//   // padding-top:16px;
//   height: 52px;
//   img{
//     margin-left: 20px;
//   }
//   span{
//     margin-left:10px;
//     font-size: 18px;
//     font-weight: 600;
//   }
// }
.app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
    background: #e9eaef;
    &.mobile.openSidebar {
        position: fixed;
        top: 0;
    }
}
.main-container{
  height: 100%;
}
.drawer-bg {
    background: $text-primary;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
}


.el-header {
    padding: 0;
    background-color: #fff;
    top: 0;
    right: 0;
    height: auto!important;
    // width: 100%;
    // width: auto;
    z-index: 100;
    // padding-right: 10px;
    border-bottom: 1px solid #d8dce5;
    // box-shadow: 0 1px 3px 0 rgba(0,0,0,.04), 0 0 3px 0 rgba(0,0,0,.04);
}

.comp-name{
  position: relative;
}

.comp-name-tag{
  position: absolute;
  top: -10px;
  right: -30px;
  background: #e3e3e3;
  color: #fff;
  font-size: 12px;
  padding: 2px;
  border-radius: 2px;
}


.hideSidebar .el-header{
  // width: calc(100% - 64px);
}

.mobile .el-header{
  width: 100%;
}


</style>
