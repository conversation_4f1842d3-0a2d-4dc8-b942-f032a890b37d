<template>
    <div>

    <el-row style="height: 100%;">
             
     <el-col :span=19>
                
        <el-row>

            <el-col class="items-group" :span="6">

                <div :class="currentIdx===0?'item-top-blue':'item-top'"  @click="handleClick(blockList[0].value)">
                    <!-- <blockTitle :obj='blockList[0]' @click="handleClick"></blockTitle> -->
                    <el-row style="height:100%;">
                        <el-col class="item" :span='12' style="height:100%;">
                            <div class="left">
                                 <img class="title-img" :src="blockList[0].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                 <span class="title">工作计划</span>
                                <!-- <div class="chart-title">本月</div>
                                <div>
                                    <noData v-if="!hasDataOfPie(pieEchartOption1.series)"></noData>
                                    <app-charts-basic v-else :height='chartsHeight' ref="pieEchart1" :option='pieEchartOption1'></app-charts-basic>
                                </div> -->
                            </div>
                        </el-col>
                        <el-col :span='12'>
                             <el-row style="height: 50%; display:flex">
                                <el-col :span='2' style="height: 40px; display: flex; flex-direction: column; margin-top: 19px; margin-left: 20px;">
                                    <div :style="{background:'#E9EAEF',width:'10px',height:((notSubmitted/(submitted+notSubmitted))*40)+'px'}"></div>
                                    <div :style="{background:'#3D73DD',width:'10px',height:((submitted/(submitted+notSubmitted))*40)+'px'}"></div>
                                </el-col>
                                <el-col style="display:flex; flex-direction: column; justify-content:flex-start; height:100%; width: 90%; margin-left: 10px;" :span='22'>
                                    <span class="right-title">已提交</span>
                                    <span class="right-content">{{submitted}}</span>
                                </el-col>
                            </el-row>
                            <el-row style="height: 50%; display:flex">
                                 <el-col :span='2' style="height: 40px; display: flex; flex-direction: column; margin-top: 19px; margin-left: 20px;">
                                    <div :style="{background:'#E9EAEF',width:'10px',height:((submitted/(submitted+notSubmitted))*40)+'px'}"></div>
                                    <div :style="{background:'#E32B06',width:'10px',height:((notSubmitted/(submitted+notSubmitted))*40)+'px'}"></div>
                                </el-col>
                                <el-col style="display:flex; flex-direction: column; justify-content:flex-start; height:100%; width: 90%; margin-left: 10px;" :span='22'>
                                   <span class="right-title">未提交</span>
                                   <span class="right-content">{{notSubmitted}}</span>
                                </el-col>
                            </el-row>
                            <!-- <div>
                                <div class="chart-title">本周</div>
                                <div>
                                    <noData v-if="!hasDataOfPie(pieEchartOption2.series)"></noData>
                                    <app-charts-basic v-else :height='chartsHeight' ref="pieEchart2" :option='pieEchartOption2'></app-charts-basic>
                                </div>
                            </div> -->
                        </el-col>
                    </el-row>
                </div>



                <div :class="currentIdx===2?'item-top-blue':'item-top'" @click="handleClick(blockList[2].value)">
                    <!-- <blockTitle :obj='blockList[2]' @click="handleClick"></blockTitle> -->
                    <el-row style="height:100%;">
                        <el-col class="item" :span='12' style="height: 100%;">
                            <div class="left">
                                 <img class="title-img" :src="blockList[2].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                 <span class="title">生产</span>
                                <!-- <div class="chart-title">今年实施工程数量</div>
                                <div>
                                    <div class="text-content num">{{ implementCount }}</div>
                                </div> -->
                            </div>
                        </el-col>
                        <el-col :span='12' style="height: 100%;">
                             <el-row style="height: 50%;">
                                <el-col class="right-item" style="margin-left: 40px;" :span='24'>
                                    <span class="right-title">今年实施工程数量</span>
                                    <span class="right-content">{{implementCount}}</span>
                                </el-col>
                            </el-row>
                            <el-row style="height: 50%; display:flex">
                                <el-col :span='2' style="height: 40px; display: flex; flex-direction: column; margin-top: 19px; margin-left: 20px;">
                                    <div :style="{background:'#E9EAEF',width:'10px',height:(((100-progressSum)/100)*40)+'px'}"></div>
                                    <div :style="{background:'#3D73DD',width:'10px',height:((progressSum/100)*40)+'px'}"></div>
                                </el-col>

                                <el-col style="display:flex; flex-direction: column; justify-content:flex-start; height:100%; width: 90%; margin-left: 10px;" :span='22'>
                                   <span class="right-title">今年实施工程进度</span>
                                   <span class="right-content">{{progressSum}} <span style=" font-size:10px; color:#1D2129;font-weight:bold; margin-top:2px;">%</span></span>
                                </el-col>
                            </el-row>
                            <!-- <div>
                                <div class="chart-title">今年实施工程进度（%）</div>
                                <div>
                                    <div class="text-content num">{{ progressSum }}</div>
                                </div>
                            </div> -->
                        </el-col>
                    </el-row>
                </div>


            </el-col>
          
            <el-col class="items-group" :span="6">
                <div :class="currentIdx===3?'item-top-blue':'item-top'"  @click="handleClick(blockList[3].value)">
                    <!-- <blockTitle :obj='blockList[3]' @click="handleClick"></blockTitle> -->
                  
                    <el-row style="height: 100%;">
                        <el-col class="item" :span='12' style="height: 100%;">
                            <div class="left">
                                <img class="title-img" :src="blockList[3].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                <span class="title">销售</span>
                            </div>
                        </el-col>
                        <el-col :span='12' style="height: 100%;">
                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月新增客户</span>
                                    <span class="right-content">{{monthCreated}}</span>
                                </el-col>
                            </el-row>
                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                   <span class="right-title">本月拜访客户</span>
                                   <span class="right-content">{{monthCustomerVisit}}</span>
                                </el-col>
                            </el-row>
                            <!-- <div class="flex-dire-column-wrapper">
                                <div class="chart-title">本月拜访客户</div>
                                <div>
                                    <div class="text-content num">{{ monthCustomerVisit }}</div>
                                </div>
                            </div> -->
                        </el-col>
                    </el-row>
                </div>

               <div :class="currentIdx===4?'item-top-blue':'item-top'"  @click="handleClick(blockList[4].value)">
                    <!-- <blockTitle :obj='blockList[4]' @click="handleClick"></blockTitle> -->
                    <el-row style="height: 100%;">
                        <el-col class="item" :span='12' style="height: 100%;">
                            <div class="left">
                                <img class="title-img" :src="blockList[4].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                <span class="title">研发</span>
                                <!-- <div class="chart-title">产品数量</div>
                                <div>
                                    <div class="text-content num">{{ productCount }}</div>
                                </div> -->
                            </div>
                        </el-col>

                        <el-col :span='12' style="height: 100%;">

                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                    <span class="right-title">产品数量</span>
                                    <span class="right-content">{{productCount}}</span>
                                </el-col>
                            </el-row>
                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                   <span class="right-title">项目数量</span>
                                   <span class="right-content">{{projectCount}}</span>
                                </el-col>
                            </el-row>

                            <!-- <div class="flex-dire-column-wrapper">
                                <div class="chart-title">项目数量</div>
                                <div>
                                    <div class="text-content num">{{ projectCount }}</div>
                                </div>
                            </div> -->
                        </el-col>
                    </el-row>
                </div>

            </el-col>

            <el-col class="items-group" :span="6">
                
            
                  <div :class="currentIdx===6?'item-top-blue':'item-top'"  @click="handleClick(blockList[6].value)">
                    <!-- <blockTitle :obj='blockList[6]' @click="handleClick"></blockTitle> -->
                    <el-row style="height: 100%;">
                        <el-col class="item" :span='12' style="height: 100%;">
                            <div class="left">
                                <img class="title-img" :src="blockList[6].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                <span class="title">人事</span>
                                <!-- <div class="chart-title">本月总加班天数</div>
                                <div>
                                    <div class="text-content num">{{ monthOvertimeDays }}</div>
                                </div> -->
                            </div>
                        </el-col>
                        <el-col :span='12' style="height: 100%;">
                            <!-- <div>
                                <div class="chart-title">本月总请假天数</div>
                                <div>
                                    <div class="text-content num">{{ monthLeave }}</div>
                                </div>
                            </div> -->
                            
                             <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月总加班天数</span>
                                    <span class="right-content">{{monthOvertimeDays}}</span>
                                </el-col>
                            </el-row>
                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                   <span class="right-title">本月总请假天数</span>
                                   <span class="right-content">{{monthLeave}}</span>
                                </el-col>
                            </el-row>

                        </el-col>
                        <!-- <el-col :span='8' style="display: flex; flex-direction: column; height: 100%;">
                            <div class="flex-dire-column-wrapper">
                                <div class="chart-title">本月总迟到次数</div>
                                <div>
                                    <div class="text-content num">{{ monthTardiness }}</div>
                                </div>
                            </div>
                        </el-col> -->
                    </el-row>
                </div>


                 <div :class="currentIdx===5?'item-top-blue':'item-top'"  @click="handleClick(blockList[5].value)">
                    <!-- <blockTitle :obj='blockList[5]' @click="handleClick"></blockTitle> -->
                    <el-row style="height: 100%;">
                        <el-col class="item" :span='12' style="height: 100%;">
                            <div class="left">
                                <img class="title-img" :src="blockList[5].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                <span class="title">售后</span>
                                <!-- <div class="chart-title">本月报修单总数</div>
                                <div>
                                    <div class="text-content num">{{ monthCount }}</div>
                                </div> -->
                            </div>
                        </el-col>
                        <el-col :span='12' style="height: 100%;">
                             <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月报修单总数</span>
                                    <span class="right-content">{{monthCount}}</span>
                                </el-col>
                            </el-row>
                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                   <span class="right-title">本月报修收费（元）</span>
                                   <span class="right-content" :class ="[ getSizeByNum(monthRepairFee) ? 'normal' : 'mini' ]">{{monthRepairFee}}</span>
                                </el-col>
                            </el-row>
                            <!-- <div>
                                <div class="chart-title">本月报修收费（元）</div>
                                <div>
                                    <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthRepairFee)}">{{ monthRepairFee }}</div>
                                </div>
                            </div> -->
                        </el-col>
                    </el-row>
                </div>
                

            </el-col>


             <el-col class="items-group" :span="6">


                 <div class="item-top">
                    <!-- :class="currentIdx===7?'item-top-blue':'item-top'"  @click="handleClick(blockList[7].value)" -->
                      <!-- 采购 -->
                  
                        <!-- <blockTitle :obj='blockList[7]'></blockTitle> -->
                        <el-row style="height: 100%;">
                            <el-col class="item" :span='12' style="height: 100%;">
                                <div class="left">
                                <img class="title-img" :src="blockList[7].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                <span class="title">采购</span>
                                    <!-- <div class="chart-title" style="padding-top: 0;">本月采购单数量</div>
                                    <div>
                                        <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthPurchaseOrderCount)}">{{ monthPurchaseOrderCount }}</div>
                                    </div> -->
                                </div>
                            </el-col>
                            <el-col :span='12' style="height: 100%;">
                               
                                <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月采购单数量</span>
                                    <span class="right-content">{{monthPurchaseOrderCount}}</span>
                                </el-col>
                              </el-row>
                        
                              <el-row style="height: 50%;">
                                 <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月采购成本(元)</span>
                                    <span :class ="[ getSizeByNum(monthPurchaseCost) ? 'normal' : 'mini' ]">{{monthPurchaseCost}}</span>
                                 </el-col>
                               </el-row>


                                <!-- <div class="" >
                                    <div class="chart-title" style="padding-top: 0;">本月采购入库单数量</div>
                                    <div>
                                        <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthGodownEntryCount)}">{{ monthGodownEntryCount }}</div>
                                    </div>
                                </div> -->
                            </el-col>
                            <!-- <el-col :span='8' style="display: flex; flex-direction: column; height: 100%;">
                                <div class="flex-dire-column-wrapper">
                                    <div class="chart-title" style="padding-top: 0;">本月采购成本(元)</div>
                                    <div>
                                        <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthPurchaseCost)}">{{ monthPurchaseCost }}</div>
                                    </div>
                                </div>
                            </el-col> -->
                        </el-row>
                   
                </div>

            

                <div class="item-top">
                    <!-- :class="currentIdx===8 ?'item-top-blue':'item-top'"  @click="handleClick(blockList[8].value)" -->

                    <!-- 供应商 -->
                   <!-- <blockTitle :obj='blockList[8]'></blockTitle> -->
                        <el-row style="height: 100%;">
                            <el-col class="item" :span='12' style="height: 100%;">
                                <div class="left">
                                <img class="title-img" :src="blockList[8].imgPath" alt="" srcset="" style="width:60px; height:60px;">
                                <span class="title">供应链</span>
                                    <!-- <div class="chart-title" style="padding-top: 0;">本月入库数量</div>
                                    <div>
                                        <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthInStorageCount)}">{{ monthInStorageCount }}</div>
                                    </div> -->
                                </div>
                            </el-col>
                            <el-col :span='12' style="height: 100%;">

                            <el-row style="height: 50%;">
                                <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月入库数量</span>
                                    <span class="right-content">{{monthInStorageCount}}</span>
                                </el-col>
                              </el-row>
                              <el-row style="height: 50%;">
                                 <el-col class="right-item" :span='24'>
                                    <span class="right-title">本月出库数量</span>
                                    <span class="right-content">{{monthOutStorageCount}}</span>
                                 </el-col>
                              </el-row>

                           
                                <!-- <div>
                                    <div class="chart-title" style="padding-top: 0;">本月出库数量</div>
                                    <div>
                                        <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthOutStorageCount)}">{{ monthOutStorageCount }}</div>
                                    </div>
                                </div> -->
                            </el-col>
                            <!-- <el-col :span='8' style="display: flex; flex-direction: column; height: 100%;">
                                <div class="flex-dire-column-wrapper">
                                    <div class="chart-title" style="padding-top: 0;">本月库存数量</div>
                                    <div>
                                        <div class="text-content num" :style="{fontSize: getFontsizeByNum(monthStorageCount)}">{{ monthStorageCount }}</div>
                                    </div>
                                </div>
                            </el-col> -->
                        </el-row>

                </div>    

             </el-col>
          
        </el-row>

         <el-row :span=24>
               <el-col :span="24">
                <div  style="background-color: white; margin-left:10px; margin-right:10px; border-radius: 10px; height:500px;">
                    <component :is='blockList[currentIdx].value' :obj='blockList[currentIdx]'></component>
                </div>
               </el-col>
         </el-row>

        </el-col>

    <el-col :span=5 style="height:100%;">

         <div style="height:833px; background-color: white; margin-top:10px; margin-right:10px; border-radius: 10px;">
            

            <div style="display:flex; padding-top:19px;">
                <span style="margin-left:10px; width: 5px; height: 24px; background: #3D73DD;"></span>
                <span style="width:90%; padding-top:3px;  padding-left:20px; font-size:16px; font-weight:bold; background: rgba(61,115,221,0.32); ">读书分享</span>
            </div>

            <div style="display:flex; justify-content:flex-end; cursor:pointer; margin-right:20px; margin-top:10px">
                <span @click="handleShareDialog" style="color:#3D73DD;">查看未分享人员</span>
            </div>
    

           <div>
                <noData v-if="!hasDataOfPie(pieEchartOption3.series)"></noData>
                <app-charts-basic v-else :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption3'></app-charts-basic>
           </div>

           

             <el-row style="display:flex;">
                <el-row :span="2">
                    <div style="width: 5px; height: 42px; background: #E9EAEF; margin-left:10px;"></div>
                </el-row>
               
                <el-col :span="22">
                  <span class="right-title" style="margin-left:10px;">本周学习分享</span>  
                  <div class="right-content" style="margin-left:20px;">{{ trainsName | emptyFilter }} </div>  
                </el-col>
             </el-row>

             <div style="display:flex; padding-top:19px;">
                <span style="margin-left:10px; width: 5px; height: 24px; background: #3D73DD;"></span>
                <span style="width:90%; padding-top:3px;  padding-left:20px; font-size:16px; font-weight:bold; background: rgba(61,115,221,0.32); ">日报提交</span>
            </div>

            <div style="display:flex; justify-content:flex-end; cursor:pointer; margin-right:20px; margin-top:10px">
                <span @click="handleSubmittedDialog" style="color:#3D73DD;">查看未提交人员</span>
            </div>
    
            <div>
                 <noData v-if="!hasDataOfPie(pieEchartOption4.series)"></noData>
                 <app-charts-basic v-else :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption4'></app-charts-basic>
            </div>


            <div style="display:flex; padding-top:19px;">
                <span style="margin-left:10px; width: 5px; height: 24px; background: #3D73DD;"></span>
                <span style="width:90%; padding-top:3px;  padding-left:20px; font-size:16px; font-weight:bold; background: rgba(61,115,221,0.32); ">组织过程资产</span>
            </div>
        
              <el-row style="margin-top:20px;">
                    <el-col :span="8" class="daily-item">
                        <span class="daily-title">知识库</span>
                        <span class="daily-content">{{dailyData.KnowledgeCount}}</span>
                    </el-col>
                    <el-col :span="8" class="daily-item">
                        <span class="daily-title">资质荣誉</span>
                        <span class="daily-content">{{dailyData.EnterpriseQualificationCount}}</span>
                    </el-col>
                    <el-col :span="8" class="daily-item">
                        <span class="daily-title">商标注册证</span>
                        <span class="daily-content">{{dailyData.TrademarkLicensenCount}}</span>
                    </el-col>
              </el-row>

                <el-row style="margin-top:10px;">
                    <el-col :span="8" class="daily-item">
                        <span class="daily-title">专利及软件著作</span>
                        <span class="daily-content">{{dailyData.PatentCount}}</span>
                    </el-col>
                    <el-col :span="8" class="daily-item">
                        <span class="daily-title">型式试验证书</span>
                        <span class="daily-content">{{dailyData.TestCertificatenCount}}</span>
                    </el-col>
                </el-row>

            <!-- <blockTitle :obj='blockList[1]' @click="handleClick"></blockTitle>
            <el-row>
                 <el-col :span='12' style="display: flex; flex-direction: column; height: 100%;">
                     <div class="flex-dire-column-wrapper">
                         <div class="chart-title">本周学习分享</div>
                             <div>
                                 <div class="text-content omit">{{ trainsName | emptyFilter }}</div>
                         </div>
                     </div>
                  </el-col>
                <el-col :span='12'>
                     <div class="flex-dire-column-wrapper">
                         <div class="chart-title">分享情况</div>
                            <div>
                              <noData v-if="!hasDataOfPie(pieEchartOption3.series)"></noData>
                              <app-charts-basic v-else :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption3'></app-charts-basic>
                         </div>
                    </div>
                </el-col>
            </el-row> -->
         </div>

    </el-col>

    </el-row>

     <shareDialog
      :dialogStatus="shareDialogStatus"
      :dialogFormVisible="handleShareDialogVisible"
      @closeDialog="handleShareCloseDialog"
    ></shareDialog>

    <submittedDialog
      :dialogStatus="submittedDialogStatus"
      :dialogFormVisible="handleSubmittedDialogVisible"
      @closeDialog="handleSubmittedCloseDialog"
    ></submittedDialog>

    </div>
</template>

<script>
import * as odc from "@/api/operatingDataCenter";
import blockTitle from './blockTitle'
import noData from "@/views/common/components/noData";
import { pieEchartOptionTemp, colors } from "./vars";
import mixins from './mixins'
import { mapGetters } from "vuex";
import shareDialog from './pages/shareDialog'
import submittedDialog from './pages/submittedDialog'

export default {
    name: 'operation-data',
    mixins: [mixins],
    components: {
        submittedDialog,
        shareDialog,
        blockTitle,
        noData,
        gongzuojihua: () => import('./pages/gongzuojihua'),
        richangyunying: () => import('./pages/richangyunying'),
        shouhou: () => import('./pages/shouhou'),
        yanfa: () => import('./pages/yanfa'),
        shengchan: () => import('./pages/shengchan'),
        renshi: () => import('./pages/renshi'),
        xiaoshou: () => import('./pages/xiaoshou'),
    },
    computed: {
        ...mapGetters(["sidebar"]),
        blockList() {
            let list = [
                {value: 'gongzuojihua', title: '工作计划', imgPath: require('../../../assets/images/gongzuojihua.png')},
                {value: 'richangyunying', title: '日常运营', imgPath: require('../../../assets/images/richangyunying.png')},
                {value: 'shengchan', title: '生产', imgPath: require('../../../assets/images/shengchan.png')},
                {value: 'xiaoshou', title: '销售', imgPath: require('../../../assets/images/xiaoshou.png')},
                {value: 'yanfa', title: '研发', imgPath: require('../../../assets/images/yanfa.png')},
                {value: 'shouhou', title: '售后', imgPath: require('../../../assets/images/shouhou.png')},
                {value: 'renshi', title: '人事', imgPath: require('../../../assets/images/renshi.png')},
                {value: 'caigou', title: '采购', imgPath: require('../../../assets/images/caigou.png')},
                {value: 'gongyinglian', title: '供应链', imgPath: require('../../../assets/images/gongyinglian.png')},
            ]
            return list
        },
    },
    created() {
        // 不需要自动收起“菜单”
        // if(this.sidebar.opened){
        //     this.$store.dispatch("ToggleSideBar");
        // }
    },
    mounted() {
        let that = this
        //工作计划
        that.getWorkPlanChart()
        //日常运营
        that.getDailyOperationChart()
        //生产
        that.getImplementChart()
        //研发
        that.getResearchChart()
        //售后
        that.getMaintenanceChart()
        //人事
        that.getPersonnelChart()
        //销售
        that.getCustomers()
        //采购
        that.getPurchaseChart()
        //供应链
        that.getSupplyChain()
        //日常运营详情
        that.getDailyOperationDetailsChart()
        //日报提交
        that.getUnsubmit()
    },
    data() {
        return {
            shareDialogStatus: 'create',
            handleShareDialogVisible: false,

            submittedDialogStatus: 'create',
            handleSubmittedDialogVisible:false,
            dailyData:{},
            chartsHeight: '200px',
            currentIdx: 0,
            
            /** 工作计划 */
            pieEchartOption1: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption2: JSON.parse(JSON.stringify(pieEchartOptionTemp)),

            submitted:0,
            notSubmitted:0,
            
            /** 日常运营 */
            trainsName: '',
            pieEchartOption3: JSON.parse(JSON.stringify(pieEchartOptionTemp)),

            /** 生产 */
            implementCount: 0,
            progressSum: 0,

            /** 研发 */
            productCount: 0,
            projectCount: 0,

            /** 售后 */
            monthCount: 1,
            monthRepairFee: 0,

            /** 人事 */
            monthLeave: 0,
            monthOvertimeDays: 0,
            monthTardiness: 0,

            /** 销售 */
            monthCreated: 0,
            monthCustomerVisit: 0,


            /** 采购 */
            monthPurchaseOrderCount: 0,//本月采购单数量
            monthGodownEntryCount: 0,//本月采购入库单数量
            monthPurchaseCost: 0,//本月采购成本（元）

            /** 供应链 */
            monthInStorageCount: 0,//本月入库数量
            monthOutStorageCount: 0,//本月出库数量
            monthStorageCount: 0,//本月库存数量


             /** 日报提交 */
            pieEchartOption4: JSON.parse(JSON.stringify(pieEchartOptionTemp)),


        }
    },
    methods: {
         getSizeByNum(num) {
            num += ''
            var counta = num.length;
            return counta <= 8 ? true : false
            // if(counta <= 8){
            //     return '24px';
            // }else{
            //     return '16px';
            // }

        },
        getWorkPlanChart() {
            let that = this
            odc.getWorkPlanChart({}).then(res => {
                let targetOption1 = {}
                let targetOption2 = {}
                if(res) {
                    let monthDatas = res.MonthData
                    let weekDatas = res.WeekData

                    weekDatas.map(s => {  
                    if("已提交" === s.Label){
                          this.submitted = s.Value
                        }else{
                           this.notSubmitted = s.Value
                     }
                                    
                     })
                    // targetOption1 = {
                    //     legend: {
                    //         data: monthDatas.map(s => s.Label)
                    //     },
                    //     series: [{
                    //         data: monthDatas.map(s => {
                    //             return {
                    //                 value: s.Value === 0 ? null : s.Value,
                    //                 name: s.Label
                    //             }
                    //         })
                    //     }],
                    //     color: colors
                    // }

                    // targetOption2 = {
                    //     legend: {
                    //         data: weekDatas.map(s => s.Label)
                    //     },
                    //     series: [{
                    //         data: weekDatas.map(s => {
                    //             return {
                    //                 value: s.Value === 0 ? null : s.Value,
                    //                 name: s.Label
                    //             }
                    //         })
                    //     }],
                    //     color: colors
                    // }
                }
                // that.pieEchartOption1 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)
                // that.pieEchartOption2 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption2)
            })
        },
        getDailyOperationChart() {
            let that = this
            odc.getDailyOperationChart({}).then(res => {

                let targetOption1 = {}
                if(res) {
                    that.trainsName = res.TrainsName

                    let chartDatas = res.ChartData
                    targetOption1 = {
                       
                        legend: {
                             orient: 'vertical',
                             left: '60%',
                             top: 'middle',
                             data: chartDatas.map(s => s.Label)
                        },
                        series: [{
                            center: ['35%', '50%'],
                            data: chartDatas.map(s => {
                                return {
                                    value: s.Value === 0 ? null : s.Value,
                                    name: s.Label
                                }
                            })
                        }],
                        color: colors
                    }

                }
                that.pieEchartOption3 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)
            })
        },
        getImplementChart() {
            odc.getImplementChart({}).then(res => {
                this.implementCount = res.ImplementCount
                this.progressSum = res.ProgressSum
            })
        },
        getResearchChart() {
            odc.getResearchChart({}).then(res => {
                this.productCount = res.ProductCount
                this.projectCount = res.ProjectCount
            })
        },
        getMaintenanceChart() {
            odc.getMaintenanceChart({}).then(res => {
                this.monthCount = res.MonthCount
                this.monthRepairFee = res.MonthRepairFee

            })
        },
        getPersonnelChart() {
            odc.getPersonnelChart({}).then(res => {
                this.monthLeave = res.MonthLeave //请假
                this.monthOvertimeDays = res.MonthOvertimeDays //加班
                this.monthTardiness = res.MonthTardiness //迟到
            })
        },
        getCustomers() {
            odc.getCustomers({}).then(res => {
                this.monthCreated = res.MonthCreated
                this.monthCustomerVisit = res.MonthCustomerVisit
            })
        },
        getPurchaseChart() {
            odc.getPurchaseChart({}).then(res => {
                this.monthPurchaseOrderCount = res.MonthPurchaseOrderCount
                this.monthGodownEntryCount = res.MonthGodownEntryCount
                this.monthPurchaseCost = res.MonthPurchaseCost
            })
        },
        getSupplyChain() {
            odc.getSupplyChain({}).then(res => {
                this.monthInStorageCount = res.MonthInStorageCount
                this.monthOutStorageCount = res.MonthOutStorageCount
                this.monthStorageCount = res.MonthStorageCount
            })
        },
        handleClick(val) {
            if(val) {
                this.currentIdx = this.blockList.findIndex(s => s.value == val)
            }
        },

        getDailyOperationDetailsChart() {
           
            odc.getDailyOperationDetailsChart({}).then(res => {
               
                this.dailyData = res;

            }).catch(err => {
              
            })

         },


        getUnsubmit() {
            let that = this
            odc.getUnsubmitDaily({}).then(res => {
                let targetOption1 = {}
                if(res) {
                    let chartDatas = res
                    targetOption1 = {
                       
                        legend: {
                             orient: 'vertical',
                             left: '60%',
                             top: 'middle',
                             data: chartDatas.map(s => s.Label)
                        },
                        series: [{
                            center: ['35%', '50%'],
                            data: chartDatas.map(s => {
                                return {
                                    value: s.Value === 0 ? null : s.Value,
                                    name: s.Label
                                }
                            })
                        }],
                        color: colors
                    }

                }
                that.pieEchartOption4 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)
            })
        },

    //销售产品类别
    handleShareDialog() {
      this.handleShareDialogVisible = true
    },
    handleShareCloseDialog() {
      this.handleShareDialogVisible = false
    },

    handleSubmittedDialog(){
      this.handleSubmittedDialogVisible = true
    },

     handleSubmittedCloseDialog(){
       this.handleSubmittedDialogVisible = false
    },

    }
}
</script>

<style lang="scss" scoped>
@import './common.scss';
// .app-container{
//    // min-width: 1700px;
//    // overflow: auto;
//    // background-color: #E9EAEF;
// }

// .flex-dire-column-wrapper{
//     height: 100%;
//     display: flex;
//     flex-direction: column;
//     min-height: 230px;
// }

// .el-col{
//     height: 50%;
// }

// .flex-1{
//     box-sizing: border-box;
//     background-color: white;
//     height: 160px;
//     margin: 10px;
//     border-radius: 10px;
//     padding: 5px;
   
//     display: flex;

//     flex-direction: column;
//     >div:last-child{
//         flex: 1;
//     }
// }

.item-top{
    background-color: white;
    height: 157px;
    margin: 10px;
    border-radius: 10px;
}

.item-top-blue{
    background-color: white;
    height: 157px;
    margin: 10px;
    border-radius: 10px;
    border: 1px solid #3D73DD;
}


// .border{
//     border: 1px solid #dcdfe6;
// }


// .flex-2{
//     flex-shrink: 0;
//     flex-grow: 1;
//     flex: 2;
//     display: flex;
// }


// .text-content{
//     text-align: center; flex: 1; font-weight: bold; display: flex; justify-content: center; align-items: center; word-break: break-all; white-space: normal; word-break: break-all;
// }


.left{
     height:80%;
     border-right:1px solid #E9EAEF; 
     display:flex; 
     flex-direction: column;
     justify-content: center;
     align-items: center;
}

.item{
     display:flex; 
     flex-direction: column;
     justify-content: center;
}

.title{
    font-size:16px; 
    margin-top:29px;
    font-weight:bold;
}

.right-title{
    font-size:12px; 
    color:#A0A1A3;
    margin-top: 19px;
}

.right-content{
    font-size:24px; 
    color:$text-primary;
    font-weight:bold;
    margin-top:2px;
}


.right-item{
 display:flex; 
 flex-direction: column;
 justify-content:flex-start;
 height:100%;
 width: 90%;
 margin-left: 30px;
}


.daily-item{
  border-radius: 10px;
  height: 60px;
  width: 100px;
  border: 1px solid #DCDFE6;
  margin-left: 10px;
  display:flex; 
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.daily-title{
    font-size:12px; 
    color: #A0A1A3;
}

.daily-content{
    font-size:24px; 
    color: $text-primary;
    font-weight:bold;
}

.normal{
    color:$text-primary; font-weight: bold; font-size:24px; margin-top:3px;
}

.mini{
    color:$text-primary; font-weight: bold; font-size:16px; margin-top:10px;
}

// .items-group{
//     >div{
//         cursor: pointer;
//     }
// }
</style>