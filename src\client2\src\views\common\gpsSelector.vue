<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      ref="listSelector"
      paginationLayout='total,prev,pager,next,jumper'
    >
    
      <template slot="EndTime" slot-scope="scope">
        <span v-if="!scope.row.EndTime">
          {{ scope.row.EndTime | emptyFilter }}
        </span>
        <span v-else :style="{color: scope.row.EndTimeExpire ? '#D9001B' : '#027DB4'}">
          {{ scope.row.EndTime | dateFilter('YYYY-MM-DD') }}
        </span>
      </template>
      <template slot="Status" slot-scope="scope">
        <span :style="{color: scope.row.Status == '在线' ? '#70B603' : scope.row.Status == '离线' ? '#D9001B' : '#393133'}">
          {{ scope.row.Status | emptyFilter }}
        </span>
      </template>
      
      <!-- <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='OrderName'>
            <el-input style="width: 100%;" v-model="listQueryParams.OrderName" placeholder=""></el-input>
          </template>
          <template slot='EmployeeName'>
            <el-input style="width: 100%;" v-model="listQueryParams.EmployeeName" placeholder=""></el-input>
          </template>

          <template slot='ContractNumber'>
            <el-input style="width: 100%;" v-model="listQueryParams.ContractNumber" placeholder=""></el-input>
          </template>
        </app-table-form>
      </template> -->
    </listSelector>
  </div>
</template>

<script>
// import { vars } from "../common/vars"
// import * as afterService from "@/api/afterSalesMgmt/afterService";
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
// import normarEmpSelector from "../afterSalesMgmt/common/normarEmpSelector";
import { vars } from "../salesMgmt/common/vars";
// import * as orderVars from './salesMgmt/common/vars'
// import * as maintenCenterVars from './maintenCenter/common/vars'
import noData from "@/views/common/components/noData";
export default {
  name: "gps-selector",
  components: {
    listSelector,
    noData,
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },

  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },
  watch: {
    
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList(val) {
      if (val && val.length > 0) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
      }else{
        this.listSelectorCheckedData = [];
      }
    },
  },
  data() {
    return {
      listQueryParams: {
        // ContractNumber: '',
        // OrderName: '',
        // // EmployeeName: '',
        // ReceivedPayment: '',
        // IsSelectList: true
      },
      tableSearchItems: [
        // { prop: "ContractNumber", label: "合同编号" },
        // { prop: "OrderName", label: "订单名称" },
        // { prop: "ReceivedPayment", label: "是否结算" },
        
        // { prop: "EmployeeName", label: "业务员" },
        // { prop: "Employee", label: "负责人" },
      ],
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/CarCoordinatorManagement/GetGPSDevice",
      listSelectorMultiple: this.multiple,
      listSelectorCondition: this.condition ? this.condition : {},
      listSelectorTitle: "绑定设备",
      listSelectorTopMessage: "",
      listSelectorKeyName: "GPSImei",
      listSelectorColumnData: [
        {
          attr: { prop: "DeviceName", label: "设备名称", showOverflowTooltip: true },
          // slot: true
        },
        {
          attr: { prop: "EndTime", label: "用户到期" },
          slot: true
        },
        {
          attr: { prop: "Status", label: "GPS状态" },
          slot: true,
        },
        {
          attr: { prop: "BindingState", label: "绑定状态" },
          
        },
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  methods: {
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      // this.listQueryParams.OrderName = ''
      // this.listQueryParams.EmployeeName = ''
      // this.listQueryParams.ReceivedPayment = ''
      // this.listQueryParams.ContractNumber = ''
      // this.listQuery.EmployeeId = ''
      // this.listQuery.Employee = []
      this.handleFilter()
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let list =
        data.map((s) => {
          s.Code = s.OrderNumber;
          return s;
        }) || [];
      this.$emit("changed", JSON.parse(JSON.stringify(list)));
      this.listSelectorCloseDialog();
    },
    // handleChangeUsers(users) {
    //   if (users && users.length > 0) {
    //     this.listQuery.Employee = [users[0]];
    //     this.listQuery.EmployeeId = users[0].EmployeeId;
    //   } else {
    //     this.listQuery.Employee = [];
    //     this.listQuery.EmployeeId = '';
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>

.imp-item-wrapper{
  display: flex;
  max-height: 200px;
  overflow-y: auto;
  .imp-number{
    width: 120px;
  }
  .imp-name{
    width: 180px;
    padding: 0 10px;
  }
  .imp-number, .imp-name{
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
  // .imp-prog{
  //   width: 160px;
  // }
}

</style>

<style scoped>
.status-1,.status-4,.status-7 {
    background-color: red;
}

.status-2,.status-5,.status-8 {
    background-color: blue;
}

.status-3,.status-6,.status-9 {
    background-color: orange;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

.status-field-1 {
  color: red;
}

.status-field-2 {
  color: #f09c69;
}

.status-field-3 {
  color: #9da0ad;
}

.status-field-4 {
  color: #4239fe;
}


</style>