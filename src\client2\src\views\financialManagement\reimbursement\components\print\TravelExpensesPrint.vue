<!-- 差旅费报销打印组件 -->
<template>
  <div style="display: none">
    <div ref="printContent" class="print_container">
      <div class="print_title">差旅费报销单</div>
      <div class="print_top">
        <span>
          公司名称：
          <span class="f_bold">{{ getObjData(formData, "KingdeeDepartmentName") }}</span>
        </span>
        <span>填报日期：{{ getObjData(formData, "FBillDate") | dateFilter("YYYY/MM/DD") }}</span>
        <span>单据编号：{{ getObjData(formData, "FBillNo") }}</span>
      </div>
      <table class="print_table">
        <tr>
          <td style="width: 10%">报销人</td>
          <td style="width: 10%" class="f_bold">{{ getSubmitEmployeeName }}</td>
          <td style="width: 10%">职别</td>
          <td>{{ getExpenseRankType }}</td>
          <td style="width: 12%">部门</td>
          <td colspan="2">{{ getObjData(formData, "DepartmentName") }}</td>
        </tr>
        <tr>
          <td>领款人</td>
          <td class="f_bold">{{ getPayeeEmployeeName }}</td>
          <td>研发项目</td>
          <td colspan="4">
            <div class="list_item" v-for="(item, index) in projectNameList" :key="index">
              {{ item }}
            </div>
          </td>
        </tr>
        <tr>
          <td>出差事由</td>
          <td colspan="3" class="t_left">{{ getObjData(formData, "BusinessTripReasons") }}</td>
          <td>关联出差申请</td>
          <td colspan="2" class="t_left">{{ evectionReqText }}</td>
        </tr>
        <!-- 报销明细 -->
        <tr>
          <td colspan="7" style="padding: 0">
            <table class="table_sub_1">
              <tr>
                <td colspan="2" style="width: 15%">日期</td>
                <td rowspan="2" style="width: 15%">起讫地点</td>
                <td rowspan="2" style="width: 5%">天数</td>
                <td rowspan="2" v-for="item in TRAVEL_TYPE" :key="item.value">
                  {{ item.label }}
                </td>
                <td rowspan="2">小计</td>
              </tr>
              <tr>
                <td>起</td>
                <td>止</td>
              </tr>
              <tr
                v-for="(item, index) in getObjData(formData, 'TravelExpenseDetailList', [])"
                :key="index"
              >
                <td>{{ item.StartDate | dateFilter("YY/MM/DD") }}</td>
                <td>{{ item.EndDate | dateFilter("YY/MM/DD") }}</td>
                <td>{{ item.TravelRouteStart }} - {{ item.TravelRouteEnd }}</td>
                <td>{{ item.Days }}</td>
                <td v-for="t in TRAVEL_TYPE" :key="t.value" class="t_right">
                  {{ formatThousands(getAmount(item.Id, t.value)) }}
                </td>
                <!-- 行小计 -->
                <td class="t_right">{{ formatThousands(subtotalTotalAmount(item.Id)) }}</td>
              </tr>
              <!-- 占位 -->
              <tr
                v-for="i in Math.max(
                  0,
                  3 - (getObjData(formData, 'TravelExpenseDetailList', []).length || 0)
                )"
                :key="`empty-${i}`"
              >
                <td v-for="t in 11" :key="t"></td>
              </tr>
              <tr>
                <td colspan="3">合计</td>
                <td>{{ totalDaysMap }}</td>
                <td v-for="t in TRAVEL_TYPE" :key="t.value" class="t_right">
                  {{ formatThousands(rowTotalAmountMap[t.value].totalAmount) }}
                </td>
                <!-- 合计小计 -->
                <td class="t_right f_bold">{{ formatThousands(getObjData(formData, "Total")) }}</td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="2">合计金额(大写)</td>
          <td colspan="2" class="t_left">{{ convertToChinese(getObjData(formData, "Total")) }}</td>
          <td>预支金额</td>
          <td colspan="2" class="t_right">
            {{ formatThousands(getObjData(formData, "Advance")) }}
          </td>
        </tr>
        <tr>
          <td colspan="2">费用说明</td>
          <td colspan="5" class="t_left">{{ getObjData(formData, "ExpenseDescription") }}</td>
        </tr>
        <tr>
          <td class="footer_row" colspan="7">
            <span v-for="(item, index) in flowList" :key="index">
              {{ item.role }}【{{ item.name }}】{{ index === flowList.length - 1 ? "" : "→" }}
            </span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import printMixins from "./printMixins.js";
import { EXPENSE_RANK_TYPE, TRAVEL_TYPE } from "@/views/financialManagement/reimbursement/vars.js";
import Decimal from "decimal.js";

export default {
  name: "TravelExpensesPrint",
  data() {
    return {
      // 必须得有混入方法使用动态包裹对象key
      parcelKey: "TravelExpenseObj",
      EXPENSE_RANK_TYPE,
      TRAVEL_TYPE,
      /**
        金额映射和TRAVEL_TYPE枚举对应
        {
          TravelExpenseDetailList.Id:{
            1: {amount:0,label:"机票费",value:1},
            2: {amount:0,label:"车船费",value:2},
            3: {amount:0,label:"交通费",value:3},
            4: {amount:0,label:"住宿费",value:4},
            5: {amount:0,label:"出差补助",value:5},
            6: {amount:0,label:"其他",value:6}
          }
        }
       */
      amountMap: {},
    };
  },
  mixins: [printMixins],
  watch: {
    formData: {
      handler(val) {
        this.computeAmountMap();
      },
      deep: true,
    },
  },
  computed: {
    getExpenseRankType() {
      return (
        this.EXPENSE_RANK_TYPE.find(
          item => item.value === this.getObjData(this.formData, "ExpenseRankType")
        )?.label || ""
      );
    },
    // 出差申请
    evectionReqText() {
      const list = this.getObjData(this.formData, "RelateHRpersonalInfoList", []);
      return list.map(t => t?.ApprovalCode || "").join(",");
    },
    /**
     * 费用明细小计
     * @param {String} TravelExpenseDetailList.Id
     */
    subtotalTotalAmount() {
      return id => {
        if (!this.amountMap?.[id]) return 0;

        const list = Object.values(this.amountMap[id]);
        if (!list.length) return 0;

        const total = list.reduce((acc, t) => {
          return Decimal(acc).plus(t.amount).toNumber();
        }, 0);

        return total;
      };
    },
    /**
     * 天数合计
     * @param {Object} item TravelExpenseDetailList
     */
    totalDaysMap() {
      const list = this.getObjData(this.formData, "TravelExpenseDetailList", []);
      if (!list.length) return 0;

      return list.reduce((acc, t) => {
        return Decimal(acc).plus(t.Days).toNumber();
      }, 0);
    },
    /**
     * 费用明细行合计枚举
     * key:对应TRAVEL_TYPE枚举的value
     */
    rowTotalAmountMap() {
      const map = this.enumToObj({ totalAmount: 0 });
      const list = []; // 所有明细金额对象数组
      for (const key in this.amountMap) {
        list.push(...Object.values(this.amountMap[key]));
      }
      if (!list.length) return map;

      list.forEach(t => {
        if (map[t.value] !== undefined) {
          map[t.value].totalAmount = Decimal(map[t.value].totalAmount).plus(t.amount).toNumber();
        }
      });
      return map;
    },
  },
  methods: {
    // 金额枚举数据处理
    computeAmountMap() {
      this.amountMap = {};
      const list = this.getObjData(this.formData, "TravelExpenseDetailList", []);
      list.forEach(t => {
        this.amountMap[t.Id] = this.enumToObj({ amount: 0 });
        t.TravelExpenseDetailCosts.forEach(s => {
          const item = this.amountMap[t.Id][s.TravelType];
          if (item !== undefined) {
            item.amount = Decimal(item.amount).plus(s.Cost).toNumber();
          }
        });
      });
    },
    // 把TRAVEL_TYPE数组枚举处理成对象
    enumToObj(config = {}) {
      const obj = this.$_.cloneDeep(TRAVEL_TYPE).reduce((acc, t) => {
        acc[t.value] = { ...t, ...config };
        return acc;
      }, {});
      return obj;
    },
    /**
     * 根据id和枚举获取对应的金额
     * @param {string} id TravelExpenseDetailList.Id
     * @param {number} travelType
     * @returns
     */
    getAmount(id, travelType) {
      return this.amountMap?.[id]?.[travelType]?.amount || 0;
    },
  },
};
</script>

<style lang="scss">
@import "./printStyle.scss";
.table_sub_1 {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  text-align: center;
  font-size: $font-size;

  tr:first-of-type {
    td {
      border-top: none;
    }
  }
  tr:last-of-type {
    td {
      border-bottom: none;
    }
  }
  tr {
    td:first-of-type {
      border-left: none;
    }
    td:last-of-type {
      border-right: none;
    }
  }
}
</style>
