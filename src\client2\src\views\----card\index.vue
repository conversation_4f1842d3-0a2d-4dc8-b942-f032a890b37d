<template>
<div class="wrappers">
    <!-- <div class="kuai"> -->
    
    <div class="det-panel" v-loading="formLoading">
        <!-- <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth"> -->
            <div class="panel-title2">
                <svg-icon icon-class="个人名片" style="font-size: 36px; color: #dcdfe6; padding-right: 10px;"></svg-icon>名片管理
            </div>
            <div class="basic-info">
                <div class="avator-wrapper">
                    <AppImgCutter :val='{id: "xxx", path: formData.AvatarPath || defavatar}' :disabled='true'></AppImgCutter>
                    <!-- <app-upload-file
                :max="1"
                :fileSize="1024 * 1024 * 2"
                :value="fileList"
                :preview="true"
                :disabled="true"
                ></app-upload-file>-->
                </div>
                <div class="info-content">
                    <el-row>
                        <el-col :span="6">
                            <div class="cus-form-item">
                                <div class="cus-label">姓名</div>
                                <div class="cus-text">{{formData.Name}}</div>
                            </div>
                            <div class="cus-form-item">
                                <div class="cus-label">
                                    工号
                                </div>
                                <div class="cus-text">
                                    {{formData.Number}}
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="cus-form-item">
                                <div class="cus-label">
                                    性别
                                </div>
                                <div class="cus-text">
                                    {{formData.Sex == 1 ? '男':'女'}}
                                </div>
                            </div>
                            <div class="cus-form-item">
                                <div class="cus-label">
                                    所属部门
                                </div>
                                <div class="cus-text">
                                    {{ formData.DepartmentName }}
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="cus-form-item">
                                <div class="cus-label">
                                    手机
                                </div>
                                <div class="cus-text">
                                    {{formData.Mobile}}
                                </div>
                            </div>
                            <div class="cus-form-item">
                                <div class="cus-label">
                                    职位
                                </div>
                                <div class="cus-text">
                                    {{ formData.JobName }}
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <div class="cus-form-item">
                                <div class="cus-label">
                                    企业官网
                                </div>
                                <div class="cus-text">
                                    {{ formData.Website }}
                                    <el-button type="text" @click="btnEditWebsite()">修改</el-button>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24">
                            <div class="cus-form-item" style="height: auto;">
                                <div class="cus-label">
                                    名片二维码
                                </div>
                                <div class="cus-text">
                                    <img :src="code" width="192" />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <!-- <el-row>
                <el-col :span="24">
                    <el-form-item label="名片二维码" prop="Status">
                        <img :src="code" width="192" />
                    </el-form-item>
                </el-col>
            </el-row> -->

            <el-row>
                <el-col :span="24">
                    <el-form ref="formModel" :rules="rules" :model="formModel" label-position="right" :label-width="labelWidth">
                        <div class="btns-wrapper">
                            <el-button type="primary" @click="handleAdd">添加新内容</el-button>
                            <el-button @click="createData" type="primary">保存</el-button>
                        </div>
                        <div class="wrapper" style="padding-top: 10px;">
                            <el-card class="box-card" v-for="(item, idx) in formModel.list" :key="idx">
                                <div slot="header">
                                    <div class="item-title-wrapper">
                                        <div class="item-title">标题{{ idx + 1 }}</div>
                                        <div class="item-btns">
                                            <!-- <i v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></i> -->
                                            <svg-icon icon-class="arrow-circle-up" v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></svg-icon>
                                            <svg-icon icon-class="arrow-circle-down" v-show="idx < formModel.list.length - 1" title="下移" @click="move('down', idx)"></svg-icon>
                                            <!-- <i v-show="idx < formModel.list.length - 1" class="el-icon-bottom" title="下移" @click="move('down', idx)"></i> -->
                                            <i class="el-icon-delete-solid" style="font-size: 20px; color: red; margin-left: 4px;" title="删除" @click="handleRemove(item)"></i>
                                        </div>
                                    </div>
                                </div>
                                <el-row class="item-content">
                                    <el-col :span="24" class="cus-textarea-wrapper">
                                        <el-form-item label="标题" :prop="'list.' + idx + '.Title'" :rules="{required: true, message: '标题不能为空', trigger: 'change'}">
                                            <el-input maxlength="50" v-model="formModel.list[idx].Title"></el-input>
                                        </el-form-item>
                                        <el-form-item label="内容" :prop="'list.' + idx + '.Content'" :rules="{required: true, message: '内容不能为空', trigger: 'change'}">
                                            <el-input maxlength="10000" type="textarea" v-model="formModel.list[idx].Content"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-card>
                        </div>
                    </el-form>
                </el-col>
            </el-row>
        <!-- </el-form> -->
    </div>
    <!-- </div> -->
</div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as systemEmployee from "@/api/personnelManagement/systemEmployee";
import {
    getUserInfo
} from "@/utils/auth";
//import QRCode from "qrcode";
// import { regs } from "@/utils/regs";
export default {
    name: "card",

    created() {
        this.rules = this.initRules(this.rules);
        this.handleAdd();
        this.getDetail();
        this.getDetaillist();
    },

    data() {
        return {
            rules: {},
            qrcode: "",
            code: "",
            DepartmentList: [],
            JobList: [],
            jobLoading: false,
            fileList: [], //图像信息[{Id: '', Path: ''}]
            formLoading: false,
            formsLoading: false,
            labelWidth: "80px",
            formModel: {
                list: [],
            },
            formData: {
                EmployeesId: "",
                Name: "",
                Sex: 1,
                Number: "",
                Mobile: "",
                DepartmentId: "",
                DepartmentName: "",
                JobId: "",
                JobName: "",
                Email: "",
                SpecialPlane: "",
                WeChat: "",
                Status: 1,
                WorkingState: 1,
                Avatar: "",
                AvatarPath: "",
            },
            defavatar: require("../../assets/images/avatar3.png"),
        };
    },
    mounted() {
        this.getQRCode();
    },
    methods: {
        handleAdd() {
            if (this.formModel.list.length > 4) {
                this.$message.error("已达添加上限");
            } else {
                this.formModel.list.push({
                    Id: null,
                    Name: "",
                    //   ImplementationRegionalId: this.implementationRegionalId,
                    Title: "",
                    Content: "",
                });
            }
        },
        // 根据字符串生成二维码
        setQRCode() {
            // QRCode.toDataURL("baidu.com", { width: 192, margin: 1 }).then(res => {
            //   // toDataURL方法返回一个promise
            //   this.qrcode = res;
            // });
        },
        move(direction, currIdx) {
            if (
                (direction == "up" && currIdx > 0) ||
                (direction == "down" && currIdx < this.formModel.list.length - 1)
            ) {
                let currRow = JSON.parse(JSON.stringify(this.formModel.list[currIdx]));
                let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
                this.formModel.list.splice(currIdx, 1);
                this.formModel.list.splice(targetIdx, 0, currRow);
            }
        },
        handleRemove(row) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                var index = this.formModel.list.indexOf(row);
                if (index !== -1) {
                    this.formModel.list.splice(index, 1);
                }
            });
        },
        getQRCode() {        
            var empid =  getUserInfo().employeeid;
            systemEmployee.createQRCode({id:empid}).then((res) => {
                //alert(res)
                this.code = res;
                // QRCode.toDataURL("baidu.com", { width: 192, margin: 1 }).then(res => {
                //   // toDataURL方法返回一个promise
                //   this.qrcode = res;
                // });
            });
        },

        handleUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.AvatarPath = imgs[0].Path;
                this.formData.Avatar = imgs[0].Id;
            } else {
                this.formData.Avatar = "";
                this.formData.AvatarPath = "";
            }
        },
        btnEditWebsite() {
            this.$prompt("", "企业官网", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    inputPlaceholder: "请输入网址",
                    inputPattern: /^[\s\S]*.*[^\s][\s\S]*$/,
                    inputErrorMessage: "请输入网址",
                    closeOnClickModal: false,
                }).then(({
                    value
                }) => {
                    systemEmployee
                        .editCard({
                            Website: value,
                        })
                        .then((res) => {
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000,
                            });
                        });
                    this.formData.Website = value;
                })
                .catch(() => {});
        },
        btnEditBusiness() {
            //  this.$alert('<textarea rows="5" cols="20" id="txtBusiness" style="resize:none;margin: 0px; width: 387px; height: 147px;"></textarea>', '主营业务', {
            //       dangerouslyUseHTMLString: true
            //     });

            this.$prompt("", "主营业务", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    inputPlaceholder: "请输入主营业务",
                    inputType: "textarea",
                    inputPattern: /^[\s\S]*.*[^\s][\s\S]*$/,
                    inputErrorMessage: "请输入主营业务",
                })
                .then(({
                    value
                }) => {
                    systemEmployee
                        .editCard({
                            Business: value,
                        })
                        .then((res) => {
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000,
                            });
                        });
                    this.formData.Business = value;
                })
                .catch(() => {});
        },
        getDetail() {
            this.formLoading = true;
            systemEmployee.cardDetail().then((res) => {
                var formObj = Object.assign({}, this.formData, res);
                this.fileList = [];
                if (this.formData.AvatarPath) {
                    this.fileList = [{
                        Id: this.formData.Avatar,
                        Path: this.formData.AvatarPath,
                    }, ];
                }

                this.formData = formObj;
                this.formLoading = false;
            });
        },

        getDetaillist() {
            this.formsLoading = true;
            systemEmployee.getDetaillist().then((res) => {
                // var formObj = Object.assign({}, this.formModel.list, res);
                this.formModel.list = res;
                this.formsLoading = false;
            });
        },

        //保存
        createData() {

             this.$refs.formModel.validate(valid => {
                 if(valid) {
                     let postData = JSON.parse(JSON.stringify(this.formModel.list));
                     systemEmployee.editCardBus(postData).then((res) => {
                         this.$notify({
                             title: "提示",
                             message: "保存成功",
                             type: "success",
                             duration: 2000,
                         });
                     });
                 }
             })

            //  this.formData.Website = value;
        },
    },
};
</script>

<style scoped>
.wrappers >>> .el-card:not(:last-child){
    margin-bottom: 10px;
}

.wrappers >>> .el-card__body{
    padding: 10px;
}
</style>

<style lang="scss" scoped>

.wrappers {
    background: #ffffff;
    margin: 10px;
    width: 800px;
    height: 100%;
    background: white;
    margin: 0 auto;
    box-shadow: 0 0 5px 1px rgba(0,0,0,0.1);;
    overflow-y: auto;
}


.panel-title2 {
  font-size: 16px;
  font-weight: 700;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  height: 38px;
  line-height: 38px;
  display: flex;
  align-items: center;
}


.wrapper {
    min-height: 100px;
    padding: 10px;

    // .item {
    //     border: 1px solid #ebeef5;
    //     border-radius: 10px;
    //     margin-bottom: 10px;

    // padding-top: 10px;
    .item-title-wrapper {
        display: flex;
        height: 34px;
        line-height: 34px;
        // margin-bottom: 4px;
        padding: 0 10px;
        // border-bottom: 1px solid #ebeef5;

        .item-title {
            flex: 1;
            margin-bottom: 4px;
        }

        .item-btns{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            svg {
                cursor: pointer;
                font-size: 20px;
                vertical-align: middle;
                margin-left: 4px;
            }
        }
    }

    .item-content {
        padding-right: 10px;
    }
    // }
}


.det-panel{
  .basic-info{
    display: flex;
    padding-top: 10px;
    border-bottom: 1px solid #dcdfe6;
    .avator-wrapper{
      margin-left: 10px;
    }
    .info-content{
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    //   padding: 5px 0;
    }
  }
  .other-info{
    padding: 10px 0;
  }
}

.cus-form-item{
  display: flex;
  height: 28px;
  line-height: 28px;
  .cus-label{
    width: 90px;
    text-align: right;
    font-weight: 900;
    padding-right: 5px;
  }
  .cus-label-st{
    width: 60px;
  }
  .cus-text {
    padding-left: 5px;
    flex: 1;
    font-weight: 500 !important;
  }
}

.btns-wrapper{
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
}
</style>
