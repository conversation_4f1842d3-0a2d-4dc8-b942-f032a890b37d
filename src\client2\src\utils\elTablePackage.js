import { Table } from 'element-ui';
/**
 * element-ui 表格注入插件
 * 列表滚动时  fixed 列 会滑动延时
 * 删除 Table._Ctor 重注入滑动事件效果
 */
delete Table._Ctor
const ElPackageBindEvents = Table.methods.bindEvents
Table.methods = Object.assign(Table.methods,{
    bindEvents() {
        ElPackageBindEvents.call(this)
        this.bodyWrapper.addEventListener('mousewheel', this.handleBodyMousewheel)
    },
    handleBodyMousewheel(event) {
        // fixedWrapper 由于 element 表格的 fixed 列 是浮出的多个表格
        // 左侧/右侧浮动的 fixed 表格 refs 不一样 所以分开取值 避免同时存在左右 fixedWrapper时 找不到dom
        const leftFixedWrapper = this.$refs.fixedWrapper
        const rightFixedWrapper = this.$refs.rightFixedWrapper
        if (leftFixedWrapper) {
            let fixedBodyWrapper = leftFixedWrapper.querySelector('.el-table__fixed-body-wrapper')
            if (fixedBodyWrapper) {
                event.preventDefault()
                // 延时 0.1s 执行表格滚动
                setTimeout(() => {
                    fixedBodyWrapper.scrollBy({ top: event.deltaY })
                    this.$refs.bodyWrapper.scrollBy({ top: event.deltaY })
                }, 100);
            }
        }
        if (rightFixedWrapper) {
            let fixedBodyWrapper = rightFixedWrapper.querySelector('.el-table__fixed-body-wrapper')
            if (fixedBodyWrapper) {
                event.preventDefault()
                // 延时 0.1s 执行表格滚动
                setTimeout(() => {
                    fixedBodyWrapper.scrollBy({ top: event.deltaY })
                    this.$refs.bodyWrapper.scrollBy({ top: event.deltaY })
                }, 100);
            }
        }
    }
})
export default{
    Name: Table.name,
    Table: Table
}