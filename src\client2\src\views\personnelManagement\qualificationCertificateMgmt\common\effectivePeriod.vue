<!--提醒设置-->
<template>
    <!--组件内容区-->
    <app-dialog title="提醒设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
        <template slot="body">
            <el-form
                :rules="formRules"
                ref="formRef"
                :model="formModel"
                label-position="right"
                label-width="100px"
                v-loading='loading'>
                <el-form-item label="提醒开关">
                    <el-switch v-model="formModel.Switch"></el-switch>
                </el-form-item>
                <!-- 提醒开关开启才显示下面的参数 -->
                <template v-if="formModel.Switch">
                    <el-form-item label="有效期" prop="ExpiryDateType">
                        <el-radio-group v-model="formModel.ExpiryDateType">
                            <el-radio :label="1">不足1个月</el-radio>
                            <el-radio :label="2">不足2个月</el-radio>
                            <el-radio :label="3">不足3个月</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="推送给" prop="PushedEmployeeList">
                        <emp-selector
                            class="fl"
                            style="width:430px;"
                            :readonly="false"
                            key="Id"
                            :showType="2"
                            :multiple="true"
                            :beforeConfirm='handleBeforeConfirm'
                            :list="formModel.PushedEmployeeList"
                            @change="handleViewRange"
                        ></emp-selector>
                    </el-form-item>
                    <el-form-item label="发起人" prop="InitiatorEmployeeList">
                        <emp-selector
                            class="fl"
                            style="width:430px;"
                            :readonly="false"
                            key="Id"
                            :showType="2"
                            :multiple="true"
                            :beforeConfirm='handleBeforeConfirm1'
                            :list="formModel.InitiatorEmployeeList"
                            @change="handleViewRange1"
                        ></emp-selector>
                    </el-form-item>
                </template>
            </el-form>
        </template>
        <template slot="footer">
            <app-button :buttonType="2" @click="handleClose"></app-button>
            <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
        </template>
    </app-dialog>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
// 企业资质
import * as enterpriseQualificationApi from '@/api/personnelManagement/enterpriseQualification'

import empSelector from "../../../common/empSelector";
import {getUserInfo} from '@/utils/auth';
export default {
    /**名称 */
    name: "enterprise-qualification-modify-classify",
    /**组件声明 */
    components: {
        empSelector
    },
    /**参数区 */
    props: {
        componentType: {
            type: Number,
            require: true,
            default: 1
        }
    },
    /**数据区 */
    data() {
        return {
            commonApi: null,
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            /**表单模型 */
            formModel: { 
                AlertSettingBusinessType: 1, // 1.资质荣誉 2.商标注册 3.知识产权 4.试验证书
                Switch: false,
                ExpiryDateType: 1,
                PushedEmployeeList: [],
                InitiatorEmployeeList: []
            },
            /**表单规则 */
            formRules: {
                ExpiryDateType: { fieldName: "有效期", rules: [{ required: true }] },
                PushedEmployeeList: { fieldName: "推送人员", rules: [{ required: true }] },
                InitiatorEmployeeList: { fieldName: "发起人", rules: [{ required: true }] },
            }
        };
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {                
                this.formModel = this.$options.data().formModel
                this.formModel.AlertSettingBusinessType = this.componentType
                if (val) {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        handleBeforeConfirm1(users) {
            if(users && users.length > 1) {
                this.$message({
                    message: '添加不可超过1人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 可见范围 选择人员/选择部门
        handleViewRange1(users) {
            if (users && users.length > 0) {
                this.formModel.InitiatorEmployeeList = users;
            } else {
                this.formModel.InitiatorEmployeeList = [];
            }
            this.$refs["formRef"].validateField(`InitiatorEmployeeList`);
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 10) {
                this.$message({
                    message: '添加不可超过10人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 可见范围 选择人员/选择部门
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formModel.PushedEmployeeList = users;
            } else {
                this.formModel.PushedEmployeeList = [];
            }
            this.$refs["formRef"].validateField(`PushedEmployeeList`);
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this, formModel = JSON.parse(JSON.stringify(self.formModel));
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    formModel.PushedEmployeeIdList = formModel.PushedEmployeeList.map(s => s.EmployeeId)
                    formModel.InitiatorEmployeeIdList = formModel.InitiatorEmployeeList.map(s => s.EmployeeId)
                    delete formModel.PushedEmployeeList;
                    delete formModel.InitiatorEmployeeList;
                    // console.log(formModel)
                    self.buttonLoading = true;
                    enterpriseQualificationApi.EditAlertSetting(formModel).then(response => {
                        self.buttonLoading = false;
                        self.handleClose();
                    }).catch(err => {
                        self.buttonLoading = false
                    })
                }
            });
        },
        getDetail() {
            let self = this;
            self.loading = true;
            enterpriseQualificationApi.GetAlertSetting({alertSettingBusinessType:self.componentType}).then(response => {
                self.loading = false;
                self.formModel = Object.assign({}, self.formModel, response)
                if (!response.InitiatorEmployeeList||response.InitiatorEmployeeList.length==0) {
                    let a=getUserInfo();
                    this.formModel.InitiatorEmployeeList=[{
                        EmployeeId:a.employeeid,
                        Name:a.empName,
                        Number: a.empNumber,
                        AreaName: null,
                        Avatar: "",
                        AvatarId: "",
                        DepartmentList: null,
                        HeadImageMediaModel: null,
                        ImageResourceId: null,
                        Mobile: null,
                        UserId: "00000000-0000-0000-0000-000000000000"
                    }];
                }
            }).catch(err => {
                self.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
