<!--分类 添加/编辑-->
<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
      <template slot="body">
        <el-form :rules="formRules" ref="formData" :model="formModel" label-position="right" label-width="120px">
          <el-form-item label="分类名称" prop="Name">
            <el-input maxlength="30" :disabled="!editable" v-model="formModel.Name"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as classify from '@/api/classify'

export default {
  name: "test-paper-classify",
  components: {},
  props: {
    dialogStatus: {
      type: String,
      default: "create"
    },
    node: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      goOn: false,
      buttonLoading: false,
      formModel: { Name: "", Level: 1, BusinessType: 10 },
      formRules: {
        Name: { fieldName: "分类名称", rules: [{ required: true }] }
      }
    };
  },
  computed: {
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加分类";
      } else if (this.dialogStatus == "update") {
        return "编辑分类";
      } else if (this.dialogStatus == "detail") {
        return "分类详情";
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.goOn = false;
          if (this.dialogStatus == "create") {
            this.resetFormModel();
          } else {
            this.getDetail();
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  mounted() { },
  methods: {

    /**清理表单 */
    resetFormModel() {
      let temp = {
        Id: "",
        Level: this.node.Level ? this.node.Level + 1 : 1,
        ParentId: this.node.Id,
        Name: ""
      };
      this.formModel = Object.assign({}, this.formModel, temp);
    },

    /**获取详情 */
    getDetail() {
      this.formModel = Object.assign({}, this.formModel, this.node);
    },

    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formData.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;

          if (_this.dialogStatus == "create") {
            result = classify.add(_this.formModel);
          } else if (_this.dialogStatus == "update") {
            result = classify.edit(_this.formModel);
          }
          result.then(response => {
            _this.buttonLoading = false;
            _this.$notify({
              title: "成功",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            if (this.goOn) {
              this.resetFormModel();
              this.$refs['formData'].resetFields();
            }
            this.$emit('saveSuccess', this.goOn);
          }).catch(err => {
            _this.buttonLoading = false;
          });
        } else {
          return false;
        }
      });
    },

    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
</style>


