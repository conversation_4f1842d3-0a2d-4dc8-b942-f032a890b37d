<!--跟进问卷-->
<template>
<div class="dialogMain">
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100">
        <template slot="body">
            <div class="pageWarp" v-loading="loading">
                <el-tabs v-model="activeName">
                    <el-tab-pane label="问题描述" name="1"></el-tab-pane>
                    <el-tab-pane label="解决方案" name="2"></el-tab-pane>
                    <el-tab-pane label="试验验证" name="3"></el-tab-pane>
                </el-tabs>
                <el-row class="wrapperBox">
                    <template v-if="activeName=='1'">
                        <el-form label-width="80px" style="padding-top:0">
                            <el-form-item label="问题标题">{{formData.Title}}</el-form-item>
                            <el-form-item label="问题描述">
                                <div class="divUeditor ql-editor" style="padding:5px 0;" v-html="formData.Describe" v-viewer></div>
                            </el-form-item>
                            <el-form-item label="相关附件">
                                <app-uploader accept="all" readonly :fileType="3" :max="10000" v-if="formData.AttachmentList.length>0"
                                :value="formData.AttachmentList"
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"></app-uploader>
                                <template v-else>无</template>
                            </el-form-item>
                        </el-form>
                    </template>
                    <template v-if="activeName=='2'">
                        <no-data v-if="solutionData.length===0"></no-data>
                        <el-card shadow="hover" :body-style="{ padding: '15px 11px' }"
                            v-for="(item,index) in solutionData" :key="index">
                            <el-row slot="header" class="flexWarp">
                                <div class="flexColumn">{{item.CaseName}}</div>
                                <div style="padding-right: 9px;">
                                    <el-button type="text" v-if="editable&&!item.EffectEvaluationType" @click="handelAddTrialCreate(item, 'create')">添加试验</el-button>
                                    <el-button type="text" @click="handleSubmitProgramme(item, 'detail')">查看详情</el-button>
                                </div>
                            </el-row>
                            <el-row style="font-size: 12px;">
                                <el-col :span="8">提交人：{{item.SumbitEmployeesName}}</el-col>
                                <el-col :span="8">提交时间：{{item.CreateTime | dateFilter('YYYY-MM-DD HH:mm')}}</el-col>
                                <el-col :span="8">试验结果：
                                    <template>
                                        <span v-if="item.EffectEvaluationType" class="item-status" :style="{backgroundColor: getEffectEvaluationTypeObj(item.EffectEvaluationType).bgColor,
                                                color: getEffectEvaluationTypeObj(item.EffectEvaluationType).color}"
                                        >{{ getEffectEvaluationTypeObj(item.EffectEvaluationType).label }}</span>
                                        <span v-if="item.AdoptSuggestionType" class="item-status" :style="{backgroundColor: getAdoptSuggestionTypeObj(item.AdoptSuggestionType).bgColor,
                                            color: getAdoptSuggestionTypeObj(item.AdoptSuggestionType).color}"
                                        >{{ getAdoptSuggestionTypeObj(item.AdoptSuggestionType).label }}</span>
                                    </template>
                                    <template v-if="!item.EffectEvaluationType&&!item.AdoptSuggestionType">无</template>
                                </el-col>
                            </el-row>
                        </el-card>
                    </template>
                    <template v-if="activeName=='3'">
                        <el-table ref="mainTable1" :data="testVerificationData" height="100%">
                            <el-table-column type="index" label="序号" width="50px"></el-table-column>
                            <el-table-column prop="CaseName" label="方案标题"></el-table-column>
                            <el-table-column prop="TestEmployeesName" label="试验人"></el-table-column>
                            <el-table-column prop="EffectEvaluationType" label="试验结果">
                                <template slot-scope="scope">
                                    <span class="item-status" v-if="scope.row.EffectEvaluationType"
                                        :style="{backgroundColor: getEffectEvaluationTypeObj(scope.row.EffectEvaluationType).bgColor,color: getEffectEvaluationTypeObj(scope.row.EffectEvaluationType).color}"
                                    >{{ getEffectEvaluationTypeObj(scope.row.EffectEvaluationType).label }}</span>
                                    <template v-else>无</template>
                                </template>
                            </el-table-column>
                            <el-table-column prop="AdoptSuggestionType" label="是否采纳">
                                <template slot-scope="scope">
                                    <span class="item-status" v-if="scope.row.AdoptSuggestionType"
                                        :style="{backgroundColor: getAdoptSuggestionTypeObj(scope.row.AdoptSuggestionType).bgColor,color: getAdoptSuggestionTypeObj(scope.row.AdoptSuggestionType).color}"
                                    >{{ getAdoptSuggestionTypeObj(scope.row.AdoptSuggestionType).label }}</span>
                                    <template v-else>无</template>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" :width="editable?120:60">
                                <template slot-scope="scope">
                                    <el-button type="text" @click="handelTrialCreate(scope.row, 'detail')">详情</el-button>
                                    <template v-if="editable">
                                        <el-button type="text" @click="handelTrialCreate(scope.row, 'update')">编辑</el-button>
                                        <app-table-row-button @click="handelDelTrialCreate(scope.row)" :type="3"></app-table-row-button>
                                    </template>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </el-row>
            </div>
        </template>
        <template slot="footer">
            <div class="fDiv fl" v-if="dialogStatus=='follow'">
                问题状态：
                <el-select :disabled="disabledStatus||loading" v-model="formData.ProductQuestionStatus" style="line-height: 28px;" @change="handleStatusChange">
                    <el-option v-for="item in ProductQuestionStatusEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </div>
            <app-button v-if="dialogStatus!='update'" @click="handleClose" text="关闭" type></app-button>
            <template v-else>
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </template>
    </app-dialog>

    
    <!-- 添加/查看 试验结果 -->
    <trial-create v-if="createTrialCreateVisible" :solutionId="selectId" :questionId="id" :id="selectRow.Id"
        :dialogStatus="createTrialCreateStatus" :dialogFormVisible="createTrialCreateVisible"
        @closeDialog="createTrialCreateVisible=false" @saveSuccess="createSTrialCreateSuccess" />
        
    <!-- 创建/修改/详情 解决方案 -->
    <programme-create v-if="submitProgrammeVisible" :id="selectRow.Id" :problemId="selectId"
    :dialogStatus="submitProgrammeStatus" :dialogFormVisible="submitProgrammeVisible"
    @closeDialog="submitProgrammeVisible=false" />
</div>
</template>

<script>
import { ProductQuestionStatusEnum, EffectEvaluationTypeEnum, AdoptSuggestionTypeEnum } from "./enum.js";
import NoData from "@/views/common/components/noData";


import * as productQuestionImprovementApi from '@/api/knowledge/productQuestionImprovement.js'
import * as productQuestionSolutionImprovementApi from '@/api/knowledge/productQuestionSolutionImprovement.js'
import * as productQuestionSolutionTestResultApi from '@/api/knowledge/productQuestionSolutionTestResult.js'


import programmeCreate from "./programmeCreate";
import trialCreate from "./trialCreate";



export default {
    name: "problem-improve-follow",
    components: {
        NoData,
        trialCreate,
        programmeCreate,
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail ：详情 follow: 跟进)
        dialogStatus: {
            type: String,
            default: "detail",
        },
        id: {
            type: String,
            default: "",
        },
    },
    filters: {
        // SurveyTypeFilter(val) {
        //     let obj = QuestionAnswerType.find(
        //         s => s.value == val
        //     );
        //     if (obj) {
        //         return obj.label;
        //     }
        //     return "无";
        // }
    },
    data() {
        return {
            ProductQuestionStatusEnum,
            EffectEvaluationTypeEnum,
            AdoptSuggestionTypeEnum,


            disabledBtn: false,
            disabledStatus: false,
            


            loading: false,
            activeName: '1',
            formData: {
                Id: '',
                Status: null, //  问题解决的 状态
                ProductSpecificationImprovementId: '', // 相关产品 型号 id
                ProductSpecificationImprovementName: '', // 相关产品 型号 name
                Title: '',// 问卷名称
                Describe: '',// 问题描述
                SponsorEmployees: '',// 发起人员名称
                // SponsorEmployeesIdList: [],//发起人id集合
                SponsorEmployeesList: [],//发起人集合

                AttachmentList: [],// 相关附件

                ViewRange: 1,
                ViewRangeValues: '',
                ViewRangeStr: '',
                ViewRangeStrList: [],
            },
            CoachPlanLoading1: false,
            CoachPlanLoading2: false,


            selectId: '',
            selectRow: {},

            createTrialCreateVisible: false,
            createTrialCreateStatus: "",
            solutionData: [],
            testVerificationData: [],

            
            /** 提交解决方案 */
            submitProgrammeStatus: 'detail',
            submitProgrammeVisible: false,
        };
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "follow") {
                return `跟进`;
            }
            return "详情";
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    // 查询 基本信息
                    this.getDetail();
                    // 查询解决方案
                    this.getSolutionData();
                    // 查询实验验证
                    this.getTestVerification();
                }
            },
            immediate: true
        },
    },
    created() {
    },
    mounted() {},
    methods: {
        // 确定按钮
        createData(){},
        // 提交方案弹窗
        handleSubmitProgramme(row, optType = "detail"){
            this.selectId = row.ProductQuestionImprovementId;
            this.selectRow = row;
            this.submitProgrammeStatus = optType;
            this.submitProgrammeVisible = true;
        },
        // 查询解决方案
        getSolutionData(){
            productQuestionSolutionImprovementApi.getList({ ProductQuestionImprovementId: this.id }).then(res => {
                this.solutionData = res.Items;
            });
        },
        // 查询实验验证
        getTestVerification(){
            productQuestionSolutionTestResultApi.getList({ ProductQuestionImprovementId: this.id }).then(res => {
                this.testVerificationData = res.Items;
            });
        },
        // 添加 试验结果  弹窗
        handelAddTrialCreate(row, optType){
            this.selectId = row.Id
            this.selectRow = row;
            this.createTrialCreateStatus = optType
            this.createTrialCreateVisible = true
        },
        // 实验验证列表  的 编辑/查看详情
        handelTrialCreate(row, optType = "detail"){
            this.selectId = row.ProductQuestionSolutionImprovementId;
            this.selectRow = row;
            this.createTrialCreateStatus = optType
            this.createTrialCreateVisible = true
        },
        // 实验验证列表  的 删除
        handelDelTrialCreate(rows){
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }
            this.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                productQuestionSolutionTestResultApi.del(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    // 查询解决方案
                    this.getSolutionData();
                    // 查询实验验证
                    this.getTestVerification();
                });
            });
        },
        // 添加试验结果  确定返回
        createSTrialCreateSuccess(){
            this.createTrialCreateVisible = false
            // 查询解决方案
            this.getSolutionData();
            // 查询实验验证
            this.getTestVerification();
        },
        // 查询 基本信息
        getDetail() {
            this.isOneLoad = true;
            this.loading = true
            productQuestionImprovementApi.detail({ id: this.id }).then(res => {
                this.formData = {...this.formData, ...res};
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        // 修改问题状态
        handleStatusChange(){
            productQuestionImprovementApi.EditStatus({
                Id:this.formData.Id,
                ProductQuestionStatus:this.formData.ProductQuestionStatus
            }).then(res=>{
                this.$notify({
                    title: "成功",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.getDetail();
                this.$emit('reload');
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 获取  试验结果 对象
        getEffectEvaluationTypeObj(val){
            return this.EffectEvaluationTypeEnum.find(s => s.value == val) || {};
        },
        // 获取  是否采纳 对象
        getAdoptSuggestionTypeObj(val){
            return this.AdoptSuggestionTypeEnum.find(s => s.value == val) || {};
        },
        
    }
};
</script>

<style scoped>
.dialogMain >>> .el-dialog__body{
    padding: 0 !important;
}
/* .opr >>> .el-form-item__content,.opr >>> .el-form-item__label{
    font-size: 12px;
} */
</style>
<style lang="scss" scoped>
.item-status+.item-status{
    margin-left: 5px;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
.pageWarp{
    width: 100%;

    .divUeditor{
        width: 100%;
        min-height: 200px;
        // max-height: 390px;
        // overflow: hidden;
        // overflow-y: auto;
        
        img {
            border: 0;
            max-width: 100%;
        }
    }
    .wrapperBox{
        width: 100%;
        height: 600px;
        overflow: hidden;
        overflow-y: auto;
        .el-card{
            margin-bottom: 20px;
        }
    }
}
</style>


