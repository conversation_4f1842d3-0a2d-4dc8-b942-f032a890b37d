<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1200' :maxHeight="700" >
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                <el-form-item v-if="dialogStatus == 'detail'" label="课程内容" prop="TrainsDescribe">
                    <editor-bar v-if="editable" :value="formData.TrainsDescribe" @edit="formData.TrainsDescribe = arguments[0]"></editor-bar>
                    <div class="divUeditor ql-editor" style="padding-top:7px;" v-html="formData.TrainsDescribe" v-if="!editable"></div>
                </el-form-item>
                <el-form-item label="相关资料" v-if="dialogStatus == 'detail'">

                    <app-uploader style="margin-top:-6px;" :readonly='!editable' accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>

                </el-form-item>
                <el-form-item label="类型" prop="TrainsClassificationPath">
                    <el-cascader :disabled="!editable" v-model="formData.TrainsClassificationPath" :options="trainsClassificationList" :props="{ checkStrictly: true, emitPath: false }" clearable></el-cascader>
                </el-form-item>

                <el-form-item label="课程封面" prop="LogoPath">
                    <app-upload-file :max='1' :fileSize="1024 * 1024 * 2" :fileType='1' :value='CoverFileList' :readonly="!editable" @change='handleUpChange' :preview='true'></app-upload-file>
                </el-form-item>

                <el-form-item label="课程名称" prop="TrainsName">
                    <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.TrainsName"></el-input>
                </el-form-item>

                <el-form-item label="负责人" prop="PrincipalEmployeeList">
                    <emp-selector :readonly='!editable' :showType="2" :multiple="true" :list="formData.PrincipalEmployeeList" @change="handleChangeManager" style="margin-left: 0px;"></emp-selector>
                </el-form-item>

                <el-form-item label="培训目的" prop="TrainsObjective">
                    <el-input :disabled="!editable" maxlength="100" type="text" v-model="formData.TrainsObjective"></el-input>
                </el-form-item>
                <el-form-item  v-if="dialogStatus != 'detail'" label="课程内容" prop="TrainsDescribe">
                    <editor-bar v-if="editable" :value="formData.TrainsDescribe" @edit="formData.TrainsDescribe = arguments[0]"></editor-bar>
                    <div class="divUeditor ql-editor" v-html="formData.TrainsDescribe" v-if="!editable"></div>
                </el-form-item>

                <!-- <el-form-item label="是否显示" prop="IsShow">
                    <el-radio v-model="formData.IsShow" :label="true">有效</el-radio>
                    <el-radio v-model="formData.IsShow" :label="false">无效</el-radio>
                </el-form-item> -->
                <el-form-item label="相关资料"  v-if="dialogStatus != 'detail'">

                    <app-uploader :readonly='!editable' accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>

                </el-form-item>

            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import empSelector from "../../common/empSelector";
import * as train from "@/api/informationCenter/train";
import EditorBar from "@/components/QuillEditor/index.vue";
import * as trainsClassification from "@/api/informationCenter/trainsClassification";
export default {
    name: "trainsClassifications-create",
    directives: {},
    components: {
        EditorBar,
        empSelector
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        selectTypeId: {
            type: String,
            default: ""
        },

    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            this.gettrainsClassificationList();

            if (!val) {
                this.fileList = [];
                this.isContinue = false;
                this.CoverFileList = [];
            }
            if (val) {
                this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建课程";
            } else if (this.dialogStatus == "update") {
                return "编辑课程";
            } else if (this.dialogStatus == "detail") {
                return "课程详情";
            }
        }
    },

    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            isClear: false,
            trainsClassificationList: [],
            fileList: [], //图像信息[{Id: '', Path: ''}]
            CoverFileList: [],
            formLoading: false,
            isContinue: false,
            rules: {

                LogoPath: {
                    fieldName: "课程封面",
                    rules: [{
                        required: true
                    }],
                },

                TrainsClassificationPath: {
                    fieldName: "类型",
                    rules: [{
                        required: true
                    }]
                },
                TrainsName: {
                    fieldName: "课程名称",
                    rules: [{
                        required: true
                    }]
                },
                // PrincipalEmployeeList: [{
                //     type: "array",
                //     required: true,
                //     message: "请选择负责人",
                //     trigger: "change"
                // }],
                PrincipalEmployeeList: {
                    fieldName: "请选择负责人",
                    rules: [{
                        required: true
                    }]
                },
                TrainsObjective: {
                    fieldName: "培训目的",
                    rules: [{
                        required: true
                    }]
                },
                TrainsDescribe: {fieldName: "课程内容",rules: [{required: true}, {max: 20000, trigger: "blur"}]}
            },
            labelWidth: "100px",
            formData: {
                Id: "",
                AttachmentList: [], // 
                PrincipalEmployeeList: [],
                TrainsClassificationId: "",
                TrainsClassificationPath: "",
                IsShow: true,
                TrainsName: "",
                TrainsObjective: "",
                TrainsDescribe: "",
                Logo: null,
                LogoPath: "",
            }
        };
    },
    methods: {
        handleUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.LogoPath = imgs[0].Path
                this.formData.Logo = imgs[0].Id
            } else {
                this.formData.Logo = ''
                this.formData.LogoPath = ''
            }
        },
        //获取类型下拉框
        gettrainsClassificationList() {
            trainsClassification.getTreeList().then(res => {
                //产品类型下拉框数据源处理
                this.deleteChildrens(res);
                this.trainsClassificationList = [];

                res.forEach(element => {
                    if (element.label != "全部") {
                        this.trainsClassificationList.push(element);
                    }
                });
                if (this.dialogStatus == "create") {
                    if(this.selectTypeId != '00000000-0000-0000-0000-000000000000') {
                        this.formData.TrainsClassificationPath = this.selectTypeId;
                    }
                    //   //添加时, 默认选中 未分类
                    //   var obj = res.find(s => s.label == "未分类");
                    //   if (obj) {
                    //     this.formData.ProductClassificationPath = [obj.value];
                    //   }
                }
            });
        },

        //递归删除children为空的属性
        deleteChildrens(res) {
            res.forEach(element => {
                if (element.children.length === 0) {
                    delete element.children;
                } else {
                    this.deleteChildrens(element.children);
                }
            });
        },

        handleChangeManager(users) {
            if (users && users.length > 0 && users.length <= 5) {
                this.formData.PrincipalEmployeeList = users;
            } else if (users && users.length > 5) {
                this.$message.error("负责人添加不可超过5人");
                this.formData.PrincipalEmployeeList = [];
            } else {
                this.formData.PrincipalEmployeeList = [];
            }
            this.$refs["formData"].validateField("PrincipalEmployeeList");
        },
        resetFormData() {
            let temp = {
                Id: "",
                PrincipalEmployeeList: [],
                TrainsClassificationId: "",
                TrainsClassificationPath: "",
                IsShow: true,
                TrainsName: "",
                TrainsObjective: "",
                TrainsDescribe: "",
                Logo: "",
                AttachmentList: [], //
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            this.formLoading = true;

            train
                .detail({
                    id: this.id
                })
                .then((res) => {
                    this.formLoading = false;
                    this.formData = Object.assign({}, this.formData, res);
                    this.formData.PrincipalEmployeeList = res.PrincipalEmployeeList ?
                        res.PrincipalEmployeeList : [];

                    //绑定所属分类
                    this.formData.TrainsClassificationId = [
                        this.formData.TrainsClassificationId
                    ];
                    this.CoverFileList = [];
                    if (this.formData.LogoPath) {
                        this.CoverFileList = [{
                            Id: this.formData.Logo,
                            Path: this.formData.LogoPath
                        }];
                    }
                    //     this.formData.AttachmentList =
                    // res.AttachmentIdList && res.AttachmentIdList.map((s) => s.Id);
                })
                .catch((err) => {
                    this.formLoading = false;
                });

            // .then(res => {
            //     this.formData = Object.assign({}, this.formData, res);
            //     this.formData.PrincipalEmployeeList = res.PrincipalEmployeeList ?
            //         res.PrincipalEmployeeList : [];

            //     //绑定所属分类
            //     this.formData.TrainsClassificationId = [
            //         this.formData.TrainsClassificationId
            //     ];
            //     // //用于定位cascader控件的值
            //     // this.formData.ProductClassificationPath = this.formData.ProductClassificationPath.split(',');

            //     //反向绑定封面图片
            //     // this.fileList = [];
            //     // if (this.formData.ProductCoverPath) {
            //     //     this.fileList = [{
            //     //         Id: this.formData.ProductCover,
            //     //         Path: this.formData.ProductCoverPath
            //     //     }];
            //     // }

            //     this.formLoading = false;
            // });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        createData() {

            let validate = this.$refs.formData.validate();

            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));

                postData.TrainsClassificationId = postData.TrainsClassificationPath;
                postData.PrincipalEmployeeList = this.formData.PrincipalEmployeeList.map(
                    v => v.EmployeeId
                );
                postData.AttachmentIdList =
                    postData.AttachmentList && postData.AttachmentList.map((s) => s.Id);

                //提交数据保存
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = train.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = train.edit(postData);
                }

                result.then(res => {
                    if (this.isContinue) {
                        this.resetFormData();
                        this.$emit("reload");
                    } else {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData();
                    }

                });
            });
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleSave() {
            this.createData();
        }
    }
};
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;

    .left {
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }

    .right {
        width: 40%;
    }
}

.panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
}
</style>
