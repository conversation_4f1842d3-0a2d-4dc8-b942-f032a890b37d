/**
 *
 * 所有表格需要显示的列的集合——所有列表中，业务上规定需要显示的列的集合。
 *
 * 在模块管理里面，配置列权限的时候，目前是通过模块代码反射出模型的所有列，但是反射出来的列中可能有一部分是不需要显示的。
 *
 * 通过该文件配置，最终得出可以配置权限列的集合；
 *
 * 该文件可以从每个表格对应的  tabColumns 参数中的 prop 属性 中提取；
 *
 *
 * 关系：
 *
 * 模块接口获取的列集合 A ：Col1、Col2、Col3、Col4、Col5、Col6、Col7、Col8…………
 * 该文件定义模块的集合 B ：Col1、Col2、Col3、Col4
 *
 * 集合B 中所有项一定全部包含在 集合A 中；
 * 集合B 中的元素 小于或等于 集合A 中的元素；
 *
 *
 */

export const columnsOfTable = [
    //职位管理
    { moduleCode: 'PositionResponseModel', columns: ['Name', 'DepartmentName', 'CompanyName', 'Status', 'Remark', 'CreateDateTime', 'LastUpdateDateTime'] },
    //组织管理
    { moduleCode: 'OrganizationResponseModel', columns: ['Name', 'ParentName', 'Status'] },
    //员工信息
    { moduleCode: 'EmployeeResponseModel', columns: ['Photo', 'Number', 'Name', 'Gender', 'Phone', 'StatusId', 'DepartmentName', 'CompanyName', 'Email', 'JobTitle', 'WechatID', ' EducationId'] },
    //角色管理
    { moduleCode: 'RoleView', columns: ['Name', 'Organizations', 'Status', 'EnterpriseID', 'EnterpriseName'] },
    //用户管理
    { moduleCode: 'UserReponseModel', columns: ['Name', 'Gender', 'Tel', 'Phone', 'Email'] },
    //模块管理
    { moduleCode: 'ModuleView', columns: ['Id', 'DomId', 'Name', 'ButtonShowType', 'Class', 'Icon', 'Remark'] },
    //维修单
    { moduleCode: 'RepairOrderResponseModel', columns: ['OrderNumber', 'ServicePersonals', 'CarInformation', 'FurnaceNumber', 'ReporterName', 'ServiceStartTime', 'ServiceEndTime', 'Symptom', 'Results'] },//, 'Status'
    //反馈列表
    { moduleCode: 'FeedbackListResponeDto', columns: ['CreateUser', 'CreateDateTime', 'Contents', 'State'] },
    //新闻管理
    { moduleCode: 'NewsListResponseModel', columns: ['NewsContentId', 'NewsClassId', 'OrderIndex', 'Title', 'IsRecommend', 'State', 'Image', 'PcImage', 'AppImage', 'UpdateTime'] },
    //App版本列表
    { moduleCode: 'AppVersionListrespondDto', columns: ['Version', 'VersionName', 'Explain', 'ReleaseTime', 'State', 'Description'] },
    //公告管理
    { moduleCode: 'NoticeListPageResponseModel', columns: ['Title', 'Name', 'Status', 'Contents'] },
    //表单管理
    { moduleCode: 'FormResponseModel', columns: ['Name', 'Description', 'Status'] },
    //流程设计
    { moduleCode: 'FlowSchemeResponseModel', columns: ['SchemeName', 'Description'] },
    // 流程中心（我的流程、待处理流程、已处理流程、抄送我的）
    { moduleCode: 'FlowInstanceResponseModel', columns: ['CreateDateTime', 'CustomName', 'ActivityName', 'Description', 'IsFinish'] },
    //人事档案
    { moduleCode: 'PersonnelFileResponseDtoModel', columns: ['Photo', 'Number', 'Name', 'Gender', 'Phone', 'PositionId', 'OrgName', 'CompanyName', 'EducationId', 'StatusId', 'EntryTime'] },
    //任务管理
    { moduleCode: 'GetTaskManagementResponseModel', columns: ['TaskSn', 'TaskTitle', 'TaskContent', 'TaskStateType', 'TaskPriorityType', 'TaskProgressValue', 'TaskOwnerEmployee', 'TaskParticipantEmployeeList', 'TaskStartDateTime', 'TaskEndDateTime'] },
    //故障关键字
    { moduleCode: 'FaultKeywordResponseDtoModel', columns: ['FaultKeyWordID', 'FaultKeyWordName'] },
    //参数分类
    { moduleCode: 'PlcFunctionTypeListPageResponseModel', columns: ['PlcFunctionTypeName', 'OrderIndex'] },
    //参数单位
    { moduleCode: 'PlcFunctionParamUnitListPageResponseModel', columns: ['PlcFunctionParamUnitName', 'Remark', 'OrderIndex'] },
    //参数所属节点类型
    { moduleCode: 'PlcFunctionParamTypeListPageResponseModel', columns: ['PlcFunctionParamTypeName', 'ParentPlcFunctionParamTypeName', 'OrderIndex'] },
    //基础参数
    { moduleCode: 'PlcFunctionListPageResponseModel', columns: ['PlcFunctionId', 'PlcFunctionName', 'PlcFunctionParamUnitName', 'PlcFunctionTypeList', 'OrderIndex'] },
    //故障案例库
    { moduleCode: 'FailureCaseListResponseModel', columns: ['FailureCaseCode', 'FailureSymptom'] },
    //维修单转案例库
    { moduleCode: 'RepairOrderToFailureCasesListPageResponseDtoModel', columns: ['Symptom', 'OrderNumber', 'CreateUserName', 'CreateDateTime', 'StatusId'] },
    //客户档案
    { moduleCode: 'CustomerArchiveListResponseModel', columns: ['CustomerName', 'OrganizationDepartmentName', 'OrganizationName', 'CustomerLevelTypeName', 'ImportanceTypeName', 'GenderTypeName', 'Birthday', 'WeddingDay', 'School', 'Telephone', 'MajorRelationship', 'CharacterTrait', 'Hobby', 'FrequentContacts', 'RecentBook', 'Remark', 'ShowVisitingRecord',] },
    //竞争对手档案
    { moduleCode: 'CompetitorArchiveListResponseModel', columns: ['CompanyName', 'RegisteredCapital', 'EstablishmentDate', 'TotalNumber', 'HasNationalAdvancedCertificate', 'BusinessScopeChangeDate', 'CompanyWebsite', 'Advantage', 'Inferiority', 'Opportunity', 'Threaten', 'MarketDistributionAndOrganizationalStrategy'] },
    //改菜单只在“角色管理”——》“为角色分配模块”里面用到，页面授权不需要（显示所有列）
    { moduleCode: 'MessagesTypesResponseModel', columns: ['MessageTypeID', 'MessageTypeName'] },
    //企业管理
    { moduleCode: 'GetEnterpriseResponseModel', columns: ['EnterpriseID', 'OrganizingInstitutionBarCode', 'EnterpriseName', 'EnterpriseCode', 'EnterpriseAlias', 'EnterpriseAddress', 'RegisterDate', 'LegalPerson', 'Contacts', 'ContactNumber', 'UnifiedSocialCreditCode', 'EnterpriseWebsite', 'EnterpriseMailbox'] },
    //客户单位
    { moduleCode: 'CustomerOrganizationDetailListResponseModel', columns: ['OrganizationName', 'Address', 'Email', 'Fax', 'Website', 'Telephone', 'Position', 'ImportanceTypeName', 'CustomerStatusTypeName', 'CompanyOwnerEmployee', 'MainBusiness', 'CreatorName', 'CreateDateTime', 'ShowVisitingRecord',] },
    //客户项目
    { moduleCode: 'CustomerProjectListResponseModel', columns: ['OrganizationName', 'CustomerProjectName', 'ProjectStartDate', 'ProjectEndDate', 'ProjectOwnerPerson', 'ProjectOtherPerson', 'Telephone', 'CustomerProjectPhaseTypeName', 'CompanyRelatedProject', 'CompanyOwnerEmployee', 'ProjectStatus', 'CompetitorStatus', 'AssessmentResult', 'CreatorName', 'ShowProcurementForecast',] },
    //部件数据源配置
    { moduleCode: 'GetComponentDatasourceConfigurationResponseModel', columns: ['ComponentDatasourceConfigurationId', 'ConfigurationKey', 'ConfigurationValues', 'DataSourceType', 'ConfigurationState', 'Remark'] },
    //维修单设备数据源配置
    { moduleCode: 'EquipmentListPageResponseModel', columns: ['OrgName', 'EquipmentName', 'PropName', 'PropTotal'] },
    //项目管理-变更管理
    { moduleCode: 'ProjectManagementChangeListResponseModel', columns: ['ChangeCode', 'ProjectManagementChangeTypeName', 'ChangeReason', 'ChangeImpact', 'IsImpactProgress', 'ApprovalState', 'CreateEmployeeName', 'CreateDateTime',] },
    //项目管理-项目立项
    { moduleCode: 'GetProjectManagementResponseModel', columns: ['ProjectId', 'ProjectName', 'ProjectTypeEnum', 'ProjectCode', 'ProjectLevel', 'ProjectStatus', 'ProjectSource', 'ProjectStartDateTime', 'ProjectEndDateTime', 'ProjectContext', 'ProjectObjective', 'ManpowerBudget', 'ExpenseBudget', 'RelatedProjectId', 'CreateDateTime', 'CurrentMilestone', 'DistancePlannedCompletionTimeDay', 'RelatedProjectName', 'ProjectManagerEmployeeName', 'BeginningAndEndingTime'] },
]
