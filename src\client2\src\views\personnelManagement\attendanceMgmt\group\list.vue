<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="项目管理" :subTitle='["项目管理、设置、工作台的管理页面"]' @goBack="handleGoBack"></page-title> -->
        <div class="page-wrapper">
            <div class="content-wrapper">
                <div class="tags-content-wrapper">
                    <div class="opt">
                        <i class="el-icon-back" @click="handleGoBack"> 返回</i>
                    </div>
                    <div class="tags-wrapper">
                        <el-row>
                            <el-col :span="10">
                                <span>当前部门：{{ deptDetail && deptDetail.Name }}</span>
                            </el-col>
                            <el-col :span="12">
                                <span>机器号：{{ deptDetail && deptDetail.MachineNo }}</span>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <div class="content">

                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="FuzzySearchString">
                            <el-input style="width: 100%;" 
                                placeholder="搜索员工姓名/工号"
                                @clear='handleFilter'
                                v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        handleFilter()
                                    }
                                }' 
                                clearable 
                                v-model="listQuery.FuzzySearchString"
                            ></el-input>
                        </template>

                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <div class="opt-wrapper" v-if="auth('btnGroupAdd')">
                                <!-- <el-button type="primary" :disabled='!deptDetail' size="mini" @click="onBtnClicked('btnAdd')">添加考勤组</el-button> -->
                                <el-button type="primary" :disabled='!deptDetail' class="button" @click="handleCommand('btnAdd2')">添加考勤组</el-button>
                            </div>
                        </template>
                    </app-table-form>


                    <div class="list" v-loading='projectLoading'>
                        <no-data v-show="tabDatas.length == 0"></no-data>
                        <div class="card-wrapper" v-for="p in tabDatas" :key="p.Id">
                            <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                                <div slot="header" class="clearfix">
                                    <span class="omit" :title="p.Name">{{ p.Name }}</span>
                                </div>
                                <div class="item-wrapper" @click="handleCommand('btnDetail2', p)">
                                    <div class="item omit">
                                        <span class="item-title">上班/下班时间：</span>
                                        <span>{{ `${p.WorkStartTime}-${p.WorkEndTime}` }}</span>
                                    </div>
                                    <el-row>
                                        <el-col :span="24">
                                            <div class="item omit">
                                                <span class="item-title">考勤人数：</span>
                                                <span>{{ p.PeopleNumber }}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <div class="item omit">
                                                <span class="item-title">考勤方式：</span>
                                                <span>{{ p.AttendanceType | attendanceTypeFilter }}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                                <div class="split-line"></div>
                                <div class="footer">
                                    <el-button type="text" v-if="isShow(p)" class="button" @click="handleCommand('schedule', p)">人员排班</el-button>
                                    <el-button v-if="auth('btnGroupEdit')" type="text" class="button" @click="handleCommand('btnUpdate2', p)">编辑</el-button>
                                    <el-button v-if="auth('btnGroupDel')" type="text" class="button" @click="handleCommand('delete', p)">删除</el-button>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />

                </div>
            </div>
        </div>
    </div>
    
    <!-- 由弹框改为路由页面 -->
    <!-- 创建项目 -->
    <!-- <create-group v-if="deptDetail && dialogFormVisible" :id='id' @closeDialog='closeDialog' :deptDetail='deptDetail' @saveSuccess='handleSaveSuccess' :dialogFormVisible='dialogFormVisible' :dialogStatus='dialogStatus'>
    </create-group> -->

    <schedules-dialog 
        @closeDialog='closeDialogSchedules' 
        @saveSuccess='handleSaveSuccessSchedules'
        :dialogFormVisible='dialogFormVisibleSchedules'
        v-if="dialogFormVisibleSchedules"
        dialogStatus='create' 
        :oldSchedules='[]'
        :timecardDepartmentId='timecardDepartmentId'
    >
    </schedules-dialog>




</div>
</template>

<script>


// import pageTitle from '../common/pageTitle'
// import tags from '../common/tags'
import noData from '../../../common/components/noData'
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import * as attendanceMgmt from "@/api/personnelManagement/attendanceMgmt"
// import createGroup from './createGroup'
import schedulesDialog from './schedulesDialog'
import { vars } from '../vars'
import { getUserInfo } from '@/utils/auth'
import { getChangedData } from '../../../personnelManagement/attendanceMgmt/group/dataUtils'

export default {
    name: 'attendanceMgmt-group-detail',
    components: {
        // pageTitle,
        // tags,
        noData,
        // createGroup,
        schedulesDialog,
    },
    filters: {
        attendanceTypeFilter(val) {
            let obj = vars.attendanceTypes.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    watch: {
    },
    created() {
        let pIdx = this.$route.query.pageIndex
        let pSize = this.$route.query.pageSize
        if(pIdx) {
            this.listQuery.PageIndex = parseInt(pIdx)
        }
        if(pSize) {
            this.listQuery.PageSize = parseInt(pSize)
        }

        this.getDeptDetail()
        this.getList()
    },
    computed: {


    },
    watch: {
        
    },
    mounted() {

    },
    data() {
        return {
            layoutMode: 'simple',
            deptDetail: null,

            total: 0,
            tabDatas: [],
            id: '',


            // dialogFormVisible: false,
            // dialogStatus: 'create',



            projectLoading: false,

            listQuery: {
                Status: null,
            },
            tableSearchItems: [{
                prop: "FuzzySearchString",
                label: "",
                mainCondition: true
            }],
            dialogFormVisibleSchedules: false,
            timecardDepartmentId: '',


            
        }
    },
    methods: {
        auth(domId) {
            return this.haveBtnPermission('/personnelManagement/attendanceMgmt/group', domId)
        },
        isShow(item) {
            if(item) {
                return item.SchedulManagerEmployeeList && item.SchedulManagerEmployeeList.findIndex(s => s.EmployeeId == getUserInfo().employeeid) > -1
            }
            return false
        },
        // onBtnClicked: function (domId) {
        //     switch (domId) {
        //         case "btnAdd":
        //             this.handleDialog("create");
        //             break;
        //         default:
        //             break;
        //     }
        // },
        //选择项目类型弹框
        // handleDialog(activeName, item) {
        //     if(item) {
        //         this.id = item.Id
        //     }else{
        //         this.id = ''
        //     }
        //     this.dialogStatus = activeName;
        //     this.dialogFormVisible = true;
        // },
        // closeDialog() {
        //     this.dialogStatus = ''
        //     this.dialogFormVisible = false
        // },
        // handleSaveSuccess() {
        //     this.closeDialog()
        //     this.getList()
        // },
        onResetSearch() {
            this.listQuery.FuzzySearchString = "";
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1
            this.getList()
        },
        handleResetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize,
                Status: null,
            };
            this.getList()
        },
        getDeptDetail() {
            let id = this.$route.params.id
            attendanceMgmt.detail({ id }).then(res => {
                this.deptDetail = res
            })
        },
        //获取项目列表
        getList() {
            this.projectLoading = true
            let postData = JSON.parse(JSON.stringify(this.listQuery))
            postData.TimecardGroupId = this.$route.params.id
            timecardDepartment.getList(postData).then(res => {
                this.projectLoading = false
                this.tabDatas = res.Items
                this.total = res.Total
            })
        },
        del(item) {
            if(item.PeopleNumber > 0) {
                this.$message({
                    message: '请先删除考核对象信息',
                    type: 'error'
                })
                return false
            }
            timecardDepartment.detail({ id: item.Id }).then(res => {
                if(res.TimecardEmployeeList && res.TimecardEmployeeList.length > 0) {
                    // this.$notify({
                    //     title: '失败',
                    //     message: '请先删除考核对象信息',
                    //     type: 'error',
                    //     duration: 2000
                    // })
                    this.$message({
                        message: '请先删除考核对象信息',
                        type: 'error'
                    })
                }else{
                    this.$confirm(`确认要删除吗`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        timecardDepartment.del([item.Id]).then(() => {
                            this.$notify({
                                title: '成功',
                                message: '删除成功',
                                type: 'success',
                                duration: 2000
                            })
                            this.getList()
                        })
                    })
                }
            })
        },
        handleCommand(activeName, item) {
            if(activeName == 'btnAdd2') {
                this.$router.push({path: '/personnelManagement/attendanceMgmt/group/list/add?groupId=' + this.$route.params.id})
            }else if(activeName == 'btnUpdate2') {
                this.$router.push({path: '/personnelManagement/attendanceMgmt/group/list/edit/' + item.Id + '?groupId=' + this.$route.params.id + '&pageSize=' + this.listQuery.PageSize + '&pageIndex=' + this.listQuery.PageIndex})
            }else if(activeName == 'btnDetail2') {
                this.$router.push({path: '/personnelManagement/attendanceMgmt/group/list/detail/' + item.Id + '?groupId=' + this.$route.params.id + '&pageSize=' + this.listQuery.PageSize + '&pageIndex=' + this.listQuery.PageIndex})
            }

            // if (activeName == 'update' || activeName == 'detail') {
            //     this.handleDialog(activeName, item)
            // } else 
            if (activeName == 'delete') {
                this.del(item)
            } else if (activeName == 'schedule') {
                this.openDialogSchedules(item)
            }
        },
        //排班弹框
        openDialogSchedules(item) {
            this.timecardDepartmentId = item.Id
            this.dialogFormVisibleSchedules = true
        },
        closeDialogSchedules() {
            this.dialogFormVisibleSchedules = false
        },
        handleSaveSuccessSchedules(newSchedules) {
            this.schedules = newSchedules
            let schList = getChangedData(JSON.parse(JSON.stringify(this.schedules)))
            schList = schList.map(s => {
                s.TimecardDepartmentId = this.timecardDepartmentId
                return s
            })
            
            //保存数据
            timecardDepartment.editTimecardScheduling(schList).then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.closeDialogSchedules()
            })
        },

        handleGoBack() {
            this.$router.push({
                path: '/personnelManagement/attendanceMgmt/group'
            })
        },

        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList()
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList()
        },


    }

}
</script>

<style lang="scss" scoped>
.clearfix {
    >span:first-child {
        width: calc(100% - 30px);
        display: inline-block;
    }
}

.app-container {
    .bg-white {
        .page-wrapper {
            display: flex;
            background: #fff;
            // padding: 10px;
            // padding-top: 0;

            .content-wrapper {
                flex: 1;
                .tags-content-wrapper{
                    display: flex;
                    align-items: center;
                    height: 40px;
                    background: #fff;
                    padding: 0 10px;
                    // border-bottom: 1px solid #DCDFE6;
                    border-bottom: 1px solid #EBEEF5;
                    .tags-wrapper {
                        flex: 1;
                        line-height: 28px;
                    }
                    .opt{
                        height: 20px;
                        line-height: 20px;
                        padding-right: 16px;
                        margin-right: 16px;
                        border-right: 1px solid #DCDFE6;
                        font-size: 14px;
                        cursor: pointer;
                    }
                    .opt:hover{
                        i{
                            color:rgb(64, 158, 255);
                        }
                    }
                }
                .content {
                    padding: 10px;
                    // padding-right: 0;

                    .opt-wrapper {
                        //, .condition-wrapper
                        box-sizing: border-box;
                        // border-bottom: 1px solid #DCDFE6;
                        padding-bottom: 10px;
                    }

                    .list {
                        .card-wrapper {
                            padding: 5px;
                            display: inline-block;
                            width: 25%;

                            .card {
                                width: 100%;
                            }

                            .item-wrapper {
                                cursor: pointer;
                                padding: 14px;
                                padding-bottom: 2px;

                                .item {
                                    height: 25px;
                                    line-height: 25px;

                                    div {
                                        display: inline-block;
                                    }

                                    .item-title {
                                        width: 90px;
                                    }

                                    .item-status {
                                        color: #fff;
                                        padding: 2px 4px;
                                        border-radius: 10%;
                                    }
                                }
                            }

                            .split-line {
                                height: 1px;
                                background: #DCDFE6;
                                margin-top: 10px;
                            }

                            .footer {
                                height: 28px;
                                padding-right: 14px;
                                text-align: right;
                            }
                        }
                    }
                }
            }
        }
    }
}

// { value: 1, label: '正常' },
// { value: 2, label: '已延期' },
// { value: 3, label: '已交付' },
// { value: 4, label: '已终止' },
// { value: 5, label: '立项待审批' },
// { value: 6, label: '验收待审批' },
// { value: 7, label: '立项不通过' },
// { value: 8, label: '验收不通过' },

// .proj-status-1{
//     background-color: green;
// }

// .proj-status-2{
//     background-color: gray;
// }

// .proj-status-3{
//     background-color: blue;
// }

// .proj-status-4, .proj-status-5, .proj-status-6{
//     background-color: red;
// }

// .proj-status-7, .proj-status-8{
//     background-color: orange;
// }
</style>
