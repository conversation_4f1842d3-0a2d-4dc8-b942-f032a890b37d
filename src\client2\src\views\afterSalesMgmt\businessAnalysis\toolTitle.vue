<template>
    <div class="title-wrapper">
        <div style="display: flex;">
            <span class="title">{{ title }}</span>
            <template v-if="showOptArea">
                <span class="sub-title">地区：</span>
                <el-button @click="handleClick" type="text" style="margin-right: 10px;">选择</el-button>
                <span class="omit" :title="regionalName" style="font-weight: 300; flex: 1; width: 0;">{{ regionalName }}</span>
            </template>
        </div>
        <div>
            <slot name="append"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'tool-title',
    props: {
        //是否显示操作区域
        showOptArea: {
            type: Boolean,
            default: true
        },
        title: {
            type: String,
            default: ''
        },
        regionalName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {

        }
    },
    methods: {
        handleClick() {
            this.$emit('click')
        }
    }
}
</script>

<style lang="scss" scoped>
.title-wrapper{
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
  font-weight: 700;
  height: 40px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  >div:first-child{
    flex: 1;
    display: flex;
    align-items: center;
  }
  .title{
    margin-right: 30px;
  }
  .sub-title{
    font-weight: 300; 
    // margin-right: 20px;
  }
}

</style>