<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="员工档案" :subTitle="['企业员工的个人档案管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>

                    <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <page-title :title="departmentInfo"></page-title>
                <div class="tagBox">
                    <tags :items='types' v-model="listQuery.LaborContractState">
                        <template v-for="t in types" :slot="t.value">
                            {{ t.label }}
                        </template>
                    </tags>
                </div>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="true" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                        <template slot="Name" slot-scope="scope">
                            <div>
                                {{ scope.row.Name }}
                                <app-tag-pure style="margin-left: 5px;" color="#FFAA00" text="负责人" v-if="scope.row.IsPrincipal"></app-tag-pure>

                                <!-- <el-tag style="margin-left: 5px;" type="warning" v-if="scope.row.IsPrincipal">负责人</el-tag> -->
                            </div>

                            
                        </template>


                        <template slot="DepartmentName" slot-scope="scope">
                            <div>
                                <!-- <app-tag-pure style="margin-right: 2px;" effect="dark" color="#FFAA00" text="主"></app-tag-pure> -->
                                <span>{{ scope.row.DepartmentName }}</span>
                            </div>
                        </template>

                        <!-- <template v-if="scope.row.DepartmentMinorName && scope.row.DepartmentMinorName.length > 0">
                                    <span>、</span>
                                    <span v-for="(item, idx) in scope.row.DepartmentMinorName">
                                        {{ getPartName(item) }}
                                        <template v-if="idx < scope.row.DepartmentMinorName.length - 1">、</template>
                                    </span>
                                </template> -->

                        <template slot="DepartmentMinorName" slot-scope="scope">
                          <div v-for="(item, index) in scope.row.DepartmentMinorName" :key="index">
                            {{ item }}
                          </div>
                        </template>

                        <template slot="WorkingState" slot-scope="scope">{{ scope.row.WorkingState | workingStateFilter}}</template>

                        <!-- <template slot="Status" slot-scope="scope">
                            <el-tag type="success" v-if="scope.row.Status == 1">{{ scope.row.Status | statusFilter}}</el-tag>
                            <el-tag type="danger" v-if="scope.row.Status == 2">{{ scope.row.Status | statusFilter}}</el-tag>
                        </template> -->
                        
                        <template slot="SelfMaintenance" slot-scope="scope">
                            <span class="item-status" :style="{backgroundColor: scope.row.SelfMaintenance ? '#00CC00' : '#409EFF'}">
                                {{ scope.row.SelfMaintenance ? '启用' : '关闭' }}
                            </span>
                        </template>
                        <template slot="LaborContractState" slot-scope="scope">
                            <span :style="{color: getStatusColor(scope.row.LaborContractState)}">{{ scope.row.LaborContractState | laborContractStateFilter}}</span>
                        </template>
                        
                        <template slot="ContractEndDate" slot-scope="scope">
                            {{ scope.row.ContractEndDate | dateFilter('YYYY-MM-DD') }}
                        </template>

                        <template slot="LaborContractName" slot-scope="scope">
                            {{ scope.row.LaborContractName || '无' }}
                        </template>

                        <template slot="Sex" slot-scope="scope">{{ scope.row.Sex | sexFilter}}</template>

                        

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="FuzzySearchString">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入姓名、手机号、工号、职位进行过滤"
                                        @clear='getList'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.FuzzySearchString"
                                    ></el-input>
                                </template>

                                <template slot="LaborContractSubjectId">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.LaborContractSubjectId" placeholder="">
                                        <el-option
                                            v-for="item in laborContractSubjects"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </template>
                                
                                <template slot="WorkingState">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.WorkingState" placeholder="">
                                        <el-option
                                            v-for="item in employeeWorkingStateEnum"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </template>

                                <template slot="Sex">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.Sex" placeholder="">
                                        <el-option
                                            v-for="item in genders"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </template>
                                <!-- <template slot="ConfidentialityAgreement">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.ConfidentialityAgreement" placeholder="">
                                        <el-option
                                            v-for="item in confidentialityAgreementEnum"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </template> -->

                                <template slot="HireRange">
                                    <el-date-picker
                                        v-model="listQuery.HireRange"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder
                                        end-placeholder
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;"
                                    ></el-date-picker>
                                </template>

                                <template slot="BirthdayStart">
                                    <div class="month-range-wrapper">
                                        <div class="start-month">
                                            <el-select style="width: 100%;" clearable v-model="listQuery.BirthdayStart" placeholder="">
                                                <el-option
                                                    v-for="item in months"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                >
                                                </el-option>
                                            </el-select>
                                        </div>
                                        <div class="month-separator">-</div>
                                        <div class="end-month">
                                            <el-select style="width: 100%;" clearable v-model="listQuery.BirthdayEnd" placeholder="">
                                                <el-option
                                                    v-for="item in months"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                >
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
<!-- 
                                    <el-date-picker
                                        v-model="listQuery.BirthdayRange"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder
                                        end-placeholder
                                        format="MM"
                                        value-format="MM"
                                        style="width: 100%;"
                                    ></el-date-picker> -->
                                </template>

                                <!-- <template slot="Name">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                                </template> -->

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn moduleName="xxxxx" v-on:btn-event="onBtnClicked">
                                        <template slot="btnBatchSetting" slot-scope="scope">
                                            <el-dropdown style="margin-left: 4px; height: 28px;" @command="handleSelect">
                                                <el-button type="primary">
                                                    批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                                                </el-button>
                                                <el-dropdown-menu slot="dropdown">
                                                    <el-dropdown-item command='changeConSub'>修改合同主体</el-dropdown-item>
                                                    <el-dropdown-item command='enableSelfMaintenance'>启用自维护</el-dropdown-item>
                                                    <el-dropdown-item command='disableSelfMaintenance'>关闭自维护</el-dropdown-item>
                                                </el-dropdown-menu>
                                            </el-dropdown>
                                        </template>
                                    </permission-btn>
                                </template>
                            </app-table-form>
                        </template>


                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleReview(scope.row)" :type="2"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <v-export
      @saveSuccess="() => {}"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>

    <contract-list
        v-if="dialogContractListVisible"
        @closeDialog="() => dialogContractListVisible = false"
        :dialogFormVisible="dialogContractListVisible"
        @saveSuccess="handleContractSuccess"
    ></contract-list>

    <contract-change
        v-if="dialogContractChangeVisible"
        @closeDialog="() => dialogContractChangeVisible = false"
        :dialogFormVisible="dialogContractChangeVisible"
        @saveSuccess="handleContractChangeSuccess"
        :ids='multipleSelection.map(s => s.EmployeeRecordId) || []'
    ></contract-change>

    <msg-reminder
        v-if="dialogMsgReminderVisible"
        @closeDialog="() => dialogMsgReminderVisible = false"
        :dialogFormVisible="dialogMsgReminderVisible"
    ></msg-reminder>


    <!-- 查看/修改 -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList" :selectTypeId="selectTypeId"></create-page>
</div>
</template>

<script>
import {
    listToTreeSelect
} from "@/utils";
// import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
// import {
//     downloadFile
// } from "@/utils/index";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as systemEmployee from "@/api/personnelManagement/systemEmployee";
import * as laborContractSubject from '@/api/personnelManagement/laborContractSubject'
import {
    employeeWorkingStateEnum,
    // statusEnum,
    laborContractStateEnum,
    confidentialityAgreementEnum,
} from "../enum";


import { vars } from '../../salesMgmt/common/vars'

import createPage from "./create";
import vExport from "@/components/Export/index";
import contractList from './contractList'
import contractChange from './contractChange'
import msgReminder from './msgReminder'
import * as businessRoleApi from "@/api/businessRole";

let colProp = 'ContractEndDate'
let columnObj = {
    attr: {
        prop: colProp,
        label: "合同终止日期",
        sortable: "custom",
    },
    slot: true,
}

export default {
    name: "employeeRecord-setting",
    mixins: [indexPageMixin],
    components: {
        createPage,
        vExport,
        contractList,
        contractChange,
        msgReminder,
    },
    props: {},
    filters: {
        workingStateFilter(status) {
            const statusObj = employeeWorkingStateEnum.find((s) => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "";
        },
        // statusFilter(status) {
        //     const statusObj = statusEnum.find((s) => s.value == status);
        //     if (statusObj) {
        //         return statusObj.label;
        //     }
        //     return "";
        // },
        laborContractStateFilter(status) {
            const statusObj = laborContractStateEnum.find((s) => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "无";
        },
        sexFilter(value) {
            if (value == 2) {
                return "女";
            }
            if (value == 1) {
                return "男";
            }
        },
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {
        // fildids() {
        //     return this.multipleSelection.map((s) => s.EmployeeRecordId) || [];
        // },
    },
    watch: {
        "listQuery.LaborContractState"(val) {
            if(val == 3) {
                if(!this.tabColumns.find(s => s.attr.prop == colProp)) {
                    this.tabColumns.splice(this.tabColumns.length - 1, 0, columnObj)
                }
            }else{
                let idx = this.tabColumns.findIndex(s => s.attr.prop == colProp)
                if(idx > -1) {
                    this.tabColumns.splice(idx, 1)
                }
            }

            this.$nextTick(() => {
                this.$refs.mainTable.$refs.appTableCore.doLayout()
            })

            this.handleFilter();
        },
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.DepartmentId = val.Id;
                    this.selectDepartmentName = val.DepartmentName;
                    this.handleFilter();
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getAllLaborContractSubject()
        this.getDepartments();
    },
    data() {
        return {
            laborContractSubjects: [],
            months: Array.from(Array(12), (v,k) => {
                return {
                    value: k + 1,
                    label: `${k+1}月`
                }
            }),
            layoutMode: 'simple',
            epKeys: [],
            confidentialityAgreementEnum,
            genders: vars.common.genders,
            types: [
                { value: 0, label: "全部" },
                { value: 1, label: "正常续签" },
                { value: 4, label: "未签合同" },
                { value: 2, label: "合同到期" },
                { value: 3, label: "合同终止" },
            ],
            departmentInfo: "",
            selectDepartmentName: "",

            employeeWorkingStateEnum: employeeWorkingStateEnum,
            // statusEnum: statusEnum,
            laborContractStateEnum,

            filterText: "",

            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            tableSearchItems: [{
                    prop: "FuzzySearchString",
                    label: "",
                    mainCondition: true
                },{
                    prop: "LaborContractSubjectId",
                    label: "合同主体"
                },{
                    prop: "WorkingState",
                    label: "在职状态"
                },{
                    prop: "Sex",
                    label: "性别"
                },
                // {
                //     prop: "ConfidentialityAgreement",
                //     label: "合同状态"
                // },
                {
                    prop: "HireRange",
                    label: "入职时间"
                },{
                    prop: "BirthdayStart",
                    label: "生日查询"
                }
            ],

            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            selectTypeId: "", //当前选中的类型ID
            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "Name",
                        label: "姓名",
                        showOverflowTooltip: true
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Sex",
                        label: "性别",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "Number",
                        label: "工号",
                        sortable: "custom",
                    },
                },
                {
                    attr: {
                        prop: "DepartmentName",
                        label: "主要部门",
                        showOverflowTooltip: true,
                        width: 160,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "DepartmentMinorName",
                        label: "其它部门",
                        showOverflowTooltip: true,
                        width: 160,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "JobName",
                        label: "职位",
                        showOverflowTooltip: true,
                    },
                },
                // {
                //     attr: {
                //         prop: "Mobile",
                //         label: "手机",
                //         showOverflowTooltip: true,
                //     },
                // },
                // {
                //     attr: {
                //         prop: "Email",
                //         label: "邮箱",
                //         showOverflowTooltip: true,
                //     },
                // },
                // {
                //     attr: {
                //         prop: "Status",
                //         label: "状态",
                //     },
                //     slot: true,
                // },
                
                {
                    attr: {
                        prop: "SelfMaintenance",
                        label: "自维护",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "LaborContractState",
                        label: "劳动合同状态",
                        // sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "LaborContractName",
                        label: "合同主体",
                        showOverflowTooltip: true,
                    },
                    slot: true,
                },
            ],
            listQuery: {
                DepartmentId: "",
                LaborContractState: 0,
                FuzzySearchString: '',
                // ConfidentialityAgreement: null,
                Sex: null,
                HireRange: [],
                BirthdayStart: null,
                BirthdayEnd: null,
                LaborContractSubjectId: null,
                WorkingState: null,
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

            dialogExportVisible: false,
            rData:null,
            cData:[],

            dialogContractListVisible: false,
            dialogContractChangeVisible: false,
            dialogMsgReminderVisible: false,
        };
    },
    methods: {
        getPartName(val) {
            if(val && val.indexOf('/') > -1) {
                let idx = val.lastIndexOf('/')
                return val.substring(idx + 1)
            }
            return val
        },
        getAllLaborContractSubject() {
            laborContractSubject.getAllLaborContractSubject({}).then(res => {
                this.laborContractSubjects = res.map(s => {
                    return {
                        value: s.Id,
                        label: s.LaborContractSubjectName
                    }
                })
            })
        },
        onBtnClicked: function(domId) {
            switch (domId) {
                case "btnExport":
                    this.handleExpor();
                break;
                case "btnConSubMgmt":
                    this.dialogContractListVisible = true
                break;
                case "btnMsgReminder":
                    this.dialogMsgReminderVisible = true
                break;
                default:
                break;
            }
        },
        handleContractChangeSuccess() {
            this.getList()
            this.dialogContractChangeVisible = false
        },
        handleContractSuccess() {
            this.getList()
            this.dialogContractListVisible = false
        },
        handleSelect(cmd) {
            if (this.multipleSelection.length <= 0) {
                this.$message({
                    message: '请选择需要操作的行',
                    type: 'error'
                })
                return
            }
            switch(cmd) {
                case "changeConSub":
                    this.dialogContractChangeVisible = true
                    break;
                case "enableSelfMaintenance":
                    this.setOrUnsetSelfMaintenance(true)
                    break;
                case "disableSelfMaintenance":
                    this.setOrUnsetSelfMaintenance(false)
                    break;
            }

        },
        setOrUnsetSelfMaintenance(flag) {
            let postDatas = {
                SystemEmployeeRecordIds: this.multipleSelection.map(s => s.EmployeeRecordId),
                SelfMaintenance: flag
            }
            systemEmployee.setOrUnsetSelfMaintenance(postDatas).then(res => {
                this.getList()
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });

            })
        },
        handleExpor() {
            // businessMap.exportList(this.listQuery).then(res => {
            //   downloadFile(res.Url);
            // });
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData)
            if(postData.HireRange && postData.HireRange.length == 2) {
                postData.HiredateStart = postData.HireRange[0]
                postData.HiredateEnd = postData.HireRange[1]
            }else{
                postData.HiredateStart = ''
                postData.HiredateEnd = ''
            }

            this.rData={
                "exportSource": 21,
                "columns": [],
                "searchCondition": postData
            }
            this.cData=[
                { label:'序号', value:'Idx' },
                { label:'姓名', value:'Name' },
                { label:'工号', value:'Number' },
                { label:'手机', value:'Mobile' },
                { label:'性别', value:'Sex' },
                { label:'主要部门', value:'DepartmentName' },
                { label:'其它部门', value:'DepartmentMinorName' },
                { label:'职位', value:'JobName' },
                { label:'座机', value:'SpecialPlane' },
                { label:'微信', value:'WeChat' },
                { label:'邮箱', value:'Email' },
                { label:'在职状态', value:'WorkingState' },
                { label:'身份证号', value:'IdCard' },
                { label:'籍贯', value:'NativePlace' },
                { label:'民族', value:'Nationality' },
                { label:'政治面貌', value:'PoliticCountenance' },
                { label:'婚姻', value:'IsMarried' },
                { label:'生育', value:'Birth' },
                { label:'学历', value:'EducationBackground' },
                { label:'学历性质', value:'Education' },
                { label:'毕业学校', value:'University' },
                { label:'毕业时间', value:'UniversityTimeString' },
                { label:'专业', value:'Specialty' },
                { label:'技能', value:'TechnicalAbility' },
                { label:'入职时间', value:'EntryTimeString' },
                { label:'转正时间', value:'RegularizationTimeString' },
                { label:'紧急联系人', value:'EmergencyContact' },
                { label:'紧急联系人电话', value:'EmergencyContactPhone' },
                { label:'紧急联系人2', value:'EmergencyContact1' },
                { label:'紧急联系人电话2', value:'EmergencyContactPhone1' },
                { label:'现居住地址', value:'Address' },
                { label:'实际工作月份', value:'WorkTimeString' },
                { label:'劳动合同编号', value:'ContractNo' },
                { label:'合同主体', value:'LaborContractName' },
                { label:'合同开始日期', value:'ContractStartTimeString' },
                { label:'合同到期日期', value:'ContractEndTimeString' },
                { label:'工资发放银行', value:'PayrollBank' },
                { label:'银行账号', value:'BankAccount' },
                { label:'生日', value:'BirthdayString' },
                { label:'兴趣爱好', value:'HobbiesAndInterests' },
                { label:'合同状态', value:'ConfidentialityAgreement' },
                { label:'合同期限', value:'ContractType' },
                { label:'合同终止原因', value:'ContractEndReason' },
                { label:'合同终止日期', value:'ContractEndDate' },
            ]
            
            this.dialogExportVisible=true;
        },
        handleCloseExport() {
            this.dialogExportVisible = false;
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        getStatusColor(val) {
            let obj = this.laborContractStateEnum.find(s => s.value == val)
            if(obj) {
                return obj.color
            }
            return ''
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },

        //获取成员列表
        getList() {
            if (this.checkedNode) {
                this.listLoading = true;
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                postData = this.assignSortObj(postData);

                postData.HiredateStart = ''
                postData.HiredateEnd = ''

                if(postData.HireRange && postData.HireRange.length == 2) {
                    postData.HiredateStart = postData.HireRange[0]
                    postData.HiredateEnd = postData.HireRange[1]
                }

                delete postData.HireRange

                postData.EnableDataPermissions = true

                systemEmployee
                    .getList(postData)
                    .then((res) => {
                        this.tabDatas = res.Items;
                        this.total = res.Total;
                        this.listLoading = false;
                        this.departmentInfo = this.selectDepartmentName;
                    })
                    .catch((err) => {
                        this.listLoading = false;
                    });
                this.getStatistics()
            }
        },
        getStatistics() {
            systemEmployee.getListByLaborContractState({DepartmentId: this.listQuery.DepartmentId}).then(res => {
                this.types.forEach(item => {
                    let count = 0;
                    let tmp = res.find(s => s.value == item.value);
                    if (tmp) {
                        count = tmp.total;
                    }
                    if (item.label.indexOf("(") > -1) {
                        item.label = item.label.replace(/[0-9]+/gi, count);
                    } else {
                        item.label += `(${count})`;
                    }
                });
            })
        },
        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.EmployeesId;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        //弹出详情框
        handleReview(row, optType = "detail") {
            this.id = row.EmployeesId;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        onResetSearch() {
            this.listQuery.LaborContractState = 0
            this.listQuery.FuzzySearchString = ''
            // this.listQuery.ConfidentialityAgreement = null
            this.listQuery.Sex = null
            this.listQuery.HireRange = []
            this.listQuery.BirthdayStart = null
            this.listQuery.BirthdayEnd = null
            this.listQuery.LaborContractSubjectId = null
            this.listQuery.WorkingState = null
            
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
             businessRoleApi
                .getDepartmentListByCondition({BusinessRoleType: 7, LoadParentLevel: true})
                .then((res) => {

                    //整理数据，根节点需要用 ParentId 为空表示
                    if(res && res.length > 0) {
                        res.forEach(e => {
                            if(res.findIndex(s => s.Id == e.ParentId) == -1) {
                                e.ParentId = null
                            }
                        });
                    }

                    this.treeDatas = listToTreeSelect(res);
                    
                    if(this.treeDatas && this.treeDatas.length>0){
                        this.treeDatas.forEach(v => {
                            this.epKeys.push(v.Id);
                            // if(v.children.length>0){
                            //     v.children.forEach(v1 => {
                            //         this.epKeys.push(v1.Id);
                            //     })
                            // }

                        })
                    }
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 42px);
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {

            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}

.month-range-wrapper{
    display: flex;
    .start-month, .end-month{
        flex: 1
    }
    .month-separator{
        padding: 0 6px;
    }
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
