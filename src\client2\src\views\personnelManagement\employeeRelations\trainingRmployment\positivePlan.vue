<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" style="padding-top: 0;"
                label-position="right" label-width="80px" class="wrapperMain">
                    <el-tabs v-model="activeName">
                        <el-tab-pane label="基本信息" name="1" style="height:400px;">
                            <el-row class="wrapperBox" v-loading='loading' style="padding-top: 10px;">
                                <el-col :span="24">
                                    <el-form-item label="头像" prop="AvatarPath">
                                        <img :src="formData.AvatarPath || require('../../../../assets/images/avatar3.png')"
                                        style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 65px; height: 65px;" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="姓名" prop="Name">
                                        {{formData.Name}}
                                    </el-form-item>
                                    <el-form-item label="部门" prop="DepartmentName">
                                        {{formData.DepartmentName||'无'}}
                                    </el-form-item>
                                    <el-form-item label="入职时间" prop="EntryTime">
                                        {{formData.EntryTime | dateFilter('YYYY-MM-DD')}}
                                    </el-form-item>
                                    <el-form-item label="导师" prop="TutorEmployeeId">
                                        {{formData.TutorEmployeeId&&formData.TutorEmployee.Name}}
                                    </el-form-item>
                                    <el-form-item label="部门经理" prop="DepartmentManager">
                                        {{formData.DepartmentManager&&formData.DepartmentManager.Name}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="性别" prop="Sex" label-width="120px">
                                        {{formData.Sex | sexFilter}}
                                    </el-form-item>
                                    <el-form-item label="职位" prop="JobName" label-width="120px">
                                        {{formData.JobName||'无'}}
                                    </el-form-item>
                                    <el-form-item label="预计转正时间" prop="PositiveTimePlan" label-width="120px">
                                        {{formData.PositiveTimePlan | dateFilter('YYYY-MM-DD')}}
                                    </el-form-item>
                                    <el-form-item label="直属上级" prop="ImmediateSuperior" label-width="120px">
                                        {{formData.ImmediateSuperior&&formData.ImmediateSuperior.Name}}
                                    </el-form-item>
                                    <el-form-item label="转正时间" prop="PositiveTime" label-width="120px">
                                        <el-date-picker v-model="formData.PositiveTime" type="date" :disabled="!editable" v-if="editable"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                                        <template v-else>{{formData.PositiveTime | dateFilter('YYYY-MM-DD')}}</template>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-tab-pane>
                        <el-tab-pane label="辅导计划" name="2">
                            <el-row class="wrapperBox">
                                <el-table ref="mainTable1" :data="CoachPlanData" v-loading="CoachPlanLoading" height="100%">
                                    <el-table-column type="index" label="序号" width="50px"></el-table-column>
                                    <el-table-column prop="CoachPlan" label="辅导计划"></el-table-column>
                                    <el-table-column prop="Target" label="衡量标准/目标"></el-table-column>
                                    <el-table-column prop="AccomplishTime" label="完成时间" wwidth="100px">
                                        <template slot-scope="scope">{{ scope.row.AccomplishTime | dateFilter('YYYY-MM-DD') }}</template>
                                    </el-table-column>
                                </el-table>
                            </el-row>
                        </el-tab-pane>
                        <el-tab-pane label="考核情况" name="3">
                            <el-row class="wrapperBox" v-loading='ReportLoading'>
                                <el-timeline v-if="ReportData.length>0">
                                    <el-timeline-item v-for="item in ReportData" :key="item.Id"
                                    hide-timestamp placement="top">
                                        <el-collapse v-model="ActiveNames" size="mini">
                                            <el-collapse-item :name="item.Id">
                                                <div slot="title">
                                                    <span style="margin-right: 10px">{{item.ReportTime | dateFilter('YYYY-MM-DD HH:mm')}}</span>
                                                    <span>{{getStatusObj(item.ReportType).label}}</span>
                                                </div>
                                                <el-card size="mini" class="collapseCard">
                                                    <div class="collapseItem">
                                                        <div class="collapseItem_title">汇报概述：</div>
                                                        <div class="divPre">{{item.ReportContent}}</div>
                                                    </div>
                                                     <div class="collapseItem">
                                                       <div class="collapseItem_title">考核结果：</div>
                                                       <template>
                                                        <el-table
                                                          :data="item.list"
                                                          border
                                                          style="width: 100%">
                                                         <el-table-column
                                                          prop="name"
                                                          label="姓名"
                                                          width="90">
                                                         </el-table-column>
                                                         <el-table-column
                                                          prop="role"
                                                          label="角色"
                                                          width="90">
                                                         </el-table-column>
                                                         <el-table-column
                                                          prop="feedbackAssessmentResults"
                                                          label="考核结果反馈"
                                                          width="270">
                                                         </el-table-column>
                                                          <el-table-column
                                                          prop="improvementSuggestions"
                                                          label="改进意见"
                                                          width="270">
                                                         </el-table-column>
                                                         <el-table-column
                                                          prop="date"
                                                          label="日期"
                                                          width="160">
                                                             <template slot-scope="scope" v-if="scope.row.date"> {{scope.row.date | dateFilter('YYYY-MM-DD HH:mm')}}</template>
                                                              <template  v-else> {{""}}</template>
                                                         </el-table-column>
                                                         </el-table>
                                                        </template>
                                                    </div>
                                                    <div class="collapseItem">
                                                        <div class="collapseItem_title">相关附件：</div>
                                                        <app-uploader v-if="item.AttachmentList.length>0" accept="all" :fileType="3" :max="10000"
                                                        :value="item.AttachmentList" readonly
                                                        :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"></app-uploader>
                                                        <template v-else>无</template>
                                                    </div>
                                                </el-card>
                                            </el-collapse-item>
                                        </el-collapse>
                                    </el-timeline-item>
                                </el-timeline>
                                <no-data v-else></no-data>
                            </el-row>
                        </el-tab-pane>
                        <!-- 查看员工详情时  不显示 工作汇报和考勤 -->
                        <template v-if="dialogStatus != 'detail'">
                            <el-tab-pane label="日常工作汇报" name="4">
                                <el-row class="wrapperBox flexWrapper" v-loading='WorkReportLoading'>
                                    <template v-if="WorkReportData.length>0">
                                        <div class="lft">
                                            <div class="wrapperBox_title">参与周计划</div>
                                            <div class="lftWrapper">
                                                <div class="lftWrapper_item" :class="{'active': WorkReportDataNode.Id == item.Id}" :title="item.Name"
                                                v-for="item in WorkReportData" :key="item.Id" @click="WorkReportDataNodeChange(item)">{{item.Name}}</div>
                                            </div>
                                        </div>
                                        <div class="rft" v-loading="WorkReportLoading||WorkReportDetailLoading">
                                            <noData v-if="WorkReportDetailData.length==0"></noData>
                                            <el-row size="mini" v-for="item in WorkReportDetailData" :key="item.Id">
                                                <dailyDetail :detailObj='item'></dailyDetail>
                                            </el-row>
                                        </div>
                                    </template>
                                    <no-data v-else></no-data>
                                </el-row>
                            </el-tab-pane>
                            <el-tab-pane label="考勤报告" name="5">
                                <el-row class="wrapperBox AttendanceReportMain">
                                    <el-row class="tag_group">
                                        <el-tag size="mini" v-for="(tagItem,tagIndex) in AttendanceSearchData" :key="tagIndex"
                                            @click="AttendanceSearchChange(tagItem)"
                                            :type="tagItem.Year==AttendanceReportDataListQuery.Year&&tagItem.Month==AttendanceReportDataListQuery.Month?'':'info'"
                                            :effect="tagItem.Year==AttendanceReportDataListQuery.Year&&tagItem.Month==AttendanceReportDataListQuery.Month?'dark':'plain'">{{tagItem.label}}</el-tag>
                                    </el-row>
                                    <div class="flexMain">
                                        <div class="flexWrapper">
                                            <div class="lft" v-loading="AttendanceReportLoading">
                                                <page-title title="考勤报告"></page-title>
                                                <noData v-if="!AttendanceReportData"></noData>
                                                <el-descriptions v-else direction="vertical" :column="5" border>
                                                    <el-descriptions-item label="应该出勤">{{AttendanceReportData.DueAttendanceDay.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="实际出勤">{{AttendanceReportData.ActualAttendance.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="迟到次数">{{AttendanceReportData.LateNumber.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="总迟到时长">{{AttendanceReportData.TotalLateHours.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="早退">{{AttendanceReportData.LeaveEarly.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="缺勤天数">{{AttendanceReportData.AbsentDay.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="出差">{{AttendanceReportData.BusinessTrip.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="工作日加班">{{AttendanceReportData.WorkingOvertime.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="休息日加班">{{AttendanceReportData.RestdaysWorkOvertime.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="事假">{{AttendanceReportData.CasualLeave.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="调休">{{AttendanceReportData.WorkToRest.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="病假">{{AttendanceReportData.SickLeave.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="年假">{{AttendanceReportData.AnnualVacation.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="产假">{{AttendanceReportData.MaternityLeave.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="陪产假">{{AttendanceReportData.PaternityLeave.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="婚假">{{AttendanceReportData.MarriageLeave.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label="丧假">{{AttendanceReportData.FuneralLeave.Value||0}}</el-descriptions-item>
                                                    <el-descriptions-item label=" "></el-descriptions-item>
                                                    <el-descriptions-item label=" "></el-descriptions-item>
                                                    <el-descriptions-item label=" "></el-descriptions-item>
                                                </el-descriptions>
                                            </div>
                                            <div class="rft">
                                                <div class="detailMain">
                                                    <page-title title="打卡数据"></page-title>
                                                    <el-row class="detailWrapper" v-loading="AttendanceReportLoading||AttendanceReportDetailLoading">
                                                        <noData v-show="AttendanceReportDetailData.length == 0"></noData>
                                                        <div class="item-wrapper" v-for="(item, idx) in AttendanceReportDetailData" :key="idx">
                                                            <div>{{ item.Date | dateFilter('YYYY-MM-DD') }}</div>
                                                            <div class="title">
                                                                <span style="margin-right: 20px;">
                                                                    上午：
                                                                    <span v-if="item.MornFirstCardStatus" :style="{color: getStatusColor(item.MornFirstCardStatus)}">{{ item.MornFirstCardStatus | timecardStatusFilter }}</span>
                                                                    <span v-else>{{ item.MornFirstCardStatus | timecardStatusFilter }}</span>
                                                                </span>
                                                                <span>
                                                                    下午：
                                                                    <span v-if="item.NightLastCardStatus" :style="{color: getStatusColor(item.NightLastCardStatus)}">{{ item.NightLastCardStatus | timecardStatusFilter }}</span>
                                                                    <span v-else>{{ item.NightLastCardStatus | timecardStatusFilter }}</span>
                                                                </span>
                                                            </div>
                                                            <div v-for="(t, idx2) in item.TimecardRecordData" :key="idx2">
                                                                <div>{{ t.Date | dateFilter('HH:mm:ss') }}</div>
                                                                <div>{{ t.CardDescription }}</div>
                                                            </div>
                                                            <div style="margin-bottom: 4px;" v-for="(proc, idx) in item.ProcessData" :key="idx">
                                                                <div style="padding: 1px 4px; border: 1px solid #409eff; color: #409eff; border-radius: 4px;">
                                                                    <span>{{proc.LeaveType | levelTypeFilter}}：</span>
                                                                    <span v-for="(t, idx2) in proc.ProcessNodeTimeList" :key="idx2">
                                                                        <template v-if="proc.LeaveType != 11">
                                                                            {{t | morningOrAfternoonFilter}}
                                                                            <template v-if="idx2 < proc.ProcessNodeTimeList.length - 1">、</template>
                                                                        </template>
                                                                        <template v-else>
                                                                        {{ t }}
                                                                        </template>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </el-row>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-row>
                            </el-tab-pane>
                        </template>
                        <el-tab-pane label="转正评价" name="6">
                            <el-row class="wrapperBox">
                                <el-table ref="mainTable1" :data="evaluateData" v-loading="evaluateLoading" height="100%">
                                    <el-table-column type="index" label="序号" width="50px"></el-table-column>
                                    <el-table-column prop="AppraiserEmployeeName" label="评价人" width="80px"></el-table-column>
                                    <el-table-column prop="AppraiserEmployeeDuty" label="职责" width="80px"></el-table-column>
                                    <el-table-column prop="Remark"  label="评价内容"></el-table-column> 
                                    <!-- show-overflow-tooltip -->
                                    <el-table-column prop="CreateTime" label="评价时间" width="100px">
                                        <template slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD') }}</template>
                                    </el-table-column>
                                    <!-- <el-table-column prop="CoachPlan" label="辅导计划"></el-table-column>
                                    <el-table-column prop="Target" label="衡量标准/目标"></el-table-column>
                                    <el-table-column prop="AccomplishTime" label="完成时间" wwidth="100px">
                                        <template slot-scope="scope">{{ scope.row.AccomplishTime | dateFilter('YYYY-MM-DD') }}</template>
                                    </el-table-column> -->
                                </el-table>
                            </el-row>
                        </el-tab-pane>
                    </el-tabs>
                    <div v-if="dialogStatus != 'detail'">
                        <!-- <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                        <approval-panel v-else ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                         -->
                        <approval-panel v-if="dialogStatus == 'update'" ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                        <approval-detail 
                            :isOnlyViewDetail='isOnlyViewDetail || !isApprovalor' 
                            v-if="dialogStatus == 'approval' || dialogStatus == 'detail' || dialogStatus == 'revoke' || dialogStatus == 'revokeApproval'" 
                            ref="approvalDetail" 
                            :dialogStatus='dialogStatusTrans' 
                            :approvalObj='formData.Approval'
                        ></approval-detail>
                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <!-- <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button> -->
                
                <app-button v-if="dialogStatus != 'approval'" @click="createData" :buttonType="1" v-show="editable || dialogStatus=='revoke'" :disabled="disabledBtn" style="margin-left:10px;"></app-button>
                <el-button @click="handleApproval"  type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval' && !isOnlyViewDetail && isApprovalor" style="margin-left:10px;">审批</el-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import empSelector from '@/views/common/empSelector'
import approvalMixins from '@/mixins/approvalPatch'
import NoData from "@/views/common/components/noData";
import dailyDetail from "@/views/workbench/workPlan/planList/dailyDetail";

import approvalPanel from '@/views/projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '@/views/projectDev/projectMgmt/workbench/common/approvalDetail'
import * as EmployeeTrainingPlanApi from "@/api/personnelManagement/EmployeeTrainingPlan";
import * as EmployeeCoachPlanApi from "@/api/personnelManagement/EmployeeCoachPlan";
import * as EmployeePositiveEvaluationApi from "@/api/personnelManagement/EmployeePositiveEvaluation";

import * as EmployeeReportApi from "@/api/personnelManagement/EmployeeReport";
import * as WorkPlanApi from "@/api/workbench/workPlan";
import * as WorkPlanDailyApi from "@/api/workbench/workPlanDaily";

import initCellData from '@/views/personnelManagement/attendanceMgmt/report/common'
import * as TimecardDepartmentApi from "@/api/personnelManagement/timecardDepartment";
import { vars } from '../common/vars'
import * as ApprovalVars from "@/views/workbench/myWorkbench/vars";
import * as AttendanceReportVars from "@/views/personnelManagement/attendanceMgmt/vars";
export default {
    name: "trainingRmployment-editPlan",
    directives: {},
    components: {
        NoData,
        empSelector,
        approvalPanel,
        approvalDetail,
        dailyDetail
    },
    mixins: [approvalMixins],
    computed: {
        dialogStatusTrans() {
            let statusTemp = this.dialogStatus
            if(statusTemp == "revoke" || statusTemp == "detail") {
                return 'detail'
            }else if(statusTemp == "approval" || statusTemp == "revokeApproval") {
                return 'approval'
            }else{
                return statusTemp
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != 'approval' && this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "转正申请";
            }else if(this.dialogStatus == 'detail'){
                return "员工详情";
            }else if(this.dialogStatus == 'approval'){
                return "转正审批";
            }else if(this.dialogStatus == 'revoke' || this.dialogStatus == 'revokeApproval') {
                return "(撤销)转正申请";
            }
            return "";
        },
    },
    filters: {
        sexFilter(value) {
            if (value == 2) {
                return "女";
            }
            if (value == 1) {
                return "男";
            }
        },
        levelTypeFilter(val) {
            let obj = AttendanceReportVars.vars.leaveTypes.find(s => s.value === val)
            if (obj) {
                return obj.label
            }
            return '无'
        },
        timecardStatusFilter(val) {
            let result = '-'
            if (val) {
                let obj = AttendanceReportVars.vars.timecardStatus.find(s => s.value == val)
                if (obj) {
                result = obj.label
                }
            }
            return result
        },
        morningOrAfternoonFilter(val) {
            let result = '无'
            if (val) {
                let obj = AttendanceReportVars.vars.morningOrAfternoon.find(s => s.value == val)
                if (obj) {
                result = obj.label
                }
            }
            return result
        },
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        id: {
            type: String,
            default: "",
        },
        approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
            type: String,
            default: ''
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
        WorkReportDataNode(val){
            if(val){
                this.getWorkReportDetail()
            }
        }
    },
    created() {
        if(this.dialogStatus != 'detail'){
            this.rules = this.initRules(this.rules);
        }
    },
    data() {
        return {
            ReportTypeEnum: vars.ReportTypeEnum,
            activeName: '1',

            disabledBtn: false,
            loading: false,
            rules: {
                PositiveTime: {fieldName: "转正时间",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                // Id: '',
                Name: '',
                AvatarPath: '',
                DepartmentId: '',
                DepartmentName: '',
                EntryTime: '',
                TutorEmployeeId: '',
                TutorEmployeeIdList: [],
                JobId: '',
                JobName: '',
                PositiveTimePlan: '',
                Sex: 1,
                PositiveTime: '',
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
                DepartmentManager:{},
            },
            tabAllColumns: [
                {attr: {prop: "AvatarPath",label: "头像"},slot: true},
                {attr: {prop: "Name",label: "姓名"}},
                {attr: {prop: "DepartmentName",label: "部门"}},
                {attr: {prop: "Number",label: "工号"}},
                {attr: {prop: "EntryTime",label: "入职时间"},slot: true},
                {attr: {prop: "PositiveTimePlan",label: "预计转正时间",width: '160px'},slot: true, customHeader: true,},
                {attr: {prop: "TutorEmployeeId",label: "导师",width: '220px'},slot: true, customHeader: true,},
            ],
         
            // 辅导计划
            CoachPlanData: [],
            CoachPlanLoading: false,

            // 月度汇报
            ReportData: [],
            ReportLoading: false,
            ActiveNames: [],

            // 工作计划
            WorkReportData: [],
            WorkReportLoading: false,
            WorkReportDataNode: {},
            // 工作计划 详情
            WorkReportDetailData: [],
            WorkReportDetailLoading: false,

            // 考勤报告
            AttendanceReportData: null,
            AttendanceReportLoading: false,
            AttendanceSearchData: [],
            AttendanceReportDataListQuery: {
                Year: null,
                Month: null,
            },
            // 考勤报告 详情
            AttendanceReportDetailData: [],
            AttendanceReportDetailLoading: false,

            evaluateLoading: false,
            evaluateData: [],
        };
    },
    methods: {
        WorkReportDataNodeChange(item){
            this.WorkReportDataNode = item
        },
        // 列表状态转换
        getStatusObj(status) {
            return this.ReportTypeEnum.find(s => s.value == status) || {};
        },
        // 导师  选择人员
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.TutorEmployeeId = users.map(s=>s.EmployeeId)[0]
                this.formData.TutorEmployeeIdList = users
            } else {
                this.formData.TutorEmployeeId = ''
                this.formData.TutorEmployeeIdList = []
            }
            this.$refs["formData"].validateField(`TutorEmployeeId`);
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1) {
            this.$message({
                message: '不得超过1个',
                type: 'error'
            })
            return false
            }
            return true
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            let listResult = self.$refs.formData.validate();
            let approvalPanelValidate = self.$refs.approvalPanel.validate();
               Promise.all([listResult,approvalPanelValidate]).then(valid => {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    postData.Approval = self.$refs.approvalPanel.getData() //审批层区块
                    postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                    postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                    // console.log(postData)
                    self.disabledBtn = true;
                    EmployeeTrainingPlanApi.Positive(postData).then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
               }).catch(err => {
                   this.disabledBtn=false;
               })
        },
        // 查询 基本信息
        getDetail() {
            this.loading = true
            EmployeeTrainingPlanApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                if(this.dialogStatus == "update"){
                    this.formData.PositiveTime = dayjs().format('YYYY-MM-DD HH:mm')
                }
                this.getCoachPlan();// 查询 辅导计划
                this.getReport();// 查询 月度汇报
                this.getEvaluates();// 查询 评价列表
                if(this.dialogStatus != "detail") {
                    this.setAttendanceSearchData(res);//设置考勤搜索条件数据
                    this.getWorkReport(); // 查询工作汇报
                }
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        // 查询 辅导计划
        getCoachPlan(){
            this.CoachPlanLoading = true
            EmployeeCoachPlanApi.getList({ EmployeeId: this.formData.EmployeeId }).then(res => {
                this.CoachPlanData = res;
                this.CoachPlanLoading = false
            }).catch(err => {
                this.CoachPlanLoading = false
            });
        },
        // 查询 转正评价
        getEvaluates(){
            let postDatas = { 
                EmployeeId: this.formData.EmployeeId,
                PageSize: 1000,
                PageIndex: 1
            }
            this.evaluateLoading = true
            EmployeePositiveEvaluationApi.getList(postDatas).then(res => {
                this.evaluateData = res.Items;
                this.evaluateLoading = false
            }).catch(err => {
                this.evaluateLoading = false
            });
        },
        
        // 查询 月度汇报
        getReport(){
            this.ReportLoading = true
            EmployeeReportApi.getList({ EmployeeId: this.formData.EmployeeId }).then(res => {

                if(res&&res.length>0) {
                    this.ActiveNames = [res[0].Id]
                }

                this.ReportData = res.map(v=>{
                   // debugger
                    let tempTutor = v.TutorReportFeedback
                    let tempSuperior = v.ImmediateSuperiorReportFeedback
                    let tempManager = v.DepartmentManagerReportFeedback
                    v.list = []

                    if(tempTutor){

                        v.list.push({
                            date: tempTutor.LastUpdateTime,
                            name: tempTutor.FeedbackEmployee.Name,
                            role: '导师',
                            feedbackAssessmentResults: tempTutor.ReportFeedbackContent,
                            improvementSuggestions: tempTutor.Improvements,
                        })
                    
                    }
                   
                   
                    if(tempSuperior){

                         v.list.push({
                            date: tempSuperior.LastUpdateTime,
                            name: tempSuperior.FeedbackEmployee.Name,
                            role: '直属上级',
                            feedbackAssessmentResults: tempSuperior.ReportFeedbackContent,
                            improvementSuggestions: tempSuperior.Improvements,
                        })

                    }
                    

                     if(tempManager){

                         v.list.push({
                            date: tempManager.LastUpdateTime,
                            name: tempManager.FeedbackEmployee.Name,
                            role: '部门经理',
                            feedbackAssessmentResults: tempManager.ReportFeedbackContent,
                            improvementSuggestions: tempManager.Improvements,
                        })

                     }

                     return v
                })
              

                this.ReportLoading = false
            }).catch(err => {
                this.ReportLoading = false
            });
        },
        // 查询 工作汇报
        getWorkReport(){
            this.WorkReportLoading = true
            WorkPlanApi.GetEmployeeWorkPlan({ EmployeeId: this.formData.EmployeeId }).then(res => {
                this.WorkReportData = res;
                this.WorkReportDataNode = {};
                if(res&&res.length>0){
                    this.WorkReportDataNode = res[0];
                }
                this.WorkReportLoading = false
            }).catch(err => {
                this.WorkReportLoading = false
            });
        },
        // 查询 工作汇报 详情
        getWorkReportDetail(){
            this.WorkReportDetailLoading = true
            WorkPlanDailyApi.getListByEmpId({
                EmployeeId: this.formData.EmployeeId,
                WorkPlanId: this.WorkReportDataNode.Id
            }).then(res => {
                this.WorkReportDetailData = res;
                this.WorkReportDetailLoading = false
            }).catch(err => {
                this.WorkReportDetailLoading = false
            });
        },
        // 查询 考勤报告
        getAttendanceReport(){
            let {Year, Month} = this.AttendanceReportDataListQuery;
            this.AttendanceReportLoading = true
            TimecardDepartmentApi.getTimecardReport({
                EmployeeId: this.formData.EmployeeId,
                Month: Month,
                PageIndex: 1,
                PageSize: 20,
                Year: Year,
            }).then(res => {
                if(res.Items&&res.Items.length>0){
                    this.AttendanceReportData = res.Items[0];
                    this.getAttendanceReportDetail()
                }else{
                    this.AttendanceReportData = null
                }
                this.AttendanceReportLoading = false
            }).catch(err => {
                this.AttendanceReportLoading = false
            });
        },
        // 查询 考勤报告 详情
        getAttendanceReportDetail(){
            let {Year, Month} = this.AttendanceReportDataListQuery;
            this.AttendanceReportDetailLoading = true
            TimecardDepartmentApi.getPersonalTimecardRecords({
                EmployeeId: this.formData.EmployeeId,
                Month: Month,
                Year: Year,
                // EmployeeId: '689d8a9e-95fe-45e0-a92b-d81db0f3feb6',
                // Month: 5,
                // Year: 2021,
            }).then(res => {
                this.AttendanceReportDetailData = initCellData(res);
                this.AttendanceReportDetailLoading = false
            }).catch(err => {
                this.AttendanceReportDetailLoading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 审批确认
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = ApprovalVars.vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // console.log(postData)
                        this.disabledBtn = true
                        EmployeeTrainingPlanApi.Approval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData();
                            this.handleClose();
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })
                }
            })
        },
        // 设置考勤搜索条件数据
        setAttendanceSearchData(obj){
            let SearchData = [],
                EntryYear = Number(dayjs(obj.EntryTime).format('YYYY')),
                EntryMonth = Number(dayjs(obj.EntryTime).format('MM')),
                NowYear = Number(dayjs().format('YYYY')),
                NowMonth = Number(dayjs().format('MM'));
            SearchData = this.formatSearchData(SearchData,EntryMonth - 1,EntryYear,{NowYear,NowMonth});
            this.AttendanceSearchData = SearchData;
            // console.log('入职年-月',`${EntryYear}-${EntryMonth}`,'当前年-月',`${NowYear}-${NowMonth}`)
            
            this.AttendanceReportDataListQuery.Year = SearchData[0].Year
            this.AttendanceReportDataListQuery.Month = SearchData[0].Month
            // 考勤报告
            this.getAttendanceReport();
        },
        // 递归 计算 搜索条件集合
        formatSearchData(Arr=[],Month=1,Year=1,{NowYear,NowMonth}){
            let NewMonth = JSON.parse(JSON.stringify(Month)),
                NewYear = JSON.parse(JSON.stringify(Year)),
                NewArr = JSON.parse(JSON.stringify(Arr));
            NewMonth++;
            if(NewMonth>12){
                NewMonth = 1;
                NewYear++;
            }
            if(NewYear>NowYear||NewMonth>NowMonth) {
                return NewArr
            } else {
                NewArr.push({
                    Year: NewYear,
                    Month: NewMonth,
                    label: `${NewYear}-${NewMonth<10?'0'+NewMonth:NewMonth}`
                })
                return this.formatSearchData(NewArr,NewMonth,NewYear,{NowYear,NowMonth})
            }
        },
        // 考勤报告  按月筛选
        AttendanceSearchChange(tagItem){
            if(!(this.AttendanceReportDataListQuery.Year==tagItem.Year&&this.AttendanceReportDataListQuery.Month==tagItem.Month)){
                this.AttendanceReportDataListQuery.Year=tagItem.Year;
                this.AttendanceReportDataListQuery.Month=tagItem.Month;
                this.getAttendanceReport();
            }
        },
        getStatusColor(val) {
            let obj = AttendanceReportVars.vars.timecardStatus.find(s => s.value == val)
            if (obj) {
                return obj.color
            }
            return ''
        },
    }
};
</script>

<style scoped>
.wrapperBox >>> .el-collapse-item__header{
    height: 35px;
    line-height: 35px;
    padding-left: 0;
    border: none;
}
.wrapperBox >>> .el-timeline-item__node{
    /* top: 4px; */
}
.wrapperBox >>> .el-timeline-item__wrapper{
    top: -10px;
}
.wrapperMain >>> .el-tabs__header{
    margin-bottom: 0;
}
</style>
<style lang='scss' scoped>
.wrapperMain{
    padding: 0 10px;
}
.wrapperBox{
    height: 560px;
    overflow-y: auto;
    &.AttendanceReportMain{
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .tag_group{
            padding-top: 10px;
            border-bottom: 1px solid #dcdfe6;
            .el-tag{
                margin-right: 10px;
                cursor: pointer;
                margin-bottom: 10px;
            }
        }
        .flexMain{
            flex: 1;
            overflow: hidden;
            .flexWrapper{
                display: flex;
                width: 100%;
                height: 100%;
                .lft{
                    width: calc(100% - 350px);
                    border-right: 1px solid #dcdfe6;
                }
                .rft{
                    width: 350px;
                    flex: 1;
                    .detailMain{
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        .detailWrapper{
                            flex: 1;
                            overflow-y: auto;
                            .item-wrapper{
                                border-bottom: 1px solid #dcdfe6;
                                padding: 10px;
                                .title{
                                    display: flex;
                                    >span{
                                        flex: 1;
                                    }
                                }
                                >div{
                                    margin-bottom: 4px;
                                }
                            }
                            .item-wrapper:last-child{
                                border-bottom: none;
                            }
                        }
                    }
                }
            }
        }
    }
    &_title{
        font-weight: 600;
        padding: 10px;
    }
    .el-timeline-item{
        padding-bottom: 0;
        padding-top: 10px;
    }
    .el-collapse{
        border-bottom: none;
    }
    .collapseCard{
        .collapseItem{
            padding-bottom: 10px;
            .collapseItem_title{
                line-height: 30px;
                font-weight: 600;
            }
        }
    }
    .collapseCard+.collapseCard{
        margin-top: 10px;
    }
    &.flexWrapper{
        display: flex;
        .lft{
            display: flex;
            flex-direction: column;
            width: 240px;
            border-right: 1px solid #dcdfe6;
            .lftWrapper{
                flex: 1;
                overflow-y: auto;
                &_item{
                    background-color: #fff;
                    padding: 10px;
                    height: 40px;
                    line-height: 20px;
                    cursor: pointer;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    word-break: break-all;
                    &.active{
                        background-color: #f0f7ff;
                    }
                    &:hover{
                        background-color: #F5F7FA;
                    }
                }
            }
        }
        .rft{
            width: calc(100% - 240px);
            flex: 1;
            padding: 20px 10px 0 10px;
            overflow-y: auto;
        }
    }
}

/* 表格不出现横向滚动条 */
   ::v-deep.el-table--scrollable-x .el-table__body-wrapper {
    overflow-x: hidden;
  }

</style>