import { constantRouterMap } from '@/router'


const permission = {
    state: {
        routers: constantRouterMap,
        addRouters: []
    },
    mutations: {
        SET_ROUTERS: (state, routers) => {
            state.addRouters = routers
            state.routers = constantRouterMap.concat(routers)
        }
    },
    actions: {
        // //根据页面扩展子路由（判断页面上是否有按钮需要添加对应的路由）
//         GenerateRoutes({ commit }, data) {
//             return new Promise(resolve => {
//                 var rootPaths = [
//                     // {
//                     //     path: '/',
//                     //     component: Layout,
//                     //     hidden: false,
//                     //     name: '首页',
//                     //     redirect: '/dashboard',
//                     //     children: [
//                     //         {
//                     //             path: '/dashboard',
//                     //             name: 'dashboard',
//                     //             hidden: false,
//                     //             meta: { title: '首页', icon: 'dashboard', noCache: true },
//                     //             component: () => import('@/views/dashboard/index')
//                     //         },
//                     //     ]
//                     // }
//                 ]

//                 data.modules.forEach((value, index) => { //一级菜单
//                     var parentPath = value.Item.Url
                    
//                     var firstLevelPath = {
//                         path: '/' + parentPath,
//                         component: Layout,
//                         meta: {
//                             isSys: value.Item.IsSys,
//                             moduleCode: value.Item.Code,
//                             title: value.Item.Name,
//                             icon: 'tree'
//                         },
//                         name: value.Item.Name,
//                         isMenu: value.Item.IsMenu,
//                         hidden: false,
//                         children: [

//                         ]
//                     }
//                     value.Children.forEach((child, index) => { //二级菜单
//                         let url = child.Item.Url || ''
//                         let filePath = url

//                         //有些路由需要共用组件（所以覆盖默认文件组件路径）
//                         let temp = or.override.find(s => s.routerPath.toLowerCase() == url.toLowerCase())
//                         if (temp) {
//                             filePath = temp.file
//                         }
//                         var secondLevelPath = {
//                             path: _.endsWith(url, '/index') ? url.substring(0, url.lastIndexOf('/index')) : url,
//                             meta: {
//                                 title: child.Item.Name,
//                                 isSys: child.Item.IsSys,
//                                 moduleCode: child.Item.Code,
//                                 icon: 'tree',
//                                 elements: child.Item.Elements
//                             },
//                             name: child.Item.Name,
//                             isMenu: child.Item.IsMenu,
//                             hidden: false,
//                         }

//                         if (filePath) {
//                             secondLevelPath.component = () => import('@/views' + filePath) //.toLowerCase()
//                         } else {
//                             secondLevelPath.component = DrawBoard
//                         }

                        
//                         if (child.Children && child.Children.length > 0) { 
//                             if (!secondLevelPath.children) {
//                                 secondLevelPath.children = []
//                             }

//                             child.Children.forEach((ch, index) => { //三级子菜单
//                                 let url = ch.Item.Url
//                                 let filePath = url
//                                 //有些路由需要共用组件（所以覆盖默认文件组件路径）
//                                 var thirdLevelPath = {
//                                     path: _.endsWith(url, '/index') ? url.substring(0, url.lastIndexOf('/index')) : url,
//                                     component: () => import('@/views' + filePath),//.toLowerCase()
//                                     meta: {
//                                         title: ch.Item.Name,
//                                         isSys: ch.Item.IsSys,
//                                         moduleCode: ch.Item.Code,
//                                         icon: 'tree',
//                                         elements: ch.Item.Elements
//                                     },
//                                     name: ch.Item.Name,
//                                     isMenu: ch.Item.IsMenu,
//                                     hidden: false,
//                                 }
//                                 secondLevelPath.children.push(thirdLevelPath)
//                             })
//                         }
 

//                         //特殊页面，需要关联路由（不是菜单，却需要路由）——针对菜单扩展子路由
//                         //主要针对具有按钮权限，需要弹框的场景（以前按钮都是弹框，现在有些按钮需要改为跳转（路由））
//                         let urlTemp = (child.Item.Url || '').toLowerCase()
//                         let extendPages = or.extend.find(s => s.path.toLowerCase() == urlTemp)

//                         if (extendPages) {
//                             let btns = child.Item.Elements.map(s => s.DomId)
//                             //如果 authBtnCode 为空，表示该菜单不需要授权；
//                             //否则，必须通过当前菜单下授权按钮集合里面找到扩展菜单的authBtnCode属性，才表示有授权
//                             let childrens = extendPages.pages.filter(s => !(s.authBtnCode) || (s.authBtnCode && btns.find(b => b == s.authBtnCode)))
//                             childrens.forEach((ch) => {
//                                 let pageUrlTemp = ch.pageUrl
//                                 let filePath2 = ch.pageUrl

//                                 //有些路由需要共用组件（所以覆盖默认文件组件路径）
//                                 let temp = or.override.find(s => s.routerPath.toLowerCase() == filePath2.toLowerCase())
//                                 if (temp) {
//                                     filePath2 = temp.file
//                                 }

//                                 if (filePath2.indexOf(':') > -1) {
//                                     filePath2 = filePath2.substring(0, filePath2.indexOf(':') - 1)
//                                 }
//                                 let projectSettingTitle = ch.pageTitle
//                                 let pageCode = ch.moduleCode
//                                 firstLevelPath.children.push({
//                                     path: pageUrlTemp,
//                                     IsMenu: false,
//                                     component: () => import('@/views' + filePath2),//.toLowerCase()
//                                     meta: {
//                                         title: projectSettingTitle,
//                                         isSys: false,
//                                         moduleCode: pageCode,
//                                         icon: '',
//                                         elements: []
//                                     },
//                                     name: projectSettingTitle,
//                                     isMenu: false,
//                                     hidden: true
//                                 })
//                             })
//                         }

//                         firstLevelPath.children.push(secondLevelPath)

//                     })
//                     rootPaths.push(firstLevelPath)
//                 })
//                 commit('SET_ROUTERS', rootPaths)
//                 resolve()
//             })
//         }
    }
}

export default permission
