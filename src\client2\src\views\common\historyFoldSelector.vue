<template>
  <span>
    <slot name="reference">
      <app-table-row-button
        :text="`选择最近上传的${dataType == 1 ? '文件' : '附件'}`"
        @click="handleClick"
        class="pointer"
      ></app-table-row-button>
    </slot>

    <historyList
      v-if="dialogFormVisible"
      @closeDialog="handleClose"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :checkedList="checkedList"
      :dataType="dataType"
    ></historyList>
  </span>
</template>

<script>
import historyList from "./historyList";
export default {
  components: {
    historyList,
  },
  props: {
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dataType: {
      type: Number,
      default: 1, //1：选择文件（附件集合）；2：选择附件
    },
  },
  beforeDestroy() {
    if (this.reference) {
      this.reference.removeEventListener("click", this.handleClick, false);
    }
  },
  mounted() {
    // this.pers = JSON.parse(JSON.stringify(this.list))
    let referenceObj = this.$slots.reference;
    if (referenceObj) {
      this.reference = referenceObj[0].elm;
    }
    if (this.reference) {
      this.reference.addEventListener("click", this.handleClick, false);
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      reference: undefined,
    };
  },
  methods: {
    handleClick() {
      this.dialogFormVisible = true;
    },
    handleSaveSuccess(list) {
      this.$emit("change", JSON.parse(JSON.stringify(list || [])));
      this.handleClose();
    },
    handleClose() {
      this.dialogFormVisible = false;
    },
  },
};
</script>
