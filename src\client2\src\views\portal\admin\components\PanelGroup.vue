<template>
  <div class="wrapper">
    <div class="row-container">
      <div class="card-container">
        <app-tabs :styleThemes='"card"' key="flowTabs" :items='flowTabs' :active.sync='flowTabIdx'></app-tabs>
        <div v-if="flowTabIdx == 0">
          <app-tabs key="approvalTabs" :items='approvalTabs' :active.sync='approvalTabIdx' :showOpt='true'
            @handleShowMore='showMoreApproval'></app-tabs>
          <div v-if="approvalTabIdx == 0">
            <flow-tab key="approvalTabIdx_1" :url='flowApiUrl' :condition='{status: 1}' :tabColumns='flowTabColumns'>
              <template slot="CreateDateTime" slot-scope="scope">
                {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template slot="IsFinish" slot-scope="scope">
                {{statusOptions.find(u =>u.key == scope.row.IsFinish).display_name}}
              </template>
            </flow-tab>
          </div>
          <div v-if="approvalTabIdx == 1">
            <flow-tab key="approvalTabIdx_2" :url='flowApiUrl' :condition='{status: 2}' :tabColumns='flowTabColumns'>
              <template slot="CreateDateTime" slot-scope="scope">
                {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template slot="IsFinish" slot-scope="scope">
                {{statusOptions.find(u =>u.key == scope.row.IsFinish).display_name}}
              </template>
            </flow-tab>
          </div>
          <div v-if="approvalTabIdx == 2">
            <flow-tab key="approvalTabIdx_3" :url='flowApiUrl' :condition='{status: 3}' :tabColumns='flowTabColumns'>
              <template slot="CreateDateTime" slot-scope="scope">
                {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template slot="IsFinish" slot-scope="scope">
                {{statusOptions.find(u =>u.key == scope.row.IsFinish).display_name}}
              </template>
            </flow-tab>
          </div>
        </div>
        <div v-if="flowTabIdx == 1">
          <app-tabs key="subTabs" :items='subTabs' :active.sync='submitTabIdx' :showOpt='true'
            @handleShowMore='showMoreSubmit'></app-tabs>
          <div v-if="submitTabIdx == 0">
            <flow-tab key="ubmitTabIdx_1" :url='flowApiUrl' :condition='{status: 4}' :tabColumns='flowTabColumns'>
              <template slot="CreateDateTime" slot-scope="scope">
                {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template slot="IsFinish" slot-scope="scope">
                {{statusOptions.find(u =>u.key == scope.row.IsFinish).display_name}}
              </template>
            </flow-tab>
          </div>
          <div v-if="submitTabIdx == 1">
            <flow-tab key="ubmitTabIdx_2" :url='flowApiUrl' :condition='{status: 6}' :tabColumns='flowTabColumns'>
              <template slot="CreateDateTime" slot-scope="scope">
                {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template slot="IsFinish" slot-scope="scope">
                {{statusOptions.find(u =>u.key == scope.row.IsFinish).display_name}}
              </template>
            </flow-tab>
          </div>
          <div v-if="submitTabIdx == 2">
            <flow-tab key="ubmitTabIdx_3" :url='flowApiUrl' :condition='{status: 5}' :tabColumns='flowTabColumns'>
              <template slot="CreateDateTime" slot-scope="scope">
                {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template slot="IsFinish" slot-scope="scope">
                {{statusOptions.find(u =>u.key == scope.row.IsFinish).display_name}}
              </template>
            </flow-tab>
          </div>
        </div>
      </div>

      <div class="swipper-container">
        <div class="swipper">
          <swiper :options="swiperOption" v-if="advNews && advNews.length > 0">
            <swiper-slide v-for="(item, idx) in advNews" :key="idx">
              <router-link :to="{path: '/news/detail/' + item.NewsContentId}">
                <img class="swiper-img" :src="item.PcImage" alt="">
                <div class="swiper-title">{{ item.Title }}</div>
              </router-link>
            </swiper-slide>
            <div class="swiper-pagination" slot="pagination"></div>
            <!-- <div class="swiper-button-prev" slot="button-prev"></div>
              <div class="swiper-button-next" slot="button-next"></div>
              <div class="swiper-scrollbar" slot="scrollbar"></div> -->
          </swiper>
        </div>
      </div>
    </div>
    <div class="row-container">
      <div class="card-container">
        <app-tabs :styleThemes='"card"' key="newTabs" :items='newTabs' :active.sync='newTabIdx' :showOpt='true'
          @handleShowMore='showMoreNews'></app-tabs>
        <template v-if="newTabIdx == 0">
          <flow-tab key="newTabIdx_1" reqType='get' :url="busServiceAreaName + '/News/GetIndustryNewsPage'"
            :condition='{publishObjectEnum: 2}' :tabColumns='newsTabColumns'>
            <template slot="Title" slot-scope="scope">
              <router-link :to="{path: '/news/detail/' + scope.row.NewsContentId}">{{ scope.row.Title }}</router-link>
            </template>
            <template slot="UpdateTime" slot-scope="scope">
              {{ scope.row.UpdateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </flow-tab>
        </template>

        <template v-if="newTabIdx == 1">
          <flow-tab key="newTabIdx_2" reqType='get' :url="busServiceAreaName + '/News/GetCompanyNewsPage'"
            :tabColumns='newsTabColumns'>
            <template slot="Title" slot-scope="scope">
              <router-link :to="{path: '/news/detail/' + scope.row.NewsContentId}">{{ scope.row.Title }}</router-link>
            </template>
            <template slot="UpdateTime" slot-scope="scope">
              {{ scope.row.UpdateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </flow-tab>
        </template>
      </div>
      <div class="swipper-container">
        <!-- <div class="title">
          公告
        </div> -->
        <app-tabs :styleThemes='"card"' key="noticeTabs" :items='noticeTabs' :active.sync='noticeTabIdx' :showOpt='true'
          @handleShowMore='showMoreNotice'></app-tabs>

        <flow-tab key="noticeTabs_1" :url="busServiceAreaName + '/Notice/GetListPage'" :tabColumns='noticeTabColumns'>
          <template slot="Title" slot-scope="scope">
            <router-link :to="{path: '/notice/detail/' + scope.row.NoticeId}">{{ scope.row.Title }}</router-link>
          </template>
          <template slot="ResourceUrl" slot-scope="scope">
            <!-- 该方式下载图片有问题 -->
            <a v-show="scope.row.ResourceUrl" target="_black" :href="scope.row.ResourceUrl"><img class="icon-down"
                src="../../../../assets/images/下载.png" alt=""></a>
          </template>
        </flow-tab>
      </div>
    </div>
    <div class="start-menu-wrapper">
      <div class="menu-item-list-wrapper">
        <!-- <div class="tags-view-container">
            <scroll-pane class='tags-view-wrapper' ref='scrollPane'>
              <router-link ref='tag' class="tags-view-item" :class="isActive(tag)?'active':''" v-for="tag in Array.from(visitedViews)"
                :to="tag" :key="tag.path" @contextmenu.prevent.native="openMenu(tag,$event)">
                {{tag.title}}
                <span class='el-icon-close' @click.prevent.stop='closeSelectedTag(tag)'></span>
              </router-link>
            </scroll-pane>
          </div> -->

        <div class="menu-item-list">
          <scroll-pane class='tags-view-wrapper' ref='scrollPane'>
            <div class="menu-item" v-for="(item, idx) in bookmarks" @click='openWindow(item.Url)' :key="idx"
              @mouseover.prevent="() => delIdx = idx" @mouseout.prevent="() => delIdx = -1">
              <img :src="item.Icon" class="icon-plus" :title="item.Name">
              <i v-show="delIdx == idx" class="file-btn-del el-icon-remove"
                @click.stop="removeFile(item.BookmarkId)"></i>
            </div>
            <div class="menu-item" @click="handleCreate">
              <img src="../../../../assets/images/add-icon.png" class="icon-plus" alt="">
            </div>
          </scroll-pane>
        </div>
      </div>
      <div class="qrcode-wrapper">
        <img src="../../../../assets/images/qrcode.png" alt="">
      </div>
    </div>

    <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible" :close-on-click-modal='false'>
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="100px">
        <el-row>
          <el-col>
            <el-form-item :label="'名称'" prop="Name">
              <el-input v-model.trim="temp.Name"></el-input>
            </el-form-item>
            <el-form-item :label="'跳转地址'" prop="Url">
              <el-input v-model.trim="temp.Url" placeholder="http(s)://"></el-input>
            </el-form-item>
            <el-form-item :label="'图片'" prop="Icon">
              <app-upload-file :max='1' :fileType='1' :value='fileList' :fileSize='1024 * 1024 * 1'
                @change='handleUpChange'></app-upload-file>
              <span>建议尺寸：80px * 80px</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
        <el-button size="mini" v-if="dialogStatus=='create'" type="primary" @click="createData">确认</el-button>
        <el-button size="mini" v-else type="primary" @click="createData">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  // import CountTo from 'vue-count-to'
  import * as news from '@/api/news'
  import * as notice from '@/api/notice'
  import * as flowinstances from '@/api/flowInstances'
  import elDragDialog from '@/directive/el-dragDialog'
  import ScrollPane from '@/components/ScrollPane'
  import * as br from '@/api/bookmark'
  import * as utils from '@/utils/index'
  import indexPageMixin from '@/mixins/indexPage'
  import FlowTab from './FlowTab'
  import { regs } from '@/utils/regs'
  import { serviceArea } from '../../../../api/serviceArea'

  let serviceAreaName = serviceArea.business

  export default {
    components: {
      // CountTo,
      ScrollPane,
      FlowTab,
    },
    directives: {
      elDragDialog
    },
    filters: {
      statusFilter(isFinish) {
        const statusMap = {
          0: 'color-info',
          1: 'color-success',
          2: 'color-danger',
          3: 'color-danger',
          4: 'color-danger'
        }
        return statusMap[isFinish]
      }
    },
    mixins: [indexPageMixin],
    data() {
      return {
        busServiceAreaName: serviceAreaName,
        flowApiUrl: `${serviceAreaName}/Flow/FindFlowInstanceByPage`,
        delIdx: -1,
        // activeName: 'second',
        idsNews: [],//行业新闻
        cmpNews: [],//公司新闻
        advNews: [],//轮播图
        notices: [],//公告
        swiperOption: {
          //页码
          pagination: '.swiper-pagination',
          loop: true,
          autoplay: {
            disableOnInteraction: false
          },
        },
        flowTabs: ['我的审批', '我的提交'],
        flowTabIdx: 0,

        approvalTabs: ['待审批', '已审批', '抄送我'],
        approvalTabIdx: 0,

        subTabs: ['待审批', '待提交', '已审批'],
        submitTabIdx: 0,

        newTabs: ['行业新闻', '公司新闻'],
        newTabIdx: 0,

        noticeTabs: ['公告'],
        noticeTabIdx: 0,
        flowTabColumns: [
          {
            attr: { prop: 'CustomName', label: '名称' },
          },
          {
            attr: { prop: 'ActivityName', label: '当前活动节点' },
          },
          {
            attr: { prop: 'CreateDateTime', label: '创建时间' },
            slot: true
          },
          {
            attr: { prop: 'IsFinish', label: '状态' },
            slot: true
          },
        ],
        newsTabColumns: [
          {
            attr: { prop: 'Title', label: '标题' },
            slot: true
          },
          {
            attr: { prop: 'UpdateTime', label: '发布时间', width: '180px' },
            slot: true
          },
        ],
        noticeTabColumns: [
          {
            attr: { prop: 'Title', label: '标题' },
            slot: true
          },
          {
            attr: { prop: 'ResourceUrl', label: '下载', width: '80px' },
            slot: true
          },
        ],

        // appApprovalList: [],//我的审批——待审批 status: 1
        // appApprovaledList: [],//我的审批——已审批 status: 2
        // appCCList: [],//我的审批——抄送我 status: 3

        // subWaitApprovalList: [],//我的提交——待审批 status: 4
        // subWaitSubmitList: [],//我的提交——待提交 status: 6
        // subApprovaledList: [], //我的提交——已审批 status: 5

        bookmarks: [],//快捷方式
        textMap: {
          update: '编辑',
          create: '添加'
        },
        temp: {
          BookmarkId: '',
          Url: '',//跳转地址
          Name: '',//快捷方式名称
          Icon: '',//快捷方式图片
          IconId: '',//快捷方式图片编号
        },
        rules: {
          Name: { fieldName: '名称', rules: [{ required: true }] },
          Url: { fieldName: '跳转地址', rules: [{ required: true }, { reg: regs.url }] },
          Icon: { fieldName: '图片', rules: [{ required: true }] },
        },
        dialogStatus: '',
        dialogFormVisible: false,
        fileList: [], //附件信息[{Id: '', Path: ''}]
        statusOptions: [
          {
            key: 0,
            display_name: '正在运行'
          },
          {
            key: 1,
            display_name: '完成'
          },
          {
            key: 2,
            display_name: '不通过'
          },
          {
            key: 3,
            display_name: '驳回'
          },
          {
            key: 4,
            display_name: '撤销'
          }
        ],
      }
    },
    created() {
      // this.getIndustryNews()
      // this.getCmpNew()
      this.getAdvs()
      // this.getNotices()
      this.getBookmarks()
      this.rules = this.initRules(this.rules)
    },
    methods: {
      // handleClick(tab, event){
      //   this.activeName = tab.name
      // },
      handleSetLineChartData(type) {
        this.$emit('handleSetLineChartData', type)
      },
      showMoreNews() {
        if (this.newTabIdx == 0) {
          //行业新闻id（跳转到新闻列表页，保存条件）
          this.recordConditions({ path: '/news/index', paras: { NewsClassId: '465DD4BA-A4A0-43D0-8485-0F2F0BED9EC3' } })
          this.$router.push('/news/index')
        } else if (this.newTabIdx == 1) {
          //公司新闻id
          this.recordConditions({ path: '/news/index', paras: { NewsClassId: '53CD1DE0-8C4B-4CCF-8796-61C6A34C8ED0' } })
          this.$router.push('/news/index')
        }
      },
      showMoreNotice() {
        this.$router.push('/notice/index')
      },
      showMoreSubmit() {
        let routerName = '/flowInstances/my-submit/'
        if (this.submitTabIdx == 0) {
          routerName += 'pending'
        } else if (this.submitTabIdx == 1) {
          routerName += 'waiting'
        } else if (this.submitTabIdx == 2) {
          routerName += 'approved'
        }
        this.$router.push(routerName)
      },
      showMoreApproval() {
        let routerName = '/flowInstances/my-approve/'
        if (this.approvalTabIdx == 0) {
          routerName += 'waiting'
        } else if (this.approvalTabIdx == 1) {
          routerName += 'disposed'
        } else if (this.approvalTabIdx == 2) {
          routerName += 'cc'
        }
        this.$router.push(routerName)
      },
      resetTemp() {
        this.temp = {
          BookmarkId: '',
          Url: '',//跳转地址
          Name: '',//快捷方式名称
          Icon: '',//快捷方式图片
          IconId: '',//快捷方式图片编号
        }
        this.fileList = []
      },
      handleCreate() {
        this.resetTemp()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      handleUpdate(row) { // 弹出编辑框

        let id = row.BookmarkId
        br.detail({ id: id }).then(response => {
          this.temp = response
          this.fileList = []
          if (this.temp.Icon) {
            this.fileList = [{ Id: this.temp.IconId, Path: this.temp.Icon }]
          }
          this.dialogStatus = 'update'
          this.dialogFormVisible = true
          this.$nextTick(() => {
            this.$refs['dataForm'].clearValidate()
          })
        })
      },

      // renderHeader(h, { column, $index },index) {
      //   return h('span', {}, '序号')

      //   //  return h('span', {}, [
      //   //   h('span', {}, '时间片段'),
      //   //   h('el-popover', { props: { placement: 'top-start', width: '200', trigger: 'hover', content: '领先/落后品类=单店平均单量-该品类城市店均单量' }}, [
      //   //      h('i', { slot: 'reference', class:'el-icon-question'}, '')
      //   //     ])
      //   //  ])
      // },
      // getFlowList(status) {
      //   flowinstances.getList({
      //     PageIndex: 1,
      //     PageSize: 5,
      //     Status: status
      //   }).then(response => {
      //     if(status == 1){
      //       this.appApprovalList = response.Items
      //     }else if(status == 2){
      //       this.appApprovaledList = response.Items
      //     }else if(status == 3){
      //       this.appCCList = response.Items
      //     }else if(status == 4){
      //       this.subWaitApprovalList = response.Items
      //     }else if(status == 5){
      //       this.subApprovaledList = response.Items
      //     }else if(status == 6){
      //       this.subWaitSubmitList = response.Items
      //     }
      //   }).catch(err => {

      //   })
      // },
      //获取行业新闻
      // getIndustryNews() {
      //   news.getIndustryNews({
      //     PageIndex: 1,
      //     PageSize: 5,
      //     publishObjectEnum: 2
      //   }).then(res => {
      //     this.idsNews = res.Items
      //   })
      // },
      //获取公司新闻
      // getCmpNew() {
      //   news.getCmpNew({
      //     PageIndex: 1,
      //     PageSize: 5
      //   }).then(res => {
      //     this.cmpNews = res.Items
      //   })
      // },
      //获取轮播图新闻
      getAdvs() {
        news.getAdvs().then(res => {
          this.advNews = res
        })
      },
      //获取公告
      // getNotices() {
      //   notice.getList({
      //     PageIndex: 1,
      //     PageSize: 5
      //   }).then(res => {
      //     this.notices = res.Items
      //   })
      // },
      getBookmarks() {
        br.getList({
          PageIndex: 1,
          PageSize: 30
        }).then(res => {
          this.bookmarks = res.Items
        })
      },
      handleUpChange(imgs) {
        if (imgs && imgs.length > 0) {
          this.temp.Icon = imgs[0].Path
          this.temp.IconId = imgs[0].Id
        } else {
          this.temp.Icon = ''
          this.temp.IconId = ''
        }
      },
      createData() { // 保存提交
        let self = this;
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let formData = JSON.parse(JSON.stringify(self.temp))
            let res = null
            if (self.dialogStatus == 'create') {
              delete formData.BookmarkId
              res = br.add(formData)
            } else if (self.dialogStatus == 'update') {
              res = br.edit(formData)
            }
            if (res) {
              res.then((response) => {
                self.dialogFormVisible = false
                self.$notify({
                  title: '成功',
                  message: '操作成功',
                  type: 'success',
                  duration: 2000
                })
                this.getBookmarks()
              })
            }

          }
        })
      },
      removeFile(id) {
        this.$confirm('是否确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          br.del([id]).then(() => {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getBookmarks()
          })
        })
      },
      download(url) {
        console.log(url)
        utils.downloadFile(url)
      },
      openWindow(url) {
        console.log(url)
        window.open(url, '_blank')
      }
    }
  }
</script>

<style scoped>
  .wrapper {
    min-width: 1000px;
  }

  .row-container {
    position: relative;
    padding-right: 480px;
    margin-bottom: 20px;
    height: 400px;
  }

  .row-container:first-child {
    height: 456px;
  }

  .card-container {
    background: #fff;
    margin-right: 20px;
    height: 100%;
  }

  .swipper-container {
    position: absolute;
    width: 480px;
    height: 100%;
    top: 0;
    right: 0;
    background-color: #fff;
  }

  .swipper-container .title {
    height: 56px;
    line-height: 56px;
    background: #fafafa;
    padding-left: 20px;
    color: #000034;
    font-weight: 600;
    font-size: 20px;
  }


  .el-tabs--border-card {
    background-color: #fff;
    border: none;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0.12), 0 0 0px 0 rgba(0, 0, 0, 0.04);
  }

  .date-col {
    width: 120px;
  }


  .wrapper .my-tab th {
    color: #000034;
  }

  .wrapper .my-tab th,
  td {
    color: #6f7799;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .wrapper .my-tab td,
  th {
    border-bottom: 1px solid #E4E7ED;
    height: 54px;
    line-height: 54px;
    box-sizing: border-box;
  }

  .wrapper .my-tab tr:last-of-type td {
    border-bottom: none;
  }

  .wrapper .my-tab .full-time {
    min-width: 180px;
    text-align: center;
  }

  .wrapper .my-tab {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    /* table-layout: fixed; */
    table-layout: fixed;
    width: 100%;
  }


  .swipper {
    overflow: hidden;
    width: 100%;
    height: 456px;
    background: #fff;
    position: relative;
  }

  .swipper .swiper-img {
    width: 100%
  }

  .swiper-slide {
    height: 456px;
  }

  .swiper-title {
    height: 60px;
    line-height: 60px;
    background: #8b92a3;
    color: #fff;
    padding: 0 20px;
    font-size: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .start-menu-wrapper {
    position: relative;
  }

  .menu-item-list-wrapper {
    height: 100px;
    width: 100%;
    border-radius: 4px;
    padding-right: 120px;
  }

  .menu-item-list .tags-view-wrapper {
    background: #fff;
    height: 100px;
    padding: 10px 0;
  }

  .menu-item,
  .qrcode-wrapper {
    box-sizing: border-box;
    border-radius: 4px;
  }

  .menu-item {
    border: 1px solid #dedfe4;
    margin-left: 10px;
    height: 80px;
    ;
    width: 80px;
    position: relative;
    display: inline-block;
  }

  .menu-item img {
    height: 48px;
    width: 48px;
    transition: all .5s;
  }

  .menu-item img:hover {
    transform: scale(1.1);
  }

  .menu-item .icon-plus {
    position: absolute;
    left: -24px;
    margin-left: 50%;
    top: -24px;
    margin-top: 50%;
    cursor: pointer;
  }

  .qrcode-wrapper {
    background: #fff;
    width: 100px;
    height: 100px;
    position: absolute;
    right: 0;
    top: 0;
  }

  .qrcode-wrapper img {
    width: 90px;
    height: 90px;
    margin: 5px;
  }

  .file-btn-del {
    background: transparent;
    display: block;
    cursor: pointer;
    position: absolute;
    font-size: 18px;
    top: -6px;
    right: -6px;
  }

  .file-btn-del:hover {
    transition: all 0.3s;
    color: red;
  }

  .icon-down {
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
  }

  /* 
.a-l{
  text-align: left;
}

.a-c{
  text-align: center;
} */
</style>