<template>
    <el-dialog v-el-drag-dialog class="dialog-mini" width="1030px" :title="dialogTitle" :visible.sync="isVisible"
        :before-close="handleClose" :close-on-click-modal="false" :append-to-body="true">
        <div style="max-height:600px;overflow-y:auto;" >
          <!-- style="max-height:800px;overflow-y:auto;" -->
            <!-- <ul id="demo">
                <tree-item
                    class="item"
                    :item="treeDatas2"
                    @make-folder="makeFolder"
                    @add-item="addItem"
                ></tree-item>
            </ul> -->
            <el-tree
                :data="treeDatas"
                :props="defaultProps"
                ref="tree"
                :expand-on-click-node="false"
                default-expand-all
                :highlight-current='true'
                node-key="key"
            >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span>
                        <span class="node-label" :title="node.key">
                            <span>
                               {{ data.name }} :
                               <i v-if="data.remark" class="el-icon-question" :title="data.remark"></i>
                                 <!-- {{formatToDescription(node,data) }} -->
                            </span>
                            <span>
                                <!-- {{ data.text }} -->
                                <!-- <el-input  placeholder="请输入内容"  v-model="data.text"></el-input> -->
                            </span>
                        </span>
                    </span>
                    <span v-if="!data.fieldType">
                        <input type="text" v-model="data.text" v-show="!data.children" />
                    </span>
                    <span v-if="data.fieldType">
                         <!-- <input type="text"  v-model="data.text" v-show="!data.children" /> -->
                        <!-- <textarea v-model="data.text" type="textarea" :rows="4" cols="4" v-show="!data.children"></textarea> -->
                       <el-button type="text" v-show="!data.children && data.fieldType" @click="() => edit(data)">编辑</el-button>
                    </span>
                </span>
            </el-tree>

            <el-dialog class="dialog-mini" width="800px" :title="'编辑'"
                :visible.sync="dialogSubFormVisible" :close-on-click-modal='false' :append-to-body='true'>
                <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="100px">
                    <el-row>
                        <el-col :span="24">
                            <!-- <el-form-item :label="'key'" prop="key">
                                <el-input disabled="disabled" v-model="temp.key"></el-input>
                            </el-form-item> -->
                            <el-form-item>
                              <el-input  type="textarea" :row="5" v-model="temp.text"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer">
                    <el-button size="mini" @click="dialogSubFormVisible = false">取消</el-button>
                    <el-button size="mini" type="primary" @click="createData">确认</el-button>
                </div>
            </el-dialog>
        </div>
        <div slot="footer">
            <el-button size="mini" @click="handleClose">取消</el-button>
            <el-button size="mini" type="primary" :loading='postLoading' @click="saveChange">确认</el-button>
        </div>
    </el-dialog>
</template>

<script>
// import TreeItem from './treeItem'
import * as ec from '@/api/enterpriseConfigure'
import elDragDialog from "@/directive/el-dragDialog";
import { JsonHubProtocol } from '@aspnet/signalr';
export default {
    name: 'config-mgmt-edit',
    directives: {
        elDragDialog
    },
    components: {
        // TreeItem,
    },
    computed: {
        dialogTitle() {
            if (this.dialogStatus == "create") return "添加";
            else if (this.dialogStatus == "update") return "编辑";
            else if (this.dialogStatus == "detail") return "详情";
            else return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑)
        dialogStatus: {
            required: true,
            type: String
        },
        //新增(编辑)弹框是否显示
        dialogFormVisible: {
            required: true,
            type: Boolean
        },
        id: {
            //主任务id
            required: true,
            default: ""
        },
    },
    created() {
        this.isVisible = this.dialogFormVisible;
    },
    watch: {
        isVisible(val) {
            if(this.id && val) {
                ec.detail({key: this.id}).then(res => {
                    this.treeDatas = this.formatToTreedata(res.model,res.result)
                })
            }
        }
    },
    data() {
        return {
            isVisible: false,
            dialogSubFormVisible: false,
            rules: {},
            postLoading: false,
            temp: {
                key: '',
                text: ''
            },
            defaultProps: {
                children: 'children',
                label: 'name',
            },
            treeDatas: [],
        }
    },
    mounted() {
        this.treeDatas = []
    },
    methods: {
        formatToJsonData(_data) {
            var obj = {};
            for(var i in _data) {
                let currentKey = _data[i]['key']
                let currentValue = _data[i]['text']
                let children = _data[i]['children']
                if(!children) {
                    obj[currentKey] = currentValue
                }else{
                    obj[currentKey] = this.formatToJsonData(children)
                }
            }

            return obj;
        },
        formatToTreedata(_data,_descs) {
            let obj = [];
            let validTyps = ['string','number', 'boolean']

            for(var i in _data) {
                let currentVal = _data[i]
                let currentValType = typeof(currentVal) //当前值类型
                let isValidType = validTyps.indexOf(currentValType) > -1 //当前值是否是简单类型（字符串、数字、布尔值）

                var curentName=i;
                var currentRemark="";
                var fieldType="";
                for (var j in _descs) {
                  if(i==_descs[j]['Key']){
                    curentName=_descs[j]['Description']
                    currentRemark=_descs[j]['Remark']
                    fieldType=_descs[j]['FieldType']
                  }
                }

                if(this.isObject(currentVal)) {
                    obj.push({name: curentName, key: i, text: isValidType ? currentVal : '',remark:currentRemark,fieldType:fieldType, children: this.formatToTreedata(currentVal,_descs)})
                }else{
                    obj.push({name: curentName, key: i, text: isValidType ? currentVal : '',remark:currentRemark,fieldType:fieldType})
                }
            }
            return obj;
        },
        isObject(obj) {
            return Object.prototype.toString.call(obj) === '[object Object]'
        },
        edit(data) {
            this.temp = Object.assign({}, this.temp, data)
            this.openDialog()
        },
        openDialog() {
            this.dialogSubFormVisible = true
        },
        closeDialog() {
            this.dialogSubFormVisible = false
        },
        createData() {
            let currentNode = this.$refs.tree.getCurrentNode()
            currentNode.text = this.temp.text
            this.$refs.tree.setCurrentNode(currentNode)
            this.closeDialog()
        },
        saveChange() {
            let postDatas = this.formatToJsonData(JSON.parse(JSON.stringify(this.treeDatas)))
            ec.edit(postDatas).then(res => {
                this.$notify({
                    title: '成功',
                    message: '保存成功',
                    type: 'success',
                    duration: 2000
                })
                this.$emit("reloading");
                this.$emit("closeDialog");
            }).catch(err => {

            })

        },
        handleClose() {
            this.cancel()
        },
        cancel() {
            this.$emit("closeDialog");
        },
    }
}
</script>


<style scoped>

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

.node-label{
    display: inline-block;
    max-width: 440px;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.custom-tree-node input{
  width: 580px;
  border: 1px solid #DCDFE6;
  color: #606266;
  padding:0 15px;
  height: 24px;
  border-radius:4px;
}
.custom-tree-node textarea{
  width: 580px;
  border: 1px solid #DCDFE6;
  color: #606266;
  padding:2 15px;
  border-radius:4px;
}
</style>
