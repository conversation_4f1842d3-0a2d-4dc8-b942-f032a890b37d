<template>
<div class="app-container supplierParts">
    <div class="bg-white">
        <!-- <page-title title="供应商管理" :subTitle="['不同类型物资供应商管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="content-left">
                <!-- <suppliersType-list>

                </suppliersType-list> -->
                <aside style="width: 100%;height: 100%;" v-loading="loading">
                    <div style="text-align: center; padding-top: 10px;margin-bottom: 10px;">
                        <el-button  v-if="btnMaintain=='btnMaintain'" type="primary" class="elButton" @click="addClassification">创建供应商分类</el-button>
                    </div>
                    <div style="border-bottom: 1px solid #DCDFE6;border-top: 1px solid #DCDFE6;overflow: auto;height: calc(100% - 102px);">
                        <tags mode="list" v-if="taskList.length>0" :items="taskList" v-model="taskId" @change="handleTagsChange" style="margin-top: 10px;">
                            <template v-for="(task,idx) in taskList" :slot="task.value">
                                <div class="item_warpper" :key="idx">
                                    <div>
                                        <span class="omit" :title="task.Name">{{task.Name}}</span>
                                        <span style="float: right;">
                                            <i  v-if="btnMaintain=='btnMaintain'" class="el-icon-edit-outline" style="color:#409EFF;" @click.stop="edit(task)"></i>
                                            <i v-if="btnMaintain=='btnMaintain'"  class="el-icon-delete" style="color:#F56C6C;" @click.stop="del(task.Id)"></i>
                                        </span>
                                    </div>
                                </div>
                            </template>
                        </tags>
                        <no-data v-else></no-data>
                    </div>
                    <div>
                        <pagination :total="total" small background :page.sync="paramModel.PageIndex" :size.sync="paramModel.PageSize" @pagination="handleCurrentChange" layout="prev, pager, next" :pager-count="5" />
                    </div>
                </aside>

            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :layoutMode='layoutMode' :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="[]" :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable="false">

                        <template slot="Settlement" slot-scope="scope">
                            <span>{{ scope.row.Settlement | SettlementFilter}}</span>
                        </template>
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :layoutMode='layoutMode' :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch">

                                <template slot="Code">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索供应商编号..."
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Code"
                                    ></el-input>
                                </template>
                                <template slot="Name">
                                    <el-input style="width: 100%;" maxlength="50" v-model="listQuery.Name" placeholder></el-input>
                                </template>
                                <template slot="Contacts">
                                    <el-input style="width: 100%;" maxlength="50" v-model="listQuery.Contacts" placeholder></el-input>
                                </template>
                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                                    <el-button  v-if="btnMaintain=='btnMaintain'" type="primary" class="elButton" style="margin-left:4px;" @click="handleDialog('create')">创建供应商</el-button>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <!-- <template slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>

                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                        </template> -->
                            <template slot='operation' slot-scope="scope">
                                <el-button v-show="btnMaintain=='btnMaintain'" type="text" @click="handleReview(scope.row)">详情</el-button>
                                <el-button v-show="btnMaintain=='btnMaintain'" type="text" @click="handleUpdate(scope.row)">编辑</el-button>
                                <el-button v-show="btnMaintain=='btnMaintain'" type="text" @click="handleDelete(scope.row)" style="color:#F56C6C;">删除</el-button>
                            </template>
                    </app-table>
                </div>
                <pagination :total="total2" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange1" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    <!-- 创建分类 -->
    <create-classification-page @closeDialog='closeClassificationDialog' @saveSuccess='handleClassificationSaveSuccess' :dialogFormVisible='dialogClassificationFormVisible' :id="cid" :dialogStatus="dialogClassificationFormStatus">
    </create-classification-page>
    <!-- 创建 -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" :cid="cid" @reload="getList"></create-page>
</div>
</template>

<script>
import createClassificationPage from "./createClassification";
import * as supplierManagement from "@/api/afterSalesMgmt/supplierManagement";
import suppliersTypeList from "./common/suppliersTypeList";
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import NoData from "@/views/common/components/noData";
import {
    SettlementEnum,
    InsizeEnum,
    NatureEnum
} from "../enum";

export default {
    name: "",
    components: {
        createPage,
        createClassificationPage,
        NoData,
        suppliersTypeList
    },

    mixins: [indexPageMixin],
    created() {
        //   this.getList();
          this.btnTextValue()
    },
    filters: {
        SettlementFilter(status) {

            const statusObj = SettlementEnum.find((s) => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "";
        },

    },
    /**渲染后 */
    mounted() {
        this.getTypeList();
    },
    data() {
        return {
            layoutMode: 'simple',
            btnMaintain:'',
            id: "",
            taskId: "",
            dialogClassificationFormVisible: false,
            cid: "",
            taskList: [],
            loading: false,
            paramModel: {
                PageIndex: 1,
                PageSize: 20,
            },
            dialogStatus: "create",
            dialogClassificationFormStatus: "create",
            dialogFormVisible: false,
            tableSearchItems: [{
                    prop: "Code",
                    label: "供应商编号",
                    mainCondition: true
                },
                {
                    prop: "Name",
                    label: "供应商名称",
                },
                {
                    prop: "Contacts",
                    label: "供应商联系人",
                },
            ],
            tabColumns: [{
                    attr: {
                        prop: "Code",
                        label: "供应商编号",

                    }
                },

                {
                    attr: {
                        prop: "Name",
                        label: "供应商名称",

                        showOverflowTooltip: true
                    }
                },
                {
                    attr: {
                        prop: "Contacts",
                        label: "联系人",

                    }
                },
                {
                    attr: {
                        prop: "Phone",
                        label: "联系人电话",

                    }
                },
                {
                    attr: {
                        prop: "Settlement",
                        label: "结算方式",

                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "operation",
                        label: "操作",
                    },
                    slot: true
                },
            ],
            cdMsg: null,
            tabDatas: [],
            listLoading: false,
            listQuery: {
                // 查询条件
                Name: "",
                PageIndex: 1,
                PageSize: 20,
                Code: "", //SuppliersTypeId:null,
                Contacts: "",
                SuppliersTypeId: '',
                // Settlement: 0,
                // Phone: "",
            },
            total: 0,
	    total2: 0,
        };
    },

    methods: {
   btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },

        handleTagsChange(d) {
            this.cid = d;
            this.listQuery.SuppliersTypeId = d;
            this.listQuery.PageIndex = 1;
            this.getList();

        },
        //添加分类
        addClassification() {
            this.dialogClassificationFormStatus = 'create'
            this.dialogClassificationFormVisible = true;
        },
        /**分页页码切换 */
        handleCurrentChange(val) {
            this.paramModel.PageIndex = val.page;
            this.paramModel.PageSize = val.size;
            this.getTypeList();
        },
        getTypeList() {
            this.loading = true;
            let postData = {
                "pageIndex": this.paramModel.PageIndex,
                "pageSize": this.paramModel.PageSize,

            }
            supplierManagement.getListType(postData).then(res => {
                this.loading = false;
                this.total = res.Total;
                this.taskList = res.Items;
                if (this.taskList.length > 0) {
                    this.cid = this.taskList[0].Id;
                    this.taskId = this.taskList[0].Id;
                    this.listQuery.SuppliersTypeId = this.taskList[0].Id;
                    this.getList();
                }

                this.taskList.forEach(v => {
                    v.value = v.Id;
                })
            }).catch(err => {
                this.loading = false;
            })
        },
        onResetSearch() {
            this.listQuery.Code = ''
            this.listQuery.Name = ''
            this.listQuery.Contacts = ''
            this.getList() //刷新列表
        },

        onBtnClicked: function (domId) {
            switch (domId) {
                //Add
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                default:
                    break;
            }
        },
        //查看详情
        handleReview(row, optType = "detail") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        // 弹出编辑框
        handleUpdate(row, optType = "update") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },

        // 弹出编辑框
        edit(row, optType = 'update') {
            this.cid = row.Id;
            this.dialogClassificationFormStatus = optType;
            this.dialogClassificationFormVisible = true;

        },
        del(d) {
            this.$confirm('确定删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                supplierManagement.delType([d]).then(res => {
                    this.$notify({
                        title: '成功',
                        message: '移除成功！',
                        type: 'success'
                    });
                    this.getTypeList();
                })
            }).catch(() => {

            });
         //   this.getTypeList()
        },
        getList() {
            this.listLoading = true;

            let postData = JSON.parse(JSON.stringify(this.listQuery));

            supplierManagement.getList(postData).then(res => {
                this.listLoading = false;
                this.total2 = res.Total;
                this.tabDatas = res.Items;

            });
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange1(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        closeClassificationDialog() {
            this.dialogClassificationFormVisible = false
        },
        handleClassificationSaveSuccess() {
            this.getTypeList()
            this.closeClassificationDialog()
        },
        // 多行删除
        handleDelete(rows) {
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }

            this.$confirm("是否删除供应商?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                supplierManagement.del(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: calc(100% - 48px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 191px;
        //  border-right: 1px solid #dcdfe6;

        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            padding: 10px;
            padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

.content-left {
    border-right: 1px solid #dcdfe6;
    // width: 15%;
    width: 250px;
}

.item_warpper {
    >div:nth-child(1) {
        display: flex;
        justify-content: space-between;

        >span:nth-child(1) {
            width: 156px;
        }
    }

    >div:nth-child(2) {
        display: flex;
        margin-top: 16px;

        span {
            border: 1px solid #aaaaaa;
            color: #aaaaaa;
            padding: 2px;
            border-radius: 6px;
        }

        >span:first-child {
            margin-right: 5px;
        }
    }
}
</style>
