<template>
<div class="page-content">
    <app-dialog title="调整奖品分类" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='400'>
        <template slot="body">
            <el-form ref="formData" :rules="rules" :model="formData" label-position="right" label-width="100px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="奖品类型" prop="ClassifyId">
                            <treeselect key='type2'
                                class="treeselect-common"
                                :append-to-body="true"
                                :normalizer="unitNormalizer"
                                v-model="formData.ClassifyId" :default-expand-level="3"
                                :options="treeDatas" :multiple="false" placeholder='' :show-count="true"
                                :noResultsText='noResultsTextOfSelTree'
                                :noOptionsText="noOptionsTextOfSelTree"
                                zIndex='9999'
                                @input="hadnleChangeCustomerUnitId"
                                >
                            </treeselect>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="handleSave" :buttonType="1" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as pointExchange from "@/api/knowledge/pointExchange";
import { listToTreeSelect } from "@/utils"
import * as classify from '@/api/classify'

export default {
    name: "tag-type-change",
    directives: {},
    components: {
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        ids: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    filters: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.loadTreeData();        
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    mounted() {
    },
    data() {
        return {
            disabledBtn: false,
            treeDatas: [],
            formData: {
                ClassifyId: null
            },
            rules: {
                ClassifyId:{
                    fieldName: "奖品类型",
                    rules: [{ required: true, trigger: 'change' }]
                },
            },
            unitNormalizer(node) {
                // treeselect定义字段
                return {
                    id: node.Id,
                    label: node.Name,
                    children: node.children
                }
            },
        };
    },
    methods: {
        hadnleChangeCustomerUnitId() {
            this.$refs.formData.validateField("ClassifyId");
        },
        loadTreeData() {
            // let _this = this;
            // let paramData = {
            //     PageSize: 10000,
            //     PageIndex: 1
            // };
            // customerTagClassify.getListPage(paramData).then(res => {
            //     let response = res.Items
            //     _this.treeDatas = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构
                
            // })

            let postDatas = {
                PageIndex: 1,
                PageSize: 10000,
                BusinessType: 11
            }
            classify.getListPage(postDatas).then(res => {
                var list = (res.Items || []).map((item, index, input) => {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId
                    };
                });
                this.treeDatas = listToTreeSelect(list, undefined, undefined, undefined, 'Name');
            });

        },
        handleSave() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    let postDatas = {
                        IdList: this.ids,
                        ClassifyId: this.formData.ClassifyId
                    }
                    this.disabledBtn = true
                    pointExchange.modifyClassifyId(postDatas).then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData()
                    }).catch(err => {
                        this.disabledBtn = false
                    })

                }
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>


<style lang="scss" scoped>
.page-content{
    padding: 10px 0;
    min-height: 300px;
    .list-wrapper{
        margin-top: 10px;
    }
}

</style>
