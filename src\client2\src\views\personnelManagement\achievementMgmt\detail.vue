<template>
    <div class="app-container">
        <div class="bg-white">
            <page-title :title="pateTitle" :showBackBtn="true" @goBack="handleGoBack"></page-title>
            <div class="page-main-wrapper cl">
                <div class="content-left fl">
                    <tags
                        v-show="listData.length > 0"
                        class="tagsList"
                        mode="list"
                        :items="listData"
                        v-model="currentTag"
                    >
                        <template v-for="(v, idx) in listData" :slot="v.value" slot-scope="scope">
                        <div class="item-warpper" :key="idx">
                            <div
                            class="item-title omit"
                            :title="v.label"
                            >{{(v.label ? v.label : '无')}}</div>
                        </div>
                        </template>
                    </tags>
                </div>
                <div class="content-right fl">
                    <div class="page-content-wrapper" v-loading='detailLoading'>
                        <div class="steps-wrapper">
                            <el-steps :space="200" :active="currentStep" align-center finish-status="finish" process-status="process">
                                <el-step 
                                    :class="{'step-active': currentStep == t.value}" 
                                    v-for="(t, idx) in steps" 
                                    :key="idx" 
                                    :title="t.title" 
                                    :status='t.status' 
                                    @click.native="setCurrentStep(t.value, false)"
                                    >
                                    <template slot="description" slot-scope="scope">
                                        <div v-if="t.value == 2 || t.value == 3 || t.value == 4">
                                            <!-- <span v-if="t.value == 2">
                                                截止至:{{ summaryDetail.GatherEndTime | dateFilter('YYYY-MM-DD HH:mm') }}
                                            </span> -->
                                            <!-- <span v-if="t.value == 3"> -->
                                                <!-- 截止至:{{ summaryDetail.MidtermReviewEndTime | dateFilter('YYYY-MM-DD HH:mm') }} -->
                                            <!-- </span> -->
                                            <!-- <span v-if="t.value == 4">
                                                截止至:{{ summaryDetail.AssessEndTime | dateFilter('YYYY-MM-DD HH:mm') }}
                                            </span> -->
                                        </div>
                                        <div v-else></div>
                                        <div v-if="currentTag == 'pbc'">
                                            <!-- <el-button type="primary" style="width: 180px;" :loading="loading" :disabled='loading' @click="handleSave">下一步</el-button> -->
                                            <template v-if="t.value == 2 && currentStep == 2 && summaryDetail.ProgressStatus == 2">
                                                <!-- 采集个人承诺 -->
                                                <el-button v-if="summaryDetail.CurrentStepStatus == 1" type="success" @click.stop="handleSetStep(3)">
                                                    开始个人承诺采集
                                                </el-button>
                                                <el-button v-if="summaryDetail.CurrentStepStatus == 2" type="primary" @click.stop="handleFinished">
                                                    完成个人承诺采集
                                                </el-button>
                                            </template>
                                            <template v-if="t.value == 3 && currentStep == 3 && summaryDetail.ProgressStatus == 3">
                                                <!-- 启动中期审视 -->
                                                <!-- <el-button type="text" @click.stop="handleSetStep(4)" style="color: #67C23A;">
                                                    启动
                                                </el-button>
                                                <el-button type="text" v-show="summaryDetail.MidtermReviewEndTime" @click.stop="handleFinished" style="color: #409EFF;">
                                                    结束
                                                </el-button> -->
                                            </template>
                                            <template v-if="((t.value == 4 && (currentStep == 4 || currentStep == 3))) && (summaryDetail.ProgressStatus == 3 || summaryDetail.ProgressStatus == 4)">
                                                <!-- 绩效考核 -->
                                                <el-button type="success" v-if="summaryDetail.CurrentStepStatus == 1" @click.stop="handleSetStep(5)">
                                                    开始绩效考核
                                                </el-button>
                                                <el-button type="primary" v-show="summaryDetail.CurrentStepStatus == 2" @click.stop="handleFinished">
                                                    完成绩效考核
                                                </el-button>
                                            </template>
                                            <template v-if="t.value == 5 && currentStep == 5 && summaryDetail.ProgressStatus == 5">
                                                <!-- 绩效终审 -->
                                                <el-button type="primary" v-show="summaryDetail.CurrentStepStatus == 2" @click.stop="handleFinished">
                                                    完成考核计划
                                                </el-button>
                                            </template>
                                        </div>
                                    </template>
                                </el-step>
                            </el-steps>
                        </div>
                        <div class="com-height" v-if="currentTag == 'pbc'">
                            <pbc-summary :detail='summaryDetail' @reload='loadTagList'></pbc-summary>
                        </div>
                        <div class="steps-content com-height" v-else-if="currentTag && currentTag != 'pbc'">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; overflow: auto;">

                                <template v-if="currentStep == 1 && detail">
                                    <step1 :isFollow='isFollow && (isPrincipaler || isOwner)' :detail='detail' @forwardSuccess='setCurrentStep(2)'></step1>
                                </template>
                                <template v-if="currentStep == 2 && detail">
                                    <step2 :isFollow='isFollow && (isPrincipaler || isOwner)' :id="detail.Id" :progressStatus='summaryDetail.ProgressStatus' @forwardSuccess='setCurrentStep(3)' @showDetail='handleShowDetail'></step2>
                                </template>
                                <template v-if="currentStep == 3 && detail">
                                    <step3 @reload='loadTagList' :isFollow='isFollow && (isPrincipaler || isOwner)' :id="detail.Id" :progressStatus='summaryDetail.ProgressStatus' @forwardSuccess='setCurrentStep(4)' @showDetail='handleShowDetail'></step3>
                                </template>
                                <template v-if="currentStep == 4 && detail">
                                    <!-- <step4 :isFollow='isFollow' :detail='detail' @forwardSuccess='(syncData) => setCurrentStep(6, true, syncData)' @showDetail='handleShowDetail'></step4> -->
                                    
                                    <!-- 现在改为只需要同步数据，不需要设置步骤进度 -->
                                    <!-- summaryDetail.AssessStatus -->
                                    <step4 @reload='loadTagList' :isFollow='isFollow && (isPrincipaler || isOwner)' :detail='detail' :progressStatus='summaryDetail.ProgressStatus' @forwardSuccess='(syncData) => synchData(syncData)' @showDetail='handleShowDetail'></step4>
                                </template>
                                <template v-if="currentStep == 5 && detail">
                                    <step5 @reload='loadTagList' :isFollow='isFollow && isFinaler' :detail='detail' :progressStatus='summaryDetail.ProgressStatus' :isStart='summaryDetail.ProgressStatus == 5 && summaryDetail.CurrentStepStatus == 2' @forwardSuccess='(syncData) => synchData(syncData)' @showDetail='handleShowDetail'></step5>
                                </template>
                                <template v-if="(currentStep == 6 || currentStep == 7) && detail">
                                    <step6 :isFollow='isFollow && (isPrincipaler || isOwner)' :detail='detail' @forwardSuccess='setCurrentStep(6)' @showDetail='handleShowDetail'></step6>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <common-bullet
            v-if="dialogCommonFormVisible"
            @closeDialog="() => dialogCommonFormVisible = false"
            :dialogFormVisible="dialogCommonFormVisible"
            :dialogStatus="dialogCommonFormStatus"
            :id='dialogCommonFormId'
            :disAB="true"
        ></common-bullet>

        <app-dialog
            title="提示"
            ref="appDialogRef2"
            @closeDialog="closeSettingDialog"
            @saveSuccess="handleSettingSaveSuccess"
            :dialogFormVisible="settingDialogVisible"
            :width="380"
            v-if="settingDialogVisible"
            >
            <template slot="body">
                <el-form
                    :rules="rules2"
                    ref="formData2"
                    :model="formData2"
                    label-width="100px"
                >
                    <div class="prev-dialog-wrapper">
                        <el-row>
                            <el-col>
                                <div style="text-align: center; margin-bottom: 20px;">
                                    {{ tip }}
                                </div>
                            </el-col>
                        </el-row>
                        <!-- <el-row>
                            <el-col :span="24">
                                <el-form-item label="结束时间" prop="EndTime">
                                    <el-date-picker 
                                        :picker-options="pickerOptions"
                                        @change="dateChange"
                                        v-model="formData2.EndTime" 
                                        type="datetime" 
                                        format='yyyy-MM-dd HH:mm' 
                                        value-format='yyyy-MM-dd HH:mm'
                                        placeholder=""
                                    ></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <div>
                    <!-- 取消 -->
                    <app-button @click="closeSettingDialog" :buttonType="2"></app-button>
                    <!-- 确认 -->
                    <app-button @click="handleSaveSuccess" :buttonType="1" :disabled="disabledBtn"></app-button>
                </div>
            </template>
        </app-dialog>


        
        
    </div>
</template>


<script>
import * as ach from "@/api/personnelManagement/achievementMgmt";
import step1 from './steps/step1'
import step2 from './steps/step2'
import step3 from './steps/step3'
import step4 from './steps/step4'
import step5 from './steps/step5'
import step6 from './steps/step6'
import pbcSummary from './pbc'
import commonBullet from '../../workbench/achievements/commonBullet'
import * as appraisePlanYear from "@/api/personnelManagement/appraisePlanYear"
import { getUserInfo } from "@/utils/auth";
import { yearTypeEnum, appraisePersonalsStatusEnum } from "./enum";
import dayjs from 'dayjs';

export default {
    name: "ach-mgmt-detail",
    components: {
        step1,
        step2,
        step3,
        step4,
        step5,
        step6,
        commonBullet,
        pbcSummary,
    },
    filters: {
        halfyearTypeFilter(val) {
            let obj = yearTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    computed: {
        id() {
            return this.$route.params.id;
        },
        isFollow() {
            let parts = this.$route.path.split("/");
            return !!parts.find((s) => s == "follow");
        },
        pateTitle() {
            // if (this.isFollow) {
            //     return "跟进";
            // } else {
            //     return "详情";
            // }

            if(this.summaryDetail && this.summaryDetail.Year) {
                let temp = yearTypeEnum.find(s => s.value == this.summaryDetail.HalfYearType)
                if(temp) {
                    return `${this.summaryDetail.Year} / ${temp.label}`
                }
            }
            return '加载中...'
        },
        isOwner() {
            let temp = this.summaryDetail.PrincipalEmployeeList && this.summaryDetail.PrincipalEmployeeList.findIndex(s => s.EmployeeId == getUserInfo().employeeid) > -1

            return temp
        },
        //是否为考核主管
        isPrincipaler() {
            return this.detail.PrincipalEmployeeList && this.detail.PrincipalEmployeeList.findIndex(s => s.EmployeeId == getUserInfo().employeeid) > -1
        },
        //是否为终审人
        isFinaler() {
            return this.detail.FinalEmployeeList && this.detail.FinalEmployeeList.findIndex(s => s.EmployeeId == getUserInfo().employeeid) > -1
        },
    },
    created() {
        
        // this.rules2 = this.initRules(this.rules2);
        
        // if (!this.rules2['EndTime'])
        //     this.rules2['EndTime'] = []
        // const endTimeValidate = (rule, value, callback) => {
        //     let currentTime = dayjs(dayjs().format('YYYY-MM-DD HH:mm'))
        //     let chooseTime = dayjs(value)

        //     if(!currentTime.isBefore(chooseTime)) {
        //         callback(new Error('必选选择当前时间之后的时间'))
        //     } else {
        //         callback()
        //     }
        // }
        
        // this.rules2['EndTime'].push({ validator: endTimeValidate, trigger: 'change' })

        this.loadTagList()
        
    },
    watch: {
        currentTag(val) {
            if(val != 'pbc') {
                this.getDetail()
            }
        }
    },
    mounted() {
    },
    data() {
        return {
            pickerOptions: {
                //禁止选择当前日期后的时间
                disabledDate (time){
                    return Date.now() -3600 * 1000 * 24 > time.getTime();
        　　　　 },
                // 时间选择限制
                selectableRange: `${dayjs().format('HH:mm')}:00-23:59:59`
            },
            currentTag: '',
            listData: [],
            currentStep: 1,
            summaryDetail: {},

            //success （已完成）；finish（进行中）；wait（未开始）//去掉最后一个状态
            steps: JSON.parse(JSON.stringify(appraisePersonalsStatusEnum)).slice(0, appraisePersonalsStatusEnum.length - 1).map(s => {
                return {
                    value: s.value,
                    title: s.label,
                    status: 'wait'
                }
            }),
            // steps: [
            //     {value: 1, title: '创建PBC', status: 'wait'},
            //     {value: 2, title: '个人绩效承诺', status: 'wait'},
            //     {value: 3, title: '中期审视', status: 'wait'},
            //     {value: 4, title: '绩效考核', status: 'wait'},
            //     {value: 5, title: '绩效终审', status: 'wait'},
            //     {value: 6, title: '结果公示', status: 'wait'},
            // ],
            returnUrl: this.$route.query.returnUrl,
            returnModule: this.$route.query.returnModule,
            detailLoading: false,
            detail: null,

            dialogCommonFormVisible: false,
            dialogCommonFormStatus: '',
            dialogCommonFormId: '',

            settingDialogVisible: false,
            disabledBtn: false,
            tip: '',
            enterStep: 0,
            formData2: {
                // EndTime: ''
            },
            rules2: {
                // EndTime: {fieldName: "结束时间", rules: [{ required: true, trigger: 'change' }]},
            },

            
        };
    },
    methods: {
        // dateChange: function() {
        //     var startAt = new Date(this.formData2.EndTime) * 1000 / 1000;
        //     if(startAt < Date.now()) {
        //         this.formData2.EndTime= new Date();
        //     }
        // },
        //加载左侧列表
        loadTagList() {
            appraisePlanYear.detail({ Id: this.id, IsPrincipal: true }).then(res => {

                this.summaryDetail = res

                let currentStatus = this.summaryDetail.ProgressStatus
                //如果当前状态未完成（<= 5）,那么自动定位到需要完成的状态节点；已完成：定位到第一节点
                this.setCurrentStep(currentStatus)

                let result = []
                //如果是考核负责人，则可以看到“pbc看板”
                if(res.PrincipalEmployeeIdList && res.PrincipalEmployeeIdList.findIndex(s => s == getUserInfo().employeeid) > -1) {
                    result.push({value: 'pbc', label: 'PBC看板'})
                }
                
                //考核部门列表
                if(res.AppraisePlanList && res.AppraisePlanList.length > 0) {
                    result = result.concat(res.AppraisePlanList.map(s => {
                        return {
                            value: s.Id,
                            label: s.DepartmentName
                        }
                    }) || [])
                }

                this.listData = result

                if(!this.currentTag && this.listData && this.listData.length > 0) {
                    this.currentTag = this.listData[0].value
                }

            })
            
        },
        handleGoBack() {
            this.$router.push({
                path: '/personnelManagement/achievementMgmt',
            });
        },
        getDetail() {
            let auth = false
            if(this.id) {
                this.detailLoading = true
                ach.detailPlan({Id: this.currentTag}).then(res => {
                    this.detailLoading = false
                    this.detail = Object.assign({}, this.detail, res)
                    // let currentStatus = this.detail.ProgressStatus

                    // //如果当前状态未完成（<= 5）,那么自动定位到需要完成的状态节点；已完成：定位到第一节点
                    // this.setCurrentStep(currentStatus)
                }).catch(err => {
                    this.detailLoading = false
                })
            }
        },
        // handleSetStep(step) {
        //     let tip = ''
        //     if(step == 3) {
        //         tip = '确定开始采集个人绩效吗？'
        //     }else if(step == 4) {
        //         tip = '确定启动中期审视吗？'
        //     }else if(step == 6) {
        //         tip = '确定启动考核吗？'
        //     }

        //     this.$prompt('tip', '提示', {
        //         confirmButtonText: '确定',
        //         cancelButtonText: '取消',
        //         inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        //         inputErrorMessage: '邮箱格式不正确'
        //     }).then(({ value }) => {
        //         this.$message({
        //             type: 'success',
        //             message: '你的邮箱是: ' + value
        //         });
        //     }).catch(() => {
        //         this.$message({
        //             type: 'info',
        //             message: '取消输入'
        //         });       
        //     });

        // },
        //同步数据
        synchData(syncData = null) {
            if(syncData) {
                this.detail.OverallEvaluationAttachmentList = syncData.OverallEvaluationAttachmentList
                this.detail.OverallEvaluation = syncData.OverallEvaluation
                this.detail.AutoEndTime = syncData.AutoEndTime
            }
        },
        //isSyncStatus 是否同步状态（false：仅仅只是 切换步骤）
        setCurrentStep(step, isSyncStatus = true, syncData = null) {
            //不能点击当前步骤(手动点击的时候)
            if(this.currentStep == step && !isSyncStatus) {
                return false
            }

            //当手动点击步骤条节点时 必须是已经执行过的步骤才能查看
            if(!isSyncStatus && !(this.summaryDetail && this.summaryDetail.ProgressStatus && this.summaryDetail.ProgressStatus >= step)) {
                this.$message({
                    message: "当前步骤还未进行，无法查看",
                    type: "error",
                });
                return false
            }

            //第4步完成了，需要同步“附件”和“整体评价”字段
            if(syncData) {
                this.detail.OverallEvaluationAttachmentList = syncData.OverallEvaluationAttachmentList
                this.detail.OverallEvaluation = syncData.OverallEvaluation
                this.detail.AutoEndTime = syncData.AutoEndTime
            }
            
            this.currentStep = step
            if(isSyncStatus) {
                this.summaryDetail.ProgressStatus = step
                this.setStepStatus(step)
            }
        },
        setStepStatus(currentStep) {
            this.steps.forEach(t => {
                if(t.value < currentStep) {
                    t.status = 'success'
                }else if(t.value == currentStep) {
                    t.status = 'finish'
                }else{
                    t.status = 'wait'
                }
            })
        },
        handleFinished() {
            let tip1 = ''
            let tip2 = ''
            let tip3 = ''
            if(this.summaryDetail.ProgressStatus == 2) {
                tip1 = '完成后将进入【中期审视】阶段且无法撤销回'
                tip2 = '确定完成个人绩效采集吗？'
            }else if(this.summaryDetail.ProgressStatus == 4) {
                tip1 = '完成后将进入【绩效终审】阶段且无法撤销回'
                tip2 = '确定完成绩效考核吗？'
            }else if(this.summaryDetail.ProgressStatus == 5) {
                tip1 = '确定完成考核后将立即公示考核结果，'
                tip2 = '考核内容将无法再作修改'
                tip3 = '请确认是否确定已完成考核？'
            }

            const h = this.$createElement;
            this.$msgbox({
                title: '提示',
                message: h('p', null, [
                    h('div', { style: 'text-align: center' }, tip1),
                    h('div', { style: 'text-align: center' }, tip2),
                    h('div', { style: 'text-align: center; color: red' }, tip3)
                ]),
                showCancelButton: true,
                closeOnClickModal: false,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                beforeClose: (action, instance, done) => {
                    if (action === 'confirm') {
                        instance.confirmButtonLoading = true;
                        instance.confirmButtonText = '确认中...';

                        appraisePlanYear.endAndNext({Id: this.summaryDetail.Id}).then(res => {
                            let nextStep = res
                            this.summaryDetail.ProgressStatus = nextStep
                            this.setCurrentStep(nextStep)

                            //如果是“绩效考核”点击“已完成”，那当前节点状态为已启动（没有手动启动），否则为下一节点的未启动
                            if(nextStep == 5) {
                                this.summaryDetail.CurrentStepStatus = 2
                            }else if(nextStep == 6) { //如果是“绩效终审”点击“已完成”，那当前节点状态为已启动（没有手动启动），否则为下一节点的未启动
                                this.summaryDetail.CurrentStepStatus = 3
                            }else{
                                this.summaryDetail.CurrentStepStatus = 1
                            }

                            this.$message({
                                type: 'success',
                                message: '操作成功'
                            });
                            done();
                            instance.confirmButtonLoading = false;
                        })
                    } else {
                        done();
                    }
                }
            }).then(action => {
                console.log(action)
            });
            
            
        },
        handleSetStep(step) {
            this.settingDialogVisible = true
            if(step == 3) {
                this.tip = '确定开始进行个人绩效采集吗？'
            }else if(step == 4) {
                this.tip = '确定启动中期审视吗？'
            }else if(step == 5) {
                this.tip = '确定开始启动绩效考核吗？'
            }
            this.enterStep = step
        },
        closeSettingDialog() {
            this.settingDialogVisible = false
        },
        handleSettingSaveSuccess() {

        },
        handleSaveSuccess() {
            this.$refs.formData2.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData2));
                    postData.Id = this.summaryDetail.Id

                    let result = null
                    this.disabledBtn = true
                    if(this.enterStep == 3) {
                        result = appraisePlanYear.startPersonalGatherAndNext(postData)
                    }else if(this.enterStep == 4) {
                        result = appraisePlanYear.finishPersonalGatherAndNext(postData)
                    }else if(this.enterStep == 5) {
                        result = appraisePlanYear.finishMidtermReviewAndStart(postData)
                    }

                    if(result) {
                        result.then(res => {
                            // 1 未启动  2  已启动  3 已结束
                            this.summaryDetail.CurrentStepStatus = 2

                            // let time = this.formData2.EndTime
                            if(this.enterStep == 3) {
                                // this.summaryDetail.GatherEndTime = time
                                // this.summaryDetail.GatherStatus = 2
                            }else if(this.enterStep == 4) { 
                                // this.summaryDetail.MidtermReviewEndTime = time
                            }else if(this.enterStep == 5) { 
                                // this.summaryDetail.AssessEndTime = time
                                // this.summaryDetail.AssessStatus = 2

                                // 绩效考核，等于“中期审视”的结束按钮，所以需要同步状态。
                                let nextStep = res
                                this.summaryDetail.ProgressStatus = nextStep
                                this.setCurrentStep(nextStep)
                            }

                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });

                            this.closeSettingDialog()
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    }else{
                        this.disabledBtn = false
                    }
                }
            });
        },
        handleShowDetail(obj) {
            this.dialogCommonFormStatus = obj.Type == 1 ? 'btnPromise' : 'btnEvaluate'
            this.dialogCommonFormId = obj.Id
            this.dialogCommonFormVisible = true

        },
        

    },
};
</script>

<style scoped>
.steps-wrapper >>> .el-step__icon.is-text{
    cursor: pointer;
}

.step-active >>> .el-step__icon.is-text{
    transform:scale(1.1, 1.1);
}

.step-active >>> .is-success .el-step__icon.is-text{
    box-shadow: 0 0 10px #409EFF;  
}
.steps-wrapper >>> .is-success .el-step__icon.is-text:hover{
    box-shadow: 0 0 6px #409EFF;
}

.step-active >>> .is-finish .el-step__icon.is-text{
    box-shadow: 0 0 10px #67C23A; 
}
.steps-wrapper >>> .is-finish .el-step__icon.is-text:hover{
    box-shadow: 0 0 6px #67C23A;
}

.step-active >>> .is-wait .el-step__icon.is-text{
    box-shadow: 0 0 10px #C0C4CC;
    cursor: not-allowed;
}
.steps-wrapper >>> .is-wait .el-step__icon.is-text:hover{
    box-shadow: 0 0 6px #C0C4CC;
    cursor: not-allowed;
}

.steps-wrapper >>> .is-finish{
    color: #67C23A;
    border-color: #67C23A;
}

.steps-wrapper >>> .is-success{
    color: #409EFF;
    border-color: #409EFF;
}

.steps-wrapper >>> .el-step__description{
    padding-left: 0;
    padding-right: 0;
}
</style>

<style lang="scss" scoped>
.page-main-wrapper {
    height: calc(100% - 40px);
    // display: flex;
    .content-left{
    	width:250px;
    	height: 100%;
    	border-right:1px solid #DCDFE6;
        padding: 10px 0 10px 10px;
        box-sizing: border-box;
    }
    .content-right{
        // flex: 1;
        width: calc(100% - 250px);
        height: 100%;
        .page-content-wrapper{
            height: 100%;
            display: flex;
            flex-direction: column;
            .com-height{
                flex: 1;
                overflow-y: auto;
            }
            .steps-wrapper, .steps-content {
                margin-left: auto; 
                margin-right: auto; 
            }
            .steps-wrapper{
                width: 800px; 
                padding: 20px 0; 
                // height: 100px;
            }
            .steps-content{
                // height: calc(100% - 100px - 10px); 
                flex: 1;
                position: relative;
                width: 1200px;
                margin-bottom: 10px; 
                border: 1px solid #DCDFE6; 
                box-sizing: border-box; 
                border-radius: 5px; 
                padding: 10px;
            }
        }
    }
}
</style>