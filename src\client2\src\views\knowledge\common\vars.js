//项目研发相关常量定义

export const vars = {
    questionBankEnum: {
        searchTypes: [
            { value: 0, label: '全部', color: 'inherit' },
            { value: 1, label: '初级', color: 'inherit' },
            { value: 2, label: '中级', color: '#70B603' },
            { value: 3, label: '高级', color: '#409EFF' },
        ],
        questionTypes: [
            { value: 1, label: '单选题', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
            { value: 2, label: '多选题', color: '#70B603', bgColor: 'rgba(112, 182, 3, 0.2)' }, // 绿色
            { value: 3, label: '判断题', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' }, // 橙色
        ],
    },
    testPaperEnum: {
        passNotTypes: [
            { value: 0, label: '无', color: 'inherit' },
            { value: 1, label: '及格', color: '#409EFF' },
            { value: 2, label: '不及格', color: '#FF0000' },
        ],
    },
    trainEnum: {
        viewRangeTypes: [ // 可见范围 1:所有人 2:按部门 3:按入职时间 4:自定义
            { value: 1, label: '所有人' },
            { value: 2, label: '按部门' },
            { value: 3, label: '按入职时间' },
            { value: 4, label: '自定义' },
        ],
        examTypeTypes: [ // 是否必考 1:不考 2:必考 3:选考
            { value: 1, label: '不考', color: 'inherit' },
            { value: 2, label: '必考', color: '#FF0000' },
            { value: 3, label: '选考', color: '#f59a23' },
        ],
        studyCycleTypes: [ // 学习循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
            { value: 1, label: '7天' },
            { value: 2, label: '15天' },
            { value: 3, label: '30天' },
            { value: 4, label: '60天' },
            { value: 5, label: '90天' },
            { value: 6, label: '180天' },
            { value: 7, label: '365天' },
        ],
        ExtractTypeTypes: [ // 提取类型 1.随机提取(默认) 2.按难易度提取
            { value: 1, label: '随机提取' },
            { value: 2, label: '按难易度提取' },
        ],
    },
    learningRecordsEnum: {
        stateTypes: [ // "状态 1. 未完成 2. 已完成 
            { value: 1, label: '未完成', color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)' }, // 蓝色
            { value: 2, label: '已完成', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 红色
        ],
        isPassTypes: [ // "状态 0. 无  1. 及格 2. 不及格 
            { value: 0, label: '无', color: 'inherit' },
            { value: 1, label: '及格', color: '#409EFF' }, // 蓝色
            { value: 2, label: '不及格', color: '#FF0000' }, // 红色
        ],
        isWillLearnTypes: [ // "状态 1: 非必学 , 2: 必学
            { value: 1, label: '非必学', color: '#FF0000' }, // 红色
            { value: 2, label: '必学', color: '#409EFF' }, // 蓝色
        ],
        testModeTypeTypes: [ // 考试类型 1: 正式 , 2: 练习
            { value: 1, label: '正式', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
            { value: 2, label: '练习', color: '#70B603', bgColor: 'rgba(112, 182, 3, 0.2)' }, // 绿色
        ],
        IsStudyingTypes: [ //  是否已学
            { value: true, label: '已学', color: 'rgba(64, 158, 255, 0.2)', bgColor: '#409EFF' }, // 蓝色
            { value: false, label: '未学', color: 'rgba(255, 0, 0, 0.2)', bgColor: '#FF0000' }, // 红色
        ],
        IsExamTypes: [ //  是否已学
            { value: true, label: '已考', color: 'rgba(64, 158, 255, 0.2)', bgColor: '#409EFF' }, // 蓝色
            { value: false, label: '未考', color: 'rgba(255, 0, 0, 0.2)', bgColor: '#FF0000' }, // 红色
        ]
    },
    caseShareEnum: {
        IsTransitionCourseTypes: [ //  是否 转课程
            { value: 0, label: '未转课程', color: '#F53F3F', bgColor: '#FF0000' }, // 红色
            { value: 1, label: '已转课程', color: '#267FEB', bgColor: '#409EFF' }, // 蓝色
        ]
    }
}