<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" style="padding-top: 0;"
                label-position="right" label-width="100px" class="wrapperMain">
                    <el-row class="wrapperBox" v-loading='loading' style="padding-top: 10px;">
                        <el-form-item label="标题名称" prop="CaseName">
                            <el-input v-if="editable" :disabled="!editable" maxlength="100" type="text" v-model="formData.CaseName"></el-input>
                            <div v-else>{{formData.CaseName}}</div>
                        </el-form-item>
                        <el-form-item label="案例类型" prop="ClassifyId">
                            <treeselect v-if="editable" class="treeselect-common" :normalizer="normalizer" :disabled="!editable" :options="treeData"
                                :default-expand-level="1" :multiple="false" :open-on-click="true" :open-on-focus="true"
                                :clear-on-select="false" v-model="formData.ClassifyId" placeholder="请选择案例类型"
                                :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"
                                :append-to-body="true" zIndex='9999'>
                            </treeselect>
                            <div v-else>{{formData.ClassifyName}}</div>
                        </el-form-item>
                        <el-form-item label="提交人" prop="SubmitEmployeeIdList">
                            <emp-selector v-if="editable" :readonly="!editable" :beforeConfirm='handleFinalBeforeConfirm' :showType="2"
                            :multiple="true" :list="formData.SubmitEmployeeIdList" @change="handleChangeManager"></emp-selector>
                            <div v-else>{{formData.SubmitEmployeeName}}</div>
                        </el-form-item>
                        <el-form-item label="案例描述" prop="CaseDescribe">
                            <editor-bar v-if="editable" :value="formData.CaseDescribe" @edit="formData.CaseDescribe = arguments[0]"></editor-bar>
                            <div class="divUeditor ql-editor" v-html="formData.CaseDescribe" v-if="!editable" v-viewer></div>
                        </el-form-item>
                        <el-form-item label="相关附件" prop="AttachmentList">
                            <template v-if="editable">
                                <app-uploader accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList"
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            </template>
                            <template v-else>
                                <app-uploader v-if="formData.AttachmentList.length>0" accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList" readonly
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                <template v-else>无</template>
                            </template>
                        </el-form-item>
                        <div v-loading="ApprovalLoading">
                            <approval-panel v-if="dialogStatus == 'create'" :editable="approvalShow && isApprovalor" ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                            <approval-detail 
                                :isOnlyViewDetail='isOnlyViewDetail || !isApprovalor' 
                                v-if="dialogStatus == 'approval' || dialogStatus == 'detail' || dialogStatus == 'revoke' || dialogStatus == 'revokeApproval'" 
                                ref="approvalDetail" 
                                :dialogStatus='dialogStatusTrans' 
                                :approvalObj='formData.Approval'
                            ></approval-detail>
                        </div>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <!-- <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button> -->
                
                <app-button v-if="dialogStatus != 'approval'" @click="createData" :buttonType="1" v-show="editable || dialogStatus=='revoke'" :disabled="disabledBtn" style="margin-left:10px;"></app-button>
                <el-button @click="handleApproval"  type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval' && !isOnlyViewDetail && isApprovalor" style="margin-left:10px;">审批</el-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import * as classifyApi from '@/api/classify'
import Treeselect from "@riophae/vue-treeselect";
import EditorBar from "@/components/QuillEditor/index.vue";
import empSelector from "@/views/common/empSelector";

import approvalMixins from '@/mixins/approvalPatch'
import NoData from "@/views/common/components/noData";
import approvalPanel from '@/views/projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '@/views/projectDev/projectMgmt/workbench/common/approvalDetail'

import * as CaseShareApi from '@/api/knowledge/CaseShare'
import { vars } from '../common/vars'
import * as ApprovalVars from "@/views/workbench/myWorkbench/vars";
export default {
    name: "caseShare-create",
    directives: {},
    components: {
        NoData,
        empSelector,
        Treeselect,
        EditorBar,
        approvalPanel,
        approvalDetail,
    },
    mixins: [approvalMixins],
    computed: {
        dialogStatusTrans() {
            let statusTemp = this.dialogStatus
            if(statusTemp == "revoke" || statusTemp == "detail") {
                return 'detail'
            }else if(statusTemp == "approval" || statusTemp == "revokeApproval") {
                return 'approval'
            }else{
                return statusTemp
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != 'approval' && this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "提交案例";
            }else if(this.dialogStatus == 'update'){
                return "编辑案例";
            }else if(this.dialogStatus == 'approval'){
                return "案例详情";
            }else if(this.dialogStatus == 'revoke' || this.dialogStatus == 'revokeApproval') {
                return "案例详情";
            }
            return "案例详情";
        },
    },
    filters: {
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        id: {
            type: String,
            default: "",
        },
        approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
            type: String,
            default: ''
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.loadTreeData();
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
        "formData.ClassifyId": {
            handler(val) {
                if (val) {
                    if(this.isOneLoad){
                        this.isOneLoad = false;
                    } else {
                        this.getApprovalDetail(val)
                    }
                }
                !this.isOneLoad && this.$refs["formData"] && this.$refs["formData"].validateField("ClassifyId");
            },
            immediate: true
        }
    },
    created() {
        if (this.dialogStatus == "create"||this.dialogStatus == 'update') {
            this.rules = this.initRules(this.rules);
        }
    },
    data() {
        return {
            approvalShow:false,
            isOneLoad: false,
            /** 选择案例类型 */
            treeLoading: false,
            treeData: [],
            normalizer(node) {
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },


            disabledBtn: false,
            loading: false,
            rules: {
                CaseName: {fieldName: "案例名称",rules: [{ required: true }]},
                ClassifyId: {fieldName: "案例类型",rules: [{ required: true }]},
                SubmitEmployeeIdList: {fieldName: "提交人",rules: [{ required: true }]},
                CaseDescribe: {fieldName: "案例描述",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                // Id: '',
                CaseName: '',// 案例名称
                ClassifyId: '',// 案例类型
                SubmitEmployeeId: '',// 提交人
                SubmitEmployeeIdList: [],// 提交人
                CaseDescribe: '',//案例描述
                AttachmentList: [],//附件
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                    ApprovalSet: 1,
                },
            },
            ApprovalLoading: false,
        };
    },
    methods: {
        handleFinalBeforeConfirm(users) {
            if(users && users.length > 1) {
                this.$message({
                    message: '提交人不得超过1人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleChangeManager(users) {
            // console.log(users)
            if (users && users.length > 0) {
                this.formData.SubmitEmployeeIdList = users;
            } else {
                this.formData.SubmitEmployeeIdList = [];
            }
            this.$refs["formData"].validateField("SubmitEmployeeIdList");
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let self = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: 15
            };
            self.treeLoading = true
            classifyApi.getListPage(paramData).then(response => {
                self.treeLoading = false
                self.treeData = listToTreeSelect(response.Items);
                if (self.treeData && self.treeData.length > 0&&self.dialogStatus == "create") {
                    self.formData.ClassifyId = self.treeData[0].Id;
                }
            }).catch(err => {
                self.treeLoading = false
            });
        },
        // 根据分类查询设置的审批数据
        getApprovalDetail(ClassifyId){
            let self = this;
            self.ApprovalLoading=true;
            CaseShareApi.getApprovalSetting({
                ClassifyId: ClassifyId
            }).then(response => {
                self.ApprovalLoading=false;
                if (response) {
                    self.approvalShow = response.ApprovalSet == 1
                    self.formData.Approval = response;
                    if(!self.formData.Approval.ApprovalEmployeeList){
                        self.formData.Approval.ApprovalEmployeeList=[[]];
                    }
                }else{
                    self.approvalShow=true;
                    self.formData.Approval = self.$options.data().formData.Approval;
                    self.$forceUpdate();
                }
                // console.log(self.formData.Approval,self.$options.data().formData.Approval)
            }).catch(err => {
                self.ApprovalLoading=false;
            })
        },
        resetFormData() {
            this.approvalShow=false;
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            let listResult = self.$refs.formData.validate();
            let approvalPanelValidate = self.$refs.approvalPanel.validate();
                Promise.all([listResult,approvalPanelValidate]).then(valid => {
                        let postData = JSON.parse(JSON.stringify(self.formData));
                        postData.Approval = self.$refs.approvalPanel.getData() //审批层区块
                        postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                        postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                        postData.AttachmentIdList = postData.AttachmentList.map((s) => s.Id);
                        postData.SubmitEmployeeId = postData.SubmitEmployeeIdList.map((s) => s.EmployeeId).toString();
                        
                        // console.log(postData)
                        self.disabledBtn = true;
                        CaseShareApi.add(postData).then(res => {
                            self.disabledBtn = false;
                            self.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            self.$refs.appDialogRef.createData();
                        })
                        .catch(err => {
                            self.disabledBtn = false;
                        });
                }).catch(err => {
                    self.disabledBtn=false;
                })
        },
        // 查询 基本信息
        getDetail() {
            this.isOneLoad = true;
            this.loading = true
            CaseShareApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 审批确认
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = ApprovalVars.vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // console.log(postData)
                        this.disabledBtn = true
                        CaseShareApi.approval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData();
                            this.handleClose();
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })
                }
            })
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
    }
};
</script>

<style scoped>
</style>
<style lang='scss' scoped>
.wrapperBox{
    padding-right: 20px;
}
</style>