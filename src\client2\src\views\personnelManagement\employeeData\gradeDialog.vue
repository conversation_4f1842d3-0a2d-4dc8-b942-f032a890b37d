<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1000' :maxHeight="800" >
            <template slot="body">
                <div class="body-content" v-loading="formLoading">
                    <el-form ref="form" label-width="80px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="规则名称">
                                    <span>{{ formData.RuleName }}</span>
                                    <el-button type="text" style="margin-left: 5px;" @click="openRateDetailDialog()">查看评分标准</el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="被评价人">
                                    <template v-if="formData.Employee">
                                        {{ formData.Employee.Name }}
                                    </template>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <div class="section-title">系统扣分项</div>

                    
                    <app-table-core 
                        ref="mainTable" 
                        :isShowBtnsArea='false' 
                        :tab-columns="tabColumns1" 
                        :tab-datas="sysRuleList" 
                        :isShowAllColumn="true" 
                        :isShowOpatColumn="false"
                        :multable="false" 
                    >
                        <template slot="RateId" slot-scope="scope">
                            
                            <span v-if="getRateParentId(scope.row.RateId)">
                                {{ getRateType(getRateParentId(scope.row.RateId)) }} / 
                            </span>
                            {{ getRateType(scope.row.RateId) }}
                        </template>
                        <template slot="DeductionReasonList" slot-scope="scope">
                            <div v-for="(i, idx) in scope.row.DeductionReasonList" :key="idx">
                                {{ i.DeductionReason }}
                            </div>
                        </template>
                        <template slot="DeductionValue" slot-scope="scope">
                            {{ scope.row.DeductionValue }}
                        </template>
                    </app-table-core>

                    <template v-if="isContainOther">
                        <div class="section-title">
                            <span>主管扣分项</span>
                            <span class="sub-title">根据员工的工作情况进行补充，酌情扣分</span>
                        </div>
    
                        <app-table-core 
                            ref="mainTable" 
                            :isShowBtnsArea='false' 
                            :tab-columns="tabColumns" 
                            :tab-datas="tabDatas" 
                            :isShowAllColumn="true" 
                            :startOfTable="1" 
                            :serial="false"
                            :isShowOpatColumn="false"
                            :multable="false" 
                        >
                            <template slot="Opt" slot-scope="scope">
                                <i class="el-icon-remove opt-icon" style="color: #F56C6C;" @click="handleRemove(scope.index - 1)"></i>
                            </template>
                            <template slot="RateId" slot-scope="scope">
                                {{ getRateType(scope.row.RateId) }}
                            </template>
                            <template slot="DeductionReason" slot-scope="scope">
                                <el-input v-model="scope.row.DeductionReason" maxlength="100" placeholder=""></el-input>
                            </template>
                            <template slot="DeductionValue" slot-scope="scope">
                                <el-input-number :min="0" v-model="scope.row.DeductionValue"></el-input-number>
                            </template>
                        </app-table-core>
                    </template>
                </div>
            </template>
            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <app-button @click="handleSave" text="保存" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>

        <rateDetail 
            v-if="rateDetailDialogVisible && formData.Id"
            :dialogFormVisible="rateDetailDialogVisible"
            @closeDialog="closeRateDetailDialog"
            dialogStatus="review" 
            :id="formData.Id"
        ></rateDetail>

    </div>
</template>

<script>
import rateDetail from '../employeeDataSetting/create'
import * as employeePointsRule from "@/api/personnelManagement/employeePointsRule";
import { vars } from '../common/vars'

let otherRateId = 13000

export default {
    name: 'gradeDialog',
    components: {
        rateDetail,
    },
    props: {
        dialogStatus: {
            type: String
        },
        // 1 开始
        weekIdx: {
            type: Number,
            default: 0
        },
        //被评价员工编号
        emp: {
            type: Object,
            required: true
        },
        year: {
            type: [Number, String],
            required: true
        },
        month: {
            type: Number,
            required: true
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(val) {
                    this.getWeekSalesPoint()
                }
            },
            immediate: true
        }
    },
    computed: {
        pageTitle() {
            return `第${vars.weekIdxEnum[this.weekIdx - 1]}周评分`
        },
        sysRuleList() {
            //不需要显示（10000：其他）
            return (this.formData.RuleList || []).filter(s => s.RateId != otherRateId)
        },
        isContainOther() {
            return (this.formData.RuleList || []).findIndex(s => s.RateId == otherRateId) > -1
        },
    },
    data() {
        return {
            
            formLoading: false,
            disabledBtn: false,
            rateDetailDialogVisible: false,

            tabColumns1: [
                {attr: {prop: "RateId", label: "扣分项", width: 180, align: 'center'}, slot: true},
                {attr: {prop: "DeductionReasonList", label: "扣分原因"}, slot: true},
                {attr: {prop: "DeductionValue", label: "扣分", width: 170}, slot: true},
            ],
            formData: {
                Id: '',
                Employee: null,
                RuleList: [],
                RuleName: '',
            },
            tabDatas: [],
            tabColumns: [
                {attr: {prop: "RateId", label: "扣分项", width: 180, align: 'center'}, slot: true},
                {attr: {prop: "DeductionReason", label: "扣分原因", renderHeader: this.renderHeaderRequiredCol}, slot: true},
                {attr: {prop: "DeductionValue", label: "扣分", width: 170}, slot: true},
                {attr: {prop: "Opt", label: "操作", fixed: 'left', width: 50, renderHeader: this.renderHeader}, slot: true},
            ],

        }
    },
    methods: {
        getRateType(rateId) {
            let obj = vars.rateEnum.find(s => s.RateId == rateId)
            if(obj) {
                return obj.Title
            }
            return ''
        },
        getRateParentId(rateId) {
            let obj = vars.rateEnum.find(s => s.RateId == rateId)
            if(obj) {
                return obj.ParentId
            }
            return ''
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        renderHeaderRequiredCol(h, { column }) {
            return (
            <div>
                { column.label } 
                <span class='red'> *</span>
            </div>
            )
        },
        renderHeader(h, { column }) {
            return (
                <span>
                    <i style='font-size: 18px; color: #409EFF; cursor: pointer;' class='el-icon-circle-plus' on-click={() => this.handleAdd()}></i>
                </span>
            )
        },
        handleAdd() {
            let temp = {
                RateId: otherRateId, //其他
                DeductionReason: '',
                DeductionValue: '',
            }
            this.tabDatas.push(temp)
        },
        handleRemove(idx) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.tabDatas.splice(idx, 1)
            });
        },
        //保存
        handleSave() {
            let postData = JSON.parse(JSON.stringify(this.formData));
            postData.EmployeeId = this.emp.EmployeesId
            delete postData.Employee
            postData.OtherList = JSON.parse(JSON.stringify(this.tabDatas))

            let flag = true
            if(postData.OtherList && postData.OtherList.length > 0) {
                for(let i = 0; i < postData.OtherList.length; i++) {
                    if(!postData.OtherList[i].DeductionReason) {
                        this.$message({
                            message: '扣分原因不能为空',
                            type: "error"
                        });
                        flag = false
                        break;
                    }
                }
            }

            if(!flag) {
                return false
            }

            this.disabledBtn = true;

            employeePointsRule.saveWeekSalesPoint(postData).then(res => {
                this.disabledBtn = false;

                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.$refs.appDialogRef.createData();
            }).catch(err => {
                this.disabledBtn = false;
            });
        },
        getWeekSalesPoint() {
            let postDatas = {
                Year: this.year,
                Month: this.month,
                EmployeeId: this.emp.EmployeesId,
                WeekIndex: this.weekIdx
            }
            this.formLoading = true

            let result = null

            result = employeePointsRule.getWeekSalesPoint(postDatas)
            
            // if(this.dialogStatus == 'create') {
            // }else if(this.dialogStatus == 'update') {
            //     result = employeePointsRule.getWeekSalesPointDetails(postDatas)
            // }

            result.then(res => {
                this.formLoading = false
                this.formData = res

                this.tabDatas = res.RuleList.filter(s => s.RateId == otherRateId)

            }).catch(err => {
                this.formLoading = false
            })
        },
        openRateDetailDialog() {
            this.rateDetailDialogVisible = true
        },
        closeRateDetailDialog() {
            this.rateDetailDialogVisible = false
        },
    },
}
</script>

<style lang="scss" scoped>
.body-content{
    /deep/.el-form-item{
        margin-bottom: 0;
    }
}

.section-title{
    font-size: 12px;
    color: #606266;
    font-weight: 700;
    margin-top: 20px;
    padding: 0 6px;
    .sub-title{
        font-weight: 300;
        margin: 0 20px;
        color: #909399;

    }
}

.opt-icon{
    font-size: 18px!important;
    cursor: pointer;
}
</style>