<!--类型/审批设置弹窗-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog title="类型/审批设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-row class="contentBox">
                    <div class="contentBox_left">
                        <div class="btn-wrapper">
                            <el-button type="primary" @click="addTopLevel">添加案例类型</el-button>
                        </div>
                        <div class="contentBox_left_main" v-loading="treeLoading">
                            <no-data v-if="treeData.length==0"></no-data>
                            <div class="treeBox" v-else>
                                <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps"
                                :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                                    <span class="custom-tree-node" slot-scope="{ node, data }">
                                        <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                                        <span class="node-btn-area">
                                            <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                                <span class="el-dropdown-link">
                                                    <i class="el-icon-more"></i>
                                                </span>
                                                <el-dropdown-menu slot="dropdown">
                                                    <el-dropdown-item command="update">编辑</el-dropdown-item>
                                                    <el-dropdown-item command="delete">删除</el-dropdown-item>
                                                </el-dropdown-menu>
                                            </el-dropdown>
                                        </span>
                                    </span>
                                </el-tree>
                            </div>
                        </div>
                    </div>
                    <div class="contentBox_right" v-loading='treeLoading||loading'>
                        <el-form
                            :rules="formRules"
                            ref="formModel"
                            :model="formModel"
                            label-position="right"
                            label-width="100px"
                        >
                            <el-form-item label="审批设置" prop="ApprovalSet">
                                <el-radio-group v-model="formModel.ApprovalSet" @change="handleRadioChange">
                                    <el-radio :label="ps.label" v-for="(ps,psI) in processSet" :key="psI">{{ps.value}}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <div v-if="formModel.ApprovalSet != 1">
                                <approval-panel ref="approvalPanel" :approvalPanelObj='formModel.Approval' :showHead="false"></approval-panel>
                            </div>
                        </el-form>
                    </div>
                </el-row>
            </template>
            <template slot="footer">
                <app-button :buttonType="999" type @click="handleClose" text="关闭"></app-button>
                <app-button v-show="checkedNode" :buttonType="999" :loading="buttonLoading" @click="handleButtonClick" text="保存"></app-button>
            </template>
        </app-dialog>
        <!--添加/修改 分类 弹窗组件区-->
        <classify-page :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyDialogFormVisible" @closeDialog="classifyCloseDialog" @saveSuccess="classifySaveSuccess"></classify-page>

    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import NoData from "@/views/common/components/noData";
import * as CaseShareApi from '@/api/knowledge/CaseShare'
import approvalPanel from '@/views/projectDev/projectMgmt/common/approvalPanel'

import { vars } from "../common/vars.js"
import * as classifyApi from '@/api/classify'
import classifyPage from "./classify";
import { listToTreeSelect } from "@/utils";
import * as ApprovalVars from "@/views/workbench/myWorkbench/vars";
export default {
    /**名称 */
    name: "enterprise-qualification-edit",
    /**组件声明 */
    components: {
        NoData,
        classifyPage,
        approvalPanel,
    },
    /**参数区 */
    props: {
        id: {
            type: String,
            default: ''
        }
    },
    /**数据区 */
    data() {
        return {
            processSet: ApprovalVars.vars.processSet,


            /******************* 树 *******************/
            /**树节点弹窗 */
            classifyDialogFormVisible: false,
            classifyDialogStatus: "create",
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },


            termOfValidityTypes: vars.termOfValidityTypes,
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,

            /**表单模型 */
            formModel: { 
                ApprovalSet: 1, // 
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
            },
            /**表单规则 */
            formRules: {
                ApprovalSet: { fieldName: "审批设置", rules: [{ required: true }] },
            }
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                let self = this;
                self.formModel = self.$options.data().formModel
                self.loadTreeData();
            },
            immediate: true
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    try {
                        this.$refs.formModel.clearValidate();
                        this.$refs.approvalPanel.clearValidate();
                    } catch (error) {
                        
                    }
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        getDetail(){
            let self = this;
            self.loading=true;
            CaseShareApi.getApprovalSetting({
                ClassifyId: self.checkedNode.Id
            }).then(response => {
                self.loading=false;
                if (response) {
                    self.formModel.Approval = Object.assign({}, self.formModel.Approval, response);
                    self.formModel.ApprovalSet = 2;
                }else{
                    self.formModel = self.$options.data().formModel;
                }
            }).catch(err => {
                self.loading=false;
            })
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this,formModel = JSON.parse(JSON.stringify(self.formModel));
            let validate = self.$refs.formModel.validate();
            let approvalPanelValidate = new Promise((resolve, reject) => { resolve(true) });
            if(formModel.ApprovalSet == 2){
                approvalPanelValidate = self.$refs.approvalPanel.validate()
            }
            formModel.ClassifyId = self.checkedNode.Id;
            Promise.all([validate, approvalPanelValidate]).then(valid => {
                if(formModel.ApprovalSet == 2){
                    formModel.Approval = self.$refs.approvalPanel.getData() //审批层区块
                    formModel.Approval.ApprovalEmployeeIdList = formModel.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                    formModel.Approval.CCEmployeeIdList = formModel.Approval.CCEmployeeList.map(s => s.EmployeeId)
                }
                self.disabledBtn=true;
                CaseShareApi.addApprovalSetting(formModel).then(res => {
                    self.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    // self.$refs.appDialogRef.handleClose();
                    self.disabledBtn=false;
                }).catch(err => {
                    self.disabledBtn=false;
                })
            }).catch(e => {
                self.disabledBtn=false;
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleFilesUpChange(files) {
            this.formModel.AttachmentList = files
        },
        handleRadioChange(d){
            if(d == 1){
                this.formModel.Approval={//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                }
            }
        },





        /******************* 弹窗相关 *******************/
        classifySaveSuccess(d) {
            if (!d) {
                this.classifyCloseDialog();
            }
            this.loadTreeData();
        },
        classifyCloseDialog() {
            this.classifyDialogFormVisible = false;
        },

        /******************* 树事件 *******************/
        loadTreeData() {
            let self = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: 15
            };
            self.treeLoading = true
            classifyApi.getListPage(paramData).then(response => {
                self.treeLoading = false
                self.treeData = listToTreeSelect(response.Items);
                if (self.treeData && self.treeData.length > 0) {
                    if (
                        !(
                        self.checkedNode &&
                        response.Items.find(t => {
                            return t.Id == self.checkedNode.Id;
                        })
                        )
                    ) {
                        self.checkedNode = self.treeData[0];
                    }
                } else {
                    self.checkedNode = null;
                }
                if (self.checkedNode) {
                    self.$nextTick(() => {
                        self.$refs.treeRef.setCurrentKey(self.checkedNode.Id);
                    });
                }
            }).catch(err => {
                self.treeLoading = false
            });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.classifyDialogStatus = "create";
            this.classifyDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.classifyDialogStatus = "create";
                    this.classifyDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.classifyDialogStatus = "update";
                    this.classifyDialogFormVisible = true;
                 break;
                case "delete":
                    this.handleDeleteArea(data);
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteArea(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            let paramData = { ids: [data.Id], businessType: 15 };
            this.$confirm(`是否确认删除${data.Name}?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                classifyApi.del(paramData).then(res => {
                if (this.checkedNode && this.checkedNode.Id == data.Id) {
                    this.checkedNode = null;
                }
                this.loadTreeData();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                });
            });
        },
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.el-tag{
    cursor: pointer;
    +.el-tag{margin-left: 15px;}
    &.isDisabled{
        cursor: not-allowed;
    }
}
.contentBox{
    display: flex;
    height: 450px;
    &_right{
        flex: 1;
        padding-right: 10px;
    }
    &_left{
        display: flex;
        flex-direction: column;
        width: 25%;
        border-right: 1px solid #eee;
        .btn-wrapper{
            padding: 10px 0;
            text-align: center;
        }
        &_main{
            flex: 1;
            overflow-y: auto;
            
            .custom-tree-node {
                display: block;
                width: 100%;
                position: relative;
                box-sizing: border-box;
                padding-right: 24px;

                .node-title {
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .node-btn-area {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 23px;
                    height: 16px;
                }
            }
        }
    }
}
</style>
