<!-- 借款申请弹窗 -->
<template>
  <app-dialog
    ref="appDialogRef"
    :width="1000"
    className="clear-padding"
    :title="title"
    v-bind="$attrs"
    v-on="$listeners"
    :beforeClose="beforeClose"
  >
    <div slot="body" class="body_wrapper" v-loading="loading">
      <div class="main_container">
        <el-form
          :model="formData.LoanBillObj"
          ref="formRef"
          :rules="rules"
          label-width="110px"
          style="padding-top: 0"
          class="form_container"
        >
          <div class="left_container">
            <el-form-item
              label="撤销原因"
              prop="RevocationCause"
              v-if="
                dialogStatus == 'revoke' ||
                dialogStatus == 'revokeApproval' ||
                (dialogStatus == 'detail' && formData.RevocationStatus > 0)
              "
            >
              <el-input
                :disabled="dialogStatus !== 'revoke'"
                type="textarea"
                :rows="4"
                maxlength="500"
                v-model="formData.LoanBillObj.RevocationCause"
              />
            </el-form-item>
            <el-form-item label="公司名称" prop="KingdeeDepartmentNumber">
              <CompanySelect
                v-model="formData.LoanBillObj.KingdeeDepartmentNumber"
                :disabled="disabled"
              />
            </el-form-item>
            <el-form-item label="填报日期" prop="FBillDate">
              <el-date-picker
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="formData.LoanBillObj.FBillDate"
                type="date"
                placeholder="请选择填报日期"
                :disabled="disabled"
                :picker-options="pickerOptions"
                :clearable="false"
              />
            </el-form-item>
            <el-form-item label="部门" prop="DepartmentId">
              <DepartmentSelect
                :value="formData.LoanBillObj.DepartmentId"
                :disabled="disabled"
                @change="changeDepartment"
              />
            </el-form-item>
            <el-form-item label="借款用途" prop="Purpose">
              <el-input
                v-model="formData.LoanBillObj.Purpose"
                placeholder="请输入借款用途"
                :disabled="disabled"
                clearable
                :maxlength="100"
              />
            </el-form-item>
            <el-divider />
            <el-form-item label="开户银行" prop="BankName">
              <el-input
                v-model="formData.LoanBillObj.BankName"
                placeholder="请输入开户银行"
                :disabled="disabled"
                clearable
                :maxlength="30"
              />
            </el-form-item>
            <el-form-item label="银行账号" prop="BankAccountNumber">
              <el-input
                v-model="formData.LoanBillObj.BankAccountNumber"
                placeholder="请输入银行账号"
                :disabled="disabled"
                clearable
                type="number"
              />
            </el-form-item>
            <el-form-item label="借款金额" prop="LoanAmount">
              <el-input
                v-model="formData.LoanBillObj.LoanAmount"
                :disabled="disabled"
                placeholder="请输入借款金额"
                type="text"
                v-thousands="true"
                :maxlength="15"
                clearable
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
            <el-form-item label="金额(大写)">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">
                  {{ convertToChinese(formData.LoanBillObj.LoanAmount) }}
                </div>
              </div>
            </el-form-item>
            <el-form-item label="个人已借款金额">
              <el-input
                v-model="formData.LoanBillObj.PreviousLoanAmount"
                :disabled="disabled"
                placeholder="请输入个人已借款金额"
                type="text"
                v-thousands="true"
                :maxlength="15"
                clearable
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
          </div>
          <div class="right_container">
            <el-form-item label="申请人">
              <empSelector
                :readonly="true"
                :showType="2"
                :multiple="true"
                placeholder="输入工号/姓名"
                :isAutocomplete="true"
                :collapseTags="false"
                :list="formData.LoanBillObj.SubmitEmployeeList"
                :beforeConfirm="() => true"
              />
            </el-form-item>
            <el-form-item label="单据编号" v-if="disabled">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ formData.LoanBillObj.FBillNo }}</div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 审批区域 -->
      <div class="body_footer">
        <approvalPanel
          v-if="!disabled"
          :editable="isEditApprove"
          ref="approvalPanel"
          :approvalPanelObj="formData.Approval"
        />
        <approvalDetail
          :isOnlyViewDetail="isOnlyViewDetail || !isApprovalor"
          v-if="['approval', 'detail', 'revoke', 'revokeApproval'].includes(dialogStatus)"
          ref="approvalDetail"
          :dialogStatus="dialogStatusTrans"
          :approvalObj="formData.Approval"
        />
      </div>
      <LoanApplicationDialog
        v-if="showLoanApplicationDialog"
        :dialogFormVisible="showLoanApplicationDialog"
        :tempObj="tempObj_"
        dialogStatus="create"
        :id="id"
        :approvalId="approvalId"
        :processId="tempObj_.HRApprovalProcessId"
        @closeDialog="closeSubDialog"
      />
    </div>
    <div slot="footer">
      <el-button
        v-if="['revokeApproval', 'detail'].includes(dialogStatus) && isCurrentUser"
        @click="handleReferenceCreate"
      >
        引用创建
      </el-button>
      <app-button @click="beforeClose" :buttonType="2" :loading="btnLoading" />
      <el-button
        v-if="['create', 'editDraft'].includes(dialogStatus)"
        @click="handleSaveDraft"
        :loading="btnLoading"
      >
        暂存草稿
      </el-button>
      <app-button
        @click="createData"
        :buttonType="1"
        v-if="dialogStatus != 'approval' && (!disabled || dialogStatus == 'revoke')"
        :loading="btnLoading"
        style="margin-left: 10px"
      />
      <el-button
        @click="handleApproval"
        type="primary"
        :loading="btnLoading"
        v-if="
          (dialogStatus == 'approval' || (dialogStatus == 'revokeApproval' && !isOnlyViewDetail)) &&
          isApprovalor
        "
        style="margin-left: 10px"
      >
        审批
      </el-button>
    </div>
  </app-dialog>
</template>

<script>
import dayjs from "dayjs";
import * as approvalManagement from "@/api/approvalManagement.js";
import approvalMixins from "@/mixins/approvalPatch.js";
import { getUserInfo } from "@/utils/auth";
import { getBankAccountApi } from "@/api/finance.js";
import expenseFormDialogMixins from "./expenseFormDialogMixins.js";

export default {
  name: "loanApplicationDialog",
  mixins: [approvalMixins, expenseFormDialogMixins],
  components: {
    LoanApplicationDialog: () => import("./LoanApplicationDialog"),
  },
  data() {
    return {
      // 表单parcelKey对应后端详情数据的包裹对象mixins使用
      parcelKey: "LoanBillObj",
      formData: {
        LoanBillObj: {
          FBillNo: "",
          KingdeeDepartmentNumber: null,
          FBillDate: null,
          DepartmentId: null,
          Purpose: "",
          BankName: "",
          BankAccountNumber: "",
          LoanAmount: "",
          PreviousLoanAmount: "",
          RevocationCause: "",
          // 申请人
          SubmitEmployeeList: [],
        },
        Type: 13, // 借款申请
        ApprovalStatus: 0, //审批状态
        HrApprovalProcessId: null,
        //审批信息
        Approval: {
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          ApprovalOperatorEmployeeList: [], //已审批人员
          NoApprovalEmployeeList: [], //未审批人员
          CCEmployeeList: [], //抄送人
          ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
          ApprovalState: 1, //1: 进行中; 2: 已完成
          ApprovalResult: 1, //1: 通过； 2：不通过
        },
      },
      showLoanApplicationDialog: false,
      tempObj_: null,
      rules: {
        KingdeeDepartmentNumber: [{ required: true, message: "请选择公司名称", trigger: "change" }],
        FBillDate: [{ required: true, message: "请选择填报日期", trigger: "change" }],
        DepartmentId: [{ required: true, message: "请选择部门", trigger: "change" }],
        Purpose: [{ required: true, message: "请输入借款用途", trigger: "blur" }],
        BankName: [{ required: true, message: "请输入开户银行", trigger: "blur" }],
        BankAccountNumber: [
          { required: true, message: "请输入银行账号", trigger: "blur" },
          { max: 25, message: "银行账号格式不正确", trigger: "blur" },
        ],
        LoanAmount: [{ required: true, message: "请输入借款金额", trigger: "blur" }],
        RevocationCause: [{ required: true, message: "请输入撤销原因", trigger: "blur" }],
      },
    };
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.closeDialog();
          return;
        }
        if (this.dialogStatus === "create") {
          if (this.tempObj) {
            this.formData = Object.assign(this.formData, this.$_.cloneDeep(this.tempObj));
          } else {
            this.formData.LoanBillObj.FBillDate = dayjs().format("YYYY-MM-DD");
            this.getBankAccount();

            const { employeeid, empName, empNumber, deptId } = getUserInfo();
            this.formData.LoanBillObj.SubmitEmployeeList.push({
              Name: empName,
              Number: empNumber,
              EmployeeId: employeeid,
            });
            this.changeDepartment(deptId);
          }
        } else {
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  created() {},
  computed: {
    title() {
      const titleMap = {
        create: "借款申请",
        detail: "借款申请详情",
        approval: "借款申请审批",
        revoke: "(撤销)借款申请",
        revokeApproval: "(撤销)借款申请",
      };
      return titleMap[this.dialogStatus] || "借款申请";
    },
    pickerOptions() {
      return {
        disabledDate: time => {
          return dayjs(time).isAfter(dayjs(), "day");
        },
      };
    },
  },
  methods: {
    /**
     * 创建报销单
     * @param temporize true:保存草稿 false:发起审批
     */
    createRequest(temporize = false) {
      this.formData.Approval = this.$refs.approvalPanel.getData(); //审批层区块

      const form = this.$_.cloneDeep(this.formData);
      form.Approval.ApprovalEmployeeIdList = form.Approval.ApprovalEmployeeList.map(s =>
        s.map(e => e.EmployeeId)
      );
      form.Approval.CCEmployeeIdList = form.Approval.CCEmployeeList.map(s => s.EmployeeId);

      const params = {
        ...form,
        Temporize: temporize,
        HrApprovalProcessId: this.id,
        LoanBillObj: {
          ...form.LoanBillObj,
          SubmitEmployeeIdList: form.LoanBillObj.SubmitEmployeeList.map(s => s.EmployeeId),
        },
      };

      delete params.LoanBillObj.SubmitEmployeeList;

      let reqApi;
      if (this.dialogStatus == "editDraft") {
        // 草稿编辑
        reqApi = approvalManagement.temporizeApi;
      } else {
        // 普通创建和创建时暂存
        reqApi = approvalManagement.infoAdd;
      }
      this.btnLoading = true;
      reqApi(params)
        .then(res => {
          this.$emit("reload");
          this.closeDialog();
          if (temporize) {
            this.$message.success("暂存成功");
          } else {
            this.$message.success("提交成功");
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 获取当前用户银行和卡号
    getBankAccount() {
      getBankAccountApi().then(res => {
        this.formData.LoanBillObj.BankName = res?.BankName || "";
        this.formData.LoanBillObj.BankAccountNumber = res?.BankAccountNumber || "";
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.body_wrapper {
  height: 75vh;
  display: flex;
  flex-direction: column;
  .main_container {
    flex: 1;
    width: 100%;
    .form_container {
      width: 100%;
      height: 100%;
      display: flex;
      .left_container {
        padding: 10px;
        width: 65%;
        border-right: 1px solid $border-color-light;
      }
      .right_container {
        width: 35%;
        padding: 10px;
      }
    }
  }
  .body_footer {
    flex-shrink: 0;
    border-top: 1px solid $border-color-light;
  }
}
/deep/.el-divider {
  background-color: $border-color-light;
  margin: 10px 0;
}
/deep/input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  &[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
