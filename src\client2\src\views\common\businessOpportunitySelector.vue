<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      :width='1200'
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='Year'>
            <!-- <el-input style="width: 100%;" v-model="listQueryParams.Year" placeholder=""></el-input> -->
            <el-date-picker key="xxxyear" style="width: 100%;" v-model="listQueryParams.Year" type="year" placeholder="" format='yyyy' value-format='yyyy'></el-date-picker>
          </template>
          <template slot='Name'>
            <el-input style="width: 100%;" v-model="listQueryParams.Name" placeholder=""></el-input>
          </template>
          <template slot='EmployeeName'>
            <el-input style="width: 100%;" v-model="listQueryParams.EmployeeName" placeholder=""></el-input>
          </template>
        </app-table-form>
      </template>
      <template slot="Status" slot-scope="scope">
        <span
          style="color:#fff;padding: 2px 4px;border-radius: 10%;"
          :class="'status-color-'+scope.row.Status "
        >{{scope.row.Status | statusFilter}}</span>
      </template>
      
      <template slot="Year" slot-scope="scope">
        <span>{{scope.row.Year || '无' }}</span>
      </template>
      <template slot="WeightType" slot-scope="scope">
        <span>{{scope.row.WeightType | weightTypeFilter }}</span>
      </template>
      <template slot="SourceType" slot-scope="scope">
        <span>{{scope.row.SourceType | sourceTypeFilter }}</span>
      </template>
      <template slot="TransactionProbability" slot-scope="scope">
        <span>{{scope.row.TransactionProbability }}%</span>
      </template>
      <template slot="EmployeeList" slot-scope="scope">
        <span>{{scope.row.EmployeeList.map((s) => s.Name).join("，") }}</span>
      </template>
    </listSelector>
  </div>
</template>

<script>
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
import {
  BusinessOpportunitySourceType,
  BusinessOpportunityWeightType,
  BusinessOpportunityStatus,
} from "@/utils/commonEnum";

export default {
  name: "business-opportunity-selector",
  components: {
    listSelector,
  },
  filters: {
    /**状态 */
    statusFilter(data) {
      const tempValue = BusinessOpportunityStatus.find(
        (s) => s.value == data
      );
      if (tempValue) {
        return tempValue.label;
      }
      return "";
    },
    /**权重 */
    weightTypeFilter(data) {
      const tempValue = BusinessOpportunityWeightType.find(
        (s) => s.value == data
      );
      if (tempValue) {
        return tempValue.label;
      }
      return "";
    },
    /**来源 */
    sourceTypeFilter(data) {
      const tempValue = BusinessOpportunitySourceType.find(
        (s) => s.value == data
      );
      if (tempValue) {
        return tempValue.label;
      }
      return "";
    },
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },
  watch: {
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList(val) {
      if (val && val.length > 0) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
      }
    },
  },
  data() {
    return {
      listSelectorCheckedData: [],
      listSelectorUrl:
        serviceArea.business +
        "/BusinessOpportunity/GetBusinessOpportunityListPage",
      listSelectorMultiple: this.multiple,
      listQueryParams: {
        Year: '',
        Name: '',
        EmployeeName: '',
      },
      tableSearchItems: [
        { prop: "Year", label: "年份" },
        { prop: "Name", label: "机会点项目" },
        { prop: "EmployeeName", label: "负责人" },
      ],
      listSelectorTitle: "关联机会点",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",

      listSelectorColumnData: [

        {
          attr: { prop: "Name", label: "机会点项目" },
        },
        {
          attr: { prop: "Year", label: "年份", width: '100' },
          slot: true,
        },
        {
          attr: { prop: "Status", label: "状态", width: '120' },
          slot: true,
        },
        {
          attr: { prop: "ClientUnitName", label: "客户单位" },
        },
        // {
        //   attr: { prop: "PhaseName", label: "当前阶段" },
        // },
        // {
        //   attr: { prop: "WeightType", label: "权重" },
        //   slot: true,
        // },
        // {
        //   attr: { prop: "SourceType", label: "来源" },
        //   slot: true,
        // },
        {
          attr: { prop: "EmployeeList", label: "负责人" },
          slot: true,
        },
        // {
        //   attr: { prop: "BusinessWorth", label: "价值" },
        // },
        // {
        //   attr: {
        //     prop: "TransactionProbability",
        //     label: "成交概率",
        //     align: "center",
        //   },
        //   slot: true,
        // },
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  methods: {
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      this.listQueryParams.Year = ''
      this.listQueryParams.Name = ''
      this.listQueryParams.EmployeeName = ''
      this.handleFilter()
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let list =
        data.map((s) => {
          s.Year = s.Year;
          return s;
        }) || [];
      this.$emit("changed", JSON.parse(JSON.stringify(list)));
      this.listSelectorCloseDialog();
    },

  },
};
</script>

<style lang="scss" scoped>
.status-color-2 {
  background-color: #00B050;
}
.status-color-3 {
  background-color: #409eff;
}
.status-color-4 {
  background-color: red;
}
</style>
