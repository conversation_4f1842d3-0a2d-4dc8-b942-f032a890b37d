<!-- 借款申请打印组件 -->
<template>
  <div style="display: none">
    <div ref="printContent" class="print_container">
      <div class="print_title">借款单</div>
      <div class="print_top">
        <span>
          公司名称：
          <span class="f_bold">{{ getObjData(formData, "KingdeeDepartmentName") }}</span>
        </span>
        <span>申请日期：{{ getObjData(formData, "FBillDate") | dateFilter("YYYY/MM/DD") }}</span>
        <span>单据编号：{{ getObjData(formData, "FBillNo") }}</span>
      </div>
      <table class="print_table">
        <tr>
          <td class="label">申请人</td>
          <td class="content f_bold">{{ getSubmitEmployeeName }}</td>
          <td class="label">部门</td>
          <td class="content">{{ getObjData(formData, "DepartmentName") }}</td>
        </tr>
        <tr>
          <td class="label">开户银行</td>
          <td class="content">{{ getObjData(formData, "BankName") }}</td>
          <td class="label">银行账号</td>
          <td class="content">{{ getObjData(formData, "BankAccountNumber") }}</td>
        </tr>
        <tr>
          <td class="label">借款用途</td>
          <td class="content t_left" colspan="3">{{ getObjData(formData, "Purpose") }}</td>
        </tr>
        <tr>
          <td class="content" colspan="2">
            <div class="f_j_b">
              <span>借款金额：</span>
              <span class="f_bold">
                {{ formatThousands(getObjData(formData, "LoanAmount")) }}
              </span>
            </div>
          </td>
          <td class="content t_left" colspan="2">
            <div class="f_j_b">
              <span>金额(大写)：</span>
              <span>{{ convertToChinese(getObjData(formData, "LoanAmount")) }}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="content" colspan="4">
            <div class="f_j_b">
              <span>个人已借款金额：</span>
              <span>
                {{ formatThousands(getObjData(formData, "PreviousLoanAmount")) }}
              </span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="footer_row" colspan="4">
            <span v-for="(item, index) in flowList" :key="index">
              {{ item.role }}【{{ item.name }}】{{ index === flowList.length - 1 ? "" : "→" }}
            </span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import printMixins from "./printMixins.js";
export default {
  name: "LoanApplicationPrint",
  data() {
    return {
      // 必须得有  混入方法使用动态包裹对象key
      parcelKey: "LoanBillObj",
    };
  },
  mixins: [printMixins],
};
</script>

<style lang="scss">
@import "./printStyle.scss";
.print_container {
  .label {
    width: 15%;
    font-weight: normal;
  }

  .content {
    width: 35%;
  }
}
</style>
