<template>
  <app-dialog
    :title="pageTitle"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :width="500"
  >
    <template slot="body">
      <el-row class="wrapper">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <el-form-item label="题库分类" prop="value" v-if="componentType==1">
            <treeselect key='id'
              class="treeselect-common"
              :append-to-body="true"
              :normalizer="unitNormalizer"
              v-model="formData.value" :default-expand-level="3"
              :options="treeDatas" :multiple="false" placeholder :show-count="true"
              :noResultsText='noResultsTextOfSelTree'
              :noOptionsText="noOptionsTextOfSelTree"
              zIndex='9999'
              @input="hadnleChangeCustomerUnitId"
              >
            </treeselect>
          </el-form-item>
          <template v-if="componentType==2">
            <el-form-item label="难易等级" prop="value">
              <el-radio-group v-model="formData.value">
                <el-radio v-for="item in searchTypes.filter(s=>s.value!=0)" :key="item.value" :label="item.value">{{item.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-form>
      </el-row>
    </template>
    <template slot="footer">
      <!-- 取消 -->
      <el-button @click="handleClose">取消</el-button>
      <!-- 确定 -->
      <el-button @click="handleSubmit" type="primary" :disabled='disabledBtn'>确定</el-button>
    </template>
  </app-dialog>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import * as questionBankApi from '@/api/knowledge/questionBankManagement'
import * as classifyApi from '@/api/classify'
import { vars } from '../common/vars'
export default {
  name: "batch-classify",
  components: {
  },
  props: {
    componentType: { // 1.调整分类 2.难易等级
      type: Number,
      require: true,
      default: 1
    },
    ids: {
      type: Array,
      default() {
        return []
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.formData = this.$options.data().formData; // 重置提交表单字段为初始值
          this.formData.ids = this.ids
          this.formData.type = this.componentType
          this.rules.value.fieldName = this.componentType == 1 ? '题库分类' : '难易等级'
          this.rules = this.initRules(this.rules);
          this.loadTreeData()
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      searchTypes: vars.questionBankEnum.searchTypes, // 顶部筛选条件
      disabledBtn: false,
      rules: {
        value: { fieldName: "题库分类", rules: [{ required: true, trigger: 'change' }] },
      },
      labelWidth: "120px",
      formData: {
        type: 1,
        ids: [],
        value: null,
      },
      treeDatas: [],
      unitNormalizer(node) {
          // treeselect定义字段
          return {
            id: node.Id,
            label: node.Name,
            children: node.children
          }
      },
    };
  },
  computed: {
    pageTitle() {
      if(this.componentType == 1) {
        return '修改分类'
      }else if(this.componentType == 2) {
        return '设置时段'
      }
    },
  },
  methods: {
    hadnleChangeCustomerUnitId() {
      this.$refs.formData.validateField("value");
    },
    // 加载用户客户单位树列表
    loadTreeData() {
      let self = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 9
      };
      self.treeLoading = true
      classifyApi.getListPage(paramData).then(response => {
        self.treeLoading = false
        self.treeDatas = listToTreeSelect(response.Items);
      }).catch(err => {
        self.treeLoading = false
      });
    },
    // 弹窗提交
    handleSubmit() {
      let self = this;
      console.log(self.formData)
      self.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(self.formData));
          self.disabledBtn = true
          postData.value = postData.value + ''
          questionBankApi.BatchEdit(postData).then(res => {
              self.$notify({
                  title: "提示",
                  message: "保存成功",
                  type: "success",
                  duration: 2000
              });
              self.disabledBtn = false
              self.$refs.appDialogRef.createData();
          }).catch(err => {
              self.disabledBtn = false
          });
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style lang="scss" scoped>
.wrapper {
  padding: 10px 0;
  padding-right: 30px;
  min-height: 100px;
}
</style>