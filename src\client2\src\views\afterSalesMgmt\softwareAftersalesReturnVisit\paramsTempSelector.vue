<template>
    <div>
        <app-dialog title="选择参数模板" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600" :maxHeight="800">
            <template slot="body">
                <div class="wrapper">
                    <div class="lft" v-loading='treeLoading'>
                        <noData v-if="treeData.length == 0"></noData>
                        <template v-else>
                            <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                    <span class="node-title" :style="{width: node.level == 1 ? '161px' : node.level == 2 ? '143px' : '125px'}" :title="node.label">{{ node.label }}</span>
                                </span>
                            </el-tree>
                        </template>
                    </div>
                    <div class="rht">
                        <div v-loading='listLoading' class="item-list">
                            <noData v-if="!list && list.length == 0"></noData>
                            <template v-else>
                                <div class="list-wrapper">
                                    <div v-for="i in list" :key="i.value">
                                        <div class="title omit">
                                            <el-radio v-model="checkId" :label="i.value" @change='handleChagne'>{{ i.label }}</el-radio>
                                        </div>
                                    </div>
                                    <!-- <div class="omit" v-for="i in list" :key="i.value">
                                        <el-radio v-model="checkId" :label="i.value" @change='handleChagne'>{{ i.label }}</el-radio>
                                    </div> -->
                                </div>
                            </template>
                        </div>
                    </div>
                    
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import * as classify from "@/api/classify";
import * as afterVistTemplate from "@/api/afterSalesMgmt/afterVistTemplate";
import { listToTreeSelect, treeFilter } from '@/utils'

export default {
    name: "report-selector",
    directives: {},
    components: {
        noData,
    },
    mixins: [],
    computed: {
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
    },
    props: {
        isDeviceParams: {
            type: Boolean,
            default: true
        },
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        checked: {
            type: Object, //{ value: '', label: ''}
            default: () => {
                return null
            }
        },
        //创建时必须
        // workPlanId: {
        //     type: String,
        //     default: ''
        //     // validator: (val) => {
        //     //     if(this.dialogStatus == 'create') {
        //     //         return !!val
        //     //     }
        //     //     return true
        //     // }
        // },
        // //周报主键，查看详情时必填
        // id: {
        //     type: String,
        //     default: ''
        // }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(this.checked) {
                    this.checkId = this.checked.value
                    this.checkObj = JSON.parse(JSON.stringify(this.checked))
                }
                this.loadTreeData();
            },
            immediate: true
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.getList();
                }
            },
        },
    },
    created() {
    },
    data() {
        return {
            defaultProps: {
                children: "children",
                label: "Name"
            },
            checkedNode: null,
            
            checkIdPrefix: 'check--id--',
            
            disabledBtn: false,


            list: [],
            listLoading: false,

            checkedList: [],

            treeData: [],
            treeLoading: false,
            filterText: "",

            checkId: '',
            checkObj: null


        };
    },
    methods: {
        createData() {
            let result = JSON.parse(JSON.stringify(this.checkObj))
            this.$refs.appDialogRef.createData(result);
        },
        getList() {
            this.listLoading = true
            let postDatas = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: this.isDeviceParams ? 5 : 6,
                TemplateType: this.isDeviceParams ? 1 : 2
            }

            if(this.checkedNode) {
                postDatas.ClassifyId = this.checkedNode.Id;
            }

            afterVistTemplate.getList(postDatas).then(res => {
                this.listLoading = false
                this.list = res.Items.map(s => {
                    return {
                        label: s.Name,
                        value: s.Id
                    }
                });
                // this.total = res.Total;

            }).catch(err => {
                this.listLoading = false
            })
        },
        loadTreeData() {
            let _this = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                TemplateType: this.isDeviceParams ? 1 : 2,
                BusinessType: this.isDeviceParams ? 5 : 6
            };
            _this.treeLoading = true
            classify.getListPage(paramData).then(res => {
                _this.treeLoading = false
                let response = res.Items

                _this.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

                if (_this.treeData && _this.treeData.length > 0) {
                    if (
                        !(
                            _this.checkedNode &&
                            response.find(t => {
                                return t.Id == _this.checkedNode.Id;
                            })
                        )
                    ) {
                        _this.checkedNode = _this.treeData[0];
                    }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        }
                    });
                }

            }).catch(err => {
                _this.treeLoading = false
            });
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        handleChagne(val) {
            let obj = this.list.find(s => s.value == val)
            if(obj) {
                this.checkObj = obj
            }
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
}
</script>

<style lang="css" scoped>
.rht >>> .el-collapse-item__header{
    padding-left: 10px;
}
.rht >>> .el-radio__label{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: 340px;
}
</style>

<style lang='scss' scoped>
.wrapper{
    display: flex;
    .lft, .rht{
    }
    .lft{
        height: 600px;
        overflow-y: auto;
        width: 200px;
        border-right: 1px solid #DCDFE6;
        
    }
    .rht{
        flex: 1;
        
    }
}


.custom-tree-node {
    display: block;
    position: relative;
    box-sizing: border-box;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.item-list{
    height: 100%;
    .list-wrapper{
        height: 100%;
        width: 380px;
        >div{
            display: flex;
            align-items: center;
            padding: 10px;
            .title{
                width: 0;
                flex: 1;
            }
            // &:not(:last-child) {
            //     border-bottom: 1px solid #DCDFE6;
            // }
        }
    }
}
</style>