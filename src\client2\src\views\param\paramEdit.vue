<!--基础参数-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="600"
    >
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="120px"
        >
          <el-form-item :label="'基础参数ID'" prop="Id">
            <el-input
              maxlength="12"
              v-model.trim="formModel.Id"
              :disabled="dialogStatus != 'create'"
            ></el-input>
          </el-form-item>
          <el-form-item :label="'基础参数名称'" prop="Name">
            <el-input maxlength="25" v-model="formModel.Name" :disabled="!editable"></el-input>
          </el-form-item>
          <el-form-item :label="'参数单位'" prop="ParamUnitId">
            <el-select
              class="filter-item"
              v-model="formModel.ParamUnitId"
              placeholder="请选择"
              :disabled="!editable"
              :clearable="true"
              style="width: 100%;"
            >
              <el-option
                v-for="item in  paramUnits"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="'参数分类'" prop="ParamTypeCheck">
            <el-checkbox-group
              v-model="formModel.ParamTypeCheck"
              :disabled="!editable"
              @change="handleChange"
            >
              <el-checkbox v-for="t in paramTypes" :label="t.value" :key="t.value">{{t.label}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <el-checkbox v-show="dialogStatus=='create'" v-model="isContinue">连续添加</el-checkbox>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button
          :buttonType="1"
          v-show="editable"
          :disabled="buttonLoading"
          @click="handleButtonClick"
        ></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as param from "@/api/param";
import { ParamType } from "@/utils/commonEnum";

export default {
  /**名称 */
  name: "param-edit",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    /**弹窗类型 */
    dialogStatus: {
      type: String,
      default: "create"
    },
    /**节点类型Id */
    paramNodeId: {
      type: String
    },
    /**主键Id */
    id: {
      type: String
    }
  },
  /**数据区 */
  data() {
    return {
      /**连续添加 */
      isContinue: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      paramTypes: ParamType,
      paramUnits: [],
      /**表单模型 */
      formModel: {
        Id: "",
        Name: "",
        ParamNodeId: null,
        ParamUnitId: null,
        ParamType: null,
        ParamTypeCheck: []
      },
      /**表单规则 */
      formRules: {
        Id: { fieldName: "基础参数ID", rules: [{ required: true }] },
        Name: { fieldName: "基础参数名称", rules: [{ required: true }] }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      let title = "";
      switch (this.dialogStatus) {
        case "create":
          title = "添加基础参数";
          break;
        case "update":
          title = "修改基础参数";
          break;
        case "detail":
          title = "基础参数详情";
          break;
        default:
          title = "";
          break;
      }
      return title;
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.isContinue = false;
          _this.loadParamUnitData();
          if (_this.dialogStatus != "create" && _this.id) {
            param.getParamDetails({ Id: _this.id }).then(response => {
              response.ParamTypeCheck = [];
              response.ParamType & 8 && response.ParamTypeCheck.push(8);
              response.ParamType & 4 && response.ParamTypeCheck.push(4);
              response.ParamType & 2 && response.ParamTypeCheck.push(2);
              response.ParamType & 1 && response.ParamTypeCheck.push(1);
              _this.formModel = Object.assign({}, _this.formModel, response);
            });
          } else {
            _this.resetData();
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    /**加载参数单位下拉 */
    loadParamUnitData() {
      param.getParamUnitListByCondition({}).then(response => {
        this.paramUnits = response;
      });
    },
    /**重置 */
    resetData() {
      let _this = this;
      _this.formModel = {
        Id: "",
        Name: "",
        ParamNodeId: _this.paramNodeId,
        ParamUnitId: null,
        ParamType: null,
        ParamTypeCheck: []
      };
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;

          if (_this.dialogStatus == "create") {
            result = param.addParam(_this.formModel);
          } else if (_this.dialogStatus == "update") {
            result = param.editParam(_this.formModel);
          }

          result
            .then(response => {
              _this.buttonLoading = false;
              _this.$notify({
                title: "成功",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              _this.$refs.appDialogRef.createData();
              if (_this.isContinue) {
                _this.resetData();
              } else {
                _this.handleClose();
              }
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**多选 */
    handleChange(value) {
      if (value && value.length > 0) {
        this.formModel.ParamType = value.reduce((prev, curr, idx, arr) => {
          return prev + curr;
        });
      } else {
        this.formModel.ParamType = 0;
      }
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>






