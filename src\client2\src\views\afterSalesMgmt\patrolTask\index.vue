<!--巡检管理-->
<template>
  <!--组件内容区-->
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title
        title="巡检管理"
        :subTitle="['设备巡检任务的创建、跟进、管理页面']"
        :showBackBtn="!!returnUrl"
        @goBack="handleGoBack"
      ></page-title> -->
      <div class="pageWrapper __dynamicTabContentWrapper">
        <div class="__dynamicTabWrapper" style="width:100%;">
          <app-table
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="tabData"
            :tab-auth-columns="tabAuthColumns"
            :isShowAllColumn="true"
            :loading="tableLoading"
            @rowSelectionChanged="rowSelectionChanged"
            :isShowOpatColumn="true"
            :startOfTable="startOfTable"
            @sortChagned='handleSortChange'
            :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false'
          >
            <!-- 表格查询条件区域 -->
            <template slot="conditionArea">
              <div class="tags-wrapper">
                <tags :items="types" v-model="listQuery.IsMine">
                  <template v-for="t in types" :slot="t.value">{{
                    t.label
                  }}</template>
                </tags>
              </div>
              <app-table-form
                :label-width="'100px'"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="onResetSearch" :layoutMode='layoutMode'
              >
                <template slot="Name">
                    <el-input style="width: 100%;" 
                        placeholder="搜索任务名称..."
                        @clear='handleFilter'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                handleFilter()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.Name"
                    ></el-input>
                </template>
                <template slot="Code">
                  <el-input
                    style="width: 100%;"
                    v-model.trim="listQuery.Code"
                    placeholder
                  ></el-input>
                </template>
                <template slot="EmployeeName">
                  <el-input
                    style="width: 100%;"
                    v-model.trim="listQuery.EmployeeName"
                    placeholder
                  ></el-input>
                  <!-- <el-select
                    v-model="listQuery.ImplementerId"
                    style="width: 100%;"
                    placeholder="请选择"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in  implementerList"
                      :key="item.EmployeeId"
                      :label="item.Name"
                      :value="item.EmployeeId"
                    ></el-option>
                  </el-select> -->
                </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                <permission-btn
                    moduleName="position"
                    v-on:btn-event="onBtnClicked"
                ></permission-btn>
                </template>

              </app-table-form>
            </template>
            <!-- 自定义列 -->

            <template slot="PatrolTaskStatus" slot-scope="scope">
              <span
                :style="'color:#fff;background-color:' + getStatusObj(scope.row.PatrolTaskStatus).color + ';padding: 2px 4px;border-radius: 10%;'"
                >{{ getStatusObj(scope.row.PatrolTaskStatus).label }}</span
              >
            </template>
            <!-- <template slot="ApprovalStatus" slot-scope="scope">
              {{ scope.row.ApprovalStatus | approvalStatusFilter }}
            </template> -->
            <!-- <template slot="ApprovalStatus" slot-scope="scope">
                <span
                  v-if="getApprovalObj(scope.row.ApprovalStatus).label"
                  class="item-status"
                  :style="{
                    backgroundColor: getApprovalObj(scope.row.ApprovalStatus).color
                  }"
                >
                  <span>{{ getApprovalObj(scope.row.ApprovalStatus).label }}</span>
                </span>
                <span v-else>无</span>
            </template> -->
            <template slot="StartTime" slot-scope="scope">
              <span>{{ formatterTime(scope.row.StartTime) }}</span>
            </template>
            <template slot="EndTime" slot-scope="scope">
              <span>{{ formatterTime(scope.row.EndTime) }}</span>
            </template>
            <template slot="NeedCheck" slot-scope="scope">
              <span>{{ scope.row.NeedCheck + "台" }}</span>
            </template>
            <template slot="CheckFinished" slot-scope="scope">
              <span>{{ scope.row.CheckFinished + "台" }}</span>
            </template>
            <template slot="HasProblem" slot-scope="scope">
              <span>{{ scope.row.HasProblem + "台" }}</span>
              <span
                v-show="scope.row.IsRepair"
                :style="
                  'background-color:orange;color:#fff;padding: 2px 4px;border-radius: 10%;'
                "
                >已报修</span
              >
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <!-- 已完成且审批通过且存在异常设备 -->
              <app-table-row-button v-if="!scope.row.IsRepair && scope.row.PatrolTaskStatus == 3 && scope.row.HasProblem > 0" @click="handleRepair(scope.row)" text="报修" :type="2"></app-table-row-button>
              <!-- 跟进后有审批人，则不能再跟进 -->
              <app-table-row-button v-if="scope.row.IsImplementer && scope.row.PatrolTaskStatus != 3" @click="handleEdit(scope.row, 'followUp')" text="跟进" :type="2"></app-table-row-button>
              <!-- 有审批则不显示详情 -->
              <app-table-row-button @click="handleEdit(scope.row, 'detail')" text="详情" :type="2"></app-table-row-button>
              <!-- 未开始或进行中且没有提交审批的均可编辑 -->
              <app-table-row-button v-if="rowBtnIsExists('btnEdit') && (scope.row.PatrolTaskStatus == 1 || scope.row.PatrolTaskStatus == 2)" @click="handleEdit(scope.row, 'update')" text="编辑" :type="2"></app-table-row-button>
              <!-- 当前审批人可审批 -->
              <!-- <app-table-row-button
                v-if="scope.row.IsCurrentNodeApprovalOperatorEmployee"
                @click="handleEdit(scope.row,'approval')"
                text="审批"
                :type="2"
              ></app-table-row-button> -->
              <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
            </template>
          </app-table>
        </div>
        <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!--弹窗组件区-->
    <patrolTaskEdit
      :dialogStatus="patrolTaskEditDialogStatus"
      :keyId="checkedId"
      :dialogFormVisible="patrolTaskEditDialogFormVisible"
      @closeDialog="patrolTaskEditCloseDialog"
      @saveSuccess="patrolTaskEditSaveSuccess"
    ></patrolTaskEdit>
    <!--报修-->
    <maintenOrderMgmtCreate
      :dialogStatus="maintenOrderMgmtCreateDialogStatus"
      :row="maintenOrderMgmtCreateData"
      :isOutCreate="true"
      :dialogFormVisible="maintenOrderMgmtCreateDialogFormVisible"
      @closeDialog="maintenOrderMgmtCreateCloseDialog"
      @saveSuccess="maintenOrderMgmtCreateSaveSuccess"
    ></maintenOrderMgmtCreate>
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import patrolTaskEdit from "./edit";
import maintenOrderMgmtCreate from "@/views/afterSalesMgmt/maintenCenter/maintenOrderMgmt/create";
import * as patrolTask from "@/api/afterSalesMgmt/patrolTask";
import { StatusType } from "@/utils/commonEnum";
// import { vars } from "../../salesMgmt/common/vars";
import vExport from "@/components/Export/index";
export default {
  /**名称 */
  name: "patrol-task-index",
  mixins: [indexPageMixin],
  /**组件声明 */
  components: { patrolTaskEdit, maintenOrderMgmtCreate,vExport },
  /**参数区 */
  props: {
    /**主键Id */
    keyId: {
      type: String
    }
  },
  /**数据区 */
  data() {
    return {
            layoutMode: 'simple',
      rData:null,
      cData:[],
      dialogExportVisible:false,
      /******************* 表格 *******************/
      /**查询字段 */
      tableSearchItems: [
        { prop: "Name", label: "任务名称", mainCondition: true },
        { prop: "Code", label: "巡检任务编号" },
        { prop: "EmployeeName", label: "实施人员" }
      ],
      types: [
        { value: 0, label: "全部巡检任务" },
        { value: 1, label: "我处理的" }
      ],
      //      implementerList: [],
      /**查询条件 */
      listQuery: {
        IsMine: 0,
        PageIndex: 1,
        PageSize: 20,
        Name: "",
        Code: "",
        EmployeeName: null
      },
      /**表格列声明 */
      tabColumns: [
        {
          attr: {
            prop: "Code",
            label: "巡检任务编号",
          }
        },
        {
          attr: { prop: "Name", label: "任务名称", showOverflowTooltip: true  }
        },
        {
          attr: { prop: "PatrolTaskStatus", label: "状态", sortable: 'custom' },
          slot: true
        },
        // {
        //   attr: { prop: "ApprovalStatus", label: "审批状态" },
        //   slot: true
        // },
        {
          attr: {
            prop: "NeedCheck",
            label: "应检",
          },
          slot: true
        },
        {
          attr: {
            prop: "CheckFinished",
            label: "已检",
          },
          slot: true
        },
        {
          attr: {
            prop: "HasProblem",
            label: "异常",
            sortable: 'custom'
          },
          slot: true
        },
        {
          attr: {
            prop: "ImplementerNames",
            label: "实施人员",
          }
        },

        {
          attr: {
            prop: "StartTime",
            label: "开始时间",
            sortable: 'custom'
          },
          slot: true
        },
        {
          attr: {
            prop: "EndTime",
            label: "截止时间",
            sortable: 'custom'
          },
          slot: true
        }
      ],
      /**表格加载 */
      tableLoading: false,
      statusTypes: StatusType,
      /**表格数据 */
      tabData: [],
      /**表格选中行 */
      multipleSelection: [],
      /**选中单行Id */
      checkedId: null,
      /**分页数量 */
      total: 0,
      /******************* 组件 *******************/
      /**树节点添加弹窗 */
      patrolTaskEditDialogFormVisible: false,
      patrolTaskEditDialogStatus: "create",
      /**报修弹窗 */
      maintenOrderMgmtCreateData: {},
      maintenOrderMgmtCreateDialogStatus: "create",
      maintenOrderMgmtCreateDialogFormVisible: false
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    returnUrl() {
      let url = decodeURI(this.$route.query.returnUrl || "");
      return url;
    }
  },
  /**监听 */
  watch: {
    "listQuery.IsMine"(val) {
      if (val) {
        delete this.listQuery.EmployeeName;
        let idx = this.tableSearchItems.findIndex(
          s => s.prop == "EmployeeName"
        );
        if (idx > -1) {
          this.tableSearchItems.splice(idx, 1);
        }
      } else {
        this.$set(this.listQuery, "EmployeeName", "");
        this.tableSearchItems.splice(2, 0, {
          prop: "EmployeeName",
          label: "实施人员"
        });
      }
      this.listQuery.PageIndex = 1;
      this.loadTableData();
      this.collapseOrExpand()
    }
  },
  filters: {

  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {
    // this.loadImplementerList();
    this.loadTableData();
  },
  /**方法区 */
  methods: {
    collapseOrExpand(val){
      this.$nextTick(()=>{
        this.$refs.mainTable.setTabHeight()
      })
    },
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    // getApprovalObj(status) {
    //   return vars.orderMgmt.approvalStatus.find(s => s.value == status) || {};
    // },
    getStatusObj(status) {
      return this.statusTypes.find(
        o => o.value == status
      ) || {}
    },
    /**查询 */
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.loadTableData();
    },
    /**重置 */
    onResetSearch() {
      this.listQuery = {
        IsMine: this.listQuery.IsMine,
        PageIndex: 1,
        PageSize: 20,
        Name: "",
        Code: "",
        EmployeeName: ""
      };
      //      this.loadImplementerList();
      this.loadTableData(); //刷新列表
    },

    /******************* 表格事件 *******************/
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.loadTableData()
    },    
    /**加载表格数据 */
    loadTableData() {
      let _this = this;
      let postData = JSON.parse(JSON.stringify(_this.listQuery))
      postData = this.assignSortObj(postData)
      _this.tableLoading = true
      patrolTask.getListPage(postData).then(response => {
        _this.tableLoading = false
        _this.total = response.Total;
        _this.tabData = response.Items;
      }).catch(err => {
        _this.tableLoading = false
      });
    },
    // loadImplementerList() {
    //   patrolTask.getImplementerList().then((response) => {
    //     this.implementerList = response;
    //     if (
    //       this.listQuery.ImplementerId &&
    //       !response.find((t) => t.EmployeeId == this.listQuery.ImplementerId)
    //     ) {
    //       this.listQuery.ImplementerId = null;
    //     }
    //   });
    // },

    /**表头部点击 */
    onBtnClicked: function(type) {
      switch (type) {
        //添加
        case "btnAdd":
          this.patrolTaskEditDialogStatus = "create";
          this.patrolTaskEditDialogFormVisible = true;
          break;
        case "btnExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },
    handleExport(){
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      this.rData={
          "exportSource": 15,
          "columns": [],
          "searchCondition": postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'巡检任务编号',
        value:'Code'
      },{
        label:'任务名称',
        value:'Name'
      },{
        label:'状态',
        value:'PatrolTaskStatus'
      },
      // {
      //   label:'审批状态',
      //   value:'ApprovalStatus'
      // },
      {
        label:'应检',
        value:'NeedCheck'
      },{
        label:'异常',
        value:'HasProblem'
      },{
        label:'实施人员',
        value:'ImplementerNames'
      },{
        label:'开始时间',
        value:'StartTimeString'
      },{
        label:'截止时间',
        value:'EndTimeString'
      }]
      this.dialogExportVisible=true;
    },
    /**表格行选中 */
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
      this.checkedId = rows[0].Id;
    },
    /**报修 */
    handleRepair(row) {
      let _this = this;
      _this.checkedId = row.Id;
      console.log(666666666,row)
      patrolTask
        .getEquipmentListByPatrolTaskId({ id: row.Id })
        .then(response => {
          
          _this.$nextTick(() => {
            _this.maintenOrderMgmtCreateDialogStatus = "create";
            _this.maintenOrderMgmtCreateData = {
              RegionalId: response.RegionalId,
              AreaName:response.RegionalName,
              MaintenanceEquipmentList: response
            };
            _this.maintenOrderMgmtCreateDialogFormVisible = true;
          });
        });
    },
    /**报修弹窗保存成功 */
    maintenOrderMgmtCreateSaveSuccess() {
      let _this = this;
      patrolTask.setIsRepair({ id: _this.checkedId }).then(response => {
        //        _this.loadImplementerList();
        _this.loadTableData();
        _this.maintenOrderMgmtCreateCloseDialog();
      });
    },
    /**报修弹窗关闭 */
    maintenOrderMgmtCreateCloseDialog() {
      let _this = this;
      _this.maintenOrderMgmtCreateDialogFormVisible = false;
    },
    /**表格行编辑、详情、跟进 */
    handleEdit(row, optType) {
      this.checkedId = row.Id;
      this.patrolTaskEditDialogStatus = optType;
      this.patrolTaskEditDialogFormVisible = true;
    },
    /**表格行删除 */
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        patrolTask.del([row.Id]).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          //          this.loadImplementerList();
          this.loadTableData();
        });
      });
    },
    /**分页页大小变更 */
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      //      this.loadImplementerList();
      this.loadTableData();
    },
    /**分页页码变更 */
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      //      this.loadImplementerList();
      this.loadTableData();
    },
    isShowApprovalBtn(row) {
      return row.IsCurrentNodeApprovalOperatorEmployee;
    },
    /******************* 弹窗 *******************/
    /**编辑弹窗保存成功 */
    patrolTaskEditSaveSuccess() {
      this.listQuery.PageIndex = 1;
      //      this.loadImplementerList();
      this.loadTableData();
      // this.patrolTaskEditCloseDialog();
    },
    /**编辑弹窗关闭 */
    patrolTaskEditCloseDialog() {
      this.patrolTaskEditDialogFormVisible = false;
    },
    /**时间格式化 */
    formatterTime(val) {
      let _this = this;
      return _this.$options.filters["dateFilter"](val, "YYYY-MM-DD HH:mm");
    },
    handleGoBack() {
      this.$router.push({ path: this.returnUrl });
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  // top: 40px;
  top: 0;
  right: 0;
  bottom: 0;
  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
    // >div:first-child{
    //     display: flex;
    //     justify-content: space-between;
    //     align-items:center;
    //     padding:0 10px;
    // }
  }
  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;
    .content {
      // padding: 10px;
      // padding-right: 10;
      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }
    }
  }

  .tags-wrapper {
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
    // margin-bottom: 10px;
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;
    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
