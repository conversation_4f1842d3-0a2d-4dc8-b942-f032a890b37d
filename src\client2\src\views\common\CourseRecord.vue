<template>
  <div class="course_record_container" v-loading="loading">
    <div class="time_line_wrapper" v-if="list.length">
      <div
          class="step_item"
          v-for="(item, index) in list"
          :key="index"
          :class="{ last_step_item: index === list.length - 1 }"
      >
        <div class="step_label">
          <span>{{ dateFormat(item.CreateTime).date }}</span>
          <span>{{ dateFormat(item.CreateTime).week }}</span>
        </div>
        <div class="content">
          <div class="operate_person">
            <img :src="item.OperationEmployee.Avatar" class="avatar"/>
            <span class="name">{{ item.OperationEmployee.Name }}</span>
            <span class="operation_title">{{ item.OperationTitle }}</span>
          </div>
          <div class="edit_list">
            <div class="edit_item" v-for="(edit, e) in item.CommonHistoryBlockPageModels" :key="e">
              <div class="edit_title">{{ edit.BlockTitle }}</div>
              <div class="group_list">
                <div
                    class="group_cell"
                    v-for="(group, g) in edit.CommonHistoryLinePageModels"
                    :key="g"
                >
                  <div class="cell" v-for="(cell, c) in group" :key="c">
                    <!-- 1,9只显示一个ActionName -->
                    <span
                        class="action_name"
                        :class="{ one_line: [1,9].includes(cell.ProcessStateEnum)}"
                        v-if="cell.ActionName"
                    >
                      {{ cell.ActionName }}
                    </span>
                    <div class="cell_content" v-if="![1,9].includes(cell.ProcessStateEnum)">
                      <!--展开按钮  目前只有markdown用到-->
                      <div class="a-link" style="margin-right:5px"  @click="handleUnfold(cell)" v-if="cell.ProcessStateEnum=== 13">
                        {{cell.isUnfold ? '收起':'展开'}}
                      </div>

                      <!-- 旧值 -->
                      <div class="old_value" :class="{'flex_1':cell.ProcessStateEnum === 13}">
                        <span>{{ oldValue(cell) }}</span>
                        <!-- 5,7显示头像或者人名 -->
                        <template v-if="[5, 7, 15].includes(cell.ProcessStateEnum)">
                          <div class="flex_a_c" v-for="(person, p) in cell.OldValue" :key="p">
                            <img
                                v-if="getPerson(person).avatar"
                                :src="getPerson(person).avatar"
                                :title="getPerson(person).name"
                                class="avatar"
                            />
                            <span v-else class="person_name">{{ getPerson(person).name }}</span>
                          </div>
                        </template>
                        <!--markdown-->
                        <div v-if="cell.ProcessStateEnum === 13" class="markdown_container" :class="{unfold:cell.isUnfold}">
                          <appVditor :readonly="true" v-model="cell.OldValue"/>
                        </div>
                      </div>

                      <!-- 中间分割符号或占位 -->
                      <div class="division" v-if="isShowDivision(cell)">
                        <svg-icon
                            icon-class="jiantou"
                            class="arrows_icon"
                            v-if="[2, 4, 13].includes(cell.ProcessStateEnum)"
                        />
                      </div>

                      <!-- 新值 -->
                      <div class="new_value" :class="{'flex_1':cell.ProcessStateEnum=== 13}">
                        <span>{{ newValue(cell) }}</span>
                        <!-- 显示头像或者人名 -->
                        <template v-if="[5, 7, 10, 15 ,16].includes(cell.ProcessStateEnum)">
                          <div class="flex_a_c" v-for="(person, p) in cell.NewValue" :key="p">
                            <img
                                v-if="getPerson(person).avatar"
                                :src="getPerson(person).avatar"
                                :title="getPerson(person).name"
                                class="avatar"
                                :class="{'first_m_l_20':cell.ProcessStateEnum !== 16}"
                            />
                            <span v-else class="person_name">{{ getPerson(person).name }}</span>
                          </div>
                        </template>
                        <!--参考文件-->
                        <div v-if="cell.ProcessStateEnum === 12" class="file_list">
                          <div class="flex_a_c" v-for="(file, fileIndex) in cell.NewValue" :key="fileIndex">
                            <span class="file_operate">{{ file.Operation }}</span>
                            <svg-icon icon-class="wenjianjia" class="color-warning file_icon"/>
                            <span :class="{'a-link':file.Id}" @click="handleFile(file)">{{ file.Name }}</span>
                          </div>
                        </div>
                        <!--markdown-->
                        <div v-if="cell.ProcessStateEnum === 13" class="markdown_container" :class="{unfold:cell.isUnfold}">
                          <appVditor :readonly="true" v-model="cell.NewValue"/>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="time_line_wrapper" v-else>
      <noData/>
    </div>
    <div class="pagination_container">
      <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
      />
    </div>

    <!-- 附件 -->
    <create-file-page
        v-if="showFileDialog"
        @closeDialog="showFileDialog = false"
        @saveSuccess="showFileDialog = false"
        :dialogFormVisible="showFileDialog"
        dialogStatus="detail"
        :id="fileId"
    />
  </div>
</template>

<script>
import dayjs from "dayjs";
import {getCourseRecordList} from "@/api/common";
import noData from "@/views/common/components/noData";
import createFilePage from "@/views/workbench/mainLineMgmt/components/guidepostInfo/createFile.vue";

export default {
  components: {
    createFilePage,
    noData,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      list: [],
      listQuery: {
        PageSize: 20,
        PageIndex: 1,
      },
      total: 0,
      loading: false,
      fileId: null,
      showFileDialog: false,
    };
  },
  computed: {
    dateFormat() {
      return date => {
        if (!date) return {week: "", date: ""};
        const weekMap = {
          0: "周日",
          1: "周一",
          2: "周二",
          3: "周三",
          4: "周四",
          5: "周五",
          6: "周六",
        };
        const curDate = dayjs(date).format("YYYY/MM/DD HH:mm");
        const week = dayjs(date).day();
        return {week: weekMap[week], date: curDate};
      };
    },
    oldValue() {
      return item => {
        if ([2, 4].includes(item.ProcessStateEnum) && item.OldValue === null) {
          return "无";
        } else if ([5, 7, 15].includes(item.ProcessStateEnum)) {
          return item.OldValue?.length ? "移除" : "";
        } else if ([3,13].includes(item.ProcessStateEnum)) {
          return ""
        } else {
          return item.OldValue;
        }
      };
    },
    newValue() {
      return item => {
        if ([5, 7, 15].includes(item.ProcessStateEnum)) {
          return item.NewValue?.length ? "新增" : "";
        } else if ([9, 10, 12, 13, 16].includes(item.ProcessStateEnum)) {
          return ""
        } else {
          return item.NewValue;
        }
      };
    },
    // 是否显示中间分割符号或占位
    isShowDivision() {
      return item => {
        if ([3, 10, 12, 16].includes(item.ProcessStateEnum)) return false

        if (
            [5, 7].includes(item.ProcessStateEnum) &&
            !(item.OldValue?.length && item.NewValue?.length)
        ) {
          return false;
        }

        return true;
      };
    },
    getPerson() {
      return item => {
        return {name: item?.Name || "", avatar: item?.Avatar || ""};
      };
    },
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getCourseRecordList({BusinessId: this.id, ...this.listQuery})
          .then(res => {
            console.log("修改记录", this.$_.cloneDeep(res.Items));
            this.list = res.Items;
            this.total = res.Total;
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
    },
    // 点击文件
    handleFile(item) {
      if (!item.Id) return
      this.fileId = item.Id
      this.showFileDialog = true;
    },
    // 展开
    handleUnfold(item){
      this.$set(item,'isUnfold',!item.isUnfold)
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.course_record_container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 300px;
  .time_line_wrapper {
    flex: 1;
    height: 0;
    padding: 0 10px 10px 10px;
    overflow: hidden;
    overflow-y: auto;
    .step_item {
      display: flex;
      position: relative;
      &::after {
        content: "";
        position: absolute;
        right: 0;
        bottom: 0;
        display: block;
        width: calc(100% - 140px);
        height: 1px;
        background: $border-color-light;
      }
      &.last_step_item {
        &::after {
          content: "";
          height: 0 !important;
        }
        .content::after {
          content: "";
          height: 0 !important;
        }
      }
      .step_label {
        width: 120px;
        color: $text-regular;
        text-align: right;
        padding-right: 10px;
        padding-top: 8px;
        span {
          display: block;
        }
      }
      .content {
        flex: 1;
        width: 0;
        padding: 10px 0 0 20px;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 5px;
          left: 0;
          display: block;
          width: 10px;
          height: 10px;
          border-radius: $border-radius-circle;
          border: 2px solid $border-color-lighter;
          background: #fff;
          margin-top: 10px;
          z-index: 10;
        }
        &::after {
          content: "";
          position: absolute;
          top: 20px;
          left: 0;
          display: block;
          width: 1px;
          height: 100%;
          background-color: $border-color-lighter;
          transform: translateX(5px);
          z-index: 1;
        }
        .avatar {
          width: 20px;
          height: 20px;
          border-radius: 50px;
        }
        .operate_person {
          display: flex;
          align-items: center;
          color: $text-primary;
          .name {
            margin-left: 10px;
          }
          .operation_title {
            margin-left: 10px;
          }
        }
        .edit_list {
          .edit_item {
            display: flex;
            padding: 10px 0;
            .edit_title {
              width: 120px;
              padding-right: 20px;
              line-height: 24px;
              color: $text-secondary;
            }
            .group_list {
              flex: 1;
              width: 0;
              .group_cell {
                padding-left: 5px;
                border-left: 2px solid $border-color-lighter;
                margin-bottom: 10px;
                &:last-of-type {
                  margin-bottom: 0;
                }
                .cell {
                  padding: 2px 0 2px 5px;
                  display: flex;
                  align-items: center;
                  .action_name {
                    display: block;
                    width: 100px;
                    padding-right: 15px;
                    color: $text-secondary;
                    &.one_line {
                      flex: 1;
                    }
                  }
                  .cell_content {
                    flex: 1;
                    width: 0;
                    display: flex;
                    align-items: center;
                    .old_value {
                      max-width: calc(50% - 18px);
                      display: flex;
                      align-items: center;
                      color: $text-secondary;
                      .avatar {
                        margin-right: 5px;
                        &:first-of-type {
                          margin-left: 10px;
                        }
                        &:last-of-type {
                          margin-right: 0;
                        }
                      }
                      .person_name {
                        padding: 0 5px;
                      }
                    }
                    .new_value {
                      max-width: calc(50% - 18px);
                      display: flex;
                      align-items: center;
                      color: $text-regular;
                      .avatar {
                        margin-right: 5px;
                        &.first_m_l_20{
                          &:first-of-type {
                            margin-left: 10px;
                          }
                        }
                        &:last-of-type {
                          margin-right: 0;
                        }
                      }
                      .person_name {
                        padding: 0 5px;
                      }
                      .file_list {
                        .file_operate {
                          margin-right: 5px;
                        }
                        .file_icon {
                          font-size: 16px;
                          margin-right: 2px;
                        }
                      }
                    }
                    .division {
                      width: 36px;
                      display: flex;
                      justify-content: center;
                      .arrows_icon {
                        font-size: 16px;
                        color: $text-regular;
                        cursor: auto;
                      }
                    }
                    .markdown_container {
                      display: flex;
                      flex-direction: column;
                      /deep/ .vditor-reset {
                        height: 25px;
                        overflow: hidden !important;
                      }
                      &.unfold{
                        /deep/.vditor-reset{
                          height: auto;
                          display: block !important;
                        }
                      }
                    }
                    .flex_a_c {
                      display: flex;
                      align-items: center;
                    }
                    .flex_1 {
                      flex: 1;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .pagination_container {
    border-top: 1px solid $border-color-light;
  }
}
</style>
