import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
// import news from './modules/news'
// import organization from './modules/organization'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import indexPageParas from './modules/indexPageParas'
import common from './modules/common'
import task from './modules/task'
import getters from './getters'

import communication from './modules/communication'
Vue.use(Vuex)

const store = new Vuex.Store({
  state: {
    BadgeNumber: 0,
    IsRefreshMessage: 0
  },
  mutations: {
    //设置消息中心未读消息数量
    setBadgeNumber(state, value) {
      localStorage.setItem("BadgeNumber", parseInt(value));
      state.BadgeNumber = parseInt(value);
    },
    //设置 消息列表页面 点击消息图标重复刷新 标识
    setIsRefreshMessage(state, value) {
      state.IsRefreshMessage = value;
    }
  },
  modules: {
    app,
    user,
    permission,
    tagsView,
    // news,
    // organization,
    indexPageParas,
    common,
    communication,
    task,
  },
  getters
})

export default store
