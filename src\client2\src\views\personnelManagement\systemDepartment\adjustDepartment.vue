<template>
  <div>
    <app-dialog title="调整部门" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='700' :width='600'>
      <template slot="body">
        <el-input prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="departmentTreeFilterText">
        </el-input>

        <el-tree class="filter-tree" :data="departmentTreeData" :props="departmentTreeDefaultProps" default-expand-all :expand-on-click-node="false" :filter-node-method="departmentTreeFilterNode" @node-click="handleSelectClick" ref="departmentTree">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span class="node-title" :title="node.label">
                {{ node.label }}
            </span>
          </span>
        </el-tree>

      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="createData" :disabled="this.selectDepartmentId == ''" :buttonType='1'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import { listToTreeSelect } from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as systemEmployee from '@/api/personnelManagement/systemEmployee'

export default {
  name: "adjustDepartment",
  directives: {},
  components: {},
  mixins: [],
  props: {
    node: {
      type: Array,
      required: true
    },
  },
  watch: {
    departmentTreeFilterText(val) {
      this.$refs.departmentTree.filter(val);
    },
    '$attrs.dialogFormVisible'(val) {
      this.selectDepartmentId = ''
      this.getDepartmentTree()
    }
  },

  created() { },
  data() {

    return {
      departmentTreeFilterText: '',
      departmentTreeData: [],
      departmentTreeDefaultProps: {
        children: 'children',
        label: 'label'
      },
      selectDepartmentId: ''
    };
  },
  methods: {
    departmentTreeFilterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    getDepartmentTree() {
      systemDepartment.getListByCondition({}).then(res => {
        var departments = res.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.DepartmentName,
            ParentId: item.ParentId
          };
        });
        var departmentList = listToTreeSelect(departments);
        this.departmentTreeData = departmentList
      })
    },

    handleSelectClick(data, node) {
      this.selectDepartmentId = data.Id;
      //this.selectDepartmentId = this.$refs.departmentTree.getCurrentNode();
    },

    createData() {
      var param = {
        ids: this.node,
        departmentId: this.selectDepartmentId
      };
      systemEmployee.adjustDepartment(param).then(res => {
        this.$notify({
          title: "提示",
          message: "调整成功",
          type: "success",
          duration: 2000
        });
        this.$refs.appDialogRef.createData()
      })
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
  }
};
</script>

<style lang="scss" scoped>
.custom-tree-node{
  width: 100%;
}
.node-title {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: calc(100% - 20px);
}
</style>
