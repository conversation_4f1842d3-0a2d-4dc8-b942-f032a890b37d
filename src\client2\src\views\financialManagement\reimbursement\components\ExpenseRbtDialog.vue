<!-- 费用报销弹窗 -->
<template>
  <app-dialog
    ref="appDialogRef"
    :width="1000"
    className="clear-padding"
    :title="title"
    v-bind="$attrs"
    v-on="$listeners"
    :beforeClose="beforeClose"
  >
    <div slot="body" class="body_wrapper" v-loading="loading">
      <div class="main_container">
        <el-form
          :model="formData.FinanceExpenseObj"
          ref="formRef"
          :rules="rules"
          label-width="100px"
          style="padding-top: 0"
          class="form_container"
        >
          <div class="left_container">
            <el-form-item
              label="撤销原因"
              prop="RevocationCause"
              v-if="
                dialogStatus == 'revoke' ||
                dialogStatus == 'revokeApproval' ||
                (dialogStatus == 'detail' && formData.RevocationStatus > 0)
              "
            >
              <el-input
                :disabled="dialogStatus !== 'revoke'"
                type="textarea"
                :rows="4"
                maxlength="500"
                v-model="formData.FinanceExpenseObj.RevocationCause"
              />
            </el-form-item>
            <el-form-item label="公司名称" prop="KingdeeDepartmentNumber">
              <CompanySelect
                v-model="formData.FinanceExpenseObj.KingdeeDepartmentNumber"
                :disabled="disabled"
                @change="handleChangeCompany"
              />
            </el-form-item>
            <el-form-item label="填报日期" prop="FBillDate">
              <el-date-picker
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="formData.FinanceExpenseObj.FBillDate"
                type="date"
                placeholder="请选择填报日期"
                :disabled="disabled"
                :picker-options="pickerOptions"
                :clearable="false"
              />
            </el-form-item>
            <el-form-item label="部门" prop="DepartmentId">
              <DepartmentSelect
                :value="formData.FinanceExpenseObj.DepartmentId"
                :disabled="disabled"
                @change="changeDepartment"
              />
            </el-form-item>
            <el-divider />
            <!-- 报销明细 -->
            <div class="reimbursement_list">
              <div
                class="reimbursement_item"
                v-for="(item, index) in formData.FinanceExpenseObj.FinanceExpensesDetailList"
                :key="index"
              >
                <div class="reimbursement_top">
                  <span>报销明细{{ index + 1 }}</span>
                  <app-table-row-button
                    :type="3"
                    text="删除"
                    v-if="!disabled"
                    @click="deleteExpenseRbt(index)"
                  />
                </div>
                <div class="reimbursement_content">
                  <el-form-item
                    label="用途"
                    :key="`purpose_${index}`"
                    :prop="`FinanceExpensesDetailList.${index}.Purpose`"
                    :rules="[{ required: true, message: '请输入用途', trigger: 'blur' }]"
                  >
                    <el-input
                      v-model="item.Purpose"
                      :disabled="disabled"
                      placeholder="请输入用途"
                      clearable
                      :maxlength="30"
                    />
                  </el-form-item>
                  <el-form-item
                    label="金额"
                    :key="`amount_${index}`"
                    :prop="`FinanceExpensesDetailList.${index}.Amount`"
                    :rules="[{ required: true, message: '请输入金额', trigger: 'blur' }]"
                  >
                    <el-input
                      v-model="item.Amount"
                      :disabled="disabled"
                      placeholder="请输入金额"
                      type="text"
                      v-thousands="true"
                      :maxlength="15"
                      clearable
                    >
                      <span slot="append">元</span>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="备注" prop="Remarks">
                    <el-input
                      v-model="item.Remarks"
                      placeholder="请输入备注"
                      type="textarea"
                      :rows="3"
                      :disabled="disabled"
                      :maxlength="500"
                    />
                  </el-form-item>
                  <el-form-item label="发票" prop="InvoiceList">
                    <InvoiceList v-model="item.InvoiceList" :disabled="disabled" />
                  </el-form-item>
                  <el-form-item label="附件" prop="AttachmentList">
                    <AttachmentList v-model="item.AttachmentList" :disabled="disabled" />
                  </el-form-item>
                </div>
                <el-divider />
              </div>
              <el-button @click="addExpenseRbt" v-if="!disabled" size="mini">
                新增报销明细
              </el-button>
            </div>
            <!-- 合计 -->
            <div class="total_container">
              <div class="total_item">
                <span>合计金额:</span>
                <span>{{ formatThousands(totalAmount) }} 元</span>
              </div>
              <div class="total_item">
                <span>金额(大写):</span>
                <span>{{ convertToChinese(totalAmount) }}</span>
              </div>
            </div>
            <el-form-item label="原借款" prop="OriginalLoan" style="margin-top: 10px">
              <el-input
                v-model="formData.FinanceExpenseObj.OriginalLoan"
                placeholder="请输入原借款"
                :disabled="disabled"
                type="text"
                clearable
                v-thousands="true"
                :maxlength="15"
              >
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
          </div>
          <div class="right_container">
            <!-- 关联研发项目 -->
            <AddProject
              :companyId="formData.FinanceExpenseObj.KingdeeDepartmentNumber"
              v-model="formData.FinanceExpenseObj.KingdeeProjectList"
              :disabled="disabled"
            />
            <el-divider />
            <el-form-item label="报销人">
              <empSelector
                :readonly="true"
                :showType="2"
                :multiple="true"
                placeholder="输入工号/姓名"
                :isAutocomplete="true"
                :collapseTags="false"
                :list="formData.FinanceExpenseObj.SubmitEmployeeList"
                :beforeConfirm="() => true"
              />
            </el-form-item>
            <el-form-item label="领款人" prop="PayeeEmployeeList">
              <empSelector
                :readonly="disabled"
                :showType="2"
                :multiple="false"
                placeholder="输入工号/姓名"
                :isAutocomplete="true"
                :collapseTags="false"
                :list="formData.FinanceExpenseObj.PayeeEmployeeList"
                :beforeConfirm="handleBeforePerson"
                @change="handleChangePerson"
              />
            </el-form-item>
            <el-form-item label="票据及附件数">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ fileTotal }}</div>
              </div>
            </el-form-item>
            <el-form-item label="单据编号" v-if="disabled">
              <div class="el-input is-disabled el-input--small">
                <div class="el-input__inner">{{ formData.FinanceExpenseObj.FBillNo }}</div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 审批区域 -->
      <div class="body_footer">
        <approvalPanel
          v-if="!disabled"
          :editable="isEditApprove"
          ref="approvalPanel"
          :approvalPanelObj="formData.Approval"
        />
        <approvalDetail
          :isOnlyViewDetail="isOnlyViewDetail || !isApprovalor"
          v-if="['approval', 'detail', 'revoke', 'revokeApproval'].includes(dialogStatus)"
          ref="approvalDetail"
          :dialogStatus="dialogStatusTrans"
          :approvalObj="formData.Approval"
        />
      </div>

      <ExpenseRbtDialog
        v-if="showExpenseRbtDialog"
        :dialogFormVisible="showExpenseRbtDialog"
        :tempObj="tempObj_"
        dialogStatus="create"
        :id="id"
        :approvalId="approvalId"
        :processId="tempObj_.HRApprovalProcessId"
        @closeDialog="closeSubDialog"
      />
    </div>
    <div slot="footer">
      <el-button
        v-if="['revokeApproval', 'detail'].includes(dialogStatus) && isCurrentUser"
        @click="handleReferenceCreate"
      >
        引用创建
      </el-button>
      <app-button @click="beforeClose" :buttonType="2" :loading="btnLoading" />
      <el-button
        v-if="['create', 'editDraft'].includes(dialogStatus)"
        @click="handleSaveDraft"
        :loading="btnLoading"
      >
        暂存草稿
      </el-button>
      <app-button
        @click="createData"
        :buttonType="1"
        v-if="dialogStatus != 'approval' && (!disabled || dialogStatus == 'revoke')"
        :loading="btnLoading"
        style="margin-left: 10px"
      />
      <el-button
        @click="handleApproval"
        type="primary"
        :loading="btnLoading"
        v-if="
          (dialogStatus == 'approval' || (dialogStatus == 'revokeApproval' && !isOnlyViewDetail)) &&
          isApprovalor
        "
        style="margin-left: 10px"
      >
        审批
      </el-button>
    </div>
  </app-dialog>
</template>

<script>
import dayjs from "dayjs";
import Decimal from "decimal.js";
import * as approvalManagement from "@/api/approvalManagement.js";
import approvalMixins from "@/mixins/approvalPatch.js";
import { getUserInfo } from "@/utils/auth";
import expenseFormDialogMixins from "./expenseFormDialogMixins.js";

export default {
  name: "expenseRbtDialog",
  mixins: [approvalMixins, expenseFormDialogMixins],
  components: {
    ExpenseRbtDialog: () => import("./ExpenseRbtDialog"),
  },
  data() {
    return {
      // 表单parcelKey对应后端详情数据的包裹对象mixins使用
      parcelKey: "FinanceExpenseObj",
      formData: {
        FinanceExpenseObj: {
          FBillNo: "",
          KingdeeDepartmentNumber: null,
          FBillDate: null,
          DepartmentId: null,
          OriginalLoan: "",
          RevocationCause: "",
          // 报销明细
          FinanceExpensesDetailList: [],
          // 报销人
          SubmitEmployeeList: [],
          // 领款人
          PayeeEmployeeList: [],
          // 项目
          KingdeeProjectList: [],
        },
        Type: 10, //费用报销
        ApprovalStatus: 0, //审批状态
        HrApprovalProcessId: null,
        //审批信息
        Approval: {
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          ApprovalOperatorEmployeeList: [], //已审批人员
          NoApprovalEmployeeList: [], //未审批人员
          CCEmployeeList: [], //抄送人
          ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
          ApprovalState: 1, //1: 进行中; 2: 已完成
          ApprovalResult: 1, //1: 通过； 2：不通过
        },
      },
      showExpenseRbtDialog: false,
      tempObj_: null,
      rules: {
        KingdeeDepartmentNumber: [{ required: true, message: "请选择公司名称", trigger: "change" }],
        FBillDate: [{ required: true, message: "请选择填报日期", trigger: "change" }],
        DepartmentId: [{ required: true, message: "请选择部门", trigger: "change" }],
        PayeeEmployeeList: [{ required: true, message: "请选择领款人", trigger: "change" }],
        RevocationCause: [{ required: true, message: "请输入撤销原因", trigger: "blur" }],
      },
    };
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.closeDialog();
          return;
        }
        if (this.dialogStatus == "create") {
          if (this.tempObj) {
            this.formData = Object.assign(this.formData, this.$_.cloneDeep(this.tempObj));
          } else {
            this.formData.FinanceExpenseObj.FBillDate = dayjs().format("YYYY-MM-DD");
            this.addExpenseRbt();
            const { employeeid, empName, empNumber, deptId } = getUserInfo();
            const oneself = {
              Name: empName,
              Number: empNumber,
              EmployeeId: employeeid,
            };
            this.changeDepartment(deptId)
            this.formData.FinanceExpenseObj.SubmitEmployeeList.push(oneself);
            this.formData.FinanceExpenseObj.PayeeEmployeeList.push(oneself);
          }
        } else {
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  created() {},
  computed: {
    title() {
      const titleMap = {
        create: "费用报销申请",
        detail: "费用报销申请详情",
        approval: "费用报销申请审批",
        revoke: "(撤销)费用报销申请",
        revokeApproval: "(撤销)费用报销申请",
      };
      return titleMap[this.dialogStatus] || "费用报销申请";
    },
    pickerOptions() {
      return {
        disabledDate: time => {
          return dayjs(time).isAfter(dayjs(), "day");
        },
      };
    },
    totalAmount() {
      return this.formData.FinanceExpenseObj.FinanceExpensesDetailList.reduce((total, item) => {
        return total.plus(Decimal(this.parseThousands(item.Amount || 0)));
      }, Decimal(0));
    },
    // 票据及附件数
    fileTotal() {
      const list = this.formData.FinanceExpenseObj.FinanceExpensesDetailList;
      const invoiceTotal = list.reduce((total, item) => {
        return total + item.InvoiceList.length;
      }, 0);
      const attachmentTotal = list.reduce((total, item) => {
        return total + item.AttachmentList.length;
      }, 0);
      return invoiceTotal + attachmentTotal;
    },
  },
  methods: {
    // 添加报销明细
    addExpenseRbt() {
      if (this.formData.FinanceExpenseObj.FinanceExpensesDetailList.length >= 5) {
        this.$message.error("报销明细最多只能添加5条");
        return;
      }
      this.formData.FinanceExpenseObj.FinanceExpensesDetailList.push({
        Purpose: "", //用途
        Amount: "", //金额
        Remarks: "", //备注
        InvoiceList: [], //发票
        AttachmentList: [], //附件
      });
    },
    // 删除报销明细
    deleteExpenseRbt(index) {
      if (this.formData.FinanceExpenseObj.FinanceExpensesDetailList.length === 1) {
        this.$message.error("至少保留一条报销明细");
        return;
      }
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formData.FinanceExpenseObj.FinanceExpensesDetailList.splice(index, 1);
      });
    },
    /**
     * 创建报销单
     * @param temporize true:保存草稿 false:发起审批
     */
    createRequest(temporize = false) {
      this.formData.Approval = this.$refs.approvalPanel.getData(); //审批层区块

      const form = this.$_.cloneDeep(this.formData);
      form.Approval.ApprovalEmployeeIdList = form.Approval.ApprovalEmployeeList.map(s =>
        s.map(e => e.EmployeeId)
      );
      form.Approval.CCEmployeeIdList = form.Approval.CCEmployeeList.map(s => s.EmployeeId);

      const params = {
        ...form,
        Temporize: temporize,
        HrApprovalProcessId: this.id,
        FinanceExpenseObj: {
          ...form.FinanceExpenseObj,
          Total: this.totalAmount,
          SubmitEmployeeIdList: form.FinanceExpenseObj.SubmitEmployeeList.map(s => s.EmployeeId),
          PayeeEmployeeIdList: form.FinanceExpenseObj.PayeeEmployeeList.map(s => s.EmployeeId),
        },
      };
      params.FinanceExpenseObj.FinanceExpensesDetailList.forEach(item => {
        item.AttachmentIdList = item.AttachmentList.map(s => s.Id);
      });

      delete params.FinanceExpenseObj.PayeeEmployeeList;
      delete params.FinanceExpenseObj.SubmitEmployeeList;

      let reqApi;
      if (this.dialogStatus == "editDraft") {
        // 草稿编辑
        reqApi = approvalManagement.temporizeApi;
      } else {
        // 普通创建和创建时暂存
        reqApi = approvalManagement.infoAdd;
      }
      this.btnLoading = true;
      reqApi(params)
        .then(res => {
          this.$emit("reload");
          this.closeDialog();
          if (temporize) {
            this.$message.success("暂存成功");
          } else {
            this.$confirm(
              "审批流程完成后，请及时将对应纸质票据（如发票、合同、收据等）提交至财务处",
              "提示",
              {
                confirmButtonText: "确定",
                showCancelButton: false,
                type: "success",
              }
            );
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    handleChangePerson(users) {
      this.formData.FinanceExpenseObj.PayeeEmployeeList = users;
      this.$nextTick(() => {
        this.$refs.formRef.validateField("PayeeEmployeeList");
      });
    },
    handleBeforePerson(users) {
      if (users && users.length > 1) {
        this.$message.error("领款人不得超过1人");
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.body_wrapper {
  height: 75vh;
  display: flex;
  flex-direction: column;
  .main_container {
    flex: 1;
    width: 100%;
    .form_container {
      width: 100%;
      height: 100%;
      display: flex;
      .left_container {
        padding: 10px;
        width: 65%;
        border-right: 1px solid $border-color-light;
        .reimbursement_list {
          .reimbursement_item {
            .reimbursement_top {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .reimbursement_content {
              margin-top: 10px;
            }
          }
        }
        .total_container {
          margin-top: 15px;
          padding: 10px;
          background-color: $bg-color-2;
          .total_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            span:nth-child(1) {
              display: block;
              width: 100px;
              text-align: right;
            }
            &:last-child {
              margin-top: 5px;
            }
          }
        }
      }
      .right_container {
        width: 35%;
        padding: 10px;
      }
    }
  }
  .body_footer {
    flex-shrink: 0;
    border-top: 1px solid $border-color-light;
  }
}
/deep/.el-divider {
  background-color: $border-color-light;
  margin: 10px 0;
}
/deep/input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  &[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
