<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title :title="pageTitle" :subTitle="[pageDesc]" :showBackBtn="!!returnUrl" @goBack="handleGoBack"></page-title> -->
        <div class="page-wrapper">
            <div class="product-list">
                <el-button type="primary" v-if="hasTreeOpertAuth" style="width: 180px; margin-top: 10px;margin-left:35px;" @click="addTopLevel">创建分类</el-button>
                <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label">{{ node.label }}</span>
                            <span v-if="data.Level>0 && hasTreeOpertAuth && data.Id != '00000000-0000-0000-0000-000000000000'" class="node-btn-area">
                                <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <page-title :showBackBtn='false'>
                    <div slot="def">
                        <tags :items="searchTypesData" v-model="listQuery.KnowledgeApprovalStatus">
                            <template v-for="t in searchTypesData" :slot="t.value">{{ t.label }}</template>
                        </tags>
                    </div>
                </page-title>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" :optColWidth="110"
                    @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable"
                    :multable="true" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                        
                        <template slot="KnowledgeApprovalStatus" slot-scope="scope">
                            <span class="item-status" :style="{background: getKnowledgeApprovalStatusObj(scope.row.KnowledgeApprovalStatus).bgColor, color: getKnowledgeApprovalStatusObj(scope.row.KnowledgeApprovalStatus).color}">
                                {{ getKnowledgeApprovalStatusObj(scope.row.KnowledgeApprovalStatus).label || '无' }}
                            </span>
                        </template>
                        <template slot="CreateTime" slot-scope="scope">
                            <span>{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</span>
                        </template>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索文章标题/导语..."
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>
                                <template slot="Publisher">
                                    <emp-selector :beforeConfirm='handlePublisherBeforeConfirm' :showType="2"
                                    :multiple="true" :list="listQuery.PublisherList" @change="handlePublisherChangeManager"></emp-selector>
                                </template>
                                <template slot="CreateTime">
                                    <el-date-picker v-model="listQuery.CreateTime" type="datetimerange"
                                    align="right" unlink-panels range-separator="-" start-placeholder :default-time="['00:00:00', '23:59:59']"
                                    end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm"
                                    style="width: 100%;"></el-date-picker>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                                        <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                                            <el-button type="primary">
                                                {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                                            </el-button>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item command="batchEditClassify">修改分类</el-dropdown-item>
                                                <el-dropdown-item v-if="listQuery.KnowledgeApprovalStatus == 1 && rowBtnIsExists('btnExamine')" command="batchEditExamine">审核</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </permission-btn>
                                </template>

                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleDialog('detail', scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnExamine')&&scope.row.KnowledgeApprovalStatus==1" @click="handleExamine(scope.row)" text="审核"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <customerServiceDepartmentEdit v-if="customerServiceDepartmentEditDialogFormVisible"
    :dialogStatus="customerServiceDepartmentEditDialogStatus"
    :node="paramNode" :dialogFormVisible="customerServiceDepartmentEditDialogFormVisible"
    @closeDialog="customerServiceDepartmentEditCloseDialog"
    @saveSuccess="customerServiceDepartmentEditSaveSuccess"></customerServiceDepartmentEdit>
    
    <examine-page v-if="examineVisible"


    :dialogFormVisible="examineVisible" :ids="selectIds"
    @closeDialog="examineVisible = false"
    @saveSuccess="examineSaveSuccess"
    ></examine-page>


    <modify-classification v-if="batchClassifyVisible"
    :dialogFormVisible="batchClassifyVisible" :ids="multipleSelection.map(s=>s.Id)"
    @closeDialog="batchClassifyVisible = false"
    @saveSuccess="batchClassifySaveSuccess"
    ></modify-classification>
    
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as classifyApi from '@/api/classify'
import * as KnowledgeApi from '@/api/knowledge/Knowledge'
import { listToTreeSelect } from "@/utils";
import { KnowledgeApprovalStatusEnum } from './enum'
import empSelector from "@/views/common/empSelector";
import customerServiceDepartmentEdit from "./departmentEdit";
import examinePage from "./examine";
import modifyClassification from "./modifyClassification";

export default {
    name: "knowledge-Management",
    mixins: [indexPageMixin],
    components: {
        customerServiceDepartmentEdit,
        examinePage,
        empSelector,
        modifyClassification,
    },
    computed: {
        returnUrl() {
            let url = `${this.$route.path}?ClassifyId=${this.listQuery.ClassifyId}&KnowledgeApprovalStatus=${this.listQuery.KnowledgeApprovalStatus}&PageIndex=${this.listQuery.PageIndex}&PageSize=${this.listQuery.PageSize}`
            return url
        },
        hasTreeOpertAuth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnMaintain') > -1
        },
    },
    created() {
        this.routeQuery = { ...this.routeQuery, ...this.$route.query};
        let {ClassifyId,KnowledgeApprovalStatus,PageIndex,PageSize} = this.routeQuery;
        this.listQuery.ClassifyId = ClassifyId || '';
        this.listQuery.KnowledgeApprovalStatus = KnowledgeApprovalStatus ? Number(KnowledgeApprovalStatus) : 1;
        this.listQuery.PageIndex = PageIndex ? Number(PageIndex) : 1;
        this.listQuery.PageSize = PageSize ? Number(PageSize) : 20;
        // window.history.pushState('', '', '/#' + this.$route.path)
        if(ClassifyId) {
            this.checkedNode = {
                Id: ClassifyId
            }
        }
        this.loadTreeData();
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.ClassifyId = val.Id;
                    this.getApprovalStatisticsCount();
                    this.getList();
                }
            },
        },
        'listQuery.KnowledgeApprovalStatus' (val) {
            if(val||val==0){
                this.getList();
            }
        }
    },
    filters: {
        valFilter(val) {
            if (val) {
                return val;
            }
            return "无";
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "无";
        },

    },
    mounted() {},
    data() {
        return {
            routeQuery: {},
            layoutMode: 'simple',

            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
            customerServiceDepartmentEditDialogStatus: "create",
            customerServiceDepartmentEditDialogFormVisible: false,

            searchTypesData: [
                { label: "待审核（0）", value: 1 },
                { label: "已审核", value: 2 },
                { label: "不通过", value: 3 },
                { label: "全部", value: 0 },
            ],
            total: 0,
            listLoading: false,
            listQuery: {
                Keywords: '',
                ClassifyId: "",
                KnowledgeApprovalStatus: 1, // 审核状态  (待审核 已审核 不通过 全部)
            },
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "Publisher", label: "发布人" },
                { prop: "CreateTime", label: "发布时间" },
            ],
            tabColumns: [
                { attr: { prop: "Title", label: "文章标题", showOverflowTooltip: true, }, },
                { attr: { prop: "ClassifyName", label: "文章分类", showOverflowTooltip: true, }, },
                { attr: { prop: "KnowledgeApprovalStatus", label: "审核状态", sortable: 'custom' }, slot: true },
                { attr: { prop: "Pageviews", label: "浏览量", sortable: 'custom' }, },
                { attr: { prop: "FavoritesNumber", label: "收藏量", sortable: 'custom' }, },
                { attr: { prop: "PublisherName", label: "发布人", showOverflowTooltip: true }, },
                { attr: { prop: "CreateTime", label: "发布时间", sortable: 'custom', width: '120px' }, slot: true },
            ],
            tabDatas: [],


            selectIds: [],

            // 审核
            examineVisible: false,

            // 批量操作
            multipleSelection: [],

            // 修改分类
            batchClassifyVisible: false,
        };
    },
    methods: {
        // 批量修改分类 确定关闭
        batchClassifySaveSuccess(){
            this.getApprovalStatisticsCount();
            this.getList();
            this.batchClassifyVisible = false;
        },
        // 审核  确定关闭
        examineSaveSuccess(){
            this.getApprovalStatisticsCount();
            this.getList();
            this.examineVisible = false;
        },
        /**地区弹窗保存成功 */
        customerServiceDepartmentEditSaveSuccess(isContinue) {
            if(isContinue !== true) {
                this.customerServiceDepartmentEditCloseDialog();
            }
            this.loadTreeData();
            this.getList();
        },
        /**地区弹窗关闭 */
        customerServiceDepartmentEditCloseDialog() {
            this.customerServiceDepartmentEditDialogFormVisible = false;
        },
        handlePublisherBeforeConfirm(users) {
            if(users && users.length > 1000) {
                this.$message({
                    message: '提交人不得超过1000人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handlePublisherChangeManager(users) {
            // console.log(users)
            if (users && users.length > 0) {
                this.listQuery.PublisherList = users;
            } else {
                this.listQuery.PublisherList = [];
            }
        },
        getKnowledgeApprovalStatusObj(val) {
            return KnowledgeApprovalStatusEnum.find(s => s.value == val) || {}
        },
        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId != 'btnMaintain')
            }
            return []
        },
        resetSearch() {
            this.listQuery.KeyWords = "";
            this.listQuery.PublisherList = [];
            this.listQuery.CreateTime = null;
            this.getList();
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let self = this;
            let paramData = {
                BusinessType: 17,
            };
            self.treeLoading = true
            classifyApi.getListPage(paramData).then(res => {
                self.treeLoading = false

                let response = res.Items,{ClassifyId} = this.routeQuery

                if (response && response.length > 0) {
                    response.unshift({
                        Id: "",
                        Name: "全部",
                        Level: 0,
                        ParentId: null
                    });
                    self.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构
                    if (
                        !(
                            self.checkedNode &&
                            response.find(t => {
                                return t.Id == self.checkedNode.Id;
                            })
                        )
                    ) {
                        self.checkedNode = self.treeData[0];
                    }
                } else {
                    self.checkedNode = null;
                }
                if (self.checkedNode) {
                    self.$nextTick(() => {
                        if(self.$refs.treeRef) {
                            self.$refs.treeRef.setCurrentKey(ClassifyId||self.checkedNode.Id);
                        }
                    });
                }
            }).catch(err => {
                self.treeLoading = false
            });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.customerServiceDepartmentEditDialogStatus = "create";
            this.customerServiceDepartmentEditDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "create";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "update";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "delete":
                    this.handleDeleteDepartment(data);
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteDepartment(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let paramData = { ids: [data.Id], businessType: 17 };
                classifyApi.del(paramData)
                    .then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }
                        this.loadTreeData();
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
            });
        },
        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnContribute":
                    this.handleDialog("create");
                    break;
                // 修改分类
                case "batchEditClassify":
                    this.handleClassify();
                    break;
                case "batchEditExamine":
                    this.handleBatchExamine()
                    break;
                default:
                    break;
            }
        },
        handleClassify(type){
            if (this.multipleSelection.length>0) {
                this.batchClassifyVisible=true;
            } else {
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        // 去 创建/详情  页面
        handleDialog(activeName, row = null) {
            let url = `/knowledge/knowledgeManagement/create?ClassifyId=${this.listQuery.ClassifyId}&backUrl=${encodeURIComponent(this.returnUrl)}`
            if(activeName == 'detail') {
                url = `/knowledge/knowledgeManagement/detail/${row.Id}?backUrl=${encodeURIComponent(this.returnUrl)}`
            }
            this.$router.push({ path: url });
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData, isContinue) {
            // this.listQuery.PageIndex = 1

            this.getList();
            if(!isContinue) {
                this.closeDialog();
            }
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleBatchExamine(optType = "update") {
            if (this.multipleSelection.length>0) {
                this.selectIds = this.multipleSelection.map(s => s.Id) || [];
                this.examineVisible=true;
            } else {
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        handleExamine(row, optType = "update") {
            // 弹出审核框
            this.selectIds = [row.Id];
            this.examineVisible = true;
        },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        // 获取待审批数量
        getApprovalStatisticsCount(){
            KnowledgeApi.approvalStatisticsCount({ClassifyId: this.listQuery.ClassifyId}).then((res) => {
                this.searchTypesData[0].label = `待审核（${res}）`
            })
        },
        //获取项目列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            if(postData.PublisherList&&postData.PublisherList.length>0){
                postData.Publisher = postData.PublisherList.map(s=> s.EmployeeId).toString();
                delete postData.PublisherList
            }
            if (postData.CreateTime&&postData.CreateTime.length>0) {
                postData.CreateTimeStart = postData.CreateTime[0]
                postData.CreateTimeEnd = postData.CreateTime[1]
                delete postData.CreateTime
            }
            // console.log(postData)
            KnowledgeApi.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            });
        },
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                KnowledgeApi.del([row.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            }).catch(()=>{});
        },
    },
};
</script>

<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            // top: 40px;
            top: 0;
            right: 0;
            bottom: 0;

            .product-list {
                width: 250px;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }

                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        height: 100%;
                        overflow: auto;
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 200px);
                flex: 1;
                overflow-y: auto;

                .content {

                    // padding: 10px;
                    // padding-right: 0;
                    .opt-wrapper {
                        box-sizing: border-box;
                        border-bottom: 1px solid #dcdfe6;
                        padding-bottom: 10px;
                    }

                    .list {}
                }
            }
        }
    }
}

.item-status {
    color: inherit;
    padding: 2px 4px;
    border-radius: 10%;
}

.statisticsDivClass {
    width: 100%;
    height: 75px;
    border-radius: 8px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
    cursor: pointer;

    .statisticsChildDiv {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px 0 0 8px;
        width: 26%;
        height: 75px;
        background-color: rgba(255, 108, 96, 1);
        float: left;
    }

    .statisticsChildDiv2 {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 0 8px 8px 0;
        width: 74%;
        height: 75px;
        background-color: rgba(255, 255, 255, 1);
        float: left;

        .label1 {
            color: #999999;
            margin: 0;
            margin-top: 12px;
            font-weight: 500 !important;
        }

        div {
            margin-top: 5px;
        }

        .label2 {
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            font-size: 20px;
            color: $text-second-color;
            margin: 0;
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

.statisticsDivClass:hover {
    transform: translate(-3px, -3px);
    box-shadow: 0px 0px 3px 0px #dcdfe6;
}

</style>
