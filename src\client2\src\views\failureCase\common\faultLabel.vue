<template>
  <div>
    <app-dialog
    title="故障分类标签"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :maxHeight='600'
    :width='900'
    >
    <template slot="body">
      <main>
        <class-label ref="cl" @addNum="addNum"></class-label>
      </main>
    </template>
    <template slot="footer">
        <div class="fl">
          已选中（{{saveNum}}）
        </div>
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button @click="handleSuccess" type="primary" size="mini">确认</el-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import classLabel from './classificationLabel';
  export default {
    name: "",
    components: {
      classLabel
    },
    props: {

    },
    data() {
      return {
        
        saveNum:0,
      };
    },
    watch: {
      "$attrs.dialogFormVisible"(val) {
        if(val){
          let a=0;
          if(this.$store.state.communication.selectedData && this.$store.state.communication.selectedData.length>0){
            this.$store.state.communication.selectedData.forEach(v => {
              a+=v.checkList.length;
            })
          }
          this.saveNum=a;
        }
      },

    },
    created() {

    },
    methods: {
      addNum(d){
        this.saveNum=d;
      },
      handleClose(){
        this.$refs.cl.getCloseMsg();
        this.$refs.appDialogRef.handleClose()
      },
      handleSuccess(){
        this.$refs.cl.getMsg();
        this.$emit('saveSuccess');
      },
      getList(){

      },
    }
  };
</script>
<style lang="scss" scoped>
  main{
    min-height: 500px;
  }
</style>