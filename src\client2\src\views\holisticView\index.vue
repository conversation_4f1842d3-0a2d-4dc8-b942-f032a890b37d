<template>
  <div class="holisticView" v-loading="loading">
    <header class="cl">
      <div class="projectBox fl">
        <p><span>{{projectDetail.ProjectName}}</span></p>
        <span>项目状态：{{projectState.label}}</span>
        <span class="manager">项目经理：{{ProjectManagerEmployeeName}}</span>
      </div>
      <div class="taskBox fr">
        <el-row>
          <el-col class="elCol" v-for="(item,index) in task" :span="item.space" :key="index">
            <p>{{item.name}}</p>
            <p class="taskP">{{item.fraction}}</p>
          </el-col>
        </el-row>
      </div>
    </header>
    <div class="stepBox">
      <p>项目里程碑</p>
    <div class="coll-wrapper">
      <el-row>
          <el-collapse v-model="versionIterationMilestoneData.activeName" accordion> 
            <el-collapse-item :name="version.ProjectManagementVersionPlanId" :key="version.ProjectManagementVersionPlanId"  v-for="(version,versionIndex) in versionIterationMilestoneData.versionIterationMilestoneList" >
              <template slot="title">
                版本：{{version.VerisionName}} 
                <span v-show="version.IsInProgress" style="fontWeight: 600">【当前】</span>
                <span v-show="!version.IsInProgress">【{{getVersionTitle(version.VersionStatus)}}】</span>
              </template>
                <div class="stepDiv">
                  <ul class="planBox cl" :style="'width:'+version.MilestoneList.length*250+'px;'">
                    <li class="fl" v-for="(pm,index) in version.MilestoneList">
                      <div class="divBox">
                        <div class="timeBox actual">
                          <div><i class="circleI"></i><span>计划开始时间：</span></div>
                          <span>{{pm.PlannedStartTime ? pm.PlannedStartTime.split('.')[0] : '&nbsp;'}}</span>
                          <div><i class="circleI"></i><span>计划结束时间：</span></div>
                          <span>{{pm.PlannedEndTime ? pm.PlannedEndTime.split('.')[0] : '&nbsp;'}}</span>
                          <div><i class="circleI cFf8b0e"></i><span>实际开始时间：</span></div>
                          <span>{{pm.ActualStartTime ? pm.ActualStartTime.split('.')[0] : '&nbsp;'}}</span>
                          <div><i class="circleI cFf8b0e"></i><span>实际结束时间：</span></div>
                          <span>{{pm.ActualEndTime ? pm.ActualEndTime.split('.')[0] : '&nbsp;'}}</span>
                        </div>
                        <p class="mile-title" :title="`${pm.Name}${pm.MilestoneStatus == 4 ? '(' + pm.ProgressValue + '%)' : ''}`">
                          {{(index+1)+'.'+pm.Name}}<span v-show="pm.MilestoneStatus == 4">({{pm.ProgressValue}}%)</span>
                        </p>
                      </div>
                      <div class="actual cl">
                        <!-- <i class="circleI fl" :class="{cFf8b0e:pm.ActualEndTime}"></i>
                        <span class="progressBar fl" :class="{cFf8b0e:pm.ActualEndTime}"></span> -->

                        <!-- 进行中或者完成 -->
                        <i class="circleI fl" :class="{cFf8b0e:pm.MilestoneStatus == 4 || pm.MilestoneStatus == 7}"></i>
                        <span class="progressBar fl" :class="{cFf8b0e:pm.MilestoneStatus == 4 || pm.MilestoneStatus == 7}">
                            <span class="progress" :style="{transform: `translateX(${pm.ProgressValue}%)`}"></span>
                        </span>

                      </div>
                    </li>
                  </ul>
                      </div>
                <div  style="padding-left:20px;">
                <el-collapse v-model="version.activeName"  accordion> 
                  <el-collapse-item :name="iteration.ProjectManagementIterationId" :key="iteration.ProjectManagementIterationId"  v-for="(iteration,iterationIndex) in version.IterationList" >

                      <template slot="title">
                        迭代：{{iteration.IterationName}} 
                        <!-- <span :style="{fontWeight: iteration.IterationStatus == 2 ? '600' : '100'}">【{{getIterationTitle(iteration.IterationStatus)}}】</span> -->
                        <span v-show="iteration.IsInProgress" style="fontWeight: 600">【当前】</span>
                        <span v-show="!version.IsInProgress">【{{getIterationTitle(iteration.IterationStatus)}}】</span>
                      </template>

                      <div class="stepDiv">
                        <ul class="planBox cl" :style="'width:'+iteration.MilestoneList.length*250+'px;'">
                          <li class="fl" :key="pm.ProjectManagementMilestoneId" v-for="(pm,index) in iteration.MilestoneList">
                            <div class="divBox">
                              <div class="timeBox actual">
                                <div><i class="circleI"></i><span>计划开始时间：</span></div>
                                <span>{{pm.PlannedStartTime ? pm.PlannedStartTime.split('.')[0] : '&nbsp;'}}</span>
                                <div><i class="circleI"></i><span>计划结束时间：</span></div>
                                <span>{{pm.PlannedEndTime ? pm.PlannedEndTime.split('.')[0] : '&nbsp;'}}</span>
                                <div><i class="circleI cFf8b0e"></i><span>实际开始时间：</span></div>
                                <span>{{pm.ActualStartTime ? pm.ActualStartTime.split('.')[0] : '&nbsp;'}}</span>
                                <div><i class="circleI cFf8b0e"></i><span>实际结束时间：</span></div>
                                <span>{{pm.ActualEndTime ? pm.ActualEndTime.split('.')[0] : '&nbsp;'}}</span>
                              </div>
                              <p class="mile-title" :title="`${pm.Name}${pm.MilestoneStatus == 4 ? '(' + pm.ProgressValue + '%)' : ''}`">
                                {{(index+1)+'.'+pm.Name}}<span v-show="pm.MilestoneStatus == 4">({{pm.ProgressValue}}%)</span>
                              </p>
                            </div>
                            <div class="actual cl">
                              <!-- <i class="circleI fl" :class="{cFf8b0e:pm.ActualEndTime}"></i>
                              <span class="progressBar fl" :class="{cFf8b0e:pm.ActualEndTime}"></span> -->

                              <!-- 进行中或者完成 -->
                              <i class="circleI fl" :class="{cFf8b0e:pm.MilestoneStatus == 4 || pm.MilestoneStatus == 7}"></i>
                              <span class="progressBar fl" :class="{cFf8b0e:pm.MilestoneStatus == 4 || pm.MilestoneStatus == 7}">
                                  <span class="progress" :style="{transform: `translateX(${pm.ProgressValue}%)`}"></span>
                              </span>

                            </div>
                          </li>
                        </ul>
                      </div>
                  </el-collapse-item>
                </el-collapse>
                </div>
            </el-collapse-item>
          </el-collapse>
      </el-row>

    </div>
      <div class="stepDiv">
         <ul class="planBox cl" :style="'width:'+progressMsg.length*250+'px;'">
          <li class="fl" v-for="(pm,index) in progressMsg">
            <div class="divBox">
              <div class="timeBox actual">
                <div><i class="circleI"></i><span>计划开始时间：</span></div>
                <span>{{pm.PlannedStartTime ? pm.PlannedStartTime.split('.')[0] : '&nbsp;'}}</span>
                <div><i class="circleI"></i><span>计划结束时间：</span></div>
                <span>{{pm.PlannedEndTime ? pm.PlannedEndTime.split('.')[0] : '&nbsp;'}}</span>
                <div  ><i class="circleI cFf8b0e"></i><span>实际开始时间：</span></div>
                <span  >{{pm.ActualStartTime ? pm.ActualStartTime.split('.')[0] : '&nbsp;'}}</span>
                <div ><i class="circleI cFf8b0e"></i><span>实际结束时间：</span></div>
                <span  >{{pm.ActualEndTime ? pm.ActualEndTime.split('.')[0] : '&nbsp;'}}</span>
              </div>
              <p class="mile-title" :title="`${pm.Name}${pm.MilestoneStatus == 4 ? '(' + pm.ProgressValue + '%)' : ''}`">
                {{(index+1)+'.'+pm.Name}}<span v-show="pm.MilestoneStatus == 4">({{pm.ProgressValue}}%)</span>
              </p>
            </div>
            <div class="actual cl">
              <!-- <i class="circleI fl" :class="{cFf8b0e:pm.ActualEndTime}"></i>
              <span class="progressBar fl" :class="{cFf8b0e:pm.ActualEndTime}"></span> -->

              <!-- 进行中或者完成 -->
              <i class="circleI fl" :class="{cFf8b0e:pm.MilestoneStatus == 4 || pm.MilestoneStatus == 7}"></i>
              <span class="progressBar fl" :class="{cFf8b0e:pm.MilestoneStatus == 4 || pm.MilestoneStatus == 7}">
                  <span class="progress" :style="{transform: `translateX(${pm.ProgressValue}%)`}"></span>
              </span>

            </div>
          </li>
        </ul>
    </div>
    </div>
    <div class="riskBox cl">
      <p class="fl">风险展示</p>
      <div class="fr">
        <el-row>
          <el-col class="elCol" v-for="(item,index) in riskMsgs" :span="item.space" :key="index">
            <p>{{item.name}}</p>
            <p><span class="riskBoxSpan" v-show="index != 0">{{item.untreated+'/'}}</span><span :class="{riskBoxSpan:index == 0}">{{item.all}}</span></p>
          </el-col>
        </el-row>
      </div>
    </div>
    <div style="padding: 10px; padding-top: 0;" v-loading="listLoading">
      <!-- <gantt ref="gantt" :viewMode="viewMode"></gantt> -->
    </div>
  </div>
</template>

<script>
import * as milestoneApi from "@/api/milestone";
import { getUserInfo } from "@/utils/auth";
// import Gantt from "../situation/gantt";
import {projectStatusEnum} from "../projectManagement/enums";
import { versionStatus, iterationStatus } from '../versionPlan/enums'
export default {
  props:['ProjectId','projectDetail'],
  name: "holisticView",
  components: {
    // Gantt
  },
  directives: {

  },
  data() {
    return {
      versionActiveName: 0,
      projectStatusEnum: projectStatusEnum,
      viewMode: 10,
      ulWidth:0,
      task:[{
        name:'任务总数',
        fraction:0,
        space:4
      },{
        name:'未开启',
        fraction:0,
        space:4
      },{
        name:'进行中',
        fraction:0,
        space:4
      },{
        name:'正常完成',
        fraction:0,
        space:4
      },{
        name:'延期未完成',
        fraction:0,
        space:4
      },{
        name:'延期完成',
        fraction:0,
        space:4
      }],
      riskMsgs:[{
        name:'风险总数',
        all:0,
        untreated:0,
        space:6
      },
      {
        name:'高级别风险数',
        all:0,
        untreated:0,
        space:6
      },
      {
        name:'中级别风险数',
        all:0,
        untreated:0,
        space:6
      },
      {
        name:'低级别风险数',
        all:0,
        untreated:0,
        space:6
      }],
      listQuery: { // 查询条件
           ProjectId: this.ProjectId,
           DatumType:{},
      },
      progressMsg:[],
      versionIterationMilestoneData:{activeName:"", versionIterationMilestoneList:[]},
      // versionIterationMilestoneList:[],
      listLoading:false,
      loading:false,
      ProjectManagerEmployeeName:'',
      projectState:{
        label:''
      },
      postDatas:null
    };
  },
  watch: {
    projectDetail: {
        handler(val) {
          this.loading=true;
          this.versionActiveName=0;
            this.listQuery.ProjectId = val.ProjectId;
            this.getVersionIterationMilestoneList();
            this.getList();
            this.getProjectTask();
            // this.postDatas.ProjectId=val.ProjectId;
            // this.postDatas.CurrentEmployeeIds=[val.CreateEmployeeId];
            this.postDatas={
              CurrentEmployeeIds:[val.CreateEmployeeId],
              IsPersonalView:true,
              OrgId:null,
              TaskMonthDateTime: this.doHandleDate(),
              TaskSourceTypeEnum: 0,
              TaskTypeEnum: 0,
              viewInsertion:true,
              monthButtonShow:true,
              ProjectId:val.ProjectId,
              AllProjectTask:true
            }
            this.$refs.gantt.getDatas(this.postDatas);
        }
    },
  },
  created() {

     this.getVersionIterationMilestoneList();

  },
  mounted() {
    this.listQuery.ProjectId = this.ProjectId;
     this.getVersionIterationMilestoneList();
     this.getList();
     this.getProjectTask();
     let currentUserInfo = getUserInfo();
     if (currentUserInfo && currentUserInfo.employeeid != "") {
          this.postDatas={
            CurrentEmployeeIds:[this.projectDetail.CreateEmployeeId],
            IsPersonalView:true,
            OrgId:null,
            TaskMonthDateTime: this.doHandleDate(),
            TaskSourceTypeEnum: 0,
            TaskTypeEnum: 0,
            viewInsertion:true,
            monthButtonShow:true,
            ProjectId:this.ProjectId,
            AllProjectTask:true
          }
          this.$refs.gantt.getDatas(this.postDatas);
      }
  },
  methods:{
    getVersionTitle(status) {
      let label = ''
      let statusLabel = versionStatus.find(s => s.value == status)
      if(statusLabel) {
        label = statusLabel.label
      }
      return label
    },
    getIterationTitle(status) {
      let label = ''
      let statusLabel = iterationStatus.find(s => s.value == status)
      if(statusLabel) {
        label = statusLabel.label
      }
      return label
    },
    getProjectTask(){
      milestoneApi.getProjectTaskStatistics({projectId:this.ProjectId}).then(response=>{
        this.task[0].fraction=response.TaskNum;
        this.task[1].fraction=response.UnOpened;
        this.task[2].fraction=response.InProgress;
        this.task[3].fraction=response.Finished;
        this.task[4].fraction=response.DelayInProgress;
        this.task[5].fraction=response.DelayFinished;
        this.ProjectManagerEmployeeName=response.ProjectManagerEmployeeName;
        this.projectState= this.projectStatusEnum.find(o => o.value == response.ProjectState);
      })
    },
    getList(){
      milestoneApi.overallViewListPageAsync(this.listQuery).then(response=>{
        // let arr=[];
        // response.Items.forEach(v => {
        //   if(v.MilestoneStatus == 5){
        //     arr.push(v);
        //   }
        // })
        this.progressMsg=response.Items;
        console.log(this.progressMsg,222);
        this.getRiskProfile();
      });
    },
    getVersionIterationMilestoneList(){
      milestoneApi.overallViewVersionIterationListPageAsync(this.listQuery).then(response=>{
        // let arr=[];
        // response.Items.forEach(v => {
        //   if(v.MilestoneStatus == 5){
        //     arr.push(v);
        //   }
        // })
        // this.versionIterationMilestoneList=response.Items;
        this.versionIterationMilestoneData.activeName="";
        if(response.Items){          
        response.Items.forEach((element,index) => {
          if(element.IsInProgress==true){
            this.versionIterationMilestoneData.activeName=element.ProjectManagementVersionPlanId;
          }
          element.activeName="";
          if(element.IterationList){
            element.IterationList.forEach((iterationElement,iterationIndex) => {
              if (iterationElement.IsInProgress==true){
              element.activeName=iterationElement.ProjectManagementIterationId;
            }
            });
          }
        });
        }
        this.versionIterationMilestoneData.versionIterationMilestoneList=response.Items;


        console.log(this.versionIterationMilestoneData,222);
        
       // this.getRiskProfile();
      });
    },
    getRiskProfile(){
      milestoneApi.getProjectManagmentRiskProfile({projectId:this.ProjectId}).then(res => {
        this.riskMsgs[0].all=res.HighRiskAllTotal+res.LowRiskAllTotal+res.MiddleRiskAllTotal;
        this.riskMsgs[0].untreated=res.HighRiskUntreatedTotal+res.MiddleRiskUntreatedTotal+res.LowRiskUntreatedTotal;
        this.riskMsgs[1].all=res.HighRiskAllTotal;
        this.riskMsgs[1].untreated=res.HighRiskUntreatedTotal;
        this.riskMsgs[2].all=res.MiddleRiskAllTotal;
        this.riskMsgs[2].untreated=res.MiddleRiskUntreatedTotal;
        this.riskMsgs[3].all=res.LowRiskAllTotal;
        this.riskMsgs[3].untreated=res.LowRiskUntreatedTotal;
        this.loading=false;
      })
    },
    doHandleDate() {
        var myDate = new Date();
        var tYear = myDate.getFullYear();
        var tMonth = myDate.getMonth();

        var m = tMonth + 1;
        if (m.toString().length == 1) {
            m = "0" + m;
        }
        return tYear +'-'+ m;
    },
    doHandleYear(tYear) {
        var myDate = new Date();
        var tYear = myDate.getFullYear();

        return tYear;
    },
    doHandleMonth() {
        var myDate = new Date();
        var tMonth = myDate.getMonth();

        var m = tMonth + 1;
        if (m.toString().length == 1) {
            m = "0" + m;
        }
        return m;
    },
  }

};
</script>
<style scoped>
  .coll-wrapper >>> .el-collapse-item__content{
    line-height: 1;
  }
</style>


<style lang='scss' scoped>

.coll-wrapper{
  padding-left:20px;
}

.pTitle{
  color:#d3d3d3;
  padding-left:30px;
}
.divBox{
  // height:150px;
  height: 230px;
}
.cFf8b0e{
  background:#ff8b0e!important;
}
.timeBox{
  // height:106px;
  height:190px;
  font-size: 12px;
  >p{
    color:#a8a8a8;
    >span{
      margin-left:6px;
    }
  }
  >div{
    color:#a8a8a8;
    padding:10px 0;
    >span{
      margin-left:6px;
    }
  }
}
.circleI{
  display: inline-block;
  width:6px;
  height:6px;
  border-radius:6px;
}
.progressBar{
  width:200px;
  display: inline-block;
  height:4px;
  background:#0078ff;
  margin-top: 1px;
  margin-left:5px;
  position: relative;
  overflow: hidden;
}

.progress{
  position: absolute;
  width: 100%;
  height: 100%;
  background: #d7d7d7;
  transition: all .4s linear;
}

.elCol{
  border-right:1px solid #e3e3e3;
}
.elCol:last-child{
  border:0;
}
.taskP{
  font-size:18px;
  font-weight: bold;
}
.riskBox{
  border-bottom: 1px solid #e3e3e3;
  margin-bottom: 30px;
  p.fl{
    font-size:18px;
    font-weight: bold;
    padding:48px 0 0 20px;
  }
  >div{
    width:500px;
    padding:30px 20px;
    p{
      text-align: center;
      .riskBoxSpan{
        font-size:18px;
        font-weight: bold;
      }
    }
  }

}
header{
  padding:30px 20px;
  border-bottom: 1px solid #C0C4CC;
  .projectBox{
    width:auto;
    >p{
      span{
        display: inline-block;
        height:28px;
        line-height: 28px;
        font-size:18px;
        font-weight: bold;
        padding-left:30px;
        background: url('../../assets/images/projectImg.png') no-repeat;
        background-position: center left;
      }
    }
    .manager{
      margin-left: 20px;
    }
  }
}
.stepBox{
  width:100%;
  padding:40px 0;
  overflow: hidden;
  position: relative;
  background:#fbfbfb;
  >p{
    font-size:18px;
    font-weight: bold;
    padding-left:30px;
  }
  .stepDiv{
    padding-bottom: 10px;
    overflow-x: auto;
    p{
    padding: 0 10px;
    }
    ul,li{
      padding:0;
      margin:0;
      list-style:none;
    }
    ul{
      padding-left:30px;
    }
    li{
      padding:0 2px;
    }
    .actual{
      .circleI{
        background:#d7d7d7;
      }
      .progressBar{
        background:#d7d7d7;
      }
    }
    .timeBox{
      .circleI{
        background:#0078ff;
      }
    }
  }
  i:hover{
    color:#409eff;
  }

}
.taskBox{
  width:450px;
  p{
    text-align: center;
  }
}


.fl{
  float: left;
}
.fr{
  float: right;
}
.cl{
  zoom: 1;
}
.cl:after, .clearfix:after {
  content: "\20";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
}

.mile-title{
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

</style>


