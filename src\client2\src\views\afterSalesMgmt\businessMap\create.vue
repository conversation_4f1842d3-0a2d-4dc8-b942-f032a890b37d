<template>
    <div class="businessMapCreate">
        <app-dialog :title="pageTitle" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='1300'
            :maxHeight="636"
        >
            <template slot="body" v-loading='loading'>
                <el-form class="businessElForm" :rules="rules" ref="formData" :model="formData" label-width="84px" label-position="right">
                    <div class="elMb">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="关联订单:" prop="Order.OrderNumber">
                                    <el-button :disabled="!editable" type="text" @click="() => dialogAccessUsers = true">选择</el-button>
                                    <span v-if="formData.Order">{{ formData.Order.OrderNumber }}</span>
                                    <el-button v-show="formData.Order && formData.Order.OrderNumber.length>0" style="padding:4px;" :disabled="!editable" icon="el-icon-close" circle @click="closeOrder"></el-button>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="合同编号:">
                                    <span>{{ formData.ContractNumber || '无' }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                    </div>
                    <!-- <div class="associatedArea" v-loading="orderLoading">
                        <div class="associatedHead">
                            <span class="omit" style="max-width:160px;" :title="'客户单位：'+formData.Order.ClientUnits">客户单位：{{formData.Order.ClientUnits}}</span>
                            <span>订单金额（元）：{{formData.Order.OrderAmount}}</span>
                            <span>状态：{{formData.Order.OrderState | orderStater}}</span>
                            <span>回款情况：{{formData.Order.ReceivedPayment | receivedPaymenter}}</span>
                        </div>
                        <div>
                            <div class="tableTitle">订单明细:</div>
                            <div>
                                <app-table-core
                                  ref="mainTable"
                                  :tab-columns="tabColumns"
                                  :tab-datas="orderDetailList.OrderDetailList"
                                  :tab-auth-columns="[]"
                                  :isShowAllColumn="true"
                                  @rowSelectionChanged="rowSelectionChanged"
                                  :isShowOpatColumn="false"
                                  :startOfTable="0"
                                  :multable='false'
                                >
                                    <template slot='OrderType' slot-scope="scope">
                                        {{scope.row.OrderType | orderTyper}}
                                    </template>
                                    <template slot='Remarks' slot-scope="scope">
                                        <div class="omit" :title="scope.row.Remarks">{{scope.row.Remarks}}</div>
                                    </template>
                                </app-table-core>
                            </div>
                        </div>
                    </div> -->
                    <div class="addEquipment">
                        <!-- <div class="panel-title">添加设备</div> -->
                        <div class="elMb">
                            <el-form-item label="所在地区:" prop="areaName">
                                <!-- <span>
                                    <span v-if="formData.areaName">{{formData.areaName}}&nbsp;</span>
                                    <el-button type="text"  :disabled="!editable" @click="handleDialog()">选择</el-button>
                                    &nbsp;<i style="color:rgb(159 166 181);">(提示：请先选中设备所在地区，下一步进行设备添加操作)</i>
                                </span> -->

                                <div class="_regional_detail_wrapper">
                                    <div class="btn_wrapper">
                                        <el-button :disabled="!editable" type="text" @click="handleDialog">选择</el-button>
                                    </div>
                                    <div class="regional_text" :title="formData.areaName">{{ formData.areaName }}</div>
                                    <div class="close_wrapper" v-show="formData.areaName && editable">
                                        <div class="i_wrapper">
                                            <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                        </div>
                                    </div>
                                </div>

                            </el-form-item>  
                        </div>
                        <div class="elMb">
                            <el-form-item label="设备列表:" prop="listLength">
                                <span>
                                    已添加（{{formData.listLength}}）
                                    <el-button type="text" :disabled="!editable || !formData.areaName" @click="add()">添加更多设备</el-button>
                                </span>
                            </el-form-item>  
                        </div>
                        <div>
                            <div class="elMb">
                                <el-form-item label="批量操作:" prop="listLength">
                                    <!-- <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="add()">添加设备</el-button> -->
                                    <el-checkbox v-model="checkedAll" @change="handleCheckedAll" style="margin-right:10px;">全选</el-checkbox>
                                    <!-- <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="handleAccess()">导入设备部件模板</el-button> -->
                                    <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="handleBatchAdd('用途',0)">添加用途</el-button>
                                    <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="handleBatchAdd('供风方式',0)">添加供风方式</el-button>
                                    <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="handleBatchAdd('生产厂家',1)">添加生产厂家</el-button>
                                    <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="handleBatchAdd('投产时间',2)">添加投产时间</el-button>
                                    <el-button type="primary"  :disabled="!editable || !formData.areaName" @click="handleBatchAdd('保修有效期',2)">添加保修有效期</el-button>
                                </el-form-item>  
                            </div>
                            <div class="cardBox">
                                <el-card class="box-card" v-for="(td,index) in tableData" :key="index" style="overflow:visible;">
                                  <div slot="header" class="clearfix">
                                    <el-checkbox v-show="dialogStatus == 'create'" v-model="td.checked">设备{{index+1}}</el-checkbox>
                                    <i v-show="dialogStatus == 'create'" class="el-icon-delete pointer" @click="handleDelete(index)"></i>
                                  </div>
                                  <div>
                                      <el-row style="margin-bottom: 10px;">
                                          <span style="font-size: 14px; font-weight: bold;">
                                              加热炉信息
                                          </span>
                                      </el-row>
                                      <el-row :gutter="20" style="margin-bottom: 20px">
                                          <el-col :span="8">
                                            <div class="dFlex">
                                              <span style="margin-right:10px;"><i class="red">*</i>加热炉/锅炉</span>
                                              <el-input v-model="td.Name" :disabled="!editable" maxlength="50" placeholder=""></el-input>
                                              <p class="red" v-show="td.jiaErrShow">{{td.jiaErrMsg}}</p>
                                            </div>
                                          </el-col>
                                           <el-col :span="8">
                                            <div class="dFlex">
                                              <span style="margin-right:10px;"><i class="red">*</i>炉号</span> 
                                              <el-input v-model="td.HeatNumber" :disabled="!editable" maxlength="20" placeholder=""></el-input>
                                              <p class="red" v-show="td.luErrShow">{{td.luErrMsg}}</p>
                                            </div>
                                          </el-col>
                                          <el-col :span="8">
                                            <div class="dFlex">
                                              <span style="margin-right:10px;">用途</span> 
                                              <el-select v-model="td.EquipmentUseId" filterable clearable placeholder="请选择">
                                                    <el-option
                                                    v-for="item in purposeOptions"
                                                    :key="item.Id"
                                                    :label="item.Name"
                                                    :value="item.Id">
                                                    </el-option>
                                                </el-select>
                                            </div>
                                          </el-col>
                                          
                                          <!-- <el-col :span="8">
                                            <div class="dFlex">
                                              <span><i class="red">*</i>类型&emsp;</span> 
                                              <treeselect
                                                :disabled="!editable"
                                                :normalizer="normalizer2"  
                                                key='type2'
                                                v-model="td.ProductListManagementId" 
                                                :default-expand-level="3"
                                                :options="typeTreedata" 
                                                :multiple="false" 
                                                placeholder='请选择' 
                                                :show-count="true"
                                                :noResultsText='noResultsTextOfSelTree' 
                                                :noOptionsText="noOptionsTextOfSelTree">
                                                <label :title="node.label" slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                                                    <span>
                                                        {{ node.label }}<span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                                                    </span>
                                                </label>
                                            </treeselect>
                                            </div>
                                          </el-col> -->
                                          
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px;">
                                            
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">加热炉功率</span>
                                                    <el-input v-model="td.HeatFurnaceRatework" :disabled="!editable" maxlength="20" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">结构类型</span>
                                                    <el-input v-model="td.StructureTypeName" :disabled="!editable" maxlength="10" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                        </el-row>
                                        <el-row style="margin-bottom: 10px; border-top: 1px solid #EBEEF5; padding-top: 10px;">
                                            <span style="font-size: 14px; font-weight: bold;">
                                                燃烧器信息
                                            </span>
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px;">
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">供风方式</span> 
                                                <el-select v-model="td.EquipmentWorkModeId" filterable clearable placeholder="请选择">
                                                        <el-option
                                                        v-for="item in workOptions"
                                                        :key="item.Id"
                                                        :label="item.Name"
                                                        :value="item.Id">
                                                        </el-option>
                                                    </el-select>
                                                </div>
                                            </el-col>
                                             <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">燃烧器型号</span> 
                                                <el-input v-model="td.BurnerModel" :disabled="!editable" maxlength="100" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">燃烧器功率</span> 
                                                <el-input v-model="td.BurnerRatework" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px">
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">燃料类型</span>
                                                    <el-input v-model="td.FuelType" :disabled="!editable" maxlength="20" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">燃烧器压力</span> 
                                                <el-input v-model="td.BurnerPressure" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex" style="height:28px;">
                                                <span style="margin-right:10px;">是否在保</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.IsWarranty" @change="handleClickIsw(index)">
                                                    <el-radio v-for="(wl,wlI) in warrantyList" :key="wlI" v-show="wl.value != 4"  :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            
                                            
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px">
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;"><i class="red" v-show="td.IsWarranty == 2 || td.IsWarranty == 4">*</i>保修有效期至&emsp;</span> <el-date-picker :disabled="(td.IsWarranty != 2 && td.IsWarranty != 4) || !editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="td.WarrantyTime" type="date"></el-date-picker>
                                                </div>
                                            </el-col>
                                             <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">生产厂家</span> 
                                                <el-input v-model="td.Manufacturer" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">投产时间</span>
                                                <el-date-picker :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="td.InstallTime" type="date"></el-date-picker>
                                                </div>
                                            </el-col>
                                             
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px">
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">出产日期</span>
                                                <el-date-picker :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="td.ProduceDate" type="date"></el-date-picker>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">出产编号</span> 
                                                <el-input v-model="td.ProduceNumber" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">电源参数</span> 
                                                <el-input v-model="td.PowerParams" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                        </el-row>
                                        <el-row style="margin-bottom: 20px">
                                            <el-col :span="8">
                                                <div class="dFlex">
                                                <span style="margin-right:10px;">试验证书编号</span> 
                                                <el-input v-model="td.TestCertificateNo" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                                </div>
                                            </el-col>
                                        </el-row>

                                        <el-row style="margin-bottom: 10px; border-top: 1px solid #EBEEF5; padding-top: 10px;">
                                            <span style="font-size: 14px; font-weight: bold;">
                                                安全连锁保护信息
                                            </span>
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px;">
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">炉管保护</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.FurnaceProtect">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">漏液保护</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.LeakProtect">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">炉况优化</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.FurnacePracticeOptimize">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">温度连锁</span> 
                                                <el-select v-model="td.TemperatureLinkage" filterable clearable placeholder="请选择">
                                                    <el-option
                                                    v-for="item in tempTypes"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                                    </el-option>
                                                </el-select>
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">燃气高压连锁</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.GasHighPressureInterlocking">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">燃气低压连锁</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.GasLowPressureInterlocking">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">炉体压力连锁</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.FurnacePressureChain">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">液位连锁</span> 
                                                <el-radio-group :disabled="!editable" v-model="td.LiquidLevelLinkage">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>    
                                                </div>
                                            </el-col>
                                            <el-col :span="8">
                                                <div class="dFlex sp-row">
                                                <span style="margin-right:10px;">控制系统PLC厂家</span> 
                                                <el-input type="text" maxlength="100" :disabled="!editable" v-model="td.ControlSystemPLCManufacturer"></el-input>
                                                </div>
                                            </el-col>
                                        </el-row>



                                        <el-row style="margin-bottom: 10px; border-top: 1px solid #EBEEF5; padding-top: 10px;">
                                            <span style="font-size: 14px; font-weight: bold;">
                                                其他信息
                                            </span>
                                        </el-row>
                                        <el-row :gutter="20" style="margin-bottom: 20px">
                                            <el-col :span="24">
                                                <div class="dFlex">
                                                <span style="margin-top:-42px;margin-right:10px;">设备现状/问题</span>
                                                <el-input type="textarea" maxlength="100" :rows="3" :disabled="!editable" v-model="td.EquipmentStatusOrProblem"></el-input>
                                                </div>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <div class="dFlex" style="align-items: flex-start;">
                                                <span style="margin-right:10px; padding-top: 4px;">附件</span> 
                                                    <app-uploader
                                                        ref="appUploaderRef"
                                                        accept="all"
                                                        :fileType="3"
                                                        :max="10000"
                                                        :value="td.AttachmentList"
                                                        :fileSize="1024 * 1024 * 500"
                                                        :minFileSize="100 * 1024"
                                                        @change="(files) => handleFilesUpChange(td, files)"
                                                    ></app-uploader>
                                                </div>
                                            </el-col>
                                        </el-row>
                                  </div>
                                </el-card>
                            </div>
                        </div>
                    </div>
                    <!-- <div>
                        <div class="panel-title">审批</div>
                        <div>
                            <approval-panel v-if="dialogStatus == 'create' || dialogStatus == 'update'" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                            <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                        </div>
                    </div> -->
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
<!--                 <el-button @click="handleClose" v-show="dialogStatus == 'detail'"  type="primary" size="mini">关闭</el-button> -->
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn' v-show="editable"></app-button>
                <!-- <el-button @click="handleApproval"  type="primary" size="mini" :disabled='disabledBtn' v-show="dialogStatus == 'approval'">审批</el-button> -->
            </template>
        </app-dialog> 
        <!-- 选择地区 -->
        <v-area-choose
          @closeDialog="closeDialog"
          @electedRegionalData="electedRegionalData"
          :dialogFormVisible="dialogFormVisible"
          :checkedList="formData.areaId ? [formData.areaId] : []"
          :disabledFn="disabledFn"
        ></v-area-choose>
        <!-- 添加配置    -->
        <!-- <config-page
          @closeDialog="closeDialogConfig"
          @saveSuccess='handleConfigChangeSaveSuccess'
          :dialogFormVisible="dialogConfigVisible"
          :configIds='configIds'
        ></config-page> -->
        <!-- 关联订单 -->
        <order-selector
            :isShow='dialogAccessUsers'
            :checkedList='formData.Order ? [formData.Order] : []'
            @changed='handleChangeUsers'
            @closed='() => dialogAccessUsers = false'
            :multiple='false'
        >
        </order-selector>
        <!-- 结构配件清单 -->
        <!-- <v-structural
            @closeDialog="closeDialogStructural"
            :dialogFormVisible="dialogStructuralVisible"
            :id="radioId"
            :allAcceData='allAcceData'
            @saveSuccess="handleAcceSuccess">
        </v-structural> -->
        <!-- 批量导入设备部件模板 -->
        <accessories-template
            @closeDialog="closeAtDialog"
            :dialogFormVisible="dialogAtVisible"
            @getRadio="getRadio">   
        </accessories-template>
        <!-- 其它批量导入 -->
        <import-time
            @closeDialog="closeTimeDialog"
            :dialogFormVisible="dialogTimeVisible"
            :title="timeTitle"
            :type="batchType"
            :options="batchOptions"
            @saveSuccess='handleTimeSuccess'>
        </import-time>
    </div>
</template>
<script>
import * as equUse from "@/api/equipmentUse";
import * as equMode from "@/api/equipmentWorkMode";
import elDragDialog from '@/directive/el-dragDialog'
import * as accessories from "@/api/afterSalesMgmt/accessories";
import vAreaChoose from "./common/areaChoose";
import configPage from "./common/configPage";
import accessoriesTemplate from "./accessoriesTemplate";
import vStructural from "./structural";
import importTime from "./importTime";
import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail'
// import * as productListManagement from '@/api/systemManagement/productListManagement';
import * as orderMgmt from '@/api/salesMgmt/orderMgmt'
import * as businessMap from "@/api/businessMap";
import orderSelector from '../../common/orderSelector'
import { listToTreeSelect } from '@/utils';
import { vars } from '../../salesMgmt/common/vars' 
export default{
    name:'businessMapCreate',
    // mixins: [indexPageMixin],
    components: {
        vAreaChoose,
        approvalPanel,
        approvalDetail,
        // orderTable,
        configPage,
        orderSelector,
        vStructural,
        accessoriesTemplate,
        importTime,
    },
    props:{
        specialPageTitle: {
            type: String
        },
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        },
        approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
            type: String,
            default: ''
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
        regionalName:{
            type: String,
            default: ''
        },
        regionalId:{
            type: String,
            default: ''
        },
        regionalLevel:{
            type:Number,
            default:1
        }
    },
    directives: {
        elDragDialog
    },
    data(){
        return{
            bools: [
                {value: true, label: '有'},
                {value: false, label: '无'},
            ],
            tempTypes: [
                {value: 1, label: '出口温度'},
                {value: 2, label: '炉体温度'},
            ],
		    warrantyList:vars.business.warranty,
            batchTitle:'',
            dialogBatchVisible:false,
            batchOptions:[],
            purposeOptions:[],
            workOptions:[],
            optionsvalue: '',
            checkedAll:false,
            loading:false,
            timeTitle:'',
            batchType:0,
            dialogTimeVisible:false,
            dialogAtVisible:false,
            areaId:'',
            orderLoading:false,
            configIndex:0,
            configIds:[],
            dialogConfigVisible:false,
            dialogAccessUsers: false,
            dialogStructuralVisible:false,
            // typeTreedata:[],
            normalizer2(node) {
                return {
                    id: node.Id,
                    label: node.ProductName,
                    children: node.children
                }
            },
            orderDetailList:{
                OrderDetailList:[]
            },
            tableObj:{
                jiaErrMsg:'加热炉/锅炉不能为空',
                jiaErrShow:false,
                luErrMsg:'炉号不能为空',
                luErrShow:false,
                "Name": "",
                HeatNumber:"",
                EquipmentUseId:"",
                EquipmentWorkModeId:"",
                BurnerModel:"",
                Manufacturer:"",
                StructureTypeName:"",
                BurnerPressure:"",
                ProduceDate:"",
                ProduceNumber:"",
                PowerParams:"",
                TestCertificateNo:"",
                AttachmentList:[],
                // "ProductListManagementId": null,
                "InstallTime": "",
                "IsWarranty": 1,
                FurnaceProtect: false,
                LeakProtect: false,
                FurnacePracticeOptimize: false,
                TemperatureLinkage: 1,
                GasHighPressureInterlocking: false,
                GasLowPressureInterlocking: false,
                FurnacePressureChain: false,
                LiquidLevelLinkage: false,
                ControlSystemPLCManufacturer: '',
                EquipmentStatusOrProblem: '',
                FuelType: "",
                HeatFurnaceRatework: "",
                BurnerRatework: "",
                "WarrantyTime": "",
                // "EquipSettingIds": [],
                // "Remark": "",  
                "checked":false,
                'acceData':[],
                'acceNum':0,
                'equipNum':0,
                'structPartsSpecificationIdList':[],
            },
            tableData:[],
            dialogFormVisible:false,
            formData:{
                listLength:0,
                areaName:"",
                areaId:"",
                ContractNumber: '',
                Order: {//订单ID
                    ClientUnits: "",
                    CreateTime: "",
                    RegionalName: "",
                    Employee: null,
                    EmployeeId: "",
                    Id: "",
                    OrderNumber: "",
                    OrderState: "",
                    OrderSum: "",
                    ReceivedPayment: "",
                    RegionalId: "",
                    SignatureTime: "",
                    OrderAmount:0,
                }, 
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
            },
            disabledBtn:false,
            rules: {
                // 'Order.OrderNumber': {
                //     fieldName: "选择订单",
                //     rules: [{ required: true, trigger: 'change' }]
                // },
                areaName:{
                    fieldName: "选择地区",
                    rules: [{ required: true, trigger: 'change' }]
                }
            },
            tabColumns: [
                {
                  attr: { prop: "ProductName", label: "产品名称" }
                },
                {
                  attr: { prop: "ProductListName", label: "类型" },
                },
                {
                  attr: { prop: "ProductPrice", label: "单价(元)" }
                },
                {
                  attr: { prop: "ProductNum", label: "数量" }
                },
                {
                  attr: { prop: "ProductTotalPrice", label: "总价(元)" },
                },
                {
                  attr: { prop: "OrderType", label: "订单类型"},
                  slot: true
                },
                {
                  attr: { prop: "Remarks", label: "备注", width: "200" },
                  slot: true
                },
              ],
            // tabColumns1:[
            //     {
            //       attr: { prop: "Name", label: "设备名称",mandatory:true },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "ProductListManagementId", label: "类型",mandatory:true },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "InstallTime", label: "投产时间",mandatory:true },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "IsWarranty", label: "是否在保",mandatory:true },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "WarrantyTime", label: "保修有效期至",mandatory:true },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "EquipSettingIds", label: "配置参数",width:"120" },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "Remark", label: "备注" },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "Operation", label: "操作",width:"80" },
            //       slot: true
            //     },
            // ],
            areaTreeData:null,
            radioId:'',
            allAcceData:[],
            acceIndex:0,
        }
    },
    filters: {
        orderStater(status) {
            let obj = vars.orderMgmt.approvalStatus.find(s => s.value == status)
            if(obj) {
                return obj.label
            }
            return status
        },
        receivedPaymenter(status) {
            const statusObj = vars.orderMgmt.remittanceStatus.find(s => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return status;
        },
        orderTyper(status) {
            const statusObj = vars.orderMgmt.orderTypes.find(s => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return status;
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.batchOptions=[];
                    this.acceIndex=0;
                    this.radioId='';
                    this.checkedAll=false;
                    this.resetFormData();
                    if(this.regionalLevel > 1){
                        this.formData.areaName=this.regionalName;
                        this.formData.areaId=this.regionalId;
                        this.add();
                    }
                    this.getPurposeWork();
                    // if((this.dialogStatus == 'approval' || this.dialogStatus == 'detail') && this.id) {
                    //     this.getApprovalData()
                    // }
                }
            },
            immediate: true
        }
    },
    computed:{
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail" && this.dialogStatus != 'approval'
        },
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            return '添加设备'
        },
    },
    created(){
        this.rules = this.initRules(this.rules)
        // this.getAreaTreeData();
        // this.getRegionals();
    },
    mounted(){
        
    },
    methods:{
        handleFilesUpChange(td, files) {
            td.AttachmentList = files
        },
        closeOrder(){
            this.formData.ContractNumber = ''
            this.formData.Order=null;
        },
        getPurposeWork(){
            equUse.getList({"pageIndex": 1,"pageSize": 10000}).then(res => {
                this.purposeOptions=res.Items;
            })
            equMode.getList({"pageIndex": 1,"pageSize": 10000}).then(res => {
                this.workOptions=res.Items;
            })
        },
        handleCheckedAll(val){
            this.tableData.forEach(v => {
                v.checked=val;
            })
        },
        handleAcceSuccess(d){
            this.tableData[this.acceIndex].acceData=d;
            this.tableData[this.acceIndex].acceNum=d.length;
            this.closeDialogStructural();
        },
        handleTimeSuccess(d){
            this.closeTimeDialog();
            console.log(d)
            this.tableData.forEach(v => {
                if(v.checked){
                    if(this.timeTitle== "投产时间"){
                        v.InstallTime=d;
                    }else if(this.timeTitle== "保修有效期"){
                        if(v.IsWarranty == 2){
                            v.WarrantyTime=d;
                        }
                    }else if(this.timeTitle== "生产厂家"){
                        v.Manufacturer=d;
                    }else if(this.timeTitle== "用途"){
                        v.EquipmentUseId=d;
                    }else if(this.timeTitle== "供风方式"){
                        v.EquipmentWorkModeId=d;
                    }
                }
            })
        },
        getRadio(d){
            this.radioId=d;
            this.closeAtDialog();
            
                let postData={
                    "pageIndex": 1,
                    "pageSize": 1000000,
                    "id": this.radioId
                }
                accessories.getPartSpecification(postData).then(res => {
                    this.tableData.forEach(v => {
                        if(v.checked){
                            res.Items.forEach(v => {
                                v.StructPartName=v.Name;
                            })
                            v.acceNum+=res.Items.length;
                            v.acceData=v.acceData.concat(res.Items);
                        }
                    })
                    
                }).catch(err => {
                    
                })
            
        },
        handleAccess(){
            this.dialogAtVisible=true;
        },
        handleBatchAdd(title,type){
            this.timeTitle=title;
            this.batchType=type;
            if(title == '用途'){
                this.batchOptions=JSON.parse(JSON.stringify(this.purposeOptions));
            }else if(title == '供风方式'){
                this.batchOptions=JSON.parse(JSON.stringify(this.workOptions));
            }
            this.dialogTimeVisible=true;
        },
        closeAtDialog(){
            this.dialogAtVisible=false;
        },
        closeTimeDialog(){
            this.dialogTimeVisible=false;
        },
        closeBatchDialog(){
            this.dialogBatchVisible=false;
        },
        handleBatchSuccess(){
            this.dialogBatchVisible=false;
        },
        handleApproval(){
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
        
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        //项目创建审批
                        this.disabledBtn = true
                        businessMap.approvalAsync(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })
                }
            })
        },
        getApprovalData() {
            this.loading = true
            businessMap.getCreateApprovalDetailsAsync({id: this.id}).then(res => {
                this.loading = false
                this.formData.areaName=res.RegionalName;
                this.formData.Order.OrderNumber=res.OrderNumber;
                this.formData.Approval=res.Approval;
                res.EquipmentList.forEach(v => {
                    //v.IsWarranty=(v.IsWarranty == true ? 1 : 0);
                    v.EquipSettingIds=v.EquipmentSettingIdList;
                    v.equipNum=(v.EquipmentSettingList ? v.EquipmentSettingList.length : 0);
                    v.acceNum=v.PartSpecificationList.length;
                })
                this.tableData=res.EquipmentList;
                this.getOrderDetail(res.OrderId);
            }).then(err => {
                this.loading = false
            })
        },
        add(){
            this.tableData.push(JSON.parse(JSON.stringify(this.tableObj)));
            this.checkedAll=false;
            this.formData.listLength=this.tableData.length;
        },
        selectChange(){
            
        },
        // getRegionals() {
        //     productListManagement.getListByCondition({}).then(res => {
        //         this.typeTreedata = listToTreeSelect(res);
        //     })
        // },
        getAreaTreeData(){
            businessMap.getListByCondition({RegionalName:""}).then(res => {
                this.areaTreeData=JSON.parse(JSON.stringify(res));
            })
        },
        handleDelete(index){
            this.tableData.splice(index,1);
            this.formData.listLength=this.tableData.length;
        },
        handleClickIsw(index){
            if(this.tableData[index].IsWarranty != 2 && this.tableData[index].IsWarranty != 4){
                this.tableData[index].WarrantyTime='';
            }
        },
        handleAddConfig(index){
            this.configIndex=index;
            this.configIds=this.tableData[this.configIndex].EquipSettingIds;
            this.dialogConfigVisible=true;
        },
        handleAddStructural(index){
            this.acceIndex=index;
            this.allAcceData=this.tableData[index].acceData;
            this.dialogStructuralVisible=true;
        },
        resetFormData() {
            this.tableData=[];
            this.orderDetailList.OrderDetailList=[];
            this.formData={
                areaName:"",
                listLength:0,
                areaId:"",
                Order: {//订单ID
                    ClientUnits: "",
                    CreateTime: "",
                    RegionalName: "",
                    Employee: null,
                    EmployeeId: "",
                    Id: "",
                    OrderNumber: "",
                    OrderState: "",
                    OrderSum: "",
                    ReceivedPayment: "",
                    RegionalId: "",
                    SignatureTime: "",
                    OrderAmount:0
                }, 
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
            }
        },
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        createData(){
            this.disabledBtn = true;
            let listResult=this.validateTable();
            let orderResult=this.$refs.formData.validate()
            // let approvalPanelValidate = this.$refs.approvalPanel.validate()
            // this.
            if(listResult){
                Promise.all([orderResult]).then(valid => {
                    // this.tableData.forEach(v => {
                    //     v.EquipmentSettingIds=v.EquipSettingIds;
                    // })
                    this.tableData.forEach(v => {
                        v.structPartsSpecificationIdList=[];
                        v.StructPartsSpecificationList=[];
                        v.acceData.forEach(v1 => {
                            v.structPartsSpecificationIdList.push(v1.StructPartsSpecificationId);
                            v.StructPartsSpecificationList.push({
                                SpecificationModel: v1.SpecificationModel,
                                StructPartId: v1.StructPartId,
                                StructPartName: v1.StructPartName,
                                StructPartsSpecificationId: v1.StructPartsSpecificationId,
                                SupplierId: v1.SupplierId,
                                SupplierName: v1.SupplierName,
                            })
                        })

                        v.AttachmentIdList = v.AttachmentList.map(t => t.Id)
                        delete v.AttachmentList
                    })
                    let postData={
                        "OrderId": this.formData.Order.Id,
                        "OrderNumber": this.formData.Order.OrderNumber,
                        "RegionalId": this.formData.areaId,
                        "EquipmentList": this.tableData,
                    }
                    // postData.Approval = this.$refs.approvalPanel.getData() //审批层区块

                    //提交数据保存

                    // postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                    // postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                    // postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    businessMap.add(postData).then(res => {
                        this.$notify({
                          title: '成功',
                          message: '添加成功！',
                          type: 'success'
                        });
                        this.$emit('saveSuccess');
                        this.disabledBtn=false;
                    }).catch(err => {
                        this.disabledBtn=false;
                    })
                }).catch(e => {
                    this.disabledBtn=false;
                })
            }else{
                this.disabledBtn=false;
            }
        },
        validateTable(){
            if(this.tableData.length == 0) {
                this.$message({
                    message: '设备列表不能为空',
                    type: 'error'
                })
                return false;
            }else if(this.tableData.find(s => !s.Name.trim())) {
                this.$message({
                    message: '加热炉/锅炉不能为空',
                    type: 'error'
                })
                return false;
            }else if(this.tableData.find(s => !s.HeatNumber.trim())) {
                this.$message({
                    message: '炉号不能为空',
                    type: 'error'
                })
                return false;
            }
            for (var i = 0; i < this.tableData.length; i++) {
                if((this.tableData[i].IsWarranty == 2 || this.tableData[i].IsWarranty == 4) && !this.tableData[i].WarrantyTime.trim()){
                    this.$message({
                        message: '保修有效期不能为空!',
                        type: 'error'
                    })
                    return false;
                }
            }
            return true;
        },
        rowSelectionChanged(rows) {
          this.multipleSelection = rows;
        },
        //地区选择
        closeDialog() {
          this.dialogFormVisible = false;
        },
        handleDialog(){
            this.dialogFormVisible=true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('areaName');
            if(data){
                this.formData.areaId=data.Id;
                this.formData.areaName=data.ParentName;
                this.tableData=[];
                this.add();
            }else{
                this.formData.areaId='';
                this.formData.areaName='';
                this.tableData=[];
            }
        },
        disabledFn(data, nodeType) {
            //禁选一级节点
            if(data.level <= 1) {
                return true
            }
            return false
        },
        closeOrderCreateDialog() {
            this.dialogOrderCreateFormVisible = false
        },
        handleOrderCreateSaveSuccess() {
            this.getList()
            this.closeOrderCreateDialog()
        },
        handleChangeUsers(orders) {
            if(orders && orders.length > 0) {
                // this.$refs.formData.clearValidate('Order.OrderNumber');
                this.formData.ContractNumber = orders[0].ContractNumber
                this.formData.Order = orders[0];
                this.formData.Order.OrderAmount=0;
                this.getOrderDetail(orders[0].Id);
            }else{
                this.formData.ContractNumber = ''
                this.formData.Order = []
            }
            this.dialogAccessUsers = false
        },
        closeDialogConfig() {
          this.dialogConfigVisible = false;
        },
        closeDialogStructural(){
            this.dialogStructuralVisible=false;
        },
        handleConfigChangeSaveSuccess(ids){
            this.tableData[this.configIndex].equipNum=(ids.length-1)/2;
            this.tableData[this.configIndex].EquipSettingIds=ids;
        },
        getOrderDetail(id) {
            this.orderLoading = true
            orderMgmt.detail({id:id, approvalId: ''}).then(res => {
                this.orderLoading = false
                if(res.OrderDetailList.length>0){
                    res.OrderDetailList.forEach(v => {
                        this.formData.Order.OrderAmount+=v.ProductTotalPrice;
                    })
                }
                this.formData.Order.ClientUnits=res.ClientUnits;
                this.formData.Order.ReceivedPayment=res.ReceivedPayment;
                this.formData.Order.RegionalName=res.Regional;
                this.formData.Order.OrderState=res.OrderState;
                this.orderDetailList=res;
            }).then(err => {
                this.orderLoading = false
            })
        },
    }

}
</script>
<style scoped>
.elMb >>> .el-form-item--mini.el-form-item{
    margin-bottom:6px;
}
</style>
<style lang="scss" scoped>
.el-icon-delete{
    color: rgb(245, 108, 108);
    float: right;
    margin-right: 10px;
    font-size: 18px;
    margin-top: 8px;
}
.dFlex{
    display: flex;
    align-items: center;
    >span{
        width:140px;
        text-align: right;
    }
    >div{
        width:calc(100% - 140px);
    }
}
.box-card{
    margin-bottom:10px;
}
.associatedArea{
    border-top:1px solid #EBEEF5;
    margin:15px 0;
    .associatedHead{
        padding:10px 10px;
        border-left:1px solid #EBEEF5;
        border-right:1px solid #EBEEF5;
        border-bottom:1px solid #EBEEF5;
        display: flex;
        >span{
            margin-right:40px;
        }
    }
    .tableTitle{
        border-left:1px solid #EBEEF5;
        border-right:1px solid #EBEEF5;
        padding:8px 10px;
    }
}
.addEquipment{
    margin-bottom: 15px;
    >div:nth-child(2){
        // border-bottom: 1px solid #dcdfe6;
        padding:2px 0;
    }
    >div:nth-child(3){
        >div:first-child{
            padding:0 0 10px 0;
        }
    }
}
.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    border-bottom: 1px solid #dcdfe6;
}

.sp-row{
    height: 38px;
    line-height: 38px;
}
</style>