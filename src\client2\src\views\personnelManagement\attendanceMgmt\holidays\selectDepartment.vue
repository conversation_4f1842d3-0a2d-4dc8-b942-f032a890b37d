<template>
  <div>
    <app-dialog title="适用范围" ref="appDialogSel" v-bind="$attrs" v-on="$listeners" :width="700">
      <template slot="body">
        <div class="wrapper">
          <el-form ref="formData" class="wrapper_left" :model="formData" :rules="rules" label-width="120px">
            <el-form-item label="选择有效类型">
              <el-select v-model="formData.calculateTimeType" placeholder style="width: 100%;">
                  <el-option label="按入职时间" :value="1"></el-option>
                  <el-option label="按指定日期" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="重置日期时间" prop='calculateTime' v-if="formData.calculateTimeType===2">
                <el-date-picker :clearable="false" style="width: 100%;" v-model="formData.calculateTime" type="date" format='yyyy-MM-dd' value-format='yyyy-MM-dd' placeholder=""></el-date-picker>
            </el-form-item>
          </el-form>
          <el-tree class="tree" v-loading="loading" :data="orgsTree" show-checkbox node-key="Id" @check='checkOrg' :default-expanded-keys="defaultExpandedKey" :props="defaultProps" ref="orgsTree">
          </el-tree>
        </div>
      </template>

      <template slot="footer">
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>

  </div>
</template>

<script>
import { listToTreeSelect } from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as hrLeaveBalanceData from "@/api/personnelManagement/hrLeaveBalanceData"
import indexPageMixin from "@/mixins/indexPage";

export default {
  name: "select-department",
  directives: {},
  components: {
  },
  mixins: [indexPageMixin],
  computed: {

  },
  props: {
    computationRuleId: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      this.OrgIds = []
      this.defaultExpandedKey = []
      if (val) {
        this.getOrgTree();
        this.formData = this.$options.data().formData
        this.formData.Id = this.computationRuleId
      }
    },
    "formData.calculateTimeType"(val) {
      if (val===1) {
        this.formData.calculateTime = null
      }
    }
  },
  filters: {
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  mounted() {

  },
  data() {
    return {
      loading: false,
      disabledBtn: false,
      orgsTree: [], //部门树
      defaultProps: { //树默认结构
        children: 'children',
        label: 'label'
      },
      defaultExpandedKey: [], //部门树默认展开节点
      OrgIds: [],
      formData: {
        Id: "",
        formulaMode: null,
        RangeApplication: "",
        calculateTimeType: 1,
        calculateTime: null,
      },
      rules: {
        calculateTime: { fieldName: "重置日期时间", rules: [{ required: true, trigger: 'change' }]},
      },
    };
  },
  methods: {

    //查询并加载部门树
    getOrgTree() {
      this.loading = true
      var _this = this;
      systemDepartment.getListByCondition({ Level: 1 }).then(response => {
        _this.list = response.map(function (item) {
          return {
            Id: item.Id,
            label: item.DepartmentName,
            ParentId: item.ParentId
          }
        })
        var orgstmp = JSON.parse(JSON.stringify(_this.list));
        var tempOrgsTree = listToTreeSelect(orgstmp);
        _this.defaultExpandedKey.push(tempOrgsTree[0]['Id']);
        _this.orgsTree = tempOrgsTree;
        _this.getDetail();
        _this.loading = false
      }).catch(err => {
        this.loading = false
      });
    },

    getDetail() {
      var _this = this;
      hrLeaveBalanceData.getHrComputationRuleDetail({ id: _this.computationRuleId }).then(res => {
        _this.formData.calculateTimeType = res.CalculateTimeType || 1
        _this.formData.calculateTime = res.CalculateTime
        _this.formData.formulaMode = res.FormulaMode
        if (res.DisabledList || res.CurrentList) {
          _this.$refs.orgsTree.setCheckedNodes([]);
          var selectElement = []
          _this.orgsTree[0].children.forEach(element => {
            var disabledObj = res.DisabledList.find(s => s == element.Id);
            if (disabledObj) {
              _this.$set(element, 'disabled', true)
            }
            var selectObj = res.CurrentList.find(s => s == element.Id);
            if (selectObj) {
              selectElement.push(element);
              _this.OrgIds.push(element.Id)
            }
          });
          _this.$refs.orgsTree.setCheckedNodes(selectElement);
        }
      });
    },

    //部门复选框发生变化触发事件
    checkOrg() {
      var _this = this;
      _this.OrgIds = [];
      //循环当前已选部门并将部门ID添加到_this.listQuery.OrgIds
      _this.$refs.orgsTree.getCheckedNodes().forEach((item) => _this.OrgIds.push(item.Id));
    },

    //编辑
    createData() {
      let self = this, validate = self.$refs.formData.validate();
      Promise.all([validate]).then(valid => {
        let postData = JSON.parse(JSON.stringify(self.formData));
        postData.RangeApplication = self.OrgIds.toString();
        self.disabledBtn = true
        hrLeaveBalanceData.editHrComputationRule(postData).then(res => {
          self.disabledBtn = false
          self.$notify({
            title: "提示",
            message: "保存成功",
            type: "success",
            duration: 2000
          });
          self.$emit('saveSuccess');
        }).catch(err => {
          self.disabledBtn = false
        })
      });
    },
    handleClose() {
      this.$refs.appDialogSel.handleClose();
    }
  }
};
</script>

<style lang="css" scoped>
.wrapper >>> .el-table__row {
  height: 35px !important;
}
</style>

<style lang='scss' scoped>
.wrapper {
  min-height: 400px;
  display: flex;
  .det {
    margin-bottom: 10px;
  }
  .wrapper_left{
    width: 60%;
    padding: 10px 50px 10px 0;
  }
  .tree{
    width: calc(40% - 2px);
    border-left: 1px solid #eee;
    padding: 10px 0;
  }
}
</style>