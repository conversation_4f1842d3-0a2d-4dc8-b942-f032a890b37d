<template>
    <div class="departTree">
        <app-dialog title="选择部门" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
             
        >
            <template slot="body">
                <div style="height:590px;overflow-y:auto;" v-loading="loading">
                    <el-tree 
                    class="tree" 
                    :data="orgsTree" 
                    :expand-on-click-node='false'
                    :check-strictly="true"
                    show-checkbox node-key="Id" 
                    :default-expanded-keys="defaultKeys"
                    :default-checked-keys="checkedList"
                    @check='checkOrg' 
                    @check-change="handleNodeCheck"
                    :props="defaultProps" 
                    ref="tree">
                    </el-tree>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2' style="margin-right:20px;"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1'></app-button>
            </template>
        </app-dialog>
    </div>
</template>
<script>
import {
    listToTreeSelect
} from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
export default{
    name:'departTree',
    components: {   
        
    },
    props:{
        // areaId:{
        //     type:String,
        //     default:''
        // },
        beforeConfirm: Function,
        queryRegionID:{
            type:String,
            default:''
        },
        defaultExpandLevel:{
            type: Number,
            default: 0
        },
        //默认选中
        checkedList: {
            type: Array,
            default: () => {
                return [];
            }
        },
    },
    data(){
        return{
            defaultKeys:[],
           orgsTree: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'label'
            },
            checkedId:'',
            loading:false,
            datas:null,
        }
    },
    watch: {
        
    },
    created(){
        
    },
    mounted(){
        if(this.checkedList.length>0){
            this.checkedId=this.checkedList[0];
        }
        this.getOrgTree();
        
    },
    methods:{
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        createData(){
            this.datas = this.$refs.tree.getCheckedNodes();
            if(this.datas && this.datas.length>0){
                this.$emit('saveSuccess',this.datas);
            }else{
                this.$message({
                    message: '请至少选中一条数据！',
                    type: 'warning'
                });
            }
        },
        handleNodeCheck(data, checked, node){
            // if(checked === true) {
            //     this.checkedId = data.Id;
            //     this.$refs.tree.setCheckedKeys([data.Id]);
            // }else {
            //     ////需要支持取消选中（如果需要关闭弹框前给与提示，请使用 areaChoose 组件的 beforeConfirm 属性 ）
            //     if (this.checkedId == data.Id) {
            //         this.$refs.tree.setCheckedKeys([data.Id]);
            //     }
            // }
            // this.$emit('checkChangeNod',datas',datas.length > ',datas[0] : null)
        },
        getOrgTree() { //查询并加载部门树
            this.loading=true;
            systemDepartment.getListByCondition({}).then(response => { //调用公共组件API获取部门数据
                this.loading=false;
                let list = response.map(function (item) {
                    return {
                        Id: item.Id,
                        label: item.DepartmentName,
                        ParentId: item.ParentId,
                        ParentName:item.DepartmentName,
                    }
                })
                var orgstmp = JSON.parse(JSON.stringify(list));
                var tempOrgsTree = listToTreeSelect(orgstmp, undefined, undefined, undefined, 'ParentName'); //将部门数据转换成树形结构
                this.defaultKeys.push(tempOrgsTree[0]['Id']); //设置默认展开
                this.orgsTree = tempOrgsTree;
                this.$refs.tree.setCheckedKeys(this.checkedList || []);
                this.defaultKeys = this.defaultKeys.concat(this.checkedList || [])
            })
        },
        checkOrg() { //部门复选框发生变化触发事件
            
        },
    }

}
</script>
<style lang="scss" scoped>

</style>