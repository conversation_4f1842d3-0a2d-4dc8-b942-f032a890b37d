<template>
    <div>
        <div class="app-container">
            <div class="bg-white">
                <app-table ref="mainTable" :key="tabKey" :tab-columns='tabColumns' :tab-datas='tabDatas'
                    :tab-auth-columns='tabAuthColumns' :isShowAllColumn='isShowAllColumn' :loading="listLoading"
                    @rowSelectionChanged='rowSelectionChanged' :isShowOpatColumn='rowBtns.length > 0'
                    :startOfTable='startOfTable'>
                    <template slot='header_Url' slot-scope="scope">
                        {{ scope.column.column.label }}
                    </template>
                    <template slot='Image' slot-scope="scope">
                        <img style="width: 100px; height: 60px;" :src="scope.row.Image" />
                    </template>
                    <template slot='PcImage' slot-scope="scope">
                        <img style="width: 100px; height: 60px;" :src="scope.row.PcImage" />
                    </template>
                    <template slot='AppImage' slot-scope="scope">
                        <img style="width: 100px; height: 60px;" :src="scope.row.AppImage" />
                    </template>
                    <template slot="IsRecommend" slot-scope="scope">
                        <el-tag v-if="scope.row.IsRecommend == 1" type="success">推荐</el-tag>
                        <el-tag v-else type="warning">不推荐</el-tag>
                    </template>
                    <template slot="Status" slot-scope="scope" :class="scope.row.Status | statusFilter">
                        <el-tag v-if="scope.row.Status" type="success">有效</el-tag>
                        <el-tag v-else type="warning">无效</el-tag>
                        <!-- <span class="color-success" v-if="scope.row.Status">有效</span>
                        <span class="color-danger" v-else>无效</span> -->
                    </template>
                    <template v-if="tableSearchItems.length > 0" slot="conditionArea">
                        <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems'
                            @onSearch='handleFilter' @onReset='resetSearch'>
                            <template slot='NewsClassId'>
                                <el-select clearable v-model="listQuery.NewsClassId" placeholder=""
                                    style="width: 100%;">
                                    <el-option v-for="item in categories" :key="item.value" :label="item.label"
                                        :value="item.value"></el-option>
                                </el-select>
                            </template>
                            <template slot='Title'>
                                <el-input style="width: 100%;" v-model="listQuery.Title" placeholder=""></el-input>
                            </template>
                        </app-table-form>
                    </template>

                    <template slot="btnsArea">
                        <permission-btn moduleName="news" v-on:btn-event="onBtnClicked"></permission-btn>
                    </template>

                    <template slot-scope="scope">
                        <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)"
                            :type='1'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnPreview')" @click="handleReview(scope.row)"
                            :type='2'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)"
                            :type='3'></app-table-row-button>
                        <!-- <el-button type="primary" v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)">编辑</el-button>
                        <el-button type="primary" v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)">删除</el-button>
                        <el-button type="primary" v-if="rowBtnIsExists('btnPreview')" @click="handleReview(scope.row)">预览</el-button> -->
                    </template>
                </app-table>

                <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
        <div v-if="dialogFormVisible">
            <edit-page :dialogStatus='dialogStatus' :dialogFormVisible='dialogFormVisible' :id='id'
                :categories='categories' @closeDialog='closeDialog' @saveSuccess='handleSaveSuccess'>
            </edit-page>
        </div>

    </div>
</template>

<script>
    import * as news from '@/api/news'
    // import waves from '@/directive/waves' // 水波纹指令
    // import Sticky from '@/components/Sticky'
    import EditPage from './edit'
    // import Layout from '@/views/layout/Layout'
    import indexPageMixin from '@/mixins/indexPage'
    export default {
        name: 'news',
        components: {
            // Sticky,
            EditPage
        },
        directives: {
            // waves,
        },
        mixins: [indexPageMixin],
        data() {
            return {
                id: '', //编辑主键
                tableSearchItems: [
                    { prop: 'NewsClassId', label: '所属分类' },
                    { prop: 'Title', label: '标题' },
                ],
                // 列表checkbox选中的值
                multipleSelection: [],
                // 列表显示的列
                tabColumns: [
                    //   {
                    //     attr: {prop: 'Url', label: '编号', width: '100'},
                    //     customHeader: true,
                    //   },
                    //   {
                    //     attr: {prop: 'NewsContentId', label: 'DES编码'},
                    //   },
                    {
                        attr: { prop: 'NewsClassId', label: '所属分类', formatter: this.formatterNewsClass },
                    },
                    {
                        attr: { prop: 'OrderIndex', label: '排序', width: '80' },
                    },
                    {
                        attr: { prop: 'Title', label: '标题' },
                    },
                    {
                        attr: { prop: 'IsRecommend', label: '推荐', width: '80' },
                        slot: true,
                        // isShow: false
                    },
                    {
                        attr: { prop: 'Status', label: '状态', width: '120' },
                        slot: true,
                    },
                    {
                        attr: { prop: 'Image', label: '官网封面', width: '140' },
                        slot: true
                    },
                    {
                        attr: { prop: 'PcImage', label: 'PC封面', width: '140' },
                        slot: true
                    },
                    {
                        attr: { prop: 'AppImage', label: 'App封面', width: '140' },
                        slot: true
                    },
                    {
                        attr: { prop: 'UpdateTime', label: '最近更新时间', formatter: this.formatterDate },
                    },
                ],
                // 列表显示的数据
                tabDatas: [],
                tabKey: 0,
                total: 0, //总条数
                listLoading: true,//加载数据中
                listQuery: { // 查询条件
                    NewsClassId: '',
                    Title: '',
                },
                dialogFormVisible: false,//新增(编辑)弹框是否显示
                dialogStatus: '',//编辑还是新增(create: 新增; update: 编辑)
                // downloadLoading: false,
                categories: []
            }
        },
        created() {

            this.getNewsCategories()
            let paras = this.$store.state.indexPageParas.listPageParas[this.$route.path]
            if (!paras) {
                paras = {
                    PageIndex: this.listQuery.PageIndex,
                    PageSize: this.listQuery.PageSize
                }
            } else {
                paras = Object.assign({}, paras, {
                    PageIndex: this.listQuery.PageIndex,
                    PageSize: this.listQuery.PageSize
                })
            }

            if (paras.NewsClassId) {
                paras.NewsClassId = paras.NewsClassId.toLowerCase()
            }

            this.listQuery = paras
            this.getList()
        },
        methods: {
            //当表格行中所有 checkbox 选中状态项改变时（返回所有选中的行数据）
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            onBtnClicked: function (domId) {
                // console.log('you click:' + domId)
                switch (domId) {
                    case 'btnAdd':
                        this.handleCreate()
                        break
                    case 'btnEdit':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行编辑',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0])
                        break
                    case 'btnDel':
                    case 'btnDelStock':
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: '至少删除一个',
                                type: 'error'
                            })
                            return
                        }
                        this.handleDelete(this.multipleSelection)
                        break
                    default:
                        break
                }
            },
            getList() {
                this.listLoading = true
                this.recordConditions({ path: this.$route.path, paras: this.listQuery })
                news.getList(this.listQuery).then(response => {
                    this.tabDatas = response.Items
                    if (this.tabDatas && this.tabDatas.length > 0) {
                        this.tabDatas.forEach(i => {
                            if (!i.Image) i.Image = ''
                            if (!i.PcImage) i.PcImage = ''
                            if (!i.AppImage) i.AppImage = ''
                        })
                    }
                    this.total = response.Total
                    this.listLoading = false
                }).catch(err => {
                    this.listLoading = false
                })
            },
            getNewsCategories() {
                news.getNewsCategories().then(res => {
                    this.categories = res.map(item => {
                        return {
                            label: item.Title, value: item.NewsClassId
                        }
                    })
                })
            },
            handleFilter() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
            closeDialog() {
                this.dialogFormVisible = false
            },
            handleSaveSuccess(_formData) {
                if (this.dialogStatus == 'update') {
                    let oldObj = this.tabDatas.findIndex(s => s.NewsContentId == _formData.NewsContentId)
                    if (oldObj > -1) {
                        this.tabDatas.splice(oldObj, 1, _formData)
                    }
                } else {
                    this.listQuery.PageIndex = 1
                    this.getList()
                }
                this.dialogFormVisible = false
            },
            handleCreate() { // 弹出添加框
                this.dialogStatus = 'create'
                this.dialogFormVisible = true
                this.id = ''
            },
            handleReview(row) {
                this.$router.push(`/news/detail/${row.NewsContentId}`)
            },
            handleUpdate(row) { // 弹出编辑框
                this.dialogStatus = 'update'
                this.dialogFormVisible = true
                this.id = row.NewsContentId
            },
            handleDelete(rows) { // 多行删除
                let ids = []
                if (_.isArray(rows)) {
                    ids = rows.map(u => u.NewsContentId)
                } else {
                    ids.push(rows.NewsContentId)
                }

                this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    news.del(ids).then(() => {
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.listQuery.PageIndex = 1
                        this.getList()
                    })
                })
            },
            formatterNewsClass(row, column) {
                let textObj = this.categories.find(o => o.value == row.NewsClassId);
                if (textObj) {
                    return textObj.label
                }
                return ''
            },
            formatterDate(row, column) {
                let f = this.$options.filters['dateFilter']
                return f(row.UpdateTime, 'YYYY-MM-DD HH:mm:ss')
            },
        }
    }
</script>
<style>
    .dialog-mini .el-select {
        width: 100%;
    }
</style>
