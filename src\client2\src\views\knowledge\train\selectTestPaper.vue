<template>
<div>
    <app-dialog
        :title="pageTitle"
        ref="appDialogRef"
        v-bind="$attrs"
        v-on="$listeners"
        :width="1000"
        :maxHeight='600'
    >
        <template slot="body">
            <el-row class="dialogWapper">
                <div class="product-list">
                    <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                    <div class="treeBox" v-loading='treeLoading'>
                        <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node, data }" :style="{paddingRight: data.Level == 1 ? '60px' : ''}">
                                <span class="node-title" :title="node.label">{{ node.label }}</span>
                            </span>
                        </el-tree>
                    </div>
                </div>
                <div class="content-wrapper">
                    <el-row class="searchBox">
                        <el-input style="width: 300px;" 
                            placeholder="搜索考卷名称/出卷人"
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQuery.Keywords"
                        ></el-input>
                    </el-row>
                    <el-table
                        fit
                        :data="tableData"
                        style="width: 100%"
                        v-loading="tableLoading"
                        height="500"
                        :header-cell-style="{'text-align':'left'}"
                        highlight-current-row row-key="Id" @sort-change="handleSortChange"
                        ref="mainTable"
                    >
                        <!-- <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column> -->
                        <el-table-column width="45" label="选择" align="center">
                            <template slot-scope="scope">
                                <el-radio class="notLabel" v-model="checkedRadioValue" @change='handleRadioChange($event,scope.row)' :label="scope.row.Id">&nbsp;</el-radio>
                            </template>
                        </el-table-column>
                        <el-table-column type="index" label="序号" width="55"></el-table-column>
                        <el-table-column label="试卷名称" prop="ExaminationPaperName" showOverflowTooltip></el-table-column>
                        <el-table-column label="试卷类型" prop="ClassifyName" showOverflowTooltip></el-table-column>
                        <el-table-column label="出卷人" prop="OwnerEmployeeNames" showOverflowTooltip></el-table-column>
                        <el-table-column label="题目数量" prop="QuestionNum" sortable="custom"></el-table-column>
                        <el-table-column label="作答时间" prop="ResponseTime" sortable="custom">
                            <template slot-scope="scope">{{scope.row.ResponseTime}}分钟</template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </el-row>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>
</div>
</template>
<script>
import indexPageMixin from "@/mixins/indexPage";
import * as classify from '@/api/classify'
import { listToTreeSelect } from "@/utils";
import * as examinationPaperManagementApi from '@/api/knowledge/ExaminationPaperManagement'
export default {
    name: "select-test-paper",
    mixins: [indexPageMixin],
    components: {
    },
    props: {
        // 是否多选
        multiple: { //create、update、detail
            type: Boolean,
            default: true
        },
        pageTitle: {
            type: String,
            default: '选择试卷'
        },
        ids: {
            type: String,
            default: ''
        }
    },
    computed: {
    },
    watch: {
        '$attrs.dialogFormVisible': {
            handler(val) {
                this.checkedRadioValue = this.ids
                this.multipleSelection = []
                if(val) {
                    this.loadTreeData();
                }
            },
            immediate: true
        },
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.ClassifyId = val.Id;
                    this.getList();
                }
            },
        },
    },
    data() {
        return {
            disabledBtn: false,
            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            multipleSelection: [],
            tableData: [],
            tableLoading: false,
            listQuery: {
                Keywords: '',
                PageIndex: 1,
                PageSize: 20
            },
            total: 0,
            checkedRadioValue: null,
        }
    },
    methods: {
        
        handleRadioChange(e, val){
            // console.log(val)
            this.checkedRadioValue = val.Id
            this.multipleSelection = []
            this.multipleSelection.push(val)
        },
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
        // 提交
        createData() {
            if(this.checkedRadioValue){
                if (this.multipleSelection.length>0) {
                    this.$refs.appDialogRef.createData({
                        Id: this.multipleSelection[0].Id,
                        Name: this.multipleSelection[0].ExaminationPaperName
                    });
                } else {
                    this.handleClose()
                }
            }else{
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
                return;
            }
        },
        // 取消
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let self = this;
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: 10
            };
            self.treeLoading = true
            classify.getListPage(paramData).then(res => {
                self.treeLoading = false
                let response = res.Items
                response.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });
                self.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将数据转换成树形结构
                //如果首次加载问价夹树（没有选中），默认选中根节点
                if (!self.checkedNode) {
                    self.setDefaultChecked();
                }
            }).catch(err => {
                self.treeLoading = false
            });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeData && this.treeData.length > 0) {
                    let rootNode = this.treeData[0];
                    this.$refs.treeRef.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        //获取项目列表
        getList() {
            this.tableLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            examinationPaperManagementApi.getList(postData).then((res) => {
                this.tableLoading = false;
                this.tableData = res.Items;
                this.total = res.Total;
            });
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
    }
}
</script>
<style scoped>
.notLabel >>> .el-radio__label{
    display: none;
}
</style>
<style lang="scss" scoped>
.dialogWapper{
    display: flex;
    height: 600px;
    .product-list {
        width: 250px;
        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        .treeBox {
            flex: 1;
            overflow-y: auto;
            width: 100%;
            .elInput {
                width: 230px;
                margin-left: 10px;
            }
            .elTree {
                height: 100%;
                overflow: auto;
            }
        }
    }
    .content-wrapper {
        width: calc(100% - 250px);
        flex: 1;
        overflow-y: auto;
        .searchBox{
            padding: 10px;
        }
    }
}
</style>