<template>
    <div>
        <el-dialog
            v-el-drag-dialog
            class="dialog-mini"
            width="1000px"
            title="转至案例库"
            :visible.sync="dialogVisible"
            :close-on-click-modal='false'
            :append-to-body='true'
            v-loading='loading'
            >
            <el-form
                :rules="rules"
                ref="dataForm"
                :model="temp"
                label-position="right"
                label-width="140px"
                v-if="dialogVisible"
                style="max-height: 600px; overflow-y: auto;"
            >
                <div class="step-area">
                    <div class="step-title">
                        <h3>基本信息</h3>
                    </div>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item :label="'故障现象'" prop="Symptom">
                                <el-input type="textarea" :rows="3" maxlength='500' :disabled="getEditable()" v-model="temp.Symptom"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item :label="'关键字'" prop="Keywords">
                                <param-add :optType='1' :disabled="getEditable()" :checkedList='temp.Keywords' @success='data => handleChoiced(data, 1)'></param-add>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item :label="'影响点位参数'" prop="PointPositionParams">
                                <param-add :optType='2' :disabled="getEditable()" :checkedList='temp.PointPositionParams' @success='data => handleChoiced(data, 2)'></param-add>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item :label="'关联综合报警参数'" prop="AssCompAlarmParams">
                                <param-add :optType='3' :disabled="getEditable()" :checkedList='temp.AssCompAlarmParams' @success='data => handleChoiced(data, 3)'></param-add>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item :label="'现场图片'" prop="Images">
                                <app-upload-file :readonly="getEditable()" :fileType='1' :max='5' :value='temp.Images' :fileSize='10 * 1024 * 1024' :minFileSize='10 * 1024' @change='handleUpChange' :preview='true'></app-upload-file>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item :label="'现场视频'" prop="VideoPath">
                                <app-upload-big-file :readonly="getEditable()" accept='mp4,avi' :fileType='3' :max='videoLimit' :value='temp.Videos' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024' @change='handleVideoUpChange'></app-upload-big-file>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item :label="'维修类型'" prop="EquipmentCategoryType">
                                <el-radio :disabled="getEditable()" v-model="temp.EquipmentCategoryType" v-for="(e, idx) in equipmentCategoryTypes" :key="idx" :label="e.type">{{ e.text }}</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item :label="'原因分析'" prop="Analysis">
                                <el-button type="text" @click="handleAddRow(2)" v-show='!getEditable()'>新增</el-button>
                                <el-table :data="temp.Analysis" border style="width: 100%">
                                    <el-table-column prop="Text" label="描述">
                                        <template slot-scope="scope">
                                            <app-tag :text='scope.row.Text' size="mini" :type="scope.row.InputMode == 1 ? '' : 'warning'" :closable='false'></app-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="120">
                                        <template slot-scope="scope">
                                            <app-table-row-button v-if='!getEditable() && scope.row.InputMode == 1' @click="handleUpdate(scope.$index, 2)" :type='1'></app-table-row-button>
                                            <app-table-row-button @click="handleUpdate(scope.$index, 2, 'detail')" :type='2'></app-table-row-button>
                                            <app-table-row-button v-if='!getEditable()' @click="handleRemove(scope.$index, 2)" :type='3'></app-table-row-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item :label="'解决方法'" prop="Solutions">
                                <el-button type="text" @click="handleAddRow(3)" v-show='!getEditable()'>新增</el-button>
                                <el-table :data="temp.Solutions" border style="width: 100%">
                                    <el-table-column prop="Text" label="描述">
                                        <template slot-scope="scope">
                                            <app-tag :text='scope.row.Text' size="mini" :type="scope.row.InputMode == 1 ? '' : 'warning'" :closable='false'></app-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="120">
                                        <template slot-scope="scope">
                                            <app-table-row-button v-if='!getEditable() && scope.row.InputMode == 1' @click="handleUpdate(scope.$index, 3)" :type='1'></app-table-row-button>
                                            <app-table-row-button @click="handleUpdate(scope.$index, 3, 'detail')" :type='2'></app-table-row-button>
                                            <app-table-row-button v-if='!getEditable()' @click="handleRemove(scope.$index, 3)" :type='3'></app-table-row-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="step-area">
                    <div class="step-title">
                        <h3>分类方式</h3>
                    </div>
                    <div style="padding-right: 10px;">
                        <equipment-area @changed="handleDynamicListChanged" :uneditable="getEditable()" :checked="temp.EquipmentTypeSubtypeRelationIds.EquipmentPropValueIds"></equipment-area>
                    </div>
                </div>
                    <div class="step-area">
                        <div class="step-title">
                            <h3>选择人员</h3>
                        </div>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item :label="'审批人员'" prop="MakerList">
                                    <emp-selector key='service-users' :readonly="true" :list="temp.MakerList" @change="handleChangeServiceUser"></emp-selector>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="step-area" v-show="dialogStatus != 'create'">
                        <div class="step-title">
                            <h3>其他信息</h3>
                        </div>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="审批记录">
                                    <el-button type="text" :disabled="auditRecord == 0" @click="() => dialogHistoryFormVisible = true">显示审批记录 ({{auditRecord}}条)</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>

            </el-form>
            <!--新增原因分析、解决方案弹框-->
            <el-dialog
                v-el-drag-dialog
                class="dialog-mini"
                :title="optTitle"
                :visible.sync="dialogSymptomVisible"
                width="800px"
                :close-on-click-modal='false'
                :before-close="() => dialogSymptomVisible = false"
                :append-to-body='true'
                >
                <el-form
                    :rules="symptomRules"
                    :model="symptomTemp"
                    label-width="100px"
                    ref="symptomDataForm"
                    label-position="right"
                    v-if="dialogSymptomVisible"
                >
                    <el-row>
                        <el-form-item prop="Text" :label="'描述'">
                            <el-input type="textarea" :disabled="!(dialogStatusOfSymptom != 'detail')" :rows="3" placeholder="" v-model.trim="symptomTemp.Text" maxlength="500"></el-input>
                        </el-form-item>
                    </el-row>
                    <el-row v-show="dialogOptType == 3">
                        <el-col :span="24">
                            <el-form-item :label="'现场图片'" prop="Images">
                                <app-upload-file :readonly="!(dialogStatusOfSymptom != 'detail')" :fileType='1' :max='5' :value='symptomTemp.Images' :fileSize='10 * 1024 * 1024' :minFileSize='10 * 1024' @change='handleUpSolChange' :preview='true'></app-upload-file>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-show="dialogOptType == 3">
                        <el-col :span="24">
                            <el-form-item :label="'现场视频'" prop="VideoPath">
                                <app-upload-big-file :readonly="!(dialogStatusOfSymptom != 'detail')" accept='mp4,avi' :fileType='3' :max='videoOfSymptomLimit' :value='symptomTemp.Videos' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024' @change='handleVideoUpSolChange'></app-upload-big-file>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer">
                    <el-button size="mini" @click="() => dialogSymptomVisible = false">取消</el-button>
                    <el-button size="mini" v-show="dialogStatusOfSymptom != 'detail'" type="primary" @click="handleSave">确认</el-button>
                </div>
            </el-dialog>
            <!--审批历史-->
            <el-dialog
                v-el-drag-dialog
                class="dialog-mini"
                width="600px"
                title="审批记录"
                :visible.sync="dialogHistoryFormVisible"
                :close-on-click-modal='false'
                :append-to-body='true'
            >
                <el-timeline style="max-height: 600px; overflow-y: scroll;">
                    <el-timeline-item
                        v-for="(activity, idx) in FlowOperationhistories"
                        :key="idx"
                        :timestamp="getTimestamp(activity)" placement="top">
                        <el-card class="audit-detail">
                            <h4>{{ activity.Result }}</h4>
                            <p>{{ activity.Content }}</p>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </el-dialog>
            <div slot="footer">
                <slot name="other-button-area"></slot>
                <el-button size="mini" @click="handleClose">取消</el-button>
                <el-button size="mini" v-if="!getEditable()" type="primary" :loading="postLoading" @click="createData">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import DynamicList from '../repairOrder/dynamicList'
import ParamAdd from './paramAdd'
import * as reptofc from '@/api/repairToFailurecase'
import EmpSelector from '../empSelector'
import EquipmentArea from "../equipmentArea"

export default {
    name: 'repToFailurecase-create',
    components: {
        DynamicList,
        ParamAdd,
        EmpSelector,
        EquipmentArea,
    },
    directives: {
        // waves,
        elDragDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        //编辑还是新增(create: 新增; update: 编辑)
        dialogStatus: {
            type: String,
            default: 'create'
        },
        //创建案例库从维修单故障现象而来，所以创建的时候会有默认值
        detail: {
            type: Object,
            default: null
        },
    },
    watch: {
        visible(val) {
            this.dialogVisible = val
        },
        dialogVisible(val) {
            if(!val) {
                this.$emit('close')
            }
            if(val && this.dialogStatus == 'create'){
                this.getApprovalUser()
            }
      
        },
        detail(val) {
            if(val) {
                let tempDatas = JSON.parse(JSON.stringify(val))
                this.$set(tempDatas, 'EquipmentCategoryType', val.EquipmentCategoryType || 1)
                if(!tempDatas.EquipmentTypeSubtypeRelationIds) {
                    tempDatas.EquipmentTypeSubtypeRelationIds = {
                        EquipmentPropValueIds: []
                    }
                }

                this.temp = tempDatas

                if(this.temp.Solutions && this.temp.Solutions.length > 0){
                    this.temp.Solutions.forEach(o => {
                        if(!o.Images) {
                            this.$set(o, 'Images', [])
                        }

                        if(!o.Videos){
                            this.$set(o, 'Videos', [])
                        }
                    })
                }

                if(this.dialogStatus != 'create'){
                    this.getApprovalLogs()
                }    
            }
        },
        //添加原因分析、解决方案弹框关闭时
        dialogSymptomVisible(val) {
            if(!val) {
                this.resetSymptomTemp()
                this.$refs['symptomDataForm'].clearValidate()
            }
        }
    },
    computed: {
        auditRecord() { //审批记录条数
            return this.FlowOperationhistories.length
        },        
        optTitle() {
            let titleTemp = ''
            if(this.dialogOptType == 2) {
                titleTemp = '原因分析'
            }else if(this.dialogOptType == 3){
                titleTemp = '解决方法'
            }

            let optTitle = ''
            if(this.dialogStatusOfSymptom == 'create'){
                optTitle = '新增'
            }else if(this.dialogStatusOfSymptom == 'update'){
                optTitle = '编辑'
            }else if(this.dialogStatusOfSymptom == 'detail'){
                optTitle = ''
            }
            return optTitle + titleTemp
        }
    },
    created() {
        this.rules = this.initRules(this.rules)

        this.symptomRules = this.initRules(this.symptomRules)
        const validateText = (rule, value, callback) => {
            if (value.length <= 0) {
                let equText = this.opts[this.dialogOptType]
                callback(new Error(`${equText}不能为空`))
            } else {
                callback()
            }
        }
        if(!this.symptomRules['Text'])
            this.symptomRules['Text'] = []
        this.symptomRules.Text.push({validator: validateText, trigger: 'blur'})
    },
    data() {
        return {
            loading:false,
            postLoading: false,
            opts: {
                // 1: '故障现象',
                2: '原因分析',
                3: '解决方法'
            },
            equipmentCategoryTypes: [
                {type: 1, text: '加热炉'},
                {type: 2, text: '燃烧器'},
                {type: 3, text: '其他'},
            ],
            rules: {
                Symptom: { fieldName: '故障现象', rules: [{required: true}, {max: 500}]},
                Keywords: { fieldName: '关键字', rules: [{required: true}]},
                PointPositionParams: { fieldName: '影响点位参数', rules: [{required: true}]},
                AssCompAlarmParams: { fieldName: '关联综合报警参数', rules: [{required: true}]},
                Analysis: { fieldName: '原因分析', rules: [{required: true}]},
                Solutions: { fieldName: '解决方法', rules: [{required: true}]},
                MakerList: { fieldName: '审批人员', rules: [{required: true}]},
            },
            symptomRules: {

            },
            dialogVisible: this.visible,
            videoLimit: 3,
            videoOfSymptomLimit: 1,
            symptomTemp: {
                Text: '',//输入内容
                Images: [],
                Videos: [],
            },
            dialogHistoryFormVisible: false,
            FlowOperationhistories: [],//审批历史记录
            temp: {
                Id: '', //当前转换记录主键
                MakerList: [],
                RepairOrderId: '', //维修单主键
                RepairOrderSymptomId: '',//维修单对应故障案例表主键
                Symptom: '',//故障现象
                SymptomInputModel: 0, //故障现象输入模式；1：输入；2：选择
                // SymptomId: '',//选择故障现象的编号
                Solutions: [
                    // {
                    //     InputMode: 2,
                    //     Id: "63013ad4-9c6c-4d5f-9c18-643d7751520a",
                    //     Text: "检查右侧燃烧器喷头有无结焦、检查探头安装位置、检查探头镜片上是否清洁"
                    // },
                    // {
                    //     InputMode: 2,
                    //     Id: "b6b00efe-9df9-4eb2-8d61-fb5b8ff8a627",
                    //     Text: "检查右侧燃烧器喷头有无结焦、检查探头安装位置、检查探头镜片上是否清洁"
                    // },
                    // { 
                    //     InputMode: 1, 
                    //     Text: "12222"
                    //     ImageIdList: [
                    //         // "c72b39a5-bef2-4362-bd35-2f8f89be2e10",
                    //         // "48798a15-681a-440c-9487-6ec3438c8750"
                    //     ],
                    //     VideoIdList: [
                    //         // "55f861f6-7796-426f-b612-767f7518855a",
                    //         // "a440f2a5-9bbf-4908-836e-c04d1a2708ab"
                    //     ],
                    //  }
                ],
                Analysis: [
                    // {
                    //     InputMode: 2,
                    //     Id: "36fd55da-aaff-4cdc-baa0-bcd95b2dba44",
                    //     Text: "右侧火焰检测线路问题"
                    // },
                    // {
                    //     InputMode: 2,
                    //     Id: "394dca63-1f1e-4247-93fc-e443f61bf2c1",
                    //     Text: "右侧火焰检测器问题"
                    // },

                ],
                Keywords: [
                    // {Id: '', Text: ''}, //Id 存主键 GUID， Text 存 FaultKeyWordName；
                ],//关键字
                PointPositionParams: [
                    // {Id: '', Text: ''}, // "影响点位参数"，Id 存 PlcFunctionId，Text 存 PlcFunctionName
                ],//影响点位参数
                AssCompAlarmParams: [
                     // {Id: '', Text: ''}, // “关联综合报警参数”，Id 存 PlcFunctionId，Text 存 PlcFunctionName
                ],//关联报警参数
                Images: [
                    // {
                    //     "Path": "http://192.168.1.77:8082/Files/Images/201909/B02CC188-2614-491D-B47A-5A425F31E918.png",
                    //     "UrlThum": null,
                    //     "Id": "C72B39A5-BEF2-4362-BD35-2F8F89BE2E10",
                    //     "FileSize": 0,
                    //     "FileName": "111111.png",
                    //     "FileMd5value": "c809c18b826862fb5d602e8f4e36d248",
                    //     "FileId": "c72b39a5-bef2-4362-bd35-2f8f89be2e10",
                    //     "IsTemp": false
                    // },
                    // {
                    //     "Path": "http://192.168.1.77:8082/Files/Images/201909/10BBF286-EC08-426D-9101-B470B2DB0948.JPG",
                    //     "UrlThum": null,
                    //     "Id": "48798A15-681A-440C-9487-6EC3438C8750",
                    //     "FileSize": 0,
                    //     "FileName": "IMG_1932.JPG",
                    //     "FileMd5value": "5e2d1fa72fa062740e837feff3c796bf",
                    //     "FileId": "48798a15-681a-440c-9487-6ec3438c8750",
                    //     "IsTemp": false
                    // }
                ],
                Videos: [
                    // {
                    //     "Path": "http://192.168.1.77:8082/Files/Videos/201909/D0C5378C-CDEB-4CDA-824F-7610A1BA2266.mp4",
                    //     "UrlThum": null,
                    //     "Id": "55F861F6-7796-426F-B612-767F7518855A",
                    //     "FileSize": 0,
                    //     "FileName": "b.mp4",
                    //     "FileMd5value": "50d99695b2afd9870027105eaac1b339",
                    //     "FileId": "55f861f6-7796-426f-b612-767f7518855a",
                    //     "IsTemp": false
                    // },
                    // {
                    //     "Path": "http://192.168.1.77:8082/Files/Videos/201909/0C57195E-5974-4CD2-89F1-D44F0E491F3D.mp4",
                    //     "UrlThum": null,
                    //     "Id": "A440F2A5-9BBF-4908-836E-C04D1A2708AB",
                    //     "FileSize": 0,
                    //     "FileName": "c - 副本.mp4",
                    //     "FileMd5value": "ebc115f7ec424108387e27f31112d4ca",
                    //     "FileId": "a440f2a5-9bbf-4908-836e-c04d1a2708ab",
                    //     "IsTemp": false
                    // }
                ],
                ImageIdList: [
                    // "c72b39a5-bef2-4362-bd35-2f8f89be2e10",
                    // "48798a15-681a-440c-9487-6ec3438c8750"
                ],
                VideoIdList: [
                    // "55f861f6-7796-426f-b612-767f7518855a",
                    // "a440f2a5-9bbf-4908-836e-c04d1a2708ab"
                ],
                EquipmentTypeSubtypeRelationIds: {
                    // EquipmentPropValueIds: [
                    //     "2e28c131-889b-4830-b1e7-59f52b894ef5",
                    //     "edb60bf4-7fb1-4361-95e6-8dfaeffacd99",
                    //     "85a6e795-97cb-476f-961e-e6f2b4378829"
                    // ],
                    // EquipmentIdAndValueList: [
                    //     {
                    //         EquipmentPropId: "3e865080-ea46-411b-815a-18d236da4e32",
                    //         Value: "2019-09-02"
                    //     },
                    //     {
                    //         EquipmentPropId: "ff773880-ce1d-4e0a-9cf6-99890ff568ba",
                    //         Value: "2019-09-19"
                    //     },
                    //     {
                    //         EquipmentPropId: "6b8e1361-9061-46f4-b724-f8e708ebc54d",
                    //         Value: "2019-09-10"
                    //     },
                    //     {
                    //         EquipmentPropId: "cd95b34d-4177-4cd3-8ad9-7c8413256317",
                    //         Value: "2019-09-20"
                    //     },
                    //     {
                    //         EquipmentPropId: "3269c64c-2be1-4630-83aa-2fee72914092",
                    //         Value: ""
                    //     }
                    // ]
                },//选择（设置）动态参数值
                EquipmentCategoryType: 1,//维修内容
            },
            dialogSymptomVisible: false, //原因分析、解决方案弹框
            dialogStatusOfSymptom: '',//原因分析、解决方案弹框创建还是编辑create、update、detail
            dialogOptType: 0,//当前操作的是原因分析（2）还是解决方法（3）
            indexOfSymptoms: -1,//当前编辑的索引

        }
    },
    methods: {
        handleClose() {
            this.dialogVisible = false
        },
        createData() {
            let self = this;
            self.postLoading = true
            this.$refs['dataForm'].validate((valid) => {
                if(!valid) {
                    self.postLoading = false
                }
                if (valid) {
                    let formData = JSON.parse(JSON.stringify(self.temp))

                    if(formData.Keywords && formData.Keywords.length > 0){
                        formData.Keywords = formData.Keywords.map(k => k.Id)
                    }
                    if(formData.PointPositionParams && formData.PointPositionParams.length > 0){
                        formData.PointPositionParams = formData.PointPositionParams.map(f => f.Id)
                    }
                    if(formData.AssCompAlarmParams && formData.AssCompAlarmParams.length > 0){
                        formData.AssCompAlarmParams = formData.AssCompAlarmParams.map(f => f.Id)
                    }
                    if(formData.Videos && formData.Videos.length > 0){
                        formData.VideoIdList = formData.Videos.map(f => f.Id)
                        delete formData.Videos
                    }
                    if(formData.Images && formData.Images.length > 0){
                        formData.ImageIdList = formData.Images.map(f => f.Id)
                        delete formData.Images
                    }
                    if(formData.Solutions && formData.Solutions.length > 0){
                        formData.Solutions.forEach(element => {
                            element.VideoIdList = []
                            element.ImageIdList = []
                            if(element && element.Videos && element.Videos.length > 0) {
                                element.VideoIdList = element.Videos.map(f => f.Id)
                            }
                            delete element.Videos

                            if(element && element.Images && element.Images.length > 0) {
                                element.ImageIdList = element.Images.map(f => f.Id)
                            }
                            delete element.Images
                        });
                    }

                    if(formData.MakerList && formData.MakerList.length > 0){
                        formData.MakerList = formData.MakerList.map(u => u.EmployeeId)
                    }

                    let postRes = null
                    if(self.dialogStatus == 'create'){
                        delete formData.Id
                        postRes = reptofc.add(formData)
                    }else if(self.dialogStatus == 'update') {
                        postRes = reptofc.edit(formData)
                    }

                    if(postRes) {
                        postRes.then(res => {
                            self.postLoading = false
                            this.$notify({
                                title: '成功',
                                message: '创建成功',
                                type: 'success',
                                duration: 2000
                            })
                            this.handleClose()
                            this.$emit('saveSuccess')
                        }).catch(err => {
                            self.postLoading = false
                        })
                    }
                }
            })
        },
        getEditable() {
            return !(this.dialogStatus == 'create' || this.dialogStatus == 'update')
        },
        handleChoiced(datas, optType) {
            if(optType == 1){
                this.temp.Keywords = datas
                this.$refs["dataForm"].validateField("Keywords");
            }else if(optType == 2){
                this.temp.PointPositionParams = datas
                this.$refs["dataForm"].validateField("PointPositionParams");
            }else if(optType == 3){
                this.temp.AssCompAlarmParams = datas
                this.$refs["dataForm"].validateField("AssCompAlarmParams");
            }
        },
        handleDynamicListChanged(obj) {
            this.temp.EquipmentTypeSubtypeRelationIds.EquipmentPropValueIds = obj
        },
        handleChangeServiceUser(emps) {
            this.temp.MakerList = emps
            this.$refs["dataForm"].validateField("MakerList");
        },
        getApprovalUser() {
            this.loading=true;
            reptofc.getApprovalUser().then(res => {
                this.loading=false;
                this.temp.MakerList = res
            }).catch(err => {
                this.loading=false;
            })
        },
        handleRemove(idx, optType) {
            this.$confirm('是否确认删除当前行?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if(optType == 2){
                    this.temp.Analysis.splice(idx, 1)
                }else if(optType == 3){
                    this.temp.Solutions.splice(idx, 1)
                }
            })
        },
        handleUpSolChange(imgs) {
            this.symptomTemp.Images = imgs
        },
        handleVideoUpSolChange(videos) {
            if(videos.length > this.videoOfSymptomLimit){
                videos = videos.slice(0, this.videoOfSymptomLimit)
            }
            this.symptomTemp.Videos = videos
        },
        handleUpChange(imgs) {
            this.temp.Images = imgs
        },        
        handleVideoUpChange(videos) {
            if(videos.length > this.videoLimit){
                videos = videos.slice(0, this.videoLimit)
            }
            this.temp.Videos = videos
        },
        resetSymptomTemp() {
            this.symptomTemp = {
                Text: '',
                Images: [],
                Videos: [],
            }
        },
        handleSave() {
            let self = this;
            this.$refs['symptomDataForm'].validate((valid) => {
                if (valid) {
                    let result = {
                        Id: '',
                        InputMode: 1,
                        Text: this.symptomTemp.Text
                    }
                    if(this.dialogOptType == 3){
                        result.Videos = this.symptomTemp.Videos
                        result.Images = this.symptomTemp.Images
                    }

                    let exists = this.dialogOptType == 2 ? this.temp.Analysis : this.temp.Solutions
                    if(this.dialogStatusOfSymptom == 'create') {
                        exists = exists.find(o => o.Text == result.Text)
                        if(exists) {
                            this.$message({
                                message: `${this.opts[this.dialogOptType]}${result.Text}已经存在，请勿重复添加`,
                                type: 'error'
                            })
                            return false
                        }

                        if(this.dialogOptType == 2){
                            this.temp.Analysis.push(result)
                        }else if(this.dialogOptType == 3){
                            this.temp.Solutions.push(result)
                        }
                    }else if(this.dialogStatusOfSymptom == 'update') {
                        exists = exists.filter((o, idx) => {
                            if(idx == this.indexOfSymptoms){
                                return false
                            }
                            return o.Text == result.Text
                        })
                        if(exists.length > 0) {
                            this.$message({
                                message: `${this.opts[this.dialogOptType]}${result.Text}已经存在，请勿重复添加`,
                                type: 'error'
                            })
                            return false
                        }

                        if(this.dialogOptType == 2){
                            this.temp.Analysis.splice(this.indexOfSymptoms, 1, result)
                        }else if(this.dialogOptType == 3){
                            this.temp.Solutions.splice(this.indexOfSymptoms, 1, result)
                        }
                    }

                    this.dialogSymptomVisible = false
                }
            })
        },
        handleAddRow(optType) {
            this.dialogSymptomVisible = true
            this.dialogStatusOfSymptom = 'create'
            this.dialogOptType = optType
        },
        handleUpdate(idx, optType, dialogStatus = 'update') {
            this.dialogSymptomVisible = true
            this.dialogStatusOfSymptom = dialogStatus
            this.dialogOptType = optType
            this.indexOfSymptoms = idx
            if(this.dialogOptType == 2) {
                this.symptomTemp = JSON.parse(JSON.stringify(this.temp.Analysis[this.indexOfSymptoms]))
            }else if(this.dialogOptType == 3){
                this.symptomTemp = JSON.parse(JSON.stringify(this.temp.Solutions[this.indexOfSymptoms]))
            }
        },
        getApprovalLogs() {
            if(this.temp.Id){
                this.loading=true;
                reptofc.getApprovalLogs({id: this.temp.Id}).then(res => {
                    this.loading=false;
                    this.FlowOperationhistories = res
                }).catch(err => {
                    this.loading=false;
                })
            }
        },
        getTimestamp(activity) {
            if(activity){
                let f = this.$options.filters['dateFilter']
                return activity.CreateUserName + ' ' + f(activity.CreateDateTime, 'YYYY-MM-DD HH:mm:ss')
            }
            return ''
        }
    },
}
</script>

<style scoped>
.step-area{
    margin-bottom: 20px;
}

.step-title{
    display: flex;
}

.step-title h3{
    flex: 1;
}

.step-title, .step-title h3{
    color: 0078ff;
}

.step-title h3, .group-title h3{
    position: relative;
    padding-left: 10px;
    height: 18px;
    margin-top: 0;
}


.step-title h3::before{
    content: ' ';
    display: inline-block;
    width: 4px;
    height: 18px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    background-color: #409eff;
}

</style>