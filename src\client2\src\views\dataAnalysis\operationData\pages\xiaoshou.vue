<template>
  <div class="block-content" v-loading="loading">
    <!-- <blockTitle :obj='obj'>
            <div slot='rht'>
                <el-radio-group v-model="period" size="mini" @change='getCustomersDetails'>
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </div>
        </blockTitle> -->

    <el-row style="width: 100%; margin-top: 19px; margin-bottom: 5px">
      <el-col :span="12" style="display: flex">
        <span
          style="
            margin-left: 30px;
            width: 5px;
            height: 20px;
            background: #3d73dd;
          "
        ></span>
        <span
          style="
            margin-top: 2px;
            font-size: 16px;
            color: #1D2129;
            margin-left: 11px;
            font-weight: bold;
          "
          >销售</span
        >
      </el-col>
      <el-col :span="12" style="display: flex; justify-content: flex-end">
        <el-radio-group
          v-model="period"
          size="mini"
          @change="getCustomersDetails"
          style="margin-right: 15px"
        >
          <el-radio-button
            v-for="item in dateTypeEnum"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-radio-button
          >
        </el-radio-group>
      </el-col>
    </el-row>

    <div class="inner-content">
      <el-row style="width: 100%; height: 1px; background: #dcdfe6"></el-row>

      <div class="top" style="margin-top: 30px">
        <el-row>
          <el-col :span="3" style="display: flex; margin-left: 30px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">新增客户</span>
              <span class="top-value">
                {{ formData.MonthCreated }}
              </span>
            </div>
          </el-col>
          <el-col :span="3" style="display: flex; margin-left: 10px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">拜访客户</span>
              <span class="top-value">
                {{ formData.MonthCustomerVisit }}
              </span>
            </div>
          </el-col>

          <el-col :span="3" style="display: flex; margin-left: 10px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">新增产值（数量）</span>
              <span class="top-value">
                {{ formData.CreatedBusinessOpportunityCount }}
              </span>
            </div>
          </el-col>
          <el-col :span="3" style="display: flex; margin-left: 10px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">新增产值（万元）</span>
              <span class="top-value">
                {{ formData.CreatedBusinessOpportunitySumAmount }}
              </span>
            </div>
          </el-col>

          <el-col :span="3" style="display: flex; margin-left: 10px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">新增订单</span>

              <span class="top-value">
                {{ formData.CreatedOrderCount }}
              </span>
            </div>
          </el-col>
          <el-col :span="3" style="display: flex; margin-left: 10px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">新增订单（万元）</span>

              <span class="top-value">
                {{ formData.CreatedOrderSumAmount }}
              </span>
            </div>
          </el-col>
          <el-col :span="3" style="display: flex; margin-left: 10px">
            <span class="shape"></span>
            <div class="top-item">
              <span class="top-title">结算订单（万元）</span>

              <span class="top-value">
                {{ formData.CreatedOrderSettledAmount }}
              </span>
            </div>
          </el-col>
        </el-row>
        <!-- <div class="flex-dire-column-wrapper">
                    <div class="chart-title">客户画像</div>
                    <div class="flex-1">
                        11
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">客户分布</div>
                    <div class="flex-1">
22
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">客户分布</div>
                    <div class="flex-1">
33
                    </div>
                </div> -->
      </div>

      <div class="bottom">
        <el-row>
          <el-col :span="12" class="flex-dire-column-wrapper">
            <span
              style="
                color: #333333;
                font-size: 14px;
                font-weight: bold;
                margin-left: 31px;
              "
              >客户画像</span
            >
            <div>
              <noData
                v-if="
                  !wordCloudOption1.series ||
                  wordCloudOption1.series[0].data.length == 0
                "
              ></noData>
              <app-charts-basic
                v-else
                :width="chartsCloudWidth"
                :height="chartsCloudHeight"
                ref="pieEchart1"
                :option="wordCloudOption1"
              ></app-charts-basic>
            </div>
          </el-col>
          <el-col :span="12" class="flex-dire-column-wrapper">
            <span
              style="
                color: #333333;
                font-size: 14px;
                font-weight: bold;
                margin-left: 31px;
              "
              >客户分布</span
            >
            <div>
              <noData
                v-if="
                  !barEchartOption1.series ||
                  barEchartOption1.series[0].data.length == 0
                "
              ></noData>
              <app-charts-basic
                v-else
                :width="chartsWidth"
                :height="chartsHeight"
                :xAxisLabelTooltip="true"
                :xAxisLabelLimit="5"
                :rotate="25"
                ref="pieEchart2"
                :option="barEchartOption1"
              ></app-charts-basic>
            </div>
          </el-col>
        </el-row>
        <el-row style="margin-top: 15px; margin-bottom: 25px;">
          <el-col :span="12" class="flex-dire-column-wrapper">
            <span
              style="
                color: #333333;
                font-size: 14px;
                font-weight: bold;
                margin-left: 31px;
              "
              >拜访计划</span
            >
            <div>
              <div id="visit-plan-chart"></div>
              <!-- <noData
                v-if="
                  !lineEchartOption1.series ||
                  lineEchartOption1.series[0].data.length == 0
                "
              ></noData> -->
              <!-- <app-charts-basic
                v-else
                :width="chartsWidth"
                :height="chartsHeight"
                :xAxisLabelTooltip="true"
                :xAxisLabelLimit="5"
                :rotate="25"
                ref="pieEchart2"
                :option="lineEchartOption1"
              ></app-charts-basic> -->
            </div>
          </el-col>
          <el-col :span="12" class="flex-dire-column-wrapper">
            <span
              style="
                color: #333333;
                font-size: 14px;
                font-weight: bold;
                margin-left: 31px;
              "
              >拜访客户</span
            >
            <div>
              <noData
                v-if="
                  !lineEchartOption2.series ||
                  lineEchartOption2.series[0].data.length == 0
                "
              ></noData>
              <app-charts-basic
                v-else
                :width="chartsWidth"
                :height="chartsHeight"
                :xAxisLabelTooltip="true"
                :xAxisLabelLimit="5"
                :rotate="25"
                ref="pieEchart2"
                :option="lineEchartOption2"
              ></app-charts-basic>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import blockTitle from "../blockTitle";
import * as odc from "@/api/operatingDataCenter";
import {
  wordCloudOption,
  barOption,
  colors,
  dateTypeEnum1,
  dateTypeEnum2,
} from "../vars";
import mixins from "../mixins";
var echarts = require('echarts');

export default {
  name: "xiaoshou",
  mixins: [mixins],
  components: {
    noData,
    blockTitle,
  },
  props: {
    obj: {
      type: Object,
      required: true,
    },
  },
  mounted() {
    this.getCustomersDetails();
  },
  computed: {
    dateTypeEnum() {
      let list = dateTypeEnum2
        .concat(dateTypeEnum1)
        .filter((s) => s.value != 5);
      return list;
    },
  },
  data() {
    return {
      period: 3,
      loading: false,
      chartsCloudHeight: "230px",
      chartsHeight: "260px",
      chartsCloudWidth: "640px",
      chartsWidth: "640px",
      visitPlanChart:null,
      barEchartOption1: JSON.parse(JSON.stringify(barOption)),
      wordCloudOption1: JSON.parse(JSON.stringify(wordCloudOption)),
      lineEchartOption1: JSON.parse(JSON.stringify(barOption)),
      lineEchartOption2: JSON.parse(JSON.stringify(barOption)),
      formData: {
        MonthCreated: 0,
        MonthCustomerVisit: 0,
        CreatedBusinessOpportunityCount: 0,
        CreatedBusinessOpportunitySumAmount: 0,
        CreatedOrderCount: 0,
        CreatedOrderSumAmount: 0,
        CreatedOrderSettledAmount: 0,
      },
    };
  },
  methods: {
    getCustomersDetails() {
      let that = this;
      that.loading = true;

      odc
        .getCustomersDetails({ Period: that.period })
        .then((res) => {
          that.loading = false;
          that.formData = {
            MonthCreated: res.MonthCreated,
            MonthCustomerVisit: res.MonthCustomerVisit,
            CreatedBusinessOpportunityCount:
              res.CreatedBusinessOpportunityCount,
            CreatedBusinessOpportunitySumAmount:
              res.CreatedBusinessOpportunitySumAmount,
            CreatedOrderCount: res.CreatedOrderCount,
            CreatedOrderSumAmount: res.CreatedOrderSumAmount,
            CreatedOrderSettledAmount: res.CreatedOrderSettledAmount,
          };

          //周计划
          that.barEchartOption1 = that._initBarChartDatas(
            res.CustomerDistribution || []
          );
          //客户画像
          that.wordCloudOption1 = that._initWordCloudChartDatas(
            res.CustomerPortrait || []
          );
        })
        .catch((err) => {
          that.loading = false;
        });

      odc
        .getVisitRecordManagementChartData({ Period: that.period })
        .then((res) => {
          that.loading = false;
          //拜访计划
          // that.lineEchartOption1 = 
          that._initVisitPlanChartDatas(
            res.VisitingPlanChart || []
          );
          //拜访客户
          that.lineEchartOption2 = that._initVisitCustomerChartDatas(
            res.VisitingClientChart || []
          );
        })
        .catch((err) => {
          that.loading = false;
        });
    },
    _initWordCloudChartDatas(list) {
      if (!list) {
        list = [];
      }

      let chartDatas = JSON.parse(JSON.stringify(list || []));
      let targetOption = {};
      if (chartDatas && chartDatas.length > 0) {
        targetOption = {
          tooltip: {
            show: false,
          },
          series: [
            {
              // textStyle: {
              //     emphasis: {
              //         shadowBlur: 10,
              //         shadowColor: 'rgba(51,51,51,.3)'
              //     }
              // },
              data: chartDatas.map((s) => {
                let colorTemp =
                  "rgb(" +
                  [
                    Math.round(Math.random() * 160),
                    Math.round(Math.random() * 160),
                    Math.round(Math.random() * 160),
                  ].join(",") +
                  ")";

                return {
                  name: s.Label,
                  value: s.Value,
                  textStyle: {
                    normal: {
                      color: colorTemp,
                    },
                    emphasis: {
                      color: colorTemp,
                    },
                  },
                };
              }),
            },
          ],
        };
      }

      targetOption = _.merge(
        {},
        JSON.parse(JSON.stringify(wordCloudOption)),
        targetOption
      );

      return targetOption;
    },
    _initBarChartDatas(list) {
      if (!list) {
        list = [];
      }

      let chartDatas = JSON.parse(JSON.stringify(list || []));
      let targetOption = {};
      if (chartDatas && chartDatas.length > 0) {
        targetOption = {
          grid: {
            left: 40,
          },
          xAxis: {
            data: chartDatas.map((s) => s.Label),
          },
          yAxis: {
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
                color: "#E9EAEF", // 颜色
              },
            },
          },
          series: [
            {
              data: chartDatas.map((s) => s.Value),
            },
          ],
        };
      }

      targetOption = _.merge(
        {},
        JSON.parse(JSON.stringify(barOption)),
        targetOption
      );

      return targetOption;
    },

    _initVisitPlanChartDatas(list) {

      this.visitPlanChart = echarts.init(document.getElementById("visit-plan-chart"), null, {
         width: 640,
         height: 260,
      });

      if (!list) {
        list = [];
      }

      let chartDatas = JSON.parse(JSON.stringify(list || []));
      let targetOption = {};

      if (chartDatas && chartDatas.length > 0) {

        const labelOption = {
          show: true,
          position: 'outside',
        };

        const haveVisitData = chartDatas.map((v) => v.HaveVisit);
        const notVisitData = chartDatas.map((v) => v.NotVisit);
        const seriesData = [
          {
            name:'已完成',
            type: "bar",
            barWidth: 20,
            label: labelOption,
            data: haveVisitData,
          },
          {
            name:'进行中',
            type: "bar",
            barWidth: 20,
            label: labelOption,
            data: notVisitData,
          },
        ];

        targetOption = {
          color: ["#3D73DD", "#00FF7F"],

          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },

          grid: {
            top: 20,
            right: 20,
            bottom: 40,
            left: 40,
          },
          legend: {
            data: [],
            top: 10,
            show: false,
          },
          xAxis:  [{
              axisLine: {
                lineStyle: {
                  color: "#ccc", // 颜色
                },
              },
              axisLabel: {
                color: "black",
                interval: 0,
                rotate: 20,
                // 超过4个字显示省略号
                formatter: function (value) {
                  if (value.length > 5) {
                     return `${value.slice(0, 5)}...`;
                  }
                     return value;
                }
              },
              triggerEvent: true,
              type: "category",
              data: chartDatas.map((s) => s.Label),
              axisTick: {
                alignWithLabel: true,
              },
            }],
          yAxis: [
            {
              type: "value",
              splitLine: {
                show: true,
                lineStyle: {
                  type: "solid",
                  color: "#E9EAEF", // 颜色
                },
              }, 
              axisLine: {
                lineStyle: {
                  color: "#ccc", // 颜色
                },
              },
              axisLabel: {
                color: "black",
              },
            }
          ],

          series: seriesData,
        };
      }

       this.visitPlanChart.setOption(targetOption);
       this.extension(this.visitPlanChart)
    },

    _initVisitCustomerChartDatas(list) {
      if (!list) {
        list = [];
      }

      let chartDatas = JSON.parse(JSON.stringify(list || []));
      let targetOption = {};
      if (chartDatas && chartDatas.length > 0) {
        targetOption = {
          grid: {
            left: 40,
          },
          xAxis: {
            data: chartDatas.map((s) => s.Label),
          },
          yAxis: {
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
                color: "#E9EAEF", // 颜色
              },
            },
          },
          series: [
            {
              data: chartDatas.map((s) => s.Value),
            },
          ],
        };
      }

      targetOption = _.merge(
        {},
        JSON.parse(JSON.stringify(barOption)),
        targetOption
      );

      return targetOption;
    },

    //鼠标移动到y轴坐标，显示 label
    extension(chart) {
        // 注意这里，是以X轴显示内容过长为例，如果是y轴的话，需要把params.componentType == 'xAxis'改为yAxis
        // 判断是否创建过div框,如果创建过就不再创建了
        // 该div用来盛放文本显示内容的，方便对其悬浮位置进行处理
        var elementDiv = document.getElementById('extension')
        if (!elementDiv) {
            var div = document.createElement('div')
            div.setAttribute('id', 'extension')
            div.style.display = 'block'
            document.querySelector('html').appendChild(div)
        }
        chart.on('mouseover', function (params) {
            if (params.componentType == 'xAxis') {
                var elementDiv = document.querySelector('#extension')
                //设置悬浮文本的位置以及样式
                var elementStyle = 'position: absolute;z-index: 99999; color: #fff; font-size: 12px;padding: 5px;display: inline;border-radius: 4px;background-color: #303133;box-shadow: rgba(0, 0, 0, 0.3) 2px 2px 8px'
                    elementDiv.style.cssText = elementStyle
                    elementDiv.innerHTML = params.value
                    //没有全屏的话，直接使用document.querySelector('html')、event.pageX 即可
                    document.querySelector('html').onmousemove = function (event) {
                    var elementDiv = document.querySelector('#extension')
                    var xx = event.pageX + 15
                    // var xx = event.layerX + 15
                    var yy = event.pageY + 15
                    // var yy = event.layerY + 15
                    elementDiv.style.top = yy + 'px'
                    elementDiv.style.left = xx + 'px'
                }
            }
        })
        chart.on('mouseout', function (params) {
            //注意这里，我是以X轴显示内容过长为例，如果是y轴的话，需要改为yAxis
            if (params.componentType == 'xAxis') {
                var elementDiv = document.querySelector('#extension')
                elementDiv.style.cssText = 'display:none'
            }
        })
    },

  },
};
</script>

<style lang="scss" scoped>
@import "../common.scss";
.block-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .inner-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    .bottom {
      height: 330px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      margin-top: 33px;
      .flex-dire-column-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }
}

.chart-title {
  text-align: left;
  // height: 18px;
  // line-height: 18px;
  // padding: 0 10px;
}

.flex-1,
.flex-2 {
  box-sizing: border-box;
  margin: 5px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  > div:last-child {
    flex: 1;
  }
}

.flex-1 {
  flex: 1;
}

.text-content {
  flex: 1;
  font-weight: bold;
  display: flex;
  padding: 10px;
  align-items: center;
  word-break: break-all;
  white-space: normal;
  word-break: break-all;
}

.top-item {
  display: flex;
  flex-direction: column;
  margin-left: 11px;
}

.top-title {
  color: #a0a1a3;
  font-size: 14px;
}
.top-value {
  color: $text-primary;
  font-weight: bold;
  font-size: 30px;
  margin-top: 3px;
}
.shape {
  width: 10px;
  height: 50px;
  background: #e9eaef;
}
</style>