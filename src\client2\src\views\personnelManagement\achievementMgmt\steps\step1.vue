<template>
    <div class="step-wrapper">
        <div class="step-main">
            <el-form ref="formData" :model="detail" label-position="right" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="考核年份">
                            {{ detail.Year }} / {{ detail.HalfYearType | halfyearTypeFilter }}
                        </el-form-item>
                    </el-col>                    
                    <el-col :span="12">
                        <el-form-item label="考核类型" prop="AppraiseType">
                            {{ detail.AppraiseType | appraiseTypeFilter }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="年度目标">
                            {{ detail.YearGoals }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="相关附件">
                            <app-uploader :readonly="true" accept="all" :fileType="3" :max="10000"
                                :value="detail.YearAttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"
                                ref="appuploader"></app-uploader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="部门名称">
                            {{ detail.DepartmentName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="终审人">
                            <!-- <emp-selector :readonly="true" key="ccusers1" :showType="2" :multiple="true" :list="detail.PrincipalEmployeeList"></emp-selector> -->
                            {{ detail.FinalEmployeeList | namesFilter }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="考核主管">
                            <!-- <emp-selector :readonly="true" key="ccusers1" :showType="2" :multiple="true" :list="detail.PrincipalEmployeeList"></emp-selector> -->
                            {{ detail.PrincipalEmployeeList | namesFilter }}
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="24">
                        <el-form-item label="团队目标">
                            {{ detail.TeamGoals }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="相关附件">
                            <template v-if="detail.AttachmentList && detail.AttachmentList.length > 0">
                                <app-uploader :readonly="true" :value="detail.AttachmentList" ref="appuploader"></app-uploader>
                            </template>
                            <template v-else>无</template>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="24">
                        <el-form-item label="考核人">
                            <!-- <emp-selector :readonly="true" key="ccusers1" :showType="2" :multiple="true" :list="detail.ObjectEmployeeList"></emp-selector> -->
                            {{ detail.ObjectEmployeeList | namesFilter }}
                        </el-form-item>
                    </el-col>
                    
                </el-row>
            </el-form>
        </div>
        <!-- <div class="btn-wrapper" v-if="editable">
            <el-button type="primary" style="width: 180px;" :loading="loading" :disabled='loading' @click="handleSave">下一步，个人绩效承诺采集</el-button>
        </div> -->
    </div>
</template>


<script>
import empSelector from '@/views/common/empSelector'
import { appraiseTypeEnum, publicPeriodSetEnum, autoEndTypeEnum, yearTypeEnum } from "../enum"
import * as ach from "@/api/personnelManagement/achievementMgmt"
export default {
    name: "step1",
    props: {
        //跟进 才 可编辑；详细不可以
        isFollow: {
            type: Boolean,
            default: false
        },
        detail: {
            type: Object,
            required: true
        }
    },
    computed: {
        editable() {
            return this.isFollow && this.detail.ProgressStatus == 1
        }
    },
    created() {
    },
    mounted() {
    },
    components: {
        empSelector,
    },
    filters: {
        halfyearTypeFilter(val) {
            let obj = yearTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        appraiseTypeFilter(val) {
            let obj = appraiseTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        publicPeriodSetFilter(val) {
            let obj = publicPeriodSetEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        autoEndTypeFilter(val) {
            let obj = autoEndTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        namesFilter(list) {
            if(list && list.length > 0) {
                return list.map(s => s.Name).join('、')
            }
            return ''
        },
    },
    data() {
        return {
            appraiseTypeEnum,
            publicPeriodSetEnum,
            autoEndTypeEnum,
            loading: false,
            
        };
    },
    methods: {
        handleSave() {
            this.$confirm(`确定开始采集个人绩效吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true
                ach.startPersonalGatherAndNext([this.detail.Id]).then(res => {
                    this.loading = false
                    this.$emit('forwardSuccess')
                }).catch(err => {
                    this.loading = false
                })
            })
        },
    },
};
</script>

<style lang="scss" scoped>
@import "./step.css";
</style>