<template>
    <!-- <el-col v-if="ctrlObj.type == 7">
        <button type="submit" v-bind='attrsOfCtrl'>{{ ctrlLabel }}</button>
    </el-col> -->

    <div>
        <!-- input -->
        <el-col :span="12" v-if="ctrlObj.type == 3">
            <el-form-item :label="ctrlLabel">
                {{ ctrlObj.value }}
                <el-input v-model="ctrlObj.value" v-bind='attrsOfCtrl' @change='handleChange(ctrlObj.value)'></el-input>    
            </el-form-item>
        </el-col>
        <!-- select -->
        <el-col :span="12" v-else-if="ctrlObj.type == 5">
            <el-form-item :label="ctrlLabel">
                <el-select v-model="ctrlObj.value" :multiple='isMultiple' v-bind='attrsOfCtrl' @change='handleChange(ctrlObj.value)' clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
        </el-col>
        <!-- Radio -->
        <el-col :span="12" v-else-if="ctrlObj.type == 6">
            <el-form-item :label="ctrlLabel">
                <el-radio-group v-model="ctrlObj.value" size="small" @change='handleChange(ctrlObj.value)'>
                    <el-radio-button v-for="(o, idx) in options" :key="idx" :label="o.value">{{ o.label }}</el-radio-button>
                </el-radio-group>
            </el-form-item>
        </el-col>
        <el-col :span="12" v-else-if="ctrlObj.type == 7">
            <el-form-item :label="''">
                <el-button type="primary" @click="handleClick">{{ ctrlLabel }}</el-button>
            </el-form-item>
        </el-col>
        <el-col :span="24" v-else-if="ctrlObj.type == 10">
            <el-table :data="ctrlObj.value" border style="width: 100%">
                <el-table-column v-for="(col, idx) in columnList" :key="idx" :prop="col" :label="col"></el-table-column>
                <el-table-column fixed="right" label="操作" v-show="tableRowBtns && tableRowBtns.length > 0">
                    <template slot-scope="scope">
                        <el-button v-for="(b, idx) in tableRowBtns" :key="idx" @click="handleRowButtonClick(b.id, scope.row, b.code)" type="text">{{ b.label }}</el-button>
                        <!-- <el-button @click="handleDelete(scope.row)" type="text">删除</el-button> -->
                    </template>
                </el-table-column>
            </el-table>
        </el-col>
        <!-- 自定义选择器 -->
        <el-col :span="12" v-else-if="ctrlObj.type == 10002">
            <el-form-item :label="ctrlLabel">
                <selector v-model="ctrlObj.value" :conf='conf' v-bind="attrsOfCtrl" @change='handleChange(ctrlObj.value)'></selector>
            </el-form-item>
        </el-col>

        <el-col :span="12" v-else>
            <!-- 未定义控件 -->
        </el-col>
    </div>
</template>

<script>
import mixin from '../dynamicFormCommon/mixins'
import Selector from './components/selector'
import * as cdc from '@/api/componentDatasourceConfiguration'
import { JsonHubProtocol } from '@aspnet/signalr'
import * as dfApi from '@/api/dynamicForm'

export default {
    name: 'dynamic-ctrl',
    mixins: [mixin],
    model: {
        prop: 'value',
        event: 'change'
    },
    components: {
        Selector,
    },
    props: {
        value: {
            type: [Number, String, Boolean, Array, Object],
        },
        ctrl: {
            type: Object,
            required: true
        },
    },
    watch: {
        ctrl: {
            handler(val, oldVal) { 
                this.ctrlObj = this.initCtrl(val)
            },
            deep: true,
            // immediate: true,
        },
        // value: {
        //     handler(val, oldVal) {
        //     },
        //     deep: true,
        //     immediate: true
        // }
    },
    mounted() {
        

    },
    computed: {
        // ctrlWidth() {
        //     if(this.ctrlObj && this.ctrlObj.attrs) {
        //         return this.ctrlObj.attrs.find(s => s.attrName == 'ctrlWidth').value - 0 || 24
        //     }
        //     return 0
        // },
        ctrlLabel() {
            let obj = this.ctrlObj.attrs.find(s => s.attrName == 'label')
            if(obj){
                return obj.value
            }
            return ''
        },
        tableRowBtns() {
            if(this.ctrlObj.type == 10) {
                let btns = this.ctrlObj.attrs.find(s => s.attrName == 'tableRowBtns222').value
                return btns
            }
            return null
        },
        columnList() {
            if(this.ctrlObj.type == 10) {
                let cols = this.ctrlObj.attrs.find(s => s.attrName == 'showColumns').value.split(',')
                return cols
            }
            return []
        },
        isMultiple() {
            //是否支持多选，不同的控件多选返回的“多选”属性可能不同
            if(this.ctrlObj.type == 5) {
                let mulAttr = this.ctrlObj.attrs.find(s => s.attrName == 'isMultiple')
                if(mulAttr && mulAttr.value && mulAttr.value.length > 0) {
                    return mulAttr.value[0] == 'true'
                }
            }
            return false
        },
        attrsOfCtrl() {
            return this.getAttrs(this.ctrlObj)
        },
        options() {
            return this.ctrlObj.attrs.find(s => s.attrName == 'dataSource').options || []
        },
        conf() {
            if(this.ctrlObj.type == '10002') {
                return this.ctrlObj.attrs.find(s => s.attrName == 'dataSourceOfSelector').extendConf
            }
            return ''
        },
    },
    data() {
        return {
            
            ctrlObj: this.initCtrl(this.ctrl)

            
        }
    },
    methods: {
        initCtrl(ctrl) {
            let result = ctrl
            this.getDataSource(result)
            return result
        },
        handleChange(newval) {
            this.$emit('change', newval)
        },
        getDataSource(ctrl) {
            let c = ctrl
            if(c.type == 5 || c.type == 6) {
                let datasourceAttr = c.attrs.find(a => a.attrName == 'dataSource')
                if(datasourceAttr && datasourceAttr.value) {
                    
                    let sourceId = datasourceAttr.value

                    //需要更替接口
                    cdc.detail({configurationId: sourceId}).then(res => {
                        if(res.DataSourceType == 1) { //手动创建的下拉选项
                            datasourceAttr.options = JSON.parse(res.ConfigurationValues)
                        }else{ //需要通过接口获取
                            //需要补充接口（待完善）
                            let api = res.ConfigurationValues
                            // request({
                            //     url: api,
                            //     method: 'get'
                            // }).then(res => {
                            // })
                        }
                    })
                }
            }
        },
        handleClick() {
            let df = dfApi
            if(this.ctrlObj.type == 7) { //按钮事件
                let code = this.ctrlObj.attrs.find(s => s.attrName == 'code').value
                eval(code)
            }
        },
        handleRowButtonClick(btnId, row, code) {
            let df = dfApi
            eval(code)
        },
    },
}
</script>

<style scoped>
.el-select{
    width: 100%;
}
</style>

