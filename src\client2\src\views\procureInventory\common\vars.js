export const vars = {
  // 采购订单状态 1.未发货 2.已发货 3.已签收
  PurchaseOrderStateTypes: [
    { value: 1, label: "未发货", color: "#FF0000", bgColor: "rgba(255, 0, 0, 0.2)" }, // 红色
    { value: 2, label: "已发货", color: "#70B603", bgColor: "rgba(112, 182, 3, 0.2)" }, // 绿色
    { value: 3, label: "已签收", color: "#409EFF", bgColor: "rgba(64, 158, 255, 0.2)" }, // 蓝色
  ],
  // 签收方式 1.签收 2.退回
  SignTypeTypes: [
    { value: 1, label: "签收", color: "#FF0000", bgColor: "rgba(255, 0, 0, 0.2)" }, // 红色
    { value: 2, label: "退回", color: "#70B603", bgColor: "rgba(112, 182, 3, 0.2)" }, // 绿色
  ],

  materialStatus: [
    { value: 1, label: "审核中", color: "#70B603", bgColor: "rgba(112, 182, 3, 0.2)" }, // 绿色
    { value: 2, label: "已审核", color: "#409EFF", bgColor: "rgba(64, 158, 255, 0.2)" }, // 蓝色
    { value: 3, label: "已作废", color: "#F53F3F", bgColor: "rgba(255, 0, 0, 0.2)" }, // 红色
  ],
  ProcessMode: [
    { value: 1, label: "维修更换" },
    { value: 2, label: "隐患治理" },
    // {value: 1, label: '维修'},
    // {value: 2, label: '拆除'},
    // {value: 4, label: '更换'},
    // {value: 5, label: '新增'},
  ],
  resultType: [
    // {value: 1, label: '待检测', color: 'red'},
    { value: 2, label: "检测通过", color: "red" },
    { value: 3, label: "检测不通过", color: "red" },
    // {value: 4, label: '已退库', color: 'red'},
  ],

  IsWarrantys: [
    { value: true, label: "是", color: "red" },
    { value: false, label: "否", color: "red" },
  ],

  TestingState: [
    { value: 1, label: "待检测", color: "#70B603", bgColor: "rgba(112, 182, 3, 0.2)" }, // 红色
    { value: 2, label: "检测通过", color: "#409EFF", bgColor: "rgba(64, 158, 255, 0.2)" }, // 绿色
    { value: 3, label: "检测不通过", color: "#FF0000", bgColor: "rgba(255, 0, 0, 0.2)" }, // 蓝色
    { value: 4, label: "已退库", color: "rgba(253, 246, 236, 1)", bgColor: "orange" }, // 黄色
  ],

  // 采购申请单单据状态
  INVOICES_STATUS: [
    { value: "A", label: "创建" },
    { value: "B", label: "审核中" },
    { value: "C", label: "已审核" },
    { value: "D", label: "重新审核" },
    { value: "Z", label: "暂存" },
  ],
  // 采购申请单关闭状态
  CLOSE_STATUS: [
    { value: "A", label: "未关闭" },
    { value: "B", label: "已关闭" },
  ],
  // 采购申请单是否急件
  IS_URGENT: [
    { value: "1", label: "是" },
    { value: "2", label: "否" },
  ],
  // 采购申请单申请类型
  APPLICATION_TYPE: [
    { value: "ZYSQ", label: "直运" },
    { value: "Expense", label: "费用" },
    { value: "Material", label: "物料" },
    { value: "Property", label: "资产" },
  ],
  // 采购申请单验收方式
  CHECK_TYPE: [
    { value: "A", label: "金额验收" },
    { value: "Q", label: "数量验收" },
    { value: "R", label: "比例验收" },
  ],
  // 采购申请单业务终止
  BUSINESS_STATUS: [
    { value: "A", label: "正常" },
    { value: "B", label: "业务终止" },
  ],
};
