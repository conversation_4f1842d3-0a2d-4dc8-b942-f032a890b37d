<template>
    <div class="app-container">
      <div class="bg-white __dynamicTabContentWrapper">
        <!-- <page-title title="APP版本列表" :subTitle="['APP版本发布管理页面']"></page-title> -->

        <div class="__dynamicTabWrapper">
          <app-table
            ref="mainTable" :layoutMode='layoutMode' :isShowBtnsArea='false'
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :isShowAllColumn="isShowAllColumn"
            :loading="listLoading"
            @rowSelectionChanged="rowSelectionChanged"
            :isShowOpatColumn="rowBtns.length > 0"
            :startOfTable="startOfTable"
          >
            <template slot="Status" slot-scope="scope">
              <span :class="scope.row.Status | statusFilter">
                <el-tag
                  v-if="scope.row.Status"
                  type="success"
                >{{statusOptions.find(u =>u.key == scope.row.Status).display_name}}</el-tag>
                <el-tag
                  v-else
                  type="warning"
                >{{statusOptions.find(u =>u.key == scope.row.Status).display_name}}</el-tag>
              </span>
            </template>

            <!-- 表格查询条件区域 -->
            <template slot="conditionArea">
              <app-table-form :layoutMode='layoutMode'
                style="padding-top: 10px;"
                :label-width="'100px'"
                :items="tableSearchItems"
                @onSearch="handleFilter"
                @onReset="resetSearch"
              >
                <template slot="Version">
                    <el-input style="width: 100%;" 
                        placeholder="搜索版本名称..."
                        @clear='handleFilter'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                handleFilter()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.Version"
                    ></el-input>
                </template>
                <template slot="OrgID">
                  <!-- <el-input style="width: 100%;" v-model="listQuery.OrgID" placeholder="审批人"></el-input> -->
                </template>
                <template slot="Explain">
                  <el-input style="width: 100%;" v-model="listQuery.Explain"></el-input>
                </template>
                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                    <permission-btn moduleName="emp" v-on:btn-event="onBtnClicked"></permission-btn>
                </template>
              </app-table-form>
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <app-table-row-button
                v-if="rowBtnIsExists('btnEdit')"
                @click="handleUpdate(scope.row)"
                :type="1"
              ></app-table-row-button>
              <app-table-row-button             
                @click="handleUpdate(scope.row, 'detail')"
                :type="2"
              ></app-table-row-button>
              <app-table-row-button
                v-if="rowBtnIsExists('btnDel')"
                @click="handleDelete(scope.row)"
                :type="3"
              ></app-table-row-button>
              <!-- <el-button type="primary" v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)">编辑</el-button>
              <el-button type="primary" v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)">删除</el-button>-->
            </template>
          </app-table>
        </div>

        <pagination
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>

      <el-dialog
        v-el-drag-dialog
        class="dialog-mini"
        width="600px"
        :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
      >
        <el-form
          :rules="rules"
          ref="dataForm"
          :model="temp"
          label-position="right"
          label-width="110px"
          v-if="dialogFormVisible"
        >
          <el-row>
            <el-col>
              <el-form-item :label="'APP版本号'" prop="Version">
                <!-- <el-input v-model="temp.Version" :disabled="editable"></el-input> -->
                <el-input-number
                  v-model="temp.Version"
                  :min="1"
                  :max="999999999"
                  :disabled="editable"
                ></el-input-number>
              </el-form-item>
              <el-form-item :label="'APP版本名称'" prop="VersionName">
                <el-input v-model="temp.VersionName" :disabled="editable"></el-input>
              </el-form-item>
              <el-form-item :label="'功能说明'" prop="Explain">
                <el-input type="textarea" v-model="temp.Explain" :disabled="editable"></el-input>
              </el-form-item>
              <el-form-item :label="'升级方式'" prop="VersionState">
                <el-radio v-model="temp.VersionState" :label="1" :disabled="editable">可升级</el-radio>
                <el-radio v-model="temp.VersionState" :label="0" :disabled="editable">必须升级</el-radio>
              </el-form-item>

              <el-form-item :label="'发布日期'" prop="ReleaseTime">
                <el-date-picker
                  class="dat-ipt"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  v-model="temp.ReleaseTime"
                  type="date"
                  placeholder
                  :disabled="editable"
                ></el-date-picker>
              </el-form-item>

              <el-form-item :label="'状态'" prop="Status">
                <el-radio
                  v-model="temp.Status"
                  v-for="(item, idx) in statusOptions"
                  :key="idx"
                  :label="item.key"
                  :disabled="editable"
                >{{ item.display_name }}</el-radio>
              </el-form-item>

              <el-form-item :label="'apk安装文件'" prop="ResourceUrl">
                <!-- <app-uploader
                  :readonly="uploadShow"
                  accept="apk"
                  :fileType="3"
                  :max="1"
                  :value="temp.AttachmentList"
                  :fileSize="1024 * 1024 * 500"
                  :minFileSize="100 * 1024"
                  @change="handleFilesUpChange"
                ></app-uploader> -->

                <app-upload-big-file
                  v-if="!uploadShow"
                  :readonly="editable"
                  accept="apk"
                  :max="1"
                  :fileType="4"
                  :value="fileList"
                  :fileSize="1024 * 1024 * 30"
                  @change="handleUpChange"
                ></app-upload-big-file>
                <div class="aBox" v-else>
                  <span class="omit">{{fileList[0].FileName}}&emsp;</span>
                  <a :href="fileList[0].Path">下载11</a>
                </div>
              </el-form-item>

              <el-form-item :label="'备注'" prop="Description">
                <el-input type="textarea" v-model="temp.Description" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
          <el-button
            size="mini"
            v-if="!editable"
            type="primary"
            :loading="postLoading"
            @click="createData"
          >确认</el-button>
          <!-- <el-button size="mini" v-else type="primary" @click="createData">确认</el-button> -->
        </div>
      </el-dialog>
    </div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import * as apps from "@/api/apps";

// import waves from '@/directive/waves' // 水波纹指令
// import Sticky from '@/components/Sticky'

import elDragDialog from "@/directive/el-dragDialog";

import indexPageMixin from "@/mixins/indexPage";
import dayjs from "dayjs";

export default {
  name: "emp",
  components: {
    // Sticky,
  },
  directives: {
    // waves,
    elDragDialog,
  },
  mixins: [indexPageMixin],
  data() {
    return {
            layoutMode: 'simple',
      uploadShow: false,
      fileList: [], //附件信息[{Id: '', Path: ''}]
      multipleSelection: [],
      tableSearchItems: [
        { prop: "Version", label: "版本名称", mainCondition: true },
        { prop: "Explain", label: "功能说明" },
      ],
      tabColumns: [
        {
          attr: { prop: "Version", label: "版本号" },
        },
        {
          attr: { prop: "VersionName", label: "版本名称" },
        },
        {
          attr: { prop: "Explain", label: "功能说明" },
        },
        {
          attr: {
            prop: "ReleaseTime",
            label: "发布日期",
            formatter: this.formatterDate,
          },
        },
        {
          attr: { prop: "Status", label: "状态" },
          slot: true,
        },
        {
          attr: { prop: "Description", label: "备注" },
        },
      ],
      tabDatas: [],
      listLoading: false,
      postLoading: false,
      listQuery: {
        // 查询条件
        // PageIndex: 1,
        // PageSize: 20,
      },
      total: 0,
      textMap: {
        update: "编辑",
        create: "添加",
      },
      dialogFormVisible: false,
      rules: {
        Version: { fieldName: "APP版本号", rules: [{ required: true }] },
        VersionName: {
          fieldName: "APP版本名称",
          rules: [{ required: true }, { max: 100 }],
        },
        Explain: {
          fieldName: "功能说明",
          rules: [{ required: true }, { max: 200 }],
        },
        ReleaseTime: { fieldName: "发布日期", rules: [{ required: true }] },
        Description: { fieldName: "备注", rules: [{ max: 200 }] },

        // Version: [{ required: true, message: '请输入APP版本', trigger: 'blur' }],
        // VersionName: [{ required: true, message: 'APP版本名称', trigger: 'blur' }],
        // Explain: [{ required: true, message: '请输入说明', trigger: 'blur' }],
        // ReleaseTime: [{ required: true, message: '发布日期不能为空', trigger: 'blur' }],
        // EducationId: [{ required: true, message: '学历不能为空', trigger: 'blur' }],
        // MajorNam: [{ required: true, message: '专业不能为空', trigger: 'blur' }],
        //   Description: [{ required: true, message: '婚姻状况不能为空', trigger: 'blur' }],
      },
      temp: {
        AppVersionId: "",
        Version: "", //APP版本号
        VersionName: "", //VersionName
        Explain: "", //功能说明
        VersionState: 1, //
        ReleaseTime: "", //入职日期
        Status: true, //状态
        Description: "", //备注
        AttachmentList: [],
        // ResourceUrl: '',//附件
        // ResourceId: '',//附件编号
      },
      fileList: [], //头像信息[{Id: '', Path: ''}]
      statusOptions: [
        {
          key: true,
          display_name: "有效",
        },
        {
          key: false,
          display_name: "无效",
        },
      ],
      // options: [
      //     {
      //         id: 'folder',
      //         label: 'Normal Folder',
      //         children: [
      //             { id: 'disabled-checked', label: 'Checked', isDisabled: true },
      //             { id: 'disabled-unchecked', label: 'Unchecked', isDisabled: true },
      //             { id: 'item-1', label: 'Item'}
      //         ]
      //     },
      //     {
      //         id: 'disabled-folder',
      //         label: 'Disabled Folder',
      //         isDisabled: true,
      //         children: [
      //             { id: 'item-2', label: 'Item', },
      //             { id: 'item-3', label: 'Item', }
      //         ],
      //     }
      // ],
      // values: [ 'disabled-checked' ],
    };
  },
  created() {
    this.rules = this.initRules(this.rules);
    this.getList();
  },
  mounted() {
    console.log("hhh");
  },
  watch: {
    dialogFormVisible(newval, oldval) {
      if (!newval) {
        this.fileList = [];
      }
    },
  },
  methods: {
    handleFilesUpChange(files) {
      this.temp.AttachmentList = files;
    },
    downloadFile(d) {
      window.open(d);
    },
    resetTemp() {
      this.temp = {
        AppVersionId: "",
        Version: "", //APP版本号
        VersionName: "", //VersionName
        Explain: "", //功能说明
        VersionState: 1,
        ReleaseTime: "", //入职日期
        Status: true, //状态
        Description: "", //备注
        AttachmentList: [],
      };
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    onBtnClicked: function (domId) {
      this.uploadShow = false;
      switch (domId) {
        case "btnAdd":
          this.handleCreate();
          break;
        case "btnEdit":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行编辑",
              type: "error",
            });
            return;
          }
          this.handleUpdate(this.multipleSelection[0]);
          break;
        case "btnDetail":
          this.uploadShow = true;
          console.log(555, this.uploadShow);
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行查看",
              type: "error",
            });
            return;
          }
          console.log(6666, this.uploadShow);
          this.handleUpdate(this.multipleSelection[0], "detail");
          break;
        case "btnDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error",
            });
            return;
          }
          this.handleDelete(this.multipleSelection);

          break;
        default:
          break;
      }
    },
    getList() {
      this.listLoading = true;
      apps
        .getList(this.listQuery)
        .then((response) => {
          this.listLoading = false;
          this.tabDatas = response.Items;
          this.total = response.Total;
        })
        .catch((err) => {
          this.listLoading = false;
        });
    },
    getDetail() {
      // apps.detail({id: }).then(response => {
      // })
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCreate() {
      // 弹出添加框
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      // this.selectOrgs = null
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
      this.temp.ReleaseTime = dayjs().format("YYYY-MM-DD");
    },
    createData() {
      // 保存提交
      let self = this;


      let formData = JSON.parse(JSON.stringify(self.temp));
      if(formData.AttachmentList && formData.AttachmentList.length > 0) {
        formData.ResourceUrl = formData.AttachmentList[0].Path;
        formData.ResourceId = formData.AttachmentList[0].Id;
      }





      if (!formData.ResourceId) {
        this.$message({
          message: "请上传apk安装文件",
          type: "error",
        });
        return false;
      }

      self.postLoading = true;
      this.$refs["dataForm"].validate((valid) => {
        // if(this.dialogStatus == 'create' && !this.temp.ResourceUrl){
        //     this.$message({
        //         message: 'apk安装文件必能为空',
        //         type: 'error'
        //     })
        //     return
        // }

        if (!valid) {
          self.postLoading = false;
        }
        if (valid) {
          let res = null;
          if (self.dialogStatus == "create") {
            delete formData.AppVersionId;
            res = apps.add(formData);
          } else if (self.dialogStatus == "update") {
            res = apps.edit(formData);
          }

          if (res) {
            res
              .then((response) => {
                // 需要回填数据库生成的数据
                //   this.temp.id = response.result.id
                //   this.list.unshift(this.temp)
                self.postLoading = false;
                self.dialogFormVisible = false;
                self.$notify({
                  title: "成功",
                  message: "操作功",
                  type: "success",
                  duration: 2000,
                });
                // self.getOrgTree()
                this.getList();
              })
              .catch((err) => {
                self.postLoading = false;
              });
          }
        }
      });
    },
    //选择部门
    handleChange(value) {},
    handleUpdate(row, optType = "update") {
      // 弹出编辑框
      if (optType == "detail") this.uploadShow = true;
      else this.uploadShow = false;
      this.temp = Object.assign({}, row); // copy obj
      this.fileList = [];
      if (this.temp.Apk) {
        this.temp.AttachmentList = [this.temp.Apk]
        this.fileList = [
          {
            Id: this.temp.Apk.FileId,
            FileName: this.temp.Apk.FileName,
            Path: this.temp.Apk.Path,
          },
        ];
        console.log(this.fileList);
      }
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
      // this.selectOrgs = this.temp.ParentID
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    handleDelete(rows) {
      // 多行删除
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map((u) => u.AppVersionId);
      } else {
        ids.push(rows.AppVersionId);
      }
      this.$confirm("是否确认删除选中项?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        apps.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000,
          });
          // this.getOrgTree()
          this.getList();
        });
      });
    },
    handleUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.temp.ResourceUrl = imgs[0].Path;
        this.temp.ResourceId = imgs[0].Id;
      } else {
        this.temp.ResourceUrl = "";
        this.temp.ResourceId = "";
      }
    },
    formatterDate(row, column) {
      let f = this.$options.filters["dateFilter"];
      return f(row.ReleaseTime, "YYYY-MM-DD");
      // return f(row.ReleaseTime, 'YYYY-MM-DD HH:mm:ss')
    },
  },
};
</script>

<style  lang="scss" scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}
.aBox {
  width: 100%;
  height: 100%;
  span:first-child {
    max-width: 150px;
  }
  // span:last-child{
  //     font-size: 12px;
  //     color:#409EFF;
  // }
  a {
    font-size: 12px;
    color: #409eff;
  }
}
</style>
