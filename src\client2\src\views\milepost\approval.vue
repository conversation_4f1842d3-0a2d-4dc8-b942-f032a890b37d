<!-- 里程碑审批 -->
<template>
  <div class="newMilestone">
   <el-dialog v-el-drag-dialog class="dialog-mini" width="800px" title="立项申请流程" :visible.sync="dialogAuditFormVisible"
      :close-on-click-modal='false' :append-to-body='true'>
      <el-form ref="dataAuditForm" :model="auditTemp" label-position="right"
          label-width="100px">
          <el-row>
            <el-col :span="12">
                <el-form-item label="里程碑点名称" prop='Name'>
                    <el-input style="width: 100%;" v-model="auditTemp.Name" placeholder></el-input>
                </el-form-item>
            </el-col>
           <el-col :span="12">
                <el-form-item label="到期提醒" prop='ExpirationReminder'>
                   <el-select class="sel-ipt" v-model="auditTemp.ExpirationReminder" placeholder clearable>
                  <el-option
                    v-for="item in expirationReminders"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划开始时间" prop='PlannedStartTime'>
                <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss'
                      value-format='yyyy-MM-dd HH:mm:ss' v-model="auditTemp.PlannedStartTime"
                      type="datetime" placeholder=""></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划结束时间" prop='PlannedEndTime'>
                <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss'
                      value-format='yyyy-MM-dd HH:mm:ss' v-model="auditTemp.PlannedEndTime"
                      type="datetime" placeholder=""></el-date-picker>
              </el-form-item>
            </el-col>


            <el-col :span="24">
              <el-form-item label="交付件" prop='Deliverables' label-position="top">
                  <el-input
                    type="textarea"
                    :rows="3"
                    placeholder="请输入内容"
                    v-model="auditTemp.Deliverables">
                  </el-input>
              </el-form-item>
            </el-col>

        </el-row>
      </el-form>

      <div slot="footer">
          <el-button>取消</el-button>
          <el-button type='primary'>确认</el-button>
      </div>
  </el-dialog>
  </div>
</template>

<script>
import * as milestoneApi from "@/api/milestone";

export default {
  name: "approvalMilestone",
  components: {

  },
  directives: {

  },

  watch: {

  },
  data() {
    return {
      dialogAuditFormVisible:false,
      auditTemp:{
        Name:'',
        ExpirationReminder:'',
        PlannedStartTime:'',
        PlannedEndTime:'',
        Deliverables:'',
      },
     expirationReminders: [
        { value: 0, label: "A" },
        { value: 1, label: "B" },
        { value: 2, label: "C" },
      ],

    };
  },
  created() {


  },
  mounted() {

  },
  methods:{
    childShow(d){
      this.dialogAuditFormVisible=d;
    },
   rowSelectionChanged(){},
   getList(){}
  }

};
</script>

<style lang='scss' scoped>



</style>


