<template>
  <app-dialog
    :title="pageTitle"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :width="componentType==1?500:700"
  >
    <template slot="body">
      <el-row class="wrapper">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <el-form-item label="课程分类" prop="value" v-if="componentType==1">
            <treeselect key='id'
              class="treeselect-common"
              :append-to-body="true"
              :normalizer="unitNormalizer"
              v-model="formData.value" :default-expand-level="3"
              :options="treeDatas" :multiple="false" placeholder :show-count="true"
              :noResultsText='noResultsTextOfSelTree'
              :noOptionsText="noOptionsTextOfSelTree"
              zIndex='9999'
              @input="hadnleChangeCustomerUnitId"
              >
            </treeselect>
          </el-form-item>
          <template v-if="componentType==2">
            <el-form-item label="可见时段">
              <el-radio-group v-model="formData.valueType">
                <el-radio :label="1">不限制</el-radio>
                <el-radio :label="2">限制</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="formData.valueType==2" label="工作日限制时段" prop="value" class="inputLeft" :rules="{required: true, message:'工作日限制时段',trigger: ['change', 'blur']}">
              <el-time-picker format='HH:mm' value-format='HH:mm' is-range
                v-model="formData.value" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" placeholder="选择时间范围">
              </el-time-picker>(周末/法定节假日不限制)
            </el-form-item>
          </template>
        </el-form>
      </el-row>
    </template>
    <template slot="footer">
      <!-- 取消 -->
      <el-button @click="handleClose">取消</el-button>
      <!-- 确定 -->
      <el-button @click="handleSubmit" type="primary" :disabled='disabledBtn'>确定</el-button>
    </template>
  </app-dialog>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import * as trainApi from "@/api/informationCenter/train";
import * as trainsClassification from "@/api/informationCenter/trainsClassification";
export default {
  name: "batch-classify",
  components: {
  },
  props: {
    componentType: { // 1.调整分类 2.设置时段
      type: Number,
      require: true,
      default: 1
    },
    ids: {
      type: Array,
      default() {
        return []
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.formData = this.$options.data().formData; // 重置提交表单字段为初始值
          this.formData.ids = this.ids
          this.formData.type = this.componentType
          this.rules = this.initRules(this.rules);
          this.loadTreeData()
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      disabledBtn: false,
      rules: {
        value: { fieldName: "课程分类", rules: [{ required: true, trigger: 'change' }] },
      },
      labelWidth: "120px",
      formData: {
        type: 1,
        ids: [],
        value: null,
        valueType: 1,
      },
      treeDatas: [],
      unitNormalizer(node) {
          // treeselect定义字段
          return {
            id: node.Id,
            label: node.TrainsClassificationName,
            children: node.children
          }
      },
    };
  },
  computed: {
    pageTitle() {
      if(this.componentType == 1) {
        return '修改分类'
      }else if(this.componentType == 2) {
        return '设置时段'
      }
    },
  },
  methods: {
    hadnleChangeCustomerUnitId() {
      this.$refs.formData.validateField("value");
    },
    // 加载用户客户单位树列表
    loadTreeData() {
      trainsClassification.getListByCondition({})
          .then(res => {
              this.treeDatas = listToTreeSelect(res);
          })
          .catch(err => {
              this.treeLoading = false;
          });
    },
    // 弹窗提交
    handleSubmit() {
      let self = this;
      console.log(self.formData)
      self.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(self.formData));
          if (postData.type==2) {
            postData.value = postData.valueType == 1 ? '' : postData.value.toString()
          }
          delete postData.valueType
          self.disabledBtn = true
          trainApi.BatchEdit(postData).then(res => {
              self.$notify({
                  title: "提示",
                  message: "保存成功",
                  type: "success",
                  duration: 2000
              });
              self.disabledBtn = false
              self.$refs.appDialogRef.createData();
          }).catch(err => {
              self.disabledBtn = false
          });
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style lang="scss" scoped>
.wrapper {
  padding: 10px 0;
  padding-right: 30px;
  min-height: 100px;
}
</style>