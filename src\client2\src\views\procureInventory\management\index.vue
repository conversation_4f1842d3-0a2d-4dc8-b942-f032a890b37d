<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <v-tree @changeNode='changeTreeNode' :isAll='true' :isSubset='true'></v-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="tagBox">
                    <tags :items='types' v-model="LaborContractState">
                        <template v-for="t in types" :slot="t.value">
                            {{ t.label }}
                        </template>
                    </tags>
                </div>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :serial='false' :key="`abc_${LaborContractState}`" :isShowBtnsArea='true' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable="false" @sortChagned="handleSortChange" :layoutMode='layoutMode'>

                    
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch">
                                
                                 <!-- v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }'  -->
                                <template slot="MaterialEmployee">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入领料人"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.EmployeeName"
                                    ></el-input>
                                </template>

                                  <template slot="MaterialCode">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入物料编码"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.MaterialCode"
                                    ></el-input>
                                </template>


                                 <template slot="MaterialUser">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入用料人"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.EmployeeName"
                                    ></el-input>
                                </template>


                                 <template slot="ReportTime">
                                   <el-date-picker format='yyyy-MM-dd'
                                    style="width: 100%;" 
                                    value-format='yyyy-MM-dd'
                                    class="dat-ipt" 
                                    v-model="listQuery.ReportTime" 
                                    type="date" placeholder=""></el-date-picker>
                                </template>


                                <template slot="MaterialReturnDate">
                                   <el-date-picker format='yyyy-MM-dd'
                                    style="width: 100%;" 
                                    value-format='yyyy-MM-dd'
                                    class="dat-ipt" 
                                    v-model="listQuery.MaterialReturnDate" 
                                    type="date" placeholder=""></el-date-picker>
                                </template>


                                <template slot="ReturnedBy">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入退料人"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.EmployeeName"
                                    ></el-input>
                                </template>

                                <template slot="Date">
                                   <el-date-picker format='yyyy-MM-dd'
                                    style="width: 100%;" 
                                    value-format='yyyy-MM-dd'
                                    class="dat-ipt" 
                                    v-model="listQuery.Date" 
                                    type="date" placeholder=""></el-date-picker>
                                </template>
                                
                                <template slot="MaterialName">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入物料名称"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.MaterialName"
                                    ></el-input>
                                </template>

                                <template slot="ERPNo">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入单号"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.ERPNo"
                                    ></el-input>
                                </template>

                                
                                <template slot="ArrivalTime">
                                   <el-date-picker format='yyyy-MM-dd'
                                    style="width: 100%;" 
                                    value-format='yyyy-MM-dd'
                                    class="dat-ipt" 
                                    v-model="listQuery.ArrivalTime" 
                                    type="date" placeholder=""></el-date-picker>
                                </template>

                                <template slot="ServiceNo">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入服务单号"
                                        @clear='getList'
                                        clearable 
                                        v-model="listQuery.ServiceNo"
                                    ></el-input>
                                </template>

                                <template slot="ApplyDate">
                                   <el-date-picker format='yyyy-MM-dd'
                                    style="width: 100%;" 
                                    value-format='yyyy-MM-dd'
                                    class="dat-ipt" 
                                    v-model="listQuery.ApplyDate" 
                                    type="date" placeholder=""></el-date-picker>
                                </template>

                                <template slot="WriteOffDate">
                                   <el-date-picker format='yyyy-MM-dd'
                                    style="width: 100%;" 
                                    value-format='yyyy-MM-dd'
                                    class="dat-ipt" 
                                    v-model="listQuery.WriteOffDate" 
                                    type="date" placeholder=""></el-date-picker>
                                </template>

                                <template slot="RegionalId">
                                    <div class="el-input el-input--mini">
                                        <div
                                            style="
                                            display: flex;
                                            height: 32px;
                                            line-height: 32px;
                                            border-radius: 4px;
                                            border: 1px solid #dcdfe6;
                                            box-sizing: border-box;
                                            "
                                        >
                                            <div style="padding-left: 10px">
                                            <span
                                                style="color: #409eff; cursor: pointer"
                                                @click="handleRegionDialog"
                                                >选择</span
                                            >
                                            </div>
                                            <div
                                            style="
                                                flex: 1;
                                                padding: 0 10px;
                                                overflow: hidden;
                                                white-space: nowrap;
                                                text-overflow: ellipsis;
                                            "
                                            :title="listQuery.RegionalName"
                                            >
                                            {{ listQuery.RegionalName }}
                                            </div>
                                            <div style="width: 28px; text-align: center">
                                            <i
                                                style="cursor: pointer"
                                                title="删除"
                                                @click="electedRegionalData(null)"
                                                v-show="listQuery.RegionalName"
                                                class="el-icon-close"
                                            ></i>
                                            </div>
                                        </div>
                                    </div>
                                </template>


                                
                                <template slot="UnPurchasedType">
                                    <div class="un_purchased_container">
                                        <el-select
                                            v-model="listQuery.UnPurchasedType"
                                            slot="prepend"
                                            class="purchased_select flex-1"
                                            clearable
                                            disabled
                                            @clear="clearUnPurchasedType"
                                            @change="changeUnPurchasedType"
                                        >
                                            <el-option
                                            :label="t.label"
                                            :value="t.value"
                                            v-for="t in unPurchasedOptions"
                                            :key="t.value"
                                            />
                                        </el-select>

                                        <app-input-number
                                            class="flex-1"
                                            :disabled="!listQuery.UnPurchasedType"
                                            v-model="listQuery.UnPurchasedNum"
                                            :min="0"
                                            :max="999999999"
                                            :precision="0"
                                            :controls="false"
                                            :isShowUnit="false"
                                            splitStr=""
                                            placeholder="可用库存"
                                        ></app-input-number>

                                        
                                        <!-- <el-input-number v-model="listQuery.UnPurchasedNum" :disabled="!listQuery.UnPurchasedType" @change="handleChange" :min="0" label=""></el-input-number> -->


                                        <!-- <el-input
                                            placeholder="可用库存"
                                            v-model="listQuery.UnPurchasedNum"
                                            type="number"
                                            v-antiShake="{
                                            time: 300,
                                            callback: () => {
                                                // handleFilter();
                                            },
                                            }"
                                            clearable
                                            @clear="() => listQuery.UnPurchasedNum = ''"
                                            class="un_purchased_input flex-1"
                                        /> -->
                                        </div>
                                </template>
                                
                                <!-- <template slot="RegionalId">
                                    <div class="_regional_detail_wrapper">
                                        <div class="btn_wrapper">
                                            <el-button type="text" @click="handleRegionDialog">选择</el-button>
                                        </div>
                                        <div class="regional_text" :title="listQuery.RegionalName">{{ listQuery.RegionalName }}</div>
                                        <div class="close_wrapper" v-show="listQuery.RegionalName">
                                            <div class="i_wrapper">
                                                <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                            </div>
                                        </div>
                                    </div>
                                </template> -->

                               
                            </app-table-form>
                        </template>
                            
                            <template slot="Opt" slot-scope="scope">
                                <app-table-row-button v-if="LaborContractState === 1 && !scope.row.ERPStatus" @click="handleWriteOff(scope.row, 'create')" :type="2" text="核销"></app-table-row-button>
                                <app-table-row-button v-if="LaborContractState === 1 && scope.row.ERPStatus" @click="handleWriteOff(scope.row, 'detail')" :type="2" text="详情"></app-table-row-button>

                                <app-table-row-button v-if="LaborContractState === 3" @click="handleAdjustment(scope.row)" :type="2" text="调整"></app-table-row-button>
                                <app-table-row-button v-if="LaborContractState === 3 && scope.row.MaterialCount > 0" @click="handleReturnMaterial(scope.row)" :type="2" text="退料"></app-table-row-button>

                                <app-table-row-button v-if="LaborContractState === 2 && scope.row.TestingResult === 2  && scope.row.ERPStatus != 1 && scope.row.ERPStatus != 2" @click="handleReturn(scope.row)" :type="2" text="退库"></app-table-row-button>
                                <app-table-row-button v-if="LaborContractState === 2 && scope.row.TestingResult === 1" @click="handleTesting(scope.row)" :type="2" text="检测"></app-table-row-button>
                            </template>

                            <template slot="Idx" slot-scope="scope">
                                {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index) }}
                            </template>

                            <template slot="CreateTime" slot-scope="scope">
                                 {{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm', '无') }}
                            </template>

                            <template slot="ArrivalTime" slot-scope="scope">
                                 {{ scope.row.ArrivalTime | dateFilter('YYYY-MM-DD HH:mm', '无') }}
                            </template>
                            <template slot="ServiceNo" slot-scope="scope">
                                 {{ scope.row.ServiceNo | emptyFilter }}
                            </template>
                            
                            <template  slot="ERPStatus" slot-scope="scope">
                                <!-- <span
                                    class="item-status"
                                    :style="{
                                    backgroundColor: getStatusObj(scope.row.ERPStatus).bgColor,
                                    color: scope.row.ERPStatus ? getStatusTextObj(scope.row.ERPStatus).color : '#1D2129'
                                    }"
                                >
                                    {{ scope.row.ERPStatus | materialStatusFilter }}
                                </span> -->


                                <app-tag-pure v-if="getStatusTextObj(scope.row.ERPStatus).label" :color="getStatusTextObj(scope.row.ERPStatus).color" :text="getStatusTextObj(scope.row.ERPStatus).label"></app-tag-pure>
                                <span v-else>无</span>

                           </template>

                           <template  slot="TestingResult" slot-scope="scope">
                                <!-- <span
                                    class="item-status"
                                    :style="{
                                    backgroundColor: getTestingStatusObj(scope.row.TestingResult).bgColor,
                                    color: getTestingTextStatusObj(scope.row.TestingResult).color
                                    }"
                                >
                                    {{ scope.row.TestingResult | testingResultFilter }}
                                </span> -->


                                <app-tag-pure v-if="getTestingTextStatusObj(scope.row.TestingResult).label" :color="getTestingTextStatusObj(scope.row.TestingResult).color" :text="getTestingTextStatusObj(scope.row.TestingResult).label"></app-tag-pure>
                                <span v-else>无</span>

                           </template>
                          
                          <template slot="ReportTime" slot-scope="scope">
                                <span> {{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm', '无') }} </span>
                          </template>

                          <template slot="PickingTime" slot-scope="scope">
                                <span> {{ scope.row.PickingTime | dateFilter('YYYY-MM-DD HH:mm', '无') }} </span>
                          </template>

                           <template slot="InventoryClosingTime" slot-scope="scope">
                                <span> {{ scope.row.InventoryClosingTime | dateFilter('YYYY-MM-DD HH:mm', '无') }} </span>
                          </template>


                          <template slot="ReturnTime" slot-scope="scope">
                                <span> {{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm', '无') }} </span>
                          </template>

                          <template slot="TestingTime" slot-scope="scope">
                                <span> {{ scope.row.TestingTime | dateFilter('YYYY-MM-DD HH:mm', '无') }} </span>
                          </template>

                          <template slot="MaterialCode" slot-scope="scope">
                                <span> {{ scope.row.MaterialCode | materialNameFilter }} </span>
                          </template>

                          <template slot="MaterialName" slot-scope="scope">
                                <span> {{ scope.row.MaterialName | materialNameFilter }} </span>
                          </template>

                          <template slot="Specifications" slot-scope="scope">
                                <span> {{ scope.row.Specifications | materialNameFilter}} </span>
                          </template>

                          <!-- <template slot="SpecificationModel" slot-scope="scope">
                                <span> {{ scope.row.SpecificationModel | materialNameFilter }} </span>
                          </template> -->

                          <template slot="MaterialInStockName" slot-scope="scope">
                                <span> {{ scope.row.MaterialInStockName | materialNameFilter }} </span>
                          </template>

                          <template  slot="Remark" slot-scope="scope">
                                <span>
                                    {{ scope.row.Remark | materialNameFilter }}
                                </span>
                           </template>

                         <!-- <template  slot="ProcessMode" slot-scope="scope">
                                <span>
                                    {{ scope.row.ProcessMode | processModeFilter }}
                                </span>
                         </template> -->


                           <template  slot="MaterialEmployeeList" slot-scope="scope">
                                <span>
                                    {{ scope.row.MaterialEmployeeList | nameFilter }}
                                </span>
                         </template>

                         <template  slot="UseMaterialEmployeeList" slot-scope="scope">
                                <span>
                                    {{ scope.row.UseMaterialEmployeeList | nameFilter }}
                                </span>
                         </template>

                          <template  slot="ReturnMaterialEmployeeList" slot-scope="scope">
                                <span>
                                    {{ scope.row.MaterialEmployeeList | nameFilter }}
                                </span>
                         </template>

                          <template  slot="TestingEmployeeList" slot-scope="scope">
                                <span>
                                    {{ scope.row.TestingEmployeeList | nameFilter}}
                                </span>
                         </template>


                         <!-- <template  slot="IsWarranty" slot-scope="scope">
                                <span>
                                    {{ scope.row.IsWarranty | warrantyFilter }}
                                </span>
                         </template> -->


                          <template slot="btnsArea">
                                    <el-button v-if="LaborContractState === 0" type="primary"  @click="allocationApply()">调拨申请</el-button>
                                    <el-button v-if="LaborContractState === 3" type="primary"  @click="addAparts()">新增配件库存</el-button>
                          </template>
                
                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <!-- <app-table-row-button v-if="LaborContractState === 1 && !scope.row.ERPStatus" @click="handleWriteOff(scope.row)" :type="2" text="核销"></app-table-row-button>
                            <app-table-row-button v-if="LaborContractState === 3" @click="handleAdjustment(scope.row)" :type="2" text="调整"></app-table-row-button>
                            <app-table-row-button v-if="LaborContractState === 3 && scope.row.MaterialCount > 0" @click="handleReturnMaterial(scope.row)" :type="2" text="退料"></app-table-row-button>

                             <app-table-row-button v-if="LaborContractState === 2 && scope.row.TestingResult === 2  && scope.row.ERPStatus != 1 && scope.row.ERPStatus != 2" @click="handleReturn(scope.row)" :type="2" text="退库"></app-table-row-button>
                             <app-table-row-button v-if="LaborContractState === 2 && scope.row.TestingResult === 1" @click="handleTesting(scope.row)" :type="2" text="检测"></app-table-row-button> -->

                            <!-- <app-table-row-button v-if="LaborContractState === 0 || LaborContractState === 3" @click="handleReview(scope.row)" :type="2" text="详情"></app-table-row-button> -->
                            <!-- <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button> -->
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 查看/修改 -->
    <apply-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id"></apply-page>
    <write-off-page @closeDialog="closeWriteOffDialog" @saveSuccess="handleSaveSuccess" v-if="dialogWriteOffFormVisible" :dialogFormVisible="dialogWriteOffFormVisible" :dialogStatus="dialogWriteOffStatus" :id="id" :RegionalId="RegionalId"></write-off-page>
    <spare-parts-inventory-page @closeDialog="closeSparePartsInventoryDialog" @saveSuccess="handleSaveSuccess" v-if="dialogSparePartsInventoryFormVisible" :dialogFormVisible="dialogSparePartsInventoryFormVisible" :dialogStatus="dialogSparePartsInventoryStatus" :id="id"></spare-parts-inventory-page>
    <return-page @closeDialog="closeReturnDialog" @saveSuccess="handleSaveSuccess" v-if="dialogReturnFormVisible" :dialogFormVisible="dialogReturnFormVisible" :dialogStatus="dialogReturnStatus" :id="id"></return-page>
    <material-return-page @closeDialog="closeReturnMaterialDialog" @saveSuccess="handleSaveSuccess" v-if="dialogReturnMaterialFormVisible" :dialogFormVisible="dialogReturnMaterialFormVisible" :dialogStatus="dialogReturnMaterialStatus" :id="id"></material-return-page>
    <add-item @closeDialog="closeItemDialog" @saveSuccess="handleSaveSuccess" v-if="dialogItemFormVisible" :dialogFormVisible="dialogItemFormVisible" :dialogStatus="dialogItemStatus" :id="id"></add-item>
    <testing-page @closeDialog="closeTestingDialog" @saveSuccess="handleSaveSuccess" v-if="dialogTestingFormVisible" :dialogFormVisible="dialogTestingFormVisible" :dialogStatus="dialogTestingStatus" :id="id"></testing-page>

    <!-- 选择地区 -->
    <v-area-choose
    @closeDialog="closeRegionDialog"
    @electedRegionalData="electedRegionalData"
    :dialogFormVisible="dialogRegionFormVisible"
    :checkedList="listQuery.RegionalId ? [listQuery.RegionalId] : []"
    :defaultExpandLevel='1'
    ></v-area-choose>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";
import applyPage from "./apply";
import returnPage from "./return";
import materialReturnPage from "./materialReturn";
import testingPage from "./testing";
import writeOffPage from "./writeOff";
import sparePartsInventoryPage from "./sparePartsInventory";
import vTree from '../../afterSalesMgmt/businessMap/common/tree'
import { vars } from "../common/vars";
import addItem from "./addItem";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";

export default {
    name: "comment-mgmt",
    mixins: [indexPageMixin],
    components: {
        addItem,
        sparePartsInventoryPage,
        applyPage,
        returnPage,
        materialReturnPage,
        testingPage,
        writeOffPage,
        vTree,
        vAreaChoose,
    },
    props: {},
    filters: {

        materialNameFilter(materialName){
             if(materialName){
                 return materialName;
             } 
              return "无";
        },

        // materialStatusFilter(status) {
        //     if (status) {
        //         let tmp = vars.materialStatus.find(s => s.value == status);
        //         return tmp.label;
        //     }
        //     return "无";
        // },

        // testingResultFilter(status){
        //     let tmp = vars.TestingState.find(s => s.value == status);
        //     if (tmp) {
        //         return tmp.label;
        //     }
        //     return "无";
        // },

        processModeFilter(model){
             let tmp = vars.ProcessMode.find(s => s.value == model);
             //console.log(tmp)
            if (tmp) {
                return tmp.label;
            }
            return "无";
        },

        nameFilter(list){
           // debugger
            if(list && list.length > 0){
               let result =  list.map(v => v.Name)   
              return result.toString()
            }
            return "无";     
        },

        warrantyFilter(warranty){
          return warranty?"是":"否"
        },

        sexFilter(value) {
            if (value == 2) {
                return "女";
            }
            if (value == 1) {
                return "男";
            }
        },
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
    },
    watch: {
        "LaborContractState": {
            handler(newVal) {
                this.listQuery.PageIndex = 1
                if(newVal === 0){
                    this.tabColumns = this.tabPickingColumns
                    this.tableSearchItems = [
                    {
                        prop: "MaterialEmployee",
                        label: "领料人"
                    },{
                        prop: "MaterialCode",
                        label: "物料编码"
                    },{
                        prop: "ApplyDate",
                        label: "申请时间"
                    },{
                        prop: "RegionalId",
                        label: "使用地区"
                    },{
                        prop: "MaterialName",
                        label: "物料名称"
                    },{
                        prop: "ERPNo",
                        label: "单号"
                    }
                ]
                }else if(newVal === 1){
                    this.tabColumns = this.tabMaterialsColumns
                    this.tableSearchItems = [
                    // {
                    //     prop: "MaterialUser",
                    //     label: "用料人"
                    // },
                    {
                        prop: "ReportTime",
                        label: "报修时间"
                    },{
                        prop: "WriteOffDate",
                        label: "销库时间"
                    },{
                        prop: "RegionalId",
                        label: "使用地区"
                    },{
                        prop: "ArrivalTime",
                        label: "到站时间"
                    },{
                        prop: "ServiceNo",
                        label: "服务单号"
                    },{
                        prop: "ERPNo",
                        label: "单号"
                    }
                ]
                }else if(newVal === 2){
                    this.tabColumns = this.tabMaterialReturnColumns
                    this.tableSearchItems = [
                    {
                        prop: "ReturnedBy",
                        label: "退料人"
                    },{
                        prop: "MaterialReturnDate",
                        label: "退料时间"
                    },{
                        prop: "MaterialName",
                        label: "物料名称"
                    },{
                        prop: "ERPNo",
                        label: "单号"
                    }
                ]
                    
                }else if(newVal === 3){
                    this.tabColumns = this.tabPersonalColumns
                    this.tableSearchItems = [
                    {
                        prop: "MaterialCode",
                        label: "物料编码"
                    },{
                        prop: "MaterialEmployee",
                        label: "领料人"
                    },{
                        prop: "RegionalId",
                        label: "使用地区"
                    },{
                        prop: "MaterialName",
                        label: "物料名称"
                    },{
                        prop: "UnPurchasedType",
                        label: '可用库存'
                    }
                ]
                }
                this.onResetSearch();
            },
            immediate: true
        }

    },
    created() {
         this.tabColumns = this.tabPickingColumns
    },
    data() {
        return {
           // scores: vars.customeMgmt.scores,
            layoutMode: 'simple',
            epKeys: [],
            types: [
                { value: 0, label: "领料" },
                { value: 1, label: "用料" },
                { value: 2, label: "退料" },
                { value: 3, label: "个人库存" },
            ],
            tableSearchItems: [
                {
                    prop: "MaterialEmployee",
                    label: "领料人"
                },{
                    prop: "MaterialCode",
                    label: "物料编码"
                }
            ],
            id: "",
            RegionalId: null,
            dialogStatus: "create",
            dialogFormVisible: false,
            dialogWriteOffStatus: "create",
            dialogWriteOffFormVisible: false,
            dialogSparePartsInventoryStatus:"create",
            dialogSparePartsInventoryFormVisible:false,
            dialogReturnStatus: "create",
            dialogReturnFormVisible:false,
            dialogReturnMaterialStatus: "create",
            dialogReturnMaterialFormVisible:false,
            dialogItemStatus: "create-item",
            dialogItemFormVisible:false,
            dialogTestingStatus: "create",
            dialogTestingFormVisible:false,

            listLoading: false,
            tabColumns:[],
            tabPickingColumns: [
                {
                    attr: {
                        prop: "Idx",
                        label: "序号",
                        width:'50',
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ERPNo",
                        label: "单号（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialCode",
                        label: "物料编码（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialName",
                        label: "物料名称（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Specifications",
                        label: "规格型号（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                    slot :true
                   
                },
                {
                    attr: {
                        prop: "MaterialInStockName",
                        label: "仓库名称（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialEmployeeList",
                        label: "领料人",
                        showOverflowTooltip: true,
                        width:'80',
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "ERPStatus",
                        label: "物料状态",
                        showOverflowTooltip: true,
                        width:'80',
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "CreateTime",
                        label: "申请时间",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
                 {
                    attr: {
                        prop: "MaterialUnit",
                        label: "单位",
                        showOverflowTooltip: true,
                        width:'60',
                    },
                },
                {
                    attr: {
                        prop: "MaterialCount",
                        label: "数量",
                        showOverflowTooltip: true,
                        width:'60',
                    },
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "使用地区",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Remark",
                        label: "备注",
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
            ],
            tabMaterialsColumns:[
                
                {
                    attr: {
                        prop: "Idx",
                        label: "序号",
                        width:'50',
                    },
                    slot: true
                },
                // {
                //     attr: {
                //         prop: "StructPartName",
                //         label: "配件名称",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                // },
                // {
                //     attr: {
                //         prop: "SpecificationModel",
                //         label: "规格型号",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // },
                // {
                //     attr: {
                //         prop: "ProcessMode",
                //         label: "用途分类",
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // },
                {
                    attr: {
                        prop: "Code",
                        label: "报修单编号",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "ERPNo",
                        label: "单号（ERP）",
                        // width:'120',
                        showOverflowTooltip: true,
                    },
                },

                {
                    attr: {
                        prop: "ArrivalTime",
                        label: "到站时间",
                        // width:'120',
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ServiceNo",
                        label: "服务单号",
                        // width:'120',
                        showOverflowTooltip: true,
                    },
                    slot: true
                },

                {
                    attr: {
                        prop: "RegionalName",
                        label: "报修地区",
                        showOverflowTooltip: true,
                    },
                },
                // {
                //     attr: {
                //         prop: "Name",
                //         label: "报修设备",
                //         width:'140',
                //         showOverflowTooltip: true,
                //     },
                   
                // },

                {
                    attr: {
                        prop: "ReportTime",
                        label: "报修时间",
                        showOverflowTooltip: true,
                    }, 
                    slot:true
                },
                {
                    attr: {
                        prop: "ERPStatus",
                        label: "物料状态",
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
                // {
                //     attr: {
                //         prop: "UseMaterialEmployeeList",
                //         label: "用料人",
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // },
                // {
                //       attr: {
                //         prop: "PickingTime",
                //         label: "领料时间",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // },
                {
                     attr: {
                        prop: "InventoryClosingTime",
                        label: "销库时间",
                        // width:'120',
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "Opt",
                        label: "操作",
                        width:'80',
                    },
                    slot: true
                },
                // {
                //     attr: {
                //         prop: "IsWarranty",
                //         label: "是否在保",
                //         showOverflowTooltip: true,
                //     },
                //     slot:true    
                // },
                // {
                //     attr: {
                //         prop: "MaterialCode",
                //         label: "物料编码（ERP）",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // },
                // {
                //     attr: {
                //         prop: "MaterialName",
                //         label: "物料名称（ERP）",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // },
                // {
                //     attr: {
                //         prop: "Specifications",
                //         label: "规格型号（ERP）",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                   
                // },
                // {
                //     attr: {
                //         prop: "MaterialInStockName",
                //         label: "仓库名称（ERP）",
                //         width:'120',
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                   
                // },
                // {
                //     attr: {
                //         prop: "Remark",
                //         label: "备注",
                //         showOverflowTooltip: true,
                //     },
                //     slot:true
                // }
            ],
            tabMaterialReturnColumns:[
                {
                    attr: {
                        prop: "Idx",
                        label: "序号",
                        width:'50',
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ERPNo",
                        label: "单号（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialCode",
                        label: "物料编码（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialName",
                        label: "物料名称（ERP）",
                        width:'120',
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "Specifications",
                        label: "规格型号（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialInStockName",
                        label: "仓库名称（ERP）",
                        width:'120',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "ReturnMaterialEmployeeList",
                        label: "退料人",
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "MaterialUnit",
                        label: "单位",
                        width: '55px',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialReturnCount",
                        label: "数量",
                        width: '55px',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "ReturnTime",
                        label: "退料时间",
                        // showOverflowTooltip: true,
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "TestingResult",
                        label: "质检状态",
                        width: '100px'
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "TestingEmployeeList",
                        label: "质检人",
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "ERPStatus",
                        label: "物料状态",
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "TestingTime",
                        label: "质检时间",
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "Remark",
                        label: "备注",
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "Opt",
                        label: "操作",
                        width:'80',
                    },
                    slot: true
                },
            ],
            tabPersonalColumns:[
                
                {
                    attr: {
                        prop: "Idx",
                        label: "序号",
                        width:'50',
                    },
                    slot: true
                },
                 {
                    attr: {
                        prop: "MaterialCode",
                        label: "物料编码（ERP）",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialName",
                        label: "物料名称（ERP）",
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "Specifications",
                        label: "规格型号（ERP）",
                        showOverflowTooltip: true,
                    },
                     slot:true
                },
                {
                    attr: {
                        prop: "MaterialStockName",
                        label: "仓库名称（ERP）",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "使用地区",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialEmployeeList",
                        label: "领料人",
                        showOverflowTooltip: true,
                    },
                    slot:true
                },
                {
                    attr: {
                        prop: "MaterialUnit",
                        label: "单位",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "MaterialCount",
                        label: "可用库存",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Opt",
                        label: "操作",
                        width:'120',
                    },
                    slot: true
                },
            ],
            LaborContractState: 0,
            listQuery: {
                PageSize: 20,
                PageIndex: 1,
                EmployeeName:'',
                RegionalId:'',
                MaterialCode:'',
                ReportTime:'',
                MaterialReturnDate:'',

                Date: null,
                RegionalName: '',
                MaterialName: '',
                ERPNo: '',
                ArrivalTime: null,
                ServiceNo: '',

                ApplyDate: null,
                WriteOffDate: null,

                UnPurchasedType: 4,
                UnPurchasedNum: null

            },


            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

            dialogRegionFormVisible: false,

            unPurchasedOptions: [
                {
                value: 1,
                label: "等于",
                },
                {
                value: 2,
                label: "不等于",
                },
                {
                value: 3,
                label: "小于等于",
                },
                {
                value: 4,
                label: "大于等于",
                },
                {
                value: 5,
                label: "大于",
                },
                {
                value: 6,
                label: "小于",
                },
            ],
        };
    },
    methods: {
        //地区选择
        closeRegionDialog() {
          this.dialogRegionFormVisible = false;
        },
        handleRegionDialog(){
            this.dialogRegionFormVisible=true;
        },
        electedRegionalData(data){
            if(data){
                this.listQuery.RegionalId = data.Id;
                this.listQuery.RegionalName = data.ParentName;
            }else{
                this.listQuery.RegionalId = '';
                this.listQuery.RegionalName = '';
            }
        },
        changeTreeNode(d) {
            if (d.Id == -1) {
                this.listQuery.RegionalId = null;
            } else {
                this.listQuery.RegionalId = d.Id;
            }
            this.listQuery.PageIndex = 1
            this.getList();
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },

        handleWriteOff(row, optType = 'detail'){
            this.id = row.MaintenanceId;
            this.RegionalId = row.RegionalId;
            this.dialogWriteOffStatus = optType;
            this.dialogWriteOffFormVisible = true;
        },

        closeWriteOffDialog(){
           this.dialogWriteOffFormVisible = false;
        },


        handleReturn(row){
           this.id = row.Id;
           this.dialogReturnFormVisible = true;
        },

        closeReturnDialog(){
           this.dialogReturnFormVisible = false;
        },

        closeReturnMaterialDialog(){
            this.dialogReturnMaterialFormVisible = false;
        },

        closeItemDialog(){
            this.dialogItemFormVisible = false;
        },

        closeTestingDialog(){
           this.dialogTestingFormVisible = false;
        },

        handleTesting(row){
            this.id = row.Id;
            this.dialogTestingFormVisible = true;
        },

        handleAdjustment(row){
             this.dialogItemStatus = "create-item"
             this.id = row.Id;
             this.dialogItemFormVisible = true;
        },

        handleReturnMaterial(row){
            this.id = row.Id;
            this.dialogReturnMaterialFormVisible = true;
        },

        addAparts(){
            this.dialogSparePartsInventoryFormVisible = true;
        },

        closeSparePartsInventoryDialog(){
           this.dialogSparePartsInventoryFormVisible = false;
        },

        
        handleSaveSuccess(_formData) {
            this.closeItemDialog();
            this.closeDialog();
            this.closeSparePartsInventoryDialog();
            this.closeWriteOffDialog();
            this.closeTestingDialog();
            this.closeReturnMaterialDialog();
            this.closeReturnDialog();
            this.getList();
        },

        allocationApply(){
            this.dialogStatus = "create",
            this.dialogFormVisible = true;
        },

        //获取成员列表
        getList() {

            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));

            if(this.LaborContractState === 0){

                materialTransferApi.getList(postData).then((res) => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch((err) => {
                    this.listLoading = false;
                });

            }else if(this.LaborContractState === 1){
                materialTransferApi.getListPageByMaintenance(postData).then((res) => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch((err) => {
                    this.listLoading = false;
                });

            }else if(this.LaborContractState === 2){
                materialTransferApi.getMaterialReturnList(postData).then((res) => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
            }else if(this.LaborContractState === 3){
                materialTransferApi.getMaterialPersonalStockList(postData).then((res) => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
            }

          
            
        },
        handleTableDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id)
            } else {
                ids.push(rows.Id)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                commentMgmt.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        //弹出详情框
        handleReview(row, optType = "detail") {
            this.id = row.Id;
            if(this.LaborContractState === 0){
               this.dialogStatus = optType;
               this.dialogFormVisible = true;
            }else if(this.LaborContractState === 1){

            }else if(this.LaborContractState === 2){

            }else if(this.LaborContractState === 3){
                this.dialogItemStatus = "detail-item";
                this.dialogItemFormVisible = true;
            }
           
            
        },
    

        onResetSearch() {
            this.listQuery.EmployeeName = ''
            this.listQuery.RegionalId = ''
            this.listQuery.MaterialCode = ''
            this.listQuery.ReportTime = ''
            this.listQuery.MaterialReturnDate = ''
            this.listQuery.Date = null
            this.listQuery.RegionalName = ''
            this.listQuery.MaterialName = ''
            this.listQuery.ERPNo = ''

            this.listQuery.ArrivalTime = null
            this.listQuery.ServiceNo = ''
            
            this.listQuery.ApplyDate = null
            this.listQuery.WriteOffDate = null

            this.listQuery.UnPurchasedType = 4
            this.listQuery.UnPurchasedNum = null

            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

       getStatusObj(status) {
          return vars.materialStatus.find(s => s.value == status) || {};
        },

        getStatusTextObj(status){
            return vars.materialStatus.find(s => s.value == status) || {};
        },

        getTestingStatusObj(status) {
          return vars.TestingState.find(s => s.value == status) || {};
        },
        getTestingTextStatusObj(status) {
          return vars.TestingState.find(s => s.value == status) || {};
        },
        // 清除未购数量
        clearUnPurchasedType() {
            this.listQuery.UnPurchasedType = 4;
            this.listQuery.UnPurchasedNum = null
            // this.handleFilter();
        },
        changeUnPurchasedType(value) {

        }
      
    
    },
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        flex: 1;
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}

.month-range-wrapper{
    display: flex;
    .start-month, .end-month{
        flex: 1
    }
    .month-separator{
        padding: 0 6px;
    }
}

// .item-status {
//   padding: 2px 4px;
//   border-radius: 10%;
// }


/deep/.un_purchased_container {
    display: flex;
    align-items: center;
    .purchased_select {
    // width: 100px;
    .el-input__inner {
        border-radius: 4px 0 0 4px !important;
    }
    }
    // .un_purchased_input {
    // // width: 120px;
    //     line-height: 32px;
    //     .el-input__inner {
    //         line-height: 32px;
    //         border-radius: 0 4px 4px 0 !important;
    //     }
    //     input::-webkit-outer-spin-button,
    //     input::-webkit-inner-spin-button {
    //         line-height: 32px;
    //         -webkit-appearance: none;
    //     }
    //     input[type="number"] {
    //         line-height: 32px;
    //         -moz-appearance: textfield;
    //     }
    // }
}

</style>
