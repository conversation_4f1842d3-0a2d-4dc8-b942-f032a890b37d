<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title :title="pageTitle" :subTitle="[pageDesc]" :showBackBtn="!!returnUrl" @goBack="handleGoBack"></page-title> -->
        <div class="page-wrapper">
            <div class="product-list">
                <el-input 
                class="elInput" 
                style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" 
                prefix-icon="el-icon-search" 
                placeholder="输入关键字进行过滤" 
                v-model="filterText"
                v-antiShake='{
                    time: 300,
                    callback: () => {
                        getRegionList()
                    }
                }' 
                ></el-input>
                
                <div class="opts-wrapper">
                    <div class="main-btn">
                        <el-button type="primary" v-if="hasTreeOpertAuth" @click="handleRegionDialog">设置访问站点</el-button>
                    </div>
                    <div>
                        <el-popover placement="bottom" width="120" trigger="click">
                            <div>
                                <div style="margin-bottom: 10px;">排序设置</div>
                                <div class="sort-item">
                                    <el-button type="text" @click="regionListSort(1)">默认排序</el-button>
                                    <i v-if="regionListQuery.SortType == 1" class="el-icon-check" style="color: #67c23a"></i>
                                </div>
                                <div class="sort-item">
                                    <el-button type="text" @click="regionListSort(2)">按回访正序排</el-button>
                                    <i v-if="regionListQuery.SortType == 2" class="el-icon-check" style="color: #67c23a"></i>
                                </div>
                                <div class="sort-item">
                                    <el-button type="text" @click="regionListSort(3)">按回访倒序排</el-button>
                                    <i v-if="regionListQuery.SortType == 3" class="el-icon-check" style="color: #67c23a"></i>
                                </div>
                                <div class="sort-item">
                                    <el-button type="text" @click="regionListSort(4)">按设备数量排</el-button>
                                    <i v-if="regionListQuery.SortType == 4" class="el-icon-check" style="color: #67c23a"></i>
                                </div>
                            </div>
                            <el-button slot="reference" type="text"><i class="el-icon-d-caret"></i> 排序</el-button>
                        </el-popover>
                    </div>
                    <div>
                        <el-popover placement="bottom" width="350" trigger="click">
                            <div class="search-btns" style="margin-bottom: 10px;">
                                <app-table-row-button :type='3' text='重置' @click="handleResearch"></app-table-row-button>
                                <app-table-row-button :type='1' text='查询' @click='getRegionList'></app-table-row-button>
                            </div>
                            <ul class="screeningBox">
                                <li class="cl">
                                    <span class="fl">回访时间</span>
                                    <el-date-picker style="width:220px;" class="fr" v-model="regionListQuery.Range" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="-" start-placeholder="" end-placeholder=""></el-date-picker>
                                </li>
                                <li class="cl">
                                    <span class="fl">未回访</span>
                                    <el-select
                                        style="width:220px;"
                                        class="fr"
                                        v-model="regionListQuery.NotReturnVisit"
                                        clearable
                                        placeholder=""
                                        >
                                        <el-option
                                            v-for="item in notReturnVisitType"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </li>
                                <li class="cl">
                                    <span class="fl">类型</span>
                                    <el-select
                                    style="width:220px;" class="fr"
                                    placeholder=""
                                    clearable
                                    v-model="regionListQuery.EquipmentModel"
                                    >
                                        <el-option v-for="item in equipmentModelTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </li>
                                <li class="cl">
                                    <span class="fl">是否托管</span>
                                    <el-select
                                    style="width:220px;" class="fr"
                                    placeholder=""
                                    clearable
                                    v-model="regionListQuery.WhetherHosting"
                                    >
                                        <el-option label="是" :value="true"></el-option>
                                        <el-option label="否" :value="false"></el-option>
                                    </el-select>
                                </li>
                                <li class="cl">
                                    <span class="fl">站控系统</span>
                                    <el-select
                                    style="width:220px;" class="fr"
                                    placeholder=""
                                    clearable
                                    v-model="regionListQuery.IsStationControlSystem"
                                    >
                                        <el-option label="有" :value="true"></el-option>
                                        <el-option label="没有" :value="false"></el-option>
                                    </el-select>
                                </li>
                            </ul>
                            <el-button slot="reference" type="text"><i class="el-icon-search"></i> 筛选</el-button>
                        </el-popover>
                    </div>
                </div>

                <div class="treeBox" v-loading='regionListLoading'>
                    <no-data v-if="regionalList.length == 0"></no-data>

                    <el-tree v-if="regionalList.length > 0" class="elTree" ref="treeRef" :data="regionalList" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => handleCheckedNode(data)">
                        <span class="custom-tree-node" slot-scope="{ node, data }" :style="[{'color': data.AfterSalesRegional ? '' : '#968F8F'}]">
                            <span :style="[{'padding-top': data.AfterSalesRegional ? '4px' : '0'}]" class="node-title" :title="node.label">{{ node.label }}（{{ data.StatisticalCount }}）</span>
                            <span v-if="data.AfterSalesRegional" style="color: #968f8f; font-size: 12px;" :style="{'padding-bottom': data.AfterSalesRegional ? '4px' : '0'}">
                                <span style="padding-right: 15px; display: inline-block; margin-top: 4px;">最近回访：
                                    <template v-if="data.LastTime">{{ data.LastTime | dateFilter('YYYY-MM-DD') }}</template>
                                    <template v-else>无</template>
                                    <span :title="data.IsPending ? '有待我处理的问题' : data.IsExisting ? '存在问题' : ''"><svg-icon v-if="data.IsPending || data.IsExisting" icon-class="important" :style="{color: data.IsPending ? '#D9001B' : data.IsExisting ? '#F59A23' : ''}" style="top: -5px; right: 6px; top: 1px; font-size: 18px; padding-left: 5px; padding-top: 5px; z-index: 999; position: absolute;"></svg-icon></span>
                                </span>
                            </span>
                        </span>
                    </el-tree>

                    <!-- <tags mode="list" class="tagsList" :items="regionalList" v-model="currentregionalId">
                        <template v-for="(v, idx) in regionalList" :slot="v.value" slot-scope="scope">
                            <div class="item-warpper" :key="idx">
                                <div :title="v.label" class="item-title-wrapper">
                                    <div class="item-title omit">
                                        {{ v.label }}
                                    </div>
                                    <div class="num">
                                        （{{ v.StatisticalCount }}）
                                    </div>
                                </div>
                                <div>
                                    最近一次回访：
                                    <span v-if="v.LastTime">{{ v.LastTime | dateFilter('YYYY-MM-DD') }}</span>
                                    <span v-else>
                                        无
                                    </span>
                                </div>
                            </div>
                        </template>
                    </tags> -->
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <!-- :default-sort = "sortObj" -->

                <el-card class="box-card box-card-sp">
                    <div slot="header" class="clearfix title-wrapper">
                        <span class="title">
                            <i class="el-icon-info"></i>
                            <span style="margin-left: 4px;">站点信息</span>
                        </span>
                        <el-button style="float: right; padding: 3px 0" type="text" :disabled='!checkedRegional' @click="handleRegionalDialog">站点信息</el-button>
                    </div>
                    <div v-if="checkedRegional" class="detail-row1">
                        <el-row style="margin-bottom: 4px;">
                            <el-col :span="6">
                                <div :title="checkedRegional.label" class="omit">
                                    <span class="title">站点名称：</span>{{ checkedRegional.label }}
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div :title="checkedRegional.RegionalModel ? checkedRegional.RegionalModel.RegionalMobile : ''" class="omit">
                                    <span class="title">电话：</span>{{ checkedRegional.RegionalModel ? checkedRegional.RegionalModel.RegionalMobile : '' || '无' }}
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div>
                                    <span class="title">站控系统：</span>{{ checkedRegional.RegionalModel && checkedRegional.RegionalModel.IsStationControlSystem ? '有' : '没有' }}
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div>
                                    <span class="title">备注：</span>{{ checkedRegional.RegionalModel && checkedRegional.RegionalModel.Remark }}
                                </div>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <div :title="checkedRegional.RegionalModel ? checkedRegional.RegionalModel.RegionalContact : ''" class="omit">
                                    <span class="title">站长：</span>{{ checkedRegional.RegionalModel ? checkedRegional.RegionalModel.RegionalContact : '' || '无' }}
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div :title="checkedRegional.RegionalModel ? checkedRegional.RegionalModel.RegionalPhone : ''" class="omit">
                                    <span class="title">地区电话：</span>{{ checkedRegional.RegionalModel ? checkedRegional.RegionalModel.RegionalPhone : '' || '无' }}
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <!-- <div :title="checkedRegional.WhetherHosting ? checkedRegional.WhetherHosting : ''" class="omit">
                                    <template v-if="!hasDisabled(currentregionalId)">
                                        是否托管：{{ checkedRegional.WhetherHosting === true ? '是' : checkedRegional.WhetherHosting === false ? '否' : '-' }}
                                    </template>
                                </div> -->
                                <div>
                                    <span class="title">部署版本：</span>{{ checkedRegional.RegionalModel && checkedRegional.RegionalModel.DeploymentVersion }}
                                </div>
                            </el-col>
                        </el-row>
                        
                    </div>
                </el-card>

                <el-tabs v-model="activeTab" style="padding: 0 10px;">
                    <el-tab-pane v-for="t in tabs" :key="t.value" :label="t.label" :name="t.value"></el-tab-pane>
                </el-tabs>

                <!-- <app-table-form style="padding: 10px;" :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                    <template slot="Keywords">
                        <el-input style="width: 100%;" 
                            placeholder="搜索模板名称"
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQuery.Keywords"
                        ></el-input>
                    </template>

                    <template slot="btnsArea">
                        <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                            <template slot="btnAdd" slot-scope="scope">
                                <el-button type="primary" :disabled='!currentregionalId' @click="onBtnClicked(scope.data.DomId)">{{ scope.data.Name }}</el-button>
                            </template>
                        </permission-btn>
                    </template>
                </app-table-form> -->

                <div style="padding: 0 10px;" v-if="activeTab == '1'">
                    <el-button type="primary" v-if="hasbtnAddRecord" :disabled='!currentregionalId || hasDisabled(currentregionalId)' @click="onBtnClicked('btnAddRecord')"><i class="el-icon-plus" style="margin-right: 2px;"></i>添加回访记录</el-button>
                    <el-button type="primary" style="margin-left: 15px;" v-if="hasReturnVisitRemin" @click="handleSettingDialog" :disabled='!checkedRegional || hasDisabled(checkedRegional.value)'><i class="el-icon-message-solid" style="margin-right: 2px;"></i>设置回访提醒</el-button>
                    <el-button type="primary" style="margin-left: 15px;" v-if="hasExport" @click="onBtnClicked('btnExport')" >导出</el-button>
                </div>

                <div style="padding: 0 10px;" v-if="activeTab == '2'">
                    <el-button type="primary" v-if="hasbtnAdd" :disabled='!currentregionalId || hasDisabled(currentregionalId)' @click="onBtnClicked('btnAdd')"><i class="el-icon-plus" style="margin-right: 2px;"></i>添加设备参数</el-button>
                </div>

                <div class="content" id="__dynamicTabCoreWrapper">
                    <template v-if="activeTab == '1'">
                        <app-table-core :optColWidth='100' :height='tabHeight' ref="mainTable1" key="mainTable1" :isShowBtnsArea='false' :tab-columns="tabColumnsAccessRecord" :tab-datas="tabDatasAccessRecord" :tab-auth-columns="[]" :isShowAllColumn="false" :loading="listLoadingAccessRecord" :isShowOpatColumn="true" :startOfTable="(listQueryAccessRecord.PageSize * (listQueryAccessRecord.PageIndex - 1))" :multable="false" :layoutMode='layoutMode'>
                            <template slot='VistTime' slot-scope="scope">
                                {{ scope.row.VistTime | dateFilter('YYYY-MM-DD') }}
                            </template>
                            <template slot='EmployeeList' slot-scope="scope">
                                {{ scope.row.EmployeeList | namesFilter }}
                            </template>

                            <template slot='SalesAfterVisttType' slot-scope="scope">
                                <!-- {{ scope.row.SalesAfterVisttType == 1 ? '正常回访' : '故障处理' }} -->
                                {{ scope.row.SalesAfterVisttType | salesAfterVisttTypeFilter }}
                            </template>

                            <template slot="TotalTimeEmployeeList" slot-scope="scope">
                                <span v-for="(emp, idx) in scope.row.EmployeeList" :key="idx">
                                    {{ getTotalTime(emp.EmployeeId, scope.row.TotalTimeEmployeeList) | totalTimeFilter }}
                                    <template v-if="idx < scope.row.EmployeeList.length - 1">/</template>
                                </span>
                            </template>

                            <template slot-scope="scope">
                                <el-button type="text" @click="handleRecordDialog(scope.row)">回访详情</el-button>
                            </template>
                        </app-table-core>
                        
                    </template>
                    <template v-else-if="activeTab == '2'">
                        <app-table-core :height='tabHeight' ref="mainTable2" key="mainTable2" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable="false" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                            <template slot='EquipmentModel' slot-scope="scope">
                                {{ scope.row.EquipmentModel | equipmentModelFilter }}
                            </template>
                            <template slot='WhetherHosting' slot-scope="scope">
                                {{ scope.row.WhetherHosting ? '是' : '否' }}
                            </template>

                            <template slot="StartOrStopFurnace" slot-scope="scope">
                                <span class="item-status" v-if="scope.row.StartOrStopFurnace"
                                :style="{backgroundColor: getStartOrStopFurnaceObj(scope.row.StartOrStopFurnace).bgColor,
                                        color: getStartOrStopFurnaceObj(scope.row.StartOrStopFurnace).color}">
                                    {{ getStartOrStopFurnaceObj(scope.row.StartOrStopFurnace).label }}
                                </span>
                                <template v-else>无</template>
                            </template>

                            <!-- 表格查询条件区域 -->
                            <!-- <template slot="conditionArea">
                                
                            </template> -->

                            <!-- 表格行操作区域 -->
                            <!-- 使用统一方式，个别电脑有样式问题，所以多加操作列 Opt -->
                            <!-- <template slot-scope="scope"></template> -->
                            <template slot="Opt" slot-scope="scope">
                                <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
                                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1"></app-table-row-button>
                                <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
                            </template>
                            
                        </app-table-core>
                        
                    </template>
                    <template v-else-if="activeTab == '3'">
                        <div class="cl">
                            <div class="fl" style="width: 200px; height: 100%; padding-left: 10px;">

                                <tags mode="list" v-if="tabsOfTab3.length>0" :items="tabsOfTab3" v-model="listQueryQuestion.GroupType">
                                    <template v-for="(item) in tabsOfTab3" :slot="item.value">
                                        <div :key="item.Id" class="item">
                                            {{ item.label }}（{{ item.total }}）
                                        </div>
                                    </template>
                                </tags>

                            </div>
                            <div class="fl" style="width: calc(100% - 200px); border-left: 1px solid #dcdfe6;">
                                <div style="padding: 0 10px;">
                                    <el-button type="primary" :disabled='!currentregionalId || hasDisabled(currentregionalId)' @click="onBtnClicked('btnAddQuestion')"><i class="el-icon-plus" style="margin-right: 2px;"></i>添加问题</el-button>
                                </div>
                                <app-table-core :height='tabHeight - 82' :serial='false' ref="mainTable3" key="mainTable3" :isShowBtnsArea='false' :tab-columns="tabColumnsQuestion" :tab-datas="tabDatasQuestion" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoadingQuestion" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode'>
                                    <template slot='Idx' slot-scope="scope">
                                        <!-- {{ scope.index + listQueryQuestion.PageIndex * listQueryQuestion.PageSize }} -->
                                        {{ (listQueryQuestion.PageIndex - 1) * listQueryQuestion.PageSize + (scope.index + 1) }}
                                    </template>
                                    <template slot='ProcessEmployeeList' slot-scope="scope">
                                        {{ scope.row.ProcessEmployeeList | namesFilter }}
                                    </template>
                                    <template slot='CreateEmployee' slot-scope="scope">
                                        {{ scope.row.CreateEmployee | nameFilter }}
                                    </template>
                                    <template slot='CreateTime' slot-scope="scope">
                                        {{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
                                    </template>
                                    <template slot='ProcessTime' slot-scope="scope">
                                        {{ scope.row.ProcessTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
                                    </template>

                                    <template slot="SalesAfterQuestionStatus" slot-scope="scope">
                                        <span class="item-status" v-if="scope.row.SalesAfterQuestionStatus"
                                        :style="{backgroundColor: getQuestionStatusObj(scope.row.SalesAfterQuestionStatus).bgColor,
                                                color: getQuestionStatusObj(scope.row.SalesAfterQuestionStatus).color}">
                                            {{ getQuestionStatusObj(scope.row.SalesAfterQuestionStatus).label }}
                                        </span>
                                        <template v-else>无</template>
                                    </template>

                                    <!-- 表格查询条件区域 -->
                                    <!-- <template slot="conditionArea">
                                        
                                    </template> -->

                                    <!-- 表格行操作区域 -->
                                    <template slot-scope="scope">
                                        <app-table-row-button @click="handleUpdateQuestion(scope.row, 'detail')" :type="2"></app-table-row-button>
                                        <app-table-row-button v-if="isCreator(scope.row) && scope.row.SalesAfterQuestionStatus == 1" @click="handleUpdateQuestion(scope.row)" :type="1"></app-table-row-button>
                                        <app-table-row-button v-if="isHandlor(scope.row) && scope.row.SalesAfterQuestionStatus == 2" @click="handleUpdateQuestion(scope.row, 'handle')" text="处理"></app-table-row-button>
                                        <app-table-row-button v-if="(isHandlor(scope.row) && scope.row.SalesAfterQuestionStatus == 2) || scope.row.SalesAfterQuestionStatus == 1" @click="handleAssignQuestion(scope.row)" text="指派"></app-table-row-button>
                                        <app-table-row-button v-if="isCreator(scope.row) && scope.row.SalesAfterQuestionStatus == 1" @click="handleDelQuestion(scope.row)" :type="3"></app-table-row-button>
                                    </template>
                                </app-table-core>

                                <pagination v-if="activeTab == '3'" :total="totalQuestion" :page.sync="listQueryQuestion.PageIndex" :size.sync="listQueryQuestion.PageSize" @pagination="handleCurrentChangeQuestion" @size-change="handleSizeChangeQuestion" />
                            </div>
                        </div>
                    </template>
                </div>

                <pagination v-show="activeTab == '1'" :total="totalAccessRecord" :page.sync="listQueryAccessRecord.PageIndex" :size.sync="listQueryAccessRecord.PageSize" @pagination="handleCurrentChangeAccessRecord" @size-change="handleSizeChangeAccessRecord" />
                <pagination v-show="activeTab == '2'" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                

            </div>
        </div>
    </div>
    
    <v-area-choose
      v-if="dialogRegionFormVisible"
      @closeDialog="closeRegionDialog"
      @electedRegionalData="handleRegionSaveSuccess"
      :dialogFormVisible="dialogRegionFormVisible"
      :condition='{}'
      :multiple='true'
      :checkedList="checkedList"
      :defaultExpandLevel='1'
    ></v-area-choose>
    
    <addDeviceParams v-if="dialogDeviceParamsFormVisible" :checkedRegional='checkedRegional' @closeDialog="closeDeviceParamsDialog" @saveSuccess="handleDeviceParamsSaveSuccess" :dialogFormVisible="dialogDeviceParamsFormVisible" :dialogStatus="dialogDeviceParamsStatus" :id="id" @reload="getList"></addDeviceParams>

    <addReturnVisit v-if="dialogReturnVisitFormVisible" :checkedRegional='checkedRegional' @closeDialog="closeReturnVisitDialog" @saveSuccess="handleReturnVisitSaveSuccess" :dialogFormVisible="dialogReturnVisitFormVisible" :dialogStatus="dialogReturnVisitStatus" :id="id" @reload="getList"></addReturnVisit>

    <regionalDetail v-if="checkedRegional && dialogRegionalFormVisible" @closeDialog='closeRegionalDialog' @saveSuccess="handleRegionalSaveSuccess" :dialogFormVisible='dialogRegionalFormVisible' :id='checkedRegional.RegionalId'></regionalDetail>

    <setting v-if="dialogSettingFormVisible && checkedRegional" :regionalBusinessRelationId='checkedRegional.value' @closeDialog="closeSettingDialog" @saveSuccess="handleSettingSaveSuccess" :dialogFormVisible="dialogSettingFormVisible" :dialogStatus="dialogSettingStatus"></setting>

    <addQuestion v-if="dialogQuestionFormVisible" :checkedRegional='checkedRegional' @closeDialog="closeQuestionDialog" @saveSuccess="handleQuestionSaveSuccess" :dialogFormVisible="dialogQuestionFormVisible" :dialogStatus="dialogQuestionStatus" :id="questionId" @reload="() => {getListQuestion(); getStatistics();}"></addQuestion>
    <assignQuestion v-if="dialogAssignQuestionFormVisible" @closeDialog="closeAssignQuestionDialog" @saveSuccess="handleAssignQuestionSaveSuccess" :dialogFormVisible="dialogAssignQuestionFormVisible" :id="questionId" @reload="() => {getListQuestion(); getStatistics();}"></assignQuestion>

    <!-- <el-drawer title="历史回访记录" :visible.sync="drawer" :show-close="false">
        <template slot="title">
          <div style="display: flex;">
            <div style="flex: 1;">
              <i class="el-icon-time" style="margin-right: 4px; vertical-align: middle;"></i>
              <span style="font-size: 12px;">历史回访记录</span>
            </div>
            <div>
              <i style="cursor: pointer;" class="el-icon-close" @click="() => drawer = false"></i>
            </div>
          </div>
        </template>
        <div class="elTimeBox">
          <div class="history-list">
            <app-table-core
                :loading="loadingHistor"
                ref="mainTable"
                :tab-columns="historeTabColumns"
                :tab-datas="histories"
                :tab-auth-columns="[]"
                :isShowAllColumn="true"
                :startOfTable="startOfTable"
                @sortChagned="handleSortChange2"
                :isShowOpatColumn='false'
                :multable='false'
                :optColWidth='100'
                >
                <template slot='VistTime' slot-scope="scope">
                    {{ scope.row.VistTime | dateFilter('YYYY-MM-DD') }}
                </template>
                <template slot='EmployeeList' slot-scope="scope">
                    <template v-if="scope.row.EmployeeList && scope.row.EmployeeList.length > 0">
                        {{ scope.row.EmployeeList.map(s => s.Name).join('、') }}
                    </template>
                    <template v-else>无</template>
                </template>
                <template slot='Opt' slot-scope="scope">
                    <app-table-row-button @click="handleReturnVisitDialog('detail', scope.row)" :type="2"></app-table-row-button>
                    <app-table-row-button v-if="isOwner(scope.row)" @click="handleReturnVisitDialog('update', scope.row)" :type="1"></app-table-row-button>
                    <app-table-row-button v-if="isOwner(scope.row)" @click="handleDelVisitDialog(scope.row)" :type="3"></app-table-row-button>
                </template>
            </app-table-core>

          </div>
          <div class="pagintion-wrapper">
            <pagination
              :total="totalHistory"
              small
              background
              :page.sync="listQueryHistory.PageIndex"
              :size.sync="listQueryHistory.PageSize"
              @pagination="handleCurrentChangeHistory"
              layout="prev, pager, next"
              :pager-count="5"
            />
          </div>
        </div>
    </el-drawer> -->


    <record v-if="dialogRecordFormVisible && checkedRegional.RegionalId" :regionalId='checkedRegional.RegionalId' :listQueryParams='listQueryAccessRecord' :currentId='selectedId' @closeDialog="closeRecordDialog" :dialogFormVisible="dialogRecordFormVisible"></record>

        <!-- 调整详情 -->
        <visit-site-set
            v-if="dialogVisitSiteSetVisible" @reload="visitSiteSetReload"
            @closeDialog="dialogVisitSiteSetVisible=false"
            :dialogFormVisible="dialogVisitSiteSetVisible"></visit-site-set>

 <!-- 导出 -->
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>

</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import currMixins from './mixins'
import * as equipmentParameter from "@/api/afterSalesMgmt/equipmentParameter";
import * as salesAfterVist from "@/api/afterSalesMgmt/salesAfterVist";
import * as salesAfterQuestion from "@/api/afterSalesMgmt/salesAfterQuestion";
import * as regionalBusinessRelation from "@/api/afterSalesMgmt/regionalBusinessRelation";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import addDeviceParams from "./addDeviceParams";
import addQuestion from "./addQuestion";
import assignQuestion from "./assignQuestion";
import addReturnVisit from './addReturnVisit'
import regionalDetail from '../../systemManagement/regionalManagement/detail'
import setting from './setting'
import noData from "@/views/common/components/noData"
import { vars } from "./enum";
import { getUserInfo } from "@/utils/auth";
import record from './record'
import { listToTreeSelect, treeFilter } from '@/utils'
import visitSiteSet from './visitSiteSet'
import vExport from "@/components/Export/index";

// const types = [
//     {value: 1, label: '没有'},
//     {value: 2, label: '有'}
// ]

export default {
    name: "",
    mixins: [indexPageMixin, currMixins, tabDynamicHeightMixins],
    components: {
        addDeviceParams,
        addReturnVisit,
        vAreaChoose,
        regionalDetail,
        setting,
        noData,
        record,
        visitSiteSet,
        addQuestion,
        assignQuestion,
        vExport,
    },
    watch: {
        currentregionalId: {
            handler(val) {
                if(val) {
                    this.getPageList()
                }
            },
            immediate: true
        },
        activeTab: {
            handler(val) {
                this.getPageList()
                this._doLayout(val)
            },
            immediate: true
        },

        'listQueryQuestion.GroupType'() {
            this.getPageList()
        },
    },
    computed: {
        // checkedRegional() {
        //     if(this.currentregionalId) {
        //         return this.regionalList.find(s => s.value == this.currentregionalId)
        //     }
        //     return {}
        // },
        hasTreeOpertAuth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnMaintain') > -1
        },
        hasReturnVisitRemin() {
            return this.topBtns.findIndex(s => s.DomId == 'btnReturnVisitRemin') > -1
        },
        hasExport() {
            return this.topBtns.findIndex(s => s.DomId == 'btnExport') > -1
        },
        hasbtnAddRecord() {
            return this.topBtns.findIndex(s => s.DomId == 'btnAddRecord') > -1
        },
        // hasbtnAddQuestion() {
        //     return this.topBtns.findIndex(s => s.DomId == 'btnAddQuestion') > -1
        // },
        hasbtnAdd() {
            return this.topBtns.findIndex(s => s.DomId == 'btnAdd') > -1
        },
        checkedList() {
            return this.regionalList && this.regionalList.map(s => s.RegionalId) || [];
        }
    },
    created() {
        this.getRegionList()
    },
    filters: {
        valFilter(val) {
            if (val) {
                return val;
            }
            return "无";
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "无";
        },
        namesFilter(list) {
            if(list && list.length > 0) {
                return list.map(s => s.Name).join(',')
            }
            return ''
        },
        // typeFilter(val) {
        //     let obj = types.find(s => s.value == val)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return '无'
        // },
        equipmentModelFilter(val) {
            let obj = vars.types.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return '无'
        },
        startOrStopFurnaceFilter(val) {
            let obj = vars.startOrStopFurnaceType.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return '无'
        },
        salesAfterVisttTypeFilter(val) {
            let obj = vars.salesAfterVisttTypes.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    mounted() {},
    data() {
        return {
            defaultProps: {
                children: "children",
                label: "Name"
            },
            equipmentModelTypes: vars.types,
            notReturnVisitType: vars.notReturnVisitType,
            startOrStopFurnaceType: vars.startOrStopFurnaceType,
            questionStatus: vars.questionStatus,
            layoutMode: 'simple',
            /**树筛选内容 */
            filterText: "",
            regionListLoading: false,
            regionListQuery: {
                PageIndex: 1,
                PageSize: 10000,
                SortType: 1,
                Range: [],
                NotReturnVisit: null,
                EquipmentModel: null,
                WhetherHosting: null,
                IsStationControlSystem: null,
            },

            id: "",
            dialogDeviceParamsStatus: "create",
            dialogDeviceParamsFormVisible: false,

            dialogReturnVisitStatus: "create",
            dialogReturnVisitFormVisible: false,

            dialogSettingFormVisible: false,
            dialogSettingStatus: "create",

            dialogExportVisible: false,
            rData: null,
            cData: [],


            dialogVisitSiteSetVisible: false,
            total: 0,
            listLoading: false,
            listQuery: {
                Keywords: '',
                RegionalBusinessRelationId: null,
            },
            multipleSelection: [],
            tableSearchItems: [
                {
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },


            ],
            
            tabDatas: [],
            tabColumns: [
                {
                    attr: {
                        prop: "Name",
                        label: '加热炉',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Usage",
                        label: "用途",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "EquipmentModel",
                        label: "类型",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "WhetherHosting",
                        label: "是否托管",
                        showOverflowTooltip: true,
                        sortable: 'custom'
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "PLCType",
                        label: "PLC型号",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "StartOrStopFurnace",
                        label: "启炉/停炉",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "Manufacturers",
                        label: "厂家",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "站点信息",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Opt",
                        label: "操作",
                    },
                    slot: true
                },
            ],

            //指派
            dialogAssignQuestionFormVisible: false,

            dialogQuestionStatus: 'create',
            dialogQuestionFormVisible: false,
            questionId: '',
            tabColumnsQuestion: [
                {
                    attr: {
                        prop: "Idx",
                        label: '序号',
                        showOverflowTooltip: true,
                        width: '50'
                    },
                    slot: true,
                    
                },
                {
                    attr: {
                        prop: "Title",
                        label: '问题标题',
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "Describe",
                        label: "问题描述",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "SalesAfterQuestionTypeName",
                        label: "问题类型",
                        showOverflowTooltip: true,
                    },
                },
                {
                    attr: {
                        prop: "SalesAfterQuestionStatus",
                        label: "状态",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "CreateEmployee",
                        label: "创建人",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ProcessEmployeeList",
                        label: "处理人",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "CreateTime",
                        label: "创建时间",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ProcessTime",
                        label: "处理时间",
                        showOverflowTooltip: true,
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "站点信息",
                        showOverflowTooltip: true,
                    },
                },
                
            ],
            tabDatasQuestion: [],
            listLoadingQuestion: false,
            totalQuestion: 0,
            listQueryQuestion: {
                PageSize: 20,
                PageIndex: 1,
                GroupType: 1
            },
            tabsOfTab3: [
                {value: 1, label: '待我处理', total: 0},
                {value: 2, label: '我创建的', total: 0},
                {value: 0, label: '全部', total: 0},
            ],
            
            listLoadingAccessRecord: false,
            listQueryAccessRecord: {
                PageSize: 20,
                PageIndex: 1
            },
            totalAccessRecord: 0,
            tabDatasAccessRecord: [],
            selectedId: '',//相中详情id
            tabColumnsAccessRecord: [
                {
                    attr: {
                        prop: "VistTime",
                        label: '回访时间',
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "EmployeeList",
                        label: "回访人",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "SalesAfterVisttType",
                        label: "回访类型",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "TotalTimeEmployeeList",
                        label: "总耗时",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "站点信息",
                        showOverflowTooltip: true,
                    },
                },
            ],

            oldRegionalList: [], //原始的地区列表，没有转换为树结构的数据
            regionalList: [],
            currentregionalId: '',
            checkedRegional: {},
            /**
             * 地区选择器
             */
            dialogRegionFormVisible: false, //地区选择器弹框

            dialogRegionalFormVisible: false,


            drawer: false,
            totalHistory: 0,
            // loadingHistor: false,
            // historeTabColumns: [
            //     {
            //       attr: { prop: "VistTime", label: "回访时间", width: '120', sortable: 'custom' },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "EmployeeList", label: "回访人", showOverflowTooltip: true },
            //       slot: true
            //     },
            //     {
            //       attr: { prop: "Opt", label: "操作", width: '140' },
            //       slot: true
            //     },
                
            // ],
            // histories: [],
            // listQueryHistory: {
            //     PageIndex: 1,
            //     PageSize: 20
            // },
            
            dialogRecordFormVisible: false,

            activeTab: '1',
            tabs: [
                {label: '回访记录列表', value: '1'},
                {label: '设备列表', value: '2'},
                {label: '问题列表', value: '3'},
            ],

        };
    },
    methods: {
   /**导出 */
    handleExport() {
      this.dialogExportVisible = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData.RegionalId = this.checkedRegional.RegionalId;
    
      this.rData={
         "exportSource":32,
         "columns":[],
         "searchCondition":postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'站点名称',
        value:'RegionalName'
      },{
        label:'电话',
        value:'RegionalMobile'
      },
      {
        label:'站长',
        value:'RegionalContact'
      },
      {
        label:'地区电话',
        value:'RegionalPhone'
      },{
        label:'回访时间',
        value:'VistTime'
      },
      {
        label:'回访类型',
        value:'SalesAfterVisttTypeString'
      },{
        label:'回访人',
        value:'EmployeeString'
      },{
        label:'模板内信息',
        value:'SalesAfterVistContent'
      },
      
      ]
    },

    handleSuccessExport() { },
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
        
        hasDisabled(id) {
            if(this.oldRegionalList) {
                let regObj = this.oldRegionalList.find(s => s.Id == id)
                if(regObj) {
                    return !regObj.AfterSalesRegional
                }
            }
            return true
        },
        // halfHeight(height) {
        //     if(height > 0) {
        //         return Math.floor(height / 2)
        //     }
        //     return 0
        // },
        handleCheckedNode(data) {
            this.currentregionalId = data.value
            this.checkedRegional = data
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        visitSiteSetReload(){
            this.getRegionList()
        },
        getPageList() {
            let val = this.activeTab
            if(val == '1') {
                this.getListAccessRecord()
            }else if(val == '2') {
                this.getList()
            }else if(val == '3') {
                this.getListQuestion()
                this.getStatistics()
            }
        },

        
        getQuestionStatusObj(val) {
            return this.questionStatus.find(s => s.value == val) || {}
        },
        getStartOrStopFurnaceObj(val) {
            return this.startOrStopFurnaceType.find(s => s.value == val) || {}
        },
        handleRecordDialog(row) {
            if(row) {
                this.selectedId = row.Id
            }
            this.dialogRecordFormVisible = true
        },
        closeRecordDialog() {
            this.dialogRecordFormVisible = false
        },

        /**
         * 地区选择器弹框
         */
        handleRegionDialog(row, optType = "update") {
            // this.dialogRegionFormVisible = true;
            this.dialogVisitSiteSetVisible = true;
        },
        closeRegionDialog() {
            this.dialogRegionFormVisible = false;
        },
        handleRegionSaveSuccess(rows) {
            let postDatas = {
                RegionalIdList: rows.map(s => s.Id) || [],
                RegionalBusinessType: 1
            }
            regionalBusinessRelation.edit(postDatas).then(res => {
                this.getRegionList()
            })
            
            // if (rows && rows.length > 0) {
            //     // let list = rows.map(s => {
            //     //     return {
            //     //         RegionalId: s.Id,
            //     //         RegionalName: s.ParentName,
            //     //         EmployeeIdList: [],
            //     //         //之前模板和地区关联，所以添加地区后，需要导入模板
            //     //         //现在改为和工程关联，为了不改变太多逻辑，所以直接设置工程、地区为相同模板
            //     //         ImplementationTemplateId: this.implementationTemplateId 
            //     //     };
            //     // });
            // }else{
            //     this.regionalList = [];
            // }
            this.closeRegionDialog();
        },
        handleResearch() {
            this.regionListQuery = Object.assign({}, this.regionListQuery, {
                Range: [],
                NotReturnVisit: null,
                EquipmentModel: null,
                WhetherHosting: null,
                IsStationControlSystem: null,
            })
            this.getRegionList()
        },
        regionListSort(sortType) {
            this.regionListQuery.SortType = sortType
            this.getRegionList()
        },
        getRegionList() {
            let _this = this
            let postDatas = JSON.parse(JSON.stringify(_this.regionListQuery))
            postDatas.RegionalBusinessType = 1
            postDatas.Keywords = _this.filterText
            if(postDatas.Range && postDatas.Range.length == 2) {
                postDatas.StartTime = postDatas.Range[0]
                postDatas.EndTime = postDatas.Range[1]
            }
            delete postDatas.Range

            _this.regionListLoading = true
            regionalBusinessRelation.getTreeListPage(postDatas).then(res => {

                _this.regionListLoading = false

                this.oldRegionalList = JSON.parse(JSON.stringify(res.Items))

                let list = res.Items.map(s => {
                    s.value = s.Id
                    s.label = s.RegionalName
                    s.Id = s.Id
                    s.Name = s.RegionalName
                    return s
                })

                list = listToTreeSelect(list, undefined, undefined,{
                            key: 'RegionalId',
                            parentKey: 'ParentId'
                        }, 'Name'); 

                _this.regionalList = list || []

                //如果当前 currentregionalId 为空（首次加载）
                if(list && list.length > 0 && !_this.currentregionalId) {
                    _this.checkedRegional = list[0]
                    _this.currentregionalId = list[0].value
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.currentregionalId);
                        }
                    });
                    //如果当前地区（currentregionalId）被删除——不存在 list 中
                } else if(res.Items && res.Items.length > 0 && !res.Items.map(s => s.value).find(s => s == _this.currentregionalId)){
                    _this.checkedRegional = {}
                    _this.currentregionalId = ''
                } else {
                    if(_this.currentregionalId) {
                        _this.$nextTick(() => {
                            if(_this.$refs.treeRef) {
                                _this.$refs.treeRef.setCurrentKey(_this.currentregionalId);

                                //编辑地区后，需要刷新选中的地区
                                let currReg = _this.oldRegionalList.find(s => s.Id == _this.currentregionalId)
                                if(currReg) {
                                    currReg.value = currReg.Id
                                    currReg.label = currReg.RegionalName
                                    currReg.Id = currReg.Id
                                    currReg.Name = currReg.RegionalName
                                    _this.checkedRegional = currReg
                                }
                            }
                        });
                    }
                }
            }).catch(err => {
                _this.regionListLoading = false
            })
        },
        // getRegionList() {
        //     let _this = this
        //     let postDatas = JSON.parse(JSON.stringify(_this.regionListQuery))
        //     postDatas.RegionalBusinessType = 1
        //     postDatas.Keywords = _this.filterText
        //     if(postDatas.Range && postDatas.Range.length == 2) {
        //         postDatas.StartTime = postDatas.Range[0]
        //         postDatas.EndTime = postDatas.Range[1]
        //     }
        //     delete postDatas.Range

        //     _this.regionListLoading = true
        //     regionalBusinessRelation.getList(postDatas).then(res => {

        //         _this.regionListLoading = false
        //         let list = res.Items.map(s => {
        //             s.value = s.Id
        //             s.label = s.RegionalName
        //             return s
        //         })

        //         _this.regionalList = list || []

        //         //如果当前 currentregionalId 为空（首次加载）
        //         if(list && list.length > 0 && !_this.currentregionalId) {
        //             _this.currentregionalId = list[0].value
        //         //如果当前地区（currentregionalId）被删除——不存在 list 中
        //         }else if(list && list.length > 0 && !list.map(s => s.value).find(s => s == _this.currentregionalId)){
        //             _this.currentregionalId = ''
        //         }
        //     }).catch(err => {
        //         _this.regionListLoading = false
        //     })
        // },
        handleBeforeOpen() {
            this.$refs.tagSelectorRef.pers = []
            return true
        },

        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId == 'btnAdd')
            }
            return []
        },



        

        resetSearch() {
            this.listQuery = {
                PageIndex: this.listQuery.PageIndex,
                PageSize: this.listQuery.PageSize,
                RegionalBusinessRelationId: this.listQuery.RegionalBusinessRelationId,
                Keywords: "",
            };
            this.getList();
        },

        handleSuccessExport() {},

        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                case "btnAddRecord":
                    this.handleReturnVisitDialog("create");
                    break;
                case "btnAddQuestion":
                    this.handleDialogQuestion("create");
                    break;
                case "btnExport":
                    this.handleExport();
                    break;
                default:
                    break;
            }
        },
        handleReturnVisitDialog(activeName, row) {
            if(row) {
                this.id = row.Id
            }
            this.dialogReturnVisitStatus = activeName;
            this.dialogReturnVisitFormVisible = true;
        },
        closeReturnVisitDialog() {
            this.dialogReturnVisitFormVisible = false;
        },
        handleReturnVisitSaveSuccess(_formData, isContinue) {
            this.getRegionList()
            // this.getList()
            // xxxxxxxxxxxxx
            this.getListAccessRecord()
            // this.getHistory()
            if(!isContinue) {
                this.closeReturnVisitDialog();
            }
        },

        handleDialog(activeName, row) {
            if(row) {
                this.id = row.Id
            }
            this.dialogDeviceParamsStatus = activeName;
            this.dialogDeviceParamsFormVisible = true;
        },
        closeDeviceParamsDialog() {
            this.dialogDeviceParamsFormVisible = false;
        },
        handleDeviceParamsSaveSuccess(_formData, isContinue) {
            // this.listQuery.PageIndex = 1
            this.getRegionList()
            this.getList();
            if(!isContinue) {
                this.closeDeviceParamsDialog();
            }
        },
        handleSettingDialog() {
            this.dialogSettingFormVisible = true
        },
        closeSettingDialog() {
            this.dialogSettingFormVisible = false
        },
        handleSettingSaveSuccess() {
            this.closeSettingDialog()
        },
        // handleOpenDrawer() {
        //     this.drawer = true
        // },
        /**
         * 指派
         */
        handleAssignQuestionSaveSuccess() {
            // this.listQuery.PageIndex = 1
            this.getRegionList()
            this.getListQuestion();
            this.getStatistics()
            this.closeAssignQuestionDialog();
        },
        closeAssignQuestionDialog() {
            this.dialogAssignQuestionFormVisible = false
        },
        handleAssignQuestion(row) {
            this.questionId = row.Id
            this.dialogAssignQuestionFormVisible = true
        },

        handleDelQuestion(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                salesAfterQuestion.del([row.Id]).then(() => {
                    this.getListQuestion();
                    this.getStatistics()
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                });
            });
        },
        handleUpdateQuestion(row, optType = "update") {
            // 弹出编辑框
            this.questionId = row.Id;
            this.dialogQuestionStatus = optType;
            this.dialogQuestionFormVisible = true;
        },
        handleDialogQuestion(activeName, row) {
            if(row) {
                this.questionId = row.Id
            }
            this.dialogQuestionStatus = activeName;
            this.dialogQuestionFormVisible = true;
        },
        closeQuestionDialog() {
            this.dialogQuestionFormVisible = false;
        },
        handleQuestionSaveSuccess(_formData, isContinue) {
            // this.listQuery.PageIndex = 1
            this.getRegionList()
            this.getListQuestion();
            this.getStatistics()
            if(!isContinue) {
                this.closeQuestionDialog();
            }
        },

        handleCurrentChangeQuestion(val) {
            this.listQueryQuestion.PageIndex = val.page;
            this.listQueryQuestion.PageSize = val.size;
            this.getListQuestion();
        },
        handleSizeChangeQuestion(val) {
            this.listQueryQuestion.PageSize = val.size;
            this.getListQuestion();
        },
        getStatistics() {
            if(this.checkedRegional) {
                let postDatas = {
                    RegionalId: this.checkedRegional.RegionalId,
                    // regionalBusinessRelationId: this.checkedRegional.value
                }
                salesAfterQuestion.statistics(postDatas).then(res => {
                    this.tabsOfTab3.forEach(t => {
                        if(t.value == 1) {
                            t.total = res.MyProcessCount
                        }else if(t.value == 2) {
                            t.total = res.MyCreatCount
                        }else{
                            t.total = res.AllCount
                        }
                    });
                })
            }
        },
        getListQuestion() {
            if(this.checkedRegional) {
                let postData = JSON.parse(JSON.stringify(this.listQueryQuestion))
                // postData.RegionalBusinessRelationId = this.checkedRegional.value

                //通过选中的节点id（节点id、地区组件id），找到节点id
                // let regObj = this.oldRegionalList.find(s => s.Id == this.checkedRegional.value)
                // if(regObj) {
                // }
                // postData.RegionalBusinessRelationId = this.checkedRegional.value
                postData.RegionalId = this.checkedRegional.RegionalId
                if(postData.GroupType <= 0) {
                    delete postData.GroupType
                }
                this.listLoadingQuestion = true
                salesAfterQuestion.getList(postData).then(res => {
                    this.listLoadingQuestion = false
                    this.tabDatasQuestion = res.Items
                    this.totalQuestion = res.Total
                    this._doLayout(3)
                }).catch(err => {
                    this.listLoadingQuestion = false
                })
            }
        },
        //是否为创建人
        isCreator(row) {
            if(row && row.CreateEmployee) {
                let currentUserId = getUserInfo().employeeid
                return row.CreateEmployee.EmployeeId == currentUserId
            }
            return false
        },
        //是否为处理人
        isHandlor(row) {
            if(row && row.ProcessEmployeeList) {
                let currentUserId = getUserInfo().employeeid
                return row.ProcessEmployeeList.map(s => s.EmployeeId).findIndex(s => s == currentUserId) > -1
            }
            return false
        },
        
        handleCurrentChangeAccessRecord(val) {
            this.listQueryAccessRecord.PageIndex = val.page;
            this.listQueryAccessRecord.PageSize = val.size;
            this.getListAccessRecord();
        },
        handleSizeChangeAccessRecord(val) {
            this.listQueryAccessRecord.PageSize = val.size;
            this.getListAccessRecord();
        },
        getListAccessRecord() {
            if(this.checkedRegional) {
                let postData = JSON.parse(JSON.stringify(this.listQueryAccessRecord))
                // postData.RegionalBusinessRelationId = this.checkedRegional.value

                //通过选中的节点id（节点id、地区组件id），找到节点id
                let regObj = this.oldRegionalList.find(s => s.Id == this.checkedRegional.value)
                if(regObj) {
                    postData.RegionalId = regObj.RegionalId
                }
                this.listLoadingAccessRecord = true
                salesAfterVist.getList(postData).then(res => {
                    this.listLoadingAccessRecord = false
                    this.tabDatasAccessRecord = res.Items
                    this.totalAccessRecord = res.Total

                    this._doLayout(1)
                    
                }).catch(err => {
                    this.listLoadingAccessRecord = false
                })
            }
        },
        _doLayout(idx) {
            this.$nextTick(() => {
                this.setTabHeight()
                this.$refs[`mainTable${idx}`].doLayout()
            })
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },

        // handleNav(type, proj) {
        //   this.$router.push({
        //     path: `/projectDev/projectMgmt/${type}/index?productId=${proj.productId}&projectId=${proj.projectId}`
        //   });
        // },
        isOwner(row) {
            if(row && row.EmployeeList) {
                let currentUserId = getUserInfo().employeeid
                return row.EmployeeList.map(s => s.EmployeeId).findIndex(s => s == currentUserId) > -1
            }
            return false
        },
        handleUpdate(row, optType = "update") {
            // 弹出编辑框
            this.id = row.Id;
            this.dialogDeviceParamsStatus = optType;
            this.dialogDeviceParamsFormVisible = true;
        },
        // handleResetSearch() {
        //     this.listQuery = {
        //         // 否则手动重置查询条件
        //         PageIndex: this.listQuery.PageIndex,
        //         PageSize: this.listQuery.PageSize,
        //         Name: "",
        //     };
        //     this.getList(); //刷新列表
        // },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        // handleSortChange2({
        //     column,
        //     prop,
        //     order
        // }) {
        //     this.sortObj = {
        //         prop,
        //         order,
        //     };
        //     // this.getHistory();
        // },
        //获取项目列表
        getList() {
            
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            
            // if(this.checkedRegional) {
            //     postData.RegionalBusinessRelationId = this.checkedRegional.value
            // }
            postData = this.assignSortObj(postData);

            if(!this.checkedRegional.value) {
                return false
            }

            //通过选中的节点id（节点id、地区组件id），找到节点id
            let regObj = this.oldRegionalList.find(s => s.Id == this.checkedRegional.value)
            if(regObj) {
                postData.RegionalId = regObj.RegionalId
            }

            this.listLoading = true;
            equipmentParameter.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
                this._doLayout(2)
            });
        },
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                equipmentParameter.del([row.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                    this.getList();
                });
            });
        },
        handleDelVisitDialog(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                salesAfterVist.del([row.Id]).then(() => {
                    this.getRegionList()
                    // this.getHistory();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000,
                    });
                });
            });
        },
        handleRegionalDialog() {

            this.dialogRegionalFormVisible = true
        },
        handleCurrentChangeHistory(val) {
            this.listQueryHistory.PageIndex = val.page;
            this.listQueryHistory.PageSize = val.size;
            // this.getHistory();
        },
        // getHistory() {
        //     if(!this.checkedRegional) {
        //         return false
        //     }

        //     let postData = JSON.parse(JSON.stringify(this.listQueryHistory))
        //     postData.RegionalBusinessRelationId = this.checkedRegional.value
        //     postData = this.assignSortObj(postData);
        //     this.loadingHistor = true
        //     salesAfterVist.getList(postData).then(res => {
        //         this.loadingHistor = false
        //         this.histories = res.Items
        //         this.totalHistory = res.Total
        //     }).catch(err => {
        //         this.loadingHistor = false
        //     })

        // },
        closeRegionalDialog() {
            this.dialogRegionalFormVisible = false
        },
        handleRegionalSaveSuccess(){
            this.getRegionList()
        },
    },
};
</script>

<style scoped>
.box-card {
  overflow: visible;
  margin: 10px;
  /* margin-bottom: 20px; */
}
.box-card >>> .el-card__body {
  padding: 10px;
  padding-bottom: 0;
}
/* .box-card >>> .el-card__header {
  background: #f3f2f2;
} */
</style>

<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            // top: 40px;
            top: 0;
            right: 0;
            bottom: 0;

            .product-list {
                width: 250px;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }
                .opts-wrapper{
                    padding: 0 10px;
                    display: flex;
                    margin-bottom: 6px;
                    .main-btn{
                        flex: 1;
                    }
                    >div:not(:first-child) {
                        margin-left: 20px;
                    }
                    
                }
                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        height: 100%;
                        overflow: auto;
                        ::v-deep.el-tree-node{
                            display: flex;
                            flex-direction: column;
                        }
                        ::v-deep.el-tree-node__content{
                            height: auto;
                            // max-height: 40px!important;
                            // display: flex;
                        }
                    }

                    .tagsList{
                        padding-left: 10px;
                        .item-warpper{
                            .item-title-wrapper{
                                margin-bottom: 6px;
                                display: flex;
                                .item-title{
                                }
                                .num{
                                    flex-shrink: 0;
                                }
                            }
                            // >div:not(:last-child) {
                            // }
                        }
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 200px);
                flex: 1;
                overflow-y: auto;
                /deep/.box-card-sp{
                    padding-bottom: 10px;
                    .title{
                        font-weight: 700;
                    }
                    .title-wrapper{
                        display: flex;
                        align-items: center;
                        padding-right: 10px;
                        .title{
                            flex: 1;
                        }
                    }
                }
                /deep/.el-tabs__header{
                    margin-bottom: 10px;
                }
                .content {

                    // padding: 10px;
                    // padding-right: 0;
                    .opt-wrapper {
                        box-sizing: border-box;
                        border-bottom: 1px solid #dcdfe6;
                        padding-bottom: 10px;
                    }

                    .list {}
                }
            }
        }
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}

.statisticsDivClass {
    width: 100%;
    height: 75px;
    border-radius: 8px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
    cursor: pointer;

    .statisticsChildDiv {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px 0 0 8px;
        width: 26%;
        height: 75px;
        background-color: rgba(255, 108, 96, 1);
        float: left;
    }

    .statisticsChildDiv2 {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 0 8px 8px 0;
        width: 74%;
        height: 75px;
        background-color: rgba(255, 255, 255, 1);
        float: left;

        .label1 {
            color: #999999;
            margin: 0;
            margin-top: 12px;
            font-weight: 500 !important;
        }

        div {
            margin-top: 5px;
        }

        .label2 {
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            font-size: 20px;
            color: $text-second-color;
            margin: 0;
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

.statisticsDivClass:hover {
    transform: translate(-3px, -3px);
    box-shadow: 0px 0px 3px 0px #dcdfe6;
}


.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

// .detail-row1{
//     display: flex;
//     align-items: center;
//     height: 30px;
// }

// .detail-row1{
//     >div{
//         flex: 1;
//         padding: 0 10px;
//     }
//     >div:first-child{
//         border-right:1px solid #dcdfe6;
//     }
//     >div:last-child{
//         flex: 0;
//         margin-right: 10px;
//         width: 60px;
//     }
// }



// .elTimeBox {
//   .history-list{
//     overflow-y: auto;
//     padding-bottom: 60px;
//   }
//   .pagintion-wrapper{
//     position: absolute;
//     bottom: 0;
//     left: 0;
//     right: 0;
//     height: 46px;
//     display: flex;
//     align-items: center;
//     width: 100%;
//     border-top: 1px solid #dcdfe6;
//   }
// }

.screeningBox {
  li {
    line-height: 28px;
    margin-bottom: 8px;
  }
}

.sort-item{
    position: relative;
    i{
        position: absolute;
        right: 0;
        top: 5px;
    }
}


</style>
