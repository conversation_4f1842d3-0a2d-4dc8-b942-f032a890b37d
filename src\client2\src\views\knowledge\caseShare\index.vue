<template>
    <!-- 案例分享 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="true"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="190"
            :loading="listLoading"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :startOfTable="startOfTable"
            @sortChagned="handleSortChange">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'120px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索标题名称/提交人" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>
                        <!-- 可见范围 -->
                        <template slot="ApprovalStatus">
                            <el-select v-model="listQuery.ApprovalStatus" placeholder="请选择">
                                <el-option v-for="item in ApprovalStatusEnum.filter(s=>s.value!=4)" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <!-- 是否转为课程 -->
                        <template slot="IsTransitionCourse">
                            <el-select v-model="listQuery.IsTransitionCourse" placeholder="请选择">
                                <el-option v-for="item in IsTransitionCourseEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <!-- 提交时间 -->
                        <template slot="CreateTime">
                            <el-date-picker style="width: 180px;" v-model="listQuery.CreateTime" type="date" align="right" format="yyyy-MM-dd" placeholder="请选择"
                            value-format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                        </template>
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                        </template>
                    </app-table-form>
                </template>
                <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD') }}</template>
                <template slot="ApprovalStatus" slot-scope="scope">
                    <!-- <span class="item-status" v-if="scope.row.ApprovalStatus"
                        :style="{backgroundColor: getApprovalStatusObj(scope.row.ApprovalStatus).color,color: getApprovalStatusObj(scope.row.ApprovalStatus).bgColor}"
                    >{{ getApprovalStatusObj(scope.row.ApprovalStatus).label }}</span> -->

                    <app-tag-pure effect="dark" v-if="scope.row.ApprovalStatus" :color="getApprovalStatusObj(scope.row.ApprovalStatus).color" :text="getApprovalStatusObj(scope.row.ApprovalStatus).label"></app-tag-pure>
                    <template v-else>无</template>
                </template>
                <template slot="IsTransitionCourse" slot-scope="scope">
                    <app-tag-pure effect="dark" :color="getIsTransitionCourseObj(scope.row.IsTransitionCourse).color" :text="getIsTransitionCourseObj(scope.row.IsTransitionCourse).label"></app-tag-pure>

                    <!-- <span class="item-status"
                        :style="{backgroundColor: getIsTransitionCourseObj(scope.row.IsTransitionCourse).color,color: getIsTransitionCourseObj(scope.row.IsTransitionCourse).bgColor}"
                    >{{ getIsTransitionCourseObj(scope.row.IsTransitionCourse).label }}</span> -->
                </template>
                <template slot="BonusPoints" slot-scope="scope">{{ scope.row.BonusPoints ? `+${scope.row.BonusPoints}` : '无'}}</template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 详情 -->
                    <app-table-row-button @click="handleUpdate(scope.row,'detail')" text="详情" :type="2"></app-table-row-button>
                    <!-- 转为课程 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnConvertCourse')&&scope.row.ApprovalStatus==2&&scope.row.IsTransitionCourse===0"
                    :type="1" text="转为课程" @click="handConvertCourse(scope.row)"></app-table-row-button>
                    <!-- 奖励积分 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnBonusPoints')&&scope.row.ApprovalStatus==2&&scope.row.BonusPoints===0"
                    :type="1" text="奖励积分" @click="handBonusPoints(scope.row)"></app-table-row-button>
                    
                    <!-- 删除 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnDel')&&scope.row.ApprovalStatus!=1" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                </template>
            </app-table>
        </div>
    </div>
    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogFormVisible" :id="selectId" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogFormVisible"
    @closeDialog="createDialogFormVisible=false" @saveSuccess="createSaveSuccess"></create-page>
    <!-- 类型/审批设置弹窗 -->
    <type-approve-page v-if="dialogTypeApproveVisible" :dialogFormVisible="dialogTypeApproveVisible" @closeDialog="dialogTypeApproveVisible=false" />
    <!-- 奖励积分 -->
    <bonus-points-page v-if="createBonusPointsVisible" :id="selectId" dialogStatus="create"
    :dialogFormVisible="createBonusPointsVisible" @closeDialog="createBonusPointsVisible=false" @saveSuccess="createBonusPointsSuccess"></bonus-points-page>

    
    <!-- 转为课程  -->
    <convert-course @closeDialog="dialogConvertCourseVisible=false" @saveSuccess="handleConvertCourseSuccess"
    :dialogFormVisible="dialogConvertCourseVisible" :dialogStatus="dialogConvertCourseStatus" :defaultRow="dialogConvertCourseRow"></convert-course>

</div>
</template>
<script>
import * as CaseShareApi from '@/api/knowledge/CaseShare'
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import typeApprovePage from "./typeApproveSet";
import bonusPointsPage from "./bonusPoints";
import convertCourse from "../train/create.vue";

import { vars } from '../common/vars'
import * as ApprovalVars from "@/views/projectDev/common/vars";
export default {
    name: 'case-share-index',
    mixins: [indexPageMixin],
    components: {
        createPage,
        typeApprovePage,
        bonusPointsPage,
        convertCourse
    },
    filters: {
        patentWorkTypeFilter(val) {
            let obj = vars.patentWorkTypes.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "";
        }
    },
    created() {
        this.getList();
    },
    data() {
        return {
            // 1 待审批 2 已审批 3 不通过 4 已撤销
            ApprovalStatusEnum: ApprovalVars.vars.approvalStatuObj.approvalStatus,
            IsTransitionCourseEnum: vars.caseShareEnum.IsTransitionCourseTypes,
            selectId: '',
            createDialogStatus: 'create',
            createDialogFormVisible: false,

            dialogTypeApproveVisible: false,

            createBonusPointsVisible: false,

            dialogConvertCourseStatus: 'create',
            dialogConvertCourseVisible: false,
            dialogConvertCourseRow: null,

            total: 0,
            listQuery: {
                CreateTime: ''
            },
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "CreateTime", label: "提交时间" },
                { prop: "ApprovalStatus", label: "审批状态" },
                { prop: "IsTransitionCourse", label: "是否转为课程" },
            ],
            multipleSelection: [],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "CaseName", label: "标题名称", showOverflowTooltip: true}},
                { attr: { prop: "SubmitEmployeeName", label: "提交人"} },
                { attr: { prop: "CreateTime", label: "提交时间", sortable: 'custom'}, slot: true },
                { attr: { prop: "ApprovalStatus", label: "审批状态", sortable: 'custom'}, slot: true },
                { attr: { prop: "IsTransitionCourse", label: "是否转课程", sortable: 'custom'}, slot: true },
                { attr: { prop: "BonusPoints", label: "奖励积分", sortable: 'custom'}, slot: true },
            ],
        }
    },
    methods: {
        getList() {
            let self = this;
            self.listLoading = true;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);

            CaseShareApi.getList(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 显示 类型/审批设置弹窗
        handleTypeApprove(){
            this.dialogTypeApproveVisible = true
        },
        // 表格顶部按钮点击事件
        onBtnClicked(domId) {
            switch (domId) {
                case "btnTypeApprove":
                    this.handleTypeApprove();
                    break;
                default:
                    break;
            }
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.selectId = optType === 'create' ? '' : row.Id;
            this.createDialogStatus = optType
            this.createDialogFormVisible = true
        },
        /** 编辑框 点确定后 关闭 并刷新列表 */
        createSaveSuccess(d) {
            // console.log('d',d)
            if (!d) {
                this.createDialogFormVisible = false
            }
            this.listQuery.PageIndex = 1;
            this.getList()
        },
        handBonusPoints(row){
            this.selectId = row.Id;
            this.createBonusPointsVisible = true
        },
        handConvertCourse(row){
            this.dialogConvertCourseRow = row;
            this.dialogConvertCourseVisible = true
        },
        /** 奖励积分 点确定后 关闭 并刷新列表 */
        createBonusPointsSuccess(d) {
            // console.log('d',d)
            if (!d) {
                this.createBonusPointsVisible = false
            }
            this.getList()
        },
        /** 转为课程 点确定后 关闭 并刷新列表 */
        handleConvertCourseSuccess(d) {
            this.dialogConvertCourseVisible = false
            CaseShareApi.TransitionCourse({
                Id: this.dialogConvertCourseRow.Id
            }).then(() => {
                this.listQuery.PageIndex = 1;
                this.getList()
            });
        },
        
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                CaseShareApi.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        getApprovalStatusObj(val) {
            return this.ApprovalStatusEnum.find(
                s => s.value == val
            ) || {};
        },
        getIsTransitionCourseObj(val) {
            return this.IsTransitionCourseEnum.find(
                s => s.value == val
            ) || {};
        },
        
    }
}
</script>
