<!--企业资质编辑-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="900"
    >
      <template slot="body">
        <el-row class="contentBox">
          <div class="contentBox_left">
            <el-form
              :rules="formRules"
              ref="formRef"
              :model="formModel"
              label-position="right"
              label-width="120px"
              v-loading="loading"
            >
              <el-form-item label="资质名称" prop="QualificationName">
                <div :title="editable ? '' : formModel.QualificationName">
                  <el-input
                    :disabled="!editable"
                    maxlength="100"
                    type="text"
                    v-model="formModel.QualificationName"
                  ></el-input>
                </div>
              </el-form-item>
              <el-form-item label="证书编号" prop="CertificateNumber">
                <div :title="editable ? '' : formModel.CertificateNumber">
                  <el-input
                    :disabled="!editable"
                    maxlength="50"
                    type="text"
                    v-model="formModel.CertificateNumber"
                  ></el-input>
                </div>
              </el-form-item>
              <el-form-item label="发证时间" prop="IssueDate">
                <el-date-picker
                  @change="computedDate"
                  :clearable="false"
                  :disabled="!editable"
                  v-model="formModel.IssueDate"
                  type="date"
                  align="right"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="有效期至" prop="EffectiveState" v-if="editable">
                <el-tag
                  v-for="btnItem in termOfValidityTypes"
                  :key="btnItem.value"
                  :effect="btnItem.value == formModel.EffectiveState ? 'dark' : 'light'"
                  :disabled="!editable"
                  :class="!editable ? 'isDisabled' : ''"
                  @click="chooseTag(btnItem)"
                >
                  {{ btnItem.label }}
                </el-tag>
              </el-form-item>
              <el-form-item label="是否年审">
                <el-switch
                  v-model="formModel.IsAnnual"
                  :active-value="true"
                  :inactive-value="false"
                  :disabled="!editable"
                />
              </el-form-item>
              <el-form-item label="有效日期" prop="EffectiveDate">
                <el-date-picker
                  clearable
                  @change="formModel.EffectiveState = 0"
                  :disabled="!editable"
                  v-model="formModel.EffectiveDate"
                  type="date"
                  align="right"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="备注" prop="Remark">
                <el-input
                  :disabled="!editable"
                  maxlength="500"
                  type="textarea"
                  :rows="5"
                  v-model="formModel.Remark"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="contentBox_right">
            <div class="panel-title">附件</div>
            <div>
              <app-uploader
                ref="appUploaderRef"
                :readonly="!editable"
                accept="all"
                :fileType="3"
                :max="10000"
                :value="formModel.AttachmentList"
                :fileSize="1024 * 1024 * 500"
                :minFileSize="100 * 1024"
                @change="handleFilesUpChange"
              ></app-uploader>
            </div>
          </div>
        </el-row>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button
          v-if="editable"
          :buttonType="1"
          :loading="buttonLoading"
          @click="handleButtonClick"
        ></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as enterpriseQualificationApi from "@/api/personnelManagement/enterpriseQualification";
import { vars } from "../common/vars.js";
import dayjs from "dayjs";

export default {
  /**名称 */
  name: "enterprise-qualification-edit",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
    },
    id: {
      type: String,
      default: "",
    },
  },
  /**数据区 */
  data() {
    return {
      goOn: false, // 是否继续添加
      termOfValidityTypes: vars.termOfValidityTypes,
      loading: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,

      /**表单模型 */
      formModel: {
        QualificationName: "", // 资质名称
        CertificateNumber: "", // 证书编号
        IssueDate: null, // 发证时间
        EffectiveDate: "", // 有效日期
        EffectiveState: 0, // 有效期至
        Remark: "", // 备注
        IsAnnual:false,
        AttachmentList: [],
      },
      /**表单规则 */
      formRules: {
        QualificationName: { fieldName: "资质名称", rules: [{ required: true }] },
        CertificateNumber: { fieldName: "证书编号", rules: [{ required: true }] },
        IssueDate: { fieldName: "发证时间", rules: [{ required: true }] },
      },
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建企业资质";
      } else if (this.dialogStatus == "update") {
        return "编辑企业资质";
      } else if (this.dialogStatus == "detail") {
        return "企业资质详情";
      }
    },
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail";
    },
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let self = this;
        self.formModel = self.$options.data().formModel;
        self.goOn = false;
        if (val && self.dialogStatus != "create" && self.id) {
          self.getDetail();
        }
      },
      immediate: true,
    },
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    // 计算有效日期
    computedDate() {
      if (this.formModel.IssueDate && this.formModel.EffectiveState) {
        let currentDate = this.formModel.IssueDate,
          lastNumber =
            this.termOfValidityTypes.find(s => s.value == this.formModel.EffectiveState).number ||
            0;
        this.formModel.EffectiveDate = dayjs(currentDate)
          .add(lastNumber, "year")
          .format("YYYY-MM-DD");
      }
    },
    // 选择 有效期至
    chooseTag(row) {
      if (this.editable) {
        this.formModel.EffectiveState =
          this.formModel.EffectiveState === row.value ? null : row.value;
        this.computedDate();
      }
    },
    /**提交方法 */
    handleButtonClick() {
      let self = this;
      self.$refs.formRef.validate(valid => {
        if (valid) {
          //附件
          let formModel = JSON.parse(JSON.stringify(self.formModel));
          formModel["AttachmentIdList"] = formModel.AttachmentList.map(s => {
            return s.Id;
          });
          formModel.EffectiveState = formModel.EffectiveState ? formModel.EffectiveState : 0;
          let result = null;
          self.buttonLoading = true;

          if (this.dialogStatus == "create") {
            result = enterpriseQualificationApi.add(formModel);
          } else if (this.dialogStatus == "update") {
            result = enterpriseQualificationApi.edit(formModel);
          }
          result
            .then(response => {
              self.buttonLoading = false;
              self.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000,
              });
              if (self.goOn) {
                self.$refs.appUploaderRef.clearFiles();
                self.$refs.formRef.resetFields();
                self.formModel = self.$options.data().formModel;
              }
              console.log("goOn", self.goOn);
              self.$emit("saveSuccess", self.goOn);
            })
            .catch(err => {
              self.buttonLoading = false;
            });
        }
      });
    },
    getDetail() {
      this.loading = true;
      enterpriseQualificationApi
        .detail({ id: this.id })
        .then(response => {
          this.loading = false;
          this.formModel = Object.assign({}, this.formModel, response);
        })
        .catch(err => {
          this.loading = false;
        });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    handleFilesUpChange(files) {
      this.formModel.AttachmentList = files;
    },
  },
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.el-tag {
  cursor: pointer;
  + .el-tag {
    margin-left: 15px;
  }
  &.isDisabled {
    cursor: not-allowed;
  }
}
.contentBox {
  display: flex;
  &_left {
    flex: 1;
    padding-right: 10px;
  }
  &_right {
    width: 34%;
    border-left: 1px solid #eee;
    padding: 13px 5px;
  }
}
</style>
