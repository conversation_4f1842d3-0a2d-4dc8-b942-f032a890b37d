<template>
  <div>
    <div class="app-container">
      <div class="bg-white">
        <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns='tabAuthColumns' :isShowAllColumn='isShowAllColumn' :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn='rowBtns.length > 0' :startOfTable='startOfTable' :multable=false>
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='resetSearch'>
              <template slot='Name'>
                <el-input style="width: 100%;" v-model="listQuery.Keyword" placeholder=""></el-input>
              </template>
            </app-table-form>
          </template>

          <!-- 表格批量操作区域 -->
          <template slot="btnsArea">
            <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type='1'>
            </app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type='3'>
            </app-table-row-button>

            <!-- <app-table-row-button
              v-if="rowBtnIsExists('btnSetAdmin')"
              @click="handleSetAdmin(scope.row)"
              :type='2'
              :text="getRowBtnText('btnSetAdmin')"
            >
            </app-table-row-button> -->
          </template>
        </app-table>

        <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
      </div>

      <!-- 操作窗口 -->
      <el-dialog v-el-drag-dialog class="dialog-mini" width="650px" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal='false' :append-to-body='true'>
        <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="140px">
          <el-row>
            <el-col>
              <el-form-item :label="'组织机构代码'" prop="OrganizingInstitutionBarCode">
                <el-input maxlength="50" v-model="temp.OrganizingInstitutionBarCode" :disabled="editable" placeholder="格式: xxxxxxxx-x"></el-input>
              </el-form-item>

              <el-form-item :label="'企业名称'" prop="EnterpriseName">
                <el-input maxlength="50" v-model="temp.EnterpriseName" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'企业编号'" prop="EnterpriseCode">
                <el-input maxlength="20" v-model="temp.EnterpriseCode" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'企业别名'" prop="EnterpriseAlias">
                <el-input maxlength="4" v-model="temp.EnterpriseAlias" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'企业地址'" prop="EnterpriseAddress">
                <el-input maxlength="200" v-model="temp.EnterpriseAddress" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'成立日期'" prop="RegisterDate">
                <el-date-picker class="dat-ipt" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="temp.RegisterDate" type="date" placeholder="" :disabled="editable">
                </el-date-picker>
              </el-form-item>

              <el-form-item :label="'法定代表人'" prop="LegalPerson">
                <el-input maxlength="20" v-model="temp.LegalPerson" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'联系人'" prop="Contacts">
                <el-input maxlength="20" v-model="temp.Contacts" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'联系电话'" prop="ContactNumber">
                <el-input maxlength="15" v-model="temp.ContactNumber" :disabled="editable"></el-input>
              </el-form-item>

              <el-form-item :label="'企业邮箱'" prop="EnterpriseMailbox">
                <el-input maxlength="50" v-model="temp.EnterpriseMailbox" :disabled="editable" placeholder="格式: <EMAIL>"></el-input>
              </el-form-item>

              <el-form-item :label="'企业网站'" prop="EnterpriseWebsite">
                <el-input maxlength="50" v-model="temp.EnterpriseWebsite" :disabled="editable" placeholder="格式: http(s)://www.jiayuntong.com"></el-input>
              </el-form-item>

              <el-form-item :label="'统一社会信用代码'" prop="UnifiedSocialCreditCode">
                <el-input maxlength="50" v-model="temp.UnifiedSocialCreditCode" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
          <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认</el-button>
        </div>
      </el-dialog>

      <!-- 设置企业管理员 -->
      <!-- <el-dialog v-el-drag-dialog class="dialog-mini" width="650px" :loading="setAdminLoading" :title="textMap[dialogStatus]" :visible.sync="dialogSetAdminFormVisible" :close-on-click-modal='false' :append-to-body='true'>
        <el-form ref="dataForm" label-position="right" label-width="140px">
          <el-row>
            <el-col>
              <el-form-item :label="'当前企业管理员'">
                <el-input v-model="UserName" :disabled="true"></el-input>
              </el-form-item>

              <el-form-item label="重置企业管理员">
                <user-selector :condition='{EnterpriseID: this.enterpriseId,IsFilterNonEmployeeUser:true}' :disabled="true" :multiple="false" :showType="2" :list='checkedUsers' @change='handleChangeServiceUser'></user-selector>

              </el-form-item>

            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="cancelClick">取消</el-button>
          <el-button size="mini" type="primary" :loading="postLoading" :disabled="this.checkedUsers.length==0" @click="setAdminClick">确认</el-button>
        </div>
      </el-dialog> -->

    </div>
  </div>
</template>

<script>
import * as enterprise from "@/api/enterprise";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import { regs } from "@/utils/regs";
import UserSelector from "../common/userSelector";
export default {
  name: "position",
  components: {
    UserSelector
  },
  directives: {
    // waves,
    elDragDialog
  },
  mixins: [indexPageMixin],
  data() {
    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        };
      },
      multipleSelection: [],
      tableSearchItems: [{ prop: "Name", label: "关键字" }],
      tabColumns: [
        {
          attr: {
            prop: "OrganizingInstitutionBarCode",
            label: "组织机构代码",
                      }
        },
        {
          attr: { prop: "EnterpriseName", label: "企业名称", }
        },
        {
          attr: { prop: "EnterpriseCode", label: "企业编号", }
        },
        {
          attr: { prop: "EnterpriseAlias", label: "企业别名", }
        },

        {
          attr: {
            prop: "EnterpriseAddress",
            label: "企业地址",
            
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "RegisterDate",
            label: "成立日期",
            
            formatter: this.formatterDate
          }
        },
        {
          attr: { prop: "LegalPerson", label: "法定代表人", }
        },
        {
          attr: { prop: "Contacts", label: "联系人", }
        },
        {
          attr: { prop: "ContactNumber", label: "联系电话", }
        }
      ],
      tabDatas: [],
      listLoading: false,
      postLoading: false,
      listQuery: {
        // 查询条件
        Keyword: ""
      },
      total: 0,
      textMap: {
        update: "编辑",
        create: "添加",
        setAdmin: "设置管理员"
      },
      dialogFormVisible: false,
      rules: {
        OrganizingInstitutionBarCode: {
          fieldName: "组织机构代码",
          rules: [
            { required: true },
            { reg: regs.OrganizingInstitutionBarCode }
          ]
        },
        EnterpriseName: {
          fieldName: "企业名称",
          rules: [{ required: true }, { max: 50 }]
        },
        EnterpriseCode: {
          fieldName: "企业编号",
          rules: [{ required: true }, { reg: regs.LettersAndNumbers }, { max: 20 }]
        },
        EnterpriseAlias: {
          fieldName: "企业别名",
          rules: [{ required: true }, { max: 4 }]
        },
        EnterpriseMailbox: {
          fieldName: "企业邮箱",
          rules: [{ required: true }, { reg: regs.email }]
        },
        EnterpriseAddress: {
          fieldName: "企业地址",
          rules: [{ required: true }, { max: 200 }]
        },
        RegisterDate: { fieldName: "成立日期", rules: [{ required: true }] },
        LegalPerson: {
          fieldName: "法定代表人",
          rules: [{ required: true }, { max: 20 }]
        },
        Contacts: {
          fieldName: "联系人",
          rules: [{ required: true }, { max: 20 }]
        },
        ContactNumber: {
          fieldName: "联系电话",
          rules: [{ required: true }, { reg: regs.phone }]
        },
        UnifiedSocialCreditCode: {
          fieldName: "统一社会信用代码",
          rules: [{ required: false }, { reg: regs.UnifiedSocialCreditCode }]
        },
        EnterpriseWebsite: {
          fieldName: "企业网站",
          rules: [{ required: false }, { reg: regs.url }]
        }
      },
      temp: {
        EnterpriseName: ""
      },

      //设置企业管理员相关
      checkedUsers: [],
      setAdminLoading: false,
      dialogSetAdminFormVisible: false,
      UserName: "",
      enterpriseId: ""
    };
  },
  created() {
    this.rules = this.initRules(this.rules);
    this.getList();
  },
  mounted() { },
  methods: {
    resetTemp() {
      this.temp = {
        EnterpriseName: ""
      };
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    onBtnClicked: function (domId) {
      // console.log('you click:' + domId)
      switch (domId) {
        case "btnAdd":
          this.handleCreate();
          break;
        case "btnEdit":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行编辑",
              type: "error"
            });
            return;
          }
          this.handleUpdate(this.multipleSelection[0]);
          break;
        case "btnDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error"
            });
            return;
          }
          this.handleDelete(this.multipleSelection);
          break;
        case "btnDetail":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行查看",
              type: "error"
            });
            return;
          }
          this.handleUpdate(this.multipleSelection[0], "detail");
          break;
        default:
          break;
      }
    },
    getList() {
      this.listLoading = true;
      enterprise.getList(this.listQuery).then(response => {
        this.tabDatas = response.Items;
        this.total = response.Total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCreate() {
      // 弹出添加框
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      // 保存提交
      let self = this;
      self.postLoading = true;
      this.$refs["dataForm"].validate(valid => {
        if (!valid) {
          self.postLoading = false;
        }
        if (valid) {
          let formData = JSON.parse(JSON.stringify(self.temp));
          if (self.dialogStatus == "create") {
            delete formData.EnterpriseID;
          }

          let res = null;
          if (self.dialogStatus == "create") {
            res = enterprise.add(formData);
          } else if (self.dialogStatus == "update") {
            res = enterprise.edit(formData);
          }
          if (res) {
            res
              .then(response => {
                self.postLoading = false;
                self.dialogFormVisible = false;
                self.$notify({
                  title: "成功",
                  message: "创建成功",
                  type: "success",
                  duration: 2000
                });

                this.getList();
              })
              .catch(err => {
                self.postLoading = false;
              });
          }
        }
      });
    },
    handleUpdate(row, optType = "update") {
      // 弹出编辑框
      this.temp = Object.assign({}, row); // copy obj
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    handleDelete(rows) {
      // 多行删除
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.EnterpriseId);
      } else {
        ids.push(rows.EnterpriseId);
      }

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        enterprise.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    //提交 设置管理员
    setAdminClick() {
      let _this = this;
      var uId = this.checkedUsers[0].UserId;
      var eId = this.enterpriseId;
      enterprise
        .setAdministrator({ userId: uId, enterpriseId: eId })
        .then(res => {
          _this.$notify({
            title: "成功",
            message: "设置成功",
            type: "success",
            duration: 2000
          });
          _this.checkedUsers = [];
          _this.dialogSetAdminFormVisible = false;
        });
    },
    //取消设置管理员
    cancelClick() {
      this.dialogSetAdminFormVisible = false;
      this.checkedUsers = [];
      this.enterpriseId = "";
    },
    handleChangeServiceUser(users) {
      this.checkedUsers = users;
    },

    formatterDate(row, column) {
      let f = this.$options.filters["dateFilter"];
      return f(row.RegisterDate, "YYYY-MM-DD");
    }
  }
};
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}
</style>