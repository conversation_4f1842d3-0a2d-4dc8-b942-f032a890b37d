<template>
    <div class="avatar-warpper clearfix">
        <!-- <div v-if="showType == 1">
            <div class="avatar-item" v-for="(p, idx) in pers" :key="'p_' + idx"
                @mouseover="() => delIdx = idx"
                @mouseout="() => delIdx = -1"
            >
                <i class="btn-remove el-icon-remove"
                    v-if="!readonly"
                    v-show="delIdx == idx"
                    @click.stop="handleRemove(idx)"
                ></i>
                <div class="avatar">
                    <img :src="p.Avatar || defavatar" alt="">
                </div>
                <div class="username" :title="p.Name">{{ p.Name }}</div>
            </div>

            <div class="avatar-item">
                <div class="avatar">
                    <i class="el-icon-plus icon-plus" v-show="!readonly" @click="() => dialogAccessUsers = true"></i>
                </div>
                <div></div>
            </div>
        </div> -->
        <div>
            <el-input placeholder="" :value="names" :disabled='readonly' class="input-with-select">
                <i slot="suffix" v-show="!readonly" @click="handleClear" class="el-input__icon el-icon-close icon-close"></i>
                <div slot='append'>
                    <el-button :disabled='readonly' style="padding: 7px 12px;" icon="el-icon-more" @click="() => dialogAccessUsers = true"></el-button>
                </div>
            </el-input>
        </div>

        <el-dialog width="1000px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="'选择任务'" :visible.sync='dialogAccessUsers' :append-to-body='true'>
            <task-table :condition='{ProjectId: ProjectId}' ref="accessUser" v-bind="$attrs" :existsUsers='pers' :visible.sync='dialogAccessUsers' v-show="dialogAccessUsers" @changed='handleChangeUsers'></task-table>
        </el-dialog>
    </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import TaskTable from './taskTable'
export default {
    name: 'task-list',
    directives: {
      // waves,
      elDragDialog
    },
    components: {
        TaskTable,
    },
    props: {
        //已存在的人员
        list: {
            type: Array,
            default: () => {
                return []
            },
        },
        readonly: {
            type: Boolean,
            default: false
        },
        ProjectId: {
            type: String,
            default: ''
        }
        // showType: {
        //     type: Number,
        //     default: 1, //2：头像模式；2：文本框模式
        // },
    },
    computed: {
        names() {
            if(this.pers && this.pers.length > 0){
                return this.pers.map(p => p.TaskSn).join(',')
            }
            return ''
        }
    },
    data() {
        return {
            delIdx: -1,
            defavatar: require('../../assets/images/avatar.png'),
            pers: [],
            dialogAccessUsers: false,
        }
    },
    watch: {
        list: {
            handler(val) {
                this.pers = JSON.parse(JSON.stringify(val))
            },
            immediate: true
        },
    },
    mounted() {
        // this.pers = JSON.parse(JSON.stringify(this.list))
    },
    methods: {
        handleRemove(idx) {
            this.pers.splice(idx, 1)
            this.usersChanged()
        },
        handleChangeUsers(users) {
            this.pers = users
            this.usersChanged()
            this.dialogAccessUsers = false
        },
        usersChanged () {
            this.$emit("change", this.pers)
        },
        handleClear() {
            this.pers = []
            this.usersChanged()
        },
        handleShow() {
            
            this.dialogAccessUsers = true
        },
    }
}
</script>

<style scoped>


.avatar-warpper{

}

.avatar-warpper .avatar-item{
    position: relative;
    float: left;
    text-align: center;
    margin: 0 8px;
    width: 50px;
}

.avatar-warpper .avatar-item .avatar{
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: url(../../assets/images/avatar.png) no-repeat;
}

.avatar-warpper .avatar-item .avatar img{
    width: 50px;
    height: 50px;
}

.btn-remove{
    position: absolute;
    top: -5px; 
    right: -5px;
    cursor: pointer;
}

.btn-remove:hover {
    transition: all 0.3s;
    color: red;
}

.icon-plus{
    line-height: 50px;
    width: 50px;
    font-size: 24px;
    cursor: pointer;
    /* border: 1px solid red; */
}

.username{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.icon-close{
    cursor: pointer;
}

.dialog_wrapper >>> .el-dialog__body{
    padding-top: 15px;
}
</style>

