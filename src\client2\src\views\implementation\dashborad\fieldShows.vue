<template >
  <div>
    <app-dialog title="字段显示" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='700' :maxHeight='600'>
      <template slot="body">
        <div class="checkBox" style="margin:30px" v-loading="loading">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-for="item in cloumnData" :label="item.label" :key="item.label"></el-checkbox>
          </el-checkbox-group>
        </div>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" text="保存设置"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as impManagement from "@/api/implementation/impManagement";
export default {
  name: "field-Shows",
  components: {
  },
  mixins: [],
  props: {
    templateId: {
      type: String,
      default: ""
    },
    cloumnData: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.checkList = [];
        this.getDetail();
      }
    }
  },
  created() { },
  data() {
    return {
      loading: false,
      checkList: [],
    };
  },
  methods: {

    getDetail() {
      let _this = this;
      var temp = {};
      temp.Code = 1;
      temp.Type = 2;
      temp.BusinessId = this.templateId;
      let formData = JSON.parse(JSON.stringify(temp));
      this.loading = true;
      impManagement.getPersonalDataCenter(formData).then(res => {
        if (res.length != 0) {
          _this.checkList = res;
        } else {
          _this.checkList = _this.cloumnData.map(s => s.label);
          _this.checkList.splice(_this.checkList.findIndex(s => s == "总产值"), 1)
          _this.checkList.splice(_this.checkList.findIndex(s => s == "已完成产值"), 1)
        }
        _this.loading = false;
      }).catch(err => {
        _this.loading = false;
      });
    },

    createData() {
      if (this.checkList.length < 1) {
        this.$message({
          message: '请选择至少一个字段显示!',
          type: 'warning'
        });
      } else {
        var temp = {};
        temp.Code = 1;
        temp.Type = 2;
        temp.BusinessId = this.templateId;
        temp.Content = JSON.stringify(this.checkList);
        let formData = JSON.parse(JSON.stringify(temp));
        impManagement.fieldShows(formData).then(res => {
          this.$notify({
            title: "成功",
            message: "设置成功",
            type: "success",
            duration: 2000
          });
          this.$refs.appDialogRef.createData(this.checkList);
        });
      }
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style scoped>
.checkBox >>> .el-checkbox__label {
  font-weight: normal !important;
}
</style>
<style lang="scss" scoped>
main {
  padding-left: 10px;
  > div:first-child {
    font-weight: bold;
    margin-bottom: 10px;
  }
}
</style>