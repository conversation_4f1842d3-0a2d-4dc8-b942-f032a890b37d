<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="650" :width="1200">
      <template slot="body">
        <el-form class="businessElForm" :rules="rules" ref="formData" :model="formData" label-position="right" label-width="100px" v-loading="loading">
          <div>
            <div class="panel-title">基本信息</div>
            <el-row>
              <el-col :span="12">
                <el-form-item label="所属分类" prop="ClassifyId">
                  <treeselect :disabled="editable" class="treeselect-common" :normalizer="normalizer" :options="ClassifyList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择所属分类" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"></treeselect>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="故障代码" prop="FailureCaseCode">
                  <el-input style="width:100%" class="gzxxxq" v-model.trim="formData.FailureCaseCode" :disabled="editable" :placeholder="placeholderText" maxlength="10" @blur="handleBlur" @input="handleInput"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="故障名称" prop="FailureSymptom">
                  <el-input v-model.trim="formData.FailureSymptom" :disabled="editable" placeholder maxlength="100"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="故障关键字" prop="name2">
                  <div class="cl">
                    <el-button v-show="!editable" class="elButton fl" type="text" @click="handleChoice">选择</el-button>
                    <span class="elSpan fl" v-for="(kl, index) in keyList" :key="index">
                      {{ kl.FaultKeyWordName }}
                      <i class="el-icon-error" v-show="!editable" @click="delKey(index)"></i>
                    </span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div>
            <div class="panel-title pt">
              故障分类标签
              <el-button v-show="!editable" class="el_Button" type="primary" @click="handleLabel" size="mini">添加故障分类标签</el-button>
            </div>
            <ul class="elUl" v-if="selectedMsg">
              <li>故障类型：{{ selectedMsg.Name }}</li>
              <li v-for="(sd, index) in selectedData" v-show="sd.checkList.length > 0" :key="index">
                {{ sd.Name }}：{{ sd.checkList.join("、") }}
              </li>
            </ul>
            <no-data v-else></no-data>
          </div>
          <div>
            <div class="panel-title pt mt">相关附件</div>
            <app-uploader :readonly="editable" accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="files => handleFilesUpChange(files)"></app-uploader>
          </div>
          <div>
            <div class="panel-title pt mt">
              故障原因({{ formData.FailureAnalysiseList.length }})
              <el-button v-show="!editable" class="el_Button" type="primary" size="mini" @click="handleCause">添加故障原因</el-button>
            </div>
            <app-table-core ref="mainTable" :tab-columns="failureAnalysiseColumns" :tab-datas="formData.FailureAnalysiseList" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="0" :multable="false">
              <template slot="op" slot-scope="scope">
                <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
                <el-button size="mini" type="text" @click="handleEdit(scope.row, scope.index)" v-show="!editable">编辑</el-button>
                <el-button size="mini" type="text" class="red" @click="handleDel(scope.index)" v-show="!editable">删除</el-button>
              </template>
            </app-table-core>
          </div>
          <div>
            <div class="panel-title pt mt">
              解决方法({{ formData.SolutionList.length }})
              <el-button v-show="!editable" class="el_Button" type="primary" @click="handleSolution" size="mini">添加解决方法</el-button>
            </div>
            <app-table-core ref="mainTable" :tab-columns="solutionColumns" :tab-datas="formData.SolutionList" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="0" :multable="false">
              <template slot="op" slot-scope="scope">
                <el-button size="mini" type="text" @click="handleSLDetail(scope.row)">详情</el-button>
                <el-button size="mini" type="text" @click="handleSLEdit(scope.row, scope.index)" v-show="!editable">编辑</el-button>
                <el-button size="mini" type="text" class="red" @click="handleSLDel(scope.index)" v-show="!editable">删除</el-button>
              </template>
            </app-table-core>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <el-button @click="handleClose" v-show="editable" size="mini">关闭</el-button>
        <el-button @click="handleClose" v-show="!editable" size="mini">取消</el-button>
        <el-button @click="handleSuccess" v-show="!editable" type="primary" size="mini" :disabled="disabledBtn">确认</el-button>
      </template>
    </app-dialog>
    <fault-key @closeDialog="closeKeyDialog" @saveSuccess="handleKeySuccess" :dialogFormVisible="dialogKeyFormVisible" :keyList="keyList">
    </fault-key>
    <faultLabel @closeDialog="closeLabelDialog" @saveSuccess="handleLabelSuccess" :dialogFormVisible="dialogLabelFormVisible">
    </faultLabel>
    <v-cause ref="vc" @closeDialog="closeCauseDialog" @saveSuccess="handleCauseSuccess" @editSuccess="handleCauseEdit" :dialogFormVisible="dialogCauseFormVisible" :dialogStatus="dialogCauseStatus" :causeData="causeData">
    </v-cause>
    <v-solution ref="vs" @closeDialog="closeSolutionDialog" @saveSuccess="handleSolutionSuccess" @editSuccess="handleSolutionEdit" :dialogFormVisible="dialogSolutionFormVisible" :dialogStatus="dialogSolutionStatus" :solutionData="solutionData">
    </v-solution>
  </div>
</template>
<script>
import faultKey from "./faultKey";
import faultLabel from "./faultLabel";
import vCause from "./cause";
import vSolution from "./solution";
import NoData from "@/views/common/components/noData";
import * as failurecase from "@/api/failurecase";
import * as equipmentSetting from "@/api/equipmentSetting";
import * as classify from '@/api/classify';
import { listToTreeSelect } from "@/utils";

export default {
  name: "mainten-order-mgmt-assign",
  components: {
    faultKey,
    faultLabel,
    vCause,
    vSolution,
    NoData
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String,
      default: "create"
    },
    id: {
      type: String,
      defalult: ""
    },
    selectClassifyId: {
      type: String,
      default: ""
    },
  },
  data() {
    var caseValidate = (rule, value, callback) => {
      if (this.smsMsg) {
        callback(new Error('故障代码不可重复!'));
      } else {
        callback();
      }
    };
    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label.split(",")[0],
          id: node.Id,
          children: node.children
        };
      },
      ClassifyList: [],
      smsMsg: null,
      tabDatas: [],
      placeholderText: "",
      causeIndex: 0,
      solutionIndex: 0,
      causeData: null,
      solutionData: null,
      loading: false,
      dialogCauseStatus: "create",
      dialogSolutionStatus: "create",
      keyList: [],
      dialogKeyFormVisible: false,
      dialogLabelFormVisible: false,
      dialogCauseFormVisible: false,
      dialogSolutionFormVisible: false,
      rules: {
        ClassifyId: { required: true, message: '请选择故障案例分类', trigger: 'change' },
        FailureCaseCode: [
          { required: true, message: "故障代码不能为空", trigger: "blur" }, { validator: caseValidate }
        ],
        FailureSymptom: [
          { required: true, message: "故障名称不能为空", trigger: "blur" }
        ]
      },
      formData: {
        FailureCaseCode: "",
        FailureSymptom: "",
        FaultKeywordIdList: [],
        EquipmentSettingIds: [],
        AttachmentList: [],
        FailureAnalysiseList: [],
        SolutionList: [],
        ClassifyId: null,
        Id: ""
      },
      disabledBtn: false,
      failureAnalysiseColumns: [
        {
          attr: { prop: "FailureResonCode", label: "故障原因代码", width: 500 }
        },
        {
          attr: { prop: "FailureReson", label: "故障原因", width: 500 }
        },
        {
          attr: { prop: "op", label: "操作" },
          slot: true
        }
      ],
      solutionColumns: [
        {
          attr: {
            prop: "FailureSolutionCode",
            label: "解决方法代码",
            width: 500
          }
        },
        {
          attr: { prop: "Solution", label: "解决方法", width: 500 }
        },
        {
          attr: { prop: "op", label: "操作" },
          slot: true
        }
      ],
      listData: []
    };
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.ClassifyList = [];
      }
      if (val) {
        this.placeholderText = "";
        this.disabledBtn = false;
        this.resetData();
        this.$refs.vc.giveData(
          this.formData.FailureAnalysiseList,
          this.formData.SolutionList,
          this.formData.FailureCaseCode
        );
        this.$refs.vs.giveData(
          this.formData.SolutionList,
          this.formData.FailureAnalysiseList,
          this.formData.FailureCaseCode
        );
        this.getClassifyList();
        if (this.dialogStatus == "create") {
          failurecase.getCode().then(res => {
            this.placeholderText = '最近一次添加代码：' + res.FailureAnalysiseCode;
          });
          this.getList();
        }
        // if (this.dialogStatus == "edit" || this.dialogStatus == "detail") {
        //   this.getDetail();
        // }
      }
    },
    "formData.FailureCaseCode"(val) {
      this.formData.FailureCaseCode = this.formData.FailureCaseCode.replace(
        /[\W]/g,
        ""
      );
    }
  },
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建故障现象";
      } else if (this.dialogStatus == "detail") {
        return "故障现象详情";
      } else if (this.dialogStatus == "edit") {
        return "编辑故障现象";
      }
    },
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus == "detail";
    },
    selectedMsg() {
      return this.$store.state.communication.selectedMsg;
    },
    selectedData() {
      return this.$store.state.communication.selectedData;
    }
  },
  created() { },
  methods: {
    //获取分类下拉框
    getClassifyList() {
      this.loading = true;
      classify.getListByCondition({ BusinessType: 3 }).then(res => {
        var departments = res.Items.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.Name,
            ParentId: item.ParentId
          };
        });
        this.ClassifyList = listToTreeSelect(departments);
        if (this.selectClassifyId) {
          this.formData.ClassifyId = this.selectClassifyId;
        }
        if (this.dialogStatus != "create" && this.id) {
          this.$nextTick(() => {
            this.getDetail();
          });
        } else {
          this.loading = false;
        }
      }).catch(err => {
        this.loading = false;
      });
    },

    handleInput() {
      if (this.formData.FailureCaseCode.length > 0) {
        this.smsMsg = false;
      }
    },
    handleBlur() {
      this.smsMsg = this.tabDatas.find(s => s.FailureCaseCode == this.formData.FailureCaseCode);
    },
    getList() {
      this.loading = true;
      let listQuery = {
        FailureCaseCode: "",
        FailureSymptom: "",
        Keywords: "",
        PageIndex: 1,
        PageSize: 99999,
        EquipmentSettingIdList: []
      }
      failurecase.getListPage(listQuery).then(res => {
        this.tabDatas = res.Items;
        this.loading = false;
      }).catch(err => {
        this.loading = false
      });
    },
    getDetail() {
      this.loading = true;
      let a = failurecase.getDetails({ id: this.id }),
        b = equipmentSetting.getListPage({ PageIndex: 1, PageSize: 9999 });
      Promise.all([a, b]).then(res => {
        this.listData = listToTreeSelect(res[1].Items);
        this.formData.ClassifyId = res[0].ClassifyId;
        this.formData.FailureCaseCode = res[0].FailureCaseCode;
        this.formData.FailureSymptom = res[0].FailureSymptom;
        this.formData.AttachmentList = res[0].AttachmentList;
        this.formData.FailureAnalysiseList = res[0].FailureAnalysiseList;
        this.formData.SolutionList = res[0].FailureSolutionList;
        this.formData.Id = res[0].Id;
        this.keyList = res[0].FaultKeywordList;
        if (res[0].EquipmentSettingIds.length > 0) {
          let c = this.listData.find(
            s => s.Id == res[0].EquipmentSettingIds[0]
          ),
            d = null;
          this.$store.commit("getSelectedMsg", c);
          c.children.forEach(v => {
            v.checkList = [];
            v.children.forEach(v1 => {
              d = res[0].EquipmentSettingIds.find(s => s == v1.Id);
              if (d) {
                v.checkList.push(v1.Name);
              }
            });
          });
          this.$store.commit("getselectedData", c.children);
        }
        this.$refs.vc.giveData(
          this.formData.FailureAnalysiseList,
          this.formData.SolutionList,
          this.formData.FailureCaseCode
        );
        this.$refs.vs.giveData(
          this.formData.SolutionList,
          this.formData.FailureAnalysiseList,
          this.formData.FailureCaseCode
        );
        this.loading = false;
      });
    },
    resetData() {
      this.keyList = [];
      this.$store.commit("getselectedData", []);
      this.$store.commit("getSelectedMsg", null);
      this.formData = {
        FailureCaseCode: "",
        FailureSymptom: "",
        FaultKeywordIdList: [],
        EquipmentSettingIds: [],
        AttachmentList: [],
        FailureAnalysiseList: [],
        SolutionList: [],
        ClassifyId: null,
        Id: ""
      };
    },
    delKey(index) {
      this.keyList.splice(index, 1);
    },
    handleSolution() {
      this.$refs.vs.giveData(
        this.formData.SolutionList,
        this.formData.FailureAnalysiseList,
        this.formData.FailureCaseCode
      );
      this.dialogSolutionStatus = "create";
      this.dialogSolutionFormVisible = true;
    },
    handleCause() {
      this.$refs.vc.giveData(
        this.formData.FailureAnalysiseList,
        this.formData.SolutionList,
        this.formData.FailureCaseCode
      );
      this.dialogCauseStatus = "create";
      this.dialogCauseFormVisible = true;
    },
    handleLabel() {
      this.dialogLabelFormVisible = true;
    },
    handleChoice() {
      this.dialogKeyFormVisible = true;
    },
    closeSolutionDialog() {
      this.dialogSolutionFormVisible = false;
    },
    handleSolutionEdit(d) {
      let t = true;
      this.formData.SolutionList.forEach((v, i) => {
        if (i != this.solutionIndex) {
          if (v.FailureSolutionCode == d.FailureSolutionCode) {
            // this.$message.error("代码编号已存在!");
            this.$refs.vs.promptRepeat();
            t = false;
          }
        }
      });
      if (t) {
        this.formData.FailureAnalysiseList.forEach((v, i) => {
          if (v.FailureResonCode == d.FailureSolutionCode) {
            // this.$message.error("代码编号已存在!");
            this.$refs.vs.promptRepeat();
            t = false;
          }
        });
      }
      if (t) {
        if (d.FailureSolutionCode == this.formData.FailureCaseCode) {
          // this.$message.error("代码编号已存在!");
          this.$refs.vs.promptRepeat();
          t = false;
        }
      }
      if (t) {
        this.formData.SolutionList[this.solutionIndex] = d;
        this.$refs.vs.giveData(
          this.formData.SolutionList,
          this.formData.FailureAnalysiseList,
          this.formData.FailureCaseCode
        );
        this.closeSolutionDialog();
      }
    },
    handleCauseEdit(d) {
      let t = true;
      this.formData.FailureAnalysiseList.forEach((v, i) => {
        if (i != this.causeIndex) {
          if (v.FailureResonCode == d.FailureResonCode) {
            // this.$message.error("代码编号已存在!");
            this.$refs.vc.promptRepeat();
            t = false;
          }
        }
      });
      if (t) {
        this.formData.SolutionList.forEach((v, i) => {
          if (v.FailureSolutionCode == d.FailureResonCode) {
            // this.$message.error("代码编号已存在!");
            this.$refs.vc.promptRepeat();
            t = false;
          }
        });
      }
      if (t) {
        if (d.FailureResonCode == this.formData.FailureCaseCode) {
          // this.$message.error("代码编号已存在!");
          this.$refs.vc.promptRepeat();
          t = false;
        }
      }
      if (t) {
        this.formData.FailureAnalysiseList[this.causeIndex] = d;
        this.$refs.vc.giveData(
          this.formData.FailureAnalysiseList,
          this.formData.SolutionList,
          this.formData.FailureCaseCode
        );
        this.closeCauseDialog();
      }
    },
    handleSolutionSuccess(d, t) {
      this.formData.SolutionList.unshift(d);
      this.$refs.vs.giveData(
        this.formData.SolutionList,
        this.formData.FailureAnalysiseList,
        this.formData.FailureCaseCode
      );
      if (!t) {
        this.closeSolutionDialog();
      }
    },
    closeCauseDialog() {
      this.dialogCauseFormVisible = false;
    },
    handleCauseSuccess(d, t) {
      this.formData.FailureAnalysiseList.unshift(d);
      this.$refs.vc.giveData(
        this.formData.FailureAnalysiseList,
        this.formData.SolutionList,
        this.formData.FailureCaseCode
      );
      if (!t) {
        this.closeCauseDialog();
      }
    },
    closeLabelDialog() {
      this.dialogLabelFormVisible = false;
    },
    handleLabelSuccess() {
      this.closeLabelDialog();
    },
    closeKeyDialog() {
      this.dialogKeyFormVisible = false;
    },
    handleKeySuccess(d) {
      this.keyList = d;
    },
    handleDetail(d) {
      this.causeData = d;
      this.dialogCauseStatus = "detail";
      this.dialogCauseFormVisible = true;
    },
    handleEdit(d, index) {
      console.log(1, index);
      this.causeIndex = index;
      this.causeData = d;
      this.dialogCauseStatus = "edit";
      this.dialogCauseFormVisible = true;
    },
    handleDel(index) {
      this.$confirm("是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.formData.FailureAnalysiseList.splice(index, 1);
        })
        .catch(() => { });
    },
    handleSLDetail(d) {
      this.solutionData = d;
      this.dialogSolutionStatus = "detail";
      this.dialogSolutionFormVisible = true;
    },
    handleSLEdit(d, index) {
      this.solutionIndex = index;
      this.solutionData = d;
      this.dialogSolutionStatus = "edit";
      this.dialogSolutionFormVisible = true;
    },
    handleSLDel(index) {
      this.$confirm("是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.formData.SolutionList.splice(index, 1);
        })
        .catch(() => { });
    },
    handleFilesUpChange(files) {
      this.formData.AttachmentList = files;
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    handleSuccess() {
      this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      if (this.dialogStatus == 'create') {

        this.smsMsg = this.tabDatas.find(s => s.FailureCaseCode == this.formData.FailureCaseCode);

        if (this.smsMsg) {
          // this.$message.error('故障原因代码不可重复!');
          this.disabledBtn = false;
          return;
        }
      }
      Promise.all([listResult]).then(valid => {
        if (this.keyList) {
          this.keyList.forEach(v => {
            this.formData.FaultKeywordIdList.push(v.FaultKeyWordId);
          });
        }

        if (this.selectedMsg) {
          let a = null;
          this.formData.EquipmentSettingIds.push(this.selectedMsg.Id);
          this.selectedData.forEach(v => {
            if (v.checkList.length > 0) {
              this.formData.EquipmentSettingIds.push(v.Id);
              v.children.forEach(v1 => {
                a = null;
                a = v.checkList.find(s => s == v1.Name);
                if (a) {
                  this.formData.EquipmentSettingIds.push(v1.Id);
                }
              });
            }
          });
        }
        console.log(666666, this.formData);
        this.formData.AttachmentIdList =
          this.formData.AttachmentList &&
          this.formData.AttachmentList.map(s => s.Id);
        if (this.formData.SolutionList.length > 0) {
          this.formData.SolutionList.forEach(v => {
            v.AttachmentIdList =
              v.AttachmentList && v.AttachmentList.map(s => s.Id);
          });
        }
        if (this.dialogStatus == "create") {
          failurecase
            .addFailure(this.formData)
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "成功",
                message: "添加成功！",
                type: "success"
              });
              this.$emit("saveSuccess", false);
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        } else {
          failurecase
            .editFailure(this.formData)
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "成功",
                message: "编辑成功！",
                type: "success"
              });
              this.$emit("saveSuccess", false);
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        }
      }).catch(err => {
        this.disabledBtn = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.nav {
  margin-bottom: 8px;
}
main {
  ul {
    li {
      margin-bottom: 8px;
    }
  }
}
.el_Button {
  margin-left: 20px;
}
.elSpan {
  height: 24px;
  line-height: 24px;
  color: #aaa;
  padding: 0 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  position: relative;
  margin-left: 10px;
  .el-icon-error {
    position: absolute;
    right: -5px;
    top: -5px;
    color: #aaa;
    cursor: pointer;
  }
}
.elUl {
  li {
    margin-bottom: 8px;
    padding-left: 6px;
  }
}
.mt {
  margin-top: 10px;
}
.pt {
  padding-top: 4px;
  border-top: 1px solid #dcdfe6;
}
</style>
