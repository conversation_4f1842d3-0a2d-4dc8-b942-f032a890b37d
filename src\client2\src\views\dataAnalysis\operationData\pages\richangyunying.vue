<template>
    <div class="block-content" v-loading='loading'>
        <blockTitle :obj='obj'></blockTitle>
        <div class="inner-content">
            <div class="top">
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">本周学习分享</div>
                    <div class="flex-1">
                        <div class="text-content">
                            {{ formData.TrainsName | emptyFilter }}
                        </div>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">分享情况&nbsp;<i class="el-icon-info"></i></div>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption3.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption3'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">知识库</div>
                    <div class="flex-1">
                        <div class="text-content num">
                            {{ formData.KnowledgeCount }}
                        </div>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <!-- <div class="chart-title">本周-周计划</div>
                    <div class="flex-1">
                        111
                    </div> -->
                </div>
            </div>
            <div class="bottom">
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">资质荣誉</div>
                    <div class="flex-1">
                        <div class="text-content num">
                            {{ formData.EnterpriseQualificationCount }}
                        </div>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">商标注册证</div>
                    <div class="flex-1">
                        <div class="text-content num">
                            {{ formData.TrademarkLicensenCount }}
                        </div>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">专利及软件著作</div>
                    <div class="flex-1">
                        <div class="text-content num">
                            {{ formData.PatentCount }}
                        </div>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <div class="chart-title">型式试验证书</div>
                    <div class="flex-1">
                        <div class="text-content num">
                            {{ formData.TestCertificatenCount }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import blockTitle from '../blockTitle'
import noData from "@/views/common/components/noData";
import * as odc from "@/api/operatingDataCenter";
import { pieEchartOptionTemp, colors } from "../vars";
import mixins from '../mixins'

export default {
    name: 'richangyunying',
    mixins: [mixins],
    components: {
        noData,
        blockTitle,
    },
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    mounted() {
        this.getDailyOperationDetailsChart()
    },
    data() {
        return {
            loading: false,
            chartsHeight: '180px',
            pieEchartOption3: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            formData: {
                KnowledgeCount: 0, //知识库数量
                EnterpriseQualificationCount: 0, //企业资质
                TrademarkLicensenCount: 0, //商标注册证
                PatentCount: 0, //专利及软件著作
                TestCertificatenCount: 0, //型式试验证书
                TrainsName: '', //本周学习分享
            },
        }
    },
    methods: {
        getDailyOperationDetailsChart() {
            let that = this
            that.loading = true
            odc.getDailyOperationDetailsChart({}).then(res => {
                that.loading = false
                let chartDatas = JSON.parse(JSON.stringify(res.ChartData || []))

                let targetOption1 = {}
                if(chartDatas && chartDatas.length > 0) {

                    targetOption1 = {
                        legend: {
                            data: chartDatas.map(s => s.Label)
                        },
                        series: [{
                            data: chartDatas.map(s => {
                                return {
                                    value: s.Value === 0 ? null : s.Value,
                                    name: s.Label
                                }
                            })
                        }],
                        color: colors
                    }
                }
                that.pieEchartOption3 = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption1)


                delete res.ChartData
                that.formData = Object.assign({}, that.formData, res)

            }).catch(err => {
                that.loading = false
            })

        },
    },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
.block-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    .inner-content{
        flex: 1;
        display: flex;
        flex-direction: column;
        .top, .bottom{
            flex: 1;
            display: flex;
            .flex-dire-column-wrapper{
                flex: 1;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
    }
}

.chart-title{
    text-align: center;
}

.flex-1, .flex-2{
    box-sizing: border-box;
    margin: 5px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    >div:last-child{
        flex: 1;
    }
}

.flex-1{
    flex: 1;
}

.text-content{
    text-align: center; flex: 1; font-weight: bold; display: flex; justify-content: center; align-items: center; word-break: break-all; white-space: normal; word-break: break-all;
}

</style>