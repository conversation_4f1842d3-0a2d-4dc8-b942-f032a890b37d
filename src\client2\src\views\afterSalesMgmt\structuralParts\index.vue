<template>
<div class="app-container structuralParts">
    <div class="bg-white">
        <!-- <page-title title="结构配件管理" :subTitle="['设备配件的维护管理页面']"></page-title> -->
        <main class="cl">
            <aside class="fl" style="padding-bottom: 46px; position: relative;">
                <div>
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="paramModel.name">
                        <!-- <el-button slot="append" type="primary" icon="el-icon-search" @click="handleFilter"></el-button> -->
                    </el-input>
                </div>
                <div>
                    <!-- <div>
                        <el-select v-model="paramModel.isPivotal" @change="handleFilter" clearable placeholder="是否关键配件">
                            <el-option v-for="item in keyParts" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select v-model="paramModel.structPartRisk" @change="handleFilter" clearable placeholder="全部风险类别">
                            <el-option :key="0" label="全部风险类别" :value="0"></el-option>
                            <el-option v-for="item in riskCategories" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </div> -->
                </div>
                <div>
                    <el-button v-if="btnMaintain=='btnMaintain'" type="primary" class="elButton" icon="el-icon-circle-plus-outline" @click="addAccess()">添加结构配件</el-button>
                </div>
                <div v-loading="loading">
                    <tags v-if="taskList.length>0" mode="list" :items="taskList" v-model="taskId" @change="handleTagsChange">
                        <template v-for="(task,idx) in taskList" :slot="task.value">
                            <div class="item_warpper" :key="idx">
                                <div>
                                    <span class="omit" :title="task.Name">{{task.Name}}</span>
                                    <span>
                                        <i class="el-icon-edit-outline pointer" v-if="btnMaintain=='btnMaintain'" @click.stop="editS(task.Id)"></i>
                                        <i class="el-icon-delete pointer" v-if="btnMaintain=='btnMaintain'" @click.stop="dels(task.Id)"></i>
                                    </span>
                                </div>
                                <!-- <div>
                                    <span :style="'color:'+task.IsPivotalColor+';boder-color:'+task.IsPivotalColor+';'">{{task.IsPivotaler}}</span>
                                    <span class="c409EFF" v-show="task.StructPartRisker">{{task.StructPartRisker}}</span>
                                </div> -->
                            </div>
                        </template>
                    </tags>
                    <no-data v-else></no-data>
                </div>
                <div style="position: absolute; left: 0; right: 0; bottom: 0;">
                    <pagination :total="total" small background :page.sync="paramModel.PageIndex" :size.sync="paramModel.PageSize" @pagination="handleCurrentChange" layout="prev, pager, next" :pager-count="5" />
                </div>
            </aside>
            <section class="fl __dynamicTabContentWrapper">
                <div v-show="taskList.length>0">
                    <el-button v-if="btnMaintain=='btnMaintain'" type="primary" class="elButton" @click="addSpec()">添加规格型号</el-button>
                </div>
                <div v-loading="loading1" id="__dynamicTabCoreWrapper">
                    <app-table-core ref="mainTable" :tab-columns="tabColumns" :tab-datas="tableDatas" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="false" :startOfTable="startOfTable" :multable='false' @rowSelectionChanged="rowSelectionChanged">
                        <template slot='MaterialNo' slot-scope="scope">
                            {{scope.row.MaterialNo ? scope.row.MaterialNo : '无'}}
                        </template>
                        <template slot='SupplierName' slot-scope="scope">
                            {{scope.row.SupplierName ? scope.row.SupplierName : '无'}}
                        </template>
                        <template slot='Remark' slot-scope="scope">
                            {{scope.row.Remark ? scope.row.Remark : '无'}}
                        </template>
                        <template slot='operation' slot-scope="scope">
                            <el-button v-if="btnMaintain=='btnMaintain'" type="text" @click="getPartDetail(scope.row.Id,'detail')">详情</el-button>
                            <el-button v-if="btnMaintain=='btnMaintain'" type="text" @click="getPartDetail(scope.row.Id,'edit')">编辑</el-button>
                            <el-button v-if="btnMaintain=='btnMaintain'" type="text" @click="delsPart(scope.row.Id)" style="color:#F56C6C;">删除</el-button>
                        </template>
                    </app-table-core>
                </div>
                <pagination :total="listQuery.total1" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" layout="total, prev, pager, next, jumper" @pagination="handleCurrentChange1" @size-change="handleSizeChange" />
            </section>
        </main>
    </div>
    <v-accessories @closeDialog="closeDialog" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogAStatus" @saveSuccess="handleSaveSuccess" :id='id'>
    </v-accessories>
    <v-specifications @closeDialog="closeSpecDialog" :dialogFormVisible="dialogSpecFormVisible" :dialogStatus="dialogSpecStatus" @saveSuccess="handlePartSuccess" :taskData='taskData' :id="pId">
    </v-specifications>
    
</div>
</template>

<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
import NoData from "@/views/common/components/noData";
import indexPageMixin from "@/mixins/indexPage";
import vAccessories from "./accessories";
import vSpecifications from "./specifications";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import {
    vars
} from "./vars";

export default {
    name: "structuralParts",
    /**组件 */
    components: {
        vAccessories,
        vSpecifications,
        NoData
    },
    mixins: [indexPageMixin, tabDynamicHeightMixins],
    props: {

    },
    data() {
        return {
            multipleSelection: [],
            btnMaintain: '',
            id: '',
            pId: '',
            loading: false,
            loading1: false,
            dialogAStatus: 'create',
            dialogSpecStatus: 'create',
            dialogFormVisible: false,
            dialogSpecFormVisible: false,
            keyParts: vars.keyParts,
            riskCategories: vars.riskCategories,
            paramModel: {
                PageIndex: 1,
                PageSize: 30,
                name: '',
                isPivotal: null,
                structPartRisk: 0,
            },
            total: 0,
            taskId: '',
            taskData: null,
            taskList: [],
            listQuery: {
                total1: 0,
                PageIndex: 1,
                PageSize: 20,
            },
            tabColumns: [{
                    attr: {
                        prop: "SpecificationModel",
                        label: "规格型号",
                    }
                },
                {
                    attr: {
                        prop: "MaterialNo",
                        label: "物资编号",
                    },
                },
                {
                    attr: {
                        prop: "SupplierName",
                        label: "供应商",
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "UnitPrice",
                        label: "价格",
                    },
                },
                {
                    attr: {
                        prop: "Remark",
                        label: "备注",
                    }
                },
                {
                    attr: {
                        prop: "operation",
                        label: "操作",
                    },
                    slot: true
                },
            ],
            tableDatas: [],
            supplierList: [],
            timeout: null,
        };
    },
    watch: {
        'paramModel.name': {
            handler: function (val) {
                let that = this;
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    that.handleFilter(val)
                }, 500)
            },
        }
    },
    filters: {

    },
    created() {
        this.btnTextValue()
    },
    /**渲染后 */
    mounted() {
        this.getList(true);

    },
    methods: {
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        getPartDetail(id, dss) {
            this.pId = id;
            this.dialogSpecStatus = dss;
            this.dialogSpecFormVisible = true;
        },
        getConditionData() {
            accessories.getListByCondition().then(res => {
                this.loading1 = true;
                this.supplierList = res;
                this.getPartList();
            })
        },
        editS(d) {
            this.id = d;
            this.dialogAStatus = 'edit';
            this.dialogFormVisible = true;
        },
        dels(d) {
            this.$confirm('确定删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                accessories.delStructural([d]).then(res => {

                    this.$notify({
                        title: '成功',
                        message: '删除成功！',
                        type: 'success'
                    });
                    this.getList();
                })
            }).catch(() => {

            });

        },
        delsPart(d) {
            this.$confirm('确定删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                accessories.delPart([d]).then(res => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功！',
                        type: 'success'
                    });
                    this.getPartList();
                })
            }).catch(() => {

            });

        },
        getPartList() {

            let postData = {
                "pageIndex": this.listQuery.PageIndex,
                "pageSize": this.listQuery.PageSize,
                "structPartId": this.id
            }
            accessories.getPartList(postData).then(res => {
                this.loading1 = false;
                this.listQuery.total1 = res.Total;
                this.tableDatas = res.Items;
                // this.tableDatas.forEach(v => {
                //     v.SupplierIder = this.supplierList.find(s => s.Id == v.SupplierId);
                //     v.SupplierIder = v.SupplierIder.Name;
                // })
            }).catch(err => {
                this.loading1 = false;
            })
        },
        getList(t) {
            this.loading = true;
            let postData = {
                "pageIndex": this.paramModel.PageIndex,
                "pageSize": this.paramModel.PageSize,
                "name": this.paramModel.name,
                "isPivotal": this.paramModel.isPivotal,
                "structPartRisk": this.filterStruct(this.paramModel.structPartRisk)
            }
            console.log(666, postData)
            accessories.getStructuralList(postData).then(res => {
                this.loading = false;
                this.total = res.Total;
                if (res.Items && res.Items.length > 0) {
                    this.id = res.Items[0].Id;
                    this.taskId = res.Items[0].Id;
                    if (t) {
                        this.getConditionData();
                    } else {
                        this.getPartList();
                    }
                    res.Items.forEach(v => {
                        v.value = v.Id;
                        v.IsPivotaler = v.IsPivotal ? '关键配件' : '非关键配件';
                        v.IsPivotalColor = v.IsPivotal ? '#F59A23' : '#aaaaaa';
                        v.StructPartRisker = [];
                        this.riskCategories.forEach(v1 => {
                            if (v.StructPartRisk && v.StructPartRisk.length > 0) {
                                if (v.StructPartRisk.indexOf(v1.value) >= 0) {
                                    v.StructPartRisker.push(v1.label);
                                }
                            }
                        })
                        v.StructPartRisker = v.StructPartRisker.join('、');
                    })
                    this.taskList = res.Items;
                    this.taskData = this.taskList[0];
                } else {
                    this.taskList = [];
                    this.tableDatas=[];
                }
            }).catch(err => {
                this.loading = false;
            })
        },
        filterStruct(a) {
            let b = null;
            if (a != 0) {
                b = a;
            }
            return b
        },
        handleSaveSuccess(d) {
            if (!d) {
                this.closeDialog();
            }
            this.paramModel.PageIndex = 1;
            this.getList();
        },
        handlePartSuccess(d) {
            if (!d) {
                this.closeSpecDialog();
            }
            this.listQuery.PageIndex = 1;
            this.getPartList();
        },
        addAccess() {
            this.dialogAStatus = 'create';
            this.dialogFormVisible = true;
        },
        addSpec() {
            this.dialogSpecStatus = 'create';
            this.dialogSpecFormVisible = true;
        },
        /**分页页码切换 */
        handleCurrentChange(val) {
            this.paramModel.PageIndex = val.page;
            this.paramModel.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange1(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getPartList();
        },
        handleSizeChange(val) {

        },
        // changeParamisPivotal(){
        //   this.paramModel.PageIndex = 1;
        //   this.getList();
        // },
        // changeParamModel() {
        //   this.paramModel.PageIndex = 1;
        //   this.getList();
        // },
        handleFilter() {
            this.paramModel.PageIndex = 1;
            this.getList();
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        closeSpecDialog() {
            this.dialogSpecFormVisible = false;
        },
        handleTagsChange(d) {
            this.id = d;
            this.taskData = this.taskList.find(v => v.Id == d);
            this.listQuery.PageIndex = 1;
            this.getPartList();
        }
    }
};
</script>
<style lang="scss" scoped>
.item_warpper {
    >div:nth-child(1) {
        display: flex;
        justify-content: space-between;

        >span:nth-child(1) {
            width: 280px;
        }
    }

    >div:nth-child(2) {
        display: flex;
        margin-top: 16px;

        span {
            border-width: 1px;
            border-style: solid;
            padding: 0 2px;
            border-radius: 6px;
        }

        >span:first-child {
            margin-right: 5px;
        }
    }
}

main {
    // height: calc(100% - 40px);
    height: 100%;

    >aside {
        width: 390px;
        height: 100%;
        border-right: 1px solid #DCDFE6;

        >div:nth-child(1) {
            padding: 10px;
        }

        >div:nth-child(2) {
            padding: 0 10px;
            display: flex;
            justify-content: space-between;

            >div {
                width: calc(50% - 5px);
            }
        }

        >div:nth-child(3) {
            padding: 10px 0;
            text-align: center;
            border-top: 1px solid #DCDFE6;
            border-bottom: 1px solid #DCDFE6;
            margin-top: 10px;

            button {
                width: 180px;
            }
        }

        >div:nth-child(4) {
            height: calc(100% - 120px);
            overflow: auto;

            >div {
                margin-top: 10px;
            }
        }

        >div:nth-child(5) {
            width: 100%;
            border-top: 1px solid #dcdfe6;
        }
    }

    >section {
        width: calc(100% - 390px);
        height: 100%;

        >div:nth-child(1) {
            padding: 10px;
        }

        >div:nth-child(2) {
            padding: 0 10px;
            height: calc(100% - 52px);
            overflow: auto;
        }
    }
}

.el-icon-edit-outline {
    color: #409EFF;
}

.el-icon-delete {
    color: red;
}

.c409EFF {
    color: #409EFF;
    border-color: #409EFF;
}
</style>
