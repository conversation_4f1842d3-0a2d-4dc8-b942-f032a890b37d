<template>
    <div>
        <app-dialog
            :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
        >
            <template slot="body">
                <el-form ref="formData" v-loading='tabProceEqusLoading' :model="formData" label-position="right" :label-width="labelWidth">
                    <div class="wrapper" style="min-height: 400px; max-height: 600px; overflow-y: auto;">
                        <div>
                            <div style="margin-bottom: 10px;">
                                <!-- 查看审批详情 或 审批 -->
                                <div>{{ formData.ImplementationRegionalName }}</div>
                                <!-- 新增 -->
                                <div>{{ regionFullPath }}</div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <el-button v-show="editable" type="primary" :disabled='formData.EquipmentList.length >= 50' @click="handleAdd">添加实施设备</el-button>
                            </div>
                            <div class="elTable" style="overflow-y: visible;">
                                <div class="cl">
                                    <div class="fl w45">序号</div>
                                    <div class="fl w300">设备名称<i class="iClolor">*</i></div>
                                    <div class="fl w170">类型<i class="iClolor">*</i></div>
                                    <div class="fl w400">备注</div>
                                    <div class="fl auto">操作</div>
                                </div>
                                <div class="elBody" v-for="(row,index) in formData.EquipmentList">
                                    <div class="cl">
                                        <div class="fl w45">
                                            {{ index + 1 }}
                                        </div>
                                        <div class="fl w300">
                                            <el-input :title="row.Name" maxlength="50" v-model="row.Name" clearable :disabled="!editable"></el-input>
                                        </div>
                                        <div class="fl w170" style="overflow: visible;">
                                                <!-- :always-open="true" -->
                                            <treeselect :disabled="!editable" class="treeselect-common" :normalizer="normalizer2" key='type2'
                                                v-model="row.ProductListManagementId"
                                                :append-to-body="true"
                                                :zIndex='99999999'
                                                :default-expand-level="3"
                                                :options="products" :multiple="false" placeholder='' :show-count="true"
                                                :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
                                                <label :title="node.label" slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                                                    <span>
                                                        {{ node.label }}<span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                                                    </span>
                                                </label>
                                            </treeselect>
                                        </div>
                                        <div class="fl w400">
                                            <el-input :title="row.Remark" maxlength="30" v-model="row.Remark" clearable :disabled="!editable"></el-input>
                                        </div>
                                        <div class="fl auto">
                                            <el-button
                                            type="text"
                                            class="danger"
                                            :disabled="!editable"
                                            @click="handleRemove(row)"
                                            >删除</el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div>
                                <div class="panel-title">审批</div>
                                <div>
                                    <approval-panel v-if="dialogStatus == 'create' || dialogStatus == 'update'" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                                    <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                                </div>
                            </div> -->
                        </div>
                    </div>
                </el-form>
            </template>
            <template slot="footer">

                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>


                <!-- 审批模式下才显示审批 -->
                <!-- <el-button @click="handleApproval" type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval' && !isOnlyViewDetail">审批</el-button> -->
            </template>
        </app-dialog>
    </div>
</template>

<script>
// import approvalPanel from '../../../projectDev/projectMgmt/common/approvalPanel'
// import approvalDetail from '../../../projectDev/projectMgmt/workbench/common/approvalDetail'
// import { vars } from '../../../projectDev/common/vars'
import * as projMgmt from '@/api/projectDev/projectMgmt/projectMgmt'
import * as prodMgmt from '@/api/systemManagement/productListManagement'
import * as impMgmt from "@/api/implementation/impManagement2"
import { listToTreeSelect } from '@/utils'


  export default {
    name: "equ-page",
    directives: {
    },
    components: {
        // approvalPanel,
        // approvalDetail,
    },
    props: {
        specialPageTitle: {
            type: String
        },
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        },
        //地区
        regionFullPath: {
            type: String,
            default: ''
        },
        //地区id（新增必传）
        implementationRegionalId: {
            type: String,
            required: true
        },
        // approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
        //     type: String,
        //     default: ''
        // },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        // isOnlyViewDetail: {
        //     type: Boolean,
        //     default: false
        // },       
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail" && this.dialogStatus != 'approval'
        },
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            if(this.dialogStatus == 'create') {
                return '添加实施设备'
            }else if(this.dialogStatus == 'update') {
                return '编辑项目'
            }else if(this.dialogStatus == 'detail') {
                return '项目详情'
            }else if(this.dialogStatus == 'approval') {
                return '审批'
            }
        },
    },
    watch: {
        '$attrs.dialogFormVisible': {
            handler(val) {
                if(val) {
                    this.resetFormData()
                }

                if((this.dialogStatus == 'approval' || this.dialogStatus == 'detail') && this.id) {
                    this.getDetail()
                }
            },
            immediate: true
        }
    },
    created() {
        this.getProductList()
    },
    data() {
        return {
            normalizer2(node) {
                return {
                    id: node.Id,
                    label: node.ProductName,
                    children: node.children
                }
            },
            tabProceEqusLoading: false,
            disabledBtn: false,
            // dialogWidth: 1000,
            // approvalResults: vars.approvalResult,
            labelWidth: '100px',
            products: [],
            formData: {
                // Id: '', //项目ID
                EquipmentList: [
                    // {
                    // //     Id: '',//主键
                    //     ImplementationRegionalId: '',//地区id
                    // //     ImplementationProcedureId: '',//工序id
                    //     Name: '',//设备名称
                    //     ProductListManagementId: undefined,//产品编号
                    //     Remark: '',//备注
                    // }
                ],
                // Approval: {//审批信息
                //     ApprovalEmployeeList: [[]],
                //     ApprovalType: 1,
                //     ApprovalOperatorEmployeeList: [], //已审批人员
                //     NoApprovalEmployeeList: [], //未审批人员
                //     CCEmployeeList: [], //抄送人
                //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //     ApprovalState: 1, //1: 进行中; 2: 已完成
                //     ApprovalResult: 1, //1: 通过； 2：不通过
                // },
            },
        };
    },
    methods: {
        handleAdd() {
            this.formData.EquipmentList.push({
                // Id: '',//主键
                ImplementationRegionalId: this.implementationRegionalId,//地区id
                // ImplementationProcedureId: '',//工序id
                Name: '',//设备名称
                ProductListManagementId: undefined,//产品编号
                Remark: '',//备注
            })
        },
        handleRemove(row) {
            this.$confirm("是否确认删除配置行?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                var index = this.formData.EquipmentList.indexOf(row);
                if (index !== -1) {
                    this.formData.EquipmentList.splice(index, 1);
                }
            });
        },
        resetFormData() {
            let temp = {
                // Id: '', //项目ID
                EquipmentList: [],
                // Approval: {//审批信息
                //     ApprovalEmployeeList: [[]],
                //     ApprovalType: 1,
                //     ApprovalOperatorEmployeeList: [], //已审批人员
                //     NoApprovalEmployeeList: [], //未审批人员
                //     CCEmployeeList: [], //抄送人
                //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //     ApprovalState: 1, //1: 进行中; 2: 已完成
                //     ApprovalResult: 1, //1: 通过； 2：不通过
                // },
            }
            this.formData = Object.assign({}, this.formData, temp)
        },
        createData() {
            // let validate = this.$refs.formData.validate()
            // let approvalPanelValidate = this.$refs.approvalPanel.validate()
            // Promise.all([approvalPanelValidate]).then(valid => {
            // })

            if(this.formData.EquipmentList.some(s => !s.Name.trim())) {
                this.$message.error(`实施设备名称不能为空!`)
                return false
            }

            if(this.formData.EquipmentList.some(s => !s.ProductListManagementId)) {
                this.$message.error(`实施设备类型不能为空!`)
                return false
            }


            // this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块
            let postData = JSON.parse(JSON.stringify(this.formData));


            //提交数据保存

            let result = null

            // postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
            // postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)

            this.disabledBtn = true

            impMgmt.addImpEqus(postData).then(res => {
                this.disabledBtn = false
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.$refs.appDialogRef.createData()
            }).catch(err => {
                this.disabledBtn = false
            })
            
        },
        getProductList() {
            prodMgmt.getListByCondition({}).then(res => {
                this.products = listToTreeSelect(res)
            })
        },
        // 工序设备列表
        getDetail() {
            let postData = {
                ApprovalBusinessId: this.id,
            }

            this.tabProceEqusLoading = true
            impMgmt.getEqusNoPage(postData).then(res => {
                this.tabProceEqusLoading = false
                this.formData = res
            }).catch(err => {
                this.tabProceEqusLoading = false
            })
        },
        // // 项目创建审批
        // handleApproval() {
        //     let postData = this.$refs.approvalDetail.getData()
        //     postData.BusinessId = this.id
        //     let approvalLabel = vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label

        //     this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
        //         confirmButtonText: '确定',
        //         cancelButtonText: '取消',
        //         type: 'warning'
        //     }).then(() => {
        //         //项目创建审批
        //         this.disabledBtn = true
        //         impMgmt.addEquApproval(postData).then(res => {
        //             this.disabledBtn = false
        //             this.$notify({
        //                 title: "提示",
        //                 message: "审批成功",
        //                 type: "success",
        //                 duration: 2000
        //             });
        //             this.$refs.appDialogRef.createData()
        //         }).catch(err => {
        //             this.disabledBtn = false
        //         })
        //     })
        // },
        // handleChanged(approvalLevelUsers) {
        //     this.formData.Approval.ApprovalEmployeeList = approvalLevelUsers
        // },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }
};
</script>

<style lang="scss" scoped>

.step-wrapper{
    .step-item-wrapper{
        padding: 20px;
    }
}


.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}
.elTable{
    border-top: 1px solid #EBEEF5;
    border-left: 1px solid #ebeef5;
    margin-bottom: 18px;
    >div:first-child{
        div{
            padding:8px;
            text-align: center;
            border-right: 1px solid #EBEEF5;
            border-bottom: 1px solid #ebeef5;
        }
    }
    .elTableFooter{
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: right;
        border-color: #ebeef5;
        border-width: 1px;
        border-style: solid;
        border-top: 0;
        padding-right: 10px;
    }
    .cl{
        display: flex;
    }
    .auto{
        flex: 1;
    }
}
.elBody{
    >div:first-child{
        >div{
            padding:8px;
            text-align: center;
            border-right: 1px solid #EBEEF5;
            border-bottom: 1px solid #ebeef5;
            height:45px;
        }
    }
}
.w300{
    width:300px;
}
.w170{
    width:170px;
}
.w400{
    width:400px;
}
.w45{
    width:45px;
}
.iClolor{
    color:#F56C6C;
}

</style>
