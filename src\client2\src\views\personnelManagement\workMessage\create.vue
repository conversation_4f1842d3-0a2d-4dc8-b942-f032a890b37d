<!--寄语编辑-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
            <template slot="body">
                <el-form
                    :rules="formRules"
                    ref="formRef"
                    :model="formModel"
                    label-position="right"
                    label-width="80px"
                    v-loading='loading'
                >
                    <el-form-item label="寄语内容" prop="Content">
                        <el-input :disabled="!editable" maxlength="1000" type="textarea" :rows="5" v-model="formModel.Content" placeholder="请输入寄语内容"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template slot="footer">
                <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <app-button :buttonType="2" @click="handleClose"></app-button>
                <app-button v-if="editable" :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as workMessageApi from '@/api/personnelManagement/workMessage'

export default {
    /**名称 */
    name: 'work-message-edit',
    /**组件声明 */
    components: {},
    /**参数区 */
    props: {
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        }
    },
    /**数据区 */
    data() {
        return {
            goOn: false, // 是否继续添加
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,

            /**表单模型 */
            formModel: { 
                Content: '', // 资质名称
            },
            /**表单规则 */
            formRules: {
                Content: { fieldName: "寄语内容", rules: [{ required: true }] },
            }
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '创建寄语'
            }else if(this.dialogStatus == 'update') {
                return '编辑寄语'
            }else if(this.dialogStatus == 'detail') {
                return '寄语详情'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail"
        },
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                let self = this;
                self.formModel = self.$options.data().formModel
                self.goOn = false
                if(val && self.dialogStatus != 'create' && self.id) {
                    self.getDetail()
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    //附件
                    let formModel = JSON.parse(JSON.stringify(self.formModel));
                    let result = null;
                    self.buttonLoading = true;

                    if(this.dialogStatus == 'create') {
                        result = workMessageApi.add(formModel)
                    }else if(this.dialogStatus == 'update') {
                        result = workMessageApi.edit(formModel)
                    }
                    result.then(response => {
                        self.buttonLoading = false;
                        if(self.goOn){
                            self.$refs.formRef.resetFields();
                            self.formModel = self.$options.data().formModel;
                            self.$emit("reload");
                        } else {
                            self.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            self.$refs.appDialogRef.createData();
                        }
                    }).catch(err => {
                        self.buttonLoading = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            workMessageApi.detail({id: this.id}).then(response => {
                this.loading = false
                this.formModel = Object.assign({}, this.formModel, response)
            }).catch(err => {
                this.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleFilesUpChange(files) {
            this.formModel.AttachmentList = files
        },
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.el-tag{
    cursor: pointer;
    +.el-tag{margin-left: 15px;}
    &.isDisabled{
        cursor: not-allowed;
    }
}
.contentBox{
    display: flex;
    &_left{
        flex: 1;
        padding-right: 10px;
    }
    &_right{
        width: 34%;
        border-left: 1px solid #eee;
        padding: 13px 5px;
    }
}
</style>
