<template>
  <div class="app-container engineering">
    <div class="bg-white">
      <!-- <page-title title="项目/工程" :subTitle='["实施工程的创建、管理页面"]' :showBackBtn='!!returnUrl' @goBack="handleGoBack"></page-title> -->
      <div class="page-wrapper">
	        <div class="content-left">
	        	<!-- isCheckbox表示是否显示单选框 -->
	        	<!-- isAll表示是否显示全部区域 -->
	        	<!-- isSubset表示是否显示子集 -->
	        	<v-tree @changeNode='changeTreeNode' :level='1' :isAll='true' :isSubset='true' :defaultValue="defaultValue"></v-tree>
	        </div>
          	<div class="content-right" v-loading='loading'>
          		<div>
                  <tags :items='types' v-model="tagType" @change="handleTagsChange">
                      <template v-for="t in types" :slot="t.value">
                          {{ t.label+'&nbsp;('+t.amount+')' }}
                      </template>
                  </tags>
                </div>
                <div class="formBox">
	                <app-table-form
	                  label-width="90px"
	                  :items="tableSearchItems"
	                  @onSearch="handleFilter"
	                  @onReset="handleResetSearch"
	                >
					<template slot="OrderNumber">
	                    <el-input style="width: 100%;" v-model.trim="listQuery.OrderNumber"></el-input>
	                  </template>
	                  <template slot="Name">
	                    <el-input style="width: 100%;" v-model.trim="listQuery.Name"></el-input>
	                  </template>
	                  <template slot="ImplementStatus">
	                    <el-select style="width: 100%;" v-model="listQuery.ImplementStatus" placeholder="" clearable>
	                        <el-option v-for="item in ImplementStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
	                    </el-select>
	                  </template>
	                  <template slot="EmployeeName">
						  <el-input :disabled="tagType != 1" style="width: 100%;" v-model.trim="listQuery.EmployeeName" placeholder></el-input>
	                    <!-- <el-select :disabled="tagType != 1" style="width: 100%;" v-model="listQuery.ImplementPrincipal" placeholder="" clearable>
	                        <el-option v-for="item in ImplementPrincipalList" :key="item.value" :label="item.label" :value="item.value"></el-option>
	                    </el-select> -->
	                  </template>
					  <template slot="Year">
	                    <el-date-picker key="xxxyear" style="width: 100%;" v-model="listQuery.Year" type="year" placeholder="" format='yyyy' value-format='yyyy'></el-date-picker>
	                  </template>
	                </app-table-form>
                </div>
                <div style="padding-left:6px;margin:10px 0;border-bottom: 1px solid #DCDFE6;padding-bottom: 10px;">
                	<permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                </div>
                <div class="main" v-if="implementationList.length>0">
                	<el-card class="box-card" style="margin-bottom: 10px;" v-for="(item,index) in implementationList" :key="index">
					  <div slot="header" class="clearfix">
					    <span>{{item.Name}}</span>
					    <el-dropdown trigger="click" style="float: right; padding: 3px 8px">
                            <span class="el-dropdown-link">
                                <i class="el-icon-more"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                            	<el-dropdown-item @click.native="handleCommand(item,'detail')">详情</el-dropdown-item>
                                <!-- <el-dropdown-item v-show="showChange(item)" @click.native="handleCommand(item,'update')">变更</el-dropdown-item> -->
								<el-dropdown-item v-show="showChange(item)" @click.native="handleCommand(item,'update')">编辑</el-dropdown-item>
                                <el-dropdown-item v-show=" rowBtnIsExists('btnDel') && item.ImplementStatus != 5 && item.ImplementStatus != 9 && item.ImplementStatus != 6 "
								 @click.native="handleCommand(item,'delete')">删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
					  </div>
					  <div>
					  	<el-collapse v-model="item.activeNames" :key="(listQuery.PageIndex-1)*listQuery.PageSize+index">
						  <el-collapse-item :name="1" @click.native="handleFirstCollClick(item.activeNames,item,index)">
							  	<template slot="title">
									<div class="collapseTitle cl">
										<span>
											整体进度：
											<span class="elProgress">
								      			<el-progress :percentage="item.OverallProgress" :color="item.color"></el-progress>
								      		</span>
										</span>
										<span>工程状态：<i :style="'color:'+item.color">{{item.ImplementStatus | findState}}</i></span>
										<span>年份：<i>{{item.Year || '无'}}</i></span>
										<span class="peopleBox omit" style="margin-left:30px;" :title="item.ImplementPrincipalPeople">工程负责人：{{item.ImplementPrincipalPeople}}</span>
										<span class="omit" :title="item.EngineeringNumber">单号：{{ item.EngineeringNumber || '无' }}</span>
										<!-- <span class="manpowerBox omit" :title="item.ManpowerBudget+'人'">参与人员：{{item.ManpowerBudget}}人</span> -->
									</div>
							    </template>
							    <div>
							    	<div class="collapseHead cl">
							    		<div class="fl">
											<span>切换看板：</span>
							    			<el-select v-model="item.region" placeholder="请选择活动区域">
										      <el-option @click.native='handleSelectClick(item,rml,index)' v-for="(rml,i) in item.RegionalManagementList" :label="rml.RegionalName" :value="rml.RegionalId" :key='i'></el-option>
										    </el-select>
							    		</div>
							    		<div class="statistical fr cl">
							    			<span>应实施总数({{item.Total}})</span>
							    			<span>已完成数({{item.Completed}})</span>
							    			<span>完成率({{item.CompletedRate}}%)</span>
							    		</div>
							    	</div>
							    	<div class="chartBox" ref="chartBox">
							    		<div :id="'myChart'+index" style="min-width:835px;height:400px;"></div>
							    	</div>
						    	</div>
						  	</el-collapse-item>
						  	<el-collapse-item v-show='item.secondListShow' title="工序进度" :name="2" @click.native="handleLastCollClick(item)">
							  	<div class="insideColl">
							  		<el-collapse v-for="(ipl,iplIndex) in item.ImplementationProcedureList" @change="handleSecondCollClick(item)" :key="iplIndex">
							  			<el-collapse-item :name="1">
							  				<template slot="title">
							  					<div class="collapseTitle cl">
													<span>{{iplIndex+1}}、{{ipl.Name+'('+ipl.ImplementationEquipmentCount+')'}}</span>
													<span>工序负责人：{{ipl.EmployeeList | getNameList}}</span>
													<span v-show="ipl.DaysRemaining">剩余工期：{{ipl.DaysRemaining | filterDays}}天</span>
												</div>
							  				</template>
							  				<div class="proBox">
							  					<ul>
							  						<li class="cl" v-for="iil in ipl.ImplementationItemList">
							  							<span class="fl omit">{{iil.Name}}</span>
							  							<span class="fl" :style="'background:'+iil.color+';'">{{iil.progress}}</span>
							  							<span class="fl" :style="'color:'+iil.color+';'">{{iil.Progress}}%</span>
							  							<span class="fl">
							  								<el-progress :percentage="iil.Progress" :color="iil.color"></el-progress>
							  							</span>
							  						</li>
							  					</ul>
							  				</div>
							  			</el-collapse-item>
							  		</el-collapse>
							  	</div>
							</el-collapse-item>
						</el-collapse>
						<div class="btns cl" v-show="item.ImplementStatus != 5 && item.ImplementStatus != 7">
							<el-button type="primary" size="mini" @click="handleRecordClick(item.RegionalManagementList)">操作记录/问题</el-button>
							<!-- <el-button type="primary" size="mini" v-show="item.CheckAcceptance" @click="handleAccepClick(item)">验收记录</el-button> -->
							<!-- <el-button type="primary" size="mini" v-show="showAccept(item)" @click="checkAcceptanceContent(item)">验收交付</el-button> -->
							<el-button type="primary" size="mini" @click="handleNav(item)">实施工作台</el-button>
						</div>
					  </div>
					</el-card>
					<pagination
		              v-show="total>listQuery.PageSize"
		              :total="total"
		              :page.sync="listQuery.PageIndex"
		              :size.sync="listQuery.PageSize"
		              layout="total, prev, pager, next, jumper"
		              @pagination="handleCurrentChange"
		              @size-change="handleSizeChange"
		            />
                </div>
				<div class="main" v-else>
					<no-data></no-data>
				</div>
          	</div>
      </div>
    </div>
    <!-- 验收详情 -->
    <!-- <v-accep-detail
	v-if="dialogAccepVisible"
    @closeDialog='closeAccepDialog'
    :dialogFormVisible='dialogAccepVisible'
    :project='currentProject'
    :isAccept='isOperable("card-approval-rule-btn", currentProject)'>
    </v-accep-detail> -->

    <!-- 创建实施工程 -->
    <v-create
	v-if="dialogCreateVisible"
    @closeDialog='closeCreateDialog'
    @saveSuccess='handleCreateSuccess'
    :dialogFormVisible='dialogCreateVisible'
    :dialogStatus='dialogCreateStatus'
	:regionalId='listQuery.RegionalId'
    :id="id">
    </v-create>
    <!-- 实施记录/问题 -->
    <v-record-problem
	v-if="dialogRecordVisible"
    @closeDialog='closeRecordDialog'
    :dialogFormVisible='dialogRecordVisible'
    :siteList="siteList">
    </v-record-problem>
    <!-- 验收交付工程 -->
    <!-- <v-accept
	v-if="dialogDeliveryVisible"
    @closeDialog='closeDeliveryDialog'
    @saveSuccess='handleAcceptSuccess'
    :dialogFormVisible='dialogDeliveryVisible'
    :dialogStatus='dialogDeliveryStatus'
    :ImplementId='ImplementId'>
    </v-accept> -->
	<v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>
  </div>
</template>

<script>
import * as impManagement from "@/api/implementation/impManagement";
import vTree from '../../afterSalesMgmt/businessMap/common/tree'
import indexPageMixin from "@/mixins/indexPage";
// import vAccepDetail from './bounced/AccepDetail';
import vCreate from './create';
import vRecordProblem from './recordAndProblem';
// import vAccept from './accept';
import { getUserInfo } from '@/utils/auth'
import {vars} from '../common/vars';
import vExport from "@/components/Export/index";
import NoData from "@/views/common/components/noData";
var echarts = require('echarts');
export default {
  name: "engineering",
  mixins: [indexPageMixin],
  components: {
    vTree,
    // vAccepDetail,
    vCreate,
    vRecordProblem,
	// vAccept,
	vExport,
	NoData
  },
  computed: {
	returnUrl() {
		let url = decodeURI(this.$route.query.returnUrl || '')
		return url
	},
  },
  watch: {
  },
  filters: {
  	findState(d){
  		let result=vars.implementationSatus.find(v => v.value == d);
  		return result.label;
  	},
  	getNameList(d){
  		if(d && d.length>0){
  			let arr=d.map(v => v.Name);
  			return arr.join('、');
  		}else{
  			return '无'
  		}
  	},
  	filterDays(d){
  		if(d>=0) return d;
  		else return '已逾期'+Math.abs(d);
  	}
  },

  data() {
    return {
		defaultValue:this.$route.query.rId,
		firstLoadPage: true, //第一次加载页面
		rData:null,
		cData:[],
		dialogExportVisible:false,
    	currentProject:null,
    	ImplementId:'',
    	siteList:[],
    	id:'',
    	loading:false,
    	implementationList:[],
		// dialogDeliveryVisible:false,
		// dialogDeliveryStatus:'create',
    	tagType:1,
    	userMsg:getUserInfo(),
    	total: 0,
    	dialogCreateStatus:'create',
    	
    	dialogRecordVisible:false,
    	dialogCreateVisible:false,
    	// dialogAccepVisible:false,
    	myChart:null,
    	types: [{value: 1, label: '全部工程',amount:''}, {value: 2, label: '我的工程',amount:''}],
    	tableSearchItems: [
	        { prop: "Name", label: "工程名称" },
	        { prop: "ImplementStatus", label: "状态" },
			{ prop: "EmployeeName", label: "工程负责人" },
			{ prop: "OrderNumber", label: "订单查询" },
			{ prop: "Year", label: "年份" },
	    ],
	    listQuery: {
	    	RegionalId:'',
	        Name: '',
	        ImplementStatus: null,
	        EmployeeName: '',
	        PageIndex:1,
			PageSize:4,
			OrderNumber:'',
			Year: ''
	    },
	    // ImplementPrincipalList:[],
		ImplementStatusList: vars.implementationSatus,
		
		// [{ value: 1, label: '未开始', color: '#A9ACB3' },
		// { value: 2, label: '进行中', color: '#00CC00' },
		// { value: 3, label: '已交付', color: '#409EFF' },
		// { value: 4, label: '已终止', color: '#FF9900' },
		// // { value: 5, label: '创建待审批', color: '#FF2131' },
		// { value: 6, label: '验收待审批', color: '#FF2131' },
		// // { value: 7, label: '创建不通过', color: '#FF9900' },
		// { value: 8, label: '验收不通过', color: '#FF9900' },],
		// // { value: 9, label: '变更待审批', color: '#FF2131' },
		// // { value: 10, label: '变更不通过', color: '#FF9900' },],
			option :{
	        legend: {
		        data: ['应实施', '已完成']
		    },
	        grid:{
	        	x:50,
	        	x2:25,
	          y2:46,
	          bottom:'16%'
	        },
	        xAxis: {
	          type: 'category',
	          axisLine: {
	            lineStyle: {
	              color: '#ccc', // 颜色
	            }
	          },
	          axisLabel: {
	            color:'black',
	            interval:0,
	            formatter:function(value)
                 {
                     var ret = "";//拼接加\n返回的类目项
                     var maxLength = 4;//每项显示文字个数
                     var valLength = value.length;//X轴类目项的文字个数
                     var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                     if (rowN > 1)//如果类目项的文字大于3,
                     {
                         for (var i = 0; i < rowN; i++) {
                             var temp = "";//每次截取的字符串
                             var start = i * maxLength;//开始截取的位置
                             var end = start + maxLength;//结束截取的位置
                             //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                             temp = value.substring(start, end) + "\n";
                             ret += temp; //凭借最终的字符串
                         }
                         return ret;
                     }
                     else {
                         return value;
                     }
                 }
	          },
	          data: ['燃烧器', '加热炉', '锅炉', 'aaa', 'bbb', 'Sat', 'Sun']
	        },
	        yAxis: {
	          type: 'value',
	          splitLine:{
	          	show: true,
	          	lineStyle:{
	          		type:'dashed'
	          	}
	          },//去除网格线
	          axisLine: {
	            lineStyle: {
	              color: '#ccc', // 颜色
	            }
	          },
	          axisLabel: {
	            color:'black'
	          },
	        },
	        series: [{
	        	name:'应实施',
	          data: [120, 200, 150, 80, 70, 110, 130],
	          type: 'bar',
	          barWidth: 20,
	          // showBackground: true,
	          label: {
	              show: true,
	              position: 'outside'
	          },
	          itemStyle:{
	            normal:{
	              color:'#FF9900'
	            }
	          },
	        },{
	        	name:'已完成',
	          data: [120, 200, 150, 80, 70, 110, 130],
	          type: 'bar',
	          barWidth: 20,
	          // showBackground: true,
	          label: {
	              show: true,
	              position: 'outside'
	          },
	          itemStyle:{
	            normal:{
	              color:'#409EFF'
	            }
	          },
	        },]
	    },
    };
  },
	created() {
		let pageIndex = this.$route.query.pageIndex
		if(pageIndex && pageIndex > 0) {
			this.listQuery.PageIndex = parseInt(pageIndex)
		}
	},
	mounted() {

	    // this.getImplementPrincipalData();//获得工程负责人数据

	},
  	methods: {
		handleSuccessExport() {},
		handleCloseExport() {
		this.dialogExportVisible = false;
		},
  		showChange(d){
  			let status=d.ImplementStatus;
  			let arr=[1,2,4,7,8,10];
  			let [t1,t2]=[false,false];
  			if(arr.includes(status)){
  				t1=true;
  			}
  			if(d.ImplementPrincipalEmployeeList){
	  			if(d.ImplementPrincipalEmployeeList.some(v => v.Number == this.userMsg.empNumber)){
	  				t2=true;
	  			}
  			}
  			if(d.CreateEmployee){
  				if(d.CreateEmployee.Number == this.userMsg.empNumber){
	  				t2=true;
	  			}
  			}
  			if(t1 && t2) return true;
  			else return false;
  		},
  		isOperable(type, row) {
            if(type && row) {
                let currEmpId = getUserInfo().employeeid //当前操作用户
                let creatorId = row.CreateEmployeeId //当前项目创建人
                let status = row.ImplementStatus //当前项目状态
                let ownerEmpList = row.ImplementPrincipalEmployeeList.map(s => s.EmployeeId) //当前项目负责人currEmpId
                if(type == "card-approval-rule-btn") { // 验收交付
                    //正常 或 延期 或 验收不通过，创建人或负责人 可发起（再次发起）验收
                    return (status == 1 || status == 2 || status == 8) && (currEmpId == creatorId || ownerEmpList.findIndex(s => s == currEmpId) > -1)
                     // || row.ActionItems.findIndex(s => s == type) > -1
                }
            }
            return false
        },
  		showAccept(d){
  			let r=false;
  			if(d.ImplementPrincipalEmployeeList){
  				r=d.ImplementPrincipalEmployeeList.some(v => v.Number == this.userMsg.empNumber);
  			}
  			return r && (d.ImplementStatus != 3 && d.ImplementStatus != 7 && d.ImplementStatus != 5 && d.ImplementStatus != 9 && d.ImplementStatus != 6);
		  },
		  handleNav(item){
			  	let targetUrl = "/implementation/engineering/workbench/index?id="+item.Id+'&pageIndex=' + this.listQuery.PageIndex+'&rId='+this.listQuery.RegionalId
				if(!!this.returnUrl) {
					targetUrl += `&returnUrl=${this.returnUrl}&returnUrlUseType=2`
				}
			  	this.$router.push(targetUrl)
		  },
  		// checkAcceptanceContent(d){
  		// 	this.ImplementId=d.Id;
  		// 	this.dialogDeliveryVisible=true;
  		// },
  		// getImplementPrincipalData(){
  		// 	impManagement.getImplementPrincipal().then(res => {
  		// 		this.ImplementPrincipalList = res.map(s => {
        //             return {
        //                 label: s.Name,
        //                 value: s.EmployeeId,
        //             }
        //         })
  		// 	})
  		// },
  		getList(){
  			this.loading=true;
  			let result=null;
  			if(!this.listQuery.EmployeeName){
				let params=JSON.parse(JSON.stringify(this.listQuery));
	            params.EmployeeName=this.userMsg.empName;
	            impManagement.getList(params).then(res => {
	            	this.types[1].amount=res.Total;
	            })
  			}
  			impManagement.getList(this.listQuery).then(res => {
  				if(!this.listQuery.EmployeeName){
  					this.types[0].amount=res.Total;
  				}else{
  					this.types[1].amount=res.Total;
  				}
  				res.Items.forEach(v => {
					v.OverallProgress=Math.round(v.OverallProgress * 100) / 100;
  					v.RegionalManagementList.unshift({RegionalName:'整体进度',RegionalId:v.RegionalId});
  					v.region=v.RegionalId;
  					v.Completed=0;
  					v.CompletedRate=0;
  					v.Total=0;
  					v.ImplementPrincipalPeople='';
  					v.secondListShow=false;
  					v.activeNames=[];
  					result=vars.implementationSatus.find(s => v.ImplementStatus == s.value);
  					v.color=result.color;
  					if(v.ImplementPrincipalEmployeeList && v.ImplementPrincipalEmployeeList.length>0){
  						v.ImplementPrincipalPeople=[];
  						v.ImplementPrincipalEmployeeList.forEach(v1 => {
  							v.ImplementPrincipalPeople.push(v1.Name);
  						})
  						v.ImplementPrincipalPeople=v.ImplementPrincipalPeople.join('、')
  					}
  				})

                this.implementationList=res.Items;
				this.total=res.Total;
  				this.loading=false;
  			}).catch(err => {
                this.loading = false
            })
  		},
  		handleTagsChange(d){
  			if(d == 1){
				  this.listQuery.EmployeeName=null;
				  if(this.tableSearchItems.length<3){
					this.tableSearchItems.push({ prop: "EmployeeName", label: "工程负责人" });
				  }
  			}else{
				  this.listQuery.EmployeeName=this.userMsg.empName;
				  if(this.tableSearchItems.length == 3){
					this.tableSearchItems.pop();
				  }
  			}
  			this.listQuery.PageIndex = 1
  			this.getList();
  		},
  		handleFilter() {
	      	this.listQuery.PageIndex = 1
            this.getList()
	    },
  		handleResetSearch() {
			this.listQuery.PageIndex=this.listQuery.PageIndex;
			this.listQuery.PageSize=this.listQuery.PageSize;
			this.listQuery.Name='';
			this.listQuery.OrderNumber="";
			this.listQuery.ImplementStatus=null;
			this.listQuery.EmployeeName=null;
			this.listQuery.Year = ''
	      	this.getList() //刷新列表
	    },
  		handleCurrentChange(val) {
	      this.listQuery.PageIndex = val.page;
	      this.listQuery.PageSize = val.size;
	      this.getList();
	    },
	    handleSizeChange(val) {
	      this.listQuery.PageSize = val.size;
	      this.getList();
	    },
	    // closeDeliveryDialog(){
	    // 	this.dialogDeliveryVisible=false;
	    // },
	    // handleAcceptSuccess(){
	    // 	this.getList();
	    // 	this.closeDeliveryDialog();
	    // },
  		handleRecordClick(d){
  			this.siteList=JSON.parse(JSON.stringify(d)).slice(1,d.length);
  			this.siteList= JSON.parse(JSON.stringify(this.siteList).replace(/RegionalName/g,"label"));
  			this.siteList= JSON.parse(JSON.stringify(this.siteList).replace(/ImplementationRegionalId/g,"value"));
  			this.dialogRecordVisible=true;
  		},
	    closeRecordDialog(){
	    	this.dialogRecordVisible=false;
	    },
  		handleCreateAccep(){
  			this.dialogCreateVisible=true;
  		},
  		closeCreateDialog(){
  			this.dialogCreateVisible=false;
  		},
  		handleCreateSuccess(){

  			this.getList();
  			this.dialogCreateVisible=false;
  		},
  		// handleAccepClick(item){
  		// 	this.currentProject=item;
  		// 	this.dialogAccepVisible = true
  		// },
  		// closeAccepDialog() {
        //     this.dialogAccepVisible = false
        // },
  		handleSecondCollClick(d){

  		},
  		handleSelectClick(d,r,index){
  			if(d.RegionalId == r.RegionalId){
  				this.handleFirstCollClick([1],d,index);
  				d.secondListShow=false;
  			}else{
	  			let params={
	  				"ImplementId": d.Id,
	  				"RegionalId":r.RegionalId,
	  				"IsAll":false
	  			}
	  			d.secondListShow=true;
	  			impManagement.getListStatisticsChart(params).then(res => {
	  				this.$nextTick(() => {
	  					let result=null;
	  					res.ImplementationProcedureList.forEach(v => {
	  						v.ImplementationItemList.forEach(v1 => {
	  							result=vars.processStatus.find(s => s.value == v1.Status);
	  							v1.color=result.color;
	  							v1.progress=result.label;
	  						})
	  					})
	  					d.ImplementationProcedureList=res.ImplementationProcedureList;
	  					d.Completed=res.Completed;
	  					d.CompletedRate=res.CompletedRate;
	  					d.Total=res.Total;
		                d.region=r.RegionalId;
		                d.activeNames=[1,2];
		            })
		            this.getChartData(res,index);
	  			})
  			}
  		},
  		handleLastCollClick(d){

  		},
  		handleFirstCollClick(e,d,index){
  			if(e.includes(1)){
	  			let params={
	  				"ImplementId": d.Id,
	  				"RegionalId":null,
	  				"IsAll":true
	  			}
	  			impManagement.getListStatisticsChart(params).then(res => {
	  				this.$nextTick(() => {

	  					d.Completed=res.Completed;
	  					d.CompletedRate=res.CompletedRate;
	  					d.Total=res.Total;

	                	// d.activeNames=[1];

		            })
		            this.getChartData(res,index);
	  			})
  			}
  		},
  		getChartData(res,index){
  			this.option.xAxis.data=[];
            this.option.series[0].data=[];
            this.option.series[1].data=[];
            if(res.StatisticsChartList && res.StatisticsChartList.length>0){
	            res.StatisticsChartList.forEach(v => {
	            	this.option.xAxis.data.push(v.ProductName);
	            	this.option.series[0].data.push(v.Total);
	            	this.option.series[1].data.push(v.Completed);
	            })
            }
			this.inItBarChart(index);
  		},
  		inItBarChart(index){
		    let myChart = echarts.init(document.getElementById('myChart'+index));
		    if(this.option.series[0].data.length<=0 && this.option.series[1].data.length<=0){

			    myChart.showLoading({
		          text: '暂无数据',
		          color: '#ffffff',
		          textColor: '#8a8e91',
		          maskColor: 'rgba(255, 255, 255, 0)',
		          }
		        );
		    }
		    myChart.setOption(this.option);
		    this.$nextTick(() => {
                myChart.resize();
            })
            window.addEventListener('resize',function(){
            	myChart.resize();
            })
	    },
        changeTreeNode(d){
	  		if(d.Id == -1){
	  			this.listQuery.RegionalId=null;
	  		}else{
	  			this.listQuery.RegionalId=d.Id;
			}
			if(!this.firstLoadPage) {
				this.listQuery.PageIndex = 1
			}else{
				this.firstLoadPage = false
			}
	  		this.getList();
	  	},
	    onBtnClicked: function(domId) {
	      switch (domId) {
	        case "btnAdd":
	        this.dialogCreateStatus='create';
	          this.handleCreateAccep();
			  break;
			case "btnExport":
				this.handleExport();
				break;
	        default:
	          break;
	      }
		},
		handleExport(){
			this.rData={
			    "exportSource": 7,
			    "columns": [],
			    "searchCondition": this.listQuery
			}
			this.cData=[{
				label:'序号',
				value:'Number'
			},{
				label:'实施工程',
				value:'Name'
			},{
				label:'整体进度',
				value:'OverallProgress'
			},{
				label:'工程状态',
				value:'ImplementStatus'
			},{
				label:'工程负责人',
				value:'ImplementPrincipalEmployeeString'
			},{
				label:'参与人员',
				value:'ManpowerBudget'
			},{
				label:'费用预算（万）',
				value:'CostBudget'
			},{
				label:'单号',
				value:'EngineeringNumber'
			}]
			this.dialogExportVisible=true;
		},
	    handleCommand(d,status){
	    	this.dialogCreateStatus=status;
	    	this.id=d.Id;
	    	if(status == 'delete'){
	    		this.commandDel(d);
	    	}else{
	    		this.handleCreateAccep();
	    	}
	    },
	    commandDel(d){

		 	this.$confirm('确认删除这条数据?', '提示', {
	          confirmButtonText: '确定',
	          cancelButtonText: '取消',
	          type: 'warning'
	        }).then(() => {
	        	impManagement.del([d.Id]).then(res => {
			        this.$notify({
                    	title: "提示",
	                    message: "删除成功",
	                    type: "success",
	                    duration: 2000
	                });
			        this.getList();
	    		})

	        }).catch(() => {

	        });

	    	// if(d.IsDelete){
		    // 	this.$alert('请先删除该实施项目的地区列表信息！', '提示', {
		    //       confirmButtonText: '确定',
		    //       callback: action => {}
		    //     });
	    	// }else{

	    	// }
		},
        handleGoBack() {
            this.$router.push({path: this.returnUrl})
        },
	}
};
</script>

<style lang="scss" scoped>
.peopleBox{
	max-width: 21%;
}
.manpowerBox{
	max-width:15%
}
.proBox{
	ul{
		padding-left:66px;
		li{
			padding:10px 0;
			>span{
				margin-right:30px;
			}
			>span:nth-child(1){
				width:120px;
				text-align: right;
			}
			>span:nth-child(2){
				border-radius: 4px;
				padding:0px 10px;
				color:white;
			}
			>span:nth-child(3){
				min-width: 35px;
				margin-right:20px;
			}
			>span:nth-child(4){
				margin-top: 3px;
				width:calc(100% - 330px);
			}
		}
	}
}
.btns{
	padding:10px;
	button{
		float: left;
		margin-right:10px;
	}
	button:active{
		opacity: 0.8;
	}
	button:nth-child(1){
		background:#00CC00;
		border-color:#00CC00;
	}
	button:nth-child(2){
		background:#FF9900;
		border-color:#FF9900;
	}
	button:nth-child(3){
		background:#FF9900;
		border-color:#FF9900;
	}
	button:nth-child(4){
		background:#409EFF;
		border-color:#409EFF;
	}
}
.collapseHead{
	padding:0 20px;
	padding-top: 10px;
	.statistical{
		>span{
			float:left;
			margin-right:20px;
		}
	}
}
.chartBox{
	width:100%;
	overflow-x:auto;
}
.collapseTitle{
	width:97%;
	>span{
		float: left;
		margin-right: 29px;
	}
}
.main{
	padding:0 10px;
	height:calc(100% - 204px);
	overflow-y:auto;
}
.page-wrapper {
	min-height: 100%!important;
	margin-bottom:0!important;
    .content-left{
    	position: absolute;
    	top:0;
    	left:0;
    	width:250px;
    	height:100%;
    	border-right:1px solid #DCDFE6;
    }
    .content-right {
		position: absolute;
		top:0;
		right:0;
	    width:calc(100% - 250px);
	    height:100%;
	    overflow-y: auto;
	    >div:first-child{
			margin-top: 4px;
			margin-bottom: 15px;
			border-bottom: 1px solid #EBEEF5;
			padding: 0 8px;
	    }
    }

}
.formBox{
	width:calc(100% - 10px);
	min-height: 93px;
	border-bottom:1px solid #DCDFE6;
}
.elProgress{
	display: inline-block;
	width:150px;
	// .eP{
	// 	margin-top:16px;
	// }
	// .ep1{
	// 	margin-top:3px;
	// }
}
</style>
