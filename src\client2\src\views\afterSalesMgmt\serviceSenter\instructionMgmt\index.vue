<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="xxxx" :subTitle="['xxxxx']"></page-title> -->
        <div class="pageWrapper __dynamicTabContentWrapper" v-loading='listLoading'>
            <div style="padding: 10px;">
                <!-- <app-button type='primary' text='预览' @click="handlePreviewDialog"></app-button> -->
                <app-button type='primary' text='保存' :disabled='disabledBtn' @click="createData"></app-button>
            </div>
            <div class="content __dynamicTabWrapper">
                <!-- <pageTitle :showBtnBack='true' @goBack='handleGoBack' title="新增模板">
                </pageTitle> -->

                <div class="body-content">
                    <div class="rht __dynamicTabContentWrapper">
                        <!-- <div class="block-title">页面地图预览</div> -->
                        <div class="detail-wrapper">
                            <div class="row" :style="{marginBottom: '40px'}" v-for="(rows, rowIdx) in formData.List" :key="`rowIdx_${rowIdx}`">
                                 <!-- :style="{marginRight: '40px'}" -->
                                <div class="col" :title="cols.PageName" :class="{active: isCurrentPoint(rowIdx, colIdx)}" @click="changeCurrentPoint({x: colIdx, y: rowIdx})" v-for="(cols, colIdx) in rows" :key="`rowIdx_${colIdx}`">
                                    <div class="omit">
                                        {{ cols.PageName }}
                                    </div>
                                    <i v-show="!isCurrentPoint(rowIdx, colIdx)" @click.stop="handleRemove(rows, colIdx, rowIdx)" class="el-icon-delete-solid btn-del" style="color: #fff;"></i>
                                    <span class="horizontal-line"></span>
                                    <span v-if="rowIdx > 0 && formData.List[rowIdx - 1][colIdx]" class="vertical-line"></span>
                                    <!-- <span @click.stop="handleAddCol(rows)" v-if="colIdx == rows.length - 1" class="el-icon-circle-plus btn-right-add"></span> -->
                                    <span @click.stop="handleAddRow()" v-if="colIdx == 0 && rowIdx == formData.List.length - 1" class="el-icon-circle-plus btn-bottom-add"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="lft">
                        <el-form
                            :rules="rules"
                            ref="formData"
                            :model="formData"
                            label-position="right"
                            label-width="80px"
                            style="height: 100%;"
                            >
                            <div class="__dynamicTabContentWrapper" style="height: 100%;">
                                <div class="wrapper">
                                    <el-row>
                                        <!-- <el-col :span="24">
                                            <el-form-item label="模板名称" prop="TempName">
                                                <el-input :maxlength="tempNameMaxLength" v-model.trim="formData.TempName">
                                                    <span slot="suffix">{{ formData.TempName.length }}/{{ tempNameMaxLength }}</span>
                                                </el-input>
                                            </el-form-item>
                                        </el-col> -->
                                        <el-col :span="24">
                                            <el-form-item label="页面名称" :prop="'List.' + currentPage.y + '.' + currentPage.x + '.PageName'" :rules="{required: true, message: '页面名称不能为空', trigger: 'change'}">
                                                <el-input :maxlength="tempNameMaxLength" v-model.trim="formData.List[currentPage.y][currentPage.x].PageName">
                                                    <span slot="suffix">{{ formData.List[currentPage.y][currentPage.x].PageName.length }}/{{ tempNameMaxLength }}</span>
                                                </el-input>
                                            </el-form-item>
                                        </el-col>
                                        <!-- <el-col :span="24">
                                            <el-form-item label="页面标题">
                                                <el-input :maxlength="pageTitleMaxLength" v-model.trim="formData.List[currentPage.y][currentPage.x].PageTitle">
                                                    <span slot="suffix">{{ formData.List[currentPage.y][currentPage.x].PageTitle.length }}/{{ pageTitleMaxLength }}</span>
                                                </el-input>
                                            </el-form-item>
                                        </el-col> -->

                                        <!-- <el-col :span="24">
                                            <div class="sp-row">
                                                <span class="title">企业介绍</span>
                                            </div>
                                        </el-col> -->
                                    </el-row>
                                </div>
                                <div class="__dynamicTabWrapper" id="__dynamicTabCoreWrapper">
                                    <editor-bar ref="editor" :value="formData.List[currentPage.y][currentPage.x].Content" @edit="formData.List[currentPage.y][currentPage.x].Content = arguments[0]"></editor-bar>
                                </div>
                            </div>
                        </el-form>
                    </div>
                    
                </div>

                <!-- <previewDialog 
                    v-if="dialogPreviewFormVisible && formData.List"
                    @closeDialog='closePreviewDialog' 
                    :dialogFormVisible='dialogPreviewFormVisible'
                    :datas='formData.List'
                >
                </previewDialog> -->
            </div>
        </div>
    </div>
</div>
</template>

<script>

import elDragDialog from "@/directive/el-dragDialog";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import editorBar from '@/components/QuillEditor/index.vue'
import previewDialog from './previewDialog'
import * as templateMgmt from "@/api/maintenanceCenter/templateMgmt"

let listTemp = [ //rows
    [ // columns
        {
            PageName: '页面一',
            PageTitle: '',
            Content: ''
        }
    ]
]
export default {
    name: "instruction-mgmt",
    mixins: [tabDynamicHeightMixins],
    components: {
        editorBar,
        previewDialog,
    },
    props: {},
    filters: {


    },
    computed: {
        pageCount() {
            let sum = this.formData.List.reduce(function(prev,cur,index,array){
                return prev + cur.length
            }, 0);
            return sum
        },
    },
    watch: {
        // 已经消抖后的数据
        tabHeight: {
            handler(val) {
                this.setEditorHeight(val)
            },
        }

    },
    created() {

        this.rules = this.initRules(this.rules);

        // if(this.dialogStatus == 'create') {
        //     this.formData.List = [
        //         [
        //             {
        //                 PageName: '页面1',
        //                 PageTitle: '',
        //                 Content: ''
        //             }
        //         ]
        //     ]
        // }
        
        //编辑、详情
        // if(this.dialogStatus != 'create') {
        //     // this.getDetail()
        // }
        this.getDetail()
    },
    data() {
        return {
            layoutMode: 'simple',

            tempNameMaxLength: 10,
            pageTitleMaxLength: 50,
            rules: {
                TempName: { fieldName: "模板名称", rules: [{ required: true }]},
                PageName: { fieldName: "页面民初", rules: [{ required: true }]},
            },
            /** 列表 */
            listLoading: false,
            //当前选中页面坐标（x：横坐标（每一行中的元素索引）；y：纵坐标（rows，也就是行索引））
            currentPage: {x: 0, y: 0},
            formData: {
                Id: '',
                TempName: '',
                List: JSON.parse(JSON.stringify(listTemp))
            },
            disabledBtn: false,

            dialogPreviewFormVisible: false,

            editorText: ''
            

        };
    },
    methods: {
        setEditorHeight(val) {
            this.$nextTick(() => {
                this.$refs.editor && this.$refs.editor.setHeight(val - 80)
            })
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));

                    let result = null;

                    this.disabledBtn = true;
                    result = templateMgmt.add(postData);

                    // if (this.dialogStatus == "create") {
                    //     delete postData.Id;
                    // } else if (this.dialogStatus == "edit") {
                    //     result = templateMgmt.edit(postData);
                    // }

                    result.then(res => {
                        this.disabledBtn = false

                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        
                    })
                    .catch(err => {
                        this.disabledBtn = false;
                    });
                }
            });
        },
        changeCurrentPoint({x, y}) {
            let validText = this.formData.List[this.currentPage.y][this.currentPage.x].PageName

            if(!validText) {
                this.$message({
                    message: '页面名称不能为空',
                    type: 'error'
                })
            }else{
                this.currentPage = {x, y}
            }
        },
        isCurrentPoint(rowIdx, colIdx) {
            return rowIdx == this.currentPage.y && colIdx == this.currentPage.x
        },
        handleRemove(rows, colIdx, rowIdx) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                //如果当前行只有一个元素，那么需要删除整行
                if(rows.length == 1) {
                    this.formData.List.splice(rowIdx, 1)

                    //当删除页面的y坐标在当前选中页面y坐标前面，需要更新选中坐标y
                    if(rowIdx < this.currentPage.y) {
                        this.currentPage.y -= 1
                    }
                }else{
                    //删除行中的元素
                    rows.splice(colIdx, 1)
                    //（删除行索引与当前选中行索引相同）当删除页面的x坐标在当前选中页面x坐标前面，需要更新选中坐标x
                    if(this.currentPage.y == rowIdx && colIdx < this.currentPage.x) {
                        this.currentPage.x -= 1
                    }
                }
            });
        },
        handleAddCol(rows) {
            rows.push({
                PageName: `页面${this.pageCount + 1}`,
                PageTitle: '',
                Content: ''
            })
        },
        handleAddRow() {
            this.formData.List.push([
                {
                    PageName: `页面${this.pageCount + 1}`,
                    PageTitle: '',
                    Content: ''
                }
            ])
        },
        getDetail() {
            this.listLoading = true
            templateMgmt.detail({}).then(res => {
                this.listLoading = false
                let result = {
                    Id: '',
                    TempName: '',
                    List: JSON.parse(JSON.stringify(listTemp))
                }

                if(!res) {

                }else if(res && (!res.List || res.List.length == 0)){
                    result = JSON.parse(JSON.stringify(res))
                    result.List = JSON.parse(JSON.stringify(listTemp))
                }else{
                    result = JSON.parse(JSON.stringify(res))
                }

                this.formData = result

                this.setEditorHeight(this.tabHeight)
            }).catch(err => {
                this.listLoading = false
            })
        },
        handleGoBack() {
            this.$router.push({path: '/enterpriseIntroduction/index'})
        },

        /** 新增、编辑 */
        handlePreviewDialog() {
            this.dialogPreviewFormVisible = true;
        },
        closePreviewDialog() {
            this.dialogPreviewFormVisible = false
        },


    },
};
</script>

<style lang="scss" scoped>
.content{
    padding-top: 0!important;
    .btns-wrapper{
        button{
            margin-left: 4px;
        }
    }
}

.pageWrapper {
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .content {
        padding: 10px;
        padding-right: 0;
        padding-left: 0;

        .opt-wrapper {
            box-sizing: border-box;
            border-bottom: 1px solid #dcdfe6;
            padding-bottom: 10px;
        }
    }

}



.body-content{
    display: flex;
    height: 100%;
    /deep/.el-input__inner{
        padding-right: 45px;
    }
    .lft{
        padding: 10px;
        width: 800px;
        // flex: 1;
        flex-shrink: 0;
        box-sizing: border-box;
        border-left: 1px solid rgb(238, 238, 238);
        
    }
    .rht{
        // flex: 1;
        overflow: hidden;
        .block-title{
            background: rgba(58, 98, 215, 0.2);
            color: #3A62D7;
            line-height: 32px;
            padding: 0 8px;
            margin: 10px;
            margin-bottom: 0;
        }
        .detail-wrapper{
            // flex: 1;
            width: 140px;
            overflow: auto;
            padding: 10px;
            .row{
                display: flex;
                .col{
                    width: 120px; 
                    flex-shrink: 0;
                    background: #475fda;
                    color: #fff;
                    text-align: center;
                    padding: 10px;
                    box-sizing: border-box;
                    position: relative;
                    border: 1px solid #475fda;
                    cursor: pointer;
                    &.active{
                        box-sizing: border-box;
                        background: #fff;
                        color: #3A62D7;
                        border: 1px solid #475fda;
                    }
                    .btn-right-add, .btn-bottom-add{
                        position: absolute;
                        font-size: 24px;
                        color: #3A62D7;
                    }
                    .btn-right-add{
                        margin-top: -12px;
                        top: 50%;
                        right: -28px;
                    }
                    .btn-bottom-add{
                        margin-left: -12px;        
                        left: 50%; 
                        bottom: -28px;
                    }
                    .btn-del{
                        cursor: pointer;
                        position: absolute;
                        top: 2px;
                        right: 2px;
                    }
                    .horizontal-line, .vertical-line{
                        position: absolute;
                        border: none;
                        background: #DCDFE6;
                    }
                    .horizontal-line {
                        top: 19px;
                        left: -40px;
                        width: 40px;
                        height: 1px;
                        height: 1;
                        
                    }
                    .vertical-line{
                        top: -41px;
                        left: 60px;
                        width: 1px;
                        height: 40px;
                    }

                }
            }
        }
        
    }
}

.sp-row{
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .title{
        width: 68px;
        text-align: right;
    }
}
</style>
