<template>
  <div>
    <app-dialog title="创建供应商分类" ref="addDialog" v-bind="$attrs" v-on="$listeners" :maxHeight='800' :width='600'>
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <div class="wrapper" v-loading="loading" style="min-height: 60px;">
            <el-row>
              <el-col :span="24">
                <el-form-item label="名称" prop="Name">
                  <el-input maxlength="30" v-model="formData.Name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>

import * as supplierManagement from "@/api/afterSalesMgmt/supplierManagement";

export default {
  name: "Classification-createFolder",
  directives: {
  },
  components: {

  },
  mixins: [
  ],    
  props: {
    dialogStatus: {
        type: String,
    },
    id: {
        type: String,
        default: "",
    },
  },
  watch: {
    '$attrs.dialogFormVisible'(val) {
      if (val) {
        this.resetFormData()
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }

      }
    }
  },
  created() {
    this.rules = this.initRules(this.rules)
  },
  data() {

    return {
      disabledBtn: false,
      rules: {
        Name: { fieldName: "名称", rules: [{ required: true, max: 100 }] },
      },
      labelWidth: '80px',    
      loading: false,
      formData: {
        Id: '',
       Name: '', 
      },
    };
  },
  methods: {
    resetFormData() {
      let temp = {
        Id: '',
        Name: '',
      }
      this.formData = Object.assign({}, this.formData, temp)
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {

          let postData = JSON.parse(JSON.stringify(this.formData));

          let result = null
          this.disabledBtn = true
          if(this.dialogStatus == 'create') {
            postData.Id && delete postData.Id
            result = supplierManagement.addType(postData)
          }else if(this.dialogStatus == 'update') {
            result = supplierManagement.editType(postData)
          }
          result.then(res => {
            this.disabledBtn = false
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.$refs.addDialog.createData()
          }).catch(err => {
            this.disabledBtn = false
          })
        }
      });
    },
     getDetail() {
            this.loading = true;
            supplierManagement.detailType({
                id: this.id,
            })
            .then((res) => {
                this.loading = false;

                this.formData = Object.assign({}, this.formData, res);

            })
            .catch((err) => {
                this.loading = false;
            });
        },
    handleClose() {
      this.$refs.addDialog.handleClose()
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
