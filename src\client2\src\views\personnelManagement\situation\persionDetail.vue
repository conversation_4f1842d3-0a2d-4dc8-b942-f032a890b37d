<template>
    <div>
        <app-dialog title="人员详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1008">
            <template slot="body">
                <el-form
                    ref="formData"
                    :model="formData"
                    label-position="right"
                    label-width="100px"
                >
                    <div class="wrapper" v-loading='loading'>
                        <el-card class="box-card" v-loading='empInfoLoading'>
                            <div class="persion-wrapper">
                                <div class="avatar">
                                    <img class="img-avatar-shadow" :src="empInfo.AvatarPath" alt="">
                                </div>
                                <div class="col2">
                                    <div>{{ empInfo.Name }}</div>
                                    <div>{{ empInfo.Number }}</div>
                                </div>
                                <div class="col3">
                                    <div>
                                        <div>部门：</div>
                                        <div class="omit">{{ empInfo.DepartmentName }}</div>
                                    </div>
                                    <div>
                                        <div>电话：</div>
                                        <div class="omit">{{ empInfo.Mobile }}</div>
                                    </div>
                                </div>
                                <div class="col4">
                                    <div>
                                        <div>职位：</div>
                                        <div class="omit">{{ empInfo.JobName }}</div>
                                    </div>
                                    <div>
                                        <div>邮箱：</div>
                                        <div class="omit">{{ empInfo.Email }}</div>
                                    </div>
                                </div>
                            </div>
                        </el-card>

                        <div>
                            查看年份：
                            <el-select v-model="year" placeholder="" @change='() => { getSummary(); getTaskInfo(); getAchInfo(); }'>
                                <el-option v-for="item in years" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </div>

                        <div class="tagBox">
                            <tags :items='types' v-model="tagType">
                                <template v-for="t in types" :slot="t.value">
                                    {{ t.label }}
                                </template>
                            </tags>
                        </div>

                        <el-card class="box-card" v-loading='summaryLoading'>
                            <div slot="header" class="clearfix">
                                <div class="header-wrapper">
                                    <div>假期数据</div>
                                    <div>
                                        <el-select v-model="month" placeholder="" @change='getSummary'>
                                            <el-option v-for="item in monList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                    </div>
                                    <div class="summary">
                                        <span>假期余额</span>
                                        <span>剩余调休：
                                            <template v-if="empInfo.LeaveBalance">{{ empInfo.LeaveBalance.CompensatoryLeave || 0 }}</template>
                                        </span>
                                        <span>剩余年假：
                                            <template v-if="empInfo.LeaveBalance">{{ empInfo.LeaveBalance.AnnualLeave || 0 }}</template>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <el-row>
                                    <el-col :span="4" v-for="(t, idx) in summaryList" :key="idx">
                                        <div style="text-align: center; padding: 6px 0; background: #e3e3e3;">{{ t.label }}</div>
                                        <div style="text-align: center; padding: 6px 0;">{{ t.value }}</div>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-card>

                        <el-card class="box-card">
                            <div slot="header" class="clearfix">
                                <div class="header-wrapper">
                                    <div>任务数据</div>
                                </div>
                            </div>
                            <div>
                                <div class="task-summary">
                                    <div class="sum-1">
                                        <div>全部任务</div>
                                        <div>{{ taskSummary.Total }}</div>
                                    </div>
                                    <div class="sum-2">
                                        <div>常规任务</div>
                                        <div>{{ taskSummary.RoutineTaskCount }}</div>
                                    </div>
                                    <div class="sum-3">
                                        <div>研发任务</div>
                                        <div>{{ taskSummary.ProjectTaskCount }}</div>
                                    </div>
                                </div>
                                <div id="myChart" ref="myChart" style="width:100%; height: 280px;"></div>
                            </div>
                        </el-card>

                        <!-- <el-card class="box-card" v-loading='achLoading'>
                            <div slot="header" class="clearfix">
                                <div class="header-wrapper">
                                    <div>个人绩效</div>
                                </div>
                            </div>
                            <el-row>
                                <el-col :span="12">
                                    {{ year }}年/上半年度：{{ achInfo.FirstAppraiseTeamEvaluate || '无' }}
                                </el-col>
                                <el-col :span="12">
                                    {{ year }}年/下半年度：{{ achInfo.LastAppraiseTeamEvaluate || '无' }}
                                </el-col>
                            </el-row>
                        </el-card> -->
                    </div>
                </el-form>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>

import * as systemEmployee from "@/api/personnelManagement/systemEmployee"
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import * as wordPlan from '@/api/workbench/workPlan'
import * as myAch from "@/api/myAchievements"

var echarts = require('echarts');

let currentYear = (new Date()).getFullYear()

export default {
    name: "persionDetail",
    directives: {},
    components: {
        
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        empId: {
            type: String,
            required: true
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getEmpInfo()
                        this.getSummary()
                        this.getTaskInfo()
                        this.getAchInfo()
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        
    },
    created() {
    },
    data() {
        return {
            types: [
                {value: 1, label: '基础信息'},
                // {value: 2, label: '销售信息'},
                // {value: 3, label: '售后信息'},
            ],
            tagType: 1,

            loading: false,
            year: currentYear,
            years: Array.from(Array(6), (v, k)=> {
                return {
                    value: currentYear - k,
                    label: currentYear - k
                }
            }),
            month: '',
            monList: [{value: '', label: `全年`}].concat(Array.from(Array(12), (v,k) => {
                return {
                    value: k + 1,
                    label: `${k+1}月`
                }
            })),
            formData: {
                Id: "",
                ProductName: "", //产品名称
                ProductModel: "", //产品型号
                Remark: "", //产品描述
                ProductType: 1,
                RelatedEmployee: []
            },
            empInfo: {},
            taskSummary: {},
            achInfo: {},
            empInfoLoading: false,
            summaryLoading: false,
            achLoading: false,
            summaryList: [
                {label: '应出勤（天）', field: 'DueAttendanceDay', value: 0},
                {label: '实际出勤（天）', field: 'ActualAttendance', value: 0},
                {label: '迟到次数', field: 'LateNumber', value: 0},
                {label: '总迟到时长（分）', field: 'TotalLateHours', value: 0},
                {label: '早退（分）', field: 'LeaveEarly', value: 0},
                {label: '缺勤天数', field: 'AbsentDay', value: 0},
                {label: '出差（天）', field: 'BusinessTrip', value: 0},
                {label: '工作日加班（次）', field: 'WorkingOvertime', value: 0},
                {label: '加班（天）', field: 'RestdaysWorkOvertime', value: 0},
                {label: '事假（天）', field: 'CasualLeave', value: 0},
                {label: '调休（天）', field: 'WorkToRest', value: 0},
                {label: '病假（天）', field: 'SickLeave', value: 0},
                {label: '年假（天）', field: 'AnnualVacation', value: 0},
                {label: '产假（天）', field: 'MaternityLeave', value: 0},
                {label: '陪产假（天）', field: 'PaternityLeave', value: 0},
                {label: '婚假（天）', field: 'MarriageLeave', value: 0},
                {label: '丧假（天）', field: 'FuneralLeave', value: 0},
            ],
            myChart: null,
            option: {
                // title: {
                //     text: '折线图堆叠'
                // },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    x: 'center',
                    y: 'bottom',
                    data: ['应完成', '已完成']
                },
                grid: {
                    top: '2%',
                    left: '1%',
                    right: '2%',
                    bottom: '12%',
                    containLabel: true
                },
                // toolbox: {
                //     feature: {
                //         saveAsImage: {}
                //     }
                // },
                
                yAxis: {
                    type: 'value'
                },
                series: []
            }
        };
    },
    methods: {
        resetFormData() {
            this.formData = {
                Id: "",
                ProductName: "", //产品名称
                ProductModel: "", //产品型号
                Remark: "", //产品描述
                ProductType: 1,
                RelatedEmployee: []
            };
            
        },
        getEmpInfo() {
            this.empInfoLoading = true
            systemEmployee.getOverviewDetails({id: this.empId}).then(res => {
                this.empInfoLoading = false
                this.empInfo = res
            }).catch(err => {
                this.empInfoLoading = false
            })
        },
        getSummary() {
            let postData = {
                Year: this.year,
                EmployeeId: this.empId
            }
            if(this.month) { //没有月份，表示全年
                postData.Month = this.month
            }
            this.summaryLoading = true
            timecardDepartment.getTimecardReport(postData).then((res) => {
                this.summaryLoading = false
                if(res.Items && res.Items.length == 1) {
                    let obj = res.Items[0]
                    this.summaryList.forEach(col => {
                        let fieldTemp = col.field
                        if(obj[fieldTemp]) {
                            col.value = obj[fieldTemp].Value
                        }
                    });
                }
            }).catch(err => {
                this.summaryLoading = false
            })
        },
        getTaskInfo() {
            let postData = {
                Year: this.year,
                EmployeeId: this.empId
            }

            this.$nextTick(() => {
                let chartObj = this.$refs.myChart
                this.myChart = echarts.init(chartObj);
    
                this.myChart.showLoading({
                    text: '加载中...',
                    textStyle: { fontSize : 30 , color: '#444' },
                    effectOption: {backgroundColor: 'rgba(0, 0, 0, 0)'}
                });
                wordPlan.chartByEmployee(postData).then(res => {
                    this.taskSummary = res
                    
                    let len = res.ShouldCompleteChartData.length //有多少个月

                    if(chartObj) {
                        this.option.xAxis = {
                            type: 'category',
                            boundaryGap: false,
                            data: Array.from(Array(len), (v,k) => {
                                return `${k + 1}月`
                            })
                            // data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
                        },
                        this.option.series = [{
                            name: '应完成',
                            type: 'line',
                            data: res.ShouldCompleteChartData.map(s => s.Value) 
                            // data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
                        },
                        {
                            name: '已完成',
                            type: 'line',
                            data: res.CompleteChartData.map(s => s.Value) 
                            // data: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
                        }];
                        
                        this.myChart.setOption(this.option, true);
                        this.myChart.hideLoading();
                        this.$nextTick(() => {
                            this.myChart.resize();
                        })
                        window.addEventListener('resize', () => {
                            this.myChart.resize();
                        })
                    }
                })
            })
        },
        getAchInfo() {
            let postData = {
                Year: this.year,
                EmployeeId: this.empId
            }
            this.achLoading = true
            myAch.getEmployeeTeamEvaluate(postData).then(res => {
                this.achLoading = false
                this.achInfo = res || {}
            }).catch(err => {
                this.achLoading = true
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang='scss' scoped>
.wrapper{
    .box-card{
        margin-bottom: 20px;
    }
    .persion-wrapper{
        display: flex;
        >div{
            margin-right: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }
        .avatar{
            margin-right: 10px;
            img{
                width: 50px;
                height: 50px;
                border-radius: 50%;
            }
        }
        .col2{
            border-right: 1px solid #DCDFE6;
            padding-right: 10px;
        }
        .col3, .col4{
            flex: 1;
            >div{
                display: flex;
                .omit{
                    flex: 1;
                    overflow: hidden;
                }
            }
        }
    }
}

.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding:5px 0;
    margin-bottom: 10px;
}

.header-wrapper{
    display: flex;
    align-items: center;
    >div{
        margin-right: 10px;
    }
    .summary{
        flex: 1;
        text-align: right;
        >span:not(:last-child) {
            margin-right: 40px;
        }
    }
}

.task-summary{
    display: flex;
    padding: 0 10px;
    margin-bottom: 20px;
    >div{
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        color: #fff;
        height: 70px;
        padding: 10px 0;
        border-radius: 10px;
        >div{
            line-height: 100%;
            font-weight: bold;
        }
    }
    >div:not(:last-child){
        margin-right: 10px;
    }
    .sum-1{
        background: #5c9cff;
    }
    .sum-2{
        background: #93d3f9;
    }
    .sum-3{
        background: #e07f8e;
    }
}
</style>