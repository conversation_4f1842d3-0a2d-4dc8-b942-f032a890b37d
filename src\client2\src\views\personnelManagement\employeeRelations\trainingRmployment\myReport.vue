<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <el-row class="wrapper __dynamicTabContentWrapper">
                    <div class="__dynamicTabWrapper">
                        <app-table ref="mainTable1" :tab-columns="tabAllColumns" :tab-datas="ReportData" :loading="ReportLoading"
                            :isShowAllColumn="true" :isShowOpatColumn="true" :optColWidth="120"
                            :multable="false" layoutMode='simple' :isShowBtnsArea='editable' :isShowConditionArea="false">
                            <template slot="ReportTime" slot-scope="scope">{{ scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>
                            <template slot="ReportType" slot-scope="scope">{{ getStatusObj(scope.row.ReportType).label }}</template>
                            
                            
                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <el-button type="primary" @click="handleAdd">添加汇报</el-button>
                            </template>
                            
                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button @click="handleReview(scope.row, 'detail')" :type="2"></app-table-row-button>
                                <template v-if="editable">
                                    <app-table-row-button @click="handleReview(scope.row, 'update')" :type="1"></app-table-row-button>
                                    <app-table-row-button @click="handleDelRow(scope.row)" :type="3"></app-table-row-button>
                                </template>
                            </template>
                        </app-table>
                    </div>
                </el-row>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="999" type text="关闭"></app-button>
                <!-- 确认 -->
                <!-- <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button> -->
            </template>
        </app-dialog>
        <!-- 添加/编辑/查看 我的报告 -->
        <my-report-create v-if="dialogCreatePlanFormVisible" @closeDialog="closeCreatePlanDialog"
        @saveSuccess="handleCreatePlanSaveSuccess" :id="selectRow.Id" :employeeId="employeeId"
        :dialogFormVisible="dialogCreatePlanFormVisible" :dialogStatus="dialogCreatePlanStatus"></my-report-create>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as EmployeeReportApi from "@/api/personnelManagement/EmployeeReport";
import myReportCreate from "./myReportCreate"
import { vars } from '../common/vars'
export default {
    name: "trainingRmployment-myReportPage",
    directives: {},
    components: {
        myReportCreate
    },
    mixins: [indexPageMixin],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "我的报告";
            } else if (this.dialogStatus == "detail") {
                return "报告详情";
            }
            return "";
        },
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            default: "update",
        },
        id: {
            type: String,
            default: "",
        },
        employeeId: {
            type: String,
            default: "",
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    if (this.dialogStatus != "create" && this.employeeId) {
                        this.getReport();// 查询 我的报告
                    }
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            ReportTypeEnum: vars.ReportTypeEnum,
            dialogCreatePlanFormVisible: false,
            selectRow: {},
            dialogCreatePlanStatus: 'create',

            loading: false,
            // 我的报告
            ReportData: [],
            ReportLoading: false,
            tabAllColumns: [
                {attr: {prop: "ReportType",label: "汇报类型"}, slot: true},
                {attr: {prop: "ReportTime",label: "汇报时间"}, slot: true},
            ],
        };
    },
    methods: {
        handleDelRow(row){
            let self = this;
            self.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                EmployeeReportApi.del([row.Id]).then(res => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getReport();
                });
            });
        },
        // 查询 我的报告
        getReport(){
            this.ReportLoading = true
            EmployeeReportApi.getList({ EmployeeId: this.employeeId }).then(res => {
                this.ReportData = res;
                this.ReportLoading = false
            }).catch(err => {
                this.ReportLoading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 添加弹窗
        handleAdd() {
            this.selectRow = {};
            this.dialogCreatePlanStatus = 'create';
            this.dialogCreatePlanFormVisible = true
        },
        // 编辑/查看弹窗
        handleReview(row, optType) {
            this.selectRow = row;
            this.dialogCreatePlanStatus = optType;
            this.dialogCreatePlanFormVisible = true
        },
        // 添加新人培养计划 成功回调
        handleCreatePlanSaveSuccess(){
            this.getReport()
            this.closeCreatePlanDialog()
            this.$emit('reload')
        },
        // 关闭 添加新人培养计划弹窗
        closeCreatePlanDialog(){
            this.dialogCreatePlanFormVisible = false
        },
        // 列表状态转换
        getStatusObj(status) {
            return this.ReportTypeEnum.find(s => s.value == status) || {};
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
    height: 360px;
}
</style>