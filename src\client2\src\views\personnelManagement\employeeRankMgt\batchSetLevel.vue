<template>
    <div>
        <app-dialog title="初始职级设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <div class="wrapperMain __dynamicTabContentWrapper">
                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter"
                    @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="KeyWords">
                            <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}'
                                clearable v-model.trim="listQuery.KeyWords" placeholder="搜索员工姓名/工号"></el-input>
                        </template>
                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <el-button type="primary" @click="againAdd">继续添加</el-button>
                        </template>
                    </app-table-form>
                    <div class="content __dynamicTabWrapper">
                        <el-table :data="dataSourceList.filter(data => !listQuery.KeyWords || data.Name.toLowerCase().includes(listQuery.KeyWords.toLowerCase()) || data.Number.toLowerCase().includes(listQuery.KeyWords.toLowerCase()))" height="450">
                            <el-table-column type="index" label="序号" width="50"></el-table-column>
                            <el-table-column prop="OldEmployeeLevelType" label="员工职级">
                                <template slot-scope="scope">
                                    {{scope.row.OldEmployeeLevelType?leveEnum.find(s=>s.value===scope.row.OldEmployeeLevelType).label:'无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="Name" label="姓名"></el-table-column>
                            <el-table-column prop="Number" label="工号"></el-table-column>
                            <el-table-column prop="DepartmentName" label="部门"></el-table-column>
                            <el-table-column prop="NewEmployeeLevelType" label="调整后" width="120">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.NewEmployeeLevelType" style="width:70px;">
                                        <el-option v-for="item in leveEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <i :class="isUp(scope.row)?'el-icon-top':'el-icon-bottom'"
                                    v-if="scope.row.OldEmployeeLevelType&&scope.row.NewEmployeeLevelType&&scope.row.OldEmployeeLevelType!=scope.row.NewEmployeeLevelType"
                                    :style="`font-size: 22px;margin-left: 5px;vertical-align: middle;color:${isUp(scope.row)?'#67c23a':'#f56c6c'}`"></i>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <!-- 关闭 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>

                <app-button @click="createData" :buttonType="1" :disabled="disabledBtn" style="margin-left:10px;"></app-button>
            </template>
        </app-dialog>

        
        <!-- 选择人员 -->
        <emp-selector ref="selectorEmpSelector" key="btnAddSelector" :showType="3" :multiple="true"
            :beforeConfirm='handleAddEmpSelector' submit-text="下一步"
            :list="dataSourceList" @change="handleChangeEmpSelector">
        </emp-selector>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import {leveEnum} from "@/components/UserLeveIcon/enum";
import * as employeeLevelHistoryApi from "@/api/personnelManagement/employeeLevelHistory";

import empSelector from '@/views/common/empSelector'
export default {
    name: "employeeRankMgt-batchSetLevel",
    mixins: [indexPageMixin],
    components: {
        empSelector,
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.dataSourceList = JSON.parse(JSON.stringify(this.tabData))
                }
            },
            immediate: true
        }
    },
    props: {
        tabData: {
            required: true,
            type: Array,
            default: ()=>[]
        },
    },
    created() {
        console.log(this.tabData)
    },
    data() {
        return {
            leveEnum,
            disabledBtn: false,
            /******************* 表格 *******************/
            layoutMode: 'simple',
            dataSourceList: [],
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                // { prop: "AttendanceStatusList", label: "考勤状态"},
                // { prop: "NoWorkArrange", label: "工作计划"},
            ],
        }
    },
    methods: {
        createData(){
            let self = this, postData = JSON.parse(JSON.stringify(self.dataSourceList));
            if(postData.length > 0){
                self.disabledBtn = true;
                employeeLevelHistoryApi.EditEmployeeLevelType(postData).then(res => {
                    self.disabledBtn = false;
                    self.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    self.$refs.appDialogRef.createData();
                })
                .catch(err => {
                    self.disabledBtn = false;
                });
            }
        },
        // 继续添加 显示选择人员弹窗
        againAdd(){
            this.$refs.selectorEmpSelector.handleShow()
        },
        // 选中负责人
        handleChangeEmpSelector(users) {
            console.log(users)
            let newArr = users.map(s=>{
                if(this.dataSourceList.findIndex(q=>q.EmployeeId == s.EmployeeId)>-1) {
                    s['OldEmployeeLevelType'] =this.dataSourceList.find(q=>q.EmployeeId == s.EmployeeId).OldEmployeeLevelType
                    s['NewEmployeeLevelType'] =this.dataSourceList.find(q=>q.EmployeeId == s.EmployeeId).NewEmployeeLevelType
                } else {
                    s['OldEmployeeLevelType'] = JSON.parse(JSON.stringify(s)).EmployeeLevelType || 1;
                    if(!s['NewEmployeeLevelType']){
                        s['NewEmployeeLevelType'] = JSON.parse(JSON.stringify(s)).EmployeeLevelType || 1;
                    }
                }
                return s
            })
            this.dataSourceList = JSON.parse(JSON.stringify(newArr))
        },
        // 负责人 数量限制
        handleAddEmpSelector(users) {
            if (users && users.length == 0) {
                this.$message({
                    message: '请选择人员',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleFilter(){},
        onResetSearch(){},
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleDelRow(index){
            let self = this;
            self.$confirm(`是否确认移除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                self.dataSourceList.splice(index, 1)
                self.$forceUpdate();
            });
        },
        isUp(row){
            if(row.OldEmployeeLevelType>row.NewEmployeeLevelType){
                return false
            }
            if(row.OldEmployeeLevelType<row.NewEmployeeLevelType){
                return true
            }
        },
    }
};
</script>

<style lang='scss' scoped>
.colorBlue{
    color: #409EFF;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
.wrapperMain{
    display: flex;
}
</style>