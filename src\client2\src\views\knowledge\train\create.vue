<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1200' :maxHeight="750" >
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                <el-tabs v-model="activeName">
                    <el-tab-pane label="课程内容" name="first">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="课程封面" prop="LogoPath">
                                    <app-upload-file style="width:100%;height:100%" :max='1' :fileSize="1024 * 1024 * 2" :fileType='1' :value='CoverFileList' :readonly="!editable" @change='handleUpChange' :preview='true'></app-upload-file>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="类型" prop="TrainsClassificationPath">
                                    <el-cascader :disabled="!editable" v-model="formData.TrainsClassificationPath" @change='handleValidField' :options="trainsClassificationList" :props="{ checkStrictly: true, emitPath: false }" clearable></el-cascader>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>

                            <el-form-item label="课程名称" prop="TrainsName">
                                <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.TrainsName"></el-input>
                            </el-form-item>
    
                            <el-form-item label="负责人" prop="PrincipalEmployeeList">
                                <emp-selector :readonly='!editable' :showType="2" :multiple="true" :list="formData.PrincipalEmployeeList"
                                :beforeConfirm='handleBeforeConfirmManager'
                                @change="handleChangeManager" style="margin-left: 0px;"></emp-selector>
                            </el-form-item>
    
                            <el-form-item label="培训目的" prop="TrainsObjective">
                                <el-input :disabled="!editable" maxlength="100" type="text" v-model="formData.TrainsObjective"></el-input>
                            </el-form-item>
                            <el-form-item label="截至日期" prop="EndTime">
                                <el-date-picker format='yyyy-MM-dd' :disabled="!editable" value-format='yyyy-MM-dd' v-model="formData.EndTime" type="date" placeholder=""></el-date-picker>
                                <span style="margin-left: 20px; color: #02a7fd;"><span style="font-weight: bold;">注意事项：</span>读书分享必须要填，需根据该字段进行统计。</span>
                            </el-form-item>
                            
                            <el-form-item  v-if="dialogStatus != 'detail'" label="课程内容" prop="TrainsDescribe">
                                <editor-bar v-if="editable" :value="formData.TrainsDescribe" @edit="formData.TrainsDescribe = arguments[0]"></editor-bar>
                                <div class="divUeditor ql-editor" v-html="formData.TrainsDescribe" v-if="!editable"></div>
                            </el-form-item>
                            <el-form-item label="相关资料"  v-if="dialogStatus != 'detail'">
                                <app-uploader :readonly='!editable' accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            </el-form-item>
                        </el-row>

                    </el-tab-pane>
                    <el-tab-pane label="课程设置" name="second">
                        <el-card shadow="never" header="学习设置">
                            <el-form-item label="可见范围" prop="TrainSettingOperationModel.ViewRange"
                            :rules="{validator: (rule, value, callback) => validatorViewRange(rule, value, callback, formData), trigger: ['change', 'blur']}">
                                <div class="cl">
                                    <el-select :disabled="!editable" class="fl" @change='() => formData.TrainSettingOperationModel.ViewRangeValues = null' style="margin-right:10px;" v-model="formData.TrainSettingOperationModel.ViewRange">
                                        <el-option v-for="item in viewRangeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <div class="fl" v-if="formData.TrainSettingOperationModel.ViewRange == 2">
                                        <el-button :disabled="!editable" type="text" @click="handleShowTree('ViewRangeDepartmentList', 'ViewRange')">选择部门</el-button>
                                    </div>
                                    <el-row class="fl" v-if="formData.TrainSettingOperationModel.ViewRange == 3">
                                        <div class="fl">入职时间为</div>
                                        <div class="fl" style="padding:0 10px">
                                            <el-date-picker v-model="formData.TrainSettingOperationModel.ViewRangeValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 130px;"></el-date-picker>
                                        </div>
                                        <div class="fl">以后的</div>
                                    </el-row>
                                    <emp-selector
                                        class="fl"
                                        style="width:300px;"
                                        v-if="formData.TrainSettingOperationModel.ViewRange == 4"
                                        :readonly="!editable"
                                        key="ccusers"
                                        :showType="2"
                                        :multiple="true"
                                        :beforeConfirm='handleBeforeConfirm'
                                        :list="formData.TrainSettingOperationModel.ViewRangeEmployeeList"
                                        @change="handleViewRange($event, 'ViewRangeEmployeeList', 'ViewRange')"
                                    ></emp-selector>
                                </div>
                                <ul v-if="formData.TrainSettingOperationModel.ViewRange == 2 && formData.TrainSettingOperationModel.ViewRangeDepartmentList.length>0" class="dUl">
                                    <li class="omit" :title="dn.DepartmentName" v-for="(dn,dnI) in formData.TrainSettingOperationModel.ViewRangeDepartmentList" :key="dnI">{{dn.DepartmentName}}</li>
                                </ul>
                            </el-form-item>
                            <el-form-item label="可见时段">
                                <el-row class="rowBox">
                                    <span style="width: 200px;">
                                        <el-radio-group v-model="formData.TrainSettingOperationModel.VisibleTime">
                                            <el-radio :label="1">不限制</el-radio>
                                            <el-radio :label="2">限制</el-radio>
                                        </el-radio-group>
                                    </span>
                                    <span style="padding-left: 40px;" v-if="formData.TrainSettingOperationModel.VisibleTime==2">
                                        <el-form-item label="工作日限制时段(周末/法定节假日不限制)" label-width="275px" style="margin-bottom:0" prop="VisibleTimeValues" class="inputLeft" :rules="{required: true, message:'工作日限制时段',trigger: ['change', 'blur']}">
                                            <el-time-picker format='HH:mm' :disabled="!editable" value-format='HH:mm' is-range
                                                v-model="formData.VisibleTimeValues" range-separator="至" start-placeholder="开始时间"
                                                end-placeholder="结束时间" placeholder="选择时间范围">
                                            </el-time-picker>
                                        </el-form-item>
                                    </span>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="是否必学">
                                <el-radio-group v-model="formData.TrainSettingOperationModel.IsWillLearn">
                                    <el-radio :label="1">非必学</el-radio>
                                    <el-radio :label="2">必学</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="人员名单" prop="TrainSettingOperationModel.IsWillLearnKey" v-if="formData.TrainSettingOperationModel.IsWillLearn == 2"
                            :rules="{validator: (rule, value, callback) => validatorIsWillLearnKey(rule, value, callback, formData), trigger: ['change', 'blur']}">
                                <div class="cl">
                                    <el-select :disabled="!editable" class="fl" style="margin-right:10px;" @change='() => formData.TrainSettingOperationModel.IsWillLearnValues = null' v-model="formData.TrainSettingOperationModel.IsWillLearnKey">
                                        <el-option v-for="item in viewRangeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <div class="fl" v-if="formData.TrainSettingOperationModel.IsWillLearnKey == 2">
                                        <el-button :disabled="!editable" type="text" @click="handleShowTree('IsWillLearnDepartmentList', 'IsWillLearnKey')">选择部门</el-button>
                                    </div>
                                    <el-row class="fl" v-if="formData.TrainSettingOperationModel.IsWillLearnKey == 3">
                                        <div class="fl">入职时间为</div>
                                        <div class="fl" style="padding:0 10px">
                                            <el-date-picker v-model="formData.TrainSettingOperationModel.IsWillLearnValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 130px;"></el-date-picker>
                                        </div>
                                        <div class="fl">以后的</div>
                                    </el-row>
                                    <emp-selector
                                        class="fl"
                                        style="width:300px;"
                                        v-if="formData.TrainSettingOperationModel.IsWillLearnKey == 4"
                                        :readonly="!editable"
                                        key="ccusers"
                                        :showType="2"
                                        :multiple="true"
                                        :beforeConfirm='handleBeforeConfirm'
                                        :list="formData.TrainSettingOperationModel.IsWillLearnEmployeeList"
                                        @change="handleViewRange($event, 'IsWillLearnEmployeeList', 'IsWillLearnKey')"
                                    ></emp-selector>
                                </div>
                                <ul v-if="formData.TrainSettingOperationModel.IsWillLearnKey == 2 && formData.TrainSettingOperationModel.IsWillLearnDepartmentList.length>0" class="dUl">
                                    <li class="omit" :title="dn.DepartmentName" v-for="(dn,dnI) in formData.TrainSettingOperationModel.IsWillLearnDepartmentList" :key="dnI">{{dn.DepartmentName}}</li>
                                </ul>
                            </el-form-item>
                            <el-form-item label="循环周期">
                                <el-row class="rowBox">
                                    <span style="width: 200px;">
                                        <el-radio-group v-model="formData.TrainSettingOperationModel.StudyCycleTime">
                                            <el-radio :label="1">不循环</el-radio>
                                            <el-radio :label="2">循环</el-radio>
                                        </el-radio-group>
                                    </span>
                                    <template v-if="formData.TrainSettingOperationModel.StudyCycleTime==2">
                                        <span style="padding-left: 40px;">
                                            <el-form-item label="选择周期" style="margin-bottom:0" prop="TrainSettingOperationModel.StudyCycleTimeKey" :rules="{required: true, message:'周期不能为空',trigger: ['change', 'blur']}">
                                                <el-select v-model="formData.TrainSettingOperationModel.StudyCycleTimeKey" placeholder="请选择">
                                                    <el-option v-for="item in studyCycleTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </span>
                    <!-- StudyCycleTime:  1, // 学习循环周期 1:不循环 2:循环
                    StudyCycleTimeKey: 1, // 学习循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                    StudyCycleTimeValues: '', // 学习循环周期Value -->
                                        <span>
                                            <el-form-item label="开始时间" prop="TrainSettingOperationModel.StudyCycleTimeValues" :rules="{required: true, message:'开始时间不能为空',trigger: ['change', 'blur']}" style="margin-bottom:0">
                                                <el-date-picker v-model="formData.TrainSettingOperationModel.StudyCycleTimeValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                            </el-form-item>
                                        </span>
                                    </template>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="学习时长">
                                <el-row class="rowBox">
                                    <span style="width: 200px;">
                                        <el-radio-group v-model="formData.TrainSettingOperationModel.LearningTime">
                                            <el-radio :label="1">不限制</el-radio>
                                            <el-radio :label="2">限制时长</el-radio>
                                        </el-radio-group>
                                    </span>
                                    <span style="padding-left: 40px;" v-if="formData.TrainSettingOperationModel.LearningTime==2">
                                        <el-form-item label="时长设置" style="margin-bottom:0" prop="TrainSettingOperationModel.LearningTimeValue" class="inputLeft" :rules="{required: true, message:'时长不能为空',trigger: ['change', 'blur']}">
                                            <el-input-number style="width:75px;" :disabled="!editable" v-model="formData.TrainSettingOperationModel.LearningTimeValue" :min="1" :max="999" label="描述文字"
                                            :controls="false" :step="1" step-strictly>
                                            </el-input-number>分钟
                                        </el-form-item>
                                    </span>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="学时" prop="TrainSettingOperationModel.Period" class="inputLeft" :rules="{required: true, message:'学时不能为空',trigger: ['change', 'blur']}">
                                <el-input-number style="width:75px;" :disabled="!editable" v-model="formData.TrainSettingOperationModel.Period" :min="0" :max="10" label="描述文字"
                                :controls="false" :step="0.5" step-strictly>
                                </el-input-number>
                            </el-form-item>
                        </el-card>
                        <el-card shadow="never" header="考试设置">
                            <!-- 课程设置  干掉了  考试相关设置  只留下了 绑定课程 -->
                            <el-form-item label="关联试卷" prop="TrainSettingOperationModel.ExamTypeValue" style="margin-bottom:0">
                                <span style="padding:0">
                                    <el-button type="text" @click="selectTestPaperVisible=true">选择</el-button>
                                </span>
                                <span style="margin-left: 20px;" v-if="formData.TrainSettingOperationModel.ExamTypeValue"><el-tag size="medium" @close="handleCloseTag" closable>{{formData.TrainSettingOperationModel.ExamTypeValueName}}</el-tag></span>
                            </el-form-item>

                            <!-- <el-form-item label="考试类型">
                                <el-row class="rowBox">
                                    <span style="width: 200px;">
                                        <el-select v-model="formData.TrainSettingOperationModel.ExamType" placeholder="请选择">
                                            <el-option v-for="item in examTypeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                    </span>
                                    <span style="padding-left: 40px;min-width:300px;">
                                        <el-form-item label="关联试卷" prop="TrainSettingOperationModel.ExamTypeValue" :rules="{required: true, message:'关联试卷不能为空',trigger: ['change', 'blur']}" v-if="formData.TrainSettingOperationModel.ExamType!=1" style="margin-bottom:0">
                                            <span style="padding:0">
                                                <el-button type="text" @click="selectTestPaperVisible=true">选择</el-button>
                                            </span>
                                            <span style="margin-left: 20px;" v-if="formData.TrainSettingOperationModel.ExamTypeValue"><el-tag size="medium" @close="handleCloseTag" closable>{{formData.TrainSettingOperationModel.ExamTypeValueName}}</el-tag></span>
                                        </el-form-item>
                                    </span>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="关联人员" prop="TrainSettingOperationModel.ExamTypeKey" v-if="formData.TrainSettingOperationModel.ExamType != 1"
                            :rules="{validator: (rule, value, callback) => validatorExamTypeKey(rule, value, callback, formData), trigger: ['change', 'blur']}">
                                <div class="cl">
                                    <el-select :disabled="!editable" class="fl" @change='() => formData.TrainSettingOperationModel.ExamTypeValues = null' style="margin-right:10px;" v-model="formData.TrainSettingOperationModel.ExamTypeKey">
                                        <el-option v-for="item in viewRangeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <div class="fl" v-if="formData.TrainSettingOperationModel.ExamTypeKey == 2">
                                        <el-button :disabled="!editable" type="text" @click="handleShowTree('ExamTypeDepartmentList', 'ExamTypeKey')">选择部门</el-button>
                                    </div>
                                    <el-row class="fl" v-if="formData.TrainSettingOperationModel.ExamTypeKey == 3">
                                        <div class="fl">入职时间为</div>
                                        <div class="fl" style="padding:0 10px">
                                            <el-date-picker v-model="formData.TrainSettingOperationModel.ExamTypeValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 130px;"></el-date-picker>
                                        </div>
                                        <div class="fl">以后的</div>
                                    </el-row>
                                    <emp-selector
                                        class="fl"
                                        style="width:300px;"
                                        v-if="formData.TrainSettingOperationModel.ExamTypeKey == 4"
                                        :readonly="!editable"
                                        key="ccusers"
                                        :showType="2"
                                        :multiple="true"
                                        :beforeConfirm='handleBeforeConfirm'
                                        :list="formData.TrainSettingOperationModel.ExamTypeEmployeeList"
                                        @change="handleViewRange($event, 'ExamTypeEmployeeList', 'ExamTypeKey')"
                                    ></emp-selector>
                                </div>
                                <ul v-if="formData.TrainSettingOperationModel.ExamTypeKey == 2 && formData.TrainSettingOperationModel.ExamTypeDepartmentList.length>0" class="dUl">
                                    <li class="omit" :title="dn.DepartmentName" v-for="(dn,dnI) in formData.TrainSettingOperationModel.ExamTypeDepartmentList" :key="dnI">{{dn.DepartmentName}}</li>
                                </ul>
                            </el-form-item>
                            <el-form-item label="循环周期">
                                <el-row class="rowBox">
                                    <span style="width: 200px;">
                                        <el-radio-group v-model="formData.TrainSettingOperationModel.ExamCycleTime">
                                            <el-radio :label="1">不循环</el-radio>
                                            <el-radio :label="2">循环</el-radio>
                                        </el-radio-group>
                                    </span>
                                    <template v-if="formData.TrainSettingOperationModel.ExamCycleTime==2">
                                        <span style="padding-left: 40px;">
                                            <el-form-item label="选择周期" style="margin-bottom:0" prop="TrainSettingOperationModel.ExamCycleTimeKey" :rules="{required: true, message:'周期不能为空',trigger: ['change', 'blur']}">
                                                <el-select v-model="formData.TrainSettingOperationModel.ExamCycleTimeKey" placeholder="请选择">
                                                    <el-option v-for="item in studyCycleTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </span>
                                        <span>
                                            <el-form-item label="开始时间" prop="TrainSettingOperationModel.ExamCycleTimeValues" :rules="{required: true, message:'开始时间不能为空',trigger: ['change', 'blur']}" style="margin-bottom:0">
                                                <el-date-picker v-model="formData.TrainSettingOperationModel.ExamCycleTimeValues" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                            </el-form-item>
                                        </span>
                                    </template>
                                </el-row>
                            </el-form-item> -->
                        </el-card>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>
    <v-tree
        v-if="dialogTreeVisible"
        @saveSuccess="handleTreeSaveSuccess"
        @closeDialog="handleTreeCloseDialog"
        :dialogFormVisible="dialogTreeVisible"
        :checkedList='checkedList'>
    </v-tree>
    <!-- 选择试卷 -->
    <select-test-paper v-if="selectTestPaperVisible" :ids="formData.TrainSettingOperationModel.ExamTypeValue"
        @saveSuccess="selectTestPaperSuccess" @closeDialog="selectTestPaperVisible=false"
        :dialogFormVisible="selectTestPaperVisible" :multiple="true" />
</div>
</template>

<script>
import empSelector from "../../common/empSelector";
import * as train from "@/api/informationCenter/train";
import EditorBar from "@/components/QuillEditor/index.vue";
import selectTestPaper from './selectTestPaper'
import * as CaseShareApi from '@/api/knowledge/CaseShare'

import * as trainsClassification from "@/api/informationCenter/trainsClassification";
import { vars } from '../common/vars'
import vTree from './tree';
export default {
    name: "trainsClassifications-create",
    directives: {},
    components: {
        EditorBar,
        vTree,
        selectTestPaper,
        empSelector
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        selectTypeId: {
            type: String,
            default: ""
        },
        defaultRow: {
            default: null
        },
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            this.activeName='first';
            this.gettrainsClassificationList();

                console.log(this.defaultRow)
            if (!val) {
                this.fileList = [];
                this.CoverFileList = [];
            }
            if (val) {
                // this.resetFormData();
                this.formData = this.$options.data().formData
                if(this.defaultRow){
                    this.getCaseShareDetail();
                }
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                if(this.defaultRow){
                    return "转为课程";
                }
                return "创建课程";
            } else if (this.dialogStatus == "update") {
                return "编辑课程";
            } else if (this.dialogStatus == "detail") {
                return "课程详情";
            }
        }
    },

    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            /** 选择试卷 */
            selectTestPaperVisible: false,
            
            /** 选择部门 */
            depKeyName: '',
            depValidName: '',
            checkedList:[],
            dialogTreeVisible:false,


            viewRangeTypes: vars.trainEnum.viewRangeTypes, // 可见范围 1:所有人 2:按部门 3:按入职时间 4:自定义
            examTypeTypes: vars.trainEnum.examTypeTypes, // 是否必考 1:不考 2:必考 3:选考
            studyCycleTypes: vars.trainEnum.studyCycleTypes, // 学习循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
            activeName: 'first',
            isClear: false,
            trainsClassificationList: [],
            fileList: [], //图像信息[{Id: '', Path: ''}]
            CoverFileList: [],
            formLoading: false,
            disabledBtn: false,
            rules: {

                LogoPath: {
                    fieldName: "课程封面",
                    rules: [{
                        required: true
                    }],
                },

                TrainsClassificationPath: {
                    fieldName: "类型",
                    rules: [{
                        required: true
                    }]
                },
                TrainsName: {
                    fieldName: "课程名称",
                    rules: [{
                        required: true
                    }]
                },
                // PrincipalEmployeeList: [{
                //     type: "array",
                //     required: true,
                //     message: "请选择负责人",
                //     trigger: "change"
                // }],
                PrincipalEmployeeList: {
                    fieldName: "请选择负责人",
                    rules: [{
                        required: true
                    }]
                },
                TrainsObjective: {
                    fieldName: "培训目的",
                    rules: [{
                        required: true
                    }]
                },
                TrainsDescribe: {fieldName: "课程内容",rules: [{required: true}, {max: 20000, trigger: "blur"}]},
                // ViewRange: {fieldName: "可见范围",rules: [{required: true}, {trigger: ['change', 'blur']}]},
                // StudyCycleTimeKey: {fieldName: "周期",rules: [{required: true}, {trigger: ['change', 'blur']}]},
                // TrainSettingOperationModel: {
                //     StudyCycleTimeValues: {fieldName: "开始时间",rules: [{required: true}, {trigger: ['change', 'blur']}]},
                //     ExamCycleTimeKey: {fieldName: "周期",rules: [{required: true}, {trigger: ['change', 'blur']}]},
                //     ExamCycleTimeValues: {fieldName: "开始时间",rules: [{required: true}, {trigger: ['change', 'blur']}]},
                // },
            },
            labelWidth: "100px",
            formData: {
                Id: "",
                AttachmentList: [], // 
                PrincipalEmployeeList: [],
                TrainsClassificationId: "",
                TrainsClassificationPath: "",
                IsShow: true,
                TrainsName: "",
                TrainsObjective: "",
                EndTime: "",
                TrainsDescribe: "",
                Logo: null,
                LogoPath: "",

                TrainSettingOperationModel: {
                    ViewRange: 1, // 可见范围 1:所有人 2:按部门 3:按入职时间 4:自定义
                    ViewRangeValues: '', // 可见范围 的值   为空  或 id集合 字符串
                    ViewRangeEmployeeList: [],
                    ViewRangeDepartmentList: [],

                    IsWillLearn:  1, // 是否 必学  1 非必学  2 必学
                    IsWillLearnKey: 1, // 是否必学-人员名单 1:所有人 2:按部门 3:按入职时间 4:自定义 
                    IsWillLearnValues: '', // 是否必学-人员名单 的值   为空  或 id集合 字符串
                    IsWillLearnEmployeeList: [],
                    IsWillLearnDepartmentList: [],
                    
                    StudyCycleTime:  1, // 学习循环周期 1:不循环 2:循环
                    StudyCycleTimeKey: 1, // 学习循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                    StudyCycleTimeValues: '', // 学习循环周期Value

                    LearningTime: 1, // 学习时长 1:不限制 2:限制
                    LearningTimeValue: 60, // 学习时长Value

                    Period: 1, // 学时

                    // ExamType: 1, // 考试类型 1:不考 2:必考 3:选考
                    ExamTypeValue: '', // 考试类型关联试卷试卷Id 字符串
                    ExamTypeValueName: '', // 考试类型关联试卷试卷 名称
                    // ExamTypeKey: 1, // 是否必学-人员名单 1:所有人 2:按部门 3:按入职时间 4:自定义 
                    // ExamTypeValues: '', // 是否必学-人员名单 的值   为空  或 id集合 字符串
                    // ExamTypeEmployeeList: [],
                    // ExamTypeDepartmentList: [],

                    // ExamCycleTime:  1, // 考试循环周期 1:不循环 2:循环
                    // ExamCycleTimeKey: 1, // 考试循环周期 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                    // ExamCycleTimeValues: '', // 考试循环周期Value

                    VisibleTime: 1, // 可见时间 1.不限制 2.限制
                    VisibleTimeValue: '', // 可见时间Value
                },
                VisibleTimeValues: null, // 可见时间Value    (由于组件文件  多了一层对象之后  值无法变化  提取到外层显示)
                
            }
        };
    },
    methods: {
        getCaseShareDetail(){
            CaseShareApi.detail({ id: this.defaultRow.Id }).then(res => {
                this.formData.TrainsName = res.CaseName;
                this.formData.PrincipalEmployeeList = [{
                    EmployeeId: res.SubmitEmployeeId,
                    Name: res.SubmitEmployeeName,
                    Number: res.SubmitEmployeeNumber,
                }]
                this.formData.TrainsDescribe = res.CaseDescribe
                this.formData.AttachmentList = res.AttachmentList
            }).catch(err => {
            });
        },
        handleValidField() {
            this.$refs["formData"].validateField("TrainsClassificationPath");
        },
        // 选中试卷确定 后关闭
        selectTestPaperSuccess(val) {
            console.log(val)
            this.formData.TrainSettingOperationModel.ExamTypeValue = val.Id
            this.formData.TrainSettingOperationModel.ExamTypeValueName = val.Name
            this.$refs.formData.validateField('ExamTypeValue');
            this.selectTestPaperVisible = false
        },
        // 校验 可见范围 相关 
        validatorViewRange(rule, value, callback, formData) {
            if (value==2&&!formData.TrainSettingOperationModel.ViewRangeDepartmentList.length>0) {
                return callback(new Error('请选择部门'));
            }
            if (value==3&&!formData.TrainSettingOperationModel.ViewRangeValues) {
                return callback(new Error('请选择入职时间'));
            }
            if (value==4&&!formData.TrainSettingOperationModel.ViewRangeEmployeeList.length>0) {
                return callback(new Error('请选择自定义人员'));
            }
            return callback();
        },
        // 校验 可见范围 相关 
        validatorIsWillLearnKey(rule, value, callback, formData) {
            if (value==2&&!formData.TrainSettingOperationModel.IsWillLearnDepartmentList.length>0) {
                return callback(new Error('请选择部门'));
            }
            if (value==3&&!formData.TrainSettingOperationModel.IsWillLearnValues) {
                return callback(new Error('请选择入职时间'));
            }
            if (value==4&&!formData.TrainSettingOperationModel.IsWillLearnEmployeeList.length>0) {
                return callback(new Error('请选择自定义人员'));
            }
            return callback();
        },
        // 校验 可见范围 相关 
        validatorExamTypeKey(rule, value, callback, formData) {
            if (value==2&&!formData.TrainSettingOperationModel.ExamTypeDepartmentList.length>0) {
                return callback(new Error('请选择部门'));
            }
            if (value==3&&!formData.TrainSettingOperationModel.ExamTypeValues) {
                return callback(new Error('请选择入职时间'));
            }
            if (value==4&&!formData.TrainSettingOperationModel.ExamTypeEmployeeList.length>0) {
                return callback(new Error('请选择自定义人员'));
            }
            return callback();
        },
        // 显示选择部门弹窗
        handleShowTree(keyName, validName){
            this.depKeyName = keyName;
            this.depValidName = validName;
            let list = this.formData.TrainSettingOperationModel[keyName]
            if(list) {
                this.checkedList = list.map(s => s.DepartmentId) || []
            }
            this.dialogTreeVisible=true;
        },
        // 关闭选择部门弹窗
        handleTreeCloseDialog(){
            this.dialogTreeVisible=false;
        },
        // 选择部门弹窗 确定
        handleTreeSaveSuccess(d){
            this.formData.TrainSettingOperationModel[`${this.depKeyName}`]=[];
            this.checkedList=[];
            if(d.length>0){
                d.forEach(v => {
                    this.formData.TrainSettingOperationModel[`${this.depKeyName}`].push({
                        DepartmentId: v.Id,
                        DepartmentName: v.ParentName,
                    });
                })
                this.checkedList = this.formData.TrainSettingOperationModel[`${this.depKeyName}`].map(s=>s.DepartmentId)
                this.$refs.formData.validateField(`TrainSettingOperationModel.${this.depValidName}`);
            }
            console.log(this.formData)
            this.dialogTreeVisible=false;
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1000) {
            this.$message({
                message: '不得超过1000个',
                type: 'error'
            })
            return false
            }
            return true
        },
        // 删除关联的试卷
        handleCloseTag(){
            this.formData.TrainSettingOperationModel.ExamTypeValue = ''
            this.formData.TrainSettingOperationModel.ExamTypeValueName = ''
        },
        handleUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.LogoPath = imgs[0].Path
                this.formData.Logo = imgs[0].Id
            } else {
                this.formData.Logo = ''
                this.formData.LogoPath = ''
            }
        },
        //获取类型下拉框
        gettrainsClassificationList() {
            trainsClassification.getTreeList().then(res => {
                //产品类型下拉框数据源处理
                this.deleteChildrens(res);
                this.trainsClassificationList = [];

                res.forEach(element => {
                    if (element.label != "全部") {
                        this.trainsClassificationList.push(element);
                    }
                });
                if (this.dialogStatus == "create") {
                    if(this.selectTypeId != '00000000-0000-0000-0000-000000000000') {
                        this.formData.TrainsClassificationPath = this.selectTypeId;
                    }
                }
            });
        },

        //递归删除children为空的属性
        deleteChildrens(res) {
            res.forEach(element => {
                if (element.children.length === 0) {
                    delete element.children;
                } else {
                    this.deleteChildrens(element.children);
                }
            });
        },
        
        handleBeforeConfirmManager(users) {
            if(users && users.length > 6) {
            this.$message({
                message: '负责人添加不可超过5人',
                type: 'error'
            })
            return false
            }
            return true
        },
        handleChangeManager(users) {
            if (users && users.length > 0) {
                this.formData.PrincipalEmployeeList = users;
            } else {
                this.formData.PrincipalEmployeeList = [];
            }
            this.$refs["formData"].validateField("PrincipalEmployeeList");
        },
        // 可见范围 选择人员/选择部门
        handleViewRange(users, keyName, validName) {
            // this.depKeyName = keyName;
            // this.depValidName = validName;
            if (users && users.length > 0) {
                this.formData.TrainSettingOperationModel[`${keyName}`] = users;
            } else {
                this.formData.TrainSettingOperationModel[`${keyName}`] = [];
            }
            this.$refs["formData"].validateField(`TrainSettingOperationModel.${validName}`);
        },
        resetFormData() {
            let temp = {
                Id: "",
                PrincipalEmployeeList: [],
                TrainsClassificationId: "",
                TrainsClassificationPath: "",
                IsShow: true,
                TrainsName: "",
                TrainsObjective: "",
                EndTime: "",
                TrainsDescribe: "",
                Logo: "",
                AttachmentList: [], //
                TrainSettingOperationModel: this.$options.data().formData.TrainSettingOperationModel,
                VisibleTimeValues: null, // 可见时间Value    (由于组件文件  多了一层对象之后  值无法变化  提取到外层显示)
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            this.formLoading = true;

            train
                .detail({
                    id: this.id
                })
                .then((res) => {
                    this.formData = Object.assign({}, this.formData, res);
                    this.formData.PrincipalEmployeeList = res.PrincipalEmployeeList ?
                        res.PrincipalEmployeeList : [];

                    //绑定所属分类
                    this.formData.TrainsClassificationId = [
                        this.formData.TrainsClassificationId
                    ];
                    this.CoverFileList = [];
                    if (this.formData.LogoPath) {
                        this.CoverFileList = [{
                            Id: this.formData.Logo,
                            Path: this.formData.LogoPath
                        }];
                    }
                    this.setTrainSettingOperationModel();
                    console.log(this.formData)
                    this.formLoading = false;
                })
                .catch((err) => {
                    this.formLoading = false;
                });
        },
        // 设置 课程设置的默认值
        setTrainSettingOperationModel(){
            let formModel = this.formData.TrainSettingOperationModel;
            if (formModel) {
                /** 
                 *  可见时段 类型和值 
                 *  VisibleTime 可见时段 1.不限制 2.限制
                 *  VisibleTimeValue 可见范围值
                */
                if (formModel.VisibleTime == 2) {
                    this.formData['VisibleTimeValues'] = formModel.VisibleTimeValue?formModel.VisibleTimeValue.split(','):null
                    // let arr = formModel.VisibleTimeValue?formModel.VisibleTimeValue.split(','):[]
                    // formModel['VisibleTimeValues'] = [new Date(2020, 10, 10, arr[0].split(':')[0], arr[0].split(':')[1]),new Date(2020, 10, 10, arr[1].split(':')[0], arr[1].split(':')[1])]
                }
                
                /** 
                 *  是否必学 类型和值 
                 *  IsWillLearn 是否必学 1:非必学, 2:必学
                 *  IsWillLearnKey 必学人员名单类型 1:所有人 2:按部门 3:按入职时间 4:自定义
                */
                if (formModel.IsWillLearn == 1) {
                    formModel.IsWillLearnKey =  1
                }
                /** 
                 *  学习循环周期 类型和值 
                 *  StudyCycleTime 学习循环周期 1:不循环 2:循环
                 *  StudyCycleTimeKey 学习循环周期类型 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                */
                if (formModel.StudyCycleTime == 1) {
                    formModel.StudyCycleTimeKey = 1
                }
                /** 
                 *  学习时长 类型和值 
                 *  LearningTime 学习时长 1:不限制 2:限制
                 *  LearningTimeValue 学习时长 值
                */
                if (formModel.LearningTime == 1) {
                    formModel.LearningTimeValue = 60
                }
                

                
                // 课程设置  干掉了  考试相关设置  只留下了 绑定课程
                // /** 
                //  *  考试类型 类型和值 
                //  *  ExamType 考试类型 1:不考 2:必考 3:选考
                //  *  ExamTypeKey 考试  人员名单类型 1:所有人 2:按部门 3:按入职时间 4:自定义
                //  *  ExamTypeValue 考试  试卷
                //  *  ExamTypeValues 考试  人员名单值
                // */
                // if (formModel.ExamType == 1) {
                //     formModel.ExamTypeKey =  1
                // }
                // /** 
                //  *  考试循环周期 类型和值 
                //  *  ExamCycleTime 考试循环周期 1:不循环 2:循环
                //  *  ExamCycleTimeKey 考试循环周期类型 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
                // */
                // if (formModel.ExamCycleTime == 1) {
                //     formModel.ExamCycleTimeKey = 1
                // }
            } else {
                this.formData.TrainSettingOperationModel = this.$options.data().formData.TrainSettingOperationModel
            }
        },
        // 提交时 课程设置的值转换 对应类型时  值需要清空
        formatTrainSettingOperationModel(formData){
            let formModel = JSON.parse(JSON.stringify(formData)).TrainSettingOperationModel, Obj = {};

            /** 
             *  可见范围 类型和值 
             *  ViewRange 可见范围类型 1:所有人 2:按部门 3:按入职时间 4:自定义
             *  ViewRangeValues 可见范围值
            */
            if (formModel.ViewRange == 1) {
                formModel.ViewRangeValues = ''
            }
            if (formModel.ViewRange == 2) {
                formModel.ViewRangeValues = formModel.ViewRangeDepartmentList.map(s=>s.DepartmentId).toString()
            }
            if (formModel.ViewRange == 4) {
                console.log(formModel.ViewRangeEmployeeList)
                formModel.ViewRangeValues = formModel.ViewRangeEmployeeList.map(s=>s.EmployeeId).toString()
            }
            delete formModel.ViewRangeEmployeeList
            delete formModel.ViewRangeDepartmentList
            

            /** 
             *  可见时段 类型和值 
             *  VisibleTime 可见时段 1.不限制 2.限制
             *  VisibleTimeValue 可见范围值
            */
            if (formModel.VisibleTime == 1) {
                formModel.VisibleTimeValue = ''
            }
            if (formModel.VisibleTime == 2) {
                formModel.VisibleTimeValue = formData.VisibleTimeValues.toString()
            }
            delete formData.VisibleTimeValues
            
            /** 
             *  是否必学 类型和值 
             *  IsWillLearn 是否必学 1:非必学, 2:必学
             *  IsWillLearnKey 必学人员名单类型 1:所有人 2:按部门 3:按入职时间 4:自定义
             *  IsWillLearnValues 必学人员名单值
            */
            if (formModel.IsWillLearn == 1) {
                formModel.IsWillLearnKey =  null
                formModel.IsWillLearnValues = ''
            }
            if (formModel.IsWillLearn == 2) {
                if (formModel.IsWillLearnKey == 1) {
                    formModel.IsWillLearnValues = ''
                }
                if (formModel.IsWillLearnKey == 2) {
                    formModel.IsWillLearnValues = formModel.IsWillLearnDepartmentList.map(s=>s.DepartmentId).toString()
                }
                if (formModel.IsWillLearnKey == 4) {
                    formModel.IsWillLearnValues = formModel.IsWillLearnEmployeeList.map(s=>s.EmployeeId).toString()
                }
            }
            delete formModel.IsWillLearnEmployeeList
            delete formModel.IsWillLearnDepartmentList

            /** 
             *  学习循环周期 类型和值 
             *  StudyCycleTime 学习循环周期 1:不循环 2:循环
             *  StudyCycleTimeKey 学习循环周期类型 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
             *  StudyCycleTimeValues 学习循环周期 值
            */
            if (formModel.StudyCycleTime == 1) {
                formModel.StudyCycleTimeKey = null
                formModel.StudyCycleTimeValues = ''
            }

            /** 
             *  学习时长 类型和值 
             *  LearningTime 学习时长 1:不限制 2:限制
             *  LearningTimeValue 学习时长 值
            */
            if (formModel.LearningTime == 1) {
                formModel.LearningTimeValue = ''
            }


            // 课程设置  干掉了  考试相关设置  只留下了 绑定课程
            // /** 
            //  *  考试类型 类型和值 
            //  *  ExamType 考试类型 1:不考 2:必考 3:选考
            //  *  ExamTypeKey 考试  人员名单类型 1:所有人 2:按部门 3:按入职时间 4:自定义
            //  *  ExamTypeValue 考试  试卷
            //  *  ExamTypeValues 考试  人员名单值
            // */
            // if (formModel.ExamType == 1) {
            //     formModel.ExamTypeKey =  null
            //     formModel.ExamTypeValue = ''
            //     formModel.ExamTypeValueName = ''
            //     formModel.ExamTypeValues = ''
            // }
            // if (formModel.ExamType == 2 || formModel.ExamType == 3) {
            //     if (formModel.ExamTypeKey == 1) {
            //         formModel.ExamTypeValues = ''
            //     }
            //     if (formModel.ExamTypeKey == 2) {
            //         formModel.ExamTypeValues = formModel.ExamTypeDepartmentList.map(s=>s.DepartmentId).toString()
            //     }
            //     if (formModel.ExamTypeKey == 4) {
            //         formModel.ExamTypeValues = formModel.ExamTypeEmployeeList.map(s=>s.EmployeeId).toString()
            //         console.log(formModel.ExamTypeValues, formModel.ExamTypeEmployeeList)
            //     }
            // }
            // delete formModel.ExamTypeEmployeeList
            // delete formModel.ExamTypeDepartmentList

            // /** 
            //  *  考试循环周期 类型和值 
            //  *  ExamCycleTime 考试循环周期 1:不循环 2:循环
            //  *  ExamCycleTimeKey 考试循环周期类型 1:7天 2:15天 3:30天 4:60天 5:90天 6:180天 7:365天
            //  *  ExamCycleTimeValues 考试循环周期 值
            // */
            // if (formModel.ExamCycleTime == 1) {
            //     formModel.ExamCycleTimeKey = null
            //     formModel.ExamCycleTimeValues = ''
            // }

            formData.TrainSettingOperationModel = formModel
            return formData
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        createData() {

            let validate = this.$refs.formData.validate();
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData = this.formatTrainSettingOperationModel(postData)
            console.log(postData.TrainSettingOperationModel)
            Promise.all([validate]).then(valid => {
                postData.TrainsClassificationId = postData.TrainsClassificationPath;
                postData.PrincipalEmployeeList = this.formData.PrincipalEmployeeList.map(
                    v => v.EmployeeId
                );
                postData.AttachmentIdList =
                    postData.AttachmentList && postData.AttachmentList.map((s) => s.Id);
                this.disabledBtn = true;
                //提交数据保存
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = train.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = train.edit(postData);
                }

                result.then(res => {
                    this.disabledBtn = false;
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                }).catch(()=>{
                    this.disabledBtn = false;
                });
            });
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleSave() {
            this.createData();
        }
    }
};
</script>

<style scoped>
.inputLeft >>> .el-input-number .el-input__inner{
  text-align: left;
  width: 70px;
}
</style>
<style lang="scss" scoped>
.dUl{
    min-width:300px;
    max-width:500px;
    margin-top:10px;
    max-height: 350px;
    overflow-y: auto;
    border:1px solid #DCDFE6;
    padding: 0px 10px;
    border-radius: 4px;
}
.el-card{
    margin-bottom: 20px;
}
.wrapper {
    display: flex;

    .left {
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }

    .right {
        width: 40%;
    }
}

.panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
}
.rowBox>span{
    float: left;
    padding:0 10px;
}
.rowBox>span:first-child{
    padding: 0;
}
</style>
