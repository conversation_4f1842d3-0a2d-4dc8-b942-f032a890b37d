<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='OrderName'>
            <el-input style="width: 100%;" v-model="listQueryParams.OrderName" placeholder=""></el-input>
          </template>
          <!-- <template slot='EmployeeName'>
            <el-input style="width: 100%;" v-model="listQueryParams.EmployeeName" placeholder=""></el-input>
          </template> -->
          <template slot='ContractNumber'>
            <el-input style="width: 100%;" v-model="listQueryParams.ContractNumber" placeholder=""></el-input>
          </template>
          <template slot='ReceivedPayment'>
            <el-select v-model="listQueryParams.ReceivedPayment" clearable placeholder="" style="width: 100%;">
                <el-option v-for="(hrs,hrsI) in remittanceStatus" :key="hrsI"  :label="hrs.label" :value="hrs.value"></el-option>
            </el-select>
          </template>
          
          
          
          <!-- <template slot='Employee'>
            <emp-selector :multiple='false' :showType='2' key='service-users' :list='listQuery.Employee'
              @change='handleChangeUsers'>
            </emp-selector>
          </template> -->
        </app-table-form>
      </template>

      <template slot='ContractNumber' slot-scope="scope">
        {{ scope.row.ContractNumber || '无' }}
      </template>
      <template slot="Employee" slot-scope="scope">{{ scope.row.Employee | nameFilter }}</template>
      <!-- <template
        slot="SignatureTime"
        slot-scope="scope"
      >{{ scope.row.SignatureTime | dateFilter('YYYY-MM-DD HH:mm') }}</template> -->
      <!-- <template slot="OrderState" slot-scope="scope">
        <span
          class="item-status"
          :style="{color: getStatusObj(scope.row.OrderState).color }"
        >{{ getStatusObj(scope.row.OrderState).label }}</span>
      </template> -->
      <template slot="ReceivedPayment" slot-scope="scope">
        <span
          :class="`status-field-${scope.row.ReceivedPayment}`"
        >{{ scope.row.ReceivedPayment | receivedPayment }}</span>
      </template>
      <template slot="ImplementList" slot-scope="scope">
        <el-popover
          placement="left-start"
          title="关联项目/工程"
          width="300"
          trigger="hover"
          >
          <template v-if="scope.row.ImplementList && scope.row.ImplementList.length > 0">
            <div class="imp-item-wrapper" v-for="(item, idx) in scope.row.ImplementList" :key="idx">
              <div class="imp-number" v-if="item.EngineeringNumber" :title="`【${item.EngineeringNumber}】`">
                【{{ item.EngineeringNumber }}】
              </div>
              <div class="imp-name" :title="`${item.Name}`">
                {{ item.Name }}
              </div>
              <!-- <div class="imp-prog">
                <el-progress :percentage="item.Progress"></el-progress>
              </div> -->
            </div>
          </template>
          <no-data v-else></no-data>

          <div style="max-width: 85%;" slot="reference">
            <el-button type="text">
              已关联（{{ scope.row.ImplementList.length }}）
            </el-button>
          </div>
        </el-popover>
      </template>
      
    </listSelector>
  </div>
</template>

<script>
// import { vars } from "../common/vars"
// import * as afterService from "@/api/afterSalesMgmt/afterService";
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
// import normarEmpSelector from "../afterSalesMgmt/common/normarEmpSelector";
import { vars } from "../salesMgmt/common/vars";
// import * as orderVars from './salesMgmt/common/vars'
// import * as maintenCenterVars from './maintenCenter/common/vars'
import noData from "@/views/common/components/noData";
export default {
  name: "order-selector",
  components: {
    listSelector,
    noData,
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    receivedPayment(status) {
      const statusObj = vars.orderMgmt.remittanceStatus.find(
        (s) => s.value == status
      );
      if (statusObj) {
        return statusObj.label;
      }
      return "";
    },

  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },
  watch: {
    
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList(val) {
      if (val && val.length > 0) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
      }else{
        this.listSelectorCheckedData = [];
      }
    },
  },
  data() {
    return {
      remittanceStatus: vars.orderMgmt.remittanceStatus,
      listQueryParams: {
        ContractNumber: '',
        OrderName: '',
        // EmployeeName: '',
        ReceivedPayment: '',
        IsSelectList: true
      },
      tableSearchItems: [
        { prop: "ContractNumber", label: "合同编号" },
        { prop: "OrderName", label: "订单名称" },
        { prop: "ReceivedPayment", label: "是否结算" },
        
        // { prop: "EmployeeName", label: "业务员" },
        // { prop: "Employee", label: "负责人" },
      ],
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/Order/GetListPage",
      listSelectorMultiple: this.multiple,
      listSelectorCondition: this.condition ? this.condition : {},
      listSelectorTitle: "添加关联订单",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",
      listSelectorColumnData: [
        {
          attr: { prop: "ContractNumber", label: "合同编号", showOverflowTooltip: true },
          slot: true
        },
        {
          attr: { prop: "OrderName", label: "订单名称" },
        },
        // {
        //   attr: { prop: "OrderNumber", label: "订单编号" },
        // },
        
        // {
        //   attr: { prop: "ClientUnits", label: "客户单位" },
        // },
        // {
        //   attr: { prop: "RegionalName", label: "地区" },
        // },
        {
          attr: { prop: "OrderSum", label: "合同金额(元)" },
        },
        // {
        //   attr: { prop: "OrderState", label: "状态" },
        //   slot: true,
        // },
        {
          attr: { prop: "ReceivedPayment", label: "是否结算" },
          slot: true,
        },
        // {
        //   attr: { prop: "Employee", label: "业务员" },
        //   slot: true,
        // },
        // {
        //   attr: { prop: "SignatureTime", label: "签单时间" },
        //   slot: true,
        // },
        {
          attr: { prop: "ImplementList", label: "关联项目/工程" },
          slot: true,
        },
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  methods: {
    
    getStatusObj(status) {
      const statusObj = vars.orderMgmt.approvalStatus.find(
        (s) => s.value == status
      );
      return statusObj || {}
    },
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      this.listQueryParams.OrderName = ''
      // this.listQueryParams.EmployeeName = ''
      this.listQueryParams.ReceivedPayment = ''
      this.listQueryParams.ContractNumber = ''
      // this.listQuery.EmployeeId = ''
      // this.listQuery.Employee = []
      this.handleFilter()
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let list =
        data.map((s) => {
          s.Code = s.OrderNumber;
          return s;
        }) || [];
      this.$emit("changed", JSON.parse(JSON.stringify(list)));
      this.listSelectorCloseDialog();
    },
    // handleChangeUsers(users) {
    //   if (users && users.length > 0) {
    //     this.listQuery.Employee = [users[0]];
    //     this.listQuery.EmployeeId = users[0].EmployeeId;
    //   } else {
    //     this.listQuery.Employee = [];
    //     this.listQuery.EmployeeId = '';
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>

.imp-item-wrapper{
  display: flex;
  max-height: 200px;
  overflow-y: auto;
  .imp-number{
    width: 120px;
  }
  .imp-name{
    width: 180px;
    padding: 0 10px;
  }
  .imp-number, .imp-name{
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
  // .imp-prog{
  //   width: 160px;
  // }
}

</style>

<style scoped>
.status-1,.status-4,.status-7 {
    background-color: red;
}

.status-2,.status-5,.status-8 {
    background-color: blue;
}

.status-3,.status-6,.status-9 {
    background-color: orange;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

.status-field-1 {
  color: red;
}

.status-field-2 {
  color: #f09c69;
}

.status-field-3 {
  color: #9da0ad;
}

.status-field-4 {
  color: #4239fe;
}


</style>