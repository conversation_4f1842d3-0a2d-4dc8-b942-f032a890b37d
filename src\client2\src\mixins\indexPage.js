// import * as modus from '@/api/modules'

import {isShowLeft, isShowRight, headerClassMoreBtn} from '@/utils/customTab'
  

export default {
    computed: {
        //是否可编辑
        //（首页一般分为新增、编辑、详情）
        editable() {
            return this.dialogStatus == 'detail'
        }
    },
    data() {
        return {
            headerClassMoreBtn,
            /**
             * 此属性作废，不要再使用
             */
            isShowAllColumn: false, //是否显示所有列（为true表示显示所有列；并且忽略 tabAuthColumns 参数）
            /**
             * 此属性作废，不要再使用
             */
            tabAuthColumns: [], //已授权的列（以授权的列才会显示）

            // dialogStatus: '', //弹框类型：create、update、detail
            startOfTable: 0, //表格行索引(表格编号列 = 页码 * 每页条数 + 当前行索引)
            sortObj: null,
        }
    },
    watch: {
        'listQuery.PageIndex' (newval) {
            this.startOfTable = (newval - 1) * this.listQuery.PageSize
        },
        'listQuery.PageSize' (newval) {
            this.startOfTable = (this.listQuery.PageIndex - 1) * newval
        },
    },
    created() {
        // this.isShowAllColumn = this.$route.meta.isSys || this.isShowAllColumn //true系统模块;false 非系统模块，如果是系统模块所有字段都需要加载出来
        // if (!this.isShowAllColumn) {
        //     let moduleCode = this.$route.meta.moduleCode //当前菜单对应的模块代码
        //     this.getAuthColumns(moduleCode)
        // }
    },
    methods: {
        // getAuthColumns(moduleCode) {
        //     if (moduleCode) {
        //         //---------------------------该接口需要替换，目前为根据模块获取所有字段，应该换成根据模块code获取已授权的字段-------------------------------
        //         modus.getProperties(moduleCode).then(res => {
        //             if (res && res.length > 0) {
        //                 let _columns = res.map(r => r.Key)
        //                 this.tabAuthColumns = _columns
        //             }
        //         })
        //     }
        // },
        //记录首页查询条件。参数：{path: '', paras: {}}
        recordConditions(parasObj) {
            this.$store.commit('SAVE_LIST_PAGE_PARAS', {
                path: parasObj.path,
                paras: parasObj.paras
            })
        },
        //合并排序对象
        assignSortObj(obj) {
            if(!obj) {
                obj = {}
            }
            let temp = JSON.parse(JSON.stringify(obj))
            if(this.sortObj) {
                if(this.sortObj.order) {
                    temp = Object.assign({}, temp, {
                        Order: {
                            Key: this.sortObj.prop,
                            IsDesc: this.sortObj.order == 'descending'
                        }
                    })
                }
            }
            return temp
        },

        /**
         * 表格展开列
         * @param {*} h 
         * @param {*} param1 
         * @param {*} tabColumns 
         * @returns 
         */
        renderHeaderExpandColumn(h, { column }, tabColumns) {
            let colObj = tabColumns.find(s => s.attr.prop == column.property)
            let callback = colObj.attr.callBackColOptBtn
            let isShowHide = colObj.attr && colObj.attr.disabledClose !== true

            return (
                <span style='display: flex;'>
                    <span>{column.label}</span>
                    <app-table-row-button on-click={() => colObj.attr['showOverflowTooltip'] = !colObj.attr['showOverflowTooltip']} text={colObj.attr['showOverflowTooltip'] ? '展开' : '收起'}>
                        <i class={colObj.attr['showOverflowTooltip'] ? 'el-icon-arrow-right' : 'el-icon-arrow-down'}></i>
                    </app-table-row-button>
                    <span class="flex-1"></span>
                    {

                        // 如果设置了isShowMoreOpt，并且有回调函数
                        colObj.attr.isShowMoreOpt && callback ? 
                        <app-table-column-more-opt class={headerClassMoreBtn} isShowHide={isShowHide} isShowLeft={isShowLeft(colObj.attr.prop, tabColumns)} isShowRight={isShowRight(colObj.attr.prop, tabColumns)} isFixed={!!colObj.attr.fixed} sortType={colObj.attr.ascending ? 'ascending' : colObj.attr.descending ? 'descending' : ''} on-colOptBtnClick={(optCmd) => callback(column.property, optCmd)}></app-table-column-more-opt>
                        : colObj.attr.isShowMoreOpt ? 
                        <app-table-column-more-opt class={headerClassMoreBtn} on-colOptBtnClick={(optCmd) => {}}></app-table-column-more-opt>
                        : ''
                    }
                </span>
            )
        },
    }
}