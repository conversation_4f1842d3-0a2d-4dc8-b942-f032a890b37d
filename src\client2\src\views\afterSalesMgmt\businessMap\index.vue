<template>
  <div class="app-container afterSalesContainer">
    <div class="bg-white">
      <!-- <page-title
        :title="pageTitle"
        :subTitle="['添加、管理已完成安装部署的设备/项目的管理页面']"
      ></page-title> -->
      <div class="page-wrapper">
        <div class="content-left">
          <!-- isCheckbox表示是否显示单选框 -->
          <!-- isAll表示是否显示全部区域 -->
          <v-tree
            @changeNode="changeTreeNode"
            :isAll="true"
          ></v-tree>
        </div>
        <div class="content-right __dynamicTabContentWrapper">
          <div>{{ ParentName + "(" + total + ")" }}</div>
          <div class="__dynamicTabWrapper">
            <app-table
              ref="mainTable" :isShowBtnsArea='false' :layoutMode='layoutMode'
              :tab-columns="tabColumns"
              :tab-datas="tabDatas"
              :tab-auth-columns="tabAuthColumns"
              :isShowAllColumn="true"
              :loading="listLoading"
              @rowSelectionChanged="rowSelectionChanged"
              :isShowOpatColumn="true"
              :startOfTable="startOfTable"
              :multable="true"
              @sortChagned='handleSortChange'
            >
  <!-- <template slot="OrderNumber" slot-scope="scope">
                  {{ scope.row.OrderNumber ? scope.row.OrderNumber : '无' }}
              </template> -->
              <template slot="IsWarranty" slot-scope="scope">
                <span
                  :style="'color:' + (scope.row.IsWarranty > 1 ? (scope.row.IsWarranty == 4 ? '#F59A23' : '#00cc00') : '#F56C6C')"
                  >{{ scope.row.IsWarranty == 1 ? "否" : (scope.row.IsWarranty == 2 ? '是' : (scope.row.IsWarranty == 3 ? '是(未知有效期)' : '过保'))}}</span
                >
              </template>
              
              <template slot="HeatNumber" slot-scope="scope">
                  {{ scope.row.HeatNumber ? scope.row.HeatNumber : '无' }}
              </template>
              <template slot="EquipmentWorkModeName" slot-scope="scope">
                  {{ scope.row.EquipmentWorkModeName ? scope.row.EquipmentWorkModeName : '无' }}
              </template>
              <template slot="BurnerModel" slot-scope="scope">
                  {{ scope.row.BurnerModel ? scope.row.BurnerModel : '无' }}
              </template>
              <template slot="Manufacturer" slot-scope="scope">
                  {{ scope.row.Manufacturer ? scope.row.Manufacturer : '无' }}
              </template>
              <template slot="InstallTime" slot-scope="scope">
                  <!-- {{scope.row.InstallTime | dateFilter('YYYY-MM-DD HH:mm:ss')}} -->
                  {{ scope.row.InstallTime ? (scope.row.InstallTime.split(' ')[0]) : '无' }}
              </template>
              <!-- <template slot="ApprovalStatus" slot-scope="scope">
                <span
                  :style="'color:' + getColor(scope.row.ApprovalStatus) + ';'"
                  >{{ scope.row.ApprovalStatus | stateFilter }}</span
                >
              </template> -->

              <template slot="ApprovalStatus" slot-scope="scope">
                  <span
                    v-if="getApprovalObj(scope.row.ApprovalStatus).label"
                    class="item-status"
                    :style="{
                      backgroundColor: getApprovalObj(scope.row.ApprovalStatus).color
                    }"
                  >
                    <span>{{ getApprovalObj(scope.row.ApprovalStatus).label }}</span>
                  </span>
                  <span v-else>无</span>
              </template>
              <!-- <template slot="InstallTime" slot-scope="scope">{{
                scope.row.InstallTime | dateFilter("YYYY-MM-DD")
              }}</template> -->
              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :layoutMode='layoutMode'
                  :label-width="'100px'"
                  :items="tableSearchItems"
                  @onSearch="handleFilter"
                  @onReset="handleResetSearch"
                  @collapseOrExpand='handleCollapseOrExpand'
                >
                    <template slot="Code">
                        <el-input style="width: 100%;" 
                            placeholder="搜索设备编号..."
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQuery.Code"
                        ></el-input>
                    </template>
                  <template slot="Name">
                    <el-input
                      style="width: 100%;"
                      v-model.trim="listQuery.Name"
                    ></el-input>
                  </template>
                  <template slot="OrderNumber">
                    <el-input
                      style="width: 100%;"
                      v-model.trim="listQuery.OrderNumber"
                    ></el-input>
                  </template>
                  <template slot="StructureTypeName">
                    <el-input
                      style="width: 100%;"
                      v-model.trim="listQuery.StructureTypeName"
                    ></el-input>
                  </template>
                  
                  <template slot="EquipmentUseId">
                    <el-select style="width:100%;" filterable v-model="listQuery.EquipmentUseId" clearable placeholder="请选择">
                        <el-option
                        v-for="item in purposeOptions"
                        :key="item.Id"
                        :label="item.Name"
                        :value="item.Id">
                        </el-option>
                    </el-select>
                  </template>
                  <template slot="EquipmentWorkModeId">
                    <el-select style="width:100%;" filterable v-model="listQuery.EquipmentWorkModeId" clearable placeholder="请选择">
                        <el-option
                        v-for="item in workOptions"
                        :key="item.Id"
                        :label="item.Name"
                        :value="item.Id">
                        </el-option>
                    </el-select>
                  </template>
                  <!-- <template slot="InstallTime">
                    <el-date-picker
                      style="width: 100%;"
                      v-model="listQuery.InstallTime"
                      type="datetime"
                      placeholder="">
                    </el-date-picker>
                  </template> -->
                  <template slot="Range">
                    <el-date-picker
                        v-model="listQuery.Range"
                        type="daterange"
                        align="right"
                        unlink-panels
                        range-separator="-"
                        start-placeholder
                        end-placeholder
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 100%;"
                    ></el-date-picker>
                  </template>
                  <template slot="Manufacturer">
                    <el-input
                      style="width: 100%;"
                      v-model.trim="listQuery.Manufacturer"
                    ></el-input>
                  </template>
                  <template slot="BurnerModel">
                    <el-input
                      style="width: 100%;"
                      v-model.trim="listQuery.BurnerModel"
                    ></el-input>
                  </template>
                  <template slot="EquipmentAge">
                    <el-select style="width:100%;" filterable v-model="listQuery.EquipmentAge" clearable placeholder="请选择">
                        <el-option
                        v-for="item in ageOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                  </template>
                  <!-- <template slot="ProductListManagementId">
                    <treeselect
                      :normalizer="normalizer"
                      key="type2"
                      v-model="listQuery.ProductListManagementId"
                      :default-expand-level="3"
                      :options="typeTreedata"
                      :multiple="false"
                      placeholder="不选择"
                      :show-count="true"
                      :noResultsText="noResultsTextOfSelTree"
                      :noOptionsText="noOptionsTextOfSelTree"
                    >
                      <label
                        :title="node.label"
                        slot="option-label"
                        slot-scope="{
                          node,
                          shouldShowCount,
                          count,
                          labelClassName,
                          countClassName
                        }"
                        :class="labelClassName"
                      >
                        <span>
                          {{ node.label
                          }}<span v-if="shouldShowCount" :class="countClassName"
                            >({{ count }})</span
                          >
                        </span>
                      </label>
                    </treeselect>
                  </template> -->
                  <!-- <template slot="InstallTime">
                    <el-date-picker
                      style="width: 100%;"
                      v-model="listQuery.InstallTime"
                      type="daterange"
                      range-separator="-"
                      start-placeholder
                      end-placeholder
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      @change="changetimeQuantum"
                    >
                    </el-date-picker>
                  </template> -->
                  <!-- <template slot="InstallTimeStart">
                        <el-date-picker style="width: 100%;" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="listQuery.InstallTimeStart" type="date"></el-date-picker>
                      </template>
                      <template slot="InstallTimeEnd">
                        <el-date-picker style="width: 100%;" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="listQuery.InstallTimeEnd" type="date"></el-date-picker>
                      </template> -->
                  <template slot="IsWarranty">
                    <el-select v-model="listQuery.IsWarranty" clearable placeholder="不限" style="width: 100%;">
                      <el-option v-for="(wl,wlI) in warrantyList" :key="wlI"  :label="wl.label" :value="wl.value"></el-option>
                    </el-select>
                  </template>
                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                        <permission-btn
                            class="fl"
                            v-on:btn-event="onBtnClicked"
                        ></permission-btn>

                        <el-button type="primary" style="margin-left: 4px;" @click="handleBatchOpt">批量调整地区</el-button>


                        <!-- <div class="fr">
                            <el-select
                            v-model="listQuery.OrderType"
                            placeholder="请选择"
                            @change="orderTypeChange"
                            >
                            <el-option
                                v-for="item in sortOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                            </el-select>
                        </div> -->
                    </template>
                </app-table-form>
              </template>


              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button
                  v-if="rowBtnIsExists('btnDetail')"
                  @click="handleUpdate(scope.row, 'detail')"
                  :type="2"
                ></app-table-row-button>
                <!-- ChangeId 不为空表示：（未开始1 或 进行中2）且（没有未结束的投票） -->
                <app-table-row-button
                  v-if="rowBtnIsExists('btnChange')"
                  v-show="scope.row.ApprovalStatus != 4"
                  @click="handleChangeDialog(scope.row)"
                  text="编辑"
                ></app-table-row-button>
                <!-- <app-table-row-button v-if="rowBtnIsExists('btnRepair')" @click="handleRepairDialog(scope.row)" text='报修记录'></app-table-row-button> -->
                <app-table-row-button
                  v-if="rowBtnIsExists('btnDel') && scope.row.ApprovalStatus != 4"
                  @click="handleDel(scope.row)"
                  :type="3"
                ></app-table-row-button>
              </template>
            </app-table>
          </div>
          <pagination
            :total="total"
            :page.sync="listQuery.PageIndex"
            :size.sync="listQuery.PageSize"
            @pagination="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
    <!-- 创建 -->
    <create-page
      v-if="dialogFormVisible"
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
      :typeTreedata="typeTreedata"
      @reload="getList"
      :regionalName='ParentName'
      :regionalId='listQuery.RegionalId'
      :regionalLevel="regionalLevel"
    ></create-page>

    <!-- 变更 -->
    <change-page
      v-if="currentRow && dialogChangeFormVisible"
      @closeDialog="closeChangeDialog"
      @saveSuccess="handleChangeSaveSuccess"
      :dialogFormVisible="dialogChangeFormVisible"
      :dialogStatus="dialogStatus"
      :id="currentRow.Id"
      :isShowHistories="isShowHistories"
    ></change-page>
    <!-- 报修记录 -->
    <repair-page
      v-if="currentRow && dialogRepairVisible"
      @closeDialog="closeRepairDialog"
      :dialogFormVisible="dialogRepairVisible"
      :typeTreedata="typeTreedata"
      :id="currentRow.Id"
    >
    </repair-page>
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>

    <!-- 设备参数设置 -->
    <param-setting
      v-if="dialogParamFormVisible"
      @closeDialog="closeParamDialog"
      @saveSuccess="handleParamSaveSuccess"
      :dialogFormVisible="dialogParamFormVisible"
      @reload="getList"
    ></param-setting>


    <batch-page
      v-if="dialogBatchFormVisible"
      @closeDialog="closeBatchDialog"
      @saveSuccess="handleBatchSaveSuccess"
      :dialogFormVisible="dialogBatchFormVisible"
      :IdList="multipleSelection.map(s => s.Id)"
    ></batch-page>
  </div>
</template>

<script>
import * as equUse from "@/api/equipmentUse";
import * as equMode from "@/api/equipmentWorkMode";
import vTree from "./common/tree";
import indexPageMixin from "@/mixins/indexPage";
import * as businessMap from "@/api/businessMap";
import * as productListManagement from "@/api/systemManagement/productListManagement";
// import { downloadFile } from "@/utils/index";
import { listToTreeSelect } from "@/utils";
import createPage from "./create";
import changePage from "./change";
import repairPage from "./repair";
import { vars } from "../../salesMgmt/common/vars";
import vExport from "@/components/Export/index";
import paramSetting from './paramSetting';
import batchPage from './batch'
export default {
  name: "businessMapIndex",
  mixins: [indexPageMixin],
  components: {
    vTree,
    createPage,
    changePage,
    repairPage,
    vExport,
    paramSetting,
    batchPage,
  },
  computed: {
    pageTitle() {
      return "业务地图";
    }
  },

  watch: {},
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    receivedPayment(status) {
      const statusObj = vars.orderMgmt.remittanceStatus.find(
        s => s.value == status
      );
      if (statusObj) {
        return statusObj.label;
      }
      return "";
    },
    // stateFilter(status) {
    //   if (status == 4 || status == 5 || status == 6) {
    //     const statusObj = vars.orderMgmt.approvalStatus.find(
    //       s => s.value == status
    //     );
    //     return statusObj.label;
    //   }
    //   return "创建审批通过";
    // }
  },

  data() {
    return {
            layoutMode: 'simple',
      warrantyList:vars.business.warranty,
      ageOptions:vars.equipmentAge,
      regionalLevel:1,
      purposeOptions:[],
      workOptions:[],
      rData:null,
      cData:[],
      dialogExportVisible:false,
      sortOptions: [
        {
          label: "按添加时间排序",
          value: 1
        },
        {
          label: "按报修次数排序",
          value: 2
        }
      ],
      normalizer(node) {
        // treeselect定义字段
        return {
          id: node.Id,
          label: node.ProductName,
          children: node.children
        };
      },
      typeTreedata: [],
      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,
      total: 0,
      listLoading: false,
      listQuery: {
        EquipmentAge:'',
        Name:'',
        RegionalId: "",
        OrderNumber: "",
        StructureTypeName: "",
        EquipmentUseId:"",
        EquipmentWorkModeId:"",
        //  InstallTime:"",
        Range:"",
        Manufacturer:"",
        BurnerModel:"",
        Code: "",
        // ProductListManagementId: null,
        // InstallTimeStart: "",
        // InstallTimeEnd: "",
        IsWarranty: "",
        OrderType: 1
      },
      multipleSelection: [],
      tableSearchItems: [
        { prop: "Code", label: "设备编号", mainCondition: true },
        { prop: "Name", label: "加热炉/锅炉" },
        { prop: "EquipmentAge", label: "设备年龄" },
        
        
        { prop: "EquipmentUseId", label: "用途" },
        { prop: "IsWarranty", label: "是否在保" },
        { prop: "Manufacturer", label: "生产厂家" },
        
        { prop: "Range", label: "投产时间" },
        
        { prop: "BurnerModel", label: "燃烧器型号" },
        { prop: "EquipmentWorkModeId", label: "供风方式" },
        { prop: "OrderNumber", label: "订单/合同" },
        { prop: "StructureTypeName", label: "结构类型" },
        
        // { prop: "ProductListManagementId", label: "类型" },
        
        
      ],
      tabColumns: [
        {
          attr: { prop: "RegionalName", label: "所在地区", showOverflowTooltip: true }
        },
        
  //        {
  //          attr: { prop: "OrderNumber", label: "订单号" },
	// slot: true
  //       },
        {
          attr: { prop: "Name", label: "加热炉/锅炉",width:'100' }
        },
        {
          attr: { prop: "HeatNumber", label: "炉号",width:'80' },
          slot:true
        },
        
        {
          attr: { prop: "EquipmentWorkModeName", label: "供风方式",width:'100' },
          slot: true
        },
        {
          attr: { prop: "Code", label: "设备编号", showOverflowTooltip: true,width:'120' }
        },
        {
          attr: { prop: "BurnerModel", label: "燃烧器型号",width:'100' },
          slot: true
        },
        {
          attr: { prop: "Manufacturer", label: "生产厂家",width:'100' },
          slot: true
        },
        // {
        //   attr: { prop: "ApprovalStatus", label: "审批状态" },
        //   slot: true
        // },
        // {
        //   attr: { prop: "EquipmentTypeName", label: "类型" }
        // },
        {
          attr: { prop: "InstallTime", label: "投产时间", sortable: 'custom', width: '100' },
          slot: true
        },
        {
          attr: { prop: "IsWarranty", label: "是否在保", sortable: 'custom', width: '100' },
          slot: true
        },
        {
          attr: { prop: "RepairCount", label: "报修次数", sortable: 'custom', width: '100' }
        }
      ],
      tabDatas: [],
      currentProduct: null,
      dialogChangeFormVisible: false,
      currentRow: null,
      ParentName: "",
      dialogRepairVisible: false,
      isShowHistories: false,

      dialogParamFormVisible: false,

      dialogBatchFormVisible: false,

    };
  },
  created() {
    this.getRegionals();
    this.getPurposeWork();
    // this.getList()
  },
  mounted() {},
  methods: {
    handleBatchOpt() {
      if(this.multipleSelection.length == 0) {
        this.$message({
          message: '请选择需要调整的记录',
          type: 'error'
        })
        return false
      }

      this.handleBatchDialog()

    },
    getPurposeWork(){
        equUse.getList({"pageIndex": 1,"pageSize": 10000}).then(res => {
            this.purposeOptions=res.Items;
        })
        equMode.getList({"pageIndex": 1,"pageSize": 10000}).then(res => {
            this.workOptions=res.Items;
        })
    },
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    getApprovalObj(status) {
      return vars.orderMgmt.approvalStatus.find(s => s.value == status) || {};
    },
    // getColor(d) {
    //   if (d == 4 || d == 5 || d == 6) {
    //     let a = vars.orderMgmt.approvalStatus.find(s => s.value == d);
    //     return a.color;
    //   } else return "#606266";
    // },
    orderTypeChange(val) {
      this.listQuery.OrderType = val;
      this.getList();
    },
    changetimeQuantum(val) {
      this.listQuery.InstallTimeStart = val[0];
      this.listQuery.InstallTimeEnd = val[1];
    },
    getRegionals() {
      productListManagement.getListByCondition({}).then(res => {
        this.typeTreedata = listToTreeSelect(res);
      });
    },
    changeTreeNode(d) {
      this.regionalLevel=d.level;
      this.listQuery.RegionalId = d.Id;
      this.ParentName = d.ParentName;
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    onBtnClicked: function(domId) {
      switch (domId) {
        case "btnAdd":
          this.handleDialog("create");
          break;
        case "btnExport":
          this.handleExpor();
          break;
        case "btnParamSetting":
          this.handleParamDialog();
          break;
        default:
          break;
      }
    },
    handleExpor() {
      // businessMap.exportList(this.listQuery).then(res => {
      //   downloadFile(res.Url);
      // });
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      if(postData.Range && postData.Range.length>0){
        postData.InstallTimeStart=postData.Range[0];
        postData.InstallTimeEnd=postData.Range[1];
      }else{
        postData.InstallTimeStart='';
        postData.InstallTimeEnd='';
      }
     this.rData={
          "exportSource": 16,
          "columns": [],
          "searchCondition": postData
      }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'地区',
        value:'RegionalName'
      },{
        label:'加热炉/锅炉',
        value:'Name'
      },{
        label:'炉号',
        value:'HeatNumber'
      },{
        label:'供风方式',
        value:'EquipmentWorkModeName'
      },{
        label:'设备编号',
        value:'Code'
      },{
        label:'订单号',
        value:'OrderNumber'
      },{
        label:'加热炉功率',
        value:'HeatFurnaceRatework'
      },{
        label:'燃料类型',
        value:'FuelType'
      },{
        label:'燃烧器型号',
        value:'BurnerModel'
      },{
        label:'生产厂家',
        value:'Manufacturer'
      },{
        label:'燃烧器功率',
        value:'BurnerRatework'
      },
      // {
      //   label:'订单设备编号',
      //   value:'OrderEquipmentId'
      // },
      // {
      //   label:'审批状态',
      //   value:'ApprovalStatus'
      // },{
      //   label:'类型',
      //   value:'EquipmentTypeName'
      // },
      {
        label:'是否在保',
        value:'IsWarrantyName'
      },{
        label:'投产时间',
        value:'InstallTimeString'
      },{
        label:'报修次数',
        value:'RepairCount'
      },{
        label:'保修有效期至',
        value:'WarrantyTimeString'
      },{
        label:'设备现状/问题',
        value:'EquipmentStatusOrProblem'
      },{
        label:'结构类型',
        value:'StructureTypeName'
      },{
        label:'出产日期',
        value:'ProduceDateString'
      },{
        label:'燃烧器压力',
        value:'BurnerPressure'
      },{
        label:'出产编号',
        value:'ProduceNumber'
      },{
        label:'电源参数',
        value:'PowerParams'
      },{
        label:'试验证书编号',
        value:'TestCertificateNo'
      }
      ]
      this.dialogExportVisible=true;
    },
    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      // this.listQuery.PageIndex = 1
      this.getList();
      this.closeDialog();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    handleNav(type, proj) {
      this.$router.push({
        path: `/projectDev/projectMgmt/${type}/index?productId=${proj.productId}&projectId=${proj.projectId}`
      });
    },
    handleUpdate(row, optType = "update") {
      // 弹出编辑框
      this.currentRow = row;
      this.id = row.Id;
      this.dialogStatus = optType;
      this.isShowHistories = true;
      this.dialogChangeFormVisible = true;
    },
    handleCollapseOrExpand(isExpand) {
      this.$nextTick(() => {
        this.$refs.mainTable.setTabHeight()
      })
    },
    handleResetSearch() {
      this.listQuery.Range =[];
      this.listQuery.OrderNumber="";
      this.listQuery.StructureTypeName = ''
      this.listQuery.PageIndex = this.listQuery.PageIndex;
      this.listQuery.PageSize = this.listQuery.PageSize;
      this.listQuery.Name = "";
      this.listQuery.EquipmentUseId = "";
      this.listQuery.EquipmentWorkModeId = "";
      this.listQuery.Manufacturer = "";
      this.listQuery.BurnerModel = "";
      this.listQuery.Code = "";
      this.listQuery.EquipmentAge="";
      // this.listQuery.ProductListManagementId = null;
      // this.listQuery.InstallTimeStart = "";
      // this.listQuery.InstallTimeEnd = "";
      this.listQuery.IsWarranty = "";
      this.listQuery.OrderType = 1;
      this.getList(); //刷新列表
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.getList()
    },
    //获取项目列表
    getList() {
      this.listLoading = true;
      if (this.listQuery.RegionalId == -1) {
        this.listQuery.RegionalId = null;
      }
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      postData = this.assignSortObj(postData)
      if(postData.Range && postData.Range.length == 2) {
        postData.installTimeStart = postData.Range[0]
        postData.installTimeEnd = postData.Range[1]
      }
      businessMap.getList(postData).then(res => {
        this.listLoading = false;
        this.tabDatas = res.Items;
        this.total = res.Total;
      });
    },
    handleDel(row) {
      //  ${row.OrderNumber}
      this.$confirm(`是否确认删除?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        businessMap.deleter([row.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },
    /**
     * 变更
     */
    closeChangeDialog() {
      this.dialogChangeFormVisible = false;
    },
    handleChangeSaveSuccess() {
      this.getList();
      this.closeChangeDialog();
    },
    handleChangeDialog(row) {
      this.currentRow = row;
      this.dialogStatus = "create";
      this.isShowHistories = false;
      this.dialogChangeFormVisible = true;
    },

    closeRepairDialog() {
      this.dialogRepairVisible = false;
    },
    handleRepairDialog(row) {
      this.currentRow = row;
      this.dialogRepairVisible = true;
    },

    /**
     * 设备参数设置
     */
    handleParamDialog() {
      this.dialogParamFormVisible = true
    },
    closeParamDialog() {
      this.dialogParamFormVisible = false
    },
    handleParamSaveSuccess() {

      this.closeParamDialog();
    },

    // 批量调整地区
    handleBatchDialog() {
      this.dialogBatchFormVisible = true
    },
    closeBatchDialog() {
      this.dialogBatchFormVisible = false
    },
    handleBatchSaveSuccess() {
      this.closeBatchDialog();
      this.getList()
    },
    


  }
};
</script>

<style lang="scss" scoped>
.permissionBtn {
  padding-left: 17px;
}
.page-wrapper {
  .content-left {
    position: absolute;
    top: 0;
    left: 0;
    width: 250px;
    height: 100%;
    border-right: 1px solid #dcdfe6;
  }
  .content-right {
    position: absolute;
    top: 0;
    right: 0;
    width: calc(100% - 250px);
    height: 100%;
    overflow-y: auto;
    .opt-wrapper {
      box-sizing: border-box;
      border-bottom: 1px solid #dcdfe6;
      padding-bottom: 10px;
    }
    > div:first-child {
      padding: 10px 0 10px 20px;
      border-bottom: 1px solid #ebeef5;
    }
  }
}


.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
