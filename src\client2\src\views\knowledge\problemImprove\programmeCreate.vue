<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" style="padding-right: 10px;" v-loading='loading||problemLoading'
                label-position="right" label-width="80px" class="wrapperMain">
                    <el-form-item label="问题标题" prop="Title">{{formData.Title}}</el-form-item>
                    <el-form-item label="方案标题" prop="CaseName">
                        <el-input v-if="editable" :disabled="!editable" maxlength="100" type="text" v-model.trim="formData.CaseName"></el-input>
                        <div v-else>{{formData.CaseName}}</div>
                    </el-form-item>
                    <el-form-item label="提交人" prop="SumbitEmployeeList">
                        <emp-selector v-if="editable" :readonly="!editable" :beforeConfirm='handleFinalBeforeConfirm' :showType="2"
                        :multiple="true" :list="formData.SumbitEmployeeList" @change="handleChangeManager"></emp-selector>
                        <div v-else>{{formData.SumbitEmployeeList.map(s=>s.Name).toString()}}</div>
                    </el-form-item>
                    <el-form-item label="方案描述" prop="Describe">
                        <editor-bar v-if="editable" :value="formData.Describe" @edit="formData.Describe = arguments[0]"></editor-bar>
                        <div class="divUeditor ql-editor" v-html="formData.Describe" v-if="!editable"></div>
                    </el-form-item>
                    <el-form-item label="相关附件">
                        <template v-if="editable">
                            <app-uploader accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList"
                            :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                        </template>
                        <template v-else>
                            <app-uploader v-if="formData.AttachmentList.length>0" accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" readonly
                            :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            <template v-else>无</template>
                        </template>
                    </el-form-item>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 关闭 -->
                <app-button v-if="dialogStatus == 'detail'" @click="handleClose" text="关闭" type></app-button>
                <!-- 取消 -->
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import { getUserInfo } from "@/utils/auth";
import * as productQuestionImprovementApi from '@/api/knowledge/productQuestionImprovement.js'

import * as productQuestionSolutionImprovementApi from '@/api/knowledge/productQuestionSolutionImprovement.js'
import EditorBar from "@/components/QuillEditor/index.vue";
import empSelector from "@/views/common/empSelector";
export default {
    name: "problem-improve-programme-create",
    directives: {},
    components: {
        EditorBar,
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return `提交解决方案`;
            }else if(this.dialogStatus == 'update'){
                return `编辑解决方案`;
            }
            return "解决方案详情";
        },
    },
    filters: {
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        problemId: { //  问题id
            type: String,
            require: true,
            default: "",
        },
        id: { // 解决方案id
            type: String,
            default: "",
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    let userInfo = getUserInfo();
                    this.getProblemDetail();// 查询 问题基本信息
                    this.formData.SumbitEmployeeList.push({
                        EmployeeId: userInfo.employeeid,
                        Name: userInfo.empName,
                        Number: userInfo.empNumber,
                        AreaName: null,
                        Avatar: "",
                        AvatarId: "",
                        DepartmentList: userInfo.deptName,
                        HeadImageMediaModel: null,
                        ImageResourceId: null,
                        Mobile: userInfo.phone,
                        UserId: "00000000-0000-0000-0000-000000000000"
                    });
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            
            problemLoading: false,
            loading: false,
            rules: {
                CaseName: {fieldName: "方案标题",rules: [{ required: true }]},
                SumbitEmployeeList: {fieldName: "提交人",rules: [{ required: true }]},
                Describe: {fieldName: "方案描述",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                // Id: '',
                ProductQuestionImprovementId: '',// 问题id
                Title: '', // 问题标题


                CaseName: '',//  方案标题
                Describe: '',// 方案描述
                SumbitEmployees: '', // 提交人 名称
                SumbitEmployeeList: [], // 提交人
                SumbitEmployeeIdList: [], // 提交人 id 集合
                AttachmentList: [],// 相关附件
            },
        };
    },
    methods: {
        // 提交人 选择校验
        handleFinalBeforeConfirm(users) {
            if(users && users.length > 10) {
                this.$message({
                    message: '提交人不得超过10人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 提交人 选择确定
        handleChangeManager(users) {
            if (users && users.length > 0) {
                this.formData.SumbitEmployeeList = users;
            } else {
                this.formData.SumbitEmployeeList = [];
            }
            this.$refs["formData"].validateField("SumbitEmployeeList");
        },
        // 获取问题基本信息
        getProblemDetail(){
            this.isOneLoad = true;
            this.problemLoading = true
            productQuestionImprovementApi.detail({ id: this.problemId }).then(res => {
                this.formData.ProductQuestionImprovementId = res.Id;
                this.formData.Title = res.Title;
                
                this.problemLoading = false
            }).catch(err => {
                this.problemLoading = false
            });
        },
        // 附件 上传 赋值
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1000) {
            this.$message({
                message: '不得超过1000个',
                type: 'error'
            })
            return false
            }
            return true
        },
        // 发起人 选择确定
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.SumbitEmployeeList = users;
            } else {
                this.formData.SumbitEmployeeList = [];
            }
            this.$refs["formData"].validateField("SumbitEmployeeList");
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    postData.SumbitEmployeeIdList = postData.SumbitEmployeeList.map(s=>s.EmployeeId)
                    delete postData.SumbitEmployeeList
                    postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    delete postData.AttachmentList

                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = productQuestionSolutionImprovementApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = productQuestionSolutionImprovementApi.edit(postData);
                    }
                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 查询 基本信息
        getDetail() {
            this.isOneLoad = true;
            this.loading = true
            productQuestionSolutionImprovementApi.detail({ id: this.id }).then(res => {
                this.formData = {...this.formData,...res};
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
</style>
<style lang='scss' scoped>
.wrapperBox{
    padding-top: 10px;
    padding-right: 20px;
    &_main{
        max-height: 420px;
        overflow-y: auto;
    }
}
.el-card{
    margin-bottom: 15px;
}
.omit{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tips_title{
    float: left;
    max-width: 100%;
}

.divUeditor{
    width: 100%;
    min-height: 200px;
    padding: 5px 0;
    // max-height: 390px;
    // overflow: hidden;
    // overflow-y: auto;
    
    img {
        border: 0;
        max-width: 100%;
    }
}
</style>