<!--积分明细-->
<template>
    <div>
        <app-dialog title="积分明细" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <div class="wrapperBody">
                    <div class="lft">
                        <el-form label-width="85px">
                            <el-form-item label="姓名：">{{formData.EmployeeInfoDto.Name}}</el-form-item>
                            <el-form-item label="工号：">{{formData.EmployeeInfoDto.Number}}</el-form-item>
                            <el-form-item label="部门：">{{formData.EmployeeInfoDto.DepartmentName}}</el-form-item>
                            <el-form-item label="总积分：">{{formData.EmployeeInfoDto.TotalIntegral}}</el-form-item>
                            <el-form-item label="可用积分：">
                                {{formData.EmployeeInfoDto.UsableIntegral}}
                                <el-button style="margin-left: 10px" type="text" @click="handelAdjustment" v-show="isSetIntegral">积分调整</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="rft">
                        <el-row class="historyWrapper">
                            <page-title title="历史记录"></page-title>
                            <el-row class="historyWrapper--main">
                                <no-data v-if="formData.StudyIntegralDetailList.length==0"></no-data>
                                <el-card shadow="hover" size="mini" v-for="(item,idx) in formData.StudyIntegralDetailList" :key="idx">
                                    <div>
                                        <span>{{item.RecordTime | dateFilter('YYYY-MM-DD HH:mm')}}</span>
                                        <span style="margin-left: 10px">{{item.BusinessName}}</span>
                                        <span style="margin-left: 10px" class="item-status" v-if="item.ApproveStatus"
                                            :style="{backgroundColor: getApproveStatusObj(item.ApproveStatus).color,
                                            color: getApproveStatusObj(item.ApproveStatus).bgColor}"
                                        >{{ getApproveStatusObj(item.ApproveStatus).label }}</span>
                                    </div>
                                    <div>
                                        <span>{{labelName(item.Label,1)}}</span>
                                        <span class="blue">{{labelName(item.Label,2)}}</span>
                                    </div>
                                    <div v-if="item.OperatorEmployeeName">操作人：<span class="blue">{{item.OperatorEmployeeName}}</span></div>
                                    <div :class="item.IsInteger?'blue':'red'">
                                        积分<span>{{item.Value}}</span>
                                    </div>
                                </el-card>
                            </el-row>
                        </el-row>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <app-button :buttonType="999" @click="handleClose" text="关闭" type></app-button>
            </template>
        </app-dialog>
        <adjustment-integral v-if="createAdjustmentIntegralVisible" :id="id" dialogStatus="create"
        :dialogFormVisible="createAdjustmentIntegralVisible" @closeDialog="createAdjustmentIntegralVisible=false"
        @saveSuccess="createAdjustmentIntegralSuccess"></adjustment-integral>
    </div>
</template>
<script>
import NoData from "@/views/common/components/noData";
import adjustmentIntegral from "./adjustmentIntegral";

import indexPageMixin from "@/mixins/indexPage";
import * as StudyRecordApi from '@/api/knowledge/StudyRecord'
import { vars } from "@/views/projectDev/common/vars";
export default {
    name: "integral-detail",
    components: {
        NoData,
        adjustmentIntegral
    },
    mixins: [indexPageMixin],
    props: {
        id: {
            type: String,
            required: true
        },
        isSetIntegral: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            // 1 待审批 2 已审批 3 不通过 4 已撤销
            ApproveStatusEnum: vars.approvalStatuObj.approvalStatus,
            listLoading: false,
            formData: {
                EmployeeInfoDto: {
                    EmployeeId: "",
                    Name: "",
                    Number: "",
                    DepartmentName: "",
                    TotalIntegral: 0,
                    UsableIntegral: 0
                },
                StudyIntegralDetailList: []
            },
            createAdjustmentIntegralVisible: false,
        };
    },
    computed: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    console.log(this.isSetIntegral)
                    this.getDetail();
                }
            },
            immediate: true
        },
    },
    methods: {
        // 调整积分确定返回
        createAdjustmentIntegralSuccess(){
            this.createAdjustmentIntegralVisible = false;
            this.getDetail()
            this.$emit('reload')
        },
        // 显示调整积分弹窗
        handelAdjustment(){
            this.createAdjustmentIntegralVisible = true
        },
        /**获取详情 */
        getDetail() {
            let self = this;
            self.listLoading = true;
            StudyRecordApi.GetIntegralDetail({
                employeeId: self.id
            }).then(res => {
                self.formData = Object.assign({}, self.formData, res);
                self.listLoading = false;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        labelName(str,type){
            if(type===1){
                return str.split('：')[0]+'：';
            }
            if(type===2){
                return str.replace(str.split('：')[0]+'：','');
            }
            return str
        },
        getApproveStatusObj(val) {
            return this.ApproveStatusEnum.find(
                s => s.value == val
            ) || {};
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapperBody{
    display: flex;
    height: 450px;
    .lft{
        width: 240px;
        height: 100%;
        padding-right: 9px;
        overflow-y: auto;
        border-right: 1px solid #eee;
    }
    .rft{
        width: calc(100% - 250px);
        flex: 1;
        .historyWrapper{
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            &--main{
                flex: 1;
                padding: 10px;
                overflow-y: auto;
                .el-card{
                    line-height: 20px;
                    .red{
                        color: #ff0000;
                    }
                    .blue{
                        color: #409EFF;
                    }
                }
                .el-card+.el-card{
                    margin-top: 10px;
                }
            }
        }
    }
}
</style>


