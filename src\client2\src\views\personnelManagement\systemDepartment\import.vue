<template>
  <div class="createEmployee">
    <app-dialog title="批量导入" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
      <template slot="body">

        <el-row style="line-height: 28px;">
          <el-col :span="20">
            <span>请下载通用模板，按照格式修改后导入</span>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" style="float:right" @click="DownloadTemplate">下载模版</el-button>
          </el-col>
        </el-row>
        <hr />

        <el-upload :action="'string'" class="upload-demo" ref="upload" :on-change="handleChange" :on-exceed="handleExceed" accept=".xls,.xlsx" :on-remove="handleRemove" :limit="1" :file-list="fileList" :auto-upload="false">
          <el-button type="primary">选择文件</el-button>
          <div slot="tip" class="el-upload__tip">仅支持xls、xlsx格式文件</div>
        </el-upload>

        <hr />

        <el-row style="line-height: 30px;">
          <el-col :span="10">
            <span>文件中的成员将被导入至：</span>
          </el-col>
          <el-col :span="10">
            <treeselect :normalizer="normalizer" class="treeselect-common" :options="DepartmentList" :default-expand-level="5" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="DepartmentId" placeholder="请选择所属部门" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" :zIndex="9999"></treeselect>
          </el-col>
          <el-col :span="4" style="text-align: right;">
            <app-button @click="handleImport" text="开始导入"></app-button>
          </el-col>
        </el-row>

        <hr />

        <el-row>
          <el-col :span="24">

            <el-card class="box-card">
              <div slot="header" class="clearfix">
                导入详情 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <span v-if="IsImport" style="float:right;margin-right: 10px;">
                  <label style="color:green"> 导入成功：{{ importSsuccesCount}}人 </label>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <label style="color:red"> 导入失败：{{ importUnsuccessCount}}人 </label>
                </span>
              </div>
              <div class="text item">
                <el-table :data="tableData" style="width: 100%;" height="250" v-loading="tableLoading">
                  <el-table-column type="index" label="序号">
                  </el-table-column>
                  <el-table-column prop="Name" label="姓名" align="center">
                  </el-table-column>
                  <el-table-column prop="Result" label="结果" align="center">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.Result" type="success">成功</el-tag>
                      <el-tag v-else type="danger">失败</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="Reason" label="原因" show-overflow-tooltip>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>

          </el-col>
        </el-row>

      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import { listToTreeSelect } from '@/utils'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as systemEmployee from '@/api/personnelManagement/systemEmployee'

export default {
  name: "import-page",
  directives: {},
  components: {
  },
  mixins: [],
  props: {
    selectTypeId: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      this.DepartmentId = null;
      this.fileList = [];
      this.selectFile = null;
      this.tableData = [];
      this.tableLoading = false;
      this.DepartmentList = [];
      this.IsImport = false;
      this.importSsuccesCount = 0
      this.importUnsuccessCount = 0
      this.getSystemDepartmentList();
    }
  },
  computed: {},
  created() { },
  mounted() { },
  data() {
    return {

      fileList: [],

      tableData: [],
      tableLoading: false,

      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label.split(",")[0],
          id: node.Id,
          children: node.children
        };
      },

      DepartmentList: [],

      DepartmentId: null,

      selectFile: null,

      importSsuccesCount: 0,
      importUnsuccessCount: 0,

      IsImport: false
    };
  },
  methods: {

    handleRemove(file, fileList) {
      this.selectFile = null;
      console.log(file, fileList);
    },

    handleExceed() {
      this.$message.warning(`只能选择一个excel`);
    },

    handleChange(file) {
      this.selectFile = file.raw
    },

    //下载模版
    DownloadTemplate() {
      var dowFileUrl = `/template/人员批量导入模板.xlsx`
      window.open(dowFileUrl);
      //   window.location.href = dowFileUrl
    },

    //导入
    createData() {

      console.log(this.selectFile);

      if (!this.selectFile) {
        this.$message.error(`请选择excel文件`);
        return
      }

      if (this.selectFile == null || this.selectFile.size <= 0) {
        this.$message.error(`请选择正确的excel文件`);
        return
      }

      if (!this.DepartmentId) {
        this.$message.error(`请选择将数据导入到哪个部门`);
        return
      }

      this.tableLoading = true;
      let formData = new FormData()
      formData.append('file', this.selectFile)
      formData.append('departmentId', this.DepartmentId)

      systemEmployee.importEmployee(formData).then(res => {
        this.IsImport = true;
        this.tableData = res;
        if (res.length == 0) {
          this.importSsuccesCount = 0
          this.importUnsuccessCount = 0
        } else {
          this.importSsuccesCount = res.filter(s => s.Result).length
          this.importUnsuccessCount = res.length - this.importSsuccesCount
        }

        this.tableLoading = false;
        this.$message.success(`导入完成`);
      }).catch(err => {
        this.tableLoading = false;
      })
    },

    //获取部门信息下拉框
    getSystemDepartmentList() {
      systemDepartment.getListByCondition({}).then(res => {
        var departments = res.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.DepartmentName,
            ParentId: item.ParentId
          };
        });
        var departmentList = listToTreeSelect(departments);
        this.DepartmentList = departmentList

        this.DepartmentId = this.selectTypeId;
      })
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    handleImport() {
      this.createData();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}
.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.box-card {
  width: 100%;
}

.el-upload__tip {
  color: silver;
}
</style>
