<!--修改状态-->
<template>
  <div>
    <app-dialog title="修改状态" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
      <template slot="body">

        <el-select v-model="value" placeholder="请选择" style="margin:20px">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>

      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as carMgt from '@/api/personnelManagement/carCoordinator'

export default {
  /**名称 */
  name: "car-edit-state",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    //操作的节点，如果是新增，则为父节点；编辑为当前节点
    obj: {
      type: Object,
      required: true
    }
  },
  /**数据区 */
  data() {
    return {

      currentObj: {},
      /**按钮在执行，不允许点击 */
      buttonLoading: false,

      options: [{
        value: 1,
        label: '正常使用'
      },{
        value: 5,
        label: '固定用车'
      }, {
        value: 3,
        label: '维修保养'
      }, {
        value: 4,
        label: '车辆停用'
      }],
      value: 1
    };
  },
  /**计算属性---响应式依赖 */
  computed: {

  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.getDetail();
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
  },
  /**渲染后 */
  mounted() {
  },
  /**方法区 */
  methods: {

    getDetail() {
      let _this = this;
      carMgt.detail({ id: this.obj.Id }).then(response => {
        this.currentObj = response;
      }).catch(err => {
        _this.currentObj = {};
      });
    },

    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      this.currentObj.CarState = this.value
      carMgt.edit(this.currentObj).then(response => {
        _this.buttonLoading = false;
        _this.$notify({
          title: "成功",
          message: "保存成功",
          type: "success",
          duration: 2000
        });
        _this.$refs.appDialogRef.createData();
      }).catch(err => {
        _this.buttonLoading = false;
      });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


