<template>
<div>
    <app-dialog
        :title="pageTitle"
        ref="appDialogRef"
        v-bind="$attrs"
        v-on="$listeners"
        :width="900"
        :maxHeight='550'
    >
        <template slot="body">
            <el-row class="dialogWapper">
                <div class="content-wrapper">
                    <el-table
                        fit
                        :data="tableData"
                        style="width: 100%"
                        v-loading="tableLoading"
                        height="490"
                        :header-cell-style="{'text-align':'left'}"
                        highlight-current-row row-key="Id" @sort-change="handleSortChange"
                        ref="mainTable"
                    >
                        <el-table-column type="index" label="序号" width="55"></el-table-column>
                        <el-table-column label="奖品名称" prop="Name" showOverflowTooltip></el-table-column>
                        <el-table-column label="审批状态" prop="ApprovalStatus">
                            <template slot-scope="scope">
                                <span
                                    v-if="getApprovalObj(scope.row.ApprovalStatus).label"
                                    class="item-status"
                                    :style="{
                                    backgroundColor: getApprovalObj(scope.row.ApprovalStatus).color
                                    }"
                                >
                                    <span>{{ getApprovalObj(scope.row.ApprovalStatus).label }}</span>
                                </span>
                                <span v-else>无</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="奖品图片" prop="Name">
                            <template slot-scope="scope">
                                <img :src="scope.row.LogoPath" width="45px" height="45px" style="padding-top: 8px;" />
                            </template>
                        </el-table-column> -->
                        <el-table-column label="奖品编号" prop="Code" showOverflowTooltip></el-table-column>
                        <el-table-column label="单位" prop="Unit"></el-table-column>
                        <el-table-column label="数量" prop="ExchangesNumber"></el-table-column>
                        <el-table-column label="所需积分" prop="PointsExchangesValue" sortable="custom"></el-table-column>
                        <el-table-column label="兑换时间" prop="CreateTime" sortable="custom" width="150">
                            <template slot-scope="scope">
                                <span style="margin-left: 10px">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </el-row>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2' text="关闭"></app-button>
        </template>
    </app-dialog>
</div>
</template>
<script>
import indexPageMixin from "@/mixins/indexPage";
import * as pointExchange from "@/api/knowledge/pointExchange";
import { vars } from '../../workbench/common/vars'
export default {
    name: "exchange-prizes",
    mixins: [indexPageMixin],
    components: {
    },
    props: {
        pageTitle: {
            type: String,
            default: '兑换记录'
        },
        redeemPrizeId: {
            type: String,
            required: true
        },
    },
    computed: {
    },
    watch: {
        '$attrs.dialogFormVisible': {
            handler(val) {
                if(val) {
                    this.getList();
                }
            },
            immediate: true
        },
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
    },
    data() {
        return {
            tableData: [],
            total: 0,
            tableLoading: false,
            listQuery: {
                Keywords: '',
                PageIndex: 1,
                PageSize: 20
            },
            
        }
    },
    created(){
    },
    methods: {
        getApprovalObj(status) {
            return vars.approvalStatus.find(s => s.value == status) || {};
        },
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
        //获取项目列表
        getList() {
            this.tableLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData.redeemPrizeId = this.redeemPrizeId
            postData = this.assignSortObj(postData);
            pointExchange.GetApplyListPage(postData).then((res) => {
                this.tableLoading = false;
                this.tableData = res.Items;
                this.total = res.Total;
            });
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 取消
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }
}
</script>
<style scoped>
.notLabel >>> .el-radio__label{
    display: none;
}
</style>
<style lang="scss" scoped>
.dialogWapper{
    display: flex;
    height: 550px;
    .product-list {
        width: 250px;
        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        .treeBox {
            flex: 1;
            overflow-y: auto;
            width: 100%;
            .elInput {
                width: 230px;
                margin-left: 10px;
            }
            .elTree {
                height: 100%;
                overflow: auto;
            }
            .custom-tree-node {
                display: block;
                width: calc(100% - 24px);
                position: relative;
                box-sizing: border-box;
                padding-right: 30px;

                .node-title {
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .node-btn-area {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 23px;
                    height: 16px;
                }
            }
        }
    }
    .content-wrapper {
        width: calc(100% - 250px);
        flex: 1;
        overflow-y: auto;
        .searchBox{
            padding: 10px;
        }
    }
}
</style>