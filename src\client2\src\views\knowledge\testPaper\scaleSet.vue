<!--比例设置 添加/编辑-->
<template>
<app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
    <template slot="body">
        <div class="pageWarp" v-loading="loading">
            <el-row>选择提取难易度（至少选中1项）</el-row>
            <el-form :rules="formRules" ref="formData" :model="formModel" label-position="right" label-width="120px">
                <el-form-item label-width="0" prop="DifficultyLevelData"
                    :rules="{validator: (rule, value, callback) => validatorAllVal(rule, value, callback), trigger: 'change'}">
                    <el-row v-for="(item,index) in formModel.DifficultyLevelData" :key="item.current">
                        <el-row class="flexWarp">
                            <el-checkbox @change="changeChecked($event,index)" v-model="item.isChecked" :label="`${item.key}（${item.value}）`" style="width:110px;"></el-checkbox>
                            <el-form-item class="flexColumn" label-width="0" :prop="'DifficultyLevelData.'+index+'.val'"
                                :rules="{validator: (rule, value, callback) => validatorVal(rule, value, callback, item), trigger: 'blur'}">
                                <el-input-number :disabled="!item.isChecked" v-model="item.val" :min="0" :max="9999" label="描述文字"
                                :controls="false" :step="1" step-strictly></el-input-number>
                            </el-form-item>
                        </el-row>
                    </el-row>
                </el-form-item>
            </el-form>
        </div>
    </template>[2,5,5,55,5,]
    <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="loading" @click="handleButtonClick"></app-button>
    </template>
</app-dialog>
</template>

<script>
import * as examinationPaperManagementApi from '@/api/knowledge/ExaminationPaperManagement'
export default {
    name: "test-paper-scale-set",
    components: {},
    props: {
        dialogStatus: {
            type: String,
            default: "create"
        },
        node: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            formModel: {
                DifficultyLevelData: [
                    { current: 1, key: "初级", value: 0, isChecked: false, val: '', },
                    { current: 2, key: "中级", value: 0, isChecked: false, val: '', },
                    { current: 3, key: "高级", value: 0, isChecked: false, val: '', },
                ],
            },
            formRules: {
            },
        };
    },
    computed: {
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "比例设置";
            } else if (this.dialogStatus == "update") {
                return "比例设置";
            } else if (this.dialogStatus == "detail") {
                return "比例设置详情";
            }
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                this.resetFormModel();
                if (val) {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    mounted() {},
    methods: {
        validatorAllVal(rule, value, callback) {
            if (this.formModel.DifficultyLevelData.length>0) {
                if(!this.formModel.DifficultyLevelData.some(s=>s.isChecked)) {
                    return callback(new Error('请选择设置内容'));
                }
            }
            return callback();
        },
        // 校验 输入的题目数量
        validatorVal(rule, value, callback, row) {
            if (row.isChecked) {
                if(!value) {
                    return callback(new Error('请输入有效值'));
                }
                if(value>row.value) {
                    return callback(new Error('总数量不足提取数量'));
                }
            }
            return callback();
        },
        changeChecked(event, index){
            if(!event){
                this.formModel.DifficultyLevelData[index].val = ''
                this.$refs.formData.validateField(`DifficultyLevelData.${index}.val`);
            }
        },
        /**清理表单 */
        resetFormModel() {
            this.formModel = this.$options.data().formModel;
        },

        /**获取详情 */
        getDetail() {
            let self = this;
            self.loading = true;
            examinationPaperManagementApi.GetNumberByDifficultyLevel({
                ClassifyId: self.node.QuestionBankClassifyId
            }).then(response => {
                if(response&&response.length>0){
                    self.formModel.DifficultyLevelData.map(s=>{
                        s.value = response.find(q=>q.current == s.current).value;
                        s.val = self.node.KeyValuePairs&&self.node.KeyValuePairs.length>0?self.node.KeyValuePairs.find(q=>q.current == s.current).value:'';
                        s.isChecked = self.node.KeyValuePairs&&self.node.KeyValuePairs.length>0?self.node.KeyValuePairs.find(q=>q.current == s.current).isChecked:false;
                    })
                }
                self.loading = false;
            }).catch(err => {
                self.loading = false;
            });
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this, postData = JSON.parse(JSON.stringify(self.formModel.DifficultyLevelData));
            self.$refs.formData.validate(valid => {
                if (valid) {
                    postData = postData.map(s=>{
                        s.value = s.val
                        delete s.val
                        return s
                    });
                    self.$refs.appDialogRef.createData(postData);
                } else {
                    return false;
                }
            });
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.pageWarp{
    padding: 10px 10px 0  10px;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
</style>


