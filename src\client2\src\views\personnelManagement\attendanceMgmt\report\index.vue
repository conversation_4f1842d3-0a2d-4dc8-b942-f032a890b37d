<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                    <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <page-title :title="departmentInfo"></page-title>
                <div class="tagBox">
                    <div class="year-wrapper">
                        切换年份：
                        <el-select v-model="listQuery.Year" placeholder="请选择" @change='getList'>
                            <el-option v-for="item in years" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="btn-list">
                        <v-button-list :btnListData='monList' v-model="listQuery.Month" @change="getList"></v-button-list>
                    </div>
                </div>
                <!-- <div style="width: 100%; padding: 10px; border-bottom: 1px solid #EBEEF5;">
                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch">
                        <template slot="Name">
                            <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                        </template>
                        <template slot="Number">
                            <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Number" placeholder></el-input>
                        </template>
                    </app-table-form>
                </div> -->
                <!-- 表格批量操作区域 -->
                <!-- <div class="btns-area">
                    <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                </div> -->
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :serial='false' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="false" :isShowBtnsArea='true' :startOfTable="startOfTable" :multable="false" :isShowConditionArea='true' :height='tabHeight'>
                        <template slot="Opt" slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row)" :type="2" text='考勤详情'></app-table-row-button>
                        </template>

                        <template slot="Idx" slot-scope="scope">
                            {{ ((listQuery.PageIndex - 1) * listQuery.PageSize) + scope.index }}
                        </template>

                        <template slot="DepartmentNameList" slot-scope="scope">
                          <div v-for="(item, index) in scope.row.DepartmentNameList" :key="index">
                            {{ item }}
                          </div>
                        </template>
                      
                        <template v-for="(col, idx) in transColumns" :slot="col.prop" slot-scope="scope">
                            <span v-if="scope.row[col.prop] && col.prop !== 'AttendanceConfirm' " :key="idx" :style="{color: scope.row[col.prop].IsException ? 'red' : scope.row[col.prop].Value == 0 ? '#d7d7d7' : ''}">
                                {{ scope.row[col.prop].Value }}
                            </span>
                            <!-- <span v-else :key="idx" :style="{color: scope.row[col.prop].IsException ? 'red' : '#409EFF'}">{{scope.row[col.prop].IsException | filterStatus}}</span> -->
                        </template>

                        <!-- 表格查询条件区域 -->
                        <!-- <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch">
                                <template slot="Name">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                                </template>
                                <template slot="Number">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Number" placeholder></el-input>
                                </template>
                            </app-table-form>
                        </template> -->

                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch">
                                <template slot="Name">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                                </template>
                                <template slot="Number">
                                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Number" placeholder></el-input>
                                </template>
                            </app-table-form>
                        </template>
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 查看/修改 -->
    <detail-page v-if="employeeId && dialogFormVisible" :row='currentRow' @closeDialog="closeDialog" :employeeId='employeeId' :month='listQuery.Month' :year='listQuery.Year' :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus"></detail-page>

    <setting-page @closeDialog="closeSettingDialog" :dialogFormVisible="dialogFormSettingVisible" @saveSuccess="handleSettingSaveSuccess"></setting-page>

    <v-export
        @saveSuccess="handleSuccessExport"
        @closeDialog="handleCloseExport"
        :dialogFormVisible="dialogExportVisible"
        :rData="rData"
        :cData='cData'
        >
    </v-export>
</div>
</template>

<script>
import {
    listToTreeSelect, throttle
} from "@/utils";
import elDragDialog from "@/directive/el-dragDialog";
import vExport from "@/components/Export/index";
import indexPageMixin from "@/mixins/indexPage";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import detailPage from "./detail";
import settingPage from "./setting";
import vButtonList from '@/views/common/buttonList'
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import { vars } from '../vars' 
import * as businessRoleApi from "@/api/businessRole";

export default {
    name: "attendanceMgmt-report-index",
    mixins: [indexPageMixin, tabDynamicHeightMixins],
    components: {
        detailPage,
        settingPage,
        vButtonList,
        vExport,
    },
    props: {},
    filters: {
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
        filterStatus(value){
            return value?"待确认":"已确认"
        }
    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
        transColumns() {
            return this.tabColumns.filter(s => s.slot === true && s.setable !== false).map(s => {
                return {
                    prop: s.attr.prop,
                    label: s.attr.label
                }
            })
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.DepartmentId = val.Id;
                    this.selectDepartmentName = val.DepartmentName;
                    this.getList();
                }
            },
            immediate: true,
        },
    },
    created() {
        let currentYear = (new Date()).getFullYear()
        this.years = Array.from(Array(5), (v,k) => { 
            return {
                value: currentYear - k,
                label: currentYear - k
            }
        })
        // for(let i = currentYear - 4; i <= currentYear; i++) {
        //     this.years.push({
        //         value: i,
        //         label: i
        //     })
        // }

        this.getDepartments();
    },
    data() {
        return {
            rData:null,
            cData:[],
            dialogExportVisible:false,

            currentRow: null,
            currentMonth: 1,
            years: [],
            monList: Array.from(Array(12), (v,k) => {
                return {
                    value: k + 1,
                    label: `${k+1}月`
                }
            }), //.concat({value: 0, label: '全年'})
            epKeys: [],

            departmentInfo: "",
            selectDepartmentName: "",

            // statusEnum: statusEnum,

            filterText: "",

            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            tableSearchItems: [{
                    prop: "Name",
                    label: "姓名"
                },
                // {
                //     prop: "Mobile",
                //     label: "手机号"
                // },
                {
                    prop: "Number",
                    label: "工号"
                },
            ],

            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            
            employeeId: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: vars.execptionSettingTypes,
            // tabHeight: 400,
            listQuery: {
                DepartmentId: "",
                Name: "",
                // Mobile: "",
                Number: '',
                Year: (new Date()).getFullYear(),
                Month: (new Date()).getMonth() + 1
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,

            machineNo: '',
            timecardNo: '',

            dialogFormSettingVisible: false,

        };
    },
    methods: {
        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnExport":
                    this.handleExport()
                    break;
                case "btnExportDetail":
                    this.handleExport('notAddColumns')
                    break;
                case "btnNotice":
                    this.openSettingDialog();
                    break;
                default:
                    break;
            }
        },
        handleSuccessExport() {},
        handleCloseExport() {
            this.dialogExportVisible = false;
        },
        handleExport(type) {
            this.rData = {
                exportSource: 19,
                columns: [],
                searchCondition: this.listQuery
            };


            let temp = JSON.parse(JSON.stringify(this.tabColumns)).filter(s => s.attr.prop != 'Opt').map(s => {
                return {
                    label: s.attr.label,
                    value: s.attr.prop
                }
            });

            temp.splice(1, 0, {
                label: "工号",
                value: "Number"
            })



            if(type != 'notAddColumns') {
                let extendList = [{
                    label: '入职时间',
                    value: 'EntryTime'
                },{
                    label: '剩余调休（天）',
                    value: 'CompensatoryLeave'
                },{
                    label: '剩余年假（天）',
                    value: 'AnnualLeave'
                },{
                    label: '考勤详细',
                    value: '__details'
                }]
                temp = temp.concat(extendList)
            }


            this.cData = temp

            this.dialogExportVisible = true;
        
        },
        openSettingDialog() { 
            this.dialogFormSettingVisible = true
        },
        closeSettingDialog() {
            this.dialogFormSettingVisible = false
        },
        handleSettingSaveSuccess() {
            this.getList()
            this.closeSettingDialog()
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        // handleSaveSuccess(_formData) {
        //     this.getList();
        //     this.closeDialog();
        // },

        //获取成员列表
        getList() {
            if (this.checkedNode) {
                this.listLoading = true;
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                // postData = this.assignSortObj(postData);
                postData.EnableDataPermissions = true
                timecardDepartment
                    .getTimecardReport(postData)
                    .then((res) => {
                        this.tabDatas = res.Items;
                        this.total = res.Total;
                        this.listLoading = false;
                        this.departmentInfo = this.selectDepartmentName;
                    })
                    .catch((err) => {
                        this.listLoading = false;
                    });
            }
        },
        handleShowNotify() {

        },
        //弹出详情框
        handleReview(row, optType = "detail") {
            this.employeeId = row.EmployeeId;
            this.currentRow = row
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        onResetSearch() {
            this.listQuery.Name = "";
            // this.listQuery.Mobile = "";
            this.listQuery.Number = ''
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            businessRoleApi
                .getDepartmentListByCondition({BusinessRoleType: 8})
                .then((res) => {

                    //整理数据，根节点需要用 ParentId 为空表示
                    if(res && res.length > 0) {
                        res.forEach(e => {
                            if(res.findIndex(s => s.Id == e.ParentId) == -1) {
                                e.ParentId = null
                            }
                        });
                    }

                    this.treeDatas = listToTreeSelect(res);

                    if(this.treeDatas && this.treeDatas.length>0){
                        this.treeDatas.forEach(v => {
                            this.epKeys.push(v.Id);
                            // if(v.children.length>0){
                            //     v.children.forEach(v1 => {
                            //         this.epKeys.push(v1.Id);
                            //     })
                            // }

                        })
                    }
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        flex: 1;
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;

        .content {
            // padding: 0 10px;
            // padding-right: 0;
            

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 10px 8px;
    display: flex;
    .year-wrapper{
        >div:first-child{
            width: 100px!important;
        }
    }
    .btn-list{
        flex: 1;
        margin-left: 20px;
        display: flex;
        align-items: center;
    }
}

.btns-area {
  padding: 10px;
  button{
      margin-right: 4px;
  }
}
</style>
