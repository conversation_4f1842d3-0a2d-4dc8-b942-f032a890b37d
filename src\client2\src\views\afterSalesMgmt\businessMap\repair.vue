<template>
    <div class="businessRepair">
        <app-dialog title="报修记录" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
            v-loading='loading'
        >
            <template slot="body">
                <div class="temBody">
                    <el-form
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="110px">
                        <div>
                            <div class="panel-title">设备信息</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="设备名称" prop="Name">
                                        {{formData.Name}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="设备编号" prop="Code">
                                        <span>{{formData.Code}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="类型" prop="ProductListManagementId">
                                        {{formData.productType}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="安装时间" prop="InstallTime">
                                         {{formData.InstallTime | dateFilter('YYYY-MM-DD')}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="是否在保" prop="IsWarranty">
                                        {{formData.IsWarranty ? '是' : '否'}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="保修有效期至" prop="WarrantyTime">
                                        <span v-if="formData.IsWarranty">{{formData.WarrantyTime | dateFilter('YYYY-MM-DD')}}</span>
                                        <span v-else>无</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="安装地区" prop="RegionalId">
                                        {{formData.areaName}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="备注" prop="Remark">
                                        {{formData.Remark ? formData.Remark : '无'}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                    <div>
                        <div class="panel-title">报修记录({{total}})</div>
                        <ul class="repairUl" v-if="repairList.length>0">
                            <li class="cl" v-for="(rl,index) in repairList">
                                <span class="fl">{{PageSize*(PageIndex-1)+index+1}}、</span>&nbsp;
                                <span class="fl">{{rl.Code}}&emsp;</span>
                                <span class="fl">{{rl.ReportEmployee ? rl.ReportEmployee.Name : ''}}&emsp;</span>
                                <span class="fl">{{rl.ReportTime | dateFilter('YYYY-MM-DD HH:mm')}}</span>
                                <el-button class="fr" type="text" @click="viewRepairDetail(rl)">查看报修详情</el-button>
                            </li>
                        </ul>
                        <!--暂无数据-->
                        <no-data v-else></no-data>
                    </div>
                    <pagination
                      v-show="total>PageSize"
                      :total="total"
                      :page.sync="PageIndex"
                      :size.sync="PageSize"
                      layout="total, prev, pager, next, jumper"
                      @pagination="handleCurrentChange"
                      @size-change="handleSizeChange"
                    />
                </div>
            </template>
            <template slot="footer">
                <el-button @click="handleClose">关闭</el-button>
            </template>
        </app-dialog> 
        <!-- 创建 -->
        <create-page
            @closeDialog="closeDialog"
            @saveSuccess="handleSaveSuccess"
            :dialogFormVisible="dialogFormVisible"
            :dialogStatus="dialogStatus"
            :id="maintenId"
        ></create-page>
    </div>
</template>
<script>
import elDragDialog from '@/directive/el-dragDialog'
import * as businessMap from "@/api/businessMap";
import * as systemManagement from "@/api/systemManagement/regionalManagement"
import { listToTreeSelect } from '@/utils';
import NoData from "@/views/common/components/noData";
import createPage from "../maintenCenter/maintenOrderMgmt/create"
export default{
    name:'businessRepair',
    // mixins: [indexPageMixin],
    components: {
        NoData,
        createPage
    },
    props:{
        id: {
            type: String,
            default: ''
        },
        typeTreedata:{
            type:Array,
            default:[]
        }
    },
    directives: {
        elDragDialog
    },
    data(){
        return{
            loading:false,
            total:0,
            PageSize:5,
            PageIndex:1,
            formData:{
                Code: "",
                CreateTime: "",
                RegionalId: "",
                Id: "",
                InstallTime: "",
                IsWarranty: false,
                Name: "666",
                OrderEquipmentId: "",
                OrderId: "",
                OrderNumber: "",
                ProductListManagementId: "",
                Remark: null,
                WarrantyTime: null,
                productType:'',
                areaName:'',
            },
            areaTreedata:[],
            repairList:[],
            dialogFormVisible:false,
            dialogStatus:'detail',
            maintenId:''
        }
    },
    filters: {
        
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val){
                this.getDetail();
                this.getList();
            }
      
        },
    },
    computed:{
        
    },
    created(){
        this.getAreas();
        
        this.getList();
    },
    mounted(){
        
    },
    methods:{
        viewRepairDetail(d){
          // 弹出编辑框
          this.maintenId = d.MaintenanceId;
          this.dialogFormVisible = true;
        },
        closeDialog() {
          this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
          this.closeDialog();
        },
        getAreas() {
            systemManagement.getListByCondition({}).then(res => {
                this.areaTreedata = res
                this.getDetail();
            })
        },
        handleCurrentChange(val) {
          this.PageIndex = val.page;
          this.PageSize = val.size;
          this.getList();
        },
        handleSizeChange(val) {
          this.PageSize = val.size;
          this.getList();
        },
        getDetail() {
            this.loading = true
            businessMap.detail({id:this.id}).then(res => {
                this.formData = Object.assign({}, this.formData, res)
                this.queryList(this.typeTreedata,res.ProductListManagementId);
                let areaData=this.areaTreedata.find(s => s.Id == res.RegionalId);
                this.formData.areaName=areaData.ParentName;
                this.loading = false
            }).catch(err => {
                this.loading = false
            })
        },
        queryList(json,id) {
            for (let i = 0; i < json.length; i++) {
                if (json[i].Id == id) {
                    this.formData.productType=json[i].ProductName;
                } else {
                    if(json[i].children && json[i].children.length>0){
                        this.queryList(json[i].children, id);
                    }
                }
            }
            
        },
        getList(){
            this.loading = true
            let params={
                "PageIndex": this.PageIndex,
                "PageSize":this.PageSize,
                "Id": this.id
            }
            businessMap.getEquipmentRepairRecordPageAsync(params).then(res => {
                console.log(res)
                this.total=res.Total;
                this.repairList=res.Items;
                this.loading = false
            }).catch(err => {
                this.loading = false
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }

}
</script>
<style lang="scss" scoped>
.temBody{
    width:100%;
    height:510px;
    overflow-y: auto;
}
.repairUl{
    li{
        height: 30px;
        line-height: 30px;
        border-bottom: 1px solid #DCDFE6;
        padding-left: 4px;
    }
}
</style>