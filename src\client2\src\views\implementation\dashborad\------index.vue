<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title title="数据看板" :subTitle='["工程项目的进度看板"]'></page-title> -->
      <div class="page-wrapper">
        <div class="content-left">
	        	<!-- isCheckbox表示是否显示单选框 -->
	        	<!-- isAll表示是否显示全部区域 -->
	        	<!-- isSubset表示是否显示子集 -->
	        	<v-tree @changeNode='changeTreeNode' :level='1' :isAll='true' :isSubset='true'></v-tree>
        </div>
        <div class="content-right" v-loading='listLoading'>
          <app-table
              ref="mainTable"
              :tab-columns="tabColumns"
              :tab-datas="tabDatas"
              :tab-auth-columns="tabAuthColumns"
              :isShowAllColumn="true"
              :isShowOpatColumn="false"
              :startOfTable="startOfTable"
              :serial='false'
              :multable='false'
          >
              <template slot="conditionArea">
                  <app-table-form
                  :label-width="'140px'"
                  :items="tableSearchItems"
                  @onSearch="handleFilter"
                  @onReset="handleResetSearch"
                  >
                      <template slot="ImplementationName">
                          <el-input v-model.trim="listQuery.ImplementationName"></el-input>
                      </template>
                      <template slot="ContractNumber">
                          <el-input v-model.trim="listQuery.ContractNumber"></el-input>
                      </template>
                      <template slot="Status">
                          <el-select style="width: 100%;" v-model="listQuery.Status" placeholder="" clearable>
                              <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                          </el-select>
                      </template>
                  </app-table-form>
              </template>

              <!-- 表格批量操作区域 -->
              <template slot="btnsArea">
                  <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
              </template>
              
              <template slot="No" slot-scope="scope">
                  <span>
                      {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index + 1) }}
                  </span>
              </template>
              <template slot="ContractNumber" slot-scope="scope">
                  {{ scope.row.ContractNumber || "无" }}
              </template>
              <template slot="Status" slot-scope="scope">
                  <span :style="{color: getStatusObj(scope.row.Status).color}">
                      {{ scope.row.Status | findState }}
                  </span>
              </template>
              <template slot="Progress" slot-scope="scope">
                <div style="max-width: 85%;">
                  <el-progress :percentage="scope.row.Progress"></el-progress>
                </div>
              </template>
              <template slot="IsFinished" slot-scope="scope">
                  {{ scope.row.IsFinished }}/{{ scope.row.EquipmentCount }}
              </template>
              <template slot="ImplementPrincipalEmployeeList" slot-scope="scope">
                {{ scope.row.ImplementPrincipalEmployeeList | listToStringFilter }}
              </template>
              <template slot="RegionalFoucsContents" slot-scope="scope">
                  <template v-if="scope.row.RegionalFoucsContents && scope.row.RegionalFoucsContents.length > 0">
                      <div class="list-wrapper">
                          <div
                              class="list-item"
                              v-for="(item, idx) in scope.row.RegionalFoucsContents"
                              :key="idx"
                              >
                              <!-- 【 -->
                              {{ item.RegionalName }}：<span
                                  :style="{ color: `${getRegionalStatus(item.Status).color}` }"
                                  >{{ item.Progress }}% 
                                  <!-- ({{
                                  getRegionalStatus(item.Status).label
                                  }}) -->
                                  </span
                              >
                              <span
                                  v-if="
                                  item.FoucsContentItemModels &&
                                      item.FoucsContentItemModels.length > 0
                                  "
                              >
                                  重点关注：
                                  <span
                                  v-for="(pro, idx2) in item.FoucsContentItemModels"
                                  :key="idx2"
                                  >
                                      <a href="javascript: void(0);" @click="handleShowDetail(pro, item.ImplementationRegionalId)">
                                          【{{ pro.ItemName }}
                                          <span
                                              :style="{ color: `${getProcesStatus(pro.Status).color}` }"
                                              >{{ pro.Progress }}% 
                                              <!-- ({{
                                              getProcesStatus(pro.Status).label
                                              }}) -->
                                              </span
                                          >】
                                      </a>
                                  </span>
                              </span>
                              <!-- 】 -->
                          </div>
                      </div>
                  </template>
                  <template v-else>
                      <no-data></no-data>
                  </template>
              </template>
              
          </app-table>

          <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="listQuery.PageIndex"
              :size.sync="listQuery.PageSize"
              @pagination="handleCurrentChange"
              @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
    <v-export
    @saveSuccess="handleSuccessExport"
    @closeDialog="handleCloseExport"
    :dialogFormVisible="dialogExportVisible"
    :rData="rData"
    :cData="cData"
    >
    </v-export>

    <detail-page
        v-if="id && regionalId && dialogFormVisible"
        @closeDialog="closeDialog"
        @saveSuccess="() => {}"
        :dialogFormVisible="dialogFormVisible"
        :dialogStatus="dialogStatus"
        :id="id"
        :regionalId='regionalId'
    ></detail-page>

    <!-- 事项 详情、编辑 -->
    <proces-create
        @closeDialog="closeProcesCreateDialog"
        @saveSuccess="() => {}"
        :dialogFormVisible="dialogProcesCreateFormVisible"
        :dialogStatus="dialogProcesCreateStatus"
        :procesId="procesId"
    ></proces-create>
  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import procesCreate from '../engineering/workbench/procesCreate'
import * as afterService from "@/api/afterSalesMgmt/afterService";
import { getUserInfo } from "@/utils/auth";
import * as impManagement from "@/api/implementation/impManagement";
import { vars } from "../common/vars";
import layoutVue from "../../login/layout.vue";
import vExport from "@/components/Export/index";
import noData from "@/views/common/components/noData";
import detailPage from './detail'
import vTree from '../../afterSalesMgmt/businessMap/common/tree'

export default {
  name: "data-dashborad",
  mixins: [indexPageMixin],
  components: { vExport, noData, detailPage, procesCreate, vTree },
  filters: {
    listToStringFilter(list) {
      if(list && list.length > 0) {
        return list.map(s => s.Name).join(',')
      }
      return '无'
    },
    findState(d) {
      let result = vars.implementationSatus.find(v => v.value == d);
      if(result) {
        return result.label;
      }
      return ''
    }
  },
  created() {
    this.getList();
  },
  watch: {
  },
  mounted() {

  },
  data() {
    return {
      statusList: vars.regionalStatus,
      total: 0,
      listLoading: false,
      listQuery: {
        ImplementationName: "",
        ContractNumber: "",
        Status: null,
        RegionalId: ''
      },
      multipleSelection: [],
      tableSearchItems: [
        { prop: "ImplementationName", label: "项目名称(实施工程)" },
        { prop: "ContractNumber", label: "合同编号" },
        { prop: "Status", label: "状态" }
      ],
      tabColumns: [
        {
          attr: { prop: "RegionalFoucsContents", type: "expand" },
          slot: true
        },
        {
          attr: { prop: "No", label: "序号", width: "48" },
          slot: true
        },
        {
          attr: { prop: "Name", label: "项目名称（实施工程）", 'min-width': "6"  }
        },
        {
          attr: { prop: "Status", label: "状态", 'min-width': "2"  },
          slot: true
        },
        {
          attr: { prop: "Progress", label: "进度", 'min-width': "5" },
          slot: true
        },
        {
          attr: { prop: "IsFinished", label: "整体进度", 'min-width': "2" },
          slot: true
        },
        {
          attr: { prop: "ImplementPrincipalEmployeeList", label: "负责人", 'min-width': "4" },
          slot: true
        },
        {
          attr: { prop: "ContractNumber", label: "合同编号", 'min-width': "4" },
          slot: true
        },
        // {
        //   attr: { prop: "ContractPrice", label: "合同金额" }
        // },
        

      ],
      tabDatas: [],
      rData: null,
      cData: [],
      dialogExportVisible: false,

      /**
       * 详细
       */
      id: '',
      regionalId: '',
        dialogStatus: '',
        dialogFormVisible: false,

        /**
         * 事项编辑
         */
        procesId: "",
        dialogProcesCreateStatus: "detail",
        dialogProcesCreateFormVisible: false,
    };
  },
  beforeRouteLeave(to, from, next) {
    //离开组件的时候触发
    // if (this.resizeFlag) {
    //   clearTimeout(this.resizeFlag);
    // }

    // window.onresize = null;
    next();
  },
  methods: {
    handleShowDetail(row, regionalId) {
        if(row.BusType == 1) { //工序
            this.id = row.ItemId;
            this.regionalId = regionalId
            this.dialogStatus = 'detail';
            this.dialogFormVisible = true;
        }else if(row.BusType == 2) { //事项
            this.handleProcesCreateDialog(row, 'detail')
        }
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSuccessExport() {},
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    onBtnClicked() {
      this.handleExport();
    },
    handleExport() {
      this.rData = {
        exportSource: 6,
        columns: [],
        searchCondition: this.listQuery
      };
      this.cData = [
        {
          label: "序号",
          value: "Number"
        },
        {
          label: "项目名称（实施工程）",
          value: "Name"
        },
        {
          label: "状态",
          value: "Status"
        },
        {
          label: "进度",
          value: "Progress"
        },
        {
          label: "整体进度",
          value: "TotalProgress"
        },
        {
          label: "合同编号",
          value: "ContractNumber"
        },
        {
          label: "合同金额",
          value: "ContractPrice"
        },
        {
          label: "地区进度/重点关注",
          value: "RegionalFoucsContentString"
        }
      ];
      this.dialogExportVisible = true;
      
    },
    // dynamicComputedHeight() {
    //   let tableWrapper = this.$refs.tableWrapper;
    //   if (tableWrapper) {
    //     this.tableWrapperHeight = tableWrapper.clientHeight;
    //   }
    // },
    getStatusObj(status) {
      return vars.implementationSatus.find(s => s.value == status) || {};
    },
    getRegionalStatus(val) {
      return vars.regionalStatus.find(s => s.value == val) || {};
    },
    getProcesStatus(val) {
      return vars.processStatus.find(s => s.value == val) || {};
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        RegionalId: this.listQuery.RegionalId,
        ImplementationName: "",
        ContractNumber: "",
        Status: null
      };
      this.getList(); //刷新列表
    },
    //获取项目列表
    getList() {
      this.listLoading = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      let that = this;
      impManagement
        .getImplementationStatement(postData)
        .then(res => {
          this.listLoading = false;
          //用户动态计算宽度（不用于显示，所以需要和显示的格式保持一致）
          this.tabDatas = res.Items.map(s => {
            if (
              s &&
              s.RegionalFoucsContents &&
              s.RegionalFoucsContents.length > 0
            ) {
              s.RegionalFoucsContents.map(item => {
                let loopText = "";
                if (
                  item.FoucsContentItemModels &&
                  item.FoucsContentItemModels.length > 0
                ) {
                  loopText += "重点关注：";
                  for (let i = 0; i < item.FoucsContentItemModels.length; i++) {
                    let pro = item.FoucsContentItemModels[i];
                    loopText += `【${pro.ItemName} ${pro.Progress}% (${
                      that.getProcesStatus(pro.Status).label
                    })】`;
                  }
                }
                item.text = `【${item.RegionalName}：${item.Progress}% (${
                  that.getRegionalStatus(item.Status).label
                }) ${loopText}】`;
                return item;
              });
            }
            return s;
          });
          this.total = res.Total;
        })
        .catch(err => {
          this.listLoading = false;
        });
    },

    /**事项详情、编辑 */
    handleProcesCreateDialog(row, optType = "update") {
        this.procesId = row.ItemId;
        this.dialogProcesCreateStatus = optType;
        this.dialogProcesCreateFormVisible = true;
    },
    /**事项进度关闭 */
    closeProcesCreateDialog() {
        this.dialogProcesCreateFormVisible = false;
    },  
    changeTreeNode(d){
      if(d.Id == -1){
        this.listQuery.RegionalId=null;
      }else{
        this.listQuery.RegionalId=d.Id;
      }
      this.listQuery.PageIndex = 1
      this.getList();
    },
    // getMaxLength(arr) {
    //   return arr.reduce((acc, item) => {
    //     if (item) {
    //       let calcLen = this.getTextWidth(item);
    //       if (acc < calcLen) {
    //         acc = calcLen;
    //       }
    //     }
    //     return acc;
    //   }, 0);
    // },
    /**
     * 使用span标签包裹内容，然后计算span的宽度 width： px
     * @param valArr
     */
    getTextWidth(str) {
      let width = 0;
      let html = document.createElement("span");
      html.innerText = str;
      html.className = "getTextWidth";
      //字体大小需要和表格中显示字体保持一致，否则宽度计算不合
      html.style =
        "display: inline-block; overflow-x: auto; color: red; white-space:nowrap; font-size: 12px;";
      document.querySelector("body").appendChild(html);
      width = document.querySelector(".getTextWidth").offsetWidth;
      document.querySelector(".getTextWidth").remove();
      return width;
    }
  }
};
</script>

<style scoped>
.page-wrapper >>> .el-progress-bar{
  margin-right: -60px;
  padding-right: 55px;
}
</style>

<style lang="scss" scoped>
// .main-page-content {
//   display: flex;
//   flex-direction: column;
//   height: 100%;
//   .table-wrapper {
//     flex: 1;
//   }
// }

.list-wrapper{
    .list-item{
        // white-space: nowrap;
        a:hover{
            text-decoration: underline; 
        }
    }
    .list-item:not(:last-child) {
        margin-bottom: 10px;
    }
}

.page-wrapper {
    // min-height: calc(100% - 40px)!important;
    min-height: 100%!important;
    margin-bottom:0!important;
    .content-left{
    	position: absolute;
    	top:0;
    	left:0;
    	width:250px;
    	height:100%;
    	border-right:1px solid #DCDFE6;
    }
    .content-right {
      position: absolute;
      top:0;
      right:0;
        width:calc(100% - 250px);
        height:100%;
        overflow-y: auto;
        
    }

}

</style>