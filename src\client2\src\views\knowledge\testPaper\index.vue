<!--软件实施常见问题管理-->
<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="pageWrapper">
        <!--左侧树-->
        <div class="product-list"><br v-if="btnAddChildren==''" />
          <el-button v-if="btnAddChildren=='btnAddChildren'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">创建试卷分类</el-button>
          <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <div class="treeBox" v-loading='treeLoading'>
            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                <span v-if="data.Level>0" class="node-btn-area">
                  <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)" v-if="btnAddChildren=='btnAddChildren'">
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                      <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
        <!--右侧内容-->
        <div class="content-wrapper __dynamicTabContentWrapper">
          <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="dataSourceList" :isShowAllColumn="true" :optColWidth="130"
            :loading="tableLoading" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode'
            :isShowBtnsArea='false' @sortChagned="handleSortChange">

              <template slot="ResponseTime" slot-scope="scope">
                  <template v-if="scope.row.ResponseTime>0">
                      {{scope.row.ResponseTime}}分钟
                  </template>
                  <template v-else>不限时</template>
              </template>

              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                  <template slot="KeyWords">
                    <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable
                      v-model.trim="listQuery.KeyWords" placeholder="搜索试卷名称/出卷人"></el-input>
                  </template>
                  <!-- 表格批量操作区域 -->
                  <template slot="btnsArea">
                    <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked"></permission-btn>
                  </template>
                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button @click="handleView(scope.row)" :type="2" text="考试情况"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleEdit(scope.row, 'update')" :type="1"></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
              </template>

            </app-table>
          </div>
          <!----------------------------------------- 分页 ------------------------------------------->
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>
    <!--添加/修改 弹窗组件区-->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="implementationProblemId" :selectClassifyId="selectClassifyId" @reload="getList"></create-page>

    <!--添加/修改 分类 弹窗组件区-->
    <classify-page :dialogStatus="classifyDialogStatus" :node="paramNode" :dialogFormVisible="classifyDialogFormVisible" @closeDialog="classifyCloseDialog" @saveSuccess="classifySaveSuccess"></classify-page>

    <examination-page @closeDialog="examinationVisible=false" :dialogFormVisible="examinationVisible" :checkedNode="examinationRow" />
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as classify from '@/api/classify'
import * as examinationPaperManagementApi from '@/api/knowledge/ExaminationPaperManagement'
import createPage from './create'
import classifyPage from "./classify";
import examinationPage from "./examinationDetails";
import { listToTreeSelect } from "@/utils";
import { vars } from '../common/vars'

export default {
  /**名称 */
  name: "question-bank",
  mixins: [indexPageMixin],
  /**组件声明 */
  components: {
    createPage,
    classifyPage,
    examinationPage
  },
  filters: {
    // statusFilter(status) {
    //   let obj = StatusList.find(
    //     s => s.value == status
    //   );
    //   if (obj) {
    //     return obj.label;
    //   }
    //   return status;
    // },
  },
  /**数据区 */
  data() {
    return {
      searchTypes: vars.questionBankEnum.searchTypes, // 顶部筛选条件
      questionTypes:  vars.questionBankEnum.questionTypes, // 顶部筛选条件
      
      implementationProblemId: '',

      /******************* 弹窗 *******************/
      dialogStatus: 'create',
      dialogFormVisible: false,
      selectClassifyId: "",

      /******************* 表格 *******************/
      layoutMode: 'simple',
      tableLoading: false,
      dataSourceList: [],
      total: 0,

      listQuery: {
        KeyWords: '',
        ClassifyId: null,
        LastUpdateTime: [],
        QuestionType: null, // 题目类型  (单选 多选 判断题)
        DifficultyLevel: 0, // 题目级别  (初级 中级 高级)
      },


      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        // { prop: "QuestionType", label: "题型" },
        // { prop: "LastUpdateTime", label: "最后修改时间" },
      ],

      tabColumns: [
        {
          attr: { prop: "ExaminationPaperName", label: "试卷名称", showOverflowTooltip: true, width: 200 }
        },
        {
          attr: { prop: "ClassifyName", label: "试卷类型", showOverflowTooltip: true, width: 200 }
        },
        {
          attr: { prop: "OwnerEmployeeNames", label: "出卷人", showOverflowTooltip: true }
        },
        {
          attr: { prop: "QuestionNum", label: "题目数量", sortable: 'QuestionNum' }
        },
        {
          attr: { prop: "ResponseTime", label: "作答时间", sortable: 'ResponseTime' }, slot: true
        },
        {
          attr: { prop: "Integral", label: "积分", sortable: 'Integral' }
        },
        // {
        //   attr: { prop: "Total", label: "总分", sortable: 'Total' }
        // },
        {
          attr: { prop: "ExamPeopleCount", label: "参考人数", sortable: 'ExamPeopleCount' }
        },
      ],

      /******************* 权限 *******************/
      btnAddChildren: '',
      btnAdd: '',
      btnEdit: '',


      /******************* 树 *******************/
      /**树节点弹窗 */
      classifyDialogFormVisible: false,
      classifyDialogStatus: "create",
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },

      examinationRow: {},
      examinationVisible: false
    };
  },
  computed: {},
  watch: {
    // "listQuery.visitRecordTypeEnum"() {
    //     this.listQuery.PageIndex = 1
    //     this.getList();
    // },
    "listQuery.DifficultyLevel"() {
        this.listQuery.PageIndex = 1
        this.getList();
    },
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.ClassifyId = val.Id;
          this.listQuery.PageIndex = 1;
          this.getList();
        }
      },
      immediate: true
    }
  },
  created() {
    this.btnTextValue();
  },
  mounted() {
    this.loadTreeData();
  },
  methods: {
    // 查看考试情况
    handleView(row){
      this.examinationRow = row;
      this.examinationVisible = true;
    },
    // 难易等级 文字转换
    getDifficultyLevelObj(val) {
        return this.searchTypes.filter(s=>s.value!=0).find(
            s => s.value == val
        ) || {};
    },
    // 问题类型 文字转换
    getQuestionTypeObj(val) {
        return this.questionTypes.find(
            s => s.value == val
        ) || {};
    },
    handleFilterBtn(btns) {
      if (btns && btns.length > 0) {
        return btns.filter(s => s.DomId != 'btnAddChildren')
      }
      return []
    },
    /******************* 表格事件 *******************/
    /**加载数据 */
    getList() {
      let self = this, postData = JSON.parse(JSON.stringify(self.listQuery));
      if (self.checkedNode.Id) {
        self.listQuery.ClassifyId = self.checkedNode.Id;
      }
      if (postData.LastUpdateTime.length > 0) {
        postData.LastUpdateTimeStart = postData.LastUpdateTime[0]
        postData.LastUpdateTimeEnd = postData.LastUpdateTime[1]
      }
      postData = this.assignSortObj(postData);
      delete postData.LastUpdateTime
      self.tableLoading = true
      examinationPaperManagementApi.getList(postData).then(response => {
        self.tableLoading = false
        self.total = response.Total;
        self.dataSourceList = response.Items;
      }).catch(err => {
        self.tableLoading = false
      });
    },

    /**添加 */
    handleAdd(activeName) {
      this.selectClassifyId = this.checkedNode.Id;
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },

    /**修改 */
    handleEdit(row, optType) {
      this.selectClassifyId = "";
      this.implementationProblemId = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    /**删除 */
    handleDelete(row) {
      let ids = [];
      ids.push(row.Id);
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        examinationPaperManagementApi.del(ids).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    handleSortChange({ column, prop, order }) {
      this.sortObj = { prop, order, };
      this.getList();
    },

    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    onResetSearch() {
      this.listQuery.LastUpdateTime = []
      this.listQuery.QuestionType = null
      this.listQuery.KeyWords = ""
      this.getList();
    },

    btnTextValue() {
      let btns = this.topBtns
      btns.forEach(item => {
        if (item["DomId"] == "btnAddChildren") {
          this.btnAddChildren = "btnAddChildren"
        }
        if (item["DomId"] == "btnAdd") {
          this.btnAdd = "btnAdd"
        }
        if (item["DomId"] == "btnEdit") {
          this.btnEdit = "btnEdit"
        }
      })
    },

    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleAdd("create");
          break;
      }
    },


    /******************* 弹窗相关 *******************/
    closeDialog() {
      this.dialogFormVisible = false
    },
    handleSaveSuccess(d) {
      if (!d) {
        this.closeDialog();
      }
      this.getList();
    },

    classifySaveSuccess(d) {
      if (!d) {
        this.classifyCloseDialog();
      }
      this.loadTreeData();
    },
    classifyCloseDialog() {
      this.classifyDialogFormVisible = false;
    },


    checkPremissBtns(domId) {
      return this.rowBtns.findIndex(b => b.DomId == domId) > -1
    },
    /******************* 树事件 *******************/
    loadTreeData() {
      let self = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 10
      };
      self.treeLoading = true
      classify.getListPage(paramData).then(response => {
        self.treeLoading = false
        response.Items.unshift({
          Id: "",
          Name: "全部",
          Level: 0,
          ParentId: null
        });
        self.treeData = listToTreeSelect(response.Items);

        if (self.treeData && self.treeData.length > 0) {
          if (
            !(
              self.checkedNode &&
              response.Items.find(t => {
                return t.Id == self.checkedNode.Id;
              })
            )
          ) {
            self.checkedNode = self.treeData[0];
          }
        } else {
          self.checkedNode = null;
        }
        if (self.checkedNode) {
          self.$nextTick(() => {
            self.$refs.treeRef.setCurrentKey(self.checkedNode.Id);
          });
        }
      }).catch(err => {
        self.treeLoading = false
      });
    },
    /**添加顶级节点 */
    addTopLevel() {
      this.paramNode = {
        Id: null,
        Name: "",
        Level: 0
      };
      this.classifyDialogStatus = "create";
      this.classifyDialogFormVisible = true;
    },
    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    /**树下拉事件 */
    handleCommand(optType, node, data) {
      switch (optType) {
        case "create":
          this.paramNode = data;
          this.classifyDialogStatus = "create";
          this.classifyDialogFormVisible = true;
          break;
        case "update":
          this.paramNode = data;
          this.classifyDialogStatus = "update";
          this.classifyDialogFormVisible = true;
          break;
        case "delete":
          this.handleDeleteArea(data);
          break;
        default:
          break;
      }
    },
    /**删除树节点 */
    handleDeleteArea(data) {
      if (data.children && data.children.length > 0) {
        this.$notify({
          title: "提示",
          message: "请先删除子级",
          type: "error",
          duration: 2000
        });
        return;
      }
      let paramData = { ids: [data.Id], businessType: 10 };
      this.$confirm(`是否确认删除${data.Name}?`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        classify.del(paramData).then(res => {
          if (this.checkedNode && this.checkedNode.Id == data.Id) {
            this.checkedNode = null;
          }
          this.loadTreeData();
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
        });
      });
    },
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.bg-white {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      //   padding: 10px;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
