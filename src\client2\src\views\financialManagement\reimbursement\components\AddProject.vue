<!-- 关联研发项目(根据金蝶选择的公司获取项目列表) -->
<template>
  <div class="add_project">
    <div class="add_project_btn">
      <span class="text-secondary">关联研发项目</span>
      <el-button type="primary" size="mini" @click="handleAddProject" v-if="!disabled">
        添加项目
      </el-button>
    </div>
    <div class="project_list">
      <div class="project_item" v-for="(item, index) in list" :key="index">
        <div class="cell top">
          <span class="flex-1 omit" :title="item.KingdeeName">{{ item.KingdeeName }}</span>
          <span class="flex-0">
            <app-table-row-button
              :type="3"
              v-if="!disabled"
              text="删除"
              @click="deleteProject(index)"
            />
          </span>
        </div>
        <div class="cell omit" :title="item.KingdeeNumber">项目编号：{{ item.KingdeeNumber }}</div>
      </div>
    </div>

    <SelectProjectDialog
      multiple
      :isShow="dialogVisible"
      :checkedList="list"
      :disabledList="[]"
      @changed="selectChange"
      @closed="dialogVisible = false"
      :companyId="companyId"
    />
  </div>
</template>

<script>
import SelectProjectDialog from "./SelectProjectDialog";

export default {
  name: "AddProject",
  components: {
    SelectProjectDialog,
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 公司id
    companyId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      list: [],
      keywords: "",
      dialogVisible: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.list = this.$_.cloneDeep(val);
      },
      immediate: true,
    },
  },
  methods: {
    deleteProject(index) {
      this.list.splice(index, 1);
      this.change();
    },
    change() {
      const list = this.$_.cloneDeep(this.list);
      this.$emit("input", list);
      this.$emit("change", list);
    },
    handleAddProject() {
      if (!this.companyId) {
        this.$message.error("请先选择公司");
        return;
      }
      this.dialogVisible = true;
    },
    selectChange(list) {
      if (!list.length) return;
      this.list = this.$_.cloneDeep(list);
      this.change();
    },
  },
};
</script>

<style lang="scss" scoped>
.add_project {
  .add_project_btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .project_list {
    margin-top: 10px;
    .project_item {
      font-size: 12px;
      // min-height: 85px;
      padding: 5px;
      line-height: normal;
      &:not(:last-child) {
        padding-bottom: 10px;
        border-bottom: 1px solid $border-color-light;
      }
      .cell {
        color: $text-secondary;
        &:not(:first-child) {
          margin-top: 4px;
        }
        &.top {
          color: $text-main-color;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }
  .dialog_body_wrapper {
    height: 500px;
    display: flex;
    flex-direction: column;
    .table_container {
      flex: 1;
    }
  }
}
</style>
