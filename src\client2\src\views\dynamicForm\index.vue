<template>
    <div>
        <div class="app-container">
            <div class="bg-white">
                <app-table ref="mainTable" :multable='false' :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns='tabAuthColumns' :isShowAllColumn='isShowAllColumn' :loading="listLoading"
                    @rowSelectionChanged="rowSelectionChanged"
                    :startOfTable='startOfTable'>

                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                        <permission-btn moduleName="emp" v-on:btn-event="onBtnClicked"></permission-btn>
                    </template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type='1'></app-table-row-button>
                        <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type='3'></app-table-row-button>
                        <!-- <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)"
                            :type='1'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDetail')"
                            @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)"
                            :type='3'></app-table-row-button> -->
                    </template>
                </app-table>
                <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
            <div v-if="dialogVisible">
                <edit-page :dialogStatus='dialogStatus' :dialogFormVisible='dialogVisible' :formObj='formObj'
                    @closeDialog='closeDialog' @saveSuccess='handleSaveSuccess' @reloading='handleReloading' 
                    >
                </edit-page>
            </div>
        </div>
    </div>
</template>

<script>
    import { listToTreeSelect } from '@/utils'
    // import * as ec from '@/api/enterpriseConfigure'
    import elDragDialog from '@/directive/el-dragDialog'
    import indexPageMixin from '@/mixins/indexPage'
    import mixin from '../dynamicFormCommon/mixins'
    import EditPage from './edit'
    import * as df from '@/api/dynamicForm'

    export default {
        name: 'build-form-list',
        components: {
            EditPage,
        },
        directives: {
            // waves,
            elDragDialog
        },
        mixins: [indexPageMixin, mixin],
        watch: {
            
        },
        data() {
            return {
                isShowAllColumn: true,
                tabDatas: [
                    // {
                    //     "id": "xxx-xxx-xxx",
                    //     "formTitle": "项目管理首页表单",
                    //     "formContent": {
                    //         "name": "Container",
                    //         "level": 1,
                    //         "type": "1",
                    //         "children": [
                    //             {
                    //                 "type": 1,
                    //                 "level": 2,
                    //                 "children": [
                    //                     {
                    //                         "type": 3,
                    //                         "level": 3,
                    //                         "attrs": [
                    //                             {
                    //                                 "type": 3,
                    //                                 "label": "名称",
                    //                                 "attrName": "name",
                    //                                 "value": "asdfasdfas",
                    //                                 "desc": "要在此表单元素上呈现的名称。"
                    //                             },
                    //                             {
                    //                                 "type": 3,
                    //                                 "label": "ID",
                    //                                 "attrName": "id",
                    //                                 "value": "sdfasdf",
                    //                                 "desc": "要在此表单元素上呈现的ID。"
                    //                             },
                    //                             {
                    //                                 "type": 5,
                    //                                 "label": "类型",
                    //                                 "attrName": "type",
                    //                                 "value": "1002",
                    //                                 "desc": "要在此表单元素上呈现的ID。",
                    //                                 "options": [
                    //                                     { "value": "1000", "label": "文本" },
                    //                                     { "value": "1001", "label": "数字" },
                    //                                     { "value": "1002", "label": "电子邮件" },
                    //                                     { "value": "1003", "label": "电话" },
                    //                                     { "value": "1004", "label": "日期" },
                    //                                     { "value": "1005", "label": "时间" },
                    //                                     { "value": "1006", "label": "密码" }
                    //                                 ]
                    //                             },
                    //                             {
                    //                                 "type": 3,
                    //                                 "label": "默认值",
                    //                                 "attrName": "defaultValue",
                    //                                 "value": "adfasdfasdf",
                    //                                 "desc": "字段的默认值"
                    //                             }
                    //                         ]
                    //                     }
                    //                 ]
                    //             }
                    //         ]
                    //     },
                    // }
                ],
                //isCreateAccount: true, //是否同步创建用户
                multipleSelection: [],
                tableSearchItems: [
                    // { prop: 'OrgId', label: '所在组织' },
                    // { prop: 'Name', label: '姓名' },
                    // { prop: 'Phone', label: '手机' },
                    // { prop: 'Number', label: '工号' },
                    // { prop: 'JobTitle', label: '职称' },
                    // { prop: 'EducationId', label: '学历' },
                ],
                tabColumns: [
                    {
                        attr: { prop: 'formTitle', label: '表单名称' }
                    },
                    {
                        attr: { prop: 'formUrl', label: '请求地址' }
                    }
                ],
                
                tabDatas: [],
                listLoading: false,
                total: 0,
                textMap: {
                    update: '编辑',
                    create: '添加'
                },
                dialogVisible: false,
                dialogStatus: '',
                formObj: {}

            }
        },
        created() {
            
            this.getList()
        },
        mounted() {
            

        },
        methods: {
            handleCreate() {
                this.formObj = {
                    "id": '',
                    "formTitle": '',
                    "formContent": {
                        "id": this.newGuid(),
                        "name": "Container",
                        "level": 1,
                        "type": 1,
                        "children": []
                    },
                }
                this.dialogStatus = 'create'
                this.dialogVisible = true
            },
            closeDialog() {
                this.dialogVisible = false
            },
            handleReloading() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSaveSuccess(_formData) {
                this.listQuery.PageIndex = 1
                this.getList()
                this.dialogVisible = false
            },
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            onBtnClicked: function (domId) {
                // console.log('you click:' + domId)
                switch (domId) {
                    case 'btnAdd':
                        this.handleCreate()
                        break
                    case 'btnEdit':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行编辑',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0])
                        break
                    case 'btnDetail':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行查看',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0], 'detail')
                        break;
                    case 'btnDel':
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: '至少删除一个',
                                type: 'error'
                            })
                            return
                        }
                        this.handleDelete(this.multipleSelection)
                        break
                    default:
                        break
                }
            },
            getList() {
                this.listLoading = true
                df.getList(this.listQuery).then(response => {
                    this.tabDatas = response.Items
                    this.total = response.Total
                    this.listLoading = false
                })
            },
            handleFilter() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
            //选择部门
            handleChange(value) {

            },
            handleUpdate(row, optType = 'update') { // 弹出编辑框
                if(optType == 'detail') {
                    this.$router.push({name: 'dynamicFormAnalysis', params: {
                        id: row.id
                    }})
                }else{
                    df.detail({ formId: row.id }).then(response => {
                        let datas = response

                        datas.formContent = datas.formContent.map(s => {
                            s.children && delete s.children
                            return s
                        })

                        //将表单结构中复杂属性值字符串转为对象
                        datas = this.fetchFormStructDatasToObject(datas)
                        datas.formContent = this.mergeCtrlData(datas.formContent)
                        //创建表单时，后台会默认创建一个name=id的控件，用来标识表单唯一id——需要隐藏起来，不可操作
                        datas.formContent = datas.formContent.filter(s => s.type != 0)

                        //将表单数据转成tree格式
                        let treeDatas = listToTreeSelect(datas.formContent, undefined, undefined, {key: 'id', parentKey: 'pid'})
                        if(treeDatas && treeDatas.length > 0) {
                            datas.formContent = treeDatas[0]
                        }

                        this.formObj = datas
                        this.dialogStatus = optType
                        this.dialogVisible = true
                    })
                }
                
            },
            handleDelete(rows) { // 多行删除
                let ids = []
                if (_.isArray(rows)) {
                    ids = rows.map(u => u.id)
                } else {
                    ids.push(rows.id)
                }
                this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    df.del(ids).then(() => {
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.getList()
                    })
                })

            },
        }
    }
</script>

<style scoped>
    .sel-ipt,
    .dat-ipt {
        width: 100%;
    }

    .avatar {
        width: 68px;
        height: 68px;
    }

    .tip-avatar {
        width: 140px;
        height: 140px;
    }

    .avatar,
    .tip-avatar {
        border-radius: 50%;
    }

    /* .cus_wdt{
    width: 200px;
} */
</style>
