<!--用车-->
<template>
  <div>
    <app-dialog title="用车" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600" :width="500">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">

          <el-row style="width:100%">
            <el-col :span="12">
              <el-form-item label="车牌号">
                <label>{{obj.CarNumber}}</label>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="司机">
                <label>{{obj&&obj.DriverEmployee?obj.DriverEmployee.Name:"无"}}</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="用车时间" prop="CarServiceTime">
                <el-date-picker style="width:100%" v-model="formData.CarServiceTime" type="datetime" placeholder="请选择用车时间" default-time="00:00:00" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row style="width:100%">
            <el-col :span="24">
              <el-form-item label="预计还车">
                <el-radio v-model="IsCarReturnTimePlan" :label="true">预计时间</el-radio>
                <el-radio v-model="IsCarReturnTimePlan" :label="false">未知时间</el-radio>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-show="IsCarReturnTimePlan">
            <el-col :span="24">
              <el-form-item label="设置时间" prop="UseHourState">
                <el-radio v-model="formData.UseHourState" :label="1">12小时</el-radio>
                <el-radio v-model="formData.UseHourState" :label="2">24小时</el-radio>
                <el-radio v-model="formData.UseHourState" :label="3">6小时</el-radio>

                <!-- <el-date-picker style="width:100%" v-model="formData.CarReturnTimePlan" type="datetime" placeholder="请选择预计还车时间" default-time="00:00:00" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm">
                </el-date-picker> -->
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="使用人" prop="UsersList">
                <emp-selector :showType="2" :isAutocomplete='true' :multiple="true" placeholder="请选择使用人" :list="formData.UsersList" @change="handleChangeUsers" :beforeConfirm='handleBeforeConfirm'></emp-selector>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="目的地" prop="Destination">
                <el-input v-model="formData.Destination" maxlength="100" type="text" placeholder="请输入目的地">
                  <template slot="append">
                    <el-button @click="handleRegionalDialog()">选择</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="用车理由" prop="UseReason">
                <el-input v-model="formData.UseReason" maxlength="100" type="textarea" :rows="3" placeholder="请输入用车理由">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" :buttonType='1' type="primary" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>

    <!-- 选择地区 -->
    <v-area-choose v-if="dialogRegionalVisible" @closeDialog="closeRegionalDialog" @electedRegionalData="electedRegionalData" :dialogFormVisible="dialogRegionalVisible" :disabledFn="disabledFn"></v-area-choose>
  </div>
</template>

<script> 
import * as carRecordMgt from '@/api/personnelManagement/carCoordinatorRecord'
import empSelector from "@/views/common/empSelector";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";

import dayjs from "dayjs";
export default {
  name: "use-car",
  directives: {},
  components: {
    empSelector,
    vAreaChoose
  },
  mixins: [],
  props: {
    obj: {
      type: Object,
      required: true
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.IsCarReturnTimePlan = true;
      }
      if (val) {
        this.resetFormData();
        this.getLastUserList();
      }
    }
  },
  computed: {
  },
  /**渲染后 */
  mounted() {
  },
  created() {

  },
  data() {
    return {
      disabledBtn: false,
      IsCarReturnTimePlan: true,

      formLoading: false,
      rules: {
        CarServiceTime: {
          required: true,
          message: '请选择用车时间',
          trigger: 'change'
        },
        UsersList: {
          required: true,
          message: '请选择使用人',
          trigger: 'change'
        },
        Destination: {
          required: true,
          message: '请输入目的地',
          trigger: 'blur'
        },
        CarReturnTimePlan: [{
          validator: (rule, value, callback) => {
            if (this.IsCarReturnTimePlan) {
              if (value != "" && value != null) {
                callback()
              } else {
                callback(new Error("请选择预计还车时间"))
              }
            } else {
              callback()
            }
          }
        }],
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        CarId: "",
        CarServiceTime: dayjs().format('YYYY-MM-DD HH:mm'),
        Users: "",
        UsersList: [],
        Destination: "",
        UseReason: "",
        CarReturnTimePlan: "",
        UseHourState: 1
      },
      dialogRegionalVisible: false
    };
  },
  methods: {

    getLastUserList() {
      let _this = this;
      let postData = {
        carId: this.obj.Id
      }
      carRecordMgt.getLast(postData).then(response => {
        _this.handleChangeUsers(response)
      }).catch(err => {
        _this.carStateList = []
      });
    },

    handleRegionalDialog() {
      this.dialogRegionalVisible = true;
    },

    closeRegionalDialog() {
      this.dialogRegionalVisible = false;
    },

    electedRegionalData(data) {
      if (data) {
        this.formData.Destination = data.ParentName;
        this.$refs.formData.validateField("Destination");
      } else {
        this.formData.Destination = '';
      }
    },

    disabledFn(data, nodeType) {
      //禁选一级节点
      if (data.level <= 1) {
        return true
      }
      return false
    },

    handleChangeUsers(users) {
      if (users && users.length > 0) {
        this.formData.Users = users.map(s => s.EmployeeId).toString();
        this.formData.UsersList = users;
      } else {
        this.formData.Users = "";
        this.formData.UsersList = [];
      }
    },
    handleBeforeConfirm(users) {
      if (users && users.length > 10) {
        this.$message({
          message: '司机不得超过10人',
          type: 'error'
        })
        return false
      }
      return true
    },


    resetFormData() {
      let temp = {
        Id: "",
        CarId: "",
        CarServiceTime: dayjs().format('YYYY-MM-DD HH:mm'),
        Users: "",
        UsersList: [],
        Destination: "",
        UseReason: "",
        CarReturnTimePlan: "",
        UseHourState: 1
      };
      this.formData = Object.assign({}, this.formData, temp);
      this.IsCarReturnTimePlan = true;
    },

    handleSave() {
      this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      Promise.all([listResult]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        postData.CarId = this.obj.Id;

        if (!this.IsCarReturnTimePlan) {
          postData.CarReturnTimePlan = "";
          postData.UseHourState = "";
        }

        delete postData.Id;
        carRecordMgt.add(postData).then(res => {
          this.disabledBtn = false;
          this.$notify({
            title: '成功',
            message: '创建成功！',
            type: 'success'
          });
          this.$emit('saveSuccess', false);
        }).catch(err => {
          this.disabledBtn = false;
        })

      }).catch(err => {
        this.disabledBtn = false;
      })
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
