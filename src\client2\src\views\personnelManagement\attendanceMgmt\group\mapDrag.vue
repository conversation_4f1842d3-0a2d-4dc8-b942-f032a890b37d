<!--
  描述：拖放地图组件，默认尺寸是 500 * 300

  接收属性参数：
    lat: 纬度
    lng: 经度

  自定义事件：
    drag: 拖放完成事件

  示例：
    <mapDrag @drag="dragMap" lat="22.574405" lng="114.095388"></mapDrag>
-->
<template>
  <div class="m-map">
    <div class="search">
      <input type="text" placeholder="请输入关键字" v-model="searchKey">
      <button type="button" @click="handleSearch">搜索</button>
      <button type="button" @click="handleClear">清除</button>
      <div v-show="searchKey" class="result">
        <div v-for="(p, idx) in positionList" :key="idx" @click='moveTo(p)'>
          <span>{{ p.name }}</span>
          <span class="color-info"> {{ p.address }}</span>
        </div>
      </div>
    </div>
    <div id="js-container" class="map">正在加载数据 ...</div>
  </div>
</template>

<script>
// import remoteLoad from './remoteLoad.js'
// import { MapKey, MapCityName } from '@/config/config'
import { remoteLoad } from '@/utils/index'
export default {
  props: ['lat', 'lng', 'range', 'status'],
  data() {
    return {
      map: null,
      zoom: 16,
      marker: null,
      circle: null,
      searchKey: '',
      placeSearch: null,
      positionList: [],

      // dragStatus: false,
      // AMapUI: null,
      // AMap: null,
      // MapKey: 'de13aada309258f42c9e4e192fd0a2d9',
      // MapCityName: '深圳',
    }
  },
  watch: {
    searchKey() {
      if (this.searchKey === '') {
        // this.placeSearch.clear()
      }
    },
    range: {
      handler(val) {
        if (val) {
          if (this.range && this.map) {
            this.circle.setRadius(val)
            // this.map.remove(this.circle)
            // this.circle = new AMap.Circle({
            //   center: [this.lng, this.lat],// 圆心位置
            //   radius: this.range, //半径
            //   strokeColor: "#85BCFD", //线颜色
            //   strokeOpacity: 0.7, //线透明度
            //   strokeWeight: 3,    //线宽
            //   fillColor: "#1791FC", //填充色
            //   fillOpacity: 0.3,//填充透明度
            //   zIndex: 50
            // });
            // console.log('值变化圆');
            // console.log(this.circle);
            // console.log(this.range);
            // this.circle.setMap(this.map);//显示圆圈
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    if (this.map) {
      this.map.removeEventListener('moveend')
      this.map = null
    }
  },
  methods: {
    // 搜索
    handleSearch() {
      let that = this
      if (that.searchKey) {
        
        let config = {
            pageCapacity: 10,	//每页显示的数量
            onSearchComplete: that.localSearchResult, //接收数据的回调函数
        };
        //创建搜索对象
        that.placeSearch = new T.LocalSearch(that.map, config);

        that.placeSearch.search(that.searchKey, 4)

      }
    },
    localSearchResult(result) {
      this.positionList = result.getSuggests()
    },
    //清除结果
    handleClear() {
      this.searchKey = ''
      // this.placeSearch.clear()
    },
    moveTo(pos) {
      if(pos && pos.lonlat) {
        let r = pos.lonlat.split(',')
        this.map.panTo(new T.LngLat(r[0], r[1])); 
      }
    },
    // 实例化地图
    initMap() {
      let that = this

      let _initMap = () => {
        that.map = new T.Map('js-container');
        that.map.centerAndZoom(new T.LngLat(that.lng, that.lat), that.zoom);
  
        //向地图上添加标注
        that.marker = new T.Marker(new T.LngLat(that.lng, that.lat));
        that.map.addOverLay(that.marker);
  
        // 定义该矩形的显示区域
        that.circle = new T.Circle(new T.LngLat(that.lng, that.lat), that.range, {
          color:"#85BCFD",
          weight: 3, 
          opacity: 0.7, 
          fillColor:"#1791FC", 
          fillOpacity: 0.3, 
          lineStyle:"solid",
        });
        //向地图上添加圆
        that.map.addOverLay(that.circle);
  
        let getPositionInfo = (lnglat) => {
          let geocode = new T.Geocoder();
          geocode.getLocation(lnglat, (local) => {
            local.range = that.range
            that.$emit('drag', local)
          });
        }
  
        if(that.status == 'create') {
          let lnglat = new T.LngLat(that.lng, that.lat)
          getPositionInfo(lnglat)
        }
  
        that.map.addEventListener("moveend", (e) => {
          let _lng = e.target.getCenter().getLng()
          let _lat = e.target.getCenter().getLat()
  
          let lnglat = new T.LngLat(_lng, _lat)
          that.marker.setLngLat(lnglat)
          that.circle.setCenter(lnglat)
  
          getPositionInfo(lnglat)
  
        });
      }

      if(!window.T || !window.T.Map) {
        remoteLoad('https://api.tianditu.gov.cn/api?v=4.0&tk=8d0260a4d5e77291a423fdc1d256b336').then(res => {
          _initMap()
        })
      }else{
        _initMap()
      }


      // // 获取地图中心位置
      // function getMapCenter() {
      //     alert("当前地图中心点：" + map.getCenter().getLng() + "," + map.getCenter().getLat());
      // }

      // //获取地图缩放级别
      // function getMapZoom() {
      //     alert("当前地图缩放级别：" + map.getZoom());
      // }

    },
    show() {
      this.marker.show()
      this.circle.show()
    },
    hide() {
      this.marker.hide()
      this.circle.hide()
    },
  },
  
 
}
</script>

<style lang="scss" scoped>
.m-map {
  min-width: 500px;
  min-height: 300px;
  position: relative;
}
.m-map .map {
  width: 100%;
  height: 100%;
}
.m-map .search {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 350px;
  z-index: 999;
}
.m-map .search input {
  width: 180px;
  border: 1px solid #ccc;
  line-height: 20px;
  padding: 5px;
  outline: none;
}
.m-map .search button {
  line-height: 26px;
  background: #fff;
  border: 1px solid #ccc;
  width: 50px;
  text-align: center;
  margin-left: 5px;
}
.m-map .result {
  max-height: 500px;
  overflow: auto;
  margin-top: 10px;
  background: #fff;

}
.m-map .result > div{
  padding: 5px;
  cursor: pointer;
  line-height: 24px;

}
.m-map .result > div:not(:last-child){
  border-bottom: 1px solid $border-color-light;
}
</style>
