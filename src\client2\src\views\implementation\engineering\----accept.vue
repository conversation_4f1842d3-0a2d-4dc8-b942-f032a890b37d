<template>
    <div>
        <app-dialog
            :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :maxHeight='dialogWidth'
        >
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                    <div class="wrapper">
                        <div class="left">
                            <div>
                                <div class="panel-title">项目信息</div>
                                <div>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="验收目标" prop="AcceptanceResult">
                                                <el-radio
                                                    :disabled='!editable'
                                                    v-model="formData.AcceptanceResult"
                                                    v-for="(t, idx) in acceptTypes"
                                                    :key="idx"
                                                    :label="t.value"
                                                >
                                                    {{ t.label }}
                                                </el-radio>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="验收结论" prop="AcceptanceConclusion">
                                                <el-input type="textarea" maxlength="2000" :disabled='!editable' :rows="8" v-model="formData.AcceptanceConclusion"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="漏留事项" prop="LegacyMatters">
                                                <div class="btns-area">
                                                    <el-button type="text" v-show="editable" :disabled='!editable' class="button" @click="handleAddMatters">增加事项</el-button>
                                                </div>
                                                <div class="appr-level-wrapper" v-for="(m, idx) in formData.LegacyMatters" :key="idx">
                                                    <div class="item-wrapper">
                                                        <el-input type="text" :disabled='!editable' v-model="formData.LegacyMatters[idx]"></el-input>
                                                    </div>
                                                    <i class="icon el-icon-circle-close" v-show='editable' @click='handleRemove(idx)'></i>
                                                </div>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                            <div>
                            </div>
                            <div>
                                <!-- <div class="panel-title">审批</div> -->
                                <div>
                                    <approval-panel v-if="dialogStatus == 'create'" ref="approvalPanel" :editable='editable' :approvalPanelObj='formData.Approval'></approval-panel>
                                    <approval-detail v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :isOnlyViewDetail='isOnlyViewDetail' :approvalObj='formData.Approval'></approval-detail>
                                </div>
                            </div>
                        </div>
                        <div class="right">
                            <div>
                                <div class="panel-title">交付内容</div>
                                <!-- <div v-if="dialogStatus != 'create' && formData.AttachmentList.length>0"> -->
                                <div>
                                    <app-uploader
                                        :readonly="!editable"
                                        accept='all'
                                        :fileType='3'
                                        :max='10000'
                                        :value='formData.AttachmentList'
                                        :fileSize='1024 * 1024 * 500'
                                        :minFileSize='100 * 1024'
                                        @change='handleFilesUpChange'
                                    ></app-uploader>
                                </div>
                                <!-- <no-data v-else></no-data> -->
                            </div>
                        </div>
                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button @click="createData" type="primary" v-show="editable" :disabled='disabledBtn'>确认</el-button>
                <el-button @click="handleContinue" type="primary" v-show="isContinue && !editable">再次编辑</el-button>

                <!-- 审批模式下才显示审批 -->
                <el-button @click="handleApproval" type="primary" v-show="dialogStatus == 'approval' && !isOnlyViewDetail" :disabled='disabledBtn'>审批</el-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import noData from "../../common/components/noData";
import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail'
import { vars } from '../common/vars'
import * as impManagement from "@/api/implementation/impManagement";
  export default {
    name: "project-mgmt-accept",
    directives: {
    },
    components: {
        approvalPanel,
        approvalDetail,
        noData
    },
    props: {
        specialPageTitle: {
            type: String
        },
        dialogStatus: { //create、update、detail
            type: String
        },
        ImplementId: { //项目id，新增需要
            type: String,
            default: ''
        },
        id: { //验收主键id，编辑、详情 需要
            type: String,
            default: ''
        },
        //是否能再次编辑（在详情页面直接改为编辑页面）
        isContinue: {
            type: Boolean,
            default: false
        },
        approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
            type: String,
            default: ''
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
    },
    computed: {
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            return '验收交付'
        }
    },
    watch: {
        ImplementId: {
            handler(val) {
                this.formData.ImplementId = val
            },
            immediate: true
        },
        '$attrs.dialogFormVisible': {
            handler(val) {
                if(val) {
                    this.resetFormData()
                    if(this.dialogStatus != 'create' && this.id) {
                        this.getDetail()
                    }
                }

                if(!val && this.isContinue) {
                    this.editable = false
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            disabledBtn:false,
            editable: this.dialogStatus != "detail" && this.dialogStatus != 'approval', //不等于详情页面可编辑
            dialogWidth: 1000,
            rules: {
                AcceptanceConclusion: { fieldName: "验收结论", rules: [{ required: true }] },
            },
            labelWidth: '100px',
            acceptTypes: vars.acceptTypes,
            formData: {
                Id: '', //ID
                ImplementId: '',//项目id
                AcceptanceResult: 1, //验收结果
                AcceptanceConclusion: '', //验收结论
                LegacyMatters: [''], //遗留事项
                AttachmentList: [],// 项目附件ID列表
                Approval: {//审批信息
                    // ApprovalEmployeeList: [[]],
                    // ApprovalType: 1, //1:单签 2:会签
                    // CCEmployeeList: [],

                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                }
            },
        };
    },
    methods: {
// 项目创建审批
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
        
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.disabledBtn = true
                        //项目创建审批
                        impManagement.acceptApproval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                        this.disabledBtn = false
                    })
                    })
                }
            })
        },
        handleContinue() {
            this.dialogStatus = 'create'
            this.editable = true
        },
        resetFormData() {
            let temp = {
                Id: '', //ID
                // ImplementId: '',//项目id
                AcceptanceResult: 1, //验收结果
                AcceptanceConclusion: '', //验收结论
                LegacyMatters: [''], //遗留事项
                AttachmentList: [],// 项目附件ID列表
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                }
            }

            this.formData = Object.assign({}, this.formData, temp)

        },
        createData() {
            let validate = this.$refs.formData.validate()
            let approvalPanelValidate = this.$refs.approvalPanel.validate()

            Promise.all([validate, approvalPanelValidate]).then(valid => {
                this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块
                let postData = JSON.parse(JSON.stringify(this.formData));

                postData = Object.assign({}, postData)

                postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                this.disabledBtn = true
                let result = null
                console.log(postData)
                // isContinue 表示重用审批信息，新增操作
                if(this.dialogStatus == 'create' || this.isContinue) {
                    delete postData.Id
                    result = impManagement.addAccept(postData)
                }else if(this.dialogStatus == 'update') {
                    result = impManagement.edit(postData)
                }

                result.then(res => {
                    this.disabledBtn = false
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData()
                }).catch(err => {
                    this.disabledBtn = false
                })


            })
        },
        getDetail() {
            impManagement.getAcceptDetail({id: this.id,approvalId: this.approvalId }).then(res => {
                this.formData = Object.assign({}, this.formData, res)
            })
        },
        handleChanged(approvalLevelUsers) {
            this.formData.Approval.ApprovalEmployeeList = approvalLevelUsers
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        handleAddMatters() {
            this.formData.LegacyMatters.push('')
        },
        handleRemove(idx) {
            this.formData.LegacyMatters.splice(idx, 1)
        },
    }
};
</script>

<style lang="scss" scoped>

.step-wrapper{
    .step-item-wrapper{
        padding: 20px;
    }
}

.wrapper{
    display: flex;
    .left{
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 6px;
    }
    .right{
        width:40%;
    }
}

.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}

.appr-level-wrapper{
    padding-bottom: 12px;
    display: flex;
    .item-wrapper{
        flex: 1;
    }
    .icon{
        width: 28px;
        height: 28px;
        font-size: 20px;
        padding: 4px;
        cursor: pointer;
        vertical-align: middle;
    }
}
</style>
