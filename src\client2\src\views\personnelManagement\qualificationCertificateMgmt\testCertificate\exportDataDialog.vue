<template>
  <app-dialog
    title="导出数据"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :width="800"
    className="clear-padding"
  >
    <template slot="body">
      <div class="body_wrapper">
        <main
          v-loading="loading"
          element-loading-text="文件生成中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255,255, 0.8)"
        >
          <span class="title">导出字段选择</span>
          <div class="all_check">
            <el-checkbox v-model="isAllCheck" @change="changeAllCheck">全选</el-checkbox>
          </div>
          <div class="check_list">
            <el-checkbox-group v-model="checkList">
              <el-checkbox v-for="item in _tabColumns" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </main>
      </div>
    </template>
    <template slot="footer">
      <app-button @click="closeDialog" :buttonType="2" :loading="loading" />
      <app-button @click="createData" text="确定" :loading="loading" />
    </template>
  </app-dialog>
</template>

<script>
import * as exportPort from "@/api/export";
import { downloadFile } from "@/utils";

export default {
  props: {
    tabColumns: {
      type: Array,
      default: () => [],
    },
    listQueryParams: {
      type: Object,
      default: ()=>({}),
    },
    // 所选的行对象(为空的话默认导出当前页)
    multipleSelection:{
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      isAllCheck: false,
      checkList: [],
    };
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.closeDialog();
          return;
        }
        // 全选
        this.changeAllCheck(true);
      },
      immediate: true,
    },
    checkList(val) {
      this.isAllCheck = this._tabColumns.length === val.length;
    },
  },
  computed: {
    _tabColumns() {
      return this.tabColumns.map(item => ({
        label: item.attr.label,
        value: item.attr.prop,
      }));
    },
  },
  methods: {
    changeAllCheck(value) {
      this.isAllCheck = value;
      if (value) {
        this.checkList = this._tabColumns.map(item => item.value);
      } else {
        this.checkList = [];
      }
    },
    createData() {
      if(!this.checkList.length){
        this.$message.error('请至少选择一项导出字段');
        return
      }

      const params = {
        columns: this.checkList,
        exportSource: 35,
        searchCondition: {
          ...this.listQueryParams,
          IdList:this.multipleSelection.map(t=>t.Id),
        },
      };
      this.loading = true;
      exportPort
        .exportData(params, {
          timeout: 300 * 1000,
        })
        .then(res => {
          this.loading = false;
          this.closeDialog();
          downloadFile(res.Url);
        })
        .catch(err => {
          this.loading = false;
        });
    },
    closeDialog() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<style scoped lang="scss">
.body_wrapper {
  padding: 10px;
  .title {
    display: block;
    font-weight: bold;
    padding-bottom: 10px;
  }
  .check_list {
    margin-top: 10px;
  }
}
</style>
