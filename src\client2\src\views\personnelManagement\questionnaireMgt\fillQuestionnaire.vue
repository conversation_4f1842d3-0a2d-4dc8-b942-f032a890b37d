<!--填写问卷-->
<template>
<div class="dialogMain">
    <app-dialog title="填写问卷" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
        <template slot="body">
            <el-form ref="formData" :model="formData" :rules="formRules" style="padding-top:0;">
                <div class="pageWarp" v-loading="loading">
                    <el-tabs v-model="activeName" @tab-click="tabClick" lazy>
                        <el-tab-pane :label="surveyGroupItem.Name" :name="surveyGroupItem.Id"
                        v-for="surveyGroupItem in formData.SurveyGroupList" :key="surveyGroupItem.Id">
                        </el-tab-pane>
                    </el-tabs>
                    <template v-for="(surveyGroupItem,surveyGroupItemIndex) in formData.SurveyGroupList">
                        <div class="pageWarp_main" :key="surveyGroupItem.Id" v-if="activeName===surveyGroupItem.Id">
                            <div class="tabsBox">
                                <no-data v-if="surveyGroupItem.ParticipantEmployeeList.length==0"></no-data>
                                <div class="tabsBox_item" :class="{'active': surveyGroupItem.activeEmployeeId==employeeItem.EmployeeId}" @click="surveyGroupItem.activeEmployeeId=employeeItem.EmployeeId;tabClick();"
                                v-for="employeeItem in surveyGroupItem.ParticipantEmployeeList" :key="employeeItem.EmployeeId">
                                    <img class="tabsBox_item_userPhoto" :src="employeeItem.AvatarPath||require('../../../assets/images/avatar3.png')" />
                                    <div class="tabsBox_item_icon" :class="{'active':setCompletionStatus(employeeItem.QuestionAnswerData)}"></div>
                                    {{employeeItem.Name}}
                                </div>
                            </div>
                            <div class="opr">
                                <div ref="questionRef" class="question">
                                    <no-data v-if="surveyGroupItem.ParticipantEmployeeList.length==0"></no-data>
                                    <template v-for="(employeeItem,employeeItemIndex) in surveyGroupItem.ParticipantEmployeeList">
                                        <div :key="employeeItem.EmployeeId" v-if="surveyGroupItem.activeEmployeeId==employeeItem.EmployeeId">
                                            <template v-for="(questionItem,questionItemIndex) in employeeItem.QuestionAnswerData">
                                                <el-row :key="questionItemIndex">
                                                    <el-row class="question_title" v-if="questionItemIndex>0?QuestionAnswerData[questionItemIndex-1].SubmitItemType!=questionItem.SubmitItemType:true">{{questionItem.SubmitItemType|submitItemTypeFilter}}</el-row>
                                                    <el-row class="question_item">
                                                        <el-row class="question_item_title">{{questionItem.OptionIndex+1}}、{{questionItem.OptionIndex|questionAnswerDataFilter}}</el-row>
                                                        <el-row style="height: 47px;overflow: hidden;">
                                                            <el-form-item required label=" " :rules="{required: true, message: '得分不能为空', trigger: 'change'}"
                                                            :prop="`SurveyGroupList.${surveyGroupItemIndex}.ParticipantEmployeeList.${employeeItemIndex}.QuestionAnswerData.${questionItemIndex}.Value`">
                                                                <div class="flexWarp">
                                                                    <div>得分：</div>
                                                                    <div class="flexColumn not_backface">
                                                                        <el-radio-group v-model="questionItem.Value">
                                                                            <el-radio :label="0">0分</el-radio>
                                                                            <el-radio :label="1">1分</el-radio>
                                                                            <el-radio :label="2">2分</el-radio>
                                                                            <el-radio :label="3">3分</el-radio>
                                                                            <el-radio :label="4">4分</el-radio>
                                                                            <el-radio :label="5">5分</el-radio>
                                                                        </el-radio-group>
                                                                    </div>
                                                                </div>
                                                            </el-form-item>
                                                        </el-row>
                                                    </el-row>
                                                </el-row>
                                            </template>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <div class="fDiv fl" v-if="false">
                <el-button-group>
                    <el-button @click="submitValue" type="text">批量设置</el-button>
                    <el-button type="text">
                        <el-dropdown @command="commandValue=>submitValueObj.Type = commandValue" trigger="click">
                            <span class="el-dropdown-link">
                                {{submitValueObj.Type}}<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="当前页">当前页</el-dropdown-item>
                                <el-dropdown-item command="当前组">当前组</el-dropdown-item>
                                <el-dropdown-item command="所有组">所有组</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
                    <el-button type="text">
                        <el-dropdown @command="commandValue=>submitValueObj.Value = commandValue" trigger="click">
                            <span class="el-dropdown-link">
                                {{submitValueObj.Value=='随机'?'XX':submitValueObj.Value}}分<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="随机">随机</el-dropdown-item>
                                <el-dropdown-item :command="0">0分</el-dropdown-item>
                                <el-dropdown-item :command="1">1分</el-dropdown-item>
                                <el-dropdown-item :command="2">2分</el-dropdown-item>
                                <el-dropdown-item :command="3">3分</el-dropdown-item>
                                <el-dropdown-item :command="4">4分</el-dropdown-item>
                                <el-dropdown-item :command="5">5分</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
                </el-button-group>
            </div>
            <app-button :buttonType="2" @click="handleClose"></app-button>
            <!-- 确认 -->
            <app-button @click="submitData" :buttonType="1" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import NoData from "@/views/common/components/noData";
import * as SurveyApi from '@/api/personnelManagement/survey.js'
import { SurveyTypeEnum, QuestionAnswerType, QuestionAnswerData } from "../enum.js";
export default {
    name: "questionnaireMgt-select-classify",
    components: {
        NoData
    },
    props: {
        id: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            SurveyTypeEnum,
            QuestionAnswerType,
            QuestionAnswerData,


            activeName: '',

            disabledBtn: false,
            loading: false,
            formData: {
                SurveyGroupList: [],
            },
            formRules: {
                Name: {fieldName: "问卷名称",rules: [{ required: true }]},
            },

            submitValueObj: {
                Type: '当前页',
                Value: 1,
            },
        };
    },
    computed: {
    },
    filters: {
        questionAnswerDataFilter(val) {
            let obj = QuestionAnswerData.find(
                s => s.OptionIndex == val
            );
            if (obj) {
                return obj.Title;
            }
            return "无";
        },
        submitItemTypeFilter(val) {
            let obj = QuestionAnswerType.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "无";
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getDetail();// 查询 基本信息
                }
            },
            immediate: true
        },
    },
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    mounted() {},
    methods: {
        submitValue(){
            // submitValueObj.Value
            let self = this,
            SurveyGroupIndex = self.formData.SurveyGroupList.findIndex(s=>s.Id == self.activeName),
            EmployeeIndex = self.formData.SurveyGroupList[SurveyGroupIndex].ParticipantEmployeeList.findIndex(s=>s.EmployeeId == self.formData.SurveyGroupList[SurveyGroupIndex].activeEmployeeId);

            if(self.submitValueObj.Type=='当前页'){
                self.formData.SurveyGroupList[SurveyGroupIndex].ParticipantEmployeeList[EmployeeIndex].QuestionAnswerData.map(s=>{
                    s.Value = self.submitValueObj.Value!='随机'?self.submitValueObj.Value:(~~(Math.random() * 5))
                })
            }
            if(self.submitValueObj.Type=='当前组'){
                self.formData.SurveyGroupList[SurveyGroupIndex].ParticipantEmployeeList.map(q=>{
                    q.QuestionAnswerData.map(s=>{
                        s.Value = self.submitValueObj.Value!='随机'?self.submitValueObj.Value:(~~(Math.random() * 5))
                    })
                })
            }
            if(self.submitValueObj.Type=='所有组'){
                self.formData.SurveyGroupList.map(j=>{
                    j.ParticipantEmployeeList.map(q=>{
                        q.QuestionAnswerData.map(s=>{
                            s.Value = self.submitValueObj.Value!='随机'?self.submitValueObj.Value:(~~(Math.random() * 5))
                        })
                    })
                })
            }
        },
        // 点击切换时  清空已校验的结果
        tabClick(){
            this.$refs.formData.clearValidate();
            let refArr = this.$refs.questionRef;
            refArr.map((s,si)=>{
                this.$refs.questionRef[si].scrollTop = 0
            })
        },
        // 提交前手动校验 用于定位
        submitData(){
            let self = this,postData = JSON.parse(JSON.stringify(self.formData));
            if(postData.SurveyGroupList&&postData.SurveyGroupList.length>0){
                postData.SurveyGroupList.some((s,si)=>{
                    if(
                        s.ParticipantEmployeeList.some(q=>{
                            if(q.QuestionAnswerData.some(j=>j.Value==null)){
                                self.activeName = s.Id
                                self.formData.SurveyGroupList[si].activeEmployeeId = q.EmployeeId
                                self.tabClick();
                                return true;
                            }
                        })
                    ){
                        return true;
                    }
                })
            }
            // 上面定位完成后  去提交前校验 当前显示的模块
            self.$nextTick(()=>{
                self.createData();
            })
        },
        createData(){
            let self = this,postData = JSON.parse(JSON.stringify(self.formData));
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let newPostData = postData.SurveyGroupList.map(s=>{
                        return {
                            SurveyId: s.SurveyId,
                            SurveyGroupId: s.Id,
                            ParticipantEmployeeList: s.ParticipantEmployeeList.map(q=>{
                                return {
                                    EmployeeId: q.EmployeeId,
                                    QuestionAnswerData: q.QuestionAnswerData.map(j=> {
                                        delete j.Title
                                        return j
                                    })
                                }
                            })
                        }
                    })
                    self.disabledBtn = true;
                    SurveyApi.PersonSubmit(newPostData).then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        self.$refs.appDialogRef.createData();
                    })
                }
            })
        },
        // 查询 基本信息
        getDetail() {
            let self = this;
            self.loading = true
            SurveyApi.detail({
                Id: self.id,
                IsAnswer: true,
            }).then(res => {
                if(res&&res.SurveyGroupList.length>0){
                    self.activeName = res.SurveyGroupList[0].Id
                    res.SurveyGroupList.map(s=>{
                        if(s.ParticipantEmployeeList&&s.ParticipantEmployeeList.length>0){
                            s.ParticipantEmployeeList.map(q=>{
                                if(q.QuestionAnswerData.length==0) {
                                    q.QuestionAnswerData = JSON.parse(JSON.stringify(self.QuestionAnswerData))
                                }
                            })
                            s['activeEmployeeId'] = s.ParticipantEmployeeList[0].EmployeeId
                        }
                    })
                }
                self.formData = res;
                self.loading = false
            }).catch(err => {
                self.loading = false
            });
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 获取计算左侧人员选中的index
        setEmployeeIndex(arr,id){
            return arr.findIndex(s=>s.EmployeeId == id)
        },
        // 获取计算左侧人员头像前面的 是否填写完成的状态
        setCompletionStatus(arr=[]){
            if(arr.length>0) {
                return arr.every(s=>s.Value!=null)
            }
            return false
        },
    }
};
</script>

<style scoped>
.dialogMain >>> .el-dialog__body{
    padding: 0 !important;
}
/* .opr >>> .el-form-item__content,.opr >>> .el-form-item__label{
    font-size: 12px;
} */
.pageWarp >>> .el-tabs__header{
    margin: 0;
}
.pageWarp >>> .el-tabs__item{
    padding: 0 10px;
}
.pageWarp >>> .el-form-item__label{
    padding: 0;
}
.pageWarp_main >>> .el-tabs__item{
    padding: 0 10px 0 0!important;
    text-align: left;
}
.question >>> *{
    /* 解决单选按钮选中时  页面 部分区域会闪动的问题 */
    -webkit-backface-visibility: hidden;
}
</style>
<style lang="scss" scoped>
.el-dropdown-link {
    cursor: pointer;
    // color: #409EFF;
    margin-left: 10px;
    font-size: 12px;
}
.flexWarp{display: flex;}
.flexColumn{flex: 1;}
.pageWarp{
    height: 600px;
    display: flex;
    flex-direction: column;
    &_main{
        // display: flex;
        height: 560px;
        display: flex;
        overflow: hidden;
        .tabsBox{
            width: 180px;
            height: 100%;
            border-right: 1px solid #dcdfe6;
            overflow: hidden;
            overflow-y: auto;
            &_item{
                min-width: 120px;
                position: relative;
                background-color: #fff;
                padding: 8px 10px 8px 50px;
                height: 36px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                cursor: pointer;
                &_userPhoto{
                    position: absolute;
                    left: 15px;
                    top: 4px;
                    width: 26px;
                    height: 26px;
                    border-radius: 50%;
                    overflow: hidden;
                }
                &_icon{
                    position: absolute;
                    left: 2px;
                    top: 14px;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    overflow: hidden;
                    background-color: #F56C6C;
                    &.active{
                        background-color: #409EFF;
                    }
                }
                &.active{
                    color: #409EFF;
                    background-color: #ecf5ff;
                }
                &:hover{
                    background-color: #F5F7FA;
                }
            }
        }
        .opr{
            flex: 1;
            width: calc(100% - 180px);
        }
        .question{
            width: 100%;
            height: 560px;
            padding-left: 10px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            overflow-y: auto;
            &_title{
                font-weight: 700;
                padding-top: 15px;
                padding-bottom: 10px;
            }
            &_item{
                padding-top: 10px;
                border-bottom: 1px solid #dcdfe6;
                &_title{
                    padding-bottom: 10px;
                }
            }
        }
    }
}
</style>


