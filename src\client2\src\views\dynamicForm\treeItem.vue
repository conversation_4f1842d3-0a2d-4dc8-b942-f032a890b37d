<template>
    <div :class="{'is-container': isContainer && isBuildModel}">
        <div
            v-if="isBuildModel && (item.type == 1 || item.type == 2)"
            :class="{'container-title-wrap': isBuildModel}"
            >
            <div v-show="isBuildModel" class="container-title">
                {{ getCtrlName(item.type) }}
                <span v-if="isFolder" @click="toggle">[{{ isOpen ? '收起' : '展开' }}]</span>
            </div>
            <div>
                <span class="btn-del" v-show="item.level > 1" @click="handleControlRemove(item)">删除</span>
            </div>
        </div>

        <div v-show="isOpen" v-if="isBuildModel && !(item.type == 1)" style="padding: 10px; margin-bottom: 10px;" :class="{'is-not-container': !isContainer && isBuildModel}">
            <div class="container-title-wrap" v-show="item.type != 2">
                <div class="container-title">
                    {{ getCtrlName(item.type) }} {{ item.id }}
                </div>
                <div>
                    <span class="btn-del" @click="handleControlRemove(item)">删除</span>
                </div>
            </div>
            <div v-for="(i, idx) in item.attrs" :key="idx">
                <dynamic-ctrl v-if="i.type == 10002" :key="idx" :ctrlObj='item' :attr='i' v-model="i.extendConf"></dynamic-ctrl>
                <dynamic-ctrl v-else :key="idx" :ctrlObj='item' :attr='i' v-model="i.value"></dynamic-ctrl>
            </div>
        </div>

        <div v-show="isOpen" v-if="isFolder" :style="{padding: item.type == 1 && isBuildModel ? '10px' : '0px'}">
            <el-form v-if="item.type == 2" label-width="120px"  v-bind='attrsOfCtrl'>
                <tree-item
                    class="item"
                    v-for="(child, idx) in item.children"
                    :key="idx"
                    :item="child"
                    :parentItem='item'
                    @make-folder="$emit('make-folder', $event)"
                    @add-item="$emit('add-item', $event)"
                    :displayModel='displayModel'
                ></tree-item>
            </el-form>
            <tree-item
                v-else
                class="item"
                v-for="(child, idx) in item.children"
                :key="idx"
                :item="child"
                :parentItem='item'
                @make-folder="$emit('make-folder', $event)"
                @add-item="$emit('add-item', $event)"
                :displayModel='displayModel'
            ></tree-item>
        </div>
        
        <el-row v-show="item.type == 1 || item.type == 2" v-if="isBuildModel">
            <el-col :span="24">
                <el-dropdown @command="handleControlAdd">
                    <el-button type="primary">
                        添加部件<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item v-for="(c, idx) in controls" :command='{type: c.type, item}' :key="idx">{{ c.name }}</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import mixin from '../dynamicFormCommon/mixins'
import DynamicCtrl from './dynamicCtrl'
export default {
    name: 'tree-item',
    mixins: [mixin],    
    components: {
        DynamicCtrl,
    },
    props: {
        item: Object,
        parentItem: Object,
        displayModel: {
            type: String,
            default: 'build', //review
        }
    },
    // watch: {
    //     item: {
    //         handler(val, oldVal) { 
    //             // this.attrObj = this.initAttr(JSON.parse(JSON.stringify(val)))
    //             // this.attrObj = JSON.parse(JSON.stringify(val))
    //         },
    //         deep: true,
    //         immediate: true,
    //     },
    // },
    computed: {
        isFolder: function () {
            return this.item.children && this.item.children.length
        },
        attrsOfItem() {
            let currType = this.controls.find(c => c.type == this.item.type)
            if(currType){
                return currType.attrs
            }
            return []
        },
        isBuildModel() {
            return this.displayModel == 'build'
        },
        isContainer() {
            return this.item.type == 1 || this.item.type == 2
        },
        attrsOfCtrl() {
            return this.getAttrs(this.item)
        }
    },
    data: function () {
        return {
            isOpen: true,

        }
    },
    methods: {
        toggle: function () {
            if (this.isFolder) {
                this.isOpen = !this.isOpen
            }
        },
        handleControlAdd(obj) {
            obj.id = this.newGuid()

            this.$emit('add-item', obj)
        },
        handleControlRemove(item) {

            if(this.parentItem && this.parentItem.children) {
                let idx = this.parentItem.children.findIndex(s => s.id == item.id)
                this.parentItem.children.splice(idx, 1)
            }

        },

    }
}
</script>

<style scoped>
.container-title-wrap{
    background: #e3e3e3;
    height: 24px;
    line-height: 24px;
    display: flex;
}

.container-title{
    flex: 1;
}

.btn-del{
    display: inline-block;
    cursor: pointer;
}

.is-container{
    border: 1px solid blue;
}

.is-not-container{
    border: 1px solid red;
}

</style>