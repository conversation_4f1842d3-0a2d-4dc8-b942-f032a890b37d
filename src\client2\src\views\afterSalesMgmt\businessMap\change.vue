<template>
    <div>
        <app-dialog
            :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :width='1300'
            :maxHeight="680"
             
        >
            <template slot="body">
                <div class="wrapper bMBody" v-loading='loading'>
                    <div class="tagBox" v-if="dialogStatus == 'detail' && !isShowChangeDetail">
                        <tags :items='types' v-model="tagType" @change="handleTagsChange">
                            <template v-for="t in types" :slot="t.value">
                                {{ t.label }}
                            </template>
                        </tags>
                    </div>
                    <div v-show="tagType == 1" class="cl">
                        <div class="left left100">
                            <el-form
                            :rules="rules"
                            ref="formData"
                            :model="formData"
                            label-position="right"
                            label-width="130px">
                                <!-- <div class="firstDiv"><span>关联订单：</span>{{formData.OrderNumber ? formData.OrderNumber : '无'}}</div> -->
                                <!-- <div class="secondDiv" v-show="dialogStatus == 'create' || dialogStatus == 'approval'">
                                    <el-form-item label="变更原因" prop="ChangeReason" label-width="78px">
                                        <el-input
                                            :disabled='!editable'
                                          maxlength="2000"
                                          type="textarea"
                                          :rows="3"
                                          v-model="formData.ChangeReason"
                                        ></el-input>
                                    </el-form-item>
                                </div> -->
                                
                                <el-row class="sp-row">
                                    <el-col :span="12">
                                        <el-form-item label="关联订单:" prop="OrderNumber">
                                            <!-- <span>{{ formData.OrderNumber ? formData.OrderNumber : '无' }}</span> -->
                                            <el-button v-show="editable" type="text" @click="() => dialogAccessUsers = true">选择</el-button>
                                            <span v-if="formData.Order">{{ formData.Order.OrderNumber ? formData.Order.OrderNumber : (dialogStatus == 'create' ? '' : '无')}}</span>
                                            <!-- <span v-if="dialogStatus == 'detail' && !formData.Order.OrderNumber">无</span> -->
                                            <el-button v-show="formData.Order && formData.Order.OrderNumber && dialogStatus != 'detail'" style="padding:4px;" :disabled="!editable" icon="el-icon-close" circle @click="closeOrder"></el-button>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="合同编号:">
                                            <span>{{ formData.ContractNumber || '无' }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                
                                <el-form-item label="所在地区:" prop="areaName">
                                    <div class="_regional_detail_wrapper">
                                        <div class="btn_wrapper">
                                            <el-button :disabled="!editable" type="text" @click="handleDialog">选择</el-button>
                                        </div>
                                        <div class="regional_text" :title="formData.areaName">{{ formData.areaName }}</div>
                                        <div class="close_wrapper" v-show="formData.areaName && editable">
                                            <div class="i_wrapper">
                                                <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- <span>
                                        {{formData.areaName}}
                                        &nbsp;<el-button type="text" v-show="editable" @click="handleDialog()">选择</el-button>
                                        &nbsp;<i style="color:rgb(159 166 181);" v-show="editable">(提示：请先选中设备所在地区，下一步进行设备添加操作)</i>
                                    </span> -->
                                </el-form-item>  
                                <div>
                                    <div class="panel-title">设备信息</div>
                                    <el-row style="margin-bottom: 10px;">
                                        <span style="font-size: 14px; font-weight: bold;">
                                            加热炉信息
                                        </span>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="加热炉/锅炉" prop="Name">
                                                <el-input maxlength="10" :disabled='!editable' v-model="formData.Name"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="炉号" prop="HeatNumber">
                                                <el-input maxlength="20" :disabled='!editable' v-model="formData.HeatNumber"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="用途" prop="useId">
                                                 <el-select style="width:100%;" :disabled='!editable' filterable clearable v-model="formData.useId" placeholder="请选择" @change="handleUseChange">
                                                    <el-option
                                                    v-for="item in purposeOptions"
                                                    :key="item.Id"
                                                    :label="item.Name"
                                                    :value="item.Id">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="加热炉功率" prop="HeatFurnaceRatework">
                                                <el-input maxlength="20" :disabled="!editable" v-model="formData.HeatFurnaceRatework"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="结构类型" prop="StructureTypeName">
                                                <el-input v-model="formData.StructureTypeName" :disabled="!editable" maxlength="10" placeholder=""></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row style="margin-bottom: 10px; border-top: 1px solid #EBEEF5; padding-top: 10px;">
                                        <span style="font-size: 14px; font-weight: bold;">
                                            燃烧器信息
                                        </span>
                                    </el-row>
                                    
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="供风方式" prop="workId">
                                                 <el-select style="width:100%;" :disabled='!editable' filterable clearable v-model="formData.workId" placeholder="请选择" @change='handleWorkChange'>
                                                    <el-option
                                                    v-for="item in workOptions"
                                                    :key="item.Id"
                                                    :label="item.Name"
                                                    :value="item.Id">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="燃烧器型号" prop="BurnerModel">
                                                <el-input maxlength="100" :disabled='!editable' v-model="formData.BurnerModel"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="燃烧器功率" prop="BurnerRatework">
                                                <el-input maxlength="30" :disabled="!editable" v-model="formData.BurnerRatework"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="燃料类型" prop="FuelType">
                                                <el-input maxlength="20" :disabled="!editable" v-model="formData.FuelType"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="燃烧器压力" prop="BurnerPressure">
                                                <el-input maxlength="30" :disabled="!editable" v-model="formData.BurnerPressure"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="是否在保" prop="IsWarranty">
                                               <el-radio-group v-model="formData.IsWarranty"  @change="handlerChangeRadio">
						                        <el-radio :disabled='!editable' v-for="(wl,wlI) in warrantyList" :key="wlI" v-show="wl.value != 4"  :label="wl.value">{{wl.label}}</el-radio>
                                              </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="保修有效期至" prop="WarrantyTime">
                                                <el-date-picker style="width: 100%;" :disabled="(formData.IsWarranty != 2) || !editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.WarrantyTime" type="date"></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="生产厂家" prop="Manufacturer">
                                                <el-input maxlength="30" :disabled='!editable' v-model="formData.Manufacturer"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <!-- <el-col :span="12" v-if="dialogStatus != 'detail'">
                                            <el-form-item label="结构配件" prop="acceNum">
                                                <i>已添加({{formData.acceNum}})&emsp;</i>
                                              <el-button type="text" :disabled="!editable"  @click="handleAddStructural()">{{ formData.acceNum <= 0 ? '添加结构配件' : '编辑结构配件'}}</el-button>
                                            </el-form-item>
                                        </el-col> -->
                                        <el-col :span="8">
                                            <el-form-item label="投产时间" prop="InstallTime">
                                                <el-date-picker style="width:100%;" :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.InstallTime" type="date"></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="出产日期" prop="ProduceDate">
                                                <el-date-picker style="width: 100%;" :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.ProduceDate" type="date"></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="出产编号" prop="ProduceNumber">
                                                <el-input v-model="formData.ProduceNumber" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="电源参数" prop="PowerParams">
                                                <el-input v-model="formData.PowerParams" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="试验证书编号" prop="TestCertificateNo">
                                                <el-input v-model="formData.TestCertificateNo" :disabled="!editable" maxlength="30" placeholder=""></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    

                                    <el-row style="margin-bottom: 10px; border-top: 1px solid #EBEEF5; padding-top: 10px;">
                                        <span style="font-size: 14px; font-weight: bold;">
                                            安全连锁保护信息
                                        </span>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="炉管保护" prop="FurnaceProtect">
                                                <el-radio-group :disabled="!editable" v-model="formData.FurnaceProtect">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="漏液保护" prop="LeakProtect">
                                                <el-radio-group :disabled="!editable" v-model="formData.LeakProtect">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="炉况优化" prop="FurnacePracticeOptimize">
                                                <el-radio-group :disabled="!editable" v-model="formData.FurnacePracticeOptimize">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="温度连锁" prop="TemperatureLinkage">
                                                <el-select v-model="formData.TemperatureLinkage" filterable clearable :disabled="!editable" placeholder="请选择">
                                                    <el-option
                                                    v-for="item in tempTypes"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="燃气高压连锁" prop="GasHighPressureInterlocking">
                                                <el-radio-group :disabled="!editable" v-model="formData.GasHighPressureInterlocking">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="燃气低压连锁" prop="GasLowPressureInterlocking">
                                                <el-radio-group :disabled="!editable" v-model="formData.GasLowPressureInterlocking">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="炉体压力连锁" prop="FurnacePressureChain">
                                                <el-radio-group :disabled="!editable" v-model="formData.FurnacePressureChain">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="液位连锁" prop="LiquidLevelLinkage">
                                                <el-radio-group :disabled="!editable" v-model="formData.LiquidLevelLinkage">
                                                    <el-radio v-for="(wl,wlI) in bools" :key="wlI" :label="wl.value">{{wl.label}}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="控制系统PLC厂家" prop="ControlSystemPLCManufacturer">
                                                <el-input type="text" maxlength="100" :disabled="!editable" v-model="formData.ControlSystemPLCManufacturer" :title="formData.ControlSystemPLCManufacturer"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>


                                    <el-row style="margin-bottom: 10px; border-top: 1px solid #EBEEF5; padding-top: 10px;">
                                        <span style="font-size: 14px; font-weight: bold;">
                                            其他信息
                                        </span>
                                    </el-row>
                                    
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="设备现状/问题" prop="EquipmentStatusOrProblem">
                                                <el-input type="textarea" maxlength="100" :rows="3" :disabled="!editable" v-model="formData.EquipmentStatusOrProblem"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    
                                    <!-- <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="类型" prop="ProductListManagementId">
                                                <treeselect :normalizer="normalizer" key='type2'
                                                    v-model="formData.ProductListManagementId" :default-expand-level="3"
                                                    :options="typeTreedata" :multiple="false" placeholder='请选择' :show-count="true"
                                                    :noResultsText='noResultsTextOfSelTree'
                                                    :disabled="!editable"
                                                     :noOptionsText="noOptionsTextOfSelTree">
                                                </treeselect>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="安装时间" prop="InstallTime">
                                                 <el-date-picker style="width: 100%;" :disabled='!editable' format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.InstallTime" type="date"></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="是否在保" prop="IsWarranty">
                                               <el-radio-group v-model="formData.IsWarranty"  @change="handlerChangeRadio">
                                                <el-radio :disabled='!editable' :label="true">是</el-radio>
                                                <el-radio :disabled='!editable' :label="false">否</el-radio>
                                              </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="保修有效期至" prop="WarrantyTime">
                                                <el-date-picker style="width: 100%;" :disabled="!formData.IsWarranty || !editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.WarrantyTime" type="date"></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="安装地区" prop="RegionalId">
                                                <treeselect :normalizer="normalizer2" key='type1'
                                                    v-model="formData.RegionalId" :default-expand-level="3"
                                                    :options="treedata" :multiple="false" placeholder='' :show-count="true"
                                                    :disabled="!editable"
                                                    :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
                                                </treeselect>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="24">
                                            <el-form-item label="备注" prop="Remark">
                                                <el-input maxlength="30" :disabled='!editable' type="textarea" :rows="4" placeholder="请输入内容" v-model="formData.Remark"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row> -->
                                </div>
                                <!-- <div>
                                    <div class="panel-title">审批</div>
                                    <div>
                                        <approval-panel v-if="dialogStatus == 'create' || dialogStatus == 'update'" ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                                        <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                                    </div>
                                </div> -->
                            </el-form>
                        </div>
                        <div class="right">
                            <!-- 默认值create，其实是编辑 -->
                            <div class="panel-title">附件</div>
                            <el-row style="margin-bottom: 60px;">
                                <el-col>
                                    <app-uploader
                                        ref="appUploaderRef"
                                        :readonly="!editable"
                                        accept="all"
                                        :fileType="3"
                                        :max="10000"
                                        :value="formData.AttachmentList"
                                        :fileSize="1024 * 1024 * 500"
                                        :minFileSize="100 * 1024"
                                        @change="handleFilesUpChange2"
                                    ></app-uploader>
                                </el-col>
                            </el-row>
                            <div v-if="dialogStatus == 'detail'">
                                <!-- <div class="panel-title">配置参数</div>
                                <v-config-page-body ref="vcpb" :configChange='configChange' :dialogStatus='dialogStatus' @getConfigList='getConfigList' :ids="ids"></v-config-page-body> -->
                                <div v-if="isShowHistories">
                                    <div class="panel-title">编辑历史记录</div>
                                    <ul class="historyUl" v-if="historyData && historyData.length>0">
                                        <li class="cl" v-for="(item,index) in historyData" :key="index">
                                            <span class="fl">{{index+1}}、</span>&nbsp;
                                            <span class="fl">{{item.CreateEmployee.Name}}&nbsp;</span>
                                            <span class="fl">{{item.CreateTime | dateFilter('YYYY-MM-DD HH:mm')}}</span>
                                            <el-button class="fr" type="text" @click="handleChangeDialog(item)">查看详情</el-button>
                                        </li>
                                    </ul>
                                    <no-data v-else></no-data>
                                </div>
                                <no-data v-else></no-data>
                            </div>
                        </div>
                    </div>
                    <div v-show="tagType == 2">
                        <!-- <div class="left left2">
                            <app-table-core
                              ref="mainTable"
                              :tab-columns="tabColumns"
                              :tab-datas="tableDatas"
                              :tab-auth-columns="[]"
                              :isShowAllColumn="true"
                              :isShowOpatColumn="false"
                              :startOfTable="0"
                              :multable='false'
                            >
                            </app-table-core>
                            <pagination
                                v-show="total>PageSize && tagType == 2"
                                :total="total"
                                :page.sync="PageIndex"
                                :size.sync="PageSize"
                                layout="total, prev, pager, next, jumper"
                                @pagination="handleCurrentChange"
                                @size-change="handleSizeChange"
                                />
                        </div> -->
                        <div class="right right2 right3" v-loading="loadingRight">
                            <div>
                                <!-- <div class="panel-title">配件更换记录({{replaceTotal}})</div> -->
                                <ul v-if="replaceRecord.length>0">
                                    <li v-for="(rr,i2) in replaceRecord" style="margin-top: 6px;">
                                        <!-- <div>{{rr.CommentContent[2].Content}}</div> -->
                                        <div style="margin-bottom: 8px;">
                                            <span>{{rr.CommentContent[2].Content}}</span>
                                            <span>{{rr.CommentContent[0].Content}}</span>
                                            <span style="color:#409EFF;">{{rr.CommentContent[1].Content}}</span>
                                            <!-- <span v-if="rr.CommentContent[0].Content == '更换'">&emsp;<i style="color:#409EFF;">{{rr.CommentContent[3].Content}}</i></span> -->
                                            <span>使用数量：<i style="color:#409EFF;">{{rr.CommentContent[3] ? rr.CommentContent[3].Content : 0}}</i></span>
                                        </div>
                                    </li>
                                </ul>
                                <no-data v-else></no-data>
                            </div>
                        </div>
                    </div>
                    <div v-show="tagType == 3" class="tag3">
                        <app-table-core
                            ref="mainTable"
                            :tab-columns="tabColumns1"
                            :tab-datas="repairList"
                            :tab-auth-columns="[]"
                            :isShowAllColumn="true"
                            :isShowOpatColumn="false"
                            :startOfTable="startOfTable"
                            :multable='false'
                            :height='589'
                        >
                           <template slot='ReportTime' slot-scope="scope">
                                    {{scope.row.ReportTime | dateFilter('YYYY-MM-DD HH:mm')}}
                                </template>
                                <template slot='ReportEmployee' slot-scope="scope">
                                    {{scope.row.ReportEmployee ? scope.row.ReportEmployee.Name : ''}}
                                </template>
                                <template slot='operation' slot-scope="scope">
                                    <el-button type="text" @click="viewRepairDetail(scope.row)">查看报修单</el-button>
                                </template>
                        </app-table-core>
                    </div>
                    <pagination
                      v-show="tagType == 3"
                      :total="total"
                      :page.sync="PageIndex"
                      :size.sync="PageSize"
                      layout="total, prev, pager, next, jumper"
                      @pagination="handleCurrentChange"
                      @size-change="handleSizeChange"
                    />
                </div>

            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2' v-show="dialogStatus != 'detail'"></app-button>
                <el-button @click="handleClose" v-show="dialogStatus == 'detail'">关闭</el-button>
                <!-- 确认 -->
                <app-button v-if="editable" @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
<!--                 <el-button v-if="dialogStatus == 'detail'">关闭</el-button> -->
                <el-button @click="handleApproval"  type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval'">审批</el-button>
            </template>
        </app-dialog>
        <change-detail
        v-if="currentRow"
        @closeDialog='closeChangeDialog'
        :dialogFormVisible='dialogChangeFormVisible'
        :dialogStatus="dialogDetailStatus"
        :isShowHistories="false"
        :isShowChangeDetail="true"
        :id='currentRow.Id'>
        </change-detail>
        <!-- 创建 -->
        <v-create-page
            v-if="dialogCPFormVisible"
            @closeDialog="closeCPDialog"
            @saveSuccess="handleCPSaveSuccess"
            :dialogFormVisible="dialogCPFormVisible"
            :dialogStatus="dialogCPStatus"
            :id="maintenId"
        ></v-create-page>
        <!-- 关联订单 -->
        <order-selector
            :isShow='dialogAccessUsers'
            :checkedList='formData.Order ? [formData.Order] : []'
            @changed='handleChangeUsers'
            @closed='() => dialogAccessUsers = false'
            :multiple='false'
        >
        </order-selector>
        <!-- 选择地区 -->
        <v-area-choose
        v-if="dialogAreaFormVisible"
          @closeDialog="closeAreaDialog"
          @electedRegionalData="electedRegionalData"
          :dialogFormVisible="dialogAreaFormVisible"
          :checkedList="formData.areaId ? [formData.areaId] : []"
          :disabledFn="disabledFn"
        ></v-area-choose>
        <!-- 结构配件清单 -->
        <!-- <v-structural
            @closeDialog="closeDialogStructural"
            :dialogFormVisible="dialogStructuralVisible"
            :id="radioId"
            :allAcceData='allAcceData'
            @saveSuccess="handleAcceSuccess">
        </v-structural> -->
    </div>
</template>

<script>
// import vCreatePage from "../maintenCenter/maintenOrderMgmt/create";
import * as equUse from "@/api/equipmentUse";
import * as equMode from "@/api/equipmentWorkMode";
import * as accessories from "@/api/afterSalesMgmt/accessories";
import * as businessMap from "@/api/businessMap";
import * as productListManagement from '@/api/systemManagement/productListManagement';
import * as systemManagement from "@/api/systemManagement/regionalManagement";
import vConfigPageBody from './common/configPageBody';
import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel';
import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail';
import { listToTreeSelect } from '@/utils';
import * as change from '@/api/projectDev/projectMgmt/change';
import { vars } from '../../salesMgmt/common/vars' ;
import NoData from "@/views/common/components/noData";
import orderSelector from '../../common/orderSelector';
import vAreaChoose from "./common/areaChoose";
import vStructural from "./structural";
  export default {
    name: "businessMapChange",
    
    props: {
        specialPageTitle: {
            type: String
        },
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: 'create'
        },
        //变更详情id（查看变更详情时必须）
        id: {
            type: String,
            default: ''
        },
        approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
            type: String,
            default: ''
        },
        isShowChangeDetail:{
            type: Boolean,
            default: false
        },
        isShowHistories:{
            type: Boolean,
            default: false
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },        
        // 设备变更里的部件更换记录和报修记录在我已审批中调用接口id和其它地方不同
        isChangeId:{
            type: Boolean,
            default: false
        }
    },
    computed: {
        
    },
    watch: {
        '$attrs.dialogFormVisible':{
            handler(val) {
                if(val){
                    // this.getPurposeWork();
                    this.loading=true;
                    let p1=equUse.getList({"pageIndex": 1,"pageSize": 10000});
                    let p2=equMode.getList({"pageIndex": 1,"pageSize": 10000});
                    Promise.all([p1, p2]).then((result) => {

                        this.loading=false;
                        this.purposeOptions=result[0].Items;
                        this.workOptions=result[1].Items;
                        this.changeId='';
                        this.tagType=1;
                        this.PageIndex=1;
                        if(this.dialogStatus == 'approval' || (this.approvalId && this.dialogStatus == 'detail') || this.isShowChangeDetail) {
                            this.getChangeDetail(this.id)
                        }else{
                            this.getDetail()
                        }
                        if(this.dialogStatus == 'detail'){
                            this.getHistory();
                        }
                        if(this.dialogStatus == 'detail' && !this.isShowChangeDetail && !this.isChangeId){
                            this.getPartSpecificationData();
                            this.getRepairList();
                        }
                    }).catch((error) => {
                        this.loading=false;
                    })
                }
            },
            immediate: true
        }
    },
    computed: {
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            if(this.dialogStatus == 'create') {
                return '编辑设备'
            }else if(this.dialogStatus == 'detail'){
                if(this.isShowChangeDetail){
                    return '编辑详情'
                }else{
                    return '详情'
                }
            }else if(this.dialogStatus == 'approval'){
                return '变更审批'
            }
        },
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != 'approval' && this.dialogStatus != 'detail'
        },
        configChange(){
            return this.dialogStatus == 'approval'
        },
        startOfTable(){
            return (this.PageIndex - 1) * this.PageSize
        },
    },
    filters: {

    },
    
    data() {
        return {
            bools: [
                {value: true, label: '有'},
                {value: false, label: '无'},
            ],
            tempTypes: [
                {value: 1, label: '出口温度'},
                {value: 2, label: '炉体温度'},
            ],
            warrantyList:vars.business.warranty,
            allAcceData:[],
            radioId:'',
            dialogStructuralVisible:false,
            purposeOptions:[],
            workOptions:[],
            dialogAreaFormVisible:false,
            dialogAccessUsers: false,
            changeId:'',
            replaceRecord:[],
            replaceTotal:0,
            loadingRight:false,
            dialogCPStatus:'detail',
            dialogCPFormVisible:false,
            maintenId:'',
            total:0,
            saveTotal1:0,
            saveTotal2:0,
            PageSize:20,
            PageIndex:1,
            tabColumns: [
                {
                  attr: { prop: "StructPartName", label: "配件名称" }
                },
                {
                  attr: { prop: "SpecificationModel", label: "规格型号" },
                },
                {
                  attr: { prop: "SupplierName", label: "供应商" },
                  
                },
              ],
              tabColumns1:[
                  {
                  attr: { prop: "Code", label: "报修单号" }
                },
                {
                  attr: { prop: "ReportTime", label: "报修时间" },
                  slot:true
                },
                {
                  attr: { prop: "ReportEmployee", label: "记录人" },
                  slot:true
                },
                {
                  attr: { prop: "operation", label: "操作" },
                  slot:true
                },
              ],
              tableDatas:[],
              repairList:[],
            tagType:1,
            types: [{value: 1, label: '设备基本信息'}, {value: 2, label: '部件更换记录(0)'}, {value: 3, label: '维修记录(0)'}],
            dialogChangeFormVisible:false,
            currentRow:null,
            dialogDetailStatus:'detail',
            ids:[],
            listData:[],
            listChildData:[],
            radio:'',
            loading: false,
            disabledBtn: false,
            rules: {
                // ChangeReason: [{ required: true, message: '变更原因不能为空'}],
                Name:[{ required: true, message: '加热炉/锅炉不能为空'}],
                HeatNumber:[{ required: true, message: '炉号不能为空'}],
                areaName: [{ required: true, message: '所在地区不能为空', trigger: 'change'}],
                // ProductListManagementId: [{ required: true, message: '请选择类型', trigger: 'change'}],
                // InstallTime: [{ required: true, message: '请选择安装时间', trigger: 'change'}],
                // IsWarranty: [{ required: true, message: '请选择是否在保', trigger: 'change'}],
                WarrantyTime: [{ required: false, message: '请选择保修截止时间', trigger: 'change'}],
                // RegionalId: [{ required: true, message: '请选择安装地区', trigger: 'change'}],
            },
            formData: {
                Order:{
                    OrderNumber:'',
                },
                Order:null,
                HeatNumber:'',
                useId:'',
                EquipmentUseId:'',
                workId:'',
                EquipmentWorkModeId:'',
                BurnerModel:'',
                Manufacturer:'',
                areaName:'',
                acceNum:0,
                acceData:null,
                areaId:'',
                ChangeReason:'',
                Approval: {
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                },
                StructureTypeName: '',
                BurnerPressure: '',
                ProduceDate: '',
                ProduceNumber: '',
                PowerParams: '',
                TestCertificateNo: '',
                FurnaceProtect: false,
                LeakProtect: false,
                FurnacePracticeOptimize: false,
                TemperatureLinkage: 1,
                GasHighPressureInterlocking: false,
                GasLowPressureInterlocking: false,
                FurnacePressureChain: false,
                LiquidLevelLinkage: false,
                ControlSystemPLCManufacturer: '',
                ContractNumber: '',
                AttachmentList: [],
                Code: "",
                CreateTime: "",
                RegionalId: "",
                EquipmentSettingIdList:[],
                EquipmentSettingIds:[],
                Id: "",
                InstallTime: "",
                EquipmentStatusOrProblem: "",
                FuelType: "",
                HeatFurnaceRatework: "",
                BurnerRatework: "",
                IsWarranty: '',
                Name: "",
                OrderEquipmentId: "",
                OrderId: "",
                OrderNumber: "",
                OwnerEmployeeList: [],
                ParticipantEmployeeList: [],
                ProductListManagementId: "",
                Remark: "",
                WarrantyTime: null,
                //里程碑详情
                ContractHistoryEditRequestModel: {
                    Id: '', //项目ID
                    ClientUnits: "", //客户单位
                    ContractNumber: "", //合同编号
                    Order: null, //订单ID
                    Employee: [], //业务员
                    SignedTime: "", //签约时间
                    ContractStartTime: '', //合同生效时间
                    ContractEndTime: "", //合同到期时间
                    Contacts: "", //对方联系人
                    ContactsPhone: "", //联系电话
                    ClauseRemarks: "", //条款描述
                    Remark: "", //备注
                    AttachmentList: [],// 项目附件ID列表
                },
            
            },
            normalizer(node) {
                // treeselect定义字段
                return {
                    id: node.Id,
                    label: node.ProductName,
                    children: node.children
                }
            },
            typeTreedata: [],
            normalizer2(node) {
                // treeselect定义字段
                return {
                    id: node.Id,
                    label: node.RegionalName,
                    children: node.children
                }
            },
            treedata:[],
            historyData:[]
        };
    },
    created() {
        // this.rules = this.initRules(this.rules)
        // this.getRegionals();
        // this.getAreas();
    },
    mounted(){
        
    },
    methods: {
        handleUseChange(val){
            this.formData.EquipmentUseId=val;
        },
        handleWorkChange(val){
            this.formData.EquipmentWorkModeId=val;
        },
        disabledFn(data, nodeType) {
            //禁选一级节点
            if(data.level <= 1) {
                return true
            }
            return false
        },
        closeOrder(){
            this.formData.ContractNumber = ''
            this.formData.Order=null;
            this.formData.OrderNumber="";
            this.formData.OrderId='';
        },
        getPurposeWork(){
            equUse.getList({"pageIndex": 1,"pageSize": 10000}).then(res => {
                this.purposeOptions=res.Items;
            })
            equMode.getList({"pageIndex": 1,"pageSize": 10000}).then(res => {
                this.workOptions=res.Items;
            })
        },
        handleAcceSuccess(d){
            this.formData.PartSpecificationList=d;
            this.formData.acceNum=d.length;
            this.closeDialogStructural();
        },
        closeDialogStructural(){
            this.dialogStructuralVisible=false;
        },
        handleAddStructural(){
            this.allAcceData=this.formData.PartSpecificationList ? this.formData.PartSpecificationList : [];
            this.dialogStructuralVisible=true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('areaName');
            if(data){
                this.formData.areaId=data.Id;
                this.formData.areaName=data.ParentName;
                this.formData.RegionalName=data.ParentName;
                this.formData.RegionalId=data.Id;
            }else{
                this.formData.areaId='';
                this.formData.areaName='';
                this.formData.RegionalName='';
                this.formData.RegionalId='';
            }
        },
        closeAreaDialog() {
          this.dialogAreaFormVisible = false;
        },
        handleDialog(){
            this.dialogAreaFormVisible=true;
        },
        handleChangeUsers(orders) {
            if(orders && orders.length > 0) {
                // this.$refs.formData.clearValidate('Order.OrderNumber');
                this.formData.ContractNumber = orders[0].ContractNumber
                this.formData.Order = orders[0];
                this.formData.OrderNumber=orders[0].OrderNumber;
                this.formData.OrderId=orders[0].Id;
                this.formData.Order.OrderAmount=0;
            }else{
                this.formData.ContractNumber = ''
                this.formData.Order = []
            }
            this.dialogAccessUsers = false
        },
        closeCPDialog() {
          this.dialogCPFormVisible = false;
        },
        handleCPSaveSuccess(_formData) {
          this.closeCPDialog();
        },
        viewRepairDetail(d){
          // 弹出编辑框
          this.maintenId = d.MaintenanceId;
          this.dialogCPFormVisible = true;
        },
        handleCurrentChange(val) {
          this.PageIndex = val.page;
          this.PageSize = val.size;
          this.getRepairList();
        },
        handleSizeChange(val) {
          this.PageSize = val.size;
          this.getRepairList();
        },
        handleTagsChange(d){
            this.PageIndex=1;
        //   if(d == 2){
        //       this.total=this.saveTotal1;
        //       this.getAllCommentData();
        //   }else 
          if(d == 3){
            this.total=this.saveTotal2;
          }
        },
        getAllCommentData(){
            this.loadingRight = true
            let params={
                "currentBusinessId": null,
                  "currentParentBusinessId": this.id,
                  "type": 24
            }
            accessories.getAllComment(params).then(res => {
                // this.total=res.Total;
                this.replaceRecord=[];
                if(res.length>0){
                    this.replaceTotal=res.length;
                    res.forEach(v => {
                        v.CommentContent=JSON.parse(v.CommentContent);
                    })
                    this.replaceRecord=res;
                }else{
                    this.replaceTotal=0;
                }

                this.loadingRight = false
            }).catch(err => {
                this.loadingRight = false
            })
        },
        getRepairList(){
            this.loading = true
            let id='';
            if(this.isChangeId){
                id=this.changeId;
            }else{
                id=this.id;
            }
            let params={
                "PageIndex": this.PageIndex,
                "PageSize":this.PageSize,
                "Id": id
            }
            businessMap.getEquipmentRepairRecordPageAsync(params).then(res => {
                // this.total=res.Total;
                this.repairList=res.Items;
                this.saveTotal2=res.Total;
                this.types[2].label='报修记录('+res.Total+')';
                this.loading = false
            }).catch(err => {
                this.loading = false
            })
        },
        getPartSpecificationData(){
            // let id='';
            // if(this.isChangeId){
            //     id=this.changeId;
            // }else{
            //     id=this.id;
            // }
            // this.loading=true;
            // let postData={
            //     "pageIndex": this.PageIndex,
            //     "pageSize": this.PageSize,
            //     "businessId": id,
            //     "IsDelete" : null
            // }
            // accessories.getBusinessRelationPage(postData).then(res => {
            //     this.loading=false;
            //     this.saveTotal1=res.Total;
            //     this.types[1].label='部件更换记录('+res.Total+')';
            //     this.tableDatas=res.Items;
            // }).catch(err => {
            //     this.loading=false;
            // })
            this.loadingRight = true
            let params={
                "currentBusinessId": null,
                  "currentParentBusinessId": this.id,
                  "type": 24
            }
            accessories.getAllComment(params).then(res => {
                // this.total=res.Total;
                
                this.replaceRecord=[];
                if(res.length>0){
                    this.types[1].label='部件更换记录('+res.length+')';
                    res.forEach(v => {
                        v.CommentContent=JSON.parse(v.CommentContent);
                    })
                    this.replaceRecord=res;
                }else{
                    this.types[1].label='部件更换记录(0)';
                }

                this.loadingRight = false
            }).catch(err => {
                this.loadingRight = false
            })
        },
        /**
           * 变更详情
           */
          closeChangeDialog() {
              this.dialogChangeFormVisible = false
          },
          handleChangeDialog(row) {
              this.currentRow = row
              this.dialogChangeFormVisible = true
          },
        getHistory(){
            businessMap.getChangeHistoryAsync({id: this.id}).then(res => {

                this.historyData=res;
            })
        },
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
        
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.disabledBtn = true
                        //项目创建审批
                        change.createApproval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })
                }
            })
        },
        getChangeDetail(id){
            let result=null;
            this.loading = true
            if(this.isShowChangeDetail){
                result=businessMap.getHistoryDetails({id:id});
            }else{
                let isApprovaled = this.dialogStatus == 'detail' ? true : false;
                result=change.detail({id:id,approvalId: this.approvalId,isApprovaled});
            }

            result.then(res => {

                this.loading = false
                this.changeId=res.ChangeDetailsResponseModel.RelationId;
                this.ids=res.ChangeOrderEquipmentModel.EquipmentSettingIdList;
                this.rules.WarrantyTime[0].required=(res.ChangeOrderEquipmentModel.IsWarranty == 2 ? true :false);
                // this.formData.ChangeReason=res.ChangeDetailsResponseModel.ChangeReason;
                this.formData = Object.assign({}, this.formData, res.ChangeOrderEquipmentModel)
                this.formData=Object.assign({}, this.formData, res.ChangeDetailsResponseModel)
                this.formData.Order={
                    OrderNumber:res.ChangeOrderEquipmentModel.OrderNumber,
                    OrderId:res.ChangeOrderEquipmentModel.OrderId
                }
                this.formData.OrderNumber=res.ChangeOrderEquipmentModel.OrderNumber;
                this.formData.areaName=res.ChangeOrderEquipmentModel.RegionalName;
                this.formData.areaId = res.ChangeOrderEquipmentModel.RegionalId;

                if(this.dialogStatus == 'detail' && !this.isShowChangeDetail){
                    this.getPartSpecificationData();
                    this.getRepairList();
                }
            }).catch(err => {
                this.loading = false
            })
        },
        getRegionals() {
            productListManagement.getListByCondition({}).then(res => {
                this.typeTreedata = listToTreeSelect(res);
            })
        },
        getAreas() {
            systemManagement.getListByCondition({}).then(res => {
                this.treedata = listToTreeSelect(res)
            })
        },
        getConfigList(r,ld,lcd){
            this.radio=r;
            this.listData=ld;
            this.listChildData=lcd;
            this.saveIds();
        },
        saveIds(){
            this.formData.EquipmentSettingIdList=[];
            this.formData.EquipmentSettingIdList.push(this.radio);
            this.listChildData.forEach(v => {
                if(v.value.length>0){
                    this.formData.EquipmentSettingIdList.push(v.Id);
                    this.formData.EquipmentSettingIdList.push(v.value);
                }
            })
        },
        handlerChangeRadio(d){
            if(d != 2){
                this.formData.WarrantyTime='';
                this.rules.WarrantyTime[0].required=false;
                this.$refs['WarrantyTime'].clearValidate();
            }else{
                this.rules.WarrantyTime[0].required=true;
            }
        },
        createData() {
            this.disabledBtn=true;
            //如果是否保修不是选的是则保修时间不警告提示
            let listResult = this.$refs.formData.validate()
            // let approvalPanelValidate = this.$refs.approvalPanel.validate()
            // this.$refs.vcpb.handlerGetCofigData();
            Promise.all([listResult]).then(valid => {
                // this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块

                //提交数据保存
                // this.formData.Approval.ApprovalEmployeeIdList = this.formData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                // this.formData.Approval.CCEmployeeIdList = this.formData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                
                let postDatas = JSON.parse(JSON.stringify(this.formData))
                postDatas.AttachmentIdList = postDatas.AttachmentList && postDatas.AttachmentList.map(s => s.Id)
                delete postDatas.AttachmentList

                businessMap.initiateChangeAsync(postDatas).then(res => {
                    this.$notify({
                      title: '成功',
                      message: '变更成功！',
                      type: 'success'
                    });
                    this.disabledBtn=false;
                    this.$emit('saveSuccess');
                }).catch(err => {
                    this.disabledBtn=false;
                })
            }).catch(e => {
                this.disabledBtn=false;
            })
        },
        getDetail() {
            this.loading = true
            this.formData.ChangeReason='';
            this.formData.acceNum=0;
            businessMap.detail({id:this.id}).then(res => {
                this.loading=false;
                // this.ids=res.EquipmentSettingIdList;
                // this.rules.WarrantyTime[0].required=res.IsWarranty;
                this.formData = Object.assign({}, this.formData, res)
                if(res.OrderNumber){
                    this.formData.Order={
                        Code:res.OrderNumber,
                        Id:res.OrderId,
                        OrderNumber:res.OrderNumber,
                        OrderAmount:0,
                    }
                }else{
                    this.formData.Order={
                        OrderNumber:''
                    };
                }
                
                let a=this.purposeOptions.find(v => v.value == this.formData.EquipmentUseId);
                if(a) this.formData.useId = this.formData.EquipmentUseId;
                else this.formData.useId = this.formData.EquipmentUseName;
                a=this.workOptions.find(v => v.value == this.formData.EquipmentWorkModeId);
                if(a) this.formData.workId = this.formData.EquipmentWorkModeId;
                else this.formData.workId = this.formData.EquipmentWorkModeName;
                if(res.PartSpecificationList){
                    res.PartSpecificationList.forEach(v => {
                        v.Name=v.StructPartName;
                    })
                    this.formData.acceNum+=res.PartSpecificationList.length;
                }
                this.formData.areaName=res.RegionalName;
                this.formData.areaId = res.RegionalId
                if(this.formData.IsWarranty == 2){
                    this.rules.WarrantyTime[0].required=true;
                }else{
                    this.rules.WarrantyTime[0].required=false;
                }
                // if(!this.isShowHistories && this.dialogStatus == 'create'){
                //     this.formData.Approval={
                //         ApprovalEmployeeList: [[]],
                //         ApprovalType: 1,
                //         ApprovalOperatorEmployeeList: [], //已审批人员
                //         NoApprovalEmployeeList: [], //未审批人员
                //         CCEmployeeList: [], //抄送人
                //         ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //         ApprovalState: 1, //1: 进行中; 2: 已完成
                //         ApprovalResult: 1, //1: 通过； 2：不通过
                //     }
                // }
            }).catch(e => {
                this.loading=false;
            })
            // let isApprovaled = this.dialogStatus == 'detail' ? true : false

            // change.detail({id: this.id,approvalId: this.approvalId,  isApprovaled}).then(res => {
            //     this.loading = false
            //     this.formData = Object.assign({}, this.formData, res)

            //     let temp = {
            //         ChangeEditRequestModel: this.formData.ChangeDetailsResponseModel
            //     }
                
            //     this.formData.ContractHistoryEditRequestModel.Employee = [this.formData.ContractHistoryEditRequestModel.Employee]

            //     this.commonChangeDetail = JSON.parse(JSON.stringify(temp))
            //     delete this.formData.ChangeDetailsResponseModel
            // }).catch(err => {
            //     this.loading = false
            // })
        },
        handleHistoryClose(){
            this.getDetail();
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        handleFilesUpChange2(files) {
            this.formData.AttachmentList = files
        },
        handleFilesUpChange(files) {
            this.formData.ContractHistoryEditRequestModel.AttachmentList = files;
        },
    },
    components: {
        vCreatePage:() => import('../maintenCenter/maintenOrderMgmt/create'),
        vConfigPageBody,
        approvalPanel,
        approvalDetail,
        NoData,
        changeDetail: () => import('./change'),
        orderSelector,
        vAreaChoose,
        vStructural
    },
};
</script>

<style scoped>
.sp-row >>> .el-form-item{
    margin-bottom: 0;
}
</style>

<style lang="scss" scoped>
.bMBody{
    padding:0 10px;
}
.tag3{
    height:589px;
}
.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding:5px 0;
}
.firstDiv{
    padding-left:6px;
    padding-bottom:10px;
    border-bottom:1px solid #DCDFE6;
    margin-bottom:10px;
    >span{
        font-weight: 700;
    }
}
.wrapper {
    .left, .right {
        display: inline-block;
    }
    .left {
        width: calc(66% - 2px)!important;
        float: left;
        height:610px;
        overflow-y: auto;
        padding-right:6px;
    }
    .left100{
        width: 100%;
        height:640px;
        overflow-y: auto;
    }
    .left2{
        width:66%!important;
        height:597px;
    }
    .right{
        width: 34%!important;
        float: right;
        height:640px;
        padding-left:10px;
        overflow-y: auto;
        border-left:1px solid #DCDFE6;
        padding-top: 14px;
        overflow-y: auto;
    }
    .right:nth-child(2){
        height:640px;
    }
    .right2{
        height:640px;
    }
    .right3{
        width:100%!important;
        border-left:0;
        padding-top:0;
    }
}


.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 0;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}
.historyUl{
    li{
        height: 30px;
        line-height: 30px;
        border-bottom: 1px solid #DCDFE6;
        padding-left: 4px;
    }
}
</style>