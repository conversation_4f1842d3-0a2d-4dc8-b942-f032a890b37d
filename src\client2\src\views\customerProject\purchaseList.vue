<template>
    <div>
        <el-dialog
            v-el-drag-dialog
            class="dialog-mini"
            width="1000px"
            :title="'采购预测'"
            :visible.sync="dialogVisible"
            :close-on-click-modal='false'
            :append-to-body='true'
            >
            <app-table ref="mainTable" :tab-columns="tabColumns"
                :tab-datas="tabDatas"
                :isShowConditionArea='false'
                :multable='false'
                :isShowAllColumn='true' :loading="listLoading" @rowSelectionChanged="rowSelectionChanged"
                :startOfTable='startOfTable'
                :isShowBtnsArea="editablePurchase"
                >
                <template slot='PurchaseItem' slot-scope="scope">
                    {{ scope.row.PurchaseItem }}
                </template>
                <template slot='Quantity' slot-scope="scope">
                    {{ scope.row.Quantity }}
                </template>
                <template slot='EstimatedPurchasePrice' slot-scope="scope">
                    {{ scope.row.EstimatedPurchasePrice }}
                </template>
                <template slot='ProcurementInPreviousYears' slot-scope="scope">
                    {{ scope.row.ProcurementInPreviousYears }}
                </template>
                <template slot='MainConcern' slot-scope="scope">
                    {{ scope.row.MainConcern }}
                </template>
                <template slot='Remark' slot-scope="scope">
                    {{ scope.row.Remark }}
                </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                    <el-button type="primary" @click="handleAdd">新增</el-button>
                </template>


                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <app-table-row-button v-show="editablePurchase" @click="handleUpdate(scope.row)" :type="1">
                    </app-table-row-button>
                    <app-table-row-button @click="handleUpdate(scope.row, 'detail')"
                    :type="2"></app-table-row-button>
                    <app-table-row-button v-show="editablePurchase" @click="handleDelete(scope.row)" :type="3">
                    </app-table-row-button>
                </template>
            </app-table>
            <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex"
            :size.sync="listQuery.PageSize" @pagination="handleCurrentChange"
            @size-change="handleSizeChange" />
            
            <div slot="footer">
                <slot name="other-button-area"></slot>
                <el-button size="mini" @click="handleClose">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog
            v-el-drag-dialog
            class="dialog-mini"
            width="800px"
            :title="'采购预测'"
            :visible.sync="dialogOfViste"
            :close-on-click-modal='false'
            :append-to-body='true'
            >
            <el-form
                :rules="rules"
                ref="dataForm"
                :model="temp"
                label-position="right"
                label-width="140px"
                v-if="dialogOfViste"
                style="max-height: 600px; overflow-y: auto;"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'采购项'" prop="PurchaseItem">
                            <el-input :disabled="editable" v-model="temp.PurchaseItem"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'数量'" prop="Quantity">
                            <el-input-number v-model="temp.Quantity" :min="1" :max="99999" :disabled="editable"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'预计采购价格(元)'" prop="EstimatedPurchasePrice">
                            <div class="el-input el-input--mini" :class="{'is-disabled': editable}">
                                <masked-input
                                    type="text"
                                    name="EstimatedPurchasePrice"
                                    class="el-input__inner"
                                    v-model="temp.EstimatedPurchasePrice"
                                    :mask="numberMask"
                                    :guide="false"
                                    :disabled="editable"
                                    placeholderChar="#">
                                </masked-input>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item :label="'往年采购情况'" prop="ProcurementInPreviousYears">
                            <el-input type="textarea" :maxlength="500" :disabled="editable" :rows="4" placeholder="" v-model="temp.ProcurementInPreviousYears"></el-input>
                        </el-form-item>
                    </el-col> 
                    <el-col :span="24">
                        <el-form-item :label="'主要关注事项'" prop="MainConcern">
                            <el-input type="textarea" :maxlength="500" :disabled="editable" :rows="4" placeholder="" v-model="temp.MainConcern"></el-input>
                        </el-form-item>
                    </el-col> 
                    <el-col :span="24">
                        <el-form-item :label="'备注'" prop="Remark">
                            <el-input type="textarea" :maxlength="500" :disabled="editable" :rows="4" placeholder="" v-model="temp.Remark"></el-input>
                        </el-form-item>
                    </el-col>                   
                </el-row>
            </el-form>
            <div slot="footer">
                <slot name="other-button-area"></slot>
                <el-button size="mini" @click="handleVisteDialogClose">取消</el-button>
                <el-button size="mini" type="primary" v-show="!editable" :loading='postLoading' @click="createData">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import { getUserInfo } from '@/utils/auth'
import EmpSelector from "../common/empSelector"
// import CommentList from "../task/commentList"
import indexPageMixin from "@/mixins/indexPage"
import * as pf from "@/api/procurementForecasts"
import * as users from '@/api/users'
import createNumberMask from 'text-mask-addons/dist/createNumberMask'



export default {
    name: 'purchase-list',
    components: {
        EmpSelector,
        // CommentList,
    },
    directives: {
        elDragDialog,
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: true
        },
        editablePurchase: { //是否能够操作（新增、编辑、删除）采购预测
            type: Boolean,
            default: true
        },        
    },
    mixins: [indexPageMixin],
    watch: {
        id: {
            handler(val) {
                this.listQuery.CustomerProjectId = val
                this.temp.CustomerProjectId = val
                this.getList()
            },
        },
        visible(val) {
            this.dialogVisible = val
            if(val) {
                
            }
        },
        dialogVisible(val) {
            if(!val) {
                this.$emit('close')
            }
        },
    },
    computed: { 
        // editable() {
        //     return this.dialogStatus == 'detail'
        // }
    },
    created() {
        this.rules = this.initRules(this.rules)

    },
    data() {
        return {
            numberMask: createNumberMask({
                prefix: '',
                suffix: '', // This will put the dollar sign at the end, with a space.
                includeThousandsSeparator: false,
                allowDecimal: true,
                decimalLimit: 2,
                integerLimit: 9,
            }),
            listLoading: false,
            multipleSelection: [],
            listQuery: {
                // PageIndex: 1,   
                // PageSize: 20,
                CustomerProjectId: ""
            },
            tabColumns: [
                { attr: { prop: "PurchaseItem", label: "采购项" } },
                { attr: { prop: "Quantity", label: "数量" } },
                { attr: { prop: "EstimatedPurchasePrice", label: "预计采购价格（元）" } },
                { attr: { prop: "ProcurementInPreviousYears", label: "往年采购情况", showOverflowTooltip: true } },
                { attr: { prop: "MainConcern", label: "主要关注事项" , showOverflowTooltip: true} },
                { attr: { prop: "Remark", label: "备注" , showOverflowTooltip: true} }
            ],
            tabDatas: [],
            total: 0,
            rules: {
                PurchaseItem: { fieldName: "采购项", rules: [{ required: true }, { max: 50 }]},
                Quantity: { fieldName: "数量", rules: [{ required: true }]},
                EstimatedPurchasePrice: { fieldName: '预计采购价格', rules: [{ required: true }]}
            },
            dialogVisible: this.visible,
            dialogOfViste: false,
            postLoading: false,
            temp: {
                CustomerProjectId: '',
                ProcurementForecastId: '',
                PurchaseItem: '',
                Quantity: '',
                EstimatedPurchasePrice: '',
                ProcurementInPreviousYears: '', 
                MainConcern: '',
                Remark: '',
            },
        }
    },
    methods: {
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        resetTemp() {
            this.temp = Object.assign({}, this.temp, {
                ProcurementForecastId: '',
                PurchaseItem: '',
                Quantity: '',
                EstimatedPurchasePrice: '',
                ProcurementInPreviousYears: '', 
                MainConcern: '',
                Remark: '',
            })
        },
        getList() {
            this.listLoading = true
            pf.getList(this.listQuery).then(response => {
                this.tabDatas = response.Items.map(s => {
                    if(!s.EstimatedPurchasePrice) {
                        s.EstimatedPurchasePrice = ''
                    }
                    s.EstimatedPurchasePrice = s.EstimatedPurchasePrice + ''
                    return s
                });
                this.total = response.Total;
                this.listLoading = false;
            }).catch(err => {
                this.listLoading = false
            })
        },        
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },

        handleChangeOwnerUsers(users) {
            if (users && users.length > 0) {
                this.temp.Vistor = [users[0]];
            } else {
                this.temp.Vistor = [];
            }
        },
        showDialog() {
            this.dialogOfViste = true
        },
        closeDialog() {
            this.dialogOfViste = false
        },
        handleAdd() {
            this.dialogStatus = 'create'
            this.resetTemp()
            this.showDialog()
        },
        //编辑
        handleUpdate(row, optType = "update") {
            this.dialogStatus = optType
            pf.detail({id: row.ProcurementForecastId}).then(res => {
                res.EstimatedPurchasePrice = res.EstimatedPurchasePrice + ''
                this.temp = Object.assign({}, this.temp, res)
                this.showDialog()
            }).catch(err => {
                
            })
            // this.showDialog()
            // this.temp = Object.assign({}, this.temp, row)
        },
        //删除
        handleDelete(rows) {
            let ids = [rows.ProcurementForecastId]
            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                pf.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },        
        handleVisteDialogClose() {
            this.closeDialog()
        },
        handleClose() {
            this.dialogVisible = false
        },
        createData() {
            let self = this;
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {

                    let postDatas = JSON.parse(JSON.stringify(this.temp))
                    pf.edit(postDatas).then(res => {
                        this.closeDialog()
                        this.$message.success({
                            title: '成功',
                            message: '操作成功',
                        })
                        this.getList()
                    }).catch(err => {


                    })

                }
            })
        },
             
    },
}
</script>

<style scoped>


</style>