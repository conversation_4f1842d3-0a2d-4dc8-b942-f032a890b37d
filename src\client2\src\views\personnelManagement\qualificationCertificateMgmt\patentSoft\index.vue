<template>
    <!-- 专利软著 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="true" :isShowOpatColumn="true"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="120"
            :loading="listLoading"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :startOfTable="startOfTable" @rowSelectionChanged="rowSelectionChanged"
            @sortChagned="handleSortChange">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索知识产权名称/专利号/登记号" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>
                        <template slot="PatentWorkType">
                            <el-select style="width: 100%;" clearable v-model="listQuery.PatentWorkType" placeholder="请选择">
                                <el-option v-for="item in patentWorkTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <template slot="ClassifyId">
                            <el-select style="width: 100%;" clearable v-model="listQuery.ClassifyId" placeholder="请选择">
                                <el-option v-for="item in relationList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <template slot="TimeEffectiveState">
                            <el-select style="width: 100%;" clearable v-model="listQuery.TimeEffectiveState" placeholder="请选择">
                                <el-option v-for="item in statusTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <template slot="NoticeNumber">
                            <el-input style="width: 100%;" v-model="listQuery.NoticeNumber" placeholder="请输入"></el-input>
                        </template>
                        <template slot="NoticeDate">
                            <el-date-picker v-model="listQuery.NoticeDate" type="daterange" align="right" unlink-panels range-separator="-"
                            start-placeholder end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                        </template>
                        <template slot="ValidDate">
                            <el-date-picker v-model="listQuery.ValidDate" type="daterange" align="right" unlink-panels range-separator="-"
                            start-placeholder end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                        </template>
                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked">
                                <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                                    <el-button type="primary">
                                        {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                                    </el-button>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="batchTermOfValidity">修改有效期</el-dropdown-item>
                                        <el-dropdown-item command="batchTermOfRelate">修改关联产品</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </permission-btn>
                        </template>
                    </app-table-form>
                </template>

                <template slot="ClassifyName" slot-scope="scope">{{ scope.row.ClassifyName | emptyFilter }}</template>
                <template slot="PatentWorkType" slot-scope="scope">{{ scope.row.PatentWorkType | patentWorkTypeFilter }}</template>
                <template slot="NoticeDate" slot-scope="scope">{{ scope.row.NoticeDate | dateFilter("YYYY-MM-DD") }}</template>
                <template slot="ValidDate" slot-scope="scope">
                    <span :style="{color: (scope.row.TimeEffectiveState==2||scope.row.TimeEffectiveState==3) ? getStatusObj(scope.row.TimeEffectiveState).color : ''}">
                        {{ scope.row.ValidDate | dateFilter("YYYY-MM-DD") }}
                    </span>
                </template>
                <template slot="TimeEffectiveState" slot-scope="scope">
                    <span class="item-status" v-if="!!scope.row.TimeEffectiveState" :style="{backgroundColor: getStatusObj(scope.row.TimeEffectiveState).bgColor, color: getStatusObj(scope.row.TimeEffectiveState).color}">
                        {{ getStatusObj(scope.row.TimeEffectiveState).label }}
                    </span>
                    <template v-else>无</template>
                </template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 详情 -->
                    <app-table-row-button @click="handleUpdate(scope.row,'detail')" text="详情" :type="2"></app-table-row-button>
                    <!-- 编辑 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row, 'update')" :type="1"></app-table-row-button>
                    <!-- 删除 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                </template>
            </app-table>
        </div>
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogFormVisible" :id="selectId" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogFormVisible" @closeDialog="createDialogFormVisible=false" @saveSuccess="createSaveSuccess"></create-page>
    
    <!-- 修改有效时段（提醒设置） -->
    <effective-period v-if="batchEffectivePeriodVisible" :component-type="3" :dialogFormVisible="batchEffectivePeriodVisible" @closeDialog="batchEffectivePeriodVisible=false"></effective-period>

    <!-- 关联产品管理 -->
    <relate-dialog v-if="relateDialogFormVisible" :dialogFormVisible="relateDialogFormVisible" @closeDialog="closeRelateDialog" @saveSuccess="relateSaveSuccess"></relate-dialog>

    <!-- 批量修改有效期 或者 关联产品 -->
    <batch-update-dialog v-if="batchUpdateVisible" :batchUpdateType="batchUpdateType" :ids="multipleSelectionIds" :dialogFormVisible="batchUpdateVisible" @closeDialog="batchUpdateVisible=false" @saveSuccess="batchUpdateSaveSuccess"></batch-update-dialog>

    <!-- 导出 -->
    <v-export
      @saveSuccess="handleSuccessExport"
      @closeDialog="handleCloseExport"
      :dialogFormVisible="dialogExportVisible"
      :rData="rData"
      :cData='cData'
    >
    </v-export>
</div>
</template>
<script>
import * as patentApi from '@/api/personnelManagement/Patent'
import * as classify from "@/api/classify";
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import effectivePeriod from "../common/effectivePeriod"
import relateDialog from "./relateDialog"
import batchUpdateDialog from "./batchUpdateDialog"
import vExport from "@/components/Export/index";
import busMixins from './mixins'
import { vars } from '../common/vars'
export default {
    name: 'patent-soft',
    mixins: [indexPageMixin, busMixins],
    components: {
        createPage,
        // termOfValidity,
        effectivePeriod,
        relateDialog,
        batchUpdateDialog,
        vExport,
    },
    filters: {
        patentWorkTypeFilter(val) {
            let obj = vars.patentWorkTypes.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "";
        }
    },
    created() {
        this.getClassifyList()
        this.getList();
    },
    data() {
        return {
            /**修改提醒设置 */
            batchEffectivePeriodVisible: false,

            patentWorkTypes: vars.patentWorkTypes, // 著作类型 s
            statusTypes: vars.statusTypes, // 列表中状态  s
            
            selectId: '',
            createDialogStatus: 'create',
            createDialogFormVisible: false,

            batchUpdateVisible: false,
            batchUpdateType: 1, //1 修改日期；2 修改关联产品
            statusList: [],
            total: 0,
            listQuery: {},
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "PatentWorkType", label: "著作类型" },
                { prop: "ClassifyId", label: "关联产品" },
                { prop: "TimeEffectiveState", label: "状态" },
                { prop: "NoticeNumber", label: "授权公告号" },
                { prop: "NoticeDate", label: "授权公告日" },
                { prop: "ValidDate", label: "有效期至" },
            ],
            multipleSelection: [],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "Name", label: "知识产权名称", showOverflowTooltip: true} },
                { attr: { prop: "PatentWorkType", label: "著作类型", width: "120"}, slot: true },
                { attr: { prop: "ClassifyName", label: "关联产品", width: "120"}, slot: true },
                { attr: { prop: "TimeEffectiveState", label: "状态", sortable: "custom", width: "100"}, slot: true },
                { attr: { prop: "PatentNumber", label: "专利号/登记号", showOverflowTooltip: true} },
                { attr: { prop: "NoticeNumber", label: "授权公告号", showOverflowTooltip: true} },
                { attr: { prop: "NoticeDate", label: "授权公告日", sortable: "custom", width: "110"}, slot: true },
                { attr: { prop: "ValidDate", label: "有效期至", sortable: "custom", width: "110"}, slot: true },
                { attr: { prop: "Remark", label: "备注", showOverflowTooltip: true} },
            ],

            relateDialogFormVisible: false,
            relationList: [],

            rData: null,
            cData: [],
            dialogExportVisible: false,

        }
    },
    methods: {
        getClassifyList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas.BusinessType = this.businessType
            classify.getListByCondition(postDatas).then(res => {
                self.relationList = res.map(s => {
                    return {
                        value: s.Id,
                        label: s.Name
                    }
                });
            })
        },
        getList() {
            let self = this;
            self.listLoading = true;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);
            if (postDatas.NoticeDate && postDatas.NoticeDate.length == 2) {
                postDatas.StartNoticeDate = postDatas.NoticeDate[0] == postDatas.NoticeDate[1] ? postDatas.NoticeDate[0] + ' 00:00:00' : postDatas.NoticeDate[0]
                postDatas.EndNoticeDate = postDatas.NoticeDate[0] == postDatas.NoticeDate[1] ? postDatas.NoticeDate[1] + ' 23:59:59' : postDatas.NoticeDate[1]
            }
            if (postDatas.ValidDate && postDatas.ValidDate.length == 2) {
                postDatas.StartValidDate = postDatas.ValidDate[0] == postDatas.ValidDate[1] ? postDatas.ValidDate[0] + ' 00:00:00' : postDatas.ValidDate[0]
                postDatas.EndValidDate = postDatas.ValidDate[0] == postDatas.ValidDate[1] ? postDatas.ValidDate[1] + ' 23:59:59' : postDatas.ValidDate[1]
            }
            delete postDatas.NoticeDate
            delete postDatas.ValidDate
            patentApi.getList(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 表格顶部按钮点击事件
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleUpdate(null, 'create');
                    break;
                case "batchTermOfValidity":
                    this.batchUpdateType = 1
                    this.handleTermOfRelate();
                    break;
                case "batchTermOfRelate":
                    this.batchUpdateType = 2
                    this.handleTermOfRelate();
                    break;
                case "btnEffectivePeriod":
                    this.handleEffectivePeriod();
                    break;
                case "btnRelate":
                    this.handleRelateDialog()
                    break;
                case "btnExport":
                    this.handleExpor();
                    break;
                default:
                    break;
            }
        },
        handleSuccessExport() {},
        handleCloseExport() {
            this.dialogExportVisible = false;
        },
        handleExpor() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData)
            this.rData={
                "exportSource": 29,
                "columns": [],
                "searchCondition": postData
            }
            this.cData = this.tabColumns.map(s => {
                return {
                    label: s.attr.label,
                    value: s.attr.prop
                }
            }).concat([{label: '申请日期', value: 'ApplyDate'}])
            this.dialogExportVisible=true;
        },
        /** 弹出 编辑有效时段 */
        handleEffectivePeriod() {
            this.multipleSelectionIds = this.multipleSelection.map(s=>{return s.Id});
            this.batchEffectivePeriodVisible=true;
        },
        /** 弹出 批量编辑编辑有效期 */
        // handleTermOfValidity() {
        //     if (this.multipleSelection.length>0) {
        //         this.multipleSelectionIds = this.multipleSelection.map(s=>{return s.Id});
        //         this.batchTermOfValidityVisible=true;
        //     } else {
        //         this.$message({
        //             message: "至少选择一条",
        //             type: "error"
        //         });
        //     }
        // },

        handleTermOfRelate() {
            if (this.multipleSelection.length>0) {
                this.multipleSelectionIds = this.multipleSelection.map(s=>{return s.Id});
                this.batchUpdateVisible=true;
            } else {
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.selectId = optType === 'create' ? '' : row.Id;
            this.createDialogStatus = optType
            this.createDialogFormVisible = true
        },
        /** 编辑框 点确定后 关闭 并刷新列表 */
        createSaveSuccess(d) {
            console.log('d',d)
            if (!d) {
                this.createDialogFormVisible = false
            }
            // this.listQuery.PageIndex = 1;
            this.getList()
        },
        batchUpdateSaveSuccess() {
            this.batchUpdateVisible = false
            this.getList()
        },
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                patentApi.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        getStatusObj(val) {
            return vars.statusTypes.find(
                s => s.value == val
            ) || {};
        },
        
        handleRelateDialog() {
            this.relateDialogFormVisible = true
        },
        closeRelateDialog() {
            this.relateDialogFormVisible = false
        },
        relateSaveSuccess() {
            this.getList()
            this.closeRelateDialog()
        },
    }
}
</script>
