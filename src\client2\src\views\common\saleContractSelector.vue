<template>
    <div>
        <listSelector
            :checkedData="listSelectorCheckedData"
            :getListUrl="listSelectorUrl"
            :multiple="listSelectorMultiple"
            :pageTitle="listSelectorTitle"
            :topMessage="listSelectorTopMessage"
            :selectKeyName="listSelectorKeyName"
            :condition="listQueryParams"
            :columnData="listSelectorColumnData"
            :dialogFormVisible="listSelectorDialogFormVisible"
            @closeDialog="listSelectorCloseDialog"
            @saveSuccess="listSelectorSaveSuccess"
            :width='1200'
            ref="listSelector"
        >
            <template slot="conditionArea">
                <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch' :layoutMode='layoutMode'>
                    <template slot="Keywords">
                        <el-input style="width: 100%;" 
                            placeholder="搜索售后合同编号/名称"
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQueryParams.Keywords"
                        ></el-input>
                    </template>
                </app-table-form>
            </template>

            <template slot="AfterContractStatus" slot-scope="scope">
                <span :style="{color: getStatusObj(scope.row.AfterContractStatus).color}">
                    {{ getStatusObj(scope.row.AfterContractStatus).label }}
                </span>
            </template>
            <template slot="AfterContractProgress" slot-scope="scope">
                <div class="progress-wrapper">
                    <el-progress :percentage="scope.row.AfterContractProgress || 0" :color="scope.row.AfterContractProgress > 100 ? getProgressTypeObj('>').color : scope.row.AfterContractProgress == 100 ? getProgressTypeObj('=').color : getProgressTypeObj('<').color"></el-progress>
                </div>
            </template>
            <template slot="RemainingAmount" slot-scope="scope">
                <span :style="{color: scope.row.RemainingAmount < 0 ? 'red' : ''}">
                    {{ scope.row.RemainingAmount }}
                </span>
            </template>
        </listSelector>
    </div>
</template>

<script>
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
import { vars } from '../afterSalesMgmt/maintenCenter/saleContractMgmt/vars'

export default {
    name: "sale-contract-selector",
    components: {
        listSelector,
    },
    filters: {
    },
    props: {
        isShow: {
            type: Boolean,
            default: false,
        },
        checkedList: {
            type: Array,
            default: () => {
                return [];
            },
        },
        condition: {
            type: Object,
            default: null,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
    },
    created() {
        
    },
    watch: {
        isShow(val) {
            this.listSelectorDialogFormVisible = val;
        },
        checkedList(val) {
            if (val && val.length > 0) {
                this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
            }
        },
        condition: {
            handler(val) {
                if(this.condition) {
                    this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
                }
            },
            deep: true
        }
    },
    data() {
        return {
            layoutMode: 'simple',
            status: vars.status,
            progressTypes: vars.status,
            listSelectorCheckedData: [],
            listSelectorUrl: serviceArea.business +"/AfterContract/GetListPage",
            listSelectorMultiple: this.multiple,
            listQueryParams: {
                Keywords: '',
                
            },
            tableSearchItems: [
                {
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },
            ],
            listSelectorTitle: "关联售后合同",
            listSelectorTopMessage: "",
            listSelectorKeyName: "Id",

            listSelectorColumnData: [{
                    attr: {
                        prop: "AfterContractCode",
                        label: "售后合同编号",
                    },
                },
                {
                    attr: {
                        prop: "AfterContractName",
                        label: "售后合同名称",
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "AfterContractStatus",
                        label: "服务状态",
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "AfterContractProgress",
                        label: "服务进度",
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "RemainingAmount",
                        label: "剩余额度",
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "ServiceCharge",
                        label: "服务费",
                    },
                },
            ],
            listSelectorDialogFormVisible: false,
        };
    },
    methods: {
        getStatusObj(val) {
            return this.status.find(s => s.value == val) || {}
        },
        getProgressTypeObj(val) {
            return this.progressTypes.find(s => s.value == val) || {}
        },
        handleFilter() {
            this.$refs.listSelector.getDatas()
        },
        onResetSearch() {
            // this.listQueryParams.PageIndex = 1
            this.listQueryParams.Keywords = ''
            this.handleFilter()
        },
        listSelectorCloseDialog() {
            this.onResetSearch()
            this.listSelectorDialogFormVisible = false;
            this.$emit("closed", this.listSelectorDialogFormVisible);
        },
        listSelectorSaveSuccess(data) {
            let list = data || [];
            this.$emit("changed", JSON.parse(JSON.stringify(list)));
            this.listSelectorCloseDialog();
        },
    },
};
</script>

<style scoped>
.progress-wrapper >>> .el-progress-bar {
  margin-right: -70px;
  padding-right: 65px;
}
</style>
