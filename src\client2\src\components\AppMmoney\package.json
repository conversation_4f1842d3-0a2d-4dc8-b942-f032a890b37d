{"name": "v-money", "version": "0.8.1", "description": "Vue currency input/directive mask", "main": "dist/v-money.js", "scripts": {"size": "size-limit", "upgrade:check": "ncu", "upgrade:apply": "ncu -a", "dev": "vue build ./src/docs/docs.vue --config ./src/docs/build.config.js --host 0.0.0.0", "build": "vue build ./src/index.js --config ./build.config.js --dist ./dist/ --prod --lib", "docs:build": "vue build ./src/docs/docs.vue --config ./src/docs/build.config.js --dist ./docs/ --prod", "docs:serve": "open http://localhost:5000/docs/ && serve ./", "compile": "npm run build -- --disable-compress", "test": "jest"}, "keywords": ["vue", "input", "mask", "directive", "currency", "money"], "files": ["dist/"], "size-limit": [{"path": "dist/v-money.js", "limit": "1.6 KB"}], "author": "<PERSON> <<EMAIL>> (https://vuejs-tips.github.io/)", "license": "MIT", "devDependencies": {"babel-jest": "^21.2.0", "babel-preset-vue-app": "^1.3.1", "jest": "^21.2.1", "npm-check-updates": "^2.13.0", "size-limit": "^0.11.6", "vuetify": "^0.16.7"}}