
////获取调整后的数据（被调整排班的数据）
function getChangedData(schedules) {
    if(schedules && schedules.length > 0) {
        //// schedules 结构
        // [{
        //     employeeId: 'xxxxxxxx',
        //     date: '2021-03-15', 
        //     type: 1
        // }]

        let result = schedules.reduce((prev, curr) => {
            let temp = prev.find(s => s.EmployeeId == curr.employeeId)
            if(temp) {
                temp.DateList.push({
                    Date: curr.date,
                    DateType: curr.type
                })
            }else{
                temp = {
                    TimecardDepartmentId: '',
                    EmployeeId: curr.employeeId,
                    DateList: [{
                        Date: curr.date,
                        DateType: curr.type
                    }]
                }
                prev.push(temp)
            }
            return prev
        }, [])

        return result



        // return schedules.map(s => {
        //     return {
        //         TimecardDepartmentId: '',
        //         EmployeeId: s.EmployeeId,
        //         DateList: s.DateList.filter(d => d.DateType != d.OldDateType).map(s => {
        //             return {
        //                 Date: s.Date,
        //                 DateType: s.DateType
        //             }
        //         }), //只需要调整后的
        //     }


        // }) || []
    }
    return []
}

export {
    getChangedData,
}
