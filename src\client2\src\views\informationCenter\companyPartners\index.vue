<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title title="合作伙伴" :subTitle='["企业合作伙伴的添加、管理页面"]'></page-title> -->
      <div class="page-wrapper">
        <div class="content-wrapper">
          <div class="content">
            <div class="opt-wrapper">
              <!-- <el-button type="primary" @click="handleDialog('create')">添加伙伴</el-button> -->   
              <permission-btn style="margin-left:6px;" moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
            </div>
            <div class="list" v-loading='parLoading'>
              <no-data v-show="!list || list.length == 0"></no-data>
              <div class="card-wrapper" v-for="c in list" :key="c.Id">
                <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                  <div slot="header" class="clearfix">
                    <span>
                      <!-- <el-button type="primary" plain @click="handleUpdate(c)">编辑</el-button>
                        <el-button type="primary" plain @click="handleDelete(c)">删除</el-button> -->
                      <i  v-if="rowBtnIsExists('btnEdit')" class="el-icon-edit-outline" style="cursor:pointer" @click="handleUpdate(c)"></i>
                      <i v-if="rowBtnIsExists('btnDel')" class="el-icon-delete" style="cursor:pointer" @click="handleDelete(c)"></i>
                    </span>
                  </div>
                  <div @click="handleUpdate(c, 'detail')" class="elMain">
                    <!-- <el-container>
                        <el-aside width="200px">
                          <img :src="c.PartnersLogoPath"
                            style="padding-top: 10px;width: 150px;height: 150;;padding-left: 15px"></el-aside>
                        <el-container>
                          <el-main :title="c.PartnersName" class="mainBold omit"> {{ c.PartnersName }}</el-main>
                          <el-footer >
                            {{ c.PartnersTitle | ellipsis }}


                          </el-footer>
                        </el-container>
                      </el-container> -->
                    <div>
                      <img :src="c.PartnersLogoPath">
                    </div>
                    <div>
                      <p :title="c.PartnersName" class="mainBold omit">{{ c.PartnersName }}</p>
                      <p class="omit1">{{c.PartnersTitle}}</p>
                    </div>
                  </div>
                  <!-- <div class="item-wrapper">

                      <div class="item">
                        <div class="item-title">
                          <img :src="c.PartnersLogoPath" style="width: 75px; height: 75px">
                          {{ c.PartnersName }} {{ c.PartnersTitle | ellipsis }}
           <div class="item" style="padding-left: 95px">

                          </div>
                        </div>

                      </div>
                    </div> -->
                </el-card>

              </div>
            </div>
          </div>
        </div>
      </div>
  
    </div>
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="Id" @reload="getList"></create-page>
  </div>
</template>

<script>

import * as companyIntroduction from '@/api/informationCenter/companyIntroduction'
import createPage from './create'
import elDragDialog from '@/directive/el-dragDialog'

export default {
  components: {
    createPage
  },
  directives: {
    elDragDialog
  },
  // filters: {
  //   ellipsis(value) {
  //     if (!value) return ''
  //     if (value.length > 30) {
  //       return value.slice(0, 30) + '...'
  //     }
  //     return value
  //   }
  // },
  created() {
    this.getList()
    //     this.getListPage()
  },
  mounted() {
  },
  data() {
    return {
      dialogStatus: 'create',
      dialogFormVisible: false,
      parLoading: false,
      listQuery: { // 查询条件
        PageSize: 15
      },
      list: [],//项目集合
      Id: '',
      total: 0,
    }
  },
  methods: {
    checkPremissBtns(domId) {
      return this.rowBtns.findIndex(b => b.DomId == domId) > -1
    },
   
     onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog('create')
                    break;
                default:
                    break;
            }
        },
    transCount(list, groupType) {
      let total = 0
      if (list) {
        if (!groupType) { //全部
          total = list.map(s => s.Total).reduce((prev, curr) => {
            return prev + curr
          }, total)
        } else {
          let tmp = list.find(s => s.Type == groupType)
          if (tmp) {
            total = tmp.Total
          }
        }
      }
      return total
    },
    handleDialog(activeName) {

      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false
    },
    handleSaveSuccess(_formData) {
       this.listQuery.PageIndex = 1
      this.getList()
      this.closeDialog()
    },

    handleUpdate(row, optType = "update") {
      // 弹出编辑框

      this.Id = row.Id
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    //   getListPage(){
    //     companyIntroduction.getListPage(this.listQuery).then(response => {
    //   this.tabDatas = response.Items
    //   this.total = response.Total
    //   this.listLoading = false
    // })

    //   },
    //获取列表
    getList() {
      this.parLoading = true
      companyIntroduction.getList().then(res => {
        this.parLoading = false
        this.list = res
        //  this.total = res.Total
      })
    },

    handleCommand(activeName, product) {
      if (activeName == 'update') {
        this.Id = product.Id
        this.handleDialog(activeName)
      } else if (activeName == 'delete') {
        this.del(product)
      }
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleDelete(rows) {

      let ids = []
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id)
      } else {
        ids.push(rows.Id)
      }

      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        companyIntroduction.del(ids).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
  }

}
</script>

<style lang="scss" scoped>
.bg-white {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
.elMain {
  width: 100%;
  height: 146px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  > div:first-child {
    width: 126px;
    height: 100%;
    > img {
      width: 100%;
      height: 100%;
    }
  }
  > div:last-child {
    width: calc(100% - 136px);
    padding-left: 10px;
    color: #909399;
  }
}
.omit1 {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.clearfix {
  > span {
    float: right;
    padding-right: 9px;
    i {
      font-size: 20px;
      margin-top: 8px;
      margin-right: 4px;
    }
  }
}
.mainBold {
  font-size: 16px;
  font-weight: 700;
}
.divUeditor {
  overflow: hidden;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.page-wrapper {
  display: flex;
  background: #fff;
  padding: 10px;

  .content-wrapper {
    flex: 1;

    .content {
      padding: 10px;
      padding-right: 0;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .list {
        .card-wrapper {
          padding: 5px;
          display: inline-block;
          width: 33.3%;

          .card {
            width: 100%;
            height: 185px;
          }

          .item-wrapper {
            cursor: pointer;
            padding: 14px;
            padding-bottom: 2px;

            .item {
              padding-bottom: 4px;

              div {
                display: inline-block;
              }

              .item-title {
                width: 90px;
              }
            }
          }

          .split-line {
            height: 1px;
            background: #dcdfe6;
            margin-top: 10px;
          }

          .footer {
            padding-right: 14px;
            text-align: right;
          }
        }
      }
    }
  }
}
</style>
