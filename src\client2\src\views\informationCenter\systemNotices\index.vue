<template>
  <div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
      <!-- <page-title
        title="公告通知"
        :subTitle="['内部公告通告的发布及管理页面']"
      ></page-title> -->

      <div class="__dynamicTabWrapper">
        <app-table
          ref="mainTable"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :tab-auth-columns="tabAuthColumns"
          :isShowAllColumn="true"
          :loading="listLoading" :isShowBtnsArea='false'
          @rowSelectionChanged="rowSelectionChanged"
          :isShowOpatColumn="true"
          :multable="false"
          :startOfTable="startOfTable"
          @sortChagned='handleSortChange'
          :indexColMinWidth='1'
          :optColWidth='200'  :layoutMode='layoutMode'
        >
          <template slot="NoticeTitle" slot-scope="scope">
            <app-tag-pure effect="dark" v-if="scope.row.IsTop" text="置顶"></app-tag-pure>

            <span v-if="scope.row.NoticeStatus != 0" class="span">
              <i class="tip" v-if="!scope.row.IsRead" title="未读"></i>
              <!-- <i class="el-icon-s-opportunity" v-else title="已读" style="color: green;"></i> -->
            </span>
            {{ scope.row.NoticeTitle }}
          </template>
          <template slot="NoticeStatus" slot-scope="scope">
            <app-tag-pure effect="dark" v-if="getNoticeStatus(scope.row.NoticeStatus)" :color="getNoticeStatus(scope.row.NoticeStatus).color" :text="getNoticeStatus(scope.row.NoticeStatus).label"></app-tag-pure>
          </template>


          <template slot="CreateEmployee" slot-scope="scope">{{
            scope.row.CreateEmployee | nameFilter
          }}</template>
           
          <template slot="Author" slot-scope="scope">{{
            scope.row.Author
          }}</template>

          <template slot="CreateTime" slot-scope="scope">
            {{ scope.row.CreateTime | dateFilter("YYYY-MM-DD HH:mm") }}
          </template>
          <template slot="PublishTime" slot-scope="scope">
            <span v-if="scope.row.NoticeStatus != 0">
              {{ scope.row.PublishTime | dateFilter("YYYY-MM-DD HH:mm") }}
            </span>
          </template>

          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form  :layoutMode='layoutMode'
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="onResetSearch"
            >
                <template slot="NoticeTitle">
                    <el-input style="width: 100%;" 
                        placeholder="搜索公告标题..."
                        @clear='handleFilter'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                handleFilter()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.NoticeTitle"
                    ></el-input>
                </template>

              <!-- <template slot="StartDateTime">
                <el-date-picker style="width: 100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="listQuery.StartDateTime" type="date" placeholder></el-date-picker>
              </template>
              <template slot="EndDateTime">
                <el-date-picker style="width: 100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="listQuery.EndDateTime" type="date" placeholder></el-date-picker>
              </template> -->

              <template slot="Range">
                <el-date-picker
                  v-model="listQuery.Range"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder
                  end-placeholder
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%;"
                ></el-date-picker>
              </template>
                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                    <permission-btn
                    style="margin-left:6px;"
                    moduleName="position"
                    v-on:btn-event="onBtnClicked"
                    ></permission-btn>
                </template>
            </app-table-form>
          </template>


          <template slot="tableTopAres">
            <div style="padding: 4px 4px 4px 8px;">
              <tags
                :items="systemNoticesGroupTypes"
                v-model="listQuery.NoticeStatus"
              >
                <template v-for="t in systemNoticesGroupTypes" :slot="t.value">
                  {{ t.label }}
                </template>
              </tags>
            </div>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button
              v-if="rowBtnIsExists('btnEdit') && scope.row.NoticeStatus != 1"
              @click="handleUpdate(scope.row)"
              :type="1"
            ></app-table-row-button>
            <app-table-row-button
              v-if="rowBtnIsExists('btnRecall') && scope.row.NoticeStatus == 1"
              @click="handleRecall(scope.row)"
              :type="2"
              text="撤回"
            ></app-table-row-button>
            <!-- <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleReview(scope.row)" :type='2'></app-table-row-button> -->
            <app-table-row-button
              @click="handleReview(scope.row)"
              :type="2"
              text="详情"
            ></app-table-row-button>
            <app-table-row-button
              v-if="rowBtnIsExists('btnDel') && scope.row.NoticeStatus != 1"
              @click="handleDelete(scope.row)"
              :type="3"
            ></app-table-row-button>

            <app-table-row-button
              @click="handleSetTop(scope.row)"
              :type="scope.row.IsTop ? 5 : 1"
              :text="scope.row.IsTop ? '取消置顶' : '置顶'"
            ></app-table-row-button>
          </template>
        </app-table>
      </div>

      <pagination
        :total="total"
        :page.sync="listQuery.PageIndex"
        :size.sync="listQuery.PageSize"
        @pagination="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <create-page
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
      @reload="getList"
    ></create-page>
  </div>
</template>

<script>
import * as systemNotices from "@/api/informationCenter/systemNotices";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import { noticeStatusEnum } from "../enums";

import createPage from "./create";

export default {
  name: "systemNotices",
  components: {
    createPage
  },
  directives: {
    elDragDialog
  },
  mixins: [indexPageMixin],
  created() {
    this.getList();
  },
  watch: {
    "listQuery.NoticeStatus"(val) {
      this.getList();
    }
  },
  filters: {
    authorFilter(author){
       if (author) {
        return author[0].Name;
      }
      return "";
    },
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
  },
  mounted() {},
  data() {
    return {
            layoutMode: 'simple',
      systemNoticesGroupTypes: [
        { value: -1, label: "公告列表" },
        { value: 0, label: "草稿箱" }
      ],

      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,

      noticeStatusEnum: noticeStatusEnum,

      multipleSelection: [],
      tableSearchItems: [
        { prop: "NoticeTitle", label: "公告标题", mainCondition: true },
        { prop: "Range", label: "发布时间" }
        // { prop: "StartDateTime", label: "创建时间" },
        // { prop: "EndDateTime", label: "至" },
      ],
      tabColumns: [
        {
          attr: {
            prop: "NoticeTitle",
            label: "标题",
            showOverflowTooltip: true, 'min-width': '4'
          },
          slot: true
        },
        {
          attr: { prop: "NoticeStatus", label: "状态", sortable: 'custom', 'min-width': '1' },
          slot: true
        },
        {
          attr: { prop: "CreateEmployee", label: "发布人", 'min-width': '1' },
          slot: true
        },
        {
          attr: { prop: "Author", label: "作者", 'min-width': '1' },
          slot: true
        },
        {
          attr: { prop: "PublishTime", label: "发布时间", sortable: 'custom', 'min-width': '1' },
          slot: true
        }
      ],
      tabDatas: [],
      listLoading: false,
      listQuery: {
        // 查询条件
        NoticeTitle: "",
        Range: [],
        StartDateTime: null,
        EndDateTime: null,
        NoticeStatus: -1,
        WebOrApp: "Web"
      },
      total: 0
    };
  },

  methods: {
    getNoticeStatus(val) {
      return this.noticeStatusEnum.find(s => s.value == val);
    },
    onResetSearch() {
      this.listQuery.Range = [];
      this.listQuery.NoticeTitle = "";
      this.listQuery.StartDateTime = null;
      this.listQuery.EndDateTime = null;
      this.getList(); //刷新列表
    },
    handleSetTop(row) {
      let isTop = row.IsTop
      let tip = isTop ? "取消置顶" : '置顶'
      
      this.$confirm(`是否${tip}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        systemNotices.setTop({id: row.Id}).then(() => {
          this.getList();
          this.$notify({
            title: "成功",
            message: `${tip}成功`,
            type: "success",
            duration: 2000
          });
        });
      });
    },
    //查看详情
    handleReview(row) {
      this.$router.push(`/informationCenter/systemNotices/detail/${row.Id}`);
    },

    //撤回
    handleRecall(row) {
      systemNotices.recall({ id: row.Id }).then(response => {
        this.$notify({
          title: "成功",
          message: "撤回成功",
          type: "success",
          duration: 2000
        });
        this.getList();
      });
    },

    // 弹出编辑框
    handleUpdate(row, optType = "update") {
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    onBtnClicked: function(domId) {
      switch (domId) {
        //我要发布
        case "btnAdd":
          this.handleDialog("create");
          break;
        //批量删除
        case "btnBatchDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error"
            });
            return;
          }
          this.handleDelete(this.multipleSelection);
          break;
        default:
          break;
      }
    },
    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.getList();
      this.closeDialog();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleSortChange({ column, prop, order }) {
      this.sortObj = {prop, order}
      this.getList()
    },    
    getList() {
      this.listLoading = true;
      if (this.listQuery.Range && this.listQuery.Range.length == 2) {
        this.listQuery.StartDateTime = this.listQuery.Range[0];
        this.listQuery.EndDateTime = this.listQuery.Range[1];
      }

      let postData = JSON.parse(JSON.stringify(this.listQuery))
      postData = this.assignSortObj(postData)
      systemNotices.getList(postData).then(response => {
        this.tabDatas = response.Items;
        this.total = response.Total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    // 多行删除
    handleDelete(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        systemNotices.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}

.span {
  position: relative;
  padding: 5px;
  display: inline-block;
}
.tip {
  display: block;
  background: #f00;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  top: 2px;
  position: absolute;
}


</style>
