<!--客服区域-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      title="文件移动"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="600"
    >
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="100px"
          style="height: 150px;"
        >
          <el-form-item :label=" '目录名称' " prop="TargetId">
            <treeselect
              :append-to-body="true"
              :noResultsText="noResultsTextOfSelTree"
              :noOptionsText="noOptionsTextOfSelTree"
              :normalizer="normalizer"
              :default-expand-level="3"
              :options="treeData"
              v-model="formModel.TargetId"
              :multiple="false"
              :show-count="false"
              placeholder="请选择"
              zIndex='9999'
            ></treeselect>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">

        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import commMixin from './mixins'
import * as foldersFile from '@/api/foldersFile'
import { listToTreeSelect, treeFilter } from '@/utils'

export default {
  /**名称 */
  name: "move-dialog",
  /**组件声明 */
  components: {},
  mixins: [commMixin],
  /**参数区 */
  props: {
    moveId: {
      type: String,
      required: true
    },
    // 1 岗位技能管理    2： 公司制度管理     3 : 管理流程
    isPostMgmtType: {
        type: Number,
        default: 1
    },
  },
  /**数据区 */
  data() {
    return {
      treeData: [],
      normalizer(node) {
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        };
      },
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单模型 */
      formModel: { TargetId: null, Id: '' },
      /**表单规则 */
      formRules: {
        TargetId: { fieldName: "名称", rules: [{ required: true }] }
      },
      treeLoading: false,
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    

  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if(val) {
          this.formModel.Id = this.moveId
          this.loadTreeData()
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);

  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {

    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;
          let postDatas = JSON.parse(JSON.stringify(_this.formModel))
          postDatas.FoldersFilesBusinessType = this.isPostMgmtType

          foldersFile.move(postDatas).then(response => {
              _this.buttonLoading = false;
              _this.$notify({
                title: "成功",
                message: "保存成功",
                type: "success",
                duration: 2000
              });

              _this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    loadTreeData() {
        let _this = this;
        let paramData = {
            FoldersFilesBusinessType: this.isPostMgmtType,
            FoldersOrFilesType: 1
        };
        _this.treeLoading = true
        foldersFile.getList(paramData).then(res => {
            _this.treeLoading = false
            let response = (res || []).map(s => {
              s.label = s.Name;
              return s
            })
            _this.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

        }).catch(err => {
            _this.treeLoading = false
        });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


