<template>
    <div>
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">

                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="80px">
                    <div class="wrapper" v-loading='loading'>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="ERP账号" prop="erpList" style="padding:10px; border-bottom:solid 1px #eee;">
                                    <erp-selector 
                                        listSelectorTitle='选择ERP账号'
                                        :listSelectorUrl='serviceArea.business + "/ERPAccount/GetListPage"'
                                        :multiple='false' :showType='2'
                                        :list='formData.erpList'
                                        :columns='erpColumns'
                                        key='service-erp'
                                        @change='handleChangeErp'
                                        :readonly="!editable"
                                    ></erp-selector>
                                    </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="物料编码">
                                     <span>{{formData.MaterialCode}}</span>
                                </el-form-item>
                            </el-col>
                            
                             <el-col :span="12">
                                <el-form-item label="物料名称">
                                    <span>{{formData.MaterialName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            
                            <el-col :span="12">
                                <el-form-item label="规格型号">
                                    <span>{{formData.Specifications}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="当前仓库">
                                     <span>{{formData.MaterialInStockNameStr}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                         <el-row>
                            <el-col :span="12">
                                <el-form-item label="退料人">
                                    <span>{{formData.MaterialEmployeeList | nameFilter}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="退料数量">
                                    <span>{{formData.MaterialReturnCount}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="质检人">
                                   <span>{{formData.TestingEmployeeList | nameFilter}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="质检时间">
                                    <span>{{formData.TestingTime | dateFilter('YYYY-MM-DD HH:mm', '无')}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                            <el-col :span="24">
                                 <el-form-item :label="'调入仓库'" prop="MaterialInStockName">
                                  <el-autocomplete
                                        class="inline-input"
                                        v-model.trim="formData.MaterialInStockName"
                                        :fetch-suggestions="querySearchIn"
                                        :placeholder="' 请输入调入仓库'"
                                        :trigger-on-focus="false"
                                        @select="handleSelectIn"
                                        @change="changeTransferInID"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                            <el-col :span="24">
                                <el-form-item label="备注">
                                    <el-input maxlength="500"  type="textarea" :rows="2" v-model="formData.Remark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </div>
                </el-form>

            </template>

            <template slot="footer">
        
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>

import { serviceArea } from "@/api/serviceArea"
import erpSelector from './erpSelector'
import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";
import * as odc from "@/api/operatingDataCenter";
import * as erp from '@/api/erpManagement/erp' 

  export default {
    name: "return-create",
    components: {
        erpSelector,
    },
    computed: {
        title() {
            if(this.dialogStatus == 'create') {
                this.getDetail();
                return '退库'
            }else if(this.dialogStatus == 'update') {
                return '编辑退库'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail";
        },
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();

                    if (this.dialogStatus == "create") {
                        this.getCacheAccount()
                    }
                }
            },
            immediate: true
        }
    },
    filters: {
        nameFilter(list){
            if(list && list.length){
              return list.map(v => v.Name).toString()      
            }
            return "无";     
        },
    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            serviceArea,
            disabledBtn: false,
             erpColumns: [
                {
                    attr: {
                        prop: "Account",
                        label: "ERP账号",
                    },
                },
                {
                    attr: {
                            prop: "RegionalName",
                            label: "所属地区",
                    },
                },
            ],
            rules: {
                erpList: { fieldName: "ERP账号", rules: [{ required: true }] },
                MaterialInStockName: { fieldName: "调入仓库", rules: [{ required: true, trigger: 'change' }] },
            },
            loading: false,
            formData: {
                erpList:[],
                ERPAccountId: '',
                MaterialInStockName: '',
                Remark: '',
              
            },
        };
    },
    methods: {
        getCacheAccount() {
            erp.getCacheAccount({}).then(res => {
                if(res) {
                    this.handleChangeErp([res])
                }
            })
        },
        resetFormData() {
            this.formData = {
                ERPAccountId: '',
            }
        },

         getDetail(){
              materialTransferApi.returnMaterialDetails({'id':this.id})
             .then(res => {
                this.loading = false

                // this.formData = Object.assign({}, this.formData, res);

                this.$set(this.formData, 'MaterialInStockNameStr', res.MaterialStockName)
                this.$set(this.formData, 'Id', res.Id)
                this.$set(this.formData, 'MaterialCode', res.MaterialCode)
                this.$set(this.formData, 'MaterialName', res.MaterialName)
                this.$set(this.formData, 'Specifications', res.Specifications)
                this.$set(this.formData, 'MaterialEmployeeList', res.MaterialEmployeeList)
                this.$set(this.formData, 'MaterialReturnCount', res.MaterialReturnCount)
                this.$set(this.formData, 'TestingEmployeeList', res.TestingEmployeeList)
                this.$set(this.formData, 'TestingTime', res.TestingTime)

                this.$nextTick(() => {
                    this.$refs.formData.clearValidate()
                })
                
              }).catch(err => {
                this.loading = false         
              });
        },


         querySearchIn(queryString, cb){
            let keywords = queryString.trim()
            odc.getStock({keyWords:keywords,ERPAccountId:this.ERPAccountId,}).then(res => {
                let result = res.map(v=>{
                    v.value = v.FName
                    return v
                });
                cb(result);
            }).catch(err => {
                
            });
        },

      handleSelectIn(item){
          this.formData.MaterialStockName = item.FNumber
          this.formData.MaterialStock = item.FStockId
          this.formData.MaterialInStock = item.FStockId
          this.formData.MaterialInStockFNumber = item.FNumber
      },


    changeTransferInID(val){
        this.formData.MaterialStockName = ''
        this.formData.MaterialStock = ''
        this.formData.MaterialInStock = ''
        this.formData.MaterialInStockFNumber = ''
        this.formData.MaterialInStockName = ''
      },

         handleChangeErp(erps) {
            this.$refs.formData.clearValidate('erpList');
            this.$set(this.formData, 'ERPAccountId', erps[0].Id)
            this.$set(this.formData, 'erpList', erps)
            // this.formData.ERPAccountId = erps[0].Id
            // this.formData.erpList = JSON.parse(JSON.stringify(erps || []))
        },
     
        createData() {
            this.$refs.formData.validate((valid, errs) => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                
                    this.disabledBtn = true
                    if (this.dialogStatus == "create") {
                        materialTransferApi.cancellingStock(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        });
                    }
        
                } 
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },   
    }
};
</script>

<style lang="scss" scoped>

</style>
