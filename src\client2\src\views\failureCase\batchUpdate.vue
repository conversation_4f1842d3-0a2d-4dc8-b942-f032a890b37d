<template>
  <app-dialog
    title="批量修改分类"
    ref="appDialogRef"
    v-bind="$attrs"
    v-on="$listeners"
    :width="500"
  >
    <template slot="body">
      <el-row class="wrapper">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
            <el-form-item label="已选中">
                {{ ids.length }}
            </el-form-item>

            <el-form-item label="请选择分类" prop="value">
                <treeselect key='type2'
                class="treeselect-common"
                :append-to-body="true"
                :normalizer="unitNormalizer"
                v-model="formData.value" :default-expand-level="3"
                :options="treeDatas" :multiple="false" placeholder :show-count="true"
                :noResultsText='noResultsTextOfSelTree'
                :noOptionsText="noOptionsTextOfSelTree"
                zIndex='9999'
                @input="hadnleChangeCustomerUnitId"
                >
                </treeselect>
            </el-form-item>

        </el-form>
      </el-row>
    </template>
    <template slot="footer">
      <!-- 取消 -->
      <el-button @click="handleClose">取消</el-button>
      <!-- 确定 -->
      <el-button @click="handleSubmit" type="primary" :disabled='disabledBtn'>确定</el-button>
    </template>
  </app-dialog>
</template>

<script>
import {serviceArea} from "@/api/serviceArea";
import { listToTreeSelect } from "@/utils";
import * as guide from '@/api/informationCenter/implementationGuide'
import * as problem from '@/api/informationCenter/implementationProblem'
import * as failurecase from "@/api/failurecase";
import * as classify from '@/api/classify'

export default {
  name: "relationship-create",
  components: {

  },
  props: {
    dialogStatus: {
      type: String,
      default: 'create'
    },
    ids: {
      type: Array,
      default() {
        return []
      }
    },
    batchType: {
      // 1 软件实施指南 2 案例常见问题 3 故障案例库
      type: Number,
      default: 1
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.formData.ids = this.ids
        }
      },
      immediate: true
    },
    "batchType": {
      handler(val) {
        if (val) {
            this.loadTreeData()
        }
      },
      immediate: true
    }
  },
  created() {
      this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      serviceArea,
      RelationLineIdTypes: [], // 客户关系线列表
      disabledBtn: false,
      rules: {
        value: { fieldName: "分类", rules: [{ required: true, trigger: 'change' }] },
      },
      labelWidth: "100px",
      formData: {
        ids: [],
        value: null
      },
      treeDatas: [],
      unitNormalizer(node) {
          // treeselect定义字段
          return {
            id: node.Id,
            label: node.Name,
            children: node.children
          }
      },
    };
  },
  computed: {

  },
  methods: {
    handleChangeOwnerUsers(users) {
      if (users && users.length > 0) {
        this.formData.value = [users[0]];
      } else {
        this.formData.value = null;
      }
    },
    hadnleChangeCustomerUnitId() {
      this.$refs.formData.validateField("value");
    },
    loadTreeData() {
      let _this = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: this.batchType
      };

      classify.getListPage(paramData).then(response => {
        _this.treeDatas = listToTreeSelect(response.Items);
      }).catch(err => {
      });
    },

    // 弹窗提交
    handleSubmit() {
      let self = this;
      console.log(self.formData)
      self.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(self.formData));
          self.disabledBtn = true
          let result = null
          if(self.batchType == 1) {
              result = guide.batchEdit(postData)
          }else if(self.batchType == 2) {
              result = problem.batchEdit(postData)
          }else if(self.batchType == 3) {
              result = failurecase.batchEdit(postData)
          }
          result.then(res => {
              self.$notify({
                  title: "提示",
                  message: "保存成功",
                  type: "success",
                  duration: 2000
              });
              self.disabledBtn = false
              self.$refs.appDialogRef.createData();
          }).catch(err => {
              self.disabledBtn = false
          });
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style lang="scss" scoped>
.wrapper {
  padding: 10px 0;
  padding-right: 30px;
  min-height: 100px;
}
</style>