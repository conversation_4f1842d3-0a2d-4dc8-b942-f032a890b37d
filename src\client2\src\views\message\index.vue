<template>
  <div>
    <div class="app-container">
      <div class="bg-white">
        <app-table
          ref="mainTable"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :isShowBtnsArea="true"
          :isShowAllColumn="true"
          :loading="listLoading"
          @rowSelectionChanged="rowSelectionChanged"
          :isShowOpatColumn="true"
        >
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form
              style="padding-top: 10px;"
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="resetSearch"
            >
              <template slot="MessageContent">
                <el-input
                  style="width: 100%;"
                  v-model="listQuery.MessageContent"
                  placeholder
                ></el-input>
              </template>
            </app-table-form>
          </template>

          <!-- 表格批量操作区域 -->
          <template slot="btnsArea">
            <el-button
              style="margin-left:4px;"
              @click="btnDelete"
            >
              <i class="el-icon-remove"></i>删除
            </el-button>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button
              @click="handleDetail(scope.row, 'detail')"
              :type="2"
            ></app-table-row-button>
            <app-table-row-button
              @click="handleDelete(scope.row)"
              :type="3"
            ></app-table-row-button>
          </template>
        </app-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.PageIndex"
          :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>

      <!-- 详情页面 -->
      <message-detail
        :dialogFormVisible="dialogFormVisible"
        v-if="dialogFormVisible"
        :MessageId="activeMessageId"
        :MessageTypeId="MessageTypeId"
        @closeDetail="closeDetail"
        @handleDialogSuccess="handleDialogSuccess"
      >
      </message-detail>

      <!-- 拜访记录 -->
      <visted-list :isShowPager='false' :editableForVistedLog='false' :id="messageId" :vistedType="3" @close='handleClosed'
        @saveSuccess='handSaveSuccess' :dialogStatus="'拜访记录'" :visible='vistedListDialogVisible'></visted-list>

      <!-- 任务详情页面 -->
      <div v-if="taskDialogFormVisible">
          <edit-page :dialogStatus='dialogStatus' :dialogFormVisible='taskDialogFormVisible' :id='taskId'
              @closeDialog='closeTaskDetail' @saveSuccess='handleTaskDialogSuccess' @reloading='handleReloading'
              :isShowPredecessorTaskDetail='true'>
          </edit-page>
      </div>
    </div>
    <template v-if="MessageTypeId == 8">
      <!-- 里程碑 -->
      <div v-if="keyType == 1 && projDialogFormVisible1">
        <comment-application-or-approval ref="caoa" :editDialogStatus='"detail"' @closeDialog='() => projDialogFormVisible1 = false'></comment-application-or-approval>
      </div>
      <!-- 版本计划 -->
      <div v-if="keyType == 2 && projDialogFormVisible2">
        <version-plan-edit :dialogStatus='"detail"' :dialogFormVisible='projDialogFormVisible2' :id='businessId' :ProjectId='projectId' @closeDialog='() => projDialogFormVisible2 = false' :projectDetail='{}'></version-plan-edit>
      </div>
      <!-- 需求 -->
      <div v-if="keyType == 3 && projDialogFormVisible3">
        <demand-edit :dialogStatus='"detail"' :dialogFormVisible='projDialogFormVisible3' :id='businessId' :ProjectId='projectId' :isResearchAndDevelopment='isResearchAndDevelopment' @closeDialog='() => projDialogFormVisible3 = false'></demand-edit>
      </div>
      <!-- 项目任务 -->
      <div v-if="keyType == 4 && projDialogFormVisible4">
        <proj-task-edit :dialogStatus='"detail"' :dialogFormVisible='projDialogFormVisible4' :id='businessId' @closeDialog='() => projDialogFormVisible4 = false' :isShowPredecessorTaskDetail='true' :isResearchAndDevelopment='isResearchAndDevelopment' :ProjectId='projectId'></proj-task-edit>
      </div>
      <!-- 问题 -->
      <div v-if="keyType == 5 && projDialogFormVisible5">
        <question-edit :editDialogStatus='"detail"' :projectDetail='{}' :dialogFormVisible='projDialogFormVisible5' :id='businessId' :isResearchAndDevelopment='isResearchAndDevelopment' :ProjectId='projectId' @closeDialog='() => projDialogFormVisible5 = false'></question-edit>
      </div>
      <!-- 风险 -->
      <div v-if="keyType == 7 && projDialogFormVisible7">
        <risk-edit ref="rm" @closeDialog='() => projDialogFormVisible7 = false' :editDialogStatus='"detail"'></risk-edit>  
      </div>
      <!-- 变更管理 -->
      <div v-if="keyType == 8 && projDialogFormVisible8">
        <proj-mgmt-edit :project-id="projectId" :stateType='ProjectManagementChangeState' :ProjectManagementChangeId="businessId" :isVisible="projDialogFormVisible8" v-if="projDialogFormVisible8" @handleClose="() => projDialogFormVisible8 = false" :editDialogStatus="'detail'"></proj-mgmt-edit>
      </div>
      <!-- 立项详情 -->
      <div v-if="keyType == 9 && projDialogFormVisible9">
        <projectApplicationProcess :dialogStatus='"detail"' :isRejectStatus='false' :dialogFormVisible='projDialogFormVisible9' :projectId='businessId' @closeDialog='() => projDialogFormVisible9 = false'></projectApplicationProcess>
      </div>
    </template>
  </div>
</template>

<script>
import { getToken, getUserInfo, setCookie, getCookie } from "@/utils/auth";
import * as messages from "@/api/messages";
import * as customerArchive from "@/api/customerArchive";
import * as risk from '@/api/projectManagementRisk'
import UserSelector from "../common/userSelector";
import EmpSelector from "../common/empSelector";
import EditPage from "../task/edit";
import MessageDetail from "./detail";
import VistedList from '../customerOrganizationDetail/vistedList'
import * as projMgmt from "@/api/projectManagement"
import * as projectManagementChange from "@/api/projectManagementChange"

export default {
  name: "message",
  components: {
    EditPage,
    // UserSelector,
    EmpSelector,
    MessageDetail,
    VistedList,
    projectApplicationProcess: () => import('../projectManagement/projectApplicationProcess'),
    commentApplicationOrApproval: () => import('../milepost/commentApplicationOrApproval'),
    versionPlanEdit: () => import('../versionPlan/edit'),
    demandEdit: () => import('../demand/edit'),
    projTaskEdit: () => import('../projectTask/edit'),
    questionEdit: () => import('../question/edit'),
    riskEdit: () => import('../projectManagementRisk/riskModule'),
    projMgmtEdit: () => import('../projectManagementChange/edit')
  },
  data() {
    return {
      //项目详情弹框
      projDialogFormVisible1: false,
      projDialogFormVisible2: false,
      projDialogFormVisible3: false,
      projDialogFormVisible4: false,
      projDialogFormVisible5: false,
      projDialogFormVisible7: false,
      projDialogFormVisible8: false,
      projDialogFormVisible9: false,
      businessId: '', //项目id
      keyType: '', //项目子模块类型编号
      isResearchAndDevelopment: false, //MessageTypeId=8 为项目类型的详情才有值
      projectId: '', //MessageTypeId=8 为项目类型的详情才有值
      ProjectManagementChangeState: '',

      //详情页面
      dialogFormVisible: false,

      //任务详情页面
      taskDialogFormVisible: false,

      vistedListDialogVisible: false, //拜访记录
      messageId: '',
      taskId:'',

      //主消息列表
      tabDatas: [],
      total: 0,
      listLoading: false,
      multipleSelection: [],
      tableSearchItems: [{ prop: "MessageContent", label: "消息关键字" }],
      tabColumns: [
        {
          attr: {
            prop: "MessageTypeName",
            label: "消息分组",
            width: 200
          }
        },
        {
          attr: {
            prop: "MessageTitle",
            label: "消息标题",
            // showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "MessageContent",
            label: "消息内容",
            showOverflowTooltip: true
          }
        },
        {
          attr: {
            prop: "SenderEmployeeName",
            label: "消息发布人",
            width: 100
          }
        },
        {
          attr: {
            prop: "CreateDatetime",
            label: "发布时间",
            width: 200
          }
        }
      ],
      listQuery: {
        // 查询条件
        MessageContent: "",
        CurrentEmployeeID: ""
      },
      activeMessageId: "",
      MessageTypeId: 3 //ai 算法报警信息
    };
  },
  created() {
    this.getList();
  },
  watch: {
    //监听消息列表页面 点击消息图标重复刷新 标识
    "$store.state.IsRefreshMessage": function(params) {
      if (this.$store.state.IsRefreshMessage == 1) {
        this.getList();
      }
    }
  },
  methods: {
    //获取列表数据
    getList() {
      this.$nextTick(function() {
        //清空消息未读数量
        this.$store.commit("setBadgeNumber", 0);
        //修改消息图标点击标识 触发刷新页面的监听事件
        this.$store.commit("setIsRefreshMessage", 0);
      });

      this.listLoading = true;
      this.listQuery.CurrentEmployeeID = getUserInfo().employeeid;
      messages.getList(this.listQuery).then(response => {
        this.tabDatas = response.Items;
        this.total = response.Total;
        this.listLoading = false;
      });
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    // 弹出详情页
    handleDetail(row) {
      //项目立项管理、项目整体管理 下面的消息
      this.MessageTypeId = row.MessageTypeId
      if(row.MessageTypeId == 8) {
          // SubTypeId：
          //   1：里程碑
          //   2：版本计划
          //   3：需求
          //   4：任务（项目任务）
          //   5：问题
          //   7：风险
          //   8：变更管理
          //   9：项目立项
          this.businessId = row.BusinessId //子模块对应的业务id
          this.keyType = row.SubTypeId //子模块类型
          this.projectId = row.ProjectId
          projMgmt.detail({ projectId: this.projectId }).then(response => {
            // let flag = this.keyType ? `${this.keyType}` : '0'
            this[`projDialogFormVisible${this.keyType}`] = true
            let detail = response;
            if(this.keyType != 1 && this.keyType != 7 && this.keyType != 8) {
              this.isResearchAndDevelopment = detail.IsResearchAndDevelopment
            }
            
            if(this.keyType == 1) {
                this.$nextTick(() => {
                    this.$refs.caoa && this.$refs.caoa.showDetails(this.businessId);
                    this.isResearchAndDevelopment = detail.IsResearchAndDevelopment
                })
            }else if(this.keyType == 7) {
                let d = { clickType: 1, empEmployeeId: 'pppppp' }
                risk.getProjectManagementRisk({projectManagmentRiskId: this.businessId}).then(res => {
                  let detail = res //风险详情
                  d = Object.assign({}, d, detail)
                  this.$nextTick(() => {
                    this.$refs.rm && this.$refs.rm.childShow(d);
                    this.isResearchAndDevelopment = detail.IsResearchAndDevelopment
                  })
                })
            }else if(this.keyType == 8) {
              projectManagementChange.getProjectManagementChangeDetail({
                  id: this.businessId
              }).then(response => {
                this.ProjectManagementChangeState = response.StateTypeId
                this.isResearchAndDevelopment = detail.IsResearchAndDevelopment
              });
            }
          });
          return false
      }

      //消息类型为:任务管理消息
      if(row.MessageTypeId == 6){
        messages.getTaskIdById({messageId:row.MessageId}).then(response => {
            this.dialogStatus = 'detail'
            this.taskId = response.TaskDetailId;
            this.taskDialogFormVisible = true;
        });
      }else if(row.MessageTypeId == 7) {//评论消息
        this.messageId = row.MessageId
        this.vistedListDialogVisible = true

      }else{ //
        this.dialogFormVisible = true;
        this.activeMessageId = row.MessageId;
        this.MessageTypeId = row.MessageTypeId;
      }
    },
    //多选删除
    btnDelete: function() {
      if (this.multipleSelection.length < 1) {
        this.$message({
          message: "至少删除一个",
          type: "error"
        });
        return;
      }
      this.handleDelete(this.multipleSelection);
    },
    handleDelete(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.MessageId);
      } else {
        ids.push(rows.MessageId);
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        messages.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },
    closeDetail() {
      this.dialogFormVisible = false;
    },
    handleDialogSuccess() {
      this.dialogFormVisible = false;
      this.listQuery.PageIndex = 1;
      this.getList();
    },


    closeTaskDetail(){
      this.taskDialogFormVisible = false;
    },
     handleTaskDialogSuccess() {
      this.taskDialogFormVisible = false;
      this.listQuery.PageIndex = 1;
      this.getList();
    },
     handleReloading() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
      handleClosed() {
        this.vistedListDialogVisible = false
      },
      handSaveSuccess() {

      },
  }
};
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}

hr {
  background-color: #cac6c6;
  height: 1px;
  width: 100%;
  border: none;
}

label {
  font-size: 14px;
  font-weight: bold;
}

.el-row {
  margin: 10px;
}

.contentRight {
  text-align: right;
  padding-right: 10px;
}

.card-container {
  background: #fff;
  margin: 10px 20px 0 20px;
  height: auto;
}

.paging {
  margin-top: 8px;
}
</style>