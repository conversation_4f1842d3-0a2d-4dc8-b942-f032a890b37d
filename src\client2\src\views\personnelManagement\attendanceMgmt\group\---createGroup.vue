<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000" :maxHeight="800">
      <template slot="body">
        {{ deptDetail.Id }} {{ deptDetail.MachineNo }}
        <el-form :rules="rules" label-position="right" ref="formData" :model="formData" :label-width="labelWidth" v-loading='loading'>
          <div class="wrapper" v-loading='loading'>
            <div class="lft" :style="{maxHeight: (windowHeight - 200) + 'px'}">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="机器号">
                    {{ deptDetail.MachineNo }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="考勤组名称" prop="Name">
                    <el-input maxlength="15" :disabled="!editable" v-model="formData.Name"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="考勤方式">
                    <el-radio :disabled="!editable" v-for='(item, idx) in attendanceTypes' :key="idx" v-model="formData.AttendanceType" :label="item.value">{{ item.label }}</el-radio>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="formData.AttendanceType == 2">
                <el-col :span="24">
                  <el-form-item label="排班管理员">
                    <emp-selector key="btnAddSelector" :readonly="!editable" :showType="2" :multiple="true" :beforeConfirm='handleAddBeforeConfirm' :list="formData.SchedulManagerEmployeeList" @change="handleChangeEmployeeList">
                    </emp-selector>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="23">
                  <el-form-item label="上班时间" prop="WorkStartRange">
                    <el-time-picker format='HH:mm' value-format='HH:mm' is-range v-model="formData.WorkStartRange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
                    </el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row>
                        <el-col :span="12">
                            <el-form-item label="上班时间" prop="WorkStartTime">
                                {{ formData.WorkStartTime }}
                                <el-time-select style="width: 100px;" :disabled="!editable" v-model="formData.WorkStartTime" :picker-options="{start: '00:00', step: '00:30', end: '23:30'}" placeholder=""></el-time-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="下班时间" prop="WorkEndTime">
                                {{ formData.WorkEndTime }}
                                <el-time-select style="width: 100px;" :disabled="!editable" v-model="formData.WorkEndTime" :picker-options="{start: '00:00', step: '00:30', end: '23:30'}" placeholder=""></el-time-select>
                            </el-form-item>
                        </el-col>
                    </el-row> -->
              <el-row>
                <el-col :span="24" class="spe3">
                  <el-form-item label="休息时段" prop="LunchBreakRange">
                    <el-time-picker format='HH:mm' value-format='HH:mm' is-range v-model="formData.LunchBreakRange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
                    </el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="工作时长">
                    <span :style="{color: workTime < 0 ? 'red' : ''}">
                      {{ workTime | minuteFilter }}
                    </span>
                    <!-- <el-tooltip effect="light" content="工作时长：工作时间 - 休息时间" placement="right">
                                    <span style="color: #c5cad7;">
                                        <i class="el-icon-question"></i>
                                    </span>
                                </el-tooltip> -->
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="spe3">
                  <el-form-item label="工作日加班">
                    <el-switch :disabled="!editable" v-model="formData.WorkdayOvertimeIsTrue" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
                  </el-form-item>
                  <el-form-item label="加班至" prop="WorkdayOvertime" v-if="formData.WorkdayOvertimeIsTrue">
                    <el-time-select :disabled="!editable" style="width: 100px;" placeholder="" v-model="formData.WorkdayOvertime" :picker-options="{start: '00:00', step: '00:30', end: '23:30'}"></el-time-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="加班规则">
                    <el-radio :disabled="!editable" v-for='(item, idx) in workOvertimeRules' :key="idx" v-model="formData.WorkOvertimeRules" :label="item.value">{{ item.label }}</el-radio>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="spe3">
                  <el-form-item label="缺卡不计缺勤">
                    <el-switch :disabled="!editable" v-model="formData.AllowLackCard" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
                  </el-form-item>
                  <el-form-item class="m-l-40" label="" v-if="formData.AllowLackCard">
                    <el-checkbox :disabled="!editable" v-model="formData.AllowLackCardForenoon">上班时间</el-checkbox>
                    <el-checkbox :disabled="!editable" v-model="formData.AllowLackCardAfternoon">下班时间</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="spe3">
                  <el-form-item label="外勤打卡">
                    <el-switch :disabled="!editable" v-model="formData.AllowFieldCard" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" v-if="formData.AllowFieldCard">
                  <el-form-item label="打卡地点">
                    <el-radio :disabled="!editable" v-for='(item, idx) in fieldCardAssignedPlace' :key="idx" v-model="formData.FieldCardAssignedPlace" :label="item.value">{{ item.label }}</el-radio>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="formData.AllowFieldCard && formData.FieldCardAssignedPlace==2">
                <el-col :span="24">
                  <el-button type="text" v-if="formData.TimecardPositions.length<3" @click="handleShowMap" :disabled="!editable">添加打卡地点</el-button>
                  <app-table-core :multable='false' :tab-columns="tabColumns2" :tab-datas="formData.TimecardPositions">

                    <template slot="Range" slot-scope="scope">
                      {{ scope.row.Range | statusFilter }}
                    </template>

                    <template slot-scope="scope">
                      <app-table-row-button :disabled='!editable' @click="handleTableUpdate(scope.row, scope.index-1)" :type="1"></app-table-row-button>
                      <app-table-row-button :disabled='!editable' @click="handleReview(scope.row, scope.index-1)" :type="3"></app-table-row-button>
                    </template>
                  </app-table-core>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="弹性打卡">
                    <div class="spe2">
                      <el-checkbox :disabled="!editable" v-model="formData.ElasticRules1" @change='(isTrue) => handleChagne(isTrue, 1)'>允许晚到晚走、早到早走</el-checkbox>
                    </div>
                    <div class="spe2">
                      <el-checkbox :disabled="!editable" v-model="formData.ElasticRules2" @change='(isTrue) => handleChagne(isTrue, 2)'>晚到、早走几分钟不计为异常</el-checkbox>
                    </div>
                    <div style="margin-bottom: 6px;" v-if="formData.ElasticRules2">
                      上班最多可晚到：
                      <el-select :disabled="!editable" v-model="formData.ElasticWorkStartTimeLate" placeholder="">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </div>
                    <div v-if="formData.ElasticRules2">
                      下班最多可早走：
                      <el-select :disabled="!editable" v-model="formData.ElasticWorkEndTimeEarly" placeholder="">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row class="spe">
                <el-col :span="24">
                  <el-form-item label="考勤申诉设置">
                    <el-radio :disabled="!editable" v-for='(item, idx) in flowTypes' :key="idx" v-model="formData.FlowType" :label="item.value">{{ item.label }}</el-radio>
                  </el-form-item>
                </el-col>
              </el-row>
              <div v-show="formData.FlowType == 2">
                <approval-panel :showHead="false" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
              </div>
            </div>
            <div class="rht">
              <div class="rht-content">
                <div class="opt-wrapper">
                  <div>
                    考勤对象
                    <emp-selector key="btnAddSelector" :showType="2" :multiple="true" :list="formData.TimecardEmployeeList" :beforeOpen='handleAdd' @change="handleChangeUsers" :disabledList="disabledList">
                      <el-button :disabled="!editable" slot="reference" type="primary">添加考勤对象</el-button>
                    </emp-selector>
                    <el-button style="margin-left: 4px;" :disabled="!editable" type="danger" @click="handleRemove">批量移除人员</el-button>
                  </div>
                  <div class="btns-area" v-if="formData.AttendanceType == 2 && editable">
                    <el-button type="text" @click="openDialogSchedules">人员排班</el-button>
                  </div>
                </div>

                <div class="orderListWrapper">
                  <app-table-core class="orderList" ref="mainTable" :tab-columns="tabColumns" :tab-datas="formData.TimecardEmployeeList" :tab-auth-columns="[]" :isShowAllColumn="false" :isShowOpatColumn="false" :startOfTable="0" :multable="editable" :max-height='windowHeight - 200 - 30' @rowSelectionChanged="rowSelectionChanged">
                    <template slot="TimecardNo" slot-scope="scope">
                      <el-form-item :prop="'TimecardEmployeeList.' + scope.index + '.TimecardNo'" :rules="{required: true, message: '考勤编号不能为空', trigger: 'blur'}">
                        <el-input maxlength="9" :disabled="!editable" v-model.trim="scope.row.TimecardNo"></el-input>
                      </el-form-item>
                    </template>
                  </app-table-core>
                </div>
              </div>
            </div>

          </div>
        </el-form>
        <template v-if="dialogFormVisibleMap">
          <map-detail @closeDialog='closeDialogMap' @saveSuccess='handleSaveSuccessMap' v-if="dialogFormVisibleMap" :dialogFormVisible='dialogFormVisibleMap' :dataSource='dataSource'></map-detail>
        </template>
        <schedules-page @closeDialog='closeDialogSchedules' @saveSuccess='handleSaveSuccessSchedules' :dialogFormVisible='dialogFormVisibleSchedules' v-if="dialogFormVisibleSchedules" :dialogStatus='dialogStatusSchedules' :oldSchedules='formData.timecardSchedulingList || []' :empIds='formData.TimecardEmployeeList.map(s => s.EmployeeId) || []' :timecardDepartmentId='id'>
        </schedules-page>
      </template>
      <template slot="footer">

        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>

      </template>

    </app-dialog>
  </div>
</template>

<script>
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import approvalPanel from '../../../projectDev/projectMgmt/common/approvalPanel'
import empSelector from '../../../common/empSelector'
import mapDetail from './mapDetail'
import schedulesPage from './schedulesDialog'
import dayjs from 'dayjs'
import { vars } from '../vars'
import { getChangedData } from './dataUtils'
import { regs } from "@/utils/regs"
import AppTableCore from '../../../../components/AppTable/AppTableCore.vue'
let Dec = require("decimal.js");

export default {
  name: "attendanceMgmt-group-createGroup",
  directives: {},
  components: {
    approvalPanel,
    empSelector,
    schedulesPage,
    mapDetail,
  },
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
    },
    deptDetail: {
      type: Object,
      require: true
    },
    id: {
      type: String,
      default: "",
    },
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建考勤组";
      } else if (this.dialogStatus == "update") {
        return "编辑考勤组";
      } else if (this.dialogStatus == "detail") {
        return "考勤组详情";
      }
    },
    workTime() {
      if (this.formData.WorkStartRange && this.formData.WorkStartRange.length == 2) {
        let workStartTime = this.formData.WorkStartRange[0]
        let workEndTime = this.formData.WorkStartRange[1]
        if (workStartTime && workEndTime) {
          let startTime = dayjs(`2020-1-1 ${workStartTime}:00`)
          let endTime = dayjs(`2020-1-1 ${workEndTime}:00`)
          let result = endTime.diff(startTime, 'm') //工作时间分钟

          if (this.formData.LunchBreakRange && this.formData.LunchBreakRange.length == 2) {
            let lunchBreakStartTime = this.formData.LunchBreakRange[0]
            let lunchBreakEndTime = this.formData.LunchBreakRange[1]
            if (lunchBreakStartTime && lunchBreakEndTime) {
              let temp1 = dayjs(`2020-1-1 ${lunchBreakStartTime}:00`)
              let temp2 = dayjs(`2020-1-1 ${lunchBreakEndTime}:00`)
              let temp = temp2.diff(temp1, 'm')
              result = new Dec(result).sub(new Dec(temp))
            }
          }
          return result;
        }
      }
      return '-'
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.resetFormData();
          if (this.dialogStatus != "create" && this.id) {
            this.getDetail();
          }
        }
      },
      immediate: true
    },

  },
  created() {
    this.rules = this.initRules(this.rules);

  },
  filters: {
    statusFilter(status) {
      let obj = vars.rangeValue.find(s => s.value == status)
      if (obj) {
        return obj.label
      }
      return status
    },
  },
  data() {
    return {
      windowHeight: 0,//窗口高度
      dialogFormVisibleMap: false,
      flowTypes: vars.flowTypes, //考勤申诉设置
      attendanceTypes: vars.attendanceTypes, //考勤方式
      workOvertimeRules: vars.workOvertimeRules, //加班规则
      fieldCardAssignedPlace: vars.fieldCardAssignedPlace, //打卡地点
      onOffShiftTime: vars.onOffShiftTime, //上下/班时间

      options: [
        { label: '0分钟', value: 0 },
        { label: '5分钟', value: 5 },
        { label: '10分钟', value: 10 },
        { label: '15分钟', value: 15 },
        { label: '20分钟', value: 20 },
        { label: '30分钟', value: 30 },
      ],
      pers: [],
      disabledList: [],
      tabColumns2: [{
        attr: {
          prop: "Address",
          label: "地点名称"
        }
      },
      {
        attr: {
          prop: "Remark",
          label: "备注",
        }
      },
      {
        attr: {
          prop: "Range",
          label: "有效范围",
        },
        slot: true
      },
      ] /**表格加载 */,
      tabColumns: [
        {
          attr: { prop: "Name", label: "姓名" },
        },
        {
          attr: { prop: "Number", label: "工号" },
        },
        {
          attr: { prop: "DepartmentName", label: "部门", showOverflowTooltip: true },
        },
        {
          attr: { prop: "TimecardNo", label: "考勤号码", renderHeader: this.renderHeader, width: '95' },
          slot: true
        },

      ],

      multipleSelection: [],

      dialogFormVisibleSchedules: false,
      dialogStatusSchedules: 'create',

      loading: false,
      disabledBtn: false,
      rules: {
        Name: { fieldName: "考勤组名称", rules: [{ required: true, trigger: "change" }], },
        WorkStartRange: { fieldName: "上班时间", rules: [{ required: true, trigger: "change" }], },
        // WorkStartTime: { fieldName: "上班时间", rules: [{ required: true, trigger: "change" }],},
        // WorkEndTime: { fieldName: "下班时间", rules: [{ required: true, trigger: "change" }],},
        LunchBreakRange: { fieldName: "休息时段", rules: [{ required: true, trigger: "change" }], },
        // LunchBreakStartTime: { fieldName: "开始时间", rules: [{ required: true, trigger: "change" }],},
        // LunchBreakEndTime: { fieldName: "结束时间", rules: [{ required: true, trigger: "change" }],},
        WorkdayOvertime: { fieldName: "加班至", rules: [{ required: true, trigger: "change" }], },
        shiftTime_minute: { fieldName: "分钟", rules: [{ required: true }, { reg: regs.phoneAndTel }] },
        shiftTime_hour: { fieldName: "小时", rules: [{ required: true }, { reg: regs.PositiveIntegers }] },
      },
      labelWidth: "100px",
      formData: {
        Id: "", //
        TimecardGroupId: '',
        Name: "", //
        MachineNo: "", //
        WorkStartRange: ['09:30', '18:30'],
        // WorkStartTime: "",
        // WorkEndTime: "",
        LunchBreakTime: "",
        LunchBreakRange: ['12:30', '14:00'],
        // LunchBreakStartTime: "",
        // LunchBreakEndTime: "",
        WorkingHours: 0,
        WorkdayOvertime: "",
        WorkdayOvertimeIsTrue: false,
        ElasticRules1: false,
        ElasticRules2: false,

        AllowLackCard: false,
        AllowLackCardForenoon: false,
        AllowLackCardAfternoon: false,

        FlowType: 1,
        ElasticWorkStartTimeLate: 5,
        ElasticWorkEndTimeEarly: 0,
        TimecardEmployeeList: [],

        AttendanceType: 1, //考勤方式
        AllowFieldCard: false, //外勤打卡
        WorkOvertimeRules: 1, //加班规则
        FieldCardAssignedPlace: 1, //打卡地点
        OnOffShiftTime: 1, //上/下班时间
        SchedulManagerEmployeeList: [], //排班管理员
        ShiftTime: "",
        shiftTime_hour: "",
        shiftTime_minute: "",
        /**
         * 如何维护：保存时，只提交“考勤对象（TimecardEmployeeList）”中的用户，且被“修改后”的排班数据
         * ————表示只提交排班被改动的数据
         */
        timecardSchedulingList: [], //排班列表
        TimecardPositions: [],//打卡地点

        Approval: {
          ApprovalEmployeeIdList: [[]],
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          CCEmployeeIdList: [],
          CCEmployeeList: []
        }
      },
      dataSource: {}
    };
  },
  mounted() {
    window.addEventListener('resize', this.setDialogHeight);
    this.$nextTick(() => {
      this.setDialogHeight()
    })

  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setDialogHeight);
  },
  methods: {
    setDialogHeight() {
      // this.dialogHeight = this.$refs.formData.$el.offsetHeight
      this.windowHeight = document.body.clientHeight
    },
    handleRemove2(item) {
      // this.$confirm("是否确认删除", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      // }).then(() => {
      //     let result = null
      //     if(this.currentType == 1) {
      //         result = equUse.del([item.Id])
      //     }else{
      //         result = equMode.del([item.Id])
      //     }
      //     result.then(res => {
      //         this.getList()
      //         this.$notify({
      //             title: "提示",
      //             message: "删除成功",
      //             type: "success",
      //             duration: 2000
      //         });
      //     })
      // });
    },
    handleShowMap() {
      this.dataSource = null
      this.dialogFormVisibleMap = true
    },
    closeDialogMap() {
      this.dialogFormVisibleMap = false
    },
    handleSaveSuccessMap(data, index) {
      data.TimecardDepartmentsId = this.formData.Id
      if (index != null) {
        this.formData.TimecardPositions.splice(index, 1, data)
      } else {
        this.formData.TimecardPositions.push(data)
      }
    },
    handleTableUpdate(row, idx) {
      this.dataSource = row
      this.dataSource.CurrentIndex = idx
      this.dialogFormVisibleMap = true
    },
    handleReview(row, idx) {
      this.$confirm("是否确认删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formData.TimecardPositions.splice(idx, 1)
      });
      // this.formData.TimecardPositions
    },
    handleAddBeforeConfirm(users) {
      if (users && users.length > 10) {
        this.$message({
          message: '排班管理员不得超过10人',
          type: 'error'
        })
        return false
      }

      return true
    },
    handleChagne(isChecked, type) {
      if (isChecked) {
        if (type == 1) {
          this.formData.ElasticRules2 = false
        } else {
          this.formData.ElasticRules1 = false
        }
      }
    },
    resetFormData() {
      let temp = {
        Id: "", //
        TimecardGroupId: '',
        Name: "", //
        MachineNo: "", //
        WorkStartRange: ['09:30', '18:30'],
        // WorkStartTime: "",
        // WorkEndTime: "",
        LunchBreakTime: "",
        LunchBreakRange: ['12:30', '14:00'],
        // LunchBreakStartTime: "",
        // LunchBreakEndTime: "",
        WorkingHours: 0,
        WorkdayOvertime: "",
        WorkdayOvertimeIsTrue: false,
        ElasticRules1: false,
        ElasticRules2: false,

        AllowLackCard: false,
        AllowLackCardForenoon: false,
        AllowLackCardAfternoon: false,

        FlowType: 1,
        ElasticWorkStartTimeLate: 5,
        ElasticWorkEndTimeEarly: 0,
        TimecardEmployeeList: [],

        AttendanceType: 1, //考勤方式
        AllowFieldCard: false, //外勤打卡
        WorkOvertimeRules: 1, //加班规则
        FieldCardAssignedPlace: 1, //打卡地点
        OnOffShiftTime: 1, //上/下班时间
        SchedulManagerEmployeeList: [], //排班管理员
        timecardSchedulingList: [], //排班列表
        TimecardPositions:[],//外勤打卡地址

        Approval: {
          ApprovalEmployeeIdList: [[]],
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          CCEmployeeIdList: [],
          CCEmployeeList: []
        }


      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    createData() {

      let validate = this.$refs.formData.validate()
      let approvalPanelValidate = this.formData.FlowType == 2 ? this.$refs.approvalPanel.validate() : new Promise((resolve, reject) => { resolve(true) })

      Promise.all([validate, approvalPanelValidate]).then(valid => {
        this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块
        let postData = JSON.parse(JSON.stringify(this.formData))

        if (postData.WorkStartRange.length == 2) {
          postData.WorkStartTime = postData.WorkStartRange[0]
          postData.WorkEndTime = postData.WorkStartRange[1]
        }

        postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
        postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
        if (postData.SchedulManagerEmployeeList && postData.SchedulManagerEmployeeList.length > 0) {
          postData.SchedulManagerEmployeeIdList = postData.SchedulManagerEmployeeList.map(e => e.EmployeeId)
        }

        let muns = postData.TimecardEmployeeList.map(s => s.TimecardNo.trim())

        let isDup = (new Set(muns)).size != muns.length
        if (isDup) {
          this.$message({
            message: '考勤编号存在重复，请修改后再提交',
            type: 'error'
          })
          return false
        }

        if (this.workTime < 0) {
          this.$message({
            message: '工作时间设置有误，请重新设置',
            type: 'error'
          })
          return false
        }

        //午休时间
        if (this.dialogStatus == 'create') {
          postData.TimecardGroupId = this.deptDetail.Id
        }

        //工作日加班（否）
        if (!postData.WorkdayOvertimeIsTrue) {
          //清空”加班至“
          postData.WorkdayOvertime = ''
        }

        if (postData.LunchBreakRange && postData.LunchBreakRange.length == 2) {
          postData.LunchBreakTime = `${postData.LunchBreakRange[0]}-${postData.LunchBreakRange[1]}`
        }
        postData.TimecardEmployeeList = postData.TimecardEmployeeList.map(s => {
          return {
            EmployeeId: s.EmployeeId,
            TimecardNo: s.TimecardNo
          }
        })

        postData.ShiftTime = postData.shiftTime_hour + ":" + postData.shiftTime_minute
        

        postData.timecardSchedulingList = getChangedData(postData.timecardSchedulingList)

        let result = null

        if (this.dialogStatus == 'create') {
          delete postData.Id
          result = timecardDepartment.add(postData)
        } else {
          result = timecardDepartment.edit(postData)
        }

        this.disabledBtn = true
        result.then(res => {
          this.disabledBtn = false
          this.$notify({
            title: "提示",
            message: "保存成功",
            type: "success",
            duration: 2000
          });

          this.$refs.appDialogRef.createData(result)
        }).catch(err => {
          this.disabledBtn = false
        })
      })
    },
    // getTimecardDepartmentEmployee
    async handleAdd() {
      let result = false
      if (this.selectTypeId != -1) {
        let postData = []
        //如果是编辑，需要排除当前组（所包含的用户）
        if (this.dialogStatus == 'update') {
          postData.push(this.formData.Id)
        }
        await timecardDepartment.getTimecardDepartmentEmployee(postData).then(res => {
          this.disabledList = res || []
          result = true
        })
      } else {
        this.$message({
          message: "该节点不可添加人员",
          type: "error"
        });
      }
      return result
    },

    getDetail() {
      this.loading = true
      timecardDepartment.detail({ id: this.id }).then((res) => {
        this.loading = false

        let result = {}
        for (let key of Object.keys(this.formData)) {
          result[key] = res[key] || this.formData[key]
        }
        result.WorkStartRange = [res.WorkStartTime, res.WorkEndTime]
        if (result.ShiftTime) {
          let times = result.ShiftTime.split(':')
          result.shiftTime_hour = times[0]
          result.shiftTime_minute = times[1]
        } else {
          result.shiftTime_hour = "";
          result.shiftTime_minute = "";
        }

        let temps = result.LunchBreakTime.split('-')
        if (temps.length == 2) {
          result.LunchBreakRange = temps
        }

        //如果”加班至“不为空
        this.$set(result, 'WorkdayOvertimeIsTrue', !!result.WorkdayOvertime)

        this.formData = result

      }).catch(err => {
        this.loading = false
      });
    },
    handleRemove() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: '请选择需要移除的项目',
          type: 'error'
        })
        return false
      }

      this.$confirm('是否确认移除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(element => {
          let idx = this.formData.TimecardEmployeeList.findIndex(s => s.EmployeeId == element.EmployeeId)
          if (idx != -1) {
            this.formData.TimecardEmployeeList.splice(idx, 1)
          }
        });
      })
    },
    handleChangeEmployeeList(users) {
      this.formData.SchedulManagerEmployeeList = users
    },
    handleChangeUsers(users) {
      users = users || []
      if (users.length == 0) {
        this.formData.TimecardEmployeeList = []
      } else {
        let oldUsers = this.formData.TimecardEmployeeList.map(s => s.EmployeeId)

        let newUser = users.filter(s => oldUsers.findIndex(o => o == s.EmployeeId) == -1)

        if (newUser && newUser.length > 0) {
          newUser.forEach(n => {
            let temp = JSON.parse(JSON.stringify(n))
            //由于考勤机器不能识别工号前面的0，所以需要去掉
            let tempNumber = n.Number.replace(/\b(0+)/gi, "")
            this.$set(temp, 'TimecardNo', tempNumber)
            this.formData.TimecardEmployeeList.push(temp)
          })
        }
      }
    },
    openDialogSchedules() {
      this.dialogFormVisibleSchedules = true
    },
    closeDialogSchedules() {
      this.dialogFormVisibleSchedules = false
    },
    handleSaveSuccessSchedules(newSchedules) {
      this.formData.timecardSchedulingList = newSchedules
      this.closeDialogSchedules()
    },
    renderHeader(h, { column }) {
      return h("span", [
        h(
          "span",
          {
            style: "color: red"
          },
          "* "
        ),
        h("span", column.label)
      ]);
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  },
};
</script>

<style scoped>
.spe2 >>> label {
  font-weight: 300 !important;
}

.spe >>> .el-form-item {
  margin-bottom: 0 !important;
}

.spe3 {
  display: flex;
}

.spe3 >>> .m-l-40 .el-form-item__content {
  margin-left: 40px !important;
}

.spe3 >>> .m-l-0 .el-form-item__content {
  margin-left: 0 !important;
}

.spe3 >>> .m-l-0 .el-form-item__error {
  left: 24px !important;
}

.orderList >>> .el-form-item__content {
  margin-left: 0 !important;
}

/* .box-card >>> .el-card__body{
    padding: 10px 10px 0!important;
} */

.orderList >>> .el-form-item {
  margin-bottom: 0 !important;
}
.orderList >>> .is-error .vue-treeselect__control {
  border: 1px solid red !important;
}
</style>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .lft {
    width: 50%;
    padding: 0 10px;
    border-right: 1px solid #dcdfe6;
    overflow-y: auto;
    box-sizing: border-box;
  }
  .rht {
    width: 50%;
    padding-left: 10px;
    height: 100%;
    .rht-content {
      display: flex;
      flex-direction: column;
      .orderListWrapper {
        flex: 1;
        overflow-y: auto;
      }
      .opt-wrapper {
        display: flex;
        .btns-area {
          flex: 1;
          text-align: right;
          padding-right: 10px;
        }
      }
    }
  }
}
</style>
