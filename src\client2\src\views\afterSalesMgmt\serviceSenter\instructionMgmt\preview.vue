<template>

    <div class="wrapper">
        <img class="bg-top" src="../../../../assets/images/phone-top.png" alt="">

        <el-carousel ref="rowCarousel" height="700px" direction="vertical" :autoplay="false" :loop='false' :initial-index='currentPage.y'>
            <el-carousel-item v-for="(rows, rowIdx) in datas" :key="`row_${rowIdx}`">
                <el-carousel :ref="`colCarousel${rowIdx}`" height="700px" direction="horizontal" :autoplay="false" :loop='false' :initial-index='currentPage.x'>
                    <el-carousel-item v-for="(col, rowIdx) in rows" :key="`row_${rowIdx}_col_${rowIdx}`">
                        <div class="phone-page-content">
                            <div>
                                {{ col.PageTitle }}
                            </div>
                            <div class="ql-editor">
                                <div v-html="col.Content"></div>
                            </div>
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </el-carousel-item>
        </el-carousel>

        <div class="direction-btns">
            <img v-show="currentPage.x > 0" src="../../../../assets/images/left.png" @click="handleMove('left')" alt="" srcset="">
            <img v-show="currentPage.y > 0" src="../../../../assets/images/up.png" @click="handleMove('up')" alt="" srcset="">
            <img v-show="currentPage.x < datas[currentPage.y].length - 1" src="../../../../assets/images/right.png" @click="handleMove('right')" alt="" srcset="">
            <img v-show="currentPage.y < datas.length - 1" src="../../../../assets/images/down.png" @click="handleMove('bottom')" alt="" srcset="">
        </div>

        <div class="nav-panel" v-show="showNavPanel">
            <div class="title">
                <span>快速导航</span>
                <i @click="() => showNavPanel = false" class="el-icon-close btn-close"></i>
            </div>
            <div class="content">
                <div class="detail-wrapper">
                    <div class="row" :style="{marginBottom: '10px'}" v-for="(rows, rowIdx) in datas" :key="`rowIdx_${rowIdx}`">
                        <div class="col" :title="cols.PageName" :class="{active: isCurrentPoint(rowIdx, colIdx)}" @click="changeCurrentPoint({x: colIdx, y: rowIdx})" :style="{marginRight: '10px'}" v-for="(cols, colIdx) in rows" :key="`rowIdx_${colIdx}`">
                            <div class="omit">
                                {{ cols.PageName }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="nav-wrapper">
            <span @click="() => showNavPanel = true">导航</span>
        </div>
    </div>

</template>

<script>

export default {
    name: "enterpriseIntroduction-preview-dialog",
    props: {
        //二维数组
        datas: {
            type: Array,
        },
    },
    data() {
        return {
            //当前选中二维数组坐标
            currentPage: {x: 0, y: 0},

            showNavPanel: false,

        
        };
    },
    methods: {
        isCurrentPoint(rowIdx, colIdx) {
            return rowIdx == this.currentPage.y && colIdx == this.currentPage.x
        },
        changeCurrentPoint({x, y}) {
            this.currentPage = {x, y}
            this.resetRowIdx(y)
            this.resetColIdx(y, x)
        },
        resetRowIdx(rowIdx) {
            let ref = this.$refs.rowCarousel
            this.currentPage.y = rowIdx
            ref && ref.setActiveItem(this.currentPage.y)
        },
        resetColIdx(rowIdx, colIdx) {
            let ref = this.$refs[`colCarousel${rowIdx}`][0]
            if(ref) {
                this.currentPage.x = colIdx
                ref.setActiveItem(this.currentPage.x)
            }
        },
        handleMove(direction) {
            switch(direction) {
                case "up":
                    if(this.currentPage.y > 0) {
                        let rowIdx = this.currentPage.y - 1
                        this.resetRowIdx(rowIdx)
                        this.resetColIdx(rowIdx, 0)
                    }
                    break;
                case "right":
                    if(this.currentPage.x < this.datas[this.currentPage.y].length - 1) {
                        let colIdx = this.currentPage.x + 1
                        this.resetColIdx(this.currentPage.y, colIdx)
                    }
                    break;
                case "bottom":
                    if(this.currentPage.y < this.datas.length - 1) {
                        let rowIdx = this.currentPage.y + 1
                        this.resetRowIdx(rowIdx)
                        this.resetColIdx(rowIdx, 0)
                    }
                    break;
                case "left":
                    if(this.currentPage.x > 0) {
                        let colIdx = this.currentPage.x - 1
                        this.resetColIdx(this.currentPage.y, colIdx)
                    }
                    break;
            }

        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style scoped>
.wrapper >>> .el-carousel__indicators{
    display: none;
}

.wrapper >>> .el-carousel__arrow{
    display: none;
}
</style>

<style lang='scss' scoped>
.wrapper{
    position: relative;
    height: 700px;
    width: 400px;
    margin: 0 auto;
    background: $text-primary;
    color: #fff;
    .bg-top{
        width: calc(100% - 6px);
        position: absolute;
        z-index: 999;
        left: 0;
        top: 0;
        right: 6px;
    }
    .direction-btns{
        width: 100%;
        position: absolute;
        z-index: 999;
        bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        img:not(:last-child) {
            margin-right: 14px;
        }
        img{
            height: 14px;
            width: 14px;
            opacity: .8;
        }
    }
    .nav-wrapper{
        position: absolute;
        display: flex;
        align-items: center;
        right: 0;
        bottom: 120px;
        // background: $text-primary;
        opacity: .6;
        border-radius: 18px 0 0 18px;
        padding: 6px 10px;
        z-index: 2000;
    }
    .nav-panel{
        position: absolute;
        z-index: 1000;
        left: 20px;
        right: 20px;
        top: 80px;
        bottom: 80px;
        // background: $bg-color-white;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        .title{
            display: flex;
            justify-content: space-between;
            padding: 10px;
            // color: $text-primary;
            // border-bottom: 1px solid $border-color-base;
            .btn-close{
                cursor: pointer!important;
            }
        }
        .content{
            flex: 1;
            overflow: hidden;
            padding: 10px;
            .detail-wrapper{
                box-sizing: border-box;
                overflow: auto;
                height: 100%;
                .row{
                    display: flex;
                    .col{
                        width: 120px; 
                        flex-shrink: 0;
                        background: #F1F4FC;
                        color: #30499c;
                        border-radius: 18px;
                        text-align: center;
                        padding: 10px;
                        position: relative;
                        cursor: pointer;
                        &.active{
                            // background: $color-primary;
                            // color: $bg-color-white;
                        }
                    }
                }
            }
        }
    }
}

.phone-page-content{
    width: 100%;
    height: 100%;
    margin: auto;
    padding: 10px;
    padding-top: 30px;
    box-sizing: border-box;
    overflow-y: auto;
    word-break: break-all;
}
</style>