<template>
  <div>
    <app-dialog title="审批设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :Height='500' :width='600' v-loading="loading">
      <template slot="body">
        <el-row>
          <el-col :span="24" style="border-bottom: 1px solid silver;font-weight: bold;padding: 10px 0 10px 25px;">
            <span><label style="color:red">*</label>审批：</span>
          </el-col>
        </el-row>

        <el-row style="margin-top:20px;margin-bottom:10px;font-weight: bold;">
          <el-col :span="24">
            <el-form ref="formData" :model="formData" label-width="100px" label-position="right">
              <div class="title-panel">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="流程类型">
                      <el-radio v-for='(item, idx) in flowTypes' :key="idx" v-model="formData.FlowType" :label="item.value">{{ item.label }}</el-radio>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <approval-panel :showHead="false" v-show="formData.FlowType == 2" ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
            </el-form>
          </el-col>
        </el-row>

      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="createData" :buttonType='1' text="保存"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as implementerManagement from '@/api/maintenanceCenter/implementerManagement'
import { vars } from '../../../projectDev/common/vars'
import approvalPanel from '../../../projectDev/projectMgmt/common/approvalPanel'

export default {
  name: "approvalSet",
  directives: {},
  components: {
    approvalPanel,
  },
  mixins: [],
  props: {},
  watch: {
    '$attrs.dialogFormVisible'(val) {
      this.formData = {
        FlowType: 1,
        Approval: {
          ApprovalEmployeeIdList: [[]],
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          CCEmployeeIdList: [],
          CCEmployeeList: []
        }
      };
      if (val) {
        this.getDetail()
      }
    }
  },

  created() { },
  data() {

    return {
      loading:false,
      flowTypes: vars.approvalFlowTypes,
      formData: {
        FlowType: 1,
        Approval: {
          ApprovalEmployeeIdList: [[]],
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          CCEmployeeIdList: [],
          CCEmployeeList: []
        }
      }

    };
  },
  methods: {

    getDetail() {
      this.loading=true;
      implementerManagement.getApprovalPrestore().then(res => {
        this.loading=false;
        if (!res.Approval) {
          res.Approval = {
            pprovalEmployeeIdList: [[]],
            ApprovalEmployeeList: [[]],
            ApprovalType: 1,
            CCEmployeeIdList: [],
            CCEmployeeList: []
          }
        }
        if (res) {
          this.formData = Object.assign({}, this.formData, res)
        }
      }).catch(err => {
        this.loading=false;
      })
    },

    createData() {
      this.formData.Approval = this.$refs.approvalPanel.getData() //审批层区块
      let postData = JSON.parse(JSON.stringify(this.formData))
      postData = Object.assign({}, postData)

      postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
      postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
      if (this.formData.FlowType == 2) {
        this.$refs.approvalPanel.validate().then(valid => {
          implementerManagement.setApprovalPrestore(postData).then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
          })
        })
      } else {
        implementerManagement.setApprovalPrestore(postData).then(res => {
          this.$notify({
            title: "提示",
            message: "保存成功",
            type: "success",
            duration: 2000
          });
        })
      }
      this.$refs.appDialogRef.createData()
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
