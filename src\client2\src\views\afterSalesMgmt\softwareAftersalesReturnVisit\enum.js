
export const vars = {
    types: [
        { label: '大小火', value: 1 },
        { label: '电子比', value: 2 },
        { label: '其他', value: 3 }
    ],
    paramTypes: [
        { label: '有', value: 1 },
        { label: '准确', value: 2 },
        { label: '不准确', value: 3 }
    ],
    cyclePeriodTypes: [
        { label: '不循环', value: 1 },
        { label: '每3天循环1次', value: 2 },
        { label: '每7天循环1次', value: 3 },
        { label: '每14天循环1次', value: 4 },
        { label: '每30天循环1次', value: 5 },
        { label: '自定义循环', value: 6 }
    ],
    remindTypes: [
        { label: '提前5分钟', value: 1 },
        { label: '提前15分钟', value: 2 },
        { label: '提前30分钟', value: 3 },
        { label: '提前1小时', value: 4 },
        { label: '提前2小时', value: 5 },
        { label: '提前3小时', value: 6 },
        { label: '提前6小时', value: 7 },
        { label: '提前12小时', value: 8 },
        { label: '提前24小时', value: 9 },
        { label: '提前48小时', value: 10 },
    ],
    notReturnVisitType: [
        {value: 1, label: '超过7天'},
        {value: 2, label: '超过15天'},
        {value: 3, label: '超过1个月'},
        {value: 4, label: '超过3个月'},
        {value: 5, label: '超过半年'},
    ],
    startOrStopFurnaceType: [
        {value: 1, label: '启炉', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)'}, // 蓝色
        {value: 2, label: '停炉', color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)'}, // 红色
    ],
	questionStatus: [
		{ value: 1, label: '待处理', bgColor: 'red', color: '#fff' },
		{ value: 2, label: '处理中', bgColor: '#00cc00', color: '#fff' },
		{ value: 3, label: '已处理', bgColor: '#409EFF', color: '#fff' },
	],
    salesAfterVisttTypes: [
        { value: 1, label: '正常回访' },
        { value: 2, label: '软件故障回访' },
        { value: 3, label: '硬件故障回访' },
    ]
}