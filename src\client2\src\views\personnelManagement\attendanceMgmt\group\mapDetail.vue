<template>
  <app-dialog title="编辑打卡地区" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1208">
    <template slot="body">
      <div class="g-wraper">
        <div class="m-part">
          <mapDrag @drag="dragMap" class="mapbox" :status="dataSource && dataSource.Id ? 'update' : 'create'" :lat="dragData.Lat" :lng="dragData.Lng" :range="dragData.Range"></mapDrag>
          <el-form ref="formData" :model="dragData" label-position="right" label-width="100px">
            <div class="wrapper">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="地址" prop="formatted_address">
                    <span maxlength="30">{{dragData.Address}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input maxlength="30" v-model="dragData.Remark"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="考勤坐标" prop="position">
                    <span maxlength="30">{{dragData.Position}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="有效范围">
                    <el-select v-model="rangeValues">
                      <el-option v-for='item in rangeValue' :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
      </div>
    </template>
    <template slot="footer">
      <!-- <el-button @click="handleClose">取消</el-button>
      <el-button @click="createData" type="primary" v-show="editable">确认</el-button>-->
      <!-- 取消 -->
      <app-button @click="handleClose" :buttonType="2"></app-button>
      <!-- 确认 -->
      <app-button @click="createData" :buttonType="1"></app-button>
    </template>
  </app-dialog>

</template>

<script>
import mapDrag from './mapDrag'
import { vars } from '../vars'
export default {
  name: 'index',
  components: {
    mapDrag,
  },
  props: {
    dataSource: {
      type: Object
    }
  },
  watch: {
    rangeValues: {
      handler(val) {
        if (val) {
          this.convertToNumber(this.rangeValues)
        }
      },
      immediate: true,
      deep: true
    },
  },
  data() {
    return {
      currentIndex: null,
      rangeValue: vars.rangeValue, //范围
      rangeValues: 1,

      dragData: {
        Id: null,
        Lng: 114.071045,
        Lat: 22.556195,
        Address: null,
        Remark: null,
        Position: null,
        Range: null

        // "Id": "b45850ba-8542-49b0-bd65-ce88e91e840c",
        // "Lng": 114.071045,
        // "Lat": 22.556195,
        // "Address": "广东省深圳市福田区华富街道深业上城写字楼B座",
        // "Remark": null,
        // "Position": "114.071045,22.556195",
        // "Range": 1000
      },
      formData: {
        Id: ""
      }
    }
  },
  created() {
    if (this.dataSource) {
      this.formData = JSON.parse(JSON.stringify(this.dataSource))
      this.currentIndex = this.formData.CurrentIndex
      this.dragData.Id = this.formData.Id
      this.dragData.Lng = this.formData.Lng
      this.dragData.Lat = this.formData.Lat
      this.dragData.Address = this.formData.Address
      this.dragData.Remark = this.formData.Remark
      this.dragData.Position = `${this.formData.Lng},${this.formData.Lat}`
      this.rangeValues = this.formData.Range
      this.convertToNumber(this.rangeValues)
    }
  },
  mounted() {

  },
  methods: {
    dragMap(data) {
      this.dragData.Lng = data.location.lon
      this.dragData.Lat = data.location.lat
      this.dragData.Address = data.formatted_address
      this.dragData.Position = data.location.lon + ',' + data.location.lat
      this.dragData.Range = data.range
    },
    createData() {
      this.formData = this.dragData;
      this.convertToEnum(this.formData.Range)

      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));
          this.$refs.appDialogRef.createData(postData, this.currentIndex);
        }
      });

      this.$refs.appDialogRef.handleClose();
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    convertToEnum(value) {
      if (value) {
        switch (value) {
          case 100:
            this.formData.Range = 1
            break;
          case 200:
            this.formData.Range = 2
            break;
          case 300:
            this.formData.Range = 3
            break;
          case 500:
            this.formData.Range = 4
            break;
          case 1000:
            this.formData.Range = 5
            break;
          case 2000:
            this.formData.Range = 6
            break;
          case 3000:
            this.formData.Range = 7
            break;
          case 5000:
            this.formData.Range = 8
            break;
        }
      }
    },
    convertToNumber(value) {
      if (value) {
        switch (value) {
          case 1:
            this.dragData.Range = 100
            break;
          case 2:
            this.dragData.Range = 200
            break;
          case 3:
            this.dragData.Range = 300
            break;
          case 4:
            this.dragData.Range = 500
            break;
          case 5:
            this.dragData.Range = 1000
            break;
          case 6:
            this.dragData.Range = 2000
            break;
          case 7:
            this.dragData.Range = 3000
            break;
          case 8:
            this.dragData.Range = 5000
            break;
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.g-wraper {
  margin: 0 auto;
  color: #666;
  font-size: 16px;
  line-height: 30px;
}
.m-part .mapbox {
  width: 100%;
  height: 500px;
  margin-bottom: 10px;
  margin-top: 10px;
}
.m-part .info {
  flex: 1;
  margin: 0;
  padding: 0;
  list-style: none;
  line-height: 30px;
}
.m-part .info span {
  display: block;
  color: #999;
  padding: 0 10px;
}
</style>