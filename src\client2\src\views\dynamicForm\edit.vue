<template>
    <el-dialog v-el-drag-dialog class="dialog-mini" width="1000px" :title="dialogTitle" :visible.sync="isVisible"
        :before-close="handleClose" :close-on-click-modal="false" :append-to-body="true">

        {{ treeData }}
        <el-form :rules="rules" ref="dataForm" id="dataForm" :model="treeData" label-position="right" label-width="100px">
            <div id="main-form-container">
                <!-- {{ treeData }} -->
                <el-form-item :label="'表单名称'" prop="formTitle">
                    <el-input :disabled="this.dialogStatus == 'detail'" v-model="treeData.formTitle" placeholder="请输入表单名称"></el-input>
                </el-form-item>
                <el-form-item :label="'表单编号'" prop="formSn">
                    <el-input :disabled="this.dialogStatus == 'detail'" v-model="treeData.formSn" placeholder="请输入表单编号"></el-input>
                </el-form-item>
                
                <!-- <div style="border: 1px solid blue;"> -->
                <tree-item
                    class="item"
                    :item="treeData.formContent"
                    @make-folder="makeFolder"
                    @add-item="addItem"
                    :displayModel='displayModel'
                >
                </tree-item>
                <!-- </div> -->
            </div>
        </el-form>
        <div slot="footer">
            <el-button size="mini" @click="handleClose">取消</el-button>
            <el-button size="mini" type="primary" v-show="dialogStatus == 'create' || dialogStatus == 'update'" :loading='postLoading' @click="saveChange">确认</el-button>
        </div>
    </el-dialog>
</template>

<script>

// var treeData = {
//     "id": "xxx-xxx-xxx",
//     "name": "跟节点",
//     "level": 1,
//     "type": "1",
//     "children": [
//         {
//             "type": 1,
//             "level": 2,
//             "children": [
//                 {
//                     "type": 3,
//                     "level": 3,
//                     "attrs": [
//                         {
//                             "type": 3,
//                             "label": "名称",
//                             "value": "asdfasdfas",
//                             "desc": "要在此表单元素上呈现的名称。"
//                         },
//                         {
//                             "type": 3,
//                             "label": "ID",
//                             "value": "sdfasdf",
//                             "desc": "要在此表单元素上呈现的ID。"
//                         },
//                         {
//                             "type": 5,
//                             "label": "类型",
//                             "value": "1002",
//                             "desc": "要在此表单元素上呈现的ID。",
//                             "options": [
//                                 { "value": "1000", "label": "文本" },
//                                 { "value": "1001", "label": "数字" },
//                                 { "value": "1002", "label": "电子邮件" },
//                                 { "value": "1003", "label": "电话" },
//                                 { "value": "1004", "label": "日期" },
//                                 { "value": "1005", "label": "时间" },
//                                 { "value": "1006", "label": "密码" }
//                             ]
//                         },
//                         {
//                             "type": 3,
//                             "label": "默认值",
//                             "value": "adfasdfasdf",
//                             "desc": "字段的默认值"
//                         }
//                     ]
//                 }
//             ]
//         }
//     ]
// }

import elDragDialog from "@/directive/el-dragDialog"
import TreeItem from './treeItem'
import mixin from '../dynamicFormCommon/mixins'
import { JsonHubProtocol } from '@aspnet/signalr'
import * as df from '@/api/dynamicForm'
// import request from '@/utils/request'
import axios from 'axios'

export default {
    name: 'edit-page',
    mixins: [mixin],
    components: {
        TreeItem,
    },
    directives: {
        elDragDialog
    },
    computed: {
        dialogTitle() {
            if (this.dialogStatus == "create") return "添加"
            else if (this.dialogStatus == "update") return "编辑"
            else if (this.dialogStatus == "detail") return "详情"
            else return ""
        },
        displayModel() {
            if(this.dialogStatus == 'create' || this.dialogStatus == 'update') {
                return 'build'
            }else if(this.dialogStatus == 'detail') {
                return 'review'
            }else{
                return ''
            }
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑)
        dialogStatus: {
            required: true,
            type: String
        },
        //新增(编辑)弹框是否显示
        dialogFormVisible: {
            required: true,
            type: Boolean
        },
        formObj: {
            type: Object,
            required: true
        },
    },    
    created() {
        this.rules = this.initRules(this.rules)
        this.isVisible = this.dialogFormVisible
    },
    watch: {
        isVisible(val) {
            this.treeData = JSON.parse(JSON.stringify(this.formObj))
        }
    },
    mounted() {
        
    },
    data() {
        return {
            isVisible: false,
            dialogSubFormVisible: false,
            rules: {
                formTitle: { fieldName: '表单名称', rules: [{ required: true }, { max: 100 }] },
                formSn: { fieldName: '表单编号', rules: [{ required: true }, { max: 100 }] },
            },
            postLoading: false,

            treeData: {
                id: '',
                formTitle: '',
                formSn: '',
                formContent: []
            },

        }
    },
    methods: {
        
        // removeItem(obj) {
            

        //     this.treeData.formContent.children[0].children.splice(0, 1)
            
        // },
        addItem(obj) {
            let ctrlType = obj.type
            let addCtrlObj = {
                id: obj.id,
                pid: obj.item.id,
                type: ctrlType, 
                level: obj.item.level + 1,
                value: '',
            }

            let attrs = this.controls.find(s => s.type == ctrlType).attrs
            if(attrs) {
                addCtrlObj.attrs = JSON.parse(JSON.stringify(attrs))
            }
            switch(ctrlType) {
                case 3:// input
                    
                break;
                case 5:// select

                break;
            }

            if(ctrlType == 1 || ctrlType == 2){
                addCtrlObj.children = []
            }
            if(!obj.item.children) {
                this.$set(obj.item, 'children', [])
            }
            obj.item.children.push(addCtrlObj)
        },
        makeFolder() {
            if (!this.isFolder) {
                this.$emit('make-folder', this.item)
                this.isOpen = true
            }
        },
        closeDialog() {
            this.dialogSubFormVisible = false
        },
        createData() {
            this.closeDialog()
        },
        saveChange() {
            // this.postLoading = true
            this.$refs['dataForm'].validate((valid) => {
                if(!valid) {
                    this.postLoading = false
                }
                if (valid) {
                    let postDatas = JSON.parse(JSON.stringify(this.treeData))

                    // let temp = JSON.stringify(this.treeData)
                    
                    postDatas.formContent = this.formStructDatasToString(postDatas.formContent)
                    let result = null
                    if(this.dialogStatus == 'update') {
                        result = df.edit(postDatas)
                    }else{
                        result = df.add(postDatas)
                    }

                    if(result) {
                        result.then(res => {
                            this.$notify({
                                title: '成功',
                                message: '保存成功',
                                type: 'success',
                                duration: 2000
                            })
                            this.handleClose()
                            this.postLoading = false
                            this.$emit("reloading");
                        }).catch(err => {
                            this.postLoading = false
                        })
                    }else{
                        this.postLoading = false
                    }
                }
            })
        },
        handleClose() {
            this.cancel()
        },
        cancel() {
            this.$emit("closeDialog");
        },        
    }
}
</script>

<style scoped>
    #main-form-container{
        overflow: auto;
    }
/* 
    #main-form-container >>> ul{
        list-style: none;
        padding: 0;
        border: 1px solid #e3e3e3;
        box-sizing: border-box;
    } */

    #main-form-container >>> .el-row{
        padding: 0;
        border: 1px solid #e3e3e3;
        box-sizing: border-box;
    }



</style>