$border-color: #000;
$font-size: 14px;
$font-size-big: 15px;
$print-margin: 5mm 5mm 5mm 35mm;
$padding: 5px;
$row-height: 35px;

.print_container {
  width: 240mm;
  min-height: 140mm;
  background: white;
  box-sizing: border-box;
  font-size: $font-size;
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
  position: relative;

  .print_title {
    text-align: center;
    margin-bottom: 5px;
    font-size: 24px;
    font-weight: bold;
  }

  .print_top {
    display: flex;
    justify-content: space-between;
    font-size: $font-size;
  }
  .print_table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    border: 2px solid $border-color;
    margin-top: 10px;
    text-align: center;
    font-size: $font-size;

    td {
      border: 1px solid $border-color;
      padding: $padding;
      height: $row-height;
      vertical-align: middle;
      font-size: $font-size;
    }

    .footer_row {
      min-height: $row-height;
      padding: $padding;
      text-align: left;
    }
  }

  .list_item {
    text-align: left;
    &:not(:last-of-type) {
      margin-bottom: 5px;
    }
  }
  .t_left {
    text-align: left;
  }
  .t_right {
    text-align: right;
  }
  .f_bold {
    font-weight: bold;
  }
  .f_big_text{
    font-size: $font-size-big;
  }
  .f_a_center {
    display: flex;
    align-items: center;
  }
  .f_j_b {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

/* 打印时的样式 */
@media print {
  @page {
    size: 240mm 140mm landscape;
    margin: $print-margin;
  }
  // html {
  // zoom: 70%;
  // }
  body {
    margin: 0;
    padding: 0;
  }
}
