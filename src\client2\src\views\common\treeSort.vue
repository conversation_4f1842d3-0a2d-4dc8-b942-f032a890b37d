<template>
    <div>
        <app-dialog title="排序" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600" :width='600'>
            <template slot="body">
                <div>
                    <el-tree class="elTree" 
                        v-loading="loading" 
                        ref="tree" 
                        :data="datas" 
                        :node-key="rowKey" 
                        :filter-node-method="() => {}" 
                        :props="treeConfig" 
                        :default-expand-all='defaultExpandAll'
                        :expand-on-click-node="false" 
                        :highlight-current="true" 
                        :check-on-click-node="true" 
                        :default-expanded-keys="defaultExpandedKeys"
                        @node-click="(data) => checkedNode = data">

                        <span class="custom-tree-node" style="width: 100%;" slot-scope="{ node, data }">
                            <div style="display: flex;">
                                <span class="node-title" :title="node.label">
                                    <span title="下移" v-show="isShowMoveBtn(node, 'down')" @click="handleCommand('down', node, data)">
                                        <svg-icon icon-class="arrow-circle-down"></svg-icon>
                                    </span>
                                    <span title="上移" v-show="isShowMoveBtn(node, 'up')" @click="handleCommand('up', node, data)">
                                        <svg-icon icon-class="arrow-circle-up"></svg-icon>
                                    </span>
                                    {{ node.label }}
                                </span>
                                <span class="node-btn-area">
                                </span>
                            </div>
                        </span>

                    </el-tree>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage"
import * as common from "@/api/common"

export default {
    name: "param-setting",
    directives: {},
    components: {
    },
    mixins: [indexPageMixin],
    props: {
        rowKey: {
            type: String,
            default: 'Id'
        },
        treeDatas: {
            type: Array,
            default: () => {
                return []
            }
        },
        /**
         * 1: 部门管理； 
         * 2: 设备用途；
         * 3：设备供风方式；
         * 4：文档（项目管理)
         * 5：地区管理（排序）
         * 6：研发管理，工作台，版本 排序
         * 7：员工手册树排序
         * 8：工程模板
         * 9：岗位技能管理
         * 
         * 2、3 非树形列表，没有使用此组件
         */
        businessType: {
            type: Number,
            required: true
        },
        //树配置
        defaultProps: {
            type: Object,
            required: true
        },
        //默认展开节点
        defaultExpandedKeys: {
            type: Array,
            default: () => {
                return []
            }
        },
        //是否展开全部节点
        defaultExpandAll: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val) {
                this.datas = JSON.parse(JSON.stringify(this.treeDatas)) || []
                this.treeConfig = JSON.parse(JSON.stringify(this.defaultProps)) || {}
            }
            
            //弹框关闭，如果有排序操作，则通知调用方
            if(!val && this.isChanged) {
                this.isChanged = false
                this.$emit("reload")
            }
        },
    },
    computed: {
        

    },
    created() {
    },
    data() {
        return {
            datas: [],
            treeConfig: {},
            checkedNode: null,
            loading: false,
            isChanged: false,
            
        };
    },
    methods: {
        //左侧树操作菜单
        handleCommand(direction, node, data) {
            let key = this.rowKey
            if(node && node.parent && node.parent.childNodes && node.parent.childNodes.length > 1) {
                let nodes = node.parent.childNodes || []
                let currentNodeKey = node.data[key]
                let currIdx = nodes.findIndex(s => s.data[key] == currentNodeKey)
                
                if ((direction == "up" && currIdx > 0) || (direction == "down" && currIdx < nodes.length - 1)) {
                    let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
                    let currRow = nodes[currIdx]
                    // let targetRow = nodes[targetIdx]

                    //准备提交真个列表集合数据（因为必须先提交保存成功，前端才能改变顺序，所以使用该临时变量）
                    let tempNodes = nodes.map(s => {
                        return s.data[this.rowKey]
                    })
                    let tempCurrRow = tempNodes[currIdx]
                    tempNodes.splice(currIdx, 1);
                    tempNodes.splice(targetIdx, 0, tempCurrRow);

                    let postDatas = {
                        setKeyValue: tempNodes.map((s, idx) => {
                            return {
                                Key: s,
                                OrderIndex: (idx + 1) * 10
                            }
                        }),
                        setSort: this.businessType
                    }

                    this.loading = true
                    common.sort(postDatas).then(res => {
                        this.isChanged = true
                        this.loading = false
                        nodes.splice(currIdx, 1);
                        nodes.splice(targetIdx, 0, currRow);
                    }).catch(err => {
                        this.loading = false
                    })
                }

            }

        },
        //节点是否显示上移、下移按钮
        isShowMoveBtn(node, direction) {
            let key = this.rowKey
            if(node && node.parent && node.parent.childNodes && node.parent.childNodes.length > 1) {
                let currentNodeKey = node.data[key]
                let brotherNodes = node.parent.childNodes || []
                let currIdx = brotherNodes.findIndex(s => s.data[key] == currentNodeKey)

                if(direction == 'down' && currIdx != brotherNodes.length - 1) {
                    return true
                }

                if(direction == 'up' && currIdx != 0) {
                    return true
                }

            }
            return false
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        
    }
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
    // flex: 1;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // font-size: 14px;
    // padding-right: 8px;
    display: block;
    position: relative;
    box-sizing: border-box;
    // padding-right: 24px;
    .node-title{
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .node-btn-area{
        padding-left: 4px;
        padding-right: 10px;
        flex: 1;
    }
}
</style>
