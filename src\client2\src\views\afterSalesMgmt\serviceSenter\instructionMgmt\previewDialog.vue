<template>
    <div>
        <app-dialog title="效果预览" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <preview :datas='datas'></preview>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import preview from './preview'
export default {
    name: "enterpriseIntroduction-preview-dialog",
    components: {
        preview,
    },
    props: {
        //二维数组
        datas: {
            type: Array,
        },
    },
    data() {
        return {
            
        
        };
    },
    methods: {
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>
