<template>
    <div class="supplierDetail">
        <app-dialog title="设备供应商详情" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='1200'
            :max-height='630'
        >
            <template slot="body">
                <div class="cl" style="border-bottom: 1px solid #EBEEF5;padding-bottom: 10px;">
                    <span class="fl" style="margin-top:5px;">设备年龄：{{ageOptions.find(v => v.value == equipmentValue).label}}</span>
                    <span class="fl" style="margin-top:5px;margin-left:30px;">设备数量：{{total}}</span>
                    <el-button class="fr" @click="handleExport" type="primary">导出</el-button>
                </div>
                <app-table
                  ref="mainTable"
                  :tab-columns="tabColumns"
                  :tab-datas="tabDatas"
                  :tab-auth-columns="tabAuthColumns"
                  :isShowAllColumn="true"
                  :loading="loading"
                  :isShowOpatColumn="false"
                  :startOfTable="startOfTable"
                  :multable="false"
                  :isShowBtnsArea="false"
                  :isShowConditionArea="false"
                >
                  <template slot="HeatNumber" slot-scope="scope">
                      {{ scope.row.HeatNumber ? scope.row.HeatNumber : '无' }}
                  </template>
                  <template slot="EquipmentWorkModeName" slot-scope="scope">
                      {{ scope.row.EquipmentWorkModeName ? scope.row.EquipmentWorkModeName : '无' }}
                  </template>
                  <template slot="BurnerModel" slot-scope="scope">
                      {{ scope.row.BurnerModel ? scope.row.BurnerModel : '无' }}
                  </template>
                  <template slot="Manufacturer" slot-scope="scope">
                      {{ scope.row.Manufacturer ? scope.row.Manufacturer : '无' }}
                  </template>
                  <template slot="InstallTime" slot-scope="scope">
                      {{ scope.row.InstallTime ? (scope.row.InstallTime.split(' ')[0]) : '无' }}
                  </template>
                </app-table>
                <pagination
                  v-show="total > 0"
                  :total="total"
                  :page.sync="listQuery.PageIndex"
                  :size.sync="listQuery.PageSize"
                  @pagination="handleCurrentChange"
                  @size-change="handleSizeChange"
                />
            </template>
            <template slot="footer">
                <el-button @click="handleClose" type="primary">关闭</el-button>
            </template>
        </app-dialog>
        <v-export
        @saveSuccess="handleSuccessExport"
        @closeDialog="handleCloseExport"
        :dialogFormVisible="dialogExportVisible"
        :rData="rData"
        :cData='cData'
        >
        </v-export>
    </div>
</template>
<script>
import vExport from "@/components/Export/index";
import indexPageMixin from "@/mixins/indexPage";
import * as businessMap from "@/api/businessMap";
import { vars } from "../../salesMgmt/common/vars";
export default{
    name:'supplierDetail',
    mixins: [indexPageMixin],
    components: {   
        vExport
    },
    props:{
        id:{
            type:String,
            default:''
        },
        equipmentValue:{
          type:Number,
          default:3
        }
    },
    data(){
        return{
          ageOptions:vars.equipmentAge,
            loading:false,
            dialogExportVisible:false,
            rData:null,
            cData:[],
            tabColumns: [
                {
                  attr: { prop: "RegionalName", label: "所在地区", showOverflowTooltip: true }
                },
                {
                  attr: { prop: "Name", label: "加热炉/锅炉", }
                },
                {
                  attr: { prop: "HeatNumber", label: "炉号", },
                  slot:true
                },
                
                {
                  attr: { prop: "EquipmentWorkModeName", label: "供风方式", },
                  slot: true
                },
                {
                  attr: { prop: "BurnerModel", label: "燃烧器型号", },
                  slot: true
                },
                {
                  attr: { prop: "Manufacturer", label: "生产厂家", },
                  slot: true
                },
                {
                  attr: { prop: "InstallTime", label: "投产时间", },
                  slot: true
                },
            ],
            tabDatas:[],
            listQuery: {
              PageIndex:1,
              PageSize:20,
              EquipmentAge:'',
              Name:'',
              RegionalId: "",
              OrderNumber: "",
              EquipmentUseId:"",
              EquipmentWorkModeId:"",
              //  InstallTime:"",
              Range:"",
              Manufacturer:"",
              BurnerModel:"",
              Code: "",
              // ProductListManagementId: null,
              // InstallTimeStart: "",
              // InstallTimeEnd: "",
              IsWarranty: "",
              OrderType: 1
            },
            total:0,
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                  this.listQuery.EquipmentAge=this.equipmentValue;
                  this.getList();
                }
            },
            immediate: true
        }
    },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
      getList() {
        this.loading = true;
        this.listQuery.RegionalId=this.id;
        let postData = JSON.parse(JSON.stringify(this.listQuery));
        businessMap.getList(postData).then(res => {
          this.loading = false;
          this.tabDatas = res.Items;
          this.total = res.Total;
        });
      },
      handleCurrentChange(val) {
        this.listQuery.PageIndex = val.page;
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      handleSizeChange(val) {
        this.listQuery.PageSize = val.size;
        this.getList();
      },
        handleSuccessExport() {},
        handleCloseExport() {
        this.dialogExportVisible = false;
        },
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        handleExport() {
        this.rData={
          "exportSource": 16,
          "columns": [],
          "searchCondition":this.listQuery
        }
      this.cData=[{
        label:'序号',
        value:'Number'
      },{
        label:'地区',
        value:'RegionalName'
      },{
        label:'加热炉/锅炉',
        value:'Name'
      },{
        label:'炉号',
        value:'HeatNumber'
      },{
        label:'供风方式',
        value:'EquipmentWorkModeName'
      },{
        label:'设备编号',
        value:'Code'
      },{
        label:'订单号',
        value:'OrderNumber'
      },{
        label:'加热炉功率',
        value:'HeatFurnaceRatework'
      },{
        label:'燃料类型',
        value:'FuelType'
      },{
        label:'燃烧器型号',
        value:'BurnerModel'
      },{
        label:'生产厂家',
        value:'Manufacturer'
      },{
        label:'燃烧器功率',
        value:'BurnerRatework'
      },
      // {
      //   label:'订单设备编号',
      //   value:'OrderEquipmentId'
      // },
      // {
      //   label:'审批状态',
      //   value:'ApprovalStatus'
      // },{
      //   label:'类型',
      //   value:'EquipmentTypeName'
      // },
      {
        label:'是否在保',
        value:'IsWarrantyName'
      },{
        label:'投产时间',
        value:'InstallTimeString'
      },{
        label:'报修次数',
        value:'RepairCount'
      },{
        label:'保修有效期至',
        value:'WarrantyTimeString'
      },{
        label:'设备现状/问题',
        value:'EquipmentStatusOrProblem'
      },]
      this.dialogExportVisible = true;
    },
    }

}
</script>
<style lang="scss" scoped>

</style>