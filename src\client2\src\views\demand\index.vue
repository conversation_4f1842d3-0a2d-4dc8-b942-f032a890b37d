<template>
  <div class="demand">
    <div class="bg-white">
      <app-table ref="mainTable" :tab-columns="tabColumns" :multable='false' :tab-datas="tabDatas" :isShowAllColumn="isShowAllColumn" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable">
        <!-- 表格查询条件区域 -->
        <template slot="conditionArea">
          <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch">
            <template slot="DemandTitle">
              <el-input style="width: 100%;" v-model="listQuery.DemandTitle" maxlength="100" placeholder></el-input>
            </template>

            <template slot="Status">
              <el-select style="width: 100%;" class="sel-ipt" v-model="listQuery.Status" placeholder="请选择状态">
                <el-option v-for="item in ProjectDemandStatusEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>

            <template slot="ProjectManagementIterationPlanId" v-if="isResearchAndDevelopment">
              <treeselect :normalizer="normalizer" class="treeselect-common" key='type1' v-model="listQuery.ProjectManagementIterationPlanId" :default-expand-level="3" :options="iterationTreeData" :multiple="false" placeholder='' :show-count="false" :disable-branch-nodes="true" :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
              </treeselect>
            </template>

          </app-table-form>
        </template>

        <!-- 数据转换区域 -->
        <template slot="Status" slot-scope="scope">
          <span class="common-status" :class="'status-' + getStatusClass(scope.row.Status)">{{ getStatusEnumLabel(scope.row.Status) }}</span>
        </template>
        <template slot="Priority" slot-scope="scope">
          <span>{{ getPriorityEnumLabel(scope.row.Priority) }}</span>
        </template>
        <template slot="CreateDateTime" slot-scope="scope">
          {{ scope.row.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
        </template>

        <!-- 表格批量操作区域 -->
        <template slot="btnsArea">
          <div class="pdl">
            <el-button @click="handleCreate"><span class="el-icon-circle-plus"></span> 新增</el-button>
          </div>
        </template>

        <!-- 表格批量操作区域 -->
        <!-- <template slot="btnsArea">
          <permission-btn moduleName="PersonnelFileResponseDtoModel" v-on:btn-event="onBtnClicked"></permission-btn>
        </template> -->
        <template slot="tableTopAres">
          <div class="pdl">
            <el-radio-group v-model="listQuery.ProjectDemandCategoryType" @change="getList()">
              <el-radio-button :label="1">待我处理</el-radio-button>
              <el-radio-button :label="0">需求列表</el-radio-button>
            </el-radio-group>
          </div>
        </template>

        <!-- 表格行操作区域 -->
        <template slot-scope="scope">
          <app-table-row-button v-show="IsShowEidt(scope.row)" @click="handleUpdateOrDetail(scope.row, 'update')" :type="1"></app-table-row-button>
          <app-table-row-button @click="handleUpdateOrDetail(scope.row, 'detail')" :type="2"></app-table-row-button>
          <app-table-row-button v-show="IsShowDelete(scope.row)" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>

          <el-button size="mini" type="text" v-show="isShowTurnTheAcceptanceBtn(scope.row)" @click="saveSubmission(0,scope.row)">转验收</el-button>

          <el-button size="mini" type="text" v-show="isShowCheckAcceptingBtn(scope.row)" @click="saveSubmission(1,scope.row)">提交测试</el-button>
          <el-button size="mini" type="text" v-show="isShowCheckAcceptingBtn(scope.row)" @click="saveSubmission(2,scope.row)">重新开发</el-button>
          <el-button size="mini" type="text" v-show="isShowCheckAcceptingBtn(scope.row)" @click="saveSubmission(3,scope.row)">挂起</el-button>
          <el-button size="mini" type="text" v-show="isShowCheckAcceptingBtn(scope.row)" @click="saveSubmission(4,scope.row)">完成</el-button>

          <el-button size="mini" type="text" v-show="isShowTestingBtn(scope.row)" @click="saveSubmission(5,scope.row)">测试完成</el-button>

          <el-button size="mini" type="text" v-show="isShowHangUpBtn(scope.row)" @click="saveSubmission(6,scope.row)">重新开发</el-button>
          <el-button size="mini" type="text" v-show="isShowHangUpBtn(scope.row)" @click="saveSubmission(7,scope.row)">完成</el-button>

        </template>
      </app-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>

    <!-- 新增 -->
    <div v-if="dialogFormVisible">
      <edit-page :dialogStatus='dialogStatus' :dialogFormVisible='dialogFormVisible' :id='RowProjectManagementDemandId' :ProjectId='ProjectId' :isResearchAndDevelopment='isResearchAndDevelopment' @closeDialog='closeDialog' @saveSuccess='handleSaveSuccess' @reloading='handleReloading'>
      </edit-page>
    </div>

    <!-- 编辑/查看 -->
    <div v-if="dialogEditVisible">
      <edit-demand :dialogEditStatus='dialogEditStatus' :dialogEditVisible='dialogEditVisible' :id='RowProjectManagementDemandId' :ProjectId='ProjectId' :isResearchAndDevelopment='isResearchAndDevelopment' @closeDialog='closeEditDialog' @saveSuccess='handleEditSaveSuccess' @reloading='handleReloading'></edit-demand>
    </div>

    <!-- 每一步操作需要选择经办人 -->
    <div v-if="selectResponsiblePersonEmployeeDialogVisbile">
      <el-dialog class="dialog-mini" width="600px" :title="'选择经办人'" :visible.sync="selectResponsiblePersonEmployeeDialogVisbile" :close-on-click-modal="false" :append-to-body="true">
        <div style="max-height:800px;overflow-y:auto;">
          <el-form v-loading='redDataFormLoading' :rules="redDataFormRules" ref="redDataForm" :model="redDataForm" label-position="right" label-width="100px">
            <el-row>
              <el-col :span="24">
                <el-form-item :label="'经办人'" prop="OperateEmployee">
                  <emp-selector :showType="2" :multiple="false" :list="redDataForm.OperateEmployee" @change="handleChangeRedUsers"></emp-selector>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="'备注'" prop="Remark">
                  <el-input type="textarea" :rows="6" placeholder="" maxlength="500" v-model="redDataForm.Remark"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div slot="footer">
          <el-button size="mini" @click="handleRedistributionDialogClose">取消</el-button>
          <el-button size="mini" type="primary" :loading='redDataFormPostLoading' @click="onRedistribution">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import * as projectDemandApi from "@/api/projectManagementDemand";
import Treeselect from '@riophae/vue-treeselect'
import empSelector from "../common/empSelector";
import {
  ProjectDemandStatusEnum,
  ProjectDemandPriorityEnum,
  ProjectDemandTerminalTypeEnum
} from "./enums";
import dayjs from "dayjs";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import localMixins from "../question/localMixins"
import { getToken, getUserInfo } from "@/utils/auth";
// import commentMixins from '../viewProjectInformation/commentMixins'
import EditPage from './edit'
import EditDemand from './editDemand'
// import CommentList from "../task/commentList"
// import OptList from "../task/optList"

export default {
  name: "demand",
  components: {
    empSelector,
    EditPage,
    EditDemand
    // CommentList,
    // OptList,
  },
  props: {
    ProjectId: {
      type: String,
      default: undefined
    },
    isResearchAndDevelopment: {
      type: Boolean,
      default: undefined
    },
  },
  mixins: [indexPageMixin, localMixins],
  computed: {
    // editable() {
    //   return this.dialogStatus != 'detail'
    // },

    getPriorityEnumLabel() {
      return val => {
        let t = this.ProjectDemandPriorityEnum.find(o => o.value == val);
        if (t) {
          return t.label;
        }
        return val;
      };
    },
    getStatusClass() {
      return status => {
        return status;
      };
    },
    getStatusEnumLabel() {
      return val => {
        let t = this.ProjectDemandStatusEnum.find(o => o.value == val);
        if (t) {
          return t.label;
        }
        return val;
      };
    },

  },
  watch: {
    ProjectId: {
      handler(val) {
        this.resetSearch();
        this.getList()
      },
      immediate: true
    },
    isResearchAndDevelopment: {
      handler(val) {
        let searchObj = { prop: "ProjectManagementIterationPlanId", label: "迭代" }
        if (val) {
          if (!this.tableSearchItems.find(s => s.prop == searchObj.prop)) {
            this.tableSearchItems.splice(1, 0, searchObj)
          }
        } else {
          let idx = this.tableSearchItems.findIndex(s => s.prop == searchObj.prop)
          if (idx > 0) {
            this.listQuery.ProjectManagementIterationPlanId = undefined
            this.tableSearchItems.splice(idx, 1)
          }
        }
        this.showOrHideVersionColumn()

      },
      immediate: true
    },
  },
  data() {
    return {
      normalizer(node) {
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        }
      },
      ProjectDemandStatusEnum: ProjectDemandStatusEnum,
      ProjectDemandPriorityEnum: ProjectDemandPriorityEnum,
      ProjectDemandTerminalTypeEnum: ProjectDemandTerminalTypeEnum,

      multipleSelection: [],
      tabDatas: [],
      total: 0,
      isShowAllColumn: true,
      listLoading: false,
      listQuery: {
        ProjectDemandCategoryType: 1,
        CurrentEmployeeId: "",
        DemandTitle: '',
        Status: '',
        ProjectManagementIterationPlanId: undefined,
      },
      tableSearchItems: [
        { prop: "DemandTitle", label: "需求标题" },
        { prop: "Status", label: "状态" },
        // { prop: "ProjectManagementIterationPlanId", label: "版本迭代" },
      ],
      tabColumns: [
        {
          attr: { prop: "DemandNumber", label: "需求编号" }
        },
        {
          attr: { prop: "DemandTitle", label: "需求标题", showOverflowTooltip: true }
        },
        // {
        //   attr: { prop: "DemandDescribe", label: "需求描述", showOverflowTooltip: true }
        // },
        {
          attr: { prop: "VersionName", label: "版本" }
        },
        {
          attr: { prop: "IterationName", label: "迭代" }
        },
        {
          attr: { prop: "Priority", label: "优先级" }, slot: true
        },
        {
          attr: { prop: "Status", label: "状态" }, slot: true
        },
        {
          attr: { prop: "ResponsiblePersonEmployeeName", label: "经办人" }
        },
        {
          attr: { prop: "CreatorEmployeeName", label: "创建人" }
        },
        {
          attr: { prop: "CreateDateTime", label: "创建时间" }, slot: true
        },
      ],

      //弹出选择经办人框相关
      selectResponsiblePersonEmployeeDialogVisbile: false,
      redDataFormLoading: false,
      redDataFormPostLoading: false,
      redDataForm: {
        ProjectManagementDemandId: '',
        OperationType: 0,
        OperateEmployee: [],
        Remark: ''
      },
      redDataFormRules: {
        OperateEmployee: { fieldName: "经办人", rules: [{ required: true }] },
      },



      RowProjectManagementDemandId: "",

      //弹出添加、编辑框相关
      dialogStatus: "",
      dialogEditStatus: "",
      disabled: true,
      dialogFormVisible: false,
      dialogEditVisible: false,
    };
  },
  created() {
    this.redDataFormRules = this.initRules(this.redDataFormRules);
  },
  mounted() {
    this.showOrHideVersionColumn()
  },
  methods: {
    showOrHideVersionColumn() {
      if (this.$refs['mainTable']) {
        if (this.isResearchAndDevelopment) {
          this.$refs['mainTable'].showOrHideColumn('VersionName', true)
          this.$refs['mainTable'].showOrHideColumn('IterationName', true)

        } else {
          this.$refs['mainTable'].showOrHideColumn('VersionName', false)
          this.$refs['mainTable'].showOrHideColumn('IterationName', false)
        }
      }
    },

    //-------------------------------------------------选择经办人相关---------------------------------------- //
    handleChangeRedUsers(emp) {
      if (emp && emp.length > 0) {
        this.redDataForm.OperateEmployee = [emp[0]];
      } else {
        this.redDataForm.OperateEmployee = [];
      }
      this.$refs["redDataForm"].validateField("OperateEmployee");

    },

    handleRedistributionDialogClose() {
      this.selectResponsiblePersonEmployeeDialogVisbile = false
    },

    onRedistribution() {
      let self = this;
      self.redDataFormPostLoading = true;
      self.$refs["redDataForm"].validate(valid => {
        if (!valid) {
          self.redDataFormPostLoading = false
        }
        if (valid) {
          let formData = JSON.parse(JSON.stringify(self.redDataForm));
          formData.ResponsiblePersonEmployeeId = self.redDataForm.OperateEmployee[0].EmployeeId

          projectDemandApi.saveTheSubmission(formData).then(res => {

            self.redDataFormPostLoading = false
            self.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000
            });
            this.handleRedistributionDialogClose()
            this.dialogFormVisible = false;
            this.getList();
          }).catch(err => {
            self.redDataFormPostLoading = false
          })
        }
      });
    },
    closeDialog() {
      this.dialogFormVisible = false
    },
    handleSaveSuccess(_formData) {
      this.listQuery.PageIndex = 1
      this.getList()
      this.dialogFormVisible = false
    },
    handleReloading() {
      this.listQuery.PageIndex = 1
      this.getList()
    },
    closeEditDialog() {
      this.dialogEditVisible = false
    },
    handleEditSaveSuccess(_formData) {
      this.listQuery.PageIndex = 1
      this.getList()
      this.dialogEditVisible = false
    },
    //-------------------------------------------------//---------------------------------------------------- //


    //操作按钮 提交
    saveSubmission(num, row) {
      //挂起 完成 测试完成 完成 按钮不触发 选择经办人流程 ， 直接修改对应的状态
      if (num == 3 || num == 4 || num == 5 || num == 7) {
        projectDemandApi.saveTheSubmission({ ProjectManagementDemandId: row.ProjectManagementDemandId, OperationType: num }).then(response => {
          this.$notify({
            title: "成功",
            message: "操作成功",
            type: "success",
            duration: 2000
          });
          this.dialogFormVisible = false;
          this.getList();
        });
      } else {
        this.resetForm();
        this.redDataForm.ProjectManagementDemandId = row.ProjectManagementDemandId;
        this.redDataForm.OperationType = num;
        this.selectResponsiblePersonEmployeeDialogVisbile = true;
      }
    },

    //控制列表中的编辑按钮
    IsShowEidt(row) {
      //目前仅创建人 和 待排期状态 可以编辑 
      return getUserInfo().employeeid == row.CreatorEmployeeId && row.Status == 2;
    },

    //控制列表中的删除按钮
    IsShowDelete(row) {

      //目前仅创建人 和 待排期 可以删除
      return getUserInfo().employeeid == row.CreatorEmployeeId && (row.Status == 2);
    },

    //显示流程操作按钮先决条件 ( 当前用户为经办人)
    showOperatorBtnPrecondition(row) {
      //   if (getUserInfo().employeeid == row.ResponsiblePersonEmployeeId && row.Status != 8) {
      //     return true;
      //   }

      if (getUserInfo().employeeid == row.ResponsiblePersonEmployeeId) return true;

    },

    //转验收 显示的按钮
    isShowTurnTheAcceptanceBtn(row) {
      return this.showOperatorBtnPrecondition(row) && row.Status == 3;
    },

    //验收中 显示的按钮
    isShowCheckAcceptingBtn(row) {
      return this.showOperatorBtnPrecondition(row) && row.Status == 4;
    },

    // 挂起 显示的按钮
    isShowHangUpBtn(row) {
      return this.showOperatorBtnPrecondition(row) && row.Status == 5;
    },

    // 测试中 显示的按钮
    isShowTestingBtn(row) {
      return this.showOperatorBtnPrecondition(row) && row.Status == 6;
    },

    resetForm() {
      this.redDataForm = {
        ProjectManagementDemandId: '',
        OperationType: 0,
        OperateEmployee: [],
        Remark: ''
      };
    },


    // 弹出添加框
    handleCreate() {
      this.dialogStatus = "create";
      this.dialogFormVisible = true;

    },

    // 弹出编辑框
    handleUpdateOrDetail(row, optType) {
      this.dialogEditStatus = optType;
      this.dialogEditVisible = true;
      this.RowProjectManagementDemandId = row.ProjectManagementDemandId;
    },





    //-------------------------------------------------------------列表相关--------------------------------------------------------------//

    getList() {
      let request = JSON.parse(JSON.stringify(this.listQuery));
      request.Status = request.Status != "" ? request.Status : 0;
      request.ProjectId = this.ProjectId;
      this.listLoading = true
      request.CurrentEmployeeId = getUserInfo().employeeid;
      projectDemandApi.getList(request).then(response => {
        this.tabDatas = response.Items;
        this.total = response.Total;
        this.listLoading = false;
      });
    },

    //删除
    handleDelete(rows) {
      // 多行删除
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.ProjectManagementDemandId);
      } else {
        ids.push(rows.ProjectManagementDemandId);
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        projectDemandApi.del(ids).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    //表格操作点击事件
    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleCreate();
          break;
        case "btnEdit":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行编辑",
              type: "error"
            });
            return;
          }
          this.handleUpdateOrDetail(this.multipleSelection[0], "update");
          break;
        case "btnDetail":
          if (this.multipleSelection.length !== 1) {
            this.$message({
              message: "只能选中一个进行查看",
              type: "error"
            });
            return;
          }
          this.handleUpdateOrDetail(this.multipleSelection[0], "detail");
          break;
        case "btnDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error"
            });
            return;
          }
          this.handleDelete(this.multipleSelection);
          break;
        default:
          break;
      }
    },

    //按条件查询
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    //重置查询条件
    resetSearch() {
      this.listQuery.DemandTitle = "";
      this.listQuery.Status = "";
      this.listQuery.ProjectManagementIterationPlanId = undefined;
    },

    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

  }
};
</script>
<style src="../viewProjectInformation/common.css" scoped></style>
<style lang='scss' scoped>
.common-status {
  color: #fff;
  padding: 4px 8px;
  border-radius: 5px;
  /* opacity: 0.4; */
}
.status-2 {
  //待排期
  background: #f2913b;
}
.status-3 {
  //开放
  background: #3b95fa;
}
.status-4 {
  //验收中
  background: #05c4a7;
}
.status-5 {
  //挂起
  background: #929292;
}
.status-6 {
  //测试中
  background: #e84c3d;
}
.status-7 {
  //完成
  background: #67c23a;
}
.status-8 {
  //变更中
  background: #6746f4;
}
</style>
