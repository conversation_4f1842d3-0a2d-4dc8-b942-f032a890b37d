//公示期设置
export const publicPeriodSetEnum = [
    { label: '手动结束', value: 1 },
    { label: '自动结束', value: 2 },
]

//考核类型
export const appraiseTypeEnum = [
    { label: '半年度考核', value: 1 },
    { label: '年度考核', value: 2 },
]

//自动结束类型
export const autoEndTypeEnum = [
    { label: '结果公示后1周', value: 1 },
    { label: '结果公示后2周', value: 2 },
    { label: '结果公示后3周', value: 3 },
    { label: '结果公示后4周', value: 4 },
]

// 绩效承诺
export const appraisePromiseStatusEnum = [
    { label: '未提交', value: 1, color:'#FF5757' },
    { label: '已提交', value: 2, color:'#70B603' },
    { label: '重新提交', value: 3, color:'#F59A23' },
]

//审视状态
export const lookStatusEnum = [
    { label: '继续考核', value: 1, color: 'blue' },
    { label: '终止考核', value: 2, color:'#FF5757' },
]

export const yearTypeEnum = [
    { label: '上半年', value: 1 },
    { label: '下半年', value: 2 },
]

//状态
export const appraisePersonalsStatusEnum = [
    { label: '创建PBC', value: 1, color:'#ff0000' },
    { label: '个人绩效承诺', value: 2, color:'#67c23a' },
    { label: '中期审视', value: 3, color:'#027db4' },
    { label: '绩效考核', value: 4, color:'#f59a23' },
    { label: '绩效终审', value: 5, color:'#f59a23' },
    { label: '结果公示', value: 6, color:'#409eff' },
    { label: '考核结束', value: 7, color:'#aaaaaa' },
]


export const performanceStatus=[
    { label: '个人承诺', value: 1 ,color:'#70B603'},
    { label: '自我评价', value: 2,color:'#F59A23' },
    { label: '等待考核结果', value: 3,color:'#FF5757' },
    { label: '结果公示', value: 4 ,color:'#409EFF'},
    { label: '考核结束', value: 5 ,color:'#F59A23'},
    { label: '终止考核', value: 6 ,color:'#FF5757'},  
]

export const finalStatus = [
    {value: 10, label: '无异议'},
    {value: 20, label: 'A'},
    {value: 30, label: 'B+'},
    {value: 40, label: 'B'},
    {value: 50, label: 'C'},
    {value: 60, label: 'D'},
]

export const expectationEvaluation=[
    { label: 'A', value: '1'},
    { label: 'B+', value: '2'},
    { label: 'B', value: '3'},
    { label: 'C', value: '4'},
    { label: 'D', value: '5'},
]
