<!--维修单设备配置编辑-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="600"
    >
      <template slot="body">
        <el-form :model="formModel" v-loading="formLoading" ref="refForm" style="min-height: 80px;">
          <div class="body-wrapper">
            <!-- <div style="margin-bottom: 10px;">这里显示父级</div> -->
            <div style="margin-bottom: 10px;">
              <el-button
                v-show="dialogStatus == 'create'"
                type="text"
                class="button"
                @click="handleAdd"
              >添加更多</el-button>
            </div>
            <div class="item-wrapper" v-for="(item, idx) in formModel.list" :key="idx">
              <div class="item-title" v-show="dialogStatus == 'create'">{{ idx + 1 }}、</div>
              <div class="item-content">
                <el-form-item>
                  <el-input
                    maxlength="25"
                    v-model="item.Name"
                    :placeholder="(equipmentSettingType==1?'设备':equipmentSettingType==2?'属性':'值')+'名称'"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="handleButtonClick" :buttonType="1" :disabled="buttonLoading"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
//按照以下顺序
//组件 import empSelector from "../../../../common/empSelector";
import * as equipmentSetting from "@/api/equipmentSetting";
//方法、属性 import { empSelector } from "../../../../common/empSelector";

export default {
  /**名称 */
  name: "demand-assign",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    //编辑还是新增(create: 新增; update: 编辑; detail：详情)
    dialogStatus: {
      required: true,
      type: String
    },
    /**设备类型：1设备 2属性 3值 */
    equipmentSettingType: {
      required: true,
      type: Number,
      default: 1
    },
    /**Id */
    id: {
      type: String,
      default: null
    },
    /**父级Id */
    parentId: {
      type: String,
      default: null
    }
  },
  /**数据区 */
  data() {
    return {
      /**表单加载 */
      formLoading: false,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单数据 */
      formModel: { list: [] }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    pageTitle() {
      let title = "";
      switch (this.dialogStatus) {
        case "create":
          title = "添加";
          break;
        case "update":
          title = "修改";
          break;
        case "detail":
          title = "详情";
          break;
        default:
          title = "";
          break;
      }

      switch (this.equipmentSettingType) {
        case 1:
          title += "设备";
          break;
        case 2:
          title += "属性";
          break;
        case 3:
          title += "值";
          break;
        default:
          title = "";
          break;
      }
      return title;
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.resetFormModel();
          if (_this.dialogStatus != "create" && _this.id) {
            _this.getDetail();
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.resetFormModel();
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    /**重置 */
    resetFormModel() {
      let _this = this;
      _this.formModel = { list: [] };
      if (_this.dialogStatus == "create") {
        let loop = _this.dialogStatus == "create" ? 5 : 1;
        for (let i = 0; i < loop; i++) {
          _this.formModel.list.push({
            Id: "",
            ParentId: _this.parentId,
            EquipmentSettingType: _this.equipmentSettingType,
            Name: ""
          });
        }
      }
    },
    /**新增一行 */
    handleAdd() {
      let _this = this;
      if (_this.formModel.list.length >= 50) {
        this.$message.error("最多添加50行");
        return;
      }
      _this.formModel.list.push({
        Id: "",
        ParentId: _this.parentId,
        EquipmentSettingType: _this.equipmentSettingType,
        Name: ""
      });
    },
    /**获取详情 */
    getDetail() {
      let _this = this;
      _this.formLoading = true;
      equipmentSetting
        .getDetails({ id: _this.id })
        .then(res => {
          _this.formLoading = false;
          let tmp = {
            list: [
              {
                Id: res.Id,
                ParentId: res.ParentId,
                EquipmentSettingType: res.EquipmentSettingType,
                Name: res.Name
              }
            ]
          };
          _this.formModel = Object.assign({}, _this.formModel, tmp);
        })
        .catch(err => {
          this.formLoading = false;
        });
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      let postData = JSON.parse(JSON.stringify(this.formModel));
      postData.list = postData.list.filter(s => s.Name.trim() != "");
      if (postData.list.length == 0) {
        this.$message.error("请填写设备信息");
        return false;
      }
      let equipmentNames = postData.list.map(s => s.Name.trim());
      if (new Set(equipmentNames).size != equipmentNames.length) {
        this.$message.error("名称不能重复");
        return false;
      }

      let result = null;
      _this.buttonLoading = true;

      if (_this.dialogStatus == "create") {
        result = equipmentSetting.addList(postData.list);
      } else if (_this.dialogStatus == "update") {
        //只能编辑单条
        result = equipmentSetting.edit(postData.list[0]);
      }

      result
        .then(response => {
          _this.buttonLoading = false;
          this.$notify({
            title: "提示",
            message: "保存成功",
            type: "success",
            duration: 2000
          });

          _this.$refs.appDialogRef.createData();
        })
        .catch(err => {
          _this.buttonLoading = false;
        });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.body-wrapper {
  .item-wrapper {
    display: flex;
    .item-title {
    }
    .item-content {
      flex: 1;
    }
  }
}
</style>

