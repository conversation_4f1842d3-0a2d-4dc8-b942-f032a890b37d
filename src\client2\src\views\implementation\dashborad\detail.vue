<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='1200'>
      <template slot="body">
        <div class="wrapper" v-loading='loading'>
          <div class="content">
            <div class="whole" style="margin-top:10px;">
              <el-row>
                <el-col :span="7">
                  <div style="display: flex;">工序整体进度：
                    <div style="flex: 1;">
                      <el-progress :percentage="procesDetail.Progress" :color="getEngStatus(procesDetail.Progress == 0 ? 1 : procesDetail.Progress == 100 ? 11 : 2).color"></el-progress>
                    </div>
                  </div>
                </el-col>
                <el-col :span="3">状态：{{ procesDetail.Progress == 0 ? '未开始' : procesDetail.Progress == 100 ? '已完成' : '进行中' }}</el-col>
                <el-col :span="4">开始时间：{{ procesDetail.StartTime | dateFilter('YYYY-MM-DD HH:mm', '无') }}</el-col>
                <el-col :span="4">截止时间：{{ procesDetail.EndTime | dateFilter('YYYY-MM-DD HH:mm', '无') }}</el-col>
                <el-col :span="4">剩余工期：{{procesDetail.DaysRemaining?(( procesDetail.DaysRemaining > 0 ?procesDetail.DaysRemaining:('已逾期'+(-procesDetail.DaysRemaining)))+'天'):'无'}}</el-col>
                <el-col>
                  <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-top: 6px;">
                    工序负责人：
                    <span :title="procesDetail.EmployeeList ? procesDetail.EmployeeList.map(t=>{return t.Name;}).join(',') : ''">
                      {{ procesDetail.EmployeeList ? procesDetail.EmployeeList.map(t=>{return t.Name;}).join(',') : '' }}
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>
            <!----------------------------------------------------- 事项列表 ----------------------------------------------------->
            <div class="process-wrapper">
              <div class="tab-wrapper" style="width:100%">
                <app-table-core ref="mainTable3" :tab-columns="tabColumnsProces" :tab-datas="tabDatasProces" :tab-auth-columns="[]" :isShowAllColumn="true" :loading="listLoadingProces" @rowSelectionChanged="() => {}" :isShowOpatColumn="true" :startOfTable="0" :multable="false" :isShowConditionArea="false">
                  <template slot="Status" slot-scope="scope">
                    <span class="item-status" :style="{backgroundColor: `${getProcesStatus(scope.row.Status).color}`}">{{ scope.row.Status | procesStatusFilter }}</span>
                  </template>
                  <template slot="Progress" slot-scope="scope">
                    <el-progress class="elProgress" :color="getProcesStatus(scope.row.Status).color" :percentage="scope.row.Progress"></el-progress>
                  </template>
                  <template slot="Remark" slot-scope="scope">{{ scope.row.Remark ? scope.row.Remark : '无' }}</template>

                  <!-- 表格行操作区域 -->
                  <template slot-scope="scope">
                    <app-table-row-button @click="handleProcesCreateDialog(scope.row, 'detail')" :type="1" text="详情"></app-table-row-button>

                  </template>
                </app-table-core>
                <!-- <pagination v-show="totalProces>0" :total="totalProces" :page.sync="listQueryProces.PageIndex" :size.sync="listQueryProces.PageSize" @pagination="handleCurrentChangeProces" @size-change="handleSizeChangeProces" /> -->
              </div>
              <div class="right" v-if="false">
                <!----------------------------------------------------- 工序设备列表 ----------------------------------------------------->

                <template v-if="!tabProceEqusLoading && tabProceEqus.length == 0">
                  <no-data></no-data>
                </template>
                <div v-else class="right-list">
                  <div v-for="(equ, idx) in tabProceEqus" :key="idx" v-loading="tabProceEqusLoading" class="item-wrapper">
                    <!-- <div class=""> -->
                    <div class="title-wrapper">
                      <div class="item-title" @click="handleEquDetailDialog(equ)">
                        {{ equ.Name }}
                        <!-- <el-checkbox
                                                :disabled="equ.Status == 3"
                                                v-model="checkedEqus"
                                                :label="equ.Id"
                                                :title="equ.Name"
                                            >{{ equ.Name }}</el-checkbox>-->
                      </div>
                      <!-- <div>
                                        <el-dropdown
                                        v-show="isProcesMgmt()"
                                        trigger="click"
                                        style="float: right; padding-right: 9px;"
                                        @command="handleCommand($event, equ)"
                                        >
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item
                                            v-show="s.value != 1 && s.value != equ.Status"
                                            v-for="(s, idx) in equStatus"
                                            :key="idx"
                                            :command="s.value"
                                            >{{ s.label }}</el-dropdown-item>
                                        </el-dropdown-menu>
                                        </el-dropdown>
                                    </div> -->
                    </div>
                    <!-- </div> -->
                    <div class="cl">
                      <div style="width:auto;display: inline-block; margin-right: 4px;" class="fl">
                        <span class="item-status" style="display: inline-block" :style="{backgroundColor: `${getEquStatus(equ.Status).color}`}">{{ equ.Status | equStatusFilter }}</span>
                      </div>
                      <div style="display: inline-block;" :title="equ.ProductListManagementName" class="fl">
                        <span style="display: inline-block; box-sizing: border-box; max-width: 100px; overflow: hidden; text-overflow: ellipsis;white-space: nowrap; color: #303133; border: 1px solid #dcdfe6;" class="item-status">{{ equ.ProductListManagementName }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- .content{
                min-height: 400px;
                displayL flex;
                flex-dec: column;
                .whole{
                    padding-bottom: 10px; 
                    border-bottom: 1px solid #dcdfe6;
                }
            } -->

      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>

    <!-- 设备详情 -->
    <app-dialog title="设备详情" ref="appDialogRef3" @closeDialog="closeEquDetailDialog" :dialogFormVisible="dialogEquDetailFormVisible" :maxHeight="600" :width="600">
      <template slot="body">
        <div class="equ-detail-wrapper">
          <div class="row-wrapper">
            <div class="row-title">设备名称：</div>
            <div class="row-content">{{ equDetail.Name }}</div>
          </div>
          <div class="row-wrapper">
            <div class="row-title">类型：</div>
            <div class="row-content">{{ equDetail.ProductListManagementName }}</div>
          </div>
          <div class="row-wrapper">
            <div class="row-title">备注：</div>
            <div class="row-content">
              <span v-if="equDetail.Remark">{{ equDetail.Remark }}</span>
              <span v-else>无</span>
            </div>
          </div>
        </div>
      </template>
      <template slot="footer">
        <div>
          <!-- 取消 -->
          <app-button @click="closeEquDetailDialog" :buttonType="2"></app-button>
        </div>
      </template>
    </app-dialog>

    <!-- 事项 详情、编辑 -->
    <proces-create @closeDialog="closeProcesCreateDialog" @saveSuccess="() => {}" :dialogFormVisible="dialogProcesCreateFormVisible" :dialogStatus="dialogProcesCreateStatus" :procesId="procesId"></proces-create>
  </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import procesCreate from '../engineering/workbench/procesCreate'
import * as impMgmt from "@/api/implementation/impManagement2"
import { vars } from "../common/vars"
import mixins from "../common/mixins"

export default {
  name: "dashborad-detail",
  directives: {},
  components: {
    noData,
    procesCreate,
  },
  mixins: [mixins],
  props: {
    dialogStatus: {
      //create、update、detail
      type: String
    },
    id: {
      type: String,
      required: true
    },
    regionalId: {
      type: String,
      required: true
    }
  },
  filters: {
    equStatusFilter(status) {
      let tmp = vars.equStatus.find(s => s.value == status);
      if (tmp) {
        return tmp.label;
      }
      return status;
    },
    questionStatusFilter(status) {
      let tmp = vars.questionStatus.find(s => s.value == status);
      if (tmp) {
        return tmp.label;
      }
      return status;
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          if (this.dialogStatus != "create" && this.id) {
            this.getDetail();
            this.getProcesList()

            if (this.regionalId) {
              this.getProcesEqus()
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "";
      } else if (this.dialogStatus == "update") {
        return "";
      } else if (this.dialogStatus == "detail") {
        return "详情";
      }
    },

  },
  created() {

  },
  data() {
    return {

      loading: false,

      /**
       * 设备详情
       */
      dialogEquDetailFormVisible: false,
      equDetail: {},

      /**
       * 事项编辑
       */
      procesId: "",
      dialogProcesCreateStatus: "detail",
      dialogProcesCreateFormVisible: false,


      /**
       * 工序详情
       */
      procesDetail: {},

      /**
       * 事项列表
       */
      // totalProces: 0,
      tabDatasProces: [],
      // listQueryProces: {
      //     PageIndex: 1,
      //     PageSize: 20,
      // },
      listLoadingProces: false,
      multipleSelectionProces: [],
      tableSearchItemsProces: [],
      tabColumnsProces: [
        {
          attr: { prop: "Name", label: "实施事项" }
        },
        {
          attr: { prop: "Status", label: "状态" },
          slot: true
        },
        {
          attr: { prop: "Progress", label: "进度" },
          slot: true
        },
        {
          attr: { prop: "Remark", label: "备注", showOverflowTooltip: true },
          slot: true
        }
      ],

      /**
       * 工序设备列表
       */
      tabProceEqus: [],
      tabProceEqusLoading: false,
      checkAll: false,
      checkedEqus: [],
      products: [],
      questionEmployees: [],

    };
  },
  methods: {
    getDetail() {
      this.loading = true
      impMgmt.getImpProceDetails({ id: this.id }).then(res => {
        this.loading = false
        this.procesDetail = res;
      }).catch(err => {
        this.loading = false
      });
    },
    /**工序事项列表 */
    getProcesList() {
      // let postData = JSON.parse(JSON.stringify(this.listQueryProces))
      let postData = {
        ImplementationProcedureId: this.id
      };

      this.listLoadingProces = true;
      impMgmt
        .getImpItems(postData)
        .then(res => {
          this.listLoadingProces = false;
          this.tabDatasProces = res;
          // this.totalProces = res.Total
        })
        .catch(err => {
          this.listLoadingProces = false;
        });
    },

    /**事项详情、编辑 */
    handleProcesCreateDialog(row, optType = "update") {
      this.procesId = row.Id;
      this.dialogProcesCreateStatus = optType;
      this.dialogProcesCreateFormVisible = true;
    },
    /**事项进度关闭 */
    closeProcesCreateDialog() {
      this.dialogProcesCreateFormVisible = false;
    },
    getProcesStatus(val) {
      return vars.processStatus.find(s => s.value == val) || {}
    },
    handleEquDetailDialog(row) {
      this.equDetail = row;
      this.dialogEquDetailFormVisible = true;
    },
    // 工序设备列表
    getProcesEqus() {
      let postData = {
        ImplementationProcedureId: this.id,
        ImplementationRegionalId: this.regionalId
      };
      this.tabProceEqusLoading = true;
      impMgmt
        .getEqusNoPage(postData)
        .then(res => {
          this.tabProceEqusLoading = false;
          this.tabProceEqus = res;
        })
        .catch(err => {
          this.tabProceEqusLoading = false;
        });
    },
    closeEquDetailDialog() {
      this.dialogEquDetailFormVisible = false;
    },
    handleFilesUpChange(files) {
      this.formData.AttachmentList = files;
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>
<style scoped>
.wrapper >>> .el-progress-bar {
  width: 90%;
}
</style>

<style lang="scss" scoped>
.content {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  .whole {
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;
  }

  .process-wrapper {
    flex: 1;
    display: flex;
    height: calc(100% - 28px);
    // .left, .right {
    //     display: inline-block;
    // }
    .left {
      width: calc(100% - 260px) !important;
      padding-right: 5px;
      height: 100%;
      overflow-y: auto;
      .btns {
        margin: 6px 0;
      }
      .tab-wrapper {
        height: calc(100% - 28px - 12px);
        overflow-y: auto;
        padding-bottom: 6px;
      }
      // float: left;
    }
    .right {
      width: 260px !important;
      // height: 100%;
      overflow-y: auto;
      // float: right;
      // height: 636px;
      // padding-left:10px;
      // overflow-y: auto;
      border-left: 1px solid #dcdfe6;
      padding-left: 10px;
      margin-right: 10px;
      .btn-wrapper {
        height: 28px;
        margin: 6px 0;
      }
      .right-list {
        height: calc(100% - 28px - 12px);
        overflow-y: auto;
        padding-bottom: 6px;
        .item-wrapper {
          border: 1px solid #dcdfe6;
          border-radius: 10px;
          padding: 10px;
          margin-top: 10px;
          .title-wrapper {
            display: flex;
            margin-bottom: 4px;
            .item-title {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 4px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              word-wrap: break-word;
              &:hover {
                cursor: pointer;
                text-decoration: underline;
              }
            }
          }
        }
        .item-wrapper:first-child {
          margin-top: 0;
        }
      }
    }
  }
}

/** 设备详情弹框 */
.equ-detail-wrapper {
  .row-wrapper {
    display: flex;
    margin-bottom: 4px;
    .row-title {
      width: 80px;
      text-align: right;
    }
    .row-content {
      flex: 1;
    }
  }
}
</style>
