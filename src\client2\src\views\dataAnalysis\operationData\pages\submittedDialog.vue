<!--组件名称-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog title="查看未提交人员" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800" :maxHeight="600">

      <template slot="body">

        <el-row style="margin-top:15px;margin-left:10px;">
            <el-col :span="6">已出勤:{{attendance}}</el-col>
            <el-col :span="6">未出勤:{{noAttendance}}</el-col>
            <el-col :span="6">请假:{{askForLeave}}</el-col>
            <el-col :span="6">休息:{{rest}}</el-col>
        </el-row>

         <div style="display: flex; align-items:center; padding: 10px; padding-bottom: 0; padding-left: 0;">
             <div style="flex: 1; padding: 0 10px;">
                     <el-input
                        placeholder="输入姓名、部门负责人"
                        @clear='getNotSubmittedEmployee'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                            getNotSubmittedEmployee()
                             }
                          }' 
                       clearable 
                       v-model="Keywords"
                     ></el-input>
              </div>
            <app-table-row-button :disabled='exportLoading' @click="onExport" text='导出'></app-table-row-button>
         </div>

         <div class="tab-wrapper">
            <app-table-core ref="mainTable" :loading='dailySubmitTabDatasLoading' :tab-columns="dailySubmitTabColumns" :serial='false' :tab-datas="dailySubmitTabDatas" :tab-auth-columns="[]" :isShowAllColumn="true" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable='false'>
                 <template slot="Name" slot-scope="scope">
                         {{ scope.row.Name | emptyFilter }}
                 </template>
                 <template slot="DepartmentPrincipal" slot-scope="scope">
                          {{ scope.row.DepartmentPrincipal | emptyFilter }}
                 </template>
                 <template slot="DayPersonalTimecardRecord" slot-scope="scope">
                 <span v-if="currentDayBefore(`${listQuery.Year}-${listQuery.Month}-${currentDay}`) < 0">-</span>
                <template v-else>
                  <statusList v-if="scope.row.DayPersonalTimecardRecord" :ref="`row_${scope.index}_${scope.index}`" :rowKey='`row_${scope.index}`' :showDetail='false' :recordObj='scope.row.DayPersonalTimecardRecord'></statusList>
                 </template>
                 </template>
                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                <!-- 详情 -->
                 <app-table-row-button @click="handleDailyDetailDialog(scope.row)" text="详情" :type="2"></app-table-row-button>
                </template>
             </app-table-core>
         </div>

      </template>

      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <!-- <app-button v-show="editable" :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button> -->
      </template>
    </app-dialog>


    <dailyDetailDialog
        v-if="dialogDailyDetailVisible && rowObj"
        @closeDialog="closeDailyDetailDialog"
        @saveSuccess="() => {}"
        :dialogFormVisible="dialogDailyDetailVisible"
        :dialogStatus="'detail'"
        :row='rowObj'
    ></dailyDetailDialog>

  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as workPlanDaily from "../../../../api/workbench/workPlanDaily";
import statusList from '../../../personnelManagement/situation/statusList'
import dailyDetailDialog from '../../../workbench/workPlanDashboard/dailyDetailDialog'
import tabDynamicHeightMixins from "../../../../mixins/tabDynamicHeightMixins"
import commMixin from '../../../workbench/workPlan/common/comMixins'
import dataMixins from "../../../personnelManagement/situation/mixins"
import indexPageMixin from "../../../../mixins/indexPage";
import * as exportPort from "../../../../api/export"
import { downloadFile } from "../../../../utils/index"
import dayjs from 'dayjs'


//当天
let currentDayTemp = new Date().getDate()

export default {
components: {
    dailyDetailDialog,
    statusList,
  },
  mixins: [indexPageMixin, commMixin, tabDynamicHeightMixins, dataMixins],
  /**名称 */
  name: "share",
  /**组件声明 */
  /**参数区 */
  props: {
    //弹窗类型
    dialogStatus: {
      type: String,
      default: "create",
    },
  },
  /**数据区 */
  data() {
    return {
      buttonLoading: false,
      exportLoading: false,
      dialogDailyDetailVisible: false,
      currentDay: currentDayTemp,
      List: [],
      Keywords: '',
      attendance:0,
      noAttendance:0,
      askForLeave:0,
      rest:0,
      listQuery:{
          year:Number(dayjs().format('YYYY')),
          month:Number(dayjs().format('MM')),
          day:Number(dayjs().format('DD'))
      },
      
     dailySubmitTabDatasLoading: false,
     dailySubmitTabDatas: [],
     dailySubmitTabColumns: [
            {
              attr: { prop: "Name", label: "姓名", showOverflowTooltip: true},
              slot: true
            },
            {
              attr: { prop: "DepartmentPrincipal", label: "部门负责人", showOverflowTooltip: true},
              slot: true
            },
            {
              attr: { prop: "DayPersonalTimecardRecord", label: "考勤状态", width: 150},
              slot: true
            },
      ],

    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },

  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if(val) {
            this.getNotSubmittedEmployee()
            this.getStatistics()
        }
      },
      immediate: true,
    },
  },
  /**渲染前 */
  created() {
    let _this = this;
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {

    getNotSubmittedEmployee() {
            let year = Number(dayjs().format('YYYY'))
            let month = Number(dayjs().format('MM'))
            let day = Number(dayjs().format('DD'))

            let postDatas = {
                PageIndex: 1,
                PageSize: 1000,
                Year: year,
                Month: month,
                Day: day,
                Keywords: this.Keywords,
                SelectType: 2
            }

            this.dailySubmitTabDatasLoading = true
            workPlanDaily.getNotSubmittedEmployee(postDatas).then(res => {
                this.dailySubmitTabDatasLoading = false
                this.dailySubmitTabDatas = res.Items.map(s => {
                     this.initDatas(s)
                     return s
                  }) || []
                }).catch(err => {
                   this.dailySubmitTabDatasLoading = false
                })
        },

      getStatistics(){
        workPlanDaily.getStatistics(this.listQuery).then(res => {
                this.attendance=res.Attendance
                this.noAttendance=res.NoAttendance
                this.askForLeave=res.AskForLeave
                this.rest=res.Rest
               }).catch(err => {
             })
      },

       onExport() {
            let year = Number(dayjs().format('YYYY'))
            let month = Number(dayjs().format('MM'))
            let day = Number(dayjs().format('DD'))

            let postDatas = {
                "exportSource": 31,
                "searchCondition": {
                     Year: year,
                     Month: month,
                     Day: day,
                     Keywords: this.Keywords,
                     SelectType: 2
                  }
                }

                this.exportLoading = true
                exportPort.exportData(postDatas, {
                timeout: 300 * 1000,
                }).then(res => {
                    this.exportLoading = false;
                    downloadFile(res.Url);
                }).catch(err => {
                    this.exportLoading = false;
                })
        },

         handleDailyDetailDialog(row) {
            this.rowObj = row
            this.dialogDailyDetailVisible = true
        },

         closeDailyDetailDialog() {
            this.dialogDailyDetailVisible = false
        },

         currentDayBefore(day) {
            return dayjs(dayjs().format('YYYY-MM-DD')).diff(day, 'day')
        },

    /**关闭 */
    handleClose() {
      let _this = this;
      _this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.wrapper {
    min-height: 300px;
    .title-wrapper{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .title{
            font-weight: 600;
        }
    }

}

.icon{
    border-radius: 30px;
    width: 40px;
    height: 40px;
}

.employee{
    height: 172px;
    overflow-y: auto;
}

.employee li{
     float: left;
     margin:15px;
}

.employee p{
    margin-top: 5px;
} 

</style>