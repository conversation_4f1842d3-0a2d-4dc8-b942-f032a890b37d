<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1200' :maxHeight='600'>
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="110px">
          <div class="wrapper" v-loading='loading'>
            <div class="left">
              <!-- <div class="secondDiv" v-show="dialogStatus == 'update' || isChange">
                                <el-form-item label="变更原因" prop="ChangeReason" label-width="78px">
                                    <el-input
                                        :disabled='!editable'
                                      maxlength="2000"
                                      type="textarea"
                                      :rows="3"
                                      v-model="formData.ChangeReason"
                                    ></el-input>
                                </el-form-item>
                            </div> -->
              <div class="panel-title">基本信息</div>
              <div>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="实施工程名称" prop="Name">
                      <el-input :disabled="!editable" maxlength="100" v-model="formData.Name"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="模板" prop="ImplementationTemplateId">
                      <el-select :disabled="!editable || dialogStatus == 'update'" class="filter-item" style="width:100%" v-model="formData.ImplementationTemplateId" placeholder="">
                        <el-option v-for="item in templates" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="关联订单" prop="OrderNumber">
                      <el-button :disabled="!editable" type="text" @click="() => dialogAccessUsers = true">选择订单</el-button>
                      <span v-if="formData.OrderNumber">{{ formData.OrderNumber }}</span><i @click="handleClearOrder" v-show="formData.OrderNumber" style="cursor: pointer; margin-left: 10px;" class="el-icon-close"></i>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="单号" prop="EngineeringNumber">
                      <div style="line-height: 28px;">
                        <el-input :disabled="!editable" maxlength="30" v-model="formData.EngineeringNumber"></el-input>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="工程编号" prop="Code">
                      <div style="line-height: 28px;">{{formData.Code}}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="所在地区" prop='RegionalId'>
                      <!-- :always-open="true" -->
                      <treeselect :append-to-body="true" class="treeselect-common" :disabled="!editable || dialogStatus == 'update'" :normalizer="normalizer" key='type2' :zIndex='99999999' v-model="formData.RegionalId" :default-expand-level="0" :options="treedata" :multiple="false" placeholder='' :show-count="true" :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
                      </treeselect>
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="年份" prop='Year'>
                      <el-date-picker key="xxxyear" style="width: 100%;" v-model="formData.Year" type="year" :disabled="!editable" placeholder="" format='yyyy' value-format='yyyy'></el-date-picker>
                      <!-- <el-date-picker style="width: 100%;" format='yyyy' value-format='yyyy' :disabled="!editable" v-model="formData.Year" type="year" placeholder=""></el-date-picker> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="费用预算(万)" prop='CostBudget'>
                      <el-input :disabled="!editable" maxlength="10" onkeyup="this.value = this.value.replace(/[^\d.]/g,'');" v-model="formData.CostBudget"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="人力预算(人)" prop='ManpowerBudget'>
                      <el-input :disabled="!editable" maxlength="9" onkeyup="this.value = this.value.replace(/[^\d.]/g,'');" v-model="formData.ManpowerBudget"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-col :span="24">
                  <el-form-item label="工程介绍" prop="Remarks">
                    <el-input maxlength="2000" :disabled='!editable' type="textarea" :rows="4" placeholder="请输入内容" v-model="formData.Remarks"></el-input>
                  </el-form-item>
                </el-col>
              </div>
              <!-- <div>
                                <div class="panel-title">审批</div>
                                <div>
                                    <approval-panel v-if="dialogStatus == 'create' || dialogStatus == 'update'" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                                    <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                                </div>
                            </div> -->
            </div>
            <div class="right">
              <div class="staffingBox">
                <div class="panel-title">人员配置</div>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="工程负责人" prop='ImplementPrincipalIdList' label-width="92px">
                      <emp-selector :beforeConfirm='handleBeforeConfirm' :readonly="!editable" :showType="2" :multiple="true" :list="formData.ImplementPrincipalIdList" @change="handleChangeManager"></emp-selector>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="lastDiv">
                <div>
                  <div class="panel-title">附件</div>
                  <div>
                    <app-uploader :readonly='!editable' accept='all' :fileType='3' :max='10000' :value='formData.AttachmentList' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024' @change='handleFilesUpChange'></app-uploader>
                  </div>
                </div>
                <!-- <div v-if="dialogStatus == 'detail' && isShowHistories" style="margin-top: 10px">
                                    <div class="panel-title">订单历史记录</div>
                                    <div v-for="(item, idx) in histories" :key="idx">
                                        <div>{{ item.Salesman }}&nbsp;&nbsp;{{ item.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}&nbsp;&nbsp;<el-button type="text" @click="handleDialogHistory('detail', item, idx == 0 ? 6 : 9)">{{ idx == 0 ? '创建订单' : '变更订单' }}</el-button></div>
                                    </div>
                                </div> -->
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button v-if="editable" @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
        <!-- <el-button @click="handleApproval"  type="primary" size="mini" :disabled='disabledBtn' v-show="dialogStatus == 'approval'">审批</el-button> -->
      </template>
    </app-dialog>
    <order-selector :isShow='dialogAccessUsers' :checkedList='formData.OrderId ? [{Id: formData.OrderId, OrderNumber: formData.OrderNumber}] : []' @changed='handleChangeUsers' @closed='() => dialogAccessUsers = false' :multiple='false'>
    </order-selector>
  </div>
</template>  
<script type="text/javascript">
import * as impManagement from "@/api/implementation/impManagement";
import * as impMgmt from "@/api/implementation/impManagement2"
import * as change from '@/api/projectDev/projectMgmt/change'
import { getUserInfo } from '@/utils/auth'
// import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
// import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail'
import orderSelector from '../../common/orderSelector'
import empSelector from '../../common/empSelector'
import { listToTreeSelect } from '@/utils'
import * as regionalManagement from "@/api/systemManagement/regionalManagement"
import { vars } from '../../projectDev/common/vars'
export default {
  name: "",
  props: {
    specialPageTitle: {
        type: String
    },
    //开始、结束操作弹框
    dialogStatus: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    isChange: {
      type: Boolean,
      default: false
    },
    //创建实施工程默认选中地区
    regionalId: {
      type: String,
      default: ''
    }
    //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
    // isOnlyViewDetail: {
    //     type: Boolean,
    //     default: false
    // },      
    // approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
    //     type: String,
    //     default: ''
    // }, 
  },
  components: {
    // approvalPanel,
    // approvalDetail,
    orderSelector,
    empSelector
  },
  filters: {


  },

  data() {
    return {
      dialogAccessUsers: false,
      loading: false,
      disabledBtn: false,
      templates: [],
      formData: {
        Id: '',
        Name: '',
        EngineeringNumber: '',
        Code: '无',
        RegionalId: null,
        CostBudget: '',
        Year: '',
        ManpowerBudget: '',
        Remarks: '',
        ImplementPrincipalIdList: [],
        AttachmentList: [],// 项目附件ID列表
        OrderId: '',
        OrderNumber: '',
        ImplementationTemplateId: null,

        // ChangeReason:'', 
        // Approval: {//审批信息
        //     ApprovalEmployeeList: [[]],
        //     ApprovalType: 1,
        //     ApprovalOperatorEmployeeList: [], //已审批人员
        //     NoApprovalEmployeeList: [], //未审批人员
        //     CCEmployeeList: [], //抄送人
        //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
        //     ApprovalState: 1, //1: 进行中; 2: 已完成
        //     ApprovalResult: 1, //1: 通过； 2：不通过
        // },
      },
      rules: {
        // ChangeReason:[{ required: false, message: '变更原因不能为空'}],
        Name: [{ required: true, message: '实施工程名称不能为空' }],
        // OrderNumber: [{ required: true, message: '关联订单不能为空'}],
        RegionalId: [{ required: true, message: '请选择所在地区', trigger: 'change' }],
        // ManpowerBudget: [{ required: true, message: '人力预算不能为空',}],
        Remarks: [{ required: true, message: '模板不能为空', }],
        ImplementationTemplateId: [{ required: true, message: '模板不能为空', }],
        ImplementPrincipalIdList: [{ type: 'array', required: true, message: '请选择工程负责人', trigger: 'change' }],
      },
      //   normalizer(node) {
      //     // treeselect定义字段
      //     return {
      //       id: node.Id,
      //       label: node.RegionalName,
      //       children: node.children
      //     }
      //   },
      normalizer(node) {
        // treeselect定义字段
        return {
          id: node.Id,
          label: node.label,
          children: node.children
        };
      },
      treedata: [],
    };
  },
  computed: {
    pageTitle() {
      if (this.specialPageTitle) {
        return this.specialPageTitle
      }
      if (this.dialogStatus == 'create') {
        return '创建实施工程'
      } else if (this.dialogStatus == 'detail') {
        return '实施工程详情'
      } else if (this.dialogStatus == 'update') {
        return '编辑实施工程'
      } else if (this.dialogStatus == 'approval') {
        if (this.isChange) {
          return '变更审批'
        } else {
          return '创建审批'
        }
      }
    },
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != 'approval' && this.dialogStatus != 'detail'
    },
  },

  watch: {
    '$attrs.dialogFormVisible': {
      handler(val) {
        if (val) {
          console.log(this.dialogStatus, this.isChange)
          if (this.dialogStatus == 'create') {
            this.initFormData();
          } else if (this.dialogStatus == 'approval' && this.isChange) {
            this.getChangeDetail()
          } else if (this.dialogStatus == 'update') {
            // this.rules.ChangeReason[0].required=true;
            this.getDetail();
          } else {
            // this.rules.ChangeReason[0].required=false;
            if (this.dialogStatus == 'approval' || (this.dialogStatus == 'detail' && !this.isChange)) {
              this.getDetail();
            }
            // else if(this.dialogStatus == 'detail' && this.isChange){
            //     this.getChangeDetail()
            // }
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getAreas();
    this.initFormData();
    this.getTemplates();
  },
  mounted() {

  },
  methods: {
    handleBeforeConfirm(users) {
      if (users && users.length > 5) {
        this.$message({
          message: '指派人数不得超过5人',
          type: 'error'
        })
        return false
      }
      return true
    },
    getTemplates() {
      let postData = { IsUsed: true }
      impMgmt.getTemplates(postData).then(res => {
        this.templates = res.map(s => {
          s.value = s.Id
          s.label = s.Name
          return s
        })
      })
    },
    // handleApproval() {
    //     let postData = this.$refs.approvalDetail.getData()
    //     postData.BusinessId = this.id
    //     let approvalLabel = vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label

    //     this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
    //         confirmButtonText: '确定',
    //         cancelButtonText: '取消',
    //         type: 'warning'
    //     }).then(() => {
    //         this.disabledBtn = true
    //         let result=null;
    //         if(this.isChange){
    //             result = change.createApproval(postData);
    //         }else{
    //             result = impManagement.approval(postData);
    //         }

    //         result.then(res => {
    //             this.disabledBtn = false
    //             this.$notify({
    //                 title: "提示",
    //                 message: "审批成功",
    //                 type: "success",
    //                 duration: 2000
    //             });
    //             this.$refs.appDialogRef.createData()
    //         }).catch(err => {
    //             this.disabledBtn = false
    //         })
    //     })
    // },
    getChangeDetail() {
      this.loading = true
      let isApprovaled = this.dialogStatus == 'detail' ? true : false;
      change.detail({ id: this.id, isApprovaled }).then(res => {
        this.loading = false
        this.formData = Object.assign({}, this.formData, res.ChangeDetailsResponseModel, res.ImplementRequestModel)
        this.formData.CostBudget = (this.formData.CostBudget ? this.formData.CostBudget : '');
        this.formData.Year = (this.formData.Year ? this.formData.Year + '' : '');
        this.formData.ImplementPrincipalIdList = this.formData.ImplementPrincipalEmployeeList;
      }).catch(err => {
        this.loading = false
      })
    },
    getDetail() {
      this.loading = true;
      impManagement.detail({
        id: this.id
        // ,approvalId: this.approvalId
      }).then(res => {
        this.formData.ImplementPrincipalIdList = res.ImplementPrincipalEmployeeList ? res.ImplementPrincipalEmployeeList : [];
        this.formData = Object.assign({}, this.formData, res);
        this.formData.CostBudget = (this.formData.CostBudget ? this.formData.CostBudget : '');
        this.formData.Year = (this.formData.Year ? this.formData.Year + '' : '');
        this.loading = false;
      }).catch(e => {
        this.loading = false;
      })
    },
    initFormData() {
      this.formData = {
        Id: '',
        Name: '',
        EngineeringNumber: '',
        Code: '无',
        RegionalId: null,
        CostBudget: '',
        Year: '',
        ManpowerBudget: '',
        Remarks: '',
        ImplementPrincipalIdList: [],
        AttachmentList: [],// 项目附件ID列表
        OrderId: '',
        OrderNumber: '',
        ImplementationTemplateId: '',
        // ChangeReason:'',
        // Approval: {//审批信息
        //     ApprovalEmployeeList: [[]],
        //     ApprovalType: 1,
        //     ApprovalOperatorEmployeeList: [], //已审批人员
        //     NoApprovalEmployeeList: [], //未审批人员
        //     CCEmployeeList: [], //抄送人
        //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
        //     ApprovalState: 1, //1: 进行中; 2: 已完成
        //     ApprovalResult: 1, //1: 通过； 2：不通过
        // },
      }
    },
    handleChangeManager(users) {
      if (users && users.length > 0) {
        this.formData.ImplementPrincipalIdList = users;
      } else {
        this.formData.ImplementPrincipalIdList = [];
      }
      this.$refs["formData"].validateField("ImplementPrincipalIdList");
    },
    handleClearOrder() {
      this.formData.OrderId = '';
      this.formData.OrderNumber = '';
    },
    handleChangeUsers(orders) {
      if (orders && orders.length > 0) {
        this.$refs.formData.clearValidate('Order.OrderNumber');
        this.formData.OrderId = orders[0].Id;
        this.formData.OrderNumber = orders[0].OrderNumber;
      } else {
        this.formData.OrderId = '';
        this.formData.OrderNumber = '';
      }
      this.dialogAccessUsers = false
    },
    getAreas() {
      regionalManagement.getPoorListByCondition({ Level: 1 }).then(res => {
        var orgs = res.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.RegionalName,
            ParentId: item.ParentId
          };
        });
        var tree = listToTreeSelect(orgs);
        this.treedata = tree;
        if (this.dialogStatus == 'create' && this.regionalId) {
          this.formData.RegionalId = this.regionalId
        }
      })
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
    createData() {
      let validate = this.$refs.formData.validate()
      // let approvalPanelValidate = this.$refs.approvalPanel.validate()
      Promise.all([validate]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        // postData.Approval = this.$refs.approvalPanel.getData() //审批层区块
        let arr = postData.ImplementPrincipalIdList.map(v => v.EmployeeId);
        postData.ImplementPrincipalIdList = arr;
        let result = null
        // postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
        // postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
        postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
        this.disabledBtn = true
        if (this.dialogStatus == 'create') {
          delete postData.Id
          delete postData.Code
          result = impManagement.add(postData)
        } else if (this.dialogStatus == 'update') {
          let params = {
            //   "ChangeEditRequestModel": {
            //     "ChangeReason": postData.ChangeReason,
            //     "AttachmentIdList": postData.AttachmentIdList,
            //     "Approval": postData.Approval
            //   },
            "ImplementHistoryEditRequestModel": {
              "AttachmentIdList": postData.AttachmentIdList,
              "ImplementId": postData.Id,
              "Code": postData.Code,
              "Name": postData.Name,
              "EngineeringNumber": postData.EngineeringNumber,
              "RegionalId": postData.RegionalId,
              "OrderNumber": postData.OrderNumber,
              "ImplementationTemplateId": postData.ImplementationTemplateId,
              "OrderId": postData.OrderId,
              "CostBudget": postData.CostBudget,
              "Year": postData.Year,
              "ManpowerBudget": postData.ManpowerBudget,
              "Remarks": postData.Remarks,
              "ImplementPrincipalIdList": postData.ImplementPrincipalIdList,
            }
          }
          result = impManagement.edit(params)
        }
        result.then(res => {
          this.disabledBtn = false
          this.$notify({
            title: "提示",
            message: "保存成功",
            type: "success",
            duration: 2000
          });
          this.$emit('saveSuccess');
        }).catch(err => {
          this.disabledBtn = false
        })
      })
    },
    handleFilesUpChange(files) {
      this.formData.AttachmentList = files
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 35%;
  }
}
// .staffingBox{
//     padding-bottom: 20px;
// }
.lastDiv {
  height: calc(100% - 100px);
  overflow-y: auto;
}
</style>