<!--批量修改有效期-->
<template>
    <!--组件内容区-->
    <app-dialog title="修改有效期" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
        <template slot="body">
            <el-form
                :rules="formRules"
                ref="formRef"
                :model="formModel"
                label-position="right"
                label-width="100px"
                v-loading='loading'
            >
            <el-form-item label="已选中">{{ids.length}}</el-form-item>
            <el-form-item label="有效期至" prop="ValidDate">
                <el-date-picker v-model="formModel.ValidDate" type="date" align="right" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <app-button :buttonType="2" @click="handleClose"></app-button>
            <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
        </template>
    </app-dialog>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
// 企业资质
import * as enterpriseQualificationApi from '@/api/personnelManagement/enterpriseQualification'

// 商标注册证
import * as TrademarkLicenseApi from '@/api/personnelManagement/TrademarkLicense'

// 专利及软件著作
import * as patentApi from '@/api/personnelManagement/Patent'

// 型式试验证书
import * as TestCertificateApi from '@/api/personnelManagement/TestCertificate'
export default {
    /**名称 */
    name: "enterprise-qualification-modify-classify",
    /**组件声明 */
    components: {},
    /**参数区 */
    props: {
        ids: {
            type: Array,
            default: () => {
                return []
            }
        },
        componentType: {
            type: Number,
            require: true,
            default: 1
        }
    },
    /**数据区 */
    data() {
        return {
            commonApi: null,
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            /**表单模型 */
            formModel: { 
                ValidDate: null
            },
            /**表单规则 */
            formRules: {
                ValidDate: { fieldName: "有效期", rules: [{ required: true }] },
            }
        };
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                switch (this.componentType) {
                    case 1:
                        this.commonApi = enterpriseQualificationApi
                        break;
                    case 2:
                        this.commonApi = TrademarkLicenseApi
                        break;
                    case 3:
                        this.commonApi = patentApi
                        break;
                    case 4:
                        this.commonApi = TestCertificateApi
                        break;
                }
                
                this.formModel = this.$options.data().formModel
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    self.buttonLoading = true;
                    let result = null
                    if(this.componentType == 4) {
                        result = self.commonApi.BatchEditValidDate({
                            IdList: self.ids,
                            ValidDate: self.formModel.ValidDate
                        })
                    }else{
                        result = self.commonApi.BatchEditValidDate({
                            ids: self.ids,
                            value: self.formModel.ValidDate
                        })
                    }

                    result.then(response => {
                        self.buttonLoading = false;
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.buttonLoading = false
                    })
                }
            });
        },
        getDetail() {
            this.loading = true
            systemJob.detail({id: this.id}).then(response => {
                this.loading = false
                this.formModel = Object.assign({}, this.formModel, response)
            }).catch(err => {
                this.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
