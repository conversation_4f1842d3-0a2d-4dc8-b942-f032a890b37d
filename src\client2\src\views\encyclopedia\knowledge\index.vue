<template>
<!-- 知识库 -->
<div class="app-container">
    <div class="bg-white">
        <div>
            <page-title title="知识库" :showBackBtn='true' @goBack="goBack" text-bold></page-title>
        </div>
        <div class="pageWrapper">
            <div class="product-list">
                <el-row class="tabsBox">
                    <div class="tabsBox_item" v-for="tabItem in pageTabData" :key="tabItem.key"
                    :class="{'active':pageTabType==tabItem.key}" @click="pageTabType=tabItem.key">
                        <i style="font-size: 16px;vertical-align: middle;" :class="tabItem.icon"></i>
                        {{tabItem.label}}
                        <template v-if="tabItem.key!=1">（{{tabItem.value}}）</template>
                    </div>
                </el-row>
            </div>
            
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="flexWarp" style="padding:10px 10px 0px 10px;line-height: 28px;">
                    <div class="flexColumns">
                        <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                            <template slot="Keywords">
                                <el-input placeholder="搜索标题/导语..." @clear='getList' v-antiShake='{
                                        time: 300,
                                        callback: () => {
                                            getList()
                                        }
                                    }' clearable v-model.trim="listQuery.Keywords"></el-input>
                            </template>
                            <template slot="Publisher">
                                <emp-selector :beforeConfirm='handlePublisherBeforeConfirm' :showType="2"
                                :multiple="true" :list="listQuery.PublisherList" @change="handlePublisherChangeManager"></emp-selector>
                            </template>
                            <template slot="CreateTime">
                                <el-date-picker v-model="listQuery.CreateTime" type="daterange"
                                align="right" unlink-panels range-separator="-" start-placeholder
                                end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                style="width: 100%;"></el-date-picker>
                            </template>
                            <template slot="btnsArea">
                                <el-button type="primary" @click="handelAdd(null)" style="margin-left:10px;">我要投稿</el-button>
                            </template>
                        </app-table-form>
                    </div>
                    <div class="flexWarp">
                        <div>文章分类</div>
                        <div class="flexColumns">
                            <treeselect :normalizer="normalizer" class="treeselect-common" style="width:250px;margin-left:10px;"
                                :options="treeData" :default-expand-level="3"
                                :multiple="false" :open-on-click="true" :open-on-focus="true"
                                :clear-on-select="true" v-model="listQuery.ClassifyId" placeholder="请选择文章分类"
                                :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"
                                :append-to-body="true" zIndex='9999'>
                            </treeselect>
                        </div>
                    </div>
                </div>
                <el-row class="grid_content __dynamicTabWrapper" v-loading="listLoading">
                    <el-card shadow="hover" :body-style="{ padding: '10px' }" v-for="(cardItem,cardIndex) in tabDatas" :key="cardIndex">
                        <div class="flexWarp">
                            <div class="logoBox pointer" @click="handelDetail(cardItem)">
                                <img :src="cardItem.CoverPath||defImgUrl" />
                                <div class="tips">{{cardItem.ClassifyName}}</div>
                            </div>
                            <div class="flexColumns" style="width: calc(100% - 220px);">
                                <div class="title flexWarp">
                                    <div class="item-status draft_icon" v-if="pageTabType==4&&cardItem.IsDraft">草稿</div>
                                    <div class="item-status draft_icon" v-if="pageTabType==3&&cardItem.KnowledgeApprovalStatus!=2"
                                    :style="{background: getKnowledgeApprovalStatusObj(cardItem.KnowledgeApprovalStatus).bgColor,
                                            color: getKnowledgeApprovalStatusObj(cardItem.KnowledgeApprovalStatus).color}">
                                        {{ getKnowledgeApprovalStatusObj(cardItem.KnowledgeApprovalStatus).label || '无' }}
                                    </div>
                                    <div class="pointer flexColumns textEllipsis" @click="handelDetail(cardItem)" :title="cardItem.Title">{{cardItem.Title}}</div>
                                    <el-dropdown v-if="showEdit(cardItem)" class="moreBtnBox" trigger="click" @command="handleCommand($event, cardItem.Id)">
                                        <i class="el-icon-more"></i>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="update">编辑</el-dropdown-item>
                                            <el-dropdown-item command="delete">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                                <div class="content colorAAA pointer" @click="handelDetail(cardItem)">{{cardItem.Introduction}}</div>
                                <div class="describeMain flexWarp">
                                    <div class="flexColumns">
                                        <div class="flexWarp">
                                            <!-- <template v-for="(avatarItem,avatarIndex) in cardItem.PublisherList">
                                                <el-tooltip :key="avatarItem.EmployeeId" v-if="avatarIndex<3"
                                                effect="dark" :content="avatarItem.Name" placement="top-start">
                                                    <el-avatar :size="40" style="margin-right:5px;">
                                                        <img :src="avatarItem.Avatar"/>
                                                    </el-avatar>
                                                </el-tooltip>
                                            </template> -->
                                            <div class="flexColumns">
                                                <div>{{cardItem.PublisherList.map(s => s.Name).join('、')}}</div>
                                                <div class="colorAAA">{{cardItem.CreateTime | dateFilter('YYYY-MM-DD')}}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="describeBox flexWarp colorAAA">
                                        <div class="describeBox_item">
                                            {{cardItem.Pageviews}} 浏览 <i class="el-icon-view"></i>
                                        </div>
                                        <div class="describeBox_item pointer" :class="{'isFavorite': cardItem.IsFavorite}" @click="favoritesChange(cardItem)">
                                            {{cardItem.FavoritesNumber}} {{cardItem.IsFavorite?'已收藏':'收藏'}} <i class="el-icon-star-on"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                    <no-data v-if="showNoData"></no-data>
                </el-row>
                <pagination :total="total" :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize" @pagination="handleCurrentChange"
                @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
</div>
</template>
<script>
import NoData from "@/views/common/components/noData";
import { getUserInfo } from "@/utils/auth";
import * as KnowledgeApi from '@/api/knowledge/Knowledge'
import * as classifyApi from '@/api/classify'
import { listToTreeSelect } from "@/utils";
import empSelector from "@/views/common/empSelector";
import Treeselect from "@riophae/vue-treeselect";
import indexPageMixin from "@/mixins/indexPage";

import { KnowledgeApprovalStatusEnum } from '@/views/knowledge/knowledgeManagement/enum.js'
export default {
    name: 'knowledge-knowledge-index',
    mixins: [indexPageMixin],
    components: {
        NoData,
        Treeselect,
        empSelector,
    },
    computed: {
        returnUrl() {
            let url = `${this.$route.path}?pageTabType=${this.pageTabType}&ClassifyId=${this.listQuery.ClassifyId||''}&PageIndex=${this.listQuery.PageIndex}&PageSize=${this.listQuery.PageSize}`
            return url
        },
    },
    watch: {
        pageTabType() {
            this.getList();
        },
        'listQuery.ClassifyId' () {
            this.getList();
        }
    },
    data() {
        return {
            showNoData: false,
            routeQuery: {},
            defImgUrl: require('../../../assets/images/materialData_notData.png'),
            defavatar: require("../../../assets/images/avatar3.png"),
            pageTabType: 1,
            pageTabData: [
                { key: 1, label: '知识库', value: 0, icon: 'el-icon-notebook-1'},
                { key: 2, label: '我的收藏', value: 0, icon: 'el-icon-star-on'},
                { key: 3, label: '我的投稿', value: 0, icon: 'el-icon-s-promotion'},
                { key: 4, label: '我的草稿', value: 0, icon: 'el-icon-document'},
            ],

            treeData: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },

            favoritesBtnDisabled: false,

            listLoading: false,
            layoutMode: 'simple',
            total: 0,
            listQuery: {
                ClassifyId: null
            },
            tabDatas: [],
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "Publisher", label: "发布人" },
                { prop: "CreateTime", label: "发布时间" },
            ],
        };
    },
    created() {
        this.routeQuery = { ...this.routeQuery, ...this.$route.query};
        let {ClassifyId,PageIndex,PageSize} = this.routeQuery;
        this.listQuery.ClassifyId = ClassifyId || null;
        this.listQuery.PageIndex = PageIndex ? Number(PageIndex) : 1;
        this.listQuery.PageSize = PageSize ? Number(PageSize) : 20;
        this.getTreeData();
        this.getStatisticsCount();
        this.getList();
    },
    methods: {
        // 收藏
        favoritesChange(row){
            if(this.favoritesBtnDisabled) return false;
            this.favoritesBtnDisabled = true;
            KnowledgeApi.favorites({Id:row.Id}).then(res => {
                this.favoritesBtnDisabled = false;
                this.getStatisticsCount();
                this.getList();
                this.$notify({
                    title: "成功",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
            }).catch(err => {
                this.favoritesBtnDisabled = false;
            });
        },
        handleDelete(id){
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                KnowledgeApi.del([id]).then(res => {
                    this.getStatisticsCount();
                    this.getList();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                });
            });
        },
        handleCommand(optType, id){
            switch (optType) {
                case "update":
                    this.handelAdd(id)
                    break;
                case "delete":
                    this.handleDelete(id);
                    break;
                default:
                    break;
            }
        },
        handelAdd(id=null){
            let url = `/encyclopedia/knowledge/create?backUrl=${encodeURIComponent(this.returnUrl)}`;
            if (id) url = `/encyclopedia/knowledge/create?id=${id}&backUrl=${encodeURIComponent(this.returnUrl)}`;
            this.$router.push({ path: url });
        },
        handelDetail(row){
            this.$router.push({ path: `/encyclopedia/knowledge/detail/${row.Id}?backUrl=${encodeURIComponent(this.returnUrl)}` });
        },
        // 获取文章分类
        getTreeData() {
            let self = this,
                postDatas = {
                    PageIndex: 1,
                    PageSize: 10000,
                    BusinessType: 17
                }
            classifyApi.getListPage(postDatas).then(res => {
                var list = (res.Items || []).map((item, index, input) => {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId
                    };
                });
                self.treeData = listToTreeSelect(list, undefined, undefined, undefined, 'Id');
            });
        },
        // 查询左侧统计数量
        getStatisticsCount(){
            let self = this;
            KnowledgeApi.statisticsCount().then(res => {
                self.pageTabData.map(s=>{
                    if(s.key == 2){
                        s.value = res.FavoriteCount
                    }
                    if(s.key == 3){
                        s.value = res.PublisherCount
                    }
                    if(s.key == 4){
                        s.value = res.DraftCount
                    }
                })
            });
        },
        handlePublisherBeforeConfirm(users) {
            if(users && users.length > 1000) {
                this.$message({
                    message: '提交人不得超过1000人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handlePublisherChangeManager(users) {
            // console.log(users)
            if (users && users.length > 0) {
                this.listQuery.PublisherList = users;
            } else {
                this.listQuery.PublisherList = [];
            }
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        onResetSearch() {
            this.listQuery.KeyWords = "";
            this.listQuery.PublisherList = [];
            this.listQuery.CreateTime = null;
            this.getList(); //刷新列表
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        getList(){
            let self = this;
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            if(postData.PublisherList&&postData.PublisherList.length>0){
                postData.Publisher = postData.PublisherList.map(s=> s.EmployeeId).toString();
                delete postData.PublisherList
            }
            if (postData.CreateTime&&postData.CreateTime.length>0) {
                postData.CreateTimeStart = postData.CreateTime[0]
                postData.CreateTimeEnd = postData.CreateTime[1]
                delete postData.CreateTime
            }
            if(self.pageTabType == 1) {
                postData.KnowledgeApprovalStatus = 2
            }
            if(self.pageTabType == 2) {
                postData.IsFavorite = true
            }
            if(self.pageTabType == 3) {
                postData.IsPublisher = true
            }
            if(self.pageTabType == 4) {
                postData.IsDraft = true
            }
            // console.log(postData)
            this.showNoData = false;
            KnowledgeApi.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.showNoData = res.Items.length===0;
                this.total = res.Total;
            });
        },
        goBack() {
            this.$router.go(-1)
        },
        showEdit(row){
            let userInfo = getUserInfo();
            return (row.PublisherIdList.some(s=>s == userInfo.employeeid) || row.CreateEmployeeId == userInfo.employeeid)&&this.pageTabType!=2
        },
        getKnowledgeApprovalStatusObj(val) {
            return KnowledgeApprovalStatusEnum.find(s => s.value == val) || {}
        },
    }
}
</script>
<style scoped>
.content-wrapper >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>
<style lang="scss" scoped>
.textEllipsis{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
}
.draft_icon{
    font-size: 12px;
    font-weight: normal;
    vertical-align: middle;
    margin-right: 5px;
    color: #67c23a;
    height: 20px;
    line-height: 20px;
    border-color: #e1f3d8;
    margin-top: 10px;
    background-color: #f0f9eb;
}
.pointer{
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
.flexWarp{
    display: flex;
}
.flexColumns{
    flex: 1;
}
.colorAAA{
    color: #AAAAAA;
}
.el-icon-more{
    transform: rotate(90deg);
}
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    top: 40px;
    right: 0;
    bottom: 0;
    .product-list {
        width: 180px;
        display: flex;
        .tabsBox{
            width: 100%;
            border-right: 1px solid #dcdfe6;
            &_item{
                background-color: #fff;
                padding: 10px;
                height: 40px;
                line-height: 20px;
                cursor: pointer;
                .svg-icon{
                    float: left;
                    margin-right: 7px;
                }
                &.active{
                    background-color: #f0f7ff;
                }
                &:hover{
                    background-color: #F5F7FA;
                }
            }
        }
    }
    
    .content-wrapper {
        width: calc(100% - 180px);
        flex: 1;
        overflow-y: auto;

        .grid_content{
            padding: 0 10px 10px 10px;
            overflow-y: auto;
            .el-card{
                margin-top: 10px;
            }
            .logoBox{
                position: relative;
                width: 200px;
                height: 140px;
                margin-right: 20px;
                img{
                    width: 200px;
                    height: 140px;
                    border: none;
                }
                .tips{
                    position: absolute;
                    width: 100%;
                    height: 36px;
                    line-height: 36px;
                    padding: 0 5px;
                    bottom: 0;
                    left: 0;
                    background-color: #7f7f7f;
                    opacity: 0.8;
                    color: #ffffff;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    word-break: break-all;
                }
            }
            .title{
                position: relative;
                font-weight: 700;
                font-size: 18px;
                height: 40px;
                line-height: 40px;
                padding-right: 25px;
                .moreBtnBox{
                    position: absolute;
                    right: 0;
                    top: 0;
                    line-height: 1;
                    cursor: pointer;
                }
            }
            .content{
                width: 100%;
                height: 40px;
                line-height: 20px;
                font-size: 14px;
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            .describeMain{
                margin-top: 20px;
                width: 100%;
                height: 40px;
                line-height: 20px;
                
                .describeBox{
                    height: 40px;
                    line-height: 40px;
                    &_item{
                        color: #aaaaaa;
                        padding-left: 20px;
                        &.isFavorite{
                            color: #F59A23;
                        }
                    }
                }
            }
        }
    }
}
</style>