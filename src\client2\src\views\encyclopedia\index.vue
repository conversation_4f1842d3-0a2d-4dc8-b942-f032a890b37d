<template>
  <div class="app-container">
    <div class="encyclopedia-div">

      <!-- <el-row :gutter="20">
        <el-col :span="6">
          <el-input v-model.trim="KeyWords" placeholder="搜索业务..." @input="handleSearch" clearable></el-input>
        </el-col>
      </el-row> -->

      <template v-for="(group, idx) in encyclopediaData">
        <div class="group-title" :key="`group-title-${idx}`">{{ group.groupName }}</div>
        <el-row :key="`group${idx}`" :gutter="20" style="display: flex; flex-flow: wrap;">
            <template v-for="item in group.list">
              <el-col :span="6" :key="item.Name" v-if="item.IsShow" style="padding-bottom:20px;min-width: 280px;max-width: 335px;">
                 <!-- :style="{'background-color': item.BackGroundColor}" -->
                <el-card shadow="hover" class="grid-content" :body-style="{ padding: '0px' }">
                  <div class="item-wrapper" @click="handleClick(item)">
                    <div class="img-wrapper">
                      <img :src="item.ImgPath" class="image">
                    </div>

                    <div class="div">
                      <div class="title">
                        <span class="omit" :title="item.Name">{{item.Name}}</span>
                        <div  v-if="(haveMenusPermission('/procureInventory/materialData') && item.BusinessType === 'materialDatabase') 
                        || (haveMenusPermission('/knowledge/knowledgeManagement') && item.BusinessType === 'knowledgeIndex')
                        || (haveMenusPermission('/knowledge/templateManagement') && item.BusinessType === 'templateIndex') 
                        || (haveMenusPermission('/failureCase') && item.BusinessType === 'failureCase')
                        || (haveMenusPermission('/informationCenter/implementationGuide') && item.BusinessType === 'implementationGuide')
                        || (haveMenusPermission('/informationCenter/implementationProblem') && item.BusinessType === 'implementationProblem')" >
                          <i style="color: #409eff; margin-right: 2px;" class="el-icon-s-tools"></i>
                          <app-table-row-button @click.stop="handleBtnClick(item)" :type='1' text='管理'></app-table-row-button>
                        </div>
                      </div>
                      <p style="margin: 10px 0;">{{item.Content}}</p>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </template>
        </el-row>
      </template>

      <!-- <div class="group-title">敬请期待...</div>
      <div>后期将继续推出更多业务百科内容，请多关注...</div> -->

    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      KeyWords: '',
      encyclopediaData: [],

      encyclopediaList: [],
    };
  },
  created() {
    this.getDataList();
  },
  mounted() {
  },
  computed:{
  },
  methods: {
    handleBtnClick(item) {
      switch (item.BusinessType) {
            case 'failureCase':
                this.$router.push(`/failureCase`);
                break;
            case 'implementationGuide':
                this.$router.push(`/informationCenter/implementationGuide`);
                break;
            case 'implementationProblem':
                this.$router.push(`/informationCenter/implementationProblem`);
                break;
            case 'materialDatabase':
                this.$router.push(`/procureInventory/materialData`);
                break;
            case 'knowledgeIndex':
                this.$router.push(`/knowledge/knowledgeManagement`);
                break;
            case 'templateIndex':
                this.$router.push(`/knowledge/templateManagement`);
                break;
        }
    },
    handleClick(item) {
        switch (item.BusinessType) {
            case 'failureCase':
                this.$router.push(`/encyclopedia/failureCase/index`);
                break;
            case 'implementationGuide':
                this.$router.push(`/encyclopedia/implementationGuide/index`);
                break;
            case 'implementationProblem':
                this.$router.push(`/encyclopedia/implementationProblem/index`);
                break;
            case 'materialDatabase':
                this.$router.push(`/encyclopedia/materialDatabase/index`);
                break;
            case 'knowledgeIndex':
                this.$router.push(`/encyclopedia/knowledge/index`);
                break;
            case 'templateIndex':
                this.$router.push(`/encyclopedia/templateRepository/index`);
                break;
        }
    },

    handleSearch() {
      let groups = JSON.parse(JSON.stringify(this.encyclopediaList))
      if(this.KeyWords) {
        groups = groups.reduce((pres, cur) => {
          let filterList = cur.list.filter(s => s.Name.indexOf(this.KeyWords) != -1) || []
          if(filterList.length > 0) {
            cur.list = filterList
            pres = pres.concat([cur])
          }

          return pres
        }, [])
      }

      this.encyclopediaData = groups
    },

    getDataList() {
      let btns = this.topBtns;
      
      this.encyclopediaList = [
        {
          groupName: '生产安装',
          list: [{
            Name: '维修故障案例',
            Content: '设备故障案例查询，帮助你快速查找、定位、解决问题。',
            ImgPath: require("../../assets/images/weixiuguzhanganli.png"),
            
            BackGroundColor: 'rgb(230, 162, 60)',
            IsShow: btns.findIndex(s => s.DomId == "btnViewProblem") != -1,
            BusinessType: 'failureCase'
          }, {
            Name: '软件实施常见问题',
            Content: '软件实施常见问题百科，帮助你快速查找常见问题的解决方案。',
            ImgPath: require("../../assets/images/ruanjianshishichangjianwenti.png"),
            BackGroundColor: 'rgb(103, 194, 58)',
            IsShow: btns.findIndex(s => s.DomId == "btnViewCase") != -1,
            BusinessType: 'implementationProblem'
          }, {
            Name: '软件实施指南',
            Content: '站点软件实施规范参考查询',
            ImgPath: require("../../assets/images/ruanjianshishizhinan.png"),
            BackGroundColor: 'rgb(64, 158, 255)',
            IsShow: btns.findIndex(s => s.DomId == "btnViewGuide") != -1,
            BusinessType: 'implementationGuide'
          }]
        },{
          groupName: '组织过程资产',
          list: [{
              Name: '知识库',
              Content: '企业知识库的查阅、分享',
              ImgPath: require("../../assets/images/zhishiku.png"),
              BackGroundColor: 'rgb(194,128,255)',
              IsShow: btns.findIndex(s => s.DomId == "btnViewKnowledge") != -1,
              BusinessType: 'knowledgeIndex'
          }, {
              Name: '组织资源库',
              Content: '提供企业内部使用的各类组织资源下载',
              ImgPath: require("../../assets/images/zuzhizhiyuanku.png"),
              BackGroundColor: 'rgb(0,191,191)',
              IsShow: btns.findIndex(s => s.DomId == "btnViewTemplate") != -1,
              BusinessType: 'templateIndex'
          }]
        },{
          groupName: '采购库存',
          list: [{
            Name: '材料资料库',
            Content: '用于管理企业材料资料的物料信息。',
            ImgPath: require("../../assets/images/cailiaozhiliaoku.png"),
            BackGroundColor: 'rgb(128,128,255)',
            IsShow: btns.findIndex(s => s.DomId == "btnViewDatabase") != -1,
            BusinessType: 'materialDatabase'
          }]
        }
      ]
      
      this.encyclopediaData = this.encyclopediaList;
    }
  }
}
</script>

<style lang="scss" scoped>
.encyclopedia-div {
  padding: 20px;

  .grid-content {
    /deep/.el-card__body{
      height: 100%;
    }
    border-radius: 10px;
    // height: 140px;
    // min-height: 140px;
    cursor: pointer;
    .item-wrapper{
      height: 120px;
      display: flex;
      .img-wrapper{
        width: 120px;
        height: 100%;
        padding-left: 10px;
        display: flex;
        justify-items: center;
        align-items: center;
      }
    }

  }

  .image {
    width: 100px;
    height: 100px;
  }
  .div {
    // width: 80%;
    flex: 1;
    padding: 10px;
    display: flex;
    flex-direction: column;
    .title {
      font-family: "Arial Negreta", "Arial Normal", Arial, sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      // color: rgb(255, 255, 255);
      display: flex;
      align-items: center;
      span{
        width: 0;
        flex: 1;
      }
    }
    p {
      // color: rgb(255, 255, 255);
    }
  }
}

.el-row {
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0;
  }
}

.group-title{
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}
</style>
