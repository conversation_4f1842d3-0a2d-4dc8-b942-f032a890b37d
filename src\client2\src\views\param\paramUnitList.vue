<!--参数单位列表-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog title="参数单位管理" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
      <template slot="body">
        <el-row>
          <el-button type="primary" @click="handleAdd">添加单位</el-button>
        </el-row>
        <el-row>
          <el-table
            fit
            :data="tableData"
            style="width: 100%"
            v-loading="tableLoading"
            max-height="500"
            :header-cell-style="{'text-align':'center'}"
          >
            <el-table-column type="index" label="序号"></el-table-column>
            <el-table-column prop="Name" label="参数单位名称"></el-table-column>
            <el-table-column prop="Remark" label="备注"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="tableParam.PageIndex"
            :size.sync="tableParam.PageSize"
            @pagination="handleCurrentChange"
          />
        </el-row>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
      </template>
    </app-dialog>

    <!--弹窗组件区-->
    <!--参数单位编辑弹窗-->
    <paramUnitEdit
      :id="paramUnitId"
      :dialogStatus="paramUnitEditDialogStatus"
      :dialogFormVisible="paramUnitEditDialogFormVisible"
      @closeDialog="paramUnitEditCloseDialog"
      @saveSuccess="paramUnitEditSaveSuccess"
    ></paramUnitEdit>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import paramUnitEdit from "./paramUnitEdit";
import indexPageMixin from "@/mixins/indexPage";
import * as param from "@/api/param";

export default {
  /**名称 */
  name: "param-unit-list",
  mixins: [indexPageMixin],
  /**组件声明 */
  components: { paramUnitEdit },
  /**参数区 */
  props: {},
  /**数据区 */
  data() {
    return {
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**列表查询参数 */
      tableParam: { PageIndex: 1, PageSize: 10 },
      /**总数 */
      total: 0,
      /**列表数据 */
      tableData: [],
      /**列表加载中 */
      tableLoading: false,
      /**编辑弹窗 */
      paramUnitEditDialogStatus: "create",
      paramUnitEditDialogFormVisible: false,
      paramUnitId: ""
    };
  },
  /**计算属性---响应式依赖 */
  computed: {},
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.loadListData();
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    loadListData() {
      param.getParamUnitListPage(this.tableParam).then(response => {
        console.log(response);
        this.total = response.Total;
        this.tableData = response.Items;
      });
    },
    /**添加 */
    handleAdd() {
      this.paramUnitId = "";
      this.paramUnitEditDialogStatus = "create";
      this.paramUnitEditDialogFormVisible = true;
    },
    /**编辑 */
    handleEdit(data) {
      this.paramUnitId = data.Id;
      this.paramUnitEditDialogStatus = "update";
      this.paramUnitEditDialogFormVisible = true;
    },
    /**删除 */
    handleDelete(data) {
      let _this = this;
      _this
        .$confirm(`是否确认删除${data.Name}吗?`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          param.deleteParamUnit([data.Id]).then(response => {
            _this.loadListData();
            _this.$notify({
              title: "成功",
              message: "删除成功",
              type: "success",
              duration: 2000
            });
          });
        });
    },
    /**弹窗保存成功 */
    paramUnitEditSaveSuccess() {
      this.tableParam.PageIndex = 1;
      this.loadListData();
    },
    /**关闭弹窗 */
    paramUnitEditCloseDialog() {
      this.paramUnitEditDialogFormVisible = false;
    },
    /**分页页码切换 */
    handleCurrentChange(val) {
      this.tableParam.PageIndex = val.page;
      this.tableParam.PageSize = val.size;
      this.loadListData();
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


