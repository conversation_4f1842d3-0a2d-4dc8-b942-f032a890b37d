<template>
  <div class="createEmployee">
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="700"
    >
      <template slot="body">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基本信息" name="first"></el-tab-pane>
          <el-tab-pane label="详情信息" name="second"></el-tab-pane>
          <el-tab-pane label="附件信息" name="third"></el-tab-pane>
        </el-tabs>

        <!-- 基本信息表单 -->
        <el-form
          v-show="activeName == 'first'"
          ref="basicsFormData"
          :model="basicsFormData"
          v-loading="basicsFormLoading"
          label-position="right"
          :label-width="labelWidth"
        >
          <el-card class="box-card">
            <div class="text item">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="姓名" prop="Name">
                    <label class="basicsLabel">{{ basicsFormData.Name }}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别" prop="Sex">
                    <label class="basicsLabel">{{ basicsFormData.Sex == 1 ? "男" : "女" }}</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="手机号" prop="Mobile">
                    <label class="basicsLabel">{{ basicsFormData.Mobile }}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工号" prop="Number">
                    <label class="basicsLabel">{{ basicsFormData.Number }}</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="主要部门" prop="DepartmentId">
                    <label class="basicsLabel">{{ basicsFormData.DepartmentName }}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="座机" prop="SpecialPlane">
                    <label class="basicsLabel">{{ basicsFormData.SpecialPlane }}</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="其它部门" prop="DepartmentMinorId">
                    <div style="margin-top: 8px;">
                      <div class="basicsLabel" style="line-height: normal;" v-for="item in (basicsFormData.DepartmentMinorName || [])">{{ item }}</div>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="微信" prop="WeChat">
                    <label class="basicsLabel">{{ basicsFormData.WeChat }}</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="所属职位" prop="JobId">
                    <label class="basicsLabel">{{ basicsFormData.JobName }}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="Email">
                    <label class="basicsLabel">{{ basicsFormData.Email }}</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="在职状态" prop="WorkingState">
                    <label class="basicsLabel">
                      {{ basicsFormData.WorkingState | workingStateFilter }}
                    </label>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>

        <!-- 详情信息表单 -->
        <el-form
          v-show="activeName == 'second'"
          :rules="rules"
          ref="detailFormData"
          class="detailFormDataClass"
          :model="detailFormData"
          v-loading="detailFormDataLoading"
          label-position="right"
          :label-width="detailLabelWidth"
          style="max-height: 415px;overflow-y: auto;"
        >
          <!--个人资料-->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>个人资料</span>
            </div>
            <div class="text item">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="身份证号" prop="IdCard">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入身份证号"
                      v-model="detailFormData.IdCard"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="籍贯" prop="NativePlace">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入籍贯"
                      v-model="detailFormData.NativePlace"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="民族" prop="Nationality">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入民族"
                      v-model="detailFormData.Nationality"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="政治面貌" prop="PoliticCountenance">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      :disabled="!editable"
                      placeholder="请选择政治面貌"
                      clearable
                      v-model="detailFormData.PoliticCountenance"
                    >
                      <el-option
                        v-for="item in politicCountenanceEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="婚姻" prop="IsMarried">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      :disabled="!editable"
                      placeholder="请选择婚姻"
                      clearable
                      v-model="detailFormData.IsMarried"
                    >
                      <el-option
                        v-for="item in isMarriedEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生育" prop="Birth">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      :disabled="!editable"
                      placeholder="请选择生育"
                      clearable
                      v-model="detailFormData.Birth"
                    >
                      <el-option
                        v-for="item in birthEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="学历" prop="EducationBackground">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      :disabled="!editable"
                      placeholder="请选择学历"
                      clearable
                      v-model="detailFormData.EducationBackground"
                    >
                      <el-option
                        v-for="item in educationBackgroundEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="学历性质" prop="Education">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      :disabled="!editable"
                      placeholder="请选择学历性质"
                      clearable
                      v-model="detailFormData.Education"
                    >
                      <el-option
                        v-for="item in educationEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="毕业学校" prop="University">
                    <el-input
                      maxlength="50"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入毕业学校"
                      v-model="detailFormData.University"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="毕业时间" prop="UniversityTime">
                    <el-date-picker
                      style="width:100%"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="dat-ipt"
                      v-model="detailFormData.UniversityTime"
                      type="date"
                      :disabled="!editable"
                      placeholder="请选择毕业时间"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="专业" prop="Specialty">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入专业"
                      v-model="detailFormData.Specialty"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="技能" prop="TechnicalAbility">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入技能"
                      v-model="detailFormData.TechnicalAbility"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="入职时间" prop="EntryTime">
                    <div style="display: flex;">
                      <div>
                        <el-date-picker
                          style="width: 230px"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          class="dat-ipt"
                          v-model="detailFormData.EntryTime"
                          type="date"
                          :disabled="!editable"
                          placeholder="请选择入职时间"
                        ></el-date-picker>
                      </div>
                      <div style="padding: 0 10px;">
                        司龄
                        <label>{{ getYear(detailFormData.EntryTime) }}&nbsp;年</label>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="转正时间" prop="RegularizationTime">
                    <el-date-picker
                      style="width:100%"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="dat-ipt"
                      v-model="detailFormData.RegularizationTime"
                      type="date"
                      :disabled="!editable"
                      placeholder="请选择转正时间"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="紧急联系人" prop="EmergencyContact">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入紧急联系人"
                      v-model="detailFormData.EmergencyContact"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="紧急联系人电话" prop="EmergencyContactPhone">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入紧急联系人电话"
                      v-model="detailFormData.EmergencyContactPhone"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="紧急联系人2">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入紧急联系人2"
                      v-model="detailFormData.EmergencyContact1"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="紧急联系人电话2">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入紧急联系人电话2"
                      v-model="detailFormData.EmergencyContactPhone1"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="现居住地址" prop="Address">
                    <el-input
                      maxlength="200"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入现居住地址"
                      v-model="detailFormData.Address"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="实际工作月份" prop="WorkTime">
                    <div style="display: flex;">
                      <div>
                        <el-input-number
                          style="width: 230px;"
                          v-model="detailFormData.WorkTime"
                          :disabled="!editable"
                          :min="0"
                        ></el-input-number>
                      </div>
                      <div style="padding: 0 10px;">
                        工龄
                        <label>{{ lengthOfService }}&nbsp;年</label>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!--合同协议-->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>合同协议</span>
            </div>
            <div class="text item">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="劳动合同编号" prop="ContractNo">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入合同号"
                      v-model="detailFormData.ContractNo"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同状态" prop="ConfidentialityAgreement">
                    <el-radio
                      v-for="(i, idx) in confidentialityAgreementEnum"
                      :key="idx"
                      v-model="detailFormData.ConfidentialityAgreement"
                      :disabled="!editable"
                      :label="i.value"
                    >
                      {{ i.label }}
                    </el-radio>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="detailFormData.ConfidentialityAgreement != 1">
                <el-col :span="12">
                  <el-form-item label="合同主体" prop="LaborContractSubjectId">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      :disabled="!editable"
                      placeholder="请选择合同主体"
                      clearable
                      v-model="detailFormData.LaborContractSubjectId"
                    >
                      <el-option
                        style="width:330px"
                        v-for="item in laborContractSubjects"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item prop="ContractStartTime">
                    <span slot="label">
                      <span
                        v-if="detailFormData.ConfidentialityAgreement == 2"
                        style="color: #f56c6c; margin-right: 4px;"
                      >
                        *
                      </span>
                      合同开始日期
                    </span>
                    <el-date-picker
                      style="width:100%"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="dat-ipt"
                      v-model="detailFormData.ContractStartTime"
                      type="date"
                      :disabled="!editable || detailFormData.ConfidentialityAgreement == 3"
                      placeholder="请选择合同开始时间"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="ContractType">
                    <span slot="label">
                      合同期限
                    </span>
                    <el-radio
                      v-model="detailFormData.ContractType"
                      v-for="t in contractTypeEnum"
                      :key="t.value"
                      :disabled="!editable || detailFormData.ConfidentialityAgreement == 3"
                      :label="t.value"
                    >
                      {{ t.label }}
                    </el-radio>
                  </el-form-item>
                </el-col>

                <el-col :span="12" v-if="detailFormData.ContractType == 1">
                  <el-form-item prop="ContractEndTime">
                    <span slot="label">
                      <span
                        v-if="detailFormData.ConfidentialityAgreement == 2"
                        style="color: #f56c6c; margin-right: 4px;"
                      >
                        *
                      </span>
                      合同到期日期
                    </span>
                    <el-date-picker
                      style="width:100%"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="dat-ipt"
                      v-model="detailFormData.ContractEndTime"
                      type="date"
                      :disabled="!editable || detailFormData.ConfidentialityAgreement == 3"
                      placeholder="请选择合同到期时间"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="detailFormData.ConfidentialityAgreement == 3">
                  <el-form-item label="合同终止原因" prop="ContractEndReason">
                    <el-input
                      maxlength="100"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入合同终止原因"
                      v-model="detailFormData.ContractEndReason"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="detailFormData.ConfidentialityAgreement == 3">
                  <el-form-item label="合同终止日期" prop="ContractEndDate">
                    <el-date-picker
                      style="width:100%"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="dat-ipt"
                      v-model="detailFormData.ContractEndDate"
                      type="date"
                      :disabled="!editable"
                      placeholder="请选择合同终止日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row>
                            <el-col :span="12">
                                <el-form-item label="个人档案编号" prop="PersonalFileNumber">
                                    <el-input maxlength="20" type="text" :disabled="!editable" placeholder="请输入个人档案编号" v-model="detailFormData.PersonalFileNumber"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
            </div>
          </el-card>

          <!--工资卡信息-->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>工资卡信息</span>
            </div>
            <div class="text item">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="工资发放银行" prop="PayrollBank">
                    <el-input
                      maxlength="20"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入工资发放银行"
                      v-model="detailFormData.PayrollBank"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行账号" prop="BankAccount">
                    <el-input
                      maxlength="30"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入银行账号"
                      v-model="detailFormData.BankAccount"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!--其他-->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>其他</span>
            </div>
            <div class="text item">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="生日" prop="Birthday">
                    <el-date-picker
                      style="width:100%"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="dat-ipt"
                      v-model="detailFormData.Birthday"
                      type="date"
                      placeholder="请选择生日"
                      :disabled="!editable"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="兴趣爱好" prop="HobbiesAndInterests">
                    <el-input
                      maxlength="100"
                      type="text"
                      :disabled="!editable"
                      placeholder="请输入兴趣爱好"
                      v-model="detailFormData.HobbiesAndInterests"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>

        <!-- 附件信息表单 -->
        <div v-show="activeName == 'third'">
          <el-card class="box-card" style="height:318px; overflow-y: auto; margin-top:10px">
            <div class="text item">
              <app-uploader
                :readonly="!editable"
                accept="all"
                :fileType="3"
                :max="10000"
                :value="detailFormData.AttachmentIdList"
                :fileSize="1024 * 1024 * 500"
                :minFileSize="100 * 1024"
                @change="handleFilesUpChange"
              ></app-uploader>

              <div v-if="detailFormData.AttachmentIdList == null">
                暂无附件
              </div>
            </div>
          </el-card>
        </div>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <app-button @click="handleSave" v-show="editable" text="保存"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import dayjs from "dayjs";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as systemEmployee from "@/api/personnelManagement/systemEmployee";
import * as laborContractSubject from "@/api/personnelManagement/laborContractSubject";
import { regs } from "@/utils/regs";
import {
  isMarriedEnum,
  educationBackgroundEnum,
  employeeWorkingStateEnum,
  politicCountenanceEnum,
  birthEnum,
  educationEnum,
  confidentialityAgreementEnum,
  contractTypeEnum,
} from "../enum";

export default {
  name: "systemEmployee-create",
  directives: {},
  components: {},
  mixins: [],
  props: {
    dialogStatus: {
      type: String,
    },
    id: {
      type: String,
      default: "",
    },
    selectTypeId: {
      type: String,
      default: "",
    },
  },
  filters: {
    workingStateFilter(val) {
      let obj = employeeWorkingStateEnum.find(s => s.value == val);
      if (obj) {
        return obj.label;
      }
      return "";
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      this.activeName = "first";
      if (val) {
        this.getAllLaborContractSubject();
        this.resetFormData();
        this.getDetail();
        this.getEmployeeRecordDetails();
      }
    },
    "detailFormData.ConfidentialityAgreement": {
      handler(val) {
        this.dynamicValid();
      },
      immediate: true,
    },
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      if (this.dialogStatus == "update") {
        return "编辑员工档案";
      } else if (this.dialogStatus == "detail") {
        return "查看员工档案";
      } else {
        return "";
      }
    },
    // 工龄
    lengthOfService() {
      if (!this.detailFormData?.WorkTime) return 0;
      return Math.floor(this.detailFormData.WorkTime / 12);
    },
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  mounted() {},
  data() {
    return {
      laborContractSubjects: [],
      isMarriedEnum: isMarriedEnum,
      educationBackgroundEnum: educationBackgroundEnum,
      politicCountenanceEnum,
      birthEnum,
      educationEnum,
      confidentialityAgreementEnum,
      contractTypeEnum,

      activeName: "first",
      siling: 0,
      basicsFormLoading: false,
      labelWidth: "100px",
      basicsFormData: {
        EmployeesId: "",
        Name: "",
        Sex: 1,
        Number: "",
        Mobile: "",
        DepartmentId: null,
        DepartmentName: "",
        DepartmentMinorId: [],
        DepartmentMinorName: [],
        JobId: "",
        JobName: "",
        Email: "",
        SpecialPlane: "",
        WeChat: "",
        Status: 1,
        WorkingState: 1,
      },

      detailFormData: {
        // SiLing: 0,
        EmployeeRecordId: "",
        EmployeesId: "",
        IdCard: "",
        NativePlace: "",
        Nationality: "",
        PoliticCountenance: null,
        Birth: null,
        IsMarried: 1,
        EducationBackground: null,
        Education: null,
        University: "",
        UniversityTime: "",
        Specialty: "",
        TechnicalAbility: "",
        WorkTime: null,
        EntryTime: null,
        RegularizationTime: null,
        EmergencyContact: "",
        EmergencyContactPhone: "",
        // WorkYears: 0,
        ContractNo: "",
        ContractStartTime: null,
        ContractEndTime: null,
        ContractType: 1,
        ContractEndDate: null,
        ContractEndReason: null,
        ConfidentialityAgreement: 1,
        // PersonalFileNumber: "",
        PayrollBank: "",
        BankAccount: "",
        Birthday: null,
        HobbiesAndInterests: "",
        AttachmentIdList: [], // 附件ID集合

        EmergencyContact1: "",
        EmergencyContactPhone1: "",
        Address: "",
        LaborContractSubjectId: null,
      },
      detailFormDataLoading: false,
      detailLabelWidth: "120px",

      rules: {
        LaborContractSubjectId: {
          fieldName: "合同主体",
          rules: [{ required: true }],
        },
        ContractEndDate: {
          fieldName: "合同到期日期",
          rules: [{ required: true }],
        },
        ContractEndReason: {
          fieldName: "合同终止原因",
          rules: [{ required: true }],
        },
        RegularizationTime: {
          fieldName: "转正时间",
          rules: [{ required: true }],
        },
        UniversityTime: {
          fieldName: "毕业时间",
          rules: [{ required: true }],
        },
        EducationBackground: {
          fieldName: "学历",
          rules: [{ required: true, trigger: "change" }],
        },
        Education: {
          fieldName: "学历性质",
          rules: [{ required: true, trigger: "change" }],
        },
        Birth: {
          fieldName: "生育",
          rules: [{ required: true, trigger: "change" }],
        },
        PoliticCountenance: {
          fieldName: "政治面貌",
          rules: [{ required: true, trigger: "change" }],
        },
        IsMarried: {
          fieldName: "婚姻",
          rules: [{ required: true, trigger: "change" }],
        },
        Address: {
          fieldName: "现居住地址",
          rules: [{ required: true }],
        },
        University: {
          fieldName: "毕业学校",
          rules: [{ required: true }],
        },
        NativePlace: {
          fieldName: "籍贯",
          rules: [{ required: true }],
        },
        Nationality: {
          fieldName: "民族",
          rules: [{ required: true }],
        },
        WorkTime: {
          fieldName: "实际工作月份",
          rules: [{ required: true }],
        },
        EntryTime: {
          fieldName: "入职时间",
          rules: [{ required: true }],
        },
        IdCard: {
          fieldName: "身份证号码",
          rules: [{ required: true }],
        },
        BankAccount: {
          fieldName: "银行账号",
          rules: [{ required: true }],
        },
        PayrollBank: {
          fieldName: "工资发放银行",
          rules: [{ required: true }],
        },
        // ContractNo:{
        //     fieldName: "劳动合同编号",
        //     rules: [{ required: true }]
        // },
        Birthday: {
          fieldName: "生日",
          rules: [{ required: true }],
        },
        ContractType: {
          fieldName: "合同期限",
          rules: [{ required: true }],
        },
        // ContractStartTime:{
        //     fieldName: "合同开始日期",
        //     rules: [{ required: true }]
        // },
        // ContractEndTime:{
        //     fieldName: "合同到期日期",
        //     rules: [{ required: true }]
        // },

        // IdCard: [{
        //     required: true,
        //     trigger: "blur",
        //     validator: (rule, value, callback) => {
        //         if (value != "" && value != null) {
        //             if ((regs.idCard).test(value) == false) {
        //                 callback(new Error("请输入正确的身份证号码"));
        //             } else {
        //                 callback();
        //             }
        //         } else {
        //             callback();
        //         }
        //     }
        // }],
        // EmergencyContactPhone: [{
        //     required: false,
        //     trigger: "blur",
        //     validator: (rule, value, callback) => {
        //         if (value != "" && value != null) {
        //             if ((regs.phone).test(value) == false) {
        //                 callback(new Error("请输入正确的紧急联系人电话"));
        //             } else {
        //                 callback();
        //             }
        //         } else {
        //             callback();
        //         }
        //     }
        // }],
        // ContractNo: [{
        //     required: false,
        //     trigger: "blur",
        //     validator: (rule, value, callback) => {
        //         if (value != "" && value != null) {
        //             if ((regs.LettersAndNumbers).test(value) == false) {
        //                 callback(new Error("合同号只能输入字母和数字"));
        //             } else {
        //                 callback();
        //             }
        //         } else {
        //             callback();
        //         }
        //     }
        // }],
        // PersonalFileNumber: [{
        //     required: false,
        //     trigger: "blur",
        //     validator: (rule, value, callback) => {
        //         if (value != "" && value != null) {
        //             if ((regs.LettersAndNumbers).test(value) == false) {
        //                 callback(new Error("个人档案编号只能输入字母和数字"));
        //             } else {
        //                 callback();
        //             }
        //         } else {
        //             callback();
        //         }
        //     }
        // }],

        // BankAccount: [{
        //     required: true,
        //     trigger: "blur",
        //     validator: (rule, value, callback) => {
        //         if (value != "" && value != null) {
        //             if ((regs.PositiveIntegers).test(value) == false) {
        //                 callback(new Error("银行账号只能输入数字"));
        //             } else {
        //                 callback();
        //             }
        //         } else {
        //             callback();
        //         }
        //     }
        // }],
      },
    };
  },
  methods: {
    getYear(datetime) {
      //司龄
      let birthday = datetime;
      if (dayjs(birthday).isValid()) {
        let currentDate = dayjs().format("YYYY-MM-DD");
        let yearOfBirth = dayjs(birthday).format("YYYY-MM-DD");
        let workDays = dayjs(currentDate).diff(dayjs(yearOfBirth), "day");
        var sl = Math.floor(workDays / 365);
        this.siling = sl;
        return Math.floor(workDays / 365);
      } else {
        return "-";
      }
    },
    //获取成员档案信息
    getEmployeeRecordDetails() {
      this.detailFormLoading = true;
      systemEmployee
        .getEmployeeRecordDetails({
          id: this.id,
        })
        .then(res => {
          var formObj = Object.assign({}, this.detailFormData, res);
          this.detailFormData = formObj;
          this.detailFormLoading = false;
        });
    },

    //选择附件
    handleFilesUpChange(files) {
      this.detailFormData.AttachmentIdList = files;
    },

    //清理表单
    resetFormData() {
      let temp = {
        // SiLing: 0,
        EmployeeRecordId: "",
        EmployeesId: "",
        IdCard: "",
        NativePlace: "",
        Nationality: "",
        PoliticCountenance: null,
        Birth: null,
        IsMarried: 1,
        EducationBackground: null,
        Education: null,
        University: "",
        UniversityTime: "",
        Specialty: "",
        TechnicalAbility: "",
        EntryTime: null,
        WorkTime: null,
        RegularizationTime: null,
        EmergencyContact: "",
        EmergencyContactPhone: "",
        // WorkYears: 0,
        ContractNo: "",
        ContractStartTime: null,
        ContractEndTime: null,
        ContractType: 1,
        ConfidentialityAgreement: 1,
        // PersonalFileNumber: "",
        PayrollBank: "",
        BankAccount: "",
        Birthday: null,
        HobbiesAndInterests: "",
        AttachmentIdList: [], // 附件ID集合
        EmergencyContact1: "",
        EmergencyContactPhone1: "",
        Address: "",
        LaborContractSubjectId: null,
      };
      this.detailFormData = Object.assign({}, this.detailFormData, temp);
    },

    //获取成员信息
    getDetail() {
      this.basicsFormLoading = true;
      systemEmployee
        .detail({
          id: this.id,
        })
        .then(res => {
          var formObj = Object.assign({}, this.basicsFormData, res);
          this.basicsFormData = formObj;
          this.basicsFormLoading = false;
        });
    },

    //保存
    handleSave() {
      if (this.$refs.detailFormData == null) {
        this.$notify({
          title: "提示",
          message: "保存成功",
          type: "success",
          duration: 2000,
        });
        this.$refs.appDialogRef.createData();
        return;
      }
      this.$refs.detailFormData.validate(valid => {
        //当前只有“实际工作月份”一个字段需要验证（所以写死）
        if (!valid) {
          this.activeName = "second";
        }
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.detailFormData));

          postData.EmployeesId = this.id;

          postData.AttachmentIdList =
            postData.AttachmentIdList && postData.AttachmentIdList.map(s => s.Id);
          postData.WorkYears = this.lengthOfService;
          postData.GsYears = this.siling;

          if (postData.ContractType != 1) {
            postData.ContractEndTime = null;
          }

          //提交数据保存
          systemEmployee.editEmployeeRecord(postData).then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000,
            });
            this.$refs.appDialogRef.createData();
          });
        }
      });
    },
    getAllLaborContractSubject() {
      laborContractSubject.getAllLaborContractSubject({}).then(res => {
        this.laborContractSubjects = res.map(s => {
          return {
            value: s.Id,
            label: s.LaborContractSubjectName,
          };
        });
      });
    },
    dynamicValid() {
      this.rules["ContractStartTime"] = [];
      this.rules["ContractEndTime"] = [];

      if (this.detailFormData.ConfidentialityAgreement == 2) {
        if (!this.rules["ContractStartTime"]) this.rules["ContractStartTime"] = [];

        const validateContractStartTime = (rule, value, callback) => {
          if (!value) {
            callback(new Error("合同开始日期不可为空"));
          } else {
            callback();
          }
        };

        if (!this.rules["ContractEndTime"]) this.rules["ContractEndTime"] = [];

        const validateContractEndTime = (rule, value, callback) => {
          if (!value) {
            callback(new Error("合同到期日期不可为空"));
          } else {
            callback();
          }
        };
        this.rules["ContractStartTime"].push({ validator: validateContractStartTime });
        this.rules["ContractEndTime"].push({ validator: validateContractEndTime });
      }

      this.$nextTick(() => {
        if (this.$refs["detailFormData"]) {
          this.$refs["detailFormData"].clearValidate();
        }
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}

.basicsLabel {
  font-weight: 100 !important;
}

.detailFormDataClass {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.box-card {
  margin-bottom: 10px;
}
</style>
