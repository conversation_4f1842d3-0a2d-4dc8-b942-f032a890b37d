<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title title="数据看板" :subTitle='["工程项目的进度看板"]'></page-title> -->
      <div class="page-wrapper">
        <div class="content-left">
          <!-- isCheckbox表示是否显示单选框 -->
          <!-- isAll表示是否显示全部区域 -->
          <!-- isSubset表示是否显示子集 -->
          <v-tree @changeNode='changeTreeNode' :isAll='true' :isSubset='true'></v-tree>
        </div>
        <div class="content-right __dynamicTabContentWrapper">
          <div style="margin-bottom: 10px;">
            <tags :items='templates' v-model="listQuery.ImplementationTemplateId">
              <template v-for="t in templates" :slot="t.value">
                {{ t.label }}
              </template>
            </tags>
          </div>
          
          <div class="__dynamicTabWrapper">
            <template v-if="jurisdictionTabColumns.length > 0">
              <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="jurisdictionTabColumns" @rowSelectionChanged="rowSelectionChanged" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :isShowOpatColumn="false" fit :startOfTable="startOfTable" :serial='false' :loading='listLoading'>
                <template v-for="(d, idx) in dynamicTabColumns" :slot="`header_${d.attr.prop}`" slot-scope="scope">
                  <div class="omit" style="width: 100%;" :key="idx" :title="scope.column.column.label">
                    {{ scope.column.column.label }}
                  </div>
                </template>

                <template slot="idx" slot-scope="scope">
                  <span>
                    <svg-icon v-if="scope.row.IsConcern" style="font-size: 12px;" icon-class="bookmark-fill"></svg-icon>
                    {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index) }}
                  </span>
                </template>
                <template slot="ContractNumber" slot-scope="scope">
                  {{ scope.row.ContractNumber || "无" }}
                </template>
                <template slot="RegionalStatus" slot-scope="scope">
                  <span :style="{color: getRegionalStatus(scope.row.RegionalStatus).color}">
                    {{ scope.row.RegionalStatus | findState }}
                  </span>
                </template>
                <template slot="ProgressString" slot-scope="scope">
                  <div style="max-width: 85%;">
                    <el-progress :percentage="scope.row.ProgressString" :color="getRegionalStatus(scope.row.RegionalStatus).color"></el-progress>
                  </div>
                </template>
                <template slot="RegionalName" slot-scope="scope">
                  {{ scope.row.RegionalName || '无' }}
                </template>

                <template slot="ParticularYear" slot-scope="scope">
                  {{ scope.row.ParticularYear || '无' }}
                </template>

                <template slot="EngineeringNumber" slot-scope="scope">
                  {{ scope.row.EngineeringNumber || '无' }}
                </template>

                <template v-for="(d) in dynamicTabColumns" :slot="`${d.attr.prop}`" slot-scope="scope">
                  <a v-if="scope.row['main_key_' + d.attr.prop] + '' !== ''" :key="d.Id" href="javascript: void(0);" @click="handleShowDetail(scope.row, d.attr.prop, scope.row.ImplementationRegionalId)">
                    <span :style="{ color: `${getRegionalStatus(scope.row['main_key_' + d.attr.prop] === 0 ? 1 : scope.row['main_key_' + d.attr.prop] === 100 ? 3 : 2).color}` }">
                      {{ scope.row['main_key_' + d.attr.prop] + '' }}%
                    </span>
                  </a>
                  <span v-else :key="d.Id">无</span>
                </template>
                <!-- <template slot="IsFinished" slot-scope="scope">
                      {{ scope.row.IsFinished }}/{{ scope.row.EquipmentCount }}
                  </template> -->
                <!-- <template slot="ImplementPrincipalEmployeeList" slot-scope="scope">
                    {{ scope.row.ImplementPrincipalEmployeeList | listToStringFilter }}
                  </template> -->

                <template slot="conditionArea">
                  <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="handleResetSearch" :layoutMode='layoutMode'>
                    <template slot="MultiCondition">
                      <el-input style="width: 100%;" @clear="getList" v-antiShake='{time: 300,callback: () => {getList()}}' clearable v-model.trim="listQuery.MultiCondition" placeholder="搜索单号/项目名称/合同编号"></el-input>
                    </template>

                    <template slot="Status">
                      <el-select style="width: 100%;" v-model="listQuery.Status" placeholder="" clearable>
                        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </template>

                    <template slot="ParticularYear">
                      <el-date-picker key="xxxyear" style="width: 100%;" v-model="listQuery.ParticularYear" type="year" placeholder="" format='yyyy' value-format='yyyy'></el-date-picker>
                    </template>
                    <template slot="EmployeeString">
                      <el-input style="width: 100%;" clearable v-model.trim="listQuery.EmployeeString"></el-input>
                    </template>

                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                      <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                      <div style="margin-left:5px">
                        <el-button @click="handleSetFollow" type="primary">设为关注</el-button>
                        <el-button @click="handleQuitFollow" type="primary">取消关注</el-button>
                      </div>
                    </template>

                  </app-table-form>
                </template>

              </app-table>
            </template>
          </div>
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>
    <v-export @saveSuccess="handleSuccessExport" @closeDialog="handleCloseExport" :dialogFormVisible="dialogExportVisible" :rData="rData" :cData="cData">
    </v-export>

    <field-shows @saveSuccess="handleSuccessFieldShows" @closeDialog="handleCloseFieldShows" :dialogFormVisible="dialogFieldShowsVisible" :cloumnData="cloumnData" :templateId="templateId">
    </field-shows>

    <detail-page v-if="id && regionalId && dialogFormVisible" @closeDialog="closeDialog" @saveSuccess="() => {}" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" :regionalId='regionalId'></detail-page>

    <!-- 事项 详情、编辑 -->
    <!-- <proces-create
        @closeDialog="closeProcesCreateDialog"
        @saveSuccess="() => {}"
        :dialogFormVisible="dialogProcesCreateFormVisible"
        :dialogStatus="dialogProcesCreateStatus"
        :procesId="procesId"
    ></proces-create> -->
  </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
// import procesCreate from '../engineering/workbench/procesCreate'
// import * as afterService from "@/api/afterSalesMgmt/afterService";
// import { getUserInfo } from "@/utils/auth";
import * as impManagement from "@/api/implementation/impManagement";
import * as impMgmt from "@/api/implementation/impManagement2"
import { vars } from "../common/vars";
// import layoutVue from "../../login/layout.vue";
import vExport from "@/components/Export/index";
import fieldShows from './fieldShows'
import noData from "@/views/common/components/noData";
import detailPage from './detail'
import vTree from '../../afterSalesMgmt/businessMap/common/tree'
import mixins from "../common/mixins";

export default {
  name: "data-dashborad",
  mixins: [indexPageMixin, mixins],
  components: {
    vExport, noData,
    detailPage, fieldShows,
    // procesCreate, 
    vTree
  },
  filters: {
    // listToStringFilter(list) {
    //   if(list && list.length > 0) {
    //     return list.map(s => s.Name).join(',')
    //   }
    //   return '无'
    // },
    findState(d) {
      let result = vars.regionalStatus.find(v => v.value == d);
      if (result) {
        return result.label;
      }
      return ''
    }
  },
  created() {

    this.getTemplates();
  },
  watch: {
    'listQuery.ImplementationTemplateId': {
      handler(val) {
        let dynamicTabColumnsTemp = [] //动态列（模板工序集合）

        this.notRowspanColumns = []
        this.notRowspanColumns.push('RegionalName') //需要加上动态列中的属性

        let temp = this.templates.find(s => s.value == val)
        if (temp) {
          let tempList = temp.list

          tempList.forEach(s => {
            if (dynamicTabColumnsTemp.findIndex(a => a.attr.prop == s.id) == -1) {
              dynamicTabColumnsTemp.push({
                attr: { prop: s.id, label: s.name },
                // attr: { prop: s.id, label: s.name, 'min-width': "2" },
                customHeader: true,
                slot: true
              })

              if (this.notRowspanColumns.findIndex(a => a == s.id) == -1) {
                this.notRowspanColumns.push(s.id)
              }
            }
          })
          this.dynamicTabColumns = dynamicTabColumnsTemp //动态列
        }
        if (val) {
          this.getList();
          this.getDetail();
        }
      },
    }
  },
  data() {
    return {
      layoutMode: 'simple',
      templates: [],
      statusList: vars.regionalStatus,
      total: 0,
      listLoading: false,
      listQuery: {
        MultiCondition: "",
        ImplementationTemplateId: '',
        // ImplementationName: "",
        // ContractNumber: "",
        // EngineeringNumber: "",
        Status: null,
        RegionalId: '',
        ParticularYear: '',
        EmployeeString: ''
      },
      multipleSelection: [],
      tableSearchItems: [
        { prop: "MultiCondition", label: "", mainCondition: true },//多条件搜索
        { prop: "Status", label: "状态" },
        { prop: "ParticularYear", label: "年份" },
        { prop: "EmployeeString", label: "站点负责人" },
      ],

      jurisdictionTabColumns: [],
      tabColumns: [
        {
          attr: { prop: "idx", label: "序号", width: '60', fixed: 'left' },
          slot: true
        },
        {
          attr: { prop: "EngineeringNumber", label: "单号", fixed: 'left', showOverflowTooltip: true },
          slot: true
        },
        {
          attr: { prop: "Name", label: "项目名称", width: '150', fixed: 'left', showOverflowTooltip: true }
        },
        {
          attr: { prop: "RegionalName", label: "实施地区", width: '280', fixed: 'left', showOverflowTooltip: true }
        },
        {
          attr: { prop: "EmployeeString", label: "站点负责人", showOverflowTooltip: true }
        },
        {
          attr: { prop: "ContractNumber", label: "合同编号", showOverflowTooltip: true },
          slot: true
        },
        {
          attr: { prop: "ParticularYear", label: "年份" },
          slot: true
        },
        {
          attr: { prop: "RegionalStatus", label: "状态" },
          slot: true
        },
        {
          attr: { prop: "ProgressString", label: "生产进度", width: '130'},
          slot: true
        },
        {
          attr: { prop: "CompletedOutputValue", label: "已完成产值" },
        },
        {
          attr: { prop: "ContractPrice", label: "总产值" },
        },

      ],
      dynamicTabColumns: [],
      tabDatas: [],
      rData: null,
      cData: [],
      cloumnData: [],
      templateId: "",
      dialogExportVisible: false,
      dialogFieldShowsVisible: false,
      notRowspanColumns: [], //不需要合并的列
      /**
       * 详细
       */
      id: '',
      regionalId: '',
      dialogStatus: '',
      dialogFormVisible: false,

      checkList: [],
      /**
       * 事项编辑
       */
      // procesId: "",
      // dialogProcesCreateStatus: "detail",
      // dialogProcesCreateFormVisible: false,
    };
  },
  beforeRouteLeave(to, from, next) {
    //离开组件的时候触发
    // if (this.resizeFlag) {
    //   clearTimeout(this.resizeFlag);
    // }

    // window.onresize = null;
    next();
  },
  methods: {

    // objectSpanMethod({ row, column, rowIndex, columnIndex }) {
    //   if (column.property && this.notRowspanColumns.findIndex(s => s.toLowerCase() == column.property.toLowerCase()) == -1) {
    //     if (row.rowspan > 0) {
    //       return {
    //         rowspan: row.rowspan,
    //         colspan: 1
    //       };
    //     } else {
    //       return {
    //         rowspan: 0,
    //         colspan: 0
    //       };
    //     }
    //   }
    // },
    getTemplates() {
      let postData = { IsUsed: true }
      impMgmt.getTemplates(postData).then(res => {
        this.templates = res.map(s => {
          s.value = s.Id
          s.label = s.Name
          s.list = s.ImplementationProcedureList.map(n => {
            return {
              id: this.getKey(n.Id),
              name: n.Name
            }
          })
          return s
        })
        if (this.templates.length > 0 && !this.listQuery.ImplementationTemplateId) {
          this.listQuery.ImplementationTemplateId = this.templates[0].value
        }
      })
    },
    handleShowDetail(row, helpKey, regionalId) {


      // if(row.BusType == 1) { //工序
      this.id = row[`main_key_id_${helpKey}`];


      this.regionalId = regionalId
      this.dialogStatus = 'detail';
      this.dialogFormVisible = true;
      // }else if(row.BusType == 2) { //事项
      //     // this.handleProcesCreateDialog(row, 'detail')
      // }
    },

    //获取当前用户设置的列表字段集合
    getDetail() {
      this.jurisdictionTabColumns = [];
      var temp = {};
      temp.Code = 1;
      temp.Type = 2;
      temp.BusinessId = this.listQuery.ImplementationTemplateId;
      let formData = JSON.parse(JSON.stringify(temp));
      impManagement.getPersonalDataCenter(formData).then(res => {
        this.jurisdictionTabColumns = this.getNewColumns(res);
      });
    },

    //根据用户的设置动态控制列是否显示
    getNewColumns(columnList) {
      let allColumns = this.tabColumns.concat(this.dynamicTabColumns);
      if (columnList.length == 0) {
        allColumns.splice(allColumns.findIndex(s => s.attr.label == "总产值"), 1)
        allColumns.splice(allColumns.findIndex(s => s.attr.label == "已完成产值"), 1)
        return allColumns;
      }
      var newColumns = [];
      allColumns.forEach(element => {
        var newObj = columnList.findIndex(s => s == element.attr.label);
        if (newObj != -1) {
          newColumns.push(element);
        }
      });
      return newColumns
    },

    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSuccessExport() { },
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleSuccessFieldShows(res) {
      this.jurisdictionTabColumns = this.getNewColumns(res);
      this.dialogFieldShowsVisible = false;
    },
    handleCloseFieldShows() {
      this.dialogFieldShowsVisible = false;
    },
    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnExport":
          this.handleExport();
          break;
        case "btnFieldShows"://字段显示
          this.handleFieldShows();
          break;
        default:
          break;
      }
    },

    /**设为关注 */
    handleSetFollow() {
      var rows = this.multipleSelection;

      if (rows.length < 1) {
        this.$message({
          message: "至少选择一条",
          type: "error"
        });
        return;
      }
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.ImplementationRegionalId);
      } else {
        ids.push(rows.ImplementationRegionalId);
      }
      this.$confirm("是否确认设为关注?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var temp = {};
        temp.Code = 1;
        temp.Type = 1;
        temp.BusinessId = this.listQuery.ImplementationTemplateId;
        temp.Content = JSON.stringify(ids);
        let formData = JSON.parse(JSON.stringify(temp));
        impManagement.addPersonalDataCenter(formData).then(() => {
          this.$notify({
            title: "成功",
            message: "设置成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    /**取消关注 */
    handleQuitFollow() {
      var rows = this.multipleSelection;
      if (rows.length < 1) {
        this.$message({
          message: "至少选择一条",
          type: "error"
        });
        return;
      }
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.ImplementationRegionalId);
      } else {
        ids.push(rows.ImplementationRegionalId);
      }
      this.$confirm("是否确认取消关注?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var temp = {};
        temp.Code = 1;
        temp.Type = 1;
        temp.BusinessId = this.listQuery.ImplementationTemplateId;
        temp.Content = JSON.stringify(ids);
        let formData = JSON.parse(JSON.stringify(temp));
        impManagement.delPersonalDataCenter(formData).then(() => {
          this.$notify({
            title: "成功",
            message: "取消成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },


    /**字段显示 */
    handleFieldShows() {
      this.templateId = this.listQuery.ImplementationTemplateId;
      this.cloumnData = [
        {
          label: "序号",
          value: "Number"
        }
      ];
      let a = this.tabColumns.concat(this.dynamicTabColumns);
      a.forEach((v, i) => {
        if (i > 0) {
          this.cloumnData.push({
            label: v.attr.label,
            value: v.attr.prop
          })
        }
      })
      this.dialogFieldShowsVisible = true;
    },

    /**导出 */
    handleExport() {
      this.rData = {
        exportSource: 6,
        columns: [],
        searchCondition: this.listQuery
      };
      this.cData = [
        {
          label: "序号",
          value: "Number"
        }
      ];
      let a = this.tabColumns.concat(this.dynamicTabColumns);
      a.forEach((v, i) => {
        if (i > 0) {
          this.cData.push({
            label: v.attr.label,
            value: v.attr.prop
          })
        }
      })
      this.dialogExportVisible = true;

    },
    // dynamicComputedHeight() {
    //   let tableWrapper = this.$refs.tableWrapper;
    //   if (tableWrapper) {
    //     this.tableWrapperHeight = tableWrapper.clientHeight;
    //   }
    // },
    // getStatusObj(status) {
    //   return vars.regionalStatus.find(s => s.value == status) || {};
    // },
    getRegionalStatus(val) {
      return vars.regionalStatus.find(s => s.value == val) || {};
    },
    getProcesStatus(val) {
      return vars.processStatus.find(s => s.value == val) || {};
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleResetSearch() {
      this.listQuery = {
        // 否则手动重置查询条件
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize,
        RegionalId: this.listQuery.RegionalId,
        ImplementationTemplateId: this.listQuery.ImplementationTemplateId,
        // ImplementationName: "",
        // ContractNumber: "",
        // EngineeringNumber: "",
        MultiCondition: "",
        Status: null,
        ParticularYear: '',
        EmployeeString: '',
      };
      this.getList(); //刷新列表
    },
    //获取项目列表
    // getList() {
    //   this.listLoading = true;
    //   let postData = JSON.parse(JSON.stringify(this.listQuery));
    //   let that = this;
    //   impManagement
    //     .getImplementationStatement(postData)
    //     .then(res => {
    //       this.listLoading = false;
    //       //用户动态计算宽度（不用于显示，所以需要和显示的格式保持一致）
    //       this.tabDatas = res.Items.map(s => {
    //         if (
    //           s &&
    //           s.RegionalFoucsContents &&
    //           s.RegionalFoucsContents.length > 0
    //         ) {
    //           s.RegionalFoucsContents.map(item => {
    //             let loopText = "";
    //             if (
    //               item.FoucsContentItemModels &&
    //               item.FoucsContentItemModels.length > 0
    //             ) {
    //               loopText += "重点关注：";
    //               for (let i = 0; i < item.FoucsContentItemModels.length; i++) {
    //                 let pro = item.FoucsContentItemModels[i];
    //                 loopText += `【${pro.ItemName} ${pro.Progress}% (${
    //                   that.getProcesStatus(pro.Status).label
    //                 })】`;
    //               }
    //             }
    //             item.text = `【${item.RegionalName}：${item.Progress}% (${
    //               that.getRegionalStatus(item.Status).label
    //             }) ${loopText}】`;
    //             return item;
    //           });
    //         }
    //         return s;
    //       });
    //       this.total = res.Total;
    //     })
    //     .catch(err => {
    //       this.listLoading = false;
    //     });
    // },
    getKey(guid) {
      return `key_${guid.replace(/-/g, '_')}`
    },
    getList() {
      if (!this.listQuery.ImplementationTemplateId) {
        return false
      }
      this.listLoading = true;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      let that = this;
      impManagement
        .getImplementationStatementNew(postData)
        .then(res => {
          this.listLoading = false;

          //将一对多（集合），集合中的元素 FoucsContentItemModels 属性集合转换为动态列
          let tempDatas = res.Items.map(s => {
            // if(!s.processStatus) {
            //   s.ProgressString = ''
            // }
            s.ProgressString = s.ProgressString.replace('%', '') - 0

            if (s.ImplementationProcedures && s.ImplementationProcedures.length > 0) {

              // //数据结构有问题，应该取里面的状态
              // if(s.RegionalFoucsContents[0].Status) {
              //   // s.Status = s.RegionalFoucsContents[0].Status
              //   // s.ProgressString = s.RegionalFoucsContents[0].Progress
              // }

              s.ImplementationProcedures = s.ImplementationProcedures.map(i => {
                // 将 集合 FoucsContentItemModels 中的项转换成 i 对象的属性
                // if (i.FoucsContentItemModels && i.FoucsContentItemModels.length > 0) {
                //   i.FoucsContentItemModels.forEach(n => {
                //   })
                // }
                // delete i.FoucsContentItemModels
                let tempKey = this.getKey(i.ImplementationProcedureTemplateId)
                // i[tempKey] = n.Progress
                s[`main_key_${tempKey}`] = i.Progress
                s[`main_key_id_${tempKey}`] = i.Id

                return i
              })
            }
            return s
          })

          //转换好的数据，转换成 合并列需要的格式
          // let resultDatas = []
          // tempDatas.forEach((s, index) => {
          //   let temp = JSON.parse(JSON.stringify(s))
          //   temp.rowspan = 1 //默认不跨行(跨一行)
          //   this.notRowspanColumns.forEach(colName => {
          //     temp[colName] = ''
          //   })

          //   //因为有合并行，所有需要需要处理（多行同一个序号）
          //   temp.idx = ((postData.PageIndex - 1) * postData.PageSize) + (index)

          //   let tempList = s.ImplementationProcedures

          //   if (tempList && tempList.length > 0) {
          //     tempList.forEach((ele, idx2) => {
          //       if (idx2 == 0) {
          //         temp.rowspan = tempList.length //判断需要跨行数量
          //       } else {
          //         temp.rowspan = 0
          //       }
          //       temp = Object.assign({}, temp, ele)//拷贝时改变了状态的值
          //       temp.Status = s.Status;
          //       resultDatas.push(JSON.parse(JSON.stringify(temp)))
          //     });
          //   } else {
          //     resultDatas.push(JSON.parse(JSON.stringify(temp)))
          //   }
          // })
          this.tabDatas = tempDatas
          this.total = res.Total;

        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    /**事项详情、编辑 */
    // handleProcesCreateDialog(row, optType = "update") {
    //     this.procesId = row.ItemId;
    //     this.dialogProcesCreateStatus = optType;
    //     this.dialogProcesCreateFormVisible = true;
    // },
    /**事项进度关闭 */
    // closeProcesCreateDialog() {
    //     this.dialogProcesCreateFormVisible = false;
    // },  
    changeTreeNode(d) {
      if (d.Id == -1) {
        this.listQuery.RegionalId = null;
      } else {
        this.listQuery.RegionalId = d.Id;
      }
      this.listQuery.PageIndex = 1
      this.getList();
    },
    // getMaxLength(arr) {
    //   return arr.reduce((acc, item) => {
    //     if (item) {
    //       let calcLen = this.getTextWidth(item);
    //       if (acc < calcLen) {
    //         acc = calcLen;
    //       }
    //     }
    //     return acc;
    //   }, 0);
    // },
    /**
     * 使用span标签包裹内容，然后计算span的宽度 width： px
     * @param valArr
     */
    getTextWidth(str) {
      let width = 0;
      let html = document.createElement("span");
      html.innerText = str;
      html.className = "getTextWidth";
      //字体大小需要和表格中显示字体保持一致，否则宽度计算不合
      html.style =
        "display: inline-block; overflow-x: auto; color: red; white-space:nowrap; font-size: 12px;";
      document.querySelector("body").appendChild(html);
      width = document.querySelector(".getTextWidth").offsetWidth;
      document.querySelector(".getTextWidth").remove();
      return width;
    }
  }
};
</script>

<style scoped>
.page-wrapper >>> .el-progress-bar {
  margin-right: -60px;
  padding-right: 55px;
}

/* 去掉table hover 样式 */
/* .page-wrapper >>> .el-table tbody tr:hover > td {
  background-color: #fff !important;
} */
.page-wrapper >>> .el-table .el-table__body tr.current-row > td {
  background-color: #fff !important;
}

.page-wrapper >>> a:hover {
  text-decoration: underline;
}
</style>

<style lang="scss" scoped>
// .main-page-content {
//   display: flex;
//   flex-direction: column;
//   height: 100%;
//   .table-wrapper {
//     flex: 1;
//   }
// }

// .list-wrapper{
//     .list-item{
//         // white-space: nowrap;
//         a:hover{
//             text-decoration: underline;
//         }
//     }
//     .list-item:not(:last-child) {
//         margin-bottom: 10px;
//     }
// }

.page-wrapper {
  // min-height: calc(100% - 40px)!important;
  min-height: 100% !important;
  margin-bottom: 0 !important;
  .content-left {
    position: absolute;
    top: 0;
    left: 0;
    width: 250px;
    height: 100%;
    border-right: 1px solid #dcdfe6;
  }
  .content-right {
    position: absolute;
    top: 0;
    right: 0;
    width: calc(100% - 250px);
    height: 100%;
    overflow-y: auto;
    > div:first-child {
      margin-top: 4px;
      // margin-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
      padding: 0 8px;
    }
  }
}

.conditionArea-wrap {
  padding: 10px;
  // padding-top: 18px;
  border-bottom: 1px solid #ebeef5;
}

.btns-area {
  text-align: left;
  padding: 10px;
  // padding-right: 10px;
}
</style>