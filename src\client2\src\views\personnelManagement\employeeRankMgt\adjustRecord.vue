<template>
    <div>
        <app-dialog title="调整记录" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <div class="wrapperMain __dynamicTabContentWrapper">
                    <div class="content __dynamicTabWrapper">
                        <app-table ref="mainTable"
                        :tab-columns="tabColumns" :tab-datas="tabDatas" :optColWidth="80"
                        :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true"
                        :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode'
                        :isShowBtnsArea='false' @sortChagned="handleSortChange">
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter"
                                @onReset="onResetSearch" :layoutMode='layoutMode'>
                                    <template slot="KeyWords">
                                        <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}'
                                            clearable v-model.trim="listQuery.KeyWords" placeholder="搜索员工姓名/工号/职位"></el-input>
                                    </template>
                                </app-table-form>
                            </template>


                            <template slot="OldEmployeeLevelType" slot-scope="scope">
                                {{scope.row.OldEmployeeLevelType?'T'+scope.row.OldEmployeeLevelType:'无'}}
                            </template>
                            <template slot="NewEmployeeLevelType" slot-scope="scope">
                                {{scope.row.NewEmployeeLevelType?'T'+scope.row.NewEmployeeLevelType:'无'}}
                            </template>
                            <template slot="ApplyDate" slot-scope="scope">
                                {{scope.row.ApplyDate | dateFilter("YYYY-MM-DD")}}
                            </template>


                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button v-if="scope.row.ApprovalStatus!=0" @click="handleEdit(scope.row,'detail')" :type="2"></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </template>

            <template slot="footer">
                <!-- 关闭 -->
                <app-button @click="handleClose" text="关闭" type></app-button>
            </template>
        </app-dialog>

        
        <!-- 调整申请 -->
        <adjust-application
            v-if="dialogAdjustApplicationVisible"
            @closeDialog="dialogAdjustApplicationVisible=false"
            :dialogFormVisible="dialogAdjustApplicationVisible"
            :dialogStatus="dialogStatus"
            :id="selectId"></adjust-application>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import adjustApplication from "./adjustApplication"
import * as employeeLevelHistoryApi from "@/api/personnelManagement/employeeLevelHistory";
export default {
    name: "employeeRankMgt-adjustRecord",
    mixins: [indexPageMixin],
    components: {
        adjustApplication
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList()
                }
            },
            immediate: true
        }
    },
    mounted() {
        
    },
    created() {
    },
    data() {
        return {
            
            listLoading: false,
            listQuery: {},
            tabDatas: [], //原始数据
            total: 0,
            /******************* 表格 *******************/
            layoutMode: 'simple',
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
            ],
            tabColumns: [
                { attr: { prop: "Name", label: "姓名", } },
                { attr: { prop: "Number", label: "工号" } },
                { attr: { prop: "DepartmentName", label: "部门", showOverflowTooltip: true } },
                { attr: { prop: "JobName", label: "职位", showOverflowTooltip: true } },
                { attr: { prop: "OldEmployeeLevelType", label: "调整前" }, slot: true },
                { attr: { prop: "NewEmployeeLevelType", label: "调整后" }, slot: true },
                { attr: { prop: "CreateEmployeeName", label: "提交人" } },
                { attr: { prop: "ApplyDate", label: "申请日期", sortable: "custom", }, slot: true },
            ],

            dialogAdjustApplicationVisible: false,
            selectId: '',
            dialogStatus: '',
        };
    },
    methods: {
        // 编辑/详情
        handleEdit(row, optType){
            this.selectId = row.Id;
            this.dialogStatus = optType;
            this.dialogAdjustApplicationVisible = true;
        },
        //获取成员列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            this.listLoading = true;
            employeeLevelHistoryApi.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        onResetSearch() {
            this.listQuery = this.$options.data().listQuery;
            this.getList();
        },

        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            this.getList();
        },
    }
};
</script>

<style lang='scss' scoped>
.colorBlue{
    color: #409EFF;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
.wrapperMain{
    display: flex;
}
</style>