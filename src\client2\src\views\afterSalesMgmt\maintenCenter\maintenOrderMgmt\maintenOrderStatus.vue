<template>
    <app-tag-pure effect="dark" v-if="getSaintenanceStatus(status)" :color="getSaintenanceStatus(status).color" :text="getSaintenanceStatus(status).label"></app-tag-pure>
</template>

<script>
import { vars } from "../common/vars";
export default {
    name: '',
    filters: {

    },
    props: {
        status: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            
        }
    },
    methods: {
        getSaintenanceStatus(val) {
            return vars.maintenOrderMgmt.maintenanceStatus.find(s => s.value == val)
        },
        
    },
}
</script>

