<template>
  <div>
    <app-dialog
      title="到访记录"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='600'
    >
      <template slot="body" v-loading="loading">

        <div style="min-height: 500px;">
          <noData v-if="groups.length == 0"></noData>
          <div v-else v-for="(g, idx) in groups" :key="idx">
              <div style="padding: 4px 0;">
                  <span @click="() => g.expand = !g.expand"><i :class="g.expand ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i></span>
                  <span>{{ g.EmployeeName }}（{{ g.stayMinute | totalTimeFilter }}）</span>
              </div>
              <div style="margin-left: 20px;" v-for="(sub, idx2) in g.List" :key="idx2" v-show="g.expand">
                  <div>【{{ sub.ClockType | clockTypeFilter }}】{{ sub.CreateTime | dateFilter('MM-DD HH:mm:ss') }}</div>
                  <div>{{ sub.LocationAddress }}</div>
              </div>
          </div>
        </div>

      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as maintenOrderMgmtApi from "@/api/maintenanceCenter/maintenOrderMgmt"
import noData from "@/views/common/components/noData";
import mixins from '../../../afterSalesMgmt/softwareAftersalesReturnVisit/mixins'
export default {
  name: "mainten-order-mgmt-record",
  components: {
    noData,
  },
  mixins: [mixins],
  props: {
    id: {
        type: String,
        default: ''
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if(val) {
        this.getList()
      }
    },
  },
  created() {

  },
  filters: {
    clockTypeFilter(val) {
        if(val == 1) {
            return '进站'
        }else{
            return '出站'
        }
    }
  },
  data() {
    return {
      loading: false,
      groups: [],
    };
  },
  methods: {
    getList() {
      let postDatas = {
        PageIndex: 1,
        PageSize: 1000,
        MaintenanceId: this.id
      }

      maintenOrderMgmtApi.getClock(postDatas).then(res => {
        let result = []
        let list = res.PageData.Items || []
        let empList = res.TotalTimeEmployeeList || []
        let groups = _.groupBy(list, 'CreateEmployeeId');

        Object.keys(groups).forEach(key => {

            let empId = key
            let group = groups[empId]
            
            let g = {
                EmployeeId: empId,
                EmployeeName: group[0].CreateEmployee.Name,
                List: group,
                expand: true,
                stayMinute: 0 //分钟
            }

            let empObj = empList.find(s => s.EmployeeId == empId)
            if(empObj) {
                g.stayMinute = empObj.TotalTime
            }
            
            // if(group && group.length > 0) {
            //     let sTimeList = group.filter(s => s.ClockType == 1 && s.ClockTime).sort(s => s.ClockTime).map(s => s.ClockTime) // 到站
            //     let eTimeList = group.filter(s => s.ClockType == 2 && s.ClockTime).sort(s => s.ClockTime).map(s => s.ClockTime) // 离站
            //     if(sTimeList.length > 0 && eTimeList.length > 0) {
            //         let eTime = eTimeList[0]
            //         let sTime = sTimeList[sTimeList.length - 1]
            //         let min = dayjs(eTime).diff(dayjs(sTime),'minutes')
            //         g.stayMinute = min
            //     }
            // }

            result.push(g)
        }); 

        this.groups = result
      })

    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
