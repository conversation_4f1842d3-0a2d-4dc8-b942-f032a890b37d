<template>
    <div class="block-content" v-loading='loading'>
        <!-- <blockTitle :obj='obj'>
            <div slot='rht'>
                <el-radio-group v-model="period" size="mini" @change='getMaintenanceDetailsChart'>
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </div>
        </blockTitle> -->

         <el-row style="width:100%; margin-top:19px; margin-bottom:5px;">
            <el-col :span=12 style="display:flex;">
                <span style="margin-left:30px; width: 5px; height: 20px; background: #3D73DD;"></span>
                <span style="margin-top:2px; font-size:16px; color: #1D2129; margin-left:11px; font-weight:bold;">售后</span>
            </el-col>
            <el-col :span=12 style="display:flex; justify-content:flex-end;">
               <el-radio-group v-model="period" size="mini" @change='getMaintenanceDetailsChart' style="margin-right:15px;">
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </el-col>
         </el-row>

        <div class="inner-content">
            <el-row style="width: 100%; height: 1px; background: #DCDFE6;"></el-row>
            <el-row class="top">
                <el-col :span="3" style="display:flex; margin-left:30px;">
                    <span class="shape"></span>
                    <div class="top-item">
                        <span class="top-title">报修单总数</span>
                   
                        <span class="top-value">
                             {{ formData.MaintenanceTotal }}
                        </span>
                    </div>
                   
                    
                </el-col>
               
                <el-col :span="3" style="display:flex;">
                    <span class="shape"></span>
                    <div class="top-item">
                         <span class="top-title">报修收费（元）</span>
                  
                         <span :class ="[ getSizeByNum(formData.MonthRepairFee) ? 'normal' : 'mini' ]">
                            {{ formData.MonthRepairFee }}
                         </span>
                    </div>
                    
                </el-col>

                 <el-col :span="3" style="display:flex; margin-left:10px;">
                    <span class="shape"></span>
                    <div class="top-item">
                         <span class="top-title">设备总数</span>
                  
                         <span class="top-value">
                            {{formData.equipmentTotal}}
                         </span>
                    </div>
                
                </el-col>

                 <el-col :span="3" style="display:flex;">
                     <div :span='2' style="height: 50px; display: flex; flex-direction: column;">
                         <div :style="{background:'#E9EAEF',width:'10px',height:((formData.unmanaged/(formData.managed+formData.unmanaged))*50)+'px'}"></div>
                         <div :style="{background:'#3D73DD',width:'10px',height:((formData.managed/(formData.managed+formData.unmanaged))*50)+'px'}"></div>
                     </div>
                    <div class="top-item">
                        <span class="top-title">已托管</span>
                  
                        <span class="top-value">
                             {{formData.managed}}
                        </span>
                    </div>
                   
                </el-col>

                <el-col :span="3" style="display:flex;">
                     <div :span='2' style="height: 50px; display: flex; flex-direction: column;">
                         <div :style="{background:'#E9EAEF',width:'10px',height:((formData.managed/(formData.managed+formData.unmanaged))*50)+'px'}"></div>
                         <div :style="{background:'#E32B06',width:'10px',height:((formData.unmanaged/(formData.managed+formData.unmanaged))*50)+'px'}"></div>
                     </div>
                    <div class="top-item">
                          <span class="top-title">未托管</span>
                   
                          <span class="top-value">
                            {{formData.unmanaged}}
                          </span>
                    </div>
                   
                </el-col>
              
            </el-row>

            <div class="bottom">
                <!-- <div class="flex-dire-column-wrapper">
                    <div class="chart-title a-l">设备总数</div>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption3.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart3" :option='pieEchartOption3'></app-charts-basic>
                    </div>
                </div> -->

                 <div class="flex-dire-column-wrapper">
                    <span class="chart-title">报修单完成情况</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption1.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart1" :option='pieEchartOption1'></app-charts-basic>
                    </div>
                </div>

                <div class="flex-dire-column-wrapper">
                    <span class="chart-title">设备报修</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption4.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart4" :option='pieEchartOption4'></app-charts-basic>
                    </div>
                </div>
                
                <div class="flex-dire-column-wrapper">
                    <!-- <span class="chart-title">集控装置情况</span> -->
                    <span class="chart-title">设备分布</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption2.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart2" :option='pieEchartOption2'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <span class="chart-title">保修情况</span>
                    <div class="flex-1">
                        <noData v-if="!hasDataOfPie(pieEchartOption5.series)"></noData>
                        <app-charts-basic v-else :height='chartsHeight' ref="pieEchart5" :option='pieEchartOption5'></app-charts-basic>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import blockTitle from '../blockTitle'
import * as odc from "@/api/operatingDataCenter";
import { pieEchartOptionTemp, colors, dateTypeEnum1 } from "../vars";
import mixins from '../mixins'

export default {
    name: 'shouhou',
    mixins: [mixins],
    components: {
        noData,
        blockTitle,
    },
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    mounted() {
        this.getMaintenanceDetailsChart()
    },
    data() {
        return {
            period: 3,
            dateTypeEnum: dateTypeEnum1,
            loading: false,
            chartsHeight: '260px',
            pieEchartOption1: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption2: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption3: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption4: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            pieEchartOption5: JSON.parse(JSON.stringify(pieEchartOptionTemp)),
            
            formData: {
                MaintenanceTotal: 0, //知识库数量
                MonthRepairFee: 0, //企业资质
                unmanaged:0,
                managed:0,
                equipmentTotal:0,
            },
        }
    },
    methods: {
         getSizeByNum(num) {
            num += ''
            var counta = num.length;
            return counta <= 8 ? true : false
            // if(counta <= 8){
            //     return '30px';
            // }else{
            //     return '20px';
            // }

        },
        getMaintenanceDetailsChart() {
            let that = this
            that.loading = true
            odc.getMaintenanceDetailsChart({Period: that.period}).then(res => {
                that.loading = false

                that.formData.MaintenanceTotal = res.MaintenanceTotal
                that.formData.MonthRepairFee = res.MonthRepairFee

                res.EquipmentParameterChartData.map(v=>{
                     
                     v.Label === '未托管'? that.formData.unmanaged = v.Value : that.formData.managed = v.Value


                })

                that.formData.equipmentTotal = 0
                res.RegionEquipment.map(v=>{
                        
                       that.formData.equipmentTotal += v.Value
                })
                

                that.pieEchartOption1 = that._initPieChartDatas(res.MaintenanceStatusChartData || [], false, true)
                that.pieEchartOption2 = that._initPieChartDatas(res.EquipmentParameterChartData || [], true)
                that.pieEchartOption3 = that._initPieChartDatas(res.RegionEquipment || [], false)
                that.pieEchartOption4 = that._initPieChartDatas(res.MaintenanceEquipment || [], false, true)
                that.pieEchartOption5 = that._initPieChartDatas(res.WarrantyChartData || [], false)
            }).catch(err => {
                that.loading = false
            })

        },

        _initPieChartDatas(list, isHollow = false, isShowLabel = false) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                targetOption = {
                    legend: {
                        type: 'scroll',
                        //icon:"circle",
                        orient: 'vertical',
                        left: '62%',
                        top: 'middle',
                        data: chartDatas.map(s => s.Label)//chartDatas.map(s => s.Label)
                    },
                    series: [{
                        radius: ['20%', '50%'],
                        center: ['35%', '50%'],
                        label: {
                            normal: {
                                show: false,
                                position: 'inner'
                            }
                        },
                        data: chartDatas.map(s => {
                            return {
                                value: s.Value === 0 ? null : s.Value,
                                name: s.Label
                            }
                        })
                    }],
                    color: ['#3D73DD', '#3DAEDD', '#883DDD', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2']
                }
            }

            // if(isHollow && targetOption.series && targetOption.series.length > 0) {
            //     targetOption.series[0].type = 'pie'
            //     targetOption.series[0].radius = ['35%', '50%']
            // }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(pieEchartOptionTemp)), targetOption)

            return targetOption

        },
    },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
.block-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    .inner-content{
        flex: 1;
        display: flex;
        flex-direction: column;
        .bottom{
            flex: 1;
            display: flex;
            .flex-dire-column-wrapper{
                flex: 1;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
        // .top{
        //     border-bottom: 1px solid rgb(220, 223, 230);
        // }
        .bottom{
            padding-top: 10px;
        }
    }
}

.chart-title{
    text-align: center; color:#333333; font-size:14px; font-weight: bold; margin-left:31px;
}

.flex-1, .flex-2{
    box-sizing: border-box;
    margin: 5px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    >div:last-child{
        flex: 1;
    }
}

.flex-1{
    flex: 1;
}

.text-content{
    text-align: center; flex: 1; font-weight: bold; display: flex; justify-content: center; align-items: center; word-break: break-all; white-space: normal; word-break: break-all;
}

.top{
    display: flex;
    margin-top: 30px;
}

.top-item{
   display: flex; flex-direction: column; margin-left:11px; padding-left: 5px;
}

.top-title{
   color:#A0A1A3; font-size:14px;
}
.top-value{
    color:$text-primary; font-weight: bold; font-size:30px; margin-top:3px;
}

.shape{
   width: 10px;height: 50px;background: #E9EAEF;
}

.normal{
    color:$text-primary; font-weight: bold; font-size:30px; margin-top:3px;
}

.mini{
    color:$text-primary; font-weight: bold; font-size:20px; margin-top:10px;
}

</style>