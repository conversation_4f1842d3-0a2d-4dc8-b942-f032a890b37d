<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">



          <el-form-item label="企业LOGO" prop="PartnersLogoPath">
            <app-upload-file :max='1' :fileSize='1024 * 1024 * 2' :value='fileList' :readonly="!editable" @change='handleUpChange' :preview='true' ></app-upload-file>
            <!-- <span>建议尺寸：75px * 75px</span> -->
          </el-form-item>



          <el-form-item label="企业名称" prop="PartnersName">
            <el-input maxlength="25" type="text" :disabled="!editable" v-model="formData.PartnersName"></el-input>
          </el-form-item>

          <el-form-item label="合作简介" prop="PartnersTitle">
            <el-input maxlength="2000" type="textarea" :rows="8" v-model="formData.PartnersTitle" :disabled="!editable"></el-input>
          </el-form-item>

        </el-form>
      </template>
      <template slot="footer">

        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary" ></app-button>
        <!-- <el-button @click="handleSave" type="primary">保存</el-button> -->
      </template>
    </app-dialog>
  </div>
</template>

<script>

import * as companyIntroduction from '@/api/informationCenter/companyIntroduction'
//import EditorBar from '../../../components/WangEditor/index.vue'

export default {
  name: "demand-pool-create",
  directives: {},
  components: {//EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.fileList = [];
        this.isContinue = false;
      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
       //不等于详情页面可编辑
       editable() {
            return this.dialogStatus != "detail"
        },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加合作伙伴";
      } else if (this.dialogStatus == "update") {
        return "编辑合作伙伴";
       }else if(this.dialogStatus == 'detail') {
                return '详情'
            }
            return ''
    },
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      fileList: [], //图像信息[{Id: '', Path: ''}]
      isClear:false,
      isContinue: false,
      rules: {

        PartnersLogoPath: {
          fieldName: "企业LOGO",
          rules: [{ required: true }]
        },
        PartnersName: {
          fieldName: "企业名称",
          rules: [{ required: true, max: 50 }]
        },
        PartnersTitle: {
          fieldName: "合作简介",
          rules: [{ required: true }]
        }
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        PartnersLogo: "",
        PartnersLogoPath: "",

        PartnersTitle: "",
        PartnersName: "",

      }
    };
  },
  methods: {
    handleUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.PartnersLogoPath = imgs[0].Path
        this.formData.PartnersLogo = imgs[0].Id
      } else {
        this.formData.PartnersLogo = ''
        this.formData.PartnersLogoPath = ''
      }
    },

    resetFormData() {
      let temp = {
        Id: "",

        PartnersLogo: "",
        PartnersLogoPath: "",

        PartnersTitle: "",
        PartnersName: "",

      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      companyIntroduction.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        this.fileList = [];
        if (this.formData.PartnersLogoPath) {
          this.fileList = [
            { Id: this.formData.PartnersLogo, Path: this.formData.PartnersLogoPath }
          ];
        }
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //保存
    createData() {
      let validate = this.$refs.formData.validate();

      Promise.all([validate]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        //提交数据保存
        let result = null;
        if (this.dialogStatus == "create") {
          delete postData.Id;
          result = companyIntroduction.add(postData);
        } else if (this.dialogStatus == "update") {
          result = companyIntroduction.edit(postData);
        }

        result.then(res => {
          if (this.isContinue) {

            this.resetFormData();
            this.$emit("reload");
          } else {
            this.$notify({
                //  title: '成功',
                message: '保存成功',
                type: 'success',
                duration: 2000
              })
            this.$refs.appDialogRef.createData();
          }
        });
      });
    },

    handleSave() {
      this.formData.IsDraft = false;
      this.createData();
    },


  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
