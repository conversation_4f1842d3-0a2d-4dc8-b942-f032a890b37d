<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>

                    <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="tagBox">
                    <tags :items="searchTypesData" v-model="listQuery.Status">
                        <template v-for="t in searchTypesData" :slot="t.value">
                            {{ t.label }}
                        </template>
                    </tags>
                </div>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tableDatas" :isShowAllColumn="true" :loading="listLoading"
                    :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false'
                    @sortChagned="handleSortChange" :optColWidth="160">
                        <template slot="EntryTime" slot-scope="scope">{{ scope.row.EntryTime | dateFilter('YYYY-MM-DD') }}</template>
                        <template slot="EntryTimePlan" slot-scope="scope">{{ scope.row.EntryTimePlan | dateFilter('YYYY-MM-DD') }}</template>
                        <template slot="JobName" slot-scope="scope">{{ scope.row.JobName || '无' }}</template>
                        <template slot="DepartmentName" slot-scope="scope">{{ scope.row.DepartmentName || '无' }}</template>
                        <template slot="Status" slot-scope="scope">
                            <span class="item-status"
                                :style="{backgroundColor: getStatusObj(scope.row.Status).bgColor,color: getStatusObj(scope.row.Status).color}"
                            >{{ getStatusObj(scope.row.Status).label }}</span>
                        </template>
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form label-width="110px" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" clearable placeholder="搜索姓名/职位"
                                        @clear='getList' v-antiShake='{time: 300,callback: () => {getList()}}' 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>
                                <template slot="EntryTime">
                                    <el-date-picker v-model="listQuery.EntryTime" type="date"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                </template>
                                <template slot="EntryTimePlan">
                                    <el-date-picker v-model="listQuery.EntryTimePlan" type="date"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                </template>
                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row, 'detail')" :type="2"></app-table-row-button>
                            <!-- 1 已入职 不显示编辑和办理入职按钮 -->
                            <template v-if="scope.row.Status!=3">
                                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleReview(scope.row, 'update')" :type="1"></app-table-row-button>
                                <app-table-row-button v-if="rowBtnIsExists('btnHandleInduction')" @click="handleInduction(scope.row)" :type="1" text="办理入职"></app-table-row-button>
                            </template>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    <!-- 创建/ 编辑 / 查看  待入职-->
    <create-page 
        @closeDialog='closeDialog' 
        @saveSuccess='handleSaveSuccess'
        :dialogFormVisible='dialogFormVisible'
        :dialogStatus='dialogStatus' 
        :id='dialogFormId'
    ></create-page>
    <!-- 新员工欢迎公告 -->
    <welcome-notice @closeDialog='dialogWelcomeNoticeVisible=false' :dialogFormVisible='dialogWelcomeNoticeVisible'></welcome-notice>
    
    <!-- 员工入职 -->
    <induction-page @closeDialog="closeInductionDialog" @saveSuccess="handleInductionSaveSuccess"
    :dialogFormVisible="dialogInductionVisible" dialogStatus="induction" :id="dialogFormId" @reload="getList" :selectTypeId="listQuery.DepartmentId"></induction-page>


    <!-- 补充人员资料 -->
    <supplement-page @closeDialog="closeSupplementDialog" @saveSuccess="handleSupplementSaveSuccess"
    :dialogFormVisible="dialogSupplementFormVisible" :dialogStatus="'update'" :id="supplementEmpId"></supplement-page>

</div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import indexPageMixin from "@/mixins/indexPage";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as EntryManagementApi from "@/api/personnelManagement/EntryManagement";

import welcomeNotice from "./welcomeNotice.vue";
import createPage from "./create.vue";
import inductionPage from "../../../personnelManagement/systemDepartment/create.vue";
import supplementPage from '../../../personnelManagement/employeeRecord/create'
import { vars } from '../common/vars'

export default {
    name: "employeeRelations-trainingRmployment",
    mixins: [indexPageMixin],
    components: {
        createPage,
        welcomeNotice,
        inductionPage,
        supplementPage
    },
    props: {},
    filters: {
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {
        // fildids() {
        //     return this.multipleSelection.map((s) => s.Id) || [];
        // },
    },
    watch: {

        filterText(val) {
            this.$refs.tree.filter(val);
        },
        'listQuery.Status'() {
            this.getList();
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.DepartmentId = val.Id;
                    this.GetStatusList();
                    this.getList();
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getDepartments();
    },
    data() {
        return {
            InductionStatusEnum: vars.InductionStatusEnum,

            dialogFormId: '',
            dialogStatus: 'create',
            dialogFormVisible: false,

            dialogWelcomeNoticeVisible: false,
            
            dialogInductionVisible: false,

            dialogSupplementFormVisible: false,
            supplementEmpId: '',


            searchTypesData: [
                { label: "待入职（0）", value: 2 },
                { label: "已入职（0）", value: 3 },
                { label: "全部（0）", value: 1 },
            ],
            
            layoutMode: 'simple',
            epKeys: [],

            filterText: "",

            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "EntryTimePlan", label: "预计入职时间" },
                { prop: "EntryTime", label: "入职时间" },
            ],

            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            listLoading: false,
            tabColumns: [
                {attr: {prop: "Name",label: "姓名", width: '80px'}},
                {attr: {prop: "DepartmentName",label: "所属部门", showOverflowTooltip: true}, slot: true},
                {attr: {prop: "JobName",label: "职位", showOverflowTooltip: true}, slot: true},
                {attr: {prop: "Status",label: "入职状态",sortable: "custom"}, slot: true},
                {attr: {prop: "EntryTimePlan",label: "预计入职时间",sortable: "custom"},slot: true},
                {attr: {prop: "EntryTime",label: "入职时间",sortable: "custom"},slot: true},
                {attr: {prop: "CreateEmployeeName",label: "创建人", width: '80px'}},
            ],
            listQuery: {
                Status: 1, // 1.全部 2.待入职 3.已入职 4.未到岗
                // EntryTime: '',
                // EntryTimePlan: '',
            },
            tableDatas: [], //原始数据
            total: 0,
        };
    },
    methods: {
        // 办理入职
        handleInduction(row){
            this.dialogFormId = row.Id;
            this.dialogInductionVisible = true
        },
        // 列表状态转换
        getStatusObj(status) {
            return this.InductionStatusEnum.find(s => s.value == status) || {};
        },
        // 关闭 创建弹出
        closeDialog() {
            this.dialogFormVisible = false
        },
        // 创建弹出保存
        handleSaveSuccess(d) {
            if (!d) {
                this.listQuery.PageIndex = 1
                this.closeDialog();
            }
            this.GetStatusList();
            this.getList()
        },
        // 关闭 办理入职弹窗
        closeInductionDialog() {
            this.dialogInductionVisible = false
        },
        //办理入职弹出保存
        handleInductionSaveSuccess(dialogStatus, empId, id) {
            EntryManagementApi.EditStatus({Id: id}).then(() => {
                this.lastFunc(dialogStatus, empId);
            }).catch(()=>{
                this.lastFunc(dialogStatus, empId);
            });
        },
        lastFunc(dialogStatus, empId){
            if(dialogStatus == 'induction') {
                this.$confirm("是否继续完善个人档案信息？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.handleSupplementDialog(empId)
                });
            }
            this.GetStatusList();
            this.getList()
            this.closeInductionDialog();
        },
        // 完善资料弹窗
        handleSupplementDialog(id) {
            this.supplementEmpId = id
            this.dialogSupplementFormVisible = true
        },
        // 完善资料弹窗  确定
        handleSupplementSaveSuccess() {
            this.GetStatusList();
            this.getList()
            this.closeSupplementDialog()
        },
        // 完善资料弹窗 关闭
        closeSupplementDialog() {
            this.dialogSupplementFormVisible = false
        },
        // 表格头部按钮区域
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.dialogFormId = "";
                    this.handleAdd()
                    break;
                case "btnWelcomeNotice":
                    this.dialogWelcomeNoticeVisible = true
                    break;
                default:
                break;
            }
        },
        // 表格排序查询
        handleSortChange({column, prop, order}) {
            let delIndex = prop.indexOf('.')
            if (delIndex>-1) {
                prop = prop.substring(delIndex+1)
            }
            this.sortObj = { prop, order };
            this.getList();
        },
        // 编辑/查看弹窗
        handleReview(row, optType='') {
            this.dialogStatus = optType;
            this.dialogFormId = row.Id;
            this.dialogFormVisible = true;
        },
        // 创建弹出
        handleAdd() {
            this.dialogFormId = '';
            this.dialogStatus ='create';
            this.dialogFormVisible = true;
        },
        // 删除表格行
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                EntryManagementApi.del([row.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.GetStatusList();
                    this.getList()
                });
            });
        },
        //获取成员列表
        getList() {
            if (this.checkedNode) {
                this.listLoading = true;
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                postData = this.assignSortObj(postData);

                EntryManagementApi.getList(postData).then((res) => {
                    this.tableDatas = res.Items;
                    this.total = res.Total;
                    this.listLoading = false;
                    // this.departmentInfo = this.selectDepartmentName;
                })
                .catch((err) => {
                    this.listLoading = false;
                });
            }
        },
        // 获取顶部tabs数值
        GetStatusList(){
            let self = this;
            if (self.checkedNode) {
                EntryManagementApi.GetStatusList({}).then((res) => {
                    if(res&&res.length>0) {
                        self.searchTypesData.map(s=>{
                            s.label = res.find((q)=>q.value==s.value).label
                        })
                    }
                });
            }
        },
        // 重置搜索
        onResetSearch() {
            this.listQuery.EntryTime = ''
            this.listQuery.EntryTimePlan = ''
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            systemDepartment
                .getListByCondition(this.departmentListQuery)
                .then((res) => {
                    this.treeDatas = listToTreeSelect(res);
                    
                    if(this.treeDatas && this.treeDatas.length>0){
                        this.treeDatas.forEach(v => {
                            this.epKeys.push(v.Id);
                            // if(v.children.length>0){
                            //     v.children.forEach(v1 => {
                            //         this.epKeys.push(v1.Id);
                            //     })
                            // }

                        })
                    }
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
    },
};
</script>

<style scoped>
</style>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .tagBox{
            border-bottom: 1px solid #DCDFE6;
            padding: 4px 8px;
        }

    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
</style>
