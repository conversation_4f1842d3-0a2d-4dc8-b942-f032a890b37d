<template>
  <div>
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1000"
      :maxHeight="600"
      className="clear-padding"
    >
      <template slot="body">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          :label-width="labelWidth"
          v-loading='loading'
          style="padding:10px"
        >
            <div class="wrapper" v-loading='loading'>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="考勤部门名称" prop="Name">
                            <el-input maxlength="30" :disabled="!editable" v-model="formData.Name"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="机器号" prop="MachineNo">
                            <el-input maxlength="30" :disabled="!editable" v-model="formData.MachineNo"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                      <div class="attendance_container">
                        <div class="attendance_time_top">
                          <span class="label_primary">考勤时间配置</span>
                          <i class="el-icon-circle-plus" @click="handleAddTimeRow" v-if="editable" />
                        </div>
                        <AttendanceTimeTable
                          :list="formData.AttendanceTimeConfig"
                          :isShowDailyAttendance="true"
                          ref="attendanceTimeTableRef"
                          style="margin-top: 10px"
                        />
                      </div>
                    </el-col>
                </el-row>
            </div>
        </el-form>
      </template>
      <template slot="footer">

        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button
          @click="createData"
          :buttonType="1"
          v-show="editable"
          :disabled="disabledBtn"
        ></app-button>

      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as attendanceMgmt from "@/api/personnelManagement/attendanceMgmt";
import AttendanceTimeTable from './AttendanceTimeTable'

export default {
  name: "attendanceMgmt-group-create",
  directives: {},
  components: {
    AttendanceTimeTable
  },
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
    },
    id: {
      type: String,
      default: "",
    },
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "创建考勤部门";
      } else if (this.dialogStatus == "update") {
        return "编辑考勤部门";
      } else if (this.dialogStatus == "detail") {
        return "考勤部门详情";
      }
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.resetFormData();
          if (this.dialogStatus != "create" && this.id) {
            this.getDetail();
          }
        }
      },
      immediate: true
    },

  },
  created() {
    this.rules = this.initRules(this.rules);

  },
  data() {
    return {
      loading: false,
      disabledBtn: false,
      rules: {
        Name: {
          fieldName: "考勤部门名称",
          rules: [{ required: true, trigger: "change" }],
        },
        MachineNo: {
          fieldName: "机器号",
          rules: [{ required: true, trigger: "change" }],
        },
      },
      labelWidth: "120px",
      formData: {
        Id: "",
        Name: "",
        MachineNo: "",
        AttendanceTimeConfig:[]
      },
    };
  },
  methods: {

    resetFormData() {
      let temp = {
        Id: "",
        Name: "",
        MachineNo: "",
        AttendanceTimeConfig:[]
      };
      this.formData = Object.assign({}, this.formData, temp);
    },
    createData() {
        const attendanceTimeValid = this.$refs.attendanceTimeTableRef.validate()
        const createValidate = this.$refs.formData.validate()

        Promise.all([attendanceTimeValid,createValidate]).then(()=>{
          const postData = this.$_.cloneDeep(this.formData)

          // 考勤配置时间组件
          const attendanceTimeFrom = this.$refs.attendanceTimeTableRef.getValue()
          postData.AttendanceTimeConfig =attendanceTimeFrom.AttendanceTimeConfig

          let result = null
          if(this.dialogStatus === 'create') {
            delete postData.Id
            result = attendanceMgmt.add(postData)
          }else{
            result = attendanceMgmt.edit(postData)
          }

          this.disabledBtn = true
          result.then(res => {
            this.disabledBtn = false
            this.$message.success('保存成功')
            this.$refs.appDialogRef.createData(result)
          }).catch(err => {
            this.disabledBtn = false
          })

        }).catch(()=>{

        })
    },
    getDetail() {
      this.loading = true
      attendanceMgmt.detail({ id: this.id }).then((res) => {
        this.loading = false

        let result = {}
        for(let key of Object.keys(this.formData)){
            result[key] = res[key] || this.formData[key]
        }

        this.formData = result

      }).catch(err => {
        this.loading = false
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    // 添加考勤时间配置
    handleAddTimeRow(){
      this.$refs.attendanceTimeTableRef.addRow()
    },
  },
};
</script>

<style lang="scss" scoped>
.step-wrapper {
  .step-item-wrapper {
    padding: 20px;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
.attendance_container{
  padding: 10px 0;
  border-top: 1px solid $border-color-light;
  .attendance_time_top{
    display: flex;
    align-items: center;
    margin-left: -10px;
    .label_primary {
      color: $text-main-color;
      font-size: 14px;
      font-weight: bold;
      padding-left: 10px;
      border-left: 4px solid $color-primary;
    }
    .el-icon-circle-plus{
      color: $color-primary;
      font-size: 16px;
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
</style>
