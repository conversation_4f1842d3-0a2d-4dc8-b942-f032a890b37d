<!-- 里程碑点评申请或审批 -->
<template>
  <div class="milestoneApplicationOrApproval" v-if="dialogAuditFormVisible">
   <el-dialog v-el-drag-dialog class="dialog-mini" width="1000px" :title="title" :visible.sync="dialogAuditFormVisible"
      :close-on-click-modal='false' :append-to-body='true'>
      <el-form ref="dataForm" :rules='rules' :model="auditTemp" label-position="right"
          label-width="120px">
          <el-row>
            <el-col :span="12">
                <el-form-item label="单号" prop='MilestoneCode'>
                    <el-input style="width: 100%;" v-model="auditTemp.MilestoneCode" :disabled="disabledMilestoneCode" placeholder></el-input>
                </el-form-item>
            </el-col>
           <el-col :span="12">
                <el-form-item label="里程碑点" prop='Name'>
                    <el-input style="width: 100%;" v-model="auditTemp.Name" :disabled="disEditable" placeholder></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="计划开始时间" prop='PlannedStartTime'>
                    <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss'
                            value-format='yyyy-MM-dd HH:mm:ss' v-model="auditTemp.PlannedStartTime" :disabled="disEditable"
                            type="datetime" placeholder=""></el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="计划完成时间" prop='PlannedEndTime'>
                    <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss'
                            value-format='yyyy-MM-dd HH:mm:ss' v-model="auditTemp.PlannedEndTime" :disabled="disEditable"
                            type="datetime" placeholder=""></el-date-picker>
                </el-form-item>
            </el-col>
      		<el-col :span="12">
                <el-form-item label="实际开始时间" prop='ActualStartTime'>
                    <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss'
                            value-format='yyyy-MM-dd HH:mm:ss' v-model="auditTemp.ActualStartTime" :disabled="isActualStartTimeDisabled"
                            type="datetime" placeholder=""></el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="实际完成时间" prop='ActualCompletionTime'>
                    <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss'
                            value-format='yyyy-MM-dd HH:mm:ss' v-model="auditTemp.ActualEndTime" :disabled="isActualEndTimeDisabled"
                            type="datetime" placeholder=""></el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="到期提醒" prop='ExpirationReminderThreshold'>
                <el-select v-model="auditTemp.ExpirationReminderThreshold" :disabled="disEditable" placeholder="">
                  <el-option :key="0" label="不提醒" :value="0"></el-option>
                  <el-option v-for="idx of 7" :key="idx" :label="`${idx}天`" :value="idx">
                  </el-option>
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" v-if="editDialogStatus!='create'">
                  <el-form-item label="基准类型" prop="MilestoneDatumTypeId">
                    <el-select class="sel-ipt" v-model="auditTemp.MilestoneDatumTypeId" :disabled="true">
                      <el-option
                        v-for="item in milestoneDatumType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
           </el-col>
            <!-- <el-col :span="12">
                  <el-form-item label="进度" prop="ProgressValue">
                    <el-input-number v-model="auditTemp.ProgressValue" :precision="0" :min="0" :max="100" :disabled="editDialogStatus != 'changeProgress'"></el-input-number>%
                  </el-form-item>
           </el-col> -->
            <el-col :span="12">
                  <el-form-item label="版本/迭代" prop="ProjectManagementVersionPlanId">

            <treeselect :normalizer="normalizer" class="treeselect-common" key='type1' v-model="auditTemp.ProjectManagementVersionPlanId" :disabled="disEditable" :clearable="false" :default-expand-level="3" :options="transIterationTreeData" :multiple="false" placeholder='' :show-count="false" :disable-branch-nodes="false" :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
            </treeselect>


                  </el-form-item>
           </el-col>

            <el-col :span="24">
              <el-form-item label="交付件" prop='Deliverable' label-position="top">
                  <el-input
                    type="textarea"
                    :disabled="disEditable"
                    :rows="3"
                    placeholder="请输入内容"
                    v-model="auditTemp.Deliverable">
                  </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="遗留事项" prop='LegacyMatters' label-position="top">
                  <el-input
                    type="textarea"
                    :rows="3"
                    placeholder="请输入内容"
                    v-model="auditTemp.LegacyMatters" :disabled="!(!disEditable||!isLegacyMattersDisabled)">
                  </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item :label="'附件'" prop="AttachmentResourceList">
                <app-upload-big-file :limitTotalSize="1024 * 1024 * 1024" :max="10000" :readonly="!(!disEditable||!isAttachmentResourceListReadonly)" accept="all"
                  :fileType="4" :value="auditTemp.AttachmentResourceList" :fileSize="1024 * 1024 * 500"
                  @change="handleFilesUpChange"></app-upload-big-file>
              </el-form-item>
            </el-col>

            <el-col :span="12" v-if="!applicationOrApproval">
                <!-- <el-form-item label="" prop='Approver'>
                    <el-input style="width: 100%;" v-model="auditTemp.Approver" placeholder></el-input>
                </el-form-item> -->

                <el-form-item :label="'审批人'" prop="ParticipantEmployeeList">
                    <emp-selector :showType="2" :readonly="editable2" :list="auditTemp.ParticipantEmployeeList"
                            @change="handleChangePartUsers"></emp-selector>
                </el-form-item>

            </el-col>
            <!-- <el-col :span="24" v-else>
                <el-form-item label="审批意见" prop='ApprovalOpinion'>
                    <el-input style="width: 100%;" type="textarea" :rows="3" v-model="auditTemp.ApprovalOpinion" placeholder></el-input>
                </el-form-item>
            </el-col> -->
        </el-row>
        <el-row v-if="editDialogStatus == 'detail'">
            <comment-list :ref="'commentList'" businessType='project' :keyType='1' :id="auditTemp.ProjectManagementMilestoneId" :optType='0'></comment-list>
        </el-row>
       <div style="height: 10px; width: 100%;"></div>
      <el-row v-if="this.editDialogStatus == 'detail'">
        <opt-list :keyType='1' :id="auditTemp.ProjectManagementMilestoneId"></opt-list>
      </el-row>
      </el-form>
	      <div slot="footer" v-show="applicationOrApproval">
          <el-button @click="closeDialog">取消</el-button>
          <!-- <el-button @click="consent" type='primary' v-show="auditTemp.IsApproverEmployee">同意</el-button> -->
	        <el-button @click="transferTrial" type='primary' v-show="auditTemp.IsApproverEmployee" >转审</el-button>
	        <!-- <el-button @click="reject" type='primary' v-show="auditTemp.IsApproverEmployee" >驳回</el-button> -->
          <el-button @click="onComment" type="primary" v-show="editDialogStatus == 'detail'">评论</el-button>
          <el-button type='primary' @click="onChangeProgress" v-if="editDialogStatus == 'changeProgress'">提交</el-button>
          <el-button type="primary" @click="approval" v-show="auditTemp.IsApproverEmployee">审批</el-button>

	        <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" title="转审" :visible.sync="dialogApproval"
	          :close-on-click-modal='false' :append-to-body='true'>
	          <el-form ref="dataAuditForm" :model="transferTrialMsg" label-position="right"
	                label-width="100px">
	                <el-row>
	                  <el-col :span="24">
	                      <el-form-item label="新审批人" prop='NewApprover'>
	                          <!-- <el-input style="width: 100%;" v-model="transferTrialMsg.NewApprover" placeholder></el-input> -->
                           <emp-selector :showType="2" :multiple="false" :list="transferTrialMsg.NewApprover"  key='service-NewApprover'
                            @change="handleChangePartEmps"></emp-selector>
	                      </el-form-item>
	                  </el-col>
	                  <el-col :span="24">
	                      <el-form-item label="审批意见" prop='ApprovalOpinion'>
	                          <el-input
	                            type="textarea"
	                            :rows="3"
	                            placeholder="请输入内容"
	                            v-model="transferTrialMsg.ApprovalOpinion">
	                          </el-input>
	                      </el-form-item>
	                  </el-col>
	                 </el-row>
	              </el-form>
	              <div slot="footer">
                  <el-button @click="coloseDialogApproval">取消</el-button>
                  <el-button type='primary' @click="assign">确认</el-button>
                </div>
	        </el-dialog>
	      </div>
	      <div slot='footer' v-show="applicationOrApprovalBtn">
	        <el-button @click="closeDialog">取消</el-button>
	        <el-button type='primary' @click="save">保存</el-button>
	        <el-button type='primary' @click="submitAudit">提交</el-button>
          <el-button @click="onComment" type="primary" v-show="editDialogStatus == 'detail'">评论</el-button>
          <el-button type='primary' @click="onChangeProgress" v-if="editDialogStatus == 'changeProgress'">提交</el-button>
	      </div>
       <div slot='footer' v-show="isShowDetails">
	        <el-button @click="closeDialog">取消</el-button>
          <el-button @click="onComment" type="primary" v-show="editDialogStatus == 'detail'">评论</el-button>
          <el-button type='primary' @click="onChangeProgress" v-if="editDialogStatus == 'changeProgress'">提交</el-button>
	     </div>
       <div slot='footer' v-show="isAddEdit">
	        <el-button @click="closeDialog">取消</el-button>
          <el-button @click="save" type="primary">保存</el-button>
       </div>
        <div slot='footer' v-show="!isActualStartTimeDisabled">
	        <el-button @click="closeDialog">取消</el-button>
          <el-button type='primary' @click="submitStartApproval">提交</el-button>
          <el-button @click="onComment" type="primary" v-show="editDialogStatus == 'detail'">评论</el-button>
          <el-button type='primary' @click="onChangeProgress" v-if="editDialogStatus == 'changeProgress'">提交</el-button>
	     </div>
        <div slot='footer' v-show="!isActualEndTimeDisabled">
	        <el-button @click="closeDialog">取消</el-button>
          <el-button type='primary' @click="submitAccomplishApproval">提交</el-button>
          <el-button @click="onComment" type="primary" v-show="editDialogStatus == 'detail'">评论</el-button>
          <el-button type='primary' @click="onChangeProgress" v-if="editDialogStatus == 'changeProgress'">提交</el-button>
	     </div>

  </el-dialog>

    <!-- 审批弹框 -->
    <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" title="审批"
        :visible.sync="dialogAuditFormVisible2" :close-on-click-modal='false' :append-to-body='true'>
        <el-form :rules="auditRules2" ref="dataAuditForm2" :model="auditTemp2" label-position="right"
            label-width="100px">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="" prop='MakerList'>
                        <el-radio-group v-model="auditTemp2.Taged" size="mini">
                            <el-radio label="1" border>同意</el-radio>
                            <el-radio label="2" border>驳回</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="审批意见" prop='ApprovalOpinion'>
                        <el-input v-model="auditTemp2.ApprovalOpinion" clearable maxlength="500" type="textarea"
                            :rows="5"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer">
            <el-button size="mini" @click="handleCloseAssignDialog">取消</el-button>
            <el-button type='primary' @click="handleSaveAssign">确认</el-button>
        </div>
    </el-dialog>
  </div>
</template>

<script>
/**公共方法或枚举值 */
import {milestoneDatumType,isVersionPlanType} from "../../utils/commonEnum";
import elDragDialog from "@/directive/el-dragDialog";
import Treeselect from '@riophae/vue-treeselect'
import indexPageMixin from "@/mixins/indexPage";
import * as milestoneApi from "@/api/milestone";
import OptList from "../task/optList";
import EmpSelector from "../common/empSelector";
import CommentList from "../task/commentList"
import commentMixins from '../viewProjectInformation/commentMixins'
import questionMixins from '../question/localMixins'

export default {
  name: "milestoneApplicationOrApproval",
  components: {
   EmpSelector,
   CommentList,
   OptList,
  },
  mixins: [indexPageMixin, commentMixins,questionMixins],
  directives: {
    elDragDialog
  },
  props: {
    editDialogStatus: {
      type: String,
      default: ''
    },
    ProjectId: {
      type: String,
      default: ''
    }
  },
  watch: {
        iterationTreeData: {
            handler(val) {
                this.setTreeHead(val)
            },
            immediate: true
        },
    dialogAuditFormVisible(val) {
      if(!val) {
        this.$emit('closeDialog')
      }
    },
    dialogAuditFormVisible2(val) {
      if (!val) {
          this.resetAuditTemp()
          if (this.$refs['dataAuditForm2']) {
              this.$nextTick(() => {
                  this.$refs['dataAuditForm2'].clearValidate()
              })
          }
      }
    },
  },
  data() {
    return {
      normalizer(node) {
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        }
      },
      applicationOrApproval:false,//判断为审批还是申请
      applicationOrApprovalBtn:false,
      dialogApproval:false,
      // dialogAuditFormVisible: this.dialogFormVisible,
      dialogAuditFormVisible:false,
      isLegacyMattersDisabled:false,
      isShowDetails:false,
      isAddEdit:false,
      title:"",
      isActualStartTimeDisabled:true,
      isActualEndTimeDisabled:true,
      editable2:false,
      disEditable:true,
      isStartApproval:false,  //是否为开始审批业务
      milestoneDatumType: milestoneDatumType,
      isVersionPlanType:isVersionPlanType,
      isApproverEmployee:false,  //是否为审批人
      isAttachmentResourceListReadonly:false,
      auditTemp:{
        ProjectManagementMilestoneId:'',
        MilestoneCode:'',
        Name:'',
        PlannedStartTime:'',
        PlannedEndTime:'',
        ActualStartTime:'',
        MilestoneDatumTypeId:1,
        MilestoneStatus:0,
        ExpirationReminderThreshold:0,
        ActualCompletionTime:'',
        ExpirationReminder:'',
        Deliverable:'',
        LegacyMatters:'',
        Enclosure:'',
        Approver:'',
        ApprovalOpinion:'',
        AttachmentResourceList:[],
        ParticipantEmployeeList:[],
        MakerEmployeeIdlist:[],
        IsApproverEmployee:false,   //是否为审批人，后端返回用来判断的，提交时不用传递
        ProgressValue: 0, //进度,
        ProjectId:this.ProjectId,
        ProjectManagementVersionPlanId:"00000000-0000-0000-0000-000000000000",
      },
      auditTempModule:{
        ProjectManagementMilestoneId:'',
        MilestoneCode:'',
        Name:'',
        PlannedStartTime:'',
        PlannedEndTime:'',
        ActualStartTime:'',
        MilestoneDatumTypeId:1,
        MilestoneStatus:0,
        ExpirationReminderThreshold:0,
        ActualCompletionTime:'',
        ExpirationReminder:'',
        Deliverable:'',
        LegacyMatters:'',
        Enclosure:'',
        Approver:'',
        ApprovalOpinion:'',
        AttachmentResourceList:[],
        ParticipantEmployeeList:[],
        MakerEmployeeIdlist:[],
        IsApproverEmployee:false,   //是否为审批人，后端返回用来判断的，提交时不用传递
        ProgressValue: 0, //进度
        ProjectId:this.ProjectId,
        ProjectManagementVersionPlanId:"00000000-0000-0000-0000-000000000000",
      },
      disabledMilestoneCode:true,
      transferTrialMsg:{
        NewApprover:[],
        ApprovalOpinion:''
      },
      /**校验规则 */
      rules: {
        Name: {
          fieldName: "里程碑点",
          rules: [{ required: true }]
        },
        PlannedStartTime: {
          fieldName: "计划开始时间",
          rules: [{ required: true }]
        },
        PlannedEndTime: {
          fieldName: "计划完成时间",
          rules: [{ required: true }]
        },
        // ProjectManagementVersionPlanId: {
        //   fieldName: "版本/迭代",
        //   rules: [{ required: true }]
        // }
      },
      dialogAuditFormVisible2: false, //审批弹框
      //审批
      auditRules2: {

      },
      //审批
      auditTemp2: {
          Taged: '1', //1：通过；2：驳回
          ApprovalOpinion: '',//审批意见，通过可以不填，驳回必填
      },
      transIterationTreeData:[{Id:"00000000-0000-0000-0000-000000000000",
                    ParentId: null,
                    label: "非版本/迭代"
                    }],
    };
  },
  created() {
    this.rules = this.initRules(this.rules)

    this.auditRules2 = this.initRules(this.auditRules2)
    const validateRequired = (rule, value, callback) => {
        if (value.length <= 0 && this.auditTemp2.Taged == '2') {
            callback(new Error(`驳回时审批意见不能为空`))
        } else {
            callback()
        }
    }
    if (!this.auditRules2['ApprovalOpinion'])
        this.auditRules2['ApprovalOpinion'] = []
    this.auditRules2.ApprovalOpinion.push({ validator: validateRequired, trigger: 'blur' })

            // const validateText = (rule, value, callback) => {
            //     if (value.length <= 0) {
            //         let equText = this.getTextOfType(this.temp.TypeId)
            //         callback(new Error(`${equText}不能为空`))
            //     } else {
            //         callback()
            //     }
            // }
            // if(!this.rules['Name'])
            //     this.rules['Name'] = []
            // this.rules.Name.push({validator: validateText, trigger: 'blur'})

  },
  mounted() {
  },
  methods:{
    setTreeHead(val){
      var tempData={Id:"00000000-0000-0000-0000-000000000000",
                    ParentId: null,
                    label: "非版本/迭代"
                    }
                  this.transIterationTreeData=JSON.parse(JSON.stringify(val));
                  this.transIterationTreeData.unshift(tempData);

    },
    childShow(obj,t){
      this.disEditable=true;
      this.isShowDetails=false
      this.isAddEdit=false,
      this.dialogAuditFormVisible=true;
      this.isLegacyMattersDisabled=t;
      this.applicationOrApproval=t;
      this.editable2=t;
      this.applicationOrApprovalBtn=(!this.applicationOrApproval);
      this.isActualStartTimeDisabled=t;
      this.isActualEndTimeDisabled=t;
      this.isLegacyMattersDisabled=true;
      this.isAttachmentResourceListReadonly=true;
      this.auditTemp.ProjectManagementMilestoneId=obj.ProjectManagementMilestoneId;
      this.getDetail()
      this.title=this.applicationOrApproval ? '里程碑审批' : '里程碑变更申请'
    },
    showDetails(id){
      this.editable2=true
      this.disEditable=true;
      this.title="查看详情"
      // this.id = obj.
      this.isAddEdit=false;
      this.dialogAuditFormVisible=true;
      this.applicationOrApproval=false;
      this.applicationOrApprovalBtn=false;
      this.isLegacyMattersDisabled=true;
      this.isAttachmentResourceListReadonly=true;
      this.auditTemp.ProjectManagementMilestoneId=id;
      this.getDetail()
      this.isShowDetails=true
      this.isLegacyMattersDisabled=true
      this.isActualStartTimeDisabled=true;
      this.isActualEndTimeDisabled=true;
    },
    showAddEdit(id){
      if(id&&id!=""){
      this.title="查看详情"
      }else{
      this.title="新增"
      this.auditTemp=JSON.parse(JSON.stringify(this.auditTempModule))
      }
      // this.id = obj.
      this.isAddEdit=true;
      this.isShowDetails=false
      this.dialogAuditFormVisible=true;
      this.applicationOrApproval=false;
      this.applicationOrApprovalBtn=false;
      this.isLegacyMattersDisabled=true;
      this.isAttachmentResourceListReadonly=true;
      this.auditTemp.ProjectManagementMilestoneId=id;
      this.editable2=true
      this.disEditable=false;

      if(id&&id!=""){
      this.getDetail()
      }
      this.isLegacyMattersDisabled=true
      this.isActualStartTimeDisabled=true;
      this.isActualEndTimeDisabled=true;
    },
   rowSelectionChanged(){},
   getList(){},
   handleCurrentChange(){},
   handleSizeChange(){},
   transferTrial(){
    this.dialogApproval=true;
   },
   getDetail(){
     milestoneApi.detail({id:this.auditTemp.ProjectManagementMilestoneId}).then(response=>{
        this.auditTemp=response
     })
   },
   handleFilesUpChange(files) {
       this.auditTemp.AttachmentResourceList = files;
    },
   save(){
      this.$refs["dataForm"].validate(valid => {
        // if (!valid) {
        //   this.postLoading = false;
        // }

        if (valid) {
          // let formData = JSON.parse(JSON.stringify(self.temp));
          if(this.auditTemp.ParticipantEmployeeList){
              this.auditTemp.MakerEmployeeIdlist=this.auditTemp.ParticipantEmployeeList.map(s => s.EmployeeId);
          }
          if(this.editDialogStatus=="create"){

          milestoneApi.add(this.auditTemp).then(response=>{
              this.closeDialog();
          });
          }else{

          milestoneApi.edit(this.auditTemp).then(response=>{
              this.closeDialog();
          });
          }
        }
      });
    },
    //保存修改进度
    onChangeProgress() {
      let postDatas = JSON.parse(JSON.stringify(this.auditTemp))
      let ProgressValue = postDatas.ProgressValue
      let Id = postDatas.ProjectManagementMilestoneId
      milestoneApi.changeProgress({
        Id,
        ProgressValue
      }).then(response=>{
          this.closeDialog()
      });
    },
    submitAudit(){
        this.$refs["dataForm"].validate(valid => {
              if (valid) {
                  if(!this.auditTemp.ParticipantEmployeeList || this.auditTemp.ParticipantEmployeeList.length == 0) {
                      this.$message({
                          message: '请选择审批人',
                          type: 'error'
                      })
                  }else{
                    // 提交审批时，审批人不能为空
                    this.auditTemp.MakerEmployeeIdlist=this.auditTemp.ParticipantEmployeeList.map(s => s.EmployeeId);
                    milestoneApi.submitAudit(this.auditTemp).then(response=>{
                        this.closeDialog();
                    });
                  }
              }
         })
    },
    // nodeAudit(tagid){
    //   if(!this.auditTemp.ApprovalOpinion || this.auditTemp.ApprovalOpinion.length==0){
    //     this.$message({
    //         message: '请输入审批意见',
    //         type: 'error'
    //    })
    //    return;
    //   }
    //   var data={ProjectManagementMilestoneId:this.auditTemp.ProjectManagementMilestoneId,Description:this.auditTemp.ApprovalOpinion,Taged:tagid};
    //    milestoneApi.nodeAudit(data).then(response=>{
    //         this.closeDialog();
    //    });
    // },
    handleChangePartUsers(users){
    if (users && users.length > 0) {
          this.auditTemp.ParticipantEmployeeList = users;
        } else {
          this.auditTemp.ParticipantEmployeeList = [];
     }
    },
    closeDialog() {
      this.dialogAuditFormVisible = false
      this.$emit('getList')
    },
    // reject(){   //驳回,状态码参考维修单工作流
    //      this.nodeAudit(3);
    // },
    // consent(){    //同意,状态码参考维修单工作流
    //   this.nodeAudit(1);
    // },
    approval() {
      this.dialogAuditFormVisible2 = true
    },
    assign(){   // 转审
            if(!this.transferTrialMsg.NewApprover || this.transferTrialMsg.NewApprover.length == 0) {
                  this.$message({
                      message: '请选择审批人',
                      type: 'error'
              })
            }

            if(!this.transferTrialMsg.ApprovalOpinion || this.transferTrialMsg.ApprovalOpinion.length==0){
              this.$message({
                  message: '请输入转审意见',
                  type: 'error'
            })
            return;
            }

        var data={ProjectManagementMilestoneId:this.auditTemp.ProjectManagementMilestoneId,Description:this.transferTrialMsg.ApprovalOpinion,MakerList:this.transferTrialMsg.NewApprover.map(s => s.EmployeeId)};
        milestoneApi.assign(data).then(response=>{
              this.coloseDialogApproval();
              this.closeDialog()
        });
    },
    handleChangePartEmps(users){
    if (users && users.length > 0) {
          this.transferTrialMsg.NewApprover = users;
        } else {
          this.transferTrialMsg.NewApprover = [];
       }
    },
    coloseDialogApproval(){
         this.dialogApproval=false;
    },
    start(){
        this.isActualEndTimeDisabled=true;
        this.isActualStartTimeDisabled=false;
        this.applicationOrApproval=false;
        this.applicationOrApprovalBtn=false;
        this.isLegacyMattersDisabled=true;
        this.isAttachmentResourceListReadonly=true;
        this.title="里程碑开始"
    },
    startApproval(){  //开始审批业务

    },
    accomplish(){
        this.isActualEndTimeDisabled=false;
        this.isActualStartTimeDisabled=true;
        this.applicationOrApproval=false;
        this.applicationOrApprovalBtn=false;
        this.isLegacyMattersDisabled=false;
        this.isAttachmentResourceListReadonly=false;
        this.title="里程碑完成"
    },
    submitStartApproval(){  //提交开始审批
          if(!this.auditTemp.ParticipantEmployeeList || this.auditTemp.ParticipantEmployeeList.length == 0) {
                this.$message({
                    message: '请选择审批人',
                    type: 'error'
                  })
               return;
          }
          if(!this.auditTemp.ActualStartTime){
              this.$message({
                  message: '请输入实际开始时间',
                  type: 'error'
                })
              return;
          }

         // 提交审批时，审批人不能为空
         this.auditTemp.MakerEmployeeIdlist=this.auditTemp.ParticipantEmployeeList.map(s => s.EmployeeId);
         milestoneApi.submitStartApprovalAsync(this.auditTemp).then(response=>{
              this.closeDialog();
           });
    },
    submitAccomplishApproval(){   //提交完成审批
        if(!this.auditTemp.ParticipantEmployeeList || this.auditTemp.ParticipantEmployeeList.length == 0) {
              this.$message({
                  message: '请选择审批人',
                  type: 'error'
                })
              return;
         }
         if(!this.auditTemp.ActualEndTime){
            this.$message({
                message: '请输入实际完成时间',
                type: 'error'
              })
            return;
         }

        if(!this.auditTemp.LegacyMatters || this.auditTemp.LegacyMatters.length==0){
            this.$message({
                  message: '请输入遗留事项',
                  type: 'error'
            })
            return;
        }

        // 提交审批时，审批人不能为空
        this.auditTemp.MakerEmployeeIdlist=this.auditTemp.ParticipantEmployeeList.map(s => s.EmployeeId);
        milestoneApi.submitAccomplishApprovalAsync(this.auditTemp).then(response=>{
          this.closeDialog();
        });
    },
    saveStartApproval(obj){  //保存开始审批

    },
    saveAccomplishApproval(){   //保存完成审批

    },
    handleCloseAssignDialog() {
        // if (this.auditType == 'assign') {
        //     this.dialogAssignFormVisible = false
        // } else if (this.auditType == 'audit') {

        // }
        this.dialogAuditFormVisible2 = false
    },

    //确认转审
    handleSaveAssign() {
        //提示转审批成功
        let self = this;
        this.$refs['dataAuditForm2'].validate((valid) => {
            if (valid) {
                let dialogFormData = JSON.parse(JSON.stringify(self.auditTemp2))

                var data={
                  ProjectManagementMilestoneId: this.auditTemp.ProjectManagementMilestoneId,
                  Description: dialogFormData.ApprovalOpinion,
                  Taged: dialogFormData.Taged == '1' ? 1 : 3
                };

                milestoneApi.nodeAudit(data).then(response=>{
                  self.$notify({
                    title: "成功",
                    message: "审批成功",
                    type: "success",
                    duration: 2000
                  });
                  self.handleCloseAssignDialog()
                  self.closeDialog();
                });

            }
        })

    },
        //重置审批表单
    resetAuditTemp() {
        this.auditTemp2 = {
            Taged: '1', //1：通过；2：驳回
            ApprovalOpinion: '',//审批意见，通过可以不填，驳回必填
        }
    },
  }
};
</script>
<style lang='scss' scoped>
  .path-wrapper {
    top: 0;
    bottom: 0;
    box-sizing: border-box;
  }

  /* .form-container>>>.path-title-wrapper {
    padding: 0 10px;
    padding-bottom: 10px;
    padding-top: 10px;
    line-height: 20px;
    box-sizing: border-box;
    color: #303133;
    background: #f8f8f8;
  }

  .form-container>>>.path-title-wrapper div:first-child {
    float: left;
    font-size: 16px;
  }

  .form-container>>>.path-title-wrapper div:last-child {
    float: right;
  } */

  .pager-wrapper {
    text-align: center;
  }

  .split-line {
    height: 10px; width: 100%;
  }

  .el-radio {
    margin: 0 !important;
  }
</style>
