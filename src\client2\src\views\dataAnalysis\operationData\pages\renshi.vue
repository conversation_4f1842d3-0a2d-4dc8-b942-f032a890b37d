<template>
    <div class="block-content" v-loading='loading'>
        <!-- <blockTitle :obj='obj'>
            <div slot='rht'>
                <el-radio-group v-model="period" size="mini" @change='getPersonnelDetailsChart'>
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </div>
        </blockTitle> -->
        
          <el-row style="width:100%; margin-top:19px; margin-bottom:5px;">
            <el-col :span=12 style="display:flex;">
                <span style="margin-left:30px; width: 5px; height: 20px; background: #3D73DD;"></span>
                <span style="margin-top:2px; font-size:16px; color: #1D2129; margin-left:11px; font-weight:bold;">人事</span>
            </el-col>
            <el-col :span=12 style="display:flex; justify-content:flex-end;">
                <el-radio-group v-model="period" size="mini" @change='getPersonnelDetailsChart' style="margin-right:15px;">
                    <el-radio-button v-for="item in dateTypeEnum" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </el-col>
         </el-row>


        <div class="inner-content">
            <el-row style="width: 100%; height: 1px; background: #DCDFE6;"></el-row>
            <div class="top">
                <div style="margin-left:30px; display:flex;">
                    <span class="shape"></span>
                    <div  class="flex-dire-column">
                           <span class="top-title">新入职员工</span>
                           <span class="text-content">
                            {{ formData.NewComer }}
                           </span>
                    </div>
                </div>
                <div style="margin-left:100px; display:flex;">
                    <span class="shape"></span>
                    <div class="flex-dire-column">
                        <span class="top-title">转正员工</span>
                        <span class="text-content">
                            {{ formData.PositiveEmployees }}
                        </span>
                    </div>
                </div>
                <div style="margin-left:100px; display:flex;">
                     <span class="shape"></span>
                    <div class="flex-dire-column" >
                       <span class="top-title">离职员工</span>
                       <span class="text-content">
                            {{ formData.SeparatedEmployee }}
                       </span>
                    </div>
                    
                </div>
                <!-- <div class="flex-dire-column-wrapper">
                    <div class="chart-title a-c">出差员工</div>
                    <div class="flex-1">
                        <div class="avatar-list-wrapper">
                            <div class="inner-list-wrapper">
                                <div v-for="(item, idx) in formData.BusinessTripEmployeeList" :key="idx" class="avatar-wrapper">
                                    <el-avatar :key="item.EmployeesId" fit='cover' :size="50" :src="item.AvatarPath || defavatar"></el-avatar>
                                    <div :title="item.Name" class="omit">{{ item.Name }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
            <div class="bottom">
                <div class="flex-dire-column-wrapper">
                    <span class="bottom-title">加班天数排行</span>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption1.series || pieEchartOption1.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :isShowAxisLabel='false' :width='chartsWidth' :height='chartsHeight' ref="pieEchart1" :xAxisLabelTooltip='true' :xAxisLabelLimit='5' :rotate='25' :option='pieEchartOption1'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <span class="bottom-title">请假天数排行</span>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption2.series || pieEchartOption2.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :isShowAxisLabel='false' :width='chartsWidth' :height='chartsHeight' ref="pieEchart2" :xAxisLabelTooltip='true' :xAxisLabelLimit='5' :rotate='25' :option='pieEchartOption2'></app-charts-basic>
                    </div>
                </div>
                <div class="flex-dire-column-wrapper">
                    <span class="bottom-title">迟到次数排行</span>
                    <div class="flex-1">
                        <noData v-if="!pieEchartOption3.series || pieEchartOption3.series[0].data.length == 0"></noData>
                        <app-charts-basic v-else :isShowAxisLabel='false' :width='chartsWidth' :height='chartsHeight' ref="pieEchart3" :xAxisLabelTooltip='true' :xAxisLabelLimit='5' :rotate='25' :option='pieEchartOption3'></app-charts-basic>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import noData from "@/views/common/components/noData";
import blockTitle from '../blockTitle'
import * as odc from "@/api/operatingDataCenter";
import { barOption, colors, dateTypeEnum1 } from "../vars";

export default {
    name: 'renshi',
    components: {
        noData,
        blockTitle,
    },
    props: {
        obj: {
            type: Object,
            required: true
        }
    },
    mounted() {
        this.getPersonnelDetailsChart()
    },
    data() {
        return {
            period: 3,
            dateTypeEnum: dateTypeEnum1,
            loading: false,
            chartsHeight: '260px',
            chartsWidth: '90%',
            pieEchartOption1: JSON.parse(JSON.stringify(barOption)),
            pieEchartOption2: JSON.parse(JSON.stringify(barOption)),
            pieEchartOption3: JSON.parse(JSON.stringify(barOption)),
            
            formData: {
                NewComer: 0, 
                PositiveEmployees: 0, 
                SeparatedEmployee: 0, 
                BusinessTripEmployeeList: [],
            },
        }
    },
    methods: {
        getPersonnelDetailsChart() {
            let that = this
            that.loading = true
            odc.getPersonnelDetailsChart({Period: that.period}).then(res => {
                that.loading = false

                that.formData.NewComer = res.NewComer || 0
                that.formData.PositiveEmployees = res.PositiveEmployees || 0
                that.formData.SeparatedEmployee = res.SeparatedEmployee || 0
                that.formData.BusinessTripEmployeeList = res.BusinessTripEmployeeList || []

                that.pieEchartOption1 = that._initBarChartDatas(res.OvertimeDaysList || [])
                that.pieEchartOption2 = that._initBarChartDatas(res.LeaveDaysList || [])
                that.pieEchartOption3 = that._initBarChartDatas(res.TardinessDaysList || [])
                
            }).catch(err => {
                that.loading = false
            })

        },

        _initBarChartDatas(list) {
            if(!list) {
                list = []
            }

            let chartDatas = JSON.parse(JSON.stringify(list || []))
            let targetOption = {}
            if(chartDatas && chartDatas.length > 0) {
                targetOption = {
                     grid:{
                        top: 20,
                        right: 20,
                        bottom: 20,
                        left: 80
                    },
                    xAxis: {
                        type: 'value',
                        axisLine: { // 隐藏X轴
                          show: false
                        },
                        axisTick: { // 隐藏X轴刻度值
                         show: false
                        },
                        axisLabel: {
                          show: false
                        },
                         splitLine: { // 横向虚线
                           lineStyle:{
                               color: '#F2E9EAEF', // 颜色
                           }   
                       },

                     },
                   
                    yAxis: {
                       type: 'category',
                       splitLine: { // 横向虚线
                           show: false,
                           lineStyle:{
                           type:'dashed'
                           }   
                       },
                       axisLine: {
                            lineStyle: {
                                 color: '#F2E9EAEF', // 颜色
                               }
                       },
                       axisLabel: {
                            textStyle: {
                              show:true,
                                color: "#1D2129",
                                fontSize: '12',
                                fontWeight: 'bold',
                            },                           
                        },

                       data: chartDatas.map(s => s.Label)
                    },

                    series: [
                        {    itemStyle:{
                                 normal:{
                                      color:'#3D73DD'
                                  }
                            },
                            label: {
                                     show: true,
                                     position: 'right'
                             },
                            data: chartDatas.map(s => s.Value),
                           
                        }
                    ]
                }
            }

            targetOption = _.merge({}, JSON.parse(JSON.stringify(barOption)), targetOption)


            return targetOption

        },
    },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';

.block-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    .inner-content{
        // flex: 1;
        // display: flex;
        // flex-direction: column;
        .bottom{
            margin-top: 46px;
            flex: 1;
            display: flex;
            .flex-dire-column-wrapper{
                flex: 1;
                display: flex;
                height: 100%;
                flex-direction: column;
            }
        }
        .top{
             margin-top: 30px;
             display: flex;
        }
        // .top{
        //     .flex-dire-column-wrapper:last-child{
        //         flex: 2;
        //     }
        // }

        .flex-dire-column{
            display: flex;
            flex-direction: column;
            margin-left: 11px;
        }

    }
}

.top-title{
    font-size: 14px;
    color: #A0A1A3;
}

.bottom-title{
    font-size: 16px;
    color: $text-main-color;
    font-weight: bold;
    margin-left: 30px;
}



// .flex-1, .flex-2{
//     box-sizing: border-box;
//     margin: 5px;
//     border-radius: 5px;
//     display: flex;
//     flex-direction: column;
//     >div:last-child{
//         flex: 1;
//     }
// }

.flex-1{
    
    margin-top: 19px;
    display: flex;
    justify-content: center;
    align-items: center;

}

.text-content{
    padding: 8px; 
    color: $text-primary;
    font-weight:bold;
    font-size: 30px;
}

.shape{
   width: 10px;height: 50px;background: #E9EAEF;
}


// .avatar-wrapper{
//     width: 50px;
//     height: 70px;
//     /deep/img{
//         height: 50px;
//         width: 100%;
//         // height: 100%;
//     }
// }

// .avatar-list-wrapper{
//     position: relative;
//     flex: 1;
//     flex-shrink: 0;
//     overflow-y: auto;

//     display: flex;
//     align-items: center;
//     align-content: center;
//     flex-wrap: wrap;
//     .inner-list-wrapper{
//         position: absolute;
//         top: 50%;
//         transform: translateY(-50%);
//         overflow: overlay;
//         max-height: 100%;

//         display: flex;
//         flex-wrap: wrap;
//         >div{
//             margin: 2px;
//             text-align: center;
//         }
//     }

// }
</style>