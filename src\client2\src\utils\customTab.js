/**
 * 自定义表格共用方法
 *
 *
 */

import * as cte from "@/api/configTableHead";

/**
 * 表格序号列样式标识
 */
export const headerClassNameFlagNum = "header__cell__num";
/**
 * 表格操作列样式标识
 */
export const headerClassNameFlagOpt = "header__cell__opt";
/**
 * 表格不能拖拽列样式标识
 */
export const headerClassNameFlagUnDraggable = "header__cell__undraggable";

/**
 * 能移动
 */
export const headerClassNameFlagDraggable = "header__cell__draggable";

/**
 * 表格header更多操作按钮
 */
export const headerClassMoreBtn = 'header__cell__more__button'

/**
 * 表格列，更多操作列表，是否显示“左移”
 * 第一列不显示“左移”，排序冻结列；
 * true：显示；false：不显示
 * @param {String} colProp
 * @param {Array} columns
 * @returns
 */
export function isShowLeft(colProp, columns) {
  let notFixeds = columns.filter(s => !s.attr.fixed);
  if (notFixeds.length > 0 && notFixeds[0].attr.prop != colProp) {
    return true;
  }

  return false;
}

/**
 * 表格列，更多操作列表，是否显示“右移”
 * 最后列不显示“左移”
 * true：显示；false：不显示
 * @param {String} colProp
 * @param {Arrayy} columns
 * @returns
 */
export function isShowRight(colProp, columns) {
  let notFixeds = columns.filter(s => !s.attr.fixed);
  if (notFixeds.length > 0 && notFixeds[notFixeds.length - 1].attr.prop != colProp) {
    return true;
  }
  return false;
}

/**
 * 初始化表格 tabColumns 属性。
 * 主要逻辑：
 * 1、根据数据库存储列集合恢复排序，一级一些通用属性；
 * 2、防止表格 tabColumns 属性设置不符合逻辑，所以需要纠正；
 * @param {Array} columns 通用的表格 tabColumns 属性
 * @param {Array} showColumns 数据存储的设置好后的列集合 [{prop: 'xxx', isShow: true}]，存储一些必要属性
 * @returns
 */
export function initTabColumns(vm, columns, showColumns = []) {
  //将列按照服务端返回顺序排序（以本地配置数据为准，数据库多的列丢弃，本地配置多的列追加到最后）
  if (columns && columns.length > 0 && showColumns && showColumns.length > 0) {
    let flag = 0;
    showColumns.forEach((c, cIdx) => {
      let currColIdx = columns.findIndex(s => s.attr.prop == c.prop);
      if (currColIdx > -1) {
        let currCol = columns.splice(currColIdx, 1)[0];
        columns.splice(flag, 0, currCol);

        flag++;
      }
    });
  }

  /**
   * 数据设置不规范处理逻辑；
   * 如果没有设置“禁止隐藏列”，那么第一个分组第一列设置为禁止隐藏列；
   * “禁止隐藏列”一定是默认显示列，并且设置为显示（isShow: true）；
   */
  //不能隐藏的列
  let disabledCloseList = columns.filter(s => s.attr.disabledClose).map(s => s.attr.prop);

  //如果没有设置不能隐藏的列，那么第一组的第一列设置为不能关闭的列
  if (disabledCloseList.length == 0) {
    disabledCloseList.push(columns[0].attr.prop);
  }

  columns.forEach(f => {
    if (!f.attr.disabledClose) {
      vm.$set(f.attr, "disabledClose", false);
    }
    if (!f.attr.defaultShow) {
      vm.$set(f.attr, "defaultShow", false);
    }
    if (!f.attr.isShow) {
      vm.$set(f.attr, "isShow", false);
    }
    if (!f.attr.fixed) {
      vm.$set(f.attr, "fixed", false);
    }
    if (!f.attr.ascending) {
      vm.$set(f.attr, "ascending", false);
    }
    if (!f.attr.descending) {
      vm.$set(f.attr, "descending", false);
    }
    if(!f.attr.width) {
      vm.$set(f.attr, 'width', '')
    }

    if (disabledCloseList.find(s => s == f.attr.prop)) {
      f.attr.disabledClose = true;
      f.attr.defaultShow = true;
      f.attr.isShow = true;
    }

    //如果没有额外设置显示的列
    if (showColumns.length == 0) {
      //那么默认显示的列显示出来；并且当前列没有显示；并且默认需要显示
      if (!f.attr.isShow && f.attr.defaultShow) {
        f.attr.isShow = true;
      }
    } else {
      let mapObj = showColumns.find(s => s.prop == f.attr.prop);
      if (mapObj) {
        f.attr = Object.assign({}, f.attr, mapObj);
        f.attr.fixed = mapObj.fixed || false;
        f.attr.width = mapObj.width || ""

        // f.attr.isShow = mapObj.isShow;
        // vm.$set(f.attr, 'isShow', mapObj.isShow)
        // vm.$set(f.attr, 'fixed', mapObj.fixed || false)
      }
    }
  });
}

/**
 * 保存设置后的列状态，只保存需要保存的列
 * @param {Array} columns 通用的表格 tabColumns 属性
 * @param {Number} viewBusinessType
 */
let _saveTabColumns = (columns, viewBusinessType) => {
  if (viewBusinessType <= 0) {
    console.log("未配置 viewBusinessType 参数，所以无法保存");
    return;
  }

  let list = columns.map(s => {
    let temp = {
      prop: s.attr.prop,
      isShow: !!s.attr.isShow,
      fixed: s.attr.fixed || false,
      width: s.attr.width || "",
    };
    return temp;
  });

  let postDatas = {
    ViewBusinessType: viewBusinessType,
    Content: JSON.stringify(list),
  };

  cte.edit(postDatas).then(res => {});
};

export function saveTabColumns(columns, viewBusinessType) {
  _saveTabColumns(columns, viewBusinessType);
}

export function handleHeaderDragendSuccess(prop, width, columns, viewBusinessType) {
  let obj = columns.find(s => s.attr.prop == prop);
  if (obj) {
    obj.width = width;
    _saveTabColumns(columns, viewBusinessType);
  }
}

/**
 * 表格列拖拽成功操作
 * @param {*} obj 操作成功返回对象
 * @param {*} columns 通用的表格 tabColumns 属性
 */
export function handleDragSuccess(obj, columns, viewBusinessType) {
  debugger;
  debugger;

  //左移、右移
  let direction = obj.direction;
  //移动对象
  let current = obj.current;
  //参考对象
  let target = obj.target;

  let _tabCols = columns;
  let currIdx = _tabCols.findIndex(s => s.attr.prop == current);
  let tarIdx = _tabCols.findIndex(s => s.attr.prop == target);
  if (currIdx > -1 && tarIdx > -1) {

    let _max = currIdx > tarIdx ? currIdx : tarIdx
    let _min = currIdx < tarIdx ? currIdx : tarIdx

    let currCol = _tabCols[_max];
    _tabCols.splice(_max, 1);
    _tabCols.splice(_min, 0, currCol);
  }

  console.log(_tabCols.map(s => s.attr.label).join('、'))

  //入库
  if (viewBusinessType) {
    _saveTabColumns(columns, viewBusinessType);
  }
}

/**
 * 表格列下拉菜单操作成功后回调
 * @param {*} colProp
 * @param {*} optCmd
 * @param {*} columns
 * @param {*} viewBusinessType
 * @returns
 */
export function customerCellCallbackColumnMoreOpt(colProp, optCmd, columns, viewBusinessType) {
  debugger;
  debugger;

  let _tabCols = columns;

  // console.log('排序前：', _tabCols.map(s => s.attr.label).join('、'))

  //排序（表格只支持一列升序或降序）
  if (
    optCmd == "ascending" ||
    optCmd == "descending" ||
    optCmd == "un-ascending" ||
    optCmd == "un-descending"
  ) {
    //所有升序、降序都取消
    _tabCols
      .filter(s => s.attr.ascending || s.attr.descending)
      .forEach(s => {
        s.attr.ascending = false;
        s.attr.descending = false;
      });

    //如果是升序或降序
    let curColumn = _tabCols.find(s => s.attr.prop == colProp);
    if (curColumn && (optCmd == "ascending" || optCmd == "descending")) {
      curColumn.attr[optCmd] = true;
    }
  } else if (optCmd == "left" || optCmd == "right") {
    //移动

    //当前移动列索引（在整个tabColumns的索引）
    let curColumnIdx = _tabCols.findIndex(s => s.attr.prop == colProp);

    if (!(curColumnIdx > -1)) {
      return false;
    }

    //当前移动的列
    let curColumn = _tabCols.splice(curColumnIdx, 1)[0];

    if (curColumnIdx > -1) {
      if (curColumn) {
        //移动到的目标位置索引
        let targetIndex = -1;
        if (optCmd == "left") {
          //左移
          targetIndex = _tabCols
            .slice(0, curColumnIdx)
            .findLastIndex(s => s.attr.isShow && !s.attr.fixed);
        } else {
          //右移
          let nextColumn = _tabCols.slice(curColumnIdx).find(s => s.attr.isShow && !s.attr.fixed);
          if (nextColumn) {
            targetIndex = _tabCols.findIndex(s => s.attr.prop == nextColumn.attr.prop) + 1;
          }
        }
        if (targetIndex > -1) {
          _tabCols.splice(targetIndex, 0, curColumn);
        }

        // console.log('排序后：', _tabCols.map(s => s.attr.label).join('、'))
      }
    }
  } else if (optCmd == "frozen" || optCmd == "un-frozen") {
    //冻结
    let curColumn = _tabCols.find(s => s.attr.prop == colProp);
    if (curColumn) {
      curColumn.attr.fixed = optCmd == "frozen" ? true : false;

      //   vm.$set(curColumn.attr, "fixed", optCmd == "frozen" ? true : false);
    }
  } else if (optCmd == "hide") {
    //隐藏
    let curColumn = _tabCols.find(s => s.attr.prop == colProp);
    if (curColumn) {
      curColumn.attr.isShow = false;
    }
  }

  //入库
  if (viewBusinessType) {
    _saveTabColumns(columns, viewBusinessType);
  }
}
