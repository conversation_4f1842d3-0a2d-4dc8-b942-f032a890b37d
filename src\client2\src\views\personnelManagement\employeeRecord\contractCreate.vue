<template>
<div class="createEmployee">
    <app-dialog :title="title" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width="600">
        <template slot="body">
            <el-form
            :rules="rules"
            ref="formData"
            :model="formData"
            label-position="right"
            label-width="110px"
            >
                <div class="wrapper" v-loading='loading'>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="合同主体名称" prop="LaborContractSubjectName">
                                <el-input maxlength="100" :disabled="!editable" v-model="formData.LaborContractSubjectName"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="Remark">
                                <el-input maxlength="500" type="textarea" :rows="5" :disabled="!editable" v-model="formData.Remark"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>


export default {
    name: "contract-create",
    directives: {},
    components: {},
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        row: {
            type: Object,
            default: null
        },
        rowIdx: {
            type: Number,
            default: -1
        }
    },
    filters: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                 this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            },
            immediate: true
        },
        row: {
            handler(val) {
                if(val) {
                    this.formData = Object.assign({}, this.formData, JSON.parse(JSON.stringify(val)))
                }
            },
            deep: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        title() {
            if (this.dialogStatus == "create") {
                return "创建合同主体";
            } else if (this.dialogStatus == "update") {
                return "编辑合同主体";
            } else if (this.dialogStatus == "detail") {
                return "合同主体详情";
            }
            return "";
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    mounted() {
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {
                LaborContractSubjectName: {
                fieldName: "产品名称",
                    rules: [{ required: true, max: 100 }]
                },
                // Remark: {
                //     fieldName: "产品描述",
                //     rules: [{ required: true, max: 2000 }]
                // },
            },
            formData: {
                Id: "",
                LaborContractSubjectName: "", //
                Remark: "", //
            }
        };
    },
    methods: {
        resetFormData() {
            this.formData = {
                Id: "",
                LaborContractSubjectName: "", //
                Remark: "", //
            };
        },
        getDetail() {
            this.loading = true
            prod.detail({ id: this.id }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);
            }).catch(err => {
                this.loading = false
            });
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                let postData = JSON.parse(JSON.stringify(this.formData));
                
                // let result = null;

                this.$refs.appDialogRef.createData({
                    dialogStatus: this.dialogStatus, 
                    formObj: postData,
                    idx: this.rowIdx
                });

                // this.disabledBtn = true;
                // if (this.dialogStatus == "create") {
                //     delete postData.Id;
                //     result = prod.add(postData);
                // } else if (this.dialogStatus == "update") {
                //     result = prod.edit(postData);
                // }

                // result
                //     .then(res => {
                //     this.disabledBtn = false;
                //     this.$notify({
                //         title: "提示",
                //         message: "保存成功",
                //         type: "success",
                //         duration: 2000
                //     });
                    
                //     })
                //     .catch(err => {
                //     this.disabledBtn = false;
                //     });
                }
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.wrapper {

}

</style>
