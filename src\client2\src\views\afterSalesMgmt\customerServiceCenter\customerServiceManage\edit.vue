<!--客服编辑-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog title="人员调整" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
      <template slot="body" v-loading='loading'>
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="160px"
        >
          <el-form-item label="请选择调整后的区域" prop="CustomerServiceDepartmentId">
            <treeselect
              :append-to-body="true"
              :noResultsText="noResultsTextOfSelTree"
              :noOptionsText="noOptionsTextOfSelTree"
              :normalizer="normalizer"
              :default-expand-level="3"
              :options="customerServiceDepartmentData"
              v-model="formModel.CustomerServiceDepartmentId"
              :multiple="false"
              :show-count="false"
              placeholder="请选择调整后的区域"
              zIndex='9999'
            ></treeselect>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as customerServiceManage from "@/api/afterSalesMgmt/customerServiceManage";
import { listToTreeSelect } from "@/utils";

export default {
  /**名称 */
  name: "customer-service-manage-edit",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    /**客服Id */
    customerServiceManageIds: {
      type: Array
    }
  },
  /**数据区 */
  data() {
    return {
      loading:false,
      /**树节点标准化 */
      normalizer(node) {
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        };
      },
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**区域列表 */
      customerServiceDepartmentData: [],
      /**表单模型 */
      formModel: { CustomerServiceDepartmentId: null },
      /**表单规则 */
      formRules: {
        CustomerServiceDepartmentId: {
          fieldName: "调整后的区域",
          rules: [{ required: true }]
        }
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {},
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        this.formModel = {
          CustomerServiceManageIds: this.customerServiceManageIds,
          CustomerServiceDepartmentId: null
        };
        this.loadData();
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    loadData() {
      this.loading=true;
      customerServiceManage
        .getCustomerServiceDepartmentListByCondition({})
        .then(response => {
          this.loading=false;
          let tempData = response.map(t => {
            t.label = t.Name;
            return t;
          });
          this.customerServiceDepartmentData = listToTreeSelect(tempData);
        }).catch(err => {
          this.loading=false;
        });
    },
    /**提交方法 */
    handleButtonClick() {
      let _this = this;
      _this.$refs.formRef.validate(valid => {
        if (valid) {
          let result = null;
          _this.buttonLoading = true;

          result = customerServiceManage.changeCustomerServiceDepartment(
            this.formModel
          );

          result
            .then(response => {
              _this.buttonLoading = false;
              _this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


