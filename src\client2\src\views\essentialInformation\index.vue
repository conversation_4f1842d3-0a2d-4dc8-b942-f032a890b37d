<template>
  <div class="essentialInformation" style="margin:50px;">
    <el-form ref="projectForm" :model="projectForm" label-position="right" label-width="100px" v-loading="requestFormLoading" :disabled="editable">
      <el-row>
        <el-col :span="12">
          <el-form-item label="项目名称" prop="ProjectName">
            <span>{{projectForm.ProjectName}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目编码" prop='ProjectCode'>
            <span>{{projectForm.ProjectCode}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属产品" prop="ProductManagementId">
            <span>{{ ConvertProductManagementName(projectForm.ProductManagementId) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始时间" prop='ProjectStartDateTime'>
            <span>{{projectForm.ProjectStartDateTime}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop='ProjectEndDateTime'>
            <span>{{projectForm.ProjectEndDateTime}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目类型" prop='ProjectType'>
            <span>{{  projectForm.ProjectType == null ?"" : projectTypeEnum.find(s=>s.value == projectForm.ProjectType).label }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目级别" prop='ProjectLevel'>
            <span>{{ projectForm.ProjectLevel == null ?"" : projectLevelEnum.find(s=>s.value == projectForm.ProjectLevel).label }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人力预算(人)" prop='ManpowerBudget'>
            <span>{{projectForm.ManpowerBudget}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费用预算(万)" prop='ExpenseBudget'>
            <span>{{projectForm.ExpenseBudget}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联项目" prop='RelatedProjectId'>
            <span>{{ ConvertRelatedProjectName(projectForm.RelatedProjectId) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目来源" prop='ProjectSource'>
            <span>{{projectForm.ProjectSource}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目经理" prop='ProjectManagerEmployee'>
            <span>{{ projectForm.ProjectManagerEmployee }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目背景" prop='ProjectContext' label-position="top">
            <span>{{projectForm.ProjectContext}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目目标" prop='ProjectObjective'>
            <span>{{projectForm.ProjectObjective}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop='Remark' label-position="top">
            <span>{{projectForm.Remark}}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>

import empSelector from "../common/empSelector";
import { projectTypeEnum, projectLevelEnum } from "../projectManagement/enums";
import * as productApi from "@/api/productManagement";
import elDragDialog from "@/directive/el-dragDialog";
import * as projectApi from "@/api/projectManagement";
import * as memberApi from "@/api/projectManagementMember";

export default {
  name: "projectApplicationProcess",
  components: {
    empSelector,
  },
  props: {
    ProjectId: {
      type: String,
      default: undefined
    }
  },
  watch: {
    ProjectId: {
      handler(val) {
        this.getList()
      },
      immediate: true
    },
  },

  computed: {
    //是否可编辑
    editable() {
      return true;
    },
  },
  data() {
    return {
      requestFormLoading: false,
      RelatedProjectList: [],
      ProductManagementList: [],
      projectTypeEnum: projectTypeEnum,
      projectLevelEnum: projectLevelEnum,
      projectForm: {},
    };
  },
  created() {
    this.getAllProductList();
    this.getAllProjectList();
    this.getList();

  },
  mounted() { },
  methods: {
    getList() {
      this.requestFormLoading = true;
      projectApi.detail({ projectId: this.ProjectId }).then(response => {
        let detail = response;
        this.projectForm = Object.assign({}, detail);
        this.projectForm.ProjectManagerEmployee = detail.ProjectManagerEmployeeDto.Name;
        this.requestFormLoading = false;
      });
    },
    getAllProjectList() {
      projectApi.getAllList({ projectId: this.ProjectId }).then(response => {
        this.RelatedProjectList = response;
      });
    },

    //获取所有产品列表
    getAllProductList() {
      productApi.getAllProductList().then(response => {
        this.ProductManagementList = response;
      });
    },


    ConvertRelatedProjectName(relatedProjectId) {
      if (relatedProjectId != null) {
        var project = this.RelatedProjectList.find(s => s.value == relatedProjectId);
        return project.label;
      }
      return null;
    },


    ConvertProductManagementName(productManagementId) {
      if (productManagementId != null) {
        var product = this.ProductManagementList.find(s => s.value == productManagementId);
        return product.label;
      }
      return null;
    },
  }
};
</script>

<style lang='scss' scoped>
</style>


