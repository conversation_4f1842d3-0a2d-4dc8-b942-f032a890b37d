<template>
  <div style="min-height: 500px;">
    <div class="app-container">
      <div class="bg-white">
        <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns"
          :isShowAllColumn="isShowAllColumn" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged"
          :isShowOpatColumn="rowBtns.length > 0" :startOfTable="startOfTable">
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items="tableSearchItems"
              @onSearch="handleFilter" @onReset="resetSearch">
              <template slot="OrganizationId">
                <treeselect :normalizer="normalizer" class="treeselect-common" ref="orgsTree" :options="orgsTree" :default-expand-level="3"
                  :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true"
                  v-model="listQuery.OrganizationId" placeholder="请选择客户单位" :noResultsText='noResultsTextOfSelTree'
                  :noOptionsText="noOptionsTextOfSelTree">
                </treeselect>
              </template>
              <template slot="ProjectPerson">
                <el-input style="width: 100%;" v-model="listQuery.ProjectPerson"></el-input>
              </template>
              <template slot='CompanyOwnerEmployee'>
                <emp-selector :multiple='false' :showType='2' key='service-users' :list='listQuery.CompanyOwnerEmployee'
                  @change='handleChangeUsers'>
                </emp-selector>
              </template>
              <template slot="CustomerProjectName">
                <el-input style="width: 100%;" v-model="listQuery.CustomerProjectName"></el-input>
              </template><template slot="other-btns">
                <el-button @click="onExport">导出</el-button>
              </template>
            </app-table-form>
          </template>

          <!-- 表格批量操作区域 -->
          <template slot="btnsArea">
            <permission-btn moduleName="customerproject" v-on:btn-event="onBtnClicked"></permission-btn>
          </template>

          <!-- 自定义列 -->
          <template slot='CompanyOwnerEmployee' slot-scope="scope">
            {{ scope.row.CompanyOwnerEmployee?scope.row.CompanyOwnerEmployee.Name:"" }}
          </template>
          <template slot='ShowProcurementForecast' slot-scope="scope">
            <el-button type="text" @click="showProcurementForecast(scope.row.CustomerProjectId)">采购预测
            </el-button>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1">
            </app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleUpdate(scope.row, 'detail')"
              :type="2"></app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3">
            </app-table-row-button>
          </template>
        </app-table>

        <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange" @size-change="handleSizeChange" />
      </div>

      <el-dialog v-el-drag-dialog class="dialog-mini" width="1000px" :title="textMap[dialogStatus]"
        v-if="dialogFormVisible" :visible.sync="dialogFormVisible" :close-on-click-modal="false" :append-to-body="true">
        <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="120px">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="'客户单位'" prop="OrganizationId">
                <treeselect :normalizer="normalizer" class="treeselect-common" ref="orgsDetailTree" :options="orgsTree" :default-expand-level="3"
                  :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true"
                  v-model="temp.OrganizationId" placeholder="请选择客户单位" :noResultsText='noResultsTextOfSelTree'
                  :noOptionsText="noOptionsTextOfSelTree" :disabled="dialogStatus != 'create'">
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'客户项目名称'" prop="CustomerProjectName">
                <el-input maxlength="50" v-model="temp.CustomerProjectName" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="'项目主要负责人'" prop="ProjectOwnerPerson">
                <el-input maxlength="50" v-model="temp.ProjectOwnerPerson" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'项目相关负责人'" prop="ProjectOtherPerson">
                <el-input maxlength="50" v-model="temp.ProjectOtherPerson" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="'电话号码'" prop="Telephone">
                <el-input maxlength="50" v-model="temp.Telephone" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'项目阶段'" prop="CustomerProjectPhaseTypeId">
                <el-select v-model="temp.CustomerProjectPhaseTypeId" placeholder="请选择项目阶段" style="width:100%;" clearable
                  :disabled="editable">
                  <el-option key="1" :value="1" label="启动"></el-option>
                  <el-option key="2" :value="2" label="规划"></el-option>
                  <el-option key="3" :value="3" label="执行"></el-option>
                  <el-option key="4" :value="4" label="监控"></el-option>
                  <el-option key="5" :value="5" label="收尾"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="'本公司关联项目'" prop="CompanyRelatedProject">
                <el-input maxlength="50" v-model="temp.CompanyRelatedProject" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'本公司负责人'" prop="CompanyOwnerEmployee">
                <emp-selector :multiple='false' :showType='2' key='service-users' :list='temp.CompanyOwnerEmployee'
                  @change='handleChangeDialogEmployee' :readonly="editable">
                </emp-selector>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="'项目起止时间'" prop="Range">
                <el-date-picker v-model="temp.Range" type="datetimerange" align="right" unlink-panels
                  range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%;" :disabled="editable">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="'本公司项目备注'" prop="CompanyProjectRemark">
                <el-input maxlength="100" v-model="temp.CompanyProjectRemark" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="'项目当前状况'" prop="ProjectStatus">
                <el-input maxlength="250" v-model="temp.ProjectStatus" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="'竞争对手状况'" prop="CompetitorStatus">
                <el-input maxlength="250" v-model="temp.CompetitorStatus" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="'评估结果'" prop="AssessmentResult">
                <el-input maxlength="250" v-model="temp.AssessmentResult" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
          <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认
          </el-button>
        </div>
      </el-dialog>

      <purchase-list :id="customerProjectId" @close='handlePurchaseDialogClosed' @saveSuccess='handPurchaseSaveSuccess'
        :dialogStatus="'采购记录'" :visible='dialogFormVisibleProcurementForecasts'></purchase-list>

      <!-- <el-dialog v-el-drag-dialog class="dialog-mini" width="1000px" title="采购预测"
        :visible.sync="dialogFormVisibleProcurementForecasts" :close-on-click-modal='false' :append-to-body='true'>
        <app-table ref="mainTable" :tab-columns="tabAuthColumnsProcurementForecasts"
          :tab-datas="tabDatasProcurementForecasts" :tab-auth-columns='tabAuthColumnsProcurementForecasts'
          :isShowAllColumn='true' :loading="listLoading" @rowSelectionChanged="rowSelectionChanged"
          :isShowOpatColumn='rowBtns.length > 0' :startOfTable='startOfTable'>
          <template slot='PurchaseItem' slot-scope="scope">
            {{ scope.row.PurchaseItem }}
          </template>
          <template slot='Quantity' slot-scope="scope">
            {{ scope.row.Quantity }}
          </template>
          <template slot='EstimatedPurchasePrice' slot-scope="scope">
            {{ scope.row.EstimatedPurchasePrice }}
          </template>
          <template slot='EstimatedPurchasePrice' slot-scope="scope">
            {{ scope.row.EstimatedPurchasePrice }}
          </template>
          <template slot='ProcurementInPreviousYears' slot-scope="scope">
            {{ scope.row.ProcurementInPreviousYears }}
          </template>
          <template slot='MainConcern' slot-scope="scope">
            {{ scope.row.MainConcern }}
          </template>
          <template slot='Remark' slot-scope="scope">
            {{ scope.row.Remark }}
          </template>
        </app-table>
        <pagination v-show="total>0" :total="totalProcurementForecasts" :page.sync="procurementForecastPage.PageIndex"
          :size.sync="procurementForecastPage.PageSize" @pagination="handleProcurementForecastCurrentChange"
          @size-change="handleProcurementForecastsSizeChange" />

        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisibleProcurementForecasts = false">取消</el-button>
          <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认
          </el-button>
        </div>
      </el-dialog> -->
    </div>
  </div>
</template>


<script>
  import { listToTreeSelect } from "@/utils";
  import { downloadFile } from "@/utils/index";
  import * as customerProject from "@/api/customerProject";
  // import * as orgs from '@/api/organization'
  import * as orgs from '@/api/oilfield'
  import EmpSelector from '../common/empSelector';
  import PurchaseList from './purchaseList'
  import * as customerOrganizationDetail from "@/api/customerOrganizationDetail";
  import * as procurementForecasts from "@/api/procurementForecasts";
  import { regs } from '@/utils/regs';

  import elDragDialog from "@/directive/el-dragDialog";
  import indexPageMixin from "@/mixins/indexPage";
  export default {
    name: "customerproject",
    components: {
      EmpSelector,
      PurchaseList,
    },
    directives: {
      elDragDialog
    },
    mixins: [indexPageMixin],
    data() {
      return {

        normalizer(node) {
          // treeselect定义字段
          return {
            label: node.label,
            id: node.Id,
            children: node.children
          }
        },
        orgsTree: [],
        organizations: [],
        multipleSelection: [],
        /**查询内容 */
        tableSearchItems: [
          { prop: "OrganizationId", label: "客户单位" },
          { prop: "ProjectPerson", label: "项目负责人" },
          { prop: "CompanyOwnerEmployee", label: "本公司负责人" },
          { prop: "CustomerProjectName", label: "客户项目名称" },
        ],
        /**列表栏目 */
        tabColumns: [
          {
            attr: { prop: "OrganizationName", label: "客户单位" }
          },
          {
            attr: { prop: "CustomerProjectName", label: "客户项目名称" }
          },
          {
            attr: { prop: "ProjectStartDate", label: "项目开始时间", formatter: this.formatterDate }
          },
          {
            attr: { prop: "ProjectEndDate", label: "项目结束时间", formatter: this.formatterDate }
          },
          {
            attr: { prop: "ProjectOwnerPerson", label: "项目主要负责人" }
          },
          {
            attr: { prop: "ProjectOtherPerson", label: "项目相关负责人" }
          },
          {
            attr: { prop: "Telephone", label: "电话号码" }
          },
          {
            attr: { prop: "CustomerProjectPhaseTypeName", label: "项目阶段" }
          },
          {
            attr: { prop: "CompanyRelatedProject", label: "本公司关联项目" }
          },
          {
            attr: { prop: "CompanyOwnerEmployee", label: "本公司负责人" },
            slot: true
          },
          {
            attr: { prop: "ProjectStatus", label: "项目当前状况" }
          },
          {
            attr: { prop: "CompanyProjectRemark", label: "本公司项目备注" }
          },
          {
            attr: { prop: "CompetitorStatus", label: "竞争对手状况" }
          },

          {
            attr: { prop: "AssessmentResult", label: "评估结果" }
          },
          {
            attr: { prop: "ShowProcurementForecast", label: "采购预测" },
            slot: true
          },
        ],
        tabAuthColumns: [], //已授权的列
        tabDatas: [],
        listLoading: false,
        postLoading: false,
        // 采购预测数据总数
        // totalProcurementForecasts: 0,
        // procurementForecastPage: {
        //   PageIndex: 1,
        //   PageSize: 20,
        //   CustomerProjectId: '',
        // },
        customerProjectId: "",
        // /**列表查询参数 */
        // listQuery: {
        //   CustomerProjectName: ""
        // },
        total: 0,
        /**弹出窗标题 */
        textMap: {
          update: "编辑",
          create: "添加"
        },
        dialogFormVisible: false,
        dialogFormVisibleProcurementForecasts: false,
        /**校验规则 */
        rules: {
          CustomerProjectName: {
            fieldName: "客户项目名称",
            rules: [{ required: true }, { max: 50 }]
          },
          OrganizationId: {
            fieldName: "客户单位",
            rules: [{ required: true }]
          },
          Telephone: {
            fieldName: "电话号码",
            rules: [{ reg: regs.phoneAndTel }]
          }

        },
        /**弹出窗临时对象 */
        temp: {
          CustomerProjectId: "",
          CustomerProjectName: "", //客户项目名称
          Range: [], //起始时间
        }
      };
    },
    created() {
      this.rules = this.initRules(this.rules);
      this.getList();
    },
    mounted() {
      this.getOrgTree();
      this.getOrganizations();
    },
    methods: {
      getOrgTree() {
        var _this = this // 记录vuecomponent
        orgs.getList({ Status: true }).then(response => {
          // let datas = response.map(function (item, index, input) {
          //   let obj = {
          //     Id: item.OilfieldCompanyId,
          //     label: item.Name,
          //     ParentId: item.ParentId,
          //   }
          //   return obj
          // })
          // _this.orgsTree = listToTreeSelect(datas)
          // this.listLoading = false

          _this.orgs = response.map(function (item, index, input) {
            let obj = {
              Id: item.OilfieldCompanyId,
              label: item.Name,
              ParentId: item.ParentId,
            }
            return obj
          })
          var orgstmp = JSON.parse(JSON.stringify(_this.orgs))
          _this.orgsTree = listToTreeSelect(orgstmp)
        })
      },
      getOrganizations() {
        var _this = this; // 记录vuecomponent
        //只取已添加详情的
        customerOrganizationDetail.getCustomerOrganizationDetailList().then(response => {
          _this.organizations = response;
        })
      },
      /**重置 */
      resetTemp() {
        this.temp = {
          CustomerProjectId: "",
          CustomerProjectName: "", //客户项目名称
          Range: [], //起始时间
        };
      },
      /**选中行 */
      rowSelectionChanged(rows) {
        this.multipleSelection = rows;
      },
      /**按钮组 */
      onBtnClicked: function (domId) {
        // console.log('you click:' + domId)
        switch (domId) {
          case "btnAdd":
            this.handleCreate();
            break;
          case "btnEdit":
            if (this.multipleSelection.length !== 1) {
              this.$message({
                message: "只能选中一个进行编辑",
                type: "error"
              });
              return;
            }
            this.handleUpdate(this.multipleSelection[0]);
            break;
          case "btnDel":
            if (this.multipleSelection.length < 1) {
              this.$message({
                message: "至少删除一个",
                type: "error"
              });
              return;
            }
            this.handleDelete(this.multipleSelection);
            break;
          case "btnDetail":
            if (this.multipleSelection.length !== 1) {
              this.$message({
                message: "只能选中一个进行查看",
                type: "error"
              });
              return;
            }
            this.handleUpdate(this.multipleSelection[0], "detail");
            break;
          default:
            break;
        }
      },
      /**获取列表 */
      getList() {
        this.listLoading = true;
        customerProject
          .getCustomerProjectListPage(this.listQuery)
          .then(response => {
            this.tabDatas = response.Items;
            this.total = response.Total;

            this.listLoading = false;
          });
      },
      /**查询 */
      handleFilter() {
        this.listQuery.PageIndex = 1;
        this.getList();
      },
      /**切换页大小 */
      handleSizeChange(val) {
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      /**切换页码 */
      handleCurrentChange(val) {
        this.listQuery.PageIndex = val.page;
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      /**弹出添加框 */
      handleCreate() {
        this.resetTemp();
        this.dialogStatus = "create";
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
      },
      /**弹出编辑框 */
      handleUpdate(row, optType = "update") {
        // row.Range = [];
        // this.temp = Object.assign({}, row); // copy obj
        // this.temp.CompanyOwnerEmployee = this.temp.CompanyOwnerEmployee ? [this.temp.CompanyOwnerEmployee] : [];

        customerProject.getCustomerProjectDetail({ id: row.CustomerProjectId }).then(response => {
          response.Range = [];
          if (response.ProjectStartDate && response.ProjectEndDate) {
            this.$set(response, "Range", [
              response.ProjectStartDate,
              response.ProjectEndDate
            ]);
          }
          response.CompanyOwnerEmployee = response.CompanyOwnerEmployee ? [response.CompanyOwnerEmployee] : [];

          this.temp = response;

          this.dialogStatus = optType;
          this.dialogFormVisible = true;
          this.$nextTick(() => {
            this.$refs["dataForm"].clearValidate();
          });

        });

      },
      handlePurchaseDialogClosed() {
        this.dialogFormVisibleProcurementForecasts = false
      },
      handPurchaseSaveSuccess() {
        // this.dialogFormVisibleProcurementForecasts = false
      },
      /**保存提交 */
      createData() {
        let self = this;
        self.postLoading = true
        self.temp.CustomerProjectName = self.temp.CustomerProjectName.trim();
        this.$refs["dataForm"].validate(valid => {
          if (!valid) {
            self.postLoading = false
          }
          if (valid) {
            if (this.temp.Range && this.temp.Range.length > 0) {
              this.temp.ProjectStartDate = this.temp.Range[0];
              this.temp.ProjectEndDate = this.temp.Range[1];
            }

            if (self.temp.CompanyOwnerEmployee && self.temp.CompanyOwnerEmployee.length > 0) {
              self.temp.CompanyOwnerEmployeeId = self.temp.CompanyOwnerEmployee[0].EmployeeId;
            }
            let formData = JSON.parse(JSON.stringify(self.temp));
            let res = null;
            if (self.dialogStatus == "create") {
              delete formData.CustomerProjectId;
              res = customerProject.addCustomerProject(formData);
            } else if (self.dialogStatus == "update") {
              res = customerProject.editCustomerProject(formData);
            }
            if (res) {
              res.then(response => {
                self.postLoading = false
                self.dialogFormVisible = false;
                self.$notify({
                  title: "成功",
                  message: "保存成功",
                  type: "success",
                  duration: 2000
                });
                this.getList();
              }).catch(err => {
                self.postLoading = false
              });
            }
          }
        });
      },
      /**多行删除 */
      handleDelete(rows) {
        let ids = [];
        if (_.isArray(rows)) {
          ids = rows.map(u => u.CustomerProjectId);
        } else {
          ids.push(rows.CustomerProjectId);
        }

        this.$confirm("是否确认删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          customerProject.deleteCustomerProject(ids).then(() => {
            this.$notify({
              title: "成功",
              message: "删除成功",
              type: "success",
              duration: 2000
            });
            this.getList();
          });
        });
      },
      handleChangeUsers(users) {
        if (users && users.length > 0) {
          this.listQuery.CompanyOwnerEmployee = [users[0]];
          this.listQuery.CompanyOwnerEmployeeId = users[0].EmployeeId;
        } else {
          this.listQuery.CompanyOwnerEmployee = [];
          this.listQuery.CompanyOwnerEmployeeId = "";
        }
      },
      handleChangeDialogEmployee(users) {
        if (users && users.length > 0) {
          this.temp.CompanyOwnerEmployee = [users[0]]
        } else {
          this.temp.CompanyOwnerEmployee = []
        }
      },
      onExport() {
        customerProject.exportCustomerProject(this.listQuery).then(res => {
          downloadFile(res.Url);
          console.log(res.Url);
        });
      },
      showProcurementForecast(CustomerProjectId) {
        this.customerProjectId = CustomerProjectId;
        this.dialogFormVisibleProcurementForecasts = true;
      },
      formatterDate(row, column) {
        let f = this.$options.filters["dateFilter"];
        return f(row[column.property], "YYYY-MM-DD HH:mm:ss");
      },
    }
  };
</script>


<style scoped>
  .sel-ipt,
  .dat-ipt {
    width: 100%;
  }
</style>