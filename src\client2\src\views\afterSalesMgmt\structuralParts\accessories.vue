<template>
    <div class="accessories">
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
            v-loading='loading'
        >
            <template slot="body">
                <div class="temBody">
                    <el-form
                        :rules="rules"
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="110px">
                        <div>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="结构配件名称" prop="name">
                                        <el-input v-model.trim="formData.name" placeholder="" maxlength="50"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <!-- <el-row>
                                <el-col :span="24">
                                    <el-form-item label="是否关键配件" prop="isPivotal">
                                        <el-radio v-model="formData.isPivotal" :label="true">是</el-radio>
                                        <el-radio v-model="formData.isPivotal" :label="false">否</el-radio>
                                    </el-form-item>
                                </el-col>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="风险类别:" prop="structPartRisk">
                                        <el-checkbox-group v-model="formData.structPartRisk">
                                            <el-checkbox v-for="(rc,index) in riskCategories" :label="rc.label" :key="index"></el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </el-col>
                            </el-row> -->
                        </div>
                    </el-form>
                </div>
            </template>
            <template slot="footer">
                <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <el-button @click="handleClose" size="mini">取消</el-button>
                <app-button @click="handleSuccess" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog> 
    </div>
</template>
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
import { vars } from "./vars";
export default{
    name:'accessories',
    // mixins: [indexPageMixin],
    components: {
        
    },
    props:{
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: 'create'
        },
        id: {
            type: String,
            default: ''
        },
    },
    data(){
        return{
            disabledBtn:false,
            goOn:false,
            riskCategories:vars.riskCategories,
            loading:false,
            formData:{
                name: '',
                isPivotal: null,
                structPartRisk:[],
            },
            rules: {
                name:[{ required: true, message: '结构配件名称不能为空'}],
                isPivotal: [{ required: true, message: '请选择是否关键配件', trigger: 'change'}],
            },
        }
    },
    filters: {
        
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val){
                this.formData={
                    name: '',
                    isPivotal: null,
                    structPartRisk:[],
                }
                this.goOn=false;
                if(this.dialogStatus == "edit"){
                    this.getDetail();
                }
            }
      
        },
    },
    computed:{
        title(){
            if(this.dialogStatus == 'create'){
                return "创建结构配件"
            }else{
                return "编辑结构配件"
            }
        }
    },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        getDetail(){
            accessories.detailStructural({id:this.id}).then(res => {
                this.formData={
                    name: res.Name,
                    isPivotal: res.IsPivotal,
                    structPartRisk:res.StructPartRisk == null ? [] : this.theFilterStruct(res.StructPartRisk),
                }
            }).catch(err => {

            })
        },
        theFilterStruct(a){
            let arr=null;
            if(a && a.length>0){
                arr=[];
                this.riskCategories.forEach(v => {
                    if(a.indexOf(v.value)>=0){
                        arr.push(v.label);
                    }
                })
            }
            return arr;
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        handleSuccess(){
            this.disabledBtn=true;
            let listResult = this.$refs.formData.validate();
            Promise.all([listResult]).then(valid => {
                let postData={
                    name: this.formData.name,
                    isPivotal: this.formData.isPivotal,
                    structPartRisk:this.filterStruct(this.formData.structPartRisk),
                }
                console.log(postData)
                if(this.dialogStatus == "create"){
                    accessories.addStructural(postData).then(res => {
                        this.disabledBtn=false;
                        this.$notify({
                          title: '成功',
                          message: '创建成功！',
                          type: 'success'
                        });
                        if(this.goOn){
                            this.formData={
                                name: "",
                                isPivotal: "",
                                structPartRisk:[],
                            }
                            this.$refs['formData'].resetFields();
                        }
                        this.$emit('saveSuccess',this.goOn);
                    }).catch(err => {
                        this.disabledBtn=false;
                    })
                }else{
                    postData.id=this.id;
                    accessories.editStructural(postData).then(res => {
                        this.disabledBtn=false;
                        this.$notify({
                          title: '成功',
                          message: '编辑成功！',
                          type: 'success'
                        });
                        this.$emit('saveSuccess',false);
                    }).catch(err => {
                        this.disabledBtn=false;
                    })
                }
            }).catch(err => {
                this.disabledBtn=false;
            })
        },
        filterStruct(a){
            let arr=null;
            if(a && a.length>0){
                arr=[];
                this.riskCategories.forEach(v => {
                    if(a.indexOf(v.label) >= 0){
                        arr.push(v.value);
                    }
                })
            }
            return arr;
        }
    }

}
</script>
<style lang="scss" scoped>

</style>