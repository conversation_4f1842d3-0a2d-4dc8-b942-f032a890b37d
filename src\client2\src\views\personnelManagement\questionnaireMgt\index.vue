<template>
    <!-- 问卷调查管理 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="true"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="pageType==1?150:80"
            :loading="listLoading"
            :tab-columns="pageType==2?tabColumns:(tabColumns.filter(s=>s.attr.prop != 'SubmitState'))"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :startOfTable="startOfTable"
            @sortChagned="handleSortChange">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'120px'" :items="pageType==1?tableSearchItems:(tableSearchItems.filter(s=>s.prop == 'Keywords'))" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索调查问卷名称" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>
                        <!-- 类型 -->
                        <template slot="SurveyType">
                            <el-select v-model="listQuery.SurveyType" placeholder="请选择">
                                <el-option v-for="item in SurveyTypeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <!-- 状态 -->
                        <template slot="SurveyState">
                            <el-select v-model="listQuery.SurveyState" placeholder="请选择">
                                <el-option v-for="item in SurveyStateEnum.filter(s=>s.value!=0)" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                        <!-- 截止时间 -->
                        <template slot="DeadlineTime">
                            <el-date-picker style="width: 180px;" v-model="listQuery.DeadlineTime" type="date" align="right" format="yyyy-MM-dd" placeholder="请选择"
                            value-format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                        </template>
                        <!-- 创建时间 -->
                        <template slot="CreateTime">
                            <el-date-picker style="width: 180px;" v-model="listQuery.CreateTime" type="date" align="right" format="yyyy-MM-dd" placeholder="请选择"
                            value-format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                        </template>
                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                        </template>
                    </app-table-form>
                </template>
                <template slot="DeadlineTime" slot-scope="scope">{{ scope.row.DeadlineTime | dateFilter('YYYY-MM-DD') }}</template>
                <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>

                <template slot="SurveyState" slot-scope="scope">
                    <span class="item-status" v-if="scope.row.SurveyState"
                        :style="{backgroundColor: getSurveyStateObj(scope.row.SurveyState).bgColor,color: getSurveyStateObj(scope.row.SurveyState).color}"
                    >{{ getSurveyStateObj(scope.row.SurveyState).label }}</span>
                    <template v-else>无</template>
                </template>

                <template slot="SubmitState" slot-scope="scope">
                    <span class="item-status" v-if="scope.row.SubmitState"
                        :style="{backgroundColor: getSubmitStateObj(scope.row.SubmitState).bgColor,color: getSubmitStateObj(scope.row.SubmitState).color}"
                    >{{ getSubmitStateObj(scope.row.SubmitState).label }}</span>
                    <template v-else>无</template>
                </template>
                
                <template slot="SurveyType" slot-scope="scope">{{ scope.row.SurveyType | SurveyTypeFilter}}</template>
                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <template v-if="pageType===1">
                        <!-- 详情 -->
                        <app-table-row-button @click="handleUpdate(scope.row,'detail')" text="详情" :type="2"></app-table-row-button>
                        <!-- 跟进 -->
                        <app-table-row-button v-if="isShowFollow(scope.row)" @click="handleFollow(scope.row)" text="跟进" :type="2"></app-table-row-button>
                        <!-- 编辑 -->
                        <app-table-row-button v-if="isShowUpdate(scope.row)" @click="handleUpdate(scope.row,'update')" text="编辑" :type="2"></app-table-row-button>
                        <!-- 删除 -->
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                    </template>
                    <template v-if="pageType===2">
                        <!-- 填写问卷 -->
                        <app-table-row-button v-if="scope.row.SurveyState!=2" @click="handleFillQuestionnaire(scope.row)" text="填写问卷" :type="2"></app-table-row-button>
                    </template>
                </template>
            </app-table>
        </div>
        <!----------------------------------------- 分页 ------------------------------------------->
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogFormVisible" :id="selectId" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogFormVisible"
    :surveyType="createSelectClassifyNode.SurveyType"
    @closeDialog="createDialogFormVisible=false" @saveSuccess="createSaveSuccess"></create-page>
    <!-- 选择分类 -->
    <select-classify v-if="createSelectClassifyVisible" :dialogFormVisible="createSelectClassifyVisible"
    @closeDialog="createSelectClassifyVisible=false" @saveSuccess="selectClassifySuccess"></select-classify>

    
    <!-- 跟进 -->
    <follow-page v-if="createFollowVisible" :id="selectId" :dialogFormVisible="createFollowVisible"
    @closeDialog="createFollowVisible=false" @reload="getList"></follow-page>
    <!-- 填写问卷 -->
    <fill-questionnaire v-if="createFillQuestionnaireVisible" :id="selectId" :dialogFormVisible="createFillQuestionnaireVisible"
    @closeDialog="createFillQuestionnaireVisible=false" @saveSuccess="fillQuestionnaireSuccess"></fill-questionnaire>


</div>
</template>
<script>
import { getUserInfo } from "@/utils/auth";
import * as SurveyApi from '@/api/personnelManagement/survey.js'
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import selectClassify from "./selectClassify";
import followPage from "./follow";
import fillQuestionnaire from "./fillQuestionnaire";


import { SurveyStateEnum, SurveyTypeEnum,SubmitStateEnum } from "../enum.js";

export default {
    name: 'questionnaire-mgt-index',
    mixins: [indexPageMixin],
    components: {
        createPage,
        selectClassify,
        followPage,
        fillQuestionnaire
    },
    props: {
        pageType: { // 1 人事行政管理下面的  问卷调查管理   2  公司门户 下面的 问卷调查
            type: Number,
            default: 1,
        },
    },
    filters: {
        SurveyTypeFilter(val) {
            let obj = SurveyTypeEnum.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "无";
        }
    },
    created() {
        this.getList();
    },
    data() {
        return {
            SurveyStateEnum,
            SurveyTypeEnum,
            SubmitStateEnum,

            selectId: '',
            createDialogStatus: 'create',
            createDialogFormVisible: false,

            createSelectClassifyVisible: false,
            createSelectClassifyNode: {},

            createFollowVisible: false,
            createFillQuestionnaireVisible: false,

            total: 0,
            listQuery: {},
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "CreateTime", label: "创建时间" },
                { prop: "DeadlineTime", label: "截止时间" },
                { prop: "SurveyState", label: "状态" },
                { prop: "SurveyType", label: "类型" },
            ],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "Name", label: "问卷名称", showOverflowTooltip: true}},
                { attr: { prop: "SurveyState", label: "状态", sortable: 'custom'}, slot: true },
                { attr: { prop: "SurveyType", label: "类型"}, slot: true },
                { attr: { prop: "SubmitState", label: "提交情况"}, slot: true },
                { attr: { prop: "DeadlineTime", label: "截止时间", sortable: 'custom'}, slot: true },
                { attr: { prop: "PrincipalEmployeeName", label: "负责人", showOverflowTooltip: true} },
                { attr: { prop: "CreateTime", label: "创建时间", sortable: 'custom'}, slot: true },
            ],
        }
    },
    methods: {
        // 填写问卷 确定返回
        fillQuestionnaireSuccess(){
            this.createFillQuestionnaireVisible = false;
            this.getList();
        },
        // 选择分类  确定返回
        selectClassifySuccess(val){
            this.createSelectClassifyNode = val;
            this.createSelectClassifyVisible = false
            setTimeout(()=>{
                this.handleAdd()
            },300)
        },
        /** 弹出新增编辑框 */
        handleAdd() {
            this.selectId = '';
            this.createDialogStatus = 'create'
            this.createDialogFormVisible = true
        },
        // 获取列表
        getList() {
            let self = this;
            self.listLoading = true;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);
            let result = null;
            if(self.pageType===1){
                result = SurveyApi.getList(postDatas)
            }
            if(self.pageType===2){
                result = SurveyApi.GetPersonListPage(postDatas)
            }
            result.then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        // 列表排序
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        // 重置搜索
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        // 表格文本框果过滤
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 表格顶部按钮点击事件
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.createSelectClassifyVisible = true;
                    break;
                default:
                    break;
            }
        },
        
        // 显示跟进弹窗
        handleFillQuestionnaire(row){
            this.selectId = row.Id;
            this.createFillQuestionnaireVisible = true
        },
        // 显示跟进弹窗
        handleFollow(row){
            this.selectId = row.Id;
            this.createFollowVisible = true
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.selectId = optType === 'create' ? '' : row.Id;
            this.createDialogStatus = optType
            this.createDialogFormVisible = true
        },
        /** 编辑框 点确定后 关闭 并刷新列表 */
        createSaveSuccess(d) {
            // console.log('d',d)
            if (!d) {
                this.createDialogFormVisible = false
            }
            this.listQuery.PageIndex = 1;
            this.getList()
        },
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                SurveyApi.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        getSurveyStateObj(val){
            return this.SurveyStateEnum.find(
                s => s.value == val
            ) || {};
        },
        getSubmitStateObj(val){
            return this.SubmitStateEnum.find(
                s => s.value == val
            ) || {};
        },
        // 是否显示 编辑按钮
        isShowUpdate(row){
            return row.PrincipalEmployeeIdList.some(s=>s === getUserInfo().employeeid)&&row.SurveyState!=2;
        },
        // 是否显示跟进按钮
        isShowFollow(row){
            return row.PrincipalEmployeeIdList.some(s=>s === getUserInfo().employeeid);
        },
    }
}
</script>
