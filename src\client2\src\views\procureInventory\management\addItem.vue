<template>
    <div>
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='800'
        >
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="80px">
                    <div class="wrapper" v-loading='loading'>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="物料编码" prop="MaterialCode">
                                    <el-autocomplete
                                        :disabled="!editable"
                                        class="inline-input"
                                        v-model.trim="formData.MaterialCode"
                                        :fetch-suggestions="querySearch"
                                        placeholder="请输入物料编码"
                                        :trigger-on-focus="false"
                                        @select="handleSelectMaterial"
                                        @change="changeMaterialCode"
                                        popper-class="my-fixed-width-popper"
                                    >
                                        <template slot-scope="{item}">
                                            <materialOption :opt="item"></materialOption>
                                        </template>
                                    </el-autocomplete>
                                </el-form-item>
                            </el-col>
                        </el-row>
            
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="物料名称">
                                    <span>{{formData.MaterialName}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label=" 规格型号">
                                    <span>{{formData.Specifications}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                            <el-col :span="12">
                              <el-form-item label="使用地区" prop="RegionalId">
                                <div class="_regional_detail_wrapper">
                                    <div class="btn_wrapper">
                                        <el-button :disabled="!editable" type="text" @click="handleRegionDialog">选择</el-button>
                                    </div>
                                    <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                    <div class="close_wrapper" v-show="formData.RegionalName && editable">
                                        <div class="i_wrapper">
                                            <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label=" 领料人" prop="MaterialEmployeeList">
                                       <normar-emp-selector 
                                            :readonly='(formData.RegionalId ? false : true) || !editable '
                                            listSelectorTitle='选择人员' 
                                            :listSelectorUrl='serviceArea.business + "/ImplementerManagement/GetListPage"' 
                                            :multiple='false' :showType='2'
                                            :list='formData.MaterialEmployeeList' 
                                            key='service-users' 
                                            :columns='empColumns'
                                            @change='handleChangeUsers'
                                            :condition='{regionalId: formData.RegionalId,ReverseCheckLevel:true}'
                                            :pageSize='100'
                                            :isAutocomplete='true'
                                        ></normar-emp-selector>
                                </el-form-item>
                            </el-col>
                        </el-row>


                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="单位" prop="MaterialUnitFNumber">
                                    {{ formData.MaterialUnitFNumber }}
                                   <!-- <el-autocomplete
                                        :disabled="!editable"
                                        class="inline-input"
                                        v-model="formData.MaterialUnitFNumber"
                                        :fetch-suggestions="querySearchUnit"
                                        placeholder="请输入单位"
                                        :trigger-on-focus="false"
                                        @select="handleSelectUnit"
                                        @change="changeMaterialUnitId"
                                        @blur="changeMaterialUnitBlur"
                                    ></el-autocomplete> -->
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="申请数量" prop="MaterialCount">
                                    <el-input-number :disabled="!editable" v-model="formData.MaterialCount" label="描述文字"></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                            <el-col :span="12"  v-if="this.dialogStatus != 'sparePartsInventory-create' && this.dialogStatus != 'create-item' && this.dialogStatus !='detail-item' ">
                                <el-form-item label="调出仓库" prop="MaterialOutStockName">
                                    <el-autocomplete
                                        :disabled="!editable"
                                        class="inline-input"
                                        v-model="formData.MaterialOutStockName"
                                        :fetch-suggestions="querySearchOut"
                                        placeholder="请输入调出仓库"
                                        :trigger-on-focus="false"
                                        @select="handleSelectOut"
                                        @change="changeTransferOutID"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item :label="(this.dialogStatus != 'sparePartsInventory-create' && this.dialogStatus != 'create-item' && this.dialogStatus !='detail-item')? '调入仓库': '仓库'" prop="MaterialInStockName">
                                  <el-autocomplete
                                        :disabled="!editable"
                                        class="inline-input"
                                        v-model="formData.MaterialInStockName"
                                        :fetch-suggestions="querySearchIn"
                                        :placeholder="(this.dialogStatus != 'sparePartsInventory-create' && this.dialogStatus != 'create-item' && this.dialogStatus !='detail-item')? ' 请输入调入仓库': ' 请输入仓库'"
                                        :trigger-on-focus="false"
                                        @select="handleSelectIn"
                                        @change="changeTransferInID"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                         <el-row>
                            <el-col :span="24">
                                <el-form-item label="备注">
                                    <el-input :disabled="!editable" type="textarea" :rows="6" maxlength="500"  v-model="formData.Remark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </div>
                </el-form>

            </template>

            <template slot="footer">
        
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn' v-if="editable"></app-button>
            </template>
        </app-dialog>


         <!-- 选择地区 -->
            <v-area-choose
            @closeDialog="closeRegionDialog"
            @electedRegionalData="electedRegionalData"
            :dialogFormVisible="dialogRegionFormVisible"
            :checkedList="formData.RegionalId ? [formData.RegionalId] : []"
            :disabledFn="disabledFn"
            :defaultExpandLevel='1'
            ></v-area-choose>

    </div>
</template>

<script>
import * as odc from "@/api/operatingDataCenter";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import normarEmpSelector from '../../afterSalesMgmt/common/normarEmpSelector'
import materialOption from './materialOption'
import { serviceArea } from "@/api/serviceArea"
import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";

  export default {
    name: "erp-create",
    components: {
        vAreaChoose,
        normarEmpSelector,
        materialOption,
    },
    computed: {
        title() {
            if(this.dialogStatus == 'create' || this.dialogStatus == 'sparePartsInventory-create') {
                return '添加物料'
            }else if(this.dialogStatus == 'update') {
                return '编辑物料'
            }else if(this.dialogStatus == 'create-item'){
               this.getDetail()
               return '调整配件库存'
            }else {
               this.getDetail()
               return '详情'
            }

        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail" &&  this.dialogStatus != "detail-item";
        },
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        ERPAccountId:{
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                }
            },
        },
    },
    filters: {
     
    },
    created() {
        if(this.dialogStatus === "sparePartsInventory-create" || this.dialogStatus === 'create-item'){
            delete this.rules.MaterialOutStockName
            this.rules.MaterialInStockName.fieldName = "仓库"
        }
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            InState:false, 
            OutState:false, 
            UnitState:false, 
            serviceArea,
            dialogRegionFormVisible: false,
            disabledBtn: false,
            rules: {
                MaterialCode: { fieldName: "物料编码", rules: [{ required: true, trigger: 'change' }] },
                RegionalId: { fieldName: "使用地区", rules: [{ required: true  }] },
                MaterialEmployeeList: { fieldName: "领料人", rules: [{ required: true }] },
                MaterialCount: { fieldName: "申请数量", rules: [{ required: true }] },
                MaterialUnitFNumber: { fieldName: "单位", rules: [{ required: true }] },
                MaterialOutStockName: { fieldName: "调出仓库", rules: [{ required: true, trigger: 'change' }] },
                MaterialInStockName: { fieldName: "调入仓库", rules: [{ required: true, trigger: 'change' }] },
            },
            loading: false,
            formData: {
                MaterialEmployeeIdList: [], 
                MaterialEmployeeList: [], 
                // AgentIds:[],
                // AgentIdsNames:null,
                RegionalId:null,
                RegionalName:"",
                Remark:"",
                MaterialCount:1,
                MaterialOutStockName:"",
                MaterialOutStock:"",
                MaterialInStockName:"",
                MaterialInStock:"",
                MaterialStock:"",
                MaterialStockName:"",
                MaterialUnitId:"",
                MaterialUnit:"",
                MaterialCode:"",
                ErpMaterialId:"",
                MaterialName: "",
                MaterialOutStockFNumber:"",
                MaterialInStockFNumber:"",
                MaterialUnitFNumber:"",


            },
             empColumns: [
                {
                attr: { prop: "Name", label: "姓名", width: '100' },
                },
                {
                attr: { prop: "Number", label: "工号", width: '100' },
                },
                {
                attr: { prop: "Gender", label: "性别", width: '60' },
                },
                {
                attr: { prop: "RegionalName", label: "负责地区" },
                },
                {
                attr: { prop: "Phone", label: "手机", width: '120' },
                },
                {
                attr: { prop: "OrgName", label: "部门" },
                }
            ],
        };
    },
    methods: {
        getMaterialDetails(val) {
            odc.getMaterialDetails({keyWords:val, ERPAccountId:this.ERPAccountId,}).then(res => {
                if(res) {
                    this.changeMaterialUnitId()
                    this.handleSelectUnit(res)
                }
            })
        },

        resetFormData() {
            this.formData = {
                MaterialEmployeeIdList: [], 
                MaterialEmployeeList: [], 
                // AgentIds:[],
                // AgentIdsNames:null,
                RegionalId:null,
                RegionalName:"",
                Remark:"",
                MaterialCount:1,
                MaterialOutStockName:"",
                MaterialOutStock:"",
                MaterialInStockName:"",
                MaterialInStock:"",
                MaterialStock:"",
                MaterialStockName:"",
                MaterialUnitId:"",
                MaterialUnit:"",
                MaterialCode:"",
                ErpMaterialId:"",
                MaterialName: "",
                MaterialOutStockFNumber:"",
                MaterialInStockFNumber:"",
                MaterialUnitFNumber:"",
                MaterialStockFNumber:"",
            }
        },

        disabledFn(data, nodeType) {
          //禁选一级节点
          if(data.level <= 1) {
              return true
          }
          return false
        },

        getDetail(){
              materialTransferApi.getPersonalDetails({'id':this.id})
             .then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.formData.MaterialInStockName = this.formData.MaterialStockName
                this.formData.MaterialInStock = this.formData.MaterialStock
                this.loading = false       
              }).catch(err => {
                this.loading = false         
              });
        },

         handleChangeUsers(users) {
            this.$refs.formData.clearValidate('MaterialEmployeeList');
            this.formData.MaterialEmployeeList = users;
            this.formData.MaterialEmployeeIdList=[];

            users.forEach(v => {
                this.formData.MaterialEmployeeIdList.push(v.EmployeeId);
            })
        },

        unique(arr) {
            return Array.from(new Set(arr))
        },

        //地区选择
        closeRegionDialog() {
          this.dialogRegionFormVisible = false;
        },
        handleRegionDialog(){
            this.dialogRegionFormVisible=true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('RegionalId');
            if(data){
                this.formData.RegionalId=data.Id;
                this.formData.RegionalName=data.ParentName;
            }else{
                this.formData.RegionalId='';
                this.formData.RegionalName='';
            }
            this.formData.MaterialEmployeeList=[];
            this.formData.MaterialEmployeeIdList=[];
        },

        handleSelectMaterial(item){
            this.formData.MaterialCode = item.FNumber
            this.formData.MaterialName = item.FName
            this.formData.Specifications = item.FSpecification
            this.formData.ErpMaterialId = item.FMATERIALID

            this.getMaterialDetails(this.formData.MaterialCode)
        },

        changeMaterialCode(val){
            this.formData.MaterialCode = ''
            this.formData.MaterialName = ''
            this.formData.Specifications = ''
            this.formData.ErpMaterialId = ''
            this.formData.MaterialUnitFNumber = ''
        },

     querySearch(queryString, cb) {

        odc.getMaterial({keyWords:queryString,ERPAccountId:this.ERPAccountId,}).then(res => {
               let result = res.map(v=>{
                 v.value = v.FNumber
                 return v
              });
              cb(result);
        }).catch(err => {
              
        });
       
      },

      querySearchUnit(queryString, cb){
        odc.getUnit({keyWords:queryString,ERPAccountId:this.ERPAccountId,}).then(res => {
            this.UnitState = res.length>0?false:true
               let result = res.map(v=>{
                 v.value = v.FNumber
                 return v
              });
              cb(result);
        }).catch(err => {
              
        });
      },

      handleSelectUnit(item){
           this.formData.MaterialUnitId = item.FUNITID
           this.formData.MaterialUnitFNumber = item.FNumber
           this.formData.MaterialUnit = item.FNumber
      },

      changeMaterialUnitId(val){
          if(!val){
            this.formData.MaterialUnitId = ''
            this.formData.MaterialUnitFNumber = ''
         }
      },

      changeMaterialUnitBlur(){
        if(this.UnitState){
            this.formData.MaterialUnitId = ''
            this.formData.MaterialUnitFNumber = ''
            this.formData.MaterialUnit = ''
        }
      },

     querySearchIn(queryString, cb){
        odc.getStock({keyWords:queryString,ERPAccountId:this.ERPAccountId,}).then(res => {
               let result = res.map(v=>{
                 v.value = v.FName
                 return v
              });
              cb(result);
        }).catch(err => {
              
        });
      },

      handleSelectIn(item){
          this.formData.MaterialStockName = item.FName
          this.formData.MaterialStock = item.FStockId
          this.formData.MaterialInStock = item.FStockId
          this.formData.MaterialInStockFNumber = item.FNumber
          this.formData.MaterialStockFNumber = item.FNumber
      },
      changeTransferInID(val){
          this.formData.MaterialStockName = ''
          this.formData.MaterialStock = ''
          this.formData.MaterialInStock = ''
          this.formData.MaterialInStockFNumber = ''
          this.formData.MaterialStockFNumber = ''
          this.formData.MaterialInStockName = ''
      },


    querySearchOut(queryString, cb){
        odc.getStock({keyWords:queryString,ERPAccountId:this.ERPAccountId,}).then(res => {
               let result = res.map(v=>{
                 v.value = v.FName
                 return v
              });
              cb(result);
        }).catch(err => {
              
        });
      },

      handleSelectOut(item){
          this.formData.MaterialOutStock = item.FStockId
          this.formData.MaterialOutStockFNumber = item.FNumber
      },
      changeTransferOutID(val){
        this.formData.MaterialOutStock = ''
        this.formData.MaterialOutStockFNumber = ''
        this.formData.MaterialOutStockName = ''
      },
     
     createData() {
         this.$refs.formData.validate(valid => {
          if (valid) {
                let result = JSON.parse(JSON.stringify(this.formData))
                if(this.dialogStatus == 'create-item'){
                     materialTransferApi.editPersonalMaterial(result)
                        .then(res => {
                            this.formData = Object.assign({}, this.formData, res);
                            this.loading = false  
                            this.$notify({
                                title: "提示",
                                message: "调整成功",
                                type: "success",
                                duration: 2000,
                            });
                            this.$refs.appDialogRef.createData();     
                        }).catch(err => {
                            this.loading = false         
                        });
                }else{
                  this.$refs.appDialogRef.createData(result)
                }
            } 
           });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },   
    }
};
</script>
