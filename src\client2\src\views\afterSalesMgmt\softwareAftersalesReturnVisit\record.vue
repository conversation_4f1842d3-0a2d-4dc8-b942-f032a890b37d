<template>
<div>
    <app-dialog title="回访记录" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750" :width='1200'>
        <template slot="body">
            <div class="wrapper">
                <div class="lft-content" v-loading='loadingHistor'>
                    <tags mode="list" class="tagsList" :items="histories" v-model="historyId">
                        <template v-for="(v, idx) in histories" :slot="v.value" slot-scope="scope">
                            <div class="item-warpper" :key="idx">
                                <div :title="v.label" class="item-title omit">
                                    {{ v.label | dateFilter('YYYY-MM-DD') }}
                                </div>
                            </div>
                        </template>
                    </tags>

                    <div class="pagintion-wrapper">
                        <pagination
                        :total="totalHistory"
                        small
                        background
                        :page.sync="listQueryHistory.PageIndex"
                        :size.sync="listQueryHistory.PageSize"
                        @pagination="handleCurrentChangeHistory"
                        layout="prev, pager, next"
                        :pager-count="5"
                        />
                    </div>
                </div>
                <div class="rht-content">
                    <el-form ref="formData" :model="formData" label-position="right" label-width="100px">
                        <div class="wrapper" v-loading='loading'>
                            <div class="lft">
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="站点信息" prop="RegionalBusinessRelationId">
                                            <div class="_regional_detail_wrapper">
                                                <div class="btn_wrapper">
                                                    <el-button :disabled="!editable" type="text">选择</el-button>
                                                </div>
                                                <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                                <div class="close_wrapper" v-show="formData.RegionalName && editable">
                                                    <div class="i_wrapper">
                                                    </div>
                                                </div>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="回访类型" prop='SalesAfterVisttType'>
                                            <el-radio-group v-model="formData.SalesAfterVisttType" disabled>
                                                <el-radio v-for="(item, idx) in salesAfterVisttTypes" :key="`ch_${idx}`" :label="item.value">{{ item.label }}</el-radio>
                                                <!-- <el-radio :label="1">正常回访</el-radio>
                                                <el-radio :label="2">故障处理</el-radio> -->
                                                
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="24">
                                        <el-form-item label="回访人" prop='EmployeeList'>
                                            <emp-selector :readonly="!editable" :showType="2" :multiple="true" :list="formData.EmployeeList"></emp-selector>
                                        </el-form-item>
                                    </el-col>
                                    
                                    <el-col :span="24">
                                        <el-form-item label="回访时间" prop='VistTime'>
                                            <el-date-picker style="width:100%;" :disabled="!editable" format='yyyy-MM-dd' value-format='yyyy-MM-dd' v-model="formData.VistTime" type="date"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="总耗时" prop='TotalTime'>
                                            <span v-for="(emp, idx) in formData.EmployeeList" :key="idx">
                                                {{ getTotalTime(emp.EmployeeId, formData.TotalTimeEmployeeList) | totalTimeFilter }}
                                                <template v-if="idx < formData.EmployeeList.length - 1">/</template>
                                            </span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="参数模板" prop="SalesAfterVistTemplateId">
                                            <div class="_regional_detail_wrapper">
                                                <div class="btn_wrapper">
                                                </div>
                                                <div class="regional_text" :title="formData.SalesAfterVistTemplateName">{{ formData.SalesAfterVistTemplateName }}</div>
                                                <div class="close_wrapper" v-show="formData.SalesAfterVistTemplateName && editable">
                                                    <div class="i_wrapper">
                                                    </div>
                                                </div>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row v-if="formData.SalesAfterVistTemplateId == defaultTemp.value">
                                    <el-col :span="24">
                                        <el-form-item label="回访内容" prop='Contents'>
                                            <el-input type="textarea" maxlength="1000" :rows="9" :disabled="!editable" v-model="formData.Contents"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="备注" prop='Remark'>
                                            <el-input type="textarea" maxlength="500" :rows="9" :disabled="!editable" v-model="formData.Remark"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <div v-loading='tempListLoading' v-if="formData.SalesAfterVistTemplateId && formData.SalesAfterVistTemplateId != defaultTemp.value" style="min-height: 80px;">
                                    <noData v-if="!tempListLoading && formData.SalesAfterVistTemplate.length == 0"></noData>
                                    <el-collapse class="cus-collapse" v-model='activeNames' v-else>
                                        <el-collapse-item :name="t.Id" v-for="(t, idx) in formData.SalesAfterVistTemplate" :key="idx">
                                            <template slot="title">
                                                {{ t.Name }}
                                            </template>
                                            <el-row>
                                                <el-col :span="24" v-for="tt in t.FieldList" :key="tt.FieldId">
                                                    <div class="col-wrapper">
                                                        <div class="col-title el-form-item__label omit" :title="tt.Name">
                                                            {{ tt.Name }}
                                                        </div>
                                                        <div class="col-content" :title="tt.FieldValue">
                                                            <el-input type="textarea" :rows="3" maxlength="500" :disabled="!editable" v-model="tt.FieldValue"></el-input>
                                                        </div>
                                                    </div>
                                                    
                                                </el-col>
                                            </el-row>
                                        </el-collapse-item>
                                    </el-collapse>
                                </div>
                            </div>
                            <div class="rht">
                                <!-- <div>
                                    <div class="panel-title">附件</div>
                                    <div>
                                        <app-uploader
                                        ref="appUploaderRef"
                                        :readonly="!editable"
                                        accept="all"
                                        :fileType="3"
                                        :max="10000"
                                        :value="formData.AttachmentList"
                                        :fileSize="1024 * 1024 * 500"
                                        :minFileSize="100 * 1024"
                                        ></app-uploader>
                                    </div>
                                </div> -->
                                <div class="row-title">
                                    <tags :items="optTypes" v-model="currentOptType">
                                        <template v-for="t in optTypes" :slot="t.value">
                                            <i :class="t.value == 1 ? 'el-icon-time' : 'el-icon-paperclip'" :key="t.value"></i> {{ t.label }}
                                        </template>
                                    </tags>
                                </div>
                                <div class="rht-content">
                                    <div v-show="currentOptType == 1">
                                        <noData v-if="groups.length == 0"></noData>
                                        <div v-for="(g, idx) in groups" :key="idx">
                                            <div style="padding: 4px 0;">
                                                <span @click="() => g.expand = !g.expand"><i :class="g.expand ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i></span>
                                                <span>{{ g.EmployeeName }}（{{ g.stayMinute | totalTimeFilter }}）</span>
                                            </div>
                                            <div style="margin-left: 20px;" v-for="(sub, idx2) in g.List" :key="idx2" v-show="g.expand">
                                                <div>【{{ sub.ClockType | clockTypeFilter }}】{{ sub.CreateTime | dateFilter('MM-DD HH:mm:ss') }}</div>
                                                <div>{{ sub.LocationAddress }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-show="currentOptType == 2">
                                        <app-uploader
                                        ref="appUploaderRef"
                                        :readonly="!editable"
                                        accept="all"
                                        :fileType="3"
                                        :max="10000"
                                        :value="formData.AttachmentList"
                                        :fileSize="1024 * 1024 * 500"
                                        :minFileSize="100 * 1024"
                                        ></app-uploader>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-form>
                </div>
            </div>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
        </template>
    </app-dialog>

</div>
</template>

<script>
import dayjs from "dayjs";
import noData from "@/views/common/components/noData";
import * as salesAfterVist from "@/api/afterSalesMgmt/salesAfterVist";
import empSelector from '../../common/empSelector'
import currMixins from './mixins'
import { vars } from './enum'

let defaultTemp = {value: '00000000-0000-0000-0000-000000000000', label: '默认模板'}

export default {
    name: "recored",
    directives: {},
    components: {
        noData,
        empSelector,

    },
    mixins: [currMixins],
    props: {
        regionalId: {
            type: String,
            default: ''
        },
        regionalBusinessRelationId: {
            type: String,
            default: ''
        },
        listQueryParams: {
            type: Object,
            default: null
        },
        currentId: {
            type: String,
            default: ''
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(val) {
                    if(this.listQueryParams) {
                        this.listQueryHistory = Object.assign({}, this.listQueryHistory, JSON.parse(JSON.stringify(this.listQueryParams)))
                    }
                    this.getHistory()
                }
            },
            immediate: true
        },

        historyId(val) {
            if(val) {
                this.getDetail()
            }
        },
    },
    filters: {
        clockTypeFilter(val) {
            if(val == 1) {
                return '进站'
            }else{
                return '出站'
            }
        }
    },
    computed: {
        optTypes() {
            let opts = [{ value: 2, label: '附件' }]
            // if(this.dialogStatus == 'create' || this.dialogStatus == 'update') {
            //     return opts
            // }else{
            // }
            let temp = { value: 1, label: '到访记录' }
            opts.splice(0, 0, temp)
            return opts
            
        }
    },
    created() {
    },
    data() {
        return {
            salesAfterVisttTypes: vars.salesAfterVisttTypes,
            defaultTemp: defaultTemp,
            listQueryHistory: {
                PageIndex: 1,
                PageSize: 20
            },
            loadingHistor: false,
            histories: [],
            totalHistory: 0,

            historyId: '',

            formData: {},
            loading: false,

            tempListLoading: false,
            activeNames: [],
            editable: false,

            groups: [],
            currentOptType: 1,
            
        }
    },

    methods: {
        getLogs(condition) {
            let postDatas = {
                PageIndex: 1,
                PageSize: 1000,
                SalesAfterVistId: this.historyId
            }
            if(condition) {
                postDatas = Object.assign({}, postDatas, condition)
            }
            // postDatas.SalesAfterVistTime = '2022-04-07'
            postDatas.SalesAfterVistTime = this.formData.VistTime?dayjs(this.formData.VistTime).format('YYYY-MM-DD') : "";

            salesAfterVist.getAfterVistClock(postDatas).then(res => {
                let result = []
                let list = res.PageData.Items || []
                let empList = res.TotalTimeEmployeeList || []
                let groups = _.groupBy(list, 'CreateEmployeeId');

                Object.keys(groups).forEach(key => {

                    let empId = key
                    let group = groups[empId]
                    
                    let g = {
                        EmployeeId: empId,
                        EmployeeName: group[0].CreateEmployee.Name,
                        List: group,
                        expand: true,
                        stayMinute: 0 //分钟
                    }

                    let empObj = empList.find(s => s.EmployeeId == empId)
                    if(empObj) {
                        g.stayMinute = empObj.TotalTime
                    }
                    
                    // if(group && group.length > 0) {
                    //     let sTimeList = group.filter(s => s.ClockType == 1 && s.ClockTime).sort(s => s.ClockTime).map(s => s.ClockTime) // 到站
                    //     let eTimeList = group.filter(s => s.ClockType == 2 && s.ClockTime).sort(s => s.ClockTime).map(s => s.ClockTime) // 离站
                    //     if(sTimeList.length > 0 && eTimeList.length > 0) {
                    //         let eTime = eTimeList[0]
                    //         let sTime = sTimeList[sTimeList.length - 1]
                    //         let min = dayjs(eTime).diff(dayjs(sTime),'minutes')
                    //         g.stayMinute = min
                    //     }
                    // }

                    result.push(g)
                }); 

                this.groups = result
            })
        },
        handleCurrentChangeHistory(val) {
            this.listQueryHistory.PageIndex = val.page;
            this.listQueryHistory.PageSize = val.size;
            this.getHistory();
        },
        getHistory() {
            // if(!this.regionalId) {
            //     return false
            // }

            let postData = JSON.parse(JSON.stringify(this.listQueryHistory))
            
            if(this.regionalId) {
                postData.RegionalId = this.regionalId
            }

            if(this.regionalBusinessRelationId) {
                postData.RegionalBusinessRelationId = this.regionalBusinessRelationId
            }

            // postData = this.assignSortObj(postData);
            this.loadingHistor = true
            salesAfterVist.getList(postData).then(res => {
                this.loadingHistor = false
                this.totalHistory = res.Total
                this.histories = res.Items.map(s => {
                    return {
                        label: s.VistTime,
                        value: s.Id,
                        obj: s
                    }
                })

                if(this.histories && this.histories.length > 0) {
                    if(this.histories.find(s => s.value == this.currentId)) {
                        this.historyId = this.currentId
                    }else{
                        this.historyId = this.histories[0].value
                    }
                }
                
            }).catch(err => {
                this.loadingHistor = false
            })

        },
        getDetail() {
            this.loading = true
            salesAfterVist.detail({
                id: this.historyId
            }).then(res => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, res);
                if(this.formData.SalesAfterVistTemplateId == defaultTemp.value) {
                    this.formData.SalesAfterVistTemplateName = defaultTemp.label
                }

                if(this.formData.SalesAfterVistTemplate && this.formData.SalesAfterVistTemplate.length > 0) {
                    this.activeNames = this.formData.SalesAfterVistTemplate.map(s => s.Id)
                }

                this.getLogs({
                    SalesAfterVistEmployeeIdList: this.formData.EmployeeList ? this.formData.EmployeeList.map(s => s.EmployeeId) || [] : [],
                    RegionalId: res.RegionalId||this.regionalId,
                    SalesAfterVistTime: this.formData.VistTime
                })

            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapper{
    height: 675px;
    display: flex;
    .lft-content, .rht-content{
        overflow-y: auto;
    }
    .lft-content{
        width: 280px;
        box-sizing: border-box;
        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        .tagsList{
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
            padding-right: 10px;
        }
        .pagintion-wrapper{
            ::v-deep .pagination-container{
                padding-left: 0!important;
            }
        }
    }
    .rht-content{
        flex: 1;
        ::v-deep .el-form{
            padding-top: 0;
        }
        .wrapper{
            display: flex;
            padding: 10px;
            .lft{
                flex: 1;
            }
            .rht{
                padding-left: 20px;
                width: 300px;
                display: flex;
                flex-direction: column;
                .row-title {
                    border-bottom: 1px solid #e3e3e3;
                }
                .rht-content{
                    flex: 1;
                    overflow-y: auto;
                    >div{
                        height: calc(100% - 10px);
                    }
                }
            }
        }

        .col-wrapper{
            display: flex;
            align-items: center;
            padding: 5px 0;
            .col-title{
                width: 100px;
                text-align: right;
                padding-right: 12px;
            }
            .col-content{
                flex: 1;
            }
        }


        .panel-title{
            font-size: 16px;
            font-weight: 700;
            padding-bottom: 4px;
            padding-left: 6px;
            border-bottom: 1px solid #DCDFE6;
            margin-bottom: 10px;
        }
    }
}
</style>