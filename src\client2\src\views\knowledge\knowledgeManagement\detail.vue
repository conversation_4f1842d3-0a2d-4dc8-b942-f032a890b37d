<template>
<div class="wrapper">
    <div class="app-container pageBody" v-loading="Loading">
        <page-title :title="pageTitle" :showBackBtn='true' @goBack="handleGoBack" text-bold>
            <!-- <div slot="def">震惊！！！原来燃烧器还可以这么用！！不收藏不是人！</div> -->
        </page-title>
        <div class="pageBody_head flexWarp" v-if="formData">
            <div class="flexColumns">
                <div class="flexWarp">
                    <!-- <img :src="defavatar" style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 50px; height: 50px;margin-right:10px;" /> -->
                    <div class="flexColumns">
                        <div>{{formData.PublisherList&&formData.PublisherList.map(s => s.Name).join('、')}}</div>
                        <div style="color: #aaaaaa;">{{formData.CreateTime | dateFilter('YYYY-MM-DD')}}</div>
                    </div>
                </div>
            </div>
            <div class="describeBox flexWarp">
                <div class="describeBox_item">
                    {{commentList.length}} 评论 <i class="el-icon-chat-line-round"></i>
                </div>
                <div class="describeBox_item">
                    {{formData.Pageviews}} 浏览 <i class="el-icon-view"></i>
                </div>
                <div class="describeBox_item" :class="{'isFavorite': formData.IsFavorite}" v-if="pageType==1">
                    {{formData.FavoritesNumber}} 收藏 <i class="el-icon-star-on"></i>
                </div>
                <div v-else class="describeBox_item pointer" :class="{'isFavorite': formData.IsFavorite}" @click="favoritesChange(formData)">
                    {{formData.FavoritesNumber}} {{formData.IsFavorite?'已收藏':'收藏'}} <i class="el-icon-star-on"></i>
                </div>
            </div>
        </div>
        <div class="pageBody_main" v-if="formData">
            <div class="pageBody_main_content">
                <div class="UeditorHtml ql-editor" v-html="formData.Content" v-viewer></div>
            </div>
            <div class="pageBody_main_comment">
                <div style="width: 600px;">
                    <div class="title" style="padding-bottom: 0; height: 50px; line-height: 50px;">
                        附件（{{formData.AttachmentList.length}}）
                    </div>
                    <app-uploader
                        :readonly="true"
                        accept='all'
                        :fileType='3'
                        :max='10000'
                        :value='formData.AttachmentList'
                        :fileSize='1024 * 1024 * 500'
                        :minFileSize='100 * 1024'
                    ></app-uploader>
                </div>
                
                <div class="title">
                    评论（{{commentList.length}}）
                    <el-button type="primary" @click="handleAddComment">我要评论</el-button>
                </div>
                <div class="pageBody_main_comment_item flexWarp" v-for="commentItem in commentList" :key="commentItem.Id">
                    <div>
                        <img :src="commentItem.AvatarPath||defavatar" style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 60px; height: 60px;margin-right:10px;" />
                    </div>
                    <div class="flexColumns" style="padding: 5px 0;">
                        <div style="font-weight: 700;">{{commentItem.CreateEmployeeName}}</div>
                        <div>{{commentItem.CommentContent}}</div>
                        <div>{{commentItem.CreateTime | dateFilter('YYYY-MM-DD HH:mm')}} <el-button v-if="commentItem.Revocation" type="text" @click="handleDelComment(commentItem)">撤回</el-button></div>
                    </div>
                </div>

                
            </div>
        </div>
    </div>

    <add-comment v-if="addCommentFormVisible" :parentId="Id"
    :dialogFormVisible="addCommentFormVisible"
    @closeDialog="addCommentFormVisible = false"
    @saveSuccess="addCommentSaveSuccess"></add-comment>
</div>
</template>
<script>
import * as KnowledgeApi from '@/api/knowledge/Knowledge'
import addComment from "./addComment";
export default {
    name: 'knowledge-management-detail',
    components: {
        addComment,
    },
    props: {
        pageType: { //  1 管理端  2  用户端
            type: Number,
            default: 1
        },
    },
    computed: {
        pageTitle() {
            if(this.formData&&this.formData.Id) {
                return this.formData.Title || '未知标题'
            }
            return '加载中...'
        },
        backUrl() {
            let url = decodeURIComponent(this.$route.query.backUrl||'')
            console.log(this.$route.query)
            return url
        },
    },
    data() {
        return {
            treeData: [],
            defavatar: require("../../../assets/images/avatar3.png"),
            addCommentFormVisible: false,
            Id: "",
            Loading: false,
            formData: null,
            commentList: [],
            delLoading: false,
            // formData: {
            //     ClassifyId: null, // 文章分类 id
            //     Content: "", // 文章内容
            //     Covers: "", // 文章封面 Id
            //     CoversPath: "" , // 文章封面 url
            //     CoversFileList: [], // 文章封面
            //     PublisherList: [],
            //     PublisherIds: [], // 发布人id 集合
            //     Publisher: "", // 发布人id
            //     Title: "",
            //     Pageviews: null, //收藏量
            //     FavoritesNumber: null, //收藏量
            // },
        }
    },
    created(){
        if(this.$route.params.id){
            this.Id = this.$route.params.id
            if(this.pageType == 1){
                this.getDetail();
            }
            if(this.pageType == 2){
                this.getPageview();
            }
        }
    },
    methods: {
        // 收藏
        favoritesChange(row){
            KnowledgeApi.favorites({Id:row.Id}).then(res => {
                this.getDetail();
                this.$notify({
                    title: "成功",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
            });
        },
        handleAddComment(){
            this.addCommentFormVisible = true;
        },
        addCommentSaveSuccess(){
            this.getComment();
            this.addCommentFormVisible = false;
        },
        handleGoBack() {
            this.$router.replace({
                path: this.backUrl || `/knowledge/knowledgeManagement`
            });
        },
        // 撤回评论
        handleDelComment(row){
            let self = this;
            if(self.delLoading) return false;
            self.$confirm("确定撤回吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                self.delLoading = true;
                KnowledgeApi.revocation({Id:row.Id}).then(res => {
                    self.delLoading = false;
                    self.$notify({
                        title: "成功",
                        message: "操作成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getComment();
                })
                .catch((err) => {
                    self.delLoading = false;
                });
            }).catch(()=>{});
        },
        getDetail() {
            let self = this;
            self.Loading = true;
            KnowledgeApi.detail({Id: self.Id}).then(res => {
                self.formData = Object.assign({}, self.formData, res);
                self.Loading = false;
                self.getComment();
            })
            .catch(err => {
                self.Loading = false;
            });
        },
        getPageview() {
            let self = this;
            self.Loading = true;
            KnowledgeApi.pageview({Id: self.Id}).then(res => {
                self.formData = Object.assign({}, self.formData, res);
                self.Loading = false;
                self.getComment();
            })
            .catch(err => {
                self.Loading = false;
            });
        },
        // 获取评论
        getComment(){
            KnowledgeApi.getComment({
                BusinessId: this.Id
            }).then(res => {
                this.commentList = res || []
            })
        },
    }
}
</script>
<style scoped>
.UeditorHtml >>> img{max-width: 100%;}
</style>
<style lang="scss" scoped>
.pointer{
    cursor: pointer;
}
.flexWarp{
    display: flex;
}
.flexColumns{
    flex: 1;
}
.pageBody{
    display: flex;
    flex-direction: column;
    height: calc(100% - 20px);
    margin-bottom: 0;
    &_head{
        width: 100%;
        height: 71px;
        padding: 15px 20px;
        line-height: 20px;
        border-bottom: 1px solid #EBEEF5;
        .describeBox{
            height: 40px;
            line-height: 40px;
            &_item{
                color: #aaaaaa;
                padding-left: 20px;
                &.isFavorite{
                    color: #F59A23;
                }
            }
        }
    }
    &_main{
        padding: 15px 20px;
        flex: 1;
        overflow-y: auto;
        &_content{
            min-height: 200px;
            padding-bottom: 15px;
            border-bottom: 1px solid #EBEEF5;
        }
        &_comment{
            color: $text-main-color;
            line-height: 20px;
            .title{
                font-size: 18px;
                font-weight: 700;
                height: 60px;
                line-height: 40px;
                padding: 10px 0;
            }
            &_item+&_item{
                margin-top: 10px;
            }
        }
    }
}
</style>