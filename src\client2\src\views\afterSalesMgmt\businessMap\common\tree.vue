<template>
    <div class="tree" v-loading="loading">
        <div class="elInput">
            <el-input
            placeholder="搜索地区"
            v-model="filterText">
            </el-input>
        </div>
        <div class="treeBox">
            <el-tree
            class="filter-tree"
            :data="treeData"
            :props="defaultProps"
            :show-checkbox='isCheckbox'
            :expand-on-click-node='false'
            :check-strictly="true"
            :default-expanded-keys="defaultKeys"
            node-key="Id"
            :filter-node-method="filterNode"
            :default-checked-keys="checkedList"
            @node-click="handleNodeClick"            
            @check-change="handleNodeCheck"
            @check="handleChecked"
            ref="tree">
                <span class="custom-tree-node" slot-scope="{data}">
                    <span :title="data.RegionalName" class="show-ellipsis">{{data.RegionalName}}</span>
                </span>
            </el-tree>
        </div>
    </div>
</template>
<script>
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import { listToTreeSelect } from '@/utils';
export default{
    name:'tree',
    props: {
        //是否显示 checkbox 复选框
        isCheckbox: {
            type: Boolean,
            default: false
        },
        //是否包含“全部地区”选项
        isAll: {
            type: Boolean,
            default: false
        },
        //只加载第一级数据（ParentId==null）
        isSubset: {
            type: Boolean,
            default: false
        },
        //显示级级（isSubset 属性的补充），默认0，表示该参数不起作用，1级传1，以此类推；
        level: {
            type: Number,
            default: 0
        },
        //默认展开层级，默认不展开
        defaultExpandLevel: {
            type: Number,
            default: 0
        },
        //是否多选
        multiple: {
            type: Boolean,
            default: false
        },
        condition: {
            type: Object,
            default: null
        },
        //默认选中
        checkedList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        //单选的时候默认选中（非复选框）
        defaultValue:{
            type: String,
            default: ''
        },
        //单选模式中，默认展开选中节点
        defaultExpandChecked: {
            type: Boolean,
            default: true
        },
        //禁选(默认所有的都不禁选)
        disabledFn: {
            type: Function,
            default: () => {
                return false
            }
        },
        queryRegionID:{
            type:String,
            default:''
        }
    },
    data(){
        return{
            defaultKeys:[],
            loading:false,
            filterText: '',
            treeData:[],
            defaultProps: {
              children: 'children',
              label: 'RegionalName',
              disabled: this.disabledFn,
            },
            checkedId:'',
            conditionObj: {}
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        condition: {
            handler(val) {
                if(val) {
                    this.conditionObj = Object.assign({}, this.conditionObj, this.condition)
                }
            },
            immediate: true
        },
    },
    created(){
        this.loading=true;
        this.getAreas();
    },
    mounted(){
        //如果是“单选模式”，默认展开已选中的节点
        if (this.defaultExpandChecked && !this.multiple) {
            this.defaultKeys = this.defaultKeys.concat(this.checkedList || [])
        }
    },
    methods:{
        // filterNode(value, data) {
        //     if (!value) return true;
        //     return data.RegionalName.indexOf(value) !== -1;
        // },
        // //过滤除匹配节点及其所有子节点
        filterNode(value,data,node) {
            if(!value){
                return true;
            }
            let level = node.level;
            let _array = [];//这里使用数组存储 只是为了存储值。
            this.getReturnNode(node,_array,value);
            let result = false;
            _array.forEach((item)=>{
                result = result || item;
            });
            return result;
        },
        getReturnNode(node,_array,value){
            let isPass = node.data &&  node.data.RegionalName && node.data.RegionalName.indexOf(value) !== -1;
            isPass ? _array.push(isPass) : '';
            if(!isPass && node.level!=1 && node.parent){
                this.getReturnNode(node.parent,_array,value);
            }
        },
        //改变选中节点向父组件传递参数
        handleNodeClick(data){
            if(!this.isCheckbox) {
                this.$emit('changeNode',data)
            }
        },
        //单选按钮
        handleNodeCheck(data, checked, node){
            if(!this.multiple) { //单选
                if(checked === true) {
                    this.checkedId = data.Id;
                    this.$refs.tree.setCheckedKeys([data.Id]);
                } else {
                    ////需要支持取消选中（如果需要关闭弹框前给与提示，请使用 areaChoose 组件的 beforeConfirm 属性 ）
                    // if (this.checkedId == data.Id) {
                    //     this.$refs.tree.setCheckedKeys([data.Id]);
                    // }
                }
                let datas = this.$refs.tree.getCheckedNodes()
                this.$emit('checkChangeNode', datas && datas.length > 0 ? datas[0] : null)
                
            }else{
                this.$nextTick(() => {
                    let datas = this.$refs.tree.getCheckedNodes()
                    this.$emit('checkChangeNodeList', datas)
                })
            }
        },
        handleChecked(currentObj, treeStatus) {
            if(this.multiple) {
                // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
                let selected = treeStatus.checkedKeys.indexOf(currentObj.Id) // -1未选中
                // 选中
                if (selected !== -1) {
                    // 子节点只要被选中父节点就被选中
                    // this.selectedParent(currentObj)
                    // 统一处理子节点为相同的勾选状态
                    this.uniteChildSame(currentObj, true)
                } else {
                    // 未选中 处理子节点全部未选中
                    if (currentObj.children && currentObj.children.length !== 0) {
                        this.uniteChildSame(currentObj, false)
                    }
                }
            }
        }, 
        // 统一处理子节点为相同的勾选状态
        uniteChildSame (treeList, isSelected) {
            if(!this.disabledFn(treeList)) { //如果当前节点为 disabled 状态，则不可以选中
                this.$refs.tree.setChecked(treeList.Id, isSelected)
            }
            if(treeList.children && treeList.children.length > 0){
                for (let i = 0; i < treeList.children.length; i++) {
                    this.uniteChildSame(treeList.children[i], isSelected)
                }
            }
        },
        getAreas() {
            let postData = {
                RegionalName:"",
                RegionalId:this.queryRegionID
            }

            postData = Object.assign({}, postData, this.conditionObj)

            if(this.isSubset) {
                postData.Level = 1
            }

            if(this.level > 0) {
                postData.Level = this.level
            }

            regionalManagement.getPoorListByCondition(postData).then(res => {
                this.loading=false;
                //如果要有全部地区的一级
                if(this.isAll){
                    res.unshift({
                        Id:-1,
                        ParentId:null,
                        ParentName:'全部地区',
                        RegionalName: "全部地区",
                        level:0
                    })
                }

                if(this.checkedList && this.checkedList.length > 0){
                    this.$refs.tree.setCheckedKeys(this.checkedList || []);

                    // let a=res.find(s => s.Id == this.checkedList[0]);

                    let a = res.filter(s => this.checkedList.findIndex(n => n == s.Id) > -1)
                    if(a && a.length > 0) {
                        if(!this.multiple) { //单选
                            this.$emit('checkChangeNode',a[0])
                        }else{
                            this.$emit('checkChangeNodeList',a)
                        }
                    }
                }
                let dea=null;
                //找出默认值对象
                if(this.defaultValue){
                    dea=res.find(v => v.Id == this.defaultValue);
                }

                console.log(JSON.stringify(res))
                
                res = listToTreeSelect(res, undefined, undefined, undefined, 'RegionalName');
                
                // //如果只展示第一级 parentID
                // if(this.isSubset){
                //     res.forEach(v => {
                //         v.children=[];
                //     })
                // }
                this.treeData = res;

                if(this.defaultExpandLevel >= 1) {
                    //默认不展开
                    this.treeData.forEach(v => {
                        this.defaultKeys.push(v.Id);
                        if(this.defaultExpandLevel >= 2) {
                            if(v.children && v.children.length>0){
                                v.children.forEach(v1 => {
                                    this.defaultKeys.push(v1.Id);
                                })
                            }
                        }
                    })
                }
                if(this.defaultValue && this.defaultValue != 'null'){
                    //选中默认节点
                    this.handleNodeClick(dea);
                    this.$nextTick(function () {
                        this.$refs['tree'].setCurrentKey(this.defaultValue);
                    })
                }else{
                    //默认选中一级节点
                    this.handleNodeClick(this.treeData[0]);
                    this.$nextTick(function () {
                        this.$refs['tree'].setCurrentKey(this.treeData[0].Id);
                    })
                }

                
            })
        },
    }

}
</script>

<style lang="scss" scoped>
.custom-tree-node{
    width:100%;
    height:26px;
}
.show-ellipsis{
    display: block;
  width: calc(100% - 40px);
  height:26px;
  line-height: 26px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tree{
    width:100%;
    height:100%;
    display: flex;
    flex-direction: column;
    .elInput{
        width:100%;
        padding: 10px;
    }
    .treeBox{
        flex: 1;
        width:100%;
        overflow-y: auto;
        overflow-x:hidden;
    }
}
</style>