<template>
    <div class="app-container">
        <!-- <page-title title="在线培训" :subTitle="['在线培训管理页面']"></page-title> -->
        <div class="pageWrapper __dynamicTabContentWrapper">
            <div class="content __dynamicTabWrapper">
                <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading"
                :isShowOpatColumn="true" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false'  :serial='false'>
                    <!-- 表格查询条件区域 -->
                    <template slot="conditionArea">
                        <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch"
                        :layoutMode='layoutMode'>
                            <template slot="KeyWords">
                                <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable
                                v-model.trim="listQuery.KeyWords" placeholder="输入姓名、工号过滤"></el-input>
                            </template>
                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <el-button type="primary" @click="onBtnClicked('btnAdd')">
                                    创建规则
                                </el-button>
                            </template>
                        </app-table-form>
                    </template>
                    <template slot="Idx" slot-scope="scope">
                        {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index) }}
                        <i v-if="scope.row.IsFavorite" class="el-icon-star-on" style="font-size: 20px;color: #ffb81c;vertical-align: middle;"></i>
                    </template>
                    <template slot="PeopleNumber" slot-scope="scope">
                        {{ scope.row.PeopleNumber || 0 }}
                    </template>
    
                    <template slot="SpecificationsModel" slot-scope="scope">{{scope.row.SpecificationsModel||'无'}}</template>
                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <!-- 查看详情 -->
                        <app-table-row-button @click="handleRelationDialog(scope.row)" :type="1" text="关联人员"></app-table-row-button>
                        <app-table-row-button @click="handleTableUpdate(scope.row)" :type="1"></app-table-row-button>
                        <app-table-row-button @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button>
                    </template>
                </app-table>
    
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
        <!-- 创建/编辑/详情 -->
        <create-page 
            v-if="dialogFormVisible"
            @closeDialog="closeDialog"
            @saveSuccess="handleSaveSuccess"
            :dialogFormVisible="dialogFormVisible"
            :dialogStatus="dialogStatus"
            :id="selectId"
            @reload="getList"
        ></create-page>

    
        <relationTable
            v-if="dialogRelationVisible && currentRow"
            @closeDialog="handleCloseRelation"
            @saveSuccess="handleSaveRelationSuccess"
            :dialogFormVisible="dialogRelationVisible"
            :id="currentRow.Id"
        >
        </relationTable>
        
    </div>
    </template>
    
    <script>
    
    import relationTable from './relationTable'
    import indexPageMixin from "@/mixins/indexPage";
    import * as employeePointsRule from "@/api/personnelManagement/employeePointsRule";
    import createPage from "./create";
    
    
    export default {
        name: "employeeDataSetting",
        mixins: [indexPageMixin],
        components: {
            createPage,
            relationTable,
        },
        filters: {
            nameFilter(creator) {
                if (creator) {
                    return creator.Name;
                }
                return "";
            },
            isShowFilter(isShow) {
                if (isShow) {
                    return "有效";
                }
                return "无效";
            },
        },
        created() {
            this.getList();
        },
        data() {
            return {
                // 创建/编辑/详情
                dialogStatus: '',
                dialogFormVisible: false,
                selectId: '',
                currentRow: null,
                currentRowEmployeeList: [],
    
                listLoading: false,
                layoutMode: 'simple',
                tabColumns: [
                    {attr: {prop: "Idx",label: "序号", width: "80"},slot: true},
                    {attr: {prop: "RuleName",label: "规则名称", showOverflowTooltip: true}},
                    {attr: {prop: "PeopleNumber",label: "关联人数"}, slot: true},
                ],
                listQuery: {
                    KeyWords: '',
                },
                tableSearchItems: [
                    { prop: "KeyWords", label: "", mainCondition: true },
                ],
                tabDatas: [], //原始数据
                total: 0,
                
                dialogRelationVisible: false,
                id: '',
                

            };
        },
        methods: {
            onResetSearch() {
                this.listQuery.KeyWords = ''
                this.getList(); //刷新列表
            },
            //获取列表
            getList() {
                let postData = JSON.parse(JSON.stringify(this.listQuery));
                postData = this.assignSortObj(postData);
                this.listLoading = true;
                employeePointsRule.getList(postData).then(res => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch(err => {
                    this.listLoading = false;
                });
            },
            onBtnClicked(domId) {
                console.log(domId)
                switch (domId) {
                        //添加
                    case "btnAdd":
                        this.handleDialog("create");
                        break;
                    default:
                        break;
                }
            },
            //弹出添加框
            handleDialog(activeName) {
                this.dialogStatus = activeName;
                this.dialogFormVisible = true;
            },
    
            // 弹出编辑框
            handleTableUpdate(row, optType = "update") {
                this.selectId = row.Id;
                this.dialogStatus = optType;
                this.dialogFormVisible = true;
            },
            // 多行删除
            handleTableDelete(rows) {
                let ids = [];
                if (_.isArray(rows)) {
                    ids = rows.map(u => u.Id);
                } else {
                    ids.push(rows.Id);
                }
    
                this.$confirm("确定要删除吗?", "提示", {
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    employeePointsRule.del(ids).then(() => {
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                        this.getList();
                    });
                });
            },
            handleFilter() {
                this.listQuery.PageIndex = 1;
                this.getList();
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size;
                this.getList();
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page;
                this.listQuery.PageSize = val.size;
                this.getList();
            },
    
            closeDialog() {
                this.dialogFormVisible = false;
            },
            handleSaveSuccess(_formData) {
                this.getList();
                this.closeDialog();
            },
            back() {
                this.$router.go(-1)
            },
            handleRelationDialog(row) {
                this.currentRow = row
                this.dialogRelationVisible = true
            },
            handleCloseRelation() {
                this.dialogRelationVisible = false
            },
            handleSaveRelationSuccess() {
                this.getList()
                this.handleCloseRelation()
            },
        }
    };
    </script>
    
    <style lang="scss" scoped>
    .pageWrapper {
        display: flex;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
    }
    
    </style>
    