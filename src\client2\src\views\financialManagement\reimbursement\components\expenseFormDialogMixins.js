// 费用报销单,付款申请单,差旅费报销单,借款单 通用mixins
// 4个单据的data都需要有一个表单parcelKey对应后端详情数据的包裹对象
import thousands from "@/directive/thousands";
import CompanySelect from "./CompanySelect.vue";
import DepartmentSelect from "./DepartmentSelect.vue";
import InvoiceList from "./InvoiceList.vue";
import AttachmentList from "./AttachmentList.vue";
import AddProject from "./AddProject.vue";
import approvalPanel from "@/views/projectDev/projectMgmt/common/approvalPanel.vue";
import approvalDetail from "@/views/projectDev/projectMgmt/workbench/common/approvalDetail.vue";
import empSelector from "@/views/common/empSelector.vue";
import { formatThousands, convertToChinese, parseThousands } from "@/utils/money.js";
import { getUserInfo } from "@/utils/auth";
import * as approvalManagement from "@/api/approvalManagement.js";
import { vars as workbenchVars } from "@/views/workbench/myWorkbench/vars";

export default {
  props: {
    /**
     * create: 新增
     * update: 编辑
     * detail：详情
     * approval:审批
     * revokeApproval:撤销审批
     * revoke 撤销申请
     * editDraft:编辑草稿
     */
    dialogStatus: {
      type: String,
      default: "create",
    },
    //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
    isOnlyViewDetail: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: "",
    },
    approvalId: {
      // 审批编号，从审批列表中弹出该页面时需要
      type: String,
      default: "",
    },
    // 审批流程id
    processId: {
      type: String,
      default: "",
    },
    // 如果有直接赋值不走接口
    tempObj: {
      type: [Object, null],
      default: null,
    },
  },
  directives: {
    thousands,
  },
  components: {
    CompanySelect,
    DepartmentSelect,
    InvoiceList,
    AttachmentList,
    empSelector,
    AddProject,
    approvalPanel,
    approvalDetail,
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
    };
  },
  computed: {
    // 内嵌的引用创建弹窗映射
    dialogKey() {
      const dialogMap = {
        FinanceExpenseObj: "showExpenseRbtDialog",
        PaymentObj: "showPaymentReqDialog",
        LoanBillObj: "showLoanApplicationDialog",
        TravelExpenseObj: "showTravelExpensesDialog",
      };
      return dialogMap[this.parcelKey];
    },
    disabled() {
      return !["create", "editDraft"].includes(this.dialogStatus);
    },
    dialogStatusTrans() {
      let statusTemp = this.dialogStatus;
      if (statusTemp == "revoke" || statusTemp == "detail") {
        return "detail";
      } else if (statusTemp == "approval" || statusTemp == "revokeApproval") {
        return "approval";
      } else {
        return statusTemp;
      }
    },
    // 是否当前用户
    isCurrentUser() {
      return this.formData?.EmployeeList?.[0]?.EmployeeId == getUserInfo().employeeid;
    },
  },
  methods: {
    formatThousands,
    convertToChinese,
    parseThousands,
    changeDepartment(value) {
      this.formData[this.parcelKey].DepartmentId = value;
      this.getApprovalDetail();
    },
    // 获取审批详情(根据部门id获取审批流程)
    getApprovalDetail() {
      this.btnLoading = true;
      approvalManagement
        .getApprovalInfo({
          HRApprovalProcessId:this.processId || this.id,
          ApprovalRangeDepartmentId: this.formData[this.parcelKey].DepartmentId,
        })
        .then(res => {
          this.btnLoading = false;
          if (res) {
            this.formData.Approval = res;
            if (!this.formData.Approval.ApprovalEmployeeList) {
              this.formData.Approval.ApprovalEmployeeList = [[]];
            }
          }
        })
        .catch(err => {
          this.btnLoading = false;
        });
    },
    getDetail() {
      this.loading = true;
      let isApprovaled = this.dialogStatus == "detail" ? true : false;
      approvalManagement
        .getPersonalDetails({ id: this.id, approvalId: this.approvalId, isApprovaled })
        .then(res => {
          if (this.parcelKey == "TravelExpenseObj") {
            res.TravelExpenseObj.TravelExpenseDetailList.forEach(t => {
              t.Date = t.StartDate && t.EndDate ? [t.StartDate, t.EndDate] : [];
            });
          }
          this.formData = { ...this.formData, ...res };
          this.$set(this.formData[this.parcelKey], "RevocationCause", res.RevocationCause);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 公司改变钩子
    handleChangeCompany() {
      if (this.formData?.[this.parcelKey]?.KingdeeProjectList) {
        this.formData[this.parcelKey].KingdeeProjectList = [];
      }
    },
    createData() {
      if (this.dialogStatus == "revoke") {
        this.revokeApproval();
        return;
      } else {
        this.validate();
      }
    },
    /**
     * 校验
     * @param temporize true:保存草稿 false:发起审批
     */
    validate(temporize) {
      this.$refs.formRef.clearValidate();

      if (temporize) {
        let hasError = false;
        this.$refs.formRef.validateField(["KingdeeDepartmentNumber", "DepartmentId"], err => {
          if (err) hasError = true;
        });
        hasError ? this.$message.error("请选择公司和部门") : this.createRequest(temporize);
      } else {
        let validate = this.$refs.formRef.validate();
        let approvalPanelValidate = this.$refs.approvalPanel.validate();

        Promise.all([validate, approvalPanelValidate])
          .then(valid => {
            this.createRequest(temporize);
          })
          .catch(() => {
            this.$message.error("请填写完整信息");
          });
      }
    },
    // 撤销审批
    revokeApproval() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.btnLoading = true;
          approvalManagement
            .revocation({
              Id: this.formData.Id,
              RevocationCause: this.formData[this.parcelKey].RevocationCause,
            })
            .then(res => {
              this.$message.success("撤销请求提交成功");
              this.$emit("reload");
              this.$refs.appDialogRef.createData();
              this.closeDialog();
            })
            .finally(() => {
              this.btnLoading = false;
            });
        }
      });
    },
    // 暂存
    handleSaveDraft() {
      this.validate(true);
    },
    handleApproval() {
      let approvalDetailRef = this.$refs.approvalDetail;

      approvalDetailRef.getValidtor().then(valid => {
        if (valid) {
          let postData = approvalDetailRef.getData();
          postData.BusinessId = this.id;
          let approvalLabel = workbenchVars.approvalResult.find(
            s => s.value == postData.ApprovalResultState
          ).label;

          this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.btnLoading = true;
            let result = null;
            if (this.dialogStatus == "approval") {
              result = approvalManagement.createApproval(postData);
            } else if (this.dialogStatus == "revokeApproval") {
              result = approvalManagement.approvalRevocation(postData);
            }
            result
              .then(res => {
                this.btnLoading = false;
                this.$message.success("审批成功");
                this.$refs.appDialogRef.createData();
                this.closeDialog();
              })
              .catch(err => {
                this.btnLoading = false;
              });
          });
        }
      });
    },
    beforeClose() {
      if (this.disabled) {
        this.closeDialog();
      } else {
        this.$confirm("您确定要退出申请吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.closeDialog();
        });
      }
    },
    // 引用创建
    handleReferenceCreate() {
      const form = this.$_.cloneDeep(this.formData);
      form[this.parcelKey].FBillNo = null;
      form[this.parcelKey].RevocationCause = "";
      this.tempObj_ = form;
      this[this.dialogKey] = true;
    },
    closeSubDialog() {
      this[this.dialogKey] = false;
      this.closeDialog();
    },
    closeDialog() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
