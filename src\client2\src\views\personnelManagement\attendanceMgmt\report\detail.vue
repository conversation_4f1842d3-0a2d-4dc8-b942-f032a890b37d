<template>
  <div>
    <app-dialog title="考勤详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1200">
      <template slot="body">
        <div class="tagBox">
          <tags :items='types' v-model="tagType">
              <template v-for="t in types" :slot="t.value">
                  {{ t.label }}
              </template>
          </tags>
        </div>
        <div class="wrapper" v-if="tagType == 1">
          <div class="det">
            <el-form
                ref="formData"
                label-width="80px"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="姓名">
                            {{ row.Name }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="部门">
                            {{ row.DepartmentName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="机器号">
                            {{ row.MachineNo }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="考勤号码">
                            {{ row.TimecardNo }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="考勤月份">
                            {{ year }}-{{ month }}
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
          </div>
          <div class="calendar-wrapper">
            <el-calendar v-model="currentDate" v-loading='loading'>
              <template
                slot="dateCell"
                slot-scope="{date, data}">
                  <div v-if="!onMonth(data.day)">
                    <div>{{ data.day | dateFilter('DD') }}</div>
                  </div>
                  <cell v-if="onMonth(data.day)" :cellData="{
                    currentDate: data.day,
                    dataObj: getDatas(data.day)
                  }"></cell>

                <!--
                <p :class="data.isSelected ? 'is-selected' : ''">
                  {{ data.day.split('-').slice(1).join('-') }} {{ data.isSelected ? '✔️' : ''}}
                </p> -->
              </template>
          </el-calendar>
          </div>
        </div>

        <div class="wrapper" v-else-if="tagType == 2">
          <app-table-core ref="mainTable" :height='700' :tab-columns="tabColumns" :serial='false' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true" :isShowBtnsArea='true' :startOfTable="1" :multable="false" :isShowConditionArea='false'>
              <template slot="Idx" slot-scope="scope">
                  {{ scope.index + 1 }}
              </template>
              <template slot="ApprovalState" slot-scope="scope">
                <app-tag-pure effect="dark" v-if="getStatusObj(scope.row.ApprovalState)" :color="getStatusObj(scope.row.ApprovalState).color" :text="getStatusObj(scope.row.ApprovalState).label"></app-tag-pure>
              </template>
              <template slot="ApprovalModuleSubtypeType" slot-scope="scope">
                {{ getOptTypeDesc(scope.row.ApprovalModuleSubtypeType) }}
                <span v-if="scope.row.LeaveType">
                  ({{ scope.row.LeaveType | levelTypeFilter }})
                </span>
              </template>
              <template slot="CreateEmployee" slot-scope="scope">
                <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
              </template>
              <template slot="TimeSpan" slot-scope="scope">
                <span v-if="scope.row.TimeSpan">
                  <!-- 外出 或者 外出申请撤销 x小时x分钟 -->
                  <span v-if="scope.row.ApprovalModuleSubtypeType == 32 || scope.row.ApprovalModuleSubtypeType == 36">
                    {{ transTime(scope.row.TimeSpan) }}
                  </span>
                  <span v-else>
                    {{ scope.row.TimeSpan }}天
                  </span>
                </span>
                <span v-else>
                  无
                </span>
                
              </template>
              <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                  <app-table-row-button @click="handleShowDetail(scope.row)" :type="2"></app-table-row-button>
                  <app-table-row-button v-if="scope.row.ApprovalModuleSubtypeType == 42 || scope.row.ApprovalModuleSubtypeType == 43" @click='handlePrintRow(scope.row)' text="打印"></app-table-row-button>
                  
              </template>
          </app-table-core>
        </div>
      </template>

      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
      </template>
    </app-dialog>


    <!-- 打印视图 -->
    <evectionDetailPrint ref="print"></evectionDetailPrint>

    <!-- 考核申诉 -->
    <common-bullet :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogBulletChangeFormVisible" @closeDialog="closeBulletChangeDialog" @saveSuccess="handleBulletChangeSaveSuccess" :dialogFormVisible="dialogBulletChangeFormVisible" dialogStatus="detail" :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail" :disAB="true"></common-bullet>
    <!-- 请假申请 -->
    <vacate :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogVaVisible" @closeDialog="closeVaDialog" @saveSuccess="handleVcateSaveSuccess" :dialogFormVisible="dialogVaVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></vacate>
    <!-- 加班申请 -->
    <overtime :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogOvertimeVisible" @closeDialog="closeOveretimeDialog" @saveSuccess="handleOvertimeSaveSuccess" :dialogFormVisible="dialogOvertimeVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></overtime>
    <!-- 出差申请 -->
    <evection :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogEvVisible" @closeDialog="closeEvDialog" @saveSuccess="() => {}" :dialogFormVisible="dialogEvVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></evection>
     <!-- 考核申诉 -->
    <attendance-complaint :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogAttenVisible" @closeDialog="closeAttenDialog" @saveSuccess="handleAttenSaveSuccess" :dialogFormVisible="dialogAttenVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></attendance-complaint>
    <!-- 外出申请 -->
    <goout :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogGoOutVisible" @closeDialog="closeGoOutDialog" @saveSuccess="handleGoOutSaveSuccess" :dialogFormVisible="dialogGoOutVisible" dialogStatus='detail' :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></goout>
    <!-- 考勤人工审核——移动端提交 -->
    <att-approval :specialPageTitle='getOptTypeDesc(optType)' v-if="dialogAttendanceApprovalFormVisible" @closeDialog="closeAttendanceApprovalDialog" @saveSuccess="handleAttendanceApprovalSaveSuccess" :dialogFormVisible="dialogAttendanceApprovalFormVisible" dialogStatus="detail" :id="id" :approvalId="approvalId" :isOnlyViewDetail="isOnlyViewDetail"></att-approval>

    <!-- 出差结束申请 -->
    <evectionOver
      v-if="dialogEvOverVisible"
      @closeDialog="closeEvOverDialog"
      @saveSuccess="() => {}"
      :dialogFormVisible="dialogEvOverVisible"
      :dialogStatus='"detail"'
      :id="id"
      :approvalId='approvalId'
    >
    </evectionOver>

    <!-- 出差补助申请 -->
    <evectionSubsidy
      v-if="dialogEvSubSidyVisible"
      @closeDialog="closeEvSubSidyDialog"
      @saveSuccess="() => {}"
      :dialogFormVisible="dialogEvSubSidyVisible"
      :dialogStatus='"detail"'
      :id="id"
      :approvalId='approvalId'
    >
    </evectionSubsidy>
  </div>
</template>

<script>

import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import indexPageMixin from "@/mixins/indexPage"
import { vars } from '../vars' 
import dayjs from "dayjs"
import * as approvalVars from '../../../projectDev/common/vars'
import * as workbenchVars from "../../../workbench/myWorkbench/vars";
import * as approval from "@/api/projectDev/projectMgmt/approvalSetting";
import cell from './cell'
import * as approvalManagement from "@/api/approvalManagement";
import { getUserInfo } from "@/utils/auth";
import { minuteFilter } from '../../../../filters';
import evectionDetailPrint from './evectionDetailPrint'
import initCellData from './common'

export default {
  name: "attendanceMgmt-report-detail",
  directives: {},
  components: {
    cell,
    commonBullet:() => import('../../../workbench/achievements/commonBullet'),
    vacate:() => import('../../../workbench/myWorkbench/apply/vacate'),
    overtime:() => import('../../../workbench/myWorkbench/apply/overtime'),
    evection:() => import('../../../workbench/myWorkbench/apply/evection'),
    goout:() => import('../../../workbench/myWorkbench/apply/goout'),
    evectionOver:() => import('../../../workbench/myWorkbench/apply/evectionOver'),
    evectionSubsidy:() => import('../../../workbench/myWorkbench/apply/evectionSubsidy'),
    attendanceComplaint:() => import('../../../workbench/attendance/attendanceComplaint'),
    attApproval:() => import('../../../workbench/myApproval/others/attendanceApproval'),
    evectionDetailPrint,
  },
  mixins: [indexPageMixin],
  computed: {
    
  },
  props: {
    year: {
      type: Number,
      required: true
    },
    month: {
      type: Number,
      required: true
    },
    employeeId: {
      type: String,
      required: true
    },
    row: {
      type: Object,
      default: {}
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.getDetail();
        }
      },
      immediate: true
    },
    tagType(val) {
      if(val == 2) {
        this.getList()
      }
    }
  },
  filters: {
    levelTypeFilter(val) {
        let obj = workbenchVars.vars.leaveTypes.find(s => s.value === val)
        if(obj) {
            return obj.label
        }
        return '-'
    },
    
  },
  created() {
  },
  mounted(){
  
  },
  data() {
    return {
      tagType: 1,
      types: [
        {label: '考勤月历', value: 1},
        {label: '审批流程', value: 2},
      ],
      currentDate: new Date(this.year, this.month - 1, 1),
      datas: null,
      disabledBtn: false,
      loading: false,


      tabDatas: [], //原始数据
      tabAuthColumns: [],
      listLoading: false,
      tabColumns: [
        {
          attr: { prop: "Code", label: "审批单号", width: '150' }
        },
        {
          attr: { prop: "ApprovalState", label: "审批状态", width: '100' },
          slot: true
        },
        {
          attr: {
            prop: "ApprovalModuleSubtypeType",
            label: "流程类型",
            width: '160'
          },
          slot: true
        },
        {
          attr: {
            prop: "StartEndTime",
            label: "开始/结束时间",
            width: '240'
          },
        },
        {
          attr: {
            prop: "TimeSpan",
            label: "时长",
            width: '140'
          },
          slot: true
        },
        {
          attr: { prop: "CreateEmployee", label: "提审人" },
          slot: true
        },
        {
          attr: { prop: "CreateTime", label: "申请时间" },
          slot: true
        },
      ],
      optType: -1,
      isOnlyViewDetail: true,
      id: '',
      approvalId: '',

      //考核申诉
      dialogBulletChangeFormVisible:false,

      // 请假、加班、出差
      dialogVaVisible:false,
      dialogOvertimeVisible:false,
      dialogEvVisible:false,

      dialogGoOutVisible: false,

      //考勤申诉
      dialogAttenVisible:false,

      //考勤人工审核
      dialogAttendanceApprovalFormVisible: false,

      printRow: null,

      dialogEvOverVisible: false,

      dialogEvSubSidyVisible: false,

    };
  },
  methods: {
    handlePrintRow(row) {
      let type = 3
      if(row.ApprovalModuleSubtypeType == 42) {
        type = 3
      }else if(row.ApprovalModuleSubtypeType == 43) {
        type = 6
      }
      //3：出差；出差补助：6
      this.$refs.print.handlePrintRow({type: type, id: row.CurrentBusinessId, approvalId: row.Id})
    },
    transTime(val) {
        return minuteFilter(val)
    },
    getStatusObj(val) {
      return approvalVars.vars.approvalStatuObj.approvalStatus.find(
        s => s.value == val
      ) || {};
    },
    // //获取打卡数据
    // getTimecardRecord(date) {

    //   if(dayjs(date).isValid()) {
    //     let currentDate = dayjs(date).format('YYYY-MM-DD')
    //     if(this.datas && this.datas.TimecardRecordList && this.datas.TimecardRecordList.length > 0) {
    //       //当前的打卡数据
    //       let record = this.datas.TimecardRecordList.find(s => s.Date == currentDate) || {}
    //       return record
    //     }
    //   }

    //   return {}
    // },
    getList() {
      this.listLoading = true;
      let postDatas = {
        Year: this.year,
        Month: this.month,
        EmployeeId: this.employeeId

      }

      approvalManagement
        .getSignHRApprovalProcessByMonth(postDatas)
        .then(res => {
          this.listLoading = false;
          this.tabDatas = res;
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    getOptTypeDesc(type) {
      let tmp = approvalVars.vars.approvalStatuObj.approvalModuleSubtypeTypes.find(
        s => s.value == type
      );
      if (tmp) {
        return tmp.label;
      }
      return "";
    },
    getDatas(date) {
      if(dayjs(date).isValid()) {
        let currentDate = dayjs(date).format('YYYY-MM-DD')
        if(this.datas) {
          let result = this.datas.find(s => s.Date == currentDate)
          return result
        }
      }
      return null
    },
    handleAreaChangeSaveSuccess() {
      this.getList();
      this.closeAreaChangeDialog();
    },
    handleBulletChangeDialog(){
      this.dialogBulletChangeFormVisible=true;
    },



    handleVcateChangeDialog() {
      this.dialogVaVisible = true;
    },
    closeVaDialog() {
      this.dialogVaVisible = false;
    },

    handleOvertimeChangeDialog() {
      console.log(666)
      this.dialogOvertimeVisible = true;
    },
    closeOveretimeDialog() {
      this.dialogOvertimeVisible = false;
    },

    handleEvectionChangeDialog() {
      this.dialogEvVisible = true;
    },
    closeEvDialog() {
      this.dialogEvVisible = false;
    },

    handleAttenChangeDialog(){
      this.dialogAttenVisible=true;
    },
    closeAttenDialog(){
      this.dialogAttenVisible=false;
    },


    handleGoOutChangeDialog() {
      this.dialogGoOutVisible = true;
    },
    closeGoOutDialog() {
      this.dialogGoOutVisible = false;
    },


    handleAttendanceApprovalDialog() {
      this.dialogAttendanceApprovalFormVisible = true;
    },
    closeAttendanceApprovalDialog() {
      this.dialogAttendanceApprovalFormVisible = false;
    },



    handleShowDetail(row) {

      // console.log(***********,row)
      // console.log(111111111222222222);
      // console.log(JSON.stringify(row));
      // console.log(999999999999999999999999);

      this.id = row.CurrentBusinessId;
      
      // 不同模块下可能有多个操作需要审批，例如：项目研发 下 项目创建、项目验收 需要审批，他们调用的审批页面不同
      // let moduleType = row.ApprovalModuleType //模块（1：项目研发）
      // if(moduleType == 1) { //项目研发
      // }
      let optType = row.ApprovalModuleSubtypeType;
      this.optType = optType

      this.approvalId = row.Id;
      
      if(optType == 27) { //考核申诉
        this.handleBulletChangeDialog();
      }else if(optType == 28 || optType == 33) { //请假（撤销 请假申请）
        // if(optType == 33) {
        //   this.dialogVacateStatus = 'revokeApproval';
        // }
        this.handleVcateChangeDialog();
      }else if(optType == 29 || optType == 34) { //加班（撤销 加班申请）
        // if(optType == 34) {
        //   this.dialogOvertimeStatus = 'revokeApproval';
        // }
        this.handleOvertimeChangeDialog();
      }else if(optType == 30 || optType == 35) { //出差（撤销 出差申请）
        // if(optType == 35) {
        //   this.dialogEvectionStatus = 'revokeApproval';
        // }
        this.handleEvectionChangeDialog();
      }else if(optType == 31) { //考勤申诉
        this.handleAttenChangeDialog();
      }else if(optType == 32 || optType == 36) { //外出申请（撤销 外出申请）
        // if(optType == 36) {
        //   this.dialogGoOutStatus = 'revokeApproval';
        // }
        this.handleGoOutChangeDialog()
      }else if(optType == 37) {
        this.handleAttendanceApprovalDialog();
      }else if(optType == 42) {
        this.handleOverEvection()
      }else if(optType == 43) {
        this.handleEvSubSidyDialog()
      }
    },

    handleOverEvection() {
      this.dialogEvOverVisible = true
    },
    closeEvOverDialog() {
      this.dialogEvOverVisible=false;
    },
    handleEvSubSidyDialog() {
      this.dialogEvSubSidyVisible = true
    },
    closeEvSubSidyDialog() {
      this.dialogEvSubSidyVisible = false
    },


    // //是否为休息日
    // isRestDay(date) {
    //   if(dayjs(date).isValid()) {
    //     let currentDate = dayjs(date).format('YYYY-MM-DD')
    //     if(this.datas && this.datas.RestDayList && this.datas.RestDayList.length > 0) {
    //       return this.datas.RestDayList.findIndex(s => s == currentDate) > -1
    //     }
    //     return false
    //   }
    //   return false
    // },
    // //获取流程数据集合
    // getProcessRecord(date) {
    //   if(dayjs(date).isValid()) {
    //     let currentDate = dayjs(date).format('YYYY-MM-DD')
    //     if(this.datas && this.datas.ProcessDataList && this.datas.ProcessDataList.length > 0) {
    //       //当前的打卡数据
    //       return this.datas.ProcessDataList.filter(s => s.Date == currentDate) || []
    //     }
    //     return []
    //   }
    //   return []
    // },
    // //初始化详细接口数据（转换成ui需要的格式）
    // initDatas(datas) {
    //   // datas 格式
    //   // let mode = {
    //   //   //休息日
    //   //   "RestDayList": ["2021-01-02 00:00:00.000", "2021-01-03 00:00:00.000", "2021-01-09 00:00:00.000", "2021-01-10 00:00:00.000", "2021-01-16 00:00:00.000", "2021-01-17 00:00:00.000", "2021-01-23 00:00:00.000", "2021-01-24 00:00:00.000", "2021-01-30 00:00:00.000", "2021-01-31 00:00:00.000"],
    //   //   //打开数据
    //   //   "TimecardRecordList": [{
    //   //     "Date": "2021-01-13 00:00:00.000",
    //   //     "MornFirstCardTime": "2021-01-13 10:29:50.000",
    //   //     "MornFirstCardStatus": 1, //上午打卡状态
    //   //     "NightLastCardTime": "2021-01-13 17:19:55.000",
    //   //     "NightLastCardStatus": 1  //下午打卡状态
    //   //   }],
    //   //   //流程数据
    //   //   "ProcessDataList": [{
    //   //     "StartTime": "2021-01-05 00:00:00.000",
    //   //     "EndTime": "2021-01-05 00:00:00.000",
    //   //     "LeaveType": 1,
    //   //     "StartUpDown": 1,
    //   //     "EndUpDown": 1
    //   //   }, {
    //   //     "StartTime": "2021-01-12 00:00:00.000",
    //   //     "EndTime": "2021-01-12 00:00:00.000",
    //   //     "LeaveType": 1,
    //   //     "StartUpDown": 1,
    //   //     "EndUpDown": 1
    //   //   }, {
    //   //     "StartTime": "2021-01-19 00:00:00.000",
    //   //     "EndTime": "2021-01-19 00:00:00.000",
    //   //     "LeaveType": 1,
    //   //     "StartUpDown": 1,
    //   //     "EndUpDown": 1
    //   //   }, {
    //   //     "StartTime": "2021-01-26 00:00:00.000",
    //   //     "EndTime": "2021-01-26 00:00:00.000",
    //   //     "LeaveType": 1,
    //   //     "StartUpDown": 1,
    //   //     "EndUpDown": 1
    //   //   }]
    //   // }

    //   if(datas) {
    //     let result = JSON.parse(JSON.stringify(datas))
    //     /** 
    //      * 休息日 处理，不需要时分秒
    //      */
    //     result.RestDayList = result.RestDayList.map(s => {
    //       s = dayjs(s).format('YYYY-MM-DD')
    //       return s
    //     })

    //     /**
    //      * 打卡数据 处理，打卡日期不需要时分秒，打卡时间不需要年月日
    //      */
    //     result.TimecardRecordList = result.TimecardRecordList.map(s => {
    //       s.Date = s.Date && dayjs(s.Date).format('YYYY-MM-DD')
    //       s.MornFirstCardTime = s.MornFirstCardTime && dayjs(s.MornFirstCardTime).format('HH:mm:ss')          
    //       s.NightLastCardTime = s.NightLastCardTime && dayjs(s.NightLastCardTime).format('HH:mm:ss')
    //       return s
    //     }) || []

    //     /**
    //      * //流程数据 处理，把对应的时间段转换成”半天“：
    //      */
    //     /**
    //      * 如：
    //      * 请假流程中 2020-01-13 上午————2020-02-15 下午
    //      * 转换成：
    //      * 日期       上、下午    流程类型
    //      * 2020-01-13 上午        请假
    //      * 2020-01-13 下午        出差
    //      * 2020-01-14 上午        加班
    //      * 2020-01-14 下午
    //      * 2020-01-15 上午
    //      * 2020-01-15 下午
    //      */

    //     //请假开始、结束日期不需要时分秒
    //     result.ProcessDataList = result.ProcessDataList.map(s => {
    //       s.StartTime = dayjs(s.StartTime).format('YYYY-MM-DD')
    //       s.EndTime = dayjs(s.EndTime).format('YYYY-MM-DD')
    //       return s
    //     }) || []

    //     result.ProcessDataList = result.ProcessDataList.reduce((prev, curr) => {
    //       let transdList = [] //转换后的数组
    //       //只处理合法的数据（StartUpDown、EndUpDown：1；上午；2：下午）
    //       //日期间隔
    //       let interval = dayjs(curr.EndTime).diff(dayjs(curr.StartTime),'day')
    //       if(interval > 0 || (interval == 0 && curr.StartUpDown <= curr.EndUpDown)) {
    //         //计算流程开始时间（包含上、下午）到结束时间（包含上、下午）有多少个半天
    //         //计算公式：
    //         /**
    //          * 日期间隔 * 2 	+1				              +0				              +2
    //          * 
    //          * 日期间隔		    StartUpDown=EndUpDown		StartUpDown>EndUpDown		StartUpDown<EndUpDown
    //          * 0		          1				                不可能（不合法）			  2
    //          * 1	      	    3				                2				                4
    //          * 2		          5				                4				                6
    //          * 3		          7				                6				                8
    //          */
            
    //         //“半天”个数
    //         let count = interval * 2
    //         if(curr.StartUpDown == curr.EndUpDown) {
    //           count += 1
    //         }else if(curr.StartUpDown > curr.EndUpDown) {
    //           count += 0
    //         }else if(curr.StartUpDown < curr.EndUpDown) {
    //           count += 2
    //         }

    //         let flag = 1
    //         let timeFlag = curr.StartTime
    //         let upDownFlag = curr.StartUpDown
    //         do {
    //           transdList.push({
    //             Date: timeFlag,
    //             UpDown: upDownFlag,
    //             LeaveType: curr.LeaveType
    //           })
    //           if(upDownFlag == 2) {
    //             upDownFlag = 1
    //             timeFlag = dayjs(timeFlag).add(1, 'day').format('YYYY-MM-DD')
    //           }else{
    //             upDownFlag++
    //           }

    //           flag++

    //         }while(flag <= count)

    //       }
    //       prev = prev.concat(transdList);
    //       return prev;
    //     }, []);
        
    //     return result
    //   }

    //   return {}
    // },
    //当前日期是否存在当前月份中
    onMonth(day) {
      let currentDay = dayjs(day)
      if(currentDay.isValid()) { 
        return currentDay.year() === this.year && (currentDay.month() + 1) === this.month
      }
      return false
    },
    getDetail() {
      let postDatas = {
        Year: this.year,
        Month: this.month,
        EmployeeId: this.employeeId
      }

      this.loading = true
      timecardDepartment.getPersonalTimecardRecords(postDatas).then(res => {
        this.loading = false
        this.datas = initCellData(res)
      }).catch(err => {
        this.loading = false
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style scoped>
.det >>> .el-form-item{
  margin-bottom: 0!important;
}

.det >>> .el-form{
  padding-top: 0!important;
}

.calendar-wrapper >>> .el-calendar__header{
  display: none!important;
}

.calendar-wrapper >>> .el-calendar__body{
  padding: 10px!important;
}
.calendar-wrapper >>> .el-calendar-table{
  height: 700px;
}
.calendar-wrapper >>> .el-calendar-table:not(.is-range) td.next{
    pointer-events: none;
}
.calendar-wrapper >>> .el-calendar-table:not(.is-range) td.prev{
    pointer-events: none;
}

.calendar-wrapper >>> .el-calendar-day{
  padding: 4px;
  overflow: auto;
  height: 100%;
}
</style>

<style lang='scss' scoped>
.wrapper{
  min-height: 400px;
  margin-top: 10px;
  .det{
  }
}

.tagBox{
  border-bottom: 1px solid #EBEEF5;
  padding: 4px 8px;
}


</style>