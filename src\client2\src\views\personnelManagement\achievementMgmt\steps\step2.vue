<template>
    <div class="step-wrapper">
        <div class="step-main">
            <el-form ref="formData" label-position="right" label-width="100px">
                <div>
                    <app-table-core ref="mainTable" 
                        :tab-columns="tabColumns" 
                        :tab-datas="tabDatas" 
                        :tab-auth-columns="tabAuthColumns" 
                        :isShowAllColumn="true" 
                        :loading="listLoading" 
                        :isShowOpatColumn="true" 
                        :startOfTable="startOfTable"
                        :multable='false'
                        >

                        <template slot="SelfRatingEmployee" slot-scope="scope">
                            <span v-if="scope.row.SelfRatingEmployee">{{ scope.row.SelfRatingEmployee.Name }}</span>
                        </template>
                        <template slot="AppraisePromiseStatus" slot-scope="scope">
                            <span :style="{color: getStatusColor(scope.row.AppraisePromiseStatus)}">
                                {{ scope.row.AppraisePromiseStatus | appraisePromiseStatusFilter }}
                            </span>
                        </template>

                        <!-- 表格批量操作区域 -->
                        <!-- <template slot="btnsArea">
                            <div class="btns-wrapper">
                                <el-button type="primary" class="elButton" @click="() => {}">批量删除</el-button>
                            </div>
                        </template> -->

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleShowDetail(scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="editable && scope.row.AppraisePromiseStatus == 2" @click="handleResubmit(scope.row)" :type="2" text='重新提交'></app-table-row-button>
                        </template>
                    </app-table-core>
                </div>
            </el-form>
        </div>
        <!-- <div class="btn-wrapper" v-if="editable">
            <el-button type="primary" style="width: 180px;" :loading="loading" :disabled='loading' @click="handleSave">完成个人绩效采集，下一步</el-button>
        </div> -->
    </div>
</template>


<script>
import indexPageMixin from "@/mixins/indexPage";
import empSelector from '@/views/common/empSelector'
import { appraisePromiseStatusEnum } from "../enum"
import * as ach from "@/api/personnelManagement/achievementMgmt"
import * as myAch from "@/api/myAchievements"

export default {
    name: "step2",
    mixins: [indexPageMixin],
    components: {
        empSelector,
    },
    props: {
        //跟进 才 可编辑；详细不可以
        isFollow: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: true
        },
        progressStatus: {
            type: Number,
            required: true
        }
    },
    watch: {
        id: {
            handler(val) {
                this.getList()
            },
            immediate: true
        }
    },
    computed: {
        editable() {
            return this.isFollow && this.progressStatus < 6
        }
    },
    created() {
        
    },
    mounted() {
    },
    filters: {
        appraisePromiseStatusFilter(val) {
            let obj = appraisePromiseStatusEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    data() {
        return {
            appraisePromiseStatusEnum,
            loading: false,
            listLoading: false,
            tabDatas: [],
            tabColumns: [{
                    attr: {
                        prop: "SelfRatingEmployee",
                        label: "考核对象",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "AppraisePromiseStatus",
                        label: "绩效承诺",
                    },
                    slot: true,
                },
            ],
            
        };
    },
    methods: {
        handleResubmit(row) {
            this.$confirm('是否确认重新提交？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                myAch.resubmit([row.Id]).then(res => {
                    this.getList()
                    this.$notify({
                        title: '成功',
                        message: '操作成功',
                        type: 'success',
                        duration: 2000
                    })
                })
            })
        },
        getStatusColor(val) {
            let obj = this.appraisePromiseStatusEnum.find(s => s.value == val)
            if(obj) {
                return obj.color
            }
            return ''
        },
        handleSave() {
            //如果不是全部为“已提交”状态（说明有“未提交”、“重新提交”）
            if(!this.tabDatas.every(s => s.AppraisePromiseStatus == 2)) { 
                this.$confirm(`检测到有考核对像未提交绩效承诺，是否继续？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.saveData()
                })
            }else{
                this.$confirm(`即将进入下一阶段，是否继续?`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.saveData()
                })
            }
        },
        saveData() {
            this.loading = true
            ach.finishPersonalGatherAndNext([this.id]).then(res => {
                this.loading = false
                this.$emit('forwardSuccess')
            }).catch(err => {
                this.loading = false
            })
        },
        getList() {
            let postData = {
                PageSize: 10000,
                PageIndex: 1,
                AppraisePlanId: this.id
            }
            this.listLoading = true
            myAch.getList(postData).then(res => {
                this.listLoading = false
                this.tabDatas = res.Items || []
            }).catch(err => {
                this.listLoading = false
            })
        },
        handleShowDetail(row) {
            //1 表示中期；2：表示已自评
            this.$emit('showDetail', {Id: row.Id, Type: 1})
        },
    },
};
</script>

<style lang="scss" scoped>
@import "./step.css";
</style>