<template>
  <div class="areaChoose">
    <app-dialog
      :title="`选择最近上传的${dataType == 1 ? '文件' : '附件'}`"
      className="clear-default-height-auto"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="800"
    >
      <template slot="body">
        <div class="selector-wrapper">
          <div class="search-content" v-if="dataType == 1">
            <el-input
              placeholder="关键字搜索"
              @clear="handleFilter"
              v-antiShake="{
                time: 300,
                callback: () => {
                  handleFilter();
                },
              }"
              clearable
              v-model="listQuery.Keywords"
            ></el-input>
          </div>

          <div class="list-wrapper" v-loading="loading">
            <div class="item-wrapper" v-for="(item, idx) in list" :key="idx">
              <el-checkbox
                v-model="item[`${prefix}${item[uniqueId]}`]"
                @change="val => handleChange(val, item)"
              ></el-checkbox>
              <span style="margin-left: 5px;" class="flex-1 omit">{{ item.FileName }}</span>
            </div>
          </div>
          <pagination
            class="pagination-wrapper"
            :total="total"
            :page.sync="listQuery.PageIndex"
            :size.sync="listQuery.PageSize"
            @pagination="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, prev, pager, next, jumper"
          />
        </div>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import { getUserInfo } from "@/utils/auth";
import * as httf from "@/api/mainLineMgmt/hypertrunkTaskFile";
export default {
  components: {},
  props: {
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dataType: {
      type: Number,
      default: 1, //1：选择文件（附件集合）；2：选择附件
    },
  },
  watch: {
    checkedList: {
      handler(val) {
        this.checkedDatas = JSON.parse(JSON.stringify(val || []));
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      listQuery: {
        Keywords: "",
        PageIndex: 1,
        PageSize: 20,
      },
      prefix: "chk-",
      uniqueId: "Id",
      checkedDatas: [],
    };
  },
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    getList() {
      let postDatas = JSON.parse(JSON.stringify(this.listQuery));
      postDatas.EmployeeId = getUserInfo().employeeid;

      let result = null;
      if (this.dataType == 1) {
        result = httf.getHistory(postDatas);
      } else {
        result = httf.getAttachmentList(postDatas);
      }

      this.loading = true;
      result
        .then(res => {
          this.loading = false;
          this.total = res.Total;
          this.list = res.Items.map(s => {
            s[`${this.prefix}${s[this.uniqueId]}`] = !!this.checkedList.find(
              n => n[this.uniqueId] == s[this.uniqueId]
            );
            return s;
          });
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    handleChange(val, item) {
      let idx = this.checkedDatas.findIndex(s => s[this.uniqueId] == item[this.uniqueId]);

      if (val && idx == -1) {
        this.checkedDatas.push(item);
      } else if (!val && idx > -1) {
        this.checkedDatas.splice(idx, 1);
      }
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    createData() {
      this.$refs.appDialogRef.createData(JSON.parse(JSON.stringify(this.checkedDatas)));
    },
  },
};
</script>
<style lang="scss" scoped>
.selector-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 500px;
  .search-content {
    padding: 10px;
    margin-bottom: 5px;
    border-bottom: 1px solid $border-color-light;
  }
  .list-wrapper {
    flex: 1;
    overflow-y: auto;
    padding: 0 5px;
    .item-wrapper {
      padding: 5px;
      display: flex;
      align-items: center;
      &:hover {
        background: $bg-color-1;
      }
    }
  }
  .pagination-wrapper {
  }
}
</style>
