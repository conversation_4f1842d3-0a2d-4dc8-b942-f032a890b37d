<template>
  <!-- 附件列表 -->
  <div class="attachment_list" :class="{ disabled: disabled || list.length >= limit }">
    <app-uploader
      :readonly="disabled"
      :accept="accept"
      :fileType="3"
      :max="limit"
      :value="list"
      :fileSize="1024 * 1024 * 10"
      :isTextBtn="true"
      @change="handleFilesUpChange"
    />
    <span class="attachment_count">({{ list.length }}/{{ limit }})</span>
  </div>
</template>

<script>
export default {
  name: "AttachmentList",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: "all",
    },
  },
  data() {
    return {
      limit: 20,
      list: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        this.list = this.$_.cloneDeep(val);
      },
      immediate: true,
    },
  },
  methods: {
    uploadAttachment() {},
    handleFilesUpChange(list) {
      this.list = list;
      this.change();
    },
    change() {
      const list = this.$_.cloneDeep(this.list);
      this.$emit("input", list);
      this.$emit("change", list);
    },
  },
};
</script>

<style lang="scss" scoped>
.attachment_list {
  position: relative;
  &.disabled {
    padding-top: 20px;
  }
  .attachment_count {
    position: absolute;
    right: 0;
    top: 0;
    color: $placeholder;
  }
}
</style>
