<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth">

          <el-form-item label="标题" prop="NoticeTitle">
            <el-input maxlength="30" type="text" v-model="formData.NoticeTitle"></el-input>
          </el-form-item>

          <el-form-item label="内容" prop="NoticeContent">
            <!-- <el-input type="textarea" :rows="8" v-model="formData.NoticeContent"></el-input> -->
            <!-- <editor-bar v-model="formData.NoticeContent" :isClear="isClear"></editor-bar> -->
            <editor-bar :value="formData.NoticeContent" @edit="formData.NoticeContent = arguments[0]"></editor-bar>
          </el-form-item>

          <el-form-item label='发布对象' prop="EmployeeList" :rules="(formData.NoticeStatus == 1)?[{required:true, message: '请选择发布对象', trigger: 'change' }]:[{required:false}]">
            <emp-selector key="ccusers" :showType="2" :multiple="true" :list="formData.EmployeeList" @change="handleChangeParticipantUsers"></emp-selector>
          </el-form-item>
        
         <el-form-item label="作者" prop="Author">
            <el-input maxlength="30" type="text" v-model="formData.Author"></el-input>
          </el-form-item>
          <!-- <el-form-item label='作者' prop="AuthorList" :rules="(formData.NoticeStatus == 1)?[{required:true, message: '请选择作者', trigger: 'change' }]:[{required:false}]">
            <emp-selector key="author" :beforeConfirm='handleBeforeConfirm' :showType="2" :multiple="false" :list="formData.AuthorList" @change="handleChangeAuthor"></emp-selector>
          </el-form-item> -->

          <el-form-item label='附件' prop="AttachmentIdList">
            <app-uploader accept='all' :fileType='3' :max='10000' :value='formData.AttachmentIdList' :fileSize='1024 * 1024 * 500' :minFileSize='100 * 1024' @change='handleFilesUpChange'></app-uploader>
          </el-form-item>

        </el-form>
      </template>
      <template slot="footer">
        <!-- <el-button v-if="this.dialogStatus == 'create'" @click="handleDraft">存为草稿</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSave" type="primary">发布</el-button> -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleDraft" text="存为草稿"></app-button>
        <app-button @click="handleSave" text="发布"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as systemNotices from '@/api/informationCenter/systemNotices'
// import EditorBar from '../../../components/WangEditor/index.vue'
import EditorBar from '@/components/QuillEditor/index.vue'
import empSelector from '../../common/empSelector'
import { newsTypeEnum } from "../enums";
export default {
  name: "demand-pool-create",
  directives: {},
  components: {
    empSelector, EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.isContinue = false;
      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "发布公告通知";
      } else if (this.dialogStatus == "update") {
        return "编辑公告通知";
      }
    },
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      formLoading: false,
      isClear: false,
      newsTypeEnum: newsTypeEnum,
      isContinue: false,
      rules: {
        NoticeTitle: {
          fieldName: "标题",
          rules: [{ required: true }]
        },

        Author: {
          fieldName: "作者",
          rules: [{ required: true }]
        },

        NoticeContent: {fieldName: "内容",rules: [{ required: true }, {max: 20000, trigger: "blur"}]},
        // EmployeeList: {
        //   fieldName: "发布对象",
        //   rules: [{ required: true }]
        // }
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        NoticeTitle: "",
        NoticeContent: "",
        NoticeStatus: 1,
        EmployeeIdList: [], //发布对象ID集合
        EmployeeList: [],//发布对象集合
        AttachmentIdList: [],// 附件ID集合
        Author:"",//作者
      }
    };
  },
  methods: {
    // handleBeforeConfirm(users) {
    //     if (users && users.length > 1) {
    //         this.$message({
    //             message: '作者不得超过1人',
    //             type: 'error'
    //         })
    //         return false
    //     }
    //     return true
    // },
    // handleChangeAuthor(users){
    //   if (users && users.length > 0) {
    //          this.formData.AuthorList = [users[0]];
    //       } else {
    //          this.formData.AuthorList = [];
    //      }
    // },

    handleChangeParticipantUsers(users) {
      this.formData.EmployeeList = users
    },
    handleFilesUpChange(files) {
      this.formData.AttachmentIdList = files
    },
    resetFormData() {
      let temp = {
        Id: "",
        NoticeTitle: "",
        NoticeContent: "",
        NoticeStatus: 1,
        EmployeeIdList: [], //发布对象ID集合
        EmployeeList: [],//发布对象集合
        AttachmentIdList: [],// 附件ID集合
        Author:"",//作者 单选
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      this.formLoading = true;
      systemNotices.detail({ id: this.id, isUpdate: true }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        this.formLoading = false;
        if (this.formData.EmployeeList === null) {
          this.formData.EmployeeList = []
        }
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //保存
    createData() {
      let validate = this.$refs.formData.validate();

      Promise.all([validate]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        //提交数据保存

        let result = null
        if (postData.EmployeeList != null) {
          postData.EmployeeIdList = postData.EmployeeList.map(s => s.EmployeeId)
        }

        // postData.EmployeeIdList = postData.EmployeeList.map(s => s.EmployeeId)
        postData.AttachmentIdList = postData.AttachmentIdList && postData.AttachmentIdList.map(s => s.Id)

        if (this.dialogStatus == "create") {
          delete postData.Id;
          result = systemNotices.add(postData);
        } else if (this.dialogStatus == "update") {
          result = systemNotices.edit(postData);
        }

        result.then(res => {
          if (this.isContinue) {
            this.resetFormData();
            this.$emit("reload");
          } else {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.$refs.appDialogRef.createData();
          }
        });
      });
    },

    handleSave() {

      this.formData.NoticeStatus = 1;
      this.$nextTick(() => {
        this.createData();
      })
    },

    handleDraft() {

      this.formData.NoticeStatus = 0;
      this.$nextTick(() => {
        this.createData();
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
