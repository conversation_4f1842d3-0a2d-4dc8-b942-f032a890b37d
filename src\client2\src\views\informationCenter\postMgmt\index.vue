<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title :title="pageTitle" :subTitle="[pageDesc]" :showBackBtn="!!returnUrl" @goBack="handleGoBack"></page-title> -->
        <div class="page-wrapper">
            <div class="product-list">
                <el-row>
                    <el-button type="primary" v-if="hasTreeOpertAuth" style="width: 180px; margin-top: 10px;margin-left:35px;" @click="addTopLevel">创建目录分类</el-button>
                    <el-button type="primary" v-if="hasTreeOpertAuth" @click="handleTreeSortDialog">调整排序</el-button>
                </el-row>
                <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="true" :highlight-current="true" :check-on-click-node="true" @node-click="handleNodeClick">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label">
                                <!-- <i v-if="data.FoldersOrFilesType == 1" class="el-icon-folder" style="margin-right: 4px;"></i> -->
                                <svg-icon v-if="data.FoldersOrFilesType == 1" icon-class="文件路径"></svg-icon>
                                {{ node.label }}
                            </span>
                            <span v-if="hasTreeOpertAuth" class="node-btn-area" @click.stop>
                                <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <!-- 目录（1）才可以添加页面 -->
                                        <el-dropdown-item v-show="data.FoldersOrFilesType == 1" command="create2">添加子页面</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level < 8 && data.FoldersOrFilesType == 1" command="create1">添加子目录</el-dropdown-item>
                                        <el-dropdown-item v-show="data.FoldersOrFilesType == 2" command="move">移动至</el-dropdown-item>
                                        <el-dropdown-item command="update">编辑</el-dropdown-item>
                                        <el-dropdown-item command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper">
                <div class="article-wrapper">
                    <div style="height: 100%; display: flex; align-items: center; justify-content: center;" v-if="!checkedNode || (checkedNode && checkedNode.FoldersOrFilesType == 1)">
                        请点击左侧文件查看详情
                    </div>
                    <template v-else>
                        <div class="article-title-wrapper" v-if="!selectedNodeKeyId">
                            <!-- <div class="article-title">
                                {{ pageModel == 'update' ? '编辑页面' : '页面详情' }}
                            </div> -->
                            <el-button type="primary" v-show="pageModel == 'detail'" @click="handleEdit()">编辑</el-button>
                            <app-button v-show="pageModel == 'update'" @click="handleCancel" :buttonType='2'></app-button>
                            <el-button type="primary" v-show="pageModel == 'update'" @click="handleSave">保存</el-button>
                        </div>
                        <div class="content" id='content'>
                            <div v-loading='listLoading'>
                                <!-- // isPostMgmtType 1 岗位技能管理    2： 公司制度管理     3 : 管理流程 -->
                                <div style="margin-bottom: 60px;" class="ql-snow">
                                    <div class="section-title" v-show="isPostMgmtType == 1">
                                        {{ isPostMgmtType == 1 ? '岗位要求' : '' }}
                                    </div>

                                    <div class="ql-editor" v-viewer v-show="pageModel == 'detail' && formData.Content1" v-html="formData.Content1"></div>
                                    <div v-show="pageModel == 'detail' && !formData.Content1">
                                        <no-data></no-data>
                                    </div>
                                    <div v-show="pageModel == 'update'" :class="{'sp-ql-editor': isPostMgmtType != 1}">
                                        <editor-bar ref="editor1" :value="formData.Content1" @edit="formData.Content1 = arguments[0]"></editor-bar>
                                    </div>
                                </div>
                                <div v-if="isPostMgmtType == 1" class="ql-snow">
                                    <div class="section-title">岗位技能</div>
                                    <div class="ql-editor" v-viewer v-show="pageModel == 'detail' && formData.Content2" v-html="formData.Content2"></div>
                                    <div v-show="pageModel == 'detail' && !formData.Content2">
                                        <no-data></no-data>
                                    </div>
                                    <div v-show="pageModel == 'update'">
                                        <editor-bar ref="editor2" :value="formData.Content2" @edit="formData.Content2 = arguments[0]"></editor-bar>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
    
    <customerServiceDepartmentEdit :isPostMgmtType="isPostMgmtType" :formData="formData" v-if="customerServiceDepartmentEditDialogFormVisible" :foldersOrFilesType='foldersOrFilesType' :dialogStatus="customerServiceDepartmentEditDialogStatus" :node="paramNode" :dialogFormVisible="customerServiceDepartmentEditDialogFormVisible" @closeDialog="customerServiceDepartmentEditCloseDialog" @saveSuccess="customerServiceDepartmentEditSaveSuccess"></customerServiceDepartmentEdit>

    <moveDialog :isPostMgmtType="isPostMgmtType" v-if="moveDialogVisible && paramNode" :moveId='paramNode.Id' :foldersOrFilesType='foldersOrFilesType' :dialogFormVisible="moveDialogVisible" @closeDialog="moveDialogClose" @saveSuccess="moveDialogSaveSuccess"></moveDialog>

    <!-- 排序调整 -->
    <tree-sort
        @closeDialog="closeTreeSortDialog"
        @saveSuccess="handleTreeSortSaveSuccess"
        :dialogFormVisible="dialogTreeSortFormVisible"
        :treeDatas="treeData"
        :defaultProps='defaultProps'
        :rowKey='rowKey'
        :businessType='9'
        :defaultExpandedKeys="[]"
        @reload="loadTreeData"
    >
    </tree-sort>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import commMixin from './mixins'
import { listToTreeSelect, treeFilter } from '@/utils'
import * as foldersFile from '@/api/foldersFile'
import * as afterVistTemplate from "@/api/afterSalesMgmt/afterVistTemplate";

import customerServiceDepartmentEdit from "./departmentEdit";
import editorBar from '@/components/QuillEditor/index'
import noData from "@/views/common/components/noData";
import moveDialog from './moveDialog'
// import { vars } from "../common/vars";
import { getUserInfo } from "@/utils/auth";
import treeSort from '../../common/treeSort'

export default {
    name: "post-mgmt",
    mixins: [indexPageMixin, commMixin],
    components: {
        customerServiceDepartmentEdit,
        editorBar,
        noData,
        moveDialog,
        treeSort,
    },
    props: {
        // 外部传入的 “isPostMgmt” 参数，和“isPostMgmt”做用相同，但是优先级高于“isPostMgmt”
        // 1 岗位技能管理    2： 公司制度管理     3 : 管理流程
        isPostMgmtType: {
            type: Number,
            default: 1
        },
        // 作为组件使用时   默认选中左侧树的id
        selectedNodeKeyId: {
            type: String,
            default: ''
        }
    },
    computed: {
        hasTreeOpertAuth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnMaintain') > -1
        },


    },
    created() {
        // this.getSalesman()
        this.loadTreeData();
        

    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val && val.Id) {
                    this.pageModel = 'detail';
                    this.getArticleDetail(val.Id)
                }
            },
        },
    },
    filters: {
        valFilter(val) {
            if (val) {
                return val;
            }
            return "无";
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "无";
        },

    },
    mounted() {},
    data() {
        return {

            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
            foldersOrFilesType: -1,
            /**树节点添加弹窗 */
            customerServiceDepartmentEditDialogFormVisible: false,
            customerServiceDepartmentEditDialogStatus: "create",

            moveDialogVisible: false,

            id: "",
            dialogStatus: "create",
            // dialogFormVisible: false,
            listLoading: false,

            formData: {
                Content1: '',
                Content2: '',


            },

            pageModel: 'detail',


            dialogTreeSortFormVisible: false,
            rowKey: 'Id',
        };
    },
    methods: {
        handleEdit() {
            this.pageModel = 'update'; 
            this.setEditorHeight();
        },
        setEditorHeight() {
            this.$nextTick(() => {
                if(this.isPostMgmtType == 1) {
                    this.$refs.editor1 && this.$refs.editor1.setHeight(400)
                    this.$refs.editor2 && this.$refs.editor2.setHeight(400)
                }else{
                    this.$refs.editor1 && this.$refs.editor1.setHeight(640)
                }
            })
        },
        handleNodeClick(data) {
            //目录（点击目录只能展开收起）
            if(data.FoldersOrFilesType == 1) {
                return false
            }
            this.checkedNode = data

            //回到文章顶部
            this.$nextTick(() => {
                document.getElementById('content').scrollTop = 0;
            })
        },
        handleCancel() {
            if (this.checkedNode) {
                this.getArticleDetail(this.checkedNode.Id)
            }else {
                this.formData = this.$options.data().formData;
            }
            this.pageModel = 'detail'
        },
        handleSave() {
            
            let postData = JSON.parse(JSON.stringify(this.formData))
            postData.Id = this.checkedNode.Id
            foldersFile.editContent(postData).then(res => {
                this.handleCancel()
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
            })

        },
        handleBeforeOpen() {
            this.$refs.tagSelectorRef.pers = []
            return true
        },


        /******************* 树事件 *******************/
        loadTreeData() {
            let _this = this;
            let paramData = {
                FoldersFilesBusinessType: this.isPostMgmtType,
            };

            _this.treeLoading = true
            foldersFile.getList(paramData).then(res => {
                _this.treeLoading = false
                let response = res || []
                // response.unshift({
                //     Id: "",
                //     Name: "全部",
                //     Level: 0,
                //     ParentId: null
                // });
                _this.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

                _this.$nextTick(() => {
                    let selNodeId = this.selectedNodeKeyId
                    if(_this.$refs.treeRef && selNodeId) {
                        _this.$refs.treeRef.setCurrentKey(selNodeId);
                        let node = _this.$refs.treeRef.getNode(selNodeId)
                        if(node) {
                            _this.checkedNode = node.data;
                        }
                    }
                });
                
                // if (_this.treeData && _this.treeData.length > 0) {
                //     if (
                //         !(
                //             _this.checkedNode &&
                //             response.find(t => {
                //                 return t.Id == _this.checkedNode.Id;
                //             })
                //         )
                //     ) {
                //         _this.checkedNode = _this.treeData[0];
                //     }
                // } else {
                //     _this.checkedNode = null;
                // }
                // if (_this.checkedNode) {
                //     _this.$nextTick(() => {
                //         if(_this.$refs.treeRef) {
                //             _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                //         }
                //     });
                // }
            }).catch(err => {
                _this.treeLoading = false
            });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.foldersOrFilesType = 1
            this.customerServiceDepartmentEditDialogStatus = "create";
            this.customerServiceDepartmentEditDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create1":
                    this.paramNode = data;
                    this.foldersOrFilesType = 1
                    this.customerServiceDepartmentEditDialogStatus = "create";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "create2":
                    if(this.pageModel == 'update') {
                        this.$message({
                            message: '请先保存当前页面内容后再尝试',
                            type: 'error'
                        })
                        return false
                    }
                    this.paramNode = data;
                    this.foldersOrFilesType = 2
                    this.customerServiceDepartmentEditDialogStatus = "create";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "update";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "delete":
                    this.handleDeleteDepartment(data);
                    break;
                case "move":
                    this.paramNode = data;
                    this.handleMoveDialog()
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteDepartment(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let postDatas = {
                    Ids: [data.Id],
                    FoldersFilesBusinessType: this.isPostMgmtType,
                }
                foldersFile
                    .del(postDatas)
                    .then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }
                        this.loadTreeData();
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
            });
        },
        /**地区弹窗保存成功 */
        customerServiceDepartmentEditSaveSuccess(isContinue) {
            if(isContinue !== true) {
                this.customerServiceDepartmentEditCloseDialog();
            }
            this.loadTreeData();
        },
        /**地区弹窗关闭 */
        customerServiceDepartmentEditCloseDialog() {
            this.customerServiceDepartmentEditDialogFormVisible = false;
        },
        handleSuccessExport() {},

        moveDialogSaveSuccess() {
            this.loadTreeData()
            this.moveDialogClose()
        },
        moveDialogClose() {
            this.moveDialogVisible = false
        },
        handleMoveDialog() {
            this.moveDialogVisible = true
        },
        // handleDialog(activeName, row) {
        //     if(row) {
        //         this.id = row.Id
        //     }
        //     this.dialogStatus = activeName;
        //     this.dialogFormVisible = true;
        // },
        // closeDialog() {
        //     this.dialogFormVisible = false;
        // },
        // handleSaveSuccess(_formData, isContinue) {

        //     // this.getList();
        //     if(!isContinue) {
        //         this.closeDialog();
        //     }
        // },

        getArticleDetail(id) {
            this.listLoading = true
            foldersFile.detail({id}).then(res => {
                this.listLoading = false

                this.formData = Object.assign({}, this.formData, res)


            }).catch(err => {
                this.listLoading = false
            })
        },


        // 排序
        handleTreeSortDialog() {
            this.dialogTreeSortFormVisible = true
        },
        closeTreeSortDialog() {
            this.dialogTreeSortFormVisible = false
        },
        handleTreeSortSaveSuccess() {
            this.closeTreeSortDialog();
        },


    },
};
</script>


<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            // top: 40px;
            top: 0;
            right: 0;
            bottom: 0;

            .product-list {
                width: 400px !important;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }

                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        height: 100%;
                        overflow: auto;
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 400px) !important;
                flex: 1;
                overflow-y: auto;
                .article-wrapper{
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    height: 100%;
                    overflow-y: hidden;
                    .article-title-wrapper, .content{
                        max-width: 1200px;
                        width: 100%;
                        padding: 0 10px;
                        margin: 0 auto;
                    }
                    .article-title-wrapper{
                        height: 40px;
                        display: flex;
                        align-items: center;
                        border-bottom: 1px solid #dcdfe6;
                        .article-title{
                            flex: 1;
                            font-size: 16px;
                            font-weight: 600;

                        }
                    }
                    .content{
                        flex: 1;
                        overflow-y: auto;
                        margin: 10px auto;
                        .section-title{
                            margin-bottom: 6px;
                            font-size: 16px;
                            font-weight: 600;
                        }
                    }
                }

                // .content {

                //     // padding: 10px;
                //     // padding-right: 0;
                //     .opt-wrapper {
                //         box-sizing: border-box;
                //         border-bottom: 1px solid #dcdfe6;
                //         padding-bottom: 10px;
                //     }

                //     .list {}
                // }
            }
        }
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}

.statisticsDivClass {
    width: 100%;
    height: 75px;
    border-radius: 8px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
    cursor: pointer;

    .statisticsChildDiv {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px 0 0 8px;
        width: 26%;
        height: 75px;
        background-color: rgba(255, 108, 96, 1);
        float: left;
    }

    .statisticsChildDiv2 {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 0 8px 8px 0;
        width: 74%;
        height: 75px;
        background-color: rgba(255, 255, 255, 1);
        float: left;

        .label1 {
            color: #999999;
            margin: 0;
            margin-top: 12px;
            font-weight: 500 !important;
        }

        div {
            margin-top: 5px;
        }

        .label2 {
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            font-size: 20px;
            color: $text-second-color;
            margin: 0;
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

.statisticsDivClass:hover {
    transform: translate(-3px, -3px);
    box-shadow: 0px 0px 3px 0px #dcdfe6;
}


.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

</style>
