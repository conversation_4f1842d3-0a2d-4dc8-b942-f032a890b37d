<template>
    <el-dialog
        v-el-drag-dialog
        class="dialog-mini"
        width="1000px"
        :title="dialogTitle"
        :visible.sync="isVisible"
        :before-close='handleClose'
        :close-on-click-modal='false'
        :append-to-body='true'
    >
        <el-form
            :rules="rules"
            ref="dataForm"
            :model="formObj"
            label-position="right"
            label-width="100px"
            style="max-height: 600px; overflow-y: auto;"
        >
            <el-form-item :label="'所属分类'" prop="NewsClassId">
                <el-select v-model="formObj.NewsClassId" placeholder="">
                    <el-option v-for="item in categories" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            </el-form-item>
            <el-form-item :label="'发布对象'" prop="PublishObject">
                <el-checkbox-group v-model="formObj.PublishObject" :min="1">
                    <el-checkbox :label="1" key="1">官网</el-checkbox>
                    <el-checkbox :label="2" key="2">PC</el-checkbox>
                    <el-checkbox :label="3" key="3">APP</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="'上传封面图片'" prop="Image">
                <app-upload-file key="1" v-show="ifExistsPoster(1)" style="display: inline-block;" :max='1' :fileSize='10 * 1024 * 1024' :minFileSize='10 * 1024' :value='getFileListSource(1)' @change='handleUpChange1' :preview='true'></app-upload-file>
                <app-upload-file key="2" v-show="ifExistsPoster(2)" style="display: inline-block;" :max='1' :fileSize='10 * 1024 * 1024' :minFileSize='10 * 1024' :value='getFileListSource(2)' @change='handleUpChange2' :preview='true'></app-upload-file>
                <app-upload-file key="3" v-show="ifExistsPoster(3)" style="display: inline-block;" :max='1' :fileSize='10 * 1024 * 1024' :minFileSize='10 * 1024' :value='getFileListSource(3)' @change='handleUpChange3' :preview='true'></app-upload-file>
            </el-form-item>
            <el-form-item :label="''" prop="Image">
                图片尺寸要求：
                <ul class="tip">
                    <li>
                        1. PC端封面图片宽/高比为1.2 : 1（例：宽480 * 高400）;
                    </li>
                    <li>
                        2. 为了获取较好体验，上传封面图片建议不要超过140kb;
                    </li>
                </ul>
            </el-form-item>
            <el-form-item :label="'标题'" prop="Title">
                <el-input v-model="formObj.Title"></el-input>
            </el-form-item>
            <el-form-item :label="'排序'" prop="OrderIndex">
                <el-input v-model.number="formObj.OrderIndex" maxlength="9"></el-input>
            </el-form-item>
            <el-form-item :label="'说明'" prop="Explain">
                <el-input v-model="formObj.Explain"></el-input>
            </el-form-item>
            <el-form-item :label="'是否推荐'">
                <template>
                    <el-radio v-model="formObj.IsRecommend" :label="true">推荐</el-radio>
                    <el-radio v-model="formObj.IsRecommend" :label="false">不推荐</el-radio>
                </template>
            </el-form-item>
            <el-form-item :label="'是否显示'">
                <template>
                    <el-radio v-model="formObj.IsStatus" :label="true">有效</el-radio>
                    <el-radio v-model="formObj.IsStatus" :label="false">无效</el-radio>
                </template>
            </el-form-item>
            <el-form-item :label="'内容'">
                <!-- <el-input v-model="formObj.Contents"></el-input> -->
                <Ueditor ref="ue" v-bind:content="formObj.Contents" @ready="ueReady"></Ueditor>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button size="mini" @click="cancel">取消</el-button>
            <el-button size="mini" :loading="postLoading" type="primary" @click="createData">确认</el-button>
            <!-- <el-button size="mini" v-else type="primary" :loading="postLoading" @click="createData">确认</el-button> -->
        </div>
    </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import AppUploadFile from '@/components/AppUploadFile'
import Ueditor from '@/components/Ueditor'
import * as news from '@/api/news'
import * as common from '@/api/common'
export default {
    name: 'news-edit',
    props: {
        //编辑还是新增(create: 新增; update: 编辑)
        dialogStatus: {
            required: true,
            type: String,
        },
        //新增(编辑)弹框是否显示
        dialogFormVisible: {
            required: true,
            type: Boolean
        },
        id: {
            required: true,
            default: ''
        },
        categories: Array
    },
    watch: {
        // dialogFormVisible(isVisible) {
        //     this.isVisible = isVisible
        // },
        dialogStatus(newStatus) {
            this.dialogType = newStatus
        },
        isVisible(newval) {
            if(this.dialogType == 'create'){
                this.resetTemp()
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules)
        this.isVisible = this.dialogFormVisible
        this.dialogType = this.dialogStatus
    },
    data() {
        return {
            postLoading: false,
            posterTypes: [1, 2, 3], //1：官网；2：PC；3：APP
            isVisible: false,
            dialogType: '',
            rules: {
                NewsClassId: { fieldName: '所属分类', rules: [{required: true}]},
                Title: { fieldName: '标题', rules: [{required: true}, {max: 50}]},
                OrderIndex: { fieldName: '排序', rules: [{required: true}, {type: 'number'}]},
                Explain: { fieldName: '说明', rules: [{required: true}, {max: 200}]},
                PublishObject: { fieldName: '发布对象', rules: [{required: true}] }
                // NewsClassId: [{ required: true, message: '所属分类不能为空', trigger: 'blur' }],
                // Title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
                // Sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
                // Explain: [{ required: true, message: '说明不能为空', trigger: 'blur' }],
            },
            fileList1: [],
            fileList2: [],
            fileList3: [],
            formObj: {
                NewsContentId: '', //id编号
                NewsClassId: '', //所属分类
                Image: '',//封面图片
                Title: '',//标题
                OrderIndex: 0,//排序
                Explain: '',//说明
                IsRecommend: true,//是否推荐
                IsStatus: true,//是否显示
                Contents: '',//内容详情
                PublishObject: [2]
            },
        }
    },
    methods: {
        ueReady() {
            if(this.id){
                this.getNewsInfo(this.id)
            }
            // this.isVisible = this.dialogFormVisible
            // this.dialogType = this.dialogStatus

        },
        handleUpChange1(imgs) {
            if(imgs && imgs.length > 0){
                this.formObj.Image = imgs[0].Path
                this.formObj.ImageId = imgs[0].Id
            }else{
                this.formObj.Image = ''
                this.formObj.ImageId = ''
            }
        },
        handleUpChange2(imgs) {
            if(imgs && imgs.length > 0){
                this.formObj.PcImage = imgs[0].Path
                this.formObj.PcImageId = imgs[0].Id
            }else{
                this.formObj.PcImage = ''
                this.formObj.PcImageId = ''
            }
        },
        handleUpChange3(imgs) {
            if(imgs && imgs.length > 0){
                this.formObj.AppImage = imgs[0].Path
                this.formObj.AppImageId = imgs[0].Id
            }else{
                this.formObj.AppImage = ''
                this.formObj.AppImageId = ''
            }
        },
        createData() {
            this.postLoading = true
            this.$refs['dataForm'].validate((valid) => {
                if(!valid) {
                    this.postLoading = false
                }
                let validatefileList = true //验证图集是否正常（选择发布对象，必须为其上传图片）

                //没有勾选发布对象，清空对应的已存在的封面图片
                this.posterTypes.forEach(o => {
                    if(!(this.formObj.PublishObject.findIndex(i => i == o) > -1)){
                        if(o == 1){
                            this.formObj.Image = ''
                            this.formObj.ImageId = ''
                        }else if(o == 2){
                            this.formObj.PcImage = ''
                            this.formObj.PcImageId = ''
                        }else if(o == 3){
                            this.formObj.AppImage = ''
                            this.formObj.AppImageId = ''
                        }
                    }
                })

                //已勾选的发布对象，验证对应的封面图片是否为空
                this.formObj.PublishObject.forEach(item => {
                    let _errTip = item == 1 ? '官网' : item == 2 ? 'PC' : 'APP'
                    if(this.ifExistsPoster(item) && this.getFileListSource(item) == 0){
                        this.postLoading = false
                        this.$notify({
                            title: '提示',
                            message: `必须上传${_errTip}封面图片`,
                            type: 'error',
                            duration: 2000
                        })
                        validatefileList = false
                        if(!validatefileList) return;
                    }
                    if(item == 1){
                        this.formObj.Image = this.fileList1[0].Path
                    }else if(item == 2){
                        this.formObj.PcImage = this.fileList2[0].Path
                    }else if(item == 3){
                        this.formObj.AppImage = this.fileList3[0].Path
                    }
                })

                //格式和封面验证通过
                if (valid && validatefileList) {
                    let tempContents = this.$refs.ue.getObj().content
                    const tempData = Object.assign({}, this.formObj)
                    tempData.Contents = tempContents
                    if(tempData && tempData.PublishObject.length > 0){
                        tempData.PublishObject = tempData.PublishObject.sort().join(',')
                    }

                    let _res = null
                    if(this.dialogType == 'create'){
                        delete tempData.NewsContentId
                        _res = news.add(tempData)
                    }else{
                        _res = news.update(tempData)
                    }
                    if(_res){
                        _res.then(() => {
                            this.postLoading = false
                            this.$notify({
                                title: '成功',
                                message: '创建成功',
                                type: 'success',
                                duration: 2000
                            })
                            this.$emit('saveSuccess', this.formObj)
                        }).catch(err => {
                            this.postLoading = false
                        })
                    }
                }else{
                    this.postLoading = false
                }
            })
        },
        getNewsInfo(id) {
            news.detail({newid: id}).then((res) => {
                this.formObj = res
                this.fileList1 = []
                this.fileList2 = []
                this.fileList3 = []
                if(this.formObj && this.formObj.Image){
                    this.fileList1.push({ Id: this.formObj.ImageId, Path: this.formObj.Image })
                }
                if(this.formObj && this.formObj.PcImage){
                    this.fileList2.push({ Id: this.formObj.PcImageId, Path: this.formObj.PcImage })
                }
                if(this.formObj && this.formObj.AppImage){
                    this.fileList3.push({ Id: this.formObj.AppImageId, Path: this.formObj.AppImage })
                }
                if(res.PublishObject){
                    let types = res.PublishObject.split(',').map(t => parseInt(t))
                    res.PublishObject = types
                }else{
                    this.formObj.PublishObject = []
                }
            })
        },
        resetTemp () {
            this.formObj = {
                NewsContentId: '', //id编号
                NewsClassId: '', //所属分类
                Image: '',//封面图片
                Title: '',//标题
                OrderIndex: 0,//排序
                Explain: '',//说明
                IsRecommend: true,//是否推荐
                IsStatus: true,//是否显示
                Contents: '',//内容详情
                PublishObject: [2]
            }
        },
        cancel() {
            this.$emit('closeDialog')
        },
        handleClose(done) {
            this.$emit('closeDialog')
            done()
        },
        ifExistsPoster(posterTyper){
            if(this.formObj && this.formObj.PublishObject){
                return this.formObj.PublishObject.findIndex(t => t == posterTyper) > -1
            }
            return false
        },
        getFileListSource(posterTyper) {
            if(posterTyper == 1){
                return this.fileList1
            }else if(posterTyper == 2){
                return this.fileList2
            }else if(posterTyper == 3){
                return this.fileList3
            }
            return []
        }
    },
    computed: {
        dialogTitle() {
            if(this.dialogType == 'create')
                return '添加';
            else if(this.dialogType == 'update')
                return '编辑'
            else
                return ''
        }
    },
    components: {
        Ueditor,
        AppUploadFile,
    },
    directives: {
        elDragDialog,

    }
}
</script>

<style scoped>
/* .img-wrap{
    position: relative;
    width: 106px;
    height: 106px;
    padding: 2px;
    border: 1px dotted #cdcdcd;
    cursor: pointer;
}

.img-wrap .img{
    width: 100px;
    height: 100px;
}

.file-btn-del {
    color: #999;
    position: absolute;
    font-size: 18px;
    top: -6px;
    right: -6px;
}

.el-icon-remove:hover{
    color: red;
} */

.tip{
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.tip li{
    line-height: 20px;
}

</style>

