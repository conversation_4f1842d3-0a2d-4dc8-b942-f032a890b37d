<template>
  <!-- 金蝶公司项目选择 -->
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="multiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="_listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      :disabledList="disabledList"
      :requestMethod="requestMethod"
      :width="1200"
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form
          label-width="80px"
          :items="tableSearchItems"
          @onSearch="handleFilter"
          @onReset="onResetSearch"
        >
          <template slot="keywords">
            <el-input
              style="width: 300px"
              placeholder="搜索关键词"
              clearable
              v-model="listQueryParams.keywords"
            />
          </template>
        </app-table-form>
      </template>
    </listSelector>
  </div>
</template>

<script>
import listSelector from "@/views/common/listSelector";
import { serviceArea } from "@/api/serviceArea";

export default {
  name: "issue-selector",
  components: {
    listSelector,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => [],
    },
    disabledList: {
      type: Array,
      default: () => [],
    },
    companyId: {
      type: String,
      default: "",
    },
  },
  watch: {
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList: {
      handler(val) {
        if (val && val.length > 0) {
          this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
        } else {
          this.listSelectorCheckedData = [];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      // 组件附加条件
      listQueryParams: {
        keywords:''
      },
      tableSearchItems: [{ prop: "keywords", label: "关键词" }],
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/Kingdee/GetKingdeeProject",
      listSelectorTitle: "选择项目",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",
      requestMethod: "GET",
      listSelectorColumnData: [
        { attr: { prop: "KingdeeName", label: "名称" } },
        { attr: { prop: "KingdeeNumber", label: "编号", width: 200 } },
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  computed: {
    _listQueryParams() {
      return {
        ...this.listQueryParams,
        kingdeeDepartmentId: this.companyId,
      };
    },
  },
  methods: {
    handleFilter() {
      this.$refs.listSelector.getDatas();
    },
    onResetSearch() {
      Object.assign(this.listQueryParams, this.$options.data().listQueryParams);
      this.handleFilter();
    },
    listSelectorCloseDialog() {
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      this.$emit("changed", JSON.parse(JSON.stringify(data)));
      this.listSelectorCloseDialog();
    },
    changeDate(value) {
      this.listQueryParams.CreateStartTime = value?.[0] || null;
      this.listQueryParams.CreateEndTime = value?.[1] || null;
    },
  },
};
</script>

<style lang="scss" scoped>
.imp-item-wrapper {
  display: flex;
  max-height: 200px;
  overflow-y: auto;
  .imp-number {
    width: 120px;
  }
  .imp-name {
    width: 180px;
    padding: 0 10px;
  }
  .imp-number,
  .imp-name {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
