<template>
  <!-- 通用下拉选择器 -->
  <div class="emp_common_selector">
    <div class="select_container" :style="isShowSelectIcon ? 'padding-right: 30px;' : ''">
      <el-select
        ref="selectRef"
        v-model="currentSelect"
        :placeholder="disabled ? '' : '请选择'"
        clearable
        multiple
        :multiple-limit="limit"
        :filterable="filterable"
        :value-key="valueKey"
        :disabled="disabled"
        :loading="loading"
        @clear="clearSelect"
        @change="changeSelect"
        :class="{
          highlight_tag: isClickTag,
        }"
        class="select"
        style="width:100%"
      >
        <el-option
          v-for="item in options"
          :key="item[valueKey]"
          :label="item[label]"
          :value="item"
          :disabled="hideTagCloseIdList.includes(item[valueKey])"
        />
      </el-select>
      <div
        class="select_icon"
        :class="{ is_disabled: disabled }"
        v-if="isShowSelectIcon"
        @click="handleSelectIcon"
      >
        <slot name="selectIcon">
          <svg-icon icon-class="plus" />
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  /**
   * @desc 通用下拉选择器
   * @event {Function} handleTag 点击标签
   * @event {Function} handleSelectIcon 点击右侧icon
   * @event {Function} change 数据改变钩子
   *
   * @slot selectIcon 右侧icon
   */
  props: {
    // 选中数据(支持value和v-model)
    value: {
      type: Array,
      default: () => [],
    },
    // 请求接口
    requestApi: {
      type: Function,
      default: null,
    },
    // 请求参数
    requestParams: {
      type: Object,
      default: () => ({}),
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否可以点击标签(会高亮)
    isClickTag: {
      type: Boolean,
      default: true,
    },
    // 是否显示右侧icon
    isShowSelectIcon: {
      type: Boolean,
      default: true,
    },
    // 是否可以搜索
    filterable: {
      type: Boolean,
      default: true,
    },
    // 最多可以选择的项目数，0不限制
    limit: {
      type: Number,
      default: 0,
    },
    // 隐藏标签关闭项
    hideTagCloseIdList: {
      type: Array,
      default: () => [],
    },
    label: {
      type: String,
      default: "Name",
    },
    // 唯一标识
    valueKey: {
      type: String,
      default: "Id",
    },
  },
  data() {
    return {
      // 当前选择
      currentSelect: [],
      // 可选项
      options: [],
      loading: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.currentSelect = this.$_.cloneDeep(val);
        this.onClickTags()
        this.hideTagClose();

        if (!this.disabled && this.requestApi) {
        } else {
          this.options = this.$_.cloneDeep(this.currentSelect);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    if (!this.disabled && this.requestApi) {
      this.getSelectOptions();
    } else {
      // 回显用
      this.options = this.$_.cloneDeep(this.currentSelect);
    }
  },
  mounted() {
    this.onClickTags();
  },
  methods: {
    // 获取可选项
    getSelectOptions() {
      this.loading = true;
      this.requestApi(this.requestParams)
        .then(res => {
          this.options = res;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 选择改变回调
    changeSelect(value) {
      console.log("选择改变回调", value);
      this.$emit("input", value);
      this.$emit("change", value);

      // 不加延迟会导致获取不到最新dom
      setTimeout(() => {
        this.hideTagClose();
        this.onClickTags();
      }, 100);
    },
    // 给标签添加点击事件
    onClickTags() {
      if (!this.isClickTag) return;
      this.$nextTick(() => {
        const element = this.$refs.selectRef.$el;
        const tagDom = element.querySelectorAll(".el-tag");
        tagDom.forEach((item, index) => {
          item.onclick = e => {
            e.stopPropagation();
            const innerText = e.target.innerText;
            if (this.currentSelect[index][this.label] === innerText) {
              const clickItem = this.currentSelect[index];
              console.log("点击标签", clickItem);
              this.$emit("handleTag", clickItem);
            }
          };
        });
      });
    },
    // 隐藏标签关闭按钮
    hideTagClose() {
      this.$nextTick(() => {
        const element = this.$refs.selectRef.$el;
        const tagDom = element.querySelectorAll(".el-tag__close");
        tagDom.forEach((dom, index) => {
          const item = this.currentSelect[index];
          if (this.hideTagCloseIdList.includes(item[this.valueKey])) {
            dom.style.display = "none";
          }
        });
      });
    },
    // 点击右侧icon
    handleSelectIcon() {
      if (this.disabled) return;
      this.$emit("handleSelectIcon");
    },
    // 清空选择框
    clearSelect() {
      // 禁止关闭的列表不清空
      const hideTagCloseList = this.value.filter(t => {
        return this.hideTagCloseIdList.includes(t[this.valueKey]);
      });
      this.currentSelect = this.$_.cloneDeep(hideTagCloseList);
    },
  },
};
</script>

<style lang="scss" scoped>
.emp_common_selector {
  .select_container {
    display: flex;
    align-items: center;
    position: relative;
    /deep/ .select {
      &.highlight_tag {
        .el-tag {
          color: $color-primary;
          cursor: pointer;
          user-select: none;
        }
      }
      .el-input__inner {
        border-radius: $border-radius-base 0 0 $border-radius-base !important;
      }
    }
    .select_icon {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      color: #c9cdd4;
      font-size: 14px;
      background-color: #fff;
      cursor: pointer;
      user-select: none;
      border: 1px solid $border-color-light;
      border-left: none;
      border-radius: 0 $border-radius-base $border-radius-base 0;
      &.is_disabled {
        cursor: not-allowed;
        border-color: #edeff3;
        background-color: #fafbfc;
      }
    }
  }
}
</style>
