<template>
  <div v-if="visible">
    <div class="conditionArea-wrap clearfix">
      <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
        <template slot="TaskKeyWord">
          <el-input style="width: 100%;" placeholder="编号、标题、内容" v-model="listQuery.TaskKeyWord"></el-input>
        </template>
        <template slot="TaskStateType">
          <el-select clearable v-model="listQuery.TaskStateType" placeholder="" style="width: 100%;">
            <el-option v-for="item in taskStateTypes" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template slot="TaskPriorityType">
          <el-select clearable v-model="listQuery.TaskPriorityType" placeholder="" style="width: 100%;">
            <el-option v-for="item in taskPriorityTypes" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template slot='TaskRelationEmployeeId'>
          <emp-selector :multiple='false' :showType='2' key='service-users' :list='listQuery.TaskRelationEmployeeId' @change='handleChangeUsers'></emp-selector>
        </template>
        <template slot="TaskStartDateTime">
          <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss' v-model="listQuery.TaskStartDateTime" type="datetime" placeholder=""></el-date-picker>
        </template>
        <template slot="TaskEndDateTime">
          <el-date-picker style="width: 100%;" format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss' v-model="listQuery.TaskEndDateTime" type="datetime" placeholder=""></el-date-picker>
        </template>
        <template slot="other-btns">
          <el-button type="success" @click="handleSave">确认</el-button>
        </template>
      </app-table-form>
    </div>
    <el-table border fit :data="tabDatas" style="width: 100%" v-loading="listLoading" @selection-change='rowSelectionChanged' ref="mainTable2" :highlight-current-row='!multiple' @current-change='currentChanged' max-height="500">
      <!-- -->
      <el-table-column type="selection" width="55" v-if="multiple"></el-table-column>
      <el-table-column type="index" :index="indexMethod" label="编号"></el-table-column>
      <el-table-column prop="TaskSn" label="任务编号"></el-table-column>
      <el-table-column prop="TaskTitle" label="任务标题"></el-table-column>
      <el-table-column prop="TaskStartDateTime" label="任务开始时间">
        <template slot-scope="scope">
          {{ scope.row.TaskStartDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column prop="TaskEndDateTime" label="任务结束时间">
        <template slot-scope="scope">
          {{ scope.row.TaskEndDateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />

  </div>
</template>

<script>
import * as tasks from '@/api/task'
import indexPageMixin from '@/mixins/indexPage'
import { getUserInfo } from '@/utils/auth'
import { taskPriorityTypes, taskResource, taskTypes, taskStateTypes } from '../task/enums'
import EmpSelector from './empSelector'
import dayjs from 'dayjs'

export default {
  name: 'task-list',
  mixins: [indexPageMixin],
  components: {
    EmpSelector,
  },
  props: {
    existsUsers: {
      type: Array,
      default: () => {
        return []
      }
    },
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    condition: {
      type: Object,
      default: null
    },
    //过滤任务集合（搜索的任务里面不包含在该集合中的）
    disableList: {
      type: Array,
      default: []
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.checkedUsers = JSON.parse(JSON.stringify(this.existsUsers))
          if (this.condition) {
            this.listQuery = Object.assign({}, this.listQuery, this.condition)
          }
          this.getList()
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      checkedUsers: [],
      tableSearchItems: [
        { prop: 'TaskKeyWord', label: '任务' },
        { prop: 'TaskStateType', label: '任务状态' },
        { prop: 'TaskPriorityType', label: '紧急程度' },
        { prop: 'TaskRelationEmployeeId', label: '任务相关人' },
        { prop: 'TaskStartDateTime', label: '开始时间' },
        { prop: 'TaskEndDateTime', label: '结束时间' },
      ],
      taskStateTypes: taskStateTypes,
      taskPriorityTypes: taskPriorityTypes,
      multipleSelection: [], // 列表checkbox选中的值
      total: 0,
      listLoading: false,
      listQuery: { // 查询条件
        TaskKeyWord: '',//任务关键字
        TaskPriorityType: '',//紧急程度
        TaskStateType: '',//任务状态
        TaskRelationEmployeeId: [], //任务相关人
        TaskListCategoryType: 2,
        MyTaskManagementType: 2,
      },
      tabKey: 0,
      tabDatas: [],
    }
  },
  methods: {
    onResetSearch() {
      this.listQuery = {
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize
      };
      if (this.condition) {
        this.listQuery = Object.assign({}, this.listQuery, this.condition)
      }
    },
    currentChanged(currentRow, oldCurrentRow) {
      if (!this.multiple && currentRow) { //某页选中一个用户后，点击翻页会触发该事件，所以要加该判断
        this.multipleSelection = [currentRow]
        this.checkedUsers = [currentRow]
      }
    },
    //当表格行中所有 checkbox 选中状态项改变时（返回所有选中的行数据）
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
      this.tabDatas.forEach(r => {
        let idx = this.checkedUsers.findIndex(u => u.TaskDetailId == r.TaskDetailId)
        if (idx > -1) {
          this.checkedUsers.splice(idx, 1)
        }
      })

      if (rows && rows.length > 0) {
        rows.forEach(r => {
          if (!this.checkedUsers.some(s => s == r.TaskDetailId)) {
            this.checkedUsers.push(r)
          }
        })
      }
    },
    handleChangeUsers(users) {
      if (users && users.length > 0) {
        this.listQuery.TaskRelationEmployeeId = [users[0]]
      } else {
        this.listQuery.TaskRelationEmployeeId = []
      }
    },
    getList() {
      this.listLoading = true
      if (this.listQuery.TaskStartDateTime && this.listQuery.TaskEndDateTime) {
        let startDate = dayjs(this.listQuery.TaskStartDateTime)
        let endDate = dayjs(this.listQuery.TaskEndDateTime)
        if (!endDate.isAfter(startDate)) {
          this.$message.error({
            title: '提示',
            message: '开始时间必须小于结束时间',
            duration: 2000
          })
          return
        }
      }

      this.listLoading = true
      let condition = JSON.parse(JSON.stringify(this.listQuery))

      if (condition.TaskRelationEmployeeId && condition.TaskRelationEmployeeId.length > 0) {
        condition.TaskRelationEmployeeId = condition.TaskRelationEmployeeId[0].EmployeeId
      } else {
        condition.TaskRelationEmployeeId = ''
      }

      if (!condition.TaskPriorityType) {
        condition.TaskPriorityType = 0
      }
      if (!condition.TaskStateType) {
        condition.TaskStateType = 0
      }

      condition.CurrentUserID = getUserInfo().userid
      condition.CurrentEmployeeID = getUserInfo().employeeid
      condition.TaskDetailIdList = this.disableList

      tasks.getList(condition).then(response => {
        this.tabDatas = response.Items
        this.total = response.Total
        this.listLoading = false
        this.setCheckedusers()
      })
    },
    handleFilter() {
      this.listQuery.PageIndex = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page
      this.listQuery.PageSize = val.size

      this.getList()
    },
    setCheckedusers() {

      if (!this.multiple) { //单选
        if (this.checkedUsers.length > 0) {
          let checkUser = this.tabDatas.find(p => p.TaskDetailId == this.checkedUsers[0].TaskDetailId)
          checkUser && this.$refs.mainTable2.setCurrentRow(checkUser);
        }
      } else { //多选

        if (this.checkedUsers.length == 0) {
          this.$refs.mainTable2.clearSelection();
        } else {

          let checkedUsers = this.tabDatas.filter(s => this.checkedUsers.map(u => u.TaskDetailId).some(o => o == s.TaskDetailId)) || []
          checkedUsers.forEach(u => {
            this.$nextTick(() => {
              if (this.$refs.mainTable2) {
                this.$refs.mainTable2.toggleRowSelection(u)
              }
            })
          })
        }
      }
    },
    handleSave() {
      this.$emit('changed', this.checkedUsers)
      // this.userSelectorDlg = false

    },

    indexMethod(index) {
      return (index += 1) + this.listQuery.PageSize * (this.listQuery.PageIndex - 1)
    },
  }
}
</script>


<style scoped>
.title,
.sub-title {
  margin-bottom: 10px;
}

.sub-title {
  display: flex;
  line-height: 28px;
}

.sub-title-info {
  flex: 1;
}

.sub-title-btns {
  margin-right: 10px;
}

.tab-users {
  max-height: 480px;
  padding: 10px;
  overflow-y: scroll;
}

.tab-item-wrap {
  float: left;
  width: 20%;
  padding: 5px;
  position: relative;
}

.tab-item {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  height: 80px;
  width: 100%;
  border: 1px solid #dcdfe6;
  padding: 10px;
}

.opt {
  cursor: pointer;
  font-size: 28px;
  color: #dcdfe6;
  text-align: center;
  line-height: 80px;
  vertical-align: middle;
  padding: 0;
}

.conditionArea-wrap {
  padding: 0 10px;
}

.btns-area {
  text-align: left;
  padding: 10px 0;
  padding-right: 10px;
}

.file-btn-del {
  background: transparent;
  display: block;
  cursor: pointer;
  position: absolute;
  font-size: 18px;
  top: -3px;
  right: -3px;
}

.file-btn-del:hover {
  transition: all 0.3s;
  color: #f56c6c;
}

.color-danger,
.color-danger:hover {
  color: #f56c6c;
}
</style>