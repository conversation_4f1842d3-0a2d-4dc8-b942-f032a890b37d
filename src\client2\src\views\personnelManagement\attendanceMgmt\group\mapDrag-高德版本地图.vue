<!--
  描述：拖放地图组件，默认尺寸是 500 * 300

  接收属性参数：
    lat: 纬度
    lng: 经度

  自定义事件：
    drag: 拖放完成事件

  示例：
    <mapDrag @drag="dragMap" lat="22.574405" lng="114.095388"></mapDrag>
-->
<template>
  <div class="m-map">
    <div class="search" v-if="placeSearch">
      <input type="text" placeholder="请输入关键字" v-model="searchKey">
      <button type="button" @click="handleSearch">搜索</button>
      <button type="button" @click="handleClear">清除</button>
      <div id="js-result" v-show="searchKey" class="result"></div>
    </div>
    <div id="js-container" class="map">正在加载数据 ...</div>
  </div>
</template>

<script>
import remoteLoad from './remoteLoad.js'
// import { MapKey, MapCityName } from '@/config/config'
export default {
  props: ['lat', 'lng', 'range'],
  data() {
    return {
      map: null,
      searchKey: '',
      placeSearch: null,
      circle: null,
      dragStatus: false,
      AMapUI: null,
      AMap: null,
      MapKey: 'de13aada309258f42c9e4e192fd0a2d9',
      MapCityName: '深圳',
    }
  },
  watch: {
    searchKey() {
      if (this.searchKey === '') {
        this.placeSearch.clear()
      }
    },
    range: {
      handler(val) {
        if (val) {
          if (this.range && this.map) {
            this.map.remove(this.circle)
            this.circle = new AMap.Circle({
              center: [this.lng, this.lat],// 圆心位置
              radius: this.range, //半径
              strokeColor: "#85BCFD", //线颜色
              strokeOpacity: 0.7, //线透明度
              strokeWeight: 3,    //线宽
              fillColor: "#1791FC", //填充色
              fillOpacity: 0.3,//填充透明度
              zIndex: 50
            });
            console.log('值变化圆');
            console.log(this.circle);
            console.log(this.range);
            this.circle.setMap(this.map);//显示圆圈
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 搜索
    handleSearch() {
      if (this.searchKey) {
        this.placeSearch.search(this.searchKey)
      }
    },
    //清除结果
    handleClear() {
      this.searchKey = ''
      this.placeSearch.clear()
    },
    // 实例化地图
    initMap() {
      // 加载PositionPicker，loadUI的路径参数为模块名中 'ui/' 之后的部分
      let AMapUI = this.AMapUI = window.AMapUI
      let AMap = this.AMap = window.AMap
      AMapUI.loadUI(['misc/PositionPicker'], PositionPicker => {
        let mapConfig = {
          zoom: 16,
          cityName: this.MapCityName
        }
        if (this.lat && this.lng) {
          mapConfig.center = [this.lng, this.lat]
        }
        this.map = new AMap.Map('js-container', mapConfig)
        if (this.circle) {
          this.map.remove(this.circle)
        }
        
        // 加载地图搜索插件
        AMap.service('AMap.PlaceSearch', () => {
          this.placeSearch = new AMap.PlaceSearch({
            pageSize: 5,
            pageIndex: 1,
            citylimit: false,
            city: this.MapCityName,
            map: this.map,
            panel: 'js-result'
          })

        })
        // 启用工具条
        let that = this
        AMap.plugin(['AMap.ToolBar'], function () {
          that.map.addControl(new AMap.ToolBar({
            position: 'RB'
          }))
        })

        // 创建地图拖拽
        let positionPicker = new PositionPicker({
          mode: 'dragMap', // 设定为拖拽地图模式，可选'dragMap'、'dragMarker'，默认为'dragMap'
          map: this.map // 依赖地图对象
        })
        this.circle = new AMap.Circle({
          center: [this.lng, this.lat],// 圆心位置
          radius: this.range, //半径
          strokeColor: "#85BCFD", //线颜色
          strokeOpacity: 0.7, //线透明度
          strokeWeight: 3,    //线宽
          fillColor: "#1791FC", //填充色
          fillOpacity: 0.3,//填充透明度
          zIndex: 50
        });
        console.log('初始化圆');
        console.log(this.circle);
        console.log(this.range);
        this.circle.setMap(this.map);//显示圆圈
        // 启动拖放
        positionPicker.start()

        // 地图拖拽时，隐藏圆圈
        this.map.on('dragstart', () => {
          this.circle.hide()
        })
        this.map.on('draging', () => {
          this.circle.hide()
        })
        this.map.on('dragend', () => {
          this.circle.show()
        })

        positionPicker.on('fail', function (positionResult) {
          // 海上或海外无法获得地址信息
        })

        // 拖拽完成发送自定义 drag 事件
        positionPicker.on('success', positionResult => {
          this.circle.setCenter([positionResult.position.lng, positionResult.position.lat])
          // 过滤掉初始化地图后的第一次默认拖放
          if (!this.dragStatus) {
            this.dragStatus = true
          }
          // } else {
          //   this.$emit('drag', positionResult)
          // }
          positionResult.range = this.range

          console.log(JSON.stringify(positionResult))
          
          this.$emit('drag', positionResult)
        })
      })
    }
  },
  async created() {
    // 已载入高德地图API，则直接初始化地图
    if (window.AMap && window.AMapUI) {
      this.initMap()
      // 未载入高德地图API，则先载入API再初始化
    } else {
      let load1 = remoteLoad(`https://webapi.amap.com/maps?v=1.4.15&key=${this.MapKey}`)
      let load2 = remoteLoad('https://webapi.amap.com/ui/1.0/main.js')
      Promise.all([load1, load2]).then(res => {
        setTimeout(() => {
          this.initMap()
        }, 100);
      })
    }
  }
}
</script>

<style lang="css">
.m-map {
  min-width: 500px;
  min-height: 300px;
  position: relative;
}
.m-map .map {
  width: 100%;
  height: 100%;
}
.m-map .search {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 350px;
  z-index: 1;
}
.m-map .search input {
  width: 180px;
  border: 1px solid #ccc;
  line-height: 20px;
  padding: 5px;
  outline: none;
}
.m-map .search button {
  line-height: 26px;
  background: #fff;
  border: 1px solid #ccc;
  width: 50px;
  text-align: center;
  margin-left: 5px;
}
.m-map .result {
  max-height: 300px;
  overflow: auto;
  margin-top: 10px;
}
</style>
