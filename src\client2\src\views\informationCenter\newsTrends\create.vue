<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="636">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth">

          <el-form-item label="新闻类型" prop="NewsType">
            <el-select class="sel-ipt" v-model="formData.NewsType" placeholder="请选择新闻类型">
              <el-option v-for="item in newsTypeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="封面图片" prop="NewsCoverPath">
            <app-upload-file :max='1' :fileSize='1024 * 1024 * 2' :value='fileList' @change='handleUpChange' :preview='true'></app-upload-file>
          </el-form-item>

          <el-form-item label="是否显示" prop="IsShow">
            <el-radio v-model="formData.IsShow" :label="true">有效</el-radio>
            <el-radio v-model="formData.IsShow" :label="false">无效</el-radio>
          </el-form-item>

          <el-form-item label="标题" prop="NewsTitle">
            <el-input maxlength="100" type="text" v-model="formData.NewsTitle"></el-input>
          </el-form-item>

            <el-form-item label="作者" prop="Author">
            <el-input maxlength="30" type="text" v-model="formData.Author"></el-input>
          </el-form-item>
             
           <!-- <el-form-item label='作者' prop="AuthorList" :rules="(formData.NewsStatus == 1)?[{required:true, message: '请选择作者', trigger: 'change' }]:[{required:false}]">
            <emp-selector key="author" :beforeConfirm='handleBeforeConfirm' :showType="2" :multiple="false" :list="formData.AuthorList" @change="handleChangeAuthor"></emp-selector>
          </el-form-item> -->

          <el-form-item label="内容" prop="NewsContent">

            <!-- <el-input type="textarea" :rows="8" v-model="formData.NewsContent"></el-input> -->
            <!-- <editor-bar v-model="formData.NewsContent" :isClear="isClear"></editor-bar> -->
            <editor-bar :value="formData.NewsContent" @edit="formData.NewsContent = arguments[0]"></editor-bar>
          </el-form-item>

        </el-form>
      </template>
      <template slot="footer">
        <!-- <el-button v-if="this.dialogStatus == 'create'" @click="handleDraft">存为草稿</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSave" type="primary">发布</el-button> -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleDraft" v-if="buttonShow" text="存为草稿"></app-button>
        <app-button @click="handleSave" text="发布"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
// import EditorBar from '../../../components/WangEditor/index.vue'
import EditorBar from '@/components/QuillEditor/index.vue'
import * as newsTrends from '@/api/informationCenter/newsTrends'
import empSelector from '../../common/empSelector'
import { newsTypeEnum } from "../enums";
export default {
  name: "demand-pool-create",
  directives: {},
  components: {  empSelector,  EditorBar
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.fileList = [];
        this.isContinue = false;
      }
      if (val) {
        this.resetFormData();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "发布新闻动态";
      } else if (this.dialogStatus == "update") {
        return "编辑新闻动态";
      }
    },
    buttonShow(){
      return this.dialogStatus == 'create' || this.dialogStatus == 'update' || this.formData.IsDraft;
    }
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      isClear: false,

      formLoading: false,
      isContinue: false,
      fileList: [], //图像信息[{Id: '', Path: ''}]

      newsTypeEnum: newsTypeEnum,
      rules: {
        NewsType: {
          fieldName: "新闻类型",
          rules: [{ required: true }]
        },
        NewsCoverPath: {
          fieldName: "新闻封面",
          rules: [{ required: true }]
        },
        NewsTitle: {
          fieldName: "新闻标题",
          rules: [{ required: true }]
        },
        Author: {
          fieldName: "作者",
          rules: [{ required: true }]
        },
        NewsContent: {fieldName: "新闻内容",rules: [{ required: true }, {max: 20000, trigger: "blur"}]}
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        NewsType: 1,
        NewsCover: "",
        NewsCoverPath: "",
        IsShow: true,
        NewsTitle: "",
        NewsContent: "",
        IsDraft: false,
        Author:'',
        NewsStatus:1,
      }
    };
  },
  methods: {
    //  handleBeforeConfirm(users) {
    //     if (users && users.length > 1) {
    //         this.$message({
    //             message: '作者不得超过1人',
    //             type: 'error'
    //         })
    //         return false
    //     }
    //     return true
    // },
   
    handleUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.NewsCoverPath = imgs[0].Path
        this.formData.NewsCover = imgs[0].Id
      } else {
        this.formData.NewsCover = ''
        this.formData.NewsCoverPath = ''
      }
    },

    resetFormData() {
      let temp = {
        Id: "",
        NewsType: 1,
        NewsCover: "",
        NewsCoverPath: "",
        IsShow: true,
        NewsTitle: "",
        NewsContent: "",
        IsDraft: false,
        Author:'',
        NewsStatus:1,
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      this.formLoading = true;
      newsTrends.detail({ id: this.id, isUpdate: true }).then(res => {
        this.formData = Object.assign({}, this.formData, res);

        this.fileList = [];
        if (this.formData.NewsCoverPath) {
          this.fileList = [
            { Id: this.formData.NewsCover, Path: this.formData.NewsCoverPath }
          ];
        }
        this.formLoading = false;
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //保存
    createData() {
      let validate = this.$refs.formData.validate();

      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));

          //提交数据保存
          let result = null;
          if (this.dialogStatus == "create") {
            delete postData.Id;
            result = newsTrends.add(postData);
          } else if (this.dialogStatus == "update") {
            result = newsTrends.edit(postData);
          }

          result.then(res => {
            if (this.isContinue) {
              this.resetFormData();
              this.$emit("reload");
            } else {
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.$refs.appDialogRef.createData();
            }
          });
        }
      });
    },

    handleSave() {
      this.formData.NewsStatus = 1;
      //console.log(this.formData.NewsContent)
      this.formData.IsDraft = false;
      this.createData();
    },

    handleDraft() {
      this.formData.NewsStatus = 0;
      this.formData.IsDraft = true;
      this.createData();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
