<template>
  <div v-if="visible">
    <div class="conditionArea-wrap clearfix">
      <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
        <template slot="RepairNO">
          <el-input style="width: 100%;" v-model="listQuery.RepairNO"></el-input>
        </template>
        <!-- <template slot='OilfieldCompanyName'>
                    <treeselect :normalizer="normalizer2" class="treeselect-common" key='type1' v-model="listQuery.OilfieldCompanyName" 
                        :default-expand-level="3" 
                        :options="treedata" 
                        :multiple="false" 
                        placeholder=''
                        :show-count="true"
                        :noResultsText='noResultsTextOfSelTree'
                    ></treeselect>
                </template> -->
        <!-- <template slot="FinishResult">
                    <el-select :disabled='listQuery.Status == "1"' clearable v-model="listQuery.FinishResult" placeholder="">
                        <el-option
                            v-for="item in repairStatus"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </template> -->
        <!-- <template slot='Symptom'>
                    <el-input style="width: 100%;" v-model="listQuery.Symptom"></el-input>
                </template> -->
        <template slot='FurnaceNumber'>
          <el-input style="width: 100%;" v-model="listQuery.FurnaceNumber"></el-input>
        </template>
        <template slot="other-btns">
          <el-button type="success" @click="handleSave">确认</el-button>
        </template>
      </app-table-form>
    </div>
    <el-table border fit :data="tabDatas" style="width: 100%" v-loading="listLoading" @selection-change='rowSelectionChanged' ref="mainTable2" :highlight-current-row='!multiple' @current-change='currentChanged' max-height="500">
      <el-table-column type="selection" width="55" v-if="multiple"></el-table-column>
      <el-table-column type="index" :index="indexMethod" label="编号"></el-table-column>
      <el-table-column prop="OrderNumber" label="维修单号"></el-table-column>
      <el-table-column prop="FurnaceNumber" label="炉号"></el-table-column>
      <el-table-column prop="ReporterName" label="报告人姓名"></el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />

  </div>
</template>

<script>
import * as repair from '@/api/repairOrder'
import * as oil from '@/api/oilfield'
import indexPageMixin from '@/mixins/indexPage'
import { getUserInfo } from '@/utils/auth'
import { listToTreeSelect } from '@/utils'
import { taskPriorityTypes, taskResource, taskTypes, taskStateTypes } from '../task/enums'
import dayjs from 'dayjs'

export default {
  name: 'task-list',
  mixins: [indexPageMixin],
  components: {
  },
  props: {
    existsUsers: {
      type: Array,
      default: () => {
        return []
      }
    },
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    condition: {
      type: Object,
      default: null
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.checkedUsers = JSON.parse(JSON.stringify(this.existsUsers))
          if (this.condition) {
            this.listQuery = Object.assign({}, this.listQuery, this.condition)
          }
          this.getList()
        }
      },
      immediate: true
    },
  },
  created() {
    this.getOilfieldList()//厂矿站树形列表
  },
  data() {
    return {
      treedata: [],
      normalizer2(node) {
        // treeselect定义字段
        return {
          id: node.label,
          label: node.label,
          children: node.children
        }
      },
      checkedUsers: [],
      tableSearchItems: [
        { prop: 'RepairNO', label: '维修单号' },
        // {prop: 'OilfieldCompanyName', label: '使用单位'},
        // {prop: 'FinishResult', label: '状态'},
        // {prop: 'Symptom', label: '故障现象'},
        { prop: 'FurnaceNumber', label: '炉号' },
      ],
      taskStateTypes: taskStateTypes,
      taskPriorityTypes: taskPriorityTypes,
      multipleSelection: [], // 列表checkbox选中的值
      total: 0,
      listLoading: false,
      listQuery: { // 查询条件
        FinishResult: '',
        Status: ''
      },
      tabKey: 0,
      tabDatas: [],
    }
  },
  methods: {
    onResetSearch() {
      this.listQuery = {
        PageIndex: this.listQuery.PageIndex,
        PageSize: this.listQuery.PageSize
      };
      if (this.condition) {
        this.listQuery = Object.assign({}, this.listQuery, this.condition)
      }
    },
    currentChanged(currentRow, oldCurrentRow) {
      if (!this.multiple && currentRow) { //某页选中一个用户后，点击翻页会触发该事件，所以要加该判断
        this.multipleSelection = [currentRow]
        this.checkedUsers = [currentRow]
      }
    },
    //当表格行中所有 checkbox 选中状态项改变时（返回所有选中的行数据）
    rowSelectionChanged(rows) {

      this.multipleSelection = rows;
      this.tabDatas.forEach(r => {
        let idx = this.checkedUsers.findIndex(u => u.RepairId == r.RepairId)
        if (idx > -1) {
          this.checkedUsers.splice(idx, 1)
        }
      })

      if (rows && rows.length > 0) {
        rows.forEach(r => {
          if (!this.checkedUsers.some(s => s == r.RepairId)) {
            this.checkedUsers.push(r)
          }
        })
      }
    },
    handleChangeUsers(users) {
      if (users && users.length > 0) {
        this.listQuery.TaskRelationEmployeeId = [users[0]]
      } else {
        this.listQuery.TaskRelationEmployeeId = []
      }
    },
    getList() {
      this.listLoading = true
      let condition = JSON.parse(JSON.stringify(this.listQuery))
      if (condition.Status == '1') {
        condition.FinishResult = '-1'
      }
      if (!condition.Status) {
        condition.Status = '-1'
      }
      if (!condition.FinishResult) {
        condition.FinishResult = '-1'
      }
      if (condition.FinishResult == '-1' && condition.Status == '-1') {
        condition.IsQueryAll = true
      }
      repair.getList(condition).then(res => {
        this.tabDatas = res.Items
        this.total = res.Total
        this.listLoading = false
        this.setCheckedusers()
      }).catch(err => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.PageIndex = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page
      this.listQuery.PageSize = val.size

      this.getList()
    },
    getOilfieldList() {
      this.listLoading = true
      var _this = this
      oil.getList({
        Status: true
      }).then(response => {
        let datas = response.map(function (item, index, input) {
          let obj = {
            Id: item.OilfieldCompanyId,
            label: item.Name,
            ParentId: item.ParentId,
          }
          return obj
        })
        _this.treedata = listToTreeSelect(datas)
        this.listLoading = false
      })
    },
    setCheckedusers() {
      if (!this.multiple) { //单选
        if (this.checkedUsers.length > 0) {
          let checkUser = this.tabDatas.find(p => p.RepairId == this.checkedUsers[0].RepairId)
          checkUser && this.$refs.mainTable2.setCurrentRow(checkUser);
        }
      } else { //多选

        if (this.checkedUsers.length == 0) {
          this.$refs.mainTable2.clearSelection();
        } else {

          let checkedUsers = this.tabDatas.filter(s => this.checkedUsers.map(u => u.RepairId).some(o => o == s.RepairId)) || []
          checkedUsers.forEach(u => {
            this.$nextTick(() => {
              if (this.$refs.mainTable2) {
                this.$refs.mainTable2.toggleRowSelection(u)
              }
            })
          })
        }
      }
    },
    handleSave() {
      this.$emit('changed', this.checkedUsers)
      // this.userSelectorDlg = false

    },

    indexMethod(index) {
      return (index += 1) + this.listQuery.PageSize * (this.listQuery.PageIndex - 1)
    },
  }
}
</script>


<style scoped>
.title,
.sub-title {
  margin-bottom: 10px;
}

.sub-title {
  display: flex;
  line-height: 28px;
}

.sub-title-info {
  flex: 1;
}

.sub-title-btns {
  margin-right: 10px;
}

.tab-users {
  max-height: 480px;
  padding: 10px;
  overflow-y: scroll;
}

.tab-item-wrap {
  float: left;
  width: 20%;
  padding: 5px;
  position: relative;
}

.tab-item {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  height: 80px;
  width: 100%;
  border: 1px solid #dcdfe6;
  padding: 10px;
}

.opt {
  cursor: pointer;
  font-size: 28px;
  color: #dcdfe6;
  text-align: center;
  line-height: 80px;
  vertical-align: middle;
  padding: 0;
}

.conditionArea-wrap {
  padding: 0 10px;
}

.btns-area {
  text-align: left;
  padding: 10px 0;
  padding-right: 10px;
}

.file-btn-del {
  background: transparent;
  display: block;
  cursor: pointer;
  position: absolute;
  font-size: 18px;
  top: -3px;
  right: -3px;
}

.file-btn-del:hover {
  transition: all 0.3s;
  color: #f56c6c;
}

.color-danger,
.color-danger:hover {
  color: #f56c6c;
}
</style>