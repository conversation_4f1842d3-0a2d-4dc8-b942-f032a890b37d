<template>
  <div class="app-container repairAnalysis">
    <div class="bg-white">
      <!-- <page-title title="报修数据分析" :subTitle="['售后报修单、故障现象及配件消耗等多维售后报修数据分析页面']"></page-title> -->
      <div class="page-wrapper">
        <div class="tagBox">
          <tags :items='types' v-model="tagType" @change="handleTagsChange">
              <template v-for="t in types" :slot="t.value">
                  {{ t.label }}
              </template>
          </tags>
        </div>
        <div class="line-40 pl-10">
          <el-row>
            <el-col :span="6">
              <span class="demonstration">统计年份：</span>
              <el-date-picker
                v-model="yearVal"
                type="year"
                placeholder="选择年"
                :clearable='false'
                style="width:100px;"
                value-format="yyyy"
                @change='handleClickYear'>
              </el-date-picker>
              <el-button type="text" @click="getNowYear()">当前年份</el-button>
            </el-col>
            <el-col :span="18" v-if="tagType == 2">

                地区选择：
                <div class="el-input el-input--mini" style="display: inline-block; width: 300px;">
                  <div style="display: flex; height: 28px;line-height: 28px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; ">
                    <div style="padding-left: 10px;">
                      <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">选择</span>
                    </div>
                    <div style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="areaName">{{ areaName }}</div>
                    <div style="width: 28px; text-align: center;">
                      <i style="cursor: pointer;" title="删除" @click="handleClearRegional" v-show="RegionalId" class="el-icon-close"></i>
                    </div>
                  </div>
                </div>
              
            </el-col>
          </el-row>
        </div>
        <div class="main" v-if="tagType == 1">


        <el-row>
            <el-col :span="24" class="blockTitle pl-10 line-40 cl"><span>报修/处理走势</span></el-col>
        </el-row>
        <el-card shadow="hover" style="margin-bottom:10px;">
          <!-- <v-analysis-title title="报修/处理走势">
            <div class="chartBox cl" v-loading="loading1">
              <div class="fl">
                <div id="myChart" v-show="mtrData.length>0" style="width:100%;height:400px;"></div>
                <no-data v-show="mtrData.length==0"></no-data>
              </div>
              <div class="fl">
                <div id="myChart1" v-show="mtrData.length>0" style="width:100%;height:400px;"></div>
                <no-data v-show="mtrData.length==0"></no-data>
              </div>
            </div>
          </v-analysis-title> -->
           
            <el-row>
                <el-row>
                <el-col :span="12">
                  <div id="myChart" v-show="mtrData.length>0" style="width:100%;height:400px;"></div>
                  <no-data v-show="mtrData.length==0"></no-data>
                </el-col>
                <el-col :span="12">
                  <div id="myChart1" v-show="mtrData.length>0" style="width:100%;height:400px;"></div>
                  <no-data v-show="mtrData.length==0"></no-data>
                </el-col>
              </el-row>
            </el-row>

        </el-card>

          <v-button-list v-model="mValue" :btnListData='monList' @change="getMonthData"></v-button-list>
          <v-analysis-title title="报修单数量统计" :amount="requisitionsTotal" v-loading="loading2">
            <div class="chartBottomBox cl">
              <div class="fl">
                <div v-show="pccData.length>0" id="myChart2" style="min-width:835px;height:460px;"></div>
                <ul v-show="pccData.length>0" class="chartUl">
                  <li v-for="c in completion">完成率(<i :style="'color:'+(c.amount >= 50 ? '#19D119;' : 'red;')">{{c.amount}}%</i>)</li>
                </ul>
                <no-data v-show="pccData.length==0"></no-data>
              </div>
              <div class="fl lastChild">
                <div>
                  <page-title title="报修单占比"></page-title>
                  <div v-show="pccData.length>0" id="myChart3" style="min-width:100%;height:190px;"></div>
                  <no-data v-show="pccData.length==0"></no-data>
                </div>
                <div>
                  <page-title title="完成率排名"></page-title>
                  <el-select class='elSelect' v-model="sortRegion" @change="regionChange">
                    <el-option label="从高至低" :value="0"></el-option>
                    <el-option label="从低至高" :value="1"></el-option>
                  </el-select>
                  <ul v-show="pccData.length>0">
                    <li class="omit" v-for="(rr,i) in rateRanking">{{i+1}}、{{rr.name}} (完成率：<i :style="'color:'+(rr.amount >= 50 ? '#19D119;' : 'red;')">{{rr.amount}}%</i>)</li>
                  </ul>
                  <no-data v-show="pccData.length==0"></no-data>
                </div>
              </div>
            </div>
          <page-title class="pTitle" title="各地区报修单详情"></page-title>
          <app-table-core
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="[]"
            :isShowAllColumn="false"
            :isShowOpatColumn="false"
            :startOfTable="startOfTable"
            :multable="false"
            :serial="false"
            row-key="RegionalId"
            @rowSelectionChanged="rowSelectionChanged"
          >
            <template
              slot="Total"
              slot-scope="scope"
            >
              <span style="color:#FF9900;">{{ scope.row.Total}}</span>
            </template>
            <template
              slot="Complete"
              slot-scope="scope"
            >
              <span style="color:#409EFF;">{{ scope.row.Complete}}</span>
            </template>
            <template
              slot="completion"
              slot-scope="scope"
            >
              <span :style="'color:'+(scope.row.completion >= 50 ? '#19D119;' : 'red;')">{{ scope.row.completion}}%</span>
            </template>
          </app-table-core>
          </v-analysis-title>
        </div>
        <div class="lsBox" v-else>
          <v-loss-statistical
          :yearVal="yearVal"
          :regionalId='RegionalId'>
          </v-loss-statistical>
        </div>
      </div>
    </div>

    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="RegionalId ? [RegionalId] : []"
    ></v-area-choose>
  </div>
</template>

<script>
import * as maintenOrderMgmt from "@/api/maintenanceCenter/maintenOrderMgmt";
import vButtonList from '@/views/common/buttonList'
import NoData from "@/views/common/components/noData";
import vAnalysisTitle from "@/views/common/analysisTitle";
import { vars } from "../common/vars"
import indexPageMixin from "@/mixins/indexPage"
import vLossStatistical from './lossStatistical'
import vAreaChoose from "../../../afterSalesMgmt/businessMap/common/areaChoose";
var echarts = require('echarts');
export default {
  name: "",
  mixins: [indexPageMixin],
  components: {
    vLossStatistical,
    vButtonList,
    NoData,
    vAnalysisTitle,
    vAreaChoose,
  },
  filters: {
    
    
  },

  data() {
    return {
      mValue: (new Date()).getMonth() + 1,
      tagType:1,
      types: [{value: 1, label: '报修单数据统计'}, {value: 2, label: '故障及配件损耗统计'}],
      requisitionsTotal:0,
      completion:[],
      tabDatas:[],
      tabColumns: [
        {
          attr: { prop: "RegionalName", label: "地区名称" },
          // slot: true
        },
        {
          attr: { prop: "Total", label: "新增报修单" },
          slot: true
        },
        {
          attr: { prop: "Complete", label: "处理报修单" },
          slot: true
        },
        {
          attr: { prop: "completion", label: "完成率" },
          slot: true
        }
      ],
      sortRegion:0,
      loading1:false,
      loading2:false,
      myChart:null,
      myChart1:null,
      myChart2:null,
      myChart3:null,
      monList:[{
        value:null,
        label:'全年'
      },{
        value:1,
        label:'1月'
      },{
        value:2,
        label:'2月'
      },{
        value:3,
        label:'3月'
      },{
        value:4,
        label:'4月'
      },{
        value:5,
        label:'5月'
      },{
        value:6,
        label:'6月'
      },{
        value:7,
        label:'7月'
      },{
        value:8,
        label:'8月'
      },{
        value:9,
        label:'9月'
      },{
        value:10,
        label:'10月'
      },{
        value:11,
        label:'11月'
      },{
        value:12,
        label:'12月'
      },],
      yearVal:new Date(),

      RegionalId: '',
      areaName: '',
      
      mtrData:[],
      pccData:[],
      rateRanking:[],
      dialogRegionalVisible: false,
    };
  },
  computed: {
    
  },
  
  watch: {
  },
  created() {
    
  },
  mounted() {

    

    

    this.getMonthlyRequisition();
    this.getRequisitionCounted();
  },
  methods:{
     handleTagsChange(d){
      if(d == 1){
        this.getMonthlyRequisition();
        this.mValue=null;
        this.getRequisitionCounted();
      }else{
        
      }
    },
    rowSelectionChanged(){},
    regionChange(d){
      if(d == 0) this.rateRanking.sort(this.sortCompletion);
      else this.rateRanking.sort(this.sortCompletion2);
    },
    getRequisitionCounted(){
      this.loading2=true;
      let params={
        year:new Date(this.yearVal).getFullYear(),
        month:this.mValue
      }
      maintenOrderMgmt.getPercentageCompleteChart(params).then(res => {
        this.pccData=res;
        
        vars.towBarOption.xAxis.data=[];
        vars.towBarOption.series[0].data=[];
        vars.towBarOption.series[1].data=[];
        vars.pieOption.legend.data=[];
        vars.pieOption.series[0].data=[];
        this.completion=[];
        this.requisitionsTotal=0;
        let a=0;
        if(res.length>0){
          res.forEach(v => {
            this.requisitionsTotal+=v.Total;

            vars.towBarOption.xAxis.data.push(v.RegionalName);
            vars.towBarOption.series[0].data.push(v.Total);
            vars.towBarOption.series[1].data.push(v.Complete);

            this.completion.push({amount:this.technologyRatio(v),name:v.RegionalName});

            v.completion=this.technologyRatio(v);
            if(v.children.length>0){
              v.children.forEach(v1 => {
                v1.completion=this.technologyRatio(v1);
              })
            }
            
            vars.pieOption.legend.data.push(v.RegionalName+'('+v.Total+')');
            vars.pieOption.series[0].data.push({
              value: v.Total, 
              name: v.RegionalName+'('+v.Total+')',
              label:{show:false,position:'inside'}
            });
          })
          this.tabDatas=res;
          this.myChart3 = echarts.init(document.getElementById('myChart3'));
          vars.pieOption.legend.left='60%';
          vars.pieOption.series[0].radius='75%';
          vars.pieOption.series[0].center[0]='30%';
          vars.pieOption.title.show=false;
          this.myChart3.setOption(vars.pieOption,true);
          this.boxResize(this.myChart3);

          this.rateRanking=JSON.parse(JSON.stringify(this.completion));
          this.rateRanking.sort(this.sortCompletion);
          let myChart = echarts.init(document.getElementById('myChart2'));
          myChart.setOption(vars.towBarOption,true);
          this.boxResize(myChart);
        }
        this.loading2=false;
      }).catch(err => {
        this.loading2=false;
      })
    },
    technologyRatio(d){
      let a=0;
      if((d.Total+d.Complete) == 0) a=0;
      else a=d.Complete/d.Total;
      a=Math.floor(a*10000)/100;
      return a;
    },
     sortCompletion(a,b){
      return b.amount - a.amount;
     },
     sortCompletion2(a,b){
      return a.amount - b.amount;
     },
    getMonthlyRequisition(){
      this.loading1=true;
      let params={
        year:new Date(this.yearVal).getFullYear(),
      }
      maintenOrderMgmt.getReportSheetTrendChart(params).then(res => {
        this.mtrData=res;
        vars.lineOption.series=[];
        vars.lineOption.legend.data=[];
        if(res.length>0){
          this.loading1=false;
          res.forEach(v => {
            vars.lineOption.legend.data.push(v.RegionalName);
            vars.lineOption.series.push({
                name: v.RegionalName,
                type: 'line',
                symbolSize:1,
                data: v.Data,
                // itemStyle : { normal: {label : {show: true,textStyle: {color: '#1D2129'}}}}
            })
          })
          let myChart = echarts.init(document.getElementById('myChart'));
          vars.lineOption.title.text='报修单月走势';
          myChart.setOption(vars.lineOption,true);
          this.boxResize(myChart);
          vars.lineOption.series=[];
          vars.lineOption.legend.data=[];
          res.forEach(v => {
            vars.lineOption.legend.data.push(v.RegionalName);
            vars.lineOption.series.push({
                name: v.RegionalName,
                type: 'line',
                symbolSize:1,
                data: v.ProcessedData,
                // itemStyle : { normal: {label : {show: true,textStyle: {color: '#1D2129'}}}}
            })
          })
          myChart = echarts.init(document.getElementById('myChart1'));
          vars.lineOption.title.text='报修单处理月走势';
          myChart.setOption(vars.lineOption,true);
          this.boxResize(myChart);
        }
      }).catch(err => {
        this.loading1=false;
      })
    },
    getNowYear(){
      this.yearVal=new Date();
      this.handleClickYear();
    },
    boxResize(box){
      this.$nextTick(() => {
        box.resize();
      })
      window.addEventListener('resize',function(){
        box.resize();
      })
    },
    closeRegionalDialog() {
      this.dialogRegionalVisible=false
    },
    handleRegionalDialog() {
      this.dialogRegionalVisible=true;
    },
    handleClearRegional() {
      this.RegionalId = ''
      this.areaName = ''
    },
    electedRegionalData(data){
        if(data) {
          this.RegionalId=data.Id;
          this.areaName=data.ParentName;
        }else{
          this.RegionalId='';
          this.areaName='';
        }
    },
    handleClickYear(d){
      this.getMonthlyRequisition();
      this.mValue=null;
      this.getRequisitionCounted();
    },
    getMonthData(){
      this.getRequisitionCounted();
    },
  }
};
</script>

<style lang="scss" scoped>
.lsBox{
  position: absolute;
    top: 77px;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow-y: hidden;
}
.tagBox{
  border-bottom: 1px solid #EBEEF5;
  padding: 4px 8px;
}
.repairAnalysis .monBottom:last-child{
  margin-bottom:0;
}
.pTitle{
  border-left:1px solid #EBEEF5;
}
.chartBottomBox{
  border-bottom:1px solid #EBEEF5;
  border-left:1px solid #EBEEF5;
  >div{
    height:460px;
  }
  >div:first-child{
    width:calc(100% - 360px);
    overflow-x:auto;
    overflow-y: hidden;
    border-right:1px solid #EBEEF5;
    position: relative;
    .chartUl{
      min-width: 762px;
      position: absolute;
      bottom:20px;
      left: 48px;
      right: 22px;
      display: flex;
      li{
        flex:1;
        text-align: center;
      }
    }
  }
  .lastChild{
    width:360px;
    >div{
      height:50%;
    }
    >div:first-child{
      border-bottom:1px solid #DCDFE6;
      >div:last-child{
        overflow-x: auto;
      }
    }
    >div:last-child{
      position: relative;
      .elSelect{
        position: absolute;
        top:5px;
        right:10px;
      }
      >ul{
        height:190px;
        overflow-y: auto;
        li{
          width:90%;
          padding:10px 0 0 10px;
        }
      }
    }
  }
}
.app-container{
  overflow-y:auto;
}
.main{
  padding:10px;
  .monBottom{
    margin-top: 10px;
    margin-bottom: 20px;
    border:1px solid #EBEEF5;
  }
  .monBottom:last-child{
    border:0;
  }
}
.chartBox{
  >div{
    width:50%;
    height:400px;
    
  }
  >div:first-child{
    overflow-x: auto;
  }
}
.chartBox1{
  >div{
    width:calc(50% - 10px);
    height:455px;
    border:1px solid #ccc;
    overflow-x: auto;
  }
  >div:last-child{
    margin-left:20px;
  }
}
.line-40{
  border-bottom: 1px solid #DCDFE6;
}
.blockTitle{
  background:#ccc;
  >span:first-child{
    font-size: 16px;
    font-weight: 600;
  }
}
.shadow{
  box-shadow:2px 2px 5px -2px rgba(0, 0, 0, 0.5); 
}
</style>