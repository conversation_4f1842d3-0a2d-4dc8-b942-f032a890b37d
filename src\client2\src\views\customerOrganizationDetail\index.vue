<template>
  <div style="min-height: 500px;">
    <div class="app-container">
      <div class="bg-white">
        <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns"
          :isShowAllColumn="isShowAllColumn" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged"
          :isShowOpatColumn="rowBtns.length > 0" :startOfTable="startOfTable">
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items="tableSearchItems"
              @onSearch="handleFilter" @onReset="resetSearch">
              <template slot="OrganizationId">
                <treeselect :normalizer="normalizer" class="treeselect-common" ref="orgsTree" :options="orgsTree" :default-expand-level="3"
                  :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true"
                  v-model="listQuery.OrganizationId" placeholder="请选择客户单位" :noResultsText='noResultsTextOfSelTree'
                  :noOptionsText="noOptionsTextOfSelTree">
                </treeselect>
              </template>
              <template slot="ImportanceTypeId">
                <el-select v-model="listQuery.ImportanceTypeId" placeholder="请选择重要性" style="width:100%;" clearable>
                  <el-option key="1" :value="1" label="高"></el-option>
                  <el-option key="2" :value="2" label="中"></el-option>
                  <el-option key="3" :value="3" label="低"></el-option>
                </el-select>
              </template>
              <template slot='CompanyOwnerEmployee'>
                <emp-selector :multiple='false' :showType='2' key='service-users' :list='listQuery.CompanyOwnerEmployee'
                  @change='handleChangeUsers' placeholder>
                </emp-selector>
              </template>
              <template slot="other-btns">
                <el-button @click="onExport">导出</el-button>
              </template>
            </app-table-form>
          </template>

          <!-- 表格批量操作区域 -->
          <template slot="btnsArea">
            <permission-btn moduleName="customerorganizationdetail" v-on:btn-event="onBtnClicked"></permission-btn>
          </template>

          <!-- 自定义列 -->
          <template slot='CompanyOwnerEmployee' slot-scope="scope">
            {{ scope.row.CompanyOwnerEmployee?scope.row.CompanyOwnerEmployee.Name:"" }}
          </template>
          <template slot='ShowVisitingRecord' slot-scope="scope">
            <el-button type="text" @click="showVisitingRecord(scope.row.CustomerOrganizationDetailId,1)">拜访记录
            </el-button>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type="1">
            </app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleUpdate(scope.row, 'detail')"
              :type="2"></app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3">
            </app-table-row-button>
          </template>
        </app-table>

        <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize"
          @pagination="handleCurrentChange" @size-change="handleSizeChange" />
      </div>

      <el-dialog v-el-drag-dialog class="dialog-mini" width="1000px" :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible" :close-on-click-modal="false" :append-to-body="true">
        <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="120px"
          style="max-height: 600px; overflow-y: auto;">
          <el-row>
            <el-col :span="12">
              <el-form-item label="客户单位" prop="OrganizationId">
                <treeselect :normalizer="normalizer" class="treeselect-common" ref="orgsDetailTree" :options="orgsTree" :default-expand-level="3"
                  :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true"
                  v-model="temp.OrganizationId" placeholder="请选择客户单位" :noResultsText='noResultsTextOfSelTree'
                  :noOptionsText="noOptionsTextOfSelTree" :disabled="dialogStatus != 'create'">
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地址" prop="Address">
                <el-input maxlength="500" v-model="temp.Address" :rows="2" :disabled="editable">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="Email">
                <el-input maxlength="200" v-model="temp.Email" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="传真" prop="Fax">
                <el-input maxlength="200" v-model="temp.Fax" :rows="2" :disabled="editable">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="网站" prop="Website">
                <el-input maxlength="200" v-model="temp.Website" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="本公司负责人" prop="CompanyOwnerEmployee">
                <emp-selector :multiple='false' :showType='2' key='service-users' :list='temp.CompanyOwnerEmployee'
                  @change='handleChangeDialogEmployee' placeholder="本公司负责人" :readonly="editable">
                </emp-selector>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="电话号码" prop="Telephone">
                <el-input maxlength="200" v-model="temp.Telephone" :disabled="editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重要性" prop="ImportanceTypeId">
                <el-select v-model="temp.ImportanceTypeId" placeholder="请选择重要性" style="width:100%;" clearable
                  :disabled="editable">
                  <el-option key="1" :value="1" label="高"></el-option>
                  <el-option key="2" :value="2" label="中"></el-option>
                  <el-option key="3" :value="3" label="低"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="客户状态" prop="CustomerStatusTypeId">
                <el-select v-model="temp.CustomerStatusTypeId" placeholder="请选择客户状态" style="width:100%;" clearable
                  :disabled="editable">
                  <el-option key="1" :value="1" label="潜在"></el-option>
                  <el-option key="2" :value="2" label="意向"></el-option>
                  <el-option key="3" :value="3" label="洽谈"></el-option>
                  <el-option key="4" :value="4" label="成交"></el-option>
                  <el-option key="5" :value="5" label="流失"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="主营业务" prop="MainBusiness">
              <el-input maxlength="500" v-model="temp.MainBusiness" type="textarea" :disabled="editable"></el-input>
            </el-form-item>
          </el-row>
          <div class="card-container" v-if="editable">
            <el-tabs v-model="flowTabSelect">
              <el-tab-pane v-for="item in flowTabs" :key="item" :label="item" :name="item"></el-tab-pane>
            </el-tabs>

            <!-- 客户项目 -->
            <div v-show="flowTabSelect == '客户项目'">
              <el-table fit :data="customerProjectList" style="width: 100%" v-loading="listLoading"
                max-height="500">
                <el-table-column type="index" :index="indexMethod" label="序号">
                </el-table-column>
                <el-table-column prop="CustomerProjectName" label="项目名称" width="120"></el-table-column>
                <el-table-column prop="ProjectStartDate" label="开始时间" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.ProjectStartDate?formatterTableDateTime(scope.row,"ProjectStartDate"):""}}
                  </template>
                </el-table-column>
                <el-table-column prop="ProjectEndDate" label="结束时间" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.ProjectEndDate?formatterTableDateTime(scope.row,"ProjectEndDate"):""}}
                  </template>
                </el-table-column>
                <el-table-column prop="ProjectOwnerPerson" label="主要负责人" width="120"></el-table-column>
                <el-table-column prop="ProjectOtherPerson" label="相关负责人" width="120"></el-table-column>
                <el-table-column prop="Telephone" label="电话" width="120"></el-table-column>
                <el-table-column prop="ProjectPhase" label="项目阶段" width="120"></el-table-column>
                <el-table-column prop="CompanyRelatedProject" label="本公司关联项目" width="120"></el-table-column>
                <el-table-column prop="CompanyOwnerEmployee" label="本公司负责人" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.CompanyOwnerEmployee?scope.row.CompanyOwnerEmployee.Name:"" }}
                  </template>
                </el-table-column>
                <el-table-column prop="ProjectStatus" label="项目状态" width="120"></el-table-column>
                <el-table-column prop="CompetitorStatus" label="竞争对手状态" width="120"></el-table-column>
                <el-table-column prop="AssessmentResult" label="评估结果" width="120"></el-table-column>
                <el-table-column prop="ShowVisitingRecord" label="采购预测" width="120" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" @click="showProcurementForecast(scope.row.CustomerProjectId)">采购预测
                    </el-button>
                  </template>
                </el-table-column>

              </el-table>
            </div>

            <!-- 客户干系人 -->
            <div v-show="flowTabSelect == '客户干系人'">
              <el-table fit :data="customerArchiveList" style="width: 100%" v-loading="listLoading"
                max-height="500">
                <el-table-column type="index" :index="indexMethod" label="序号">
                </el-table-column>
                <el-table-column prop="OrganizationDepartmentName" label="部门" width="120"></el-table-column>
                <el-table-column prop="CustomerName" label="客户名称" width="120"></el-table-column>
                <el-table-column prop="CustomerLevelTypeName" label="级别" width="120"></el-table-column>
                <el-table-column prop="ImportanceTypeName" label="重要性" width="120"></el-table-column>
                <el-table-column prop="GenderTypeName" label="性别" width="120"></el-table-column>
                <el-table-column prop="Birthday" label="生日" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.Birthday?formatterTableDate(scope.row,"Birthday"):""}}
                  </template>
                </el-table-column>
                <el-table-column prop="WeddingDay" label="结婚纪念日" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.WeddingDay?formatterTableDate(scope.row,"WeddingDay"):""}}
                  </template>
                </el-table-column>
                <el-table-column prop="School" label="毕业学校" width="120"></el-table-column>
                <el-table-column prop="Telephone" label="电话" width="120"></el-table-column>
                <el-table-column prop="MajorRelationship" label="主要人脉关系" width="120"></el-table-column>
                <el-table-column prop="CharacterTrait" label="性格特点" width="120"></el-table-column>
                <el-table-column prop="Hobby" label="兴趣爱好" width="120"></el-table-column>
                <el-table-column prop="FrequentContacts" label="最近常联系的人" width="120"></el-table-column>
                <el-table-column prop="RecentBook" label="最近看什么书" width="120"></el-table-column>
                <el-table-column prop="ShowVisitingRecord" label="拜访记录" width="120" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" @click="showVisitingRecord(scope.row.CustomerArchiveId,2)">拜访记录
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

        </el-form>
        <div slot="footer">
          <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
          <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认
          </el-button>
        </div>
      </el-dialog>
      <visted-list :editableForVistedLog='false' :id="vistedListId" :vistedType="vistedType" @close='handleClosed'
        @saveSuccess='handSaveSuccess' :dialogStatus="'拜访记录'" :visible='vistedListDialogVisible'></visted-list>
      <purchase-list :id="customerProjectId" @close='handlePurchaseDialogClosed' @saveSuccess='handPurchaseSaveSuccess'
        :dialogStatus="'采购记录'" :editablePurchase='false' :visible='dialogFormVisibleProcurementForecasts'></purchase-list>
    </div>
  </div>
</template>

<script>
  import { listToTreeSelect } from "@/utils";
  import { downloadFile } from "@/utils/index";
  import * as orgs from '@/api/oilfield'
  import * as customerOrganizationDetail from "@/api/customerOrganizationDetail";
  import EmpSelector from '../common/empSelector'
  import * as customerArchive from "@/api/customerArchive";
  import * as customerProject from "@/api/customerProject";
  import { regs } from '@/utils/regs'

  import elDragDialog from "@/directive/el-dragDialog";
  import indexPageMixin from "@/mixins/indexPage";
  import VistedList from './vistedList'
  import PurchaseList from "../customerProject/purchaseList"

  export default {
    name: "customerorganizationdetail",
    components: {
      EmpSelector,
      VistedList,
      PurchaseList,
    },
    directives: {
      elDragDialog
    },
    mixins: [indexPageMixin],
    data() {
      return {
        normalizer(node) {
          // treeselect定义字段
          return {
            label: node.label,
            id: node.Id,
            children: node.children
          }
        },
        //tab页
        flowTabs: ["客户项目", "客户干系人"],
        flowTabSelect: "客户项目",
        customerProjectList: [],
        customerArchiveList: [],
        vistedType: 1,
        multipleSelection: [],
        /**查询内容 */
        tableSearchItems: [
          { prop: "OrganizationId", label: "客户单位" },
          { prop: "ImportanceTypeId", label: "重要性" },
          { prop: "CompanyOwnerEmployee", label: "本公司负责人" },
        ],
        /**列表栏目 */
        tabColumns: [
          {
            attr: { prop: "OrganizationName", label: "客户单位" }
          },
          {
            attr: { prop: "Address", label: "地址", showOverflowTooltip: true }
          },
          {
            attr: { prop: "Email", label: "邮箱" }
          },
          {
            attr: { prop: "Fax", label: "传真" }
          },
          {
            attr: { prop: "Website", label: "网站" }
          },
          {
            attr: { prop: "Telephone", label: "电话号码" }
          },
          {
            attr: { prop: "ImportanceTypeName", label: "重要性" }
          },
          {
            attr: { prop: "CustomerStatusTypeName", label: "客户状态" }
          },
          {
            attr: { prop: "CompanyOwnerEmployee", label: "本公司负责人" },
            slot: true
          },
          {
            attr: { prop: "MainBusiness", label: "主营业务" }
          },
          {
            attr: { prop: "ShowVisitingRecord", label: "拜访记录" },
            slot: true
          },
          {
            attr: { prop: "CreatorName", label: "创建人" }
          },
          {
            attr: { prop: "CreateDateTime", label: "创建时间", formatter: this.formatterDate }
          }
        ],
        orgsTree: [],
        // tabAuthColumns: [], //已授权的列
        tabDatas: [],
        listLoading: false,
        postLoading: false,
        // /**列表查询参数 */
        // listQuery: {
        //   OrganizationId: "",
        //   ImportanceTypeId: "",
        //   CompanyOwnerEmployeeId: "",
        // },
        total: 0,
        /**弹出窗标题 */
        textMap: {
          update: "编辑",
          create: "添加"
        },
        dialogFormVisible: false,
        /**校验规则 */
        rules: {
          OrganizationId: {
            fieldName: "客户单位",
            rules: [{ required: true }]
          },
          Address: {
            fieldName: "地址",
            rules: [{ required: true }, { max: 500 }]
          },
          CompanyOwnerEmployee: {
            fieldName: "本公司负责人",
            rules: [{ required: true }]
          },
          ImportanceTypeId: {
            fieldName: "重要性",
            rules: [{ required: true }]
          },
          CustomerStatusTypeId: {
            fieldName: "客户状态",
            rules: [{ required: true }]
          },
          Email: {
            fieldName: "邮箱",
            rules: [{ reg: regs.email }]
          },
          Fax: {
            fieldName: "传真",
            rules: [{ reg: regs.fax }]
          },
          Website: {
            fieldName: "网站",
            rules: [{ reg: /[\w+\.]+[\w]+(\/\S*)?/ }]
          },
          Telephone: {
            fieldName: "电话号码",
            rules: [{ reg: regs.phoneAndTel }]
          }
        },
        /**弹出窗临时对象 */
        temp: {
          CustomerOrganizationDetailId: "",
          OrganizationId: null,
          Address: "",
          Email: "",
          Fax: "",
          Website: "",
          Telephone: "",
          Position: "",
          ImportanceTypeId: null,
          CustomerStatusTypeId: null,
          CompanyOwnerEmployee: [],
          MainBusiness: "",
        },
        dialogFormVisibleProcurementForecasts: false,
        customerProjectId: "",
        vistedListDialogVisible: false,
        vistedListId: ''
      };
    },
    created() {
      this.rules = this.initRules(this.rules);
      this.getList();
    },
    mounted() {
      this.getOrgTree();
    },
    methods: {
      //拜访记录页面
      handleClosed() {
        this.vistedListDialogVisible = false
      },
      handSaveSuccess() {

      },
      showVisitingRecord(Id, type) {
        this.vistedListDialogVisible = true
        this.vistedListId = Id
        this.vistedType = type
      },

      handlePurchaseDialogClosed() {
        this.dialogFormVisibleProcurementForecasts = false
      },
      handPurchaseSaveSuccess() {
        // this.dialogFormVisibleProcurementForecasts = false
      },
      showProcurementForecast(CustomerProjectId) {
        this.customerProjectId = CustomerProjectId;
        this.dialogFormVisibleProcurementForecasts = true;
      },


      getOrgTree() {
        var _this = this // 记录vuecomponent
        orgs.getList({ Status: true }).then(response => {
          // let datas = response.map(function (item, index, input) {
          //   let obj = {
          //     Id: item.OilfieldCompanyId,
          //     label: item.Name,
          //     ParentId: item.ParentId,
          //   }
          //   return obj
          // })
          // _this.orgsTree = listToTreeSelect(datas)
          // this.listLoading = false

          _this.orgs = response.map(function (item, index, input) {
            let obj = {
              Id: item.OilfieldCompanyId,
              label: item.Name,
              ParentId: item.ParentId,
            }
            return obj
          })
          var orgstmp = JSON.parse(JSON.stringify(_this.orgs))
          _this.orgsTree = listToTreeSelect(orgstmp)

        })
      },
      /**重置 */
      resetTemp() {
        this.temp = {
          CustomerOrganizationDetailId: "",
          OrganizationId: null,
          Address: "",
          Email: "",
          Fax: "",
          Website: "",
          Telephone: "",
          Position: "",
          ImportanceTypeId: null,
          CustomerStatusTypeId: null,
          CompanyOwnerEmployee: [],
          MainBusiness: "",
        };
      },
      /**选中行 */
      rowSelectionChanged(rows) {
        this.multipleSelection = rows;
      },
      /**按钮组 */
      onBtnClicked: function (domId) {
        // console.log('you click:' + domId)
        switch (domId) {
          case "btnAdd":
            this.handleCreate();
            break;
          case "btnEdit":
            if (this.multipleSelection.length !== 1) {
              this.$message({
                message: "只能选中一个进行编辑",
                type: "error"
              });
              return;
            }
            this.handleUpdate(this.multipleSelection[0]);
            break;
          case "btnDel":
            if (this.multipleSelection.length < 1) {
              this.$message({
                message: "至少删除一个",
                type: "error"
              });
              return;
            }
            this.handleDelete(this.multipleSelection);
            break;
          case "btnDetail":
            if (this.multipleSelection.length !== 1) {
              this.$message({
                message: "只能选中一个进行查看",
                type: "error"
              });
              return;
            }
            this.handleUpdate(this.multipleSelection[0], "detail");
            break;
          default:
            break;
        }
      },
      /**获取列表 */
      getList() {
        this.listLoading = true;
        customerOrganizationDetail
          .getCustomerOrganizationDetailListPage(this.listQuery)
          .then(response => {
            this.tabDatas = response.Items;
            this.total = response.Total;

            this.listLoading = false;
          });
      },
      /**查询 */
      handleFilter() {
        this.listQuery.PageIndex = 1;
        this.getList();
      },
      /**切换页大小 */
      handleSizeChange(val) {
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      /**切换页码 */
      handleCurrentChange(val) {
        this.listQuery.PageIndex = val.page;
        this.listQuery.PageSize = val.size;
        this.getList();
      },
      /**弹出添加框 */
      handleCreate() {
        this.resetTemp();
        this.dialogStatus = "create";
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
      },
      /**弹出编辑框 */
      handleUpdate(row, optType = "update") {
        // this.temp = Object.assign({}, row); // copy obj
        customerOrganizationDetail
          .getCustomerOrganizationDetail({ id: row.CustomerOrganizationDetailId })
          .then(response => {
            this.temp = response;
            this.temp.CompanyOwnerEmployee = this.temp.CompanyOwnerEmployee ? [this.temp.CompanyOwnerEmployee] : [];

            this.dialogStatus = optType;
            if (optType == "detail") {
              this.flowTabSelect = '客户项目';
              this.getCustomerDetail();
            }
            this.dialogFormVisible = true;
            this.$nextTick(() => {
              this.$refs["dataForm"].clearValidate();
            });
          });

      },
      getCustomerDetail() {
        customerArchive.getCustomerArchiveList({ OrganizationId: this.temp.OrganizationId }).then(response => {
          this.customerArchiveList = response;
        })
        customerProject.getCustomerProjectList({ OrganizationId: this.temp.OrganizationId }).then(response => {
          this.customerProjectList = response;
        })
      },
      /**保存提交 */
      createData() {
        let self = this;
        self.postLoading = true
        this.$refs["dataForm"].validate(valid => {
          if (!valid) {
            self.postLoading = false
          }
          if (valid) {
            if (self.temp.CompanyOwnerEmployee && self.temp.CompanyOwnerEmployee.length > 0) {
              self.temp.CompanyOwnerEmployeeId = self.temp.CompanyOwnerEmployee[0].EmployeeId;
            }
            let formData = JSON.parse(JSON.stringify(self.temp));
            let res = null;
            if (self.dialogStatus == "create") {
              delete formData.CustomerOrganizationDetailId;
              res = customerOrganizationDetail.addCustomerOrganizationDetail(formData);
            } else if (self.dialogStatus == "update") {
              res = customerOrganizationDetail.editCustomerOrganizationDetail(formData);
            }
            if (res) {
              res.then(response => {
                self.postLoading = false
                self.dialogFormVisible = false;
                self.$notify({
                  title: "成功",
                  message: "保存成功",
                  type: "success",
                  duration: 2000
                });
                this.getList();
              }).catch(err => {
                self.postLoading = false
              });
            }
          }
        });
      },
      /**多行删除 */
      handleDelete(rows) {
        let ids = [];
        if (_.isArray(rows)) {
          ids = rows.map(u => u.CustomerOrganizationDetailId);
        } else {
          ids.push(rows.CustomerOrganizationDetailId);
        }
        this.$confirm("是否确认删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          customerOrganizationDetail.deleteCustomerOrganizationDetail(ids).then(() => {
            this.$notify({
              title: "成功",
              message: "删除成功",
              type: "success",
              duration: 2000
            });
            this.getList();
          });
        });
      },
      handleChangeUsers(users) {
        if (users && users.length > 0) {
          this.listQuery.CompanyOwnerEmployee = [users[0]];
          this.listQuery.CompanyOwnerEmployeeId = users[0].EmployeeId;
        } else {
          this.listQuery.CompanyOwnerEmployee = [];
          this.listQuery.CompanyOwnerEmployeeId = "";
        }
      },
      onExport() {
        customerOrganizationDetail.exportCustomerOrganizationDetail(this.listQuery).then(res => {
          downloadFile(res.Url);
          console.log(res.Url);
        });
      },
      handleChangeDialogEmployee(users) {
        if (users && users.length > 0) {
          this.temp.CompanyOwnerEmployee = [users[0]]
        } else {
          this.temp.CompanyOwnerEmployee = []
        }
        this.$refs["dataForm"].validateField("CompanyOwnerEmployee");
      },
      indexMethod(index) {
        return (index += 1) + this.startOfTable;
      },
      formatterDate(row, column) {
        let f = this.$options.filters["dateFilter"];
        return f(row[column.property], "YYYY-MM-DD");
      },
      formatterTableDate(row, columnName) {
        let f = this.$options.filters["dateFilter"];
        return f(row[columnName], "YYYY-MM-DD");
      },
      formatterTableDateTime(row, columnName) {
        let f = this.$options.filters["dateFilter"];
        return f(row[columnName], "YYYY-MM-DD HH:mm:ss");
      },

    }
  };
</script>


<style scoped>
  .sel-ipt,
  .dat-ipt {
    width: 100%;
  }

  .dialog-mini .el-dialog__body .el-form {
    padding: 20px;
  }
</style>