<!--添加/修改-->
<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">

          <el-form-item label="分类" prop="ClassifyId">
            <treeselect :normalizer="normalizer" class="treeselect-common" :disabled="!editable" :options="ClassifyList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择所属分类" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" @input="hadnleChange"></treeselect>
          </el-form-item>

          <el-form-item label="模板名称" prop="TemplateName">
            <el-input v-model="formData.TemplateName" maxlength="50" type="text" placeholder="请输入模板名称" :disabled="!editable"></el-input>
          </el-form-item>

          <el-form-item label="模板描述说明" prop="TemplateRemark">
            <el-input type="textarea" :rows="3" placeholder="请输入模板描述说明" maxlength="500" v-model="formData.TemplateRemark" :disabled="!editable">
            </el-input>
          </el-form-item>

          <el-form-item label="模板内容设置" prop="TemplateContent">
            <el-input type="textarea" :rows="10" placeholder="请输入模板内容设置" maxlength="500" v-model="formData.TemplateContent" :disabled="!editable">
            </el-input>
          </el-form-item>
        </el-form>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary" :loading='disabledBtn'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script> 
import * as workReport from '@/api/personnelManagement/workReportTemplate';
import * as classify from '@/api/classify';
import { listToTreeSelect } from "@/utils";

let contentDefault = '【今天工作汇报】\n1、访问客户：\n2、访问站点：\n\n【明日工作计划】\n1、访问客户：\n2、访问站点：\n\n';
export default {
  name: "create",
  directives: {},
  components: {
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    selectClassifyId: {
      type: String,
      default: ""
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.ClassifyList = [];
      }
      if (val) {
        this.goOn = false;
        this.resetFormData();
        this.getClassifyList();
      }
    }
  },
  computed: {
    editable() {
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加工作汇报模板";
      } else if (this.dialogStatus == "update") {
        return "编辑工作汇报模板";
      } else if (this.dialogStatus == 'detail') {
        return '详情'
      }
      return ''
    },
  },
  mounted() { },
  created() { 
      this.rules = this.initRules(this.rules);
  },
  data() {

    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label.split(",")[0],
          id: node.Id,
          children: node.children
        };
      },

      disabledBtn: false,
      goOn: false,
      ClassifyList: [],

      formLoading: false,
      rules: {
        ClassifyId: { fieldName: "请选择所属分类", rules: [{ required: true, trigger: 'change' }] },
        TemplateName: { fieldName: "请输入模板名称", rules: [{ required: true }] },
        TemplateRemark: { fieldName: "请输入模板描述说明", rules: [{ required: true }] },
      },
      labelWidth: "120px",
      formData: {
        Id: "",
        ClassifyId: null,
        TemplateName: "",
        TemplateRemark: "",
        TemplateContent: this.dialogStatus == "create" ? contentDefault : '',
      }
    };
  },
  methods: {

    hadnleChange() {
      this.$refs.formData.validateField("ClassifyId");
    },

    //清理表单
    resetFormData() {
      let temp = {
        Id: "",
        ClassifyId: null,
        TemplateName: "",
        TemplateRemark: "",
        TemplateContent: this.dialogStatus == "create" ? contentDefault : '',
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    //获取分类下拉框
    getClassifyList() {
      this.formLoading = true;
      classify.getListByCondition({ BusinessType: 4 }).then(res => {
        var departments = res.Items.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.Name,
            ParentId: item.ParentId
          };
        });
        this.ClassifyList = listToTreeSelect(departments);
        if (this.selectClassifyId) {
          this.formData.ClassifyId = this.selectClassifyId;
        }
        if (this.dialogStatus != "create" && this.id) {
          this.$nextTick(() => {
            this.getDetail();
          });
        } else {
          this.formLoading = false;
        }
      }).catch(err => {
        this.formLoading = false;
      });
    },

    //获取详情
    getDetail() {
      workReport.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        this.formLoading = false;
      }).catch(err => {
        this.formLoading = false;
      });
    },

    //提交
    handleSave() {
      this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      Promise.all([listResult]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));

        if (this.dialogStatus == "create") {
          delete postData.Id;
          workReport.add(postData).then(res => {
            this.disabledBtn = false;
            this.$notify({
              title: '成功',
              message: '创建成功！',
              type: 'success'
            });
            if (this.goOn) {
              this.resetFormData();
              this.$refs['formData'].resetFields();
            }
            this.$emit('saveSuccess', this.goOn);
          }).catch(err => {
            this.disabledBtn = false;
          })
        } else {
          workReport.edit(postData).then(res => {
            this.disabledBtn = false;
            this.$notify({
              title: '成功',
              message: '编辑成功！',
              type: 'success'
            });
            this.$emit('saveSuccess', false);
          }).catch(err => {
            this.disabledBtn = false;
          })
        }
      }).catch(err => {
        this.disabledBtn = false;
      })
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
