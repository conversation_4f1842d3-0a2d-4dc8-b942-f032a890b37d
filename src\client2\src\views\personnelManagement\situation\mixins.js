
export default {
    data() {
        return {

        }
    },
    methods: {
        initDatas(recordObj) {
            if(recordObj && recordObj.DayPersonalTimecardRecord && recordObj.DayPersonalTimecardRecord.ProcessData) {
                recordObj.DayPersonalTimecardRecord.ProcessDataGroup = recordObj.DayPersonalTimecardRecord.ProcessData.reduce((prev, curr) => {
                    let group = prev.find(g => g.Type == curr.Type)
                    if(!group) {
                        prev.push({
                            Type: curr.Type, //大流程类型
                            List: []
                        })
                    }
                    group = prev.find(g => g.Type == curr.Type)
                    
                    let subGroup = group.List.find(g => g.LeaveType == curr.LeaveType)
                    if(!subGroup) {
                        group.List.push({
                            LeaveType: curr.LeaveType, //小流程类型
                            ProcessData: [curr]
                        })
                    }else{
                        subGroup.ProcessData.push(curr)
                    }

                    return prev;
                }, []);
            }
        },
    },
};
