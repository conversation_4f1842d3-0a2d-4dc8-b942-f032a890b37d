export const regs = {
    //手机
    phone: /^1[3-9]\d{9}$/,
    //邮箱
    email: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/,
    //身份证号码（15或18位）
    idCard: /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/,
    url: /(http|https):\/\/([\w.]+\/?)\S*/,
    fax: /^(\d{3,4}-)?\d{7,8}$/,
    //手机和座机
    // phoneAndTel: /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/,
    phoneAndTel: /^((0\d{2,3}-\d{7,8})|(1[3-9]\d{9}))$/,
    //座机
    Tel: /0\d{2,3}-\d{7,8}/,
    //组织机构代码
    OrganizingInstitutionBarCode: /[A-Z0-9]{8}-[A-Z0-9]$|[A-Z0-9]{8}-[A-Z0-9]-[0-9]{2}$/,
    //企业统一社会信用代码
    UnifiedSocialCreditCode: /[0-9]{13}$|[0-9]{13}-[0-9]{2}$/,
    //字母和数字
    LettersAndNumbers: /^[0-9a-zA-Z]+$/,
    //正整数和小数
    PositiveIntegersAndDecimals: /^\d+(\.\d{0,2})?$/,
    //正整数
    PositiveIntegers: /^[+]{0,1}(\d+)$/,
    //微信
    WeChat: /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/
}

