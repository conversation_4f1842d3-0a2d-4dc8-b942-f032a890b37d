<template>
  <div class="app-container">
    <div class="bg-white">
      <div class="pageWrapper">
        <div class="product-list">
          <div class="treeBox">
            <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node }">
                <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="content-wrapper __dynamicTabContentWrapper">
          <page-title :title="departmentInfo"></page-title>
          
          <div class="content __dynamicTabWrapper">
            <app-table :isShowBtnsArea='false' ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :isShowConditionArea='true'>

              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch"  :layoutMode='"simple"'>
                  <template slot="MultiCondition">
                    <el-input style="width: 100%;" @clear="getList" v-antiShake='{time: 300,callback: () => {getList()}}' clearable v-model.trim="listQuery.MultiCondition" placeholder="搜索姓名/工号"></el-input>
                  </template>
                  <!-- <template slot="Name">
                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Name" placeholder></el-input>
                  </template>
                  <template slot="Number">
                    <el-input style="width: 100%;" maxlength="20" v-model="listQuery.Number" placeholder></el-input>
                  </template> -->
                  <!-- <template slot="Year">
                    <el-select v-model="listQuery.Year" placeholder="" @change='getList'>
                      <el-option v-for="item in years" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </template> -->
                  
                  <template slot="btnsArea">
                    <permission-btn moduleName="position" :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked"></permission-btn>
                    <span style="margin-left: 4px;">
                      <el-button type="primary" v-if="auth('btnCheck')" @click="openCheckDialog">调休异常校正</el-button>
                      <el-button type="primary" v-if="auth('btnCheckRecord')" @click="openCheckRecordDialog">异常校正记录</el-button>
                    </span>
                  </template>
                  <!-- <template slot="otherArea">
                    <div style="width: 100%; text-align: right;">
                      
                    </div>
                  </template> -->

                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button @click="handleCheckHoliday(scope.row)" :type="2" text='查看记录'></app-table-row-button>
                <app-table-row-button v-if="rowBtnIsExists('btnBalanceEdit')" @click="handleCheckHoliday(scope.row, 'update')" :type="1"></app-table-row-button>
              </template>
            </app-table>
          </div>
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>

    <vHolidayBalanceRecord v-if="dialogFormVisible && currentRow" @closeDialog="closeHbrDialog" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :data='currentRow'>
    </vHolidayBalanceRecord>

    <computationRule @closeDialog="closeSettingDialog" :dialogFormVisible="dialogFormSettingVisible" @saveSuccess="handleSettingSaveSuccess"></computationRule>

    <!-- 导出 -->
    <v-export @saveSuccess="handleSuccessExport" @closeDialog="handleCloseExport" :dialogFormVisible="dialogExportVisible" :rData="rData" :cData='cData'>
    </v-export>

    <checkDialog @closeDialog="closeCheckDialog" :dialogFormVisible="dialogFormCheckVisible" @saveSuccess="handleCheckSaveSuccess"></checkDialog>
    <checkRecordDialog @closeDialog="closeCheckRecordDialog" :dialogFormVisible="dialogFormCheckRecordVisible" @saveSuccess="handleCheckRecordSaveSuccess"></checkRecordDialog>
    
    <!-- 校正记录详细 -->
    <checkRecordDetailDialog 
        v-if="dialogFormCheckRecordDetailVisible && recordId"
        @closeDialog="closeCheckRecordDetailDialog" 
        :dialogFormVisible="dialogFormCheckRecordDetailVisible"
        :id='recordId'
        :onlyShowList='true'
    ></checkRecordDetailDialog>
    
  </div>
</template>

<script>
import {
  listToTreeSelect
} from "@/utils";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as hrLeaveBalanceData from "@/api/personnelManagement/hrLeaveBalanceData"
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import vButtonList from '@/views/common/buttonList'
import vHolidayBalanceRecord from '../../../workbench/attendance/holidayBalanceRecord';
import computationRule from './computationRule';
import vExport from "@/components/Export/index";
import checkDialog from './checkDialog'
import checkRecordDialog from './checkRecordDialog'
import checkRecordDetailDialog from './checkRecordDetailDialog'

export default {
  name: "attendanceMgmt-holidays-index",
  mixins: [indexPageMixin],
  components: {
    vButtonList,
    vHolidayBalanceRecord,
    computationRule,
    vExport,
    checkDialog,
    checkRecordDialog,
    checkRecordDetailDialog,
  },
  props: {},
  filters: {
    nodutyFilter(value) {
      let duty = value.split(",");
      return duty[0];
    },
  },
  computed: {
    fildids() {
      return this.multipleSelection.map((s) => s.Id) || [];
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.PageIndex = 1;
          this.listQuery.DepartmentId = val.Id;
          this.selectDepartmentName = val.DepartmentName;
          this.getList();
        }
      },
      immediate: true,
    },
  },
  created() {
    let currentYear = (new Date()).getFullYear()
    this.years = Array.from(Array(5), (v, k) => {
      return {
        value: currentYear - k,
        label: currentYear - k
      }
    })
    // for(let i = currentYear - 4; i <= currentYear; i++) {
    //     this.years.push({
    //         value: i,
    //         label: i
    //     })
    // }

    this.getDepartments();
  },
  data() {
    return {
      dialogFormSettingVisible: false,

      dialogFormCheckVisible: false,
      dialogFormCheckRecordVisible: false,

      currentRow: null,
      years: [],
      // monList: Array.from(Array(12), (v,k) => {
      //     return {
      //         value: k + 1,
      //         label: `${k+1}月`
      //     }
      // }),
      epKeys: [],

      departmentInfo: "",
      selectDepartmentName: "",

      // statusEnum: statusEnum,

      filterText: "",

      treeLoading: false,
      treeDatas: [],
      defaultProps: {
        //树默认结构
        children: "children",
        label: "DepartmentName",
      },
      tableSearchItems: [
        { prop: "MultiCondition", label: "", mainCondition: true },
        // { prop: "Year", label: "年份"},
        // { prop: "Name", label: "姓名"},
        // // {
        // //     prop: "Mobile",
        // //     label: "手机号"
        // // },
        // { prop: "Number", label: "工号" },
      ],

      checkedNode: null, //当前单击选中的节点
      departmentListQuery: {
        DepartmentName: "",
      },


      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,

      listLoading: false,
      tabColumns: [{
        attr: {
          prop: "Name",
          label: "姓名",
          showOverflowTooltip: true
        },
      },
      // {
      //     attr: {
      //         prop: "Sex",
      //         label: "性别",
      //     },
      //     slot: true,
      // },
      {
        attr: {
          prop: "Number",
          label: "工号",
        },
      },
      {
        attr: {
          prop: "DepartmentName",
          label: "部门",
          showOverflowTooltip: true,
        },
      },
      {
        attr: {
          prop: "CompensatoryLeave",
          label: "剩余调休（天）",
        },
      },
      {
        attr: {
          prop: "AnnualLeave",
          label: "剩余年假（天）",
        },
      },
      ],
      listQuery: {
        DepartmentId: "",
        // Name: "",
        MultiCondition: "",
        // Mobile: "",
        // Number: '',
        // Year: (new Date()).getFullYear(),
        // Month: (new Date()).getMonth() + 1
      },
      multipleSelection: [],
      tabDatas: [], //原始数据
      total: 0,

      rData: null,
      cData: [],
      dialogExportVisible: false,

      recordId: '',
      dialogFormCheckRecordDetailVisible: ''

    };
  },
  methods: {
    auth(btnDomId) {
      return btnDomId && this.topBtns.findIndex(s => s.DomId.toLowerCase() == btnDomId.toLowerCase()) > -1
    },
    handleFilterBtn(btns) {
        let temp = btns
        if(btns && btns.length > 0) {
            return btns.filter(s => s.DomId != 'btnCheckRecord' && s.DomId != 'btnCheck')
        }
        return []
    },
    /**表头部点击 */
    onBtnClicked: function (type) {
      switch (type) {
        case "btnExport":
          this.handleExport();
          break;
        //年假计算规则
        case "btnComputationRule":
          this.dialogFormSettingVisible = true
          break;
        default:
          break;
      }
    },

    closeSettingDialog() {
      this.dialogFormSettingVisible = false
    },
    handleSettingSaveSuccess() {
      this.getList()
      this.closeSettingDialog()
    },
    
    openCheckDialog() {
      this.dialogFormCheckVisible = true
    },
    closeCheckDialog() {
      this.dialogFormCheckVisible = false
    },
    handleCheckSaveSuccess(recordId) {

      this.getList()
      this.closeCheckDialog()
      this.handleCheckRecordDetailDialog(recordId)
    },

    openCheckRecordDialog() {
      this.dialogFormCheckRecordVisible = true
    },
    closeCheckRecordDialog() {
      this.dialogFormCheckRecordVisible = false
    },
    handleCheckRecordSaveSuccess() {
      this.getList()
      this.closeCheckRecordDialog()
    },

    //获取成员列表
    getList() {
      if (this.checkedNode) {
        this.listLoading = true;
        let postData = JSON.parse(JSON.stringify(this.listQuery));
        // postData = this.assignSortObj(postData);

        hrLeaveBalanceData
          .getList(postData)
          .then((res) => {
            this.tabDatas = res.Items.map(s => {
              s.Department = s.DepartmentName
              return s
            });
            this.total = res.Total;
            this.listLoading = false;
            this.departmentInfo = this.selectDepartmentName;
          })
          .catch((err) => {
            this.listLoading = false;
          });
      }
    },
    // // 弹出编辑框
    // handleTableUpdate(row, optType = "update") {
    //     this.id = row.EmployeesId;
    //     this.dialogStatus = optType;
    //     this.dialogFormVisible = true;
    // },

    closeHbrDialog() {
      this.dialogFormVisible = false;
    },
    handleCheckHoliday(row, optType = "detail") {
      if(row) {
        timecardDepartment.getLeftDetails({id: row.Id}).then(res => {
          row.AnnualLeaveValidityDate = res.AnnualLeaveValidityDate

          this.currentRow = row
          this.id = row.Id;
          this.dialogStatus = optType;
          this.dialogFormVisible = true;
        })
      }
    },

    onResetSearch() {
      // this.listQuery.Name = "";
      // // this.listQuery.Mobile = "";
      // this.listQuery.Number = ''
      this.listQuery.MultiCondition = ""
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    /***************************************左侧树菜单操作***************************************/

    listToTreeSelect,

    //按关键字过滤树菜单
    filterNode(value, data) {
      if (!value) return true;
      return data.DepartmentName.indexOf(value) !== -1;
    },

    //获取左侧树菜单
    getDepartments() {
      this.treeLoading = true;
      systemDepartment
        .getListByCondition(this.departmentListQuery)
        .then((res) => {
          this.treeDatas = listToTreeSelect(res);

          if (this.treeDatas && this.treeDatas.length > 0) {
            this.treeDatas.forEach(v => {
              this.epKeys.push(v.Id);
              // if(v.children.length>0){
              //     v.children.forEach(v1 => {
              //         this.epKeys.push(v1.Id);
              //     })
              // }

            })
          }
          //如果首次加载问价夹树（没有选中），默认选中根节点
          if (!this.checkedNode) {
            this.setDefaultChecked();
          }
          this.treeLoading = false;
        });
    },
    handleCheckRecordDetailDialog(recordId) {
      this.recordId = recordId
      this.dialogFormCheckRecordDetailVisible = true
    },
    closeCheckRecordDetailDialog() {
      this.dialogFormCheckRecordDetailVisible = false
    },

    //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
    setDefaultChecked() {
      this.$nextTick(() => {
        if (this.treeDatas && this.treeDatas.length > 0) {
          let rootNode = this.treeDatas[0];
          this.$refs.tree.setCurrentKey(rootNode.Id);
          this.checkedNode = rootNode;
        }
      });
    },
    handleSuccessExport() { },
    handleCloseExport() {
      this.dialogExportVisible = false;
    },
    /**导出 */
    handleExport() {
      this.rData = {
        "exportSource": 20,
        "columns": [],
        "searchCondition": this.listQuery
      }
      this.cData = [{
        label: '序号',
        value: 'Idx'
      }]

      let temp = this.tabColumns.map(s => {
        return {
          label: s.attr.label,
          value: s.attr.prop
        }
      })

      this.cData = this.cData.concat(temp)

      this.dialogExportVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100% - 10px);
  margin-top: 10px;

  .elInput {
    width: 230px;
    margin-left: 10px;
  }

  .elTree {
    height: calc(100% - 42px);
    overflow: auto;
    margin-top: 10px;
    padding-bottom: 10px;
  }
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  // top: 40px;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;

    border-right: 1px solid #dcdfe6;
    // >div:first-child{
    //     display: flex;
    //     justify-content: space-between;
    //     align-items:center;
    //     padding:0 10px;
    // }
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      padding: 10px 0;
      // padding-right: 0;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
