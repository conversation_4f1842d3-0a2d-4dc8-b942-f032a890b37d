<!--巡检任务详情-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1200"
    >
      <template slot="body">
        <el-form
          :rules="formRules"
          ref="formRef"
          :model="formModel"
          label-position="right"
          label-width="120px"
          style="height:600px;overflow-y:auto;"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="任务名称" prop="Name">
                <el-input
                  maxlength="50"
                  v-model="formModel.Name"
                  :disabled="!editable||isFollowUp"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="24">
              <el-date-picker
                :disabled="!editable"
                v-model="formModel.Range"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="-"
                start-placeholder
                end-placeholder
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%;"
              ></el-date-picker>
            </el-col>
          </el-row>-->
          <el-row>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="StartTime">
                <el-date-picker
                  :disabled="!editable||isFollowUp"
                  style="width: 100%;"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="formModel.StartTime"
                  type="datetime"
                  placeholder
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="截止时间" prop="EndTime">
                <el-date-picker
                  :disabled="!editable||isFollowUp"
                  style="width: 100%;"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="formModel.EndTime"
                  type="datetime"
                  placeholder
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="巡检地区" prop="RegionId">
                <!-- <treeselect
                  :noResultsText="noResultsTextOfSelTree"
                  :noOptionsText="noOptionsTextOfSelTree"
                  :normalizer="normalizer"
                  :default-expand-level="3"
                  :options="areaData"
                  v-model="formModel.RegionId"
                  :multiple="false"
                  :show-count="false"
                  placeholder="请选择巡检地区"
                  :disabled="dialogStatus!='create'"
                ></treeselect> -->

                <div class="_regional_detail_wrapper">
                  <div class="btn_wrapper">
                    <el-button :disabled="dialogStatus!='create'" type="text" @click="handleRegionalDialog()">选择</el-button>
                  </div>
                  <div class="regional_text" :title="formModel.RegionalName">{{ formModel.RegionalName }}</div>
                  <div class="close_wrapper" v-show="formModel.RegionalName && !(dialogStatus!='create')">
                    <div class="i_wrapper">
                      <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                    </div>
                  </div>
                </div>


              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实施人员" prop="ImplementerList">
                <!-- <emp-selector
                  :readonly="!editable"
                  key="ccusers"
                  :showType="2"
                  :multiple="true"
                  :list="formModel.ImplementerList"
                  @change="handleChangeEmployees"
                ></emp-selector>-->
                
                <!-- 必须先选择地区 -->
                <el-input
                  placeholder
                  :value="names"
                  :title="names"
                  :disabled="(!editable||isFollowUp) || !formModel.RegionId"
                  :readonly="true"
                  unselectable="on"
                  class="input-with-select"
                >
                  <i
                    slot="suffix"
                    v-show="editable&&!isFollowUp"
                    @click="handleClear"
                    class="el-input__icon el-icon-close icon-close"
                  ></i>
                  <div slot="append">
                    <el-button
                      :disabled="(!editable||isFollowUp) || !formModel.RegionId"
                      style="padding: 7px 12px;"
                      icon="el-icon-more"
                      @click="showImplementerList"
                    ></el-button>
                  </div>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- <el-button type="text">选择</el-button> -->
            </el-col>
          </el-row>
          <div class="panel-title">
            <span>巡检设备</span>
            <el-button
              v-if="dialogStatus=='create'||dialogStatus=='update'"
              :disabled="!formModel.RegionId"
              type="primary"
              @click="showEquipmentList"
            >添加设备</el-button>
          </div>
          <el-row>
            <el-col :span="24">
              <el-table
                fit
                :data="formModel.EquipmentList"
                style="width: 100%;"
                v-loading="tableLoading"
              >
                <el-table-column type="index" label="序号"></el-table-column>
                <el-table-column prop="Code" label="设备编号"></el-table-column>
                <el-table-column prop="Name" label="加热炉/锅炉"></el-table-column>
                <!-- <el-table-column prop="EquipmentTypeName" label="类型"></el-table-column> -->
                <el-table-column prop="InstallTime" label="投产时间">
                  <template slot-scope="scope">{{formatterTime(scope.row.InstallTime)}}</template>
                </el-table-column>
                <el-table-column prop="RegionalName" label="所在地区"></el-table-column>
                <el-table-column
                  v-if="dialogStatus!='create'&&dialogStatus!='update'"
                  prop="PatrolTaskEquipmentStatus"
                  label="巡检情况"
                 
                >
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.PatrolTaskEquipmentStatus" :disabled="!editable">
                      <el-option
                        v-for="item in  PatrolTaskEquipmentStatusTypes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="dialogStatus!='create'&&dialogStatus!='update'"
                  prop="Remark"
                  label="备注"
                 
                >
                  <template slot-scope="scope">
                    <el-input maxlength="50" v-model="scope.row.Remark" :disabled="!editable"></el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="dialogStatus=='create'||dialogStatus=='update'"
                  label="操作"
                 
                >
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleDelete(scope.row,scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <!-- <div
            v-show="(dialogStatus == 'followUp'||dialogStatus == 'approval' || dialogStatus == 'detail') && showApproval"
            class="panel-title"
            style="margin-top:10px;"
          >
            <span>审批</span>
          </div>
          <div v-show="showApproval">
            <approval-panel
              v-if="dialogStatus == 'followUp' "
              :editable="editable"
              ref="approvalPanel"
              :approvalPanelObj="formModel.Approval"
            ></approval-panel>
            <approval-detail
              :isOnlyViewDetail="isOnlyViewDetail"
              v-if="dialogStatus == 'approval' || dialogStatus == 'detail'"
              ref="approvalDetail"
              :dialogStatus="dialogStatus"
              :approvalObj="formModel.Approval"
            ></approval-detail>
          </div> -->
        </el-form>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button v-show="editable" text="保存" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
        <el-button v-show="dialogStatus == 'followUp'" type="primary" :disabled="buttonLoading" @click="handleSubmitClick">提交</el-button>
        <!-- 审批模式下才显示审批 -->
        <!-- <el-button
          @click="handleApproval"
          type="primary"
          :disabled="buttonLoading"
          v-show="dialogStatus == 'approval' && !isOnlyViewDetail"
        >审批</el-button> -->
      </template>
    </app-dialog>

    <!--  弹窗组件区 -->
    <!--  :condition="{RegionalId:formModel.RegionId}" -->
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listSelectorType != 'Implementer' ? listQueryParams : listQueryEmp"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form v-if="listSelectorType != 'Implementer'" style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='Code'>
            <el-input style="width: 100%;" v-model="listQueryParams.Code" placeholder=""></el-input>
          </template>
          <template slot='Name'>
            <el-input style="width: 100%;" v-model="listQueryParams.Name" placeholder=""></el-input>
          </template>
          <template slot='OrderNumber'>
            <el-input style="width: 100%;" v-model="listQueryParams.OrderNumber" placeholder=""></el-input>
          </template>
        </app-table-form>

        <app-table-form v-else style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItemsEmp' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='Name'>
            <el-input style="width: 100%;" v-model="listQueryEmp.Name" placeholder=""></el-input>
          </template>
          <template slot='Number'>
            <el-input style="width: 100%;" v-model="listQueryEmp.Number" placeholder=""></el-input>
          </template>
        </app-table-form>

      </template>
      
      <template slot="InstallTime" slot-scope="scope">
          {{ scope.row.InstallTime | dateFilter('YYYY-MM-DD') }}
      </template>

    </listSelector>

    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="formModel.RegionId ? [formModel.RegionId] : []"
      :beforeConfirm='handleBeforeConfirm'
      :disabledFn="disabledFn"
    ></v-area-choose>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
// import approvalPanel from "@/views/projectDev/projectMgmt/common/approvalPanel";
// import approvalDetail from "@/views/projectDev/projectMgmt/workbench/common/approvalDetail";
import empSelector from "@/views/common/empSelector";
import listSelector from "@/views/common/listSelector";
import * as patrolTask from "@/api/afterSalesMgmt/patrolTask";
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import { serviceArea } from "@/api/serviceArea";
import { PatrolTaskEquipmentStatusType } from "@/utils/commonEnum";
import { vars } from "@/views/projectDev/common/vars";
import { listToTreeSelect } from "@/utils";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";

export default {
  /**名称 */
  name: "patrol-task-edit",
  /**组件声明 */
  components: { 
    // approvalPanel, approvalDetail,
    empSelector, listSelector, vAreaChoose },
  /**参数区 */
  props: {
    specialPageTitle: {
        type: String
    },
    /**主键Id */
    keyId: {
      type: String
    },
    //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
    // isOnlyViewDetail: {
    //   type: Boolean,
    //   default: false
    // },
    // approvalId: {
    //   type: String,
    //   default: null
    // },
    /**弹窗类型 */
    dialogStatus: {
      type: String,
      default: "create"
    }
  },
  /**数据区 */
  data() {
    return {
      /**树节点标准化 */
      normalizer(node) {
        return {
          label: node.RegionalName,
          id: node.Id,
          children: node.children
        };
      },
      areaData: [],
      regionData: [],
      oldArea: null,
      PatrolTaskEquipmentStatusTypes: PatrolTaskEquipmentStatusType,
      /**按钮在执行，不允许点击 */
      buttonLoading: false,
      /**表单模型 */
      formModel: {
        Name: "",
        StartTime: null,
        EndTime: null,
        Range: [],
        ImplementerList: [],
        ImplementerIdList: [],
        RegionId: null, rootId: "" //当前地区的最上级节点（油条id）
        // Approval: {
        //   //审批信息
        //   ApprovalEmployeeList: [[]],
        //   ApprovalType: 1,
        //   ApprovalOperatorEmployeeList: [], //已审批人员
        //   NoApprovalEmployeeList: [], //未审批人员
        //   CCEmployeeList: [], //抄送人
        //   ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
        //   ApprovalState: 1, //1: 进行中; 2: 已完成
        //   ApprovalResult: 1 //1: 通过； 2：不通过
        // }
      },
      /**表单规则 */
      formRules: {
        Name: { fieldName: "任务名称", rules: [{ required: true }] },
        StartTime: { fieldName: "开始时间", rules: [{ required: true }] },
        EndTime: { fieldName: "截止时间", rules: [{ required: true }] },
        ImplementerList: {
          fieldName: "实施人员",
          rules: [{ required: true, trigger: "change" }]
        },
        RegionId: { fieldName: "巡检地区", rules: [{ required: true, trigger: "change" }] }
      },
      /**显示审批区 */
      // showApproval: false,
      /**列表加载中 */
      tableLoading: false,
      listSelectorType: "",
      listSelectorCheckedData: [],
      listSelectorUrl: "",
      listSelectorMultiple: true,
      listQueryParams: {
        Code: '',
        Name: '',
        OrderNumber: '',
        IsSelectList: true
      },
      tableSearchItems: [
        { prop: "Code", label: "设备编号" },
        { prop: "Name", label: "加热炉/锅炉" },
        { prop: "OrderNumber", label: "订单号" },
      ],
      listQueryEmp: {
        Name: '',
        Number: ''
      },
      tableSearchItemsEmp: [
        { prop: "Name", label: "姓名" },
        { prop: "Number", label: "工号" },
      ],
      listSelectorTitle: "",
      listSelectorTopMessage: "",
      listSelectorKeyName: "",
      listSelectorColumnData: [],
      listSelectorDialogFormVisible: false,
      dialogRegionalVisible: false,
      
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail" && this.dialogStatus != "approval";
    },
    isFollowUp() {
      return this.dialogStatus == "followUp";
    },
    names() {
      if (this.formModel.ImplementerList.length > 0) {
        let tmp = this.formModel.ImplementerList.map(p => {
          if (p.Name && p.Number) {
            return `${p.Name}(${p.Number})`;
          }
          return "";
        });
        return tmp.join(",");
      }
      return "";
    },
    pageTitle() {
      if(this.specialPageTitle) {
        return this.specialPageTitle
      }
      let title = "";
      switch (this.dialogStatus) {
        case "create":
          title = "创建巡检任务";
          break;
        case "update":
          title = "编辑巡检任务";
          break;
        case "detail":
          title = "巡检任务详情";
          break;
        case "followUp":
          title = "巡检任务跟进";
          break;
        case "approval":
          title = "审批";
          break;
        default:
          title = "";
          break;
      }
      return title;
    }
  },
  /**监听 */
  watch: {
    // "formModel.RegionId": {
    //   handler(val, oldVal) {
    //     // let _this = this;
    //     // if (_this.oldArea) {
    //     //   if (_this.oldArea != val && _this.formModel.EquipmentList && _this.formModel.EquipmentList.length > 0) {
    //     //     this.$confirm("重新选择其他地区将清空故障设备列表信息,是否继续操作?", "提示",{
    //     //         confirmButtonText: "确认",
    //     //         cancelButtonText: "取消",
    //     //         type: "warning"
    //     //       }
    //     //     ).then(() => {
    //     //         _this.oldArea = _this.formModel.RegionId;
    //     //         _this.formModel.EquipmentList = [];
    //     //       }).catch(() => {
    //     //         _this.formModel.RegionId = _this.oldArea;
    //     //       });
    //     //   } else {
    //     //     _this.oldArea = _this.formModel.RegionId;
    //     //   }
    //     // } else {
    //     //   _this.oldArea = _this.formModel.RegionId;
    //     //   // _this.tableData = [];
    //     // }

    //     // if(oldVal !== null) {
    //     //   this.formModel.ImplementerList = []
    //     //   this.formModel.ImplementerIdList = []
    //     // }

    //   },
    //   immediate: true
    // },
    "$attrs.dialogFormVisible": {
      handler(val) {
        let _this = this;
        if (val) {
          _this.oldArea = null;
          regionalManagement.getListByCondition({}).then(response => {
            _this.regionData = response;
            _this.areaData = listToTreeSelect(response);
          });

          if (_this.dialogStatus == "create") {
            // _this.showApproval = false;
            _this.resetFormModel();
          } else {
            _this.tableLoading = true
            patrolTask
              .getDetails({ 
                id: _this.keyId
                // , approvalId: this.approvalId
               })
              .then(response => {
                _this.tableLoading = false
                // _this.showApproval = true;
                // if (!response.Approval) {
                //   if (this.dialogStatus == "detail") {
                //     _this.showApproval = false;
                //   }
                //   response.Approval = {
                //     //审批信息
                //     ApprovalEmployeeList: [[]],
                //     ApprovalType: 1,
                //     ApprovalOperatorEmployeeList: [], //已审批人员
                //     NoApprovalEmployeeList: [], //未审批人员
                //     CCEmployeeList: [], //抄送人
                //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //     ApprovalState: 1, //1: 进行中; 2: 已完成
                //     ApprovalResult: 1 //1: 通过； 2：不通过
                //   };
                // }
                _this.formModel = Object.assign({}, _this.formModel, response);
              }).catch(err => {
                _this.tableLoading = false
              });
          }
        }
      },
      immediate: true
    }
  },
  /**渲染前 */
  created() {
    this.formRules = this.initRules(this.formRules);
    if(this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    }
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    //地区选择
    closeRegionalDialog() {
      this.dialogRegionalVisible=false
    },
    handleRegionalDialog() {
      this.dialogRegionalVisible=true;
    },
    electedRegionalData(data){
        this.$refs.formRef.clearValidate('RegionId');

        //如果清空地区 或者 更换地区，清空 实施人员
        if(!data || this.formModel.RegionId != data.Id) {
          this.clearImplementerList()
        }
        
        if (data) {
          this.formModel.RegionId=data.Id;
          this.formModel.RegionalName=data.ParentName;
        }else{
          this.formModel.RegionId=null;
          this.formModel.RegionalName='';
        }
    },
    clearImplementerList() {
      this.formModel.ImplementerList = []
    },
    disabledFn(data, nodeType) {
        //禁选一级节点
        if(data.level <= 1) {
            return true
        }
        return false
    },
    async handleBeforeConfirm(datas) {
      let _this = this
      let result = false
      let oldVal = _this.formModel.RegionId
      //如果清空（没有选中）|| 更变地点
      if (((oldVal && !datas) || (oldVal && oldVal != datas.Id)) && _this.formModel.EquipmentList && _this.formModel.EquipmentList.length > 0) {
        await this.$confirm("重新选择其他地区将清空故障设备列表信息,是否继续操作?", "提示",{
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            type: "warning"
          }
        ).then(() => {
            result = true
            _this.formModel.RegionId = datas.Id;
            _this.formModel.EquipmentList = [];
          }).catch(() => {
            // _this.formModel.RegionId = _this.oldArea;
          });
      } else {
        result = true
      }
      return result

    },
    
    
    handleFilter() {
      this.$refs.listSelector.getDatas()
    },
    onResetSearch() {
      if(this.listSelectorType != 'Implementer') {
        this.listQueryParams.Code = ''
        this.listQueryParams.Name = ''
        this.listQueryParams.OrderNumber = ''
      }else{
        this.listQueryEmp.Name = ''
        this.listQueryEmp.Number = ''
      }
      this.handleFilter()
    },
    /**重置 */
    resetFormModel() {
      this.formModel.EquipmentList = [];
      this.formModel = {
        Name: "",
        StartTime: null,
        EndTime: null,
        Range: [],
        ImplementerList: [],
        ImplementerIdList: [],
        RegionId: null,
        EquipmentList: [],
        Approval: {
          //审批信息
          ApprovalEmployeeList: [[]],
          ApprovalType: 1,
          ApprovalOperatorEmployeeList: [], //已审批人员
          NoApprovalEmployeeList: [], //未审批人员
          CCEmployeeList: [], //抄送人
          ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
          ApprovalState: 1, //1: 进行中; 2: 已完成
          ApprovalResult: 1 //1: 通过； 2：不通过
        }
      };
    },
    /**删除设备 */
    handleDelete(row, index) {
      let _this = this;
      _this.formModel.EquipmentList.splice(index, 1);
    },
    /**提交 */
    handleSubmitClick() {
      if (
        this.formModel.EquipmentList.find(t => t.PatrolTaskEquipmentStatus == 1)
      ) {
        this.$confirm(`检测到有未检设备，是否继续提交？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.handleButtonClick("Submit");
        });
      } else {
        this.handleButtonClick("Submit");
      }
    },
    /**提交方法 */
    handleButtonClick(optType = null) {
      let _this = this;
      let validList = [];
      if (this.dialogStatus == "create" || this.dialogStatus == "update") {
        validList.push(this.$refs.formRef.validate());
      } else {
        // this.formModel.Approval = this.$refs.approvalPanel.getData(); //审批层区块
        // this.formModel.Approval.ApprovalEmployeeIdList = this.formModel.Approval.ApprovalEmployeeList.map(
        //   s => s.map(e => e.EmployeeId)
        // );
        // this.formModel.Approval.CCEmployeeIdList = this.formModel.Approval.CCEmployeeList.map(
        //   s => s.EmployeeId
        // );
      }
      if (this.dialogStatus == "followUp" && optType == "Submit") {
        // validList.push(this.$refs.approvalPanel.validate());
      }
      Promise.all(validList).then(valid => {
        if (valid) {
          if (
            !_this.formModel.EquipmentList ||
            !_this.formModel.EquipmentList.length
          ) {
            _this.$message({
              message: "至少需要添加一个设备",
              type: "error"
            });
            return;
          }
          let tempData = JSON.parse(JSON.stringify(_this.formModel));
          tempData.ImplementerIdList = tempData.ImplementerList.map(t => {
            return t.EmployeeId;
          });

          let result = null;
          switch (_this.dialogStatus) {
            case "create":
              result = patrolTask.add(tempData);
              break;
            case "update":
              tempData.approval = null;
              result = patrolTask.edit(tempData);
              break;
            case "followUp":
              if (optType != "Submit") {
                tempData.BtnType = "bc";//编辑状态专用
              }
              else{  
                  tempData.BtnType = "Submit";
              }
              result = patrolTask.edit(tempData);
              break;
            default:
              return;
              break;
          }

          _this.buttonLoading = true;
          result
            .then(response => {
              _this.buttonLoading = false;
              this.$notify({
                title: "成功",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              _this.$refs.appDialogRef.createData();
              this.handleClose();
            })
            .catch(err => {
              _this.buttonLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    /**审批 */
    // handleApproval() {
    //   let postData = this.$refs.approvalDetail.getData();
    //   postData.BusinessId = this.keyId;
    //   let approvalLabel = vars.approvalResult.find(
    //     s => s.value == postData.ApprovalResultState
    //   ).label;

    //   this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning"
    //   }).then(() => {
    //     //审批
    //     this.buttonLoading = true;
    //     patrolTask
    //       .approval(postData)
    //       .then(res => {
    //         this.buttonLoading = false;
    //         this.$notify({
    //           title: "提示",
    //           message: "审批成功",
    //           type: "success",
    //           duration: 2000
    //         });
    //         this.$refs.appDialogRef.createData();
    //         this.handleClose();
    //       })
    //       .catch(err => {
    //         this.buttonLoading = false;
    //       });
    //   });
    // },
       getRootId(districtId) {
      let curr = this.regionData.find(s => s.Id == districtId);
      if (curr && curr.ParentId) {
        this.getRootId(curr.ParentId);
      } else {
        this.rootId = districtId;
      }
    },
    /**展示实施人员选择器 */
    showImplementerList() {
      let _this = this;
      this.getRootId( _this.formModel.RegionId);
      _this.listSelectorType = "Implementer";
      _this.listSelectorCheckedData = _this.formModel.ImplementerList;
      _this.listSelectorUrl =
        serviceArea.business + "/ImplementerManagement/GetListPage";
      _this.listQueryEmp = Object.assign({}, _this.listQueryEmp, { RegionalId:   _this.rootId  });;
      _this.listSelectorMultiple = true;
      _this.listSelectorTitle = "添加实施人员";
      _this.listSelectorTopMessage = "";
      _this.listSelectorKeyName = "EmployeeId";
      _this.listSelectorColumnData = [
        {
          attr: { prop: "Name", label: "人员名称" }
        },
        {
          attr: { prop: "Number", label: "工号" }
        },
        {
          attr: { prop: "Mobile", label: "手机" }
        },
        {
          attr: { prop: "DepartmentName", label: "所属部门" }
        },
        {
          attr: { prop: "RegionalName", label: "负责地区" }
        }
      ];
      _this.listSelectorDialogFormVisible = true;
    },
    /**展示设备选择器 */
    showEquipmentList() {
      let _this = this;
      _this.listSelectorType = "Equipment";
      _this.listSelectorCheckedData = _this.formModel.EquipmentList;
      _this.listSelectorUrl =
        serviceArea.business + "/OrderEquipment/GetListPage";
      _this.listQueryParams = Object.assign({}, _this.listQueryParams, { RegionalId: _this.formModel.RegionId });
      _this.listSelectorMultiple = true;
      _this.listSelectorTitle = "添加设备";
      let tempEquipment = _this.regionData.find(
        t => t.Id == _this.formModel.RegionId
      );
      _this.listSelectorTopMessage = tempEquipment
        ? "已选中安装地区：" + tempEquipment.ParentName
        : "";
      _this.listSelectorKeyName = "Id";
      _this.listSelectorColumnData = [
        {
          attr: { prop: "Code", label: "设备编号" }
        },
        {
          attr: { prop: "Name", label: "加热炉/锅炉" }
        },
        {
          attr: { prop: "OrderNumber", label: "订单号" }
        },
        // {
        //   attr: { prop: "EquipmentTypeName", label: "类型" }
        // },
        {
          attr: { prop: "IsWarrantyName", label: "是否在保" }
        },
        {
          attr: { prop: "InstallTime", label: "投产时间" },
          slot: true
        }
      ];
      _this.listSelectorDialogFormVisible = true;
    },
    /**关闭 */
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
    },
    /**选择完成 */
    listSelectorSaveSuccess(data) {
      switch (this.listSelectorType) {
        case "Implementer":
          this.formModel.ImplementerList = data;
          break;
        case "Equipment":
          this.formModel.EquipmentList = data.map(t => {
            //   Id: "C105FBED-FC21-45FA-BC08-79A1854EDFA8",
            //   Code: "EN202005250001",
            //   Name: "新增设备",
            //   EquipmentTypeName: "设备类型",
            //   InstallTime: "安装时间",
            //   RegionalName: "安装地区",
            //   PatrolTaskEquipmentStatus: 1,
            //   Remark: "备注"
            t.PatrolTaskEquipmentStatus = t.PatrolTaskEquipmentStatus
              ? t.PatrolTaskEquipmentStatus
              : 1;
            t.Remark = t.Remark ? t.Remark : "";
            return t;
          });
          break;
        default:
          break;
      }
      this.listSelectorCloseDialog()
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    /**清除地区 */
    handleClear() {
      this.formModel.ImplementerList = [];
    },
    /**时间格式化 */
    formatterTime(val) {
      let _this = this;
      return _this.$options.filters["dateFilter"](val, "YYYY-MM-DD HH:mm");
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding: 10px;
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>


