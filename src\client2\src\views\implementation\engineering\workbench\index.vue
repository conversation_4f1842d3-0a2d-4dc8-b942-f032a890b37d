<template>
  <div class="app-container">
    <div class="appContainer">
      <!----------------------------------------------------- 工程信息区 ----------------------------------------------------->
      <page-title @goBack="handleGoBack" :title="pageTitle" :subTitle="[]" :showBackBtn="true">
        <relationListBtn 
            slot="oRight" 
            ref="relationListBtn"
            :businessType="businessType"
            :businessId="engId"
            @click.native="() => showDrawer = true"
        ></relationListBtn>
      </page-title>
      <div class="basicInformation">
        <el-row class="cus-row" :gutter="20">
          <el-col :span="4">
            工程状态：
            <span :style="{color: `${getEngStatus(engDetail.Status).color}`}">{{ engDetail.Status | engStatusFilter }}</span>
          </el-col>
          <el-col :span="7" style="text-align:center">
            <div style="display: flex;">
              工程整体进度：
              <div style="flex: 1;">
                <el-progress :percentage="engDetail.Progress" :color="getEngStatus(engDetail.Status).color"></el-progress>
              </div>
            </div>
          </el-col>
          <el-col :span="5" style="text-align:center">
            <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              工程负责人：
              <span :title="engDetail.EmployeeList?engDetail.EmployeeList.map(t=>{return t.Name;}).join(','):''">
                {{ engDetail.EmployeeList?engDetail.EmployeeList.map(t=>{return t.Name;}).join(','):'' }}
              </span>
            </div>
          </el-col>
          <el-col :span="4" style="text-align:center">总参与人数：{{ engDetail.EmployeeCount }}</el-col>
          <el-col :span="4" style="text-align: right;">
            <div class="log-wrapper" style="padding-right: 10px;" v-if="false">
              <el-button type="text" v-show="tabsActive && tabsActive != 'summary'" @click="handleShowLog">操作记录</el-button>
            </div>
            <!-- <div v-if="isEngMgmt()" v-show="tabsActive == 'summary'" style="padding-right: 10px;">
              <el-button type="primary" :disabled="!currentRegionId" @click="handleEditRegionDialog">编辑地区</el-button>
              <el-button type="danger" :disabled="!currentRegionId" @click="handleDel">删除地区</el-button>
            </div> -->
          </el-col>
        </el-row>
      </div>
      <div class="body-wrapper">
        <div class="product-list">
          <!----------------------------------------------------- 地区列表 ----------------------------------------------------->
          <div class="elInput">
            <el-input placeholder="搜索地区" v-model="filterText"></el-input>
          </div>
          <div class="btns-wrapper">
            <span class="btns-desc">地区列表({{ engDetail.ImplementationRegionalList.length }})</span>
            <div class="btns">
              <el-button type="primary" v-show="isEngMgmt()" @click="handleRegionDialog('create')">添加实施地区</el-button>
            </div>
          </div>
          <div class="versions-list" v-loading="regionListLoading">
            <div>
              <no-data v-show="engDetail.ImplementationRegionalList.length == 0"></no-data>
            </div>
            <tags class="tagsList" mode="list" :items="regionals" v-model="currentRegionId">
              <template v-for="(v, idx) in regionals" :slot="v.value" slot-scope="scope">
                <div class="item-warpper" :key="idx">
                  <div class="item-title omit" :title="v.label">{{ idx + 1 }}. {{ v.label ? v.label : '无' }}
                    <el-dropdown v-if="isEngMgmt()" v-show="tabsActive == 'summary'" trigger="click" style="float:right;" @command="handleDropdownCommand">
                      <span class="el-dropdown-link">
                        <i class="el-icon-more"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :disabled="!currentRegionId" command="update">编辑地区</el-dropdown-item>
                        <el-dropdown-item :disabled="!currentRegionId" command="delete">删除地区</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                  <div style="width:100%;">
                    <span class="item-status" style="width:auto;float:left" :style="{backgroundColor: `${getRegionalStatus(v.Status).color}`}">{{ v.Status | regionalStatusFilter }}</span>
                    <el-progress :percentage="v.Progress" :color="getEngStatus(v.Status).color" style="width:70%;float:right;"></el-progress>
                    <!-- &nbsp;&nbsp;实施情况：{{ v.IsFinished }}/{{ v.EquipmentCount }} -->
                  </div>
                </div>
              </template>
            </tags>
          </div>
        </div>
        <div class="content-wrapper" v-if="!currentRegionId">
          <no-data></no-data>
        </div>
        <div class="content-wrapper" v-else v-loading="loadingProces">
          <!-- <no-data v-show="!currentRegionId"></no-data> -->
          <el-tabs type="card" v-model="tabsActive">
            <el-tab-pane :key="tabDefault[0].name" :label="tabDefault[0].label" :name="tabDefault[0].name">
              <span slot="label">
                <div>
                  <span class="tab-title" :title="tabDefault[0].label">{{ tabDefault[0].label }}</span>
                </div>
              </span>
            </el-tab-pane>
            <el-tab-pane v-for="item in proces" :key="item.name" :label="item.label+`(${item.ImplementationEquipmentCount})`" :name="item.name">
              <span slot="label">
                <div class="tab-item-wrapper">
                  <span class="tab-title" :title="item.label">{{ item.label }}</span>
                  <!-- <span class="tab-num">{{ `(${item.ImplementationEquipmentCount})` }}</span> -->
                </div>
              </span>
            </el-tab-pane>
          </el-tabs>
          <div></div>
          <!----------------------------------------------------- 整体进度 ----------------------------------------------------->
          <div style="padding: 10px; overflow-y: auto; height: calc(100% - 50px);" v-show="tabsActive == 'summary'">
            <div class="summary">
              <div class="region-title-wrapper">
                <div class="region-title">{{ engDetail.TopRegionalName }}/{{ regionFullPath }}</div>
                <div class="region-btns">
                  <!-- <el-button
                    type="primary"
                    :disabled="!currentRegionId"
                    v-show="isRegionalMgmt()"
                    @click="handleProceDialog('update')"
                  >工序管理</el-button> -->
                </div>
              </div>
              <div class="summary-desc">
                <el-row :gutter="20">
                  <el-col :span="5">
                    <div class="col-wrapper" style="height: 18px;">
                      状态：
                      <span class="item-status" :style="{backgroundColor: `${getRegionalStatus(regionInfo.Status).color}`}">{{ regionInfo.Status | regionalStatusFilter }}</span>
                    </div>
                  </el-col>
                  <el-col :span="5">
                    <div class="col-wrapper">
                      站点负责人：
                      <span v-if="currRegion && currRegion.EmployeeList">{{ currRegion.EmployeeList.map(s => s.Name).join(',') }}</span>
                    </div>
                  </el-col>

                  <!-- <el-col :span="5">
                    <div style="display: flex;">
                      地区整体进度：
                      <div style="flex: 1;" v-if="regionInfo">
                        <el-progress :percentage="regionInfo.Progress"></el-progress>
                      </div>
                    </div> 
                  </el-col> -->

                  <el-col :key="item.Id" v-for="item in regionInfo.ImplementationProcedureList" :span="5">
                    <div class="col-wrapper cl">
                      <span class="col-title fl" :title="item.Name">{{ item.Name }}</span>
                      <span class="fl">{{"："+item.EmployeeList.map(t=>t.Name).join(',')}}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="tabs-wrapper">
              <!----------------------------------------------------- 问题tag ----------------------------------------------------->
              <tags :items="tabs2" v-model="tabsActive2">
                <template v-for="(t, idx) in tabs2" :slot="t.value">
                  <span v-show="t.value != 'quesList'" :key="'out' + idx">{{ t.label }}</span>
                  <el-badge v-show="t.value == 'quesList'" :value="regionInfo.WaitDealQuestionCount" class="item" :key="'inside' + idx">{{ t.label }}</el-badge>
                </template>
              </tags>
            </div>

            <!----------------------------------------------------- 设备列表 ----------------------------------------------------->
            <div v-show="tabsActive2 == 'equList'">
              <app-table ref="mainTable" :tab-columns="tabColumnsEqu" :tab-datas="tabDatasEqu" :tab-auth-columns="[]" :isShowAllColumn="isShowAllColumn" :loading="listLoadingEqu" @rowSelectionChanged="rowSelectionChangedEqu" :isShowOpatColumn="true" :startOfTable="0" :multable="true" :disabledList="tabDatasEqu.filter(s => s.ImplementationProcedureId).map(s => s.Id)">
                <template slot="Status" slot-scope="scope">
                  <span class="item-status" :style="{backgroundColor: `${getEquStatus(scope.row.Status).color}`}">{{ scope.row.Status | equStatusFilter }}</span>
                </template>
                <!-- <template
                  slot="ApprovalStatus"
                  slot-scope="scope"
                >{{ scope.row.ApprovalStatus | equApprovalStatusFilster }}</template> -->

                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                  <app-table-form :label-width="'80px'" :items="tableSearchItemsEqu" @onSearch="handleFilterEqu" @onReset="handleResetSearchEqu">
                    <template slot="ImplementationProcedureId">
                      <el-select style="width: 100%;" class="sel-ipt" v-model="listQueryEqu.ImplementationProcedureId" placeholder clearable>
                        <el-option v-for="item in proces" :key="item.name" :label="item.label" :value="item.name"></el-option>
                      </el-select>
                    </template>
                    <template slot="ProductListManagementId">
                      <treeselect :normalizer="normalizer2" key="type2" class="treeselect-common" v-model="listQueryEqu.ProductListManagementId" :zIndex="99999999" :default-expand-level="3" :options="products" :multiple="false" placeholder :show-count="true" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree">
                        <label :title="node.label" slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                          <span>
                            {{ node.label }}
                            <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                          </span>
                        </label>
                      </treeselect>
                    </template>
                    <template slot="Status">
                      <el-select style="width: 100%;" class="sel-ipt" v-model="listQueryEqu.Status" placeholder clearable>
                        <el-option v-for="item in equStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </template>
                  </app-table-form>
                </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                  <div class="row-btns">
                    <el-button type="primary" :disabled="!currentRegionId" v-show="isRegionalMgmt()" @click="handleEquDialog('create')">添加实施设备</el-button>
                    <!-- <el-button type="danger" v-show="isRegionalMgmt()" @click="handleEquDel('multiple')">删除实施设备</el-button> -->
                    <el-button type="primary" :disabled="!currentRegionId" v-show="isRegionalMgmt()" @click="handleExport">导入至工序</el-button>
                  </div>
                </template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                  <app-table-row-button v-if="scope.row.ImplementationProcedureId" @click="handleWithdraw(scope.row)" :type="1" text="撤回" v-show="isRegionalMgmt()"></app-table-row-button>
                  <!-- <app-table-row-button v-if="!scope.row.ImplementationProcedureId" @click="handleChangeDialog('create', scope.row)" :type="1" text="变更" v-show="isRegionalMgmt()"></app-table-row-button> -->
                  <app-table-row-button v-if="!scope.row.ImplementationProcedureId" @click="handleEditDialog('update', scope.row)" :type="1" v-show="isRegionalMgmt()"></app-table-row-button>
                  <app-table-row-button v-if="!scope.row.ImplementationProcedureId" @click="handleEquDel('single', scope.row)" :type="3" v-show="isRegionalMgmt()"></app-table-row-button>
                </template>
              </app-table>
              <pagination v-show="totalEqu>0" :total="totalEqu" :page.sync="listQueryEqu.PageIndex" :size.sync="listQueryEqu.PageSize" @pagination="handleCurrentChangeEqu" @size-change="handleSizeChangeEqu" />
            </div>

            <!----------------------------------------------------- 问题列表区域 ----------------------------------------------------->

            <div class="tabs-wrapper" v-show="tabsActive2 == 'quesList'">
              <tags :items="tabs3" v-model="tabsActive3">
                <template v-for="t in tabs3" :slot="t.value">{{ t.label }}</template>
              </tags>
            </div>
            <div v-show="tabsActive2 == 'quesList'">
              <app-table ref="mainTable" :tab-columns="tabColumnsQues" :tab-datas="tabDatasQues" :tab-auth-columns="[]" :isShowAllColumn="isShowAllColumn" :loading="listLoadingQues" @rowSelectionChanged="rowSelectionChangedQues" :isShowOpatColumn="true" :startOfTable="0" :multable="true">
                <template slot="Status" slot-scope="scope">
                  <span class="item-status" :style="{backgroundColor: `${getQuestionStatus(scope.row.Status).color}`}">{{ scope.row.Status | questionStatusFilter }}</span>
                </template>
                <template slot="EmployeeList" slot-scope="scope">
                  <span v-if="scope.row.EmployeeList">{{ scope.row.EmployeeList.map(s => s.Name).join(',') }}</span>
                </template>
                <template slot="CreateEmployee" slot-scope="scope">
                  <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
                </template>
                <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>

                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                  <app-table-form :label-width="'80px'" :items="tableSearchItemsQues" @onSearch="handleFilterQues" @onReset="handleResetSearchQues">
                    <template slot="Status">
                      <el-select style="width: 100%;" class="sel-ipt" v-model="listQueryQues.Status" placeholder clearable>
                        <el-option v-for="item in ImplementationQuestionStatusType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </template>
                    <template slot="EmployeeId">
                      <el-select style="width: 100%;" class="sel-ipt" v-model="listQueryQues.EmployeeId" placeholder clearable>
                        <el-option v-for="item in questionEmployees" :key="item.EmployeeId" :label="item.Name" :value="item.EmployeeId"></el-option>
                      </el-select>
                    </template>
                  </app-table-form>
                </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                  <div class="row-btns">
                    <el-button type="primary" :disabled="!currentRegionId" v-show="isRegionalMgmt() || isProcesMgmt() || isEngMgmt()" @click="handleQuesDialog('create')">创建问题</el-button>
                  </div>
                </template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                  <app-table-row-button @click="handleQuesDialog('detail', scope.row)" :type="2"></app-table-row-button>
                  <!-- 只有处理人 且 不为 已处理  -->
                  <app-table-row-button v-show="isOwner(scope.row) && scope.row.Status != 3" @click="handleAssign(scope.row)" :type="1" text="指派"></app-table-row-button>
                  <app-table-row-button v-show="isOwner(scope.row) && scope.row.Status != 3" @click="handleQuesDialog('update', scope.row)" :type="1" text="处理"></app-table-row-button>
                  <!-- 只有创建人可删除(已处理 不可删除) -->
                  <app-table-row-button v-show="isCreator(scope.row) && scope.row.Status != 3" @click="handleDelQues(scope.row)" :type="3"></app-table-row-button>
                </template>
              </app-table>
              <pagination v-show="totalQues>0" :total="totalQues" :page.sync="listQueryQues.PageIndex" :size.sync="listQueryQues.PageSize" @pagination="handleCurrentChangeQues" @size-change="handleSizeChangeQues" />
            </div>
          </div>
          <!----------------------------------------------------- 工序详情 ----------------------------------------------------->

          <div style="padding: 10px 0 0 10px;height:calc(100% - 44px);" v-show="tabsActive != 'summary'" v-loading="listLoadingProces">
            <div class="whole" style="padding-bottom: 10px; border-bottom: 1px solid #dcdfe6;">
              <el-row :gutter="20">
                <el-col :span="5">
                  <div style="display: flex;">工序整体进度：<div style="flex: 1;">
                      <el-progress :percentage="procesDetail.Progress" :color="getEngStatus(procesDetail.Progress == 0 ? 1 : procesDetail.Progress == 100 ? 11 : 2).color"></el-progress>
                    </div>
                  </div>
                </el-col>
                <!-- <el-col :span="5">状态：{{ procesDetail.Progress == 0 ? '未开始' : procesDetail.Progress == 100 ? '已完成' : '进行中' }}</el-col> -->
                <el-col :span="5">开始时间：{{ procesDetail.StartTime | dateFilter('YYYY-MM-DD HH:mm', '无') }}</el-col>
                <el-col :span="5">截止时间：{{ procesDetail.EndTime | dateFilter('YYYY-MM-DD HH:mm', '无') }}</el-col>
                <el-col :span="4">剩余工期：{{procesDetail.DaysRemaining?(( procesDetail.DaysRemaining > 0 ?procesDetail.DaysRemaining:('已逾期'+(-procesDetail.DaysRemaining)))+'天'):'无'}}</el-col>
                <el-col :span="10">
                  <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; ">
                    工序负责人：
                    <span :title="procesDetail.EmployeeList ? procesDetail.EmployeeList.map(t=>{return t.Name;}).join(',') : ''">
                      {{ procesDetail.EmployeeList ? procesDetail.EmployeeList.map(t=>{return t.Name;}).join(',') : '' }}
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>
            <!----------------------------------------------------- 事项列表 ----------------------------------------------------->
            <div class="process-wrapper">
              <div style="width:100%;padding-top:10px">
                <!-- 表格批量操作区域 -->
                <div class="btns">
                  <el-button v-show="isProcesMgmt() || isResponsible()" type="primary" @click="handleProcesDialog">事项管理</el-button>
                </div>
                <div class="tab-wrapper">
                  <app-table-core ref="mainTable3" :tab-columns="tabColumnsProces" :tab-datas="tabDatasProces" :tab-auth-columns="[]" :isShowAllColumn="isShowAllColumn" :loading="listLoadingProces" @rowSelectionChanged="rowSelectionChangedProces" :isShowOpatColumn="true" :startOfTable="0" :multable="false" :isShowConditionArea="false">
                    <template slot="Status" slot-scope="scope">
                      <span class="item-status" :style="{backgroundColor: `${getProcesStatus(scope.row.Status).color}`}">{{ scope.row.Status | procesStatusFilter }}</span>
                    </template>
                    <template slot="Progress" slot-scope="scope">
                      <el-progress class="elProgress" :color="getProcesStatus(scope.row.Status).color" :percentage="scope.row.Progress"></el-progress>
                    </template>
                    <template slot="Remark" slot-scope="scope">{{ scope.row.Remark ? scope.row.Remark : '无' }}</template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                      <app-table-row-button @click="handleProcesCreateDialog(scope.row, 'detail')" :type="1" text="详情"></app-table-row-button>
                      <app-table-row-button @click="handleProcesCreateDialog(scope.row, 'update')" :type="1" text="进度更新" v-show="isProcesMgmt() || isResponsible()"></app-table-row-button>
                    </template>
                  </app-table-core>
                  <!-- <pagination v-show="totalProces>0" :total="totalProces" :page.sync="listQueryProces.PageIndex" :size.sync="listQueryProces.PageSize" @pagination="handleCurrentChangeProces" @size-change="handleSizeChangeProces" /> -->
                </div>
              </div>
              <div class="right" v-if="false">
                <!-------------------------------------------------- 工序设备列表 ----------------------------------------------------->
                <div class="btn-wrapper">
                  <el-checkbox style="padding-right: 10px;" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                  <el-button type="danger" :disabled="currentProcesIdx == 0" @click="handleMove('up')" v-show="isProcesMgmt()">上个工序</el-button>
                  <el-button type="primary" :disabled="currentProcesIdx == proces.length - 1" @click="handleMove('down')" v-show="isProcesMgmt()">下个工序</el-button>
                </div>

                <div v-if="!tabProceEqusLoading && tabProceEqus.length == 0">
                  <no-data></no-data>
                </div>
                <div v-else class="right-list">
                  <div v-for="(equ, idx) in tabProceEqus" :key="idx" v-loading="tabProceEqusLoading" class="item-wrapper">
                    <!-- <div class=""> -->
                    <div class="title-wrapper">
                      <div>
                        <input type="checkbox" :disabled="equ.Status == 3" v-model="checkedEqus" :value="equ.Id" />
                      </div>
                      <div class="item-title" @click="handleEquDetailDialog(equ)">
                        {{ equ.Name }}
                        <!-- <el-checkbox
                            :disabled="equ.Status == 3"
                            v-model="checkedEqus"
                            :label="equ.Id"
                            :title="equ.Name"
                        >{{ equ.Name }}</el-checkbox>-->
                      </div>
                      <div>
                        <el-dropdown v-show="isProcesMgmt()" trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, equ)">
                          <span class="el-dropdown-link">
                            <i class="el-icon-more"></i>
                          </span>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-show="s.value != 1 && s.value != equ.Status" v-for="(s, idx) in equStatus" :key="idx" :command="s.value">{{ s.label }}</el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                    <!-- </div> -->
                    <div class="cl">
                      <div style="width:auto;display: inline-block; margin-right: 4px;" class="fl">
                        <span class="item-status" style="display: inline-block" :style="{backgroundColor: `${getEquStatus(equ.Status).color}`}">{{ equ.Status | equStatusFilter }}</span>
                      </div>
                      <div style="display: inline-block;" :title="equ.ProductListManagementName" class="fl">
                        <span style="display: inline-block; box-sizing: border-box; max-width: 100px; overflow: hidden; text-overflow: ellipsis;white-space: nowrap; color: #303133; border: 1px solid #dcdfe6;" class="item-status">{{ equ.ProductListManagementName }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 地区弹框 -->
    <region-page v-if="engDetail.TopRegionalId && engDetail.ImplementationTemplateId && dialogRegionFormVisible" @closeDialog="closeRegiomDialog" @saveSuccess="handleRegionSaveSuccess" :dialogFormVisible="dialogRegionFormVisible" :dialogStatus="dialogRegionStatus" :id="engId" :engDetail="engDetail" :implementationTemplateId="engDetail.ImplementationTemplateId"></region-page>

    <!-- 工序管理 -->
    <!-- <procedure-page
      v-if="currRegion && dialogProceFormVisible"
      @closeDialog="closeProceDialog"
      @saveSuccess="handleProceSaveSuccess"
      :dialogFormVisible="dialogProceFormVisible"
      :dialogStatus="dialogProceStatus"
      :id="engId"
      :implementationRegionalId="currRegion.Id"
    ></procedure-page> -->

    <!-- 编辑地区 -->
    <edit-region v-if="engDetail.TopRegionalId && currRegion && dialogEditRegionFormVisible" @closeDialog="closeEditRegionDialog" @saveSuccess="handleEditRegionSaveSuccess" :dialogFormVisible="dialogEditRegionFormVisible" :region="currRegion" :topRegionalId="engDetail.TopRegionalId" :disabledList="engDetail.ImplementationRegionalList.map(s => s.RegionalId)"></edit-region>

    <!-- 添加实施设备 -->
    <equ-page v-if="currRegion && dialogEquionFormVisible" @closeDialog="closeEquiomDialog" @saveSuccess="handleEquionSaveSuccess" :dialogFormVisible="dialogEquionFormVisible" :dialogStatus="dialogEquStatus" :regionFullPath="regionFullPath" :implementationRegionalId="currRegion.Id"></equ-page>

    <!-- 导入工序 -->
    <app-dialog title="导入工序" ref="appDialogRef" @closeDialog="closeExportProceDialog" @saveSuccess="handleExportProceSaveSuccess" :dialogFormVisible="dialogExportProceFormVisible" :maxHeight="600" :width="600">
      <template slot="body">
        <div class="export-content">
          <div>已选中（{{ multipleSelectionEqu.length }}）</div>
          <div>请选择需导入的工序</div>
          <div>
            <div v-for="(item, idx) in proces" :key="idx">
              <el-radio v-model="newImplementationProcedureId" :label="item.name">{{ item.label }}</el-radio>
            </div>
          </div>
        </div>
      </template>
    </app-dialog>

    <!-- 上个工序 -->
    <app-dialog title="返回上一个工序" ref="appDialogRef2" @closeDialog="closePrevProceDialog" @saveSuccess="handlePrevProceSaveSuccess" :dialogFormVisible="dialogPrevProceFormVisible" :maxHeight="600" :width="600">
      <template slot="body">
        <el-form :rules="prevFormRules" ref="prevFormData" :model="prevFormData" label-position="top" label-width="100px">
          <div class="prev-dialog-wrapper">
            <el-row>
              <el-col :span="24">
                <el-form-item label="返回原因" prop="Remark">
                  <el-input maxlength="2000" type="textarea" :rows="5" v-model="prevFormData.Remark"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <div>
          <!-- 取消 -->
          <app-button @click="closePrevProceDialog" :buttonType="2"></app-button>
          <!-- 确认 -->
          <app-button @click="handlePrevProceSaveSuccess" :buttonType="1" :disabled="disabledBtn"></app-button>
        </div>
      </template>
    </app-dialog>

    <!-- 问题 创建、处理、详情 -->
    <ques-create @closeDialog="closeQuesDialog" @saveSuccess="handleQuesSaveSuccess" :dialogFormVisible="dialogQuesFormVisible" :dialogStatus="dialogQuesStatus" :impRegionalId="currRegion?currRegion.value:''" :id="quesId"></ques-create>

    <!-- 问题指派 -->
    <assign-page v-if="currentRow" :row="currentRow" @closeDialog="closeAssignDialog" @saveSuccess="handleAssignSaveSuccess" :dialogFormVisible="dialogAssignFormVisible"></assign-page>

    <!-- 实施事项管理 -->
    <proces-mgmt v-if="tabsActive != 'summary' && tabsActive" @closeDialog="closeProcesDialog" @saveSuccess="handleProcesSaveSccess" :dialogFormVisible="dialogProcesFormVisible" :id="tabsActive"></proces-mgmt>

    <!-- 事项 详情、编辑 -->
    <proces-create @closeDialog="closeProcesCreateDialog" @saveSuccess="handleProcesCreateSaveSuccess" :dialogFormVisible="dialogProcesCreateFormVisible" :dialogStatus="dialogProcesCreateStatus" :procesId="procesId"></proces-create>

    <!-- 实施设备变更 -->
    <!-- <change-page
      v-if="currentEquRow"
      @closeDialog="closeChangeDialog"
      @saveSuccess="handleChangeSaveSuccess"
      :dialogFormVisible="dialogChangeFormVisible"
      :dialogStatus="dialogChangeStatus"
      :equId="currentEquRow.Id"
    ></change-page> -->

    <!-- 编辑设备 -->
    <equ-edit v-if="currentEquRow" @closeDialog="closeEditDialog" @saveSuccess="handleEditSaveSuccess" :dialogFormVisible="dialogEditFormVisible" :dialogStatus="dialogEditStatus" :equId="currentEquRow.Id">
    </equ-edit>

    <!-- 操作记录 -->
    <div class="drawer-wrapper">
      <el-drawer :title="currentProcesTitle" :visible.sync="drawer" :direction="direction" :before-close="handleClose" :show-close="false" custom-class="demo-drawer-xxxx">
        <template slot="title">
          <div style="display: flex;">
            <div style="flex: 1;">{{ currentProcesTitle }}</div>
            <div>
              <i style="cursor: pointer;" class="el-icon-close" @click="() => drawer = false"></i>
            </div>
          </div>
        </template>
        <div class="elTimeBox">
          <el-timeline v-loading="drawerLoading">
            <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.CreateTime.split('.')[0]">
              <div class="elTimeDiv cl">
                <span class="fl cl">
                  <i class="c409EFF fl">{{activity.CommentContent.Employee | emName}}&nbsp;</i>
                  <i class="fl">将&nbsp;</i>
                  <i class="c409EFF fl omit" :title="activity.CommentContent.EquipmentList | filterNames">{{activity.CommentContent.EquipmentList | filterNames}}</i>
                </span>
                <span class="fl">
                  {{activity.CommentContent.Type}}
                  <i class="c409EFF">{{activity.CommentContent.Status | varsFilter(activity.CommentContent)}}</i>
                </span>
              </div>
              <div style="word-wrap: break-word; white-space:normal; word-break:break-all;" v-show="activity.CommentContent.Remark">返回原因：{{activity.CommentContent.Remark}}</div>
            </el-timeline-item>
          </el-timeline>
          <no-data v-show="!drawerLoading && activities.length == 0"></no-data>
        </div>
      </el-drawer>
    </div>

    <!-- 设备详情 -->
    <app-dialog title="设备详情" ref="appDialogRef3" @closeDialog="closeEquDetailDialog" :dialogFormVisible="dialogEquDetailFormVisible" :maxHeight="600" :width="600">
      <template slot="body">
        <div class="equ-detail-wrapper">
          <div class="row-wrapper">
            <div class="row-title">设备名称：</div>
            <div class="row-content">{{ equDetail.Name }}</div>
          </div>
          <div class="row-wrapper">
            <div class="row-title">类型：</div>
            <div class="row-content">{{ equDetail.ProductListManagementName }}</div>
          </div>
          <div class="row-wrapper">
            <div class="row-title">备注：</div>
            <div class="row-content">
              <span v-if="equDetail.Remark">{{ equDetail.Remark }}</span>
              <span v-else>无</span>
            </div>
          </div>
        </div>
      </template>
      <template slot="footer">
        <div>
          <!-- 取消 -->
          <app-button @click="closeEquDetailDialog" :buttonType="2"></app-button>
        </div>
      </template>
    </app-dialog>

    <el-drawer
        v-if="engId"
        title=""
        :visible.sync="showDrawer"
        :with-header="false">
        <relationList :businessId="engId" :readonly="false" :businessType="businessType" @reload="() => {$refs.relationListBtn.reload()}" @close="() => showDrawer = false"></relationList>
    </el-drawer>
  </div>
</template>


<script>
import noData from "../../../common/components/noData";
import regionPage from "./region";
import editRegion from "./editRegion";
import equPage from "./equPage";
// import procedurePage from "./procedurePage";
import quesCreate from "./quesCreate";
import procesMgmt from "./procesMgmt";
import procesCreate from "./procesCreate";
import assignPage from "./assign";
// import changePage from "./change";
import equEdit from './equEdit';
import * as impMgmt from "@/api/implementation/impManagement2";
import * as prodMgmt from "@/api/systemManagement/productListManagement";
import * as impManagement from "@/api/implementation/impManagement";
import mixins from "../../common/mixins";
import indexPageMixin from "@/mixins/indexPage";
import * as projVars from "../../../projectDev/common/vars";
import { getUserInfo } from "@/utils/auth";
import { vars } from "../../common/vars";
import { listToTreeSelect } from "@/utils";
import { ImplementationQuestionStatusType } from "@/utils/commonEnum";

import relationListBtn from '../../../workbench/mainLineMgmt/relationListBtn'
import relationList from '../../../workbench/mainLineMgmt/relationList'

export default {
  name: "engineering-workbench-index",
  components: {
    noData,
    regionPage,
    // procedurePage,
    equPage,
    editRegion,
    quesCreate,
    procesMgmt,
    procesCreate,
    assignPage,
    // changePage,
    equEdit,
    relationListBtn,
    relationList,

  },
  mixins: [indexPageMixin, mixins],
  computed: {
    regionals() {
      if (this.engDetail && this.engDetail.ImplementationRegionalList) {
        return this.engDetail.ImplementationRegionalList.filter(s => {
          let temp = s.label.split("/");
          return temp.some(s => s.indexOf(this.filterText) > -1);
        });
      }
      return [];
    },
    pageTitle() {
      if (this.engDetail) {
        return this.engDetail.ImplementName;
      }
      return "加载中...";
    },
    regionFullPath() {
      if (
        this.engDetail.ImplementationRegionalList &&
        this.engDetail.ImplementationRegionalList.length > 0
      ) {
        let currRegion = this.engDetail.ImplementationRegionalList.find(
          s => s.value == this.currentRegionId
        );
        if (currRegion) {
          return currRegion.label;
        }
      }
      return "";
    },
    currRegion() {
      if (
        this.engDetail.ImplementationRegionalList &&
        this.engDetail.ImplementationRegionalList.length > 0
      ) {
        let currRegion = this.engDetail.ImplementationRegionalList.find(
          s => s.value == this.currentRegionId
        );
        return currRegion;
      }
      return null;
    },
    //当前工序索引
    currentProcesIdx() {
      return this.proces.findIndex(s => s.name == this.tabsActive);
    },
    currentProcesTitle() {
      let temp = this.proces.find(s => s.name == this.tabsActive);
      if (temp) {
        return temp.label;
      }
      return "";
    }
  },
  watch: {
    engId(val) {
      if (val) {
        // this.getEngDetail()
        this.getRegionList();
      }
    },
    currentRegionId(val) {

      this.listQueryEqu.PageIndex = 1
      this.listQueryQues.PageIndex = 1
      // this.listQueryProces.PageIndex = 1

      if (val) {
        this.tabsActive = "summary";
        // this.getRegionInfo(); //获取工序 和 地区详情
        this.getQuesList(); //获取问题列表
        // this.getEquList(); //获取设备列表
      }
    },
    tabsActive(val) {
      if (val && val != "summary") {
        this.checkedEqus = []; //清空选中的工序设备
        this.getImpProceDetails(); //获取工序详情
        this.getProcesList(); //获取工序事项列表
        // this.getProcesEqus(); //获取工序设备列表
      }
    },
    tabsActive3(val) {
      this.getQuesList();
    },
    dialogExportProceFormVisible(val) {
      if (!val) {
        this.newImplementationProcedureId = "";
      }
    },
    drawer(val) {
      if (val) {
        this.getImplementDetail(); //获取操作日志
      }
    }
  },
  filters: {
    // equApprovalStatusFilster(status) {
    //   let tmp = vars.equApprovalStatus.find(s => s.value == status);
    //   if (tmp) {
    //     return tmp.label;
    //   }
    //   return "";
    // },
    equStatusFilter(status) {
      let tmp = vars.equStatus.find(s => s.value == status);
      if (tmp) {
        return tmp.label;
      }
      return status;
    },
    questionStatusFilter(status) {
      let tmp = vars.questionStatus.find(s => s.value == status);
      if (tmp) {
        return tmp.label;
      }
      return status;
    },
    emName(d) {
      if (d) {
        return d.Name;
      } else {
        return "";
      }
    },
    filterNames(a) {
      if (a.length > 0) {
        let arr = a.map(v => v.Name);
        return arr.join("、");
      } else {
        return "";
      }
    },
    varsFilter(s, d) {
      if (s == 0) {
        return d.ProcedureName;
      } else {
        let r = vars.processStatus.find(v => v.value == s);
        return r.label;
      }
    }
  },
  created() {
    // this.getEngDetail()
    this.prevFormRules = this.initRules(this.prevFormRules);
    this.getRegionList();
  },
  data() {
    return {
      showDrawer: false,
      businessType: 7,

      returnUrl: this.$route.query.returnUrl,
      filterText: "",
      normalizer2(node) {
        return {
          id: node.Id,
          label: node.ProductName,
          children: node.children
        };
      },
      ImplementationQuestionStatusType: ImplementationQuestionStatusType,
      equStatus: vars.equStatus, //设备状态
      questionStatus: vars.questionStatus,
      engId: this.$route.query.id, //工程id
      engDetail: {
        //工程详情
        ImplementationRegionalList: [] //地区列表
      },

      regionListLoading: false, //地区加载loading
      currentRegionId: "", //当前选中的地区id

      /**
       * 地区弹框
       */
      dialogRegionStatus: "create",
      dialogRegionFormVisible: false,

      /**
       * 工序管理
       */
      // dialogProceStatus: "create",
      // dialogProceFormVisible: false,

      /**
       * 编辑地区
       */
      dialogEditRegionFormVisible: false,

      /**
       * 添加实施设备
       */
      dialogEquStatus: "create",
      dialogEquionFormVisible: false,


      dialogEditFormVisible: false,
      dialogEditStatus: "create",

      /**
       * 实施设备变更
       */
      // dialogChangeFormVisible: false,
      // dialogChangeStatus: "create",
      currentEquRow: null,

      /**
       * 导入工序
       */
      dialogExportProceFormVisible: false,

      /**
       * 设备详情
       */
      dialogEquDetailFormVisible: false,
      equDetail: {},

      /**
       * 上一个工序
       */
      dialogPrevProceFormVisible: false,
      disabledBtn: false,
      prevFormData: {
        Remark: ""
      },
      prevFormRules: {
        Remark: {
          fieldName: "返回原因",
          rules: [{ required: true, trigger: "change" }]
        }
      },

      //新工序
      newImplementationProcedureId: "",

      /**
       * 问题 创建、处理、详情
       */
      dialogQuesStatus: "create",
      dialogQuesFormVisible: false,

      /**
       * 指派
       */
      dialogAssignFormVisible: false,
      currentRow: null,

      tabsActive: "summary",
      //工序
      tabDefault: [{ label: "整体进度", name: "summary" }],
      //工序
      loadingProces: false,
      proces: [],
      //地区详情
      regionInfo: {},
      tabs2: [
        // { label: "设备列表", value: "equList" },
        { label: "问题列表", value: "quesList" }
      ],
      tabsActive2: "quesList",
      tabs3: [
        { label: "待我处理", value: "true" },
        { label: "全部问题", value: "false" }
      ],
      tabsActive3: "true",

      isShowAllColumn: true,
      /**
       * 设备列表
       */
      totalEqu: 0,
      tabDatasEqu: [],
      listQueryEqu: {
        PageIndex: 1,
        PageSize: 20,
        ImplementationProcedureId: null,
        ProductListManagementId: null,
        Status: null
      },
      listLoadingEqu: false,
      multipleSelectionEqu: [],
      tableSearchItemsEqu: [
        { prop: "ImplementationProcedureId", label: "工序" },
        { prop: "ProductListManagementId", label: "类型" },
        { prop: "Status", label: "状态" }
      ],
      tabColumnsEqu: [
        {
          attr: { prop: "Name", label: "设备名称", showOverflowTooltip: true }
        },
        {
          attr: { prop: "Status", label: "状态" },
          slot: true
        },
        // {
        //   attr: { prop: "ApprovalStatus", label: "审批状态" },
        //   slot: true
        // },
        {
          attr: { prop: "ProductListManagementName", label: "类型" }
        },
        {
          attr: { prop: "Remark", label: "备注", showOverflowTooltip: true }
        },
        {
          attr: {
            prop: "ImplementationProcedureName",
            label: "当前工序",
          }
        }
      ],

      /**
       * 问题列表
       */
      totalQues: 0,
      tabDatasQues: [],
      listQueryQues: {
        PageIndex: 1,
        PageSize: 20,
        Status: null,
        EmployeeId: null
      },
      listLoadingQues: false,
      multipleSelectionQues: [],
      tableSearchItemsQues: [
        { prop: "Status", label: "状态" },
        { prop: "EmployeeId", label: "处理人" }
      ],
      tabColumnsQues: [
        {
          attr: { prop: "Name", label: "问题名称" }
        },
        {
          attr: { prop: "Status", label: "状态" },
          slot: true
        },
        {
          attr: { prop: "EmployeeList", label: "处理人" },
          slot: true
        },
        {
          attr: { prop: "CreateEmployee", label: "创建人" },
          slot: true
        },
        {
          attr: { prop: "CreateTime", label: "创建时间" },
          slot: true
        }
      ],

      quesId: "",

      /**
       * 工序详情
       */
      procesDetail: {},

      /**
       * 事项列表
       */
      // totalProces: 0,
      tabDatasProces: [],
      // listQueryProces: {
      //     PageIndex: 1,
      //     PageSize: 20,
      // },
      listLoadingProces: false,
      multipleSelectionProces: [],
      tableSearchItemsProces: [],
      tabColumnsProces: [
        {
          attr: { prop: "Name", label: "实施事项" }
        },
        {
          attr: { prop: "Status", label: "状态" },
          slot: true
        },
        {
          attr: { prop: "Progress", label: "进度" },
          slot: true
        },
        {
          attr: { prop: "Remark", label: "备注", showOverflowTooltip: true },
          slot: true
        }
      ],

      /**
       * 实施事项管理
       */
      dialogProcesFormVisible: false,

      /**
       * 事项编辑
       */
      procesId: "",
      dialogProcesCreateStatus: "detail",
      dialogProcesCreateFormVisible: false,

      /**
       * 工序设备列表
       */
      tabProceEqus: [],
      tabProceEqusLoading: false,
      checkAll: false,
      checkedEqus: [],
      products: [],
      questionEmployees: [],

      /**
       * 操作记录
       */
      drawer: false,
      direction: "rtl",
      activities: [],
      drawerLoading: false
    };
  },
  methods: {

    handleDropdownCommand(activeName, row) {
      if (activeName == "update") {
        this.handleEditRegionDialog();
      } else {
        this.handleDel();
      }
    },
    // //获取工程详情
    // getEngDetail() {
    //     if (this.engId) {
    //         projMgmt.detail({ id: this.engId }).then(res => {
    //             this.engDetail = res

    //         })
    //     }
    // },
    //根据实施工程ID获取地区下的实施地区与地区基本信息
    getRegionList() {
      if (this.engId) {
        let tempId = this.engId;
        // tempId = "636fe3f9-bd1d-489b-adff-e6b4f9ab425d";
        this.regionListLoading = true;
        impMgmt
          .getRegionList({ id: tempId })
          .then(res => {
            this.regionListLoading = false;
            this.engDetail = res;
            this.engDetail.ImplementationRegionalList = this.engDetail.ImplementationRegionalList.map(
              s => {
                s.value = s.Id;
                s.label = s.RegionalName;
                return s;
              }
            );
            if (this.engDetail.ImplementationRegionalList && this.engDetail.ImplementationRegionalList.length > 0) {
              //当前选中地区为空 或者 当前选中地区，不存在当前地区列表中（删除地区）
              if (!this.currentRegionId || !this.engDetail.ImplementationRegionalList.find(s => s.value == this.currentRegionId)) {
                this.currentRegionId = this.engDetail.ImplementationRegionalList[0].value;
              }
            } else {
              this.currentRegionId = ''
            }
            //测试
            // this.engDetail.TopRegionalId =
            //   "a5b60381-0e20-4c04-b8b3-d697e2d2277e";
            // this.engDetail.TopRegionalName = "塔河油田";
          })
          .catch(err => {
            this.regionListLoading = false;
          });
      }
    },
    //获取某个地区整体视图
    getRegionInfo() {
      this.loadingProces = true;
      this.proces = [];
      impMgmt
        .getImplementationDetail({ id: this.currentRegionId })
        .then(res => {
          this.loadingProces = false;
          this.regionInfo = res;
          this.proces = res.ImplementationProcedureList.map(s => {
            s.label = s.Name;
            s.name = s.Id;
            return s;
          });

          this.listQueryEqu = {
            PageIndex: 1,
            PageSize: 20,
            ImplementationProcedureId: null,
            ProductListManagementId: null,
            Status: null
          };
          this.listQueryQues = {
            PageIndex: 1,
            PageSize: 20,
            Status: null,
            EmployeeId: null
          };
          this.getProductList();
          this.getQuestionEmployeeList();
        })
        .catch(err => {
          this.loadingProces = false;
        });
    },
    //返回
    handleGoBack() {
      let pageIndex = this.$route.query.pageIndex

      let targetUrl = `/implementation/engineering`

      if (pageIndex && pageIndex > 0) {
        targetUrl += '?pageIndex=' + pageIndex + '&rId=' + this.$route.query.rId
      }

      targetUrl = this.getReturnUrl(targetUrl, this.returnUrl)

      // if (!this.returnUrl) { //如果没有returnUrl，直接返回父页面
      // }else if(this.returnUrl && this.$route.query.returnUrlUseType == 2) {//如果有returnUrl，但是不能使用该值（等于没有），返回父页面，并且returnUrl带回父页面
      //     targetUrl += `?returnUrl=` + this.returnUrl
      // }else if(this.returnUrl) { //如果有 returnUrl，并且可以直接使用，则跳转到 returnUrl 对应页面
      //     targetUrl = this.returnUrl
      // }

      this.$router.push({
        path: targetUrl
      });
    },
    /**获取产品列表 */
    getProductList() {
      prodMgmt.getListByCondition({}).then(res => {
        this.products = listToTreeSelect(res);
      });
    },
    /**获取问题处理人 */
    getQuestionEmployeeList() {
      impMgmt
        .getImplementationQuestionEmployeeList({ id: this.currentRegionId })
        .then(res => {
          this.questionEmployees = res;
        });
    },

    /**地区弹框 */
    handleRegionDialog(activeName) {
      this.dialogRegionStatus = activeName;
      this.dialogRegionFormVisible = true;
    },
    closeRegiomDialog() {
      this.dialogRegionFormVisible = false;
    },
    handleRegionSaveSuccess(_formData) {
      this.getRegionList();
      this.closeRegiomDialog();
    },

    /**工序管理 */
    // handleProceDialog(activeName) {
    //   this.dialogProceStatus = activeName;
    //   this.dialogProceFormVisible = true;
    // },
    // closeProceDialog() {
    //   this.dialogProceFormVisible = false;
    // },
    // handleProceSaveSuccess(_formData) {
    //   this.getRegionList()
    //   this.getRegionInfo();
    //   this.closeProceDialog();
    // },

    /**编辑地区 */
    handleEditRegionDialog(activeName) {
      this.dialogEditRegionFormVisible = true;
    },
    /**关闭地区编辑弹窗 */
    closeEditRegionDialog() {
      this.dialogEditRegionFormVisible = false;
    },
    /**地区编辑成功 */
    handleEditRegionSaveSuccess(newRegional) {
      if (this.engDetail && this.engDetail.ImplementationRegionalList) {
        let oldRegionIdx = this.engDetail.ImplementationRegionalList.findIndex(
          s => s.Id == newRegional.Id
        );
        this.engDetail.ImplementationRegionalList.splice(
          oldRegionIdx,
          1,
          newRegional
        );
      }
      this.closeEditRegionDialog();
    },

    /**删除地区 */
    handleDel() {
      if (this.tabDatasEqu && this.tabDatasEqu.length > 0) {
        this.$message.error("请先删除该地区的所有设备信息");
      } else {
        this.$confirm("是否确认删除该地区", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          impMgmt
            .delImpRegion([this.currentRegionId])
            .then(res => {
              this.$notify({
                title: "提示",
                message: "审批成功",
                type: "success",
                duration: 2000
              });
              this.getRegionList();
            })
            .catch(err => { });
        });
      }
    },

    /**添加实施设备 */
    handleEquDialog(activeName) {
      this.dialogEquStatus = activeName;
      this.dialogEquionFormVisible = true;
    },
    /**关闭设备弹窗 */
    closeEquiomDialog() {
      this.dialogEquionFormVisible = false;
    },
    /**设备保存成功 */
    handleEquionSaveSuccess(_formData) {
      this.getEquList();
      this.closeEquiomDialog();
    },

    /**删除实施设备 */
    handleEquDel(delType, row) {
      if (delType == "multiple" && this.multipleSelectionEqu.length == 0) {
        this.$message.error("请选择需要删除的设备");
        return false;
      }

      let ids = [];
      let tip = "";
      if (delType == "multiple") {
        tip = "选中";
        ids = this.multipleSelectionEqu.map(s => s.Id);
      } else {
        tip = "当前";
        ids = [row.Id];
      }

      if (ids.length > 0) {
        this.$confirm("是否确认删除" + tip + "设备", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          impMgmt.delImpEqu(ids).then(res => {
            this.$notify({
              title: "提示",
              message: "操作成功",
              type: "success",
              duration: 2000
            });
            this.getRegionList();
            this.getEquList();

          });
        });
      }
    },

    /**导入工序 */
    handleExport() {
      if (this.multipleSelectionEqu.length == 0) {
        this.$message.error("请选择需要导入的设备");
        return false;
      }
      if (this.multipleSelectionEqu.some(s => s.ImplementationProcedureId)) {
        this.$message.error("选择导入的设备中，不能存在工序");
        return false;
      }
      this.handleExportProceDialog();
    },
    /**导入工序弹窗 */
    handleExportProceDialog(activeName) {
      this.dialogExportProceFormVisible = true;
    },
    /**关闭导入工序 */
    closeExportProceDialog() {
      this.dialogExportProceFormVisible = false;
    },
    /**导入工序保存成功 */
    handleExportProceSaveSuccess(_formData) {
      if (!this.newImplementationProcedureId) {
        this.$message.error("请选择需要导入的目标工序");
        return false;
      }

      let postData = {
        ImplementationEquipmentIdList: this.multipleSelectionEqu.map(s => s.Id), //设备集合
        OldImplementationProcedureId: null, //旧工序（导入时为null）
        ImplementationProcedureId: this.newImplementationProcedureId, //选择的工序
        IsNextProcedure: null
      };
      impMgmt.setEquProce(postData).then(res => {
        this.$notify({
          title: "提示",
          message: "导入成功",
          type: "success",
          duration: 2000
        });
        this.getRegionList();
        this.getEquList();
        this.getRegionInfo();
        this.closeExportProceDialog();
      });
    },

    /** 上一个工序 */
    handlePrevProceSaveSuccess() {
      this.$refs.prevFormData.validate(valid => {
        if (valid) {
          let postData = {
            ImplementationEquipmentIdList: this.checkedEqus, //选中设备
            OldImplementationProcedureId: this.tabsActive, //
            IsNextProcedure: false,
            ImplementationProcedureId: null,
            Remark: this.prevFormData.Remark
          };
          impMgmt.setEquProce(postData).then(res => {
            this.$notify({
              title: "提示",
              message: "移动成功",
              type: "success",
              duration: 2000
            });
            this.checkedEqus = [];
            this.getProcesEqus();
            this.closePrevProceDialog();
            this.getRegionInfo();
          });
        }
      });
    },

    resetPrevFormData() {
      this.prevFormData = {
        Remark: ""
      };
    },
    handlePrevDialog() {
      this.dialogPrevProceFormVisible = true;
    },
    closePrevProceDialog() {
      this.dialogPrevProceFormVisible = false;
      this.resetPrevFormData();
    },

    /**设备撤回 */
    handleWithdraw(row) {
      this.$confirm("是否确认撤回当前工序", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let postData = {
          ImplementationEquipmentIdList: [row.Id], //设备集合
          OldImplementationProcedureId: row.ImplementationProcedureId, //
          ImplementationProcedureId: null, //选择的工序
          IsNextProcedure: null
        };
        impMgmt.setEquProce(postData).then(res => {
          this.$notify({
            title: "提示",
            message: "撤回成功",
            type: "success",
            duration: 2000
          });
          this.getEquList();
          this.getRegionList();
          this.getRegionInfo();
        });
      });
    },


    closeEditDialog() {
      this.dialogEditFormVisible = false;
    },
    //设备变更保存成功
    handleEditSaveSuccess() {
      this.getEquList();
      this.closeEditDialog();
    },
    // 设备变更
    handleEditDialog(optType, row) {
      this.currentEquRow = row;
      this.dialogEditStatus = optType;
      this.dialogEditFormVisible = true;
    },

    // //设备变更关闭
    // closeChangeDialog() {
    //   this.dialogChangeFormVisible = false;
    // },
    // //设备变更保存成功
    // handleChangeSaveSuccess() {
    //   this.getEquList();
    //   this.closeChangeDialog();
    // },
    // // 设备变更
    // handleChangeDialog(optType, row) {
    //   this.currentEquRow = row;
    //   this.dialogChangeStatus = optType;
    //   this.dialogChangeFormVisible = true;
    // },

    /**设备列表 */
    getEquList() {
      if (!this.currentRegionId) {
        return false;
      }

      let postData = JSON.parse(JSON.stringify(this.listQueryEqu));
      postData.ImplementationRegionalId = this.currentRegionId;
      this.listLoadingEqu = true;
      impMgmt
        .getEqus(postData)
        .then(res => {
          this.listLoadingEqu = false;
          this.tabDatasEqu = res.Items;
          this.totalEqu = res.Total;
        })
        .catch(err => {
          this.listLoadingEqu = false;
        });
    },
    /**设备行点击 */
    rowSelectionChangedEqu(rows) {
      this.multipleSelectionEqu = rows;
    },
    /**设备查询点击 */
    handleFilterEqu() {
      this.listQueryEqu.PageIndex = 1;
      this.getEquList();
    },
    /**设备重置查询 */
    handleResetSearchEqu() {
      this.listQueryEqu = {
        // 否则手动重置查询条件
        PageIndex: this.listQueryEqu.PageIndex,
        PageSize: this.listQueryEqu.PageSize
      };
      this.getEquList(); //刷新列表
    },

    handleEquDetailDialog(row) {
      this.equDetail = row;
      this.dialogEquDetailFormVisible = true;
    },
    closeEquDetailDialog() {
      this.dialogEquDetailFormVisible = false;
    },

    /**设备分页切换页码 */
    handleCurrentChangeEqu(val) {
      this.listQueryEqu.PageIndex = val.page;
      this.listQueryEqu.PageSize = val.size;
      this.getEquList();
    },
    /**设备分页大小 */
    handleSizeChangeEqu(val) {
      this.listQueryEqu.PageSize = val.size;
      this.getEquList();
    },

    /**问题列表 */
    getQuesList() {
      if (!this.currentRegionId) {
        return false;
      }
      //问题操作后，需要刷新“问题列表”右上角数量
      this.getRegionInfo(); //获取工序 和 地区详情

      let postData = JSON.parse(JSON.stringify(this.listQueryQues));
      postData.ImplementationRegionalId = this.currentRegionId;
      postData.IsMine = this.tabsActive3 == "true";
      this.listLoadingQues = true;
      impMgmt
        .getQues(postData)
        .then(res => {
          this.listLoadingQues = false;
          this.tabDatasQues = res.Items;
          this.totalQues = res.Total;

          let count = 0;
          this.tabs3.forEach(item => {
            if (item.value == "true") {
              //带我处理
              count = res.WaitDealCount;
            } else if (item.value == "false") {
              count = res.AllCount;
            }
            if (item.label.indexOf("(") > -1) {
              item.label = item.label.replace(/[0-9]+/gi, count);
            } else {
              item.label += `(${count})`;
            }
          });
        })
        .catch(err => {
          this.listLoadingQues = false;
        });
    },
    /**问题行选中 */
    rowSelectionChangedQues(rows) {
      this.multipleSelectionQues = rows;
    },
    /**问题查询点击 */
    handleFilterQues() {
      this.listQueryQues.PageIndex = 1;
      this.getQuesList();
    },
    /**问题重置查询 */
    handleResetSearchQues() {
      this.listQueryQues = {
        // 否则手动重置查询条件
        PageIndex: this.listQueryQues.PageIndex,
        PageSize: this.listQueryQues.PageSize
      };
      this.getQuesList(); //刷新列表
    },
    /**问题分页页码切换 */
    handleCurrentChangeQues(val) {
      this.listQueryQues.PageIndex = val.page;
      this.listQueryQues.PageSize = val.size;
      this.getQuesList();
    },
    /**问题分页大小 */
    handleSizeChangeQues(val) {
      this.listQueryQues.PageSize = val.size;
      this.getQuesList();
    },
    //是否为问题处理人
    isOwner(row) {
      let ids = row.EmployeeList.map(s => s.EmployeeId);
      let currentEmpId = getUserInfo().employeeid;
      return ids.some(s => s == currentEmpId);
    },
    /**是否创建人 */
    isCreator(row) {
      return (
        row.CreateEmployee &&
        row.CreateEmployee.EmployeeId == getUserInfo().employeeid
      );
    },

    /**问题 添加、编辑、详情 */
    handleQuesDialog(activeName, row) {
      if (row) {
        this.quesId = row.Id;
      }
      this.dialogQuesStatus = activeName;
      this.dialogQuesFormVisible = true;
    },
    /**关闭问题弹窗 */
    closeQuesDialog() {
      this.dialogQuesFormVisible = false;
    },
    /**问题保存成功 */
    handleQuesSaveSuccess(newRegional) {
      this.getQuesList();
      this.getQuestionEmployeeList();
      this.closeQuesDialog();
    },

    /**指派 */
    handleAssign(row) {
      this.currentRow = row;
      this.dialogAssignFormVisible = true;
    },
    /**关闭指派 */
    closeAssignDialog() {
      this.dialogAssignFormVisible = false;
    },
    /**指派保存成功 */
    handleAssignSaveSuccess(newRegional) {
      this.getQuesList();
      this.closeAssignDialog();
    },
    //获取工序详情
    getImpProceDetails() {
      impMgmt.getImpProceDetails({ id: this.tabsActive }).then(res => {
        this.procesDetail = res;
      });
    },
    /**处理问题 */
    handleDelQues(row) {
      this.$confirm(`是否确认删除 ${row.Name}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        impMgmt.delQues([row.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getQuesList();
        });
      });
    },

    /**工序事项列表 */
    getProcesList() {
      // let postData = JSON.parse(JSON.stringify(this.listQueryProces))
      let postData = {
        ImplementationProcedureId: this.tabsActive
      };

      this.listLoadingProces = true;
      impMgmt
        .getImpItems(postData)
        .then(res => {
          this.listLoadingProces = false;
          this.tabDatasProces = res;
          // this.totalProces = res.Total
        })
        .catch(err => {
          this.listLoadingProces = false;
        });
    },

    // 工序设备列表
    getProcesEqus() {
      let postData = {
        ImplementationProcedureId: this.tabsActive,
        ImplementationRegionalId: this.currentRegionId
      };
      this.tabProceEqusLoading = true;
      impMgmt
        .getEqusNoPage(postData)
        .then(res => {
          this.tabProceEqusLoading = false;
          this.tabProceEqus = res;
        })
        .catch(err => {
          this.tabProceEqusLoading = false;
        });
    },
    /**工序行选择 */
    rowSelectionChangedProces(rows) {
      this.multipleSelectionProces = rows;
    },
    /**筛选工序 */
    handleFilterProces() {
      this.listQueryProces.PageIndex = 1;
      this.getProcesList();
    },
    /**重查工序 */
    handleResetSearchProces() {
      this.listQueryProces = {
        // 否则手动重置查询条件
        PageIndex: this.listQueryProces.PageIndex,
        PageSize: this.listQueryProces.PageSize
      };
      this.getProcesList(); //刷新列表
    },
    // handleCurrentChangeProces(val) {
    //     this.listQueryProces.PageIndex = val.page;
    //     this.listQueryProces.PageSize = val.size;
    //     this.getProcesList();
    // },
    // handleSizeChangeProces(val) {
    //     this.listQueryProces.PageSize = val.size;
    //     this.getProcesList();
    // },
    /**实施事项管理 */
    handleProcesDialog(activeName) {
      this.dialogProcesFormVisible = true;
    },
    /**关闭工序管理 */
    closeProcesDialog() {
      this.dialogProcesFormVisible = false;
    },
    /**工序管理保存 */
    handleProcesSaveSccess(newRegional) {
      this.getImpProceDetails()
      this.getProcesList();
      this.getRegionList()
      this.closeProcesDialog();

    },
    //是否为工程负责人
    isEngMgmt() {
      if (this.engDetail && this.engDetail.EmployeeList) {
        let currentEmpId = getUserInfo().employeeid;
        return (
          this.engDetail.EmployeeList.map(s => s.EmployeeId).findIndex(
            s => s == currentEmpId
          ) > -1
        );
      }
      return false;
    },
    //是否为站点负责人
    isRegionalMgmt() {
      if (this.currRegion && this.currRegion.EmployeeList) {
        let currentEmpId = getUserInfo().employeeid;
        return (
          this.currRegion.EmployeeList.map(s => s.EmployeeId).findIndex(
            s => s == currentEmpId
          ) > -1
        );
      }
      return false;
    },
    //是否为工序负责人
    isProcesMgmt() {
      let process = this.proces.find(s => s.name == this.tabsActive);
      if (process) {
        let currentEmpId = getUserInfo().employeeid;
        return (
          process.EmployeeList.map(s => s.EmployeeId).findIndex(
            s => s == currentEmpId
          ) > -1
        );
      }
      return false;
    },
    
    isResponsible(){
      let currentEmpId = getUserInfo().employeeid;
      return (
          this.engDetail.EmployeeList.map(s => s.EmployeeId).findIndex(
            s => s == currentEmpId
          ) > -1
        );
    },

    /**事项详情、编辑 */
    handleProcesCreateDialog(row, optType = "update") {
      this.procesId = row.Id;
      this.dialogProcesCreateStatus = optType;
      this.dialogProcesCreateFormVisible = true;
    },
    /**事项进度关闭 */
    closeProcesCreateDialog() {
      this.dialogProcesCreateFormVisible = false;
    },
    /**事项进度保存成功 */
    handleProcesCreateSaveSuccess() {
      this.getImpProceDetails();
      this.getProcesList();
      this.closeProcesCreateDialog();
      this.getRegionList();
      this.getRegionInfo();
    },
    /**设备全选 */
    handleCheckAllChange(val) {
      this.checkedEqus = val
        ? this.tabProceEqus.filter(s => s.Status != 3).map(s => s.Id)
        : [];
      // this.isIndeterminate = false;
    },
    /**设备移动工序 */
    handleMove(direc) {
      if (!(this.checkedEqus && this.checkedEqus.length > 0)) {
        this.$message.error("请勾选需要移动的设备");
        return false;
      }
      let postData = {
        ImplementationEquipmentIdList: this.checkedEqus, //选中设备
        OldImplementationProcedureId: this.tabsActive, //
        IsNextProcedure: direc == "down" ? true : false,
        ImplementationProcedureId: null
      };

      if (direc == "up") {
        this.handlePrevDialog();
      } else {
        this.$confirm("确定要转入下个工序吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          impMgmt.setEquProce(postData).then(res => {
            this.$notify({
              title: "提示",
              message: "移动成功",
              type: "success",
              duration: 2000
            });
            this.checkedEqus = [];
            this.getProcesEqus();
            this.getRegionInfo();
          });
        });
      }
    },
    /**设置设备状态 */
    handleCommand(activeName, row) {
      let tempId = row.Id;
      impMgmt.setImpEquStatus({ id: tempId, status: activeName }).then(res => {
        this.getRegionList();
        this.getEquList();
        //如果设置为已完成，那么已选则相中应该去除该项
        if (activeName == 3) {
          let idx = this.checkedEqus.findIndex(s => s == tempId);
          this.checkedEqus.splice(idx, 1);
        }

        this.getProcesEqus();
      });
    },
    checkPremissBtns() { },

    /**
     * 操作记录
     */
    handleShowLog() {
      this.drawer = true;
    },
    handleClose(done) {
      done();
    },
    getImplementDetail(id) {
      this.drawerLoading = true;
      impManagement
        .getAllComment({ CurrentBusinessId: this.tabsActive, Type: 11 })
        .then(res => {
          this.drawerLoading = false;
          res.forEach(v => {
            v.CommentContent = JSON.parse(v.CommentContent);
          });
          this.activities = res;
        })
        .catch(err => {
          this.drawerLoading = false;
        });
    }
  }
};
</script>

<style scoped>
.content-wrapper >>> .el-tabs__header {
  margin-bottom: 0;
}

.content-wrapper >>> .el-tabs__nav-scroll {
  margin: 4px;
  margin-bottom: 0;
}

.tabs-wrapper .item >>> .el-badge__content {
  top: -2px;
  right: 6px;
}

.tabs-wrapper >>> .el-badge {
  display: inline;
  vertical-align: top;
}

/* .item-title >>> .el-checkbox__input{
  vertical-align: initial;
} */

/* .item-title >>> .el-checkbox__label{
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} */

.log-wrapper >>> button {
  padding: 0;
}

.prev-dialog-wrapper >>> .el-form-item__label {
  padding: 0;
}

.drawer-wrapper >>> .el-drawer {
  width: 820px !important;
}

.drawer-wrapper >>> .el-drawer__body {
  overflow-y: auto;
}

.basicInformation >>> .el-progress-bar,
.summary-desc >>> .el-progress-bar,
.whole >>> .el-progress-bar,
.tab-wrapper >>> .el-progress-bar {
  margin-right: -60px;
  padding-right: 55px;
}

.basicInformation >>> .el-progress {
  height: 28px;
  line-height: 28px;
}
/* 
.appContainer >>> .el-progress__text{
  margin-left: 5px;
} */
</style>

<style lang='scss' scoped>
.basicInformation {
  padding: 10px 0 10px 20px;
  border-bottom: 1px solid #eee;
  .cus-row {
    height: 28px;
    line-height: 28px;
  }
}

.appContainer {
  position: absolute;
  padding: 0;
  top: 0px;
  width: 100%;
  min-height: calc(100% - 10px);

  .body-wrapper {
    display: flex;
    background: #fff;
    // min-height: calc(100% - 10px)!important;
    margin-bottom: 0;
    position: absolute;
    width: 100%;
    height: calc(100% - 79px);
    .product-list {
      border-right: 1px solid #dcdfe6;
      width: 250px;
      // border-top: 1px solid #dcdfe6;
      > div:first-child {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
      }
      .versions-list {
        min-height: calc(100% - 36px - 64px);
        overflow-y: auto;
        height: calc(100% - 64px);
      }
      .tagsList {
        margin-top: 10px;
        padding-left: 5px;
        .item-warpper {
          padding: 4px;
          .item-title {
            margin-bottom: 4px;
          }
        }
      }
      .elInput {
        padding: 0 8px;
        margin-top: 4px;
      }
      .btns-wrapper {
        padding: 0 8px;
        margin-top: 4px;
        display: flex;
        height: 28px;
        line-height: 28px;
        .btns-desc {
          flex: 1;
        }
      }
    }
    .content-wrapper {
      height: 100%;
      // overflow-y: auto;
      .tab-item-wrapper {
        display: flex;
        .tab-title {
          flex: 1;
          // max-width: 100px;
          max-width: 90px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .tab-num {
        }
      }
      .summary {
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
        // padding: 10px;
        .region-title-wrapper {
          display: flex;
          .region-title {
            flex: 1;
            height: 28px;
            line-height: 28px;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
          }
          .region-btns button {
            margin-left: 5px;
          }
        }
        .summary-desc {
          // > div:first-child {
          //   margin-bottom: 8px;
          // }
          .col-wrapper {
            word-break: break-all;
            margin-bottom: 8px;
            line-height: 18px;
            .col-title {
              max-width: 90px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            span {
              display: inline-block;
            }
          }
        }
      }
      .tabs-wrapper {
        padding: 4px 0;
        border-bottom: 1px solid #dcdfe6;
      }
      .row-btns button {
        margin-right: 5px;
      }
    }
  }
}

.process-wrapper {
  display: flex;
  height: calc(100% - 28px);
  // .left, .right {
  //     display: inline-block;
  // }
  .left {
    width: calc(100% - 260px) !important;
    padding-right: 5px;
    height: 100%;
    overflow-y: auto;
    .btns {
      margin: 6px 0;
    }
    .tab-wrapper {
      height: calc(100% - 28px - 12px);
      overflow-y: auto;
      padding-bottom: 6px;
    }
    // float: left;
  }
  .right {
    width: 260px !important;
    height: 100%;
    overflow-y: auto;
    // float: right;
    // height: 636px;
    // padding-left:10px;
    // overflow-y: auto;
    border-left: 1px solid #dcdfe6;
    padding-left: 10px;
    margin-right: 10px;
    .btn-wrapper {
      height: 28px;
      margin: 6px 0;
    }
    .right-list {
      height: calc(100% - 28px - 12px);
      overflow-y: auto;
      padding-bottom: 6px;
      .item-wrapper {
        border: 1px solid #dcdfe6;
        border-radius: 10px;
        padding: 10px;
        margin-top: 10px;
        .title-wrapper {
          display: flex;
          margin-bottom: 4px;
          .item-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 4px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            word-wrap: break-word;
            &:hover {
              cursor: pointer;
              text-decoration: underline;
            }
          }
        }
      }
      .item-wrapper:first-child {
        margin-top: 0;
      }
    }
  }
}

.export-content {
  padding-top: 10px;
  div {
    margin-bottom: 4px;
  }
}

.elTimeBox {
  padding: 0 20px;
}
.elTimeDiv {
  margin-bottom: 6px;
  height: 20px;
  line-height: 20px;
  > span:first-child {
    margin-right: 100px;
    width: 320px;
    > i:last-child {
      max-width: 250px;
    }
  }
}
.c409EFF {
  color: #409eff;
}

/** 设备详情弹框 */
.equ-detail-wrapper {
  padding-top: 10px;
  .row-wrapper {
    display: flex;
    margin-bottom: 4px;
    .row-title {
      width: 80px;
      text-align: right;
    }
    .row-content {
      flex: 1;
    }
  }
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
