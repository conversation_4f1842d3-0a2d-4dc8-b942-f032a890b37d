import { throttle } from "@/utils";

export default {
    computed: {

    },
    data() {
        return {
            tabHeight: 400,
            dynamicTableContentId: '__dynamicTabCoreWrapper'
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.setTabHeight()
        })
        window.addEventListener('resize', throttle(this.setTabHeight, 100));
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.setTabHeight);
    }, 
    methods: {
        setTabHeight() {
            /** 
             * 1， 使用 组件 <app-table></app-table>  时  会自动给 id 加上时间戳, id = `__dynamicTabCoreWrapper${当前时间时间戳}`;
             * 
             * 2， 使用 组件 <app-table-core></app-table-core>  时  id  默认为未无时间戳的id "__dynamicTabCoreWrapper"  兼容之前的表格,
             *     需要区分id时  需要页面上手动  设置 dynamicTableContentId 的值来区分
             * 
             * (id="__dynamicTabCoreWrapper")  页面弹窗也有需要用到的表格  或是 页面会有多个 一样的表格  id会重复  需要给予不同的id 以便区分
             *   */
            let wrapperObj = document.getElementById(`${this.dynamicTableContentId}`)
            if(wrapperObj) {
                let tabHeightTemp = wrapperObj.clientHeight || wrapperObj.offsetHeight;
                this.$nextTick(() => {
                    //减去分页高度
                    this.tabHeight = tabHeightTemp
                })
            }
        },
    }
};
