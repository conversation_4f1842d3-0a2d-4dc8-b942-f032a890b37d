<template>
    <div class="addComponents">
        <app-dialog title="添加结构配件" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='1000'
            :maxHeight="636"
            v-loading='loading'
        >
            <template slot="body">
                <div class="firstDiv">
                    <span>结构配件清单 ({{listQuery.total}})</span>
                    <el-button type="primary" class="elButton" @click="handleAddAccess()">添加结构配件</el-button>
                    <el-button type="primary" class="elButton" @click="clearAll()">清空结构配件</el-button>
                </div>
                <div class="temBody">
                    <app-table-core
                    ref="mainTable"
                    :tab-columns="tabColumns"
                    :tab-datas="tableDatas"
                    :tab-auth-columns="[]"
                    :isShowAllColumn="true"
                    :isShowOpatColumn="false"
                    :startOfTable="startOfTable"
                    :multable='false'
                    >
                        <template slot='delete' slot-scope="scope">
                            <el-button type="text" style="color:#F56C6C;" @click="deleter(scope.row.tId)">删除</el-button>
                        </template>
                    </app-table-core>
                    <pagination
                      v-show="listQuery.total>listQuery.PageSize"
                      :total="listQuery.total"
                      :page.sync="listQuery.PageIndex"
                      :size.sync="listQuery.PageSize"
                      layout="total, prev, pager, next, jumper"
                      @pagination="handleCurrentChange"
                    />
                </div>
            </template>
            <template slot="footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button @click="handleSuccess">确定</el-button>
            </template>
        </app-dialog> 
        <add-accessories
            @closeDialog="closeDialog"
            :dialogFormVisible="dialogFormVisible"
            @saveSuccess="handleAcceSaveSuccess"
            v-loading="loading1"
            :fromData="accessoriesList">
        </add-accessories>
    </div>
</template>
<script>
import elDragDialog from '@/directive/el-dragDialog'
import * as accessories from "@/api/afterSalesMgmt/accessories";
import indexPageMixin from "@/mixins/indexPage";
import NoData from "@/views/common/components/noData";
import addAccessories from "./addAccessories";
export default{
    name:'addComponents',
    mixins: [indexPageMixin],
    components: {
        NoData,
        addAccessories,
    },
    props:{
        id: {
            type: String,
            default: ''
        },
        allAcceData:{
            type:Array,
            default:[]
        }
    },
    directives: {
        elDragDialog
    },
    data(){
        return{
            accessoriesList:[],
            dialogFormVisible:false,
            loading:false,
            loading1:false,
            listQuery:{
                total:0,
                PageSize:10,
                PageIndex:1,
            },
            tabColumns: [
                {
                  attr: { prop: "Name", label: "部件名称" }
                },
                {
                  attr: { prop: "SpecificationModel", label: "规格型号" },
                },
                {
                  attr: { prop: "SupplierName", label: "供应商" }
                },
                {
                  attr: { prop: "delete", label: "操作" },
                  slot: true
                },
              ],
            tableDatas:[],
            allTableData:[],
        }
    },
    filters: {
        
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val){
                this.allTableData=JSON.parse(JSON.stringify(this.allAcceData));
                this.listQuery.total=this.allTableData.length;
                this.tableDatas=[];
                this.listQuery.PageIndex=1;
                this.getData();
            }
      
        },
    },
    computed:{
        
    },
    created(){
        
    },
    mounted(){
        this.getAllListData();
    },
    methods:{
        clearAll(){
            this.allTableData=[];
            this.listQuery.total=0;
            this.getData();
        },
        deleter(d){
            let i=0;
            this.allTableData.forEach((v,index) => {
                if(v.tId == d){
                    i=index;
                }
            })
            this.allTableData.splice(i,1);
            this.listQuery.total--;
            this.getData();
        },
        getData(){
            this.tableDatas=this.allTableData.slice((this.listQuery.PageIndex-1)*this.listQuery.PageSize,(this.listQuery.PageIndex-1)*this.listQuery.PageSize+10);
        },
        // mathRand(){
        //     var Num="";
        //     for(var i=0;i<6;i++)
        //     {
        //         Num+=Math.floor(Math.random()*10);
        //     }
        //     return Num.toString();
        // },
        handleAcceSaveSuccess(d){
            d.forEach((v,i) => {
                if(v.children.length>0){
                v.children.forEach((v1,i1) => {
                    this.allTableData.unshift({
                        Id: this.guid(),
                        Name: v.label,
                        StructPartName:v.label,
                        StructPartId:v.id,
                        SpecificationModel: v1.label,
                        StructPartsSpecificationId:v1.id,
                        SupplierName: v1.SupplierName,
                        SupplierId:v1.SupplierId,
                        tId:this.guid(),
                    })
                })
                }
            })
            this.listQuery.total=this.allTableData.length;
            this.closeDialog();
            this.listQuery.PageIndex=1;
            this.getData();
            // accessories.addPartSpecification(postData).then(res => {
            //     this.$notify({
            //     title: '成功',
            //     message: '添加成功！',
            //     type: 'success'
            //     });
            //     this.loading1=true;
            //     this.getPartSpecificationData();
            // })
        },
        getAllListData(){
            this.accessoriesList=[];
            accessories.getAllList().then(res => {
                res.forEach((v,index) => {
                if(!v.ParentId){
                    this.accessoriesList.push({
                    id: v.Id,
                    pid: index,
                    label: v.Name,
                    children:[],
                    tId:this.guid(),
                    })
                }
                })
                this.accessoriesList.forEach(v => {
                    res.forEach((v1,i1) => {
                        if(v.id == v1.ParentId){
                        v.children.push({
                            id: v1.Id,
                            pid: v1.ParentId,
                            label: v1.Name,
                            SupplierName:v1.SupplierName,
                            SupplierId:v1.SupplierId,
                            tId:this.guid(),
                        })
                        }
                    })
                })
                this.accessoriesList=this.accessoriesList.filter(v => v.children.length>0);
            })
        },
        //生成随机 GUID 数
        guid() {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }
            return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
        },
        handleAddAccess(){
            this.dialogFormVisible = true;
        },
        closeDialog() {
          this.dialogFormVisible = false;
        },


        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.getData();
        //   this.listQuery.PageSize = val.size;
        //   this.getList();
        },
        handleSizeChange(val) {
          console.log('ccc',val)
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
        handleSuccess(){
            this.$emit('saveSuccess',this.allTableData);
        },
    }

}
</script>
<style lang="scss" scoped>
.firstDiv{
    margin-bottom: 10px;
    >span,.elButton{
        margin-right:10px;
    }
}
.temBody{
    height:510px;
    overflow-y: auto;
}
</style>