<!--显示规则-->
<template>
    <!--组件内容区-->
    <app-dialog title="显示规则" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
        <template slot="body">
            <el-form
                :rules="formRules"
                ref="formRef"
                :model="formModel"
                label-position="right"
                label-width="80px"
                v-loading='loading'>
                <el-form-item label="循环频率" prop="SendWordShowType">
                    <el-select style="width: 100%;" clearable v-model="formModel.SendWordShowType" placeholder="请选择">
                        <el-option v-for="item in cycleFrequencyEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <app-button :buttonType="2" @click="handleClose"></app-button>
            <app-button :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button>
        </template>
    </app-dialog>
</template>

<!--组件脚本区-->
<script>
/**引用区 */

import { cycleFrequencyEnum } from "../enum.js";
import * as workMessageApi from '@/api/personnelManagement/workMessage'
export default {
    /**名称 */
    name: "work-message-rule-setting",
    /**组件声明 */
    components: {
    },
    /**参数区 */
    props: {
    },
    /**数据区 */
    data() {
        return {
            cycleFrequencyEnum,
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            /**表单模型 */
            formModel: { 
                SendWordShowType: 5, // 1 每隔5分钟 、 2 每隔15分钟 、3 每隔30分钟 、4 每隔1小时 、5 每隔3小时 （默认） 、6 每隔6小时 、7 每隔12小时 、8 每隔24小时
            },
            /**表单规则 */
            formRules: {
                SendWordShowType: { fieldName: "循环频率", rules: [{ required: true, trigger: ['change', 'blur'] }] },
            }
        };
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {                
                this.formModel = this.$options.data().formModel
                if (val) {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let self = this, formModel = JSON.parse(JSON.stringify(self.formModel));
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    self.buttonLoading = true;
                    workMessageApi.EditShowRule(formModel).then(response => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000,
                        });
                        self.buttonLoading = false;
                        self.handleClose();
                    }).catch(err => {
                        self.buttonLoading = false
                    })
                }
            });
        },
        getDetail() {
            let self = this;
            self.loading = true;
            workMessageApi.GetShowRule({}).then(response => {
                self.loading = false;
                self.formModel = Object.assign({}, self.formModel, response)
            }).catch(err => {
                self.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
