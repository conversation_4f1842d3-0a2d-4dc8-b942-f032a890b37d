<template>
    <div>
        <div class="app-container">

            <div class="bg-white">
                <app-table ref="mainTable" :multable='false' :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns='tabAuthColumns' :isShowAllColumn='true' :loading="listLoading"
                    @rowSelectionChanged="rowSelectionChanged" 
                    :startOfTable='startOfTable'>

                    <template slot="conditionArea">
                        <app-table-form :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter'
                            @onReset='resetSearch'>
                            <template v-for="item in tableSearchItems" :slot="item.prop">
                                <el-input style="width: 100%;" v-model="listQuery[item.prop]" placeholder="" :key="item.prop"></el-input>
                            </template>
                        </app-table-form>
                    </template>


                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                        <!-- <permission-btn moduleName="emp" v-on:btn-event="onBtnClicked"></permission-btn> -->
                        <el-button type="primary" @click="handleAdd">新增</el-button>
                    </template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button @click="handleUpdate(scope.row)" :type='1'></app-table-row-button>
                        <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
                        <app-table-row-button @click="handleDelete(scope.row)" :type='3'></app-table-row-button>
                    </template>
                </app-table>
                <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
            <div v-if="dialogVisible">
                <edit-page v-if="formStruct" :dialogStatus='dialogStatus' :dialogFormVisible='dialogVisible' 
                    :formObj='formStruct' 
                    :formDatas='formDatas'
                    @closeDialog='closeDialog' @saveSuccess='handleSaveSuccess' @reloading='handleReloading'>
                </edit-page>
            </div>
        </div>
    </div>
</template>

<script>
    // import * as ec from '@/api/enterpriseConfigure'
    import elDragDialog from '@/directive/el-dragDialog'
    import indexPageMixin from '@/mixins/indexPage'
    import EditPage from './edit'
    import * as df from '@/api/dynamicForm'
    import mixin from '../dynamicFormCommon/mixins'
    import { JsonHubProtocol } from '@aspnet/signalr'

    export default {
        name: 'index-dynamic',
        components: {
            EditPage,
        },
        props: {
            formObj: {
                type: Object,
                required: true,
                default: null
            }
        },
        directives: {
            // waves,
            elDragDialog
        },
        mixins: [indexPageMixin, mixin],
        data() {
            return {
                multipleSelection: [],
                tableSearchItems: [
                    // { prop: 'OrgId', label: '所在组织' },
                    // { prop: 'Name', label: '姓名' },
                    // { prop: 'Phone', label: '手机' },
                    // { prop: 'Number', label: '工号' },
                    // { prop: 'JobTitle', label: '职称' },
                    // { prop: 'EducationId', label: '学历' },
                ],
                formStruct: JSON.parse(JSON.stringify(this.formObj)),//表单结构
                tabColumns: [
                    // {
                    //     attr: { prop: 'Description', label: '配置文件' },
                    // },
                    // {
                    //     attr: { prop: 'KeyValue', label: '内容' },
                    // }
                ],
                tabDatas: [],
                listLoading: false,
                total: 0,
                textMap: {
                    update: '编辑',
                    create: '添加'
                },
                dialogVisible: false,
                dialogStatus: '',
                //详情数据
                formDatas: null,
            
            }
        },
        created() {
            this.initFormObj() //初始化动态表单数据
            this.getList()
        },
        mounted() {
        },
        methods: {
            handleAdd() {
                this.formDatas = null
                this.dialogStatus = 'create'
                this.dialogVisible = true
            },
            initFormObj() {
                let columns = this.formStruct.formContent
                                    .filter(s => s.type != 1 && s.type != 2)
                                    .filter(s => s.attrs && s.attrs.length > 0 && s.attrs.findIndex(o => o.attrName == 'showInTableColumn' && o.value == 'true') > -1)
                let columnsObj = columns.map(s => {
                    let fieldNameObj = s.attrs.find(o => o.attrName == 'name')
                    let fieldLabelObj = s.attrs.find(o => o.attrName == 'label')
                    return {
                        attr: { prop: fieldNameObj.value, label: fieldLabelObj.value }
                    }
                })
                this.tabColumns = columnsObj
                this.tabAuthColumns = columnsObj
                this.tableSearchItems = columnsObj.map(s => s.attr)
            },
            closeDialog() {
                this.dialogVisible = false
            },
            handleReloading() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSaveSuccess(_formData) {
                this.listQuery.PageIndex = 1
                this.getList()
                this.dialogVisible = false
            },
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            onBtnClicked: function (domId) {
                // console.log('you click:' + domId)
                switch (domId) {
                    case 'btnAdd':
                        this.handleCreate()
                        break
                    case 'btnEdit':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行编辑',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0])
                        break
                    case 'btnDetail':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行查看',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0], 'detail')
                        break;
                    case 'btnDel':
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: '至少删除一个',
                                type: 'error'
                            })
                            return
                        }
                        this.handleDelete(this.multipleSelection)
                        break
                    default:
                        break
                }
            },
            getList() {
                this.listLoading = true
                df.getDatas({
                    formId: this.formStruct.id
                }).then(response => {
                    this.tabDatas = response.Items
                    this.total = response.Total
                    this.listLoading = false
                }).catch(err => {
                    this.listLoading = false
                })
            },
            handleFilter() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
            //选择部门
            handleChange(value) {

            },
            handleUpdate(row, optType = 'update') { // 弹出编辑框
                df.getDataDetail({
                    formId: this.formObj.id, //表单id
                    componentRowId: row.componentRowId, //当前行id
                }).then(res => {
                    this.formDatas = res[0]
                    this.dialogStatus = optType
                    this.dialogVisible = true
                })
            },
            handleDelete(row) { // 多行删除

                this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let delObj = {
                        FormId: this.formStruct.id,
                        ComponentRowId: row.componentRowId
                    }

                    df.delData(delObj).then(() => {
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.getList()
                    })
                })

            },
        }
    }
</script>

<style scoped>
    .sel-ipt,
    .dat-ipt {
        width: 100%;
    }

    .avatar {
        width: 68px;
        height: 68px;
    }

    .tip-avatar {
        width: 140px;
        height: 140px;
    }

    .avatar,
    .tip-avatar {
        border-radius: 50%;
    }

    /* .cus_wdt{
    width: 200px;
} */
</style>
