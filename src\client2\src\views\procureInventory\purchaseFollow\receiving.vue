<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='900' :maxHeight="600" >
        <template slot="body">
            <el-row class="wrapper">
                <div class="left">
                    <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading"
                    label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                        <el-form-item label="签收方式" prop="SignType">
                            <el-radio-group v-model="formData.SignType" :disabled="!editable">
                                <el-radio v-for="item in SignTypeTypes" :key="item.value" :label="item.value">{{item.label}}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="收货时间" prop="ReceivingTime">
                            <el-date-picker v-model="formData.ReceivingTime" type="datetime" align="right" format="yyyy-MM-dd HH:mm" placeholder="请选择收货时间"
                            value-format="yyyy-MM-dd HH:mm" :disabled="!editable"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="收货人" prop="RecipientsList">
                            <!-- <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.Recipients" placeholder="收货人"></el-input> -->
                            <emp-selector v-if="editable" key="btnAddSelector" :showType="2" :multiple="true"
                            :beforeConfirm='handleAddEmpSelector' :list="formData.RecipientsList"
                            @change="handleChangeEmpSelector"></emp-selector>
                            <template v-else>{{ formData.RecipientsList.map(s=>s.Name).toString() }}</template>
                        </el-form-item>
                        <el-form-item label="备注" prop="Remarks">
                            <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="5" v-model="formData.Remarks" placeholder=""></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="right">
                    <div class="panel-title">附件</div>
                    <div class="attachmentBox">
                        <div class="notData" v-if="formData.AttachmentList.length == 0 && dialogStatus == 'detail'">暂无内容</div>
                        <app-uploader v-else
                        ref="appUploaderRef"
                        :readonly="!editable"
                        accept="all"
                        :fileType="3"
                        :max="10000"
                        :value="formData.AttachmentList"
                        :fileSize="1024 * 1024 * 500"
                        :minFileSize="100 * 1024"
                        @change="handleFilesUpChange"
                        ></app-uploader>
                    </div>
                </div>
            </el-row>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2" :disabled="disabledBtn"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable"></app-button>
        </template>
    </app-dialog>

</div>
</template>

<script>

import { getUserInfo } from '@/utils/auth';
import { vars } from '../common/vars'
import * as purchaseFollowApi from "@/api/procureInventory/purchaseFollow";
import empSelector from '@/views/common/empSelector'
import dayjs from 'dayjs';
export default {
    name: "material-data-create",
    components: {
        empSelector
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    } else if(this.dialogStatus == 'create'){
                        this.formData.ReceivingTime = dayjs().format("YYYY-MM-DD HH:mm")
                        let a=getUserInfo();
                        this.formData.RecipientsList=[{
                            EmployeeId:a.employeeid,
                            Name:a.empName,
                            Number: a.empNumber,
                            AreaName: null,
                            Avatar: "",
                            AvatarId: "",
                            DepartmentList: a.deptName,
                            HeadImageMediaModel: null,
                            ImageResourceId: null,
                            Mobile: a.phone,
                            UserId: "00000000-0000-0000-0000-000000000000"
                        }];
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "收货";
            } else if (this.dialogStatus == "detail") {
                return "收货详情";
            }
        }
    },

    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            
            SignTypeTypes: vars.SignTypeTypes, // 采购订单状态
            formLoading: false,
            disabledBtn: false,
            rules: {
                RecipientsList: { fieldName: "收货人", rules: [{ required: true }] },
                ReceivingTime: { fieldName: "收货时间", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            formData: {
                PurchaseOrderId: this.id,
                SignType: 1, // 签收方式
                ReceivingTime: "", // 收货时间
                Recipients: "", // 收货人
                RecipientsList: [], // 收货人
                Remarks: "", // 备注
                AttachmentList: []
            }
        };
    },
    methods: {
        // 选中负责人
        handleChangeEmpSelector(users) {
            this.formData.RecipientsList = users
        },
        // 负责人 数量限制
        handleAddEmpSelector(users) {
            if (users && users.length > 5) {
                this.$message({
                    message: '负责人不得超过5人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        resetFormData() {
            let temp = {
                PurchaseOrderId: this.id,
                SignType: 1, // 签收方式
                ReceivingTime: "", // 收货时间
                Recipients: "", // 收货人
                RecipientsList: [],
                Remarks: "", // 备注
                AttachmentList: []
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            let self = this;
            self.formLoading = true;
            purchaseFollowApi.GetReceiving({Id: self.id}).then(res => {
                self.formLoading = false;
                self.formData = Object.assign({}, self.formData, res);
            })
            .catch(err => {
                self.formLoading = false;
            });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                postData.Recipients = postData.RecipientsList && postData.RecipientsList.map(s => s.EmployeeId).toString()
                console.log(postData)
                //提交数据保存
                this.disabledBtn = true;
                purchaseFollowApi.AddReceiving(postData).then(res => {
                    this.disabledBtn = false;
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false;
                });
            });
        },
        handleSave() {
            this.createData();
        }
    }
};
</script>
<style lang="scss" scoped>
.wrapper {
    display: flex;

    .left {
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }

    .right {
        width: 40%;
        border-left: 1px solid #DCDFE6;
        
        
        .panel-title{
            padding-top: 10px;
            font-size: 14px;
        }
        .attachmentBox{
            position: relative;
            width: 100%;
            height: calc(100% - 41px);
            padding-left: 10px;
            overflow: hidden;
            overflow-y: auto;
            .notData{
                color: #999;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
}
</style>
