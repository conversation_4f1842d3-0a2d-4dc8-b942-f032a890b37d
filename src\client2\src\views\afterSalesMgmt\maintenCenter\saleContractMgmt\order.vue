<template>
    <div class="app-dialog-wrapper">
      <app-dialog :title="row.AfterContractName" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1200'
        className="clear-default-height-auto dialog-auto-height"
      >
        <template slot="body">
          <div class="dialog-wrapper">
            <el-row>
              <el-col :span="10">
                <span style="font-weight: bold; padding-left: 10px;">合同金额：</span>{{ detail.AfterTaxAmount || 0 }}
              </el-col>
              <el-col :span="14">
                <span style="font-weight: bold; padding-left: 10px;">已用额度：</span>{{ detail.AmountUsed || 0 }}
              </el-col>
            </el-row>
            <app-table
              class="flex-1"
                ref="mainTable"
                :tab-columns="tabColumns"
                :tab-datas="tabDatas"
                :tab-auth-columns="[]"
                :isShowAllColumn="true"
                :loading="listLoading"
                :isShowOpatColumn="true"
                :startOfTable="startOfTable"
                :multable="true"
                @sortChagned='handleSortChange'
                @rowSelectionChanged="rowSelectionChanged"
                fit
                :isShowBtnsArea='false'
                :layoutMode='layoutMode'
                :height='500'
              >
                <template slot="ServiceNo" slot-scope="scope">{{
                  scope.row.ServiceNo ? scope.row.ServiceNo : '无'
                }}</template>
                <template slot="ReportTime" slot-scope="scope">{{
                  scope.row.ReportTime | dateFilter("YYYY-MM-DD HH:mm")
                }}</template>
                <template slot="ReportEmployee" slot-scope="scope">{{
                  scope.row.ReportEmployee | nameFilter
                }}</template>
                <template slot="HandlerEmployeeList" slot-scope="scope">
                  <span v-if="scope.row.HandlerEmployeeList">{{
                    scope.row.HandlerEmployeeList.map(s => s.Name).join(",")
                  }}</span>
                  <span v-else>无</span>
                </template>

                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                  
                  <app-table-form
                    :label-width="'100px'"
                    :items="tableSearchItems"
                    @onSearch="handleFilter"
                    @onReset="handleResetSearch"
                    :layoutMode='layoutMode'
                  >
                    <!-- :default-expand-level="3" -->
                    <template slot="Keywords">
                      <div style="display: flex; align-items: center;">
                        <span style="font-weight: bold; margin-right: 4px;">查询报修单</span>
                        <el-input style="width: 100%; flex: 1;" 
                            placeholder="搜索报修单号/服务单号/地区名称"
                            @clear='handleFilter'
                            v-antiShake='{
                                time: 300,
                                callback: () => {
                                    handleFilter()
                                }
                            }' 
                            clearable 
                            v-model="listQuery.Keywords"
                        ></el-input>
                      </div>
                      <!-- <app-table-form-item :sm='24' :labelWidth='80' label="查询报修单" style="height: 29px;" :layoutMode='layoutMode'>
                      </app-table-form-item> -->
                    </template>
                    <template slot="btnsArea">
                      <div class="cl" style="width: 100%;">
                        <el-button class="fl" type="primary" @click="handlerOrderSelectorDialog">关联报修单</el-button>
                        <el-button class="fl" type="danger" @click="handleDisassociate">取消关联</el-button>
                        <el-button class="fr" type="primary" @click="handleExport">导出表格</el-button>
                      </div>
                    </template>
                  </app-table-form>
                </template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                  <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
                </template>
            </app-table>

            <pagination
              :total="total"
              :page.sync="listQuery.PageIndex"
              :size.sync="listQuery.PageSize"
              @pagination="handleCurrentChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
        </template>
      </app-dialog>

      <create-page
        v-if="dialogDetailFormVisible"
        @closeDialog="closeDialog"
        :dialogFormVisible="dialogDetailFormVisible"
        dialogStatus="detail"
        :id="detailId"
        :declareNewCases="true"
      ></create-page>

      <maintenOrderSelector
        :isShow="maintenOrderSelectorVisible"
        :checkedList='[]'
        :condition="{  }"
        @changed="maintenOrderSelectorChanged"
        @closed="() => (maintenOrderSelectorVisible = false)"
        :multiple="true"
    ></maintenOrderSelector>

    
      <v-export
        @closeDialog="handleCloseExport"
        :dialogFormVisible="dialogExportVisible"
        :rData="rData"
        :cData='cData'
      >
    </v-export>
    </div>
</template>
   
<script>
import indexPageMixin from "@/mixins/indexPage";
import * as saleContractMgmt from "@/api/maintenanceCenter/saleContractMgmt"
import { vars } from './vars'

import * as mo from "@/api/maintenanceCenter/maintenOrderMgmt"
import createPage from '../maintenOrderMgmt/create'
import maintenOrderSelector from '../maintenOrderMgmt/maintenOrderSelector'
import vExport from "@/components/Export/index";

export default {
    name: "sale-contract-mgmt-order",
    directives: {},
    components: {
      createPage,
      maintenOrderSelector,
      vExport,
    },
    mixins: [indexPageMixin],
    props: {
        row: {
          type: Object,
          required: true
        }
    },
    filters: {
      nameFilter(creator) {
        if (creator) {
          return creator.Name;
        }
        return "";
      },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val && this.row && this.row.Id) {
                    this.getList()
                    this.getDetail()
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
    },
    data() {
        return {
            
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
              {
                  prop: "Keywords",
                  label: "",
                  mainCondition: true
              },
            ],
            tabColumns: [
              {
                attr: { prop: "Code", label: "报修编号",width:'125' }
              },
              {
                attr: { prop: "RegionalName", label: "报修地区", showOverflowTooltip: true,width:'350' }
              },
              {
                attr: { prop: "ReportTime", label: "报修时间", sortable: 'custom' ,width:'125'},
                slot: true
              },
              {
                attr: { prop: "TotalPrice", label: "总收费（元）",width:'100' }
              },
              {
                attr: { prop: "ServiceNo", label: "服务单号" ,width:'180'},
                slot:true
              },
              {
                attr: { prop: "HandlerEmployeeList", label: "实施人员"},
                slot: true
              },
            ],
            tabDatas: [],
            total: 0,

            detailId: '',
            dialogDetailFormVisible: false,
            declareNewCases: true,

            multipleSelection: [],
            maintenOrderSelectorVisible: false,

            dialogExportVisible: false,
            rData:null,
            cData:[],

            detail: {},

        };
    },
    methods: {
        rowSelectionChanged(rows) {
          this.multipleSelection = rows;
        },
        handleSortChange({ column, prop, order }) {
          this.sortObj = {prop, order}
          this.getList()
        },
        handleCurrentChange(val) {
          this.listQuery.PageIndex = val.page;
          this.listQuery.PageSize = val.size;
          this.getList();
        },
        handleSizeChange(val) {
          this.listQuery.PageSize = val.size;
          this.getList();
        },
        handleFilter() {
          this.listQuery.PageIndex = 1;
          this.getList();
        },
        handleResetSearch() {
          this.listQuery.Keywords = ''
          this.getList(); //刷新列表
        },
        closeDialog() {
          this.dialogDetailFormVisible = false;
        },
        handleUpdate(row) {
          // 弹出编辑框
          this.detailId = row.Id;
          this.dialogDetailFormVisible = true;
        },
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData.AfterContractId = this.row.Id
            postData = this.assignSortObj(postData)
            mo.getList(postData).then(res => {
              this.listLoading = false;
              this.tabDatas = res.Items;
              this.total = res.Total;
            });
        },
        getDetail() {
            saleContractMgmt.detail({ id: this.row.Id }).then(res => {
                this.detail = Object.assign({}, res);
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //关联报修单区域
        handlerOrderSelectorDialog() {
          this.maintenOrderSelectorVisible = true
        },
        maintenOrderSelectorChanged(data) {
          if(data && data.length > 0) {
            let oldIds = this.tabDatas.map(o => o.Id)
            let newIds = data.map(o => o.Id)
            //需要添加的
            let addableIds = newIds.filter(s => oldIds.findIndex(o => o == s) == -1) 

            let postDatas = {
              AfterContractId: this.row.Id,
              MaintenanceIdList: addableIds
            }
            mo.batchUpdateAfterContract(postDatas).then(res => {
              this.$emit('reload')
              this.getDetail()
              this.handleFilter()
              this.$notify({
                title: '成功',
                message: '关联成功',
                type: 'success',
                duration: 2000
              })
            })

          }
      },
      handleDisassociate() {
        if(this.multipleSelection.length == 0) {
          this.$message({
              message: '请选择需要取消关联的报修单',
              type: 'error'
          })
        }else{
          this.$confirm(`是否确认取消关联`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
          .then(() => {
            let postDatas = {
              AfterContractId: this.row.Id,
              MaintenanceIdList: this.multipleSelection.map(s => s.Id)
            }
            mo.cancelContract(postDatas).then(res => {
              this.$emit('reload')
              this.getDetail()
              this.handleFilter()
              this.$notify({
                title: '成功',
                message: '取消关联成功',
                type: 'success',
                duration: 2000
              })
            })
          });
        }
      },

      handleExport(){
        let postData = JSON.parse(JSON.stringify(this.listQuery));
        if(this.row && this.row.Id) {
          postData.AfterContractId = this.row.Id
        }
        this.rData={
            "exportSource": 25,
            "columns": [],
            "searchCondition": postData
        }
        this.cData=[{
          label:'序号',
          value:'Number'
        },{
          label:'报修人',
          value:'TroubleShooter'
        },{
          label:'报修时间',
          value:'ReportTimeString'
        },
        {
          label:'到站/离站时间',
          value:'ArrivalAndDepartureTime'
        },
        {
          label:'一级地区',
          value:'RegionalName1'
        },{
          label:'二级地区',
          value:'RegionalName2'
        },
        {
          label:'三级地区',
          value:'RegionalName3'
        },{
          label:'四级地区',
          value:'RegionalName4'
        },{
          label:'五级地区',
          value:'RegionalName5'
        },{
          label:'报修电话',
          value:'ReporterNumber'
        },
        {
          label:'故障记录',
          value:'ReportFailureRecord'
        },
        {
          label:'加热炉/锅炉',
          value:'Name'
        },{
          label:'炉号',
          value:'HeatNumber'
        },{
          label:'供风方式',
          value:'EquipmentWorkModeName'
        },{
          label:'故障现象',
          value:'Phenomenon'
        },{
          label:'故障原因',
          value:'CauseAnalysis'
        },{
          label:'解决方式',
          value:'Solution'
        },
        {
          label:'实施人员',
          value:'HandlerEmployeeListString'
        },
        {
          label:'故障分类',
          value:'FaultType'
        },{
          label:'更换配件',
          value:'StructPartName'
        },
        {
          label:'规格型号',
          value:'SpecificationModel'
        },{
          label:'配件数量',
          value:'Count'
        },{
          label:'单价',
          value:'UnitPrice'
        },{
          label:'总价',
          value:'TotalMoney'
        },{
          label:'是否在保',
          value:'IsWarrantyName'
        },
        {
          label:'故障处理结果',
          value:'HandlingResultStatus'
        },
        {
          label:'备注',
          value:'Remarks'
        },{
          label:'站内签字',
          value:'StationSign'
        },{
          label:'是否签单',
          value:'IsSignBill'
        },{
          label:'加班/夜勤',
          value:'OvertimeNightWork'
        },{
          label:'工时',
          value:'WorkingHours'
        },{
          label:'单号',
          value:'ServiceNo'
        },
        {
          label:'录入时间',
          value:'EntryTimeString'
        },
        {
          label:'服务费用',
          value:'OtherExpenses'
        },
        {
          label:'售后合同编号',
          value:'AfterContractCode'
        }
        ]
        this.dialogExportVisible=true;
      },
      handleCloseExport() {
        this.dialogExportVisible = false;
      },
    }
};
</script>


<style lang="scss" scoped>
.dialog-wrapper{
  padding-top: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>

