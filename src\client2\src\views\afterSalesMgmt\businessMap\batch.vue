<template>
  <div>
    <app-dialog
      title="批量调整地区"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='600'
    >
      <template slot="body" v-loading="loading">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          :label-width="'100px'"
        >
            <el-row>
              <el-col :span="24">
                    <el-form-item label="所在地区" prop="areaName">

                        <div class="_regional_detail_wrapper">
                            <div class="btn_wrapper">
                                <el-button type="text" @click="handleDialog">选择</el-button>
                            </div>
                            <div class="regional_text" :title="formData.areaName">{{ formData.areaName }}</div>
                            <div class="close_wrapper" v-show="formData.areaName">
                                <div class="i_wrapper">
                                    <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                
            </el-row>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>


    <v-area-choose
      @closeDialog="closeDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogFormVisible"
      :checkedList="formData.areaId ? [formData.areaId] : []"
      :disabledFn="disabledFn"
    ></v-area-choose>
  </div>
</template>

<script>
// import empSelector from '../../../common/empSelector'
import * as orderEquipment from "@/api/maintenanceCenter/orderEquipment"
import vAreaChoose from "./common/areaChoose";
import * as carMgt from '@/api/personnelManagement/carCoordinator'
export default {
    name: "batch-update",
    components: {
      // tabs,
      // tags,
      // empSelector,
      vAreaChoose,

    },
    mixins: [],
    props: {
      IdList: {
        type: Array,
        default: ()=>[]
      }
    },
    watch: {
      "$attrs.dialogFormVisible"(val) {
          this.resetFormData();
      },
    },
    created() {
      this.rules = this.initRules(this.rules);
    },
    data() {
      return {
        dialogFormVisible: false,
        loading:false,
          disabledBtn: false,
          rules: {
              areaName:{
                  fieldName: "选择地区",
                  rules: [{ required: true, trigger: 'change' }]
              }
          },
          formData: {
              IdList: this.IdList, //
              areaName: '',
              areaId: null,

          },


      };
    },
    methods: {


      resetFormData() {
        let temp = {
          IdList: [], //
          areaName: '',
          areaId: null,
        };
        this.formData = Object.assign({}, this.formData, temp);
      },
      closeDialog() {
        this.dialogFormVisible=false
      },
      handleDialog(){
        this.dialogFormVisible=true;
      },
      disabledFn(data, nodeType) {
        //禁选一级节点
        if(data.level <= 1) {
            return true
        }
        return false
      },
      electedRegionalData(data){
        this.$refs.formData.clearValidate('areaName');
        if(data){
            this.formData.areaId=data.Id;
            this.formData.areaName=data.ParentName;
        }else{
            this.formData.areaId='';
            this.formData.areaName='';
        }
      },
      createData() {

          let validate = this.$refs.formData.validate();
          Promise.all([validate]).then(valid => {
            if(valid) {
              let postDatas = {
                IdList: this.IdList,
                RegionalId: this.formData.areaId
              }
              this.disabledBtn = true
              orderEquipment.batcheRegions(postDatas).then(res => {
                this.disabledBtn = false
                this.$notify({
                      title: "提示",
                      message: "保存成功",
                      type: "success",
                      duration: 2000
                  });
                  this.$refs.appDialogRef.createData();
              }).catch(err => {
                this.disabledBtn = false
              })
            }


              // let postData = JSON.parse(JSON.stringify(this.formData));
              // //提交数据保存
              // postData = Object.assign({}, this.formData);
              // postData.HandlerEmployeeIds = postData.HandlerEmployee.map(s => s.EmployeeId)
              // postData.UseVehicle = [postData.UseVehicle] //后台为数组
              // console.log(postData)
              // let result = null;
              // if (this.IdList.length>0) {
              //   delete postData.Id
              //   result = maintenOrderMgmtApi.AssignList(postData);
              // } else {
              //   delete postData.IdList
              //   result = maintenOrderMgmtApi.assign(postData);
              // }
              // result.then(res => {
              //     this.$notify({
              //         title: "提示",
              //         message: "保存成功",
              //         type: "success",
              //         duration: 2000
              //     });
              //     this.loading=false;
              //     this.$refs.appDialogRef.createData();
              // }).catch(err => {
              //   this.loading=false;
              // })
          });
      },
      handleClose() {
        this.$refs.appDialogRef.handleClose();
      },
    }
};
</script>
