<template>
  <div v-if="visible">
    <div class="conditionArea-wrap clearfix">
      <app-table-form style="padding-top: 10px;" :label-width="'80px'" :items='tableSearchItems' :smWidth="12" @onSearch='handleFilter' @onReset='onResetSearch'>

        <template v-for="(s, idx) in tableSearchItems" :slot='s.prop'>
          <el-input style="width: 100%;" v-model="listQuery[s.prop]" placeholder="" class="inputName" :key="idx"></el-input>
        </template>
        <!-- <template slot='Name'>
                    <el-input style="width: 100%;" v-model="listQuery.Name" placeholder="" class="inputName"></el-input>
                </template>
                <template slot='phone'>
                    <el-input style="width: 100%;" v-model="listQuery.phone" placeholder="" class="inputPhone"></el-input>
                </template> -->
        <template slot="other-btns">
          <el-button type="success" @click="handleSave">确认</el-button>
        </template>
      </app-table-form>
    </div>
    <el-table fit :data="tabDatas" style="width: 100%" v-loading="listLoading" @selection-change='rowSelectionChanged' ref="mainTable2" max-height="500" :highlight-current-row='!multiple' @current-change='currentChanged'>
      <el-table-column type="selection" width="55" v-if="multiple"></el-table-column>
      <el-table-column type="index" :index="indexMethod" label="序号"></el-table-column>
      <el-table-column v-for="(c, idx) in tabColumns" :key="idx" :prop="c.prop" :label="c.label"></el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
  </div>
</template>

<script>
import * as roles from '@/api/roles'
import * as emp from '@/api/emp'
import indexPageMixin from '@/mixins/indexPage'
import * as orgs from '@/api/organization'
import { listToTreeSelect } from '@/utils'
import { getUserInfo } from "@/utils/auth"
import request from '@/utils/request'

export default {
  name: 'user-list',
  mixins: [indexPageMixin],
  props: {
    existsUsers: {
      type: Array,
      default: () => {
        return []
      }
    },
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    //是否根据当前用户获取具有权限查看的用户（默认不需要权限控制）
    // onlyAuthUsers: {
    //     type: Boolean,
    //     default: false
    // },
    //选择器配置，结构如下
    /**
     * url： api接口地址
     * columns： 所有列
     * showColumns： 需要显示的列
     * conditions： 查询条件
     * showStyle： 选中后展示的风格类型
     */
    conf: {
      type: Object,
      required: true
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (!val) {
          this.resetSearch()
        }
        if (val) {
          this.checkedUsers = JSON.parse(JSON.stringify(this.existsUsers))
          this.getList()
        }
      },
      immediate: true
    },

  },
  mounted() {
    this.getOrgTree()
  },
  data() {
    return {
      checkedUsers: [],
      // userSelectorDlg: false,
      // tableSearchItems: [
      //     { prop: 'OrgId', label: '所在组织' },
      //     { prop: 'Name', label: '姓名' },
      //     { prop: 'phone', label: '手机' },
      // ],
      tableSearchItems: this.conf.columns.filter(s => this.conf.conditions.findIndex(t => t == s.value) > -1).map(s => {
        return {
          prop: s.name,
          label: s.label
        }
      }) || [],
      multipleSelection: [], // 列表checkbox选中的值
      total: 0,
      listLoading: false,
      listQuery: { // 查询条件
        Name: '',
        phone: '',
      },
      tabKey: 0,
      tabDatas: [],
      orgsTree: [], // 用户可访问到的所有机构组成的树
      tabColumns: this.conf.columns.map(s => {
        return {
          prop: s.name,
          label: s.label
        }
      })
    }
  },

  methods: {
    onResetSearch() {
      this.resetSearch()
    },
    currentChanged(currentRow, oldCurrentRow) {
      //如果是多选，必须选择复选框，如果是单选，可以单击整行表示选中
      if (!this.multiple && currentRow) { //某页选中一个用户后，点击翻页会触发该事件，所以要加该判断
        this.multipleSelection = [currentRow]
        this.checkedUsers = [currentRow]
      }
    },
    //当表格行中所有 checkbox 选中状态项改变时（返回所有选中的行数据）
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
      this.tabDatas.forEach(r => {
        let idx = this.checkedUsers.findIndex(u => u.componentRowId == r.componentRowId)
        if (idx > -1) {
          this.checkedUsers.splice(idx, 1)
        }
      })

      if (rows && rows.length > 0) {
        rows.forEach(r => {
          if (!this.checkedUsers.some(s => s == r.componentRowId)) {
            this.checkedUsers.push(r)
          }
        })
      }
    },
    getList() {
      this.listLoading = true
      request({
        url: '/Business/DynamicForm/GetMongoDynamicFormData',
        method: 'post',
        data: {
          formId: this.getQueryString(this.conf.url, 'formId')
        }
      }).then(res => {
        this.tabDatas = res.Items
        this.total = res.Total
        this.listLoading = false
        this.setCheckedusers()
      })
    },
    getQueryString(url, name) {
      url = url.split('?')[1]
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      var r = url.substr(1).match(reg);
      if (r != null) return unescape(r[2]);
      return null;
    },
    handleFilter() {
      this.listQuery.PageIndex = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page
      this.listQuery.PageSize = val.size

      this.getList()
    },
    setCheckedusers() {

      if (!this.multiple) { //单选
        if (this.checkedUsers && this.checkedUsers.length > 0) {
          let checkUser = this.tabDatas.find(p => p.componentRowId == this.checkedUsers[0].componentRowId)
          checkUser && this.$refs.mainTable2.setCurrentRow(checkUser);
        }
      } else { //多选

        if (this.checkedUsers && this.checkedUsers.length == 0) {
          this.$refs.mainTable2.clearSelection();
        } else {
          let checkedUsers = this.tabDatas.filter(s => this.checkedUsers.map(u => u.componentRowId).some(o => o == s.componentRowId)) || []
          checkedUsers.forEach(u => {
            this.$nextTick(() => {
              if (this.$refs.mainTable2) {
                this.$refs.mainTable2.toggleRowSelection(u)
              }
            })
          })
        }
      }
    },
    handleSave() {
      this.$emit('changed', this.checkedUsers)
      // this.userSelectorDlg = false

    },

    indexMethod(index) {
      return (index += 1) + this.listQuery.PageSize * (this.listQuery.PageIndex - 1)
    },
    getOrgTree() {
      var _this = this // 记录vuecomponent
      orgs.getValidOrgs().then(response => {
        _this.orgs = response.map(function (item, index, input) {
          let obj = {
            Id: item.OrganizationId,
            label: item.Name,
            ParentId: item.ParentId,
          }
          return obj
        })
        var orgstmp = JSON.parse(JSON.stringify(_this.orgs))
        _this.orgsTree = listToTreeSelect(orgstmp)

      })
    },
  }
}
</script>


<style scoped>
.title,
.sub-title {
  margin-bottom: 10px;
}

.sub-title {
  display: flex;
  line-height: 28px;
}

.sub-title-info {
  flex: 1;
}

.sub-title-btns {
  margin-right: 10px;
}

.tab-users {
  max-height: 480px;
  padding: 10px;
  overflow-y: scroll;
}

.tab-item-wrap {
  float: left;
  width: 20%;
  padding: 5px;
  position: relative;
}

.tab-item {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  height: 80px;
  width: 100%;
  border: 1px solid #dcdfe6;
  padding: 10px;
}

.opt {
  cursor: pointer;
  font-size: 28px;
  color: #dcdfe6;
  text-align: center;
  line-height: 80px;
  vertical-align: middle;
  padding: 0;
}

.conditionArea-wrap {
  padding: 0 10px;
}

.btns-area {
  text-align: left;
  padding: 10px 0;
  padding-right: 10px;
}

.file-btn-del {
  background: transparent;
  display: block;
  cursor: pointer;
  position: absolute;
  font-size: 18px;
  top: -3px;
  right: -3px;
}

.file-btn-del:hover {
  transition: all 0.3s;
  color: #f56c6c;
}

.color-danger,
.color-danger:hover {
  color: #f56c6c;
}
</style>