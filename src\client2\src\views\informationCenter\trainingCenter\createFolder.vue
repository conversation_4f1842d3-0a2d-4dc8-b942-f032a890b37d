<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='700' :width='600'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper">
                    <el-row>
                        <!-- <el-col :span="24">
                            <el-form-item label="所属类型">
                                <span>
                                    {{trainsClassificationTreeName }}
                                </span>
                            </el-form-item>
                        </el-col> -->
                        <el-col :span="24">
                            <el-form-item label="课程类型名称" prop="TrainsClassificationName" label-width="120px">
                                <el-input maxlength="30" :disabled="!editable" v-model="formData.TrainsClassificationName"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="createData" :buttonType='1' v-show="editable"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
// import tags from '../common/tags'
import * as trainsClassification from '@/api/informationCenter/trainsClassification'

export default {
    name: "trainsClassification-createFolder",
    directives: {},
    components: {

    },
    mixins: [],
    props: {

        dialogStatus: { //create、update、detail
            type: String,
            default: 'create'
        },
        //操作的节点，如果是新增，则为父节点；编辑为当前节点
        node: {
            type: Object,
            required: true
        },
    },
    watch: {
        // product: {
        //   handler(val) {debugger
        //     if (val) {
        //       this.formData.ProductId = this.product.value
        //       this.formData.ProductName = this.product.label
        //     }
        //   },
        //   immediate: true
        // },
        '$attrs.dialogFormVisible': {
            handler(val) {
                if (val) {
                    this.resetFormData()
                    this.formData.Level = this.node.level
                    this.formData.ParentID = this.node.data.Id
                    if (this.dialogStatus != 'create') {
                        this.getDetail()
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail"
        },
        pageTitle() {
            if (this.dialogStatus == 'create') {
                return '添加子级'
            } else if (this.dialogStatus == 'update') {
                return '编辑课程类型'
            } else if (this.dialogStatus == 'detail') {
                return '分类详情'
            }
        },

    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {

        return {
            trainsClassificationTreeName: "",
            rules: {
                TrainsClassificationName: {
                    fieldName: "分类名称",
                    rules: [{
                        required: true,
                        max: 100
                    }]
                },
            },
            labelWidth: '100px',
            formData: {
                Id: '', //
                TrainsClassificationName: '', //分类名称
                Level: this.node.level,
                ParentID: this.node.data.Id
            },
        };
    },
    methods: {
        resetFormData() {
            let temp = {
                Id: '', //
                TrainsClassificationName: '', //分类名称
                Level: '',
                ParentID: ''
            }
            this.formData = Object.assign({}, this.formData, temp)
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    postData.Level = postData.Level + 1;
                    let result = null
                    if (this.dialogStatus == 'create') {
                        delete postData.Id
                        // postData.ParentId = this.node.Id
                        result = trainsClassification.add(postData)
                    } else if (this.dialogStatus == 'update') {
                        result = trainsClassification.edit(postData)
                    }

                    result.then(res => {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData()
                    })
                }
            });
        },
        getDetail() {
            this.formData = Object.assign({}, this.formData, this.node.data)

            // trainsClassification.getName({
            //         id: this.node.Id
            //     }).then((res) => {

            //         this.trainsClassificationTreeName = res
            //     })
            //     .catch((err) => {

            //     });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }
};
</script>

<style lang="scss" scoped>
</style>
