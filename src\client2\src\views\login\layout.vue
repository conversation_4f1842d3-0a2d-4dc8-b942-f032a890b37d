<template>
    <div class="container">
        <div class="wrapper">
            <div class="content clearfix">
                <div class="login-logo-wrapper">
                    <div v-if="isBetaVersion" class="login-logo-wrapper-mask">
                        <div class="title">体验版</div>
                    </div>

                    <img style="position: absolute; top: 90px; left: 45px; width: 478px; height: 97px; pointer-events: none;" src="../../assets/images/site-title.png" alt="" srcset="">
                    <!-- <div style="position: absolute; top: 90px; left: 45px;">
                        <div style="font-size: 50px; color: #0078FF;">智能 协同 安全 高效</div>
                        <div style="font-size: 30px; font-weight: 400; color: #000034; margin-top: 20px;">助力组织数智化可持续发展</div>
                    </div> -->
                    <!-- <img src="../../assets/images/login-log.png" alt=""> -->
                    <img src="../../assets/images/login-log2.png" alt="" style="pointer-events: none;">
                </div>
                <div class="login-wrapper">
                    <router-view />
                    
                    <!-- <h3 class="title">{{ websiteTitle }}</h3>
                    <h4 class="sub-title">欢迎登录</h4>
                    <div class="login-form">
                        <div class="form-row">
                            <p>用户名</p>
                            <input v-model.trim="loginForm.username" placeholder="请输入工号/邮箱/手机号" ref="username" type="text">
                        </div>
                        <div class="form-row">
                            <p>密码</p>
                            <input v-model.trim="loginForm.password" @keyup.enter="handleLogin" ref="password" type="password">
                        </div>
                        <div class="form-row">
                            <el-checkbox v-model="rememberAccount">记住账号</el-checkbox>
                        </div>
                    </div>
                    <div class="btns">
                      <el-button type="primary" class="btn-login" :loading="loading" @click.native.prevent="handleLogin">登 录</el-button>
                      <el-button class="reg-btn" @click="handleToReg" type="text">没有账号？去注册</el-button>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import loginPageMixins from "@/mixins/loginPageMixins";
export default {
  name: 'login-layout',
  mixins: [loginPageMixins],
  data() {
    return {
      

    }
  }
}
</script>

<style scoped lang="scss">
$bgHeight: 642px;
$border-radius: 8px;

.container{
    width: 100%;
    height: 100%;
    background: url('../../assets/images/login-bg.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.wrapper{
    box-sizing: border-box;
    height: $bgHeight;
    background: #ffffff;
    border-radius: $border-radius;
    // position:absolute;
    // left:50%;
    // margin-left: -680px;
    // top:50%;
    // margin-top: -359px;
}

.content{
    margin: 0 auto;
    // min-width: 1160px;
    display: flex;
}

.content img{
    // width: 100%;
    // width: 680px;
    height: $bgHeight;
}

.login-logo-wrapper, .login-wrapper{
    float: left;
}

.login-logo-wrapper{
    position: relative;
    // margin-top: 180px;
    height: $bgHeight;

    .login-logo-wrapper-mask{
        position: absolute;
        background: rgba($color: $text-primary, $alpha: .6);
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: $border-radius 0 0 $border-radius;
        .title{
            text-align: center;
            color: #ffffff;
            font-size: 30px;
            font-weight: 600;
        }
    }
}

.login-wrapper{
    // margin-left: 100px;
    // width: 380px;
    // width: 625px;
    flex-shrink: 0;
    flex: 1;
    box-sizing: border-box;
    width: 530px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    
}

</style>

