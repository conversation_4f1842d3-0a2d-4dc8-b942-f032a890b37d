<template>
    <div>
        <app-dialog
            :title="pageTitle"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :maxHeight="700"
        >
            <template slot="body">
                <el-form
                    :rules="rules"
                    ref="formData"
                    :model="formData"
                    label-position="right"
                    :label-width="labelWidth"   style="padding-right: 20px;"
                >
                    <div class="wrapper" v-loading='loading'>
                        <div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="客户名称：" prop="CustomerName">
                                          {{formData.CustomerName}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="联系电话：" prop="Telephone">
                                        {{formData.Telephone}}
                                    </el-form-item>
                                </el-col>
                               <el-col :span="12">
                                    <el-form-item label="反应时间：" prop="ComplaintTime">
                                     {{formData.ComplaintTime | dateFilter('YYYY-MM-DD')}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                     <el-form-item label="地区：" prop='RegionalName'>
                                      {{formData.RegionalName}}
                                    </el-form-item>
                                </el-col>
                                <!-- <el-col :span="12">
                                    <el-form-item label="客服人员：" prop="CustomerServiceEmployeeName">
                                         {{formData.CustomerServiceEmployeeName}}
                                    </el-form-item>
                                </el-col> -->
                                <el-col :span="12">
                                    <el-form-item label="问题类型：" prop='ProblemTypes'>
                                      {{formData.ProblemTypes | problemTypesFilter }}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="造成影响：" prop='ImpactType'>
                                     {{formData.ImpactType | impactTypeFilter }}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="投诉问题：" prop="ComplaintTitle">
                                       {{formData.ComplaintTitle}}
                                    </el-form-item>
                                </el-col>
                                 <el-col :span="24">
                                    <el-form-item label="问题说明：" prop="Remark">
                                       {{formData.Remark}}
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24">
                                    <el-form-item label="问题回复：" prop="QuestionResponse">
                                     {{formData.QuestionResponse}}
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24">
                                  <el-form-item label="处理结果：" prop="ResultType">
                                      {{formData.ResultType | resultTypeFilter }}
                                  </el-form-item>
                                </el-col>

                                <el-col :span="24">
                                    <el-form-item label="责任部门：" prop="DutyDepartmentName">
                                     {{formData.DutyDepartmentName}}
                                    </el-form-item>
                                </el-col>

                                <el-col :span="24">
                                    <el-form-item label="经办人：" prop="AgentEmployeeName">
                                     {{formData.AgentEmployeeName}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                      <div>
                              <div class="panel-title-st">客诉单处理</div>
                              <div>
                                 <el-row>
                                     <el-col :span="24">
                                          <el-form-item label="处理反馈" prop="Results">
                                              <el-input v-model="formData.Results"  type="textarea" :disabled="!editable" :rows="5" maxlength="2000"></el-input>
                                          </el-form-item>
                                      </el-col>
                                 </el-row>
                              </div>
                        </div>


                        <div>
                            <div class="panel-title">附件</div>
                            <div>
                                <app-uploader
                                    :readonly="dialogStatus != 'dispose'"
                                    accept='all'
                                    :fileType='3'
                                    :max='10000'
                                    :value='formData.AttachmentList'
                                    :fileSize='1024 * 1024 * 500'
                                    :minFileSize='100 * 1024'
                                    @change='handleFilesUpChange'
                                ></app-uploader>
                            </div>
                       </div>

                    <!-- <div class="mt10">
                        <div class="panel-title">审批</div>
                        <div>
                            <approval-panel v-if="dialogStatus == 'dispose'" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                            <approval-detail :isOnlyViewDetail='isOnlyViewDetail' ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
                        </div>
                    </div> -->

                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <!-- <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
                <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn' text="处理"></app-button> -->

                <!-- <el-button @click="createData" type="primary"  :disabled='disabledBtn' v-show="dialogStatus == 'dispose'">保存</el-button> -->
                <el-button @click="createData('isApproval')" type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'dispose'">确认</el-button>

                 <!-- 审批模式下才显示审批 -->
                <!-- <el-button @click="handleApproval" type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval' && !isOnlyViewDetail">审批</el-button> -->
            </template>
        </app-dialog>

    </div>
</template>

<script>
import { regs } from "@/utils/regs"
import * as afterService from "@/api/afterSalesMgmt/customerComplaint"
import { serviceArea } from "@/api/serviceArea"
import * as orderVars from '../../salesMgmt/common/vars'
import * as maintenCenterVars from '../maintenCenter/common/vars'
import { vars } from "./vars"
// import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
// import approvalDetail from '../../projectDev/projectMgmt/workbench/common/approvalDetail'
import * as projVars from "../../projectDev/common/vars"

export default {
    name: "after-service-create",
    directives: {},
    components: {
        // approvalPanel,
        // approvalDetail
    },
    mixins: [],
    props: {
        specialPageTitle: {
            type: String
        },
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        // isOnlyViewDetail: {
        //     type: Boolean,
        //     default: false
        // },
    },
    filters: {
       problemTypesFilter(status) {
          let obj = vars.problemTypes.find(
            (s) => s.value == status
          );
          if (obj) {
            return obj.label;
          }
          return status;
        },
        impactTypeFilter(status) {
          let obj = vars.impactType.find(
            (s) => s.value == status
          );
          if (obj) {
            return obj.label;
          }
          return status;
        },
        resultTypeFilter(status){
          let obj = vars.resultType.find(
              (s) => s.value == status
            );
            if (obj) {
              return obj.label;
            }
          return status;
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus == "dispose";
        },
        pageTitle() {
            if(this.specialPageTitle) {
                return this.specialPageTitle
            }
            return '处理客诉单'
        },
        
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
           normalizer(node) {
              // treeselect定义字段
              return {
                  id: node.Id,
                  label: node.RegionalName,
                  children: node.children
              }
            },
            serviceArea,
            oldType: null,
            isTip: true,
            loading: false,
            disabledBtn: false,
            problemTypes:vars.problemTypes,
            impactType:vars.impactType,
            rules: {
                Results: { fieldName: "处理反馈", rules: [{ required: true }]},
            },
            labelWidth: "110px",
            formData: {
                Id: "", //ID
                CustomerName: '',//名称
                Telephone:'',   //联系电话
                CustomerServiceEmployeeName:'',
                ComplaintTime: null, //开始时间
                ProblemTypes:'',
                ImpactType:'',
                ComplaintTitle:'',
                Remark:'',
                QuestionResponse:'',
                ResultType:'',
                DutyDepartmentName:'',
                AgentEmployeeName:'',
                AttachmentList: [],
                // Approval: {//审批信息
                //     ApprovalEmployeeList: [[]],
                //     ApprovalType: 1,
                //     ApprovalOperatorEmployeeList: [], //已审批人员
                //     NoApprovalEmployeeList: [], //未审批人员
                //     CCEmployeeList: [], //抄送人
                //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //     ApprovalState: 1, //1: 进行中; 2: 已完成
                //     ApprovalResult: 1, //1: 通过； 2：不通过
                // },
            },
            treedata:[]
        };
    },
    methods: {
        handleChangeUsers(users) {
            this.formData.EmployeeList = users
        },
        resetFormData() {
            let temp = {
                Id: "", //ID
                CustomerName: '',//名称
                Telephone:'',   //联系电话
                ComplaintTime: null, //开始时间
                CustomerServiceEmployeeName:'',
                ProblemTypes:'',
                ImpactType:'',
                ComplaintTitle:'',
                Remark:'',
                QuestionResponse:'',
                ResultType:'',
                DutyDepartmentName:'',
                AgentEmployeeName:'',
                AttachmentList: [],
                // Approval: {//审批信息
                //     ApprovalEmployeeList: [[]],
                //     ApprovalType: 1,
                //     ApprovalOperatorEmployeeList: [], //已审批人员
                //     NoApprovalEmployeeList: [], //未审批人员
                //     CCEmployeeList: [], //抄送人
                //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //     ApprovalState: 1, //1: 进行中; 2: 已完成
                //     ApprovalResult: 1, //1: 通过； 2：不通过
                // },
            };
            this.formData = Object.assign({}, this.formData, temp);
        },
         //处理审批
        // handleApproval() {
        //     let postData = this.$refs.approvalDetail.getData()
        //     postData.BusinessId = this.id
        //     let approvalLabel = projVars.vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label

        //     this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
        //         confirmButtonText: '确定',
        //         cancelButtonText: '取消',
        //         type: 'warning'
        //     }).then(() => {
        //         //项目创建审批
        //         this.disabledBtn = true
        //         afterService.createApproval(postData).then(res => {
        //             this.disabledBtn = false
        //             this.$notify({
        //                 title: "提示",
        //                 message: "审批成功",
        //                 type: "success",
        //                 duration: 2000
        //             });
        //             this.$refs.appDialogRef.createData()
        //         }).catch(err => {
        //             this.disabledBtn = false
        //         })
        //     })
        // },
        createData(optype) {

            let flagVAlid = new Promise((resolve, reject) => {
                return resolve(true)
            })

            let validList = [flagVAlid]

            let validate = this.$refs.formData.validate()
            validList.push(validate);
            // 保存
            let isApproval= (optype == 'isApproval');
            // if(isApproval){
            //     let approvalPanelValidate = this.$refs.approvalPanel.validate()
            //     validList.push(approvalPanelValidate);
            // }

            Promise.all(validList).then(valid => {
                //提交数据保存
             let postData = JSON.parse(JSON.stringify(this.formData));
              postData.Id=this.id
            //   postData.Approval = this.$refs.approvalPanel.getData() //审批层区块
            //   postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
            //   postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
              postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
              postData.IsApproval = isApproval

              let result = afterService.handlingResult(postData);

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },
        getDetail() {
            this.loading = true
            afterService.detail({ id: this.id }).then(res => {
                this.loading = false
                this.isTip = false
                // if(this.dialogStatus == 'dispose'){
                //     // 清空上一个审批信息
                //     res.Approval = {//审批信息
                //         ApprovalEmployeeList: [[]],
                //         ApprovalType: 1,
                //         ApprovalOperatorEmployeeList: [], //已审批人员
                //         NoApprovalEmployeeList: [], //未审批人员
                //         CCEmployeeList: [], //抄送人
                //         ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                //         ApprovalState: 1, //1: 进行中; 2: 已完成
                //         ApprovalResult: 1, //1: 通过； 2：不通过
                //     }
                // }

                this.formData = Object.assign({}, this.formData, res);
                if(this.formData.CustomerServiceEmployee){
                    this.formData.CustomerServiceEmployeeName=this.formData.CustomerServiceEmployee.Name
                }

            }).catch(err => {
                this.loading = false
            });
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        handleChangeOwnerUsers(users) {
            this.formData.EmployeeList = users
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.panel-title-st {
  font-size: 16px;
  font-weight: 700;
  padding-top: 8px;
  padding-bottom: 14px;
  padding-left: 6px;
  border-top: 1px solid #dcdfe6;
}
</style>



<style lang="scss" scoped>

.wrapper{
    display: flex;
    .left{
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }
    .right{
        width: 40%;
    }
}

.panel-title{
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #DCDFE6;
    margin-bottom: 10px;
}

.mt10{ margin-top: 17px;}
</style>
