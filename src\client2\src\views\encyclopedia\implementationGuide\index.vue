<!--软件实施指南-->
<template>
  <div class="app-container">
    <div class="opt">
      <page-title title="软件实施指南" :showBackBtn='true' @goBack="back" text-bold></page-title>
    </div>
    <div class="bg-white">
      <div class="pageWrapper">
        <!--左侧树-->
        <div class="product-list">
          <el-input class="elInput" style="margin:5px 10px 5px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <div class="treeBox" v-loading='treeLoading'>
            <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
              <span class="custom-tree-node" slot-scope="{ node }">
                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '170px' : node.level == 2 ? '180px' : '170px'}">{{ node.label }}</span>
              </span>
            </el-tree>
          </div>
        </div>
        <!--右侧内容-->
        <div class="content-wrapper __dynamicTabContentWrapper">
          <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="dataSourceList" :isShowAllColumn="true" :loading="tableLoading" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false' @sortChagned="handleSortChange">

              <!-- <template slot="Status" slot-scope="scope">
                <span class="item-status" :style="{backgroundColor: getHandlerStatusObj(scope.row.Status).color}">
                  {{scope.row.Status | statusFilter }}
                </span>
              </template> -->

              <template slot="CreateTime" slot-scope="scope">
                {{scope.row.CreateTime | dateFilter("YYYY-MM-DD HH:mm")}}
              </template>

              <template slot="CreateEmployee" slot-scope="scope">
                <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
              </template>

              <!-- 表格查询条件区域 -->
              <template slot="conditionArea">
                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                  <template slot="KeyWords">
                    <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable v-model.trim="listQuery.KeyWords" placeholder="搜索实施指南名称/创建人"></el-input>
                  </template>

                  <!-- <template slot="Status">
                    <div class="month-range-wrapper">
                      <div class="start-month">
                        <el-select style="width: 100%;" clearable v-model="listQuery.Status" placeholder="请选择状态">
                          <el-option v-for="item in StatusList" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                  </template> -->

                  <template slot="SelectTime">
                    <el-date-picker style="width: 100%;" v-model="listQuery.SelectTime" type="datetimerange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" :clearable="false"></el-date-picker>
                  </template>
                </app-table-form>
              </template>

              <!-- 表格行操作区域 -->
              <template slot-scope="scope">
                <app-table-row-button @click="handleDetail(scope.row,'detail')" :type="2"></app-table-row-button>
              </template>

            </app-table>
          </div>
          <!----------------------------------------- 分页 ------------------------------------------->
          <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </div>
    </div>

    <!--详情 弹窗组件区-->
    <create-page @closeDialog="closeDialog" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="implementationGuideId" :selectClassifyId="selectClassifyId" @reload="getList"></create-page>

  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as guide from '@/api/informationCenter/implementationGuide'
import * as classify from '@/api/classify'
import elDragDialog from "@/directive/el-dragDialog";
import noData from "@/views/common/components/noData"
import { StatusList } from "@/utils/commonEnum";
import { listToTreeSelect } from "@/utils";
import createPage from './create'

export default {
  /**名称 */
  name: "guide",
  mixins: [indexPageMixin],
  directives: {
    elDragDialog
  },
  /**组件声明 */
  components: {
    noData,
    createPage
  },
  /**参数区 */
  props: {
    /**主键Id */
    keyId: {
      type: String
    }
  },
  filters: {
    statusFilter(status) {
      let obj = StatusList.find(
        s => s.value == status
      );
      if (obj) {
        return obj.label;
      }
      return status;
    },

    toStringFilter(item) {
      if (item) {
        return item;
      }
      return '无'
    },

  },
  /**数据区 */
  data() {
    return {
      implementationGuideId: '',

      /******************* 弹窗 *******************/
      dialogStatus: 'create',
      dialogFormVisible: false,
      selectClassifyId: "",

      /******************* 表格 *******************/
      layoutMode: 'simple',
      tableLoading: false,
      dataSourceList: [],
      total: 0,

      listQuery: {
        PageIndex: 1,
        PageSize: 20,
        KeyWords: '',
        ClassifyId: null,
        SelectTime: [],
        // Status: ""
      },

      StatusList: StatusList,

      tableSearchItems: [
        { prop: "KeyWords", label: "", mainCondition: true },
        { prop: "SelectTime", label: "创建时间" },
        // { prop: "Status", label: "反馈状态" },
      ],

      tabColumns: [
        {
          attr: { prop: "GuideName", label: "实施指南名称", showOverflowTooltip: true, width: 500 }
        },
        // {
        //   attr: { prop: "Status", label: "状态", sortable: 'Status' }, slot: true
        // },
        {
          attr: { prop: "ClassifyName", label: "分类", }
        },
        {
          attr: { prop: "PageView", label: "阅读量", sortable: 'PageView' }
        },
        {
          attr: { prop: "CreateEmployee", label: "创建人", }, slot: true
        },
        {
          attr: { prop: "CreateTime", label: "创建时间", width: 150, sortable: 'CreateTime' }, slot: true
        },
      ],

      /******************* 树 *******************/
      /**树节点弹窗 */
      classifyDialogFormVisible: false,
      classifyDialogStatus: "create",
      /**树筛选内容 */
      filterText: "",
      /**树数据 */
      treeData: [],
      treeLoading: false,
      /**树默认结构 */
      defaultProps: {
        children: "children",
        label: "Name"
      },
      /**树选中节点 */
      checkedNode: null,
      /**树参数 */
      paramNode: {
        Id: "",
        Name: "",
        Level: 1
      },
    };
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    },
    checkedNode: {
      handler(val) {
        if (val) {
          this.listQuery.ClassifyId = val.Id;
          this.listQuery.PageIndex = 1;
          this.getList();
        }
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
    this.loadTreeData();
  },
  methods: {
    getHandlerStatusObj(status) {
      return (this.StatusList.find(s => s.value == status) || {});
    },

    /******************* 表格事件 *******************/
    /**加载数据 */
    getList() {
      let _this = this;
      let postData = JSON.parse(JSON.stringify(this.listQuery));
      if (_this.checkedNode.Id) {
        _this.listQuery.ClassifyId = _this.checkedNode.Id;
      }
      if (postData.SelectTime.length > 0) {
        postData.StartTime = postData.SelectTime[0]
        postData.EndTime = postData.SelectTime[1]
      }
      postData.PageType = 'encyclopedia';
      postData = this.assignSortObj(postData);
      _this.tableLoading = true
      guide.getList(postData).then(response => {
        _this.tableLoading = false
        _this.total = response.Total;
        _this.dataSourceList = response.Items;
      }).catch(err => {
        _this.tableLoading = false
      });
    },


    handleSortChange({ column, prop, order }) {
      this.sortObj = { prop, order, };
      this.getList();
    },

    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },

    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },

    onResetSearch() {
      this.listQuery.SelectTime = []
      //   this.listQuery.Status = ""
      this.listQuery.KeyWords = ""
      //   this.listQuery.ClassifyId = null
      this.getList();
    },

    /******************* 弹窗相关 *******************/
    handleDetail(row, optType) {
      this.selectClassifyId = "";
      this.implementationGuideId = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },


    closeDialog() {
      this.dialogFormVisible = false;
      this.getList();
    },


    /******************* 树事件 *******************/
    loadTreeData() {
      let _this = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 1
      };
      _this.treeLoading = true
      classify.getListPage(paramData).then(response => {
        _this.treeLoading = false
        response.Items.unshift({
          Id: "",
          Name: "全部",
          Level: 0,
          ParentId: null
        });
        _this.treeData = listToTreeSelect(response.Items);

        if (_this.treeData && _this.treeData.length > 0) {
          if (
            !(
              _this.checkedNode &&
              response.Items.find(t => {
                return t.Id == _this.checkedNode.Id;
              })
            )
          ) {
            _this.checkedNode = _this.treeData[0];
          }
        } else {
          _this.checkedNode = null;
        }
        if (_this.checkedNode) {
          _this.$nextTick(() => {
            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
          });
        }
      }).catch(err => {
        _this.treeLoading = false
      });
    },
    /**按关键字过滤树菜单 */
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    back() {
      this.$router.go(-1)
    }
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.opt {
  top: 0;
  left: 0;
  right: 0;
  position: absolute;
}

.bg-white {
  position: absolute;
  width: 100%;
  height: 95%;
  overflow-y: auto;
  top: 40px !important;
}

.pageWrapper {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;

  .product-list {
    width: 250px;
    border-right: 1px solid #dcdfe6;
  }

  .content-wrapper {
    width: calc(100% - 200px);
    flex: 1;
    overflow-y: auto;

    .content {
      //   padding: 10px;
      padding-right: 0;
      min-height: 400px;

      .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
      }

      .tab-form-wrapper {
        padding: 10px;
      }
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
