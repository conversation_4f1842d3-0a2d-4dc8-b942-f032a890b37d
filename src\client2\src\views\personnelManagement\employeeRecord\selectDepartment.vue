<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogSel" v-bind="$attrs" v-on="$listeners" :width="700">
            <template slot="body">
                <div class="wrapper">
                    <el-form ref="formData" class="wrapper_left" :model="formData" :rules="rules" label-width="80px">
                        <el-form-item label="推送给" prop="ReceiveEmployeeList">
                            <emp-selector
                                class="fl"
                                style="width:100%;"
                                :readonly="false"
                                key="Id"
                                :showType="2"
                                :multiple="true"
                                :beforeConfirm='handleBeforeConfirm'
                                :list="formData.ReceiveEmployeeList"
                                @change="handleViewRange"
                            ></emp-selector>
                        </el-form-item>
                        <el-form-item label="发起人" prop="SendEmployeeIdList">
                            <emp-selector
                                class="fl"
                                style="width:100%;"
                                :readonly="false"
                                key="Id"
                                :showType="2"
                                :multiple="true"
                                :beforeConfirm='handleBeforeConfirm1'
                                :list="formData.SendEmployeeIdList"
                                @change="handleViewRange1"
                            ></emp-selector>
                        </el-form-item>
                    </el-form>
                    <el-tree class="tree" v-loading="loading" :data="orgsTree" show-checkbox node-key="Id" @check='checkOrg' :default-expanded-keys="defaultExpandedKey" :props="defaultProps" ref="orgsTree">
                    </el-tree>
                </div>
            </template>

            <template slot="footer">
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>

    </div>
</template>

<script>
import empSelector from "@/views/common/empSelector";
import { listToTreeSelect } from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
import * as hrLeaveBalanceData from "@/api/personnelManagement/hrLeaveBalanceData"
// import indexPageMixin from "@/mixins/indexPage";
import * as SystemEmployeeRecordRemindApi from "@/api/personnelManagement/SystemEmployeeRecordRemind";
export default {
    name: "select-department",
    directives: {},
    components: {
        empSelector
    },
    //   mixins: [indexPageMixin],
    computed: {
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "编辑推送规则";
            } else if (this.dialogStatus == "create") {
                return "创建推送规则";
            }
        },
    },
    props: {
        dialogStatus: {
            type: String
        },
        computationRuleId: {
            type: String,
            default: ""
        },
        disbArr: {
            type: Array,
            default: ()=>{
                return []
            }
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            this.OrgIds = []
            this.defaultExpandedKey = []
            this.formData = this.$options.data().formData
            if (val) {
                this.getOrgTree();
                if(this.computationRuleId) {
                    this.getDetail();
                }
            }
        },
    },
    filters: {
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    mounted() {

    },
    data() {
        return {
            loading: false,
            disabledBtn: false,
            orgsTree: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'label'
            },
            defaultExpandedKey: [], //部门树默认展开节点
            OrgIds: [],
            formData: {
                Id: "",
                RangeApplication: [],
                ReceiveEmployeeList: [],
                ReceiveEmployeeIds: [],
                SendEmployeeIdList: [],
                SendEmployeeId: '',
            },
            rules: {
                ReceiveEmployeeList: { fieldName: "接收人", rules: [{ required: true }]},
                SendEmployeeIdList: { fieldName: "发起人", rules: [{ required: true }]},
            },
        };
    },
    methods: {
        handleBeforeConfirm1(users) {
            if(users && users.length > 1) {
                this.$message({
                    message: '添加不可超过1人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 可见范围 选择人员/选择部门
        handleViewRange1(users) {
            if (users && users.length > 0) {
                this.formData.SendEmployeeIdList = users;
            } else {
                this.formData.SendEmployeeIdList = [];
            }
            this.$refs["formData"].validateField(`SendEmployeeIdList`);
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 10) {
                this.$message({
                    message: '添加不可超过10人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 可见范围 选择人员/选择部门
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.ReceiveEmployeeList = users;
            } else {
                this.formData.ReceiveEmployeeList = [];
            }
            this.$refs["formData"].validateField(`ReceiveEmployeeList`);
        },
        //查询并加载部门树
        getOrgTree() {
            let self = this;
            self.loading = true
            systemDepartment.getListByCondition({ Level: 1 }).then(response => {
                let list = response.map(function (item) {
                    return {
                        Id: item.Id,
                        label: item.DepartmentName,
                        ParentId: item.ParentId
                    }
                })
                var orgstmp = JSON.parse(JSON.stringify(list));
                var tempOrgsTree = listToTreeSelect(orgstmp);
                self.defaultExpandedKey.push(tempOrgsTree[0]['Id']);
                self.orgsTree = tempOrgsTree;
                // self.getDetail();
                if (self.dialogStatus == "create") {
                    self.$refs.orgsTree.setCheckedNodes([]);
                    var selectElement = []
                    self.orgsTree[0].children.forEach(element => {
                        var disabledObj = self.disbArr.find(s => s == element.Id);
                        if (disabledObj) {
                            self.$set(element, 'disabled', true)
                        }
                    });
                    self.$refs.orgsTree.setCheckedNodes(selectElement);
                }
                self.loading = false
            }).catch(err => {
                self.loading = false
            });
        },

        getDetail() {
            let self = this;
            self.loading = true;
            SystemEmployeeRecordRemindApi.GetRecordRemindingSettingRuleDetail({Id: this.computationRuleId}).then(res => {
                if(res.SendEmployee){
                    res.SendEmployeeIdList = [res.SendEmployee]
                }
                self.formData = Object.assign({}, self.formData, res)
                        
                if (res.DisableRangeApplication || res.RangeApplication) {
                    self.$refs.orgsTree.setCheckedNodes([]);
                    var selectElement = []
                    self.orgsTree[0].children.forEach(element => {
                        var disabledObj = res.DisableRangeApplication.find(s => s == element.Id);
                        if (disabledObj) {
                            self.$set(element, 'disabled', true)
                        }
                        var selectObj = res.RangeApplication.find(s => s == element.Id);
                        if (selectObj) {
                            selectElement.push(element);
                            self.OrgIds.push(element.Id)
                        }
                    });
                    self.$refs.orgsTree.setCheckedNodes(selectElement);
                }
                self.loading = false;
            }).catch(err => {
                self.loading = false
            });
        },

        //部门复选框发生变化触发事件
        checkOrg() {
            var self = this;
            self.OrgIds = [];
            //循环当前已选部门并将部门ID添加到self.listQuery.OrgIds
            self.$refs.orgsTree.getCheckedNodes().forEach((item) => self.OrgIds.push(item.Id));
        },

            //编辑
        createData() {
            let self = this, validate = self.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(self.formData));
                postData.RangeApplication = self.OrgIds;
                postData.ReceiveEmployeeIds = postData.ReceiveEmployeeList.map(s=>s.EmployeeId)
                postData.SendEmployeeId = postData.SendEmployeeIdList.map(s=>s.EmployeeId).toString();
                console.log(postData)
                self.disabledBtn = true
                let result = null;
                if (this.dialogStatus == "update") {
                    result = SystemEmployeeRecordRemindApi.EditRecordRemindingSettingRule(postData);
                } else if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = SystemEmployeeRecordRemindApi.AddRecordRemindingSettingRule(postData);
                }
                result.then(res => {
                    self.disabledBtn = false
                    self.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    self.$emit('saveSuccess');
                }).catch(err => {
                    self.disabledBtn = false
                })
            });
        },
        handleClose() {
            this.$refs.appDialogSel.handleClose();
        }
    }
};
</script>

<style lang="css" scoped>
.wrapper >>> .el-table__row {
    height: 35px !important;
}
</style>

<style lang='scss' scoped>
.wrapper {
    min-height: 400px;
    display: flex;
    .det {
        margin-bottom: 10px;
    }
    .wrapper_left{
        width: 60%;
        padding: 10px 50px 10px 0;
    }
    .tree{
        width: calc(40% - 2px);
        border-left: 1px solid #eee;
        padding: 10px 0;
    }
}
</style>