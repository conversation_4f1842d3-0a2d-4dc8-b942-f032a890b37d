<template>
  <div>
    <app-dialog title="年假计算规则" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
      <template slot="body">
        <div class="wrapper">
          <el-table :data="tabDatas" style="width: 100%" v-loading="loading">
            <el-table-column label="序号" type="index" width="50"></el-table-column>
            <el-table-column label="年假计算方式" width="150">
              <template slot-scope="scope">
                {{formulaMode.find(s=>s.value == scope.row.FormulaMode).label}}
              </template>
            </el-table-column>
            <el-table-column label="有效类型" width="120" prop="CalculateTimeType">
              <template slot-scope="scope">
                {{!!scope.row.CalculateTimeType?CalculateTimeTypes.find(s=>s.value == scope.row.CalculateTimeType).label:''}}
              </template>
            </el-table-column>
            <el-table-column label="重置日期" width="150" prop="CalculateTime">
              <template slot-scope="scope">
                {{ scope.row.CalculateTime | dateFilter('YYYY-MM-DD')}}
              </template>
            </el-table-column>
            <el-table-column label="适用范围" prop="RangeApplicationName" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>

      <template slot="footer">
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <app-button @click="handleClose" :buttonType='1' :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>

    <selectDepartment @closeDialog="closeDepartmentDialog" :dialogFormVisible="dialogDepartmentVisible" @saveSuccess="handleDepartmentSaveSuccess" :computationRuleId="computationRuleId"></selectDepartment>

  </div>
</template>

<script>

import * as hrLeaveBalanceData from "@/api/personnelManagement/hrLeaveBalanceData"
import indexPageMixin from "@/mixins/indexPage";
import selectDepartment from "./selectDepartment";

export default {
  name: "computation-rule",
  directives: {},
  components: {
    selectDepartment
  },
  mixins: [indexPageMixin],
  computed: {

  },
  props: {

  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.getList();
        }
      },
      immediate: true
    }
  },
  filters: {
  },
  created() {
  },
  data() {
    return {
      disabledBtn: false,
      loading: false,
      tabDatas: [], //原始数据

      formulaMode: [
        { value: 1, label: '司龄+工龄' },
        { value: 2, label: '司龄' }
      ],
      CalculateTimeTypes: [
        { value: 1, label: '按入职时间' },
        { value: 2, label: '按设定时间' }
      ],
      dialogDepartmentVisible: false,

      computationRuleId: ''
    };
  },
  methods: {
    handleEdit(index, row) {
      this.computationRuleId = row.Id;
      this.dialogDepartmentVisible = true;
    },

    closeDepartmentDialog() {
      this.dialogDepartmentVisible = false
    },
    handleDepartmentSaveSuccess() {
      this.getList()
      this.closeDepartmentDialog()
    },

    getList() {
      this.loading = true
      hrLeaveBalanceData.getHrComputationRules({}).then(res => {
        this.loading = false
        this.tabDatas = res
      }).catch(err => {
        this.loading = false
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang="css" scoped>
.wrapper >>> .el-table__row {
  height: 35px !important;
}
</style>

<style lang='scss' scoped>
.wrapper {
  min-height: 400px;
  .det {
    margin-bottom: 10px;
  }
}
</style>