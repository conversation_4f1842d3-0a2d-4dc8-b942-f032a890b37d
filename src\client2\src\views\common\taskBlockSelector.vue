<template>
  <div class="task_block_selector">
    <div class="task_list">
      <div
        class="task_item"
        :class="{ show_del_icon: !disabled, not_click: !isClickTaskDetail }"
        v-for="(item, index) in taskList_"
        :key="index"
      >
        <div class="task_item_left">
          <div class="top">
            <TaskStatus :taskState="item.HypertrunkTaskStatus" class="task_status" />
            <ProgressBar v-if="isShowProgressBar" style="margin-right: 4px;" :value="taskProgress(item)" :text="String(taskProgress(item))" />
            <span class="name a-link"  @click="openTaskDialog(item)">{{ item.TaskName }}</span>
          </div>
          <div class="bottom omit a-link" @click="handleMainLine(item)">
            {{trunkNameAndStageName(item)}}
          </div>
        </div>
        <div class="task_item_right" v-if="isShowName">
          <span class="person" v-if="item.PrincipalEmployeeList">
            {{ item.PrincipalEmployeeList.map(s => s.Name).join("、") }}
          </span>
        </div>
        <svg-icon
          icon-class="delete-icon"
          class="delete_icon"
          v-if="!disabled"
          @click.stop.native="handleDelTask(index)"
        />
      </div>
      <areaBtn @click="handleAddTask" v-if="showAddBtn" :type="1" text="添加关联任务" />
      <areaBtn v-if="disabled&&!taskList_.length" :type="2" text="暂无数据" />
    </div>
    <!--任务选择器-->
    <taskSelector
      :isShow="taskSelectorIsShow"
      :condition="taskReqParams"
      @changed="taskSelectorChanged"
      :checkedList="taskList_"
      @closed="handleCloseDepDialog"
      :multiple="multiple"
    />
  </div>
</template>

<script>
import TaskStatus from "@/views/workbench/mainLineMgmt/components/taskStatus.vue";
import ProgressBar from "@/views/workbench/mainLineMgmt/components/guidepostInfo/progressBar.vue";
import taskSelector from "@/views/workbench/mainLineMgmt/components/guidepostInfo/taskSelector.vue";
import AreaBtn from "@/views/workbench/mainLineMgmt/components/guidepostInfo/areaBtn.vue";

export default {
  name: "taskBlockSelector",
  components: { AreaBtn, ProgressBar, TaskStatus, taskSelector },
  props: {
    // 任务数组(支持value/v-model)
    value: {
      type: Array,
      default: () => [],
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 任务选择器的请求参数
    taskReqParams: {
      type: Object,
      default: () => ({}),
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 是否可以点击任务详情
    isClickTaskDetail: {
      type: Boolean,
      default: true,
    },
    // 点击添加前置钩子
    beforeAddTask:{
      type:Function,
      default:()=>true
    },
    isShowName: {
      type: Boolean,
      default: true
    },
    isShowProgressBar: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      taskList_: [],
      taskSelectorIsShow: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.taskList_ = this.$_.cloneDeep(val);
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 任务进度
    taskProgress() {
      return item => {
        return item.Progress ? Math.floor(Number(item.Progress)) : 0;
      };
    },
    // 添加按钮显示隐藏
    showAddBtn() {
      if (this.disabled) return false;
      if (!this.multiple && this.taskList_.length >= 1) return false;
      return true;
    },
    // 干线名称和阶段名称
    trunkNameAndStageName(){
      return item =>{
        return item?.HypertrunkGuidepostNameList?.[0]?.Name ||  item.TrunkName || ''
      }
    }
  },
  methods: {
    // 查看任务详情
    openTaskDialog(item) {
      if (!this.isClickTaskDetail) return;

      this.$store.commit("TASK_SET_OPEN_DIALOG", {
        busType: "task", //任务弹框
        optType: "detail", //任务详情
        params: {
          taskId: item.Id,
        },
      });
    },
    // 查看干线详情
    handleMainLine(item){
      const id = item?.HypertrunkGuidepostNameList?.[0]?.HypertrunkId
      if (!id) {
        this.$message.error("未找到相关干线");
        return;
      }
      const routeData = this.$router.resolve({
        path: `/workbench/mainLineMgmt/follow/${id}`,
      });
      window.open(routeData.href, "_blank");
    },
    // 删除任务
    handleDelTask(index) {
      this.taskList_.splice(index, 1);
      this.change();
    },
    // 打开选择任务弹窗
    handleAddTask() {
      if(!this.beforeAddTask()) return
      this.taskSelectorIsShow = true;
    },
    // 任务选择器改变钩子
    taskSelectorChanged(data) {
      this.taskList_ = this.$_.cloneDeep(data || []);
      this.change();
    },
    handleCloseDepDialog() {
      this.taskSelectorIsShow = false;
    },
    change() {
      this.$emit("change", this.taskList_);
      this.$emit("input", this.taskList_);
    },
  },
};
</script>

<style lang="scss" scoped>
.task_block_selector {
  .task_list {
    .add_task_item {
      display: flex;
      align-items: center;
      height: 40px;
      background: #f7f8fa;
      border: 1px solid #e5e6eb;
      padding: 5px;
      border-radius: 4px;
      margin-bottom: 10px;
      position: relative;
      .el-icon-circle-plus {
        margin-right: 5px;
      }
    }
    .task_item {
      display: flex;
      align-items: stretch;
      justify-content: space-between;
      border: 1px solid #e5e6eb;
      background-color: #f7f8fa;
      padding: 5px 10px;
      border-radius: 4px;
      margin-bottom: 10px;
      position: relative;
      .task_item_left{
        flex:1;
        overflow: hidden;
        .top{
          display: flex;
          align-items: center;
          .task_status {
            margin-right: 4px;
          }
          .name {
            flex: 1;
            color: $color-primary;
            padding-right: 4px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .bottom{
          font-size: 12px;
          line-height: normal;
          color: $text-secondary;
          &:hover{
            color: $color-primary;
          }
        }
      }
      .task_item_right{
        position: relative;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        padding-left: 10px;
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 1px;
          height: 100%;
          background-color: $border-color-light;
        }
        .person {
          display: block;
          min-width: 52px;
          color: $text-secondary;
        }
      }
      &.not_click {
        cursor: auto;
        .name {
          color: $text-main-color !important;
        }
      }
      &.show_del_icon {
        width: calc(100% - 30px);
      }
      .delete_icon {
        position: absolute;
        top: 50%;
        right: -30px;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
