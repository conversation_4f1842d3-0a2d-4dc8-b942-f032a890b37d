<template>
    <div>
        <app-dialog title="已添加站点" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
            <template slot="body">
                <div class="pageWrapper __dynamicTabContentWrapper">
                    <div class="content __dynamicTabWrapper">
                        <app-table ref="mainTable"
                        :tab-columns="tabColumns" :tab-datas="tabDatas" :optColWidth="120" @rowSelectionChanged="rowSelectionChanged"
                        :isShowAllColumn="true" :loading="listLoading" :isShowOpatColumn="true"
                        :startOfTable="startOfTable" :multable="true" :layoutMode='layoutMode'
                        :isShowBtnsArea='false'>
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter"
                                @onReset="onResetSearch" :layoutMode='layoutMode'>
                                    <template slot="KeyWords">
                                        <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}'
                                            clearable v-model.trim="listQuery.KeyWords" placeholder="搜索地区名称"></el-input>
                                    </template>

                                    <template slot="RegionalId">
                                        <div class="el-input el-input--mini">
                                            <div style="display: flex; height: 28px;line-height: 28px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; ">
                                                <div style="padding-left: 10px;">
                                                <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">选择</span>
                                                </div>
                                                <div style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="listQuery.areaName">{{ listQuery.areaName }}</div>
                                                <div style="width: 28px; text-align: center;">
                                                <i style="cursor: pointer;" title="删除" @click="handleClearRegional" v-show="listQuery.RegionalId" class="el-icon-close"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <template slot="IsStationControlSystem">
                                        <el-select
                                            style="width:220px;"
                                            class="fr"
                                            v-model="listQuery.IsStationControlSystem"
                                            clearable
                                            placeholder=""
                                            >
                                            <el-option
                                                v-for="item in isStationControlSystemType"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            ></el-option>
                                        </el-select>
                                    </template>

                                    <!-- 表格批量操作区域 -->
                                    <template slot="btnsArea">
                                        <el-button style="margin-left: 4px;" type="primary" @click="handleAdd" :disabled="addLoading">新增回访站点</el-button>
                                        <el-button type="danger" @click="handleDel" :disabled="delLoading">批量删除</el-button>
                                    </template>
                                </app-table-form>
                            </template>

                            <template slot="IsStationControlSystem" slot-scope="scope">
                                <span :class="scope.row.IsStationControlSystem?'blue':'red'">
                                    {{scope.row.IsStationControlSystem | isStationControlSystemTypeFilter}}
                                </span>
                            </template>


                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button @click="handleRegChange(scope.row)" :type="1" text='数据迁移'></app-table-row-button>
                                <app-table-row-button @click="handleEdit(scope.row,'update')" :type="1"></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" text="关闭" type></app-button>
                <!-- 确认 -->
            </template>
        </app-dialog>

        
        <v-area-choose
        v-if="dialogRegionFormVisible || dialogRegionalVisible"
        @closeDialog="closeRegionDialog"
        @electedRegionalData="handleRegionSaveSuccess"
        :dialogFormVisible="dialogRegionFormVisible || dialogRegionalVisible"
        :condition='{}'
        :multiple='dialogRegionFormVisible ? true : false'
        :checkedList="dialogRegionFormVisible ? [] : (listQuery.RegionalId ? [listQuery.RegionalId] : [])"
        :defaultExpandLevel='1'
        ></v-area-choose>

        <v-area-choose
        v-if="dialogRegVisible"
        @closeDialog="closeRegDialog"
        @electedRegionalData="electedRegionalData"
        :beforeConfirm='handleBeforeConfirm'
        :dialogFormVisible="dialogRegVisible"
        :condition='{}'
        :multiple='false'
        :checkedList="[currentRegId]"
        >
            <div slot="tip-area">
                <div style="color: #409EFF; padding: 10px;">注意事项：数据迁移会将站点信息、定位回访记录、设备、问题迁移到被选择的站点，请确认后操作！</div>
            </div>
        </v-area-choose>

        <edit-area-page
            @closeDialog='closeDialog' 
            @saveSuccess='handleSaveSuccess'
            :dialogFormVisible='dialogEditVisible'
            :id='selectId'
        ></edit-area-page>
    </div>
</template>

<script>
import { vars } from './enum'
import indexPageMixin from "@/mixins/indexPage";
import * as regionalBusinessRelationApi from "@/api/afterSalesMgmt/regionalBusinessRelation";
import * as systemManagement from "@/api/systemManagement/regionalManagement";
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import editAreaPage from "@/views/systemManagement/regionalManagement/edit.vue";
let isStationControlSystemType = [
    {value: true, label: '有'},
    {value: false, label: '没有'},
]
export default {
    name: "report-visitSiteSet",
    directives: {},
    components: {
        vAreaChoose,
        editAreaPage
    },
    mixins: [indexPageMixin],
    computed: {
    },
    watch: {

    },
    props: {
        dialogStatus: {
            //create、update、detail、follow（跟进）
            type: String
        },
        id: {
            type: String,
            default: ""
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(val) {
                    this.getList();
                }
            },
            immediate: true
        },
    },
    created() {
    },
    filters: {
        isStationControlSystemTypeFilter(val) {
            let obj = isStationControlSystemType.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    data() {
        return {
            dialogRegionFormVisible: false,
            isStationControlSystemType: isStationControlSystemType,
            addLoading: false,
            delLoading: false,
            listLoading: false,
            listQuery: {
                PageSize: 10,
                RegionalId: null,
                IsStationControlSystem: null,
            },
            tabDatas: [], //原始数据
            total: 0,
            /******************* 表格 *******************/
            layoutMode: 'simple',
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                { prop: "RegionalId", label: "地区" },
                { prop: "IsStationControlSystem", label: "站控系统" },
            ],
            tabColumns: [
                { attr: { prop: "RegionalName", label: "地区名称", showOverflowTooltip: true, } },
                { attr: { prop: "IsStationControlSystem", label: "站控系统", width: '150px' }, slot: true },
            ],
            multipleSelection: [],

            dialogEditVisible: false,
            selectId: '',

            dialogRegionalVisible: false,

            dialogRegVisible: false,
            currentRegId: '', //老地址

        };
    },
    methods: {
        handleRegionalDialog() {
            this.dialogRegionalVisible=true;
        },
        handleClearRegional() {
            this.listQuery.RegionalId = ''
            this.listQuery.areaName = ''
        },
        /**
         * 地区选择器弹框
         */
        handleAdd() {
            this.dialogRegionFormVisible = true;
        },
        closeRegionDialog() {
            this.dialogRegionFormVisible = false;
            this.dialogRegionalVisible = false;
        },
        // 地区选择器弹框 返回选择结果
        handleRegionSaveSuccess(rows) {
            if(this.dialogRegionFormVisible) {
                let postData = {
                    RegionalIdList: rows.map(s => s.Id) || [],
                    RegionalBusinessType: 1
                }
                console.log(postData)
                this.addLoading = true;
                regionalBusinessRelationApi.edit(postData).then(res => {
                    this.addLoading = false;
                    this.getList()
                    this.$emit('reload');
                })
                .catch((err) => {
                    self.addLoading = false;
                });
            }else if(this.dialogRegionalVisible) {
                if(rows) {
                    this.listQuery.RegionalId=rows.Id;
                    this.listQuery.areaName=rows.ParentName;
                }else{
                    this.listQuery.RegionalId='';
                    this.listQuery.areaName='';
                }
            }
            this.closeRegionDialog();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleDel(){
            let self = this;
            if(self.multipleSelection.length==0){
                self.$message({
                    message: '请选择至少一条数据!',
                    type: 'warning'
                });
                return false
            }
            self.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let ids = self.multipleSelection.map(s=>s.Id)
                self.delLoading = true;
                regionalBusinessRelationApi.del(ids).then(res => {
                    self.delLoading = false;
                    self.$notify({
                        title: "成功",
                        message: "操作成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getList();
                    self.$emit('reload');
                })
                .catch((err) => {
                    self.delLoading = false;
                });
            });

        },
        //数据迁移
        handleRegChange(row) {
            this.currentRegId = row.RegionalId
            this.dialogRegVisible = true;
        },
        closeRegDialog() {
          this.dialogRegVisible = false;
        },
        async handleBeforeConfirm(data) {
            let result = false
            let _this = this
            let oldId = _this.currentRegId
            let newId = data.Id
            if(oldId != newId) {
                let postDatas = {
                    OldRegionalId: oldId,
                    NewRegionalId: newId
                }
                await systemManagement.dataMigration(postDatas).then(response => {
                    result = true
                    _this.getList()
                    this.$notify({
                        title: "提示",
                        message: "迁移成功",
                        type: "success",
                        duration: 2000
                    });
                })
            }else{
                this.$message({
                    message: '迁移后的地址和原地址相同，不可以迁移！',
                    type: 'error'
                })
            }
            return result

        },
        electedRegionalData(data){
            this.$emit('reload');
            
            // let oldId = this.currentRegId
            // let newId = data.Id

        },
        // 编辑/详情
        handleEdit(row, optType){
            this.selectId = row.RegionalId;
            this.dialogEditVisible = true;
        },
        //获取成员列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData.RegionalBusinessType = 1
            postData.SortType = 1
            this.listLoading = true;
            regionalBusinessRelationApi.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        onResetSearch() {
            this.listQuery = this.$options.data().listQuery;
            this.getList();
        },

        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleSaveSuccess(){
            this.getList()
            this.$emit('reload');
            this.dialogEditVisible = false
        },
        closeDialog(){
            this.dialogEditVisible = false
        },
    }
}
</script>


<style lang='scss' scoped>
.pageWrapper{
    display: flex;
    height: 558px;

    .red{
        color: #F56C6C;
    }
    .blue{
        color: #409EFF;
    }
}


</style>