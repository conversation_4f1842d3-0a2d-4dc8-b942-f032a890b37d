<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData"
                label-position="right" label-width="100px">
                    <el-row class="wrapper" v-loading='loading'>
                        <el-col :span="12">
                            <el-form-item label="评价人">
                                {{ formData.UserName }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="职责">
                                {{ formData.AppraiserEmployeeDuty }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="评价内容" prop="Remark">
                                <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="4" v-model="formData.Remark"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import empSelector from '@/views/common/empSelector'
import * as EmployeePositiveEvaluationApi from "@/api/personnelManagement/EmployeePositiveEvaluation";
import { getUserInfo } from "@/utils/auth";
export default {
    name: "evaluatePage",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "转正评价";
            } else if (this.dialogStatus == "update") {
                return "转正评价";
            } else if (this.dialogStatus == "detail") {
                return "转正评价";
            }
            return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
            default: "create",
        },
        row: {
            type: Object,
            required: true
        }
        
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();

                    if(this.dialogStatus == 'create') {
                        let empId = getUserInfo().employeeid
                        this.formData.Id = ''
                        this.formData.UserName = getUserInfo().username;
                        this.formData.EmployeeTrainingPlanId = this.row.Id

                        let title = ''
                        if(this.row) {
                            if(this.row.TutorEmployeeId == empId) {
                                title = '导师'
                            }else if(this.row.ImmediateSuperiorId == empId) {
                                title = '直属上级'
                            }else if(this.row.DepartmentManagerId == empId) {
                                title = '部门经理'
                            }
                        }
                        this.formData.AppraiserEmployeeDuty = title
                    }
                    // if (this.dialogStatus != "create" && this.id) {
                    //     this.getDetail();
                    // }
                }
            },
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {
                Remark: {fieldName: "评价内容",rules: [{ required: true }]},
            },
            formData: {
                Id: '',
                UserName: '',
                EmployeeTrainingPlanId: '',
                Remark: '', // 辅导计划
                AppraiserEmployeeDuty: ''
            }
        };
    },
    methods: {
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    self.disabledBtn = true;
                    let result = null;
                    if (self.dialogStatus == 'create') {
                        result = EmployeePositiveEvaluationApi.add(postData)
                    }
                    // if (self.dialogStatus == 'update') {
                    //     result = EmployeePositiveEvaluationApi.edit(postData)
                    // }
                    result.then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            // this.loading = true
            // EmployeePositiveEvaluationApi.detail({ id: this.id }).then(res => {
            //     this.formData = Object.assign({}, this.formData, res);
            //     if (res.TutorEmployeeId) {
            //         this.formData.TutorEmployeeIdList.push(res.TutorEmployee)
            //     }
            //     this.loading = false
            // }).catch(err => {
            //     this.loading = false
            // });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
}
</style>