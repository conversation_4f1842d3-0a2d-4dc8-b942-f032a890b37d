<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750">
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="模板类型" prop="ClassifyId">
                                <treeselect key='type2'
                                    class="treeselect-common"
                                    :append-to-body="true"
                                    :normalizer="unitNormalizer"
                                    v-model="formData.ClassifyId" :default-expand-level="3"
                                    :options="treeDatas" :multiple="false" placeholder='' :show-count="true"
                                    :noResultsText='noResultsTextOfSelTree'
                                    :disabled="!editable"
                                    :noOptionsText="noOptionsTextOfSelTree"
                                    zIndex='9999'
                                    @input="hadnleChangeCustomerUnitId"
                                    >
                                </treeselect>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="模板名称" prop="Name">
                                <el-input v-model="formData.Name" :disabled="!editable" maxlength="50"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div>
                        <el-button type="primary" @click="handleAddGroup" :disabled='!editable || formData.TypeList.length >= 30'>添加类型</el-button>
                    </div>
                    <div class="groups">
                        <el-collapse v-model="activeNames" ref="coll">
                            <el-collapse-item v-for="(item, idx) in formData.TypeList" :key="item.Id" :name="'group' + idx">
                                <template slot="title">
                                    <!-- 点击 title 区域，不可展开或收起 -->
                                    <div class="group-title-wrapper" @click.stop="() => {}">
                                        <div class="group-title">
                                            <el-form-item
                                                :label="'类型' + (idx + 1)"
                                                :prop="'TypeList.' + idx + '.Name'"
                                                :rules="{required: true, message: ' ', trigger: 'change'}"
                                            >
                                                <el-input @focus="handleFocus" @blur="handleBlur" :disabled="!editable" maxlength="20" v-model.trim="formData.TypeList[idx].Name"></el-input>
                                            </el-form-item>
                                        </div>

                                        <div class="item-btns">
                                            <!-- <div @click="move('up', idx)">
                                                <svg-icon icon-class="arrow-circle-up" v-show="idx > 0" class="el-icon-top" title="上移"></svg-icon>
                                            </div>
                                            <div @click="move('down', idx)">
                                                <svg-icon icon-class="arrow-circle-down" v-show="idx < formData.TypeList.length - 1" title="下移"></svg-icon>
                                            </div> -->

                                            <i v-if="editable" class="el-icon-circle-plus" style="font-size: 20px; color: #409EFF; margin-left: 4px;" title="添加" @click="handleAdd(item)"></i>
                                            <i v-if="editable && idx > 0" class="el-icon-delete-solid" style="font-size: 20px; color: red; margin-left: 4px;" title="删除" @click="removeDomain(item)"></i>
                                        </div>
                                    </div>
                                </template>
                                <div class="group-title-wrapper-list">
                                    <noData v-if="!item.FieldList || item.FieldList.length == 0" :minHeight='20'></noData>
                                    <template v-else>
                                        <el-col v-for="(p, idx2) in item.FieldList" :span="12" :key="'prop' + idx2">
                                            <div class="group-title-wrapper">
                                                <div class="group-title">
                                                    <el-form-item
                                                        :label="'属性' + (idx2 + 1)"
                                                        :prop="'TypeList.' + idx + '.FieldList.' + idx2 + '.Name'"
                                                        :rules="{required: true, message: ' ', trigger: 'change'}"
                                                    >
                                                        <el-input :disabled="!editable" maxlength="20" v-model.trim="formData.TypeList[idx].FieldList[idx2].Name"></el-input>
                                                    </el-form-item>
                                                </div>

                                                <div class="item-btns">
                                                    <i v-if="editable && idx2 > 0" class="el-icon-delete-solid" style="font-size: 20px; color: red; margin-left: 4px;" title="删除" @click="removeProp(item, idx2)"></i>
                                                </div>
                                            </div>
                                        </el-col>
                                    </template>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                    </div>
                </div>

            </el-form>
        </template>
        <template slot="footer">
            <!-- <span class="fl m-r-50" v-if="dialogStatus == 'create'">
                <el-checkbox v-model="isContinue">继续添加</el-checkbox>
            </span> -->
            
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as classify from "@/api/classify";
import * as afterVistTemplate from "@/api/afterSalesMgmt/afterVistTemplate";
import { listToTreeSelect } from "@/utils";
import * as metadata from '@/api/systemManagement/metadata'
import noData from "@/views/common/components/noData";
import commMixin from './mixins'

export default {
    name: "custom-mgmt-pool-create",
    directives: {},
    components: {
        noData,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [commMixin],
    props: {
        dialogStatus: {
            //create、update、detail、follow（跟进）
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        chooseUnitId: {
            type: String,
            default: ''
        },
    },
    watch: {

        "$attrs.dialogFormVisible": {
            
            handler(val) {
                // if (!val) {
                //     this.isContinue = false;
                // }
                
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }

                    if(this.dialogStatus == 'create') {

                        this.handleAddGroup()
                    }

                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            let mainTitle = this.isDeviceParams ? '设备参数模板' : '回访记录模板'
            if (this.dialogStatus == "create") {
                return `创建${mainTitle}`
            } else if (this.dialogStatus == "update") {
                return `编辑${mainTitle}`
            } else if (this.dialogStatus == "detail") {
                return `${mainTitle}详情`
            } 
        },
    },
    created() {
        this.getCustomerProps()
        if(this.dialogStatus == 'create') {
            this.getTreeDatas().then(res => {
                this.initTreeDatas(res.Items)
            })
        }
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            // isContinue: false,
            customerProps: [],
            treeDatas: [],
            unitNormalizer(node) {
                // treeselect定义字段
                return {
                    id: node.Id,
                    label: node.Name,
                    children: node.children
                }
            },
            months: Array.from(Array(12), (v, k) => {
                return {
                    value: k + 1,
                    label: `${k + 1}月`
                } 
            }),
            days: Array.from(Array(31), (v, k) => {
                return {
                    value: k + 1,
                    label: `${k + 1}日`
                } 
            }),
            // ClientUnitNames: [],
            pickerOptions: {
                //禁止选择当前日期后的时间
                disabledDate(time) {
                    let currentDate = dayjs().format('YYYY-MM-DD')
                    let choiceDate = dayjs(time).format('YYYY-MM-DD')
                    let diffDay = dayjs(currentDate).diff(dayjs(choiceDate), 'day')
                    if (diffDay >= 0) {
                        return false
                    }
                    return true
                },
            },
            loading: false,
            disabledBtn: false,
            rules: {
                Name: { fieldName: "模板名称", rules: [{ required: true }] },
                ClassifyId: { fieldName: "模板分类", rules: [{ required: true, trigger: 'change' }] },

            },
            labelWidth: "80px",
            activeNames: [],
            formData: {
                Id: "", //
                ClassifyId: null,
                Name: '', //设备参数模板名称
                TypeList: [
                    // {
                    //     Name: '',
                    //     FieldList: [{
                    //         Name: ''
                    //     }]
                    // },
                ],

            },
            // treedata: [],
            tabsData: [{
                    name: 'customAnalysis',
                    label: '客户分析'
                },
                {
                    name: 'relPerson',
                    label: '客户关系人'
                },
                {
                    name: 'expenseRecord',
                    label: '费用记录'
                },
                {
                    name: 'visitRecord',
                    label: '拜访记录'
                },
                {
                    name: 'assocOrder',
                    label: '关联订单'
                },
            ],
        };
    },

    methods: {
        handleFocus() {
            this.collClick = this.$refs.coll.setActiveNames
            this.$refs.coll.setActiveNames = () => {}
        },
        handleBlur() {
            this.$refs.coll.setActiveNames = this.collClick
        },    
        getCtrlOptions(metadataId) {
            // PropertyDataValue options [{value: 1, label: 'xxx'}] 组成的字符串
            let metadata = this.customerProps.find(s => s.value == metadataId)
            if(!metadata || !metadata.obj.PropertyDataValueList) {
                return []
            }
            return metadata.obj.PropertyDataValueList
        },
        //获取控件类型（跟进元数据id获取元数据对应的控件卖局）
        getCtrlType(metadataId) {
            let metadata = this.customerProps.find(s => s.value == metadataId)
            if(metadata) {
                return metadata.obj.PropertyDataType
            }
            return 0
        },
        handleAdd(group) {
            if(group) {
                if(group && group.FieldList.length >= 50) {
                    this.$message({
                        message: "添加失败，不能超过50条",
                        type: "error"
                    });
                    return false
                }
                group.FieldList.push({
                    Name: ''
                })
            }
        },
        removeProp(group, idx) {
            if(group && group.FieldList) {
                this.$confirm("是否确认删除", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    group.FieldList.splice(idx, 1)
                });
            }
        },
        handleAddGroup() {
            this.formData.TypeList.push({
                Name: '',
                FieldList: [{
                    Name: ''
                }]
            },)

            this.activeNames.push('group' + (this.formData.TypeList.length - 1))
        },
        // move(direction, currIdx) {
        //     if (
        //         (direction == "up" && currIdx > 0) ||
        //         (direction == "down" && currIdx < this.formData.TypeList.length - 1)
        //     ) {
        //         let currRow = JSON.parse(JSON.stringify(this.formData.TypeList[currIdx]));
        //         let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
        //         this.formData.TypeList.splice(currIdx, 1);
        //         this.formData.TypeList.splice(targetIdx, 0, currRow);
        //     }
        // },
        //删除动态行
        removeDomain(item) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                var index = this.formData.TypeList.indexOf(item)
                if (index !== -1) {
                    this.formData.TypeList.splice(index, 1)
                }
            });
            
        },
        hadnleChangeCustomerUnitId() {
            this.$refs.formData.validateField("ClassifyId");
        },
        getTreeDatas() {
            let paramData = {
                PageSize: 10000,
                PageIndex: 1,
                BusinessType: this.isDeviceParams ? 5 : 6
            };
            return classify.getListPage(paramData)
        },
        initTreeDatas(list) {
            let _this = this
            _this.treeDatas = listToTreeSelect(list, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构

            if(this.dialogStatus == 'create' && this.chooseUnitId && !!list.find(s => s.Id == this.chooseUnitId)) {
                this.formData.ClassifyId = this.chooseUnitId
            }
        },
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        resetFormData() {
            let temp = {
                Id: "", //
                // ClassifyId: null,
                Name: '', //设备参数模板名称
                TypeList: [
                ],
            };
            this.formData = Object.assign({}, this.formData, temp);
        },
        getCustomerProps() {
            let postData = {
                PageIndex: 1,
                PageSize: 10000
            }
            metadata.getList(postData).then(res => {
                let datas = res.Items.map((s, idx) => {
                    return{
                        value: s.Id,
                        label: s.Name,
                        obj: s
                    }
                });
                this.customerProps = datas
            });
        },
        handleRemove(idx) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.formData.TypeList.splice(idx, 1);
            });
        },
        createData() {
            let validate = this.$refs.formData.validate();

            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.TemplateType = this.isDeviceParams ? 1 : 2

                this.disabledBtn = true

                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = afterVistTemplate.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = afterVistTemplate.edit(postData);
                }

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    this.$refs.appDialogRef.createData();

                    // if (this.isContinue) {
                    //     this.resetFormData();
                    // } 

                    // this.$refs.appDialogRef.createData(postData, this.isContinue);
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },
        async getDetail() {
            let m1 = this.getTreeDatas()
            let m2 = afterVistTemplate.detail({id: this.id})
            this.loading = true
            Promise.all([m1, m2]).then(res => {

                this.loading = false
                let res1 = res[0]
                let res2 = res[1]

                this.initTreeDatas(res1.Items)
                this.formData = Object.assign({}, this.formData, res2);
                this.formData.TemplateType = this.isDeviceParams ? 1 : 2

                if(this.formData.TypeList && this.formData.TypeList.length > 0) {
                    this.formData.TypeList.forEach((el, idx) => {
                        this.activeNames.push('group' + idx)    
                    });
                }
            }).catch(err => {
                this.loading = false
            });
            // afterVistTemplate.detail({
            //     id: this.id
            // }).then(res => {
            //     this.loading = false
            //     this.formData = Object.assign({}, this.formData, res);
            //     this.formData.TemplateType = this.isDeviceParams ? 1 : 2

            //     if(this.formData.TypeList && this.formData.TypeList.length > 0) {
            //         this.activeNames = this.formData.TypeList.map(s => s.Id)
            //     }

            // }).catch(err => {
            //     this.loading = false
            // });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>

.wrapper >>> .el-collapse-item__header{
    padding-left: 0;
}
.groups >>> .el-form-item{
    margin-bottom: 0;
}

.groups >>> .el-collapse-item__wrap{
    padding: 10px 0;
    padding-right: 20px;
}



</style>

<style lang="scss" scoped>
.wrapper {
    //防止下拉打开时页面出现x轴滚动条
    padding-right: 10px;
    min-height: 500px;


}


.group-title-wrapper-list{
    >div{
        margin: 4px 0;
    }
}
.group-title-wrapper{
    flex: 1;
    display: flex;
    align-items: center;
    .group-title{
        flex: 1;
    }
    .item-btns{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        svg {
            cursor: pointer;
            font-size: 20px;
            vertical-align: middle;
            margin-left: 4px;
        }
    }
}


</style>
