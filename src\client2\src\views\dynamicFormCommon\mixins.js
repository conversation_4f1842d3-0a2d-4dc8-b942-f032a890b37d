
export default {
    data() {
        return {
            selectorShowStyle: [
                {value: '1', label: 'column1 + column2 + column3 + ...'}
            ],
            controls: [
                //type 为控件名称，同一级下唯一；同一控件attrs属性集合中，attrName必须唯一；
                // type 为 0已被定义，表示表单默认会添加一列 id 属性（{type: 0, level: 0, attrs: [{type: 3, attrName: 'name', value: 'id'}]）
                {type: 1, name: 'Container', attrs: [
                    
                ]},
                {type: 2, name: 'Form', attrs: [
                    {type: 3, label: '名称', attrName: 'name', value: '', desc: '表单名称。'},
                    {type: 3, label: 'ID', attrName: 'id', value: '', desc: '表单ID。'},
                    {type: 3, label: '请求地址', attrName: 'action', value: '', desc: '请求地址。'},
                    {type: 6, label: '默认是否显示', attrName: 'isVisible', value: 'true', desc: '当前表单是否显示，如果为弹框表单，注意设为false', options: [
                        {value: 'true', label: '是'},
                        {value: 'false', label: '否'},
                    ]},
                    {type: 6, label: '是否为弹框表单？', attrName: 'isDialogForm', value: 'false', desc: '是否为弹框表单，默认为false，如果为弹框表单，默认会隐藏起来', options: [
                        {value: 'true', label: '是'},
                        {value: 'false', label: '否'},
                    ]},
                    {type: 6, label: '是否需要表单验证？', attrName: 'isRequiredValidation', value: 'false', desc: '如果需要表单验证，则对当前表单下所有配置验证规则的元素进行验证', options: [
                        {value: 'true', label: '是'},
                        {value: 'false', label: '否'},
                    ]},
                    {type: 5, label: '请求方式', attrName: 'method', value: 'post', desc: '请求方式。', options: [
                        {value: 'post', label: 'post'},
                        {value: 'get', label: 'get'}
                    ]},
                ]},
                {type: 3, name: 'Input', attrs: [
                    //attrName 为控件属性名称，同一控件该值唯一
                    {type: 5, label: '类型', attrName: 'type', value: 'text', desc: 'input输入框类型。', options: [
                        {value: 'text', label: '文本'},
                        {value: 'number', label: '数字'},
                        {value: 'email', label: '电子邮件'},
                        {value: 'phone', label: '电话'},
                        {value: 'date', label: '日期'},
                        {value: 'password', label: '密码'},
                    ]},
                    {type: 3, label: '默认值', attrName: 'defaultValue', value: '', desc: '字段的默认值'},
                ]},
                // {type: 4, name: 'Text Area', attrs: [
                //     // InputNumber
                //     {type: 9, label: '跨行数', attrName: 'rows', value: 3, desc: '文本框跨行数'}
                // ]},
                {type: 5, name: 'Select', attrs: [
                    {type: 5, label: '数据来源', attrName: 'dataSource', value: '', desc: '配置数据来源关联字符', options: []},
                    {type: 8, label: '是否支持多选', attrName: 'isMultiple', value: [], desc: '配置数据来源关联字符', options: [
                        {value: 'true', label: '支持'}
                    ]},
                    {type: 3, label: '默认值', attrName: 'defaultValue', value: '', desc: '字段的默认值'},
                ]},
                {type: 6, name: 'Radio', attrs: [
                    {type: 5, label: '数据来源', attrName: 'dataSource', value: '', desc: '配置数据来源关联字符', options: []},
                ]},
                {type: 7, name: 'Button', attrs: [
                    {type: 5, label: '类型', attrName: 'type', value: 'submit', desc: '按钮类型'
                    // , options: [
                    //     {value: 'submit', label: '提交按钮'},
                    //     {value: 'button', label: '普通按钮'},
                    // ]
                    },
                    {type: 4, label: '点击事件执行对应代码', attrName: 'code', value: '', desc: '按钮点击事件执行的代码'}
                ]},
                {type: 8, name: 'Checkbox', attrs: [
                    {type: 5, label: '数据来源', attrName: 'dataSource', value: '', desc: '配置数据来源关联字符', options: []},
                    {type: 8, label: '是否支持多选', attrName: 'isMultiple', value: [], desc: '配置数据来源关联字符', options: [
                        {value: 'true', label: '支持'}
                    ]},
                ]},
                {type: 10, name: 'Table', attrs: [
                    {type: 3, label: '需要显示的列', attrName: 'showColumns', value: '', desc: '需要显示的列名称，多字段以逗号分割'},
                    // {type: 3, label: '表格行中需要的按钮', attrName: 'tableRowBtns', value: '', desc: '表格行中按钮，格式：[{"id": "btnAdd", "label": "新增"}]'},
                    {type: 10003, label: '表格行内按钮', attrName: 'tableRowBtns222', value: [], desc: '配置表格行内按钮'}
                ]},
                //因为属性的value值在编辑表单和使用表单的时候都需要，使用表单编辑数据时，可以覆盖value值；但是有些复杂的控件不能被覆盖，该value可能是使用该控件时需要的配置。
                {type: 10002, name: 'Selector', attrs: [
                    {type: 10002, label: '配置数据来源', attrName: 'dataSourceOfSelector', value: [], desc: '配置自定义选择的数据来源以及显示规则', extendConf: {
                        url: '',
                        columns: [], //表单中所有的列
                        showColumns: [], //展示的列
                        checkedShowColumns: [],//选中展示的列
                        conditions: [], //查询条件列
                        showStyle: '1'
                    }}
                ]}
            ],
            exclude: ['ctrlWidth', 'label', 'isMultiple'], //非控件原生属性（如：input 的 原生属性有 id、name、type），用作他用
        }
    },
    mounted() {
        this.controls.forEach(c => {
            //容器不需要这些属性（如果需要某一个，单独到容器里面配置）
            if(c.type != 1 && c.type != 2) {
                c.attrs.unshift({type: 5, label: '是否为查询条件', attrName: 'isSearchCondition', value: 'true', desc: '是否显示在查询区域中', options: [
                    {value: 'true', label: '是'},
                    {value: 'false', label: '否'}
                ]})
                c.attrs.unshift({type: 5, label: '是否显示在首页table中', attrName: 'showInTableColumn', value: 'true', desc: '是否显示在table列中', options: [
                    {value: 'true', label: '是'},
                    {value: 'false', label: '否'}
                ]})
                c.attrs.unshift({type: 3, label: 'ID', attrName: 'id', value: '', desc: '控件ID'})
                c.attrs.unshift({type: 3, label: '名称', attrName: 'name', value: '', desc: '控件名称'})
                c.attrs.unshift({type: 3, label: 'Label', attrName: 'label', value: '', desc: '控件对应显示的文字'})
                
                // c.attrs.push({type: 6, label: '所占宽度', attrName: 'ctrlWidth', value: '24', desc: '控件所在页面宽度（x/24）', options: [
                //     {value: '6', label: '25%'},
                //     {value: '8', label: '33%'},
                //     {value: '12', label: '50%'},
                //     {value: '18', label: '75%'},
                //     {value: '24', label: '100%'},
                // ]})

                /**
                 * 控件限制自身的限制方式与限制条件集合：
                 * [{vshow: [otherCtrlName: 1]}]——标识当前控件在 控件otherCtrlName==1 时才显示
                 */
                c.attrs.push({
                    type: 10001,
                    label: '限制规则',
                    attrName: 'constraints',
                    value: [
                        {consName: 'vshow', consList: []}, //表示不受控制
                        {consName: 'vvalue', consList: []}, //值限制
                    ], 
                    desc: '控制自身限制条件，目前只提供是否显示'
                })
            }
        });
    },
    computed: {
        
    },
    methods: {
        //表单(树结构)结构属性对象，负责属性值对象转为字符串
        formStructDatasToString(datas) {
            let postDatas = JSON.parse(JSON.stringify(datas))
            //保存之前将复杂对象转为字符串
            //提交之前需要把前端定义好的复杂格式转为字符串
            postDatas.children.map(t => {
                if(t.attrs && t.attrs.length > 0) {
                    t.attrs = t.attrs.map(c => {
                        if(c.extendConf) {
                            c.extendConf = JSON.stringify(c.extendConf)
                        }
                        if(c.attrName == 'constraints' || c.attrName == 'isMultiple' || c.attrName == 'dataSourceOfSelector' || c.attrName == 'tableRowBtns222') {
                            c.value = JSON.stringify(c.value)
                        }
                        return c
                    })
                }

                if(t.children) {
                    t.children = this.formStructDatasToString(t).children
                }
                return t
            })
            return postDatas
        },
        //获取表单结构（扁平——服务端将树拆解为扁平数组）后，将属性中复杂对象字符串转换为对象
        fetchFormStructDatasToObject(datas) {
            let temp = JSON.parse(JSON.stringify(datas))
            //获取保存之前将字符串转为复杂对象
            //存入属性值都是字符串类型，但是某些属性必须强制转成对象才好使用
            temp.formContent.map(t => {
                if(t.type != 0 && t.type != 1 && t.type != 2) {
                    if(t.attrs && t.attrs.length > 0) {
                        t.attrs = t.attrs.map(c => {
                            if(c.attrName == 'constraints' || c.attrName == 'isMultiple' || c.attrName == 'dataSourceOfSelector' || c.attrName == 'tableRowBtns222') {
                                c.value = JSON.parse(c.value)
                            }

                            if(c.extendConf) {
                                c.extendConf = JSON.parse(c.extendConf)
                            }else{
                                delete c.extendConf
                            }
                            return c
                        })
                    }
                }
                return t
            });
            return temp
        },
        //将控件属性转成表单需要的数据（数据库至保存必要的数据，所以需要和前端静态数据混合）
        //将表单控件集合数据结合前端定义的静态数据格式，混合成解析表单需要的数据（list）
        mergeCtrlData(list) {
            let datas = JSON.parse(JSON.stringify(list))
            datas.map(item => {
                let currentType = item.type
                if(currentType) {
                    if(item.attrs) {
                        item.attrs = item.attrs.map(s => {
                            let attrsOfType = this.controls.find(t => t.type == currentType).attrs || []
                            let currentAttr = JSON.parse(JSON.stringify(attrsOfType.find(a => a.attrName == s.attrName)))
                            if(currentAttr && s.value) {
                                currentAttr.value = s.value
                            }
                            // let extendConfTemp = JSON.parse(JSON.stringify(s.extendConf))
                            let result = Object.assign({}, s, currentAttr)
                            if(s.extendConf) {
                                result.extendConf = s.extendConf
                            }
                            return result
                        })
                    }
                }
            })
            return datas
        },
        getCtrlName(ctrlType) {
            if(ctrlType == 1) { //目前不能添加 Container，所以在mixin定义中删除，但是需要获取该控件名称
                return 'Container'
            }

            let currType = this.controls.find(c => c.type == ctrlType)
            if(currType) {
                return currType.name
            }
            return '未知控件'
        },
        newGuid(){  
            var guid = "";  
            for (var i = 1; i <= 32; i++){
                var n = Math.floor(Math.random()*16.0).toString(16);  
                guid += n;
                if((i==8)||(i==12)||(i==16)||(i==20))
                guid += "-";
            }
            return guid
        },
        getAttrs(ctrl) {
            let ctrlObj = JSON.parse(JSON.stringify(ctrl))
            if(ctrlObj && ctrlObj.attrs) {
                return ctrlObj.attrs.filter(s => this.exclude.findIndex(a => a == s.attrName) == -1).map(a => {
                    let attrName = a.attrName
                    if(attrName == 'defaultValue') {
                        attrName = 'value'
                    }
                    let attrValue = a.value
                    let attrObj = {}
                    attrObj[attrName] = attrValue

                    return attrObj

                })
            }
            return []
        }
    },
};
