<!--客服管理-->
<template>
<!--组件内容区-->
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="客服人员管理" :subTitle="['客服人员的管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <el-button v-if="isOperat" type="primary" style="width: 180px;margin-top: 10px;margin-left:35px;" @click="addCategory">添加职位分类</el-button>
                <el-input class="elInput" style="margin:10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label">{{ node.label }}</span>
                            <span v-if="data.Id" class="node-btn-area">
                                <el-dropdown v-if="isOperat" trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="update">修改名称</el-dropdown-item>
                                        <el-dropdown-item command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabData" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="tableLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :isShowConditionArea='true' :startOfTable="startOfTable" :layoutMode='layoutMode'>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="handleResetSearch" :layoutMode='layoutMode'>
                                <template slot="Name">
                                    <el-input style="width: 100%;" 
                                        placeholder="输入职位名称"
                                        @clear='loadTableData'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                listQuery.PageIndex = 1
                                                loadTableData()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Name"
                                    ></el-input>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <div class="btns-wrapper">
                                        <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                                        <!-- <el-button v-if="btnAdd=='btnAdd'" type="primary" class="elButton" @click="handleAdd()">添加人员</el-button> -->

                                        <el-button type="primary" class="elButton" @click="handleCreate('create')">创建职位</el-button>
                                        <el-button type="primary" class="elButton" @click="handleEdit" v-if="isAdjustment">调整分类</el-button>
                                    </div>
                                </template>
                            </app-table-form>
                        </template>

                        

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleCreate('update', scope.row)" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" text="移除" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    <!--弹窗组件区-->
    <category-edit v-if="categoryEditDialogFormVisible" :dialogStatus="categoryEditDialogStatus" :node="paramNode" :dialogFormVisible="categoryEditDialogFormVisible" @closeDialog="categoryEditCloseDialog" @saveSuccess="categoryEditSaveSuccess"></category-edit>
    <edit-page v-if="editDialogFormVisible" :dialogStatus="editDialogStatus" :jobIds="jobIds" :dialogFormVisible="editDialogFormVisible" @closeDialog="editCloseDialog" @saveSuccess="editSaveSuccess"></edit-page>

    <create-page v-if="createDialogFormVisible" :id="id" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogFormVisible" @closeDialog="createCloseDialog" @saveSuccess="createSaveSuccess"></create-page>

    <!-- <el-dialog width="1000px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="'添加员工'" :visible.sync="dialogAccessUsers" :append-to-body="true">
        <emp-table ref="accessUser" v-bind="$attrs" :existsUsers="pers" :condition="{SourceType:2}" v-if="dialogAccessUsers" v-show="dialogAccessUsers" @changed="handleChangeUsers"></emp-table>
    </el-dialog> -->
</div>
</template>

<!--组件脚本区-->

<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as systemJob from '@/api/personnelManagement/systemJob'
import elDragDialog from "@/directive/el-dragDialog";
// import EmpTable from "@/views/common/empTable";
import empSelector from '@/views/common/empSelector'
import categoryEdit from "./categoryEdit";
import editPage from "./edit";
import createPage from "./create";
import {
    listToTreeSelect
} from "@/utils";

export default {
    /**名称 */
    name: "systemJob-setting",
    mixins: [indexPageMixin],
    directives: {
        elDragDialog
    },
    /**组件声明 */
    components: {
        categoryEdit,
        createPage,
        editPage,
        // EmpTable,
        empSelector,
    },
    /**参数区 */
    props: {
        /**主键Id */
        keyId: {
            type: String
        }
    },
    /**数据区 */
    data() {
        return {
            layoutMode: 'simple',
            /******************* 树 *******************/
            tableSearchItems: [{
                    prop: "Name",
                    label: "", //职位名称
                    mainCondition: true
                },
            ],
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
            /******************* 表格 *******************/
            /**查询条件 */
            listQuery: {
                PageIndex: 1,
                PageSize: 20,
                CategoryId: null,
                Name: '',
            },
            /**表格列声明 */
            tabColumns: [{
                    attr: {
                        prop: "Name",
                        label: "职位名称"
                    }
                },
                {
                    attr: {
                        prop: "CategoryName",
                        label: "职位分类",
                    }
                },
                {
                    attr: {
                        prop: "Description",
                        label: "职位描述",
                        showOverflowTooltip: true
                    }
                },
                {
                    attr: {
                        prop: "RelatedNumber",
                        label: "关联人数",
                    }
                },
            ] /**表格加载 */ ,
            tableLoading: false,
            /**表格数据 */
            tabData: [],
            /**表格选中行 */
            multipleSelection: [],
            /**分页数量 */
            total: 0,
            /******************* 组件 *******************/
            /**树节点添加弹窗 */
            categoryEditDialogFormVisible: false,
            categoryEditDialogStatus: "create",

            /** 创建职位弹框 */
            createDialogFormVisible: false,
            createDialogStatus: "create",
            id: '',
            
            /**人员调整弹窗 */
            editDialogFormVisible: false,
            editDialogStatus: "create",
            /**员工添加弹窗 */
            //dialogAccessUsers: false,
            /**选择客服 */
            jobIds: [],
            pers: []
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
        isOperat() {
            return this.topBtns.findIndex(s => s.DomId == 'btnCategoryMgmt') > -1
        },
        isAdjustment() {
            return this.topBtns.findIndex(s => s.DomId == 'btnAdjustment') > -1
        },
    },
    /**监听 */
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.CategoryId = val.Id;
                    this.listQuery.PageIndex = 1;
                    this.loadTableData();
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
    },
    /**渲染后 */
    mounted() {
        this.loadTreeData();
    },
    /**方法区 */
    methods: {
        /******************* 树事件 *******************/
        loadTreeData() {

            let _this = this;
            let paramData = {
                PageSize: 1000,
                PageIndex: 1
            };
            _this.treeLoading = true
            systemJob
                .getCategoryListPage(paramData)
                .then(response => {
                    _this.treeLoading = false
                    
                    let list = response.Items.map(s => {
                        return {
                            Id: s.CategoryId,
                            Name: s.CategoryName,
                            Level: 0,
                            ParentId: null
                        }
                    })
                    list.unshift({
                        Id: "",
                        Name: "全部",
                        Level: 0,
                        ParentId: null
                    });

                    _this.treeData = listToTreeSelect(list);

                    if (_this.treeData && _this.treeData.length > 0) {
                        if (
                            !(
                                _this.checkedNode &&
                                response.find(t => {
                                    return t.Id == _this.checkedNode.Id;
                                })
                            )
                        ) {
                            _this.checkedNode = _this.treeData[0];
                        }
                    } else {
                        _this.checkedNode = null;
                    }
                    if (_this.checkedNode) {
                        _this.$nextTick(() => {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        });
                    }
                }).catch(err => {
                    _this.treeLoading = false
                });
        },
        /**添加顶级节点 */
        addCategory() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.categoryEditDialogStatus = "create";
            this.categoryEditDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.categoryEditDialogStatus = "create";
                    this.categoryEditDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.categoryEditDialogStatus = "update";
                    this.categoryEditDialogFormVisible = true;
                    break;
                case "delete":
                    this.handleDeleteDepartment(data);
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteDepartment(data) {
            // if (data.children && data.children.length > 0) {
            //     this.$notify({
            //         title: "提示",
            //         message: "请先删除子级",
            //         type: "error",
            //         duration: 2000
            //     });
            //     return;
            // }
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                systemJob
                    .deleteCategory([data.Id])
                    .then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }
                        this.loadTreeData();
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
            });
        },
        /******************* 表格事件 *******************/
        /**加载表格数据 */
        loadTableData() {
            let _this = this;
            if (_this.checkedNode.Id) {
                _this.listQuery.CategoryId = _this.checkedNode.Id;
            } else {
                delete this.listQuery.CategoryId;
            }
            _this.tableLoading = true
            systemJob
                .getList(_this.listQuery)
                .then(response => {
                    _this.tableLoading = false
                    _this.total = response.Total;
                    _this.tabData = response.Items;
                }).catch(err => {
                    _this.tableLoading = false
                });
        },
        /**表头部点击 */
        // onBtnClicked: function (type) {
        //     switch (type) {
        //         //添加
        //         case "btnAdd":
        //             if (!this.checkedNode.Id) {
        //                 this.$message({
        //                     message: "该节点不可操作",
        //                     type: "error"
        //                 });
        //                 return;
        //             }
        //             this.dialogAccessUsers = true;
        //             break;
        //             //批量调整
        //         case "btnEdit":
        //             if (this.multipleSelection.length < 1) {
        //                 this.$message({
        //                     message: "至少选中一个",
        //                     type: "error"
        //                 });
        //                 return;
        //             }
        //             this.customerServiceManageEditDialogFormVisible = true;
        //             break;
        //         default:
        //             break;
        //     }
        // },
        /**表格行选中 */
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
            this.jobIds = rows.map(t => t.JobId) || [];
        },
        
        handleEdit() {
            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少选中一个",
                    type: "error"
                });
                return;
            }
            this.editDialogFormVisible = true;
        },
        
        /**表格行删除 */
        handleDelete(rows) {
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.JobId);
            } else {
                ids.push(rows.JobId);
            }

            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                systemJob.del(ids).then(res => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.loadTableData();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.loadTableData();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.loadTableData();
        },
        /******************* 弹窗 *******************/
        /**人员变更 */
        
        /**地区弹窗保存成功 */
        categoryEditSaveSuccess() {
            this.loadTreeData();
            this.categoryEditCloseDialog();
        },
        /**地区弹窗关闭 */
        categoryEditCloseDialog() {
            this.categoryEditDialogFormVisible = false;
        },
        /**编辑弹窗保存成功 */
        editSaveSuccess() {
            this.listQuery.PageIndex = 1;
            this.loadTableData();
            this.editCloseDialog();
        },

        handleCreate(dialogType = 'create', row) {
            this.createDialogStatus = dialogType
            if(row) {
                this.id = row.JobId
            }
            this.createDialogFormVisible = true;
        },
        createCloseDialog() {
            this.createDialogFormVisible = false
        },
        createSaveSuccess() {
            this.loadTableData();
            this.createCloseDialog();
        },
        
        /**编辑弹窗关闭 */
        editCloseDialog() {
            this.editDialogFormVisible = false;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.loadTableData();
        },
        handleResetSearch() {
            this.listQuery.PageIndex = this.listQuery.PageIndex;
            this.listQuery.PageSize = this.listQuery.PageSize;
            this.listQuery.Name = "";
            this.loadTableData() //刷新列表
        },
    }
};
</script>

<!--组件样式区-->

<style lang="scss" scoped>
// @import "../../../../styles/empSelectorDialogCommon.css";
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;
        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;


        .content {

            // padding: 10px;
            // padding-top: 0;

            // padding-right: 10;
            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }

            .btns-wrapper{
                button{
                    margin-right: 4px;
                }
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: calc(100% - 24px);
        position: relative;
        box-sizing: border-box;
        padding-right: 30px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
</style>
