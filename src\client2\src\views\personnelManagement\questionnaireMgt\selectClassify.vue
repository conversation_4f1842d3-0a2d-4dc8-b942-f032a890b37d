<!--请选择问卷类型-->
<template>
<app-dialog title="请选择问卷类型" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="340">
    <template slot="body">
        <div class="pageWarp">
            <el-form :rules="formRules" :model="formModel" ref="formData" label-position="right" label-width="120px">
                <el-form-item label-width="0" prop="Name">
                    <el-select v-model="formModel.SurveyType" placeholder="请选择" style="width:100%;">
                        <el-option v-for="item in SurveyTypeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
    </template>
    <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" @click="handleButtonClick"></app-button>
    </template>
</app-dialog>
</template>

<script>
import { SurveyTypeEnum } from "../enum.js";
export default {
    name: "questionnaireMgt-select-classify",
    components: {},
    props: {},
    data() {
        return {
            SurveyTypeEnum,
            formRules: {
                SurveyType: {fieldName: "问卷类型",rules: [{ required: true }]},
            },
            formModel: {
                SurveyType: 1,
            },
        };
    },
    computed: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                this.resetFormModel();
            },
            immediate: true
        }
    },
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    mounted() {},
    methods: {
        /**清理表单 */
        resetFormModel() {
            this.formModel = this.$options.data().formModel
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.appDialogRef.createData(self.formModel);
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.pageWarp{
    padding: 10px 10px 0 10px;
}
</style>


