<!-- 金蝶数据的公司选择 -->
<template>
  <el-select
    v-model="value_"
    value-key="KingdeeId"
    placeholder="请选择公司名称"
    :loading="companyLoading"
    style="width: 100%"
    :disabled="disabled"
    @change="handleChange"
  >
    <el-option
      v-for="item in companyList"
      :key="item.KingdeeId"
      :label="item.KingdeeName"
      :value="item.KingdeeId"
    />
  </el-select>
</template>

<script>
import { getKingdeeDepartmentApi } from "@/api/kingdee";

export default {
  props: {
    value: {
      type: [String,null],
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      value_: null,
      companyList: [],
      companyLoading: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.value_ = val;
      },
      immediate: true,
    },
  },
  created() {
    this.getCompanyList();
  },
  methods: {
    // 获取公司列表
    getCompanyList() {
      this.companyLoading = true;
      getKingdeeDepartmentApi()
        .then(res => {
          this.companyList = res;
        })
        .finally(() => {
          this.companyLoading = false;
        });
    },
    handleChange(val) {
      this.$emit("change", val);
      this.$emit("input", val);
    },
  },
};
</script>
