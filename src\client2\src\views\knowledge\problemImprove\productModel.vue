<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <div class="wrapperBox">
                    <div class="opl">
                        <div style="padding: 10px 10px 7px 0;">
                            <el-input style="width: 100%;" placeholder="搜索产品名称" clearable v-model.trim="productFilterText"></el-input>
                        </div>
                        <page-title title="产品列表">
                            <el-button type="text" slot="oRight" v-if="editable" @click="handleProduct(1, 'create')">添加产品</el-button>
                        </page-title>
                        <div class="opl_treeBox" v-loading='productLoading'>
                            <el-tree class="elTree" ref="treeRef" :data="productData"
                                node-key="Id" :filter-node-method="filterNode" icon-class
                                :props="defaultProps" default-expand-all :expand-on-click-node="false"
                                :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                    <span class="node-title" :title="node.label">{{ node.label }}</span>
                                    <span class="node-btn-area" v-if="editable">
                                        <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                            <span class="el-dropdown-link">
                                                <i class="el-icon-more"></i>
                                            </span>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item command="update">编辑</el-dropdown-item>
                                                <el-dropdown-item command="delete">删除</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </span>
                                </span>
                            </el-tree>
                        </div>
                    </div>
                    <div class="opr __dynamicTabContentWrapper">
                        <div style="padding: 10px;">
                            <el-input style="width: 260px;margin-right:10px;" placeholder="搜索产品型号" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                            <el-button v-if="editable" :disabled="productData.length==0" type="primary" @click="handleModel(2, 'create')">添加产品型号</el-button>
                        </div>
                        <div class="__dynamicTabWrapper">
                            <el-table ref="productModelTable" :data="tabDatas" v-loading="tableLoading"
                            height="100%" :row-key="s=>s.Id"
                            @selection-change="rowSelectionChanged">
                                <el-table-column width="35" v-if="!editable">
                                    <template slot-scope="scope">
                                        <el-radio class="notLabel" :value="multipleSelectionId"
                                        :label="scope.row.Id" @change="multipleSelectionChange(scope.row)"></el-radio>
                                    </template>
                                </el-table-column>
                                <el-table-column type="index" :width="50" label="序号">
                                    <template slot-scope="scope">
                                        {{(listQuery.PageIndex - 1) * listQuery.PageSize + (scope.$index + 1)}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="Specification" label="产品型号" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="ProductImprovementName" label="所属产品" show-overflow-tooltip></el-table-column>
                                <el-table-column :width="80" label="操作" v-if="editable">
                                    <template slot-scope="scope">
                                        <app-table-row-button @click="handleModel(2, 'update', scope.row)" text="编辑" :type="2"></app-table-row-button>
                                        <!-- 删除 -->
                                        <app-table-row-button @click="handleDelete(2, scope.row.Id)" :type="3"></app-table-row-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <!----------------------------------------- 分页 ------------------------------------------->
                        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize"
                        @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                    </div>
                </div>
            </template>
            <template slot="footer">
                <!-- 关闭 -->
                <app-button v-if="dialogStatus == 'create'" @click="handleClose" text="关闭" type></app-button>
                <!-- 取消 -->
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="!editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>

        <!-- 新增/编辑  产品/产品型号 -->
        <product-model-create  v-if="createDialogVisible" :dialogFormVisible="createDialogVisible"
        :node="selectRow" :parentNode="selectParentRow" :dialogType="dialogTypes" :dialogStatus="createDialogStatus"
        @closeDialog="createDialogVisible=false" @saveSuccess="createDialogSaveSuccess" @reload="reloadList" />
    </div>
</template>

<script>
import productModelCreate from './productModelCreate'
// 产品管理
import * as productImprovementApi from '@/api/knowledge/productImprovement.js'
// 产品型号管理
import * as productSpecificationImprovementApi from '@/api/knowledge/productSpecificationImprovement.js'

export default {
    name: "product-model",
    directives: {},
    components: {
        productModelCreate
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return `产品型号管理`;
            }
            return "选择产品型号";
        },
    },
    filters: {
    },
    props: {
        //编辑还是新增(create:  管理;    detail： 选择 (默认))
        dialogStatus: {
            type: String,
            default: 'detail',
        },
        ids: {
            type: Array,
            default: ()=>[],
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    let self = this;
                    self.getProduct();// 查询 基本信息
                    if(self.ids.length>0){
                        self.multipleSelection = JSON.parse(JSON.stringify(self.ids))
                    }
                }
            },
            immediate: true
        },
        productFilterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode(val){
            if(val.Id){
                this.getList()
            }
        }
    },
    created() {
    },
    data() {
        return {
            disabledBtn: false,

            productFilterText: '',
            productLoading: false,
            productData: [],
            checkedNode: {},
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            
            total: 0,
            listQuery: {
                Keywords: "",
                PageIndex: 1,
                PageSize: 10,
            },
            multipleSelectionId: '',
            multipleSelection: [],
            tabDatas: [],
            tableLoading: false,

            createDialogVisible: false,
            createDialogStatus: 'create',
            selectParentRow: {},
            dialogTypes: 1,
            selectRow: {},
        };
    },
    methods: {
        multipleSelectionChange(row){
            this.multipleSelectionId = row.Id
            this.multipleSelection = [row]
        },
        // 操作 型号
        handleModel(type, optType, row = {}){
            this.dialogTypes = type;
            this.createDialogStatus = optType;
            this.selectParentRow = this.checkedNode;
            this.selectRow = row;
            this.createDialogVisible = true;
        },
        // 操作 产品
        handleProduct(type, optType, row = {}){
            this.dialogTypes = type;
            this.createDialogStatus = optType;
            this.selectParentRow = {};
            this.selectRow = row;
            this.createDialogVisible = true;
        },
        reloadList(){
            if(this.dialogTypes == 1) {
                this.getProduct();
            }
            if(this.dialogTypes == 2) {
                this.getList();
            }
            this.$emit('reload');
        },
        createDialogSaveSuccess(){
            this.createDialogVisible = false
            this.reloadList();
        },
        createData() {
            this.$refs.appDialogRef.createData(this.multipleSelection);
        },
        rowSelectionChanged(rows) {
          this.multipleSelection = rows;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "update":
                    // 编辑产品
                    this.handleProduct(1, 'update', data)
                    break;
                case "delete":
                    // 删除产品
                    this.handleDelete(1, data.Id)
                    break;
                default:
                    break;
            }
        },
        /**删除 */
        handleDelete(type, id) {
            let self = this;
            self.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let result = null;
                // 1  产品管理  2 产品型号管理
                if (type == 1) {
                    result = productImprovementApi.del([id]);
                } else if (type == 2) {
                    result = productSpecificationImprovementApi.del([id]);
                }
                result.then(res => {
                    self.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    if (type == 1) {
                        self.getProduct();
                    } else if (type == 2) {
                        self.getList();
                    }
                    self.$emit('reload');
                });
            });
        },
        // 获取产品列表
        getProduct(){
            let self = this;
            self.productLoading = true
            productImprovementApi.getList({
                PageIndex: 1,
                PageSize: 99999,
            }).then(res => {
                self.productData = res.Items;
                self.checkedNode = self.productData.length>0?self.productData[0]:{};
                if(self.checkedNode) {
                    self.$nextTick(() => {
                        if(self.$refs.treeRef) {
                            self.$refs.treeRef.setCurrentKey(self.checkedNode.Id);
                        }
                    });
                }
                // self.getList();
                self.productLoading = false
            }).catch(err => {
                self.productLoading = false
            });
        },
        // 查询 列表信息
        getList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            self.tableLoading = true
            productSpecificationImprovementApi.getList({
                ...{ProductImprovementId: self.checkedNode.Id},
                ...postDatas
            }).then(res => {
                self.tableLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
                self.$nextTick(()=>{
                    let checkedArr = self.tabDatas.filter(s=> self.multipleSelection.some(q=>q.Id == s.Id)) || [];
                    self.multipleSelectionId = "";
                    checkedArr.forEach(row => {
                        self.$refs.productModelTable.toggleRowSelection(row);
                        self.multipleSelectionId = row.Id;
                    });
                })
            }).catch(err => {
                self.tableLoading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        // 重置搜索
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        // 表格文本框果过滤
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    }
};
</script>

<style scoped>
.notLabel >>> .el-radio__label{
    display: none;
}
</style>
<style lang='scss' scoped>
.notLabel{
    vertical-align: middle;
}
.wrapperBox{
    display: flex;
    width: 100%;
    height: 546px;
    &_main{
        width: 100%;
        display: flex;
        height: 100%;
        flex-direction: column;
    }
    .opl{
        width: 209px;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #dcdfe6;
        &_treeBox{
            flex: 1;
            width: 100%;
            overflow: hidden;
            .elTree {
                width: 100%;
                padding-right: 10px;
                height: 100%;
                overflow: hidden;
                overflow-y: auto;
            }
        }
    }
    .opr{
        flex: 1;
        width: calc(100% - 210px);
        height: 100%;
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 15px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;
    margin-left: -10px;
    margin-right: -5px;
    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

</style>