<!--组件名称-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog title="查看未分享人员" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1000">
      <template slot="body">

        <div style="margin-top:15px;margin-left:10px;">

             <span style="font-weight:bold;">未分享的负责人</span>

        </div>

       

        <ul class="employee">
         <li v-for="item in List" :key="item.EmployeeId">

            <div>
                <img class="icon" :src = item.AvatarPath>
                <p>{{ item.Name }}</p>
                
            </div>

           </li>
         </ul>
       
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <!-- <app-button v-show="editable" :buttonType="1" :disabled="buttonLoading" @click="handleButtonClick"></app-button> -->
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as shareApi from "@/api/operatingDataCenter";

export default {
  /**名称 */
  name: "share",
  /**组件声明 */
  /**参数区 */
  props: {
    //弹窗类型
    dialogStatus: {
      type: String,
      default: "create",
    },

  },
  /**数据区 */
  data() {
    return {
      loading: false,
      buttonLoading: false,
      List: [],
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },

  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if(val) {
            this.getList()
        }
      },
      immediate: true,
    },
  },
  /**渲染前 */
  created() {
    let _this = this;
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    getList() {
        this.loading = true
        shareApi.getUnsharedPeople().then(res => {
            this.loading = false
            this.List = res
        }).catch(err => {
            this.loading = false
        })
    },
    /**关闭 */
    handleClose() {
      let _this = this;
      _this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.wrapper {
    min-height: 300px;
    .title-wrapper{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .title{
            font-weight: 600;
        }
    }

}

.icon{
    border-radius: 30px;
    width: 40px;
    height: 40px;
}

.employee{
    height: 172px;
    overflow-y: auto;
}

.employee li{
     float: left;
     margin:15px;
}

.employee p{
    margin-top: 5px;
} 

</style>