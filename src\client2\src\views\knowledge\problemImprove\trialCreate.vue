<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" style="padding-top: 0;" v-loading='loading||solutionDataLoading'
                label-position="right" label-width="110px" class="wrapperMain">
                    <el-row class="wrapperBox">
                        <el-col :span="24">
                            <el-form-item label="解决方案" prop="CaseName">{{formData.CaseName}}</el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="试验人" prop="TestEmployeeList">
                                <emp-selector
                                    class="fl"
                                    style="width:100%;"
                                    :readonly="!editable"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :beforeConfirm='handleBeforeConfirm'
                                    :list="formData.TestEmployeeList"
                                    @change="handleViewRange"
                                ></emp-selector>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="试验时间" prop="TestTime">
                                <el-date-picker v-model="formData.TestTime" type="date" align="right" format="yyyy-MM-dd" style="width:100%;"
                                value-format="yyyy-MM-dd" :disabled="!editable"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="试验过程描述" prop="Describe">
                                <el-input :disabled="!editable" type="textarea" :rows="4" :maxlength="1000" v-model="formData.Describe"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="使用材料" prop="UsedMaterial">
                                <el-input :disabled="!editable" type="textarea" :rows="4" :maxlength="1000" v-model="formData.UsedMaterial"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="效果评定" prop="EffectEvaluationType">
                                <el-select style="width:100%;" :disabled='!editable' clearable
                                v-model="formData.EffectEvaluationType" placeholder="请选择">
                                    <el-option
                                        v-for="item in EffectEvaluationTypeEnum"
                                        :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="采纳建议" prop="AdoptSuggestionType">
                                <el-select style="width:100%;" :disabled='!editable' clearable
                                v-model="formData.AdoptSuggestionType" placeholder="请选择">
                                    <el-option
                                        v-for="item in AdoptSuggestionTypeEnum"
                                        :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="验证结果" prop="VerificationResult">
                                <el-input :disabled="!editable" type="textarea" :rows="4" :maxlength="1000" v-model="formData.VerificationResult"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="相关附件">
                                <template v-if="editable">
                                    <app-uploader accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList"
                                    :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                </template>
                                <template v-else>
                                    <app-uploader v-if="formData.AttachmentList.length>0" accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" readonly
                                    :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                    <template v-else>无</template>
                                </template>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 关闭 -->
                <app-button v-if="dialogStatus == 'detail'" @click="handleClose" text="关闭" type></app-button>
                <!-- 取消 -->
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import { EffectEvaluationTypeEnum, AdoptSuggestionTypeEnum } from "./enum.js";

import * as productQuestionSolutionImprovementApi from '@/api/knowledge/productQuestionSolutionImprovement.js'
import * as productQuestionSolutionTestResultApi from '@/api/knowledge/productQuestionSolutionTestResult.js'


import empSelector from "@/views/common/empSelector";
export default {
    name: "problem-improve-trial-create",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return `添加试验结果`;
            }else if(this.dialogStatus == 'update'){
                return `编辑试验结果`;
            }
            return "试验结果详情";
        },
    },
    filters: {
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        id: { //  实验结果id
            type: String,
            default: "",
        },
        solutionId: { // 解决方案 id
            type: String,
            default: "",
        },
        questionId: { // 问题 id
            type: String,
            require: true,
            default: "",
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.formData = this.$options.data().formData;
                    this.formData.TestTime = dayjs().format('YYYY-MM-DD');
                    this.formData.ProductQuestionImprovementId = this.questionId;
                    this.getSolutionData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            EffectEvaluationTypeEnum, // 效果评定 1 效果显著 2 效果一般 3 效果甚微 4 毫无效果
            AdoptSuggestionTypeEnum, // 采纳建议 1 已采纳  2 未采纳

            disabledBtn: false,
            loading: false,
            rules: {
                TestEmployeeList: {fieldName: "试验人",rules: [{ required: true }]},
                TestTime: {fieldName: "试验时间",rules: [{ required: true }]},
                Describe: {fieldName: "试验过程描述",rules: [{ required: true }]},
                EffectEvaluationType: {fieldName: "效果评定",rules: [{ required: true }]},
                AdoptSuggestionType: {fieldName: "采纳建议",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                // Id: '',
                ProductQuestionImprovementId: '', // 问题id
                CaseName: '',// 解决方案名称
                ProductQuestionSolutionImprovementId: '',// 解决方案id

                TestEmployees: '',// 试验人 名称
                TestEmployeeIdList: [],// 试验人 ID 集合
                TestEmployeeList: [],// 试验人 集合
                TestTime: '', // 实验时间
                Describe: '', // 试验过程描述
                UsedMaterial: '', // 使用材料
                EffectEvaluationType: null, // 效果评定 1 效果显著 2 效果一般 3 效果甚微 4 毫无效果
                AdoptSuggestionType: null, // 采纳建议 1 已采纳  2 未采纳
                VerificationResult: '', // 验证结果
                
                AttachmentList: [],// 相关附件
            },

            /** 解决方案 详情 */
            solutionDataLoading: false,
        };
    },
    methods: {
        // 获取解决方案详情
        getSolutionData(){
            this.solutionDataLoading = true;
            productQuestionSolutionImprovementApi.detail({ id: this.solutionId }).then(res => {
                this.formData.CaseName = res.CaseName;
                this.formData.ProductQuestionSolutionImprovementId = res.Id;
                this.solutionDataLoading = false;
            }).catch(err => {
                self.solutionDataLoading = false
            });
        },
        // 附件 上传 赋值
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 10) {
                this.$message({
                    message: '不得超过10个',
                    type: 'error'
                })
                return false
            }
            return true
        },
        // 发起人 选择确定
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.TestEmployeeList = users;
            } else {
                this.formData.TestEmployeeList = [];
            }
            this.$refs["formData"].validateField("TestEmployeeList");
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    postData.TestEmployeeIdList = postData.TestEmployeeList.map(s=>s.EmployeeId)
                    delete postData.TestEmployeeList
                    postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    delete postData.AttachmentList
                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = productQuestionSolutionTestResultApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = productQuestionSolutionTestResultApi.edit(postData);
                    }

                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 查询 基本信息
        getDetail() {
            this.loading = true
            productQuestionSolutionTestResultApi.detail({ id: this.id }).then(res => {
                this.formData = {...this.formData, ...res};
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
</style>
<style lang='scss' scoped>
.wrapperBox{
    padding-top: 10px;
    padding-right: 20px;
    &_main{
        max-height: 420px;
        overflow-y: auto;
    }
}
.el-card{
    margin-bottom: 15px;
}
.omit{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tips_title{
    float: left;
    max-width: 100%;
}
</style>