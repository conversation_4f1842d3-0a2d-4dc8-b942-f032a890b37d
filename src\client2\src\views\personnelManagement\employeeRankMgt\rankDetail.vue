<template>
    <div>
        <app-dialog title="详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <div class="wrapperMain" v-loading='loading'>
                    <div class="opl">
                        <el-form
                            ref="formData"
                            label-position="right"
                            label-width="100px"
                        >
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="头像" style="margin-bottom:0;">
                                        <div style="line-height: 40px;" v-viewer>
                                            <img class="userPhoto" :src="formData.AvatarPath||defAvatar" />
                                        </div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="当前等级" style="margin-bottom:0;">
                                        <!-- <user-leve-icon :number="formData.OldEmployeeLevelType" /> -->
                                        T{{ formData.OldEmployeeLevelType }}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="姓名">{{formData.Name}}</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="工号">{{formData.Number}}</el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="部门">{{formData.DepartmentName}}</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="职位">{{formData.JobName}}</el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="入职时间">{{formData.EntryTime | dateFilter('YYYY-MM-DD')}}</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="司龄">{{getYear(formData.EntryTime)}}</el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <div class="opr">
                        <page-title title="历史记录"></page-title>
                        <div class="recordWarp_main" v-loading="recordLoading">
                            <no-data v-if="formData.RecordData.length===0"></no-data>
                            <div class="recordWarp_main--itemWarp" v-for="(recordItem,index) in formData.RecordData" :key="index">
                                <div class="recordWarp_main--itemWarp_row" v-if="recordItem.ApprovalStatus===0">
                                    {{recordItem.CreateTime | dateFilter('YYYY-MM-DD')}} 等级调整为 
                                    <span class="colorBlue">{{leveEnum.find(s=>s.value===recordItem.NewEmployeeLevelType).label}}</span>
                                </div>
                                <template v-else>
                                    <div class="recordWarp_main--itemWarp_row">
                                        2020-05-04 申请职级 
                                        <span class="colorBlue">{{leveEnum.find(s=>s.value===recordItem.OldEmployeeLevelType).label}}</span>
                                        调整为 
                                        <span class="colorBlue">{{leveEnum.find(s=>s.value===recordItem.NewEmployeeLevelType).label}}</span>
                                    </div>
                                    <div class="recordWarp_main--itemWarp_row flexWarp">
                                        <div class="flexColumn">发起人：<span class="colorBlue">{{recordItem.CreateEmployeeName}}</span></div>
                                        <div>
                                            <span class="item-status" v-if="recordItem.ApprovalStatus"
                                                :style="{backgroundColor: getApprovalStatusObj(recordItem.ApprovalStatus).color,
                                                color: getApprovalStatusObj(recordItem.ApprovalStatus).bgColor}"
                                            >{{ getApprovalStatusObj(recordItem.ApprovalStatus).label }}</span>
                                            <template v-else>无</template>
                                        </div>
                                    </div>
                                    <div class="recordWarp_main--itemWarp_row">
                                        <el-button type="text" @click="handleDetail(recordItem)">查看详情</el-button>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <!-- 关闭 -->
                <app-button @click="handleClose" text="关闭" type></app-button>
            </template>
        </app-dialog>

        
        <!-- 调整申请 -->
        <adjust-application
            v-if="dialogAdjustApplicationVisible"
            @closeDialog="dialogAdjustApplicationVisible=false"
            :dialogFormVisible="dialogAdjustApplicationVisible"
            dialogStatus="detail"
            :id="selectRow.Id"></adjust-application>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import {leveEnum} from "@/components/UserLeveIcon/enum";
import NoData from "@/views/common/components/noData"
import UserLeveIcon from "@/components/UserLeveIcon"
import * as ApprovalVars from "@/views/projectDev/common/vars";

import adjustApplication from "./adjustApplication"

import * as employeeLevelHistoryApi from "@/api/personnelManagement/employeeLevelHistory";
export default {
    name: "employeeRankMgt-ankDetail",
    directives: {},
    components: {
        UserLeveIcon,
        NoData,
        adjustApplication
    },
    mixins: [],
    computed: {
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        employeeId: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    if (this.dialogStatus != "create") {
                        this.getDetail()
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        
    },
    created() {
    },
    data() {
        return {
            leveEnum,
            // 1 待审批 2 已审批 3 不通过 4 已撤销
            ApprovalStatusEnum: ApprovalVars.vars.approvalStatuObj.approvalStatus,
            defAvatar: require('../../../assets/images/avatar3.png'),
            loading: false,
            recordLoading: false,
            formData: {
                Id: "",
                OldEmployeeLevelType: 1, //员工职级
                AvatarPath: "",//头像
                Name: "", //姓名
                Number: "", //工号
                DepartmentName: "",// 部门
                JobName: "", // 职位
                EntryTime: "",// 入职时间
                RecordData: []
            },
            selectRow: {},
            dialogAdjustApplicationVisible: false,
        };
    },
    methods: {
        // 查看详情
        handleDetail(row){
            this.selectRow = row;
            this.dialogAdjustApplicationVisible = true;
        },
        // 获取员工信息详情
        getDetail() {
            this.loading = true
            employeeLevelHistoryApi.detail({
                EmployeeId: this.employeeId,
                Id: this.id
            }).then(res => {
                this.loading = false
                this.formData = {...this.formData, ...res};
                this.getRecord();
            }).catch(err => {
                this.loading = false
            })
        },
        // 查询当前员工的 等级更改记录
        getRecord(){
            this.recordLoading = true
            employeeLevelHistoryApi.getList({
                EmployeeId: this.employeeId,
                PageIndex: 1,
                PageSize: 999999,
            }).then(res => {
                this.formData.RecordData =  res.Items;
                this.recordLoading = false
            }).catch(err => {
                this.recordLoading = false
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        getYear(datetime) {//司龄
            let birthday = datetime
            if(dayjs(birthday).isValid()) {
                let currentDate = dayjs().format('YYYY-MM-DD')
                let yearOfBirth = dayjs(birthday).format('YYYY-MM-DD')
                let workDays = dayjs(currentDate).diff(dayjs(yearOfBirth),'day')     
                return Math.floor(workDays / 365) + ' 年'
            }else{            
                return '无'
            }
        },
        getApprovalStatusObj(val) {
            return this.ApprovalStatusEnum.find(
                s => s.value == val
            ) || {};
        },
    }
};
</script>

<style lang='scss' scoped>
.colorBlue{
    color: #409EFF;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
.wrapperMain{
    display: flex;
    .opl{
        flex: 1;
        
        .userPhoto{
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
            float: left;
            cursor: pointer;
        }
    }
    .opr{
        width: 299px;
        display: flex;
        flex-direction: column;
        border-left: 1px solid #dcdfe6;
        .recordWarp_main{
            flex: 1;
            font-size: 12px;
            padding: 5px 20px 5px 10px;
            max-height: 400px;
            overflow: hidden;
            overflow-y: auto;
            &--itemWarp{
                padding-bottom: 5px;
                &_row{
                    line-height: 28px;
                }
            }
        }
    }
}
</style>