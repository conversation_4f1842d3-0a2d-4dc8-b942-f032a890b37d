 //覆盖一些element-ui样式

 //修复elementui table 表头错位
 body .el-table th.gutter{
  display: table-cell!important;
}

body .el-table colgroup.gutter{
  display: table-cell!important;
}

//表格组件使用自定义按钮（app-table-row-button）时，样式调整；
body .el-table{
  table{
    thead{
      a._cus_btn{
        font-size: 12px;
        vertical-align: baseline;
      }
    }
  }
}

//表格行内容多，垂直居中对齐（处理表格里面图标太多，所以需要垂直对齐的问题）
.table-cell-flex{
  .cell{
    display: flex!important;
    align-items: center!important;
  }
}


//修复elementui table 表头错位


////处理工作计划管理，详情，总览tab页面，表格最后一行没有 border-bottom 缺失的问题；
// .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{
//   border-bottom: none!important;
// }
// .el-table--border::after, .el-table--group::after, .el-table::before{
//   border-bottom: none!important;
// }

// .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
//   background: rgba($color: red, $alpha: .5); //#F9F9FB
// }


.el-breadcrumb__inner, .el-breadcrumb__inner a{
  font-weight: 400!important;
}

 .el-upload {
   input[type="file"] {
     display: none !important;
   }
 }

 .el-upload__input {
   display: none;
 }

 .cell {
   .el-tag {
     margin-right: 0px;
   }
 }

 .small-padding {
   .cell {
     padding-left: 5px;
     padding-right: 5px;
   }
 }

 .fixed-width{
  .el-button--mini{
    padding: 7px 10px;
    width: 60px;
  }
 }

 .status-col {
   .cell {
     padding: 0 10px;
     text-align: center;
     .el-tag {
       margin-right: 0px;
     }
   }
 }

 //暂时性解决dialog 问题 https://github.com/ElemeFE/element/issues/2461
 .el-dialog {
   transform: none;
   left: 0;
   position: relative;
   margin: 0 auto;
 }

 //文章页textarea修改样式
 .article-textarea {
   textarea {
     padding-right: 40px;
     resize: none;
     border: none;
     border-radius: 0px;
     border-bottom: 1px solid #bfcbd9;
   }
 }

 //element ui upload
 .upload-container {
   .el-upload {
     width: 100%;
     .el-upload-dragger {
       width: 100%;
       height: 200px;
     }
   }
 }

.el-dropw-menu-height-36{
  .el-dropdown-menu__item{
    .drop-item-wrapper{
      height: 36px;
    }
  }
}

//dropdown
.custom-el-dropdown-menu-400{
  max-height: 400px;
  overflow-x: hidden;
  overflow-y: auto;
  a{
    display: block
  }
}


.custom-el-dropdown-menu-width-290{
  width: 290px;
}



/* 覆盖上传控件样式 */
.el-upload--picture-card{
  line-height: 100px;
}
.el-upload--picture-card, .el-upload-list--picture-card .el-upload-list__item{
  width: 100px;
  height: 100px;
}

//修复 appTable 公共组件“显示隐藏列”按钮边框样式被冲突
.el-button-group>.el-dropdown>.el-button{
  border-left-color: #DCDFE6!important;
}
.el-button-group>.el-dropdown>.el-button:active{
  border-left-color: #3a8ee6!important;
}

.el-tabs--border-card>.el-tabs__header{
  background-color: #fafafa;
}

//table 表格中的 tooltip 样式
.el-tooltip__popper[x-placement^=top] .popper__arrow{
  border-top-color: rgba(48, 65, 86, .9)!important;
}
.el-tooltip__popper[x-placement^=top] .popper__arrow:after {
  border-top-color: rgba(48, 65, 86, .9)!important;
}

.el-tooltip__popper{
  max-width: 800px!important;
  line-height: 150%!important;
  background: rgba(48, 65, 86, .9)!important;
}


.vue-treeselect__value-container,
.vue-treeselect__single-value{
  color: #606266!important;
}

.vue-treeselect__menu{
  border-color: $border-color-light!important;
}


// el-form 中使用 vue-treeselect 会影响样式，所以需要单独设置
.vue-treeselect__multi-value, .vue-treeselect__multi-value-label > div{
  line-height: normal!important;
}


//为了和 el 表单组件统一高度
.treeselect-common {
	line-height: 30px;
	.vue-treeselect__placeholder {
		line-height: 30px;
	}
	.vue-treeselect__control {
		height: 30px;
	}
}

.vue-treeselect__control{
  border-radius: $border-radius-base!important;
  border-color: $border-color-light!important;
}

//单选在disabled模式下，不显示选中内容
.vue-treeselect--disabled .vue-treeselect__single-value{
  position: static!important;
}

// 多选树结构，模拟单选样式（由于数据结构是数组（多选），不方便调整）
// 该样式只是隐藏了复选框，模拟了单选效果；为了组件样式适配，需要设置组件树形：:append-to-body="false"
// 并且需要确保，单击可以选中，单击选中确保只选中一个；
._pretend-treeselect{
  .vue-treeselect__option--selected{
    background: #e3f2fd;
    font-weight: 600;
  }
  .vue-treeselect__checkbox-container{
    display: none;
  }
}


//el-collapse组件头部标题缩进
.el-collapse-item__header{
  padding-left:20px;
}
.el-collapse-item__content{
  padding-bottom: 0;
}


//修复table因为使用了属性 fixed="right" 而导致表格列中间出现行划线的bug
.el-table__fixed-right::before{
  height: 0;
}




.vue-treeselect__option--disabled .vue-treeselect__label-container{
  color: rgba($color: $text-primary, $alpha: 0.55)!important;
}

/* 覆盖ele默认控件禁用样式 */
.el-textarea.is-disabled .el-textarea__inner,
.el-input.is-disabled .el-input__inner,
.el-select .el-input.is-disabled .el-input__inner,
.el-input-number.is-disabled .el-input-number__decrease, 
.el-input-number.is-disabled .el-input-number__increase,
.el-input-group__append, .el-input-group__prepend,
.el-range-editor.is-disabled,
.el-range-editor.is-disabled input
// .el-range-editor.is-disabled .el-range__icon,
//.el-range-editor.is-disabled .el-range-separator
{
  background-color: #fafbfc!important;
  border-color: $border-color-light!important;
  color: $text-primary;
}

//修改 vue-treeselect 下拉禁用样式
.vue-treeselect--disabled .vue-treeselect__control{
  background-color: #fafbfc!important;
  border-color: $border-color-light!important;
}


// .vue-treeselect--disabled .vue-treeselect__single-value,
// .vue-treeselect--disabled .vue-treeselect__multi-value-item{
//   color: #C0C4CC!important;
// }

.vue-treeselect--disabled .vue-treeselect__single-value, 
.vue-treeselect__multi-value-item{
  color: $text-primary!important;
  background-color: #f4f4f5!important;
  border-color: #e9e9eb!important;
}

.vue-treeselect__value-remove{
  color: red;
}
.vue-treeselect__multi-value-item{

}


//el-date-picker 修复图标垂直居中
// .el-range-editor--small .el-range__icon, .el-range-editor--small .el-range__close-icon{
//   line-height: 22px!important;
// }

.is-error{
  .el-range-editor.is-active, 
  .el-range-editor.is-active:hover, 
  .el-select .el-input.is-focus .el-input__inner{
    border-color: #F56C6C;
  }
}




//弹框居中
.el-dialog{
  display: flex;
  flex-direction: column;
  margin:0 !important;
  position:absolute;
  top:50%;
  left:50%;
  transform:translate(-50%,-50%);
  /*height:600px;*/
  max-height:calc(100% - 30px);
  max-width:calc(100% - 30px);
}

//全屏弹框
.el-dialog.is-fullscreen{
  /*height:600px;*/
  max-height:calc(100%);
  max-width:calc(100%);
}


.el-dialog .el-dialog__body{
  flex:1;
  overflow: auto;
}



//dialog 不需要默认y轴滚动条，并且弹框高度还需要自适应（有些页面需要左右分别滚动）
.clear-default-height-auto .el-dialog__body{
  padding: 0;
  height: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  >div{
    flex: 1;
    overflow: hidden;
  }
}

//设置通用组件dialog高度为 95%（固定高度）
.dialog-auto-height .el-dialog{
  height: 95%;
}

// 设置组件的padding (容易导致前后图标垂直布局中,暂时取消)
.el-select__input{
  margin-left: 10px;
}
.el-input__inner, .el-textarea__inner{
  padding-left: 10px!important;
  padding-right: 10px!important;
}

.el-input--suffix {
  .el-input__inner, .el-textarea__inner{
    padding-right: 30px!important;
  }
}

.el-input--prefix{
  .el-input__inner, .el-textarea__inner{
    padding-left: 30px!important;
  }
}

//统一去掉 el-popover 组件的边框
.el-popover{
  border: none!important;
}




.vditor-wrapper{
  .vditor-reset{
    font-size: 13px;
    color: $text-primary;
    background: $bg-color-1;
    padding: 5px 10px!important;
    h1, h2, h3, h4, h5, h6{
      // margin-top: 16px;
      // margin-bottom: 12px;
      margin-top: 8px;
      margin-bottom: 4px;
    }
    p{
      margin-bottom: 3px;
    }
    hr{
      margin: 8px 0;
    }
    ul, ol{
      margin-bottom: 8px;
    }
    blockquote{
      margin: 0 0 8px 0;
      border-left: 2px solid $bg-color-4;
    }
    pre{
      code{
        line-height: 24px;
      }
    }
    // 标题锚点隐藏
    .vditor-anchor{
      display: none;
    }
  }
}

//调整组件 empSelect、select 标签删除按钮太靠边的问题
.el-tag{
  .el-tag__close{
    top: 1px!important;
    left: 4px!important;
  }

  .el-select__tags-text{
    line-height: normal!important;
  }
}

// textarea字数统计
 .el-input__count{
   line-height: normal;
 }

 // 隐藏时间选择器底部此刻按钮
 .date_hidden_cur_btn{
   .el-picker-panel__footer .el-button--text {
     display: none;
   }
 }
