<template>
    <div class="departTree">
        <app-dialog title="选择部门" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
             
        >
            <template slot="body">
                <div style="height:590px;overflow-y:auto;" v-loading="loading">
                    <el-tree 
                    class="tree" 
                    :data="orgsTree" 
                    :expand-on-click-node='false'
                    :check-strictly="true"
                    show-checkbox node-key="Id" 
                    :default-expanded-keys="defaultKeys"
                    :default-checked-keys="checkedList"
                    @check-change="handleCheckChange"
                    @check="handleChecked"
                    :props="defaultProps" 
                    ref="tree">
                    </el-tree>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1'></app-button>
            </template>
        </app-dialog>
    </div>
</template>
<script>
import {
    listToTreeSelect
} from '@/utils'
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'
export default{
    name:'departTree',
    components: {   
        
    },
    props:{
        // areaId:{
        //     type:String,
        //     default:''
        // },
        beforeConfirm: Function,
        queryRegionID:{
            type:String,
            default:''
        },
        defaultExpandLevel:{
            type: Number,
            default: 0
        },
        //默认选中
        checkedList: {
            type: Array,
            default: () => {
                return [];
            }
        },
    },
    data(){
        return{
            defaultKeys:[],
            orgsTree: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'label',
                disabled: this.disabledFn,
            },
            loading:false,
            datas:null,
        }
    },
    watch: {
        
    },
    created(){
        
    },
    mounted(){

        this.getOrgTree();
        
    },
    methods:{
        disabledFn(data, nodeType) {
            //禁选一级节点
            if(data.level <= 1) {
                return true
            }
            return false
        },
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        createData(){
            this.datas = this.$refs.tree.getCheckedNodes();
            this.$emit('saveSuccess',this.datas);
        },
        handleCheckChange(data, checked, node){
            // if(checked === true) {
            //     this.checkedId = data.Id;
            //     this.$refs.tree.setCheckedKeys([data.Id]);
            // }else {
            //     ////需要支持取消选中（如果需要关闭弹框前给与提示，请使用 areaChoose 组件的 beforeConfirm 属性 ）
            //     if (this.checkedId == data.Id) {
            //         this.$refs.tree.setCheckedKeys([data.Id]);
            //     }
            // }
            // this.$emit('checkChangeNod',datas',datas.length > ',datas[0] : null)

            if(data.disabled){
              this.uniteChildSame(data, false);
            }
        },
        // 统一处理子节点为相同的勾选状态
        uniteChildSame (treeList, isSelected) {
            this.$refs.tree.setChecked(treeList.Id, isSelected)
            if(treeList.children && treeList.children.length > 0){
                for (let i = 0; i < treeList.children.length; i++) {
                    this.uniteChildSame(treeList.children[i], isSelected)
                }
            }
        },
        // // 统一处理父节点为选中
        // selectedParent (currentObj) {
        //   let currentNode = this.$refs.tree.getNode(currentObj)
        //   if (currentNode.parent.key !== undefined) {
        //     this.$refs.tree.setChecked(currentNode.parent, true)
        //     this.selectedParent(currentNode.parent)
        //   }
        // }, 
        handleChecked(currentObj, treeStatus) {
          // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
          let selected = treeStatus.checkedKeys.indexOf(currentObj.Id) // -1未选中
          // 选中
          if (selected !== -1) {
            // 子节点只要被选中父节点就被选中
            // this.selectedParent(currentObj)
            // 统一处理子节点为相同的勾选状态
            this.uniteChildSame(currentObj, true)
          } else {
            // 未选中 处理子节点全部未选中
            if (currentObj.children && currentObj.children.length !== 0) {
              this.uniteChildSame(currentObj, false)
            }
          }
        },  
        getOrgTree() { //查询并加载部门树
            this.loading=true;
            systemDepartment.getListByCondition({}).then(response => { //调用公共组件API获取部门数据
                this.loading=false;
                let list = response.map(function (item) {
                    return {
                        Id: item.Id,
                        label: item.DepartmentName,
                        ParentId: item.ParentId
                    }
                })
                var orgstmp = JSON.parse(JSON.stringify(list));
                var tempOrgsTree = listToTreeSelect(orgstmp); //将部门数据转换成树形结构
                this.defaultKeys.push(tempOrgsTree[0]['Id']); //设置默认展开
                this.orgsTree = tempOrgsTree;
                this.$refs.tree.setCheckedKeys(this.checkedList || []);
                this.defaultKeys = this.defaultKeys.concat(this.checkedList || [])
            })
        },
    }

}
</script>
<style lang="scss" scoped>

</style>