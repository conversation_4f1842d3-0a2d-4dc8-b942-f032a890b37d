<template>
  <div>
    <app-dialog title="调整区域" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :Height='500' :width='600'>
      <template slot="body">
        <el-form
          ref="formData"
          label-position="right"
          label-width="180px">
          <el-form-item label="请选择调整后的负责区域：">

            <div class="_regional_detail_wrapper">
                <div class="btn_wrapper">
                    <el-button type="text" @click="handleDialog">选择</el-button>
                </div>
                <div class="regional_text" :title="regionalParendName">{{ regionalParendName }}</div>
                <div class="close_wrapper" v-show="regionalParendName">
                    <div class="i_wrapper">
                        <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                    </div>
                </div>
            </div>
          </el-form-item>  
        </el-form>

      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="createData" :disabled="!selectRegionalId" :buttonType='1'></app-button>
      </template>
    </app-dialog>

    <!-- 选择地区 -->
    <v-area-choose
      @closeDialog="closeDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogFormAreaVisible"
      :checkedList="selectRegionalId ? [selectRegionalId] : []"
    ></v-area-choose>
  </div>
</template>

<script>
import { listToTreeSelect } from '@/utils'
import vAreaChoose from "../../businessMap/common/areaChoose";
import * as implementerManagement from '@/api/maintenanceCenter/implementerManagement'

export default {
  name: "adjustRegional",
  directives: {},
  components: {
    vAreaChoose,
  },
  mixins: [],
  props: {
    node: {
      type: Array,
      required: true
    },
    regionalName: {
      type: String,
      default: ''
    },
  },
  watch: {
    '$attrs.dialogFormVisible'(val) {
      this.selectRegionalId = null
      this.regionalParendName = ''
    }
  },

  created() { },
  data() {

    return {
      selectRegionalId: null,
      regionalParendName: "",

      dialogFormAreaVisible: false,

    };
  },
  methods: {
    //地区选择
    closeDialog() {
      this.dialogFormAreaVisible = false;
    },
    handleDialog(){
        this.dialogFormAreaVisible=true;
    },
    electedRegionalData(data){
        // this.$refs.formData.clearValidate('areaName');
        if(data){
            this.selectRegionalId = data.Id;
            this.regionalParendName = data.ParentName;
        }else{
            this.selectRegionalId = null;
            this.regionalParendName = '';
        }
    },

    createData() {
      var param = {
        ids: this.node,
        regionalId: this.selectRegionalId
      };
      implementerManagement.adjustRegional(param).then(res => {
        this.$notify({
          title: "提示",
          message: "调整成功",
          type: "success",
          duration: 2000
        });
        this.$refs.appDialogRef.createData()
      })
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose()
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
