<template>
    <div class="avatar-warpper clearfix">
        <!-- {{ conf }} -->
        <el-input placeholder="" :value="names" :title="names" :disabled="readonly" :readonly='true' unselectable='on' class="input-with-select">
            <i slot="suffix" v-if="isShowClearButton" v-show="!readonly" @click="handleClear" class="el-input__icon el-icon-close icon-close"></i>
            <div slot='append'>
                <el-button :disabled='readonly' style="padding: 7px 12px;" icon="el-icon-more" @click="() => dialogAccessUsers = true"></el-button>
            </div>
        </el-input>

        <el-dialog width="800px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="'添加员工'" :visible.sync='dialogAccessUsers' :append-to-body='true'>
            <selector-table ref="accessUser" :conf='conf' v-bind="$attrs" :existsUsers='pers' :visible.sync='dialogAccessUsers' v-show="dialogAccessUsers" @changed='handleChangeUsers'></selector-table>
        </el-dialog>
    </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import SelectorTable from './selectorTable'
export default {
    name: 'selector',
    model: {
        prop: 'list',
        event: 'change'
    },
    directives: {
      // waves,
      elDragDialog
    },
    components: {
        SelectorTable,
    },
    props: {
        //已存在的人员
        list: {
            type: Array,
            default: () => {
                return []
            },
        },
        readonly: {
            type: Boolean,
            default: false
        },
        isShowClearButton: {
            type: Boolean,
            default: true
        },
        //选择器配置，结构如下
        /**
         * url： api接口地址
         * columns： 所有列
         * showColumns： 需要显示的列
         * conditions： 查询条件
         * showStyle： 选中后展示的风格类型
         */
        conf: {
            type: Object,
            required: true
        },
    },
    computed: {
        names() {
            if(this.pers && this.pers.length > 0){
                let showColumns = this.conf.columns.filter(s => this.conf.checkedShowColumns.findIndex(t => t == s.value) > -1)
                let names = showColumns.map(s => s.name)
                
                let tmp = this.pers.map(p => {
                    let cols = []
                    names.forEach(n => {
                        cols.push(p[n])
                    });
                    return cols.join('+')
                })
                return tmp.join(',')
            }
            return ''
        }
    },
    data() {
        return {
            delIdx: -1,
            pers: [],
            dialogAccessUsers: false,
        }
    },
    watch: {
        list: {
            handler(val) {
                this.pers = JSON.parse(JSON.stringify(val))
            },
            immediate: true
        },
    },
    mounted() {
    },
    methods: {
        handleRemove(idx) {
            this.pers.splice(idx, 1)
            this.usersChanged()
        },
        handleChangeUsers(users) {
            this.pers = users
            this.usersChanged()
            this.dialogAccessUsers = false
        },
        usersChanged () {
            this.$emit("change", this.pers)
        },
        handleClear() {
            this.pers = []
            this.usersChanged()
        },
        handleShow() {
            
            this.dialogAccessUsers = true
        },
    }
}
</script>

<style scoped>


.avatar-warpper{

}

.avatar-warpper .avatar-item{
    position: relative;
    float: left;
    text-align: center;
    margin: 0 8px;
    width: 50px;
}

.avatar-warpper .avatar-item .avatar{
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.avatar-warpper .avatar-item .avatar img{
    width: 50px;
    height: 50px;
}

.btn-remove{
    position: absolute;
    top: -5px; 
    right: -5px;
    cursor: pointer;
}

.btn-remove:hover {
    transition: all 0.3s;
    color: red;
}

.icon-plus{
    line-height: 50px;
    width: 50px;
    font-size: 24px;
    cursor: pointer;
    /* border: 1px solid red; */
}

.username{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.icon-close{
    cursor: pointer;
}

.dialog_wrapper >>> .el-dialog__body{
    padding-top: 15px;
}
</style>

