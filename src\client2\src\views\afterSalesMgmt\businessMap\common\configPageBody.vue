<template>
    <div class="configPageBody" v-loading="loading">
    	<div class="bBottom cl" v-show="!typeShow && !configChange">
    		<span class="fl omit">设备类型:&emsp;</span>
    		<el-radio-group class="elRadio fl" v-model="radio" @change="handleRadioChange">
			    <el-radio v-for="(ld,index) in listData" :label="ld.Id" :key="index">{{ld.Name}}</el-radio>
			</el-radio-group>
		</div>
		<div class="main" v-show="!typeShow && !configChange">
			<ul v-if="listChildData.length>0">
				<li v-for="lcd in listChildData">
					<span>{{lcd.Name}}&emsp;</span>
					<el-select @change="handleSelectChange" v-model="lcd.value" clearable placeholder="请选择">
					    <el-option
					      v-for="item in lcd.children"
					      :key="item.Id"
					      :label="item.Name"
					      :value="item.Id">
					    </el-option>
					</el-select>
				</li>
			</ul>
			<!--暂无数据-->
    		<no-data v-else></no-data>
		</div>
		<div v-show="typeShow || configChange">
			<ul class="typeUl" v-if="datas.length>0">
				<li v-for="item in datas">{{item.name}}:{{item.value}}</li>
			</ul>
			<!--暂无数据-->
    		<no-data v-else></no-data>
		</div>
		
    </div>
</template>    
<script>
	import NoData from "@/views/common/components/noData";
	import * as equipmentSetting from '@/api/equipmentSetting';
	import { listToTreeSelect } from '@/utils';
	export default{
		name:'configPageBody',
		props:['ids','dialogStatus','configChange'],
		data(){
	        return{
	        	listData:[],
	        	listChildData:[],
	        	radio:'',
	        	loading:false,
	        	datas:[]
	        }
	    },
	    computed: {
	        typeShow() {
	            return this.dialogStatus == 'detail'
	        },
	    },
	    components: { NoData },
	    created(){
        	
	    },
	    watch:{
	    	ids(val){
	    		this.getList();
	    	}
	    },
	    mounted(){
	        if(!this.dialogStatus && !this.configChange){
	        	this.getList();
	        }
	    },
	    methods:{
	    	handleRadioChange(d){
	    		let data=this.listData.find(v => v.Id == d);
	    		if(data.children){
	    			this.listChildData=data.children;
	    		}else{
	    			this.listChildData=[];
	    		}
	    		this.$emit('getConfigList',this.radio,this.listData,this.listChildData);
	    	},
	    	handleSelectChange(){
	    		this.$emit('getConfigList',this.radio,this.listData,this.listChildData);
	    	},
	    	getList(){
	    		this.datas=[];
	    		this.loading=true;
	    		equipmentSetting.getListPage({"PageIndex":1,"PageSize": 9999,}).then(res => {
	    			res.Items.forEach(v => {
	    				v.value="";
	    			})
	    			console.log(0,res)
	    			this.listData = listToTreeSelect(res.Items)
	    			//当编辑时父组件会有值带过来
	    				console.log(1,this.ids)
	    			if(this.ids && this.ids.length>0){
	    				this.listData.forEach((v,i) => {
	    					this.ids.forEach(item => {
	    						if(v.Id == item){
	    							this.radio=item;
	    							this.datas.push({
	    								name:'设备类型',
	    								value:v.Name
	    							});
	    							if(v.children){
	    								this.listChildData=v.children;
	    							}else{
	    								this.listChildData=[];
	    							}
	    						}
	    					})
	    					if(v.children){
	    						v.children.forEach(v1 => {
	    							if(v1.children && v1.children.length>0){
		    							v1.children.forEach(v2 => {
		    								this.ids.forEach(item => {
					    						if(v2.Id == item){
					    							v1.value=item;
					    							this.datas.push({
					    								name:v1.Name,
					    								value:v2.Name
					    							});
					    							return false;
					    						}
					    					})
		    							})
	    							}
	    						})
	    					}
	    				})
	    				console.log(2,this.listData)
	    				console.log(3,this.datas)
	    				this.loading=false;
	    			}else{
	    				this.setDefault();
	    				this.loading=false;
	    			}
	    		})
	    	},
	    	setDefault(){
	    		this.radio=this.listData[0].Id;
				if(this.listData[0].children){
					this.listChildData=this.listData[0].children;
				}else{
					this.listChildData=[];
				}
	    	},
	    	handlerGetCofigData(){
	    		this.$emit('getConfigList',this.radio,this.listData,this.listChildData);
	    	}
	    }
	}
</script>
<style lang="scss" scoped>
.main{
	height: 520px;
	overflow: auto;
	li{
		margin-bottom:10px;
		>span{
			display: inline-block;
			width:180px;
			text-align: right;
		}
	}
}
.elRadio{
	width:calc(100% - 74px);
	.el-radio{
		margin-bottom: 10px;
	}
}
.bBottom{
	margin-bottom:20px;
	border-bottom: 1px solid #EBEEF5;
}
.typeUl{
	li{
		margin-bottom:10px;
		padding-left:6px;
	}
}
</style>