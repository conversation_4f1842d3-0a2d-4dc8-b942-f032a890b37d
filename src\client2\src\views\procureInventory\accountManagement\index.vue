<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <!-- <div class="product-list">
                <div class="treeBox">
                    <v-tree @changeNode='changeTreeNode' :isAll='true' :isSubset='true'></v-tree>
                </div>
            </div> -->
            <div class="content-wrapper __dynamicTabContentWrapper">
                <!-- <div class="page-title-wrapper">
                    {{ pageTitle }}
                </div> -->
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading"  :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode'>

                        <template slot='ERPAccountRole' slot-scope="scope">
                            {{ scope.row.ERPAccountRole | roleTypeFilter }}
                        </template>
                        <template slot='AssociatedEmployeeList' slot-scope="scope">
                            <span v-if="scope.row.AssociatedEmployeeList">{{ scope.row.AssociatedEmployeeList.map(s => s.Name).join('、') }}</span>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row)" :type="1" text="详情"></app-table-row-button>
                            <!-- <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleReview(scope.row)" :type="2"></app-table-row-button> -->
                            <!-- <app-table-row-button @click="handleTableDelete(scope.row)" :type="3" text="删除"></app-table-row-button> -->

                            <!-- v-if="rowBtnIsExists('btnEdit')" -->
                            <app-table-row-button @click="handleEdit(scope.row, 'update')" text="编辑" :type="2"></app-table-row-button>
                        </template>

                        <template slot="btnsArea">
                          <!-- <el-button type="primary" @click="showDialog('create')">添加账号</el-button> -->
                        </template>

                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 查看/修改 -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" :regionalId="listQuery.RegionalId"></create-page> 
    
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as erpApi from "@/api/erpManagement/erp";
import createPage from "./create";
import { vars } from './vars'
// import vTree from '../../afterSalesMgmt/businessMap/common/tree'

export default {
    name: "comment-mgmt",
    mixins: [indexPageMixin],
    components: {
        createPage,
        // vTree,
    },
    props: {},
    filters: {
        roleTypeFilter(val) {
            let obj = vars.roleTypes.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return val
        },
    },
    computed: {
        // pageTitle() {
        //     return this.$route.name || ''
        // },
    },
    watch: {
    },
    created() {
        this.getList()
    },
    data() {
        return {
            layoutMode: 'simple',
            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "Account",
                        label: "ERP账号",
                    },
                },
                {
                    attr: {
                        prop: "ERPAccountRole",
                        label: "角色",
                        // showOverflowTooltip: true
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "AssociatedEmployeeList",
                        label: "关联人员",
                        // showOverflowTooltip: true
                    },
                    slot: true
                },
            
            ],
            listQuery: {
                PageIndex: 1,
                PageSize: 20,
                RegionalId: "",
            },
          
            tabDatas: [], //原始数据
            total: 0,
        };
    },
    methods: {
        // changeTreeNode(d) {
        //     if (d.Id == -1) {
        //         this.listQuery.RegionalId = null;
        //     } else {
        //         this.listQuery.RegionalId = d.Id;
        //     }
        //     this.listQuery.PageIndex = 1
        //     this.getList();
        // },

        showDialog(optType){
             if (this.listQuery.RegionalId != null) {
                this.dialogFormVisible = true;
                this.dialogStatus = optType;
            } else {
                this.$message({
                    message: "该节点不可添加账号",
                    type: "error"
                });
            }
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },

        //获取成员列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
         
            erpApi.getAccountList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        handleTableDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id)
            } else {
                ids.push(rows.Id)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                erpApi.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        //弹出详情框
        handleReview(row, optType = "detail") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
      
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

       /**表格行编辑、详情、跟进 */
      handleEdit(row, optType) {
       this.id = row.Id;
       this.dialogStatus = optType;
       this.dialogFormVisible = true;
     },

    },
};
</script>

<style lang="scss" scoped>

// .treeBox {
//     width: 100%;
//     height: 100%;
//     padding-top: 10px;
//     display: flex;
//     flex-direction: column;

//     .elTree {
//         flex: 1;
//         overflow: auto;
//         margin-top: 10px;
//         padding-bottom: 10px;
//     }
// }



.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

   
    /deep/.conditionArea-wrap[data-v-33c4a6c0]{
            padding-bottom: 0px;
            border-bottom: 0px;
            padding-top: 0px!important;
    }

    // .product-list {
    //     width: 250px;

    //     border-right: 1px solid #dcdfe6;
    //     // >div:first-child{
    //     //     display: flex;
    //     //     justify-content: space-between;
    //     //     align-items:center;
    //     //     padding:0 10px;
    //     // }
    // }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    // .custom-tree-node {
    //     display: block;
    //     width: 100%;
    //     position: relative;
    //     box-sizing: border-box;
    //     padding-right: 24px;

    //     .node-title {
    //         display: block;
    //         overflow: hidden;
    //         text-overflow: ellipsis;
    //         white-space: nowrap;
    //     }

    //     .node-btn-area {
    //         position: absolute;
    //         right: 0;
    //         top: 0;
    //         width: 23px;
    //         height: 16px;
    //     }
    // }
}

.tagBox{
    border-bottom: 1px solid #DCDFE6;
    padding: 4px 8px;
}

.month-range-wrapper{
    display: flex;
    .start-month, .end-month{
        flex: 1
    }
    .month-separator{
        padding: 0 6px;
    }
}

.page-title-wrapper{
    border-bottom: 1px solid $border-color-light;
    padding: 10px;
    margin-bottom: 10px;
}
</style>
