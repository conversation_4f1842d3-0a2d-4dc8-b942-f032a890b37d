<template>
  <div class="app-dialog-wrapper">
    <app-dialog
      :title="title"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1600"
      className="clear-default-height-auto dialog-auto-height"
    >
      <template slot="body">
            <app-table
                  ref="mainTable"
                  :isShowBtnsArea='false' 
                  :tab-columns="tabColumns" 
                  :tab-datas="formData.ModelList" 
                  :isShowAllColumn="true" 
                  :loading="listLoading"  
                  :isShowOpatColumn="false"  
                  :multable="false" 
                  :layoutMode='layoutMode'
                  :serial="false"
                >
                  <template slot="conditionArea">
                     <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="80px">
                            <div class="wrapper">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="ERP账号" prop="erpList">
                                        <erp-selector
                                            listSelectorTitle='选择ERP账号'
                                            :listSelectorUrl='serviceArea.business + "/ERPAccount/GetListPage"'
                                            :multiple='false' :showType='2'
                                            :list='formData.erpList'
                                            :columns='erpColumns'
                                            key='service-erp'
                                            @change='handleChangeErp'
                                            :readonly="!editable"
                                        ></erp-selector>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>
                    </el-form>
                  </template>


                    <template slot="Opt" slot-scope="scope">
                      <i class="el-icon-remove opt-icon" style="color: #F56C6C;" @click="handleTableDelete(scope.index - 1)"></i>
                      <!-- <app-table-row-button v-if="editable" @click="handleTableDelete(scope.index -1)" :type="3"></app-table-row-button> -->
                    </template>
                  
                    <template slot="Idx" slot-scope="scope">
                      {{ scope.index }}
                    </template>
                    <!-- {
                      "FMATERIALID": 100664,
                      "FDocumentStatus": "C",
                      "FForbidStatus": "A",
                      "FName": "炉管高温检测装置控制系统软件V1.0",
                      "FNumber": "00.1000001",
                      "FSpecification": "",
                      "value": "00.1000001"
                    }  -->
                    <template slot="MaterialCode" slot-scope="scope">
                      <div style="position: relative;">
                        <el-autocomplete
                            :disabled="!editable"
                            class="inline-input"
                            v-model.trim="scope.row.MaterialCode"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入物料编码"
                            :trigger-on-focus="false"
                            @select="(item) => handleSelectMaterial(item, scope.index - 1)"
                            @change="(val) => changeMaterialCode(val, scope.index - 1)"
                            @input='handleMaterialCodeInput'
                            popper-class="my-fixed-width-popper"
                        >
                          <template slot-scope="{item}">
                            <materialOption :opt="item"></materialOption>
                          </template>
                        </el-autocomplete>
                      </div>
                    </template>

                    <template slot="MaterialCount" slot-scope="scope">
                      <el-input-number style="width: 100px;" v-model="scope.row.MaterialCount" :min="0"></el-input-number>
                    </template>

                    <template slot="MaterialOutStockName" slot-scope="scope">
                      <div style="position: relative;">
                        <el-autocomplete
                          :disabled="!editable || !scope.row.MaterialCode"
                          class="inline-input"
                          v-model.trim="scope.row.MaterialOutStockName"
                          :fetch-suggestions="querySearchOut"
                          :trigger-on-focus="false"
                          @select="(item) => handleSelectOut(item, scope.index - 1)"
                          @change="(val) => changeTransferOutID(val, scope.index - 1)"
                          @input='handleMaterialOutStock'
                          popper-class="my-fixed-width-popper-300"
                        >
                          <template slot-scope="{item}">
                            <div :title="item.value" class="omit">{{ item.value }}</div>
                          </template>
                        </el-autocomplete>
                      </div>
                    </template>


                    <!-- :placeholder="(this.dialogStatus != 'sparePartsInventory-create' && this.dialogStatus != 'create-item' && this.dialogStatus !='detail-item')? ' 请输入调入仓库': ' 请输入仓库'" -->
                    <template slot="MaterialInStockName" slot-scope="scope">
                      <div style="position: relative;">
                        <el-autocomplete
                          :disabled="!editable"
                          class="inline-input"
                          v-model.trim="scope.row.MaterialInStockName"
                          :fetch-suggestions="querySearchIn"
                          :trigger-on-focus="false"
                          @select="(item) => handleSelectIn(item, scope.index - 1)"
                          @change="(val) => changeTransferInID(val, scope.index - 1)"
                          popper-class="my-fixed-width-popper-300"
                          @input='handleMaterialInStock'
                        >
                          <template slot-scope="{item}">
                            <div :title="item.value" class="omit">{{ item.value }}</div>
                          </template>
                        </el-autocomplete>
                      </div>
                    </template>

                    <template slot="RegionalName" slot-scope="scope">
                      <div class="_regional_detail_wrapper" style='align-items: center;'>
                        <div class="btn_wrapper" style='flex-shrink: 0;'>
                          <app-table-row-button :disabled="!editable" text="选择" @click="handleRegionDialog(scope.index - 1)"></app-table-row-button>
                            <!-- <el-button :disabled="!editable" type="text" @click="handleRegionDialog(scope.index - 1)">选择</el-button> -->
                        </div>
                        <div class="regional_text" style='line-height: normal;' :title="scope.row.RegionalName">{{ scope.row.RegionalName }}</div>
                        <div class="close_wrapper" v-show="scope.row.RegionalName && editable">
                            <div class="i_wrapper">
                                <el-button icon="el-icon-close" class="btn" circle @click="handleClear(scope.index - 1)"></el-button>
                            </div>
                        </div>
                    </div>
                    </template>



                    <template slot="MaterialEmployeeList" slot-scope="scope">
                      <!-- {{ scope.row.MaterialEmployeeList | nameFilter }} -->
                      <normar-emp-selector 
                          :readonly='(scope.row.RegionalId ? false : true) || !editable '
                          listSelectorTitle='选择人员' 
                          :listSelectorUrl='serviceArea.business + "/ImplementerManagement/GetListPage"' 
                          :multiple='false' :showType='2'
                          :list='scope.row.MaterialEmployeeList' 
                          key='service-users' 
                          :columns='empColumns'
                          @change='(users) => handleChangeUsers(users, scope.index - 1)'
                          :condition='{regionalId: scope.row.RegionalId,ReverseCheckLevel:true}'
                          :pageSize='100'
                          :isAutocomplete='true'
                      ></normar-emp-selector>
                    </template>

                     <template slot="Remark" slot-scope="scope">
                       <!-- <el-input :style="scope.row.isRemarkFocus ? 'position: absolute; height: 100px; top: 0; right: 0px; width: 300px; z-idex: 999;' : ''" :rows="scope.row.isRemarkFocus ? 5 : 1" :disabled="!editable" type="textarea" maxlength="500" @blur="handleBlur(scope.index - 1)" @focus="handleFocus(scope.index - 1)" v-model="scope.row.Remark"></el-input> -->

                       <!-- <el-input v-if="scope.index - 1 == 0" :style="scope.row.isRemarkFocus ? 'position: absolute; height: 100px; top: 0; right: 0px; width: 300px; z-index: 999;' : 'position: absolute; height: 100px; top: 0; right: 0px; width: 300px; z-idex: 999;'" :rows="scope.row.isRemarkFocus ? 5 : 5" :disabled="!editable" type="textarea" maxlength="500" @blur="handleBlur(scope.index - 1)" @focus="handleFocus(scope.index - 1)" v-model="scope.row.Remark"></el-input> -->

                       <!-- 未获取焦点，弄你 input 输入框样式 -->
                       <div style="padding: 6px 0; height: 40px;">
                        <div style="width: 100%; border: 1px solid #DCDFE6; background: #fff; cursor: text; height: 28px; line-height: 28px; overflow: hidden;" class="el-input__inner" @click="handleHelpFocus(scope.index - 1)">{{ scope.row.Remark }}</div>
                       </div>
                       <!-- 获取焦点 -->
                       <el-input v-show="scope.row.isRemarkFocus" :style="scope.row.isRemarkFocus ? 'position: absolute; height: 100px; top: 10px; right: 10px; width: 300px; z-index: 999;' : ''" :rows="scope.row.isRemarkFocus ? 5 : 1" :ref="`remark_input_${scope.index - 1}`" :disabled="!editable" type="textarea" maxlength="500" @blur="handleBlur(scope.index - 1)" v-model="scope.row.Remark"></el-input>
                     </template>
                     

                 <!-- 表格行操作区域 -->
                 <template slot-scope="scope">
                      <!-- <app-table-row-button v-if="editable" @click="handleTableDelete(scope.index -1)" :type="3"></app-table-row-button> -->
                      <!-- <app-table-row-button v-if="!editable" @click="handleReview(scope.row)" :type="2" text="详情"></app-table-row-button> -->
                </template>

                 <!-- <template slot="btnsArea">
                      <el-button v-if="editable" type="primary"  @click="addItem()">添加物料</el-button>
                 </template> -->
             </app-table>
               
             <!-- <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" /> -->

             <!-- <add-item @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="itemId"></add-item> -->
             
      </template>

      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button
          @click="createData"
          :buttonType="1"
          :disabled="disabledBtn"
          v-if="editable"
        ></app-button>
      </template>
    </app-dialog>

    <!-- 选择地区 -->
    <v-area-choose
    @closeDialog="closeRegionDialog"
    @electedRegionalData="electedRegionalData"
    :dialogFormVisible="dialogRegionFormVisible"
    :checkedList="formData.RegionalId ? [formData.RegionalId] : []"
    :disabledFn="disabledFn"
    :defaultExpandLevel='1'
    ></v-area-choose>

    <batchCoypDialog @closeDialog="closeBatchCoypDialog" :copyType="copyType" @saveSuccess="handleBatchCoypSaveSuccess" v-if="dialogFormBatchCoypVisible" :dialogFormVisible="dialogFormBatchCoypVisible"></batchCoypDialog>
    
  </div>

</template>

<script>
import * as regionalManagement from "@/api/systemManagement/regionalManagement";
import indexPageMixin from "@/mixins/indexPage";
// import addItem from "./addItem";
import erpSelector from './erpSelector'
import normarEmpSelector from '../../afterSalesMgmt/common/normarEmpSelector'
import vAreaChoose from "../../afterSalesMgmt/businessMap/common/areaChoose";
import batchCoypDialog from './batchCoypDialog'
import materialOption from './materialOption'
import { serviceArea } from "@/api/serviceArea"
import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";
import * as odc from "@/api/operatingDataCenter";
import * as erp from '@/api/erpManagement/erp' 

export default {
  name: "apply-create",
  mixins: [indexPageMixin],
  components: {
    // addItem,
    erpSelector,
    normarEmpSelector,
    vAreaChoose,
    batchCoypDialog,
    materialOption,
 },
  computed: {
    title() {
      if (this.dialogStatus == "create") {
        return "调拨申请";
      }else{
         this.getDetail()
         return "调拨申请详情";
      }
    },
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail";
    },
  },
  props: {
    dialogStatus: {
      //create、update、detail
      type: String,
    },
    id: {
      type: String,
      default: "",
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
          if (val) {
            this.resetFormData();

            let opt = {
              attr: {
                prop: "Opt",
                label: "操作",
                width: 50,
                renderHeader: this.renderHeader
              },
              slot: true
            }
  
            let idx = this.tabColumns.findIndex(s => s.attr.prop == 'Opt')
            if(this.editable) { //可编辑
              if(idx == -1) { //不存在
                this.tabColumns.splice(0, 0, opt)
              }
            }else{ //不可编辑
              if(idx > -1) { //存在
                this.tabColumns.splice(idx, 1)
              }
            }

            if (this.dialogStatus == "create") {
              this.getCacheAccount()
            }

          }
      },
      immediate: true
    },
  },
  filters: {
      nameFilter(employeeList) {
         return employeeList.map(v=> v.Name).toString()
      },

       materialNameFilter(materialName){
             if(materialName){
                 return materialName;
             } 
              return "无";
        },
  },
  created() {
    this.getPoorListByCondition()
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      serviceArea,
      // dialogFormVisible:false,
      layoutMode: "simple",
      listLoading: false,
      erpColumns: [
          {
             attr: {
                 prop: "Account",
                 label: "ERP账号",
              },
          },
          {
              attr: {
                    prop: "RegionalName",
                    label: "所属地区",
               },
          },
      ],
      tabColumns: [
        {
          attr: {
            prop: "Idx",
            label: "序号",
            width: 50
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialCode",
            label: "物料编码",
            renderHeader: this.renderHeaderMaterialCodeCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialName",
            label: "物料名称",
          },
        },
        {
          attr: {
            prop: "Specifications",
            label: "规格型号",
          },
          // slot:true
        },
        {
          attr: {
            prop: "MaterialOutStockName",
            label: "调出仓库",
            renderHeader: this.renderHeaderRequiredCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialCount",
            label: "申请数量",
            renderHeader: this.renderHeaderMaterialCountCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialUnit",
            label: "单位",
            width: 80,
            renderHeader: this.renderHeaderRequiredCol
          },
        },
        {
          attr: {
            prop: "InventoryCount",
            label: "即时库存",
            width: 80,
          },
        },
         {
          attr: {
            prop: "MaterialInStockName",
            label: "调入仓库",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true
        },
        {
          attr: {
            prop: "RegionalName",
            label: "使用地区",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true
        },
        {
          attr: {
            prop: "MaterialEmployeeList",
            label: "领料人",
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true,
        },
        {
          attr: {
            prop: "Remark",
            label: "备注",
            // showOverflowTooltip: true,
            renderHeader: this.renderHeaderCommonCol
          },
          slot: true,
        },
      ],
      
      total: 0,
      disabledBtn: false,
      rules: {
        erpList: { fieldName: "ERP账号", rules: [{ required: true }] },
      },
      formData: {
        ERPAccountId:'',
        ModelList:[],
        erpList:[],
      },

       resultData:{
          MaterialEmployeeIdList: [], 
          MaterialEmployeeList: [], 
          RegionalId:null,
          RegionalName:"",
          Remark:"",
          MaterialCount:"",
          MaterialOutStockFNumber: "",
          MaterialOutStock:"",
          MaterialOutStockName:"",
          InventoryCount: 0,
          MaterialInStockName:"",
          MaterialInStock:"",
          // MaterialStock:"",
          // MaterialStockName:"",
          MaterialUnitId:"",
          MaterialUnit:"",
          MaterialCode:"",
          ErpMaterialId:"",
          MaterialName: "",
          MaterialInStockFNumber:"",
          MaterialUnitFNumber:"",
          MaterialStockFNumber:"",

        },

        erp:{
          Id: "",
          Account: "",
          RegionalId: "",
          RegionalName: "",
        },
       
        itemId:'',

        empColumns: [
            {
            attr: { prop: "Name", label: "姓名", width: '100' },
            },
            {
            attr: { prop: "Number", label: "工号", width: '100' },
            },
            {
            attr: { prop: "Gender", label: "性别", width: '60' },
            },
            {
            attr: { prop: "RegionalName", label: "负责地区" },
            },
            {
            attr: { prop: "Phone", label: "手机", width: '120' },
            },
            {
            attr: { prop: "OrgName", label: "部门" },
            }
        ],
          

        dialogRegionFormVisible: false,
        currentRowIdx: -1,
        dialogFormBatchCoypVisible: false,
        
        regs: [],

        isSelectedMaterialCode: false,

        isSelectedMaterialOutStock: false,

        isSelectedMaterialInStock: false,

        


    };
  },
  methods: {
    getCacheAccount() {
      erp.getCacheAccount({}).then(res => {
        if(res) {
          this.handleChangeErp([res])
        }
      })
    },
    getPoorListByCondition() {
      let postDatas = {"RegionalName":"","RegionalId":""}
      regionalManagement.getPoorListByCondition(postDatas).then(res => {
        this.regs = res
      })
    },
    //判断 tId 是不是和 pId 属于同一个根节点
    isSameRootNode(pId, tId) {
      //找到第一个节点的根节点
      let pObj = this.regs.find(s => s.Id == pId)
      while(pObj && pObj.ParentId) {
        pObj = this.regs.find(s => s.Id == pObj.ParentId)
      }
      
      //找到第二个节点的根节点
      let pObj2 = this.regs.find(s => s.Id == tId)
      while(pObj2 && pObj2.ParentId) {
        pObj2 = this.regs.find(s => s.Id == pObj2.ParentId)
      } 

      return pObj.Id && pObj.Id == pObj2.Id

    },
    handleHelpFocus(idx) {
      let row = this.formData.ModelList[idx]
      this.$set(row, 'isRemarkFocus', true)

      this.$nextTick(() => {
        if(row) {
          this.$refs[`remark_input_${idx}`].focus()
        }
      })
      // setTimeout(() => {
      // }, 10)
    },
    // handleFocus(idx) {
    //   let row = this.formData.ModelList[idx]
    //   if(row) {
    //     this.$set(row, 'isRemarkFocus', true)
    //   }
    // },
    handleBlur(idx) {
      let row = this.formData.ModelList[idx]
      if(row) {
        this.$set(row, 'isRemarkFocus', false)
      }
    },
    querySearch(queryString, cb) {
      let keywords = (queryString || '').trim()
      odc.getMaterial({keyWords:keywords,ERPAccountId:this.formData.ERPAccountId,}).then(res => {
        let result = res.map(v=>{
          v.value = v.FNumber
          return v
        });
        cb(result);
      }).catch(err => {
            
      });
    },
    handleSelectMaterial(item, idx){
      this.isSelectedMaterialCode = true

      if(this.formData.ModelList && this.formData.ModelList[idx]) {
        let obj = this.formData.ModelList[idx]
        obj.MaterialCode = item.FNumber //物料编码
        obj.MaterialName = item.FName //物料名称
        obj.Specifications = item.FSpecification //规格型号
        obj.ErpMaterialId = item.FMATERIALID

        this.getMaterialDetails(obj.MaterialCode, idx)
      }
        // this.formData.MaterialCode = item.FNumber
        // this.formData.MaterialName = item.FName
        // this.formData.Specifications = item.FSpecification
        // this.formData.ErpMaterialId = item.FMATERIALID

        // this.getMaterialDetails(this.formData.MaterialCode)
    },
    handleMaterialCodeInput() {
      this.isSelectedMaterialCode = false

    },
    getMaterialDetails(val, idx) {
        odc.getMaterialDetails({keyWords:val, ERPAccountId:this.formData.ERPAccountId,}).then(res => {
            if(res) {
                this.handleSelectUnit(res, idx)
            }
        })
    },
    // changeMaterialUnitId(val, idx){
    //   if(!val){
    //     // this.formData.MaterialUnitId = ''
    //     // this.formData.MaterialUnitFNumber = ''
    //   }
    // },
    handleSelectUnit(item, idx){
      
      if(this.formData.ModelList && this.formData.ModelList[idx]) {
        let obj = this.formData.ModelList[idx]
        obj.MaterialUnitId = ''
        obj.MaterialUnitFNumber = ''
        
        obj.MaterialUnitId = item.FUNITID
        obj.MaterialUnitFNumber = item.FNumber
        obj.MaterialUnit = item.FNumber
        
        obj.MaterialOutStockName = item.FStockName
        obj.MaterialOutStockFNumber = item.FStockCode //调出仓库编码
        obj.MaterialOutStock = item.FStockId //调出仓库

        obj.InventoryCount = item.InventoryCount //即时库存

      }

    },
    changeMaterialCode(val, idx){
      if(!this.isSelectedMaterialCode) {
        if(this.formData.ModelList && this.formData.ModelList[idx]) {
          let obj = this.formData.ModelList[idx]
          obj.MaterialCode = ''
          obj.MaterialName = ''
          obj.Specifications = ''
          obj.ErpMaterialId = ''
          obj.MaterialUnitId = ''
          obj.MaterialUnit = ''
          obj.InventoryCount = 0
          obj.MaterialUnitFNumber = ''
          obj.MaterialOutStockName = ''
        }
      }
      this.isSelectedMaterialCode = false
    },
    querySearchOut(queryString, cb){
      let keywords = queryString.trim()
      odc.getStock({keyWords:keywords,ERPAccountId:this.formData.ERPAccountId,}).then(res => {
              let result = res.map(v=>{
                v.value = v.FName
                return v
            });
            cb(result);
      }).catch(err => {
            
      });
    },
    handleSelectOut(item, idx){

      this.isSelectedMaterialOutStock = true

      if(this.formData.ModelList && this.formData.ModelList[idx]) {

        let obj = this.formData.ModelList[idx]
        if(obj) {
          obj.MaterialOutStockName = item.FName
          obj.MaterialOutStock = item.FStockId
          obj.MaterialOutStockFNumber = item.FNumber
        }

        let postDatas = {
          FUNITID: obj.ErpMaterialId,
          FStockId: item.FStockId
        }
        odc.getMaterialInventoryByFStockId(postDatas).then(res => {
          obj.InventoryCount = res || 0
        })
      }
    },
    changeTransferOutID(val, idx){

      if(!this.isSelectedMaterialOutStock) {

        if(this.formData.ModelList && this.formData.ModelList[idx]) {
          let obj = this.formData.ModelList[idx]
  
          obj.MaterialOutStockName = ''
          obj.MaterialOutStock = ''
          obj.MaterialOutStockFNumber = ''
          obj.InventoryCount = 0
        }
      }
      this.isSelectedMaterialOutStock = false
    },
    handleMaterialOutStock() {
      this.isSelectedMaterialOutStock = false
    },


    

    querySearchIn(queryString, cb){
      let keywords = (queryString || '').trim()
      odc.getStock({keyWords:keywords,ERPAccountId:this.formData.ERPAccountId,}).then(res => {
              let result = res.map(v=>{
                v.value = v.FName
                return v
            });
            cb(result);
      }).catch(err => {
            
      });
    },
    handleSelectIn(item, idx){
      this.isSelectedMaterialInStock = true
      if(this.formData.ModelList && this.formData.ModelList[idx]) {
        let obj = this.formData.ModelList[idx]
        // obj.MaterialStockName = item.FName
        // obj.MaterialStock = item.FStockId

        obj.MaterialInStock = item.FStockId
        obj.MaterialInStockFNumber = item.FNumber
        obj.MaterialStockFNumber = item.FNumber
      }
    },
    handleMaterialInStock() {
      this.isSelectedMaterialInStock = false
    },
    changeTransferInID(val, idx){

      if(!this.isSelectedMaterialInStock) {
        if(this.formData.ModelList && this.formData.ModelList[idx]) {
          let obj = this.formData.ModelList[idx]
          // obj.MaterialStockName = ''
          // obj.MaterialStock = ''
  
          obj.MaterialInStockName = ''
          obj.MaterialInStock = ''
          obj.MaterialInStockFNumber = ''
          obj.MaterialStockFNumber = ''
        }
      }

      this.isSelectedMaterialInStock = false

    },

    //批量复制填入“物料编号”——单条数据返回逻辑 = 选择下拉 + 选择下拉后调用接口的所有（赋值）逻辑
    getMaterialListDetails(list) {

      let postDatas = {
        KeywordList: list,
        ERPAccountId: this.formData.ERPAccountId,
      }
      this.listLoading = true
      odc.getMaterialListDetails(postDatas).then(res => {
        this.listLoading = false
        this.formData.ModelList.forEach((row, idx) => {
          let target = res[idx]
          if(target) {
            row.MaterialCode = target.MaterialCode //物料编码
            row.MaterialName = target.MaterialName //物料名称
            row.Specifications = target.Specifications //规格型号
            row.ErpMaterialId = target.ErpMaterialId

            this.handleSelectUnit(target, idx)

          }
        });
      }).catch(err => {
        this.listLoading = false
      })
    },

    //批量调入
    // getListStock() {
    //   let list = ['10#自制库', '五厂杏13-2站北京试验']
    //   let postDatas = {
    //     KeywordList: list,
    //     ERPAccountId: this.formData.ERPAccountId,
    //   }
    //   this.listLoading = true
    //   odc.getListStock(postDatas).then(res => {
    //     this.listLoading = false
    //     this.formData.ModelList.forEach((row, idx) => {
    //       let target = res[idx]
    //       if(target) {

    //         this.handleSelectIn(target, idx)
    //       }
    //     });
    //   }).catch(err => {
    //     this.listLoading = false
    //   })
    // },

    //地区选择
    closeRegionDialog() {
      this.dialogRegionFormVisible = false;
    },
    
    handleRegionDialog(rowIdx){
      this.currentRowIdx = rowIdx
      this.dialogRegionFormVisible=true;
    },
    electedRegionalData(data){
        // this.$refs.formData.clearValidate('RegionalId');
        if(this.formData.ModelList && this.formData.ModelList[this.currentRowIdx]) {
            let obj = this.formData.ModelList[this.currentRowIdx]
            if(data){
                obj.RegionalId=data.Id;
                obj.RegionalName=data.ParentName;
            }else{
                obj.RegionalId='';
                obj.RegionalName='';
            }
            obj.MaterialEmployeeList=[];
            obj.MaterialEmployeeIdList=[];

            this.setRemark(obj)
        }
    },
    setRemark(row) {
      row.Remark = `使用地区：${row.RegionalName}\n领料人：${row.MaterialEmployeeList.map(s => s.Name).join("、")}\n备注：`
    },
    handleClear(idx) {
      if(this.formData.ModelList && this.formData.ModelList[idx]) {
        let obj = this.formData.ModelList[idx]
        obj.RegionalId='';
        obj.RegionalName='';
        obj.MaterialEmployeeList=[];
        obj.MaterialEmployeeIdList=[];

        this.setRemark(obj)
      }
    },
    disabledFn(data, nodeType) {
      //禁选一级节点
      if(data.level <= 1) {
          return true
      }
      return false
    },
    handleChangeUsers(users, idx) {

        // this.$refs.formData.clearValidate('MaterialEmployeeList');

        if(this.formData.ModelList && this.formData.ModelList[idx]) {
            let obj = this.formData.ModelList[idx]
            obj.MaterialEmployeeList = users;
            obj.MaterialEmployeeIdList = users.map(s => s.EmployeeId) || []

            this.setRemark(obj)
        }

    },
    renderHeader(h, { column }) {
        return (
            <span>
                <i style='font-size: 18px; color: #409EFF; cursor: pointer;' class='el-icon-circle-plus' on-click={() => this.addItem()}></i>
            </span>
        )
    },
    renderHeaderMaterialCodeCol(h, { column }) {
      return (
        <div>
          <div>{ column.label } <span class='red'> *</span></div>
          <div>
            <el-button type="text" style='padding: 0;' on-click={() => this.openBatchCoypDialog(column.property)}>粘贴填入</el-button>
          </div>
        </div>
      )
    },
    renderHeaderMaterialCountCol(h, { column }) {
        return (
          <div>
            <div>
              { column.label }
               <span class='red'> *</span>
            </div>
            <div>
              <el-button type="text" style='padding: 0;' disabled={this.formData.ModelList.length == 0 ? true : false} on-click={() => this.openBatchCoypDialog(column.property)}>粘贴填入</el-button>
            </div>
          </div>
        )
    },
    renderHeaderCommonCol(h, { column }) {
        return (
          <div>
            <div>
              { column.label } 
              {
                column.property != 'Remark' ? <span class='red'> *</span> : ''
              }
            </div>
            <div>
              <el-button type="text" style='padding: 0; color: #67c23a;' disabled={this.formData.ModelList.length == 0 ? true : false} on-click={() => this.batchCoverage(column)}>批量覆盖</el-button>
            </div>
          </div>
        )
    },
    renderHeaderRequiredCol(h, { column }) {
      return (
          <div>
            { column.label } 
            <span class='red'> *</span>
          </div>
        )
    },
    batchCoverage(prop) {
      let propName = prop.property
      this.formData.ModelList.forEach((row, idx) => {
        let temp = this.formData.ModelList[0]

        if(idx > 0) {
          if(propName == 'MaterialInStockName') {
            row.MaterialInStockName = temp.MaterialInStockName

            row.MaterialInStock = temp.MaterialInStock
            row.MaterialInStockFNumber = temp.MaterialInStockFNumber
            row.MaterialStockFNumber = temp.MaterialStockFNumber
          }else if(propName == 'RegionalName') {
            row.RegionalId = temp.RegionalId
            row.RegionalName = temp.RegionalName

            this.setRemark(row)
          }else if(propName == 'MaterialEmployeeList') {
            let isSame = this.isSameRootNode(temp.RegionalId, row.RegionalId)
            if(isSame) {
              row.MaterialEmployeeIdList = temp.MaterialEmployeeIdList
              row.MaterialEmployeeList = temp.MaterialEmployeeList

              this.setRemark(row)
            }
          }else if(propName == 'Remark') {
            row.Remark = temp.Remark
          }
        }
      })
    },
    openBatchCoypDialog(type) {
      this.copyType = type
      this.dialogFormBatchCoypVisible = true;
    },
    closeBatchCoypDialog() {
      this.dialogFormBatchCoypVisible = false;
    },
    handleBatchCoypSaveSuccess(obj) {
      let _copyType = obj.copyType
      let _list = obj.list || []

      if(_list.length == 0) {
        return false
      }

      let diffLen = _list.length - this.formData.ModelList.length
      if(diffLen > 0) {
        for(let i = 0; i < diffLen; i++) {
          this.addItem()
        }
      }


      if(_copyType == 'MaterialCode') {
        this.getMaterialListDetails(_list)
      }else if(_copyType == 'MaterialCount') {
        this.formData.ModelList.forEach((row, idx) => {
          row.MaterialCount = _list[idx] || 0
        })
      }

      this.closeBatchCoypDialog();
    },

    getDetail(){
       materialTransferApi.materialDetails({"id":this.id})
                  .then((res) => {
                     this.itemId = res.Id;
                     this.resultData = Object.assign({}, this.resultData, res);
                     this.formData.ERPAccountId = res.ERPAccountId;
                     this.formData.ModelList.push(this.resultData);
                     this.erp = Object.assign({}, this.erp, res);
                     this.formData.erpList.push(this.erp);
              
                  })
                  .catch((err) => {
                  
                  });
    },
    handleTableDelete(index){
      this.$confirm(`是否确认删除?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            this.formData.ModelList.splice(index,1)
        })
    },
    // handleReview(row, optType = "detail"){
    //     this.id = row.Id;
    //     this.dialogFormVisible = true;
    // },
    handleChangeErp(erps) {
      this.$refs.formData.clearValidate('erpList');
      if(erps && erps.length > 0) {
        this.formData.ERPAccountId = erps[0].Id
        this.formData.erpList = erps
      }
    },
    addItem(){
        this.formData.ModelList.push({
          MaterialEmployeeIdList: [], 
          MaterialEmployeeList: [], 
          // AgentIds:[],
          // AgentIdsNames:null,
          RegionalId:null,
          RegionalName:"",
          Remark:"",
          MaterialCount:1,
          MaterialOutStockFNumber: "",
          MaterialOutStock:"",
          MaterialOutStockName:"",
          InventoryCount: 0,
          MaterialInStockName:"",
          MaterialInStock:"",
          // MaterialStock:"",
          // MaterialStockName:"",
          MaterialUnitId:"",
          MaterialUnit:"",
          MaterialCode:"",
          ErpMaterialId:"",
          MaterialName: "",
          MaterialInStockFNumber:"",
          MaterialUnitFNumber:"",
          MaterialStockFNumber:"",
        })


        // this.dialogFormVisible = true;
    },
    //  closeDialog() {
    //     this.dialogFormVisible = false;
    //  },
    //  handleSaveSuccess(_formData) {
    //     this.formData.ModelList.push(_formData)
    //     this.closeDialog();
    //  },

    resetFormData() {
      this.formData = {
          ERPAccountId:'',
          ModelList:[],
          erpList:[],
      };
    },

    handleCurrentChange(){

    },
    handleSizeChange(){

    },
    createData() {

      this.$refs.formData.validate(valid => {
        if (valid) {

          if(this.formData.ModelList.length === 0){
            this.$message({
              message: '物料不能为空',
              type: 'error'
            });
          }else{
            let postData = JSON.parse(JSON.stringify(this.formData));

            let requiredColumns = ['MaterialCode', 'MaterialCount', 'MaterialUnit', 'MaterialOutStockName', 'MaterialInStockName', 'RegionalName', 'MaterialEmployeeList',]
            //验证通过
            let flag = true
            for(let i = 0; i < requiredColumns.length; i++) {
              let prop = requiredColumns[i]
              let idx = postData.ModelList.findIndex(s => !s[prop] || (prop == 'MaterialEmployeeList' && s[prop].length == 0))
              if(idx > -1) {
                flag = false //验证不通过
                let obj = this.tabColumns.find(s => s.attr.prop == prop)
                if(obj) {
                  this.$message({
                    message: `${obj.attr.label}不能为空`,
                    type: 'error'
                  });
                }
                break;
              }
            }
            
            if(!flag){
              return false
            }
            

            //  console.log(this.formData.ModelList)
              delete postData.erpList
              delete postData.ModelList.MaterialEmployeeList

              this.disabledBtn = true;
              if (this.dialogStatus == "create") {
                this.listLoading = true
                materialTransferApi.addList(postData)
                  .then((res) => {
                    this.listLoading = false
                    this.disabledBtn = false;
                    this.$notify({
                      title: "提示",
                      message: "保存成功",
                      type: "success",
                      duration: 2000,
                    });
                    this.$refs.appDialogRef.createData();
                  })
                  .catch((err) => {
                    this.listLoading = false
                    this.disabledBtn = false;
                  });
              }
          }
            }
          });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>


<style lang="scss" scoped>
.wrapper{
  /deep/.el-form-item{
    margin-bottom: 0;
  }
}

.opt-icon{
    font-size: 18px!important;
    cursor: pointer;
}

</style>