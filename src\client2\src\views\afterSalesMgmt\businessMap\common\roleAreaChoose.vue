<template>
    <div class="areaChoose">
        <app-dialog title="地区选择" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <div class="bodyBox">
                    <!-- <slot name="tip-area"></slot> -->
                    <!-- 弹框的树形选择不支持单击选中，必须通过选中checkbox -->
                    <!-- <v-tree class="vtree" :queryRegionID='queryRegionID' :defaultExpandLevel="defaultExpandLevel" @checkChangeNode='handleCheckChangeNode' @checkChangeNodeList='handleCheckChangeNodeList' v-bind="$attrs" :isCheckbox='true'></v-tree> -->
                      <div style="width: 100%; padding: 10px 10px 0;">
                            <el-input class="elInput" style="margin-bottom: 4px;" prefix-icon="el-icon-search" placeholder="输入关键字" clearable="" v-model="filterText"></el-input>
                      </div>
                     <el-tree class="tree" v-loading="treeLoading"
                            :check-strictly='true'
                            :data="treeData" show-checkbox node-key="Id"
                            @check='checkOrg'
                            :filter-node-method="filterNode" 
                            :props="defaultProps" ref="treeRef">
                    </el-tree>
                </div>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>
<script>

import * as businessRoleApi from "@/api/businessRole";
import { listToTreeSelect } from '@/utils'

export default{
    name:'areaChoose',
    components: {   
      
    },
    props:{
        beforeConfirm: Function,
        queryRegionID:{
            type:String,
            default:''
        },
        defaultExpandLevel:{
            type: Number,
            default: 0
        },
        businessRoleType:{
            type: Number,
            default: 0
        },
        checkedList:{
            type: Array,
            default: []
        },
        type:{
            type: Number,
            default: 0
        },
    },
    data(){
        return{
           treeData:[],
           defaultProps:{
                children: "children",
                label: "RegionalName"
           },
           treeLoading:false,
           disabledBtn:false,
           checkData:null,
           filterText: "",
        }
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
    },
    created(){
        this.getAreas()
    },
    mounted(){
        
    },
    methods:{
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },
        async createData(){
            let result = true
            if(this.beforeConfirm) {
                result = await this.beforeConfirm(this.checkData)
            }

            if(result) {
             
                this.$emit('electedRegionalData',this.checkData);
                this.handleClose();
            }
        },
     
      
        getAreas() {
            this.treeLoading = true;
            businessRoleApi
                .getRegionalListByCondition({BusinessRoleType: this.businessRoleType})
                .then(res => {
                    this.treeLoading = false
                    let treeDatas = listToTreeSelect(res,undefined,undefined,{key: 'Id',parentKey: 'ParentId'},'RegionalName');
                    this.treeData = treeDatas
                    if(this.type === 1){
                        this.treeData.map(v=>v.disabled = v.Level === 1?false:true)
                    }
                   
                    if(this.checkedList && this.checkedList.length>0){
                       this.$refs.treeRef.setCheckedKeys(this.checkedList);
                    }
                }).catch(err => {
                    this.treeLoading = false
                });
         },

        checkOrg(item) {
            this.$refs.treeRef.setCheckedKeys([item.Id]);
            this.checkData = item
        },

         //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.RegionalName.indexOf(value) !== -1;
        },

    }

}
</script>
<style lang="scss" scoped>
.bodyBox{
    width:100%;
    height:536px;
    display: flex;
    flex-direction: column;
    .vtree{
        flex: 1;
        overflow-y: hidden;
    }
}
</style>