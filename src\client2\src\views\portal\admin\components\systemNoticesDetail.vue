<template>
  <div>
    <app-dialog
      title="公告通知详情"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :destroy-on-close="true"
      :width="1200"
      :maxHeight="700"
    >
      <template slot="body">
        <div class="news-wrapper" v-loading="systemNoticesListLoading">
          <div class="news-container">
            <header>{{ newDetail.NoticeTitle }}</header>
            <div style="margin-top: 30px;">
              <span>{{ newDetail.Author }}</span>
              &nbsp;&nbsp;
              <span>{{ newDetail.CreateTime | dateFilter("YYYY-MM-DD HH:mm:ss") }}</span>
            </div>
            <div class="hr">
              <span class="hrl"></span>
              <span class="hrc"></span>
              <span class="hrr"></span>
            </div>
            <div class="divUeditor ql-editor" v-viewer v-html="newDetail.NoticeContent"></div>
          </div>
          <hr style="background-color: #e1e1e1;height: 1px;border: none;" />
          <!-- 附件区 -->
          <div>
            <b style="font-size:18px;">附件</b>
            <br />
            <app-uploader
              accept="all"
              :readonly="true"
              :fileType="3"
              :max="10000"
              :value="newDetail.AttachmentIdList"
              :fileSize="1024 * 1024 * 500"
              :minFileSize="100 * 1024"
            ></app-uploader>
          </div>
          <hr style="background-color: #e1e1e1;height: 1px;border: none;" />

          <!-- 已阅用户区 -->
          <div>
            <b style="font-size:18px;">已阅用户({{ avatarList.length }})</b>
            <br />
            <div class="placeholder" style="padding-top: 10px;" v-if="avatarList.length == 0">暂无已阅用户</div>
            <div class="avatar-item-list" v-else>
              <div class="avatar-item" v-for="(v, idx) in avatarList" :key="`avatar-${idx}`">
                <el-avatar :size="36" :src="v.Avatar" :key="idx" :title="v.Name"></el-avatar>
                <span class="title omit">{{ v.Name }}</span>
              </div>
            </div>
          </div>
          <hr style="background-color: #e1e1e1;height: 1px;border: none;" />

          <!-- 评论区 -->
          <div v-if="this.newDetail.NoticeStatus !== 0">
            <div>
              <b style="font-size:18px;">评论({{ commentListCount }})</b>
              <br />

              <div style="margin-top: 20px;" v-for="comment in commentList" :key="comment.id">
                <span>{{ comment.CreateEmployeeName }}</span>
                &nbsp;&nbsp;
                <span>{{ comment.CreateTime | dateFilter("YYYY-MM-DD HH:mm:ss") }}</span>
                <p
                  style="word-wrap: break-word;word-break: break-all;"
                  v-html="comment.CommentContent"
                ></p>
                <hr style="background-color: #e1e1e1;height: 1px;border: none;" />
              </div>
            </div>

            <div style="margin-top: 20px;height: 200px;">
              <b style="font-size:18px">我要评论</b>
              <p>
                <el-input
                  type="textarea"
                  :rows="5"
                  maxlength="500"
                  placeholder="请输入内容"
                  v-model="commentContent"
                ></el-input>
                <span style="float:right;margin-top: 10px;">
                  <el-button @click="handleClear()">清空</el-button>
                  <app-button @click="handleSubmit()" text="提交"></app-button>
                </span>
              </p>
            </div>
          </div>
        </div>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType="2" text="关闭"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as systemNotices from "@/api/informationCenter/systemNotices";
import dayjs from "dayjs";
import { getUserInfo } from "@/utils/auth";

export default {
  name: "systemNoticesDetail-Detail",
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
  },
  computed: {
    avatarList() {
      return this.newDetail.EmployeeReadList || []
    },
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val && this.id) {
          this.getNewDetail();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      systemNoticesListLoading: false,
      isClear: false,
      newDetail: {},
      commentContent: "",
      commentList: [],
      commentListCount: 0,
    };
  },
  methods: {
    handleClear() {
      this.commentContent = "";
    },

    //获取评论集合
    getCommentList() {
      systemNotices.getComment({ businessId: this.id }).then(res => {
        this.commentList = res;
        this.commentListCount = res.length;
      });
    },

    //提交评论
    handleSubmit() {
      if (this.commentContent == "") return;
      var obj = {
        CurrentBusinessId: this.id,
        CommentContent: this.commentContent,
        Type: 8,
      };
      systemNotices.addComment(obj).then(() => {
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000,
        });
        this.handleClear();
        this.getCommentList();
      });
    },

    getNewDetail() {
      this.systemNoticesListLoading = true;
      systemNotices.detail({ id: this.id }).then(res => {
        this.newDetail = res;
        if (res != null) {
          this.getCommentList();
        }
        this.systemNoticesListLoading = false;
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>

<style lang="scss" scoped>
.news-wrapper {
  padding: 20px;
  background: white;
  overflow: auto;
}

.news-container {
  width: 100%;
  padding-bottom: 20px;
}

img,
video {
  display: inline-block;
  width: 100%;
  height: auto;
  margin-top: 0.4rem;
}
header {
  font-size: 30px;
  color: #373c41;
  font-weight: bold;
  text-align: center;
}
aside {
  font-size: 16px;
  color: #8d8f91;
  margin-top: 30px;
}

article p {
  font-size: 0.42rem;
  color: #373c41;
  margin-top: 0.4rem;
  line-height: 0.67rem;
  text-indent: 2em;
}
.hr {
  width: 100%;
  margin: 10px 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.hr span {
  background-color: #e3e3e5;
}
.hrl,
.hrr {
  width: calc(50% - 5px);
  height: 1px;
}
.hrc {
  width: 3px;
  height: 3px;
  border-radius: 3px;
}
@media screen and (min-width: 1080px) {
  body {
    width: 1080px;
    height: 500px;
    overflow: auto;
    margin: 0 auto;
  }
}
.divUeditor {
  overflow: hidden;
  display: block;
  width: 100%;
  min-width: 90%;
  position: relative;
  word-wrap: break-word;
  padding-bottom: 20px;
}

.divUeditor img {
  border: 0;
  max-width: 100%;
  margin-top: 10px;
}

.down-wrapper a {
  color: #409eff;
  line-height: 140%;
}

.down-wrapper p {
  margin: 0;
  margin-bottom: 10px;
}
.opt {
  top: 10px;
  right: 10px;
  position: absolute;
  cursor: pointer;
  i {
    font-size: 14px;
  }
}
.opt:hover {
  color: rgb(64, 158, 255);
}

.avatar-item-list {
  display: flex;
  flex-wrap: wrap;
  padding-top: 10px;

  .avatar-item {
    width: 50px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 5px 0;
    .title {
      width: 100%;
      text-align: center;
      font-size: 12px;
      height: 24px;
      line-height: 24px;
    }
  }
}
</style>
