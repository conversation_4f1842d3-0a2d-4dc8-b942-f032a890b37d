<template>
    <div>
        <el-col>
            <div class="text item">
                <div style="font-size: 20px;margin-bottom: 12px;" v-if="showTitle">相关内容</div>
                <div style="text-align: center;height: 266px;line-height: 266px;" v-if="!systemNoticesDatas || systemNoticesDatas.length == 0">
                    暂无相关内容
                </div>
                <div class="content" style="height:320px" v-if="systemNoticesDatas.length>0">
                    <ul>
                        <li v-for="(item,index) in systemNoticesDatas" :key="index">
                            <a @click="handleClick(item)">
                                <span>
                                    {{item.Text}}
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </el-col>
    </div>
</template>

<script>
export default {
    name: 'relateList',
    props: {
        showTitle: {
            type: Boolean,
            default: true
        },
        list: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    watch: {
        list: {
            handler(vals) {
                this.systemNoticesDatas = JSON.parse(JSON.stringify(vals))
            },
            deep: true
        },
    },
    data() {
        return {
            systemNoticesDatas: []
        }
    },
    methods: {
        handleClick(row) {
            this.$emit('click', row)
        },
    },
}
</script>

<style scoped>
ul li{
    list-style: square;
    margin-top: 20px;
}
</style>