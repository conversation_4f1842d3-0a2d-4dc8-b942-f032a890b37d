<template>
  <div class="classificationLabel" v-loading="loading">
    <div class="nav">
      <span>故障类型：</span>
      <el-radio-group v-model="radio" @change="handleRadio">
        <el-radio v-for="(ld,index) in listData" :label="ld.Id" :key="index">{{ld.Name}}</el-radio>
      </el-radio-group>
    </div>
    <main>
      <ul v-if="secondListData.length>0">
        <li v-for="(sld,index) in secondListData" :key="index">
          <span>{{sld.Name}}：</span>
          <el-checkbox-group v-model="sld.checkList" @change="((val) => handleCheck(val,sld.checkList))">
            <el-checkbox v-for="(s,i) in sld.children" :label="s.Name" :key="i"></el-checkbox>
          </el-checkbox-group>
        </li>
      </ul>
      <no-data v-else></no-data>
    </main>
  </div>
</template>

<script>
import * as equipmentSetting from '@/api/equipmentSetting';
import { listToTreeSelect } from '@/utils';
import NoData from "@/views/common/components/noData";
export default {
  name: "mainten-order-mgmt-assign",
  components: {
    NoData
  },
  mixins: [],
  props: {
    onlyOne:{
      type:Boolean,
      default:false,
    },

  },
  watch: {
    
    
  },
  computed:{
    selectedMsg(){
      return this.$store.state.communication.selectedMsg;
    },
    selectedData(){
      return this.$store.state.communication.selectedData;
    },
    indexSelectedData(){
      return this.$store.state.communication.indexSelectedData;
    },
    indexSelectedMsg(){
      return this.$store.state.communication.indexSelectedMsg;
    },
  },
  created() {
    
  },
  mounted(){
    if(this.onlyOne){
      
        this.saveMsg=JSON.parse(JSON.stringify(this.indexSelectedMsg));
        this.saveData=JSON.parse(JSON.stringify(this.indexSelectedData));
      
    }else{
      
        this.saveMsg=JSON.parse(JSON.stringify(this.selectedMsg));
        this.saveData=JSON.parse(JSON.stringify(this.selectedData));
      
    }
    this.getList();
  },
  data() {
    return {
        loading:false,
        radio:'',
        checkList:[],
        listData:[],
        secondListData:[],
        saveSelectedMsg:null,
        saveMsg:null,
        saveData:null,
    };
  },
  methods: {
    getCloseMsg(){
      if(this.onlyOne){
           this.$store.commit('getIndexSelectedData',this.saveData);
          this.$store.commit('getIndexSelectedMsg',this.saveMsg);
        
      }else{
        
           this.$store.commit('getselectedData',this.saveData);
          this.$store.commit('getSelectedMsg',this.saveMsg);
        
      }
    },
    getMsg(){
      let a=this.listData.find(s => s.Id == this.radio);
        if(this.onlyOne){
          this.$store.commit('getIndexSelectedData',this.secondListData);
          this.$store.commit('getIndexSelectedMsg',a);
        }else{
          this.saveSelectedMsg=a;
          // this.$store.commit('getselectedData',this.secondListData);
          // this.$store.commit('getSelectedMsg',a);
          this.$store.commit('getselectedData',this.secondListData);
          this.$store.commit('getSelectedMsg',this.saveSelectedMsg);
        }
    },
    resetEmpty(){
      this.getList();
    },
    handleRadio(val){
      let a=this.listData.find(s => s.Id == val);
      this.secondListData=a.children;
      if(!this.secondListData){
        this.secondListData=[];
      }
      this.secondListData.forEach(v1 => {
        v1.checkList=[];
      })
      this.secondListData=JSON.parse(JSON.stringify(this.secondListData));
      if(this.onlyOne){
        this.$store.commit('getIndexSelectedData',this.secondListData);
        this.$store.commit('getIndexSelectedMsg',null);
      }else{
        this.saveSelectedMsg=null;
        // this.$store.commit('getselectedData',this.secondListData);
        // this.$store.commit('getSelectedMsg',null);
      }
    },
    handleCheck(val,list){
      if(list.length<=5){
        this.secondListData=JSON.parse(JSON.stringify(this.secondListData));
        let num=0;
        this.secondListData.forEach(v => {
          num+=v.checkList.length;
        })
        this.$emit('addNum',num);
      }else{
        list.pop();
      }
      this.$store.commit('getIndexSelectedData',this.secondListData);
    },
    getList(){
      this.loading=true;
      equipmentSetting.getListPage({"PageIndex":1,"PageSize": 9999,}).then(res => {
        this.loading=false;
        this.listData = listToTreeSelect(res.Items)
        this.listData.forEach(v => {
          if(v.children){
            v.children.forEach(v1 => {
              v1.checkList=[];
            })
          }
        })
        if(this.onlyOne){
          if(this.indexSelectedMsg){
            this.radio=this.indexSelectedMsg.Id;
            this.secondListData=this.indexSelectedData;
          }else{
            this.secondListData=this.listData[0].children;
            this.radio=this.listData[0].Id;
          }
        }else{
          if(this.selectedMsg){
            this.radio=this.selectedMsg.Id;
            this.secondListData=this.selectedData;
          }else{
            this.secondListData=this.listData[0].children;
            this.radio=this.listData[0].Id;
          }
        }
      }).catch(err => {
        this.loading=false;
      })
    }
  }
};
</script>
<style lang="scss" scoped>
  .nav{
    margin-bottom: 8px;
    display: flex;
    >span{
      font-weight: bold;
      width:100px;
      text-align: right;
    }
  }
  main{
    ul{
      li{
        display: flex;
        margin-bottom:8px;
        >span{
          font-weight: bold;
          min-width: 174px;
          text-align: right;
        }
      }
    }
  }
</style>