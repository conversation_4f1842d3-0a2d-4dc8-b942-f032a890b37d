<template>
    <div>
        <app-dialog title="人员排班" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1008"
        >
            <template slot="body">
                <schedules-table ref="schTab" key="schedules-table-1" :tableHeight="500" :month='month' :timecardDepartmentId='timecardDepartmentId' v-bind="$attrs" :dialogStatus='dialogStatus'></schedules-table>
                <!-- <div style="margin-top: 10px;">
                </div> -->
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" v-show="editable" :disabled="false" text='保存'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as timecardDepartment from "@/api/personnelManagement/timecardDepartment"
import dayjs from 'dayjs'
export default {
    name: "schedules-dialog",
    directives: {},
    components: {
        schedulesTable: () => import('./schedulesTable')
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑  
        editable() {
            return this.dialogStatus != "detail";
        },
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        timecardDepartmentId: {
            type: String,
            default: ''
        },
        //默认当前月份 （2021-03）
        month: {
            type: String,
            default: `${dayjs().year()}-${dayjs().month() + 1}`
        },
        

    },
    watch: {
        // "$attrs.dialogFormVisible"(val) {
        //   if (val) {
        //     // this.resetFormData();
            


        //     if (this.dialogStatus != "create" && this.empIds && this.empIds.length > 0) {

        //     }
        //   }
        // },
        // currentMonth: {
        //     handler(val) {
        //         let monthTemp = val
        //         this.tabColumnsDynamic = Array.from(Array(dayjs(monthTemp).daysInMonth()), (v,k) => {
        //             let weeks = ['日', '一', '二', '三', '四', '五', '六']
        //             let tempDate = dayjs(`${monthTemp}-${k + 1}`).day()
        //             // label: 日期（几号）
        //             // week: 周几 （1-0）（一-日）
        //             return {
        //                 attr: { prop: `month${k + 1}`, label: `${k + 1}${weeks[tempDate]}`, value: k + 1, week: tempDate, renderHeader: this.renderHeader, width: '36' },
        //                 slot: true
        //             }
        //         })
        //         this.getSchedulesByEmpids();
        //     },
        //     immediate: true
        // },
    },
    created() {
    },
    data() {
        return {
            // currentMonth: this.month,
            // tabColumns: [
            //     {   
            //         attr: { prop: "Idx", label: "序号", fixed: 'left' },
            //         slot: true
            //     },
            //     {   
            //         attr: { prop: "Name", label: "姓名", fixed: 'left' },
            //     },
            //     {   
            //         attr: { prop: "Number", label: "工号", fixed: 'left' },
            //     },
            // ],
            // tabColumnsDynamic: [],
            // // tabColumnsDynamic: Array.from(Array((new Date(2021, 3, 0)).getDate()), (v, k) => {
            // //     return {
            // //         // value: v + 1,
            // //         // label: `${v + 1}月`,

            // //         attr: { prop: `month${v + 1}`, label: `${v + 1}月` },
            // //     }
            // // }),
            // isClear: false,
            // disabledBtn: false,
            // loading: false,
            // rules: {
                
            // },
            // schedules: JSON.parse(JSON.stringify(this.oldSchedules)) || [],
            // formData: {
            // },
        };
    },
    methods: {
        //   prevMonth() {
        //       let temp = dayjs(this.currentMonth).subtract(1, 'month').format('YYYY-MM')
        //       this.currentMonth = temp
        //   },
        //   nextMonth() {
        //       let temp = dayjs(this.currentMonth).add(1, 'month').format('YYYY-MM')
        //     this.currentMonth = temp
        //   },
        //   handleToggleType(cellData) {
        //       if(cellData) {
        //           cellData.DateType = cellData.DateType == 1 ? 2 : 1
        //       }
        //   },
        //   //判断单元格里面是排班数据是否被修改
        //   isChanged(cellData) {
        //       if(cellData) {
        //           return cellData.DateType != cellData.OldDateType
        //       }
        //       return false
        //   },
        //   getCellData(scheduleList, date) {
        //       if(scheduleList && scheduleList.length > 0 && date) {
        //           return scheduleList.find(s => dayjs(s.Date).format('YYYY-MM-DD') == dayjs(date).format('YYYY-MM-DD'))
        //       }
        //       return null
        //   },

        // resetFormData() {
        //   this.formData = {
        //     Id: "",
        //     ProductName: "", //产品名称
        //     ProductModel: "", //产品型号
        //     Remark: "", //产品描述
        //     ProductType: 1,
        //     RelatedEmployee: []
        //   };
        // },
        async createData() {
            let datas = this.$refs.schTab.getDatas()

            let month = this.$refs.schTab.$data.currentMonth

            let postDatas = JSON.parse(JSON.stringify(datas))

            let result = await this.whetherIllegalValue({
                List: postDatas,
                Year: dayjs(month).year(),
                Month: dayjs(month).month() + 1
            })
            //存在非法数据
            if(result) {
                this.$confirm(`检测到有人员应出勤天数异常，是否继续?`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$refs.appDialogRef.createData(postDatas);
                })
            } else {
                this.$refs.appDialogRef.createData(postDatas);
            }
        },
        //检测是否有非法值（true：表示有；false：没有）
        async whetherIllegalValue(datas) {
            let result = false
            let postDatas = JSON.parse(JSON.stringify(datas))
            postDatas.TimecardDepartmentId = this.timecardDepartmentId

            await timecardDepartment.whetherIllegalValue(postDatas).then(res => {
                result = res
            })
            return result
        },
        /**
         * 自定义表头
         */
        // renderHeader(h, { column }) {
        //     return h("span", {
        //         style: column.label.indexOf('日') > -1 || column.label.indexOf('六') > -1 ? "color: #F59A23" : ""
        //     }, column.label);
        // },
        //根据员工编号获取排班数据（覆盖老的排班数据）
        /**
         * oldSchedules——老的排班集合
         * newSchedules——新的排班集合（改函数中获取）
         * 
         * 如果 oldSchedules 的用户不存在于 newSchedules 中，表示用户已经被删除，则 oldSchedules 中需要删除该部分用户数据（所有——不按月份）
         * 如果 newSchedules 的用户不存在于 oldSchedules 中，表示新增了需要排班的用户，需要添加到 oldSchedules 中（按月份）
         * 
         */
        // getSchedulesByEmpids() {
        //     // let mockDatas = [
        //     //     {Number: '000110', Name: '张三', DateList: [
        //     //         {Date:"2021-03-12", DateType: 2},
        //     //         {Date:"2021-03-13", DateType: 2},
        //     //         {Date:"2021-03-14", DateType: 1},
        //     //         {Date:"2021-03-15", DateType: 1},
        //     //         {Date:"2021-03-16", DateType: 1},
        //     //     ]}
        //     // ]
            
        //     let postDatas = {
        //         TimecardDepartmentId: this.timecardDepartmentId,
        //         EmployeeIdList: this.empIds,
        //         Year: dayjs(this.currentMonth).year(),
        //         Month: dayjs(this.currentMonth).month() + 1
        //     }
        //     this.loading = true
        //     timecardDepartment.getTimecardScheduling(postDatas).then(res => {
        //         this.loading = false
        //         if(res && res.length > 0) {
        //             res = res.map(s => {
        //                 s.DateList.map(n => {
        //                     n.OldDateType = n.DateType
        //                     n.Date = dayjs(n.Date).format('YYYY-MM-DD')
        //                     return n
        //                 })
        //                 return s
        //             })

        //             //需要保留的老数据（用户不存在新数据里面）
        //             let oldDatas = this.schedules.filter(s => !!this.empIds.find(n => n == s.EmployeeId))
        //             //需要增加的新用户
        //             let newUsers = res.filter(s => !oldDatas.find(n => n.EmployeeId == s.EmployeeId)) || []
        //             let newDatas = JSON.parse(JSON.stringify(newUsers)).map(s => {
        //                 s.DateList = []
        //                 return s
        //             })

        //             oldDatas = oldDatas.concat(newDatas)

        //             //为每个用户补充新的排班数据
        //             oldDatas.forEach(u => {
        //                 let newEmpObj = res.find(s => s.EmployeeId == u.EmployeeId)
        //                 if(newEmpObj) {
        //                     let newSchedules = (newEmpObj.DateList || []).filter(s => !u.DateList.find(n => n.Date != s.Date)) || []
        //                     u.DateList = u.DateList.concat(newSchedules)
        //                 }
        //             });

        //             this.schedules = oldDatas
        //         }
        //     }).catch(err => {
        //         this.loading = false
        //     })



        // //   this.loading = true
        // //   prod.detail({ empIds: this.empIds }).then(res => {
        // //     this.loading = false
        // //     this.formData = Object.assign({}, this.formData, res);
        // //   }).catch(err => {
        // //     this.loading = false
        // //   });
        // },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>

<style lang='scss' scoped>
.wrapper{
    user-select: none;
}
.tip-1{
    color: #409eff;
}
.tip-2{
    color: #70b603;
}

.tip-changed{
    position: absolute;
    height: 6px;
    width: 6px;
    background: red;
    top: 0;
    right: 0;
    border-radius: 50%;
}

</style>