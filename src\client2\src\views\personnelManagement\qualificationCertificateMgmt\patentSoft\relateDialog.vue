<template>
    <div>
        <app-dialog title="关联产品管理" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="600">
            <template slot="body">
                <div class="bg-white __dynamicTabContentWrapper" v-loading='listLoading'>
                    <div class="pageWrapper __dynamicTabWrapper">
                        <app-table ref="mainTable"
                        :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="true"
                        :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="120"
                        :loading="listLoading"
                        :tab-columns="tabColumns"
                        :tab-datas="tabDatas"
                        :tab-auth-columns="tabAuthColumns"
                        :startOfTable="startOfTable" 
                        >
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form style="padding-top: 0;" :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                    <template slot="Name">
                                        <el-input style="width: 100%;" placeholder="搜索产品名称" @clear='getList' v-antiShake='{
                                                time: 300,
                                                callback: () => {
                                                    getList()
                                                }
                                            }' clearable v-model.trim="listQuery.Name"></el-input>
                                    </template>
                                    <!-- 表格批量操作区域 -->
                                    <template slot="btnsArea">
                                        <el-button type="primary" @click="handleDialog('create')">添加产品</el-button>
                                    </template>
                                </app-table-form>
                            </template>

                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <!-- 编辑 -->
                                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row, 'update')" :type="1"></app-table-row-button>
                                <!-- 删除 -->
                                <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <!-- <app-button :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button> -->
            </template>
        </app-dialog>

        <create-product-dialog
            @closeDialog='closeDialog' 
            @saveSuccess='handleSaveSuccess'
            :dialogFormVisible='createProdDialogFormVisible'
            :dialogStatus='createProdDialogStatus' 
            :id="id"
        ></create-product-dialog>
    </div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import createProductDialog from "./createProductDialog"
import * as classify from "@/api/classify";
import busMixins from './mixins'
export default {
    name: "",
    directives: {},
    components: {
        createProductDialog,
    },
    mixins: [indexPageMixin, busMixins],
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.handleFilter()
                }
            },
            immediate: true
        }
    },
    created() {
    },
    data() {
        return {
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Name", label: "", mainCondition: true },
            ],
            tabColumns: [
                { attr: { prop: "Name", label: "关联产品", showOverflowTooltip: true} },
            ],
            tabDatas: [],
            total: 0,


            isClear: false,
            disabledBtn: false,
            formData: {
                Id: "",
                ProductName: "", //产品名称
                ProductModel: "", //产品型号
                Remark: "", //产品描述
                EmployeeIds: '',// 负责人
                ImplementPrincipalEmployee: [],// 负责人
                ProductType: 1,
                RelatedEmployee: []
            },

            createProdDialogFormVisible: false,
            createProdDialogStatus: 'create',
            id: '',

        };
    },
    methods: {
        onResetSearch() {
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        getList() {
            let self = this;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas.BusinessType = this.businessType
            self.listLoading = true;
            classify.getListPage(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.id = optType === 'create' ? '' : row.Id;
            this.createProdDialogStatus = optType
            this.createProdDialogFormVisible = true
        },
        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let postDatas = { ids: [rows.Id], businessType: this.businessType }
                classify.del(postDatas).then(() => {
                    this.getList();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                });
            });
        },
        // 产品管理区域
        handleDialog(activeName) {
            this.createProdDialogStatus = activeName;
            this.createProdDialogFormVisible = true;
        },
        closeDialog() {
            this.createProdDialogFormVisible = false
        },
        handleSaveSuccess(isContinue) {
            // this.listQuery.PageIndex = 1
            this.getList()
            if(!isContinue) {
                this.closeDialog()
            }
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    }
};
</script>

<style lang='scss' scoped>
.wrapper{
  display: flex;
  .lft{
    border-right: 1px solid #EBEEF5; 
    padding-right: 10px; 
    width: 600px;
  }
  .rgt{
     flex: 1;
  }
}
</style>