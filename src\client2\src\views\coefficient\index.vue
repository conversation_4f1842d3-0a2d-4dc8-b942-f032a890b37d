<template>
    <div class="app-container">
        <div class="bg-white">
            <page-title title="工作系数配置" :subTitle="['任务系数管理页面']"></page-title>
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas"
                :tab-auth-columns='tabAuthColumns' :isShowAllColumn='true' :loading="listLoading"
                @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn='false' :startOfTable='startOfTable'
                @cell-mouse-enter='handleCellMouseEnter' @cell-mouse-leave='handleCellMouseLeave' :multable='false'>
                <template slot='EmployeeInfo' slot-scope="scope">
                    {{ scope.row.EmployeeInfo.Name }} ({{ scope.row.EmployeeInfo.Number }})
                </template>
                <template slot='Month1' slot-scope="scope">
                    <span v-if="scope.row.Month1 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month1 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 1, scope.row.Month1)"></i>
                    </span>
                </template>
                <template slot='Month2' slot-scope="scope">
                    <span v-if="scope.row.Month2 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month2 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 2, scope.row.Month2)"></i>
                    </span>
                </template>
                <template slot='Month3' slot-scope="scope">
                    <span v-if="scope.row.Month3 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month3 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 3, scope.row.Month3)"></i>
                    </span>
                </template>
                <template slot='Month4' slot-scope="scope">
                    <span v-if="scope.row.Month4 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month4 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 4, scope.row.Month4)"></i>
                    </span>
                </template>
                <template slot='Month5' slot-scope="scope">
                    <span v-if="scope.row.Month5 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month5 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 5, scope.row.Month5)"></i>
                    </span>
                </template>
                <template slot='Month6' slot-scope="scope">
                    <span v-if="scope.row.Month6 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month6 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 6, scope.row.Month6)"></i>
                    </span>
                </template>
                <template slot='Month7' slot-scope="scope">
                    <span v-if="scope.row.Month7 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month7 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 7, scope.row.Month7)"></i>
                    </span>
                </template>
                <template slot='Month8' slot-scope="scope">
                    <span v-if="scope.row.Month8 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month8 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 8, scope.row.Month8)"></i>
                    </span>
                </template>
                <template slot='Month9' slot-scope="scope">
                    <span v-if="scope.row.Month9 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month9 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 9, scope.row.Month9)"></i>
                    </span>
                </template>
                <template slot='Month10' slot-scope="scope">
                    <span v-if="scope.row.Month10 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month10 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 10, scope.row.Month10)"></i>
                    </span>
                </template>
                <template slot='Month11' slot-scope="scope">
                    <span v-if="scope.row.Month11 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month11 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 11, scope.row.Month11)"></i>
                    </span>
                </template>
                <template slot='Month12' slot-scope="scope">
                    <span v-if="scope.row.Month12 === null">
                        --
                    </span>
                    <span v-else>
                        <span>{{ scope.row.Month12 }}</span>
                        <i v-if="showBtnEdit" style="display: none;" class="el-icon-edit"
                            @click="handleUpdate(scope.row, 12, scope.row.Month12)"></i>
                    </span>
                </template>

                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter'
                        @onReset='onResetSearch'>
                        <template slot='Years'>
                            <el-select v-model="listQuery.Years" placeholder="" style="width: 100%;">
                                <el-option v-for="item in years" :key="item" :label="item" :value="item">
                                </el-option>
                            </el-select>
                        </template>
                        <template slot='Employee'>
                            <emp-selector :multiple='false' :showType='2' key='service-users'
                                :list='listQuery.Employee' @change='handleChangeUsers'></emp-selector>
                        </template>
                    </app-table-form>
                </template>

                <!-- 表格批量操作区域 -->
                <template slot="btnsArea">
                    <permission-btn moduleName="emp" v-on:btn-event="onBtnClicked"></permission-btn>
                </template>

                <!-- 表格行操作区域 -->
                <!-- <template slot-scope="scope">
                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type='1'></app-table-row-button>
                    <app-table-row-button v-if="rowBtnIsExists('btnDetail')" @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
                </template> -->
            </app-table>

            <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>

        <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" :title="textMap[dialogStatus]"
            :visible.sync="dialogFormVisible" :close-on-click-modal='false' :append-to-body='true'>
            <el-form :rules="rules" ref="dataForm" :model="temp" label-position="right" label-width="100px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item :label="'员工'">
                            {{ temp.EmployeeInfo.Name }} ({{ temp.EmployeeInfo.Number }})
                        </el-form-item>
                        <el-form-item :label="'月份'" prop="Month">
                            {{ temp.Month }}
                        </el-form-item>
                        <el-form-item :label="'工作系数'" prop="Coefficients">
                            <el-input-number v-model="temp.Coefficients" :precision="1" :step="0.1" :min="0"
                                :max="1"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer">
                <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
                <el-button size="mini" v-if="!editable" type="primary" :loading="postLoading" @click="createData">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import * as coe from '@/api/coefficient'
    // import waves from '@/directive/waves' // 水波纹指令
    // import Sticky from '@/components/Sticky'
    import elDragDialog from '@/directive/el-dragDialog'
    import EmpSelector from '../common/empSelector'
    import indexPageMixin from '@/mixins/indexPage'
    export default {
        name: 'coefficient',
        components: {
            // Sticky,
            EmpSelector,
        },
        directives: {
            // waves,
            elDragDialog
        },
        mixins: [indexPageMixin],
        computed: {
            showBtnEdit() {
                return this.rowBtnIsExists('btnEdit')
            }
        },
        watch: {
            'listQuery.Years'(val) {
                if(val) {
                    this.getList()
                }
            }
        },
        data() {
            return {
                listQuery: {
                    Years: '',
                    Employee: []
                },
                multipleSelection: [],
                tableSearchItems: [
                    { prop: 'Years', label: '年份' },
                    { prop: 'Employee', label: '员工' },
                ],
                years: [
                    new Date().getFullYear() - 2,
                    new Date().getFullYear() - 1,
                    new Date().getFullYear()
                ],
                tabColumns: [
                    {
                        attr: { prop: 'EmployeeInfo', label: '员工', width: '120' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month1', label: '一月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month2', label: '二月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month3', label: '三月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month4', label: '四月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month5', label: '五月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month6', label: '六月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month7', label: '七月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month8', label: '八月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month9', label: '九月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month10', label: '十月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month11', label: '十一月' },
                        slot: true
                    },
                    {
                        attr: { prop: 'Month12', label: '十二月' },
                        slot: true
                    },
                ],
                tabDatas: [],
                listLoading: false,
                postLoading: false,
                total: 0,
                textMap: {
                    update: '编辑',
                    create: '添加'
                },
                dialogFormVisible: false,
                rules: {
                    Coefficients: { fieldName: '工作系数', rules: [{ required: true }] },
                },
                temp: {
                    EmployeeInfo: { //员工信息
                        Name: '',
                        Number: '',
                    },
                    Years: 0,
                    Month: 1,//月份
                    Coefficients: 0, //工作系数
                },
                statusOptions: [
                    {
                        key: true,
                        display_name: '有效'
                    }, {
                        key: false,
                        display_name: '无效'
                    }],
            }
        },
        created() {
            this.listQuery.Years = new Date().getFullYear()
            this.listQuery.EmployeeId = ''
            this.rules = this.initRules(this.rules)
            this.getList()
        },
        mounted() {

        },
        methods: {
            onResetSearch() {
                this.listQuery = {
                    PageIndex: this.listQuery.PageIndex,
                    PageSize: this.listQuery.PageSize,
                    Years: new Date().getFullYear()
                };
            },
            resetTemp() {
                this.temp = {
                    EmployeeInfo: { //员工信息
                        Name: '',
                        Number: '',
                    },
                    Month: 1,//月份
                    Coefficients: 0, //工作系数
                }
            },
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            onBtnClicked: function (domId) {
                // console.log('you click:' + domId)
                switch (domId) {
                    case 'btnAdd':
                        this.handleCreate()
                        break
                    case 'btnEdit':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行编辑',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0])
                        break
                    case 'btnDetail':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行查看',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0], 'detail')
                        break;
                    case 'btnDel':
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: '至少删除一个',
                                type: 'error'
                            })
                            return
                        }
                        this.handleDelete(this.multipleSelection)
                        break
                    default:
                        break
                }
            },
            getList() {
                this.listLoading = true
                let postDatas = JSON.parse(JSON.stringify(this.listQuery))
                if (this.listQuery.Employee && this.listQuery.Employee.length > 0) {
                    postDatas.EmployeeId = this.listQuery.Employee[0].EmployeeId
                    delete postDatas.Employee
                }
                coe.getList(postDatas).then(response => {
                    this.tabDatas = response.Items
                    this.total = response.Total
                    this.listLoading = false
                }).catch(err => {
                    this.listLoading = false
                })
            },
            handleCellMouseEnter(row, column, cell, event) {
                $(cell).find('i').css({ "display": "inline-block" })
            },
            handleCellMouseLeave(row, column, cell, event) {
                $(cell).find('i').css({ "display": "none" })
            },
            handleFilter() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCreate() { // 弹出添加框
                this.resetTemp()
                this.dialogStatus = 'create'
                this.dialogFormVisible = true
                this.$nextTick(() => {
                    this.$refs['dataForm'].clearValidate()
                })
            },
            createData() { // 保存提交
                let self = this;
                self.postLoading = true
                this.$refs['dataForm'].validate((valid) => {
                    if(!valid) {
                        self.postLoading = false
                    }
                    if (valid) {
                        let formData = JSON.parse(JSON.stringify(self.temp))
                        formData.EmployeeId = formData.EmployeeInfo.EmployeeId
                        coe.edit(formData).then((response) => {
                            // 需要回填数据库生成的数据
                            self.postLoading = false
                            self.dialogFormVisible = false
                            self.$notify({
                                title: '成功',
                                message: '保存成功',
                                type: 'success',
                                duration: 2000
                            })
                            this.getList()
                        }).catch(err => {
                            self.postLoading = false
                        })
                    }
                })
            },
            handleUpdate(row, month, valOfMonth) { // 弹出编辑框
                this.dialogFormVisible = true
                this.dialogStatus = 'update'
                this.temp = {
                    EmployeeInfo: row.EmployeeInfo,
                    Years: row.Years,
                    Month: month,
                    Coefficients: valOfMonth
                }
            },
            handleChangeUsers(users) {
                if (users && users.length > 0) {
                    this.listQuery.Employee = [users[0]]
                } else {
                    this.listQuery.Employee = []
                }
            },
            // handleDelete (rows) { // 多行删除
            //     let ids = []
            //     if(_.isArray(rows)){
            //         ids = rows.map(u => u.EmployeeId)
            //     }else{
            //         ids.push(rows.EmployeeId)
            //     }            
            //     this.$confirm('是否确认删除?', '提示', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '取消',
            //         type: 'warning'
            //     }).then(() => {
            //         emps.del(ids).then(() => {
            //             this.$notify({
            //                 title: '成功',
            //                 message: '删除成功',
            //                 type: 'success',
            //                 duration: 2000
            //             })
            //             this.getList()
            //         })
            //     })
            // },
        }
    }
</script>

<style scoped>
    .sel-ipt,
    .dat-ipt {
        width: 100%;
    }

    .avatar {
        width: 68px;
        height: 68px;
    }

    .tip-avatar {
        width: 140px;
        height: 140px;
    }

    .avatar,
    .tip-avatar {
        border-radius: 50%;
    }

    /* .cus_wdt{
    width: 200px;
} */
</style>