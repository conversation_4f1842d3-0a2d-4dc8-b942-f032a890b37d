<template>
<div class="news-wrapper">

    <!-- <el-button @click="back" icon='arrow-left' class="pan-back-btn">返回</el-button> -->
    <!-- <div class="opt">
        <i class="el-icon-back" @click="back">返回</i>
    </div> -->
    <page-title :showBackBtn='true' @goBack="back">
        <div slot="def">
            
        </div>
    </page-title>
    <div style="display:flex;height:calc(100% - 40px);overflow-y: auto;">
        <div style="width:65%; padding-left: 10px;" v-loading="loading">
            <div class="news-container">
                <header style="font-size: 25px;padding-top: 5px;">{{ newDetail.NewsTitle }}</header>
                <div style="padding-left: 20px;padding-bottom: 5px;">
                    <el-tag v-if="newDetail.IsChoiceness">精选</el-tag>
                     <el-tag style="margin-left: 5px;">{{newDetail.NewsType | newsTypeFilter}}</el-tag>
                    <!-- <el-tag>{{newDetail.CreateEmployee | nameFilter}}</el-tag>&nbsp;&nbsp; -->
                    <span style="margin-left: 5px;">{{newDetail.Author}}</span>&nbsp;&nbsp;
                    <span>{{newDetail.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div class="hr" style="margin: 0px;">
                    <span class="hrl"></span>
                    <span class="hrc"></span>
                    <span class="hrr"></span>
                </div>
                <div class="divUeditor ql-editor" v-html="newDetail.NewsContent"></div>
            </div>

            <hr style="background-color: #e1e1e1;height: 1px;border: none;" />

            <!-- 评论区 -->
            <div v-if="!this.newDetail.IsDraft">
                <div style="padding-left: 20px;">
                    <b style="font-size:18px;">评论({{commentListCount}})</b>
                    <br />

                    <div style="margin-top: 20px;" v-for="comment in commentList" :key="comment.id">
                        <span>{{comment.CreateEmployeeName}}</span> &nbsp;&nbsp;
                        <span>{{comment.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss')}}</span>
                        <p style="word-wrap: break-word;word-break: break-all;" v-html="comment.CommentContent"></p>
                        <hr style="background-color: #e1e1e1;height: 1px;border: none;" />
                    </div>
                </div>

                <div style="margin-top: 20px;height: 200px;padding-left: 20px;">
                    <b style="font-size:18px">我要评论</b>
                    <p>
                        <el-input type="textarea" :rows="5" maxlength="500" placeholder="请输入内容" v-model="commentContent">
                        </el-input>
                        <span style="float:right;margin-top: 14px;">
                            <el-button @click="handleClear()">清空</el-button>
                            <app-button @click="handleSubmit()" text="提交"></app-button>
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <el-row style="margin-left: 6%; padding-top: 10px;flex:1;" v-loading='newsTrendsListLoading'>
            <relate-list :list='newsTrendsDatas' @click="handleNewsTrendsReview"></relate-list>
        </el-row>
    </div>
</div>
</template>

<script>
// import EditorBar from '@/components/QuillEditor/index.vue'
// import EditorBar from '../../../components/WangEditor/index.vue'
import * as newsTrends from '@/api/informationCenter/newsTrends'
import relateList from '../common/relateList'
import {
    newsTypeEnum
} from "../enums";
import dayjs from 'dayjs'
export default {
    name: 'noti-detail',
    components: {
        // EditorBar
        relateList,
    },
    data() {
        return {
            newsTrendsListQuery: {
                id: null,
                NewsType: 0,
                IsShow: 1,
                PageSize: 10,
                WebOrApp: "HomePage"
            },
            id: null,
            newsTrendsListLoading: false,
            newsTrendsDatas: [],
            isClear: false,
            loading: false,
            newsTypeEnum: newsTypeEnum,
            newDetail: {},
            commentContent: "",
            commentList: [],
            commentListCount: 0
        }
    },
    created() {
        this.getNewDetail();
        this.getNewsTrendsList()
    },
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
        newsTypeFilter(status) {
            const statusObj = newsTypeEnum.find(s => s.value == status);
            if (statusObj) {
                return statusObj.label;
            }
            return "";
        }
    },
    methods: {
        handleNewsTrendsReview(row) {
            //查看详情
            newsTrends.detail({
                id: row.Id
            }).then(res => {
                this.newDetail = res
                if (res != null) {
                    //获取评论集合
                    newsTrends.getComment({
                        businessId: row.Id
                    }).then(res => {
                        this.commentList = res
                        this.commentListCount = res.length;
                    })

                }
                //获取
                this.newsTrendsListLoading = true
                this.newsTrendsListQuery.id = row.Id
                this.newsTrendsListQuery.NewsType = row.NewsType;
                newsTrends.getList(this.newsTrendsListQuery).then(response => {
                    this.newsTrendsListLoading = false
                    this.newsTrendsDatas = response.Items.map(s => {
                        s.Text = s.NewsTitle
                        return s
                    })
                })
            })
            this.id = row.Id
        },
        //获取新闻动态
        getNewsTrendsList() {
            this.newsTrendsListLoading = true
            let id = this.$route.params && this.$route.params.id
            let newsType = this.$route.query.newsType
            this.newsTrendsListQuery.id = id;
            this.newsTrendsListQuery.NewsType = newsType;
            newsTrends.getList(this.newsTrendsListQuery).then(response => {
                this.newsTrendsDatas = response.Items.map(s => {
                    s.Text = s.NewsTitle
                    return s
                })
                this.newsTrendsListLoading = false
            })
        },
        handleClear() {
            this.commentContent = ""
        },
        //获取评论集合
        getCommentList() {
            let id = this.$route.params && this.$route.params.id
            newsTrends.getComment({
                businessId: id
            }).then(res => {
                this.commentList = res
                this.commentListCount = res.length;
            })
        },

        //提交评论
        handleSubmit() {
            if (this.commentContent == "") return;
            let cid = "";
            if (this.id != null) {
                cid = this.id
            } else {
                cid = this.$route.params && this.$route.params.id
            }
            var obj = {
                CurrentBusinessId: cid,
                CommentContent: this.commentContent,
                Type: 6,
            };

            newsTrends.addComment(obj).then(() => {
                this.$notify({
                    title: '成功',
                    message: '操作成功',
                    type: 'success',
                    duration: 2000
                })
                this.handleClear();
                //      this.getCommentList();
                //获取评论集合
                newsTrends.getComment({
                    businessId: cid
                }).then(res => {
                    this.commentList = res
                    this.commentListCount = res.length;
                })
            })

        },

        getNewDetail() {
            let id = this.$route.params && this.$route.params.id
            this.loading = true;
            newsTrends.detail({
                id: id
            }).then(res => {
                this.loading = false;
                this.newDetail = res
                if (res != null) {
                    this.getCommentList();
                }
            }).catch(()=>{
                this.loading = false;
            })
        },
        back() {
            this.$router.go(-1)
        }
    }
}
</script>

<style lang="scss" scoped>
.news-wrapper {
    padding: 60px 40px 0;
    overflow: hidden;
    background: #ffffff;
    margin: 10px;
    position: absolute;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    background: white;
}

.news-container {
    width: 100%;
    padding-bottom: 20px;
    /* text-align: center; */
}

img,
video {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-top: 0.4rem;
}

header {
    font-size: 36px;
    color: #373c41;
    font-weight: bold;
    /* text-align: center; */
    margin-bottom: 10px;
}

aside {
    font-size: 16px;
    color: #8d8f91;
    margin-top: 30px;
}

article p {
    font-size: 0.42rem;
    color: #373c41;
    margin-top: 0.4rem;
    line-height: 0.67rem;
    text-indent: 2em;
}

.hr {
    width: 100%;
    margin: 30px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hr span {
    background-color: #e3e3e5;
}

.hrl,
.hrr {
    width: calc(50% - 5px);
    height: 1px;
}

.hrc {
    width: 3px;
    height: 3px;
    border-radius: 3px;
}

@media screen and (min-width: 1080px) {
    body {
        width: 1080px;
        margin: 0 auto;
    }
}

.divUeditor {
    overflow: hidden;
    display: block;
    width: 100%;
    min-width: 90%;
    position: relative;
    word-wrap: break-word;
    padding: 12px 20px;
    padding-bottom: 20px;
}

.divUeditor img {
    border: 0;
    max-width: 100%;
    margin-top: 10px;
}

.down-wrapper a {
    color: #409eff;
    line-height: 140%;
}

.down-wrapper p {
    margin: 0;
    margin-bottom: 10px;
}

.opt {
    top: 0;
    left: 0;
    right: 0;
    position: absolute;
    // cursor: pointer;

    // i {
    //     font-size: 14px;
    // }
}

// .opt:hover {
//     color: rgb(64, 158, 255);
// }
</style>
