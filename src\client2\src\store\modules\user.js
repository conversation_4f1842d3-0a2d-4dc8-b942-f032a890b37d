import {
    // login, 
    logout, getModulesTree, sign
} from "@/api/login";
import { setToken, removeToken } from "@/utils/auth";
import { getUserInfo, setUserInfo, removeUserInfo } from "@/utils/auth";

const user = {
    state: {
        modules: null, //
        userInfo: null
    },
    mutations: {
        SET_MODULES: (state, modules) => {
            state.modules = modules;
        },
        SET_USERINFO: (state, userinfo) => {
            setUserInfo(userinfo);
            state.userInfo = userinfo
        },
    },
    actions: {
        // 登录
        Login({ commit }, userInfo) {
            const username = userInfo.username.trim();
            return new Promise((resolve, reject) => {
                sign(username, userInfo.password, userInfo.enterpriseCode, userInfo.experiencePage).then(response => {
                    const data = response;
                    // let userinfo = {
                    //   username: username,
                    //   nickname: '',
                    //   token: data.token,
                    //   avatar: ''
                    // }

                    // setToken(data.Token);
                    // setUserInfo(data.UserName);
                    setToken(data.Token);

                    let userinfo = {
                        'userid': data.UserId,
                        'username': data.UserName || username,
                        'avatar': data.Avatar || '',
                        'accountid': data.AccountId || '',
                        'employeeid': data.EmployeeId || '',
                        'enterpriseid': data.EnterpriseID || '',
                        'isadmin': data.IsAdmin,
                        'empNumber': data.AccountName,
                        'experience': data.Experience || false, //是否为体验版账号
                    }
                    if (userinfo.employeeid == '********-0000-0000-0000-************') {
                        userinfo.employeeid = ''
                    }

                    commit('SET_USERINFO', userinfo)

                    resolve();
                }).catch(error => {
                    reject(error);
                });
            });
        },

        // 获取用户信息
        // GetInfo({ commit, state }) {
        //   return new Promise((resolve, reject) => {
        //     getInfo(state.token).then(response => {
        //       resolve(response)
        //     }).catch(error => {
        //       reject(error)
        //     })
        //   })
        // },

        // 获取用户模块
        // GetModules({ commit, state }) {
        //     return new Promise((resolve, reject) => {
        //         getModules(state.token).then(response => {
        //             commit("SET_MODULES", response.result);
        //             resolve(response);
        //         }).catch(error => {
        //             reject(error);
        //         });
        //     });
        // },
        // 获取用户模块
        GetModulesTree({ commit, state }) {
            return new Promise((resolve, reject) => {
                getModulesTree().then(response => {
                    commit("SET_MODULES", response);
                    resolve(response);
                }).catch(error => {
                    reject(error);
                });
            });
        },
        // 登出
        LogOut({ commit, state }) {
            return new Promise((resolve, reject) => {
                logout(state.token).then(() => {
                    removeUserInfo();
                    commit("SET_MODULES", []);
                    removeToken();
                    resolve();
                }).catch(error => {
                    reject(error);
                });
            });
        },

        // 前端 登出
        FedLogOut({ commit }) {
            return new Promise(resolve => {
                commit("setBadgeNumber", 0);
                removeUserInfo();
                removeToken();
                resolve();

            });
        }
    }
};

export default user;
