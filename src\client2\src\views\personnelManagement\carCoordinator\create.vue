<!--添加/修改车辆信息-->
<template>
  <div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">

          <el-form-item label="用车单位" prop="CarDepartmentId">
            <treeselect :normalizer="normalizer" class="treeselect-common" :disabled="!editable" :options="DepartmentList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.CarDepartmentId" placeholder="请选择所属单位" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" @input="hadnleChange"></treeselect>
          </el-form-item>

          <el-row style="height:122px;">
            <el-col :span="24">
              <el-form-item label="车辆图片" prop="CarImgPath">
                <app-upload-file :max='1' :fileSize='1024 * 1024 * 10' :value='CoverFileList' :readonly="!editable" @change='handleCoverUpChange' :preview='true'></app-upload-file>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="车牌号" prop="CarNumber">
                <el-input v-model="formData.CarNumber" maxlength="10" type="text" placeholder="请输入车牌号" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="行驶证号码" prop="DrivingLicenseNo">
                <el-input v-model="formData.DrivingLicenseNo" maxlength="30" type="text" placeholder="请输入行驶证号码" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="车辆品牌" prop="CarBrand">
                <el-input v-model="formData.CarBrand" maxlength="20" type="text" placeholder="请输入车辆品牌" :disabled="!editable">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆型号" prop="CarType">
                <el-input v-model="formData.CarType" maxlength="20" type="text" placeholder="请输入车辆型号" :disabled="!editable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="座位数" prop="Seating">
                <el-input-number style="width:100%" v-model="formData.Seating" :min="0" :max="99" label="请输入座位数" :disabled="!editable"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="载重" prop="Truck">
                <el-input v-model="formData.Truck" maxlength="10" type="text" placeholder="请输入载重" :disabled="!editable">
                  <template slot="append">吨</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="司机信息">
                <el-radio v-model="IsDriverEmployee" :disabled="!editable" :label="false">无固定司机</el-radio>
                <el-radio v-model="IsDriverEmployee" :disabled="!editable" :label="true">固定司机</el-radio>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="绑定GPS设备">
                <el-button :disabled="!editable" type="text" @click="() => dialogAccessGps = true">绑定设备</el-button>
                <span v-if="formData.DeviceName">&nbsp;{{ formData.DeviceName }}</span><i @click="handleClearGps" v-if="editable && (formData.DeviceName)" style="cursor: pointer; margin-left: 10px;" class="el-icon-close"></i>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-show="IsDriverEmployee">
            <el-col :span="24">
              <el-form-item label="司机" prop="DriverEmployeeList" :required="IsDriverEmployee">
                <emp-selector :readonly="!editable" :isAutocomplete='true' :showType="2" :multiple="true" placeholder="请选择司机" :list="formData.DriverEmployeeList" @change="handleChangeUsers" :beforeConfirm='handleBeforeConfirm'></emp-selector>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary" :disabled='disabledBtn'></app-button>
      </template>
    </app-dialog>

        <!-- :checkedList="formData.Order ? [formData.Order] : []"  -->
    <gps-selector 
        :isShow="dialogAccessGps" 
        @changed="handleChangeGps" 
        :checkedList='formData.GPSImei ? [
          {GPSImei: formData.GPSImei,
          DeviceName: formData.DeviceName}
        ] : []'
        @closed="() => dialogAccessGps = false" 
        :condition='{}'
        :multiple="false"
    ></gps-selector>
  </div>
</template>

<script> 
import * as carMgt from '@/api/personnelManagement/carCoordinator'


import empSelector from "@/views/common/empSelector";
import { listToTreeSelect } from "@/utils";
import gpsSelector from "../../common/gpsSelector"
export default {
  name: "carCoordinator-create",
  directives: {},
  components: {
    empSelector,
    gpsSelector,

  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    selectDepartmentId: {
      type: String,
      default: ""
    },
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (!val) {
        this.IsDriverEmployee = false;
        this.CoverFileList = [];
        this.DepartmentList = [];
      }
      if (val) {
        this.goOn = false;
        this.resetFormData();
        this.getCarDepartmentList();
      }
    }
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail"
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加车辆";
      } else if (this.dialogStatus == "update") {
        return "编辑车辆";
      } else if (this.dialogStatus == 'detail') {
        return '详情'
      }
      return ''
    },
  },
  /**渲染后 */
  mounted() {
  },
  created() {

  },
  data() {

    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label.split(",")[0],
          id: node.Id,
          children: node.children
        };
      },

      disabledBtn: false,
      goOn: false,
      IsDriverEmployee: false,
      CoverFileList: [],
      DepartmentList: [],

      formLoading: false,
      rules: {
        CarDepartmentId: {
          required: true,
          message: '请选择所属部门',
          trigger: 'change'
        },
        CarNumber: {
          required: true,
          message: '请输入车牌号',
          trigger: 'blur'
        },
        DriverEmployeeList: [{
          validator: (rule, value, callback) => {
            if (this.IsDriverEmployee) {
              if (value != "" && value != null) {
                callback()
              } else {
                callback(new Error("请选择司机"))
              }
            } else {
              callback()
            }
          }
        }],
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        CarDepartmentId: null,
        CarImg: "",
        CarImgPath: "",
        CarNumber: "",
        DrivingLicenseNo: "",
        CarBrand: "",
        CarType: "",
        Seating: 0,
        Truck: "",
        DriverEmployeeId: "",
        DriverEmployeeList: [],

        GPSImei: '',
        DeviceName: '',
      },

      dialogAccessGps: false,
    };
  },
  methods: {

    hadnleChange() {
      this.$refs.formData.validateField("CarDepartmentId");
    },
    handleChangeGps(rows) {
      if (rows && rows.length > 0) {
        this.formData.GPSImei = rows[0].GPSImei;
        this.formData.DeviceName = rows[0].DeviceName;
      } else {
        this.formData.GPSImei = "";
        this.formData.DeviceName = "";
      }
    },
    handleClearGps() {
      this.formData.GPSImei = ''
      this.formData.DeviceName = ''
    },
    handleChangeUsers(users) {
      if (users && users.length > 0) {
        this.formData.DriverEmployeeId = users[0].EmployeeId;
        this.formData.DriverEmployeeList = [users[0]];
      } else {
        this.formData.DriverEmployeeId = "";
        this.formData.DriverEmployeeList = [];
      }
    },
    handleBeforeConfirm(users) {
      if (users && users.length > 1) {
        this.$message({
          message: '司机不得超过1人',
          type: 'error'
        })
        return false
      }
      return true
    },


    resetFormData() {
      let temp = {
        Id: "",
        CarDepartmentId: null,
        CarImg: "",
        CarImgPath: "",
        CarNumber: "",
        DrivingLicenseNo: "",
        CarBrand: "",
        CarType: "",
        Seating: 0,
        Truck: "",
        DriverEmployeeId: "",
        DriverEmployeeList: [],

        GPSImei: '',
        DeviceName: '',
      };
      this.formData = Object.assign({}, this.formData, temp);
      this.IsDriverEmployee = false;
    },

    //获取部门信息下拉框
    getCarDepartmentList() {
      this.formLoading = true;
      carMgt.getDepartmentCondition({}).then(res => {
        var departments = res.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.Name,
            ParentId: item.ParentId
          };
        });
        var departmentList = listToTreeSelect(departments);
        this.DepartmentList = departmentList;
        if (this.selectDepartmentId) {
          this.formData.CarDepartmentId = this.selectDepartmentId;
        }
        if (this.dialogStatus != "create" && this.id) {
          this.$nextTick(() => {
            this.getDetail();
          });
        } else {
          this.formLoading = false;
        }
      });
    },

    //获取详情
    getDetail() {
      carMgt.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        if (this.formData.DriverEmployee) {
          this.IsDriverEmployee = true;
          this.formData.DriverEmployeeList = [this.formData.DriverEmployee];
        }
        this.CoverFileList = [];
        if (this.formData.CarImgPath) {
          this.CoverFileList = [{
            Id: this.formData.CarImg,
            Path: this.formData.CarImgPath
          }];
        } else {
          if (this.dialogStatus == "detail") {
            this.CoverFileList = [{
              Id: '',
              Path: require("../../../assets/images/car.png")
            }]
          } else {
            this.CoverFileList = [];
          }
        }
        this.formLoading = false;
      });
    },


    handleSave() {
      this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      Promise.all([listResult]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        if (!this.IsDriverEmployee) {
          postData.DriverEmployeeId = "";
          postData.DriverEmployeeList = [];
        }
        if (this.dialogStatus == "create") {
          delete postData.Id;
          carMgt.add(postData).then(res => {
            this.disabledBtn = false;
            this.$notify({
              title: '成功',
              message: '创建成功！',
              type: 'success'
            });
            if (this.goOn) {
              this.resetFormData();
              this.$refs['formData'].resetFields();
            }
            this.$emit('saveSuccess', this.goOn);
          }).catch(err => {
            this.disabledBtn = false;
          })
        } else {
          carMgt.edit(postData).then(res => {
            this.disabledBtn = false;
            this.$notify({
              title: '成功',
              message: '编辑成功！',
              type: 'success'
            });
            this.$emit('saveSuccess', false);
          }).catch(err => {
            this.disabledBtn = false;
          })
        }
      }).catch(err => {
        this.disabledBtn = false;
      })
    },

    //选择车辆图片事件
    handleCoverUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.CarImgPath = imgs[0].Path
        this.formData.CarImg = imgs[0].Id
      } else {
        this.formData.CarImg = ''
        this.formData.CarImgPath = ''
      }
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;

  .left {
    flex: 1;
    padding-right: 14px;
  }

  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
</style>
