<!--还车-->
<template>
  <div>
    <app-dialog title="还车" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="500">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">
          <el-row>
            <el-col :span="24">
              <el-form-item label="还车时间" prop="CarReturnTime">
                <el-date-picker style="width:100%" v-model="formData.CarReturnTime" type="datetime" placeholder="请选择还车时间" default-time="00:00:00" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="Remark">
                <el-input v-model="formData.Remark" maxlength="100" type="text" placeholder="请输入备注">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template slot="footer">
        <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" :disabled="buttonLoading" @click="handleSave"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as carRecordMgt from '@/api/personnelManagement/carCoordinatorRecord'
import dayjs from "dayjs";
export default {
  /**名称 */
  name: "return-car",
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    //操作的节点，如果是新增，则为父节点；编辑为当前节点
    obj: {
      type: Object,
      required: true
    }
  },
  /**数据区 */
  data() {
    return {

      currentObj: {},
      /**按钮在执行，不允许点击 */
      buttonLoading: false,

      formLoading: false,
      rules: {
        CarReturnTime: {
          required: true,
          message: '请选择还车时间',
          trigger: 'change'
        }
      },
      labelWidth: "100px",
      formData: {
        Id: "",
        CarReturnTime: dayjs().format('YYYY-MM-DD HH:mm'),
        Remark: "",
      }
    };
  },
  /**计算属性 */
  computed: {
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
      }
    }
  },
  /**渲染前 */
  created() {
  },
  /**渲染后 */
  mounted() {
  },
  /**方法区 */
  methods: {

    resetFormData() {
      let temp = {
        Id: "",
        CarReturnTime: dayjs().format('YYYY-MM-DD HH:mm'),
        Remark: "",
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    handleSave() {
      this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      Promise.all([listResult]).then(valid => {
        let postData = JSON.parse(JSON.stringify(this.formData));
        postData.Id = this.obj.CurrentRecordId;

        carRecordMgt.edit(postData).then(res => {
          this.disabledBtn = false;
          this.$notify({
            title: '成功',
            message: '还车成功！',
            type: 'success'
          });
          this.$emit('saveSuccess', false);
        }).catch(err => {
          this.disabledBtn = false;
        })

      }).catch(err => {
        this.disabledBtn = false;
      })
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>


