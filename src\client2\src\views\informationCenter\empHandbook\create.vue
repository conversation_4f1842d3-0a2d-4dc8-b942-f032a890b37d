<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700" :width='1200'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth" style="margin-right: 10px;">
                <div class="wrapper-page" :style="{display: isShowRange ? 'flex' : ''}">
                    <div :class="isShowRange ? 'lft' : ''">
                        <el-form-item label="手册类型" prop="ClassificationId">
                            <treeselect
                                :append-to-body="true"
                                :noResultsText="noResultsTextOfSelTree"
                                :noOptionsText="noOptionsTextOfSelTree"
                                :normalizer="normalizer"
                                :default-expand-level="3"
                                :options="treeDatas"
                                v-model="formData.ClassificationId"
                                :multiple="false"
                                :show-count="false"
                                :disabled="!editable"
                                placeholder=""
                                zIndex='9999'
                            ></treeselect>
                            <!-- <el-cascader :disabled="!editable" v-model="formData.ClassificationId" :options="treeDatas" :props="{ checkStrictly: true, emitPath: false }" clearable></el-cascader> -->
                        </el-form-item>
                        <el-form-item label="手册名称" prop="Name">
                            <el-input :disabled="!editable" maxlength="50" type="text" v-model="formData.Name"></el-input>
                        </el-form-item>
                        <el-form-item  v-if="dialogStatus != 'detail'" label="手册内容" prop="Content">
                            <editor-bar v-if="editable" :value="formData.Content" @edit="formData.Content = arguments[0]"></editor-bar>
                            <div class="divUeditor ql-editor" v-html="formData.Content" v-if="!editable"></div>
                        </el-form-item>
                        <el-form-item v-if="dialogStatus == 'detail'" label="手册内容" prop="Content">
                            <editor-bar v-if="editable" :value="formData.Content" @edit="formData.Content = arguments[0]"></editor-bar>
                            <div class="divUeditor ql-editor" style="padding-top:7px;" v-html="formData.Content" v-if="!editable"></div>
                        </el-form-item>
                    </div>
                    <div :class="isShowRange ? 'rht' : ''">
                        <el-form-item label="适用范围" prop="ShowRange" v-if="isShowRange">
                            <div class="cl">
                                <el-select :disabled="!editable" class="fl" style="margin-right:10px; width: 120px;" v-model="formData.ShowRange" placeholder="">
                                    <el-option v-for="item in scopeApplication" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <div class="fl" v-if="formData.ShowRange == 2">
                                    <el-button :disabled="!editable" type="text" @click="handleShowTree">选择</el-button>
                                    <!-- <span v-if="departName">{{departName}}</span> -->
                                </div>
                                
                                <emp-selector
                                    class="fl"
                                    v-if="formData.ShowRange == 3"
                                    :readonly="!editable"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :list="formData.ApprovalRangeEmployee"
                                    @change="handleChangePrincipalUsers"
                                ></emp-selector>
                                
                            </div>
                            <ul v-if="formData.ShowRange == 2 && departName && departName.length>0" class="dUl">
                                <li class="omit" :title="dn" v-for="(dn,dnI) in departName" :key="dnI">{{dn}}</li>
                            </ul>
                        </el-form-item>
                        <el-form-item label="相关附件"  v-if="dialogStatus != 'detail'">
                            <app-uploader :readonly='!editable' accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                        </el-form-item>
                        <el-form-item label="相关附件" v-if="dialogStatus == 'detail'">
                            <app-uploader v-if="formData.AttachmentList && formData.AttachmentList.length > 0" style="margin-top:-6px;" :readonly='!editable' accept="all" :fileType="3" :max="10000" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            <span v-else>无</span>
                        </el-form-item>
                    </div>
                </div>

            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable"></app-button>
        </template>
    </app-dialog>

    <v-tree
        v-if="dialogTreeVisible"
        @saveSuccess="handleTreeSaveSuccess"
        @closeDialog="handleTreeCloseDialog"
        :dialogFormVisible="dialogTreeVisible"
        :checkedList='checkedList'>
    </v-tree>
</div>
</template>

<script>
import empSelector from "../../common/empSelector";
import EditorBar from "@/components/QuillEditor/index.vue";
import * as empHandbookClassification from "@/api/informationCenter/empHandbookClassification";
import * as empHandbook from "@/api/informationCenter/empHandbook";
import { vars } from '../../workbench/myWorkbench/vars'
import vTree from '../../personnelManagement/processManagement/tree';
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'

import {
    listToTreeSelect
} from "@/utils";
export default {
    name: "trainsClassifications-create",
    directives: {},
    components: {
        EditorBar,
        empSelector,
        vTree,
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        selectTypeId: {
            type: String,
            default: ""
        },
        isShowRange: {
            type: Boolean,
            default: true
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            this.getTreeDatas();
            if (!val) {
                this.fileList = [];
                this.isContinue = false;
            }
            if (val) {
                this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                    systemDepartment.getListByCondition({}).then(response => { //调用公共组件API获取部门数据
                        this.loading=false;
                        let list = response.map(function (item) {
                            return {
                                Id: item.Id,
                                label: item.DepartmentName,
                                ParentId: item.ParentId,
                                ParentName:item.DepartmentName,
                            }
                        })
                        var orgstmp = JSON.parse(JSON.stringify(list));
                        var tempOrgsTree = listToTreeSelect(orgstmp, undefined, undefined, undefined, 'ParentName'); //将部门数据转换成树形结构
                        this.treeData=this.treeToArray(tempOrgsTree,'children');
                        
                    })
                }
            }
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建员工手册";
            } else if (this.dialogStatus == "update") {
                return "编辑员工手册";
            } else if (this.dialogStatus == "detail") {
                return "员工手册详情";
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);

        var validateShowRange = (rule, value, callback) => {

            if (value == 2) {
                if(this.formData.ApprovalRangeDepartmentId.length<1){
                    callback(new Error("请选择部门!"));
                }else{
                    callback();
                }
            } else if (value == 3) {
                if(!this.formData.ApprovalRangeEmployee) this.formData.ApprovalRangeEmployee=[];
                if(this.formData.ApprovalRangeEmployee.length<1){
                    callback(new Error("请选择自定义人员!"));
                }else{
                    callback();
                }
            } else {
                callback();
            }
        };

        if (!this.rules["ShowRange"]){
            this.rules["ShowRange"] = [];
        }
        this.rules.ShowRange.push({
            validator: validateShowRange,
            trigger: "change"
        });

    },
    data() {
        
        return {
            scopeApplication: vars.scopeApplication,
            normalizer(node) {
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },
            isClear: false,
            treeDatas: [],
            fileList: [], //图像信息[{Id: '', Path: ''}]
            formLoading: false,
            isContinue: false,
            rules: {
                ClassificationId: {fieldName: "手册类型", rules: [{ required: true, trigger: "change"}]},
                Name: {fieldName: "手册名称", rules: [{ required: true }]},
                Content: {fieldName: "手册内容",rules: [{required: true}, {max: 20000, trigger: ["blur", "change"]}]},
                ShowRange: [
                    { required: true, message: '适用范围不能为空', trigger: 'change' },
                ],
            },
            labelWidth: "80px",
            formData: {
                Id: "",
                ClassificationId: null,
                Name: "",
                Content: "",
                AttachmentList: [], // 
                ApprovalRangeEmployee: [],
                ShowRange: 1,
                ApprovalRangeDepartmentId:[],
            },
            dialogTreeVisible: false,
            checkedList:[],
            departName:[],
            treeData:[],
        };
    },
    methods: {
        listToTreeSelect,
        treeToArray(treeData, field) {
            var result = [];
            if(!field) field = "children";
            for(var key in treeData) {
                var obj = treeData[key];
                var clone = JSON.parse(JSON.stringify(obj));
                delete clone[field];
                result.push(clone);
                //
                if(obj[field]) {
                    var tmp = this.treeToArray(obj[field], field);
                    result = result.concat(tmp);
                }
            }
            return result;
        },
        handleChangePrincipalUsers(users) {
            this.formData.ApprovalRangeEmployee = users;
        },
        handleShowTree(){
            this.dialogTreeVisible=true;
        },
        handleTreeCloseDialog(){
            this.dialogTreeVisible=false;
        },
        handleTreeSaveSuccess(d){
            this.checkedList=[];
            this.departName=[];
            this.formData.ApprovalRangeDepartmentId=[];
            if(d.length>0){
                d.forEach(v => {
                    this.formData.ApprovalRangeDepartmentId.push(v.Id);
                    this.checkedList.push(v.Id);
                    this.departName.push(v.ParentName);
                })
                // this.formData.ApprovalRangeDepartmentId=d[0].Id;
                // this.checkedList=d.map(v => v.Id);
                // this.departName=d[0].label;
                this.$refs.formData.validateField('ShowRange',(val) => {
                    return;
                }); 
            }
            this.dialogTreeVisible=false;
        },

        getTreeDatas() {
            empHandbookClassification
            .getAllClassifications(this.classificationListQuery)
            .then(res => {
                if (!res) {
                    res = [];
                }
                this.treeDatas = listToTreeSelect(res,undefined,undefined,{key: 'Id',parentKey: 'ParentID'}, 'Name');
                //如果首次加载问价夹树（没有选中），默认选中根节点
                if (this.dialogStatus == "create") {
                    if(this.selectTypeId != '00000000-0000-0000-0000-000000000000') {
                        this.formData.ClassificationId = this.selectTypeId;
                    }
                }
            })
        },
        //递归删除children为空的属性
        deleteChildrens(res) {
            res.forEach(element => {
                if (element.children.length === 0) {
                    delete element.children;
                } else {
                    this.deleteChildrens(element.children);
                }
            });
        },
        resetFormData() {
            let temp = {
                Id: "",
                ClassificationId: null,
                Name: "",
                Content: "",
                AttachmentList: [], //
                ApprovalRangeEmployee: [],
                ShowRange: 1,
                ApprovalRangeDepartmentId:[],
            };
            this.checkedList = []
            this.departName = []
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            this.formLoading = true;

            empHandbook
                .detail({
                    id: this.id
                })
                .then((res) => {
                    this.formLoading = false;
                    if(res.ShowRange == 3){
                        // res.ApprovalRangeEmployee = res.ApprovalRangeEmployee;
                    }else if(res.ShowRange == 2){
                        this.departName=[];
                        let a=null;
                        res.ApprovalRangeDepartmentIdList.forEach(v => {
                            a=this.treeData.find(s => s.Id == v);
                            this.departName.push(a.ParentName);
                        })
                        
                        this.checkedList=res.ApprovalRangeDepartmentIdList;
                    }

                    this.formData = Object.assign({}, this.formData, res);

                    //绑定所属分类
                    this.formData.TrainsClassificationId = [
                        this.formData.TrainsClassificationId
                    ];
                    this.formData.ApprovalRangeDepartmentId=res.ApprovalRangeDepartmentIdList;
                    //     this.formData.AttachmentList =
                    // res.AttachmentIdList && res.AttachmentIdList.map((s) => s.Id);
                })
                .catch((err) => {
                    this.formLoading = false;
                });

            // .then(res => {
            //     this.formData = Object.assign({}, this.formData, res);
            //     this.formData.PrincipalEmployeeList = res.PrincipalEmployeeList ?
            //         res.PrincipalEmployeeList : [];

            //     //绑定所属分类
            //     this.formData.TrainsClassificationId = [
            //         this.formData.TrainsClassificationId
            //     ];
            //     // //用于定位cascader控件的值
            //     // this.formData.ProductClassificationPath = this.formData.ProductClassificationPath.split(',');

            //     //反向绑定封面图片
            //     // this.fileList = [];
            //     // if (this.formData.ProductCoverPath) {
            //     //     this.fileList = [{
            //     //         Id: this.formData.ProductCover,
            //     //         Path: this.formData.ProductCoverPath
            //     //     }];
            //     // }

            //     this.formLoading = false;
            // });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        createData() {

            let validate = this.$refs.formData.validate();

            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map((s) => s.Id);
                if(postData.ApprovalRangeEmployee && postData.ApprovalRangeEmployee.length > 0) {
                    postData.ApprovalRangeEmployeeIds = postData.ApprovalRangeEmployee.map(s => s.EmployeeId)
                }
                //提交数据保存
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = empHandbook.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = empHandbook.edit(postData);
                }

                result.then(res => {
                    if (this.isContinue) {
                        this.resetFormData();
                        this.$emit("reload");
                    } else {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData();
                    }

                });
            });
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        handleSave() {
            this.createData();
        }
    }
};
</script>

<style lang="scss" scoped>
.wrapper-page {
    
    min-height: 400px;
    .lft {
        flex: 1;
        // border-right: 1px solid red;
        padding-right: 14px;
    }

    .rht {
        width: 40%;
    }
}

.panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
}
</style>
