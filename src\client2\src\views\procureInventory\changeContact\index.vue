<template>
<div class="app-container">
    <!-- <page-title title="在线培训" :subTitle="['在线培训管理页面']"></page-title> -->
    <div class="pageWrapper __dynamicTabContentWrapper">
        <div class="content __dynamicTabWrapper">
            <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading" @sortChagned="handleSortChange"
            :isShowOpatColumn="true" :multable="false" :layoutMode='layoutMode' :startOfTable="startOfTable" :isShowBtnsArea='false' :optColWidth="80">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch"
                    :layoutMode='layoutMode'>
                        <template slot="KeyWords">
                            <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable
                            v-model.trim="listQuery.KeyWords" placeholder="搜索主题/变更原因/申请人/申请部门"></el-input>
                        </template>
                        <template slot="CreateTimeRange">
                            <el-date-picker style="width: 100%;" v-model="listQuery.CreateTimeRange" type="datetimerange"
                            align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd HH:mm"
                            value-format="yyyy-MM-dd HH:mm" :clearable="false"></el-date-picker>
                        </template>
                        <template slot="ApprovalState">
                            <el-select clearable style="width: 100%;" v-model="listQuery.ApprovalState" placeholder="选择审批状态">
                                <el-option v-for="item in approvalStatus.filter(s=>s.value!=4)" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </template>
                    </app-table-form>
                </template>
                <template slot="ChangeTheme" slot-scope="scope">{{scope.row.ChangeTheme||'无'}}</template>
                <template slot="ApprovalInfo.Code" slot-scope="scope">
                    <span v-if="scope.row.ApprovalInfo">{{scope.row.ApprovalInfo.Code||'无'}}</span>
                </template>
                <template slot="ApprovalInfo.ApprovalState" slot-scope="scope">
                    <span v-if="scope.row.ApprovalInfo" class="item-status" :style="{color:getStatusObj(scope.row.ApprovalInfo.ApprovalState).bgColor,
                    backgroundColor: getStatusObj(scope.row.ApprovalInfo.ApprovalState).color}">{{ getStatusObj(scope.row.ApprovalInfo.ApprovalState).label }}</span>
                </template>
                <template slot="EmployeeList" slot-scope="scope">
                    {{scope.row.EmployeeList.map(s=>s.Name).toString()||'无'}}
                </template>
                <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>
                <template slot="ApprovalInfo.ApprovalEmployeeList" slot-scope="scope">
                    <span v-if="scope.row.ApprovalInfo">{{ formartApprovalList(scope.row.ApprovalInfo.ApprovalEmployeeList) }}</span>
                </template>
                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 查看详情 -->
                    <app-table-row-button @click="handleReview(scope.row)" :type="1" text="详情"></app-table-row-button>
                </template>
            </app-table>

        </div>
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <!-- 创建/编辑/详情 -->
    <create-page v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus='dialogStatus'
    @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" @reload="getList"
    :id="selectId" ></create-page>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as approvalApi from "@/api/projectDev/projectMgmt/approvalSetting";
import createPage from '../../workbench/myWorkbench/apply/changeContact' //变更联络申请
import { vars } from "../../projectDev/common/vars";

export default {
    name: "material-data",
    mixins: [indexPageMixin],
    components: {
        createPage,
    },
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
    },
    watch: {
    },
    created() {
        this.getList();
    },
    data() {
        return {
            // 创建/编辑/详情
            dialogStatus: '',
            dialogFormVisible: false,
            selectId: '',

            approvalStatus: vars.approvalStatuObj.approvalStatus,
            listLoading: false,
            layoutMode: 'simple',
            tabColumns: [
                {attr: {prop: "ApprovalInfo.Code",label: "审批单号"},slot: true},
                {attr: {prop: "ChangeTheme",label: "变更主题", showOverflowTooltip: true},slot: true},
                {attr: {prop: "ApprovalInfo.ApprovalState",label: "审批状态", sortable: "custom"},slot: true},

                {attr: {prop: "ApplyDepartment",label: "申请部门", showOverflowTooltip: true}},
                {attr: {prop: "ChangeReasons",label: "变更原因", showOverflowTooltip: true}},
                
                {attr: {prop: "EmployeeList",label: "申请人", showOverflowTooltip: true},slot: true},
                {attr: {prop: "CreateTime",label: "申请时间", showOverflowTooltip: true, sortable: "custom"},slot: true},
                {attr: {prop: "ApprovalInfo.ApprovalEmployeeList",label: "审批人", showOverflowTooltip: true},slot: true},
            ],
            listQuery: {
                KeyWords: '',
                ApprovalTotalEnum: 1,
                ApprovalState: null,
                CreateTimeRange: null,
            },
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                { prop: "ApprovalState", label: "审批状态" },
                { prop: "CreateTimeRange", label: "申请时间" },
            ],
            tabDatas: [], //原始数据
            total: 0,

        };
    },
    methods: {
        formartApprovalList(approvalLevels) {
            return approvalLevels.map(l => l.map(s => s.Name).join(",")).join("; ");
        },
        getStatusObj(val) {
            return vars.approvalStatuObj.approvalStatus.find(
                s => s.value == val
            ) || {};
        },
        onResetSearch() {
            this.listQuery.PageIndex = 1;
            this.listQuery.KeyWords = ''
            this.listQuery.ApprovalState = null
            this.listQuery.CreateTimeRange = null
            this.getList(); //刷新列表
        },
        //查看详情
        handleReview(row, optType = "detail") {
            this.selectId = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        //获取列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            if (postData.CreateTimeRange && postData.CreateTimeRange.length == 2) {
                postData.StartTime = postData.CreateTimeRange[0]
                postData.EndTime = postData.CreateTimeRange[1]
            }
            delete postData.CreateTimeRange
            this.listLoading = true;
            approvalApi.GetListChangeContactPage(postData).then(res => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            })
            .catch(err => {
                this.listLoading = false;
            });
        },

        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },
        handleSortChange({column,prop,order}) {
            let delIndex = prop.indexOf('.')
            if (delIndex>-1) {
                prop = prop.substring(delIndex+1)
            }
            this.sortObj = {prop,order};
            this.getList();
        },
    }
};
</script>

<style lang="scss" scoped>
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}

</style>
