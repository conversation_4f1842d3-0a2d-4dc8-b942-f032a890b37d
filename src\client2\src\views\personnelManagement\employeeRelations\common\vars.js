//项目研发相关常量定义

export const vars = {
    PositiveApprovalStatusEnum: [// 转正审批状态
        { value: 1, label: '已审批', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
        { value: 2, label: '待审批', color: '#70B603', bgColor: 'rgba(112, 182, 3, 0.2)' }, // 绿色
        { value: 3, label: '不通过', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' }, // 橙色
    ],
    PositiveStatusEnum: [// 转正状态
        { value: 1, label: '试用期', color: '#70B603', bgColor: 'rgba(112, 182, 3, 0.2)' }, // 绿色
        { value: 2, label: '已转正', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
        { value: 3, label: '未转正', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' }, // 橙色
    ],
    EntryTimeEnum: [
        { value: 1, label: '最近1周入职'},
        { value: 2, label: '最近1个月入职'},
        { value: 3, label: '最近3个月入职'},
    ],
    InductionStatusEnum: [
        { label: '全部', value: 1 },
        { label: '待入职', value: 2, color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)' },
        { label: '已入职', value: 3, color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' },
        { label: '未到岗', value: 4, color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' },
    ],
    ReportTypeEnum: [
        { value: 1, label: '月度汇报'},
        { value: 2, label: '转正汇报'},
    ],
    
}