<template>
    <div class="seleEmpCon">
        <div class="lft">
            <div class="input-wrapper">
                <el-input class="elInput" style="margin:10px; width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            </div>
            <el-tree class="tree" :data="treeData" node-key="Id" :default-expanded-keys="defaultExpandedKey" :filter-node-method="filterNode" :props="defaultProps" ref="treeRef"  default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="handleNodeClick">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-title" :title="node.label">{{ node.label }}</span>
                </span>
            </el-tree>
        </div>

        <div class="rht">
            <div class="input-wrapper">
                <!-- <el-input class="elInput" style="margin:10px; width:calc(100% - 20px);" 
                    prefix-icon="el-icon-search" 
                    placeholder="输入职位名称" 
                    v-model="filterText2"
                ></el-input> -->

                <el-input class="elInput" style="margin:10px; width:calc(100% - 20px);" 
                    placeholder="输入职位名称"
                    @clear='getList'
                    v-antiShake='{
                        time: 300,
                        callback: () => {
                            listQuery.PageIndex = 1
                            getList()
                        }
                    }' 
                    clearable 
                    v-model="listQuery.Name"
                ></el-input>
            </div>
            <div class="tab-wrapper">
                <el-table class="empTab" fit :data="tabDatas" name="CommonEmpTab" @select-all="selectAll" @select="select" v-loading="listLoading" ref="mainTable2">
                    <el-table-column type="selection" width="55" align="left" :selectable="checkSelectable"></el-table-column>
                    <el-table-column prop="Name" label="职位名称" align="left"></el-table-column>
                    <el-table-column prop="CategoryName" label="职位分类" align='left'></el-table-column>
                    <el-table-column prop="Description" label="职位描述" align="left" show-overflow-tooltip></el-table-column>
                </el-table>
            </div>
            <pagination
                :total="total"
                :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize"
                @pagination="handleCurrentChange"
                @size-change="handleSizeChange"
                layout='total, sizes, prev, pager, next'
            />
        </div>
    </div>
</template>

<script>
import {
    listToTreeSelect
} from '@/utils'
// import * as commType from "@/api/invoicingMgmt/basic/commodityType";
// import * as comm from "@/api/invoicingMgmt/basic/commodity";
import * as systemJob from '@/api/personnelManagement/systemJob'
// import { vars } from '../../vars'

export default {
    name: 'emp-table',
    props: {
        existsUsers: { //父级组件传入和当前已选人员
            type: Array,
            default: () => {
                return []
            }
        },
        multiple: { //父级附件设置:multiple="false" ，即可设置为单选
            type: Boolean,
            default: true
        },
        visible: { //当前组件展示与否
            type: Boolean,
            default: false
        },
        condition: {
            type: Object,
            default: null
        },
        disabledList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    filters: {
        // sourceFilter(val) {
        //     let obj = vars.sources.find(s => s.value == val)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return ''
        // }
    },
    created() {
        var _this = this; //暂时没有找到更好的办法来判断展示单选提示和全选按钮，如果有更好的办法请联系曹思平，谢谢。
        _this.$nextTick(function () { //初次打开组件在渲染结束后初始化
            $('div[name="CommonEmpTab"] th .el-checkbox').parent().attr('name', 'checkboxDiv'); //给全选框上级元素增加标识
            _this.elCheckbox = $('div[name="CommonEmpTab"] th .el-checkbox'); //默认加载全选框，将全选框存入变量重复加载时根据选择模式进行dom操作
            if (!_this.multiple)
                $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //将全选框改成单选提示
        })
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        // filterText2(val) {
        //     let _this = this
        //     _this.filterNodes({
        //         vm: _this,
        //         val
        //     })
        // },
        visible: { //展示当前组件刷新列表并根据父组件传入的已选人员进行页面选中
            handler() {
                var _this = this;
                if (_this.multiple) //展示当前组件后判断单选和多选模式
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html(_this.elCheckbox); //将全选框放入table表头
                else
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //修改单选框提示
                _this.checkedUsers = JSON.parse(JSON.stringify(_this.existsUsers)) || [];
                _this.getList();
            },
            immediate: true
        },

    },
    data() {
        return {
            /**树筛选内容 */
            filterText: "",
            // filterText2: "",
            total: 0,
            elCheckbox: null,
            checkedUsers: JSON.parse(JSON.stringify(this.existsUsers)) || [],
            treeData: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'Name'
            },
            defaultExpandedKey: [], //部门树默认展开节点
            employeeIds: [], //员工IS
            tabDatas: [], //员工列表
            listQuery: { // 查询条件
                Name: '',
            },
            listLoading: false,
            checkedNode: null
        }
    },
    methods: {
        // filterNodes: _.debounce(({vm, val}) => {
        //     let _this = vm
        //     _this.listQuery.Name = val
        //     _this.getList()
        // }, 150),
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return (data.Name || '').indexOf(value) !== -1;
        },
        handleNodeClick(node) {
            this.checkedNode = node
            this.getList()
        },
        clearSelectRow() {
            
            let _this = this
            if(_this.checkedUsers && _this.checkedUsers.length == 0) {
                return false
            }

            this.$confirm('是否确认清空所有数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                _this.checkedUsers = []
                _this.$refs.mainTable2.clearSelection();
            })
        },
        removeSelectRow(idx) {
            let _this = this
            if (_this.$refs.mainTable2){
                let temp = _this.tabDatas.find(s => s.JobId == _this.checkedUsers[idx].JobId)
                if(temp) {
                    _this.$refs.mainTable2.toggleRowSelection(temp);
                }
            }
            _this.selectHandel(_this.checkedUsers[idx])
            // _this.checkedUsers.splice(idx, 1)
        },
        setCheckRow() { //根据父级传入的已选人员设置选中
           var _this = this;
            if (_this.checkedUsers && _this.checkedUsers.length > 0) {
                var checkedUsers = _this.tabDatas.filter(s => _this.checkedUsers.map(u => u.JobId).some(o => o == s.JobId)) || []

                checkedUsers.forEach(u => {
                    _this.$nextTick(() => {
                        if (_this.$refs.mainTable2)
                            _this.$refs.mainTable2.toggleRowSelection(u);
                    })
                })
                
            } else {
                _this.$refs.mainTable2.clearSelection();
            }
        },
        getSelectedList() {
            return JSON.parse(JSON.stringify(this.checkedUsers || []))
        },
        getCheckRow() { //返回已选中的人员给父级组件

            var _this = this;
            var tmpCheckRow = _this.$refs.mainTable2.selection; //获取当前已选择人数
            if (tmpCheckRow.length > _this.checkedUsers.length) { //全选时会出现当前已选中人数大于 _this.checkedUsers中记录的人数，进行同步
                tmpCheckRow = tmpCheckRow.filter(o => _this.checkedUsers.map(m => m.JobId).every(jobId => jobId !== o.JobId));
                tmpCheckRow.forEach(m => {
                    _this.checkedUsers.push(m);
                })
            }
            _this.$emit('changed', _this.checkedUsers);
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        // valueChange(e) { //查询框值改变
        //     var _this = this;
        //     if (e == '' && _this.treeHide) { //如查询框清空内容
        //         _this.treeHide = false; //展示树形菜单
        //         _this.getList(); //刷新人员列表  
        //     }
        // },
        selectHandel(row) { //选择框处理
            var _this = this;
            var tmpRow = _this.checkedUsers.some(m => m.JobId == row.JobId);
            if (tmpRow) { //选中的数据是否已包含在_this.checkedUsers内，包含则移除，不包含则添加
                _this.checkedUsers = _this.checkedUsers.filter(m => m.JobId != row.JobId);
            } else {
                _this.checkedUsers.push(row);
            }
        },
        selectAll(rows) { //全选框事件
            var _this = this;
            let oldSelectedList = JSON.parse(JSON.stringify(this.checkedUsers)) //已选中的
            // 已选中的，且不在当前列表中（不管怎么操作都不会被影响）
            oldSelectedList = oldSelectedList.filter(s => this.tabDatas.findIndex(n => n.JobId == s.JobId) == -1)
            // 合并上当前选中的
            oldSelectedList = oldSelectedList.concat(rows)
            //最后结果
            this.checkedUsers = oldSelectedList

            // _this.tabDatas.forEach(row => {//循环当前table数据做选择框事件相应处理
            //   if(this.disabledList.findIndex(s => s == row.JobId) == -1) {
            //     _this.selectHandel(row);
            //   }
            // });
        },
        select(selection, row) { //单选框事件
            var _this = this;
            if (!_this.multiple) {
                //单选处理
                _this.$refs.mainTable2.clearSelection();
                _this.$refs.mainTable2.toggleRowSelection(row, true);
                _this.checkedUsers = [row];
            } else {
                //多选处理
                _this.selectHandel(row);
            }
        },
        getOrgTree() { //查询并加载部门树
            var _this = this;
            let paramData = {
                PageSize: 1000,
                PageIndex: 1
            };
            systemJob.getCategoryListPage(paramData).then(response => { //调用公共组件API获取部门数据

                let list = response.Items.map(s => {
                    return {
                        Id: s.CategoryId,
                        Name: s.CategoryName,
                        Level: 0,
                        ParentId: null
                    }
                }) || []
                list.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });

                var orgstmp = JSON.parse(JSON.stringify(list));
                var tempOrgsTree = listToTreeSelect(orgstmp); //将部门数据转换成树形结构
                // _this.defaultExpandedKey.push(tempOrgsTree[0]['Id']); //设置默认展开
                
                _this.treeData = tempOrgsTree;

                if (_this.treeData && _this.treeData.length > 0) {
                    if (
                        !(
                            _this.checkedNode &&
                            response.find(t => {
                                return t.Id == _this.checkedNode.Id;
                            })
                        )
                    ) {
                        _this.checkedNode = _this.treeData[0];
                    }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        }
                    });
                }
            })
        },
        // query() { //查询按钮单击事件
        //     var _this = this;
        //     if (this.listQuery.Name) {
        //         _this.treeHide = true; //隐藏部门树
        //         _this.$refs.orgsTree.setCheckedKeys([]);
        //         _this.getList();
        //     }
        // },
        getList() {
            var _this = this;
            _this.listLoading = true;
            var result = null;
            
            let postData = JSON.parse(JSON.stringify(_this.listQuery))
            if (this.condition) {
                postData = Object.assign({}, postData, this.condition)
            }
            if (_this.checkedNode && _this.checkedNode.Id) {
                postData.CategoryId = _this.checkedNode.Id;
            } else {
                delete postData.CategoryId;
            }
            result = systemJob.getList(postData); //根据勾选部门或者输入框调用公共组件API获取人员
            result.then(response => {
                _this.total = response.Total
                _this.tabDatas = response.Items.map(s => {
                    if(s.Id) {
                        s.JobId = s.Id
                        delete s.Id
                    }
                    return s
                });
                _this.listLoading = false;
                _this.setCheckRow(); //根据已选人员设置tableRow默认选中
            })
        },
        checkSelectable(row) {
            if (this.disabledList && this.disabledList.length > 0) {
                return this.disabledList.findIndex(s => s == row.JobId) == -1
            }
            return true
        },
    },
    mounted() {
        this.getOrgTree();
    }
}
</script>

<style scoped>
/* .treehead>>>.el-input-group__append {
    background: red;
    color: #fff;
}

.input-wrapper>>>.el-input__inner {
    border-radius: 4px 0 0 4px;
} */
</style>

<style lang='scss' scoped>


.seleEmpCon {
    height: 550px;
    // border: 1px solid #eee;
    display: flex;
    
    .lft{
        width: 250px;
        border-right: 1px solid #dcdfe6;
        .input-wrapper{
            // padding: 10px;
        }
    }
    .rht{
        flex: 1;
        padding: 0;
        width: 100%;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .tab-wrapper{
            flex: 1;
            overflow-y: auto;
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 30px);
    position: relative;
    box-sizing: border-box;
    // padding-right: 10px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

}


</style>
