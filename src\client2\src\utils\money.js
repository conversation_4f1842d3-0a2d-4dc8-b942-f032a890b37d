/**
 * 金额处理相关工具函数
 */

/**
 * 千分位格式化金额
 * @param {number|string} money - 需要格式化的金额
 * @param {number} decimals - 保留小数位数，默认为2
 * @param {string} defaultValue - 当值为0或无效时返回的默认值，默认为null（表示0.00并保留decimals位小数）
 * @returns {string} - 格式化后的金额字符串
 */
export function formatThousands(money, decimals = 2, defaultValue = null) {
  // 处理无效输入，返回默认值或"0.00"
  if (money === null || money === undefined || money === "") {
    return defaultValue !== null ? defaultValue : toFixed(0, decimals);
  }

  // 确保 money 是数字
  let num = parseFloat(money);
  if (isNaN(num)) return defaultValue !== null ? defaultValue : toFixed(0, decimals);

  // 处理0值的情况
  if (num === 0 && defaultValue !== null) return defaultValue;

  // 转为固定小数位的字符串
  let formattedNum = toFixed(num, decimals);

  // 分离整数部分和小数部分
  const parts = formattedNum.split(".");

  // 对整数部分添加千分位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // 重新组合整数部分和小数部分
  return parts.join(".");
}

/**
 * 解析千分位格式的金额字符串，转换为数字格式的字符串
 * @param {string} formattedMoney - 带千分位格式的金额字符串
 * @param {number} decimals - 保留小数位数，默认为2
 * @returns {string} - 转换后的数字格式字符串，保留指定小数位
 */
export function parseThousands(formattedMoney, decimals = 2) {
  // 处理无效输入，返回 "0.00"
  if (formattedMoney === null || formattedMoney === undefined || formattedMoney === "") {
    return Number(0).toFixed(decimals);
  }

  // 移除所有千分位分隔符（逗号）
  const cleanedStr = formattedMoney.toString().replace(/,/g, "");

  // 转换为数字
  const num = parseFloat(cleanedStr);

  // 处理非数字情况
  if (isNaN(num)) {
    return toFixed(Number(0), decimals);
  }

  // 格式化为指定小数位的字符串
  return toFixed(num, decimals);
}

/**
 * 将数字金额转换为大写人民币格式
 * @param {number|string} money - 需要转换的金额
 * @param {string} defaultValue - 处理无效输入时返回的默认值，默认为'零元整'
 * @returns {string} - 转换后的大写人民币字符串
 */
export function convertToChinese(money, defaultValue = "零元整") {
  // 处理无效输入，返回默认值
  if (money === null || money === undefined || money === "") {
    return defaultValue;
  }

  money = parseThousands(money);

  // 确保 money 是数字
  let num = parseFloat(money);
  if (isNaN(num)) {
    return defaultValue;
  }

  // 处理0的特殊情况
  if (num === 0) {
    return "零元整";
  }

  // 定义中文数字映射
  const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];

  // 定义金额单位映射（分别对应：元、万、亿）
  const cnIntRadices = ["", "拾", "佰", "仟"];
  const cnIntUnits = ["", "万", "亿", "兆"];

  // 定义小数部分单位
  const cnDecUnits = ["角", "分"];

  // 只能处理15位整数，超出部分舍去
  let integerNum = Math.floor(num);
  if (integerNum.toString().length > 15) {
    return "数额过大，无法转换";
  }

  // 转为字符串并限制最多2位小数
  let moneyStr = num.toFixed(2);

  // 分离整数部分和小数部分
  const parts = moneyStr.split(".");
  const integerPart = parts[0];
  const decimalPart = parts[1];

  // 结果字符串
  let result = "";

  // 处理整数部分
  if (parseInt(integerPart, 10) > 0) {
    let zeroFlag = false; // 用于处理零的情况
    let intLen = integerPart.length;

    // 分段处理整数部分（四位一段）
    let sections = [];
    for (let i = intLen; i > 0; i -= 4) {
      sections.push(integerPart.substring(Math.max(0, i - 4), i));
    }
    sections.reverse(); // 反转为从高位到低位

    // 处理每个段
    for (let j = 0; j < sections.length; j++) {
      const section = sections[j];
      const unitIndex = sections.length - 1 - j;

      let sectionResult = "";
      let sectionHasValue = false;
      let hasZero = false;

      // 处理每个段内的数字
      for (let k = 0; k < section.length; k++) {
        const digit = parseInt(section.charAt(k), 10);
        const digitPos = section.length - 1 - k;

        if (digit === 0) {
          hasZero = true;
        } else {
          if (hasZero) {
            sectionResult += cnNums[0]; // 添加零
            hasZero = false;
          }
          sectionResult += cnNums[digit] + cnIntRadices[digitPos];
          sectionHasValue = true;
        }
      }

      // 添加单位（万、亿、兆）
      if (sectionHasValue) {
        result += sectionResult + cnIntUnits[unitIndex];
      } else if (unitIndex > 0 && result !== "") {
        // 当前段无值但之前有值，且当前段有单位（万/亿/兆），不添加单位
      }
    }

    result += "元";
  } else {
    result = "零元";
  }

  // 处理小数部分
  if (decimalPart !== "00") {
    for (let i = 0; i < 2; i++) {
      let n = parseInt(decimalPart.charAt(i), 10);
      if (n !== 0) {
        result += cnNums[n] + cnDecUnits[i];
      } else if (i === 0 && n === 0) {
        // 如果角位是0，不需要特殊处理
      }
    }
  } else {
    result += "整";
  }

  // 特殊处理：如果最后以"零元"结尾，需要补"整"
  if (result.endsWith("零元")) {
    result = result.substring(0, result.length - 2) + "元整";
  }
  return result;
}

/**
 * 保留小数
 * @param {Number|String} str - 需要处理的值
 * @param {Number} [precision=2] - 保留几位小数
 * @returns {string}
 */
const toFixed = (str, precision = 2) => {
  if (!str && str !== 0) return "";
  if (typeof str === "number") str = str.toString();

  let [decimal0, decimal1] = str.split(".");

  if (!decimal1) {
    str = `${decimal0}.${"".padEnd(precision, "0")}`;
  } else {
    decimal1 = decimal1.slice(0, precision);
    str = `${decimal0}.${decimal1.padEnd(precision, "0")}`;
  }
  return str;
};
