<template>
  <div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
      <!-- <page-title title="案例申报" :subTitle="['涉及新的故障现象报修单申报处理页面']"></page-title> -->

      <div class="__dynamicTabWrapper">
        <app-table :layoutMode='layoutMode' :isShowBtnsArea='false'
          ref="mainTable"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :tab-auth-columns="[]"
          :isShowAllColumn="true"
          :loading="listLoading"
          :isShowOpatColumn="true"
          :startOfTable="startOfTable"
          :multable="false"
        >
          <template slot="HandlerEmployeeList" slot-scope="scope">{{ scope.row.HandlerEmployeeList | scoreFilter }}</template>
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <div style="margin-bottom: 15px; border-bottom: 1px solid #EBEEF5;">
              <tags :items="types" v-model="listQuery.RiskType" @change="handleTagChange">
                <template v-for="t in types" :slot="t.value">{{ t.label }}</template>
              </tags>
            </div>
            <app-table-form :layoutMode='layoutMode'
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="handleResetSearch"
            >
                <template slot="Code">
                    <el-input style="width: 100%;" 
                        placeholder="搜索报修单编号..."
                        @clear='handleFilter'
                        v-antiShake='{
                            time: 300,
                            callback: () => {
                                handleFilter()
                            }
                        }' 
                        clearable 
                        v-model="listQuery.Code"
                    ></el-input>
                </template>
              <template slot="EmployeeName">
                <el-input style="width: 100%;" v-model="listQuery.EmployeeName"></el-input>
              </template>
            </app-table-form>
          </template>
          <!-- 表格批量操作区域 -->
          <!-- <template slot="btnsArea">
            <permission-btn
              style="margin-left:6px;"
              moduleName="position"
              v-on:btn-event="onBtnClicked"
            ></permission-btn>
          </template>-->
          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <!-- <app-table-row-button
              v-if="rowBtnIsExists('btnEdit')"
              @click="handleUpdate(scope.row)"
              :type="1"
            ></app-table-row-button>-->
            <app-table-row-button       
              v-if="rowBtnIsExists('declare')" 
              @click="handleReview(scope.row)"
              :type="2" text="申报"
            ></app-table-row-button>
            <app-table-row-button
            v-if="rowBtnIsExists('ignore')"
              v-show="listQuery.RiskType == 1"
              @click="handleNeglect(scope.row)"
              :type="3" 
              style="color:#aaa;"
              text="忽略"
            ></app-table-row-button>
          </template>
        </app-table>
      </div>

      <pagination
        :total="total"
        :page.sync="listQuery.PageIndex"
        :size.sync="listQuery.PageSize"
        @pagination="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>


    <create-page
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormVisible"
      :dialogStatus="dialogStatus"
      :id="id"
      @reload="getList"
      :declareNewCases='true'
    ></create-page>
  </div>
</template>

<script>
import * as failurecase from '@/api/failurecase';
import indexPageMixin from "@/mixins/indexPage";
import createPage from "../afterSalesMgmt/maintenCenter/maintenOrderMgmt/create";

export default {
  name: "",
  components: {
    createPage
  },

  mixins: [indexPageMixin],
  created() {
    this.getList();
  },
  watch: {
    
  },
  filters: {
    scoreFilter(score) {
      let a=[];
      if(score && score.length>0){
        score.forEach(v => {
          a.push(v.Name);
        })
        return a.join('、');
      }else{
        return '';
      } 
    },
  },
  mounted() {},
  data() {
    return {
            layoutMode: 'simple',
      types: [
        {
          value: 1,
          label: "待申报案例",
        },
        {
          value: 2,
          label: "已忽略案例",
        },
      ],
      dialogStatus: "create",
      dialogFormVisible: false,
      tableSearchItems: [
        {
          prop: "Code",
          label: "报修单编号",
          mainCondition: true
        },
        {
          prop: "EmployeeName",
          label: "实施人员",
        },
      ],
      tabColumns: [
        {
          attr: {
            prop: "Code",
            label: "报修单编号",
          },
        },
        {
          attr: {
            prop: "RegionalName",
            label: "安装地区",
          },
        },
        {
          attr: {
            prop: "HandlerEmployeeList",
            label: "实施人员",
          },
          slot: true,
        },
      ],
      tabDatas: [],
      listLoading: false,
      listQuery: {
        Code:'',
        EmployeeName:'',
        IsNeglect:false,
        PageIndex: 1,
        PageSize: 20,
        RiskType:1,
      },
      total: 0,
      id:'',
    };
  },

  methods: {
    handleNeglect(d){
      this.$confirm(`是否确认忽略?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        failurecase.neglect({id:d.Id}).then(res => {
          this.$notify({
            title: "成功",
            message: "忽略成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        })
      });
      
    },
    handleTagChange(d){
      console.log(d)
      if(d == 1){
        this.listQuery.IsNeglect=false;
      }else{
        this.listQuery.IsNeglect=true;
      }
      this.listQuery.PageIndex=1;
      this.listQuery.RiskType=d;
      this.getList();
    },
    handleResetSearch() {
       this.listQuery.Code='';
        this.listQuery.EmployeeName='';
        this.listQuery.PageIndex=1;
      this.getList(); //刷新列表
    },

    //查看详情
    handleReview(row) {
      this.id = row.Id;
      this.dialogStatus = 'detail';
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.listQuery.PageIndex=1;
      this.getList();
      this.closeDialog();
    },

    getList() {
      this.listLoading = true;
      failurecase.getApplicationCaseList(this.listQuery).then((res) => {
        this.listLoading = false;
        this.tabDatas = res.Items;
        this.total = res.Total;
      });
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
  },
};
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}

.span {
  position: relative;
  padding: 5px;
}

.tip {
  display: block;
  background: #f00;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  top: 8px;
  position: absolute;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

.status-false {
  background-color: rgb(0, 162, 255);
}

.status-true {
  background-color: red;
}
</style>
