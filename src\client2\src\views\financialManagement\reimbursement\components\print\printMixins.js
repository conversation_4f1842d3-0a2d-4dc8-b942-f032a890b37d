/// 打印票据混入方法
import * as approvalManagement from "@/api/approvalManagement.js";
import { formatThousands, convertToChinese } from "@/utils/money.js";

export default {
  data() {
    return {
      formData: {},
      /**
       * 底部流程
       * 提交人【xxx】→第一审批人【xxx】→第二审批人【xxx】→会计【xxx】财务主管【xxx】→监察长→出纳【xxx】
       */
      flowList: [],
    };
  },
  computed: {
    getSubmitEmployeeName() {
      return this.formData?.[this.parcelKey]?.SubmitEmployeeList?.[0]?.Name || "";
    },
    // 研发项目名称数组
    projectNameList() {
      const list = this.getObjData(this.formData, "KingdeeProjectList", []);
      return list.map(item => `${item.KingdeeName}(${item.KingdeeNumber})`);
    },
    // 领款人
    getPayeeEmployeeName() {
      const list = this.getObjData(this.formData, "PayeeEmployeeList", []);
      return list?.[0]?.Name || "";
    },
  },
  methods: {
    formatThousands: num => formatThousands(num, 2, ""),
    convertToChinese,
    // 安全获取列表深层数据
    getObjData(row, key, defaultValue = "") {
      return row?.[this.parcelKey]?.[key] ?? defaultValue;
    },
    // 处理打印
    handlePrint(row) {
      if (!row || !row.CurrentBusinessId) {
        this.$message.error("数据异常，无法打印");
        return;
      }
      this.flowList = [];
      // 获取借款详情
      approvalManagement
        .getPersonalDetails({
          id: row.CurrentBusinessId,
          approvalId: row.Id,
          isApprovaled: true,
        })
        .then(res => {
          this.formData = res;
          // 在数据加载完成后执行打印
          this.$nextTick(() => {
            this.print();
          });
          this.addSubmitEmployee();
        })
        .catch(error => {
          this.$message.error("获取数据失败");
        });
    },
    // 执行打印
    print() {
      this.$print(this.$refs.printContent);
    },
    // 流程添加提交人(第一步)
    addSubmitEmployee() {
      this.flowList.push({
        name: this.getSubmitEmployeeName,
        role: "提交人",
      });
      this.addApprovalFlow();
    },
    // 流程添加审批流程(第二步)
    addApprovalFlow() {
      this.formData.Approval.ApprovalEmployeeList.forEach((t, i) => {
        const item = t?.[0];
        const step = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
        this.flowList.push({
          name: item?.Name,
          role: `第${step[i]}审批人`,
        });
      });
    },
  },
};
