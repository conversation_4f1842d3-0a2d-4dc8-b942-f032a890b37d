<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :disabledList="disabledList || []"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      :width="1200"
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form
          style="padding-top: 10px;"
          :label-width="'110px'"
          :items="tableSearchItems"
          @onSearch="handleFilter"
          @onReset="onResetSearch"
        >
          <template slot="ReportRange">
            <el-date-picker
              style="width: 100%;"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              v-model="listQueryParams.ReportRange"
              type="datetimerange"
            ></el-date-picker>
          </template>
          <template slot="RegionalId">
            <div class="el-input el-input--mini">
              <div
                style="display: flex; height: 32px;line-height: 32px;border-radius: 4px;border: 1px solid #DCDFE6; box-sizing: border-box; "
              >
                <div style="padding-left: 10px;">
                  <span style="color: #409EFF; cursor: pointer;" @click="handleRegionalDialog">
                    选择
                  </span>
                </div>
                <div
                  style="flex: 1; padding: 0 10px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                  :title="listQueryParams.areaName"
                >
                  {{ listQueryParams.areaName }}
                </div>
                <div style="width: 32px; text-align: center;">
                  <i
                    style="cursor: pointer;"
                    title="删除"
                    @click="handleClearRegional"
                    v-show="listQueryParams.RegionalId"
                    class="el-icon-close"
                  ></i>
                </div>
              </div>
            </div>
          </template>
          <template slot="EmployeeName">
            <el-input
              style="width: 100%;"
              v-model.trim="listQueryParams.EmployeeName"
              placeholder
            ></el-input>
          </template>
          <template slot="ServiceNo">
            <el-input
              style="width: 100%;"
              v-model.trim="listQueryParams.ServiceNo"
              placeholder
            ></el-input>
          </template>
          <template slot="IsReplacePart">
            <el-select
              v-model="listQueryParams.IsReplacePart"
              placeholder=""
              clearable
              style="width: 100%;"
            >
              <el-option label="有" :value="true"></el-option>
              <el-option label="无" :value="false"></el-option>
            </el-select>
          </template>
          <template slot="IsWarranty">
            <el-select
              v-model="listQueryParams.IsWarranty"
              placeholder=""
              clearable
              style="width: 100%;"
            >
              <el-option
                v-for="(wl, wlI) in warrantyList"
                :key="wlI"
                :label="wl.label"
                :value="wl.value"
              ></el-option>
            </el-select>
          </template>
        </app-table-form>
      </template>

      <template slot="ServiceNo" slot-scope="scope">
        {{ scope.row.ServiceNo ? scope.row.ServiceNo : "无" }}
      </template>
      <template slot="HandlerEmployeeList" slot-scope="scope">
        <span v-if="scope.row.HandlerEmployeeList">
          {{ scope.row.HandlerEmployeeList.map(s => s.Name).join(",") }}
        </span>
        <span v-else>无</span>
      </template>
      <template slot="AfterContractCode" slot-scope="scope">
        {{ scope.row.AfterContractCode ? scope.row.AfterContractCode : "无" }}
      </template>
      <template slot="ReportTime" slot-scope="scope">
        {{ scope.row.ReportTime | dateFilter("YYYY-MM-DD HH:mm") }}
      </template>
      <template slot="Opt" slot-scope="scope">
        <app-table-row-button @click="handleDialog(scope.row)" :type="2"></app-table-row-button>
      </template>
    </listSelector>

    <v-area-choose
      v-if="dialogRegionalVisible"
      @closeDialog="closeRegionalDialog"
      @electedRegionalData="electedRegionalData"
      :dialogFormVisible="dialogRegionalVisible"
      :checkedList="listQueryParams.RegionalId ? [listQueryParams.RegionalId] : []"
    ></v-area-choose>

    <create-page
      v-if="dialogDetailFormVisible"
      @closeDialog="closeDialog"
      :dialogFormVisible="dialogDetailFormVisible"
      dialogStatus="detail"
      :id="detailId"
      :declareNewCases="true"
    ></create-page>
  </div>
</template>

<script>
import listSelector from "../../../common/listSelector";
import vAreaChoose from "../../../afterSalesMgmt/businessMap/common/areaChoose";
import { serviceArea } from "@/api/serviceArea";
import createPage from "./create";
import { vars } from "../../maintenCenter/common/vars";
import { getUserInfo, getDateTimeRange } from "@/utils/auth";

export default {
  name: "mainten-order-selector",
  components: {
    listSelector,
    vAreaChoose,
    createPage,
  },
  filters: {},
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    isTip: {
      type: Boolean,
      default: true,
    },
    disabledList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  created() {
    if (this.condition) {
      this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition);
    }
  },
  watch: {
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList(val) {
      if (val && val.length > 0) {
        this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
      } else {
        this.listSelectorCheckedData = [];
      }
    },
  },
  data() {
    return {
      warrantyList: vars.maintenOrderMgmt.isInsurance,
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/Maintenance/GetListPage",
      listSelectorMultiple: this.multiple,
      listQueryParams: {
        ReportRange: [],
        RegionalId: "", //报修地区
        areaName: "",
        ServiceNo: "",
        EmployeeName: "", //实施人员
        IsReplacePart: null, //状态
        IsWarranty: null, //报修单号
      },
      tableSearchItems: [
        { prop: "ReportRange", label: "报修时间" },
        { prop: "RegionalId", label: "地区" },
        { prop: "ServiceNo", label: "服务单号" },
        { prop: "EmployeeName", label: "实施人员" },
        { prop: "IsReplacePart", label: "更换配件" },
        { prop: "IsWarranty", label: "是否在保" },
      ],
      listSelectorTitle: "关联报修单",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",

      listSelectorColumnData: [
        {
          attr: { prop: "Code", label: "报修编号", width: "140" },
        },
        {
          attr: {
            prop: "RegionalName",
            label: "报修地区",
            showOverflowTooltip: true,
            width: "280",
          },
        },
        {
          attr: { prop: "ReportTime", label: "报修时间", sortable: "custom", width: "125" },
          slot: true,
        },
        {
          attr: { prop: "TotalPrice", label: "总收费（元）", width: "100" },
        },
        {
          attr: { prop: "ServiceNo", label: "服务单号", width: "95" },
          slot: true,
        },
        {
          attr: { prop: "HandlerEmployeeList", label: "实施人员" },
          slot: true,
        },
        {
          attr: { prop: "AfterContractCode", label: "售后合同" },
          slot: true,
        },
        {
          attr: { prop: "Opt", label: "操作", width: "100" },
          slot: true,
        },
      ],
      listSelectorDialogFormVisible: false,
      dialogRegionalVisible: false,
      dialogDetailFormVisible: false,
      detailId: "",
    };
  },
  methods: {
    handleFilter() {
      if (this.listQueryParams.ReportRange && this.listQueryParams.ReportRange.length == 2) {
        this.listQueryParams.ReportTimeStart = this.listQueryParams.ReportRange[0];
        this.listQueryParams.ReportTimeEnd = this.listQueryParams.ReportRange[1];
      } else {
        this.listQueryParams.ReportTimeStart = null;
        this.listQueryParams.ReportTimeEnd = null;
      }
      this.$refs.listSelector.getDatas();
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      this.listQueryParams.ReportRange = [];
      this.listQueryParams.RegionalId = null;
      this.listQueryParams.areaName = "";
      this.listQueryParams.ServiceNo = "";
      this.listQueryParams.EmployeeName = "";
      this.listQueryParams.IsReplacePart = null;
      this.listQueryParams.IsWarranty = "";

      this.handleFilter();
    },
    listSelectorCloseDialog() {
      this.onResetSearch();
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let inFun = () => {
        let list = data;
        this.$emit("changed", JSON.parse(JSON.stringify(list)));
        this.listSelectorCloseDialog();
      };
      if (this.isTip && data && data.find(s => !!s.AfterContractCode)) {
        this.$confirm(
          `选中的报修单中含有已关联合同数据，确定后将替换为当前合同，是否继续操作？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          inFun();
        });
      } else {
        inFun();
      }
    },

    closeRegionalDialog() {
      this.dialogRegionalVisible = false;
    },
    handleRegionalDialog() {
      this.dialogRegionalVisible = true;
    },

    handleClearRegional() {
      this.listQueryParams.RegionalId = "";
      this.listQueryParams.areaName = "";
      this.handleFilter();
    },
    electedRegionalData(data) {
      if (data) {
        this.listQueryParams.RegionalId = data.Id;
        this.listQueryParams.areaName = data.ParentName;
      } else {
        this.listQueryParams.RegionalId = "";
        this.listQueryParams.areaName = "";
      }
    },
    handleDialog(row) {
      this.detailId = row.Id;
      this.dialogDetailFormVisible = true;
    },
    closeDialog() {
      this.dialogDetailFormVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.status-color-2 {
  background-color: #00b050;
}
.status-color-3 {
  background-color: red;
}
.status-color-4 {
  background-color: #409eff;
}
</style>
