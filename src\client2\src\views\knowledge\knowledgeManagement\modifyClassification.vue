<template>
    <!-- 修改分类 -->
    <app-dialog title="修改分类" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='420' :maxHeight="200" >
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" class="create_body"
            label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                <el-form-item label-width="10px" label=" ">
                    已选中（{{ids.length}}）
                </el-form-item>
                <el-form-item label="文章分类" prop="ClassifyId">
                    <treeselect :normalizer="normalizer"
                        class="treeselect-common"
                        :options="treeData" :default-expand-level="3"
                        :multiple="false" :open-on-click="true" :open-on-focus="true"
                        :clear-on-select="true" v-model="formData.ClassifyId" placeholder="请选择文章分类"
                        :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"
                        :append-to-body="true" zIndex='9999'>
                    </treeselect>
                </el-form-item>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="createData" text="提交" :loading="buttonLoading"></app-button>
        </template>
    </app-dialog>
</template>
<script>
import * as classifyApi from '@/api/classify'
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";
import * as KnowledgeApi from '@/api/knowledge/Knowledge'
export default {
    name: 'knowledge-management-modify-classification',
    components: {
        Treeselect,
    },
    props: {
        ids: {
            type: Array,
            require: true,
        },
    },
    data() {
        return {
            treeData: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },

            labelWidth: "80px",
            buttonLoading: false,
            formData: {
                ClassifyId: null,
            },
            rules: {
                ClassifyId: {fieldName: "文章分类",rules: [{ required: true }]},
            },
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
        this.getTreeData();
    },
    methods: {
        getTreeData() {
            let postDatas = {
                PageIndex: 1,
                PageSize: 10000,
                BusinessType: 17
            }
            classifyApi.getListPage(postDatas).then(res => {
                var list = (res.Items || []).map((item, index, input) => {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId
                    };
                });
                this.treeData = listToTreeSelect(list, undefined, undefined, undefined, 'DepartmentName');
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        //保存
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = {
                    Ids: this.ids,
                    Value: this.formData.ClassifyId
                }
                this.buttonLoading = true;
                KnowledgeApi.batchEdit(postData).then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                    this.buttonLoading = false;
                });
            });
        },
    }
}
</script>

<style scoped>
.create_body >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>