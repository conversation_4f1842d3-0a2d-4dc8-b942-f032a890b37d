
export const vars = {
    equipmentAge:[
      {value: 3, label: '超过3年'},
      {value: 5, label: '超过5年'},
      {value: 6, label: '超过6年'},
      {value: 7, label: '超过7年'},
      {value: 8, label: '超过8年'},
      {value: 9, label: '超过9年'},
      {value: 10, label: '超过10年'},
      {value: 12, label: '超过12年'},
      {value: 15, label: '超过15年'},
      {value: 18, label: '超过18年'},
      {value: 20, label: '超过20年'},
    ],
    handling:[
      {value: 1, label: '维修更换'},
      {value: 2, label: '隐患治理'},
      // {value: 1, label: '维修'},
      // {value: 2, label: '拆除'},
      // {value: 4, label: '更换'},
      // {value: 5, label: '新增'},
    ],
    // 1.待确认 2.不处理 3.已转工单
    orderTypes: [
      {value: 1, label: '待确认', color: 'red'},
      {value: 2, label: '不处理', color: 'orange'},
      {value: 3, label: '已转工单', color: '#409EFF'},
      {value: 4, label: '已撤销', color: '#f18900'},
    ],
    //保修单管理
    maintenOrderMgmt: {
      FaultTypeOptions: [
        {
          value: 1,
          label: "硬件类"
        },
        {
          value: 2,
          label: "调试类"
        },
        {
          value: 3,
          label: "应用类"
        },
        {
          value: 4,
          label: "线路类"
        },
        {
          value: 5,
          label: "大修类"
        },
        {
          value: 6,
          label: "其它类"
        },
        {
          value: 7,
          label: "隐患治理类"
        },
      ],
      isInsurance:[
        {value: 1, label: '否', color: '#F56C6C'},
        {value: 2, label: '是', color: '#00cc00'},
        // {value: 4, label: '过保',color:'#F59A23'},
        {value: 3, label: '是(未知有效期)', color: '#00cc00'},
      ],
      serviceListStatus: [
          {value: 1, label: '已签服务单', color: '#409EFF'},
          {value: 2, label: '未签服务单', color: '#F59A23'},
      ],
        tagStatus: [
            {value: 1, label: '未完成', color: 'red'},
            {value: 2, label: '已处理', color: '#00B050'},
            {value: 3, label: '全部', color: '#409EFF'},
        ],
        maintenanceStatus: [
            {value: 1, label: '待处理', color: '#F59A23'},
            {value: 2, label: '处理中', color: '#00CC00'},
            {value: 3, label: '已处理', color: '#409EFF'},
            {value: 4, label: '待指派', color: '#FF0000'},
            {value: 5, label: '已维修', color: '#027DB4'},
        ],
        workTypes: [
            {value: 1, label: '正常出勤'},
            {value: 2, label: '加班'},
            {value: 3, label: '夜勤'},
        ],
        //处理结果
        handlingResultStatus: [
            {value: 1, label: '未开始',color:'red'},
            {value: 2, label: '已处理',color:'#409eff'},
            {value: 3, label: '未完成',color:'#00b050'},
        ]
    },
    lineOption:{
        title: {
            text: '折线图堆叠',
            x:'center',
            top:10,
            textStyle:{
                fontWeight:'normal'
            }
        },
        tooltip: {
            trigger: 'axis',
            // formatter:'{b}<br/>{a}：{'+(c>=0 ? 'c' : 'd')+'}'
            formatter:function(params){
              let html=params[0].name+'<br/>';
              params.forEach(v => {
                html+=v.seriesName+': '+(v.value=="" ? '无' : v.value)+'<br/>';
              })
              return html;
            },
            position: function (point, params, dom, rect, size) {
              // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
              // 提示框位置
              var x = 0; // x坐标位置
              var y = 0; // y坐标位置
             
              // 当前鼠标位置
              var pointX = point[0];
              var pointY = point[1];
             
              // 外层div大小
              // var viewWidth = size.viewSize[0];
              // var viewHeight = size.viewSize[1];
             
              // 提示框大小
              var boxWidth = size.contentSize[0];
              var boxHeight = size.contentSize[1];
             
              // boxWidth > pointX 说明鼠标左边放不下提示框
              if (boxWidth > pointX) {
                x = 5;
              } else { // 左边放的下
                x = pointX - boxWidth;
              }
             
              // boxHeight > pointY 说明鼠标上边放不下提示框
              if (boxHeight > pointY) {
                y = 5;
              } else { // 上边放得下
                y = pointY - boxHeight;
              }
             
              return [x, y];
            }
        },
        legend: {
          type: 'scroll',
            data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎'],
            icon:'line',
            x:'center',
            bottom:15
        },
        grid: {
            y:56,
            y2:66,
        },
        // toolbox: {
        //     feature: {
        //         saveAsImage: {}
        //     }
        // },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        yAxis: {
            type: 'value'
        },
        color : [ '#1BBC9D', '#3498DB', '#2DA2BF', '#EE7C31', '#EBC4DB','#336699','#cccccc','#cc9966','#cc3333','#ff6666','#99cc66','#003399','#009966','996699'],
        series: [
            // {
            //     name: '邮件营销',
            //     type: 'line',
            //     symbolSize:1,
            //     stack: '总量',
            //     data: [120, 132, 101, 134, 90, 230, 210],
            //     itemStyle : { normal: {label : {show: true,textStyle: {color: '#1D2129'}}}}
            // },
        ]
    },
    towBarOption :{
            legend: {
                data: ['新增报修单', '处理报修单'],
                top:10
            },
            grid:{
                x:50,
                x2:25,
              y2:46,
              bottom:'16%'
            },
            xAxis: {
              type: 'category',
              axisLine: {
                lineStyle: {
                  color: '#ccc', // 颜色
                }
              },
              axisLabel: {
                color:'black',
                interval:0,
              },
              data: ['燃烧器', '加热炉', '锅炉', 'aaa', 'bbb', 'Sat', 'Sun']
            },
            yAxis: {
              type: 'value',
              splitLine:{
                show: true,
                lineStyle:{
                    type:'dashed'
                }
              },//去除网格线
              axisLine: {
                lineStyle: {
                  color: '#ccc', // 颜色
                }
              },
              axisLabel: {
                color:'black'
              },
            },
            series: [{
                name:'新增报修单',
              data: [120, 200, 150, 80, 70, 110, 130],
              type: 'bar',
              barWidth: 20,
              // showBackground: true,
              label: {
                  show: true,
                  position: 'outside'
              },
              itemStyle:{
                normal:{
                  color:'#FF9900'
                }
              },
            },{
                name:'处理报修单',
              data: [120, 200, 150, 80, 70, 110, 130],
              type: 'bar',
              barWidth: 20,
              // showBackground: true,
              label: {
                  show: true,
                  position: 'outside'
              },
              itemStyle:{
                normal:{
                  color:'#409EFF'
                }
              },
            },]
        },
       lengther:10,
        pieOption:{
            title:{
                show: false,
                text: '', //设备总数(99999)
                  left: 'center',
                  top:20,
                  textStyle:{
                    fontWeight:600,
                    fontSize:16
                  }
            },
            legend: {
              type: 'scroll',
              icon:"circle",
              orient: 'vertical',
              left: '72%',
              top: 'middle',
              data: [], //'大庆','长庆','周口','项城','克拉玛依是个撒娇管理是两个极端是两个极端老规矩'
              // formatter:function(params) {
              //       let tip1 = "";
              //       let tip = "";
              //       let le = params.length  //图例文本的长度
              //       if(le > vars.lengther){   //几个字换行大于几就可以了
              //           let l = Math.ceil(le/vars.lengther)  //有些不能整除，会有余数，向上取整
              //           for(let i = 1;i <= l;i++){ //循环
              //               if(i < l){ //最后一段字符不能有\n
              //                   tip1 += params.slice(i*vars.lengther-vars.lengther,i*vars.lengther)+'\n'; //字符串拼接
              //               }else if(i === l){  //最后一段字符不一定够vars.lengther个
              //                   tip = tip1 + params.slice((l-1)*vars.lengther,le) //最后的拼接在最后
              //               }
              //           }
              //           return tip;
              //       }else{
              //           tip = params  //前面定义了tip为空，这里要重新赋值，不然会替换为空
              //           return tip;
              //       }
              //   },
              formatter: function (name) {
                if (!name) return '';
                  if (name.length > vars.lengther) {
                    name =  name.slice(0,vars.lengther) + '...';
                }
                return name;
              },
              tooltip: {
                show: true
              }
            },
            tooltip: {
              trigger: 'item',
              // formatter: '{a} <br/>{b} : {c} ({d}%)'
              formatter: function (params, ticket, callback) {
                  var showHtm="";
                  var index = params.name.lastIndexOf('(');
                  if(index == -1){
                    index=params.name.length;
                  }
                  var name =params.name.substring(0,index)
                  showHtm=name+': '+params.value+' ('+params.percent+'%)';
                  return showHtm;
              }
            },
            //设置饼状图每个颜色块的颜色
            // color : [ '#1BBC9D', '#3498DB', '#2DA2BF', '#EE7C31', '#EBC4DB','#336699','#cccccc','#cc9966','#cc3333','#ff6666','#99cc66','#003399','#009966','996699'],
            color: ['#3aa1ff', '#88d1ea', '#36cbcb', '#82dfbe', '#4ecb73', '#acdf82', '#fbd437', '#eaa674', '#f2637b', '#dc81d2'],
            series: [
              {
                name: '访问来源',
                type: 'pie',
                radius: '75%',
                center: ['40%', '50%'],
                data: [], // { value: 100, name: '大庆', label:{show:true,position:'inside'} }
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                // 设置值域的标签
                label: {
                  normal: {
                    // position: 'inner',  // 设置标签位置，默认在饼状图外 可选值：'outer' ¦
                    formatter: "{d}%"
                  }
                }
              }
        ]
    },
    stackBarOption:{
        tooltip: {
            trigger: 'axis',
            formatter(params){
              let html=params[0].name+'<br/>';
              params.forEach(v => {
                if(v.value>0 && v.seriesName != '总数'){
                  html+=v.marker+v.seriesName+"："+v.value+"<br/>";
                }
              })
              return html;
            },
            position:function (point, params, dom, rect, size) {
              // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
              // 提示框位置
              var x = 0; // x坐标位置
              var y = 0; // y坐标位置

              // 当前鼠标位置
              var pointX = point[0];
              var pointY = point[1];

              // 外层div大小
              var viewWidth = size.viewSize[0];
              // var viewHeight = size.viewSize[1];
              // 提示框大小
              var boxWidth = size.contentSize[0];
              var boxHeight = size.contentSize[1];
              if((viewWidth - pointX) < boxWidth){
                x = pointX - boxWidth;
              }else{
                x=pointX+10;
              }


              // boxHeight > pointY 说明鼠标上边放不下提示框
              if (boxHeight > pointY) {
                y = 5;
              } else { // 上边放得下
                y = pointY - boxHeight;
              }

              return [x, y];
            },
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'line'        // 默认为直线，可选为：'line' | 'shadow'
            },
        },
        legend: {
          type:'scroll',
            data: ['直接访问', '邮件营销', '联盟广告', '视频广告', '搜索引擎'],
            top:20,
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
            type: 'value',
            splitLine: {
                show: true,
                lineStyle:{
                     width: 1,
                    type: 'dashed'
                }
            }
        },
        color:['#FF9900','#00CC00','#999999','#409EFF','#1BBC9D', '#3498DB', '#2DA2BF', '#EE7C31', '#EBC4DB','#336699','#cccccc','#cc9966','#cc3333','#ff6666','#99cc66','#003399','#009966','996699'],
        series: [
            {
                name: '直接访问',
                type: 'bar',
                barWidth:30,
                stack: '总量',
                label: {
                    show: true,
                    position: 'inside'
                },
                data: [320, 302, 301, 334, 390, 330, 320],
            },
            {
                name: '邮件营销',
                type: 'bar',
                barWidth:30,
                stack: '总量',
                label: {
                    show: true,
                    position: 'inside'
                },
                data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
                name: '联盟广告',
                type: 'bar',
                barWidth:30,
                stack: '总量',
                label: {
                    show: true,
                    position: 'inside'
                },
                data: [220, 182, 191, 234, 290, 330, 310]
            },
            {
                name: '视频广告',
                type: 'bar',
                barWidth:30,
                stack: '总量',
                label: {
                    show: true,
                    position: 'inside'
                },
                data: [150, 212, 201, 154, 190, 330, 410]
            },
            {
                name: '搜索引擎',
                type: 'bar',
                barWidth:30,
                stack: '总量',
                label: {
                    show: true,
                    position: 'inside'
                },
                data: [820, 832, 901, 934, 1290, 1330, 1320]
            },
            {
                name: '消费合计总量',
                type: 'bar',
                stack: '总量',
                label: {
                normal: {
                offset:['50', '80'],
                show: true,
                position: 'insideBottom',
                formatter:'{c}',
                textStyle:{ color:'#1D2129' }
                }
                },
                itemStyle:{
                normal:{
                color:'rgba(128, 128, 128, 0)'
                }
                },
                data: [820, 832, 901, 934, 1290,1330, 1320]
            }
        ]
    },
}
