<template>
    <div class="step-wrapper">
        <div class="step-main">
            <el-form ref="formData" label-position="right" label-width="100px">
                <div>
                    <app-table ref="mainTable" 
                        :tab-columns="tabColumns" 
                        :tab-datas="tabDatas" 
                        :tab-auth-columns="tabAuthColumns" 
                        :isShowAllColumn="true" 
                        :loading="listLoading" 
                        :isShowOpatColumn="true" 
                        :startOfTable="startOfTable"
                        :isShowConditionArea='false'
                        @rowSelectionChanged="rowSelectionChanged"
                        :isShowBtnsArea='editable'
                        :multable='editable'
                        >

                        <template slot="SelfRatingEmployee" slot-scope="scope">
                            <span v-if="scope.row.SelfRatingEmployee">{{ scope.row.SelfRatingEmployee.Name }}</span>
                        </template>
                        <template slot="AppraisePromiseStatus" slot-scope="scope">
                            <span :style="{color: getStatusColor(scope.row.AppraisePromiseStatus, appraisePromiseStatusEnum)}">
                                {{ scope.row.AppraisePromiseStatus | appraisePromiseStatusFilter }}
                            </span>
                        </template>
                        <template slot="LookStatus" slot-scope="scope">
                            <span :style="{color: getStatusColor(scope.row.LookStatus, lookStatusEnum)}">
                                {{ scope.row.LookStatus | lookStatusFilter }}
                            </span>
                        </template>

                        <!-- 表格批量操作区域 -->
                        <template slot="btnsArea">
                            <div class="btns-wrapper" v-if="editable">
                                <!-- <el-button type="danger" :disabled="!isStart" @click="handleCheck(1)">终止考核</el-button>
                                <el-button type="primary" :disabled="!isStart" @click="handleCheck(2)">继续考核</el-button> -->
                                <el-button type="danger" @click="handleCheck(1)">终止考核</el-button>
                                <el-button type="primary" @click="handleCheck(2)">继续考核</el-button>
                            </div>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleShowDetail(scope.row)" :type="2"></app-table-row-button>
                            <!-- scope.row.MidtermReviewEndTime 表示中期审视启动 -->
                            <!-- <app-table-row-button v-if="editable && scope.row.AppraisePromiseStatus == 2 && scope.row.MidtermReviewEndTime" @click="handleResubmit(scope.row)" :type="2" text='重新提交'></app-table-row-button> -->
                            <app-table-row-button v-if="editable && scope.row.AppraisePromiseStatus == 2" @click="handleResubmit(scope.row)" :type="2" text='重新提交'></app-table-row-button>
                        </template>
                    </app-table>
                </div>
            </el-form>
        </div>
        <div class="btn-wrapper" v-if="editable">
            <el-button type="primary" style="width: 180px;" :loading="loading" :disabled='loading' @click="handleSave">保存提交</el-button>
        </div>
    </div>
</template>


<script>
import indexPageMixin from "@/mixins/indexPage";
import empSelector from '@/views/common/empSelector'
import { appraisePromiseStatusEnum, lookStatusEnum } from "../enum"
import * as ach from "@/api/personnelManagement/achievementMgmt"
import * as myAch from "@/api/myAchievements"

export default {
    name: "step3",
    mixins: [indexPageMixin],
    components: {
        empSelector,
    },
    props: {
        //跟进 才 可编辑；详细不可以
        isFollow: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: true
        },
        progressStatus: {
            type: Number,
            required: true
        },
        // isStart: {
        //     type: Boolean,
        //     default: false
        // }
    },
    computed: {
        editable() {
            if(this.isFollow && this.progressStatus < 6) {
                return true
            }
            return false
        }
    },
    watch: {
        id: {
            handler(val) {
                this.getList()
            },
            immediate: true
        }
    },
    created() {
    },
    mounted() {
    },
    filters: {
        appraisePromiseStatusFilter(val) {
            let obj = appraisePromiseStatusEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        lookStatusFilter(val) {
            let obj = lookStatusEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        }
    },
    data() {
        return {
            appraisePromiseStatusEnum,
            lookStatusEnum,
            multipleSelection: [],
            loading: false,
            listLoading: false,
            tabDatas: [],
            tabColumns: [{
                    attr: {
                        prop: "SelfRatingEmployee",
                        label: "考核对象",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "AppraisePromiseStatus",
                        label: "绩效承诺",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "LookStatus",
                        label: "审视状态",
                    },
                    slot: true,
                },
            ],
            
        };
    },
    methods: {
        getStatusColor(val, enums) {
            let obj = enums.find(s => s.value == val)
            if(obj) {
                return obj.color
            }
            return ''
        },
        handleSave() {
            //如果“继续考核”集合中不是全部为“已提交”状态（说明有“未提交”、“重新提交”）
            if(!this.tabDatas.filter(s => s.LookStatus == 1).every(s => s.AppraisePromiseStatus == 2)) { 
                this.$confirm(`检测到有考核对象为提交绩效承诺，是否确认保存？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.saveData()
                })
            }else{
                this.$confirm(`是否确认保存？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.saveData()
                })
            }
        },
        saveData() {
            this.loading = true
            ach.saveMidtermReview({id: this.id}).then(res => {
                this.loading = false
                this.$notify({
                        title: '成功',
                        message: '操作成功',
                        type: 'success',
                        duration: 2000
                    })
                // this.$emit('forwardSuccess')
                this.$emit('reload')
            }).catch(err => {
                this.loading = false
            })
        },
        getList() {
            let postData = {
                PageSize: 10000,
                PageIndex: 1,
                AppraisePlanId: this.id
            }
            this.listLoading = true
            myAch.getList(postData).then(res => {
                this.listLoading = false
                this.tabDatas = res.Items || []
            }).catch(err => {
                this.listLoading = false
            })
        },
        handleShowDetail(row) {
            //1 表示中期；2：表示已自评
            this.$emit('showDetail', {Id: row.Id, Type: 1})
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleCheck(busType) {
            if(this.multipleSelection.length == 0) {
                this.$message({
                    message: "请选择需要操作的行",
                    type: "error",
                });
                return;
            }

            let msg = `是否对选中项 ` + (busType == 1 ? "终止考核" : "继续考核") + "？"
            this.$confirm(msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                
                let postData = this.multipleSelection.map(s => s.Id)
                let result = null
                if(busType == 1) {
                    result = myAch.stopCheck(postData)
                }else{
                    result = myAch.goonCheck(postData)
                }

                result.then(res => {
                    this.getList()
                    this.$notify({
                        title: '成功',
                        message: '操作成功',
                        type: 'success',
                        duration: 2000
                    })
                })
            })
        },
        handleResubmit(row) {
            this.$confirm('是否确认重新提交？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                myAch.resubmit([row.Id]).then(res => {
                    this.getList()
                    this.$notify({
                        title: '成功',
                        message: '操作成功',
                        type: 'success',
                        duration: 2000
                    })
                })
            })
        },
    },
};
</script>

<style lang="scss" scoped>
@import "./step.css";
</style>