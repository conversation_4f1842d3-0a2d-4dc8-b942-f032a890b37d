<!--答题详情-->
<template>
<app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
    <template slot="body">
        <div class="pageWarp" v-loading="loading">
            <no-data v-if="ParticipantEmployeeList.length == 0"></no-data>
            <div class="pageWarp_main" v-if="ParticipantEmployeeList.length>0">
                <div class="tabsBox">
                    <div class="tabsBox_item" :class="{'active': activeEmployeeId==employeeItem.EmployeeId}" @click="tabClick(employeeItem)"
                    v-for="employeeItem in ParticipantEmployeeList" :key="employeeItem.EmployeeId">
                        <img class="tabsBox_item_userPhoto" :src="employeeItem.AvatarPath||require('../../../assets/images/avatar3.png')" />
                        {{employeeItem.Name}}
                    </div>
                </div>
                <div class="opr">
                    <div ref="questionRef" class="question">
                        <template v-for="employeeItem in ParticipantEmployeeList">
                            <div :key="employeeItem.EmployeeId" v-if="activeEmployeeId==employeeItem.EmployeeId">
                                <template v-for="(questionItem,questionItemIndex) in employeeItem.QuestionAnswerData">
                                    <el-row :key="questionItemIndex">
                                        <el-row class="question_title" v-if="questionItemIndex>0?QuestionAnswerData[questionItemIndex-1].SubmitItemType!=questionItem.SubmitItemType:true">{{questionItem.SubmitItemType|submitItemTypeFilter}}</el-row>
                                        <el-row class="question_item">
                                            <el-row class="question_item_title">{{questionItem.OptionIndex+1}}、{{questionItem.OptionIndex|questionAnswerDataFilter}}</el-row>
                                            <div class="flexWarp">
                                                <div>得分：</div>
                                                <div class="flexColumn not_backface">
                                                    <el-radio-group v-model="questionItem.Value" disabled>
                                                        <el-radio :label="0">0分</el-radio>
                                                        <el-radio :label="1">1分</el-radio>
                                                        <el-radio :label="2">2分</el-radio>
                                                        <el-radio :label="3">3分</el-radio>
                                                        <el-radio :label="4">4分</el-radio>
                                                        <el-radio :label="5">5分</el-radio>
                                                    </el-radio-group>
                                                </div>
                                            </div>
                                        </el-row>
                                    </el-row>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </template>
    <template slot="footer">
        <app-button @click="handleClose" text="关闭" type></app-button>
    </template>
</app-dialog>
</template>

<script>
import NoData from "@/views/common/components/noData";
import * as SurveyApi from '@/api/personnelManagement/survey.js'
import { QuestionAnswerType,QuestionAnswerData, } from "../enum.js";
export default {
    name: "questionnaireMgt-answer-detail",
    components: {
        NoData
    },
    props: {
        node: {
            type: Object,
            required: true,
            default: ()=>{},
        },
    },
    filters: {
        questionAnswerDataFilter(val) {
            let obj = QuestionAnswerData.find(
                s => s.OptionIndex == val
            );
            if (obj) {
                return obj.Title;
            }
            return "无";
        },
        submitItemTypeFilter(val) {
            let obj = QuestionAnswerType.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "无";
        },
    },
    data() {
        return {
            QuestionAnswerData,
            QuestionAnswerType,
            
            loading: false,
            activeEmployeeId: '',
            ParticipantEmployeeList: []
        };
    },
    computed: {
        pageTitle() {
            if(this.loading){
                return '加载中...'
            }
            return this.node.Name||"详情";
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getDetail();// 查询 基本信息
                }
            },
            immediate: true
        }
    },
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    mounted() {},
    methods: {
        // 点击切换时  清空已校验的结果
        tabClick(employeeItem){
            this.activeEmployeeId = employeeItem.EmployeeId;
            this.$refs.questionRef.scrollTop = 0
        },
        // 查询 基本信息
        getDetail() {
            let self = this;
            self.loading = true
            SurveyApi.GetSurveyGroupDetailsByEmployeeId({
                Id: self.node.SurveyGroupId,
                EmployeeId: self.node.EmployeeId
            }).then(res => {
                self.ParticipantEmployeeList = res.map(q=>{
                    if(q.QuestionAnswerData.length==0) {
                        q.QuestionAnswerData = JSON.parse(JSON.stringify(self.QuestionAnswerData))
                    }
                    return q
                });
                self.activeEmployeeId = res[0].EmployeeId
                self.loading = false
            }).catch(err => {
                self.loading = false
            });
        },
        /**清理表单 */
        resetFormModel() {
            this.formModel = this.$options.data().formModel
        },
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.appDialogRef.createData(self.formModel);
        },

        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        }
    }
};
</script>


<style scoped>
.dialogMain >>> .el-dialog__body{
    padding: 0 !important;
}
/* .opr >>> .el-form-item__content,.opr >>> .el-form-item__label{
    font-size: 12px;
} */
.pageWarp >>> .el-tabs__header{
    margin: 0;
}
.pageWarp >>> .el-tabs__item{
    padding: 0 10px;
}
.pageWarp >>> .el-form-item__label{
    padding: 0;
}
.pageWarp_main >>> .el-tabs__item{
    padding: 0 10px 0 0!important;
    text-align: left;
}
.not_backface >>> .el-radio__input.is-checked .el-radio__inner{
    background: #82bfff !important;
}
.not_backface >>> .el-radio__input.is-disabled.is-checked .el-radio__inner::after{
    background-color: #fff;
}
</style>
<style lang="scss" scoped>
.flexWarp{display: flex;}
.flexColumn{flex: 1;}
.pageWarp{
    height: 450px;
    display: flex;
    flex-direction: column;
    &_main{
        // display: flex;
        height: 450px;
        display: flex;
        overflow: hidden;
        .tabsBox{
            border-right: 1px solid #dcdfe6;
            height: 100%;
            overflow: hidden;
            overflow-y: auto;
            &_item{
                min-width: 120px;
                position: relative;
                background-color: #fff;
                padding: 8px 10px 8px 50px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                cursor: pointer;
                &_userPhoto{
                    position: absolute;
                    left: 15px;
                    top: 4px;
                    width: 26px;
                    height: 26px;
                    border-radius: 50%;
                    overflow: hidden;
                }
                &_icon{
                    position: absolute;
                    left: 2px;
                    top: 14px;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    overflow: hidden;
                    background-color: #F56C6C;
                    &.active{
                        background-color: #409EFF;
                    }
                }
                &.active{
                    color: #409EFF;
                    background-color: #ecf5ff;
                }
                &:hover{
                    background-color: #F5F7FA;
                }
            }
        }
        .opr{
            flex: 1;
        }
        .question{
            width: 100%;
            height: 450px;
            padding-left: 10px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            overflow-y: auto;
            &_title{
                font-weight: 700;
                padding-top: 15px;
                padding-bottom: 10px;
            }
            &_item{
                padding-top: 10px;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 20px;
                &_title{
                    padding-bottom: 10px;
                }
            }
        }
    }
}
</style>


