<template>
    <div>
        <el-dialog
            v-el-drag-dialog
            class="dialog-mini"
            width="800px"
            :title="'拜访记录'"
            :visible.sync="dialogVisible"
            :close-on-click-modal='false'
            :append-to-body='true'
            >
            <div v-show="editableForVistedLog" class="btn-wrap">
                <el-button type="primary" @click="handleAdd">新增拜访记录</el-button>
            </div>
            <el-form label-position="right" label-width="80px" v-loading='loading' style="max-height: 600px; overflow-y: auto;">
                <div class="viste-item-wrap" v-for="(v, idx) in list" :key="idx">
                    <div class="viste-btns-wrap">
                        <app-table-row-button :type='1' text="评论" @click="onComment(v, idx)"></app-table-row-button>
                        <app-table-row-button v-show="editableForVistedLog" :type='1' @click="update(v)"></app-table-row-button>
                        <app-table-row-button v-show="editableForVistedLog" :type='3' @click="del(v)"></app-table-row-button>
                    </div>
                    <div class="comment-item">
                        <el-row>
                            <el-col :span="11">
                                <el-form-item :label="'被拜访者'">
                                    <span>
                                        {{ v.VisitCustomer }}
                                    </span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item :label="'创建人'">
                                    <span v-if="v.CreateEmployee && v.CreateEmployee.length > 0">
                                        {{ v.CreateEmployee[0].Name }}({{ v.CreateEmployee[0].Number }})&nbsp;&nbsp;&nbsp;&nbsp;{{ v.CreateDateTime | dateFilter('YYYY-MM-DD HH:mm') }}
                                    </span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item :label="'拜访目的'">
                                    {{ v.VisitPurpose }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item :label="'拜访时间'">
                                    {{ v.VisitTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item :label="'拜访地点'">
                                    {{ v.VisitSite }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item :label="'拜访纪要'">
                                    {{ v.VisitSummary }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <comment-list v-if="dialogVisible" :businessType="'visited'" :ref="`commentList${idx}`" :key="idx" :id="v.CustomerVisitId" :optType="0"></comment-list>
                    </div>
                </div>
            </el-form>

            <pagination v-show="total>0 && isShowPager" :total="total" :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            <div slot="footer">
                <slot name="other-button-area"></slot>
                <el-button size="mini" @click="handleClose">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog
            v-el-drag-dialog
            class="dialog-mini"
            width="800px"
            :title="'拜访记录'"
            :visible.sync="dialogOfViste"
            :close-on-click-modal='false'
            :append-to-body='true'
            >
            <el-form
                :rules="rules"
                ref="dataForm"
                :model="temp"
                label-position="right"
                label-width="90px"
                v-if="dialogOfViste"
                style="max-height: 600px; overflow-y: auto;"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="'被拜访者'" prop="VisitCustomer">
                            <el-input :disabled="true" v-model="temp.VisitCustomer"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'拜访目的'" prop="VisitPurpose">
                            <el-input v-model="temp.VisitPurpose"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'拜访时间'" prop="VisitTime">
                            <!-- <el-input v-model="temp.VisitTime"></el-input> -->
                            <el-date-picker v-model="temp.VisitTime" align="right" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%;"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'拜访地点'" prop="VisitSite">
                            <el-input v-model="temp.VisitSite"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'创建人'" prop="CreateEmployee">
                            <emp-selector :showType="2" :readonly="true" :multiple="false" :list="temp.CreateEmployee" @change="handleChangeOwnerUsers"></emp-selector>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item :label="'拜访纪要'">
                            <el-input
                                type="textarea"
                                :rows="5"
                                placeholder="请输入内容"
                                v-model="temp.VisitSummary">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer">
                <slot name="other-button-area"></slot>
                <el-button size="mini" @click="handleVisteDialogClose">取消</el-button>
                <el-button size="mini" type="primary" :loading='postLoading' @click="createData">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import { getUserInfo } from '@/utils/auth'
import EmpSelector from "../common/empSelector"
import CommentList from "../task/commentList"
import * as users from '@/api/users'
import * as cv from '@/api/customerVisit'
import indexPageMixin from '@/mixins/indexPage'

export default {
    name: 'visted-list',
    components: {
        EmpSelector,
        CommentList,
    },
    directives: {
        elDragDialog
    },
    mixins: [indexPageMixin],
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            required: true
        },
        vistedType: { //1: 客户单位；2：客户干系人；3：消息
            type: Number,
            required: true
        },
        editableForVistedLog: { //是否能够操作（新增、编辑、删除）拜访记录（不包括评论、回复）
            type: Boolean,
            default: true
        },
        isShowPager: {
            type: Boolean,
            default: true
        },
        visitCustomer: { //被拜访人
            type: String,
            default: ''
        }
    },
    watch: {
        visible(val) {
            this.dialogVisible = val
            if(val) {
                this.getList()
            }
        },
        dialogVisible(val) {
            if(!val) {
                this.$emit('close')
            }

        },
        dialogOfViste(val) {
            if(val && this.dialogStatus == 'create') {
                this.resetTemp()
                this.temp.VisitCustomer = this.visitCustomer
                let currUser = getUserInfo();
                let currEmp = {
                    EmployeeId: currUser.employeeid, //员工id
                    Name: currUser.empName || "", //员工名称
                    Number: currUser.empNumber || "", //员工编号
                    Avatar: currUser.avatar
                };

                this.temp.CreateEmployee.push(currEmp);
            }
        }
    },
    computed: {

    },
    created() {
        this.rules = this.initRules(this.rules)

    },
    data() {
        return {
            total: 0,
            list: [

            ],
            rules: {
                VisitCustomer: { fieldName: "被拜访者", rules: [{ required: true }]},
                VisitPurpose: { fieldName: "拜访目的", rules: [{ required: true }]},
                VisitTime: { fieldName: "拜访时间", rules: [{ required: true }]},
                VisitSite: { fieldName: "拜访地点", rules: [{ required: true }]},
                CreateEmployee: { fieldName: "拜访人", rules: [{ required: true }]},
            },
            dialogVisible: this.visible,
            dialogOfViste: false,
            postLoading: false,
            loading: false,
            temp: {
                VisitCustomer: '',
                VisitPurpose: '',
                VisitTime: '',
                VisitSite: '',
                CreateEmployee: [], //单选
                VisitSummary: '',
                CustomerOrganizationDetailId:'',
                CustomerArchiveId:''
            },
            listQuery:{
              CustomerOrganizationDetailId:"",
              CustomerArchiveId:""
            },
            dialogStatus: '',
        }
    },
    methods: {
        resetTemp() {
            this.temp = {
                VisitPurpose: '',
                VisitTime: '',
                VisitSite: '',
                CreateEmployee: [], //单选
                VisitSummary: '',
                CustomerOrganizationDetailId:'',
                CustomerArchiveId:''
            }
        },
        del(v) {
            let id = v.CustomerVisitId
            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                cv.deleteCustomerVisit([id]).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        update(v) {
            this.dialogStatus = 'update'
            this.dialogOfViste = true
            this.temp = Object.assign({}, this.temp, v)
        },
        onComment(item, idx) {
            this.$refs['commentList' + idx][0].onComment()
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size
            this.getList()
        },        
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page
            this.listQuery.PageSize = val.size
            this.getList()
        },
        handleChangeOwnerUsers(users) {
            if (users && users.length > 0) {
                this.temp.CreateEmployee = [users[0]];
            } else {
                this.temp.CreateEmployee = [];
            }
        },
        handleAdd() {
            this.dialogStatus = 'create'
            this.dialogOfViste = true
        },
        handleVisteDialogClose() {
            this.dialogOfViste = false
        },
        getList() {
            if(this.vistedType == 1){
                  this.listQuery.CustomerOrganizationDetailId = this.id;
            }else if(this.vistedType == 2){
                  this.listQuery.CustomerArchiveId = this.id;
            }
            this.loading = true
            if(this.vistedType == 1 || this.vistedType == 2){
                cv.getCustomerVisitListPage(this.listQuery).then(response =>{
                    this.loading = false
                    this.list =  response.Items.map(s => {
                        if(s.CreateEmployee) {
                            s.CreateEmployee = [s.CreateEmployee]
                        }else{
                            s.CreateEmployee = []
                        }
                        return s
                    })
                    this.total = response.Total
                }).catch(err => {
                    this.loading = false
                });
            }

            if(this.vistedType == 3) {
                cv.getDetailByMessageId({messageId: this.id}).then(res => {
                    this.loading = false
                    if(res.CreateEmployee) {
                        res.CreateEmployee = [res.CreateEmployee]
                    }else{
                        res.CreateEmployee = []
                    }
                    this.list = [res]
                    this.total = 1
                }).catch(err => {
                    this.loading = false
                })
            }
        },
        handleClose() {
            this.dialogVisible = false
        },
        createData() {
            let self = this;
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                   if(this.vistedType==1){
                        this.temp.CustomerOrganizationDetailId=this.id;
                   }else{
                        this.temp.CustomerArchiveId=this.id;
                   }
                    let formData = JSON.parse(JSON.stringify(this.temp))

                    formData.CreateEmployeeId = this.temp.CreateEmployee[0].EmployeeId
                    delete formData.CreateEmployee

                    cv.editCustomerVisit(formData).then(response => {
                         this.dialogOfViste = false;
                         this.getList();
                    });
                }
            })
        },

    },
}
</script>

<style scoped>


.btn-wrap{
    text-align: right;
}

.viste-item-wrap >>> .el-form-item{
    margin-bottom: 0!important;
}

.viste-item-wrap{
    position: relative;
}

.viste-btns-wrap{
    position: absolute;
    top: 0;
    right: 10px;
    z-index: 9999;
}

.comment-item{
    padding-top: 10px;
    border-top: 1px solid #e9e9e9;
}

</style>
