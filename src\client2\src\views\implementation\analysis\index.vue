<template>
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title title="项目/工程数据统计" :subTitle='["实施工程/安装设备的相关数据统计、分析页面"]'></page-title> -->
      <div class="page-wrapper">
        <!-- <div class="tagBox">
          <tags :items='types' v-model="tagType" @change='handleTagsChange'>
              <template v-for="t in types" :slot="t.value">
                  {{ t.label }}
              </template>
          </tags>
        </div> -->
        
        
        <div class="line-40 pl-10">
          <span class="demonstration">统计年份：</span>
          <el-date-picker
            v-model="yearVal"
            type="year"
            placeholder="选择年"
            :clearable='false'
            style="width:100px;"
            value-format="yyyy"
            @change='handleClickYear'>
          </el-date-picker>
          <el-button type="text" @click="getNowYear()">当前年份</el-button>
        </div>
        <div class="main">
          <v-button-list v-model="mValue" :btnListData='monList' @change="getMonthData"></v-button-list>
          <div class="monBottom shadow">
            <div class="blockTitle pl-10 line-40">
              <span>全国实施工程数量({{nationalData}})</span>
            </div>
            <div class="chartBox cl" v-loading="loading1">
              <div class="fl">
                <div v-show="topData.length>0" id="myChart" style="width:auto;height:400px;"></div>
                <no-data v-show="topData.length==0"></no-data>
              </div>
              <div class="fl">
                <div v-show="topData.length>0" id="myChart1" style="width:100%;height:400px;"></div>
                <no-data v-show="topData.length==0"></no-data>
              </div>
            </div>
          </div>
          <v-button-list v-model="areaValue" :btnListData='areaList' @change="getEquipmentData"></v-button-list>
          <div class="monBottom">
            <div class="chartBox1 cl">
              <div class="fl shadow" v-loading='loading2'>
                <div class="blockTitle pl-10 line-40" style="min-width:520px;">
                  <span>设备数量分析({{devicesNum}})</span>
                </div>
                <div v-show="equipmentData.length>0" id="myChart2" style="min-width:520px;width:100%;height:400px"></div>
                <no-data style="height:400px;" v-show="equipmentData.length==0"></no-data>
              </div>
              <div class="fl shadow" v-loading='loading3'>
                <div class="blockTitle pl-10 pr-10 line-40 cl" style="min-width:520px;">
                  <span class="fl">设备类型分析({{typeNum}})</span>
                  <el-select class='fr' v-model="region" placeholder="请选择活动区域">
                    <el-option @click.native="handleSelectChange(rl)" v-for="(rl,index) in regionList" :label="rl.label" :value="rl.value" :key="index"></el-option>
                  </el-select>
                </div>
                <div id="myChart3" v-show="equipmentType.length>0" style="min-width:520px;width:99%;height:400px;"></div>
                <no-data style="height:400px;" v-show="equipmentType.length==0"></no-data>
              </div>
            </div>
          </div>
        </div>

        <!-- <div v-if="tagType == 1">
        </div> -->
        <!-- <div class="main" v-if="tagType == 2">
          <div style="height: 100%;">
            <rep :isShow='tagType == 2'></rep>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import * as businessMap from "@/api/businessMap";
import * as impManagement from "@/api/implementation/impManagement";
import * as productListManagement from '@/api/systemManagement/productListManagement';
import { downloadFile } from "@/utils/index";
import { listToTreeSelect } from '@/utils'
import vButtonList from '@/views/common/buttonList'
import NoData from "@/views/common/components/noData";
// import rep from './rep'
var echarts = require('echarts');
export default {
  name: "",
  components: {
    vButtonList,
    NoData,
    // rep
  },
  filters: {
    
    
  },

  data() {
    return {
      areaValue:null,
      mValue:null,
      // tagType:1,
      // types: [{value: 1, label: '实施数据看板'}, {value: 2, label: '实施数据报表'}],
      treeData:[],
      nationalData:0,
      topData:[],
      loading1:false,
      loading2:false,
      loading3:false,
      myChart:null,
      myChart1:null,
      myChart2:null,
      myChart3:null,
      region: '',
      regionList:[],
      monList:[{
        value:null,
        label:'全年'
      },{
        value:1,
        label:'1月'
      },{
        value:2,
        label:'2月'
      },{
        value:3,
        label:'3月'
      },{
        value:4,
        label:'4月'
      },{
        value:5,
        label:'5月'
      },{
        value:6,
        label:'6月'
      },{
        value:7,
        label:'7月'
      },{
        value:8,
        label:'8月'
      },{
        value:9,
        label:'9月'
      },{
        value:10,
        label:'10月'
      },{
        value:11,
        label:'11月'
      },{
        value:12,
        label:'12月'
      },],
      areaList:[],
      option :{
        grid:{
          y:36,
          y2:46,
        },
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#3498DB', // 颜色
            }
          },
          axisLabel: {
              color:'black',
              interval:0,
              interval:0,  
              // rotate:-20,
              formatter:function(value)  
                 {  
                      if(value.length>18) value=value.substring(0,18);
                     var ret = "";//拼接加\n返回的类目项  
                     var maxLength = 6;//每项显示文字个数  
                     var valLength = value.length;//X轴类目项的文字个数  
                     var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数  
                     if (rowN > 1)//如果类目项的文字大于3,  
                     {  
                         for (var i = 0; i < rowN; i++) {  
                             var temp = "";//每次截取的字符串  
                             var start = i * maxLength;//开始截取的位置  
                             var end = start + maxLength;//结束截取的位置  
                             //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧  
                             temp = value.substring(start, end) + "\n";  
                             ret += temp; //凭借最终的字符串  
                         }  
                         return ret;  
                     }  
                     else {  
                         return value;  
                     }  
                 }  
            },
          data: []
        },
        yAxis: {
          type: 'value',
          splitLine:{show: false},//去除网格线
          axisLine: {
            lineStyle: {
              color: '#3498DB', // 颜色
            }
          },
          axisLabel: {
            color:'black'
          },
        },
        series: [{
          data: [],
          type: 'bar',
          barWidth: 40,
          showBackground: true,
          label: {
              show: true,
              position: 'inside'
          },
          itemStyle:{
            normal:{
              color:'#3498DB'
            }
          },
          backgroundStyle: {
            color: 'white',
            borderWidth: 1 ,
            borderType: 'solid' ,
            borderColor:'#DCDFE6'
          },
        }]
      },
      devicesNum:0,
      equipmentData:[],
      typeNum:0,
      equipmentType:[],
      yearVal:new Date(),
      areaData:null,
      typeId:'',
      firstUpdate:true,
      saveOverAllId:'全部',
    };
  },
  computed: {
    
  },
  
  watch: {
    // tagType: {
    //   handler(val) {
    //     let that=this;
    //     if(val == 1) {
    //       console.log(that.myChart,that.myChart1)
    //       if(that.myChart) {
    //         window.onresize = function(){
    //           that.myChart.resize();
    //         }
    //       }
    //       if(that.myChart1){
    //         window.onresize = function(){
    //           that.myChart1.resize();
    //         }
    //       }
    //     }
    //   },
    //   immediate: true
    // }
  },
  created() {
    
  },
  mounted() {
    // this.getMonthData(this.monList[0]);
    this.getRegional();
    
    
  },
  methods: {
    // handleTagsChange(val){
    //   if(val == 1){
    //     this.getMonthData(this.monList[0]);
    //     this.getRegional();
    //   }
    // },
    getNowYear(){
      this.yearVal=new Date();
      this.handleClickYear();
    },
    handleSelectChange(d){
      this.saveOverAllId=d.label;
      this.typeId=d.value;
      this.getEquipmentTypeData(d.value);
    },
    getRegional(){
      businessMap.getListByCondition({RegionalName:"",Level:1}).then(res => {
        this.areaList = listToTreeSelect(res);
        this.areaList.forEach(v => {
          v.value=v.Id;
          v.label=v.RegionalName;
        })
        this.areaValue=this.areaList[0].Id;
        this.getEquipmentData(this.areaList[0].Id);
      })
    },
    handleClickYear(d){
      this.getData();
      this.areaValue=this.areaData;
      this.getEquipmentQuantityData();
      this.getEquipmentTypeData(this.typeId);
    },
    getEquipmentData(d){
      if(d){
        this.areaData=d;
        this.getEquipmentQuantityData();
        this.typeId=d;
        this.saveOverAllId='全部';
        this.getEquipmentTypeData(d);
      }
    },
    //设备数量分析
    getEquipmentQuantityData(){
      this.loading2=true;
      let params={
        "RegionalId": this.areaValue,
        "Year": new Date(this.yearVal).getFullYear(),
        "Month": this.mValue
      }
      this.devicesNum=0;
      impManagement.getEquipmentQuantity(params).then(res => {
        this.equipmentData=res;
        this.region=this.areaValue;
        this.regionList=[{label:'全部',value:this.areaValue}];
        if(res.length>0){
          res.forEach(v => {
            this.devicesNum+=v.Total;
            this.regionList.push({label:v.RegionalName,value:v.RegionalId});
          })
          let [ld,sd] = this.getPieChartData(res);
          this.inItPieChart(this.myChart2,ld,sd,'myChart2');
        }
        this.loading2=false;
      }).catch(e => {
        this.loading2=false;
      })
    },
    //设备类型分析
    getEquipmentTypeData(id){
      this.loading3=true;
      let params={
        "RegionalId": id,
        "Year": new Date(this.yearVal).getFullYear(),
        "Month": this.mValue,
        "IsAll": this.saveOverAllId == '全部' ? true : false
      }
      impManagement.getEquipmentType(params).then(res => {
        this.equipmentType=res;
        this.typeNum=0;
        if(res.length>0){
          res.forEach(v => {
            this.typeNum+=v.Total;
          })
          let [ld,sd] = this.getPieChartData(res);
          this.inItPieChart(this.myChart3,ld,sd,'myChart3');
        }
        this.loading3=false;
      }).catch(e => {
        this.loading3=false;
      })
    },
    getMonthData(){
      this.getData();
      //firstUpdate防止第一次进入页面时重复调用
      if(!this.firstUpdate){
        this.areaValue=this.areaData;
        this.getEquipmentQuantityData();
        this.getEquipmentTypeData(this.typeId);
      }else{
        this.firstUpdate=false;
      }
    },
    getData(){
      this.loading1=true;
      let params={
        "Year": new Date(this.yearVal).getFullYear(),
        "Month": this.mValue
      }
      impManagement.getNationwideDataChart(params).then(res => {
        this.topData=res;
        this.inItBarChart(res);
        let [ld,sd] = this.getPieChartData(res);
        this.inItPieChart(this.myChart1,ld,sd,'myChart1');
      }).catch(e => {
        this.loading1=false;
      })
    },
    getPieChartData(res){
      let [ld,sd]=[[],[]];
      res.forEach(v => {
        ld.push((v.RegionalName || v.ProductName)+'('+v.Total+')');
        sd.push({value: v.Total, name: (v.RegionalName || v.ProductName)+'('+v.Total+')',label:{show:v.Total > 0 ? true : false,position:'inside'}});
      })
      return [ld,sd];
    },
    inItBarChart(d){
      this.option.xAxis.data=[];
      this.option.series[0].data=[];
      this.nationalData=0;
      d.forEach(v => {
        this.nationalData+=v.Total;
        this.option.xAxis.data.push(v.RegionalName);
        this.option.series[0].data.push(v.Total);
      })
      this.myChart = echarts.init(document.getElementById('myChart'));
      this.myChart.setOption(this.option,true);
      this.$nextTick(() => {
        this.myChart.resize();
      })
      this.loading1=false;
    },
    inItPieChart(box,ld,sd,id){
      if(ld.length>10){
        let allValue=0;
        sd.forEach(v => {
          allValue+=v.value;
        })
        let isOther=sd.some(v => (v.value/allValue<0.05));
        if(isOther){
          let otherValue=0;
          sd.forEach((v,i) => {
            if(v.value/allValue < 0.05){
              otherValue+=v.value;
            }
          })
          let sdO=sd.filter(v => (v.value/allValue >= 0.05));
          sdO.push({
            label: {show: true, position: "inside"},
            name: "其它("+otherValue+')',
            value: otherValue
          })
          ld=[];
          sdO.forEach(v => {
            ld.push(v.name);
          })
          sd=sdO;
        }
      }
      let option = {
        legend: {
          type: 'scroll',
          icon:"circle",
          orient: 'vertical',
          left: '72%',
          top: 'middle',
          data: ld,
          formatter: function (name) {
            if (!name) return '';
              if (name.length > 15) {
                name =  name.slice(0,15) + '...';
            }
            return name;
          },
          tooltip: {
            show: true
          }
        },
        //设置饼状图每个颜色块的颜色
        color : [ '#1BBC9D', '#3498DB', '#2DA2BF', '#EE7C31', '#EBC4DB','#336699','#cccccc','#cc9966','#cc3333','#ff6666','#99cc66','#003399','#009966','996699'],
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
          formatter: function (params, ticket, callback) {
              var showHtm="";
              var index = params.name.lastIndexOf('(');
              if(index == -1){
                index=params.name.length;
              }
              var name =params.name.substring(0,index)
              showHtm=name+': '+params.value+' ('+params.percent+'%)';
              return showHtm;
          }
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '75%',
            center: ['40%', '50%'],
            data: sd,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            // 设置值域的标签
            label: {
              normal: {
                // position: 'inner',  // 设置标签位置，默认在饼状图外 可选值：'outer' ¦ 
                formatter: "{d}%"
              }
            }
          }
        ]
      };
      if(id != 'myChart1'){
        option.color=['#3498DB','#DD7195','#2DA2BF','#EE7C31','#FFAF00','#336699','#cccccc','#cc9966','#cc3333','#ff6666','#99cc66','#003399','#009966','996699'];
        option.series[0].center=['31.5%', '50%'];
        option.legend.left='63%';
      }
      if(id == 'myChart1'){
        this.myChart1 = echarts.init(document.getElementById('myChart1'));
      }
      box = echarts.init(document.getElementById(id));
      box.setOption(option);
      this.$nextTick(() => {
        box.resize();
      })
      window.addEventListener('resize',function(){
        box.resize();
      })
      // this.myChart2 = echarts.init(document.getElementById('myChart2'));
      // this.myChart2.setOption(option);
      // this.myChart3 = echarts.init(document.getElementById('myChart3'));
      // this.myChart3.setOption(option);
    },
  }
};
</script>

<style lang="scss" scoped>
.app-container{
  overflow-y:auto;
}

.tagBox{
  border-bottom: 1px solid #EBEEF5;
  padding: 4px 8px;
}

.main{
  padding:10px;
  position: absolute;
  width: 100%;
  height: calc(100% - 30px);
  .monBottom{
    margin-top: 10px;
    margin-bottom: 20px;
    border:1px solid #ccc;
  }
  .monBottom:last-child{
    border:0;
  }
}
.chartBox{
  >div{
    width:50%;
    height:400px;
    
  }
  >div:first-child{
    overflow-x: auto;
  }
}
.chartBox1{
  >div{
    width:calc(50% - 10px);
    height:455px;
    border:1px solid #ccc;
    overflow-x: auto;
  }
  >div:last-child{
    margin-left:20px;
  }
}
.line-40{
  border-bottom: 1px solid #DCDFE6;
}
.blockTitle{
  background:#ccc;
  >span:first-child{
    font-size: 16px;
    font-weight: 600;
  }
}
.shadow{
  box-shadow:2px 2px 5px -2px rgba(0, 0, 0, 0.5); 
}
</style>