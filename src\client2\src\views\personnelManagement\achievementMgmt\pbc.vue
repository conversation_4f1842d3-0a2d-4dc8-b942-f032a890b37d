<template>
    <div class="content">
        <div class="form-wrapper">
            <el-form ref="formData" label-position="right" label-width="100px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="考核年份">
                            {{ detailInfo.Year }} / {{ detailInfo.HalfYearType | halfyearTypeFilter }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="考核类型">
                            {{ detailInfo.AppraiseType | appraiseTypeFilter }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="公示期设置">
                            <!-- {{ detailInfo.PublicPeriodSet | publicPeriodSetFilter }} -->
                            {{ detailInfo.PublicPeriodSet | autoEndTypeFilter }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="年度目标">
                            {{ detailInfo.YearGoals }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="相关附件">
                            <app-uploader :readonly="true" accept="all" :fileType="3" :max="10000"
                                :value="detailInfo.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024"
                                ref="appuploader"></app-uploader>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
        </div>

        <app-table-core
            ref="mainTable"
            :tab-columns="tabColumns"
            :tab-datas="detailInfo.AppraisePlanList"
            :tab-auth-columns="[]"
            :isShowAllColumn="true"
            :loading="false"
            :isShowOpatColumn="detailInfo.ProgressStatus < 6"
            :isShowConditionArea='false'
            :isShowBtnsArea='false'
            :startOfTable="0"
            :multable="false"
        >
            <template slot="PrincipalEmployeeList" slot-scope="scope">
                <span v-if="scope.row.PrincipalEmployeeList">
                    {{ scope.row.PrincipalEmployeeList.map(s => s.Name).join(',') }}
                </span>
            </template>
            <template slot="FinalEmployeeList" slot-scope="scope">
                <span v-if="scope.row.FinalEmployeeList">
                    {{ scope.row.FinalEmployeeList.map(s => s.Name).join(',') }}
                </span>
            </template>
            <template slot="ObjectEmployeeList" slot-scope="scope">
                <span v-if="scope.row.ObjectEmployeeList">
                    {{ scope.row.ObjectEmployeeList.length }}
                </span>
            </template>
            <template slot="Headcount" slot-scope="scope">
                <span :style="{color: scope.row.SubmitPersonalPerformanceCount < scope.row.Headcount ? '#FF0000' : '#409EFF' }">
                    {{ scope.row.SubmitPersonalPerformanceCount }} / {{ scope.row.Headcount }}
                </span>
            </template>
            <template slot="MidtermReviewState" slot-scope="scope">
                <span class="item-status" :style="{backgroundColor: getAppraisePromiseStatus(scope.row.MidtermReviewState).color}">
                    {{ getAppraisePromiseStatus(scope.row.MidtermReviewState).label }}
                </span>
            </template>
            <template slot="PerformanceAppraisalState" slot-scope="scope">
                <span class="item-status" :style="{backgroundColor: getAppraisePromiseStatus(scope.row.PerformanceAppraisalState).color}">
                    {{ getAppraisePromiseStatus(scope.row.PerformanceAppraisalState).label }}
                </span>
            </template>
            <template slot="PerformanceFinalState" slot-scope="scope">
                <span class="item-status" :style="{backgroundColor: getAppraisePromiseStatus(scope.row.PerformanceFinalState).color}">
                    {{ getAppraisePromiseStatus(scope.row.PerformanceFinalState).label }}
                </span>
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
                <app-table-row-button @click="handlePbcDialog(scope.row)" :type="1"></app-table-row-button>
            </template>
        </app-table-core>
        <!-- <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.PageIndex"
            :size.sync="listQuery.PageSize"
            @pagination="handleCurrentChange"
            @size-change="handleSizeChange"
        /> -->

        <edit-page 
            @closeDialog='closePbcDialog' 
            @saveSuccess='handlePbcSaveSuccess'
            :dialogFormVisible='dialogPbcFormVisible'
            :dialogStatus='dialogPbcStatus' 
            :id='pbcId'
        >
        </edit-page>
    </div>
</template>

<script>
import editPage from './edit'
import noData from "../../common/components/noData"
import { appraisePersonalsStatusEnum, appraisePromiseStatusEnum, yearTypeEnum, appraiseTypeEnum, publicPeriodSetEnum, autoEndTypeEnum } from "./enum";

export default {
    name: "pbc-detail",
    components: {
        noData,
        editPage,
    },
    created() {
    },
    props: {
        detail: {
            type: Object,
            default: {}
        }
    },
    watch: {
        detail: {
            handler(val) {
                this.detailInfo = JSON.parse(JSON.stringify(val))
            },
            deep: true,
            immediate: true
        }
    },
    filters: {
        halfyearTypeFilter(val) {
            let obj = yearTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        appraiseTypeFilter(val) {
            let obj = appraiseTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        publicPeriodSetFilter(val) {
            let obj = publicPeriodSetEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
        autoEndTypeFilter(val) {
            let obj = autoEndTypeEnum.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return ''
        },
    },
    mounted() {},
    data() {
        return {
            listtnLoading: false,
            tabColumns: [
                {
                    attr: { prop: "DepartmentName", label: "部门名称" }
                },
                {
                    attr: { prop: "PrincipalEmployeeList", label: "考核主管" },
                    slot: true
                },
                {
                    attr: { prop: "FinalEmployeeList", label: "终审人" },
                    slot: true
                },
                {
                    attr: { prop: "ObjectEmployeeList", label: "被考核人", },
                    slot: true
                },
                {
                    attr: { prop: "Headcount", label: "个人绩效承诺", },
                    slot: true
                },
                {
                    attr: { prop: "MidtermReviewState", label: "中期审视", },
                    slot: true
                },
                {
                    attr: { prop: "PerformanceAppraisalState", label: "绩效考核", },
                    slot: true
                },
                {
                    attr: { prop: "PerformanceFinalState", label: "绩效终审", },
                    slot: true
                },
            ],
            tabDatas: [],
            detailInfo: {},

            dialogPbcFormVisible: false,
            dialogPbcStatus: 'update',
            pbcId: '',
        
        };


    },
    methods: {
        getAppraisePromiseStatus(val) {
            return appraisePromiseStatusEnum.find(s => s.value == val) || {}
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        //获取项目列表
        getList() {
            
        },

        handlePbcDialog(row) {

            this.pbcId = row.Id
            this.dialogPbcFormVisible = true
        },
        closePbcDialog() {
            this.dialogPbcFormVisible = false
        },
        handlePbcSaveSuccess() {
            this.$emit('reload')
            this.closePbcDialog()
        },
        
    }
};
</script>

<style lang="css" scoped>
.content >>> .el-form-item{
    margin-bottom: 0px!important;
}
</style>

<style lang="scss" scoped>
.content{
    .form-wrapper{
        border-bottom: 1px solid #DCDFE6;
        padding-bottom: 10px;
    }
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}
</style>
