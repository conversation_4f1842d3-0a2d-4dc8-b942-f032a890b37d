<template>
<div class="wrapper" v-loading="Loading">
    <el-form :rules="rules" ref="formData" :model="formData" class="create_body"
    label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
        <div class="app-container pageBody">
            <page-title :showBackBtn='true' @goBack="handleGoBack(backUrl)">
                <div slot="def">
                    <el-button type="success" @click="createData(true)" :disabled="buttonLoading">存为草稿</el-button>
                    <el-button type="primary" @click="createData(false)" :disabled="buttonLoading">发布/审核</el-button>
                </div>
            </page-title>
            <div class="pageBody_head">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="文章封面" prop="CoversFileList">
                            <app-upload-file :max='1' :fileSize="1024 * 1024 * 2" :fileType='1'
                            :value='formData.CoversFileList'
                            @change='handleUpChange' :preview='true'></app-upload-file>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="文章分类" prop="ClassifyId">
                            <treeselect :normalizer="normalizer"
                                class="treeselect-common"
                                :options="treeData" :default-expand-level="3" @select="onTreeSelect"
                                :multiple="false" :open-on-click="true" :open-on-focus="true"
                                :clear-on-select="true" :value="formData.ClassifyId" placeholder=""
                                :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"
                                :append-to-body="true" zIndex='9999'>
                            </treeselect>
                        </el-form-item>
                        <el-form-item label="发布人" prop="PublisherList">
                            <emp-selector :beforeConfirm='handlePublisherListBeforeConfirm' :showType="2"
                            :multiple="true" :list="formData.PublisherList" @change="handlePublisherListChangeManager"></emp-selector>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <div class="pageBody_main">
                <el-form-item label="文章标题" prop="Title">
                    <el-input maxlength="50" type="text" v-model="formData.Title"></el-input>
                </el-form-item>
                <el-form-item label="关键字" prop="Introduction">
                    <el-input maxlength="100" type="textarea" :rows="2" v-model="formData.Introduction"></el-input>
                </el-form-item>
                <el-form-item label="文章内容" prop="Content">
                    <editor-bar :value="formData.Content" @edit="editorChange"></editor-bar>
                </el-form-item>

                <el-form-item label="附件" prop="AttachmentList" class="b6">
                    <!-- <app-upload-big-file :limitTotalSize="1024 * 1024 * 1024" :max="10000" accept="all" :fileType="4" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" @change="handleFilesUpChange"></app-upload-big-file> -->
                    <app-uploader
                        accept='all'
                        :fileType='3'
                        :max='10000'
                        :value='formData.AttachmentList'
                        :fileSize='1024 * 1024 * 500'
                        :minFileSize='100 * 1024'
                        @change='handleFilesUpChange'
                    ></app-uploader>
                </el-form-item>
            </div>
        </div>
    </el-form>

    <add-comment v-if="addCommentFormVisible"
    :dialogFormVisible="addCommentFormVisible"
    @closeDialog="addCommentFormVisible = false"
    @saveSuccess="addCommentSaveSuccess"></add-comment>
</div>
</template>
<script>
import * as classifyApi from '@/api/classify'
import * as KnowledgeApi from '@/api/knowledge/Knowledge'
import { listToTreeSelect } from "@/utils";
import empSelector from "@/views/common/empSelector";
import Treeselect from "@riophae/vue-treeselect";
import EditorBar from "@/components/QuillEditor/index.vue";
import addComment from "./addComment";
export default {
    name: 'knowledge-management-create',
    components: {
        EditorBar,
        empSelector,
        addComment,
        Treeselect,
    },
    computed: {
        backUrl() {
            let url = decodeURIComponent(this.$route.query.backUrl||'')
            console.log(this.$route.query)
            return url
        },
    },
    data() {
        return {
            id: "",
            pageStatus: "create",
            vHtml: '',
            defImgUrl: require('../../../assets/images/materialData_notData.png'),
            defavatar: require("../../../assets/images/avatar3.png"),
            addCommentFormVisible: false,
            buttonLoading: false,
            Loading: false,
            

            labelWidth: "100px",
            treeData: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },
            formData: {
                ClassifyId: null, // 文章分类 id
                Content: "", // 文章内容
                Covers: "", // 文章封面 Id
                CoversPath: "" , // 文章封面 url
                CoversFileList: [], // 文章封面
                PublisherList: [], // 发布人id 集合
                Publisher: "", // 发布人id
                Introduction: "", // 关键字
                Title: "", // 文章标题
            },
            rules: {
                PublisherList: {fieldName: "发布人",rules: [{ required: true }]},
                Title: {fieldName: "文章标题",rules: [{ required: true }]},
                ClassifyId: {fieldName: "文章分类",rules: [{ required: true }]},
                Content: {fieldName: "文章内容",rules: [{ required: true }]},
                // Introduction: {fieldName: "关键字",rules: [{ required: true }]},
            },
        }
    },
    created() {
        this.formData.CoversFileList = [{
            Id: '_notData.png',
            Path: this.defImgUrl
        }]
        if(this.$route.query.id){
            this.id = this.$route.query.id
            this.pageStatus = 'update'
            this.getDetail();
        }
        if(this.$route.query.ClassifyId){
            this.formData.ClassifyId = this.$route.query.ClassifyId
        }
        
        this.rules = this.initRules(this.rules);
        this.getTreeData();
    },
    methods: {
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        onTreeSelect(node){
            this.formData.ClassifyId = node.Id
            this.$refs.formData.validateField('ClassifyId')
        },
        editorChange(){
            this.formData.Content = arguments[0]
            this.$refs.formData.validateField('Content')
        },
        // 提交按钮 type 是否草稿  true 草稿  false  发布审核
        createData(type = false){
            let self = this, postData = JSON.parse(JSON.stringify(this.formData));
            self.$refs.formData.validate(valid => {
                if (valid) {
                    postData.IsDraft = type;
                    delete postData.CoversPath;
                    delete postData.CoversFileList;
                    postData.Publisher = postData.PublisherList.map(s=>s.EmployeeId).toString();
                    postData.PublisherIdList = postData.PublisherList.map(s=>s.EmployeeId)
                    delete postData.PublisherList;
                    // console.log(postData)
                    postData.AttachmentIdList = postData.AttachmentList && this.formData.AttachmentList.map(s => s.Id);

                    let result = null;
                    self.buttonLoading = true;
                    if(self.pageStatus == 'create'){
                        delete postData.Id;
                        result = KnowledgeApi.add(postData);
                    }
                    if(self.pageStatus == 'update'){
                        result = KnowledgeApi.edit(postData);
                    }
                    result.then(response => {
                        self.$notify({
                            title: "成功",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.buttonLoading = false;
                        self.handleGoBack(self.backUrl);
                    }).catch(err => {
                        self.buttonLoading = false;
                    });
                } else {
                    return false;
                }
            });
        },
        handleUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.CoversPath = imgs[0].Path
                this.formData.Covers = imgs[0].Id
            } else {
                this.formData.Covers = ''
                this.formData.CoversPath = ''
            }
        },
        handlePublisherListBeforeConfirm(users) {
            if(users && users.length > 5) {
                this.$message({
                    message: '发布人不得超过5人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handlePublisherListChangeManager(users) {
            // console.log(users)
            if (users && users.length > 0) {
                this.formData.PublisherList = users;
            } else {
                this.formData.PublisherList = [];
            }
            this.$refs.formData.validateField('PublisherList')
        },
        getTreeData() {
            let postDatas = {
                PageIndex: 1,
                PageSize: 10000,
                BusinessType: 17
            }
            classifyApi.getListPage(postDatas).then(res => {
                var list = (res.Items || []).map((item, index, input) => {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId
                    };
                });
                this.treeData = listToTreeSelect(list, undefined, undefined, undefined, 'Id');
            });
        },
        handleAddComment(){
            this.addCommentFormVisible = true;
        },
        addCommentSaveSuccess(){
            this.addCommentFormVisible = false;
        },
        handleGoBack(backUrl) {
            this.$router.replace({
                path: backUrl || `/knowledge/knowledgeManagement`
            });
        },
        getDetail() {
            let self = this;
            self.Loading = true;
            KnowledgeApi.detail({Id: self.id}).then(res => {
                self.formData = Object.assign({}, self.formData, res);
                if(res.CoverPath&&res.Covers){
                    this.formData.CoversFileList = [{
                        Id: res.Covers,
                        Path: res.CoverPath,
                    }]
                }else{
                    this.formData.CoversFileList = [{
                        Id: '_notData.png',
                        Path: this.defImgUrl
                    }]
                }
                self.Loading = false;
            })
            .catch(err => {
                self.Loading = false;
            });
        },
    }
}
</script>
<style scoped>
.create_body >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>
<style lang="scss" scoped>
.flexWarp{
    display: flex;
}
.flexColumns{
    flex: 1;
}
.pageBody{
    display: flex;
    flex-direction: column;
    background-color: transparent;
    padding: 0 15%;
    &_head{
        width: 100%;
        padding: 15px 20px;
        line-height: 20px;
        border-bottom: 1px solid #EBEEF5;
        background: white;
        .describeBox{
            height: 50px;
            line-height: 50px;
            &_item{
                color: #aaaaaa;
                padding-left: 20px;
            }
        }
    }
    &_main{
        margin-top: 15px;
        padding: 15px;
        flex: 1;
        background: white;
        &_content{
            min-height: 200px;
            padding-bottom: 15px;
            border-bottom: 1px solid #EBEEF5;
        }
        &_comment{
            color: $text-main-color;
            line-height: 20px;
            .title{
                font-size: 18px;
                font-weight: 700;
                height: 60px;
                line-height: 40px;
                padding: 10px 0;
            }
            &_item+&_item{
                margin-top: 10px;
            }
        }
    }
}
</style>