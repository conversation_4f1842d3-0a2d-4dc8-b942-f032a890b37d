<template>
  <div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
      <!-- <page-title title="成功案例" :subTitle='["企业成功案例的发布、管理页面"]'></page-title> -->
      <div class="__dynamicTabWrapper">
        <app-table ref="mainTable" :isShowConditionArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :multable='false' :startOfTable="startOfTable">

          <template slot="CreateEmployee" slot-scope="scope">{{ scope.row.CreateEmployee | nameFilter }}</template>
          <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}</template>

          <!-- 表格查询条件区域 -->

          <!-- 表格批量操作区域 -->
          <template slot="btnsArea">
            <permission-btn style="margin-left:6px;" moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
          </template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)" :type='1'></app-table-row-button>
            <app-table-row-button  @click="handleReview(scope.row)" :type='2' ></app-table-row-button>
            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type='3' ></app-table-row-button>
          </template>
        </app-table>
      </div>
      <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList"></create-page>
  </div>
</template>

<script>

import * as successfulCases from '@/api/informationCenter/successfulCases'
import elDragDialog from '@/directive/el-dragDialog'
import createPage from "./create";
import indexPageMixin from '@/mixins/indexPage'
export default {
  name: 'successfulCases',
  components: {
    createPage
  },
  directives: {
    elDragDialog
  },
  created() {
    this.getList()
  },
  watch: {

  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
  },
  mounted() {

  },
  mixins: [indexPageMixin],
  data() {
    return {

      id: "",
      dialogStatus: "create",
      dialogFormVisible: false,

      multipleSelection: [],

      tabColumns: [
        {
          attr: { prop: 'CaseName', label: '案例名称', showOverflowTooltip: true },
        },
        {
          attr: { prop: 'CreateEmployee', label: '发布人' },
          slot: true
        }//,
        // {
        //   attr: { prop: 'CreateTime', label: '发布时间' },
        //   slot: true
        // }
      ],
      tabDatas: [],
      listLoading: false,
      listQuery: { // 查询条件
      },
      total: 0,
    }
  },

  methods: {

    //查看详情
    handleReview(row) {
      this.$router.push(`/informationCenter/productCenter/successfulCases/detail/${row.Id}`)
    },

    // 弹出编辑框
    handleUpdate(row, optType = 'update') {
      this.id = row.Id;
      this.dialogStatus = optType;
      this.dialogFormVisible = true;
    },

    onBtnClicked: function (domId) {
      switch (domId) {
        //我要发布
        case "btnAdd":
          this.handleDialog("create");
          break;
        //批量删除
        case "btnBatchDel":
          if (this.multipleSelection.length < 1) {
            this.$message({
              message: "至少删除一个",
              type: "error"
            });
            return;
          }
          this.handleDelete(this.multipleSelection);
          break;
        default:
          break;
      }
    },
    handleDialog(activeName) {
      this.dialogStatus = activeName;
      this.dialogFormVisible = true;
    },
    closeDialog() {
      this.dialogFormVisible = false;
    },
    handleSaveSuccess(_formData) {
      this.getList();
      this.closeDialog();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    getList() {
      this.listLoading = true
      successfulCases.getList(this.listQuery).then(response => {
        this.tabDatas = response.Items
        this.total = response.Total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.PageIndex = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page
      this.listQuery.PageSize = val.size
      this.getList()
    },
    // 多行删除
    handleDelete(rows) {
      let ids = []
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id)
      } else {
        ids.push(rows.Id)
      }

      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        successfulCases.del(ids).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
  }
}
</script>

<style scoped>
.sel-ipt,
.dat-ipt {
  width: 100%;
}
</style>
