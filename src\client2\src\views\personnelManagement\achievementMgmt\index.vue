<!--客服管理-->
<template>
  <!--组件内容区-->
  <div class="app-container">
    <div class="bg-white">
      <!-- <page-title title="考核计划管理" :subTitle="['考核部门管理、计划发起、跟进等绩效管理页面']"></page-title> -->
      <div class="pageWrapper __dynamicTabContentWrapper">
        <div class="content __dynamicTabWrapper">
          <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabData" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="tableLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :isShowConditionArea='false' :startOfTable="startOfTable" :multable="false">

            <template slot='Year' slot-scope="scope">
              {{ scope.row.Year }} / {{ scope.row.HalfYearType | halfyearTypeFilter }}
            </template>
            <template slot='PrincipalEmployeeList' slot-scope="scope">
              <span v-if="scope.row.PrincipalEmployeeList && scope.row.PrincipalEmployeeList.length > 0">
                {{ scope.row.PrincipalEmployeeList.map(s => s.Name).join(',') }}
              </span>
            </template>
            <template slot='ProgressStatus' slot-scope="scope">
              <span :style="'color:'+getColor(scope.row.ProgressStatus)">
                {{ scope.row.ProgressStatus | progressStatusfilter }}
              </span>
            </template>

            <!-- 表格查询条件区域 -->
            <!-- <template slot="conditionArea">
                          <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="handleResetSearch">
                              <template slot="Name">
                                  <el-input style="width: 100%;" v-model="listQuery.Name"></el-input>
                              </template>
                              <template slot="Number">
                                  <el-input style="width: 100%;" v-model="listQuery.Number"></el-input>
                              </template>
                          </app-table-form>
                      </template> -->

            <!-- 表格批量操作区域 -->
            <template slot="btnsArea">
              <div class="btns-wrapper">
                <permission-btn moduleName="position" :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked"></permission-btn>
              </div>
            </template>

            <!-- 表格行操作区域 -->
            <template slot-scope="scope">
              <app-table-row-button @click="handleDialog('detail', scope.row)" :type="2"></app-table-row-button>
              <!-- HasFinalEmployee  是否在某一个部门下为终审人员，如果是，则显示“跟进”按钮 -->
              <app-table-row-button v-if="scope.row.ProgressStatus < 6 && isOwner(scope.row)" @click="handleDialog('update', scope.row)" :type="1"></app-table-row-button>
              <app-table-row-button v-if="isOwner(scope.row, true) || scope.row.HasFinalEmployee || rowBtnIsExists('dataAllAppraise')" @click="handleNav('follow', scope.row)" text='跟进' :type="2"></app-table-row-button>
              <app-table-row-button v-if="scope.row.ProgressStatus >= 6 && topBtns.findIndex(s => s.DomId == 'btnAdd') > -1" @click="handleDialog('reCreate', scope.row)" text='再次发起' :type="2"></app-table-row-button>
              <app-table-row-button v-if="rowBtnIsExists('btnDel') && (scope.row.ProgressStatus == 1 || scope.row.ProgressStatus == 2 || scope.row.ProgressStatus == 3 || scope.row.ProgressStatus == 7)" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
            </template>
          </app-table>
        </div>
        <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
      </div>
    </div>

    <create-page v-if="customerServiceManageEditDialogFormVisible" @closeDialog="customerServiceManageEditCloseDialog" @saveSuccess="customerServiceManageEditSaveSuccess" :dialogFormVisible="customerServiceManageEditDialogFormVisible" :dialogStatus="customerServiceManageEditDialogStatus" :id="id"></create-page>

  </div>
</template>

<!--组件脚本区-->

<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as appraisePlanYear from "@/api/personnelManagement/appraisePlanYear";
import elDragDialog from "@/directive/el-dragDialog";
import createPage from "./create";
import { getUserInfo } from "@/utils/auth";
import { appraisePersonalsStatusEnum, yearTypeEnum } from "./enum";

export default {
  /**名称 */
  name: "ach-manage-index",
  mixins: [indexPageMixin],
  directives: {
    elDragDialog
  },
  /**组件声明 */
  components: {
    createPage,
  },
  filters: {
    progressStatusfilter(val) {
      let obj = appraisePersonalsStatusEnum.find(s => s.value == val)
      if (obj) {
        return obj.label
      }
      return ''
    },
    halfyearTypeFilter(val) {
      let obj = yearTypeEnum.find(s => s.value == val)
      if (obj) {
        return obj.label
      }
      return ''
    },
  },
  /**参数区 */
  props: {
    /**主键Id */
    keyId: {
      type: String
    }
  },
  /**数据区 */
  data() {
    return {
      /******************* 树 *******************/
      // tableSearchItems: [{
      //         prop: "Name",
      //         label: "人员姓名"
      //     },
      //     {
      //         prop: "Number",
      //         label: "工号"
      //     },
      // ],
      appraisePersonalsStatusEnum,


      /******************* 表格 *******************/
      /**查询条件 */
      listQuery: {
        PageIndex: 1,
        PageSize: 20,
      },
      /**表格列声明 */
      tabColumns: [{
        attr: {
          prop: "Year",
          label: "年份"
        },
        slot: true
      },
      {
        attr: {
          prop: "ProgressStatus",
          label: "状态",
        },
        slot: true
      },
      {
        attr: {
          prop: "PrincipalEmployeeList",
          label: "考核负责人",
        },
        slot: true
      },

      ] /**表格加载 */,
      tableLoading: false,
      /**表格数据 */
      tabData: [],
      /**表格选中行 */
      multipleSelection: [],
      /**分页数量 */
      total: 0,
      /******************* 组件 *******************/
      /**人员调整弹窗 */
      customerServiceManageEditDialogFormVisible: false,
      customerServiceManageEditDialogStatus: "create",
      /**选择客服 */
      customerServiceManageIds: [],
      id: ''
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    isAdd() {
      return this.topBtns.findIndex(s => s.DomId == 'btnAdd') > -1
    },
  },
  /**监听 */
  watch: {

  },
  /**渲染前 */
  created() {
    this.getList()
  },
  /**渲染后 */
  mounted() {
  },
  /**方法区 */
  methods: {
    //是否为负责人(containerAssessor 是否包含考核人)
    isOwner(row, containerAssessor = false) {
      let result = false
      let creatorId = row.CreateEmployeeId //当前创建人
      if (row) {
        let currentEmpId = getUserInfo().employeeid
        if (creatorId == currentEmpId || (row.PrincipalEmployeeList && row.PrincipalEmployeeList.findIndex(s => s.EmployeeId == currentEmpId) > -1)) {
          result = true
        }

        if (containerAssessor) {
          if ((row.AppraisePlanPrincipalEmployeeIdList && row.AppraisePlanPrincipalEmployeeIdList.findIndex(s => s == currentEmpId) > -1)) {
            result = true
          }
        }
      }
      return result
    },
    onBtnClicked: function (domId) {
      switch (domId) {
        case "btnAdd":
          this.handleDialog('create')
          break;
        default:
          break;
      }
    },
    handleFilterBtn(btns) {
      let temp = btns
      if (btns && btns.length > 0) {
        return btns.filter(s => s.DomId != 'btnAddChildren')
      }
      return []
    },

    /******************* 表格事件 *******************/
    /**加载表格数据 */
    getList() {
      let _this = this;
      let postData = JSON.parse(JSON.stringify(this.listQuery))

      _this.tableLoading = true
      appraisePlanYear.getList(postData).then(response => {
        _this.tableLoading = false
        _this.total = response.Total;
        _this.tabData = response.Items;
      }).catch(err => {
        _this.tableLoading = false
      });
    },
    /**表格行选中 */
    //详情、跟进
    handleNav(busType, row) {
      let targetPath = `/personnelManagement/achievementMgmt/${busType}/${row.Id}`
      this.$router.push({ path: targetPath })
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
      this.customerServiceManageIds = rows.map(t => t.Id);
    },
    handleDialog(activeName, row) {
      if (row) {
        this.id = row.Id
      }
      this.customerServiceManageEditDialogStatus = activeName;
      this.customerServiceManageEditDialogFormVisible = true;
    },
    /**表格行删除 */
    handleDelete(rows) {
      let ids = [];
      if (_.isArray(rows)) {
        ids = rows.map(u => u.Id);
      } else {
        ids.push(rows.Id);
      }

      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        appraisePlanYear.del(ids).then(res => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },
    /**分页页大小变更 */
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    /**分页页码变更 */
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    /******************* 弹窗 *******************/


    /**编辑弹窗保存成功 */
    customerServiceManageEditSaveSuccess() {
      this.listQuery.PageIndex = 1;
      this.getList();
      this.customerServiceManageEditCloseDialog();
    },
    /**编辑弹窗关闭 */
    customerServiceManageEditCloseDialog() {
      this.customerServiceManageEditDialogFormVisible = false;
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    handleResetSearch() {
      this.listQuery.PageIndex = this.listQuery.PageIndex;
      this.listQuery.PageSize = this.listQuery.PageSize;
      this.getList() //刷新列表
    },
    getColor(val) {
      let obj = appraisePersonalsStatusEnum.find(s => s.value == val);
      if (obj) {
        return obj.color
      }
      return ''
    },
  }
};
</script>

<!--组件样式区-->

<style lang="scss" scoped>
// @import "../../../../styles/empSelectorDialogCommon.css";
.pageWrapper {
  position: absolute;
  left: 0;
  // top: 40px;
  top: 0;
  right: 0;
  bottom: 0;


  .content {
    // padding: 10px;
    padding-top: 0;

    // padding-right: 10;
    .opt-wrapper {
      box-sizing: border-box;
      border-bottom: 1px solid #dcdfe6;
      padding-bottom: 10px;
    }
  }

  .custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-btn-area {
      position: absolute;
      right: 0;
      top: 0;
      width: 23px;
      height: 16px;
    }
  }
}
</style>
