<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <el-row class="tabsBox" v-if="pageType===2">
                <div class="tabsBox_item" :class="{'active':pageTabTypes==1}" @click="pageTabTypes=1">
                    我的计划
                </div>
                <div class="tabsBox_item" :class="{'active':pageTabTypes==2}" @click="pageTabTypes=2">
                    我负责的
                </div>
            </el-row>
        
            <div class="content-wrapper __dynamicTabContentWrapper">

                 <el-radio-group v-if="pageType != 2" v-model="tableActive" style="margin: 10px; margin-bottom: 0;">
                    <el-radio-button v-for="(item, idx) in searchTypesData" :key="idx" :label="item.value">{{ item.label }}</el-radio-button>
                 </el-radio-group>

                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tableDatas" :isShowAllColumn="true" :loading="listLoading"
                    :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false' :isShowConditionArea="pageType==1"
                    @sortChagned="handleSortChange" :optColWidth="pageType===1?140:160">
                        <template slot="EntryTime" slot-scope="scope">{{ scope.row.EntryTime | dateFilter('YYYY-MM-DD') }}</template>
                        <template slot="PositiveTimePlan" slot-scope="scope">{{ scope.row.PositiveTimePlan | dateFilter('YYYY-MM-DD') }}</template>
                        <template slot="PositiveTime" slot-scope="scope">{{ scope.row.PositiveTime | dateFilter('YYYY-MM-DD') }}</template>
                        <template slot="ReportNumber" slot-scope="scope">
                            <!-- <el-tag type="success" size="mini" v-if="scope.row.ReportNumber">已汇报{{scope.row.ReportNumber}}次</el-tag>
                            <template v-else>无</template> -->
                            <span v-if="scope.row.ReportNumber" style="color:#32CD32;">已汇报{{scope.row.ReportNumber}}次 / </span>
                            <span v-else style="color:red;">无 / </span>


                            <span v-if="scope.row.IsReportFeedback" style="color:#00BFFF;">已反馈</span>
                            <span v-else style="color:red;">待反馈</span>
                        </template>
                        <!-- <template slot="DepartmentName" slot-scope="scope">{{ scope.row.DepartmentName || '无' }}</template> -->
                        <!-- <template slot="PositiveStatus" slot-scope="scope">
                            <span class="item-status"
                                :style="{backgroundColor: getPositiveStatusObj(scope.row.PositiveStatus).bgColor,color: getPositiveStatusObj(scope.row.PositiveStatus).color}"
                            >{{ getPositiveStatusObj(scope.row.PositiveStatus).label }}</span>
                        </template> -->
                        <template slot="ImmediateSuperiorName" slot-scope="scope">
                            {{ scope.row.ImmediateSuperiorName | emptyFilter }}
                        </template>
                        <template slot="DepartmentManagerName" slot-scope="scope">
                            {{ scope.row.DepartmentManagerName | emptyFilter }}
                        </template>
                        <template slot="PositiveApprovalStatus" slot-scope="scope">
                            <app-tag-pure effect="dark" v-if="scope.row.PositiveApprovalStatus" :color="getPositiveApprovalStatusObj(scope.row.PositiveApprovalStatus).color" :text="getPositiveApprovalStatusObj(scope.row.PositiveApprovalStatus).label"></app-tag-pure>
                            <template v-else>无</template>
                        </template>
                        <template slot="JobName" slot-scope="scope">{{scope.row.JobName||'无'}}</template>
                        
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form label-width="110px" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" clearable placeholder="搜索姓名/工号/导师/职位"
                                        @clear='getList' v-antiShake='{time: 300,callback: () => {getList()}}' 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>
                                <template slot="EntryTime">
                                    <el-date-picker v-model="listQuery.EntryTime" type="daterange"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                </template>
                                <template slot="PositiveTimePlan">
                                    <el-date-picker v-model="listQuery.PositiveTimePlan" type="daterange"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                </template>
                                <template slot="PositiveTime">
                                    <el-date-picker v-model="listQuery.PositiveTime" type="daterange"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                </template>
                                <!-- <template slot="PositiveStatus">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.PositiveStatus" placeholder="请选择">
                                        <el-option v-for="item in PositiveStatusEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template> -->
                                <template slot="PositiveApprovalStatus">
                                    <el-select style="width: 100%;" clearable v-model="listQuery.PositiveApprovalStatus" placeholder="请选择">
                                        <el-option v-for="item in PositiveApprovalStatusEnum.filter(s=>s.value!=4)" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </template>
                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn v-on:btn-event="onBtnClicked"></permission-btn>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <template v-if="pageType===1">
                                <!-- <app-table-row-button @click="handleReview(scope.row, 'detail')" :type="2"></app-table-row-button> -->
                                <app-table-row-button @click="positiveReview(scope.row, 'detail')" :type="2"></app-table-row-button>
                                <template v-if="scope.row.PositiveStatus!=2&&(!scope.row.PositiveApprovalStatus||scope.row.PositiveApprovalStatus==3)">
                                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleReview(scope.row, 'update')" :type="1"></app-table-row-button>
                                    <app-table-row-button v-if="rowBtnIsExists('btnBecome')" @click="positiveReview(scope.row, 'update')" :type="1" text="转正"></app-table-row-button>
                                </template>
                                <!-- <app-table-row-button v-if="rowBtnIsExists('btnWithdraw')" :type="1" text="撤回"></app-table-row-button> -->
                                <app-table-row-button v-if="rowBtnIsExists('btnDel')&&scope.row.PositiveApprovalStatus!=1" @click="handleDel(scope.row)" :type="3"></app-table-row-button>
                            </template>
                            <template v-if="pageType===2">
                                <app-table-row-button v-if="rowBtnIsExists('btnMyReport')&&pageTabTypes==1&&scope.row.PositiveApprovalStatus!=1&&scope.row.PositiveApprovalStatus!=2" @click="handleMyReportReview(scope.row, 'update')" :type="1" text="我的报告"></app-table-row-button>
                                <app-table-row-button @click="positiveReview(scope.row, 'detail')" :type="2"></app-table-row-button>
                                <!-- <app-table-row-button v-if="pageTabTypes==2" @click="handleCoachingPlanReview(scope.row, 'detail')" :type="2"></app-table-row-button> -->
                                <app-table-row-button v-if="rowBtnIsExists('btnCoachingPlan')&&pageTabTypes==2&&scope.row.PositiveApprovalStatus!=1&&scope.row.PositiveApprovalStatus!=2&&scope.row.TutorEmployeeId == currEmp.employeeid"
                                @click="handleCoachingPlanReview(scope.row, 'update')" :type="1" text="辅导计划"></app-table-row-button>

                                <app-table-row-button v-if="rowBtnIsExists('btnEvaluate')&&(scope.row.PositiveApprovalStatus!=1 && scope.row.PositiveApprovalStatus!=2)&&pageTabTypes==2&&(scope.row.PositiveStatus==1||scope.row.PositiveStatus==3)&&(scope.row.TutorEmployeeId == currEmp.employeeid || scope.row.DepartmentManagerId == currEmp.employeeid || scope.row.ImmediateSuperiorId == currEmp.employeeid)"
                                @click="handleEvaluateReview(scope.row)" :type="1" text="转正评价"></app-table-row-button>
                                
                            </template>
                                <app-table-row-button @click="handleAssessmentFeedback(scope.row)" :type="1" v-if="(scope.row.PositiveApprovalStatus!=1 && scope.row.PositiveApprovalStatus!=2)&&(scope.row.ReportNumber > 0) && ((currEmp.employeeid == scope.row.TutorEmployeeId) || (currEmp.employeeid == scope.row.ImmediateSuperiorId) || (currEmp.employeeid == scope.row.DepartmentManagerId))" text="考核反馈"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" v-if="pageType==1"
                :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    
    <!-- 添加新人培养计划 -->
    <create-plan-page @closeDialog="closeCreatePlanDialog" @saveSuccess="handleCreatePlanSaveSuccess"
    :dialogFormVisible="dialogCreatePlanFormVisible" :dialogStatus="'create'"></create-plan-page>
    <!-- 编辑/查看 新人培养计划 -->
    <edit-plan-page @closeDialog="closeEditPlanDialog" @saveSuccess="handleEditPlanSaveSuccess" :id="selectRow.Id"
    :dialogFormVisible="dialogEditPlanFormVisible" :dialogStatus="dialogEditStatus"></edit-plan-page>
    
    <!-- 转正 -->
    <positive-plan-page v-if="dialogPositivePlanFormVisible" @closeDialog="closePositivePlanDialog"
    @saveSuccess="handlePositivePlanSaveSuccess" :id="selectRow.Id"
    :dialogFormVisible="dialogPositivePlanFormVisible" :dialogStatus="dialogPositivePlanStatus"></positive-plan-page>

    <!-- 辅导计划 -->
    <coaching-plan-page v-if="dialogCoachingPlanFormVisible" @closeDialog="closeCoachingPlanDialog"
    @saveSuccess="handleCoachingPlanSaveSuccess" :id="selectRow.Id" :employeeId="selectRow.EmployeeId"
    :dialogFormVisible="dialogCoachingPlanFormVisible" :dialogStatus="dialogCoachingPlanStatus"></coaching-plan-page>

    <!-- 我的汇报 -->
    <my-report-page v-if="dialogMyReportFormVisible" @closeDialog="dialogMyReportFormVisible=false"
    :id="selectRow.Id" :employeeId="selectRow.EmployeeId"
    :dialogFormVisible="dialogMyReportFormVisible" :dialogStatus="dialogMyReportStatus"
    @reload='getList'></my-report-page>
    
    <evaluate-page v-if="dialogEvaluateFormVisible && selectRow" @closeDialog="closeEvaluateDialog"
        @saveSuccess="handleEvaluateSaveSuccess" :row="selectRow"
        :dialogFormVisible="dialogEvaluateFormVisible" :dialogStatus="dialogEvaluateStatus"></evaluate-page>

    <assessment-feedback
      v-if="dialogAssessmentFeedbackVisible && selectRow"
      @closeDialog="closeAssessmentFeedbackDialog"
      @saveSuccess="handleFeedbackSaveSuccess" 
      :row="selectRow"
      :employeeId="selectRow.EmployeeId"
      :dialogFormVisible="dialogAssessmentFeedbackVisible" 
      :dialogStatus="dialogFeedbackStatus">
      </assessment-feedback>

</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import { getUserInfo } from "@/utils/auth";
import * as EmployeeTrainingPlanApi from "@/api/personnelManagement/EmployeeTrainingPlan";
import createPlanPage from "./createPlan"
import editPlanPage from "./editPlan"
import positivePlanPage from "./positivePlan"
import coachingPlanPage from "./coachingPlan"
import myReportPage from "./myReport"
import evaluatePage from './evaluatePage'

import assessmentFeedback from './assessmentFeedback'

import { vars } from '../common/vars'
import * as ApprovalVars from "@/views/projectDev/common/vars";
const pageColumns = [
    {attr: {prop: "Name",label: "姓名", width: '80px', showOverflowTooltip: true}},
    {attr: {prop: "Number",label: "工号", width: '80px'}},
    {attr: {prop: "DepartmentName",label: "部门", showOverflowTooltip: true}},
    {attr: {prop: "JobName",label: "职位", showOverflowTooltip: true},slot: true},
    {attr: {prop: "TutorEmployeeName",label: "导师", width: '80px'}},
    {attr: {prop: "ImmediateSuperiorName",label: "直属上级", width: '80px', showOverflowTooltip: true},slot: true},
    {attr: {prop: "DepartmentManagerName",label: "部门经理", width: '80px', showOverflowTooltip: true},slot: true},
    {attr: {prop: "ReportNumber",label: "考核情况", width: '130px'},slot: true},
    // {attr: {prop: "PositiveStatus",label: "状态",sortable: "custom", width: '100px'}, slot: true},
    {attr: {prop: "EntryTime",label: "入职时间",sortable: "custom", width: '100px'},slot: true},
    {attr: {prop: "PositiveTimePlan",label: "预计转正时间",sortable: "custom", width: '120px'},slot: true},
    {attr: {prop: "PositiveTime",label: "转正时间",sortable: "custom", width: '100px'},slot: true},
    //{attr: {prop: "PositiveStatus",label: "转正情况",sortable: "custom", width: '100px'}, slot: true},
    {attr: {prop: "PositiveApprovalStatus",label: "转正审批", width: '100px'},slot: true},
]
export default {
    name: "employeeRelations-trainingRmployment",
    mixins: [indexPageMixin],
    components: {
        coachingPlanPage,
        positivePlanPage,
        createPlanPage,
        editPlanPage,
        myReportPage,
        evaluatePage,
        assessmentFeedback,

    },
    props: {
        // 页面类型  1 人事行政管理-员工关系-新员工培养/转正   2 公司门户-新员工培养
        pageType: {
            type: Number,
            default: 1
        }
    },
    filters: {
        // nodutyFilter(value) {
        //     let duty = value.split(",");
        //     return duty[0];
        // },
    },
    computed: {
        // fildids() {
        //     return this.multipleSelection.map((s) => s.Id) || [];
        // },
        tabColumns() {
            let arr = JSON.parse(JSON.stringify(pageColumns))
            // if(this.pageType == 1) {
            //     arr.splice(arr.findIndex(s=>s.attr.label=='转正情况'),1)
            // }
            // if(this.pageType == 2) {
            //     arr.splice(arr.findIndex(s=>s.attr.label=='状态'),1)
            // }
            return arr
        },
        currEmp() {
            return getUserInfo() || {}
        },
    },
    watch: {

        tableActive(val) {
             if (this.pageType==1){
                 this.getList();
             }
        },

        filterText(val) {
            this.$refs.tree.filter(val);
        },
        pageTabTypes() {
            this.getList();
        },
    },
    mounted() {
    },
    created() {
        this.getList();
    },
    data() {
        return {
            tableActive: 1,
            searchTypesData: [ 
                { label: "试用期", value: 1 },
                { label: "已转正", value: 2 },
            ],
            pageTabTypes: 1, // 公司门户-新员工培养菜单  1 我的计划 2 我负责的


            // 1 待审批 2 已审批 3 不通过 4 已撤销
            PositiveApprovalStatusEnum: ApprovalVars.vars.approvalStatuObj.approvalStatus,
            PositiveStatusEnum: vars.PositiveStatusEnum,
            dialogPositivePlanFormVisible: false,
            dialogPositivePlanStatus: 'update',
            
            dialogMyReportFormVisible: false,

            dialogCreatePlanFormVisible: false,
            
            dialogCoachingPlanFormVisible: false,
            dialogCoachingPlanStatus: 'update',

            dialogEditPlanFormVisible: false,
            dialogEditStatus: '',
            selectRow: {},

            layoutMode: 'simple',
            filterText: "",

            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                { prop: "EntryTime", label: "入职时间" },
                { prop: "PositiveTimePlan", label: "预计转正时间" },
                { prop: "PositiveTime", label: "转正时间" },
                // { prop: "PositiveStatus", label: "状态" },
                { prop: "PositiveApprovalStatus", label: "转正审批" },
            ],

            listLoading: false,
            listQuery: {
            },
            tableDatas: [], //原始数据
            total: 0,

            dialogEvaluateStatus: 'create',
            dialogEvaluateFormVisible: false,

            dialogFeedbackStatus: 'create',
            dialogAssessmentFeedbackVisible:false,

        };
    },
    methods: {
        handleEvaluateSaveSuccess(){
            this.getList()
            this.closeEvaluateDialog()
        },
        closeEvaluateDialog() {
            this.dialogEvaluateFormVisible = false
        },
        handleEvaluateReview(row, optType) {
            this.selectRow = row;
            this.dialogEvaluateStatus = optType || 'create';
            this.dialogEvaluateFormVisible = true
        },

        handleFeedbackSaveSuccess(){
            this.getList()
            this.closeAssessmentFeedbackDialog()
        },

        closeAssessmentFeedbackDialog() {
            this.dialogAssessmentFeedbackVisible = false
        },
        handleAssessmentFeedback(row){
            this.dialogAssessmentFeedbackVisible = true
            this.selectRow = row;
        },
        getPositiveStatusObj(val) {
            return this.PositiveStatusEnum.find(
                s => s.value == val
            ) || {};
        },
        getPositiveApprovalStatusObj(val) {
            return this.PositiveApprovalStatusEnum.find(
                s => s.value == val
            ) || {};
        },
        // 我的报告 弹窗
        handleMyReportReview(row){
            this.selectRow = row;
            // 转正审批为：已审批  弹窗是查看状态
            this.dialogMyReportStatus = row.PositiveApprovalStatus!=2?'update':'detail';
            this.dialogMyReportFormVisible = true
        },
        // 辅导计划  编辑/ 查看  弹窗
        handleCoachingPlanReview(row, optType){
            this.selectRow = row;
            this.dialogCoachingPlanStatus = optType || 'update';
            this.dialogCoachingPlanFormVisible = true
        },
        // 辅导计划 成功回调
        handleCoachingPlanSaveSuccess(){
            this.getList()
            this.closeCoachingPlanDialog()
        },
        // 关闭 辅导计划弹窗
        closeCoachingPlanDialog(){
            this.dialogCoachingPlanFormVisible = false
        },
        // 转正/审批 弹窗
        positiveReview(row, optType) {
            this.selectRow = row;
            this.dialogPositivePlanStatus = optType || 'update';
            this.dialogPositivePlanFormVisible = true
        },
        // 转正 成功回调
        handlePositivePlanSaveSuccess(){
            this.getList()
            this.closePositivePlanDialog()
        },
        // 关闭 转正弹窗
        closePositivePlanDialog(){
            this.dialogPositivePlanFormVisible = false
        },
        // 添加新人培养计划 成功回调
        handleCreatePlanSaveSuccess(){
            this.getList()
            this.closeCreatePlanDialog()
        },
        // 关闭 添加新人培养计划弹窗
        closeCreatePlanDialog(){
            this.dialogCreatePlanFormVisible = false
        },
        // 编辑/查看 新人培养计划 成功回调
        handleEditPlanSaveSuccess(){
            this.getList()
            this.closeEditPlanDialog()
        },
        // 关闭 编辑/查看  新人培养计划弹窗
        closeEditPlanDialog(){
            this.dialogEditPlanFormVisible = false
        },
        // 表格头部按钮区域
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.dialogCreatePlanFormVisible = true
                    break;
                case "btnWelcomeNotice":
                    this.dialogWelcomeNoticeVisible = true
                    break;
                default:
                break;
            }
        },
        // 表格排序查询
        handleSortChange({column, prop, order}) {
            let delIndex = prop.indexOf('.')
            if (delIndex>-1) {
                prop = prop.substring(delIndex+1)
            }
            this.sortObj = { prop, order };
            this.getList();
        },
        // 编辑/查看弹窗
        handleReview(row, optType) {
            this.selectRow = row;
            this.dialogEditStatus = optType;
            this.dialogEditPlanFormVisible = true
            // this.$router.push({path: `/workbench/workPlanDashboard/detail/${row.Id}`})
        },
        // 删除表格行
        handleDel(row) {
            this.$confirm(`是否确认删除?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                EmployeeTrainingPlanApi.del([row.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList()
                });
            });
        },
        //获取成员列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            if(postData.EntryTime && postData.EntryTime.length == 2) {
                postData.EntryTimeStart = postData.EntryTime[0] + ' 00:00:00'
                postData.EntryTimeEnd = postData.EntryTime[1] + ' 23:59:59'
            }
            delete postData.EntryTime

            if(postData.PositiveTimePlan && postData.PositiveTimePlan.length == 2) {
                postData.PositiveTimePlanStart = postData.PositiveTimePlan[0] + ' 00:00:00'
                postData.PositiveTimePlanEnd = postData.PositiveTimePlan[1] + ' 23:59:59'
            }
            delete postData.PositiveTimePlan

            if(postData.PositiveTime && postData.PositiveTime.length == 2) {
                postData.PositiveTimeStart = postData.PositiveTime[0] + ' 00:00:00'
                postData.PositiveTimeEnd = postData.PositiveTime[1] + ' 23:59:59'
            }
            delete postData.PositiveTime

            let result = null;
            if (this.pageType==1) {
                postData.PositiveStatus = this.tableActive
                result = EmployeeTrainingPlanApi.getList(postData);
            }
            if (this.pageType==2) {
                postData.Type = this.pageTabTypes;
                postData.PageSize = 99999;
                result = EmployeeTrainingPlanApi.GetEmployeeTrainingPlanList(postData);
            }
            result.then((res) => {
                if (this.pageType==1) {
                    this.tableDatas = res.Items;
                    this.total = res.Total;
                }
                if (this.pageType==2) {
                    this.tableDatas = res;
                }
                this.listLoading = false;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        // 重置搜索
        onResetSearch() {
            this.listQuery = this.$options.data().listQuery
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
    },
};
</script>

<style scoped>
</style>

<style lang="scss" scoped>
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;
    .tabsBox{
        width: 200px;
        height: 100%;
        border-right: 1px solid #dcdfe6;
        &_item{
            background-color: #fff;
            padding: 10px;
            height: 40px;
            line-height: 20px;
            cursor: pointer;
            .svg-icon{
                float: left;
                margin-right: 7px;
            }
            &.active{
                background-color: #f0f7ff;
            }
            &:hover{
                background-color: #F5F7FA;
            }
        }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .tagBox{
            border-bottom: 1px solid #DCDFE6;
            padding: 4px 8px;
        }

    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
</style>
