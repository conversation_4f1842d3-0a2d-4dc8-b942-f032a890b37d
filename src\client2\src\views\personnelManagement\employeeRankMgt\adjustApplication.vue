<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <div class="wrapperBody" v-loading='loading'>
                    <div class="wrapperMain">
                        <div class="opl">
                            <el-form
                                :rules="rules" ref="formData" :model="formData"
                                label-position="right"
                                label-width="100px"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="头像" style="margin-bottom:0;">
                                            <div v-viewer>
                                                <img class="userPhoto" :src="formData.AvatarPath||defAvatar" />
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="当前等级" style="margin-bottom:0;">
                                            <!-- <user-leve-icon :number="formData.OldEmployeeLevelType" /> -->
                                            T{{ formData.OldEmployeeLevelType }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="姓名">{{formData.Name}}</el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="工号">{{formData.Number}}</el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="部门">{{formData.DepartmentName}}</el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="职位">{{formData.JobName}}</el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="入职时间">{{formData.EntryTime | dateFilter('YYYY-MM-DD')}}</el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="司龄">{{getYear(formData.EntryTime)}}</el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="调整后" prop="NewEmployeeLevelType">
                                            <el-select :disabled="!editable" v-model="formData.NewEmployeeLevelType" style="width:80%;">
                                                <el-option v-for="item in leveEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                            </el-select>
                                            <i :class="isUp?'el-icon-top':'el-icon-bottom'"
                                            v-if="formData.OldEmployeeLevelType&&formData.NewEmployeeLevelType&&formData.OldEmployeeLevelType!=formData.NewEmployeeLevelType"
                                            :style="`font-size: 22px;margin-left: 5px;vertical-align: middle;color:${isUp?'#67c23a':'#f56c6c'}`"></i>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="申请日期" prop="ApplyDate">
                                            <el-date-picker :clearable="false" :disabled="!editable"
                                            v-model="formData.ApplyDate" type="date" align="right" style="width:80%;"
                                            format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="调整理由" prop="AdjustReason">
                                    <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="5" v-model="formData.AdjustReason" style="width:92%;"></el-input>
                                </el-form-item>
                                <el-form-item label="备注">
                                    <el-input :disabled="!editable" maxlength="500" type="textarea" :rows="5" v-model="formData.Remark" style="width:92%;"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                        <div class="opr">
                            <page-title title="附件"></page-title>
                            <div class="recordWarp_main">
                                <template v-if="editable">
                                    <app-uploader accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList"
                                    :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                </template>
                                <template v-else>
                                    <app-uploader v-if="formData.AttachmentList.length>0" accept="all" :fileType="4" :max="10000"
                                        :value="formData.AttachmentList" readonly
                                    :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                    <no-data v-else></no-data>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div>
                        <approval-panel v-if="dialogStatus == 'update'" :editable="isApprovalor"
                        ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                        <approval-detail 
                            :isOnlyViewDetail='isOnlyViewDetail || !isApprovalor' 
                            v-if="dialogStatus == 'approval' || dialogStatus == 'detail' || dialogStatus == 'revoke' || dialogStatus == 'revokeApproval'" 
                            ref="approvalDetail" 
                            :dialogStatus='dialogStatusTrans' 
                            :approvalObj='formData.Approval'
                        ></approval-detail>
                    </div>
                </div>
            </template>

            <template slot="footer">
                <!-- 取消 -->
                <app-button v-if="dialogStatus=='detail'" @click="handleClose" text="关闭" type></app-button>
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                
                <app-button v-if="dialogStatus != 'approval'" @click="createData" :buttonType="1"
                v-show="editable || dialogStatus=='revoke'" :disabled="disabledBtn" style="margin-left:10px;"></app-button>
                <el-button @click="handleApproval"  type="primary" :disabled='disabledBtn'
                v-show="dialogStatus == 'approval' && !isOnlyViewDetail && isApprovalor" style="margin-left:10px;">审批</el-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import {leveEnum} from "@/components/UserLeveIcon/enum";
import * as ApprovalVars from "@/views/workbench/myWorkbench/vars";
import * as employeeLevelHistoryApi from "@/api/personnelManagement/employeeLevelHistory";

import approvalMixins from '@/mixins/approvalPatch'
import approvalPanel from '@/views/projectDev/projectMgmt/common/approvalPanel'
import approvalDetail from '@/views/projectDev/projectMgmt/workbench/common/approvalDetail'

import UserLeveIcon from "@/components/UserLeveIcon"
import NoData from "@/views/common/components/noData";
export default {
    name: "employeeRankMgt-adjustApplication",
    directives: {},
    components: {
        UserLeveIcon,
        NoData,
        approvalPanel,
        approvalDetail,
    },
    mixins: [approvalMixins],
    computed: {
        dialogStatusTrans() {
            let statusTemp = this.dialogStatus
            if(statusTemp == "revoke" || statusTemp == "detail") {
                return 'detail'
            }else if(statusTemp == "approval" || statusTemp == "revokeApproval") {
                return 'approval'
            }else{
                return statusTemp
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != 'approval' && this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "调整申请";
            }else if(this.dialogStatus == 'approval'){
                return "等级调整";
            }else if(this.dialogStatus == 'revoke' || this.dialogStatus == 'revokeApproval') {
                return "等级调整";
            } else if (this.dialogStatus == "detail") {
                return "等级调整";
            }
        },
        isUp(){
            if(this.formData.OldEmployeeLevelType>this.formData.NewEmployeeLevelType){
                return false
            }
            if(this.formData.OldEmployeeLevelType<this.formData.NewEmployeeLevelType){
                return true
            }
        },
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        employeeId: {
            type: String,
            default: ""
        },
        approvalId: {   // 审批编号，从审批列表中弹出该页面时需要
            type: String,
            default: ''
        },
        //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
        isOnlyViewDetail: {
            type: Boolean,
            default: false
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create") {
                        this.getDetail()
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            leveEnum,
            defAvatar: require('../../../assets/images/avatar3.png'),
            loading: false,
            disabledBtn: false,
            formData: {
                Id: "",
                AvatarPath: "",//头像
                Name: "", //姓名
                Number: "", //工号
                DepartmentName: "",// 部门
                JobName: "", // 职位
                EntryTime: "",// 入职时间
                OldEmployeeLevelType: 1, // 员工职级 旧等级
                NewEmployeeLevelType: 1, // 调整后等级
                ApplyDate: "",// 申请日期
                AdjustReason: "",//调整理由
                Remark: "",//备注
                AttachmentList: [],// 附件
                Approval: {//审批信息
                    ApprovalEmployeeList: [[]],
                    ApprovalType: 1,
                    ApprovalOperatorEmployeeList: [], //已审批人员
                    NoApprovalEmployeeList: [], //未审批人员
                    CCEmployeeList: [], //抄送人
                    ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
                    ApprovalState: 1, //1: 进行中; 2: 已完成
                    ApprovalResult: 1, //1: 通过； 2：不通过
                    ApprovalSet: 1,
                },
            },
            rules: {
                NewEmployeeLevelType: { fieldName: "调整后等级", rules: [{ required: true }] },
                ApplyDate: { fieldName: "申请日期", rules: [{ required: true }] },
                AdjustReason: { fieldName: "调整理由", rules: [{ required: true }] },
            },
        };
    },
    methods: {
        // 确定提交
        createData() {
            let self = this;
            let listResult = self.$refs.formData.validate();
            let approvalPanelValidate = self.$refs.approvalPanel.validate();
                Promise.all([listResult,approvalPanelValidate]).then(valid => {
                        let postData = JSON.parse(JSON.stringify(self.formData));
                        postData.Approval = self.$refs.approvalPanel.getData() //审批层区块
                        postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                        postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
                        postData.AttachmentIdList = postData.AttachmentList.map((s) => s.Id);
                        // console.log(postData)
                        self.disabledBtn = true;
                        employeeLevelHistoryApi.add(postData).then(res => {
                            self.disabledBtn = false;
                            self.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            self.$refs.appDialogRef.createData();
                        })
                        .catch(err => {
                            self.disabledBtn = false;
                        });
                }).catch(err => {
                    self.disabledBtn=false;
                })
        },
        // 审批确认
        handleApproval() {
            let approvalDetailRef = this.$refs.approvalDetail

            approvalDetailRef.getValidtor().then(valid => {
                if(valid) {
                    let postData = approvalDetailRef.getData()
                    postData.BusinessId = this.id
                    let approvalLabel = ApprovalVars.vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label
                    this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // console.log(postData)
                        this.disabledBtn = true
                        employeeLevelHistoryApi.approval(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "审批成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData();
                            this.handleClose();
                        }).catch(err => {
                            this.disabledBtn = false
                        })
                    })

                }
            })
        },
        // 获取详情
        getDetail() {
            let self = this;
            self.loading = true
            employeeLevelHistoryApi.detail({
                EmployeeId: self.employeeId,
                Id: self.id
            }).then(res => {
                self.loading = false
                if(self.dialogStatus == 'update') {
                    self.formData = {...res, ...{
                        NewEmployeeLevelType: res.OldEmployeeLevelType,// 初始化  调整后的等级 等于当前的值  让用户去选择
                        ApplyDate: dayjs().format('YYYY-MM-DD'),// 申请日期
                        AdjustReason: "",//调整理由
                        Remark: "",//备注
                        AttachmentList: [],// 附件
                        Approval: self.$options.data().formData.Approval, //  审批信息
                    }};
                } else {
                    self.formData = res
                }
            }).catch(err => {
                self.loading = false
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        getYear(datetime) {//司龄
            let birthday = datetime
            if(birthday&&dayjs(birthday).isValid()) {
                let currentDate = dayjs().format('YYYY-MM-DD')
                let yearOfBirth = dayjs(birthday).format('YYYY-MM-DD')
                let workDays = dayjs(currentDate).diff(dayjs(yearOfBirth),'day')     
                return Math.floor(workDays / 365) + ' 年'
            }else{            
                return '无'
            }
        },
        //清理表单
        resetFormData() {
            this.formData = this.$options.data().formData;
            this.formData.ApplyDate = dayjs().format('YYYY-MM-DD');
        }
    }
};
</script>

<style lang='scss' scoped>
.colorBlue{
    color: #409EFF;
}
.flexWarp{
    display: flex;
}
.flexColumn{
    flex: 1;
}
.wrapperMain{
    display: flex;
    .opl{
        flex: 1;
        
        .userPhoto{
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
            float: left;
            cursor: pointer;
        }
    }
    .opr{
        width: 299px;
        display: flex;
        flex-direction: column;
        border-left: 1px solid #dcdfe6;
        .recordWarp_main{
            flex: 1;
            padding: 5px 10px;
            padding-right: 0;
            max-height: 400px;
            overflow: hidden;
            overflow-y: auto;
            &--itemWarp{
                padding-bottom: 20px;
                &_row{
                    line-height: 28px;
                }
            }
        }
    }
}
</style>