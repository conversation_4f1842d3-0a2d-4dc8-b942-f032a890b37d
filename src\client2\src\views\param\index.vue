<!--案例库参数配置-->
<template>
<!--组件内容区-->
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="综合警报" :subTitle="['案例库参数配置添加、管理页面']"></page-title> -->
        <div style="position:absolute;top:0;bottom:0px;width:100%;">
            <el-row style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                <el-button v-if="btnMaintain=='btnMaintain'" type="primary" @click="handleAddParamUnitList">参数单位管理</el-button>

            </el-row>
            <el-row style="height:calc(100% - 50px);width:100%;">
                <el-col style="border-right:1px solid #eee;height:100%;width:310px;">
                    <div style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                        <span style="line-height:30px;">参数所属节点类型</span>
                        <span class="node-btn-area">
                            <el-button v-if="btnMaintain=='btnMaintain'" style="float: right;" type="primary" @click="handleAddParamNode">添加</el-button>
                        </span>
                    </div>
                    <div style="height:calc(100% - 50px);overflow-y:auto;overflow-x:hidden;width:100%;" v-loading='treeLoading'>
                        <el-tree ref="tree" :data="treeDatas" node-key="Id" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node, data }">
                                <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '251px' : node.level == 2 ? '233px' : '215px'}">{{ node.label }}</span>
                                <span class="node-btn-area">
                                    <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" v-show="node.level < 3" command="add">添加子节点</el-dropdown-item>
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="update">修改名称</el-dropdown-item>
                                            <el-dropdown-item v-if="btnMaintain=='btnMaintain'" command="delete">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </span>
                            </span>
                        </el-tree>
                    </div>
                </el-col>
                <el-col style="border-right:1px solid #eee;position:relative;height:100%;width:calc(100% - 310px);">
                    <div style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                        <span style="line-height:30px;">参数列表</span>
                    </div>
                    <div style="height:50px;padding:10px;border-bottom: 1px solid #dcdfe6;">
                        <span class="node-btn-area">
                            <el-button v-if="btnMaintain=='btnMaintain'" type="primary" :disabled="!(treeDatas&&treeDatas.length>0)" @click="handleAddParam">添加参数</el-button>
                        </span>
                    </div>
                    <div class="__dynamicTabContentWrapper" style="height:calc(100% - 100px);overflow-y:auto">
                        <div id="__dynamicTabCoreWrapper">
                            <el-table :height='tabHeight' fit :data="tableData" style="width: 100%" v-loading="tableLoading">
                                <el-table-column type="index" label="序号"></el-table-column>
                                <el-table-column prop="Id" label="参数ID"></el-table-column>
                                <el-table-column prop="Name" label="参数名称"></el-table-column>
                                <el-table-column prop="ParamTypeName" label="参数分类"></el-table-column>
                                <el-table-column prop="ParamUnitName" label="参数单位"></el-table-column>
                                <el-table-column label="操作">
                                    <template slot-scope="scope">
                                        <el-button type="text" v-if="btnMaintain=='btnMaintain'" @click="handleParamEdit(scope.row,'detail')">详情</el-button>
                                        <el-button type="text" v-if="btnMaintain=='btnMaintain'" @click="handleParamEdit(scope.row)">编辑</el-button>
                                        <el-button type="text" v-if="btnMaintain=='btnMaintain'" @click="handleParamDelete(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <pagination :total="total" :page.sync="tableParam.PageIndex" :size.sync="tableParam.PageSize" @pagination="handleCurrentChange" />
                    </div>
                </el-col>
            </el-row>
        </div>
    </div>

    <!--弹窗组件区-->
    <!--参数单位列表-->
    <paramUnitList :dialogFormVisible="paramUnitListDialogFormVisible" @closeDialog="paramUnitListCloseDialog"></paramUnitList>
    <!--参数节点类型编辑弹窗-->
    <paramNodeEdit :level="level" :parentId="parentId" :id="paramNodeId" :dialogStatus="paramNodeEditDialogStatus" :dialogFormVisible="paramNodeEditDialogFormVisible" @closeDialog="paramNodeEditCloseDialog" @saveSuccess="paramNodeEditSaveSuccess"></paramNodeEdit>
    <!--基础参数编辑弹窗-->
    <paramEdit :paramNodeId="paramNodeId" :id="paramId" :dialogStatus="paramEditDialogStatus" :dialogFormVisible="paramEditDialogFormVisible" @closeDialog="paramEditCloseDialog" @saveSuccess="paramEditSaveSuccess"></paramEdit>
</div>
</template>

<!--组件脚本区-->

<script>
/**引用区 */
//按照以下顺序
//组件 import empSelector from "../../../../common/empSelector";
import noData from "@/views/common/components/noData";
import paramEdit from "../param/paramEdit";
import paramNodeEdit from "../param/paramNodeEdit";
import paramUnitList from "../param/paramUnitList";
import indexPageMixin from "@/mixins/indexPage";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins"
import * as param from "@/api/param";
import {
    listToTreeSelect
} from "@/utils";

import {
    ParamType
} from "@/utils/commonEnum";
//方法、属性 import { empSelector } from "../../../../common/empSelector";

export default {
    /**名称 */
    name: "param-index",
    mixins: [indexPageMixin, tabDynamicHeightMixins],
    /**组件声明 */
    components: {
        noData,
        paramEdit,
        paramNodeEdit,
        paramUnitList
    },
    /**参数区 */
    props: {
        /**主键Id */
        keyId: {
            type: String
        }
    },
    /**数据区 */
    data() {
        return {
            /**按钮在执行，不允许点击 */
            buttonLoading: false,
            btnMaintain: '',
            /**树集合 */
            treeDatas: [],
            treeLoading: false,
            defaultProps: {
                //树默认结构
                children: "children",
                label: "Name"
            },
            /**选中树节点 */
            checkedNode: "",
            /**参数单位列表弹窗 */
            paramUnitListDialogFormVisible: false,
            /**参数节点弹窗 */
            level: 1,
            parentId: null,
            paramNodeId: "",
            paramNodeEditDialogStatus: "create",
            paramNodeEditDialogFormVisible: false,
            /**基础参数弹窗 */
            paramId: "",
            paramEditDialogStatus: "create",
            paramEditDialogFormVisible: false,
            /**参数分类枚举 */
            paramTypes: ParamType,
            /**列表查询参数 */
            tableParam: {
                PageIndex: 1,
                PageSize: 20
            },
            /**总数 */
            total: 0,
            /**列表数据 */
            tableData: [],
            /**列表加载中 */
            tableLoading: false
        };
    },
    /**计算属性---响应式依赖 */
    computed: {},
    /**监听 */
    watch: {
        "checkedNode.Id": {
            handler(val) {
                this.loadParamData();
            }
        }
    },
    /**渲染前 */
    created() {
        this.btnTextValue()
    },
    /**渲染后 */
    mounted() {
        this.loadParamNodeData();
    },
    /**方法区 */
    methods: {
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnMaintain") {
                    this.btnMaintain = "btnMaintain"
                }
            })
        },
        loadParamNodeData() {
            let _this = this;
            let paramData = {};
            _this.treeLoading = true
            param.getParamNodeListByCondition(paramData).then(response => {
                _this.treeLoading = false
                _this.treeDatas = listToTreeSelect(response);
                if (
                    _this.checkedNode &&
                    response.find(t => {
                        return t.Id == _this.checkedNode.Id;
                    })
                ) {} else if (_this.treeDatas && _this.treeDatas.length > 0) {
                    _this.checkedNode = _this.treeDatas[0];
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        _this.$refs.tree.setCurrentKey(_this.checkedNode.Id);
                    });
                }
            }).catch(err => {
                _this.treeLoading = false
            });
        },
        loadParamData() {
            let _this = this;
            if (!_this.checkedNode) {
                this.tableData = [];
                return;
            }
            _this.tableParam.ParamNodeId = _this.checkedNode.Id;
            _this.tableLoading = true
            param.getParamListPage(_this.tableParam).then(response => {
                _this.tableLoading = false
                this.total = response.Total;
                this.tableData = response.Items.map(t => {
                    let types = [];
                    _this.paramTypes.forEach(p => {
                        p.value & t.ParamType && types.push(p.label);
                    });
                    t.ParamTypeName = types.join("，");
                    return t;
                });
            }).catch(err => {
                _this.tableLoading = false
            });
        },
        /**分页页码切换 */
        handleCurrentChange(val) {
            this.tableParam.PageIndex = val.page;
            this.tableParam.PageSize = val.size;
            this.loadParamData();
        },
        //左侧树操作菜单
        handleCommand(optType, node, data) {
            if (optType == "add") {
                this.handleParamNodeAdd(data);
            } else if (optType == "update") {
                this.handleParamNodeEdit(data);
            } else if (optType == "delete") {
                this.handleParamNodeDelete(data);
            }
        },
        /**参数节点新增下级 */
        handleParamNodeAdd(data) {
            this.paramNodeId = "";
            this.parentId = data.Id;
            this.level = data.Level + 1;
            this.paramNodeEditDialogStatus = "create";
            this.paramNodeEditDialogFormVisible = true;
        },
        /**参数节点编辑 */
        handleParamNodeEdit(data) {
            this.paramNodeId = data.Id;
            this.parentId = data.ParentId;
            this.level = data.Level;
            this.paramNodeEditDialogStatus = "update";
            this.paramNodeEditDialogFormVisible = true;
        },
        /**参数节点删除 */
        handleParamNodeDelete(data) {
            let _this = this;
            _this
                .$confirm(`是否确认删除${data.Name}吗?`, "提示", {
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                .then(() => {
                    param.deleteParamNode([data.Id]).then(response => {
                        _this.loadParamNodeData();
                        _this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
                });
        },
        /**基础参数编辑*/
        handleParamEdit(data, optType = "update") {
            this.paramId = data.Id;
            this.paramEditDialogStatus = optType;
            this.paramEditDialogFormVisible = true;
        },
        /**基础参数删除 */
        handleParamDelete(data) {
            let _this = this;
            _this
                .$confirm(`是否确认删除${data.Name}吗?`, "提示", {
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                .then(() => {
                    param.deleteParam([data.Id]).then(response => {
                        _this.loadParamData();
                        _this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
                });
        },
        /**参数单位列表弹窗 */
        handleAddParamUnitList() {
            this.paramUnitListDialogFormVisible = true;
        },
        /**参数单位列表关闭 */
        paramUnitListCloseDialog() {
            this.loadParamData();
            this.paramUnitListDialogFormVisible = false;
        },
        /**参数节点新增弹窗 */
        handleAddParamNode() {
            this.paramNodeId = "";
            this.parentId = null;
            this.level = 1;
            this.paramNodeEditDialogStatus = "create";
            this.paramNodeEditDialogFormVisible = true;
        },
        /**参数节点保存成功 */
        paramNodeEditSaveSuccess() {
            this.loadParamNodeData();
        },
        /**参数节点关闭 */
        paramNodeEditCloseDialog() {
            this.paramNodeEditDialogFormVisible = false;
        },
        /**基础参数弹窗 */
        handleAddParam() {
            this.paramNodeId = this.checkedNode.Id;
            this.paramEditDialogStatus = "create";
            this.paramEditDialogFormVisible = true;
        },
        /**基础参数保存成功 */
        paramEditSaveSuccess() {
            this.tableParam.PageIndex = 1;
            this.loadParamData();
        },
        /**基础参数关闭 */
        paramEditCloseDialog() {
            this.paramEditDialogFormVisible = false;
        }
    }
};
</script>

<!--组件样式区-->

<style lang="scss" scoped>
.custom-tree-node {
    display: block;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-right: 24px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}
</style>
