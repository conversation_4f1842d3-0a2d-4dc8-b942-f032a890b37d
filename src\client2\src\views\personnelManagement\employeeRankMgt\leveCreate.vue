<!--职级说明编辑-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="800">
            <template slot="body">
                <el-form :rules="formRules" ref="formData" v-loading="loading"
                :model="formData" label-position="right" label-width="110px">
                    <el-row class="contentBox">
                        <el-form-item label="当前等级">
                            <!-- <user-leve-icon :number="formData.EmployeeLevelType" v-if="formData.EmployeeLevelType" /> -->
                            <span v-if="formData.EmployeeLevelType">
                                T{{ formData.EmployeeLevelType }}
                            </span>
                        </el-form-item>
                    </el-row>
                    <el-row class="contentBox">
                        <el-form-item label="任职资格说明" prop="Qualifications">
                            <el-input :disabled="!editable" maxlength="2000" type="textarea" :rows="5" v-model="formData.Qualifications" style="width:92%;"></el-input>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <app-button :buttonType="2" @click="handleClose"></app-button>
                <app-button v-if="editable" :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import UserLeveIcon from "@/components/UserLeveIcon"
import * as EmployeeLevelApi from "@/api/personnelManagement/EmployeeLevel";

export default {
    /**名称 */
    name: "employeeRankMgt-leve-create",
    /**组件声明 */
    components: {
        UserLeveIcon,
    },
    /**参数区 */
    props: {
        dialogStatus: { //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ''
        }
    },
    /**数据区 */
    data() {
        return {
            loading:false,
            /**按钮在执行，不允许点击 */
            buttonLoading: false,

            /**表单模型 */
            formData: { 
                EmployeeLevelType: null, // 职级
                Qualifications: '', // 任职资格说明
            },
            /**表单规则 */
            formRules: {
                EmployeeLevelType: { fieldName: "职级", rules: [{ required: true }] },
                Qualifications: { fieldName: "任职资格说明", rules: [{ required: true }] },
            }
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '创建职级说明'
            }else if(this.dialogStatus == 'update') {
                return '编辑职级说明'
            }else if(this.dialogStatus == 'detail') {
                return '职级说明详情'
            }
        },
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail"
        },
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                this.formData = this.$options.data().formData
                if(val && this.dialogStatus != 'create' && this.id) {
                    this.getDetail()
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    //附件
                    let formData = JSON.parse(JSON.stringify(self.formData));
                    let result = null;
                    self.buttonLoading = true;
                    if(this.dialogStatus == 'create') {
                        result = EmployeeLevelApi.add(formData)
                    }else if(this.dialogStatus == 'update') {
                        result = EmployeeLevelApi.edit(formData)
                    }
                    result.then(response => {
                        self.buttonLoading = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000,
                        });
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.buttonLoading = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            EmployeeLevelApi.detail({Id: this.id}).then(response => {
                this.loading = false
                this.formData = Object.assign({}, this.formData, response)
            }).catch(err => {
                this.loading = false
            })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
</style>
