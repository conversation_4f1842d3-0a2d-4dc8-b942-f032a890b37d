<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <el-tree class="elTree" ref="tree" v-loading="treeLoading" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" :default-expanded-keys="epKeys" :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node }">
                            <span class="node-title" :title="node.label | nodutyFilter" :style="{width: node.level == 1 ? '191px' : node.level == 2 ? '173px' : '145px'}">{{ node.label | nodutyFilter }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="__dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :optColWidth="180" :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" :multable="false" :isShowOpatColumn="true" :startOfTable="startOfTable" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" placeholder="搜索姓名/工号" @clear='getList' v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }' clearable v-model.trim="listQuery.Keywords"></el-input>
                                </template>
                                <!-- <template slot="Range">
                                    <el-date-picker v-model="listQuery.Range" type="daterange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                </template> -->
                            </app-table-form>
                        </template>

                        <template slot="AvatarPath" slot-scope="scope">
                            <!-- <div style="width: 100%; height: 100%; display: flex; justify-content: space-between; align-items:center; height: 80px;">
                                <img :src="scope.row.AvatarPath || defavatar" style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 75px; height: 75px;" />
                            </div> -->

                            <div class="avatar-wrapper">
                                <el-avatar fit='cover' :key="scope.row.EmployeeId" :size="50" :src="scope.row.AvatarPath || defavatar"></el-avatar>
                            </div>
                        </template>
                        <template slot="Level" slot-scope="scope">
                            <div v-if="scope.row.Level" style="width: 100%; height: 100%; display: flex; justify-content: space-between; align-items:center;">
                                <img :src="levelData[`levelImg${scope.row.Level}`]" style="width: 40px; height: 40px;" />
                            </div>
                            <div v-else>无</div>
                        </template>

                        
                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <!-- 详情 -->
                            <app-table-row-button @click="handleDetail(scope.row)" :type="2" text="学习详情"></app-table-row-button>
                            <!-- 积分明细 -->
                            <app-table-row-button @click="handleIintegralDetail(scope.row, false)" :type="2" text="积分明细"></app-table-row-button>
                            <!-- 调整积分 -->
                            <app-table-row-button @click="handleIintegralDetail(scope.row, true)" :type="2" text="调整积分"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" layout="total, prev, pager, next" />
            </div>
        </div>
    </div>
    <!-- 学习记录 -->
    <learning-records  @closeDialog="learningRecordsVisible=false" :dialogFormVisible="learningRecordsVisible" :checked-node="learningRecordsRow" />

    <!-- 积分明细 -->
    <integral-detail  @closeDialog="integralDetailVisible=false" :dialogFormVisible="integralDetailVisible" :id="selectId" :isSetIntegral="isSetIntegral" @reload="getList" />
</div>
</template>
<script>
import {
    listToTreeSelect
} from "@/utils";
import indexPageMixin from "@/mixins/indexPage";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as StudyRecordApi from '@/api/knowledge/StudyRecord'
import learningRecords from "./learningRecords";
import integralDetail from "./integralDetail";

export default {
    name: "plan-mgmt-index",
    mixins: [indexPageMixin],
    components: {
        learningRecords,
        integralDetail
    },
    props: {},
    filters: {
        nodutyFilter(value) {
            let duty = value.split(",");
            return duty[0];
        },
    },
    computed: {
    },
    watch: {
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.DepartmentId = val.Id;
                    // this.selectDepartmentName = val.DepartmentName;
                    this.getList();
                }
            },
            immediate: true,
        },
    },
    created() {
        // this.getList();
        this.getDepartments();
    },
    data() {
        return {
            // 学习记录
            learningRecordsVisible: false,
            learningRecordsRow: {},

            integralDetailVisible: false,
            selectId: '',
            isSetIntegral: false,


            defavatar: require("../../../assets/images/avatar3.png"),
            epKeys: [],
            treeLoading: false,
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "DepartmentName",
            },
            checkedNode: null, //当前单击选中的节点
            departmentListQuery: {
                DepartmentName: "",
            },

            layoutMode: 'simple',
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
                // { prop: "Range", label: "截止时间" },
            ],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "AvatarPath", label: "头像", showOverflowTooltip: true}, slot: true },
                { attr: { prop: "EmployeeName", label: "姓名", showOverflowTooltip: true}, },
                { attr: { prop: "EmployeeNo", label: "工号", sortable: "custom" } },
                { attr: { prop: "DepartmentName", label: "部门"}, },
                { attr: { prop: "EmployeeJobTitle", label: "职位"}, },
                { attr: { prop: "Level", label: "等级", sortable: "custom" }, slot: true },
                { attr: { prop: "TotalPeriod", label: "学时", sortable: "custom" }},
                { attr: { prop: "TotalIntegral", label: "总积分", sortable: "custom" }},
                { attr: { prop: "UsableIntegral", label: "可用积分", sortable: "custom" }},
            ],
            total: 0,
            listLoading: false,
            listQuery: {
                Keywords: '',
                DepartmentId: '',
                // visitRecordTypeEnum: 1,
                // visitRecordStateEnum: 1,
            }
        };
    },
    methods: {
        // 积分明细
        handleIintegralDetail(row,isShow) {
            this.isSetIntegral = isShow;
            this.selectId = row.EmployeeId;
            this.integralDetailVisible = true
        },
        // 查看详情
        handleDetail(row) {
            this.learningRecordsRow = row;
            this.learningRecordsVisible = true
        },
        onResetSearch() {
            this.listQuery.Keywords = "";
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        getList() {
            let self = this;
            self.listLoading = true;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);
            StudyRecordApi.GetIntegralListPage(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items || [];
                self.total = res.Total || 0;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.DepartmentName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        getDepartments() {
            this.treeLoading = true;
            systemDepartment
                .getListByCondition(this.departmentListQuery)
                .then((res) => {
                    this.treeDatas = listToTreeSelect(res);
                    // console.log(this.treeDatas)
                    if(this.treeDatas && this.treeDatas.length>0){
                        this.treeDatas.forEach(v => {
                            this.epKeys.push(v.Id);
                            // 只显示一级数据
                            if(v.children.length>0){
                                v.children.forEach(v1 => {
                                    // this.epKeys.push(v1.Id);
                                    delete v1.children
                                })
                            }
                        })
                    }
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
    },
};
</script>

<style scoped>
.pageWrapper >>> .list-wrapper{
    padding: 0;
}

.pageWrapper >>> .tag-list{
    padding: 10px 30px;
}
</style>

<style lang="scss" scoped>

.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;
        border-right: 1px solid #dcdfe6;
        .group:not(:last-child){
            margin-bottom: 10px;
        }
        .title{
            font-weight: bold;
            font-size: 16px;
            padding: 10px;
        }

    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .tagBox {
            border-bottom: 1px solid #dcdfe6;
            padding: 4px 8px;
        }
        .item-status{
            color: #fff;
            padding: 2px 4px;
            border-radius: 10%;
        }
    }


}

.badge{
    background-color: #f56c6c;
    border-radius: 10px;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
}

.avatar-wrapper{
    width: 50px;
    height: 50px;
    /deep/img{
        width: 100%;
        height: 100%;
    }
}
</style>
