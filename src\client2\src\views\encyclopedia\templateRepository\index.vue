<template>
<div class="app-container">
    <div class="bg-white">
        <div>
            <page-title title="组织资源库" :showBackBtn='true' @goBack="goBack" text-bold></page-title>
        </div>
        <div class="page-wrapper">
            <div class="product-list">
                <el-input class="elInput" style="margin: 10px 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label">{{ node.label }}</span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <page-title :showBackBtn='false'>
                    <div slot="def">
                        <tags :items="searchTypesData" v-model="listQuery.myFavorite">
                            <template v-for="t in searchTypesData" :slot="t.value">{{ t.label }}</template>
                        </tags>
                    </div>
                </page-title>
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" :optColWidth="110"
                    :isShowOpatColumn="true" :startOfTable="startOfTable"  :serial='false'
                    :multable="false" :layoutMode='layoutMode'>
                    <!-- <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading"
                    :isShowOpatColumn="true" :multable="false" :layoutMode='layoutMode' :isShowBtnsArea='false' :optColWidth="110"> -->
                        <!-- <template slot="tableKey7" slot-scope="scope">
                            <span>{{ scope.row.tableKey7 | dateFilter('YYYY-MM-DD HH:mm') }}</span>
                        </template>
                        <template slot="ViewRange" slot-scope="scope">
                            <span>{{ scope.row.ViewRange | ViewRangeFilter }}</span>
                        </template> -->
                        <template slot="Idx" slot-scope="scope">
                            {{ (listQuery.PageIndex - 1) * listQuery.PageSize + (scope.index + 1) }}
                            <i v-if="scope.row.IsFavorite" class="el-icon-star-on" style="font-size: 20px;color: #ffb81c;vertical-align: middle;"></i>
                        </template>
                        <template slot="Explain" slot-scope="scope">{{ scope.row.Explain | emptyFilter }}</template>

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'110px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="resetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索资源组织名称"
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
                            <app-table-row-button @click="SetFavorite(scope.row)"
                            :type="scope.row.IsFavorite?3:1" :text="scope.row.IsFavorite?'取消收藏':'设为收藏'"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogVisible" :id="selectId" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogVisible"
    @closeDialog="createDialogVisible=false" @saveSuccess="handleSaveSuccess" :page-type="2"></create-page>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as classify from '@/api/classify'
import { listToTreeSelect } from "@/utils";
import * as TemplateResourceApi from '@/api/knowledge/TemplateResource'
import createPage from "@/views/knowledge/templateManagement/create.vue";
export default {
    name: "template-Management",
    mixins: [indexPageMixin],
    components: {
        createPage,
    },
    computed: {
        hasTreeOpertAuth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnMaintain') > -1
        },
    },
    created() {
        this.loadTreeData();
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.ClassifyId = val.Id;
                    
                    this.getList();
                }
            },
        },
        "listQuery.myFavorite"() {
            this.listQuery.PageIndex = 1
            this.getList();
        },
    },
    mounted() {},
    data() {
        return {
            searchTypesData: [
                { label: "全部", value: 0 },
                { label: "我的收藏", value: 1 },
            ],

            layoutMode: 'simple',

            /******************* 树 *******************/
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,

            total: 0,
            listLoading: false,
            listQuery: {
                PageType: "onLineTraining",
                Keywords: '',
                myFavorite: 0,
                ClassifyId: null,
            },
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
            ],
            tabColumns: [
                { attr: {prop: "Idx",label: "序号", width: "50"},slot: true},
                { attr: { prop: "Name", label: "资源组织名称", showOverflowTooltip: true, }, },
                { attr: { prop: "ClassifyName", label: "资源分类", showOverflowTooltip: true, }, },
                { attr: { prop: "Explain", label: "组织资源说明", showOverflowTooltip: true, },slot: true },
            ],
            tabDatas: [],

            // 编辑
            selectId: '',
            createDialogStatus: 'create',
            createDialogVisible: false,
        };
    },
    methods: {
        // 收藏/取消收藏 
        SetFavorite(row){
            let self = this,str = row.IsFavorite?'取消收藏':'添加收藏';

            self.$confirm(`确定要${str}吗?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                TemplateResourceApi.favorites({Id: row.Id}).then(() => {
                    self.$notify({
                        title: "成功",
                        message: "操作成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getList();
                });
            });
        },
        resetSearch() {
            this.listQuery = this.$options.data().listQuery;
            this.getList();
        },
        /******************* 树事件 *******************/
        loadTreeData() {
            let _this = this;
            let paramData = {
                BusinessType: 18,
            };
            _this.treeLoading = true
            classify.getListPage(paramData).then(res => {
                _this.treeLoading = false

                let response = res.Items
                if (response && response.length > 0) {
                    response.unshift({
                        Id: "",
                        Name: "全部",
                        Level: 0,
                        ParentId: null
                    });
                    _this.treeData = listToTreeSelect(response, undefined, undefined, undefined, 'Name'); //将部门数据转换成树形结构
                    if (
                        !(
                            _this.checkedNode &&
                            response.find(t => {
                                return t.Id == _this.checkedNode.Id;
                            })
                        )
                    ) {
                        _this.checkedNode = _this.treeData[0];
                    }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        }
                    });
                }
            }).catch(err => {
                _this.treeLoading = false
            });
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData, isContinue) {
            // this.listQuery.PageIndex = 1

            this.getList();
            if(!isContinue) {
                this.closeDialog();
            }
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "create") {
            console.log(row)
            this.selectId = optType === 'create' ? '' : row.Id;
            this.createDialogStatus = optType
            this.createDialogVisible = true
        },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        //获取项目列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);
            // console.log(postData)
            TemplateResourceApi.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items;
                this.total = res.Total;
            });
        },
        goBack() {
            this.$router.go(-1)
        }
    },
};
</script>

<style lang="scss" scoped>

.app-container {
    // overflow-y: auto;

    .bg-white {
        .page-wrapper {
            display: flex;
            position: absolute;
            left: 0;
            top: 40px;
            right: 0;
            bottom: 0;
            min-height: calc(100% - 40px);
            padding: 0;
            margin: 0;

            .product-list {
                width: 250px;
                border-right: 1px solid #dcdfe6;
                display: flex;
                flex-direction: column;
                // >div:first-child{
                //     display: flex;
                //     justify-content: space-between;
                //     align-items:center;
                //     padding:0 10px;
                // }

                .treeBox {
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;

                    .elInput {
                        width: 230px;
                        margin-left: 10px;
                    }

                    .elTree {
                        height: 100%;
                        overflow: auto;
                    }
                }

            }

            .content-wrapper {
                width: calc(100% - 200px);
                flex: 1;
                overflow-y: auto;

                .content {

                    // padding: 10px;
                    // padding-right: 0;
                    .opt-wrapper {
                        box-sizing: border-box;
                        border-bottom: 1px solid #dcdfe6;
                        padding-bottom: 10px;
                    }

                    .list {}
                }
            }
        }
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}
.custom-tree-node {
    display: block;
    width: calc(100% - 24px);
    position: relative;
    box-sizing: border-box;
    padding-right: 30px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-btn-area {
        position: absolute;
        right: 0;
        top: 0;
        width: 23px;
        height: 16px;
    }
}

</style>
