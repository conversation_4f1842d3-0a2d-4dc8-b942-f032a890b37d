<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="在线培训" :subTitle="['在线培训管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <div class="btn-wrapper" v-if="auth()">
                    <el-button type="primary" @click="addClassification">创建手册类型</el-button>
                    <el-button type="primary" @click="handleTreeSortDialog">调整排序</el-button>
                </div>
                <div class="treeBox" v-loading="treeLoading">
                    <el-tree class="elTree" ref="tree" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="data => (checkedNode = data)">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label" :style="{
                    width:
                      node.level == 1
                        ? '151px'
                        : node.level == 2
                        ? '133px'
                        : '115px'
                  }">{{ node.label }}</span>
                            <span class="node-btn-area">
                                <el-dropdown trigger="click" v-if="auth()&&data.Id != defaultTreeNode.Id" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-show="data.Id != defaultTreeNode.Id && node.level < 3" command="add">添加子级</el-dropdown-item>
                                        <el-dropdown-item v-show="data.Id != defaultTreeNode.Id && node.level <= 3" command="update">编辑</el-dropdown-item>
                                        <el-dropdown-item v-show="data.Id != defaultTreeNode.Id && node.level <= 3" command="delete">删除</el-dropdown-item>
                                        <!-- <el-dropdown-item command="sort" v-show="data.Id == defaultTreeNode.Id">排序调整</el-dropdown-item> -->
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :multable="false" :startOfTable="startOfTable" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                        <template slot="IsShow" slot-scope="scope">
                            <span class="item-status" :class="`status-${scope.row.IsShow}`">
                                {{ scope.row.IsShow | isShowFilter }}
                            </span>
                        </template>
                        <template slot="ShowRange" slot-scope="scope">
                            <span>
                                {{ scope.row.ShowRange | showRangeFilter }}
                            </span>
                        </template>
                        <template slot="LastUpdateTime" slot-scope="scope">
                            {{ scope.row.LastUpdateTime | dateFilter('YYYY-MM-DD HH:mm') }}
                        </template>
                        
                        <!-- <template slot="PrincipalEmployeeList" slot-scope="scope">
                            <span v-if="scope.row.PrincipalEmployeeList">{{
                  scope.row.PrincipalEmployeeList.map(s => s.Name).join(",")
                }}</span>
                        </template> -->
                        <!-- <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template> -->

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="FuzzySearchString">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索手册名称"
                                        @clear='handleFilter'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                handleFilter()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.FuzzySearchString"
                                    ></el-input>
                                </template>

                                <template slot="IsShow">
                                    <el-select style="width:100%;" clearable v-model="listQuery.IsShow" placeholder="">
                                        <el-option
                                        v-for="item in status"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template slot="ShowRange">
                                    <el-select style="width:100%;" clearable v-model="listQuery.ShowRange" placeholder="">
                                        <el-option
                                        v-for="item in scopeApplication"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                        </el-option>
                                    </el-select>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <!-- <el-button style="width: 121px;" type="primary" @click="handleDialog('create')">创建手册</el-button> -->
                                    <permission-btn moduleName="xxxxx" :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked"></permission-btn>
                                </template>
                            </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnSetIsShow')" @click="handleSetIsShow(scope.row)" :text="scope.row.IsShow ? '设为无效' : '设为有效'"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 创建分类 -->
    <create-folder-page v-if="currentOptNode" @closeDialog="closeFolderDialog" @saveSuccess="handleFolderSaveSuccess" :dialogFormVisible="dialogFolderFormVisible" :dialogStatus="dialogFolderStatus" :node="currentOptNode">
    </create-folder-page>

    <!-- 创建一级分类 -->
    <create-classification-page @closeDialog="closeClassificationDialog" @saveSuccess="handleClassificationSaveSuccess" :dialogFormVisible="dialogClassificationFormVisible">
    </create-classification-page>

    <!-- 添加/修改  -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList" :selectTypeId="selectTypeId"></create-page>

    <tree-sort
        @closeDialog="closeTreeSortDialog"
        @saveSuccess="handleTreeSortSaveSuccess"
        :dialogFormVisible="dialogTreeSortFormVisible"
        :treeDatas='treeDatas.filter(s => s.Id != defaultTreeNode.Id)'
        :defaultProps='defaultProps'
        :rowKey='rowKey'
        :businessType='7'
        :defaultExpandedKeys="[]"
        @reload="gettrainsClassification"
    >
    </tree-sort>

</div>
</template>

<script>
import {
    listToTreeSelect
} from "@/utils";
import elDragDialog from "@/directive/el-dragDialog";
import indexPageMixin from "@/mixins/indexPage";
import * as empHandbook from "@/api/informationCenter/empHandbook";
import * as empHandbookClassification from "@/api/informationCenter/empHandbookClassification";
import createFolderPage from "./createFolder";
import createPage from "./create";
import createClassificationPage from "./createClassification";
import treeSort from '../../common/treeSort'
import { vars } from '../../workbench/myWorkbench/vars'
let status = [{
    label: '有效', value: true
},{
    label: '无效', value: false
}]
export default {
    name: "emp-handbook",
    mixins: [indexPageMixin],
    components: {
        createFolderPage,
        createPage,
        createClassificationPage,
        treeSort,
    },
    props: {},
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
        isShowFilter(isShow) {
            let obj = status.find(s => s.value == isShow)
            if(obj) {
                return obj.label
            }
            return '无'
        },
        showRangeFilter(val) {
            let obj = vars.scopeApplication.find(s => s.value == val)
            if(obj) {
                return obj.label
            }
            return '无'
        }
    },
    computed: {
        fildids() {
            return this.multipleSelection.map(s => s.Id) || [];
        }
    },
    watch: {
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.ClassificationId = val.Id;
                    this.getList();
                }
            },
            immediate: true
        }
    },
    created() {
        this.gettrainsClassification();
    },
    data() {
        return {
            status: status,
            scopeApplication: vars.scopeApplication,
            layoutMode: 'simple',
            //   btnAddChildren: '',
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "Name"
            },
            tableSearchItems: [{
                    prop: "FuzzySearchString",
                    label: "",
                    mainCondition: true
                },
                {
                    prop: "IsShow",
                    label: "状态"
                },
                {
                    prop: "ShowRange",
                    label: "可见范围"
                },
            ],

            checkedNode: null, //当前单击选中的节点
            classificationListQuery: {
                // ClassificationName: ""
            },

            dialogFolderFormVisible: false,
            dialogFolderStatus: "create",
            currentOptNode: null, //当前操作的文件夹节点（新增、编辑、删除）
            selectTypeId: "", //当前选中的类型ID

            dialogClassificationFormVisible: false,

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            tabColumns: [
                {
                    attr: {
                        prop: "Name",
                        label: "员工手册名称",
                        showOverflowTooltip: true, width: '400'
                    }
                },
                {
                    attr: {
                        prop: "ClassificationName",
                        label: "手册分类", 
                        //    sortable: 'custom'
                    }
                },
                {
                    attr: {
                        prop: "IsShow",
                        label: "状态",
                        sortable: "custom", 
                        // width: '100'
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "ShowRange",
                        label: "可见范围",
                        // sortable: "custom",
                        // width: '100'
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "LastUpdateTime",
                        label: "最后修改时间",
                        sortable: "custom",
                        // width: '100'
                    },
                    slot: true
                },
                
                
                // {
                //     attr: {
                //         prop: "PrincipalEmployeeList",
                //         label: "负责人", width: '200'
                //     },
                //     slot: true
                // }
            ],
            listQuery: {
                ClassificationId: "",
                FuzzySearchString: '',
                IsShow: null,
                ShowRange: null,
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,
            defaultTreeNode: {
                Id: "00000000-0000-0000-0000-000000000000",
                ParentID: null,
                Name: "全部"
            },

            dialogTreeSortFormVisible: false,
            rowKey: 'Id',
        };
    },
    methods: {
        onResetSearch() {
            this.listQuery.IsShow = null;
            this.listQuery.ShowRange = null;
            this.listQuery.FuzzySearchString = '';
            this.getList(); //刷新列表
        },
        //查看详情
        handleReview(row, optType = "detail") {
            //   this.$router.push(`/informationCenter/productCenter/productPresentations/detail/${row.Id}?productClassificationId=${row.ProductClassificationId}`)

            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order
            };
            this.getList();
        },
        //获取列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData.ClassificationId = null;
            if (this.checkedNode && this.checkedNode.Id != this.defaultTreeNode.Id) {
                postData.ClassificationId = this.checkedNode.Id;
            }
            postData = this.assignSortObj(postData);
            this.listLoading = true;
            empHandbook
                .getList(postData)
                .then(res => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch(err => {
                    this.listLoading = false;
                });
        },
        auth() {
            return this.topBtns.findIndex(s => s.DomId == 'btnAddClassification') > -1
        },
        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId == 'btnAdd')
            }
            return []
        },
        onBtnClicked: function (domId) {
            switch (domId) {
                //添加分类
                case "btnAddClassification":
                    this.addClassification();
                    break;
                    //添加
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                    //批量删除
                case "btnBatchDel":
                    if (this.multipleSelection.length < 1) {
                        this.$message({
                            message: "至少删除一个",
                            type: "error"
                        });
                        return;
                    }
                    this.handleTableDelete(this.multipleSelection);
                    break;
                default:
                    break;
            }
        },

        //弹出添加框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
            this.selectTypeId = this.listQuery.ClassificationId;
        },

        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        //设置有效/无效
        handleSetIsShow(rows) {
            // let ids = [];
            // if (_.isArray(rows)) {
            //     ids = rows.map(u => u.Id);
            // } else {
            //     ids.push(rows.Id);
            // }
            if(rows) {
                var isShow = rows.IsShow;
                var message = isShow ? "设置为无效" : "设置为有效";
                this.$confirm("是否确认" + message + "?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    empHandbook.setIsShow({id: rows.Id}).then(() => {
                        this.$notify({
                            title: "成功",
                            message: "设置成功",
                            type: "success",
                            duration: 2000
                        });
                        this.getList();
                    });
                });
            }
        },

        // 多行删除
        handleTableDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                empHandbook.del({id: rows.Id}).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },

        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        gettrainsClassification() {
            this.treeLoading = true;
            empHandbookClassification
            .getAllClassifications(this.classificationListQuery)
            .then(res => {
                this.treeLoading = false;
                if (!res) {
                    res = [];
                }
                res.splice(0, 0, this.defaultTreeNode);

                // this.treeDatas = listToTreeSelect(res);

                this.treeDatas = listToTreeSelect(res,undefined,undefined,{key: 'Id',parentKey: 'ParentID'}, 'Name');
                //如果首次加载问价夹树（没有选中），默认选中根节点
                if (!this.checkedNode) {
                    this.setDefaultChecked();
                }
            })
            .catch(err => {
                this.treeLoading = false;
            });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },

        //左侧树操作菜单
        handleCommand(optType, node, data) {
            this.currentOptNode = node;
            if (optType == "add") {
                this.handleCreateFolder(data);
            } else if (optType == "update") {
                this.handleCreateFolder(data, "update");
            } else if (optType == "delete") {
                this.handleDelete(node, data);
            } else if (optType == 'sort') {
                this.handleTreeSortDialog()
            }
        },

        //添加父分类
        addClassification() {
            this.dialogClassificationFormVisible = true;
            //   this.$prompt('', '添加产品分类', {
            //     confirmButtonText: '确认',
            //     cancelButtonText: '取消',
            //     inputPlaceholder: '请输入分类名称',
            //     inputPattern: /^[\u4e00-\u9fffa-zA-Z0-9]{1,10}$/,
            //     inputErrorMessage: '只能输入汉字、字母、数字，并且不能超过10个字符'
            //   }).then(({ value }) => {
            //     var obj = {
            //       ProductClassificationName: value,
            //       ParentId: null,
            //       Level: 1
            //     };
            //     productClassification.add(obj).then(res => {
            //       this.$notify({
            //         title: '成功',
            //         message: '添加成功',
            //         type: 'success',
            //         duration: 2000
            //       })
            //       this.getProductClassification()
            //     })
            //   }).catch(() => { });
        },

        // 添加/修改 子分类
        handleCreateFolder(data, optType = "create") {
            this.dialogFolderStatus = optType;
            //this.currentOptNode = data
            this.dialogFolderFormVisible = true;
        },

        //删除分类
        handleDelete(node, data) {
            this.$confirm(
                `是否确认删除?`,
                "提示", {
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            ).then(() => {
                empHandbookClassification.del({id: data.Id}).then(res => {
                    if (this.checkedNode && this.checkedNode.Id == data.Id) {
                        this.checkedNode = null;
                    }
                    this.gettrainsClassification();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                });
            });
        },

        //关闭树菜单的操作层
        closeFolderDialog() {
            this.dialogFolderFormVisible = false;
        },

        //树菜单操作保存
        handleFolderSaveSuccess() {
            this.gettrainsClassification();
            this.closeFolderDialog();
        },

        closeClassificationDialog() {
            this.dialogClassificationFormVisible = false;
        },

        handleClassificationSaveSuccess() {
            this.gettrainsClassification();
            this.closeClassificationDialog();
        },

        // 排序
        handleTreeSortDialog() {
            this.dialogTreeSortFormVisible = true
        },
        closeTreeSortDialog() {
            this.dialogTreeSortFormVisible = false
        },
        handleTreeSortSaveSuccess() {
            this.closeTreeSortDialog();
        },
    }
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    flex: 1;
    padding-bottom: 10px;
    overflow-y: auto;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        overflow: auto;
        margin-top: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

/* 有效 */
.status-true {
    background-color: green;
}

/* 无效 */
.status-false {
    background-color: red;
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}

.btn-wrapper {
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
