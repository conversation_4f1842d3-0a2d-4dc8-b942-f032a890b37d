<template>
    <!-- 企业资质 -->
<div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
        <div class="pageWrapper __dynamicTabWrapper">
            <app-table ref="mainTable"
            :layoutMode='layoutMode' :multable="false" :isShowOpatColumn="false"
            :isShowBtnsArea='false' :isShowAllColumn="true" :optColWidth="120"
            :loading="listLoading"
            :tab-columns="tabColumns"
            :tab-datas="tabDatas"
            :tab-auth-columns="tabAuthColumns"
            :startOfTable="startOfTable" @rowSelectionChanged="rowSelectionChanged"
            @sortChagned="handleSortChange">
                <!-- 表格查询条件区域 -->
                <template slot="conditionArea">
                    <!-- <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                        <template slot="Keywords">
                            <el-input style="width: 100%;" placeholder="搜索资质名称/证书编号" @clear='getList' v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        getList()
                                    }
                                }' clearable v-model.trim="listQuery.Keywords"></el-input>
                        </template>

                        <template slot="btnsArea">
                            <permission-btn v-on:btn-event="onBtnClicked">
                                <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                                    <el-button type="primary">
                                        {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                                    </el-button>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="batchTermOfValidity">修改有效期</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </permission-btn>
                        </template>
                    </app-table-form> -->
                </template>

                
                <template slot="ClassHour" slot-scope="scope">
                    >= {{ scope.row.ClassHour }}
                </template>
                <template slot="TotalPoints" slot-scope="scope">
                    >= {{ scope.row.TotalPoints }}
                </template>

                <template slot="Icon" slot-scope="scope">
                    <img style="width: 60px; height: 60px;" :src="levelData[`levelImg${scope.row.OrderIndex}`]" alt="">
                </template>
                <template slot="Description" slot-scope="scope">
                    {{ scope.row.Description || '无' }}
                </template>

                <!-- 表格行操作区域 -->
                <template slot-scope="scope">
                    <!-- 详情 -->
                    <app-table-row-button @click="handleUpdate(scope.row,'detail')" text="详情" :type="2"></app-table-row-button>
                    <!-- 编辑 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row, 'update')" :type="1"></app-table-row-button>
                    <!-- 删除 -->
                    <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" :type="3"></app-table-row-button>
                </template>
            </app-table>
        </div>
        <!-- <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" /> -->
    </div>
    <!-- 创建/修改 列表 -->
    <create-page v-if="createDialogFormVisible" :id="selectId" :dialogStatus="createDialogStatus" :dialogFormVisible="createDialogFormVisible" @closeDialog="createDialogFormVisible=false" @saveSuccess="createSaveSuccess"></create-page>
</div>
</template>
<script>
import * as learnLevel from '@/api/knowledge/LearnLevel'
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";


import { vars } from '../common/vars'
export default {
    name: 'level-setting',
    mixins: [indexPageMixin],
    components: {
        createPage,
    },
    filters: {
        patentWorkTypeFilter(val) {
            let obj = vars.patentWorkTypes.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "";
        }
    },
    created() {
        this.getList();
    },
    data() {
        return {
            patentWorkTypes: vars.patentWorkTypes, // 著作类型 s
            statusTypes: vars.statusTypes, // 列表中状态  s
            
            selectId: '',
            createDialogStatus: 'create',
            createDialogFormVisible: false,

            total: 0,
            listQuery: {
                PageSize: 1000
            },
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
                { prop: "Keywords", label: "", mainCondition: true },
            ],
            multipleSelection: [],
            tabDatas: [],
            tabColumns: [
                { attr: { prop: "Icon", label: "Icon"}, slot: true },
                { attr: { prop: "Name", label: "等级名称"} },
                { attr: { prop: "Description", label: "等级描述", showOverflowTooltip: true}, slot: true },
                { attr: { prop: "ClassHour", label: "学时", sortable: 'custom'}, slot: true },
                { attr: { prop: "TotalPoints", label: "总积分", sortable: 'custom'}, slot: true },
            ],
        }
    },
    methods: {
        getList() {
            let self = this;
            self.listLoading = true;
            let postDatas = JSON.parse(JSON.stringify(self.listQuery))
            postDatas = self.assignSortObj(postDatas);

            learnLevel.getList(postDatas).then(res => {
                self.listLoading = false;
                self.tabDatas = res.Items;
                self.total = res.Total;
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },
        onResetSearch() {
            // this.listQuery.Keywords = "";
            this.listQuery = this.$options.data().listQuery
            this.getList()
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        // 表格顶部按钮点击事件
        onBtnClicked(domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleUpdate(null, 'create');
                    break;
                case "batchTermOfValidity":
                    this.handleTermOfValidity();
                    break;
                default:
                    break;
            }
        },
        /** 弹出 批量编辑编辑有效期 */
        handleTermOfValidity() {
            if (this.multipleSelection.length>0) {
            } else {
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
        handleUpdate(row, optType = "update") {
            this.selectId = optType === 'create' ? '' : row.Id;
            this.createDialogStatus = optType
            this.createDialogFormVisible = true
        },
        /** 编辑框 点确定后 关闭 并刷新列表 */
        createSaveSuccess(d) {
            console.log('d',d)
            if (!d) {
                this.createDialogFormVisible = false
            }
            this.listQuery.PageIndex = 1;
            this.getList()
        },

        /** 删除 */
        handleDelete(rows) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                learnLevel.del([rows.Id]).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        getStatusObj(val) {
            return vars.statusTypes.find(
                s => s.value == val
            ) || {};
        },
    }
}
</script>
