<template>
  <div class="lossStatistical">
    <div class="tagBox">
      <tags :items="types" v-model="tagType" @change="handleTagsChange">
        <template v-for="t in types" :slot="t.value">{{ t.label }}</template>
      </tags>
    </div>
    <div class="main">
      <section v-loading="loadingLeft" v-if="tagType == 1">
        <div style="height:calc(100% - 52px);padding-bottom:10px;overflow-y: auto;">
          <template v-if="leftList && leftList.length > 0">
            <el-card class="box-card" v-for="(ll,index) in leftList" :key="index">
              <!-- <template slot="title"> -->
                <div class="collHead">
                  <div class="headTitle ">
                    <span class="" :style="'background:'+getColor(index)">{{(leftListQuery.PageSize * (leftListQuery.PageIndex - 1)) + index+1}}</span>
                    <span
                      class="omit "
                      :title="ll.Phenomenon"
                    >{{ll.<PERSON>enomenon}}</span>
                    <el-button type="text" @click="openDialog(ll)">查看详情</el-button>
                  </div>
                  <div>
                    <span>故障数量：{{ll.Count}}</span>
                    <span>故障占比：{{ll.Percent}}%</span>&emsp;
                    <div>
                      <el-progress :percentage="ll.Percent" :color="ll.Percent >= 50 ? '#FF9900' : '#5B9CFF'" :show-text="false"></el-progress>
                    </div>
                  </div>
                </div>
            </el-card>
          </template>
          <no-data v-else style="height:166px;"></no-data>
        </div>
        <pagination
          v-show="leftListTotal > 0"
          :total="leftListTotal"
          :page.sync="leftListQuery.PageIndex"
          :size.sync="leftListQuery.PageSize"
          @pagination="handleLeftListCurrentChange"
          @size-change="handleLeftListSizeChange"
        />
      </section>
      <section class="fl" v-loading="loadingLeft" v-else>
        <div style="height:calc(100% - 52px);padding-bottom:10px;overflow-y: auto;">
          <ul class="ulTagBox cl" style="padding:8px 0 0 0;">
            <li
              @click="handleChangeAcce(ut.value)"
              class="fl"
              :class="{'active':ut.value == activeAcceIndex}"
              v-for="ut in ulTag"
              :key="ut.value"
            >{{ut.label}}({{ut.amount}})</li>
          </ul>
          <template v-if="tabDatas.length>0">
            <el-card class="box-card" v-for="(ll,index) in tabDatas" :key="index">
              <el-collapse v-model="ll.activeName">
                <el-collapse-item :name="index">
                  <template slot="title">
                    <div class="collHead">
                      <div class="headTitle cl">
                        <span class="fl" :style="'background:'+getColor(index)">{{(acceMsgQuery.PageSize * (acceMsgQuery.PageIndex - 1)) + index+1}}</span>
                        <span
                          class="omit fl"
                          :title="ll.StructPartName"
                        >{{ll.StructPartName}}</span>
                        <el-button class="fr" @click.stop="handleDetail(ll.StructPartName, 'StructPartName')" type="text">查看报修单</el-button>
                      </div>
                      <div>
                        <span>故障数量：{{ll.Count}}</span>
                        <span>故障占比：{{ll.Percent}}%</span>&emsp;
                        <div>
                          <el-progress :percentage="ll.Percent" :color="ll.Percent >= 50 ? '#FF9900' : '#5B9CFF'" :show-text="false"></el-progress>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="lsMain" style="border-top:1px solid #dcdfe6;">
                    <div class="collHead2 cl bpt" style="padding-top: 2px;padding-bottom: 2px;" v-for="(sml,i) in ll.SpecificationModelList">
                      <div class="fl cl">
                        <el-link type="info">
                          <span
                            class="omit fl cursor"
                            style="font-weight:normal;"
                          :title="sml.SpecificationModel" @click="handleDetail(sml.SpecificationModel, 'SpecificationModel')">{{sml.SpecificationModel}}</span>
                          <span class="fl cursor" @click="handleDetail(sml.SpecificationModel, 'SpecificationModel')">({{sml.Count}})</span>
                        </el-link>
                      </div>
                      <div class="fr">
                        <span>配件占比：{{sml.Percent}}%</span>&emsp;
                        <div>
                          <el-progress :percentage="sml.Percent" :color="sml.Percent >= 50 ? '#FF9900' : '#5B9CFF'" :show-text="false"></el-progress>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </el-card>
          </template>
          <no-data v-else style="height:166px;"></no-data>
        </div>
          <pagination
            v-show="acceMsgTotal > 0"
            :total="acceMsgTotal"
            :page.sync="acceMsgQuery.PageIndex"
            :size.sync="acceMsgQuery.PageSize"
            @pagination="handleAcceMsgCurrentChange"
            @size-change="handleAcceMsgSizeChange"
          />
      </section>
      <aside class="fl" v-loading="loadingFaultRight" v-if="tagType == 1">
        <div style="height:100%;">
          <div class="asideHead">
            <span>故障类型统计排名</span>
            <div>
              <span>排序:</span>
              <el-select v-model="selectFaultValue" placeholder="请选择" @change="hangdleSortFaultChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div class="asideMain" v-if="rightFaultList && rightFaultList.length>0" style="height:calc(100% - 48px);overflow-y:auto;">
            <el-card class="box-card" v-for="(rl,index) in rightFaultList" :key="index">
              <div class="cardLi cl">
                <span class="fl" :style="'background:'+getColor(index)">{{index+1}}</span>
                <span class="omit fl" :title="rl.FaultName">{{rl.FaultName}}</span>
                <span class="fr">({{rl.Count}})</span>
              </div>
            </el-card>
          </div>
          <no-data v-else></no-data>
        </div>
      </aside>
      <aside class="fl" v-loading="loadingRight" v-else>
        <div style="height:100%">
          <div class="asideHead">
            <span>供应商配件用量统计</span>
            <div>
              <span>排序:</span>
              <el-select v-model="selectValue" placeholder="请选择" @change="hangdleSortChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
          <ul class="ulTagBox cl">
            <li
              @click="hangleRightClick(i,ut.value)"
              class="fl"
              :class="{'active':i == rightActiveIndex}"
              v-for="(ut,i) in asideTag"
              :key="i"
            >{{ut.label}}</li>
          </ul>
          <div class="asideMain" v-if="rightList.length>0" style="height:calc(100% - 79px);overflow-y:auto;">
            <el-card class="box-card" v-for="(rl,index) in rightList" :key="index">
              <div class="cardLi cl">
                <span class="fl" :style="'background:'+getColor(index)">{{index+1}}</span>
                <span class="omit fl" :title="rl.SupplierName">{{rl.SupplierName}}</span>
                <span class="fr">({{rl.Count}})</span>
              </div>
            </el-card>
          </div>
          <no-data v-else></no-data>
        </div>
      </aside>
    </div>

    <detail-page 
        v-if="dialogFormVisible"
        @closeDialog='closeDialog' 
        @saveSuccess='() => {}'
        :dialogFormVisible='dialogFormVisible'
        :keyworks='keyworks'
        :year='new Date(this.yearVal).getFullYear()'
        :regionalId='regionalId'
    >
    </detail-page>

    <record v-if="dialogRecordFormVisible" :condition='condition' @closeDialog="closeRecordDialog" :dialogFormVisible="dialogRecordFormVisible"></record>
  </div>
</template>
<script>
import * as maintenOrderMgmt from "@/api/maintenanceCenter/maintenOrderMgmt";
import NoData from "@/views/common/components/noData";
import detailPage from './detail'
import record from './record'
import { vars } from '../common/vars'
import * as orderVars from '../../../afterSalesMgmt/maintenCenter/common/vars'

export default {
  name: "lossStatistical",
  // mixins: [indexPageMixin],
  components: {
    NoData,
    detailPage,
    record,
  },
  props:['yearVal', 'regionalId'],
  data() {
    return {
      loadingFaultRight: false,
      rightFaultList: [],
      selectFaultValue: 1,
      // faultTypeOptions: orderVars.vars.maintenOrderMgmt.FaultTypeOptions.map(s => {
      //   s.total = 0
      //   return s
      // }).push({total: 0, label: '未知类', value: 999}),
      rightProcessMode:1,
      activeAcceIndex:0,
      loadingRight:false,
      loadingLeft:false,
      rightActiveIndex:0,
      options: [
        {
          value: 1,
          label: "从高到低",
        },
        {
          value: 2,
          label: "从低到高",
        },
      ],
      selectValue: 1,
      
      tagType: 1,
      types: [
        { value: 1, label: "故障现象分析" },
        { value: 2, label: "配件损耗分析" },
      ],
      leftListTotal: 0,
      leftListQuery: {
        PageIndex: 1, 
        PageSize: 20
      },
      activeNames: ["1"],
      total: 0,
      PageIndex: 1,
      PageSize: 6,
      total1: 0,
      PageIndex1: 1,
      PageSize1: 10,
      ulTag: vars.handling.map(s => {
        s.amount = 0
        return s
      }),
      // ulTag: [
      //   {
      //     label: "更换",
      //     value:4,
      //     amount:0,
      //   },
      //   {
      //     label: "维修",
      //     value:1,
      //     amount:0,
      //   },
      //   {
      //     label: "拆除",
      //     value:2,
      //     amount:0,
      //   },
      //   // {
      //   //   label: "被替换",
      //   //   value:3,
      //   // },
        
      //   {
      //     label: "新增",
      //     value:5,
      //     amount:0,
      //   },
      // ],
      asideTag: JSON.parse(JSON.stringify(vars.handling)).map(s => {
        s.label = s.label + '排名'
        return s
      }),
      // asideTag: [
      //   {
      //     label: "更换排名",
      //     value:4,
      //   },
      //   {
      //     label: "维修排名",
      //     value:1,
      //   },
      //   {
      //     label: "拆除排名",
      //     value:2,
      //   },
      //   // {
      //   //   label: "被替换排名",
      //   //   value:3,
      //   // },
        
      //   {
      //     label: "新增排名",
      //     value:5,
      //   },
      // ],
      rightList:[],
      leftList:[],
      // acceData:null,
      tabDatas: [],
      // acceMsg:[],
      acceMsgQuery: {
        PageIndex: 1,
        PageSize: 20
      },
      acceMsgTotal: 0,
      dialogFormVisible: false,
      keyworks: '',

      dialogRecordFormVisible: false,
      condition: null,

    };
  },
  filters: {},
  watch: {
    yearVal(val){
      if(this.tagType == 1){
        this.getRightFaultList()
        this.getLeftList();
      }else{
        this.getRightList();
        this.getAccessoriesSummary()
        // this.getAccessoriesList();
      }
    },
    regionalId(val) {
      if(this.tagType == 1){
        this.getRightFaultList()
        this.getLeftList();
      }else{
        this.getRightList();
        this.getAccessoriesSummary()
        this.getAccessoriesList();
      }
    },
    tagType(val) {
      if(val == 1){
        this.getRightFaultList()
      }else{
        this.getRightList();
      }
    },
    activeAcceIndex: {
      handler(val) {
        if(val) {
          this.getAccessoriesList();
        }
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.getRightFaultList()
    this.getLeftList();
  },
  methods: {
    openDialog(row) {
      this.keyworks = row.Phenomenon
      this.dialogFormVisible = true
    },
    closeDialog() {
      this.dialogFormVisible = false
    },
    handleChangeAcce(i,val){
      this.activeAcceIndex=i;
      // let a=this.acceData.find(v => v.ProcessMode == val);
      // if(a){
      //   this.acceMsg=a.StructPartList;
      // }else{
      //   this.acceMsg=[];
      // }
    },
    handleLeftListCurrentChange(val) {
      this.leftListQuery.PageIndex = val.page;
      this.leftListQuery.PageSize = val.size;
      this.getLeftList();
    },
    handleLeftListSizeChange(val) {
      this.leftListQuery.PageSize = val.size;
      this.getLeftList();
    },
    getFaultPhenomenonAnalysis() {
      maintenOrderMgmt.getFaultPhenomenonAnalysis({}).then(res => {
        
      })
    },
    getLeftList(){
      this.loadingLeft=true;
      let postData = Object.assign(this.leftListQuery) 
      postData.year = new Date(this.yearVal).getFullYear()
      postData.regionalId = this.regionalId
      maintenOrderMgmt.getFaultPhenomenonAnalysisChart(postData).then(res => {
        this.loadingLeft=false;
        let totalAround=0;
        if(res && res.Items) {
          res.Items.forEach(v => {
            totalAround+=v.Count;
            v.children=[];
            v.activeName=[];
            v.activeIndex=0;
            // v.acceData=null;
            // v.acceMsg=[];
            v.loading=false;
 
          })
          // res.Items.forEach(v => {
          //   v.percent=v.Count/totalAround;
          //   v.percent=Math.round(v.percent*10000)/100;
          // })
        }
        this.leftList = res.Items;
        this.leftListTotal = res.Total
      }).catch(err => {
        this.loadingLeft=false;
      })
    },
    getColor(index){
      return (index == 0 ? '#FF9900;' : (index == 1 ? '#5B9CFF;' : (index == 2 ? '#00cc00;' : '#aaaaaa;')));
    },
    hangdleSortFaultChange(d){
      this.getRightFaultList();
    },
    getRightFaultList(){
      let postData={
        "year": new Date(this.yearVal).getFullYear(),
        "processMode": this.rightProcessMode,
        "sortord": this.selectFaultValue,
        "regionalId": this.regionalId
      }
      this.loadingFaultRight=true;

      let faultTypes = JSON.parse(JSON.stringify(orderVars.vars.maintenOrderMgmt.FaultTypeOptions)) || []
      faultTypes.push({value: 999, label: '未知类'})

      maintenOrderMgmt.faultTypeStatistical(postData).then(res => {
        this.loadingFaultRight=false

        this.rightFaultList = res.map(s => {
          let faultObj = faultTypes.find(t => s.FaultType == t.value)
          s.FaultName = faultObj ? faultObj.label : '无'
          return s
        })
      }).catch(err => {
        this.loadingFaultRight=false
      })
    },
    getRightList(){
      this.loadingRight=true;
      let postData={
        "year": new Date(this.yearVal).getFullYear(),
        "processMode": this.rightProcessMode,
        "sortord": this.selectValue,
        "regionalId": this.regionalId
      }
      maintenOrderMgmt.getSupplierCount(postData).then(res => {
        this.loadingRight=false;
        this.rightList=res;
      }).catch(err => {
        this.loadingRight=false;
      })
    },
    hangleRightClick(d,val){
      this.rightActiveIndex=d;
      this.rightProcessMode=val;
      this.getRightList();
    },
    
    
    hangdleSortChange(d){
      this.getRightList();
    },
    handleTagsChange(d) {
      if(d == 1){
        this.getLeftList();
      }else{
        this.getAccessoriesSummary()
        // this.getAccessoriesList();
      }
    },
    handleAcceMsgCurrentChange(val) {
      this.acceMsgQuery.PageIndex = val.page;
      this.acceMsgQuery.PageSize = val.size;
      this.getAccessoriesList();
    },
    handleAcceMsgSizeChange(val) {
      this.acceMsgQuery.PageSize = val.size;
      this.getAccessoriesList();
    },

    getAccessoriesSummary(){
      let postData = Object.assign(this.acceMsgQuery) 
      postData.year = new Date(this.yearVal).getFullYear()
      postData.regionalId = this.regionalId
      maintenOrderMgmt.getAccessoriesLossAnalysisCount(postData).then(res => {
        if(!this.activeAcceIndex && this.ulTag && this.ulTag.length > 0) {
          this.activeAcceIndex = this.ulTag[0].value
        }
        this.ulTag.forEach(e => {
          let obj = res.find(s => s.ProcessMode == e.value)
          if(obj) {
            e.amount = obj.Count
          }
        })

        // this.ulTag.forEach(v => {
          
        //   this.acceData.forEach(v1 => {
        //     if(v1.ProcessMode == 4){
        //       this.acceMsg=v1.StructPartList;
        //     }
        //     if(v.value == v1.ProcessMode){
        //       v.amount=v1.Count;
        //     }
        //   })
        // })

      }).catch(err => {
        this.loadingLeft=false;
      })
    },

    getAccessoriesList(){
      this.loadingLeft=true;

      let postData = Object.assign(this.acceMsgQuery) 
      postData.year = new Date(this.yearVal).getFullYear()
      postData.ProcessMode = this.activeAcceIndex
      postData.regionalId = this.regionalId
      maintenOrderMgmt.getAccessoriesLossAnalysis(postData).then(res => {
        let obj = this.ulTag.find(e => e.value == this.activeAcceIndex)

        this.loadingLeft=false;

        res.Items.forEach(v1 => {
          v1.activeName=[];
          // if(obj && obj.amount > 0) {
          //   v1.percent=v1.Count/obj.amount;
          // }
          
          // v1.percent=Math.round(v1.percent*10000)/100;
          // v1.SpecificationModelList.forEach(v2 => {
          //   v2.percent=v2.Count/v1.Count;
          //   v2.percent=Math.round(v2.percent*10000)/100;
          // })
        })
        this.tabDatas=res.Items;
        this.acceMsgTotal = res.Total
        // else{
        //   this.acceData=[];
        //   this.acceMsg=[];
        //   this.ulTag.forEach(v => {
        //     v.amount=0;
        //   })
        // }
      }).catch(err => {
        this.loadingLeft=false;
      })
    },
    // handleChangeChildren(d,i,val){
    //   d.activeIndex=i;
    //   let a=d.acceData.find(v => v.ProcessMode == val);
    //   if(a){
    //     d.acceMsg=a.StructPartList;
    //   }else{
    //     d.acceMsg=[];
    //   }
    //   // this.handleChange(index);
    // },
    // handleChange(d) {
    //   if(d.activeName.length>0){
    //     d.loading=true;
    //     let params={
    //       PageIndex: 1,
    //       PageSize: 20,
    //       year:new Date(this.yearVal).getFullYear(),
    //       phenomenon:d.Phenomenon
    //     }
    //     maintenOrderMgmt.getFaultChart(params).then(res => {
    //       console.log(4,res)

    //       d.loading=false;
    //       if(res.length>0){
    //         res.forEach(v => {
    //           v.StructPartList.forEach(v1 => {
    //             v1.percent=v1.Count/v.Count;
    //             v1.percent=Math.round(v1.percent*10000)/100;
    //             v1.SpecificationModelList.forEach(v2 => {
    //               v2.percent=v2.Count/v1.Count;
    //               v2.percent=Math.round(v2.percent*10000)/100;
    //             })
    //           })
    //         })
    //         d.acceData=res;
    //         d.ulTag.forEach(v => {
    //           d.acceData.forEach(v1 => {
    //             if(v1.ProcessMode == 4){
    //               d.acceMsg=v1.StructPartList;
    //             }
    //             if(v.value == v1.ProcessMode){
    //               v.amount=v1.Count;
    //             }
    //           })
    //         })
    //       }else{
    //         d.acceData=[];
    //         d.acceMsg=[];
    //         d.ulTag.forEach(v => {
    //           v.amount=0;
    //         })
    //       }
    //     }).catch(err => {
    //       d.loading=false;
    //     })
    //   }
    // },
    handleChildrenChange(val){

    },
    handleCurrentChange(val) {
      this.PageIndex = val.page;
      this.PageSize = val.size;
    },
    handleCurrentChange1(val) {
      this.PageIndex1 = val.page;
      this.PageSize1 = val.size;
    },


    handleDetail(words, key) {
      let type = this.activeAcceIndex
      let str = words

      this.condition = {
        ProcessMode: type,
        RegionalId: this.regionalId
      }

      this.condition[key] = str
      
      this.dialogRecordFormVisible = true

    },
    closeRecordDialog() {
      this.dialogRecordFormVisible = false
    },
  },
};
</script>
<style lang="scss" scoped>
.elCollBt{
  >div:not(:first-child){
    border-top:1px solid #EBEEF5;
  }
}
@mixin bCircle {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 20px;
  text-align: center;
  background: #aaaaaa;
  color: white;
  margin-right: 10px;
}
.asideMain{
    padding:0 10px;
}
.cardLi {
  display: flex;
  align-items: center;
  padding: 10px 0 10px 10px;
  > span:nth-child(1) {
    @include bCircle;
  }
  > span:nth-child(2) {
    width: calc(100% - 70px);
  }
}
.asideHead {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding:10px;
  > span {
    font-weight: 700;
  }
}
.lossStatistical {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.main {
  // position: absolute;
  width: 100%;
  // flex: 1;
  overflow-y: auto;
  // height: calc(100% - 36px);
}
section {
  width: 66.66666%;
  padding: 0 10px;
  border-right: 1px solid #dcdfe6;
  height: 100%;
  overflow-y: auto;
  padding-bottom: 10px;
}
aside {
  width: 33.33333%;
  height: 100%;
  overflow-y: auto;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  padding-bottom:10px;
  .ulTagBox{
    padding:6px 10px 0 10px;
  }
}
.collHead {
  width: 100%;
  line-height: 36px !important;
  padding-left: 10px;
  padding-right: 10px;
  > div:nth-child(2) {
    display: flex;
    >span{
      width:120px;
    }
    > div {
      width: calc(100% - 242px);
      > div {
        margin-top: 14px;
      }
    }
  }
}
.collHead2 {
  width: 100%;
  padding: 6px 0;
  > div {
    width: 50%;
  }
  > div:nth-child(1) {
    > span:nth-child(1) {
      max-width: 70%;
      font-weight: 700;
    }
  }
  > div:nth-child(2) {
    display: flex;
    > span {
      width: 110px;
      text-align: right;
    }
    > div {
      width: calc(100% - 130px);
      > div {
        margin-top: 8px;
      }
    }
  }
}
.headTitle {
  display: flex;
  align-items: center;
  > span:nth-child(1) {
    @include bCircle;
  }
  > span:nth-child(2) {
    font-weight: 700;
    // width: 90%;
    flex: 1;
  }
}
.tagBox {
  width: 66.6666%;
  border-right: 1px solid #dcdfe6;
  padding-left: 8px;
  padding-top: 8px;
}
.box-card {
  margin-top: 10px;
}
.ulTagBox {
  padding: 6px 0;
  border-top: 1px solid #dcdfe6;
  li {
    padding: 2px 4px;
    cursor: pointer;
    border-radius: 5px;
    margin: 2px;
    margin-right: 10px;
    margin-left: 0;
  }
  li:hover {
    background: #f5f7fa;
  }
  li.active {
    background: #ecf5ff;
  }
}
.pl30 {
  padding-left: 30px;
}
.bt {
  border-top: 1px solid #dcdfe6;
}
.bpt {
  padding-left: 0;
  padding-right: 10px;
}
.omit {
  display: inline-block;
}
.cFF9900 {
  background: #ff9900;
}
.c5B9CFF {
  background: #5b9cff;
}
.c00cc00 {
  background: #00cc00;
}
.mr10{
  margin-right:10px;
}

.cursor{
  cursor: pointer;
}
</style>