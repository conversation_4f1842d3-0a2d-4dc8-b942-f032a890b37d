<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600">
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" style="padding-right: 20px;">

                <el-form-item label="所属部门" prop="DepartmentId">
                    <treeselect :normalizer="normalizer" class="treeselect-common" :disabled="!editable" :options="DepartmentList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.DepartmentId" placeholder="请选择所属部门" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"></treeselect>
                </el-form-item>

                <el-form-item label="新人封面" prop="CoverPath">
                    <app-upload-file :max='1' :fileSize='1024 * 1024 * 10' :value='CoverFileList' :readonly="!editable" @change='handleCoverUpChange' :preview='true'></app-upload-file>
                </el-form-item>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="姓名" prop="Name">
                            <el-input maxlength="10" type="text" placeholder="请输入姓名" :disabled="!editable" v-model="formData.Name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="性别" prop="Sex">
                            <el-radio v-model="formData.Sex" :disabled="!editable" :label="1">男</el-radio>
                            <el-radio v-model="formData.Sex" :disabled="!editable" :label="2">女</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="年龄" prop="Age">
                            <el-input maxlength="2" type="text" placeholder="请输入年龄" :disabled="!editable" v-model="formData.Age">
                                <template slot="append">岁</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="工号" prop="Number">
                            <el-input maxlength="6" type="text" placeholder="请输入工号" :disabled="!editable" v-model="formData.Number"></el-input>
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="职位" prop="Job">
                            <el-input maxlength="20" type="text" placeholder="请输入职位" :disabled="!editable" v-model="formData.Job"></el-input>
                        </el-form-item>

                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="星座" prop="Constellation">
                            <el-select style="width: 100%;" v-model="formData.Constellation" :disabled="!editable" placeholder="请选择星座">
                                <el-option v-for="item in ConstellationOptions" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>

                    <el-col :span="12">
                        <el-form-item label="入职时间" prop="EntryTime">
                            <el-date-picker :disabled="!editable" format="yyyy-MM-dd" style="width: 100%;" value-format="yyyy-MM-dd" v-model="formData.EntryTime" type="date" placeholder="请选择入职时间"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="自我介绍" prop="SelfIntroduction">
                    <el-input maxlength="2000" type="textarea" placeholder="请输入自我介绍" :rows="5" :disabled="!editable" v-model="formData.SelfIntroduction"></el-input>
                </el-form-item>

                <el-form-item label="兴趣爱好" prop="HobbiesAndInterests">
                    <el-input maxlength="1000" type="textarea" placeholder="请输入兴趣爱好" :rows="5" :disabled="!editable" v-model="formData.HobbiesAndInterests"></el-input>
                </el-form-item>

                <el-form-item label="新人生活照" prop="LifePhotos">
                    <div style="float: right;width: 100%;border: 1px solid #fcfcfc;text-align: right;">({{LifePhotosFileList.length}} / 5)</div>
                    <app-upload-file :max='5' :limit="5" :fileSize='1024 * 1024 * 10' :multiple="true" :value='LifePhotosFileList' :readonly="!editable" @change='handleLifePhotosUpChange' :preview='true'></app-upload-file>
                </el-form-item>

            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="handleSave" v-show="editable" :buttonType='1' type="primary"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as systemNewReport from '@/api/personnelManagement/systemNewReport'
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import {
    listToTreeSelect
} from "@/utils";
import {
    regs
} from "@/utils/regs";
import Treeselect from "@riophae/vue-treeselect";
export default {
    name: "newReport-create",
    directives: {},
    components: {},
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (!val) {
                this.CoverFileList = [];
                this.LifePhotosFileList = [];
                this.DepartmentList = [];
            }
            if (val) {
                this.resetFormData();
                this.getSystemDepartmentList();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail"
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建新人报道";
            } else if (this.dialogStatus == "update") {
                return "编辑新人报道";
            } else if (this.dialogStatus == 'detail') {
                return '详情'
            }
            return ''
        },
    },

    created() {
        // this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            ConstellationOptions: [{
                    value: 0,
                    label: '白羊座'
                },
                {
                    value: 1,
                    label: '金牛座'
                },
                {
                    value: 2,
                    label: '双子座'
                },
                {
                    value: 3,
                    label: '巨蟹座'
                },
                {
                    value: 4,
                    label: '狮子座'
                },
                {
                    value: 5,
                    label: '处女座'
                },
                {
                    value: 6,
                    label: '天秤座'
                },
                {
                    value: 7,
                    label: '天蝎座'
                },
                {
                    value: 8,
                    label: '射手座'
                },
                {
                    value: 9,
                    label: '摩羯座'
                },
                {
                    value: 10,
                    label: '水瓶座'
                },
                {
                    value: 11,
                    label: '双鱼座'
                }
            ],

            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.label.split(",")[0],
                    id: node.Id,
                    children: node.children
                };
            },

            CoverFileList: [],
            LifePhotosFileList: [],
            DepartmentList: [],

            formLoading: false,
            rules: {
                DepartmentId: {
                    required: true,
                    message: '请选择所属部门',
                    trigger: 'change'
                },
                CoverPath: {
                    required: true,
                    message: '请选择新人封面',
                    trigger: 'change'
                },
                Name: {
                    required: true,
                    message: '请输入姓名',
                    trigger: 'blur'
                },
                Job: {
                    required: true,
                    message: '请输入职位',
                    trigger: 'blur'
                },
                Sex: {
                    required: true,
                    message: '请选择性别',
                    trigger: 'change'
                },
                Age: [{
                    required: true,
                    trigger: "blur",
                    validator: (rule, value, callback) => {
                        if (value != "" && value != null) {
                            if ((regs.PositiveIntegers).test(value) == false) {
                                callback(new Error("年龄只能输入数字"));
                            } else {
                                callback();
                            }
                        } else {
                            callback(new Error("请输入年龄"));
                        }
                    }
                }],
                Constellation: {
                    required: true,
                    message: '请选择星座',
                    trigger: 'change'
                },
                EntryTime: {
                    required: true,
                    message: '请选择入职时间',
                    trigger: 'change'
                },
                SelfIntroduction: {
                    required: true,
                    message: '请输入自我介绍',
                    trigger: 'blur'
                },
                HobbiesAndInterests: {
                    required: true,
                    message: '请输入兴趣爱好',
                    trigger: 'blur'
                },
                Number: [{
                    required: false,
                    trigger: "blur",
                    validator: (rule, value, callback) => {
                        if (value != "" && value != null) {
                            if ((regs.PositiveIntegers).test(value) == false) {
                                callback(new Error("工号只能输入数字"));
                            } else {
                                callback();
                            }
                        } else {
                            callback(new Error("请输入工号"));
                        }
                    }
                }],
            },
            labelWidth: "100px",
            formData: {
                NewReportId: "",
                DepartmentId: null,
                Cover: "",
                CoverPath: "",
                Name: "",
                Job: "",
                Sex: "",
                Age: "",
                Constellation: "",
                Number: "",
                EntryTime: "",
                SelfIntroduction: "",
                HobbiesAndInterests: "",
                LifePhotos: "",
                LifePhotoPaths: "",
            }
        };
    },
    methods: {

        resetFormData() {
            let temp = {
                NewReportId: "",
                DepartmentId: null,
                Cover: "",
                CoverPath: "",
                Name: "",
                Job: "",
                Sex: "",
                Age: "",
                Constellation: "",
                Number: "",
                EntryTime: "",
                SelfIntroduction: "",
                HobbiesAndInterests: "",
                LifePhotos: "",
                LifePhotoPaths: "",
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        //获取部门信息下拉框
        getSystemDepartmentList() {
            systemDepartment.getListByCondition({}).then(res => {
                var departments = res.map(function (item, index, input) {
                    return {
                        Id: item.Id,
                        label: item.DepartmentName,
                        ParentId: item.ParentId
                    };
                });
                var departmentList = listToTreeSelect(departments);
                this.DepartmentList = departmentList;
            });
        },

        getDetail() {
            this.formLoading = true;
            systemNewReport.detail({
                id: this.id
            }).then(res => {
                this.formData = Object.assign({}, this.formData, res);

                this.CoverFileList = [];
                if (this.formData.CoverPath) {
                    this.CoverFileList = [{
                        Id: this.formData.Cover,
                        Path: this.formData.CoverPath
                    }];
                }

                this.LifePhotosFileList = [];
                if (this.formData.LifePhotoPaths) {
                    var lifePhotos = this.formData.LifePhotos.split(',')
                    var lifePhotoPaths = this.formData.LifePhotoPaths.split(',')

                    lifePhotos.forEach((element, index) => {
                        var photoPath = lifePhotoPaths[index];
                        this.LifePhotosFileList.push({
                            Id: element,
                            Path: photoPath
                        });
                    });
                }

                this.formLoading = false;
            });
        },

        //保存
        createData() {
            let validate = this.$refs.formData.validate();

            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    //提交数据保存
                    let result = null;
                    if (this.dialogStatus == "create") {
                        delete postData.NewReportId;
                        result = systemNewReport.add(postData);
                    } else if (this.dialogStatus == "update") {
                        result = systemNewReport.edit(postData);
                    }
                    result.then(res => {
                        if (this.isContinue) {
                            this.resetFormData();
                            this.$emit("reload");
                        } else {
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData();
                        }
                    });
                }
            });
        },

        handleCoverUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.CoverPath = imgs[0].Path
                this.formData.Cover = imgs[0].Id
            } else {
                this.formData.Cover = ''
                this.formData.CoverPath = ''
            }
        },

        handleLifePhotosUpChange(imgs) {
            if (imgs && imgs.length > 0) {
                this.formData.LifePhotoPaths = imgs.map(s => s.Path).toString()
                this.formData.LifePhotos = imgs.map(s => s.Id).toString()
            } else {
                this.formData.LifePhotos = ''
                this.formData.LifePhotoPaths = ''
            }
        },

        handleSave() {
            this.createData();
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;

    .left {
        flex: 1;
        padding-right: 14px;
    }

    .right {
        width: 40%;
    }
}

.panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
}
</style>
