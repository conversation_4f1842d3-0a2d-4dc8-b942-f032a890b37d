<template>
<div class="app-container">
    <div class="bg-white">

        <!-- <page-title title="关于我们" :subTitle='["企业介绍的描述编辑页面"]'></page-title> -->
        <!-- <el-button type="primary" size="mini" @click="handleUpdate" style="margin:8px 0 0 10px">编辑内容</el-button> -->
        <permission-btn style="margin:8px 0 0 10px" moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
        <hr style="background-color: #e1e1e1;height: 1px;border: none;" />
        <div class="news-container ql-snow" style="width: 70%;margin-left: 15%;">
            <div class="divUeditor ql-editor" v-viewer style="padding:0 20px" v-html="tableDataa"></div>
        </div>
    </div>
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :id="id" :dialogStatus="dialogStatus" @reload="getList"></create-page>
</div>
</template>

<script>
import * as companyIntroduction from '@/api/informationCenter/companyIntroduction'
import elDragDialog from '@/directive/el-dragDialog'

import indexPageMixin from '@/mixins/indexPage'

import createPage from "./create"
export default {
    name: 'companyIntroduction',

    mixins: [indexPageMixin],
    components: {
        createPage
    },
    directives: {
        elDragDialog
    },
    data() {

        return {
            tableDataa: "",
            id: "",
            dialogStatus: "update",
            dialogFormVisible: false,
            listQuery: {
                PageIndex: 1,
                PageSize: 20
            },
        };
    },
    created() {
        this.getList()

    },

    methods: {
        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnEdit":
                    this.handleUpdate()
                    break;
                default:
                    break;
            }
        },

        // 弹出编辑框
        handleUpdate() {
            this.dialogFormVisible = true;
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess() {
            this.getList();
            this.closeDialog();
        },
        getList() {

            this.listLoading = true

            //  let postDatas = JSON.parse(JSON.stringify(this.listQuery));
            //  companyIntroduction.getListPage(postDatas).then(response => {
            companyIntroduction.getHonorsOrAboutUS().then(response => {
                this.tableDataa = response.AboutAs
                this.id = response.Id
                this.listLoading = false

            })
        },
    }
}
</script>

<style scoped>
.app-container {
    position: absolute;
    left: 10px;
    top: 10px;
    background: #e9eaef;
    width: calc(100% - 20px);
    min-height: calc(100% - 20px);
    padding: 0;
    margin-bottom: 10px;
    /* width: 70%; */
    /* margin-left: 15%; */
}

.news-wrapper {
    padding: 60px 40px 0;
    /* overflow: hidden; */
    background: #ffffff;
    margin: 8px;
    position: absolute;
    min-width: 100%;
    min-height: 100%;
    height: auto;
}

.news-container {
    /*max-width: 1080px;*/
    margin: 0 auto;
    padding-bottom: 20px;
    background: white;
}

img,
video {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-top: 0.4rem;
}

header {
    font-size: 36px;
    color: #373c41;
    font-weight: bold;
}

aside {
    font-size: 16px;
    color: #8d8f91;
    margin-top: 30px;
}

article p {
    font-size: 0.42rem;
    color: #373c41;
    margin-top: 0.4rem;
    line-height: 0.67rem;
    text-indent: 2em;
}

.hr {
    width: 100%;
    margin: 30px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hr span {
    background-color: #e3e3e5;
}

.hrl,
.hrr {
    width: calc(50% - 5px);
    height: 1px;
}

.hrc {
    width: 3px;
    height: 3px;
    border-radius: 3px;
}

@media screen and (min-width: 1080px) {
    body {
        width: 1080px;
        margin: 0 auto;
    }
}

.divUeditor {
    overflow: hidden;
    display: block;
    width: 100%;
    min-width: 90%;
    position: relative;
    word-wrap: break-word;
    padding: 0 20px 20px 20px;
}

.divUeditor img {
    border: 0;
    max-width: 100%;
    margin-top: 10px;
}

.down-wrapper a {
    color: #409eff;
    line-height: 140%;
}

.down-wrapper p {
    margin: 0;
    margin-bottom: 10px;
}
</style>
