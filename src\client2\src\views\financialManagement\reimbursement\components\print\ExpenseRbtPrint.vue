<!-- 费用报销打印组件 -->
<template>
  <div style="display: none">
    <div ref="printContent" class="print_container">
      <div class="print_title">费用报销单</div>
      <div class="print_top">
        <span>
          公司名称：
          <span class="f_bold">{{ getObjData(formData, "KingdeeDepartmentName") }}</span>
        </span>
        <span>填报日期：{{ getObjData(formData, "FBillDate") | dateFilter("YYYY/MM/DD") }}</span>
        <span>单据编号：{{ getObjData(formData, "FBillNo") }}</span>
      </div>
      <table class="print_table">
        <tr>
          <td style="width: 10%">报销人</td>
          <td style="width: 40%" class="f_bold">{{ getSubmitEmployeeName }}</td>
          <td style="width: 15%">单据及附件数</td>
          <td style="width: 35%">{{ fileTotal }}</td>
        </tr>
        <tr>
          <td>研发项目</td>
          <td colspan="3">
            <div class="list_item" v-for="(item, index) in projectNameList" :key="index">
              {{ item }}
            </div>
          </td>
        </tr>
        <tr>
          <td>部门</td>
          <td>{{ getObjData(formData, "DepartmentName") }}</td>
          <td>领款人</td>
          <td class="f_bold">{{ getPayeeEmployeeName }}</td>
        </tr>
        <!-- 明细表格 -->
        <tr>
          <td>序号</td>
          <td>用途</td>
          <td colspan="2">金额（元）</td>
        </tr>
        <tr
          v-for="(item, index) in getObjData(formData, 'FinanceExpensesDetailList', [])"
          :key="index"
        >
          <td>{{ index + 1 }}</td>
          <td>{{ item.Purpose }}</td>
          <td colspan="2" class="t_right">{{ formatThousands(item.Amount) }}</td>
        </tr>
        <!-- 空行，如果明细少于5行则显示空行补齐 -->
        <tr
          v-for="i in Math.max(
            0,
            5 - (getObjData(formData, 'FinanceExpensesDetailList', []).length || 0)
          )"
          :key="`empty-${i}`"
        >
          <td>{{ getObjData(formData, "FinanceExpensesDetailList", []).length + i }}</td>
          <td></td>
          <td colspan="2"></td>
        </tr>
        <!-- 合计行 -->
        <tr>
          <td colspan="2">合计</td>
          <td colspan="2" class="t_right f_bold">
            {{ formatThousands(getObjData(formData, "Total")) }}
          </td>
        </tr>
        <tr>
          <td colspan="2" class="t_left">
            金额(大写)：{{ convertToChinese(getObjData(formData, "Total")) }}
          </td>
          <td colspan="2">
            <div class="f_j_b">
              <span>原借款：</span>
              <span>{{ formatThousands(getObjData(formData, "OriginalLoan")) }}</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="footer_row" colspan="4">
            <span v-for="(item, index) in flowList" :key="index">
              {{ item.role }}【{{ item.name }}】{{ index === flowList.length - 1 ? "" : "→" }}
            </span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import printMixins from "./printMixins.js";
export default {
  name: "ExpenseRbtPrint",
  data() {
    return {
      // 必须得有  混入方法使用动态包裹对象key
      parcelKey: "FinanceExpenseObj",
    };
  },
  mixins: [printMixins],
  computed: {
    // 票据及附件数
    fileTotal() {
      const list = this.getObjData(this.formData, "FinanceExpensesDetailList", []);
      const invoiceTotal = list.reduce((total, item) => {
        return total + item.InvoiceList.length;
      }, 0);
      const attachmentTotal = list.reduce((total, item) => {
        return total + item.AttachmentList.length;
      }, 0);
      return invoiceTotal + attachmentTotal;
    },
  },
};
</script>

<style lang="scss">
@import "./printStyle.scss";
</style>
