<template>
    <div class="app-container">
        <div class="bg-white __dynamicTabContentWrapper">
            <!-- <page-title title="配置管理" :subTitle="['系统基础配置管理页面']"></page-title> -->
            <div class="__dynamicTabWrapper">
                <app-table ref="mainTable" :multable='false' :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns='tabAuthColumns' :isShowAllColumn='true' :loading="listLoading"
                    @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn='rowBtns.length > 0'
                    :startOfTable='startOfTable' :isShowBtnsArea='false' :isShowConditionArea='false'>

                    <!-- 表格批量操作区域 -->
                    <!-- <template slot="btnsArea">
                        <permission-btn moduleName="emp" v-on:btn-event="onBtnClicked"></permission-btn>
                    </template> -->

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleUpdate(scope.row)"
                            :type='1'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDetail')"
                            @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
                        <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)"
                            :type='3'></app-table-row-button>
                    </template>
                </app-table>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
        <div v-if="dialogVisible">
            <edit-page :dialogStatus='dialogStatus' :dialogFormVisible='dialogVisible' :id='id'
                @closeDialog='closeDialog' @saveSuccess='handleSaveSuccess' @reloading='handleReloading'>
            </edit-page>
        </div>
    </div>
</template>

<script>
    import * as ec from '@/api/enterpriseConfigure'
    import elDragDialog from '@/directive/el-dragDialog'
    import indexPageMixin from '@/mixins/indexPage'
    import EditPage from './edit'
    export default {
        name: 'config-mgmt',
        components: {
            EditPage,
        },
        directives: {
            // waves,
            elDragDialog
        },
        mixins: [indexPageMixin],
        watch: {

        },
        data() {
            return {
                //isCreateAccount: true, //是否同步创建用户
                multipleSelection: [],
                tableSearchItems: [
                    // { prop: 'OrgId', label: '所在组织' },
                    // { prop: 'Name', label: '姓名' },
                    // { prop: 'Phone', label: '手机' },
                    // { prop: 'Number', label: '工号' },
                    // { prop: 'JobTitle', label: '职称' },
                    // { prop: 'EducationId', label: '学历' },
                ],
                tabColumns: [
                    {
                        attr: { prop: 'Description', label: '配置文件' },
                    },
                    {
                        attr: { prop: 'KeyValue', label: '内容' },
                    }
                ],
                tabDatas: [],
                listLoading: false,
                total: 0,
                textMap: {
                    update: '编辑',
                    create: '添加'
                },
                dialogVisible: false,
                dialogStatus: '',
                id: '',

            }
        },
        created() {

            this.getList()
        },
        mounted() {
        },
        methods: {
            closeDialog() {
                this.dialogVisible = false
            },
            handleReloading() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSaveSuccess(_formData) {
                this.listQuery.PageIndex = 1
                this.getList()
                this.dialogVisible = false
            },
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            onBtnClicked: function (domId) {
                // console.log('you click:' + domId)
                switch (domId) {
                    case 'btnAdd':
                        this.handleCreate()
                        break
                    case 'btnEdit':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行编辑',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0])
                        break
                    case 'btnDetail':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行查看',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0], 'detail')
                        break;
                    case 'btnDel':
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: '至少删除一个',
                                type: 'error'
                            })
                            return
                        }
                        this.handleDelete(this.multipleSelection)
                        break
                    default:
                        break
                }
            },
            getList() {
                this.listLoading = true
                ec.getList(this.listQuery).then(response => {
                    this.tabDatas = response.Items
                    this.total = response.Total
                    this.listLoading = false
                })
            },
            handleFilter() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
            //选择部门
            handleChange(value) {

            },
            handleUpdate(row, optType = 'update') { // 弹出编辑框
                this.id = row.KeyTag
                this.dialogStatus = optType
                this.dialogVisible = true
                // ec.detail({ key: id }).then(response => {
                //     this.dialogStatus = optType
                //     this.$nextTick(() => {
                //         this.$refs['dataForm'].clearValidate()
                //     })
                // })
            },
            handleDelete(rows) { // 多行删除
                let ids = []
                if (_.isArray(rows)) {
                    ids = rows.map(u => u.EmployeeId)
                } else {
                    ids.push(rows.EmployeeId)
                }
                this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // ec.del(ids).then(() => {
                    //     this.$notify({
                    //         title: '成功',
                    //         message: '删除成功',
                    //         type: 'success',
                    //         duration: 2000
                    //     })
                    //     this.getList()
                    // })
                })

            },
        }
    }
</script>

<style scoped>
    .sel-ipt,
    .dat-ipt {
        width: 100%;
    }

    .avatar {
        width: 68px;
        height: 68px;
    }

    .tip-avatar {
        width: 140px;
        height: 140px;
    }

    .avatar,
    .tip-avatar {
        border-radius: 50%;
    }

    /* .cus_wdt{
    width: 200px;
} */
</style>
