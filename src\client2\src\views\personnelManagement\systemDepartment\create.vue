<template>
  <div class="createEmployee">
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="700">
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" v-loading="formLoading" label-position="right" :label-width="labelWidth">
          <el-row>
            <el-col :span="24">
              <el-form-item label="头像" prop="AvatarPath">
                <!-- <app-upload-file :max="1" :fileSize="1024 * 1024 * 2" :value="fileList" @change="handleUpChange" :preview="true" :disabled="!editable"></app-upload-file> -->
                <AppImgCutter :val='avatorObj' @change="handleChagne" :disabled="!editable"></AppImgCutter>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="14">
              <el-form-item label="姓名" prop="Name">
                <el-input maxlength="50" :disabled="!editable" placeholder="请输入姓名" type="text" v-model="formData.Name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="性别" prop="Sex">
                <el-radio v-model="formData.Sex" :disabled="!editable" :label="1">男</el-radio>
                <el-radio v-model="formData.Sex" :disabled="!editable" :label="2">女</el-radio>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="14">
              <el-form-item label="手机号" prop="Mobile">
                <el-input maxlength="20" type="text" :disabled="!editable" placeholder="请输入手机号" v-model="formData.Mobile"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="工号" prop="Number">
                <el-input maxlength="10" type="text" :disabled="!editable" placeholder="格式：000123" v-model="formData.Number"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="14">
              <el-form-item label="主要部门" prop="DepartmentId" :title="formData.DepartmentName">
                <treeselect :normalizer="normalizer" class="treeselect-common" :disabled="!editable" :options="DepartmentList" :default-expand-level="3" :multiple="false" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.DepartmentId" placeholder="请选择主要部门" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" @open='handleOpen' :append-to-body="true" zIndex='9999'>
                  <div slot="value-label" slot-scope="{ node }">{{ node.raw.ParentName }}</div>
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="座机" prop="SpecialPlane">
                <el-input maxlength="20" type="text" :disabled="!editable" placeholder="格式：0755-83280109" v-model="formData.SpecialPlane"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="14">
              <el-form-item label="其它部门" prop="DepartmentMinorId">
                <treeselect :normalizer="normalizer" class="treeselect-common" :flat='true' :disabled="!editable" :options="DepartmentList" :default-expand-level="3" :multiple="true" :open-on-click="true" :open-on-focus="true" :clear-on-select="true" v-model="formData.DepartmentMinorId" placeholder="请选择其它部门" :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree" :append-to-body="true" zIndex='9999'>
                  <div slot="value-label" slot-scope="{ node }" :title="node.raw.ParentName">{{ node.raw.DepartmentName }}</div>
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="微信" prop="WeChat">
                <el-input maxlength="20" type="text" :disabled="!editable" placeholder="格式：jiayuntong2020" v-model="formData.WeChat"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="14">
              <el-form-item label="职位" prop="JobId">
                <div class="selector-wrapper">
                  <job-selector v-if="editable" key="btnAddSelector" :showType="2" :multiple="false" :list="formData.JobId ? [{JobId: formData.JobId, Name: formData.JobName}] : []" @change="handleJobChange" :disabledList="[]">
                    <el-button slot="reference" type="text" size="mini" class="filter-item">选择职位</el-button>
                  </job-selector>
                  <div class="cl">
                      <span class="omit fl" :title="formData.JobName">{{formData.JobName}}</span>
                      <i v-show="formData.JobName && editable" class="el-icon-close btn-close" @click="handleJobChange()"></i>
                      <!-- <el-button icon="el-icon-close" circle></el-button> -->
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="邮箱" prop="Email">
                <el-input maxlength="100" type="text" :disabled="!editable" placeholder="格式：<EMAIL>" v-model="formData.Email"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="在职状态" prop="WorkingState">
                <el-radio v-model="formData.WorkingState" v-for="(item, idx) in employeeWorkingStateEnum" :key="idx" :disabled="!editable" :label="item.value">{{ item.label }}</el-radio>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="账号状态" prop="Status">
                <el-radio v-model="formData.Status" :disabled="!editable" :label="1">启用</el-radio>
                <el-radio v-model="formData.Status" :disabled="!editable" :label="2">停用</el-radio>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="this.dialogStatus == 'update'">
            <el-col :span="24">
              <el-form-item label="密码管理">
                <el-button type="text" @click="handleResetPassword(formData.EmployeesId)">重置密码</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <app-button @click="handleSave" v-show="editable" text="保存"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as systemDepartment from "@/api/personnelManagement/systemDepartment";
import * as systemJob from "@/api/personnelManagement/systemJob";
import * as systemEmployee from "@/api/personnelManagement/systemEmployee";
import { regs } from "@/utils/regs";
import { employeeWorkingStateEnum } from "../enum";
import jobSelector from "../../personnelManagement/systemJob/jobSelector"
import * as EntryManagementApi from "@/api/personnelManagement/EntryManagement";

export default {
  name: "system-department-create",
  directives: {},
  components: {
    Treeselect,
    jobSelector,
  },
  mixins: [],
  props: {
    dialogStatus: {
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    selectTypeId: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      this.DepartmentList = [];
      // this.fileList = [];
      this.avatorObj = null
      if (val) {
        this.resetFormData();
        this.getSystemDepartmentList();
        if (this.dialogStatus != "create" && this.id) {
          this.getDetail();
        }
      }
    }
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      return this.dialogStatus != "detail";
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加成员";
      } else if (this.dialogStatus == "update") {
        return "编辑成员";
      } else if (this.dialogStatus == "induction") {
        return "办理入职";
      } else if (this.dialogStatus == "detail") {
        return "查看详情";
      }
    }
  },

  created() {
    this.rules = this.initRules(this.rules);
  },
  mounted() { },
  data() {
    return {
      employeeWorkingStateEnum,
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.DepartmentName,
          id: node.Id,
          children: node.children
        };
      },

      DepartmentList: [],
      // fileList: [], //图像信息[{Id: '', Path: ''}]
      avatorObj: null, //{id: '', path: ''}

      formLoading: false,
      rules: {
        AvatarPath: {
          fieldName: "头像",
          rules: [{ required: true }]
        },
        Name: {
          fieldName: "姓名",
          rules: [{ required: true }]
        },
        Sex: {
          fieldName: "性别",
          rules: [{ required: true }]
        },
        Number: {
          fieldName: "工号",
          rules: [{ required: true }, { reg: regs.PositiveIntegers }]
        },
        Mobile: {
          fieldName: "手机号",
          rules: [{ required: true }, { reg: regs.phone }]
        },
        DepartmentId: {
          fieldName: "主要部门",
          rules: [{ required: true }]
        },
        Email: {
          fieldName: "邮箱",
          rules: [{ required: false }, { reg: regs.email }]
        },
        SpecialPlane: {
          fieldName: "座机",
          rules: [{ required: false }, { reg: regs.Tel }]
        },
        WeChat: {
          fieldName: "微信",
          rules: [{ required: false }, { reg: regs.LettersAndNumbers }]
        }
      },
      labelWidth: "100px",
      formData: {
        EmployeesId: "",
        Name: "",
        Sex: 1,
        Number: "",
        Mobile: "",
        DepartmentId: null,
        DepartmentMinorId: [],
        JobId: "",
        JobName: "",
        Email: "",
        SpecialPlane: "",
        WeChat: "",
        Status: 1,
        WorkingState: 1,
        Avatar: "",
        AvatarPath: ""
      }
    };
  },
  methods: {

    handleJobChange(list) {
      if(list && list.length > 0) {
        this.formData.JobName = list[0].Name
        this.formData.JobId = list[0].JobId
      }else{
        this.formData.JobName = ''
        this.formData.JobId = ''
      }
    },
    handleUpChange(imgs) {
      if (imgs && imgs.length > 0) {
        this.formData.AvatarPath = imgs[0].Path;
        this.formData.Avatar = imgs[0].Id;
      } else {
        this.formData.Avatar = "";
        this.formData.AvatarPath = "";
      }
    },
    handleChagne(obj) {
      if(obj) {
        this.formData.AvatarPath = obj.path;
        this.formData.Avatar = obj.id;
      }else{
        this.formData.AvatarPath = '';
        this.formData.Avatar = '';
      }
    },
    //重置密码
    handleResetPassword(employeesId) {
      console.log(employeesId);
      this.$confirm("是否确认重置密码?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        systemEmployee.resetPassword({ id: employeesId }).then(() => {
          this.$notify({
            title: "成功",
            message: "重置成功",
            type: "success",
            duration: 2000
          });
          this.getList();
        });
      });
    },

    //获取部门信息下拉框
    getSystemDepartmentList() {
      systemDepartment.getListByCondition({}).then(res => {
        var departments = res.map(function (item, index, input) {
          return {
            Id: item.Id,
            label: item.DepartmentName,
            ParentId: item.ParentId
          };
        });

        debugger
        debugger

        var departmentList = listToTreeSelect(res, undefined, undefined, undefined, 'DepartmentName');
        this.DepartmentList = departmentList;
        if (this.dialogStatus == "create") {
          this.formData.DepartmentId = this.selectTypeId;
        }
      });
    },


    resetFormData() {
      let temp = {
        Avatar: "",
        AvatarPath: "",
        EmployeesId: "",
        Name: "",
        Sex: 1,
        Number: "",
        Mobile: "",
        DepartmentId: null,
        DepartmentMinorId: [],
        JobId: "",
        JobName: "",
        Email: "",
        SpecialPlane: "",
        WeChat: "",
        Status: 1,
        WorkingState: 1
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    getDetail() {
      this.formLoading = true;
        let result = null;
        if (this.dialogStatus == 'induction') {
            result = EntryManagementApi.BeginEntry({ id: this.id })
        }else{
            result = systemEmployee.detail({ id: this.id })
        }
      result.then(res => {
        var formObj = Object.assign({}, this.formData, res);
        // this.fileList = [];
        this.avatorObj = null
        if(this.dialogStatus!='induction') {
            if (formObj.AvatarPath) {
            this.avatorObj = {
                id: formObj.Avatar,
                path: formObj.AvatarPath
            }
            // this.fileList = [
            //   { Id: formObj.Avatar, Path: formObj.AvatarPath }
            // ];
            } else {
            //设置默认的头像
            var imgUrl = require('../../../assets/images/avatar3.png');
            formObj.AvatarPath = imgUrl;
            this.avatorObj = {
                id: 'avatar3.png',
                path: imgUrl
            }
            // this.fileList = [
            //   { Id: 'avatar3.png', Path: imgUrl }
            // ];
            }
        }
        this.formData = formObj;
        this.formLoading = false;
      });
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    //保存
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formData));

          //提交数据保存
          let result = null;
          if (this.dialogStatus == "create"||this.dialogStatus == "induction") {
            delete postData.EmployeesId;
            result = systemEmployee.add(postData);
          } else if (this.dialogStatus == "update") {
            result = systemEmployee.edit2(postData);
          }

          result.then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            if((this.dialogStatus == 'create'||this.dialogStatus == "induction") && res.EmployeesId) {
              this.$refs.appDialogRef.createData(this.dialogStatus, res.EmployeesId, postData.Id);
            }else{
              this.$refs.appDialogRef.createData();
            }
          });
        }
      });
    },
    handleSave() {
      this.createData();
    },
    handleOpen() {
      this.$refs.jobIdRef.blur()
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  .left {
    flex: 1;
    // border-right: 1px solid red;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}
.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}

.selector-wrapper{
  display: flex;
  .cl{
    flex: 1;
    padding: 0 6px;
    .btn-close{
      margin-left: 6px;
      cursor: pointer;
      &:hover{
        cursor: pointer;
        color: red;
      }
    }
  }
}
</style>
