<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="900">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" v-loading='loading'
                label-position="right" label-width="110px" class="wrapperMain">
                    <el-row class="wrapperBox">
                        <el-form-item label="组织资源名称" prop="Name">
                            <el-input v-if="editable" :disabled="!editable" maxlength="100" type="text" v-model.trim="formData.Name"></el-input>
                            <div v-else>{{formData.Name}}</div>
                        </el-form-item>
                        <el-form-item label="组织资源说明" prop="Explain">
                            <el-input v-if="editable" :disabled="!editable" maxlength="1000" type="textarea" :rows="5" v-model.trim="formData.Explain"></el-input>
                            <div v-else>{{formData.Explain}}</div>
                        </el-form-item>
                        <el-form-item label="资源分类" prop="ClassifyId">
                            <treeselect :normalizer="normalizer" :disabled="!editable"
                                class="treeselect-common"
                                :options="treeData" :default-expand-level="3" @select="onTreeSelect"
                                :multiple="false" :open-on-click="true" :open-on-focus="true"
                                :clear-on-select="false" :value="formData.ClassifyId" placeholder="请选择资源分类"
                                :noResultsText="noResultsTextOfSelTree" :noOptionsText="noOptionsTextOfSelTree"
                                :append-to-body="true" zIndex='9999'>
                            </treeselect>
                        </el-form-item>
                        <el-form-item label="可见范围" prop="ViewRange" v-if="pageType==1">
                            <div class="cl">
                                <el-select :disabled="!editable" class="fl" @change="() => {formData.ViewRangeValues = '';formData.ViewRangeStrList=[]}" style="margin-right:10px;" v-model="formData.ViewRange">
                                    <el-option v-for="item in ViewRangeEnum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <div class="fl" v-if="formData.ViewRange == 2">
                                    <el-button :disabled="!editable" type="text" @click="handleShowTree">选择部门</el-button>
                                </div>
                                <emp-selector
                                    class="fl"
                                    style="width:300px;"
                                    v-if="formData.ViewRange == 4"
                                    :readonly="!editable"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :beforeConfirm='handleBeforeConfirm'
                                    :list="formData.ViewRangeStrList"
                                    @change="handleViewRange"
                                ></emp-selector>
                            </div>
                            <ul v-if="formData.ViewRange == 2 && formData.ViewRangeStrList&&formData.ViewRangeStrList.length>0" class="dUl">
                                <li class="omit" :title="dn.DepartmentName" v-for="(dn,dnI) in formData.ViewRangeStrList" :key="dnI">{{dn.DepartmentName}}</li>
                            </ul>
                        </el-form-item>
                        <el-form-item label="相关附件" prop="AttachmentList">
                            <template v-if="editable">
                                <app-uploader accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList"
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            </template>
                            <template v-else>
                                <app-uploader v-if="formData.AttachmentList.length>0" accept="all" :fileType="4" :max="10000" :value="formData.AttachmentList" readonly
                                :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                                <template v-else>无</template>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 关闭 -->
                <app-button v-if="dialogStatus == 'detail'" @click="handleClose" text="关闭" type></app-button>
                <!-- 取消 -->
                <app-button v-else @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>

        <!-- 选择部门弹窗 -->
        <v-tree
            v-if="dialogTreeVisible"
            @saveSuccess="handleTreeSaveSuccess"
            @closeDialog="handleTreeCloseDialog"
            :dialogFormVisible="dialogTreeVisible"
            :checkedList='checkedList'>
        </v-tree>

    </div>
</template>

<script>
import * as classifyApi from '@/api/classify'
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";

import productModel from "@/views/knowledge/problemImprove/productModel.vue";

import * as TemplateResourceApi from '@/api/knowledge/TemplateResource'

import empSelector from "@/views/common/empSelector";
import vTree from "@/views/knowledge/train/tree";
import { ViewRangeEnum } from "./enum.js";
export default {
    name: "problem-improve-create",
    directives: {},
    components: {
        Treeselect,
        empSelector,
        vTree,
        productModel
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情模式都不可编辑
            return this.dialogStatus != 'detail'
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return `创建组织资源`;
            }else if(this.dialogStatus == 'update'){
                return `编辑组织资源`;
            }
            return "组织资源详情";
        },
    },
    filters: {
        // SurveyTypeFilter(val) {
        //     let obj = SurveyTypeEnum.find(
        //         s => s.value == val
        //     );
        //     if (obj) {
        //         return obj.label;
        //     }
        //     return "无";
        // }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            type: String,
            required: true,
        },
        id: {
            type: String,
            default: "",
        },
        classifyId: {
            type: String,
            default: "",
        },
        // 页面类型 1 管理端 2 用户端
        pageType: {
            type: Number,
            default: 1,
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();// 查询 基本信息
                    }
                }
            },
            immediate: true
        },
    },
    created() {
        this.rules = this.initRules(this.rules);
        this.getTreeData();
    },
    data() {
        return {
            ViewRangeEnum,

            treeData: [],
            normalizer(node) {
                // treeselect定义字段
                return {
                    label: node.Name,
                    id: node.Id,
                    children: node.children
                };
            },


            /** 选择部门 */
            depKeyName: '',
            depValidName: '',
            checkedList:[],
            dialogTreeVisible:false,


            disabledBtn: false,
            loading: false,
            rules: {
                Name: {fieldName: "组织资源名称",rules: [{ required: true }]},
                ClassifyId: {fieldName: "资源分类",rules: [{ required: true }]},
            },
            // 基本信息
            formData: {
                Id: '',
                Name: '',// 组织资源名称
                Explain: '',// 组织资源说明
                ClassifyId: null, // 资源分类 id

                AttachmentList: [],// 相关附件

                ViewRange: 1,
                ViewRangeValues: '',
                ViewRangeStrList: [],
            },

            // 选择产品
            checkedProductList: [],
            dialogProductModelVisible: false,
        };
    },
    methods: {
        onTreeSelect(node){
            this.formData.ClassifyId = node.Id
            this.$refs.formData.validateField('ClassifyId')
        },
        getTreeData() {
            let postDatas = {
                PageIndex: 1,
                PageSize: 10000,
                BusinessType: 18
            }
            classifyApi.getListPage(postDatas).then(res => {
                var list = (res.Items || []).map((item, index, input) => {
                    return {
                        Id: item.Id,
                        Name: item.Name,
                        ParentId: item.ParentId
                    };
                });
                this.treeData = listToTreeSelect(list, undefined, undefined, undefined, 'Id');
                if(this.dialogStatus=='create'){
                    this.formData.ClassifyId = this.classifyId||null;
                }
            });
        },
        // 关闭选择部门弹窗
        handleTreeCloseDialog(){
            this.dialogTreeVisible=false;
        },
        // 选择部门弹窗 确定
        handleTreeSaveSuccess(d){
            this.formData.ViewRangeStrList=[];
            this.checkedList=[];
            if(d.length>0){
                d.forEach(v => {
                    this.formData.ViewRangeStrList.push({
                        DepartmentId: v.Id,
                        DepartmentName: v.ParentName,
                    });
                })
                this.checkedList = this.formData.ViewRangeStrList.map(s=>s.DepartmentId)
                this.$refs.formData.validateField('ViewRangeStrList');
            }
            this.dialogTreeVisible=false;
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1000) {
            this.$message({
                message: '不得超过1000个',
                type: 'error'
            })
            return false
            }
            return true
        },
        // 显示选择部门弹窗
        handleShowTree(){
            let list = this.formData.ViewRangeStrList
            if(list) {
                this.checkedList = list.map(s => s.DepartmentId) || []
            }
            this.dialogTreeVisible=true;
        },
        // 可见范围 选择人员/选择部门
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.ViewRangeStrList = users;
            } else {
                this.formData.ViewRangeStrList = [];
            }
            this.$refs["formData"].validateField('ViewRangeStrList');
        },
        // 附件 上传 赋值
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files;
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData))
                    postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    delete postData.AttachmentList
                    if(postData.ViewRange==2) {
                        postData.ViewRangeValues = postData.ViewRangeStrList.length>0 ? postData.ViewRangeStrList.map(s => s.DepartmentId).toString() : '';
                    }
                    if(postData.ViewRange==4) {
                        postData.ViewRangeValues = postData.ViewRangeStrList.length>0 ? postData.ViewRangeStrList.map(s => s.EmployeeId).toString() : '';
                    }
                    delete postData.ViewRangeStrList
                    delete postData.ViewRangeDepartmentList
                    delete postData.ViewRangeEmployeeList
                    
                    console.log(postData)
                    let result = null;
                    if (self.dialogStatus == "create") {
                        delete postData.Id;
                        result = TemplateResourceApi.add(postData);
                    } else if (self.dialogStatus == "update") {
                        result = TemplateResourceApi.edit(postData);
                    }

                    self.disabledBtn = true;
                    result.then(res => {
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.disabledBtn = false
                        self.$refs.appDialogRef.createData();
                    }).catch(err => {
                        self.disabledBtn = false
                    })
                }
            })
        },
        // 查询 基本信息
        getDetail() {
            this.loading = true
            TemplateResourceApi.detail({ id: this.id }).then(res => {
                if(res.ViewRange===2){
                    res.ViewRangeStrList = res.ViewRangeDepartmentList
                }
                if(res.ViewRange===4){
                    res.ViewRangeStrList = res.ViewRangeEmployeeList
                }
                this.formData = res;
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style scoped>
.wrapperMain >>> .vue-treeselect__placeholder{
    line-height: 28px;
}
</style>
<style lang='scss' scoped>
.wrapperBox{
    padding-top: 10px;
    padding-right: 20px;
    &_main{
        max-height: 420px;
        overflow-y: auto;
    }
}
.el-card{
    margin-bottom: 15px;
}
.omit{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tips_title{
    float: left;
    max-width: 100%;
}
</style>