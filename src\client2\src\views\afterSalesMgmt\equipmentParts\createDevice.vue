<template>
    <div class="accessories">
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
            v-loading='loading'
        >
            <template slot="body">
                <div class="temBody">
                    <el-form
                        :rules="rules"
                        ref="formData"
                        :model="formData"
                        label-position="right"
                        label-width="110px">
                        <div>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="配件模板名称" prop="Name">
                                        <el-input v-model.trim="formData.Name" maxlength="30" placeholder=""></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </div>
            </template>
            <template slot="footer">
                <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
                    <el-checkbox v-model="goOn">继续添加</el-checkbox>
                </div>
                <el-button @click="handleClose">取消</el-button>
                <app-button @click="handleSuccess" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog> 
    </div>
</template>
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
export default{
    name:'accessories',
    // mixins: [indexPageMixin],
    components: {
        
    },
    props:{
        //开始、结束操作弹框
        dialogStatus: {
            type: String,
            default: 'create'
        },
        id: {
            type: String,
            default: ''
        },
        msgs:{
            type: Object,
            default: null
        }
    },
    data(){
        return{
            disabledBtn:false,
            goOn:false,
            loading:false,
            formData:{
                Name:''
            },
            rules: {
                Name:[{ required: true, message: '配件模板名称不能为空'}],
            },
        }
    },
    filters: {
        
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if(val){
                this.goOn=false;
                if(this.dialogStatus == 'create'){
                    this.formData.Name='';
                }else{
                    this.formData.Name=this.msgs.Name;
                }
            }
      
        },
    },
    computed:{
        title(){
            if(this.dialogStatus == 'create'){
                return "创建设备配件模板"
            }else{
                return "编辑设备配件模板"
            }
        }
    },
    created(){
        
    },
    mounted(){
        
    },
    methods:{
        handleSuccess(){
            this.disabledBtn=true;
            let listResult = this.$refs.formData.validate();
            Promise.all([listResult]).then(valid => {
                let postData={
                    name: this.formData.Name,
                }
                console.log(postData)
                if(this.dialogStatus == "create"){
                    accessories.addtemp(postData).then(res => {
                        this.disabledBtn=false;
                        this.$notify({
                          title: '成功',
                          message: '创建成功！',
                          type: 'success'
                        });
                        if(this.goOn){
                            this.formData={
                                Name: "",
                            }
                            this.$refs['formData'].resetFields();
                        }
                        this.$emit('saveSuccess',this.goOn);
                    }).catch(err => {
                        this.disabledBtn=false;
                    })
                }else{
                    postData.id=this.msgs.Id;
                    console.log(postData)
                    accessories.edittemp(postData).then(res => {
                        this.disabledBtn=false;
                        this.$notify({
                          title: '成功',
                          message: '编辑成功！',
                          type: 'success'
                        });
                        this.$emit('saveSuccess',false);
                    }).catch(err => {
                        this.disabledBtn=false;
                    })
                }
            }).catch(err => {
                this.disabledBtn=false;
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }

}
</script>
<style lang="scss" scoped>
.temBody{
    padding-bottom:10px;
}
</style>