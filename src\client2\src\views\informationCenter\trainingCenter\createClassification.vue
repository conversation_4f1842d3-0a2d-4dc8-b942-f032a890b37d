<template>
  <div>
    <app-dialog title="创建课程类型" ref="addDialog" v-bind="$attrs" v-on="$listeners" :maxHeight='700' :width='600'>
      <template slot="body">
        <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
          <div class="wrapper">
            <el-row>
              <el-col :span="24">
                <el-form-item label="课程类型名称" prop="TrainsClassificationName">
                  <el-input maxlength="30" v-model="formData.TrainsClassificationName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <app-button @click="createData" :buttonType='1'></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>

import * as trainsClassification from '@/api/informationCenter/trainsClassification'

export default {
  name: "trainsClassification-createFolder",
  directives: {
  },
  components: {

  },
  mixins: [
  ],
  watch: {
    '$attrs.dialogFormVisible'(val) {
      if (val) {
        this.resetFormData()
        this.getDetail()
      }
    }
  },
  created() {
    this.rules = this.initRules(this.rules)
  },
  data() {

    return {
      rules: {
        TrainsClassificationName: { fieldName: "分类名称", rules: [{ required: true, max: 100 }] },
      },
      labelWidth: '120px',
      formData: {
        Id: '',
        TrainsClassificationName: '',
      },
    };
  },
  methods: {
    resetFormData() {
      let temp = {
        Id: '',
        TrainsClassificationName: '',
      }
      this.formData = Object.assign({}, this.formData, temp)
    },
    createData() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          //   let postData = JSON.parse(JSON.stringify(this.formData));

          var obj = {
            TrainsClassificationName: this.formData.TrainsClassificationName,
            ParentId: null,
            Level: 1
          };

          trainsClassification.add(obj).then(res => {
            this.$notify({
              title: "提示",
              message: "保存成功",
              type: "success",
              duration: 2000
            });
            this.$refs.addDialog.createData()
          })
        }
      });
    },
    getDetail() {
      this.formData = Object.assign({}, this.formData, this.node)
    },
    handleClose() {
      this.$refs.addDialog.handleClose()
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
