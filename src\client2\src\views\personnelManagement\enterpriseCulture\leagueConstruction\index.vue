<template>
<div class="app-container newReport">
    <div class="bg-white">
        <!-- <page-title title="团建活动" :subTitle='["企业团建活动管理页面"]'></page-title> -->
        
        <div style="height: 100%;" class="__dynamicTabContentWrapper">
            <div style="border-bottom: 1px solid silver;margin-bottom: 10px;">
                <tags :items='datetimeGroupTypes' v-model="listQuery.TimeHorizon" style="margin-bottom: 7px;">
                    <template v-for="t in datetimeGroupTypes" :slot="t.value">
                        {{ t.label }}
                    </template>
                </tags>
            </div>

            <!-- 查询条件 -->
            <div style="border-bottom: 1px solid silver; padding-bottom: 10px; overflow: hidden; margin-bottom: 10px;">
                <app-table-form :label-width="'70px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
                    <template slot='ActivityTitle'>
                        <el-input style="width: 100%;padding-right:15px;" v-model="listQuery.ActivityTitle" placeholder=""></el-input>
                    </template>

                    <template slot="Range">
                        <el-date-picker v-model="listQuery.Range" type="daterange" align="right" unlink-panels range-separator="-" start-placeholder end-placeholder format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                    </template>
                </app-table-form>
            </div>
            <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn>
            <div class="noData" v-if="(!dataSourceList || dataSourceList.length == 0) && !parLoading">
                暂无数据
            </div>
            <div v-else class="content" v-loading='parLoading'>
                <!-- <no-data v-show="!dataSourceList || dataSourceList.length == 0"></no-data> -->
                <div class="card-wrapper" v-for="c in dataSourceList" :key="c.LeagueConstructionId">
                    <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                        <div slot="header" class="clearfix">
                            <span>
                                <i class="el-icon-edit-outline" v-if="rowBtnIsExists('btnEdit')" style="cursor:pointer" @click="handleUpdate(c)"></i>
                                <i class="el-icon-delete" v-if="rowBtnIsExists('btnDel')" style="cursor:pointer" @click="handleDelete(c)"></i>
                            </span>
                        </div>
                        <div @click="handleUpdate(c, 'detail')" class="elMain">
                            <div>
                                <img :src="c.ActivityCoverPath">
                                <span>{{c.ActivityTitle}}</span>
                            </div>
                        </div>
                    </el-card>
                </div>
            </div>
            <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="Id" @reload="getList"></create-page>
</div>
</template>

<script>
import * as systemLeagueConstruction from '@/api/personnelManagement/systemLeagueConstruction'
import createPage from './create'
import elDragDialog from '@/directive/el-dragDialog'
import dayjs from "dayjs";

export default {
    components: {
        createPage
    },
    directives: {
        elDragDialog
    },
    created() {
        this.getList()
    },
    mounted() {},
    watch: {
        'listQuery.TimeHorizon'() {
            this.getList()
        },
    },
    data() {
        return {
            tableSearchItems: [{
                    prop: 'ActivityTitle',
                    label: '活动标题'
                },
                {
                    prop: "Range",
                    label: "活动时间"
                },
            ],

            datetimeGroupTypes: [{
                    value: 0,
                    label: '全部'
                },
                {
                    value: 1,
                    label: '最近一个月'
                },
                {
                    value: 2,
                    label: '最近三个月'
                },
                {
                    value: 3,
                    label: '最近半年'
                }
            ],

            dialogStatus: 'create',
            dialogFormVisible: false,
            parLoading: false,
            listQuery: {
                ActivityTitle: '',
                Range: [],
                ActivityStartTime: null,
                ActivityEndTime: null,
                TimeHorizon: 0
            },
            dataSourceList: [],
            Id: '',
            total: 0,
        }
    },
    methods: {
        onResetSearch() {
            this.listQuery.Range = []
            this.listQuery.ActivityTitle = ''
            this.listQuery.ActivityStartTime = null
            this.listQuery.ActivityEndTime = null
            this.listQuery.TimeHorizon = 0
            this.getList()
        },

        handleFilter() {
            this.listQuery.PageIndex = 1
            this.getList()
        },

        onBtnClicked: function (domId) {
            switch (domId) {
                case "btnAdd":
                    this.handleDialog('create')
                    break;
                default:
                    break;
            }
        },

        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogFormVisible = false
        },
        handleSaveSuccess(_formData) {
            this.getList()
            this.closeDialog()
        },

        // 弹出编辑框
        handleUpdate(row, optType = "update") {
            this.Id = row.LeagueConstructionId
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        //获取列表
        getList() {
            this.parLoading = true
            if (this.listQuery.Range && this.listQuery.Range.length == 2) {
                this.listQuery.ActivityStartTime = dayjs(this.listQuery.Range[0]).format("YYYY-MM-DD 00:00:00");
                this.listQuery.ActivityEndTime = dayjs(this.listQuery.Range[1]).format("YYYY-MM-DD 23:59:59");
            }
            systemLeagueConstruction.getList(this.listQuery).then(res => {
                this.parLoading = false
                this.dataSourceList = res.Items
                this.total = res.Total
            })
        },

        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        handleDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.LeagueConstructionId)
            } else {
                ids.push(rows.LeagueConstructionId)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                systemLeagueConstruction.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
    }
}
</script>

<style lang="scss" scoped>

.noData {
    text-align: center;
    height: 300px;
    line-height: 300px;
}


.bg-white {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 10px;
}

.elMain {
   width: 100%;
    height: 229px;
    cursor: pointer;
    // display: flex;
    // justify-content: space-between;
    position: absolute;


    >div:first-child {
        position: relative;
        width: 100%;
        height: 100%;
        text-align: center;
        overflow: hidden;
        >img {
            //   width: auto;
            //   height: auto;
            //   max-width: 100%;
            //   max-height: 100%;
            width: calc(100% + 1px);
            height: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        >span {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 30px;
            background: rgba(70, 70, 70, 0.7);
            color: white;
            text-align: left;
            line-height: 30px;
            padding-left: 5px;
            padding-right: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    >div:last-child {
        width: 100%;
        color: #909399;
        background:#282828;
    }
}

.omit1 {
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.clearfix {
    >span {
        float: right;
        padding-right: 9px;

        i {
            font-size: 20px;
            margin-top: 8px;
            margin-right: 4px;
        }
    }
}

.mainBold {
    font-size: 16px;
    font-weight: 700;
}

.divUeditor {
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

// .page-wrapper {
//     display: flex;
//     background: #fff;
//     padding: 10px;

//     .content-wrapper {
//         flex: 1;

//     }
// }
.content {
    //   padding: 10px;
    flex: 1;
    overflow-y: auto;
    padding-right: 0;
    min-height: 400px;
    .opt-wrapper {
        box-sizing: border-box;
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 10px;
    }

    .card-wrapper {
        padding-top: 10px;
        padding-right: 5px;
        display: inline-block;
        width: 25%;

        .card {
            width: 100%;
            height: 265px;
        }

        .item-wrapper {
            cursor: pointer;
            padding: 14px;
            padding-bottom: 2px;

            .item {
                padding-bottom: 4px;

                div {
                    display: inline-block;
                }

                .item-title {
                    width: 90px;
                }
            }
        }

        .split-line {
            height: 1px;
            background: #dcdfe6;
            margin-top: 10px;
        }

        .footer {
            padding-right: 14px;
            text-align: right;
        }
    }
}
</style>
