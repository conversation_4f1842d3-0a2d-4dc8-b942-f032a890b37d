<!-- 上传发票并识别文字 -->
<template>
  <app-dialog
    ref="appDialogRef"
    :width="1200"
    className="clear-padding"
    title="上传发票"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div slot="body" class="body_wrapper">
      <div class="upload_container" v-if="!disabled">
        <el-upload
          action=""
          drag
          multiple
          :accept="accept"
          :before-upload="beforeUpload"
          :http-request="httpRequest"
          :show-file-list="false"
          :disabled="loadingCount > 0"
          style="width: 100%"
          :limit="limit"
          :on-exceed="showFilesLimitMessage"
        >
          <div v-if="loadingCount <= 0">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              <p>点击选择或将文件拖拽至此区域进行上传</p>
              <p class="placeholder">
                支持jpg、jpeg、png、pdf格式文件批量上传，单个文件大小需小于5MB
              </p>
            </div>
          </div>
          <div v-else class="upload_loading">
            <i class="el-icon-loading color-primary" style="font-size: 30px"></i>
            <div class="el-upload__text">
              <p>票据识别中，请稍等...</p>
              <el-button type="text" @click.stop="cancelUpload">取消</el-button>
            </div>
          </div>
        </el-upload>
      </div>
      <div class="label" style="line-height: 40px">已识别发票</div>
      <div class="table_container" id="__dynamicTabCoreWrapper">
        <el-form :model="formData" ref="formRef">
          <app-table-core
            ref="mainTable"
            :tab-columns="tableColumn"
            :tab-datas="formData.tableData"
            :tab-auth-columns="[]"
            :isShowOpatColumn="!disabled"
            :startOfTable="startOfTable"
            :multable="false"
            :serial="false"
            :optColWidth="90"
            :height="tabHeight"
            row-key="OcrId"
          >
            <template slot="sortOpt">
              <div class="move-wrapper" v-if="!disabled">
                <span class="sortable-ghost">
                  <svg-icon icon-class="move" className="icon-mini"></svg-icon>
                </span>
              </div>
            </template>
            <template slot="InvoiceNumber" slot-scope="{ row, index }">
              <el-form-item
                label=""
                label-width="0"
                :key="index"
                style="margin-bottom: 0"
                :prop="`tableData.${index - 1}.InvoiceNumber`"
                :rules="[{ required: true, message: '请输入发票号码', trigger: 'blur' }]"
              >
                <el-input v-model="row.InvoiceNumber" :maxlength="50" :disabled="disabled" />
              </el-form-item>
            </template>
            <template slot="Payee" slot-scope="{ row, index }">
              <el-form-item
                label=""
                label-width="0"
                :key="index"
                style="margin-bottom: 0"
                :prop="`tableData.${index - 1}.Payee`"
                :rules="[{ required: true, message: '请输入收款方', trigger: 'blur' }]"
              >
                <el-input v-model="row.Payee" :maxlength="50" :disabled="disabled" />
              </el-form-item>
            </template>
            <template slot="Payer" slot-scope="{ row, index }">
              <el-form-item
                label=""
                label-width="0"
                :key="index"
                style="margin-bottom: 0"
                :prop="`tableData.${index - 1}.Payer`"
                :rules="[{ required: true, message: '请输入付款方', trigger: 'blur' }]"
              >
                <el-input v-model="row.Payer" :maxlength="50" :disabled="disabled" />
              </el-form-item>
            </template>
            <template slot="InvoiceAmount" slot-scope="{ row, index }">
              <el-form-item
                label=""
                label-width="0"
                :key="index"
                style="margin-bottom: 0"
                :prop="`tableData.${index - 1}.InvoiceAmount`"
                :rules="[{ required: true, message: '请输入发票金额', trigger: 'blur' }]"
              >
                <el-input
                  v-model="row.InvoiceAmount"
                  type="text"
                  v-thousands="true"
                  :maxlength="15"
                  :disabled="disabled"
                />
              </el-form-item>
            </template>
            <template slot="InvoiceDate" slot-scope="{ row, index }">
              <el-form-item
                v-if="!disabled"
                label=""
                label-width="0"
                :key="index"
                style="margin-bottom: 0"
                :prop="`tableData.${index - 1}.InvoiceDate`"
                :rules="[{ required: true, message: '请输入开票时间', trigger: 'change' }]"
              >
                <el-date-picker
                  style="width: 100%"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  v-model="row.InvoiceDate"
                  type="date"
                  placeholder=""
                  :disabled="disabled"
                />
              </el-form-item>
              <span v-else>{{ row.InvoiceDate | dateFilter("YYYY/MM/DD") }}</span>
            </template>
            <template slot="image" slot-scope="{ row }">
              <el-image
                class="img"
                v-for="item in row.AttachmentList"
                :src="item.Path"
                :key="item.Id"
                @click="handleImage(item.Id)"
              />
            </template>
            <template slot-scope="{ row }">
              <app-table-row-button
                :type="3"
                v-if="!disabled"
                text="删除"
                @click="deleteInvoice(row)"
              />
            </template>
          </app-table-core>
        </el-form>
      </div>
      <div class="tips">
        注：流程审批通过后，请将纸质票据原件（电子发票需打印出来）提供给财务部进行核验
      </div>
    </div>
    <div slot="footer">
      <app-button :buttonType="2" :loading="loadingCount > 0" @click="closeDialog" />
      <app-button :loading="loadingCount > 0" v-if="!disabled" text="提交" @click="createData" />
    </div>
  </app-dialog>
</template>

<script>
import { testOcrUpLoadApiWithCancel } from "@/api/invoice";
import indexPageMixin from "@/mixins/indexPage";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins";
import { debounce } from "lodash";
import thousands from "@/directive/thousands";
import Sortable from "sortablejs";

export default {
  name: "UploadInvoiceDialog",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: Number,
      default: 20,
    },
  },
  directives: {
    thousands,
  },
  mixins: [indexPageMixin, tabDynamicHeightMixins],
  data() {
    return {
      formData: {
        tableData: [],
      },
      accept: ".jpg,.jpeg,.png,.pdf,.JPG,.JPEG,.PNG,.PDF",
      loadingCount: 0,
      // 请求取消方法列表
      reqCancelList: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.handleDrop();
    });
  },
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (!val) {
          this.closeDialog();
          return;
        }
      },
      immediate: true,
    },
    value: {
      handler(val) {
        this.formData.tableData = this.$_.cloneDeep(val);
      },
      immediate: true,
    },
  },
  computed: {
    /// 动态控制表格列用不用插槽
    tableColumn() {
      return [
        { attr: { prop: "sortOpt", label: "", width: 30 }, slot: true },
        { attr: { prop: "InvoiceNumber", label: "发票号码" }, slot: !this.disabled },
        { attr: { prop: "Payee", label: "收款方" }, slot: !this.disabled },
        { attr: { prop: "Payer", label: "付款方" }, slot: !this.disabled },
        {
          attr: { prop: "InvoiceAmount", label: "发票金额(元)", width: 150 },
          slot: !this.disabled,
        },
        { attr: { prop: "InvoiceDate", label: "开票时间", width: 200 }, slot: true },
        { attr: { prop: "image", label: "上传文件", width: 100 }, slot: true },
      ];
    },
    // 所有发票图片集合
    allInvoiceImageList() {
      const result = [];
      this.formData.tableData.forEach(item => {
        const AttachmentList = item.AttachmentList;
        AttachmentList.forEach(a => {
          result.push({ src: a.Path, id: a.Id });
        });
      });
      console.log("result", result);
      return result;
    },
  },
  methods: {
    // 显示文件数量超限提示
    showFilesLimitMessage: debounce(function () {
      this.$message.error(`最多只能上传${this.limit}张发票`);
    }, 300),
    // 上传前校验文件大小
    beforeUpload(file) {
      // 检查文件大小
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error("上传文件大小不能超过5MB!");
        return false;
      }

      return true;
    },
    httpRequest(options) {
      const formData = new FormData();
      formData.append("file", options.file);

      this.loadingCount++;
      const { promise, cancel } = testOcrUpLoadApiWithCancel(formData);
      this.reqCancelList.push(cancel);
      promise
        .then(res => {
          if (res?.length > 0) {
            res.forEach(item => {
              item.AttachmentIdList = item.AttachmentList.map(item => item.Id);
              this.formData.tableData.push(item);
            });
          }
        })
        .finally(() => {
          this.loadingCount > 0 && this.loadingCount--;
        });
    },
    deleteInvoice(row) {
      const index = this.formData.tableData.findIndex(item => item.OcrId === row.OcrId);
      this.formData.tableData.splice(index, 1);
      this.change();
    },
    createData() {
      if (this.formData.tableData.length === 0) {
        this.$message.error("请上传发票");
        return;
      }
      if (this.formData.tableData.length > this.limit) {
        this.showFilesLimitMessage();
        return;
      }

      this.$refs.formRef.validate(valid => {
        if (valid) {
          const result = this.$_.cloneDeep(this.formData.tableData);
          this.$refs.appDialogRef.createData(result);
          this.change();
        } else {
          this.$message.error("请输入完整信息");
        }
      });
    },
    change() {
      const list = this.$_.cloneDeep(this.formData.tableData);
      this.$emit("input", list);
      this.$emit("change", list);
    },
    closeDialog() {
      this.$refs.appDialogRef.handleClose();
    },
    handleDrop() {
      if (this.disabled) return;

      setTimeout(() => {
        const table = this.$refs.mainTable.$el.querySelectorAll(
          ".el-table__body-wrapper > table > tbody"
        )[0];
        const that = this;
        let list = this.formData.tableData || [];

        Sortable.create(table, {
          animation: 300,
          handle: ".sortable-ghost",
          onEnd({ newIndex, oldIndex }) {
            // 拖拽排序数据
            list.splice(newIndex, 0, list.splice(oldIndex, 1)[0]);
            const newArray = list.slice(0);
            list = []; // 必须有此步骤，不然拖拽后回弹
            that.$nextTick(function () {
              list = newArray; // 重新赋值，用新数据来刷新视图
              that.formData.tableData = list || [];
            });
          },
        });
      }, 300);
    },
    handleImage(id) {
      const index = this.allInvoiceImageList.findIndex(t => t.id === id);
      console.log("index", id, index);

      if (index > -1) {
        this.$viewerApi({
          images: this.allInvoiceImageList,
          // initialIndex: index,
          options: { initialViewIndex: index },
        });
      } else {
        this.$message.error("预览图片失败");
      }
    },
    // 取消全部请求
    cancelUpload() {
      this.reqCancelList.forEach(func => {
        func();
        this.loadingCount = 0;
      });
      this.reqCancelList = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.body_wrapper {
  padding: 10px;
  height: 600px;
  display: flex;
  flex-direction: column;
  .upload_container {
    display: flex;
    justify-content: center;
    .upload_loading {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    /deep/.el-upload {
      width: 100%;
      .el-upload-dragger {
        width: 100%;
        background-color: $bg-color-1;
        border: 1px dashed #d9d9d9;
      }
    }
  }
  .table_container {
    flex: 1;
    .img {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      object-fit: cover;
      margin-right: 5px;
      cursor: pointer;
    }
  }
  .tips {
    margin-top: 10px;
    color: $color-danger;
  }
}
/deep/input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  &[type="number"] {
    -moz-appearance: textfield;
  }
}

.move-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  .sortable-ghost {
    height: 16px;
    cursor: move;
  }
}
</style>
