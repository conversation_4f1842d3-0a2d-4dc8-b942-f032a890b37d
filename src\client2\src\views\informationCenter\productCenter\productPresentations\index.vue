<template>
    <div class="app-container">
        <div class="bg-white">
            <!-- <page-title title="产品介绍" :subTitle='["在售产品、产品类型的管理页面"]'></page-title> -->
            <div class="pageWrapper">
                <div class="product-list"><br v-if="btnAddClassification==''">
                    <div class="treeBox">
                        <div class="btn-wrapper" v-if="btnAddClassification=='btnAddClassification'">
                            <el-button type="primary" @click="addClassification">添加分类</el-button>
                        </div>
                        <el-input class="elInput" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText">
                        </el-input>
                        <el-tree class="elTree" ref="tree" :data="treeDatas" node-key="Id" v-loading='treeLoading' :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current='true' :check-on-click-node='true' @node-click="(data) => checkedNode = data">
                            <span class="custom-tree-node" slot-scope="{ node, data }">
                                <span class="node-title" :title="node.label" :style="{width: getNodeWidth(node.level) + 'px'}">{{ node.label }}</span>
                                <span class="node-btn-area">
                                    <el-dropdown trigger="click" v-if="node.label !='全部'" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                                            <!-- <el-dropdown-item v-show="node.level < 3" command="add">添加子级</el-dropdown-item>
                        <el-dropdown-item v-show="node.level > 1" command="update">修改名称</el-dropdown-item>
                        <el-dropdown-item v-show="node.level > 1" command="delete">删除</el-dropdown-item> -->
                                            <el-dropdown-item v-show="node.level < 3" command="add">添加子级</el-dropdown-item>
                                            <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                                            <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </span>
                            </span>
                        </el-tree>
                    </div>
                </div>
                <div class="content-wrapper __dynamicTabContentWrapper">
                    <div class="content __dynamicTabWrapper">
                        <app-table ref="mainTable" :tab-columns="tabColumns" :layoutMode='layoutMode' :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :multable='false' :startOfTable="startOfTable" @sortChagned='handleSortChange'>
    
                            <template slot="IsShow" slot-scope="scope">
                                <span class="item-status" :class="`status-${scope.row.IsShow}`">
                                    {{ scope.row.IsShow | isShowFilter}}
                                </span>
                            </template>
                            <template slot="CreateEmployee" slot-scope="scope">{{ scope.row.CreateEmployee | nameFilter }}</template>
                            <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>
    
                            <template slot="LastUpdateTime" slot-scope="scope">{{ scope.row.LastUpdateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template>
                            <template slot="ProductFeature" slot-scope="scope">
                                <template v-if="scope.row.ProductFeature">
                                    <el-tag v-for="tagItem in scope.row.ProductFeature.split(',')" :key="tagItem">{{tagItem}}</el-tag>
                                </template>
                                <template v-else>无</template>
                            </template>
                            <!-- 表格查询条件区域 -->
                            <template slot="conditionArea">
                                <app-table-form :label-width="'100px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch' :layoutMode='layoutMode'>
    
                                    <template slot='Keywords'>
                                        <el-input style="width: 100%;" placeholder="搜索产品名称/产品特点" @clear='getList' v-antiShake='{
                                                time: 300,
                                                callback: () => {
                                                    getList()
                                                }
                                            }' clearable v-model.trim="listQuery.Keywords"></el-input>
                                    </template>
                                    <template slot='IsShow'>
                                        <el-select style="width:100%;" v-model="listQuery.IsShow" clearable placeholder="请选择">
                                            <el-option label="有效" :value="true"></el-option>
                                            <el-option label="无效" :value="false"></el-option>
                                        </el-select>
                                    </template>
                                    <template slot='LastUpdateTime'>
                                        <el-date-picker v-model="listQuery.LastUpdateTime" type="datetimerange"
                                        align="right" unlink-panels range-separator="-" start-placeholder :default-time="['00:00:00', '23:59:00']"
                                        end-placeholder format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm"
                                        style="width: 100%;"></el-date-picker>
                                    </template>
                                    <!-- 表格批量操作区域 -->
                                    <template slot="btnsArea">
                                        <!--{{topBtns}} -->
                                        <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                                        <el-button style="margin-left:4px;" v-if="btnAdd=='btnAdd'" type="primary" @click='handleDialog("create")'>添加产品介绍</el-button>
    
                                    </template>
                                </app-table-form>
                            </template>
    
    
                            <!-- 表格行操作区域 -->
                            <template slot-scope="scope">
                                <app-table-row-button v-if="rowBtnIsExists('btnSetIsShow')" @click="handleSetIsShow(scope.row)" :text="scope.row.IsShow?'设为无效':'设为有效'"></app-table-row-button>
                                <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type='1' ></app-table-row-button>
                                <app-table-row-button @click="handleReview(scope.row)" :type='2'></app-table-row-button>
                                <app-table-row-button v-if="rowBtnIsExists('btnDel')" v-show="!scope.row.IsShow" @click="handleTableDelete(scope.row)" :type='3' ></app-table-row-button>
                            </template>
                        </app-table>
                    </div>
                    <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
                </div>
            </div>
        </div>
    
        <!-- 创建产品分类 -->
        <create-folder-page v-if="currentOptNode" @closeDialog='closeFolderDialog' @saveSuccess='handleFolderSaveSuccess' :dialogFormVisible='dialogFolderFormVisible' :dialogStatus='dialogFolderStatus' :node='currentOptNode'>
        </create-folder-page>
    
        <!-- 创建一级分类 -->
        <create-classification-page @closeDialog='closeClassificationDialog' @saveSuccess='handleClassificationSaveSuccess' :dialogFormVisible='dialogClassificationFormVisible'>
        </create-classification-page>
    
        <!-- 添加/修改 产品介绍 -->
        <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList" :selectTypeId='selectTypeId'></create-page>
    </div>
    </template>
    
    <script>
    import {
        listToTreeSelect
    } from '@/utils'
    import elDragDialog from '@/directive/el-dragDialog'
    import indexPageMixin from '@/mixins/indexPage'
    import * as productPresentations from '@/api/informationCenter/productPresentations'
    import * as productClassification from '@/api/informationCenter/productClassification'
    import createFolderPage from './createFolder'
    import createPage from "./create";
    import createClassificationPage from "./createClassification";
    
    export default {
        name: 'module-setting',
        mixins: [indexPageMixin],
        components: {
            createFolderPage,
            createPage,
            createClassificationPage
        },
        props: {},
        filters: {
            nameFilter(creator) {
                if (creator) {
                    return creator.Name;
                }
                return "";
            },
            isShowFilter(isShow) {
                if (isShow) {
                    return "有效";
                }
                return "无效";
            },
        },
        computed: {
            fildids() {
                return this.multipleSelection.map(s => s.Id) || []
            }
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            },
            checkedNode: {
                handler(val) {
                    if (val) {
                        this.listQuery.PageIndex = 1
                        this.listQuery.ProductClassificationId = val.Id
                        this.getList()
                    }
                },
                immediate: true
            },
        },
        created() {
            this.getProductClassification()
            this.btnTextValue()
        },
        data() {
            return {
                filterText: '',
                btnAddClassification: '',
                btnAddChildren: '',
                btnAdd: '',
                treeDatas: [],
                treeLoading: false,
                defaultProps: { //树默认结构
                    children: 'children',
                    label: 'ProductClassificationName'
                },
                layoutMode: 'simple',
                tableSearchItems: [
                    { prop: "Keywords", label: "", mainCondition: true },
                    // { prop: 'ProductTitle',label: '产品标题'},
                    { prop: 'IsShow',label: '状态'},
                    { prop: 'LastUpdateTime',label: '最后修改时间'},
                ],
                checkedNode: null, //当前单击选中的节点
                classificationListQuery: {
                    ProductClassificationName: ""
                },
    
                dialogFolderFormVisible: false,
                dialogFolderStatus: 'create',
                currentOptNode: null, //当前操作的文件夹节点（新增、编辑、删除）
                selectTypeId: '', //当前选中的类型ID
    
                dialogClassificationFormVisible: false,
    
                id: "",
                dialogStatus: "create",
                dialogFormVisible: false,
    
                listLoading: false,
                tabColumns: [{
                        attr: {
                            prop: 'ProductTitle',
                            label: '产品标题',
                            showOverflowTooltip: true
                        },
                    },
                    {
                        attr: {
                            prop: 'IsShow',
                            label: '状态',
                            sortable: 'custom'
                        },
                        slot: true
                    },
                    {
                        attr: {
                            prop: 'ProductClassificationName',
                            label: '类型'
                        }
                    },
                    {
                        attr: {
                            prop: 'ProductFeature',
                            label: '产品特点'
                        },
                        slot: true
                    },
                    {
                        attr: {
                            prop: 'LastUpdateTime',
                            label: '最后修改时间',
                            sortable: 'custom'
                        },
                        slot: true
                    }
                    // {
                    //     attr: {
                    //         prop: 'CreateEmployee',
                    //         label: '创建人'
                    //     },
                    //     slot: true
                    // },
                    // {
                    //     attr: {
                    //         prop: 'CreateTime',
                    //         label: '创建时间',
                    //         showOverflowTooltip: true, 
                    //         sortable: 'custom'
                    //     },
                    //     slot: true
                    // }
                ],
                listQuery: {
                    ProductClassificationId: '',
                    ProductTitle: '',
                    LastUpdateTime: null,
                    WebOrApp: 'Web'
                },
                multipleSelection: [],
                tabDatas: [], //原始数据
                total: 0,
    
            }
        },
        methods: {
            getNodeWidth(level) {
                let def_width = 190
                let result = def_width - (level - 1) * 20
                return result <= 0 ? 50 : result
            },
            btnTextValue() {
                let btns = this.topBtns
                btns.forEach(item => {
                    if (item["DomId"] == "btnAddClassification") {
                        this.btnAddClassification = "btnAddClassification"
                    }
                    if (item["DomId"] == "btnAdd") {
                        this.btnAdd = "btnAdd"
                    }
                    if (item["DomId"] == "btnAddChildren") {
                        this.btnAddChildren = "btnAddChildren"
                    }
                })
            },
            onResetSearch() {
                this.listQuery.Keywords = ''
                this.listQuery.IsShow = null;
                this.listQuery.LastUpdateTime = null
                
                this.getList() //刷新列表
            },
    
            //查看详情
            handleReview(row) {
                this.$router.push(`/informationCenter/productCenter/productPresentations/detail/${row.Id}?productClassificationId=${row.ProductClassificationId}`)
            },
            handleSortChange({ column, prop, order }) {
                this.sortObj = {prop, order}
                this.getList()
            },
            //获取产品列表
            getList() {
                if (this.checkedNode) {
                    this.listLoading = true
                    let postData = JSON.parse(JSON.stringify(this.listQuery))
                    postData = this.assignSortObj(postData)
                    if(postData.IsShow!==true&&postData.IsShow!==false){
                        delete postData.IsShow
                    }
                    if(postData.LastUpdateTime&&postData.LastUpdateTime.length>0){
                        postData.StartLastUpdateTime = postData.LastUpdateTime[0]
                        postData.EndLastUpdateTime = postData.LastUpdateTime[1]
                        delete postData.LastUpdateTime
                    }
                    productPresentations.getList(postData).then(res => {
                        this.tabDatas = res.Items
                        this.total = res.Total
                        this.listLoading = false
                    }).catch(err => {
                        this.listLoading = false
                    })
                }
            },
            onBtnClicked: function (domId) {
                switch (domId) {
                    //添加分类
                    case "btnAddClassification":
                        this.addClassification();
                        break;
                        //添加
                    case "btnAdd":
                        this.handleDialog("create");
                        break;
                        //批量删除
                    case "btnBatchDel":
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: "至少删除一个",
                                type: "error"
                            });
                            return;
                        }
                        this.handleTableDelete(this.multipleSelection);
                        break;
                    default:
                        break;
                }
            },
    
            //弹出添加框
            handleDialog(activeName) {
                this.dialogStatus = activeName;
                this.dialogFormVisible = true;
                this.selectTypeId = this.listQuery.ProductClassificationId
            },
    
            // 弹出编辑框
            handleTableUpdate(row, optType = 'update') {
                this.id = row.Id;
                this.dialogStatus = optType;
                this.dialogFormVisible = true;
            },
    
            //设置有效/无效
            handleSetIsShow(rows) {
                let ids = []
                if (_.isArray(rows)) {
                    ids = rows.map(u => u.Id)
                } else {
                    ids.push(rows.Id)
                }
    
                var isShow = rows.IsShow;
                var message = isShow ? "设置为无效" : "设置为有效";
                this.$confirm('是否确认' + message + '?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    productPresentations.setIsShow(ids).then(() => {
                        this.$notify({
                            title: '成功',
                            message: '设置成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.getList()
                    })
                })
            },
    
            // 多行删除
            handleTableDelete(rows) {
                let ids = []
                if (_.isArray(rows)) {
                    ids = rows.map(u => u.Id)
                } else {
                    ids.push(rows.Id)
                }
    
                this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    productPresentations.del(ids).then(() => {
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.getList()
                    })
                })
            },
    
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            handleFilter() {
                this.listQuery.PageIndex = 1
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
    
            closeDialog() {
                this.dialogFormVisible = false;
            },
            handleSaveSuccess(_formData) {
                this.getList();
                this.closeDialog();
            },
    
            /***************************************左侧树菜单操作***************************************/
    
            listToTreeSelect,
    
            //按关键字过滤树菜单
            filterNode(value, data) {
                if (!value) return true;
                return data.ProductClassificationName.indexOf(value) !== -1;
            },
    
            //获取左侧树菜单
            getProductClassification() {
                this.treeLoading = true
                productClassification.getListByCondition(this.classificationListQuery).then(res => {
                    this.treeLoading = false
                    if(res&&res.length>0){
                        res.unshift({
                            Id: "",
                            ProductClassificationName: "全部",
                            Level: 0,
                            ParentId: null
                        });
                    }
                    this.treeDatas = listToTreeSelect(res)
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked()
                    }
                }).catch(err => {
                    this.treeLoading = false
                })
            },
            //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
            setDefaultChecked() {
                this.$nextTick(() => {
                    if (this.treeDatas && this.treeDatas.length > 0) {
                        let rootNode = this.treeDatas[0]
                        this.$refs.tree.setCurrentKey(rootNode.Id);
                        this.checkedNode = rootNode
                    }
                });
            },
    
            //左侧树操作菜单
            handleCommand(optType, node, data) {
                if (optType == 'add') {
                    this.handleCreateFolder(data)
                } else if (optType == 'update') {
                    this.handleCreateFolder(data, 'update')
                } else if (optType == 'delete') {
                    this.handleDelete(node, data)
                }
            },
    
            //添加父分类
            addClassification() {
                this.dialogClassificationFormVisible = true;
                //   this.$prompt('', '添加产品分类', {
                //     confirmButtonText: '确认',
                //     cancelButtonText: '取消',
                //     inputPlaceholder: '请输入分类名称',
                //     inputPattern: /^[\u4e00-\u9fffa-zA-Z0-9]{1,10}$/,
                //     inputErrorMessage: '只能输入汉字、字母、数字，并且不能超过10个字符'
                //   }).then(({ value }) => {
                //     var obj = {
                //       ProductClassificationName: value,
                //       ParentId: null,
                //       Level: 1
                //     };
                //     productClassification.add(obj).then(res => {
                //       this.$notify({
                //         title: '成功',
                //         message: '添加成功',
                //         type: 'success',
                //         duration: 2000
                //       })
                //       this.getProductClassification()
                //     })
                //   }).catch(() => { });
            },
    
            // 添加/修改 子分类
            handleCreateFolder(data, optType = 'create') {
                this.dialogFolderStatus = optType
                this.currentOptNode = data
                this.dialogFolderFormVisible = true
            },
    
            //删除分类
            handleDelete(node, data) {
                this.$confirm(`是否确认删除${data.ProductClassificationName}所包含的所有产品吗?`, "提示", {
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    productClassification.del([data.Id]).then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null
                        }
                        this.getProductClassification()
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                    })
                });
            },
    
            //关闭树菜单的操作层
            closeFolderDialog() {
                this.dialogFolderFormVisible = false
            },
    
            //树菜单操作保存
            handleFolderSaveSuccess() {
                this.getProductClassification()
                this.closeFolderDialog()
            },
    
            closeClassificationDialog() {
                this.dialogClassificationFormVisible = false
            },
    
            handleClassificationSaveSuccess() {
                this.getProductClassification()
                this.closeClassificationDialog()
            },
        },
    }
    </script>
    
    <style lang="scss" scoped>
    .el-tag{
        margin-right: 5px;
    }
    
    .pageWrapper {
        display: flex;
        position: absolute;
        left: 0;
        // top: 40px;
        top: 0;
        right: 0;
        bottom: 0;
    
        .product-list {
            width: 250px;
    
            border-right: 1px solid #dcdfe6;
            display: flex;
            flex-direction: column;
            // >div:first-child{
            //     display: flex;
            //     justify-content: space-between;
            //     align-items:center;
            //     padding:0 10px;
            // }
            .btn-wrapper{
                display: flex;
                justify-content: center;
                align-items: center;
                padding-bottom: 10px;
                button{
                    width: 180px;
                }
            }
            .treeBox {
                width: 100%;
                // flex: 1;
                // overflow-y: auto;
                // height: calc(100% - 48px);
                padding-top: 10px;
                height: 100%;
                display: flex;
                flex-direction: column;
    
                .elInput {
                    width: 230px;
                    margin-left: 10px;
                }
    
                .elTree {
                    flex: 1;
                    overflow: auto;
                    margin-top: 10px;
                    padding-bottom: 10px;
                }
            }
        }
    
        .content-wrapper {
            width: calc(100% - 200px);
            flex: 1;
            overflow-y: auto;
    
            .content {
                // padding: 10px;
                // padding-right: 0;
    
                .opt-wrapper {
                    box-sizing: border-box;
                    border-bottom: 1px solid #dcdfe6;
                    padding-bottom: 10px;
                }
            }
        }
    
        .custom-tree-node {
            display: block;
            width: 100%;
            position: relative;
            box-sizing: border-box;
            padding-right: 24px;
    
            .node-title {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
    
            .node-btn-area {
                position: absolute;
                right: 0;
                top: 0;
                width: 23px;
                height: 16px;
            }
        }
    }
    
    /* 有效 */
    .status-true {
        background-color: green;
    }
    
    /* 无效 */
    .status-false {
        background-color: red;
    }
    
    .item-status {
        color: #fff;
        padding: 2px 4px;
        border-radius: 10%;
    }
    </style>
    