<!-- 关联出差单 -->
<template>
  <div class="relation_evection">
    <div class="relation_evection_top">
      <span class="text-secondary">关联出差申请</span>
      <el-button type="primary" size="mini" v-if="!disabled" @click="showEvectionSelector = true">
        添加出差单
      </el-button>
    </div>
    <div class="list">
      <div class="item-wrapper" v-for="(t, idx) in checkedList" :key="idx">
        <div class="caption">
          <span class="title">{{ t.ApprovalCode }}</span>
          <div class="caption_btn">
            <el-link
              style="margin-right: 6px"
              @click="handleDetail(t)"
              :underline="false"
              type="primary"
            >
              详情
            </el-link>
            <el-link v-if="!disabled" @click="handleRemove(idx)" :underline="false" type="danger">
              删除
            </el-link>
          </div>
        </div>
        <div class="text-secondary">
          开始时间：
          <template v-if="t.StartTime">
            {{ t.StartTime | dateFilter("YYYY/MM/DD") }}
          </template>
          {{ t.StartUpDown == 1 ? "上午" : "下午" }}
        </div>
        <div class="text-secondary">
          结束时间：
          <template v-if="t.EndTime">
            {{ t.EndTime | dateFilter("YYYY/MM/DD") }}
          </template>
          {{ t.EndUpDown == 1 ? "上午" : "下午" }}
        </div>
        <div class="text-secondary">(共计 {{ t.TimeTotal }} 天)</div>
        <el-divider v-if="idx !== checkedList.length - 1" />
      </div>
    </div>
    <!-- 出差单选择器 -->
    <EvectionSelector
      :isShow="showEvectionSelector"
      :condition="{}"
      @changed="evectionSelectorChanged"
      @closed="showEvectionSelector = false"
      :multiple="true"
      :checkedList="checkedList"
    />
    <!-- 出差申请 -->
    <Evection
      v-if="dialogEvVisible"
      dialogStatus="detail"
      @closeDialog="dialogEvVisible = false"
      :dialogFormVisible="dialogEvVisible"
      :id="applyId"
      :approvalId="approvalId"
      :showEndTime="true"
    />
  </div>
</template>

<script>
import EvectionSelector from "@/views/workbench/myWorkbench/apply/evectionSelector.vue";
import Evection from "@/views/workbench/myWorkbench/apply/evection.vue";

export default {
  name: "RelationEvection",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    EvectionSelector,
    Evection,
  },
  data() {
    return {
      checkedList: [],
      showEvectionSelector: false,
      /**
       * 出差申请相关
       */
      dialogEvVisible: false,
      applyId: "",
      approvalId: "",
    };
  },
  watch: {
    value: {
      handler(val) {
        this.checkedList = this.$_.cloneDeep(val);
      },
      immediate: true,
    },
  },
  methods: {
    evectionSelectorChanged(list) {
      this.checkedList = list || [];
      this.change();
    },
    handleDetail(row) {
      this.applyId = row.Id;
      this.dialogEvVisible = true;
    },
    handleRemove(idx) {
      this.checkedList.splice(idx, 1);
      this.change();
    },
    change() {
      const list = this.$_.cloneDeep(this.checkedList);
      this.$emit("input", list);
      this.$emit("change", list);
    },
  },
};
</script>

<style lang="scss" scoped>
.relation_evection {
  .relation_evection_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .list {
    margin-top: 10px;
    .item-wrapper {
      font-size: 12px;
      .caption {
        display: flex;
        align-items: center;
        .title {
          flex: 1;
        }
        .caption_btn {
          flex-shrink: 0;
        }
      }
    }
  }
}
/deep/.el-divider {
  background-color: $border-color-light;
  margin: 10px 0;
}
</style>
