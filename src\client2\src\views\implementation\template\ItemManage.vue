<!--事项模板管理-->
<template>
  <!--组件内容区-->
  <div>
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :maxHeight="600"
      :width="600"
    >
      <template slot="body">
        <el-form
          ref="formRef"
          :rules="rules"
          :model="formModel"
          label-position="right"
          label-width="100px"
        >
          <div class="wrapper" v-loading="loading">
            <el-button size="mini" type="primary" @click="handleAdd">添加新事项</el-button>
            <div style="margin-top:10px;max-height:550px;overflow-y:auto;">
              <div class="item" v-for="(item, idx) in formModel.list" :key="idx">
                <div class="item-title-wrapper">
                  <div class="item-title">事项{{ idx + 1 }}</div>
                  <div class="item-btns">
                    <i v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></i>
                    <i
                      v-show="idx < formModel.list.length - 1"
                      class="el-icon-bottom"
                      title="下移"
                      @click="move('down', idx)"
                    ></i>
                    <i class="el-icon-delete-solid" title="删除" @click="handleRemove(item)"></i>
                  </div>
                </div>
                <el-row class="item-content">
                  <el-col :span="24" class="cus-textarea-wrapper">
                    <el-form-item
                      label="事项名称"
                      :prop="'list.' + idx + '.Name'"
                      :rules="{required: true, message: '事项名称不能为空', trigger: 'blur'}"
                    >
                      <el-input
                        maxlength="50"
                        v-model="formModel.list[idx].Name"
                        clearable
                        :disabled="!editable"
                      ></el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24" class="cus-textarea-wrapper">
                    <el-form-item
                      label="重点关注"
                      :prop="'list.' + idx + '.IsFoucs'"
                    >
                      <el-switch v-model="formModel.list[idx].IsFoucs"></el-switch>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<!--组件脚本区-->
<script>
import * as implementationTemplate from "@/api/implementation/implementationTemplate";
export default {
  /**名称 */
  name: "template-item-manage",
  directives: {},
  /**组件声明 */
  components: {},
  /**参数区 */
  props: {
    dialogStatus: {
      //create、update、detail
      type: String
    },
    //工序模板id（新增必传）
    procedureTemplateId: {
      type: String,
      required: true
    }
  },
  /**数据区 */
  data() {
    return {
      rules: {},
      loading: false,
      disabledBtn: false,
      formModel: {
        list: [
          // {
          //   Id: null,
          //   Name: "",
          //   ImplementationProcedureTemplateId: this.procedureTemplateId,
          // }
        ]
      }
    };
  },
  /**计算属性---响应式依赖 */
  computed: {
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail" && this.dialogStatus != "approval";
    },
    /**组件标题 */
    pageTitle() {
      return "事项管理";
    }
  },
  /**监听 */
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
        if (this.procedureTemplateId) {
          this.getImplementationItems();
        }
      }
    }
  },
  /**渲染前 */
  created() {
    this.rules = this.initRules(this.rules);
  },
  /**方法区 */
  methods: {
    //获取事项列表
    getImplementationItems() {
      this.loading = true;
      this.formModel.list = [];
      implementationTemplate
        .getImplementationItemTemplateListByCondition({
          ImplementationProcedureTemplateId: this.procedureTemplateId
        })
        .then(res => {
          this.formModel.list = res;
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    /**重置数据 */
    resetFormData() {
      let temp = {
        formModel: {
          list: []
        }
      };
      this.formModel.list = Object.assign({}, this.formModel.list, temp);
    },
    /**添加事项 */
    handleAdd() {
      this.formModel.list.push({
        Id: null,
        Name: "",
        ImplementationProcedureTemplateId: this.procedureTemplateId,
        IsFoucs: false
      });
    },
    /**移动事项 */
    move(direction, currIdx) {
      if (
        (direction == "up" && currIdx > 0) ||
        (direction == "down" && currIdx < this.formModel.list.length - 1)
      ) {
        let currRow = JSON.parse(JSON.stringify(this.formModel.list[currIdx]));
        let targetIdx = direction == "up" ? currIdx - 1 : currIdx + 1;
        this.formModel.list.splice(currIdx, 1);
        this.formModel.list.splice(targetIdx, 0, currRow);
      }
    },
    /**移除事项 */
    handleRemove(row) {
      this.$confirm("是否确认删除当前事项?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var index = this.formModel.list.indexOf(row);
        if (index !== -1) {
          this.formModel.list.splice(index, 1);
        }
      });
    },
    /**保存数据 */
    createData() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          let postData = JSON.parse(JSON.stringify(this.formModel.list));
          postData = postData.map((s, idx) => {
            s.OrderIndex = (idx + 1) * 10;
            return s;
          });

          let names = postData.map(s => s.Name);
          if (new Set(names).size != names.length) {
            this.$message({
              message: "事项名称不能重复",
              type: "error"
            });
            return false;
          }

          this.disabledBtn = true;
          implementationTemplate
            .editImplementationItemTemplateList(postData)
            .then(res => {
              this.disabledBtn = false;
              this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
              });
              this.$refs.appDialogRef.createData();
            })
            .catch(err => {
              this.disabledBtn = false;
            });
        }
      });
    },
    /**关闭组件 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  min-height: 100px;
  .item {
    border: 1px solid #ebeef5;
    border-radius: 10px;
    margin-bottom: 10px;
    // padding-top: 10px;
    .item-title-wrapper {
      display: flex;
      height: 24px;
      line-height: 24px;
      margin-bottom: 4px;
      padding: 0 10px;
      border-bottom: 1px solid #ebeef5;
      .item-title {
        flex: 1;
        margin-bottom: 4px;
      }
      .item-btns i {
        margin-left: 5px;
        cursor: pointer;
      }
    }
    .item-content {
      padding-right: 10px;
    }
  }
}
</style>
