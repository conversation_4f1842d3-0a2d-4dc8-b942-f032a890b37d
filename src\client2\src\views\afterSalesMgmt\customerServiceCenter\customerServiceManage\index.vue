<!--客服管理-->
<template>
<!--组件内容区-->
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="客服人员管理" :subTitle="['客服人员的管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list"><br v-if="btnAddClassification==''">
                <el-button v-if="btnAddClassification=='btnAddClassification'" type="primary" style="width: 180px;margin: 10px 0;margin-left:35px;" @click="addTopLevel">添加一级</el-button>
                <el-input class="elInput" style="margin:0 10px 10px 10px;width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                <div class="treeBox" v-loading='treeLoading'>
                    <el-tree class="elTree" ref="treeRef" :data="treeData" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="(data) => checkedNode = data">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label" :style="{width: node.level == 1 ? '151px' : node.level == 2 ? '133px' : '115px'}">{{ node.label }}</span>
                            <span v-if="data.Level>0" class="node-btn-area">
                                <el-dropdown trigger="click" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown" v-if="btnAddChildren=='btnAddChildren'">
                                        <el-dropdown-item v-show="node.level < 3" command="create">添加下级</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="update">修改名称</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :layoutMode='layoutMode' :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabData" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="tableLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :isShowConditionArea='true' :startOfTable="startOfTable">

                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'80px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="handleResetSearch" :layoutMode='layoutMode'>
                                <template slot="Name">
                                        <el-input style="width: 100%;" 
                                            placeholder="搜索人员姓名..."
                                            @clear='handleFilter'
                                            v-antiShake='{
                                                time: 300,
                                                callback: () => {
                                                    handleFilter()
                                                }
                                            }' 
                                            clearable 
                                            v-model="listQuery.Name"
                                        ></el-input>
                                    </template>
                                <template slot="Number">
                                    <el-input style="width: 100%;" v-model="listQuery.Number"></el-input>
                                </template>

                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <div class="btns-wrapper">
                                        <!-- <permission-btn moduleName="position" v-on:btn-event="onBtnClicked"></permission-btn> -->
                                        <!-- <el-button v-if="btnAdd=='btnAdd'" type="primary" class="elButton" @click="handleAdd()">添加人员</el-button> -->

                                        <emp-selector
                                            v-if="btnAdd=='btnAdd'"
                                            key="ccusers"
                                            :showType="2"
                                            :multiple="true"
                                            :list="pers"
                                            @change="handleChangeUsers"
                                            :beforeOpen='handleAdd'
                                            :beforeConfirm='saveDatas'
                                            :condition="{SourceType:2}"
                                            >
                                            <el-button style="margin-left: 4px;" slot="reference" type="primary">添加人员</el-button>
                                        </emp-selector>
                                        <el-button v-if="btnEdit=='btnEdit'" type="primary" class="elButton" @click="handleEdit()">调整人员</el-button>
                                    </div>
                                </template>

                            </app-table-form>
                        </template>


                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" @click="handleDelete(scope.row)" text="移除" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>
    <!--弹窗组件区-->
    <customerServiceDepartmentEdit v-if="customerServiceDepartmentEditDialogFormVisible" :dialogStatus="customerServiceDepartmentEditDialogStatus" :node="paramNode" :dialogFormVisible="customerServiceDepartmentEditDialogFormVisible" @closeDialog="customerServiceDepartmentEditCloseDialog" @saveSuccess="customerServiceDepartmentEditSaveSuccess"></customerServiceDepartmentEdit>
    <customerServiceManageEdit v-if="customerServiceManageEditDialogFormVisible" :dialogStatus="customerServiceManageEditDialogStatus" :customerServiceManageIds="customerServiceManageIds" :dialogFormVisible="customerServiceManageEditDialogFormVisible" @closeDialog="customerServiceManageEditCloseDialog" @saveSuccess="customerServiceManageEditSaveSuccess"></customerServiceManageEdit>
    <!-- <el-dialog width="1000px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini" ref="accessUserDlg" v-el-drag-dialog :title="'添加员工'" :visible.sync="dialogAccessUsers" :append-to-body="true">
        <emp-table ref="accessUser" v-bind="$attrs" :existsUsers="pers" :condition="{SourceType:2}" v-if="dialogAccessUsers" v-show="dialogAccessUsers" @changed="handleChangeUsers"></emp-table>
    </el-dialog> -->
</div>
</template>

<!--组件脚本区-->

<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import * as customerServiceManage from "@/api/afterSalesMgmt/customerServiceManage";
import elDragDialog from "@/directive/el-dragDialog";
// import EmpTable from "@/views/common/empTable";
import empSelector from '@/views/common/empSelector'
import customerServiceDepartmentEdit from "./departmentEdit";
import customerServiceManageEdit from "./edit";
import {
    listToTreeSelect
} from "@/utils";

export default {
    /**名称 */
    name: "customer-service-manage-index",
    mixins: [indexPageMixin],
    directives: {
        elDragDialog
    },
    /**组件声明 */
    components: {
        customerServiceDepartmentEdit,
        customerServiceManageEdit,
        // EmpTable,
        empSelector,
    },
    /**参数区 */
    props: {
        /**主键Id */
        keyId: {
            type: String
        }
    },
    /**数据区 */
    data() {
        return {
            layoutMode: 'simple',
            /******************* 树 *******************/
            tableSearchItems: [{
                    prop: "Name",
                    label: "人员姓名",
                    mainCondition: true
                },
                {
                    prop: "Number",
                    label: "工号"
                },
            ],
            btnAddClassification: '',
            btnAdd: '',
            btnEdit: '',
            btnAddChildren: '',
            /**树筛选内容 */
            filterText: "",
            /**树数据 */
            treeData: [],
            treeLoading: false,
            /**树默认结构 */
            defaultProps: {
                children: "children",
                label: "Name"
            },
            /**树选中节点 */
            checkedNode: null,
            /**树参数 */
            paramNode: {
                Id: "",
                Name: "",
                Level: 1
            },
            /******************* 表格 *******************/
            /**查询条件 */
            listQuery: {
                PageIndex: 1,
                PageSize: 20,
                CustomerServiceDepartmentId: null,
                Name: '',
                Number: '',
            },
            /**表格列声明 */
            tabColumns: [{
                    attr: {
                        prop: "Name",
                        label: "人员名称"
                    }
                },
                {
                    attr: {
                        prop: "Number",
                        label: "工号",
                    }
                },
                {
                    attr: {
                        prop: "Phone",
                        label: "手机",
                    }
                },
                {
                    attr: {
                        prop: "OrgName",
                        label: "所属部门",
                    }
                },
                {
                    attr: {
                        prop: "CustomerServiceDepartmentName",
                        label: "负责地区",
                    }
                }
            ] /**表格加载 */ ,
            tableLoading: false,
            /**表格数据 */
            tabData: [],
            /**表格选中行 */
            multipleSelection: [],
            /**分页数量 */
            total: 0,
            /******************* 组件 *******************/
            /**树节点添加弹窗 */
            customerServiceDepartmentEditDialogFormVisible: false,
            customerServiceDepartmentEditDialogStatus: "create",
            /**人员调整弹窗 */
            customerServiceManageEditDialogFormVisible: false,
            customerServiceManageEditDialogStatus: "create",
            /**员工添加弹窗 */
            //dialogAccessUsers: false,
            /**选择客服 */
            customerServiceManageIds: [],
            pers: []
        };
    },
    /**计算属性---响应式依赖 */
    computed: {},
    /**监听 */
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.Id = val.Id;
                    this.listQuery.PageIndex = 1;
                    this.loadTableData();
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.btnTextValue()
    },
    /**渲染后 */
    mounted() {
        this.loadTreeData();
    },
    /**方法区 */
    methods: {
        btnTextValue() {
            let btns = this.topBtns
            btns.forEach(item => {
                if (item["DomId"] == "btnAddClassification") {
                    this.btnAddClassification = "btnAddClassification"
                }
                if (item["DomId"] == "btnAddChildren") {
                    this.btnAddChildren = "btnAddChildren"
                }
                if (item["DomId"] == "btnAdd") {
                    this.btnAdd = "btnAdd"
                }
                if (item["DomId"] == "btnEdit") {
                    this.btnEdit = "btnEdit"
                }
            })
        },
        /******************* 树事件 *******************/
        loadTreeData() {

            let _this = this;
            let paramData = {};
            _this.treeLoading = true
            customerServiceManage
                .getCustomerServiceDepartmentListByCondition(paramData)
                .then(response => {
                    _this.treeLoading = false
                    response.unshift({
                        Id: "",
                        Name: "全部",
                        Level: 0,
                        ParentId: null
                    });
                    _this.treeData = listToTreeSelect(response);

                    if (_this.treeData && _this.treeData.length > 0) {
                        if (
                            !(
                                _this.checkedNode &&
                                response.find(t => {
                                    return t.Id == _this.checkedNode.Id;
                                })
                            )
                        ) {
                            _this.checkedNode = _this.treeData[0];
                        }
                    } else {
                        _this.checkedNode = null;
                    }
                    if (_this.checkedNode) {
                        _this.$nextTick(() => {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        });
                    }
                }).catch(err => {
                    _this.treeLoading = false
                });
        },
        /**添加顶级节点 */
        addTopLevel() {
            this.paramNode = {
                Id: null,
                Name: "",
                Level: 0
            };
            this.customerServiceDepartmentEditDialogStatus = "create";
            this.customerServiceDepartmentEditDialogFormVisible = true;
        },
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.Name.indexOf(value) !== -1;
        },
        /**树下拉事件 */
        handleCommand(optType, node, data) {
            switch (optType) {
                case "create":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "create";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "update":
                    this.paramNode = data;
                    this.customerServiceDepartmentEditDialogStatus = "update";
                    this.customerServiceDepartmentEditDialogFormVisible = true;
                    break;
                case "delete":
                    this.handleDeleteDepartment(data);
                    break;
                default:
                    break;
            }
        },
        /**删除树节点 */
        handleDeleteDepartment(data) {
            if (data.children && data.children.length > 0) {
                this.$notify({
                    title: "提示",
                    message: "请先删除子级",
                    type: "error",
                    duration: 2000
                });
                return;
            }
            this.$confirm(`是否确认删除${data.Name}所包含的所有员工吗?`, "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                customerServiceManage
                    .deleteCustomerServiceDepartment([data.Id])
                    .then(res => {
                        if (this.checkedNode && this.checkedNode.Id == data.Id) {
                            this.checkedNode = null;
                        }
                        this.loadTreeData();
                        this.$notify({
                            title: "成功",
                            message: "删除成功",
                            type: "success",
                            duration: 2000
                        });
                    });
            });
        },
        /******************* 表格事件 *******************/
        /**加载表格数据 */
        loadTableData() {
            let _this = this;
            if (_this.checkedNode.Id) {
                _this.listQuery.CustomerServiceDepartmentId = _this.checkedNode.Id;
            } else {
                delete this.listQuery.CustomerServiceDepartmentId;
            }
            _this.tableLoading = true
            customerServiceManage
                .getCustomerServiceManageListPage(_this.listQuery)
                .then(response => {
                    _this.tableLoading = false
                    _this.total = response.Total;
                    _this.tabData = response.Items;
                }).catch(err => {
                    _this.tableLoading = false
                });
        },
        /**表头部点击 */
        // onBtnClicked: function (type) {
        //     switch (type) {
        //         //添加
        //         case "btnAdd":
        //             if (!this.checkedNode.Id) {
        //                 this.$message({
        //                     message: "该节点不可操作",
        //                     type: "error"
        //                 });
        //                 return;
        //             }
        //             this.dialogAccessUsers = true;
        //             break;
        //             //批量调整
        //         case "btnEdit":
        //             if (this.multipleSelection.length < 1) {
        //                 this.$message({
        //                     message: "至少选中一个",
        //                     type: "error"
        //                 });
        //                 return;
        //             }
        //             this.customerServiceManageEditDialogFormVisible = true;
        //             break;
        //         default:
        //             break;
        //     }
        // },
        /**表格行选中 */
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
            this.customerServiceManageIds = rows.map(t => t.Id);
        },
        handleEdit() {
            if (this.multipleSelection.length < 1) {
                this.$message({
                    message: "至少选中一个",
                    type: "error"
                });
                return;
            }
            this.customerServiceManageEditDialogFormVisible = true;
        },
        handleAdd() {
            if (!this.checkedNode.Id) {
                this.$message({
                    message: "该节点不可操作",
                    type: "error"
                });
                return false;
            }
            return true
            //this.dialogAccessUsers = true;
        },
        /**表格行删除 */
        handleDelete(rows) {
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }

            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                customerServiceManage.deleteCustomerServiceManage(ids).then(res => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.loadTableData();
                });
            });
        },
        /**分页页大小变更 */
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.loadTableData();
        },
        /**分页页码变更 */
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.loadTableData();
        },
        /******************* 弹窗 *******************/
        /**人员变更 */
        handleChangeUsers(users) {
            // this.pers = users;
            // let paramData = users.map(t => {
            //     t.CustomerServiceDepartmentId = this.checkedNode.Id;
            //     return t;
            // });
            // await customerServiceManage
            //     .addCustomerServiceManageList(paramData)
            //     .then(response => {
            //         this.$notify({
            //             title: "提示",
            //             message: "保存成功",
            //             type: "success",
            //             duration: 2000
            //         });
            //         this.dialogAccessUsers = false;
            //         this.loadTableData();
            //     });
            this.pers = []
            this.loadTableData();
            this.$notify({
                title: "提示",
                message: "保存成功",
                type: "success",
                duration: 2000
            });
        },
        async saveDatas(users) {
            let paramData = users.map(t => {
                t.CustomerServiceDepartmentId = this.checkedNode.Id;
                return t;
            });
            let result = false
            await customerServiceManage
                .addCustomerServiceManageList(paramData)
                .then(response => {
                    result = true
                    // this.$notify({
                    //     title: "提示",
                    //     message: "保存成功",
                    //     type: "success",
                    //     duration: 2000
                    // });
                    // this.dialogAccessUsers = false;
                    // this.loadTableData();
                })
            return result
        },
        /**地区弹窗保存成功 */
        customerServiceDepartmentEditSaveSuccess() {
            this.loadTreeData();
            this.customerServiceDepartmentEditCloseDialog();
        },
        /**地区弹窗关闭 */
        customerServiceDepartmentEditCloseDialog() {
            this.customerServiceDepartmentEditDialogFormVisible = false;
        },
        /**编辑弹窗保存成功 */
        customerServiceManageEditSaveSuccess() {
            this.listQuery.PageIndex = 1;
            this.loadTableData();
            this.customerServiceManageEditCloseDialog();
        },
        /**编辑弹窗关闭 */
        customerServiceManageEditCloseDialog() {
            this.customerServiceManageEditDialogFormVisible = false;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.loadTableData();
        },
        handleResetSearch() {
            this.listQuery.PageIndex = this.listQuery.PageIndex;
            this.listQuery.PageSize = this.listQuery.PageSize;
            this.listQuery.Name = "";
            this.listQuery.Number = "";
            this.loadTableData() //刷新列表
        },
    }
};
</script>

<!--组件样式区-->

<style lang="scss" scoped>
// @import "../../../../styles/empSelectorDialogCommon.css";
.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;
        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-top: 0;

            // padding-right: 10;
            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }

            .btns-wrapper{
                button{
                    margin-right: 4px;
                }
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}
</style>
