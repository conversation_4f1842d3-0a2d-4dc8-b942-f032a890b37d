<template>
  <div>
    <app-dialog title="公司团建详情" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="600" :destroy-on-close="true" :close-on-press-escape="false">
      <template slot="body">
        <el-form ref="formData" :model="formData" label-position="right" v-loading="formLoading" :label-width="labelWidth" class="newReportForm">

          <el-row style=" margin-bottom: 20px;margin-left: 20px;">
            <el-col :span="8">
              <b>活动时间：</b><label>{{formData.ActivityStartTime | dateFilter('YYYY-MM-DD') }} - {{formData.ActivityEndTime  | dateFilter('YYYY-MM-DD')}}</label>
            </el-col>
          </el-row>

          <el-row style=" margin-bottom: 20px;margin-left: 20px;">
            <el-col :span="24">
              <b>活动标题：</b><label>{{formData.ActivityTitle}}</label>
            </el-col>
          </el-row>

          <el-row style="border-top:1px solid silver;margin-left: 20px;margin-right: 20px;">
          </el-row>

          <el-row style="margin-left: 20px;margin-top: 10px;">
            <el-col :span="24">
              <b>详情内容</b>
            </el-col>
          </el-row>

          <el-row style="padding-left: 5px; margin-bottom: 10px;">
            <el-col :span="24">
              <div class="divUeditor ql-editor" v-html="formData.ActivityContent"></div>
            </el-col>
          </el-row>

          <el-row style="border-top:1px solid silver;margin-left: 20px;margin-right: 20px;">
          </el-row>

          <el-row style="margin-left: 20px;margin-top: 10px;">
            <el-col :span="8">
              <b>活动照片：</b>
            </el-col>
          </el-row>

          <el-row style="margin-bottom: 10px;margin-left: 20px;margin-top: 10px;">
            <el-col :span="24">
              <viewer :images="formData.ActivityPhotoPathList">
                <img v-for="(item, index) in formData.ActivityPhotoPathList" :src="item.src" :key="index" class="viewerImg">
              </viewer>
            </el-col>
          </el-row>

          <!-- 评论区 -->
          <div class="commentInfoClass">
            <b>评论 ({{commentListCount}}) </b>&nbsp;
            <el-button type="primary" size="mini" @click="dialogCommentVisible = true">我要评论</el-button>
            <!-- <a @click="giveLike" :style="{ color: formData.IsTheCurrentUserThumbUp ? 'red' : 'black' }" :title="formData.IsTheCurrentUserThumbUp ? '取消点赞':'点赞'"><i class="el-icon-thumb">&nbsp;{{formData.GiveLikeNum}}</i></a> -->

            <a @click="giveLike" :title="formData.IsTheCurrentUserThumbUp ? '取消点赞':'点赞'">
                <svg-icon :icon-class="formData.IsTheCurrentUserThumbUp ? 'zan-active' : 'zan'"></svg-icon>&nbsp;{{formData.GiveLikeNum}}
              </a>
          </div>

          <br />

          <!-- 评论列表 -->
          <div v-loading="commentLoading">
            <div class="commentListClass" v-for="comment in commentList" :key="comment.id">
              <span>{{comment.CreateEmployeeName}}</span> &nbsp;&nbsp;
              <span>{{comment.CreateTime  | dateFilter('YYYY-MM-DD HH:mm')}}</span>
              <a @click="withdraw(comment)" v-if="IsShowRecall(comment)" title="撤回评论">撤回</a>
              <p style="margin-top: 5px;" v-html="comment.CommentContent"></p>
              <hr />
            </div>
          </div>

        </el-form>
      </template>
      <template slot="footer">
        <app-button @click="handleClose" :buttonType='2'></app-button>
      </template>
    </app-dialog>

    <!-- 我要评论 -->
    <el-dialog title="我要评论" ref="commentDialogRef" :visible.sync="dialogCommentVisible" :close-on-click-modal="false">
      <el-input maxlength="500" type="textarea" :rows="4" placeholder="请输入内容" v-model="commentContent"></el-input>
      <template slot="footer">
        <app-button @click="handleCommentSave" :buttonType='1'></app-button>
        <app-button @click="handleCommentClose" :buttonType='2'></app-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import * as systemLeagueConstruction from '@/api/personnelManagement/systemLeagueConstruction'
import { getUserInfo } from "@/utils/auth";

export default {
  name: "systemLeagueConstruction-Detail",
  directives: {},
  components: {
  },
  mixins: [],
  props: {
    id: {
      type: String,
      default: ""
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
        if (this.id) {
          this.getDetail();
          this.getCommentList();
        }
      }
    },
    "dialogCommentVisible"() {
      this.commentContent = ""
    }
  },
  computed: {
  },

  created() {

  },
  data() {
    return {
      commentLoading: false,
      commentContent: "",//评论内容
      dialogCommentVisible: false,
      commentListCount: 0,
      commentList: [],

      formLoading: false,
      labelWidth: "100px",
      formData: {
        LeagueConstructionId: "",
        ActivityCover: "",
        ActivityCoverPath: "",
        Range: [],
        ActivityStartTime: "",
        ActivityEndTime: "",
        ActivityTitle: "",
        ActivityContent: "",
        ActivityPhotos: "",
        ActivityPhotoPaths: "",
        ActivityPhotoPathList: [],
        GiveLikeNum: 0
      }
    };
  },
  methods: {
    resetFormData() {
      this.commentListCount = 0;
      this.commentList = [];

      let temp = {
        LeagueConstructionId: "",
        ActivityCover: "",
        ActivityCoverPath: "",
        Range: [],
        ActivityStartTime: "",
        ActivityEndTime: "",
        ActivityTitle: "",
        ActivityContent: "",
        ActivityPhotos: "",
        ActivityPhotoPaths: "",
        ActivityPhotoPathList: [],
        GiveLikeNum: 0
      };
      this.formData = Object.assign({}, this.formData, temp);
    },

    //判断是否显示 撤回
    IsShowRecall(comment) {
      return getUserInfo().employeeid == comment.CreateEmployeeId
    },

    //获取详情
    getDetail() {
      this.formLoading = true;
      systemLeagueConstruction.detail({ id: this.id }).then(res => {
        this.formData = Object.assign({}, this.formData, res);
        if (this.formData.ActivityPhotos) {
          this.formData.ActivityPhotos = this.formData.ActivityPhotos.split(',')
        }

        if (this.formData.ActivityPhotoPaths) {
          var photoPathList = this.formData.ActivityPhotoPaths.split(',')

          var imgList = [];
          photoPathList.forEach(element => {
            var imgObj = {
              msrc: element,
              src: element,
            };
            imgList.push(imgObj);
          });

          this.formData.ActivityPhotoPathList = imgList;
        }

        this.formLoading = false;
        // if (res != null) {
        //   this.getCommentList();
        // }
      });
    },

    //获取评论集合
    getCommentList() {
      this.commentLoading = true;
      systemLeagueConstruction.getComment({ businessId: this.id }).then(res => {
        this.commentList = res
        this.commentListCount = res.length;
        this.commentLoading = false;
      })
    },

    //点赞 or 撤回
    giveLike() {
      systemLeagueConstruction.setGiveLike({ businessId: this.id }).then(res => {
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
        this.getDetail();
      })
    },

    //撤回评论
    withdraw(comment) {
      systemLeagueConstruction.recall({ businessId: comment.Id }).then(res => {
        this.$notify({
          title: '成功',
          message: '撤回成功',
          type: 'success',
          duration: 2000
        })
        this.getCommentList();
      })
    },

    //提交评论
    handleCommentSave() {
      if (this.commentContent.trim() == "") {
        this.$message.error('请输入评论内容');
        return;
      }
      var obj = {
        CurrentBusinessId: this.id,
        CommentContent: this.commentContent,
        Type: 13,
      };

      systemLeagueConstruction.addComment(obj).then(() => {
        this.$notify({
          title: '成功',
          message: '评论成功',
          type: 'success',
          duration: 2000
        })
        this.commentContent = ""
        this.dialogCommentVisible = false
        this.getCommentList();
      })
    },

    handleCommentClose() {
      this.$refs.commentDialogRef.handleClose();
    },

    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>


<style>
.viewer-backdrop {
  background-color: rgba(0, 0, 0, 1);
}
.viewerImg {
  height: auto;
  width: 100px;
  margin-left: 10px;
}
</style>

<style lang="scss" scoped>
.newReportForm {
  label {
    font-weight: 100 !important;
  }
}

.divUeditor {
  overflow: hidden;
  display: block;
  width: 100%;
  min-width: 90%;
  position: relative;
  word-wrap: break-word;
  padding-bottom: 20px;
}

.divUeditor img {
  border: 0;
  max-width: 100%;
  margin-top: 10px;
}

.wrapper {
  display: flex;
  .left {
    flex: 1;
    padding-right: 14px;
  }
  .right {
    width: 40%;
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}

.commentInfoClass {
  margin-left: 20px;
  border-top: 1px solid silver;
  border-bottom: 1px solid silver;
  // height: 40px;
  line-height: 40px;
  // width: 100%;
  b {
    font-size: 15px;
    float: left;
  }
  a {
    float: right;
    margin-right: 15px;
    display: flex;
    align-items: center;
  }
}

.commentListClass {
  margin-left: 20px;
  margin-right: 20px;
  p {
    word-wrap: break-word;
    word-break: break-all;
  }
  hr {
    background-color: #e1e1e1;
    height: 1px;
    border: none;
  }
  a {
    color: red;
    float: right;
    margin-right: 15px;
  }
}
</style>
