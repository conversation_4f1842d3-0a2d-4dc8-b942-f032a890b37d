<template>
    <div>
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="80px">
                    <div class="wrapper" v-loading='loading'>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="物料编码">
                                    <span>{{formData.MaterialCode}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="物料名称">
                                  <span>{{formData.MaterialName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="规格型号">
                                    <span>{{formData.Specifications}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="当前仓库">
                                   <span>{{formData.MaterialStockName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                            <el-col :span="12">
                                <el-form-item label="领料人">
                                    <span>{{formData.MaterialEmployeeList | nameFilter}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="当前数量">
                                    <span>{{formData.MaterialCount}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                             <el-col :span="24">
                                <el-form-item label="退料数量" >
                                    <el-input-number v-model="formData.MaterialReturnCount" :min="1" :max="formData.MaterialCount" label="描述文字"></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="备注">
                                    <el-input type="textarea" :rows="6" maxlength="500" v-model="formData.Remark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </div>
                </el-form>
            </template>

            <template slot="footer">
        
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>

import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";

  export default {
    name: "material-return-create",
    computed: {
        title() {
            if(this.dialogStatus == 'create') {
                return '退料'
            }else if(this.dialogStatus == 'update') {
                return '编辑退料'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail";
        },
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                }
            },
        }

    },
    filters: {

         nameFilter(list){
            if(list && list.length){
              return list.map(v => v.Name).toString()      
            }
            return "无";     
        },
     
    },
    created() {
        this.rules = this.initRules(this.rules)
        this.getDetail()
    },
    data() {
        return {
            disabledBtn: false,
            rules: {
            },
            loading: false,
            formData: {
                MaterialReturnCount : 1,
            },
        };
    },
    methods: {
        resetFormData() {
            this.formData = {
                MaterialReturnCount : 1,
            }
        },

        getDetail(){
             materialTransferApi.getPersonalDetails({'id':this.id})
             .then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.loading = false       
              }).catch(err => {
                this.loading = false         
              });
        },

        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    postData.MaterialInStockName = postData.MaterialStockName
                    postData.MaterialInStock = postData.MaterialStock
                    this.disabledBtn = true
                    if (this.dialogStatus == "create") {
                        materialTransferApi.addReturnMaterial(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        });
                    }
        
                } 
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },   
    }
};
</script>
