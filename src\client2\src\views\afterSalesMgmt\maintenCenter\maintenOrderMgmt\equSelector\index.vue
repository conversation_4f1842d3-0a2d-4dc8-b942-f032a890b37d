<template>
    <div>
        <div class="btn-wrapper">
            <el-button :disabled="isDistabled" type="text" v-if="!readonly" style="padding: 7px 12px;" @click="() => listSelectorDialogFormVisible = true">添加设备</el-button>
        </div>


        <!-- <el-dialog width="895px" class="dialog_wrapper" :close-on-click-modal="false" close="dialog-mini"
            ref="accessUserDlg" v-el-drag-dialog :title="'添加设备'" :visible.sync='listSelectorDialogFormVisible'
            :append-to-body='true'>
            <equ-table ref="accessUser" v-bind="$attrs" :existsUsers='listSelectorCheckedData' v-if="listSelectorDialogFormVisible" 
                v-show="listSelectorDialogFormVisible" @changed='listSelectorSaveSuccess'></equ-table>
        </el-dialog> -->

        <!-- 报修单、订单 弹框 -->
        <list-selector
            :checkedData="listSelectorCheckedData"
            :getListUrl="listSelectorUrl"
            :multiple="listSelectorMultiple"
            :pageTitle="listSelectorTitle"
            :topMessage="'已选中安装地区：' + path"
            :path="path"
            :addDevice='addDevice'
            :selectKeyName="listSelectorKeyName"
            :condition="listQueryParams"
            :columnData="listSelectorColumnData"
            :dialogFormVisible="listSelectorDialogFormVisible"
            @closeDialog="listSelectorCloseDialog"
            @saveSuccess="listSelectorSaveSuccess"
            @handleOpenAdd="handleOpenAdd"
            ref="listSelector"
        >
            <template slot="conditionArea">
                <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
                    <template slot='Code'>
                        <el-input style="width: 100%;" v-model="listQueryParams.Code" placeholder=""></el-input>
                    </template>
                    <template slot='Name'>
                        <el-input style="width: 100%;" v-model="listQueryParams.Name" placeholder=""></el-input>
                    </template>
                    <template slot='OrderNumber'>
                        <el-input style="width: 100%;" v-model="listQueryParams.OrderNumber" placeholder=""></el-input>
                    </template>
                </app-table-form>
            </template>
            <template slot="IsWarranty" slot-scope="scope">
                {{ scope.row.IsWarranty == 1 ? "否" : (scope.row.IsWarranty == 2 ? '是' : (scope.row.IsWarranty == 3 ? '是(未知有效期)' : '过保')) }}
            </template>
            <template slot="InstallTime" slot-scope="scope">
                <!-- {{scope.row.InstallTime | dateFilter('YYYY-MM-DD HH:mm:ss')}} -->
                {{ scope.row.InstallTime ? (scope.row.InstallTime.split(' ')[0]) : '无' }}
            </template>
            <template slot="HeatNumber" slot-scope="scope">
                {{ scope.row.HeatNumber ? scope.row.HeatNumber : '无' }}
            </template>
            <template slot="EquipmentWorkModeName" slot-scope="scope">
                {{ scope.row.EquipmentWorkModeName ? scope.row.EquipmentWorkModeName : '无' }}
            </template>
            <template slot="BurnerModel" slot-scope="scope">
                {{ scope.row.BurnerModel ? scope.row.BurnerModel : '无' }}
            </template>
            <template slot="Manufacturer" slot-scope="scope">
                {{ scope.row.Manufacturer ? scope.row.Manufacturer : '无' }}
            </template>
            <template slot='op' slot-scope='scope'>
                <el-button type='text' v-show="haveBtnPermission('/afterSalesMgmt/businessMap', 'btnChange')" @click='handleChangeDialog(scope.row)'>编辑</el-button>
            </template>
            <!-- <template slot="ReportEmployee" slot-scope="scope">
                <span v-if="scope.row.ReportEmployee && scope.row.ReportEmployee.length > 0">{{ scope.row.ReportEmployee.map(s => s.Name) }}</span>
                <span v-else>无</span>
            </template> -->
        </list-selector>
        <create-page
        v-if="dialogFormAddVisible"
        @closeDialog="closeDialog"
        @saveSuccess="handleSaveSuccess"
        :dialogFormVisible="dialogFormAddVisible"
        :dialogStatus="dialogStatus"
        :regionalName='condition.RegionalName'
        :regionalId='condition.RegionalId'
        :regionalLevel="10"
        ></create-page>
        <change-page
        v-if="currentRow && dialogChangeFormVisible"
        @closeDialog="closeChangeDialog"
        @saveSuccess="handleChangeSaveSuccess"
        :dialogFormVisible="dialogChangeFormVisible"
        :dialogStatus="dialogChangeStatus"
        :id="currentRow.Id"
        :isShowHistories="isShowHistories"
        ></change-page>
    </div>
</template>

<script>
import changePage from "../../../businessMap/change";
import elDragDialog from '@/directive/el-dragDialog'
import { vars } from "../../common/vars"
// import equTable from './equTable'
import listSelector from '../../../../common/listSelector'
import { serviceArea } from "@/api/serviceArea"
import createPage from "../../../businessMap/create";

export default {
    name: 'relation-orders',
    components: {
        // equTable,
        listSelector,
        createPage,
        changePage,
    },
    directives: {
        elDragDialog,
    },
    filters: {
    },
    props: {
        //已存在的人员
        list: {
            type: Array,
            default: () => {
                return []
            },
        },
        readonly: {
            type: Boolean,
            default: false
        },
        isDistabled: {
            type: Boolean,
            default: false
        },
        path: {
            type: String,
            default: ''
        },
        condition: {
            type: Object,
            default: null
        },
        addDevice:{
            type:Boolean,
            default:false
        }
    },
    watch: {
        list: {
            handler(val) {
                this.listSelectorCheckedData = JSON.parse(JSON.stringify(val))
            },
            immediate: true
        },
        condition(val) {
            if(val) {
                this.listQueryParams = Object.assign({}, this.listQueryParams, val)
            }
        },
    },
    data() {
        return {
            isShowHistories:false,
            dialogChangeFormVisible:false,
            currentRow: null,
            dialogChangeStatus:'',
            dialogStatus:'create',
            dialogFormAddVisible:false,
            listSelectorCheckedData: [],
            listSelectorUrl: serviceArea.business + "/OrderEquipment/GetListPage",
            listSelectorMultiple: true,
            listQueryParams: {
                Code: '',
                Name: '',
                OrderNumber: ''
            },
            tableSearchItems: [
                { prop: "Code", label: "设备编号" },
                { prop: "Name", label: "加热炉/锅炉" },
                { prop: "OrderNumber", label: "订单编号" },
            ],
            listSelectorTitle: "添加设备",
            listSelectorKeyName: "Id",
            listSelectorColumnData: [
                {
                    attr: { prop: "Code", label: "设备编号" },
                },
                {
                    attr: { prop: "Name", label: "加热炉/锅炉" },
                },
                {
                    attr: { prop: "HeatNumber", label: "炉号" },
                    slot:true
                },
                {
                    attr: { prop: "EquipmentWorkModeName", label: "供风方式" },
                    slot: true
                },
                {
                    attr: { prop: "IsWarranty", label: "是否在保" },
                    slot: true
                },
                {
                    attr: { prop: "BurnerModel", label: "燃烧器型号" },
                    slot: true
                },
                {
                    attr: { prop: "Manufacturer", label: "生产厂家" },
                    slot: true
                },
                // {
                //     attr: { prop: "OrderNumber", label: "订单编号" },
                // },
                {
                    attr: { prop: "InstallTime", label: "投产时间" },
                    slot: true
                },
                {
                    attr: { prop: "op", label: '操作' },
                    slot: true
                },
                // {
                //     attr: { prop: "RepairCount", label: "报修次数" },
                // },
            ],
            listSelectorDialogFormVisible: false

        }
    },
    methods: {
        closeChangeDialog() {
            this.dialogChangeFormVisible = false;
        },
        handleChangeSaveSuccess() {
            this.$refs.listSelector.getList();
            this.closeChangeDialog();
        },
        handleChangeDialog(row) {
            this.currentRow = row;
            this.dialogChangeStatus = "create";
            this.isShowHistories = false;
            this.dialogChangeFormVisible = true;
        },
        handleOpenAdd(){
            this.dialogFormAddVisible=true;
        },
        closeDialog(){
            this.dialogFormAddVisible=false;
        },
        handleSaveSuccess(){
            this.$refs.listSelector.getDatas();
            this.dialogFormAddVisible=false;
        },
        handleRemove(idx) {
            this.listSelectorCheckedData.splice(idx, 1)
            this.usersChanged()
        },
        listSelectorCloseDialog() {
            this.listSelectorDialogFormVisible = false
            this.$emit('close', this.listSelectorDialogFormVisible)
        },
        listSelectorSaveSuccess(data) {
            this.listSelectorCheckedData = data
            this.usersChanged()
            this.listSelectorCloseDialog()
        },
        usersChanged() {
            this.$emit("change", this.listSelectorCheckedData)
        },
        handleClear() {
            this.listSelectorCheckedData = []
            this.usersChanged();
        },
        handleShow() {
            this.dialogAccessUsers = true
        },
        handleDel(row, index) {
            this.listSelectorCheckedData.splice(index, 1)
            this.usersChanged()
        },
        handleFilter() {
            this.$refs.listSelector.getDatas()
        },
        onResetSearch() {
            this.listQueryParams.Code = ''
            this.listQueryParams.Name = ''
            this.listQueryParams.OrderNumber = ''
            this.handleFilter()
        },
    }
}
</script>

<style lang="css" scoped>
.btn-wrapper >>> button{
    padding-top: 0!important;
    padding-bottom: 0!important;
}
</style>
