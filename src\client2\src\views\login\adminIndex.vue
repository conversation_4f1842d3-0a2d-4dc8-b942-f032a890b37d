<template>
  <div class="wrap">
    <div class="reg-form-wrap">
      <h3 class="title">{{ websiteTitle }}</h3>
      <h4 class="sub-title">欢迎管理员登录</h4>
      <div class="form-wrap">
        <el-form :rules="rules" ref="dataForm" :model="loginForm" label-position="top" label-width="100px">
          <el-form-item size="small" :label="'用户名'" prop="username">
            <el-input v-model.trim="loginForm.username" ref="username" :disabled="true" type="text"></el-input>
          </el-form-item>
          <el-form-item size="small" :label="'密码'" prop="password">
            <el-input v-model.trim="loginForm.password" @keyup.enter.native="handleLogin" ref="password" type="password"></el-input>
          </el-form-item>
        </el-form>
        <div class="btns">
          <el-button type="primary" class="btn-login" :loading="postLoading" @click="handleLogin">登 录</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { websitEnv } from "@/utils/env";
import { setCookie } from "@/utils/auth";
import { getUserInfo } from "@/utils/auth";
import indexPageMixin from "@/mixins/indexPage";
import * as emps from "@/api/emp";
import * as ent from "@/api/enterprise";

export default {
  name: "adminlogin",
  mixins: [indexPageMixin],
  data() {
    return {
      accountKey: "account",
      websiteTitle: websitEnv.websiteTitle,
      loginForm: {
        username: "admin",
        password: "",
        enterpriseCode: "ESN001"
      },
      rules: {
        username: { fieldName: "用户名", rules: [{ required: true }] },
        password: { fieldName: "密码", rules: [{ required: true }] }
      },
      postLoading: false,
      pwdType: "password"
    };
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  mounted() { },
  methods: {
    showPwd() {
      if (this.pwdType === "password") {
        this.pwdType = "";
      } else {
        this.pwdType = "password";
      }
    },
    handleLogin() {
      this.postLoading = true;
      this.$refs["dataForm"].validate(valid => {
        if (!valid) {
          this.postLoading = false;
        }

        if (valid) {
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.postLoading = false;

              //登录成功，如果是员工，则获取员工信息
              let currentUser = getUserInfo();
              if (currentUser.employeeid) {
                emps.detail({ id: currentUser.employeeid }).then(res => {
                  currentUser.empName = res.Name;
                  currentUser.empNumber = res.Number;
                  currentUser.empAvatar = res.Avatar;
                  this.$store.commit("SET_USERINFO", currentUser);
                });
              }
              this.$router.push({ path: "/" });
            })
            .catch(() => {
              this.postLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style src="./login-and-reg.css" scoped></style>
<style scoped>
.wrap {
  margin-top: 120px;
}

.form-wrap >>> .el-form-item__label:before {
  content: "" !important;
}

.btns {
  margin-top: 60px;
}
</style>
