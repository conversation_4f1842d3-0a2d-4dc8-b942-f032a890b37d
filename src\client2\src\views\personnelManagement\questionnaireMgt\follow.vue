<!--跟进问卷-->
<template>
<div class="dialogMain">
    <app-dialog title="跟进问卷" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1100">
        <template slot="body">
            <div class="pageWarp" v-loading="loading">
                <div class="opl tabsBox">
                    <div class="tabsBox_head" :class="{'active':pageTabTypes=='00000000-0000-0000-0000-000000000000'}" @click="pageTabTypes='00000000-0000-0000-0000-000000000000'">整体进度看板</div>
                    <div class="tabsBox_main">
                        <div class="tabsBox_item" :class="{'active':pageTabTypes==item.Id}" @click="pageTabTypes=item.Id"
                        v-for="item in formData.SurveyGroupList" :key="item.Id">{{item.Name}}</div>
                    </div>
                </div>
                <div class="opr">
                    <el-row class="oprWarp __dynamicTabContentWrapper" v-show="pageTabTypes=='00000000-0000-0000-0000-000000000000'">
                        <el-form label-width="85px">
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="标题名称">
                                        <div class="omit tips_title" :title="formData.Name">{{formData.Name}}</div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="负责人">
                                        <div class="omit tips_title" :title="formData.PrincipalEmployeeList.length>0?formData.PrincipalEmployeeList.map(s=>s.Name).toString():''">
                                            {{formData.PrincipalEmployeeList.length>0?formData.PrincipalEmployeeList.map(s=>s.Name).toString():'无'}}
                                        </div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="截止时间">{{formData.DeadlineTime | dateFilter('YYYY-MM-DD')}}</el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="当前状态">
                                        <span class="item-status" v-if="formData.SurveyState"
                                            :style="{backgroundColor: getSurveyStateObj(formData.SurveyState).bgColor,color: getSurveyStateObj(formData.SurveyState).color}"
                                        >{{ getSurveyStateObj(formData.SurveyState).label }}</span>
                                        <template v-else>无</template>
                                        <el-popover v-model="setSurveyStateVisible" placement="bottom-start" width="170" trigger="click">
                                            <div>
                                                <el-button type="success" plain  @click="setSurveyState(1)">进行中</el-button>
                                                <el-button type="primary" plain  @click="setSurveyState(2)">已完成</el-button>
                                            </div>
                                            <el-button slot="reference" type="text">修改状态</el-button>
                                        </el-popover>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <div class="__dynamicTabWrapper">
                            <el-table
                                fit
                                :data="formData.SurveyGroupList"
                                style="width: 100%"
                                height="100%"
                                :header-cell-style="{'text-align':'left'}"
                                highlight-current-row row-key="Id"
                                ref="mainTable"
                            >
                                <el-table-column type="index" label="序号" width="50"></el-table-column>
                                <el-table-column label="组别名称" prop="Name" showOverflowTooltip></el-table-column>
                                <el-table-column label="参与人数" prop="Total" sortable></el-table-column>
                                <el-table-column label="已提交" prop="Submitted" sortable></el-table-column>
                                <el-table-column label="未提交" prop="NotSubmitted" sortable></el-table-column>
                                <el-table-column label="进度" prop="Progress" sortable width="150">
                                    <template slot-scope="scope">
                                        <el-progress :percentage="scope.row.Progress" :color="customColorMethod"></el-progress>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="60">
                                    <template slot-scope="scope">
                                        <el-button type="text" @click="handelProgressDetail(scope.row)">详情</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-row>
                    <el-row class="oprWarp __dynamicTabContentWrapper" v-show="pageTabTypes!='00000000-0000-0000-0000-000000000000'">
                        <div style="height: 39px;padding-top: 5px;padding-left: 5px;border-bottom: 1px solid #dcdfe6;">
                            <el-input style="width: 50%;" @clear="getTableDetail" v-antiShake='{time: 300,callback: () => {getTableDetail()}}' clearable
                            v-model.trim="tableListQuery.KeyWords" placeholder="搜索调查对象"></el-input>
                        </div>
                        <div class="__dynamicTabWrapper">
                            <el-table
                                fit
                                :data="surveyGroupData"
                                style="width: 100%"
                                height="100%" v-loading="tableLoading"
                                :header-cell-style="{'text-align':'left'}"
                                highlight-current-row row-key="Id"
                                ref="mainTable"
                            >
                                <el-table-column label="调查对象" prop="Name" :width="120">
                                    <template slot-scope="scope">
                                        <div style="line-height: 40px;" v-viewer>
                                            <img class="tabsBox_item_userPhoto" :src="scope.row.AvatarPath||require('../../../assets/images/avatar3.png')" />
                                            <span>{{scope.row.Name}}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="得分情况" prop="Total" showOverflowTooltip>
                                    <template slot-scope="scope">
                                        {{formatTotalScore(scope.row)}}
                                    </template>
                                </el-table-column>
                                <el-table-column label="总得分" prop="TotalScore" :width="90"></el-table-column>
                                <el-table-column label="操作" width="60">
                                    <template slot-scope="scope">
                                        <el-button type="text" @click="handelDetail(scope.row)">详情</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-row>
                </div>
            </div>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" text="关闭" type></app-button>
        </template>
    </app-dialog>
    <!-- 得分详情 -->
    <answer-detail v-if="createAnswerDetailVisible" :node="AnswerDetaNode" :dialogFormVisible="createAnswerDetailVisible"
    @closeDialog="createAnswerDetailVisible=false"></answer-detail>
    <!-- 组别详情 -->
    <progress-detail v-if="createProgressDetailVisible" :node="AnswerDetaNode" :dialogFormVisible="createProgressDetailVisible"
    @closeDialog="createProgressDetailVisible=false"></progress-detail>

    
</div>
</template>

<script>
import * as SurveyApi from '@/api/personnelManagement/survey.js'
import { QuestionAnswerType, SurveyStateEnum } from "../enum.js";
import answerDetail from "./answerDetail";
import progressDetail from "./progressDetail";

export default {
    name: "questionnaireMgt-follow",
    components: {
        answerDetail,
        progressDetail
    },
    props: {
        id: {
            type: String,
            default: "",
        },
    },
    filters: {
        SurveyTypeFilter(val) {
            let obj = QuestionAnswerType.find(
                s => s.value == val
            );
            if (obj) {
                return obj.label;
            }
            return "无";
        }
    },
    data() {
        return {
            QuestionAnswerType,
            SurveyStateEnum,

            AnswerDetaNode: {},
            createAnswerDetailVisible: false,
            createProgressDetailVisible: false,

            setSurveyStateVisible: false,

            pageTabTypes: '00000000-0000-0000-0000-000000000000',
            tableLoading: false,
            loading: false,
            formData: {
                SurveyGroupList: [],
                PrincipalEmployeeList: [],
            },
            surveyGroupData: [],
            tableListQuery:{
                KeyWords: '',
            }
        };
    },
    computed: {
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getDetail();// 查询 基本信息
                }
            },
            immediate: true
        },
        "pageTabTypes": {
            handler(val) {
                if (val!='00000000-0000-0000-0000-000000000000') {
                    this.getTableDetail();// 查询 得分信息
                }
            },
            immediate: true
        },
        
    },
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    mounted() {},
    methods: {
        // 修改状态 提交
        setSurveyState(Status){
            let self = this;
            self.setSurveyStateVisible = false;
            // EditStatus  1  进行中   2  已完成 
            let str = Status === 1 ? '修改后将开放提交入口是否继续？' : '完成后将关闭提交入口是否继续？';
            self.$confirm(str, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                SurveyApi.EditStatus({
                    Id: self.formData.Id,
                    SurveyState: Status
                }).then(res => {
                    self.$notify({
                        title: "提示",
                        message: "修改成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getDetail();// 查询 基本信息
                    self.$emit('reload')
                });
            }).catch(()=>{});
        },
        // 查看得分详情
        handelDetail(row){
            this.AnswerDetaNode = row;
            this.createAnswerDetailVisible = true
        },
        // 查看组别详情
        handelProgressDetail(row){
            this.AnswerDetaNode = row;
            this.createProgressDetailVisible = true
        },
        
        // 计算得分情况
        formatTotalScore(row){
            if(row.ScoreItemTypeList&&row.ScoreItemTypeList.length>0) {
                let str = '';
                row.ScoreItemTypeList.map((s,i)=>{
                    if(i!=0)str+='；';
                    str += `${QuestionAnswerType.find(q => q.value == s.SubmitItemType).label}（${s.TotalValue}）`
                })
                return str
            }
            return '无'
        },
        // 查询得分信息
        getTableDetail(){
            let self = this;
            self.tableLoading = true;
            SurveyApi.GetSurveyGroupDetailsScore({
                KeyWords: self.tableListQuery.KeyWords,
                Id: self.pageTabTypes
            }).then(res => {
                self.surveyGroupData = res;
                self.tableLoading = false
            }).catch(err => {
                self.tableLoading = false
            });
        },
        // 查询 基本信息
        getDetail() {
            this.isOneLoad = true;
            this.loading = true
            SurveyApi.detail({ id: this.id }).then(res => {
                this.formData = res;
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        getSurveyStateObj(val){
            return this.SurveyStateEnum.find(
                s => s.value == val
            ) || {};
        },
        handleSortChange({ column, prop, order }) {
            this.sortObj = { prop, order, };
            // this.getList();
        },
        customColorMethod(percentage) {
            if (percentage < 100) {
                return '#00cc00';
            } else if (percentage == 100) {
                return '#409EFF';
            } else {
                return '#00cc00';
            }
        }
    }
};
</script>

<style scoped>
.dialogMain >>> .el-dialog__body{
    padding: 0 !important;
}
/* .opr >>> .el-form-item__content,.opr >>> .el-form-item__label{
    font-size: 12px;
} */
</style>
<style lang="scss" scoped>
.tabsBox_item_userPhoto{
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    float: left;
    margin-right: 10px;
    cursor: pointer;
}
.pageWarp{
    width: calc(100% + 20px);
    margin: 0 -10px;
    display: flex;
    height: 500px;
    .opl{
        width: 219px;
        height: 100%;
        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        &.tabsBox{
            height: 500px;
        }
        .tabsBox{
            &_main{
                flex: 1;
                width: 100%;
                max-height: 100%;
                overflow: hidden;
                overflow-y: auto;
            }
            &_item{
                background-color: #fff;
                padding: 5px 20px;
                height: 30px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                cursor: pointer;
                .svg-icon{
                    float: left;
                    margin-right: 7px;
                }
                &.active{
                    color: #409EFF;
                    background-color: #ecf5ff;
                }
                &:hover{
                    background-color: #F5F7FA;
                }
            }
            &_head{
                background-color: #fff;
                padding: 10px 20px;
                height: 39px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                cursor: pointer;
                border-bottom: 1px solid #dcdfe6;
                .svg-icon{
                    float: left;
                    margin-right: 7px;
                }
                &.active{
                    color: #409EFF;
                    background-color: #f0f7ff;
                }
                &:hover{
                    background-color: #F5F7FA;
                }
            }
        }
    }
    .opr{
        flex: 1;
        width: calc(100% - 220px);
        .item-status{
            margin-right: 10px;
            font-size: 12px;
        }
        .oprWarp{
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
    }
}
</style>


