<template>
<div>
    <app-dialog title="报修单列表" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750" :width='1200'>
        <template slot="body">
            <div class="wrapper">
                <app-table
                    ref="mainTable"
                    :tab-columns="tabColumns"
                    :tab-datas="tabDatas"
                    :tab-auth-columns="[]"
                    :isShowAllColumn="true"
                    :loading="listLoading"
                    :isShowOpatColumn="true"
                    :startOfTable="startOfTable"
                    :multable="false"
                    @sortChagned='handleSortChange'
                    fit
                    :isShowBtnsArea='false'
                    :layoutMode='layoutMode'
                >
                    <template slot="ReportTime" slot-scope="scope">{{
                    scope.row.ReportTime | dateFilter("YYYY-MM-DD HH:mm")
                    }}</template>
                    <template slot="ReportEmployee" slot-scope="scope">{{
                    scope.row.ReportEmployee | nameFilter
                    }}</template>
                    <template slot="HandlerEmployeeList" slot-scope="scope">
                    <span v-if="scope.row.HandlerEmployeeList">{{
                        scope.row.HandlerEmployeeList.map(s => s.Name).join(",")
                    }}</span>
                    <span v-else>无</span>
                    </template>

                    <!-- 表格查询条件区域 -->
                    <template slot="conditionArea">
                    
                    <app-table-form
                        :label-width="'100px'"
                        :items="tableSearchItems"
                        @onSearch="handleFilter"
                        @onReset="handleResetSearch"
                        :layoutMode='layoutMode'
                    >
                        <!-- :default-expand-level="3" -->
                        <template slot="Keywords">
                        <div style="display: flex; align-items: center;">
                            <el-input style="width: 100%; flex: 1;" 
                                placeholder="搜索报修单号/记录人/实施人员"
                                @clear='handleFilter'
                                v-antiShake='{
                                    time: 300,
                                    callback: () => {
                                        handleFilter()
                                    }
                                }' 
                                clearable 
                                v-model="listQuery.Keywords"
                            ></el-input>
                        </div>
                        </template>
                    </app-table-form>
                    </template>

                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                    <app-table-row-button @click="handleUpdate(scope.row, 'detail')" :type="2"></app-table-row-button>
                    </template>
                </app-table>

                <pagination
                    :total="total"
                    :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize"
                    @pagination="handleCurrentChange"
                    @size-change="handleSizeChange"
                />
                
            </div>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
        </template>
    </app-dialog>


    <create-page
        v-if="dialogDetailFormVisible"
        @closeDialog="closeDialog"
        :dialogFormVisible="dialogDetailFormVisible"
        dialogStatus="detail"
        :id="detailId"
        :declareNewCases="true"
      ></create-page>
</div>
</template>

<script>
import noData from "@/views/common/components/noData";
import createPage from '../maintenOrderMgmt/create'
import indexPageMixin from "@/mixins/indexPage"
import * as mo from "@/api/maintenanceCenter/maintenOrderMgmt"

export default {
    name: "recored",
    directives: {},
    mixins: [indexPageMixin],
    components: {
        noData,
        createPage,
    },
    filters: {
      nameFilter(creator) {
        if (creator) {
          return creator.Name;
        }
        return "";
      },
    },
    props: {
        condition: {
            type: Object,
            default: null
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if(val) {

                    if(this.condition) {
                        this.listQuery = Object.assign({}, this.listQuery, this.condition)
                    }

                    this.getList()
                }
            },
            immediate: true
        },

    },
    computed: {
        
    },
    created() {
    },
    data() {
        return {
            layoutMode: 'simple',
            listLoading: false,
            tableSearchItems: [
              {
                  prop: "Keywords",
                  label: "",
                  mainCondition: true
              },
            ],
            tabColumns: [
              {
                attr: { prop: "Code", label: "报修编号",width:'125' }
              },
              {
                attr: { prop: "RegionalName", label: "报修地区", showOverflowTooltip: true,width:'350' }
              },
              {
                attr: { prop: "ReportEmployee", label: "记录人员"},
                slot: true
              },
              {
                attr: { prop: "ReportTime", label: "报修时间", sortable: 'custom' ,width:'125'},
                slot: true
              },
              {
                attr: { prop: "HandlerEmployeeList", label: "实施人员"},
                slot: true
              },
            ],
            tabDatas: [],
            total: 0,

            dialogDetailFormVisible: false,
            detailId: '',
        }
    },

    methods: {
        closeDialog() {
          this.dialogDetailFormVisible = false;
        },
        handleUpdate(row) {
          // 弹出编辑框
          this.detailId = row.Id;
          this.dialogDetailFormVisible = true;
        },
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData)
            mo.getListMiniPage(postData).then(res => {
              this.listLoading = false;
              this.tabDatas = res.Items;
              this.total = res.Total;
            });
        },
        handleSortChange({ column, prop, order }) {
          this.sortObj = {prop, order}
          this.getList()
        },
        handleCurrentChange(val) {
          this.listQuery.PageIndex = val.page;
          this.listQuery.PageSize = val.size;
          this.getList();
        },
        handleSizeChange(val) {
          this.listQuery.PageSize = val.size;
          this.getList();
        },
        handleFilter() {
          this.listQuery.PageIndex = 1;
          this.getList();
        },
        handleResetSearch() {
          this.listQuery.Keywords = ''
          this.getList(); //刷新列表
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<style lang="scss" scoped>
.wrapper{
   
   
}
</style>