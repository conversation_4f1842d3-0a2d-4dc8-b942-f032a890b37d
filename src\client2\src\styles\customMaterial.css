
/* .snotify-icon {
  position: absolute !important;
  right: 10px!important;
  top: 19px!important;
  line-height: 0!important;
  -webkit-transform: translate(0, -50%)!important;
  transform: translate(0, -50%)!important;
  max-height: 32px!important;
  max-width: 32px!important;
  width: 100%!important;
  height: 100%!important;
}
.snotifyToast__body {
  width: 270px!important;
  overflow-wrap: break-word!important;
} */

/* .snotify-simple, .snotify-error, .snotify-warning, .snotify-info, .snotify-success{
  border-left: 0!important;
} */

.snotifyToast{
  border: 1px solid rgba(0, 0, 0, 0.1)!important;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  border-radius: 4px;
}

.snotifyToast__title{
  font-weight: 600;
  font-size: 20px!important;
}

.snotifyToast__body{
  font-size: 14px!important;
}

.snotifyToast__inner{
  padding: 5px 15px 5px 38px!important;
}

.snotify-icon{
  width: 20px!important;
  height: 20px!important;
  position: absolute!important;
  top: 28px!important;
  left: 10px!important;
}

.snotifyToast__buttons button{
  border-right: none!important;
}
