<!-- 付款申请单打印组件 -->
<template>
  <div style="display: none">
    <div ref="printContent" class="print_container">
      <div class="print_title">付款申请单</div>
      <div class="print_top">
        <span>
          公司名称：
          <span class="f_bold">{{ getObjData(formData, "KingdeeDepartmentName") }}</span>
        </span>
        <span>申请日期：{{ getObjData(formData, "FBillDate") | dateFilter("YYYY/MM/DD") }}</span>
        <span>单据编号：{{ getObjData(formData, "FBillNo") }}</span>
      </div>
      <table class="print_table">
        <tr>
          <td style="width: 12%">申请人</td>
          <td>{{ getSubmitEmployeeName }}</td>
          <td style="width: 10%">部门</td>
          <td>{{ getObjData(formData, "DepartmentName") }}</td>
          <td style="width: 10%">联系方式</td>
          <td>{{ getObjData(formData, "ApplicantContact") }}</td>
        </tr>
        <tr>
          <td>收款方联系人</td>
          <td colspan="3">{{ getObjData(formData, "PayeeContact") }}</td>
          <td>联系方式</td>
          <td>{{ getObjData(formData, "PayeeContactNumber") }}</td>
        </tr>
        <tr>
          <td>付款内容</td>
          <td colspan="5" class="t_left">{{ getObjData(formData, "PaymentContent") }}</td>
        </tr>
        <tr>
          <td colspan="2" class="t_left">
            <div class="f_a_center">
              是否签订合同：
              <input type="radio" :checked="getObjData(formData, 'ContractSigned')" />
              <label style="margin-right: 10px">是</label>
              <input type="radio" :checked="!getObjData(formData, 'ContractSigned')" />
              <label>否</label>
            </div>
          </td>
          <td>上传附件</td>
          <td>
            <div class="list_item" v-for="(item, index) in attachmentNameList" :key="index">
              {{ item }}
            </div>
          </td>
          <td>研发项目</td>
          <td>
            <div class="list_item" v-for="(item, index) in projectNameList" :key="index">
              {{ item }}
            </div>
          </td>
        </tr>
        <tr>
          <td>开票情况</td>
          <td colspan="5">
            <div class="f_a_center">
              开票情况：
              <input type="radio" :checked="getObjData(formData, 'InvoiceStatus') === 1" />
              <label style="margin-right: 20px">未开票</label>
              <input type="radio" :checked="getObjData(formData, 'InvoiceStatus') === 2" />
              <label>已开票</label>
            </div>
          </td>
        </tr>
        <tr>
          <td>付款方式</td>
          <td colspan="5">
            <div class="f_a_center">
              付款方式：
              <input type="radio" :checked="getObjData(formData, 'PaymentMethod') === 1" />
              <label style="margin-right: 20px">网银转账</label>
              <input type="radio" :checked="getObjData(formData, 'PaymentMethod') === 2" />
              <label>现金</label>
            </div>
          </td>
        </tr>
        <tr>
          <td>账号信息</td>
          <td colspan="5">
            <div class="account_info t_left">
              <div class="account_info_top">
                <span>
                  收款单位：
                  <span class="f_bold f_big_text">{{ getObjData(formData, "PayeeCompany") }}</span>
                </span>
              </div>
              <div class="account_info_bottom">
                <span class="bankName">
                  开户银行：
                  <span class="f_bold f_big_text">{{ getObjData(formData, "BankName") }}</span>
                </span>
                <span>
                  银行账号：
                  <span class="f_bold f_big_text">
                    {{ getObjData(formData, "BankAccountNumber") }}
                  </span>
                </span>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td>合同金额</td>
          <td class="t_right">
            {{ formatThousands(getObjData(formData, "ContractAmount")) }}
          </td>
          <td colspan="4" rowspan="2" class="t_left">
            <div class="contract_amount_info">
              <span class="f_bold">
                本次付款金额：{{ formatThousands(getObjData(formData, "CurrentPaymentAmoun")) }}
              </span>
              <span>
                人民币(大写)：{{ convertToChinese(getObjData(formData, "CurrentPaymentAmoun")) }}
              </span>
            </div>
          </td>
        </tr>
        <tr>
          <td>已付金额</td>
          <td class="t_right">{{ formatThousands(getObjData(formData, "AmountPaid")) }}</td>
        </tr>
        <tr>
          <td>备注：</td>
          <td colspan="5" class="t_left">{{ getObjData(formData, "Remarks") }}</td>
        </tr>
        <tr>
          <td class="footer_row" colspan="6">
            <span v-for="(item, index) in flowList" :key="index">
              {{ item.role }}【{{ item.name }}】{{ index === flowList.length - 1 ? "" : "→" }}
            </span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import printMixins from "./printMixins.js";
export default {
  name: "PaymentReqPrint",
  data() {
    return {
      // 必须得有  混入方法使用动态包裹对象key
      parcelKey: "PaymentObj",
    };
  },
  mixins: [printMixins],
  computed: {
    attachmentNameList() {
      const list = this.getObjData(this.formData, "AttachmentList", []);
      return list.map(t => t.FileName);
    },
  },
};
</script>

<style lang="scss">
@import "./printStyle.scss";
.print_container {
  .account_info {
    .account_info_top {
      margin-bottom: 10px;
    }
    .account_info_bottom {
      display: flex;
      align-items: center;
      span {
        width: 50%;
      }
      .bankName {
        padding-right: 5px;
      }
    }
  }
  .contract_amount_info {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
