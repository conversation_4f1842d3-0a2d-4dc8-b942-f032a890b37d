<!--列表选择器-->
<template>
  <div>
    <!--组件内容区-->
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="width"
    >
      <template slot="body">
        <div class="dialog-content">
          <div class="lft" v-if="$slots.left">
            <slot name="left"></slot>
          </div>
          <div class="rht">
            <div class="conditionArea-wrap clearfix" style="margin-bottom: 10px;">
              <slot name="conditionArea"></slot>
            </div>
            <div class="main">
              <div v-if="topMessage" style="height:50px;padding:10px;">
                <span style="line-height:30px;">{{topMessage}}</span>
                <el-button 
                class="fr" 
                type="primary" 
                v-show="haveBtnPermission('/afterSalesMgmt/businessMap', 'btnAdd') && addDevice"
                @click="handleAdd"
                >添加设备</el-button>
              </div>
              <!-- <div class="radioBox" v-show="!multiple">单选</div> -->
              <el-table
                fit
                :data="tableData"
                style="width: 100%"
                v-loading="tableLoading"
                height="500"
                :header-cell-style="{'text-align':'left'}"
                name="commonTable"
                highlight-current-row
                @select-all="selectAll"
                @select="select"
                @sort-change="handleSortChange"
                ref="mainTable"
              >
                <!-- v-bind="$attrs" v-on="$listeners" -->
                <el-table-column type="selection" v-if="multiple" width="55" :selectable="checkSelectable"></el-table-column>
                <el-table-column width="55" v-if="!multiple">
                  <template slot-scope="scope">
                    <el-radio v-model="checkedRadioValue" @change='handleRadioChange' :label="scope.row[selectKeyName]"></el-radio>
                  </template>
                </el-table-column>
                <!-- :selectable="checkSelectable" -->
                <el-table-column type="index" label="序号" :index="indexMethod" v-if="isShowIndex"></el-table-column>
    
                <!-- 动态列 -->
                <!-- <el-table-column
                  v-for="(column,index) in columnData"
                  :key="index"
                  :prop="column.attr.prop"
                  :label="column.attr.label"
                 
                ></el-table-column> -->
    
    
                <!-- 用户自定义需要显示的列 -->
                <template v-for="c in columnData">
                  <!-- 如果对应的列需要用到slot，则为对应的列生成具名插槽（作用域插槽） -->
                  <!-- <el-table-column v-if="c.slot" :key="c.attr.prop" v-bind="c.attr">
                    <template slot="header" slot-scope="scope">
                      <span>{{ c.attr.label }}</span>
                    </template>
                    <template slot-scope="scope">
                      <slot :name="c.attr.prop" :row="scope.row"></slot>
                    </template>
                  </el-table-column> -->
    
    
                  <el-table-column v-if="c.slot" :key="c.attr.prop" v-bind="c.attr">
                    <template slot="header" slot-scope="scope">
                      <span v-if="c.customHeader">
                        <slot :name="'header_' + c.attr.prop" :column="scope"></slot>
                      </span>
                      <span v-else>{{ c.attr.label }}</span>
                    </template>
                    <template slot-scope="scope">
                      <slot :name="c.attr.prop" :row="scope.row" :index="scope.$index"></slot>
                    </template>
                  </el-table-column>
                  
                  <el-table-column v-else :key="c.attr.prop" v-bind="c.attr">
                    <template slot="header" slot-scope="scope">
                      <span>{{ c.attr.label }}</span>
                    </template>
                  </el-table-column>
                </template>
    
              </el-table>
              <pagination
                v-show="total>0"
                :total="total"
                :page.sync="tableParam.PageIndex"
                :size.sync="tableParam.PageSize"
                @pagination="handleCurrentChange"
                @size-change="handleSizeChange"
                :layout='paginationLayout'
              />
            </div>
          </div>
        </div>
      </template>
      <template slot="footer">
        <!-- <app-button :buttonType="2" @click="handleClose"></app-button>
        <app-button :buttonType="1" @click="handleButtonClick"></app-button> -->
        
        <app-cus-button iconType="close" type="" text="取消" @click="handleClose"></app-cus-button>
        <app-cus-button iconType="save" type="primary" text="确认" style="margin-left: 5px;" @click="handleButtonClick"></app-cus-button>
      </template>
    </app-dialog>
    
  </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import indexPageMixin from "@/mixins/indexPage";
import request from "@/utils/request";
export default {
  /**名称 */
  name: "list-selector",
  /**组件声明 */
  components: {
    
  },
  mixins: [indexPageMixin],
  /**参数区 */
  props: {
    /**标题 */
    pageTitle: {
      type: String
    },
    /**是否显示序号**/
    isShowIndex:{
      type:Boolean,
      default:true
    },
    /**顶部信息 */
    topMessage: {
      type: String
    },
    /**栏目数据 */
    columnData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    /**勾选主键名称 */
    selectKeyName: {
      type: String,
      default: "Id"
    },
    /**是否多选 */
    multiple: {
      type: Boolean
    },
    /**获取列表的接口地址 */
    getListUrl: {
      type: String,
      required: true
    },
    /**附加参数 */
    condition: {
      type: Object,
    },
    /**选中项 */
    checkedData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 禁用选择框(id数组)
    disabledList: {
      type: Array,
      default: () => []
    },
    //控制添加设备按钮显示隐藏
    addDevice:{
      type:Boolean,
      default:false
    },
    width: {
      type: Number,
      default: 1000
    },
    // 点击确认按钮之前（返回true可以关闭弹框，否则不关闭弹框）
    beforeConfirm: Function,
    pageSize:{
      type: Number,
      default: 20
    },
    //分页设置
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper' 
    },
    formatRowObj: {
      type: Function,
      default: null
    }
  },
  /**数据区 */
  data() {
    return {
      elCheckbox: null,
      /**查询在执行 */
      tableLoading: false,
      /**列表查询条件 */
      tableParam: {
        PageIndex: 1,
        PageSize: 20
      },
      checkedRow: [],
      /**列表总数 */
      total: 0,
      /**列表数据 */
      tableData: [],

      checkedRadioValue: ''
    };
  },
  /**计算属性---响应式依赖 */
  computed: {},
  /**监听 */
  watch: {
    /**弹窗响应 */
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.tableParam.PageSize=this.pageSize;
          let _this = this;
          //展示当前组件后判断单选和多选模式
          if (_this.multiple) {
            //将全选框放入table表头
            $('div[name="commonTable"] div[name="checkboxDiv"]').html(
              _this.elCheckbox
            );
          } else {
            //修改单选框提示
            $('div[name="commonTable"] div[name="checkboxDiv"]').html("单选");
          }
          _this.checkedRow = JSON.parse(JSON.stringify(_this.checkedData));
          if(!_this.multiple) {
            if(_this.checkedRow && _this.checkedRow.length > 0) {
              _this.checkedRadioValue = _this.checkedRow[0][this.selectKeyName]
            }else{
              _this.checkedRadioValue = ''
            }
          }
          _this.tableParam.PageIndex = 1;
          _this.getList();
        }
      },
      immediate: true
    },
  },
  /**渲染前 */
  created() {
    var _this = this;
    _this.$nextTick(function() {
      $('div[name="commonTable"] th .el-checkbox')
        .parent()
        .attr("name", "checkboxDiv"); //给全选框上级元素增加标识
      _this.elCheckbox = $('div[name="commonTable"] th .el-checkbox'); //默认加载全选框，将全选框存入变量重复加载时根据选择模式进行dom操作
      if (!_this.multiple) {
        $('div[name="commonTable"] div[name="checkboxDiv"]').html("单选"); //将全选框改成单选提示
      }
    });
  },
  /**渲染后 */
  mounted() {},
  /**方法区 */
  methods: {
    handleSortChange({column, prop, order}) {
        this.sortObj = {
            prop,
            order,
        };
        this.getList();
    },
    getDatas(){
      this.tableParam.PageIndex = 1;
      this.getList();
    },
    handleAdd(){
      this.$emit('handleOpenAdd');
    },
    indexMethod(idx) {
      return (this.tableParam.PageIndex - 1) * this.tableParam.PageSize + (idx + 1)
    },
    /**获取列表数据 */
    getList() {
      var _this = this;
      _this.tableLoading = true;
      let data = JSON.parse(JSON.stringify(_this.tableParam));
      data = Object.assign({}, data, _this.condition);
      data = this.assignSortObj(data);
      request({
        url: _this.getListUrl,
        method: "POST",
        data
      }).then(response => {
        _this.tableLoading = false;
        _this.total = response.Total;
        let list = response.Items

        if(_this.formatRowObj) {
          list = list.map(s => {
            _this.formatRowObj(s)
            return s
          })
        }

        _this.tableData = list;
        //默认选中
        _this.setCheckRow();
      }).catch(err => {
        _this.tableLoading = false;
      });
    },
    /**选择 */
    handleButtonClick() {
      let _this = this;
      var tmpCheckRow = _this.$refs.mainTable.selection; //获取当前已选择人数
      if (tmpCheckRow.length > _this.checkedRow.length) {
        //全选时会出现当前已选中人数大于 _this.checkedRow中记录数，进行同步
        tmpCheckRow = tmpCheckRow.filter(o =>
          _this.checkedRow
            .map(m => m[_this.selectKeyName])
            .every(id => id !== o[_this.selectKeyName])
        );
        tmpCheckRow.forEach(m => {
          _this.checkedRow.push(m);
        });
      }
      if(this.beforeConfirm) {
        if(this.beforeConfirm(_this.checkedRow)) {
          this.$refs.appDialogRef.createData(_this.checkedRow);
        }
      }else{
        this.$refs.appDialogRef.createData(_this.checkedRow);
      }
    },
    // 禁用选择框
    checkSelectable(row) {
      if (this.disabledList && this.disabledList.length > 0) {
        return this.disabledList.findIndex(s => s == row[this.selectKeyName]) == -1;
      }
      return true;
    },
    /**关闭 */
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },

    selectAll() {
      //全选框事件
      var _this = this;
      _this.tableData.forEach(function(row) {
        //循环当前table数据做选择框事件相应处理
        _this.selectHandel(row);
      });
    },
    select(selection, row) {
      //单选框事件
      var _this = this;
      if (!_this.multiple) {
        //单选处理
        _this.$refs.mainTable.clearSelection();
        _this.$refs.mainTable.toggleRowSelection(row, true);
        _this.checkedRow = [row];
      } else {
        //多选处理
        _this.selectHandel(row);
      }
    },
    selectHandel(row) {
      //选择框处理
      var _this = this;
      var tmpRow = _this.checkedRow.some(
        m => m[_this.selectKeyName] == row[_this.selectKeyName]
      );
      if (tmpRow) {
        //选中的数据是否已包含在_this.checkedRow内，包含则移除，不包含则添加
        _this.checkedRow = _this.checkedRow.filter(
          m => m[_this.selectKeyName] != row[_this.selectKeyName]
        );
      } else {
        _this.checkedRow.push(row);
      }
    },
    setCheckRow() {
      //根据父级传入的已选人员设置选中
      var _this = this;
      if (_this.checkedRow.length > 0) {
        var checkedRow = _this.tableData.filter(s => _this.checkedRow.map(u => u[_this.selectKeyName]).some(o => o == s[_this.selectKeyName])) || [];
        checkedRow.forEach(u => {
          _this.$nextTick(() => {
            if (_this.$refs.mainTable) {
              _this.$refs.mainTable.toggleRowSelection(u);
            }
          });
        });
        if(!this.multiple) {
          this.checkedRadioValue = _this.checkedRow[0][this.selectKeyName]
        }
      } else {
        _this.$refs.mainTable.clearSelection();
      }
    },
    handleRadioChange(val) {
      let row = this.tableData.find(s => s[this.selectKeyName] == val)
      if(row) {
        this.select([row], row)
      }
    },
    /**切换页码 */
    handleCurrentChange(val) {
      this.tableParam.PageIndex = val.page;
      this.tableParam.PageSize = val.size;
      this.getList();
    },
    /**分页页大小变更 */
    handleSizeChange(val) {
      this.tableParam.PageSize = val.size;
      this.getList();
    },
  }
};
</script>

<!--组件样式区-->

<style scoped>
.main >>> .el-radio__label{
  display: none;
}
</style>

<style lang="scss" scoped>

.dialog-content{
  display: flex;
  .lft{
    flex-shrink: 0;
  }
  .rht{
    flex: 1;
    overflow-x: hidden;
  }
}
.main{
  position: relative;
}
// .radioBox{
//   width:53px;
//   height:33px;
//   line-height: 33px;
//   text-align: center;
//   position: absolute;
//   left:1px;
//   top:1px;
//   z-index: 10;
//   background:white;
//   color:#909399;
//   font-weight: bold;
//   font-size: 12px;
// }
</style>


