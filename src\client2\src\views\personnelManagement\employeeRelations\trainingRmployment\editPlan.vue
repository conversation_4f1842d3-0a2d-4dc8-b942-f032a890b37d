<template>
    <div>
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="700">
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData"
                label-position="right" label-width="80px">
                    <el-row class="wrapper" v-loading='loading'>
                        <el-col :span="10">
                            <el-form-item label="头像" prop="AvatarPath">
                                <img :src="formData.AvatarPath || require('../../../../assets/images/avatar3.png')"
                                style="box-shadow: 1px 1px 3px #a29e9e; border-radius: 50%; width: 65px; height: 65px;" />
                            </el-form-item>
                            <el-form-item label="部门" prop="DepartmentName">
                                {{formData.DepartmentName}}
                            </el-form-item>
                            <el-form-item label="入职时间" prop="EntryTime">
                                {{formData.EntryTime | dateFilter('YYYY-MM-DD')}}
                            </el-form-item>
                            <el-form-item label="直属上级" prop="ImmediateSuperior">
                                <emp-selector :readonly="!editable"
                                    class="fl"
                                    style="width:100%;"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="false"
                                    :list="formData.ImmediateSuperior"
                                    :beforeConfirm="handleBeforeConfirm"
                                    @change="handleImmediateSuperiorViewRange"
                                ></emp-selector>
                            </el-form-item>
                        </el-col>
                        <el-col :span="14">
                            <el-form-item label="姓名" prop="Name" label-width="120px">
                                {{formData.Name}}
                            </el-form-item>
                            <el-form-item label="职位" prop="JobName" label-width="120px">
                                {{formData.JobName || '无'}}
                            </el-form-item>
                            <el-form-item label="预计转正时间" prop="PositiveTimePlan" label-width="120px">
                                <el-date-picker v-model="formData.PositiveTimePlan" type="date" :disabled="!editable"
                                format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="导师" prop="TutorEmployeeId" label-width="120px">
                                <emp-selector :readonly="!editable"
                                    class="fl"
                                    style="width:100%;"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="false"
                                    :list="formData.TutorEmployeeIdList"
                                    :beforeConfirm="handleBeforeConfirm"
                                    @change="handleViewRange"
                                ></emp-selector>
                            </el-form-item>
                            <el-form-item label="部门经理" prop="DepartmentManager" label-width="120px">
                                <emp-selector :readonly="!editable"
                                    class="fl"
                                    style="width:100%;"
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="false"
                                    :list="formData.DepartmentManager"
                                    :beforeConfirm="handleBeforeConfirm"
                                    @change="handleDepartmentManagerViewRange"
                                ></emp-selector>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType="2"></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType="1" v-show="editable" :disabled="disabledBtn"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import empSelector from '@/views/common/empSelector'
import * as EmployeeTrainingPlanApi from "@/api/personnelManagement/EmployeeTrainingPlan";
export default {
    name: "trainingRmployment-editPlan",
    directives: {},
    components: {
        empSelector,
    },
    mixins: [],
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            if (this.dialogStatus == "update") {
                return "编辑新人培养计划";
            } else if (this.dialogStatus == "detail") {
                return "新人培养计划详情";
            }
            return "";
        }
    },
    props: {
        //编辑还是新增(create: 新增; update: 编辑; detail：详情)
        dialogStatus: {
            required: true,
            type: String,
        },
        id: {
            type: String,
            default: "",
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();
                if (this.dialogStatus != "create" && this.id) {
                    this.getDetail();
                }
            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            disabledBtn: false,
            loading: false,
            rules: {
                TutorEmployeeId: {fieldName: "导师",rules: [{ required: true }]},
                PositiveTimePlan: {fieldName: "预计转正时间",rules: [{ required: true }]},
                ImmediateSuperior: {fieldName: "直属上级",rules: [{ required: true }]},
                DepartmentManager: {fieldName: "部门经理",rules: [{ required: true }]},
            },
            formData: {
                // Id: '',
                Name: '',
                AvatarPath: '',
                DepartmentId: '',
                DepartmentName: '',
                EntryTime: '',
                TutorEmployeeId: '',
                TutorEmployeeIdList: [],
                ImmediateSuperior: [],
                DepartmentManager: [],
                JobId: '',
                JobName: '',
                PositiveTimePlan: '',
            }
        };
    },
    methods: {
        handleDepartmentManagerViewRange(users) {
            if (users && users.length > 0) {
                this.formData.DepartmentManager = users
            } else {
                this.formData.DepartmentManager = []
            }
            this.$refs["formData"].validateField(`DepartmentManager`);
        },
        handleImmediateSuperiorViewRange(users) {
            if (users && users.length > 0) {
                this.formData.ImmediateSuperior = users
            } else {
                this.formData.ImmediateSuperior = []
            }
            this.$refs["formData"].validateField(`ImmediateSuperior`);
        },
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.TutorEmployeeId = users.map(s=>s.EmployeeId)[0]
                this.formData.TutorEmployeeIdList = users
            } else {
                this.formData.TutorEmployeeId = ''
                this.formData.TutorEmployeeIdList = []
            }
            this.$refs["formData"].validateField(`TutorEmployeeId`);
        },
        // 导师  选择人员
        handleViewRange(users) {
            if (users && users.length > 0) {
                this.formData.TutorEmployeeId = users.map(s=>s.EmployeeId)[0]
                this.formData.TutorEmployeeIdList = users
            } else {
                this.formData.TutorEmployeeId = ''
                this.formData.TutorEmployeeIdList = []
            }
            this.$refs["formData"].validateField(`TutorEmployeeId`);
        },
        handleBeforeConfirm(users) {
            if(users && users.length > 1) {
            this.$message({
                message: '不得超过1个',
                type: 'error'
            })
            return false
            }
            return true
        },
        resetFormData() {
            this.formData = this.$options.data().formData
        },
        createData() {
            let self = this;
            self.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(self.formData));
                    
                    if(postData.ImmediateSuperior && postData.ImmediateSuperior.length > 0) {
                        postData.ImmediateSuperiorId = postData.ImmediateSuperior[0].EmployeeId
                    }
                    delete postData.ImmediateSuperior

                    if(postData.DepartmentManager && postData.DepartmentManager.length > 0) {
                        postData.DepartmentManagerId = postData.DepartmentManager[0].EmployeeId
                    }
                    delete postData.DepartmentManager
                    
                    self.disabledBtn = true;
                    EmployeeTrainingPlanApi.edit(postData).then(res => {
                        self.disabledBtn = false;
                        self.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        self.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                        self.disabledBtn = false;
                    });
                }
            });
        },
        getDetail() {
            this.loading = true
            EmployeeTrainingPlanApi.detail({ id: this.id }).then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.formData.ImmediateSuperior = [res.ImmediateSuperior] || []
                this.formData.DepartmentManager = [res.DepartmentManager] || []

                if (res.TutorEmployeeId) {
                    this.formData.TutorEmployeeIdList.push(res.TutorEmployee)
                }
                this.loading = false
            }).catch(err => {
                this.loading = false
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>
<style lang='scss' scoped>
.wrapper{
}
</style>