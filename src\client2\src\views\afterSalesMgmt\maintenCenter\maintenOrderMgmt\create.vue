<template>
  <div>
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1300"
    >
      <template slot="body">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          label-width="100px"
        >
          <div
            class="wrapper"
            v-loading="loading"
            style="padding:0 10px;"
          > 
            <div class="cl" style="position:relative;">
              <div class="fl divLeft" :class="{cLeft:dialogStatus != 'create' && dialogStatus != 'update'}">
                <!-- 转工单才显示此 card -->
                <el-card v-if="transId" shadow="never" class="box-card box-card-sp">
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="报修内容" prop="ReportContent">
                          {{ formData.ReportContent }}
                        </el-form-item>
                      </el-col>
                    </el-row>
                </el-card>
                <el-card class="box-card" shadow="never">
                    <div slot="header" class="clearfix">
                      <span>报修信息</span>
                    </div>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="报修时间" prop="ReportTime">
                          <el-date-picker
                            :disabled="!mainPanelEditable"
                            style="width: 100%;"
                            format="yyyy-MM-dd HH:mm"
                            value-format="yyyy-MM-dd HH:mm"
                            v-model="formData.ReportTime"
                            type="datetime"
                            placeholder=""
                          ></el-date-picker>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="记录人" prop="ReportEmployee">
                          <emp-selector
                            :readonly="!mainPanelEditable"
                            :showType="2"
                            :multiple="false"
                            :list="formData.ReportEmployee"
                            @change="handleChangeOwnerUsers"
                          ></emp-selector>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="报修地区" prop="RegionalId">
                          <!-- <treeselect :normalizer="normalizer" key='type1'
                                            :disabled='!mainPanelEditable || isOutCreate'
                                            :default-expand-level="3"
                                            v-model="formData.RegionalId"
                                            :options="listToTreeSelect(treedata)" :multiple="false" placeholder='选中报修地区后才可添加设备' :show-count="true"
                                            :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree" :zIndex='99999999'
                                            >
                                        </treeselect> -->


                          <!-- {{ formData.areaName }}
                          <el-button
                            type="text"

                            :disabled="!mainPanelEditable || isOutCreate"
                            @click="handleDialog()"
                            >选择</el-button
                          >
                          &nbsp;<i
                            style="color:rgb(159 166 181);"
                            v-show="!formData.areaName"
                            >(提示：选中报修地区后才可添加设备)</i
                          > -->
                          
                          <div class="_regional_detail_wrapper">
                              <div class="btn_wrapper">
                                  <el-button :disabled="!mainPanelEditable || isOutCreate || dialogStatus == 'update'" type="text" @click="handleDialog">选择</el-button>
                              </div>
                              <div class="regional_text" :title="formData.areaName">{{ formData.areaName }}</div>
                              <div class="close_wrapper" v-show="formData.areaName && !(!mainPanelEditable || isOutCreate) && dialogStatus != 'update'">
                                  <div class="i_wrapper">
                                      <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                  </div>
                              </div>
                          </div>

                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="地区电话" prop="RegionalPhone">
                          <span>{{
                            formData.RegionalPhone ? formData.RegionalPhone : "无"
                          }}</span>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <div>
                      <div>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="报修人" prop="TroubleShooter">
                              <el-input
                                style="width: 100%;"
                                :disabled="!mainPanelEditable"
                                maxlength="20"
                                v-model="formData.TroubleShooter"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="报修人电话" prop="ReporterNumber">
                              <el-input
                                placeholder=""
                                :disabled="!mainPanelEditable"
                                maxlength="20"
                                v-model="formData.ReporterNumber"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="故障记录" prop="ReportFailureRecord">
                            <el-input
                                :disabled="!mainPanelEditable"
                                maxlength="2000"
                                :rows="5"
                                type="textarea"
                                v-model="formData.ReportFailureRecord"
                              ></el-input>
                          </el-form-item>
                      </el-col>
                    </el-row>
                </el-card>
                <el-card class="box-card" shadow="never" v-if="dialogStatus != 'create' && dialogStatus != 'update'">
                    <div slot="header" class="clearfix">
                      <span>现场人员信息</span>
                    </div>
                    <div>
                      <div>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="实施人员" prop="HandlerEmployeeList">
                              <emp-selector
                                :readonly="true"
                                :showType="2"
                                :multiple="false"
                                :list="formData.HandlerEmployeeList"
                              ></emp-selector>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="使用车辆" prop="UseVehicle">
                              <el-input
                                disabled="disabled"
                                v-model="formData.UseVehicle"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12" v-if="formData.AgentEmployeeList && formData.AgentEmployeeList.length > 0">
                            <el-form-item label="代理授权人" prop="AgentIdsNames">
                              {{ formData.AgentIdsNames }}
                            </el-form-item>
                          </el-col>

                          <el-col :span="12">
                            <el-form-item label="总耗时" prop='TotalTime'>
                                <span v-for="(emp, idx) in formData.EmployeeList" :key="idx">
                                    {{ getTotalTime(emp.EmployeeId, formData.TotalTimeEmployeeList) | totalTimeFilter }}
                                    <template v-if="idx < formData.EmployeeList.length - 1">/</template>
                                </span>

                                <i class="el-icon-time" style="margin-right: 4px; color: #409eff;"></i>
                                <app-table-row-button @click="handleShowRecord" text='记录详情'></app-table-row-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                </el-card>
                <el-card class="box-card" shadow="never" v-if="dialogStatus != 'create' && dialogStatus != 'update'">
                    <div slot="header" class="clearfix">
                      <span>服务单信息</span>
                    </div>
                    <div>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="是否签单" prop="IsSignBill">
                              <el-radio-group v-model="formData.IsSignBill" :disabled="isHandle">
                                <el-radio v-for="(sls,slsI) in serviceListStatus" :label="sls.value" :key="slsI">{{sls.label}}</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="关联售后合同">
                              <div class="_regional_detail_wrapper">
                                <div class="btn_wrapper">
                                    <el-button :disabled="isHandle" type="text" @click="handleAfterContractDialog">选择</el-button>
                                </div>
                                <!-- AfterContractId
                                AfterContractCode -->
                                <div class="regional_text" :title="formData.AfterContractCode">{{ formData.AfterContractCode }}</div>
                                <div class="close_wrapper" v-show="formData.AfterContractCode && !isHandle">
                                    <div class="i_wrapper">
                                        <el-button icon="el-icon-close" class="btn" circle @click="electedAfterContractData(null)"></el-button>
                                    </div>
                                </div>
                            </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <template v-if="formData.IsSignBill == 1">
                          <el-row>
                            <el-col :span="24">
                                <el-form-item
                                label="到站/离站时间"
                                label-width="110px"
                                >
                                  <el-date-picker
                                      style="width:100%;"
                                      :disabled="isHandle"
                                      v-model="formData.timeValue"
                                      type="datetimerange"
                                      range-separator="-"
                                      start-placeholder=""
                                      end-placeholder=""
                                      format="yyyy-MM-dd HH:mm"
                                      value-format="yyyy-MM-dd HH:mm:ss"
                                      @input="(val) => handlePickerChange(val)"
                                  >
                                  </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="加班/夜勤" prop="OvertimeNightWork" label-width="110px">
                                <el-select
                                  :disabled="isHandle"
                                  class="sel-ipt"
                                  style="width:100%"
                                  placeholder=""
                                  v-model="formData.OvertimeNightWork"
                                >
                                  <el-option
                                    v-for="item in workTypes"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                  ></el-option>
                                </el-select>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12" style="height: 47px;">
                              <el-form-item label="工时">
                                {{ workingHour }} 小时
                                <!-- <el-input-number
                                  :disabled="isHandle"
                                  v-model="formData.WorkingHours"
                                  :max="9999"
                                  :precision="1"
                                  :min="0"
                                ></el-input-number> -->
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="服务单号" prop="ServiceNo" label-width="110px">
                                <el-input
                                  :disabled="isHandle"
                                  v-model="formData.ServiceNo"
                                  maxlength="30"
                                ></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="录入日期" prop="EntryTime">
                                <!-- 不可修改 -->
                                <el-date-picker
                                  :disabled="true"
                                  style="width: 100%;"
                                  format="yyyy-MM-dd"
                                  value-format="yyyy-MM-dd"
                                  v-model="formData.EntryTime"
                                  type="date"
                                  placeholder=""
                                ></el-date-picker>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="站内签字" prop="StationSign" label-width="110px">
                                <el-input
                                :disabled="isHandle"
                                maxlength="10"
                                v-model="formData.StationSign"
                                ></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                               <el-form-item label="服务费用(元)" prop="OtherExpenses">  
                                <el-input-number
                                    :disabled="isHandle"
                                    v-model="formData.OtherExpenses"
                                    label="描述文字"
                                ></el-input-number>
                                    <!-- @change="data => handleChangeNum(item)" -->
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <!-- <el-row>
                          </el-row> -->
                          <!-- <el-row>
                          </el-row> -->
                          <!-- <el-row>
                          </el-row> -->
                        </template>
                        <template v-if="formData.IsSignBill == 2">
                          <el-row>
                            <el-col :span="24">
                              <el-form-item label="备注" prop="SignBillRemark">
                                <el-input
                                  :disabled="isHandle"
                                  maxlength="100"
                                  :rows="3"
                                  type="textarea"
                                  v-model="formData.SignBillRemark"
                              ></el-input>
                              </el-form-item>
                            </el-col> 
                          </el-row>
                        </template>
                    </div>
                </el-card>

              </div>
              <div class="fr cRight" v-if="dialogStatus != 'create' && dialogStatus != 'update'">
                
                 <div class="tab-wrapper" style="height: 100%;">

                  <el-card class="box-card" shadow="never" v-loading="imgLoading">
                  <div slot="header" class="clearfix">
                    <span>图片附件(最多上传9张!)</span>
                    <el-upload
                      class="elUpload"
                      :show-upload-list="false"
                      :before-upload="beforeAvatarUpload"
                      :on-success="handleSuccess"
                      :on-error="handleError"
                      accept=".jpg,.jpeg,.png,.gif"
                      :max-size="2048"
                      :headers='imgHeaders'
                      multiple
                      action="api/Resources/FileUpload/UploadFile"
                      >
                      <el-button type="text" v-show="!isHandle && imgsFilePath.length<9" size="medium" icon="el-icon-circle-plus">添加</el-button>
                    </el-upload>
                  </div>
                  <div class="upDiv">
                    <ul class="cl" v-show="imgsFilePath.length>0">
                      <li class="fl pointer" v-for="(ifp,ifpI) in imgsFilePath" :key="ifpI">
                        <i class="el-icon-error" v-if="!isHandle" @click="handleDeleteFile(ifpI)"></i>
                        <el-image 
                          class="elImg"
                          :src="ifp"
                          fit="scale-down"
                          :preview-src-list="imgsFilePath">
                        </el-image>
                      </li>
                    </ul>
                    <div v-show="imgsFilePath.length<1" style="width:100%;height:300px;text-align:center;line-height:300px;">暂无图片</div>
                  </div>
                  </el-card>

                </div>

              </div>
            </div>
            
          </div>

          <div class="bottom-item">
            <div :class="(dialogStatus != 'create' && dialogStatus != 'update')?'state3':'state4' ">
              <el-card class="card-info" shadow="never" style="margin-left:10px; margin-right:3px;">
                <div slot="header" class="clearfix">
                      <span>报修设备</span>
                </div>
                <div class="equipment-list" style="display: flex; min-height: 400px; color:white;">
                <el-card shadow="never" class="box-card-info sp-card" style="width: 220px; max-height: 624px; overflow-y: auto;">
                  <div class="list-wrapper2">
                    <div style="display: flex; padding-top: 14px;">
                      <div style="flex: 1;">
                        <span class="required">*</span> 设备列表
                      </div>
                      <div>
                        <equ-selector
                          :multiple="true"
                          :list="formData.MaintenanceEquipmentList"
                          @change="handleChange"
                          :readonly="!mainPanelEditable || isOutCreate"
                          :isDistabled="!formData.RegionalId"
                          :condition="{ RegionalId: formData.RegionalId,RegionalName: formData.areaName }"
                          :path="formData.areaName"
                          :addDevice='true'
                        ></equ-selector>
                      </div>
                    </div>
                    <el-form-item prop="MaintenanceEquipmentList">
                      <tags
                        mode="list"
                        :items="formData.MaintenanceEquipmentList"
                        v-model="selectGroup"
                      >
                        <template
                          v-for="(t, idx) in formData.MaintenanceEquipmentList"
                          :slot="t.value"
                        >
                          <div :key="t.value + '1'" style="display: flex;">
                            <div
                              :key="t.value + '2'"
                              style="flex: 1;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;"
                              :title="t.label"
                            >
                              {{ t.label }}
                            </div>
                            <div :key="t.value + '3'" class="btn-wrapper">
                              <el-button
                                v-if="mainPanelEditable && !isOutCreate"
                                type="text"
                                @click.stop="handleRemove(idx)"
                                >删除</el-button
                              >
                              <span
                                v-else
                                class="item-status"
                                :class="`status-${t.HandlingResultStatus}`"
                                >{{
                                  t.HandlingResultStatus
                                    | handlingResultStatusFilter
                                }}</span
                              >
                            </div>
                          </div>
                          <div :key="t.value + '4'" :style="'color:'+isInsurance.find(s => s.value == t.IsWarranty).color+';'">是否在保：{{isInsurance.find(s => s.value == t.IsWarranty).label}}</div>
                        </template>
                      </tags>
                    </el-form-item>
                  </div>
                </el-card>
                <el-card shadow="never" class="box-card-info right-wrapper sp-card sp-card2">
                <el-tabs v-model="tabsActive">
                    <el-tab-pane
                      v-for="item in tabsData"
                      :key="item.name"
                      :label="item.label"
                      :name="item.name"
                    ></el-tab-pane>
                </el-tabs>
                
                  <!-- <div>报修单信息</div> -->
                  <no-data v-if="formData.MaintenanceEquipmentList == 0"></no-data>
                  <div
                    class="tab-content-wrapper"
                    v-for="(item, idx) in formData.MaintenanceEquipmentList"
                    :key="idx"
                  >
                    <div v-show="tabsActive == 'repairInfo'">
                      <div v-show="item.Code == selectGroup">
                        <div class="section section-first">
                          <div style="position: relative; font-weight: bold; margin-bottom:5px;">
                            <span>加热炉信息</span>
                            <el-button type='primary' style="position: absolute; top: 0; right: 0;" v-show="dialogStatus == 'handle' && haveBtnPermission('/afterSalesMgmt/businessMap', 'btnChange')" @click='handleChangeDialog(item)'>编辑</el-button>
                          </div>
                          <el-row>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">加热炉/锅炉:</span>
                                {{ item.Name }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">炉号:</span>
                                {{
                                  item.HeatNumber ? item.HeatNumber : "无"
                                }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">用途:</span>
                                {{
                                  item.EquipmentUseName ? item.EquipmentUseName : "无"
                                }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">加热炉功率:</span>
                                {{
                                  item.HeatFurnaceRatework ? item.HeatFurnaceRatework : "无"
                                }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">结构类型:</span>
                                {{
                                  item.StructureTypeName ? item.StructureTypeName : "无"
                                }}
                              </div>
                            </el-col>

                            <!-- <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">设备编号:</span>
                                {{ item.Code }}
                              </div>
                            </el-col> -->
                            
                          </el-row>
                        </div>
                        <div class="section">
                          <div style="font-weight: bold; margin-bottom:5px;">燃烧器信息</div>
                          <el-row>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">供风方式:</span>
                                {{
                                  item.EquipmentWorkModeName ? item.EquipmentWorkModeName : "无"
                                }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">燃料类型:</span>
                                {{
                                  item.FuelType ? item.FuelType : "无"
                                }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">燃烧器型号:</span>
                                {{ item.BurnerModel ? item.BurnerModel : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title"
                                  >燃烧器功率:</span
                                >
                                {{ item.BurnerRatework ? item.BurnerRatework : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title"
                                  >燃烧器压力:</span
                                >
                                {{ item.BurnerPressure ? item.BurnerPressure : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">是否在保:</span>
                                {{ item.IsWarranty | isWarrantyFilter }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">保修有效期至:</span>
                                {{ item.WarrantyTime ? item.WarrantyTime.split(' ')[0] : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title"
                                  >生产厂家:</span
                                >
                                {{ item.Manufacturer ? item.Manufacturer : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">投产时间:</span>
                                {{ item.InstallTime ? item.InstallTime.split(' ')[0] : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">出产日期:</span>
                                {{ item.ProduceDate ? item.ProduceDate.split(' ')[0] : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">出产编号:</span>
                                {{ item.ProduceNumber ? item.ProduceNumber : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">电源参数:</span>
                                {{ item.PowerParams ? item.PowerParams : "无" }}
                              </div>
                            </el-col>
                            <el-col :span="12">
                              <div class="col-wrapper">
                                <span class="field-title">试验证书编号:</span>
                                {{ item.TestCertificateNo ? item.TestCertificateNo : "无" }}
                              </div>
                            </el-col>
                          </el-row>
                        </div>
                        <!-- <div class="section">
                          <div class="title">
                            <span style="color: red;">*</span> 报修故障记录
                          </div>
                          <el-row>
                            <el-col :span="24" class="cus-textarea-wrapper">
                              <el-form-item
                                label=""
                                :prop="
                                  'MaintenanceEquipmentList.' +
                                    idx +
                                    '.ReportFailureRecord'
                                "
                                :rules="{
                                  required: true,
                                  message: '报修故障记录不能为空',
                                  trigger: 'blur'
                                }"
                              >
                                <el-input
                                  :disabled="!mainPanelEditable"
                                  maxlength="2000"
                                  :rows="8"
                                  type="textarea"
                                  v-model="
                                    formData.MaintenanceEquipmentList[idx]
                                      .ReportFailureRecord
                                  "
                                ></el-input>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </div> -->
                      </div>
                    </div>
                    <div
                      v-show="tabsActive == 'maintenInfo'"
                      v-if="!mainPanelEditable"
                    >
                      <div
                        v-show="item.Code == selectGroup"
                        style="height:550px;overflow-y:auto;"
                      >
                        <div class="section section-first">
                          
                          <div class="title">
                            故障现象&nbsp;&nbsp;&nbsp;&nbsp;
                            <el-button
                              v-if="!isHandle"
                              @click="handleAddFault(item)"
                              type="text"
                              >添加故障现象</el-button
                            >
                          </div>
                          <div class="tab-wrapper wxInput">
                            <el-card
                              shadow="never"
                              class="box-card"
                              v-for="(mel, index) in item.FaultPhenomenonList"
                              :key="index"
                            >
                              <div slot="header" class="clearfix">
                                <span>维修故障{{ index + 1 }}</span>
                                <el-select
                                  :disabled="isHandle"
                                  v-model="mel.FaultType"
                                  placeholder="请选择故障类型"
                                  style="margin-left:25px;z-index:10;"
                                >
                                  <el-option
                                    v-for="fto in FaultTypeOptions"
                                    :key="fto.value"
                                    :label="fto.label"
                                    :value="fto.value"
                                  >
                                  </el-option>
                                </el-select>
                                <i
                                  class="el-icon-delete"
                                  v-show="!isHandle"
                                  @click="handleRemoveFault(item, index, idx)"
                                ></i>
                                <el-button
                                  v-show="
                                    haveBtnPermission('/caseReport', 'declare') &&
                                      declareNewCases &&
                                      mel.IsApplicationApproval
                                  "
                                  class="fr"
                                  style="margin:2px 10px 0 0;"
                                  type="text"
                                  @click="handleNewCase(mel, item)"
                                  >申报新案例</el-button
                                >
                              </div>
                              <div>
                                <el-row>
                                  <el-col :span="24" style="margin-top:10px;">
                                    <el-form-item :class="{ createMainten: !isHandle }" label-width="82px" label="故障现象"  :prop="'MaintenanceEquipmentList.' + idx + '.FaultPhenomenonList.' + index + '.Phenomenon'"
                                      :rules="{
                                        required: true,
                                        message: '故障现象不能为空',
                                        trigger: 'blur'
                                      }"
                                    >
                                      <el-input
                                        style="padding-right:10px;margin-top:7px;"
                                        :disabled="isHandle"
                                        maxlength="2000"
                                        :rows="3"
                                        type="textarea"
                                        placeholder="请输入单个故障现象"
                                        v-model="formData.MaintenanceEquipmentList[idx].FaultPhenomenonList[index].Phenomenon"
                                      ></el-input>
                                    </el-form-item>
                                  </el-col>
                                  <el-col :span="24">
                                    <el-form-item :class="{ createMainten: !isHandle }" label-width="82px" label="原因分析">
                                      <el-input
                                        style="padding-right:10px;margin-top:7px;"
                                        :disabled="isHandle"
                                        maxlength="2000"
                                        :rows="3"
                                        type="textarea"
                                        placeholder="请输入原因分析"
                                        v-model="formData.MaintenanceEquipmentList[idx].FaultPhenomenonList[index].CauseAnalysis"
                                      ></el-input>
                                    </el-form-item>
                                  </el-col>
                                  <el-col :span="24">
                                    <el-form-item :class="{ createMainten: !isHandle }" label-width="82px" label="解决方法">
                                      <el-input
                                        style="padding-right:10px;margin-top:7px;"
                                        :disabled="isHandle"
                                        maxlength="2000"
                                        :rows="3"
                                        type="textarea"
                                        placeholder="请输入解决方法"
                                        v-model="formData.MaintenanceEquipmentList[idx].FaultPhenomenonList[index].Solution"
                                      ></el-input>
                                    </el-form-item>
                                  </el-col>
                                </el-row>

                              </div>
                              <div class="section section-first">
                                <div class="title">
                                  更换配件&nbsp;&nbsp;&nbsp;&nbsp;<el-button
                                    v-if="!isHandle"
                                    @click="handleAddReplace(item, idx, index)"
                                    type="text"
                                    >添加配件</el-button
                                  >
                                </div>
                                <div class="tab-wrapper from-tab">
                                  <!-- <el-form-item label=""
                                      :prop="'MaintenanceEquipmentList.' + idx + '.ReplaceAccessoryList'"
                                      :rules="rules.ReplaceAccessoryList"> -->
                                  <app-table-core
                                    class="treeTable"
                                    ref="mainTable"
                                    :tab-columns="replaceColumns"
                                    :tab-datas="mel.MaintenancStructPartList"
                                    :tab-auth-columns="[]"
                                    :isShowAllColumn="true"
                                    :startOfTable="0"
                                    :multable="false"
                                    :showAppend="true"
                                  >
                                    <!-- :isShowOpatColumn="!isHandle" -->
                                    <template slot="StructPartName" slot-scope="scope">
                                      <div class="omit" style="width:100%;" :title="scope.row.StructPartName">{{scope.row.StructPartName}}</div>
                                    </template>
                                    <template slot="SpecificationModel" slot-scope="scope">
                                      <div class="omit" style="width:100%;" :title="scope.row.SpecificationModel">{{scope.row.SpecificationModel}}</div>
                                    </template>
                                    <template slot="UnitPrice" slot-scope="scope">
                                      <el-input-number
                                        class="elNumber"
                                        :disabled="isHandle"
                                        v-model="scope.row.UnitPrice"
                                        :precision="2"
                                        :min="0"
                                        :max="999999999"
                                        @input="handleChangeInput(idx)"
                                      ></el-input-number>
                                    </template>
                                    <template slot="Count" slot-scope="scope">
                                      <el-input-number
                                        class="elNumber"
                                        :disabled="isHandle"
                                        v-model="scope.row.Count"
                                        :min="0"
                                        :max="999999999"
                                        @input="handleChangeInput(idx)"
                                      ></el-input-number>
                                    </template>
                                    <!-- <template slot="SupplierName" slot-scope="scope">
                                      <span>{{scope.row.SupplierName ? scope.row.SupplierName : '无'}}</span>
                                    </template> -->
                                    <template slot-scope="scope">
                                      <el-button
                                        size="mini"
                                        type="text"
                                        @click="
                                          handleDetailReplace(scope.row, item)
                                        "
                                        >详情</el-button
                                      >
                                      <!-- <el-button
                                        size="mini"
                                        v-show="!isHandle"
                                        type="text"
                                        @click="
                                          handleEditReplace(
                                            scope.row,
                                            idx,
                                            index,
                                            scope.index - 1,
                                            item
                                          )
                                        "
                                        >编辑</el-button
                                      > -->
                                      <app-table-row-button
                                        v-show="!isHandle"
                                        @click="
                                          handleRemoveReplace(
                                            mel,
                                            scope.index - 1,
                                            idx
                                          )
                                        "
                                        :type="3"
                                      ></app-table-row-button>
                                    </template>
                                  </app-table-core>
                                  <!-- </el-form-item> -->
                                </div>
                              </div>
                            </el-card>
                          </div>
                        </div>
                        <!-- <el-card class="boxCard">
                          <div class="tMoney cl">
                              <span class="fl">总计费用(元)：{{ item.TotalMoney }}</span>
                          </div>
                        </el-card> -->
                        <el-card class="boxCard" shadow="never">
                          <div class="section">
                              <el-row>
                              <el-col :span="24" style="margin-bottom: 8px;">
                                  处理结果：
                                  <el-radio
                                  :disabled="isHandle"
                                  v-for="(r, idx) in handlingResultStatus"
                                  :key="idx"
                                  v-model="item.HandlingResultStatus"
                                  :label="r.value"
                                  >{{ r.label }}</el-radio
                                  >
                              </el-col>
                              </el-row>
                              <el-row>
                              <el-col :span="24" class="cus-textarea-wrapper">
                                  <div style="margin-bottom: 8px;">备注</div>
                                  <el-form-item label>
                                  <el-input
                                      :disabled="isHandle"
                                      maxlength="2000"
                                      :rows="8"
                                      type="textarea"
                                      v-model="item.Remarks"
                                  ></el-input>
                                  </el-form-item>
                              </el-col>
                              </el-row>
                              
                          </div>
                        </el-card>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
              </el-card>
             </div>
             <div  :class="(dialogStatus != 'create' && dialogStatus != 'update')?'state1':'state2' ">
               <el-card class="card-info" shadow="never" style="height:100%;margin-left:8px; margin-right:12px;">
                    <div slot="header" class="clearfix">
                        <span>服务评价</span>
                    </div>

                     <el-row class="item">
                        <el-col :span="7">
                          <span class="title">服务态度</span>
                        </el-col>
                        <el-col :span="17" class="content">
                            <el-rate :disabled="isHandle" v-model="formData.AttitudeScore"></el-rate>
                        </el-col>
                      </el-row>
                      <el-row class="item">
                          <el-col :span="7" >
                            <span class="title">维修技术</span>
                          </el-col>
                          <el-col :span="17" class="content">
                              <el-rate :disabled="isHandle" v-model="formData.SkillScore"></el-rate>
                          </el-col>
                      </el-row>
                      <el-row class="item">
                          <el-col :span="7">
                            <span class="title">时效性</span>
                          </el-col>
                          <el-col :span="17" class="content">
                              <el-rate :disabled="isHandle" v-model="formData.TimelinessScore"></el-rate>
                          </el-col>
                      </el-row>
                      <el-row class="item">
                          <el-col :span="7">
                            <span class="title">投诉/建议</span>
                          </el-col>
                          <el-col :span="17">
                            <el-input style="padding-right:5px;" type="textarea" maxlength="1000" :rows="4" :disabled="isHandle" v-model="formData.ComplaintsSuggestions"></el-input>
                          </el-col>
                      </el-row>
               </el-card>
             </div>
           </div>

          <!-- <div v-if="!mainPanelEditable && formData.Approval">
    <div class="panel-title">审批</div>
    <div>
        <approval-panel v-if="dialogStatus == 'handle'" :editable='editable' ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
        <approval-detail :isOnlyViewDetail='isOnlyViewDetail' v-if="dialogStatus == 'approval' || dialogStatus == 'detail'" ref="approvalDetail" :dialogStatus='dialogStatus' :approvalObj='formData.Approval'></approval-detail>
    </div>
</div> -->
        </el-form>
      <!-- </template> -->
  <!-- </div> -->
</template>
<template slot="footer">
  <!-- 取消 -->
  <app-button
    style="margin-right: 2px;"
    @click="handleClose"
    :buttonType="2"
  ></app-button>
  <!-- 保存 -->
  <!-- <app-button style="margin-right: 2px;" v-if="!isHandle && dialogStatus != 'approval'" @click="createData(1)" v-show="editable" :disabled='disabledBtn' text='保存'></app-button>&nbsp; -->
  <!-- 确认 -->
  <app-button
    v-if="dialogStatus != 'approval'"
    @click="createData(2)"
    :buttonType="1"
    v-show="editable"
    :disabled="disabledBtn"
  ></app-button>

  <!-- 审批模式下才显示审批 -->
  <!-- <el-button @click="handleApproval" type="primary" :disabled='disabledBtn' v-show="dialogStatus == 'approval' && !isOnlyViewDetail">审批</el-button>-->
    </template> 
</app-dialog>
    <add-accessories
@closeDialog="closeDialog"
@saveSuccess="handleSaveSuccess"
@saveEdit="saveEdit"
:dialogFormVisible="dialogAcceFormVisible"
:acceData='acceData'
:rightIndex='rightListIndex'
:dialogStatus="dialogReplaceStatus"
:msgReplace="msgReplace">
</add-accessories>
<query-phenomenon
@closeDialog="closePhenDialog"
@saveSuccess="handlePhenSaveSuccess"
:dialogFormVisible="dialogPhenFormVisible"
:outerData="outerData">
</query-phenomenon>
<new-cases
@closeDialog="closeNewDialog"
@saveSuccess="handleNewSaveSuccess"
:dialogStatus="dialogNewStatus"
:dialogFormVisible="dialogNewFormVisible"
:caseData="caseData"
></new-cases>
<reference-list
    @closeDialog="closeReferDialog"
    @saveSuccess="saveRefSuccess"
    :dialogFormVisible="dialogReferFormVisible"
    :msg='msg'
    :outerData="outerData"
    :oData="true">
</reference-list>
<failure-detail
@closeDialog="closeFdDialog"
:dialogFormVisible="dialogFdVisible"
:id='failureDetailId'>
</failure-detail>
<v-cause
  ref="vc"
  @closeDialog="closeCauseDialog"
  :dialogFormVisible="dialogCauseFormVisible"
  :dialogStatus="dialogCauseStatus"
  :causeData="causeData">
  </v-cause>
  <v-solution
  ref="vs"
  @closeDialog="closeSolutionDialog"
  :dialogFormVisible="dialogSolutionFormVisible"
  :dialogStatus="dialogSolutionStatus"
  :solutionData="solutionData">
  </v-solution>
  <!-- 选择地区 -->
    <v-area-choose
        @closeDialog="closeAreaDialog"
        @electedRegionalData="electedRegionalData"
        :dialogFormVisible="dialogAreaFormVisible"
        :checkedList="formData.RegionalId ? [formData.RegionalId] : []"
        :disabledFn="disabledFn"
        :queryRegionID='queryRegionID'
        :defaultExpandLevel='1'
    ></v-area-choose>
  <change-page
        v-if="currentRow && dialogChangeFormVisible"
        @closeDialog="closeChangeDialog"
        @saveSuccess="handleChangeSaveSuccess"
        :dialogFormVisible="dialogChangeFormVisible"
        :dialogStatus="dialogChangeStatus"
        :id="currentRow.Id"
        :isShowHistories="isShowHistories"
        ></change-page>

  <saleContractSelector
    :isShow="dialogAfterContractFormVisible"
    :checkedList="formData.AfterContractId ? [{Id: formData.AfterContractId, AfterContractCode: formData.AfterContractCode}] : []" 
    :condition="{ RelevanceRegionalId: formData.RegionalId }"
    @changed="electedAfterContractData"
    @closed="() => (dialogAfterContractFormVisible = false)"
    :multiple="false"
  ></saleContractSelector>

  <record
    @closeDialog="closeRecordDialog"
    :dialogFormVisible="dialogRecordVisible"
    :id='id'
  >
  </record>
</div>
</template>

<script>
import changePage from "../../businessMap/change";
import newCases from "../../../caseReport/common/newCases";
import tabs from "../../../projectDev/projectMgmt/common/tabs";
import record from './record'
import empSelector from "../../../common/empSelector";
import equSelector from "./equSelector";
import failureDetail from "./common/failureDetail";
import queryPhenomenon from "./common/queryPhenomenon";
import referenceList from "./common/referenceList";
import { regs } from "@/utils/regs";
// import approvalPanel from "../../../projectDev/projectMgmt/common/approvalPanel";
// import approvalDetail from "../../../projectDev/projectMgmt/workbench/common/approvalDetail";
import addAccessories from "./addAccessories";
import { listToTreeSelect } from "@/utils";
import * as systemManagement from "@/api/systemManagement/regionalManagement";
import * as mom from "@/api/maintenanceCenter/maintenOrderMgmt";
import { getUserInfo } from "@/utils/auth";
import { vars } from "../common/vars";
import * as projVars from "../../../projectDev/common/vars";
import * as salesVars from '../../../salesMgmt/common/vars'
import * as failurecase from "@/api/failurecase";
import vCause from "../../../failureCase/common/cause";
import vSolution from "../../../failureCase/common/solution";
import vAreaChoose from "../../businessMap/common/areaChoose";
import { getToken } from '@/utils/auth';
import * as customerRepair from "@/api/maintenanceCenter/customerRepair";
import noData from "@/views/common/components/noData";
import saleContractSelector from "@/views/common/saleContractSelector";
import mixins from '../../../afterSalesMgmt/softwareAftersalesReturnVisit/mixins'
import dayjs from 'dayjs'

let Dec = require("decimal.js");

export default {
  name: "mainten-order-mgmt-create",
  directives: {},
  mixins: [mixins],
  components: {
    vCause,
    vSolution,
    newCases,
    addAccessories,
    tabs,
    empSelector,
    equSelector,
    // approvalPanel,
    // approvalDetail,
    queryPhenomenon,
    referenceList,
    failureDetail,
    vAreaChoose,
    changePage,
    noData,
    saleContractSelector,
    record,
  },
  props: {
    specialPageTitle: {
        type: String
    },
    dialogStatus: {
      //create、update、detail
      type: String
    },
    id: {
      type: String,
      default: ""
    },
    //外部需要使用该组件来创建报修单
    row: {
      type: Object,
      default: null
    },
    //仅仅查看（即便使审批模式，不显示审批操作区块 和 审批按钮）
    // isOnlyViewDetail: {
    //     type: Boolean,
    //     default: false
    // },
    //是否为外部调用”创建“报修单页面
    isOutCreate: {
      type: Boolean,
      default: false
    },
    //是否展示申报新案例按钮
    declareNewCases: {
      type: Boolean,
      default: false
    },
    queryRegionID:{
      type:String,
      default:''
    },
    //转工单（创建报修单）的时候才会用到此参数
    transId: {
      type: String,
      default: ''
    }
  },
  filters: {
    handlingResultStatusFilter(status) {
      let obj = vars.maintenOrderMgmt.handlingResultStatus.find(
        s => s.value == status
      );
      if (obj) {
        return obj.label;
      }
      return status;
    },
    isWarrantyFilter(val) {
      let obj = salesVars.vars.business.warranty.find(s => s.value == val)
      if(obj) {
        return obj.label
      }
      return '无'

    },
  },
  computed: {
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "detail";
    },
    mainPanelEditable() {
      return this.dialogStatus == "create" || this.dialogStatus == "update";
    },
    isHandle() {
      return this.dialogStatus != "handle";
    },
    tabsData() {
      let tabs = [
        { name: "repairInfo", label: "报修单信息" },
        { name: "maintenInfo", label: "维修详情" }
      ];
      if (this.dialogStatus == "create" || this.dialogStatus == "update") {
        return [tabs[0]];
      } else {
        return tabs;
      }
    },
    pageTitle() {
      if (this.specialPageTitle) {
        return this.specialPageTitle;
      }
      if (this.dialogStatus == "create") {
        return "创建报修单";
      } else if (this.dialogStatus == "update") {
        return "编辑报修单";
      } else if (this.dialogStatus == "detail") {
        return "报修单详情";
      } else if (this.dialogStatus == "handle") {
        return "处理报修单";
      }
    },
    workingHour() {
      if(this.formData.timeValue && this.formData.timeValue.length == 2) {
        const date1 = dayjs(this.formData.timeValue[1])
        let minutes = date1.diff(this.formData.timeValue[0], 'minute') 
        //转成小时，保留一位小数（向下取整）
        return (new Dec(minutes).div(new Dec(60)).mul(new Dec(this.formData.HandlerEmployeeList.length))).toFixed(1, Dec.ROUND_DOWN)
      }
      return 0
    },
  },
  watch: {
    transId: {
      handler(val) {
        if (this.dialogStatus == "create" && val) { 
          this.getDetailInfo()
        }
      },
      immediate: true
    },
    row(val) {
      if (this.dialogStatus == "create" && val) {
        // this.isTip = false
        this.formData.RegionalId = val.MaintenanceEquipmentList[0].RegionalId;
        this.formData.areaName=val.MaintenanceEquipmentList[0].RegionalName;
        this.formData.RegionalPhone=val.MaintenanceEquipmentList[0].RegionalPhone;
        this.formData.MaintenanceEquipmentList = JSON.parse(
          JSON.stringify(val.MaintenanceEquipmentList)
        );
        this.formData.MaintenanceEquipmentList = this.equsInit(
          this.formData.MaintenanceEquipmentList,
          true
        );
        // this.isTip = true
      }
    },
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.tabsActive =
            this.dialogStatus == "handle" ? "maintenInfo" : "repairInfo";
            
          

          if (this.dialogStatus != "create" && this.id) {
            this.resetFormData();
            // this.loading=true;
            // failurecase.getListByCondition({"failureCaseCode": "","failureSymptom": ''}).then(res => {
            //   this.loading=false;
            //   this.selectDatas=res;
              this.getDetail();
            // }).catch(err => {
            //   this.loading=false;
            // })
          }
          if (this.dialogStatus == "create" && !this.row) {
            this.resetFormData();
            let currUser = getUserInfo();
            this.formData.ReportEmployee = [
              {
                EmployeeId: currUser.employeeid,
                Name: currUser.username,
                Avatar: currUser.empAvatar,
                Number: currUser.empNumber
              }
            ];
          }
        }
      },
      immediate: true
    },
    "formData.RegionalId"(val, oldVal) {
      if(this.dialogStatus == 'create'){
        if(val) {
          //如果当前为“转工单”方式创建报修单，在选择地区时，不需要修改 地区电话、报修人、报修人电话 三属性。
          if(!this.transId) {
            systemManagement.detail({Id: val}).then(res => {
              if(res) {
                this.formData.RegionalPhone = res.RegionalPhone
                this.formData.TroubleShooter=res.RegionalContact;
                this.formData.ReporterNumber=res.RegionalMobile;
              }
            })
          }
        }
        this.oldDistrictId = oldVal;
        if (this.isTip) {
          if (
            oldVal &&
            oldVal != val &&
            this.formData.MaintenanceEquipmentList &&
            this.formData.MaintenanceEquipmentList.length > 0
          ) {
            // this.$confirm(
            //   "重新选择其他地区将清空故障设备列表信息, 是否继续操作？",
            //   "提示",
            //   {
            //     confirmButtonText: "确定",
            //     cancelButtonText: "取消",
            //     type: "warning"
            //   }
            // )
            //   .then(() => {
                this.isTip = true;
                this.formData.MaintenanceEquipmentList = [];
              // })
              // .catch(() => {
              //   this.isTip = false;
              //   this.formData.RegionalId = this.oldDistrictId;
              // });
          }
        } else {
          this.isTip = true;
        }
      }
    }
  },
  data() {
    return {
      isInsurance:vars.maintenOrderMgmt.isInsurance,
      dialogChangeStatus:'',
      isShowHistories:false,
      dialogChangeFormVisible:false,
      currentRow:null,
      imgLoading:false,
      imgsFilePath:[],
      imgHeaders:{token: getToken()},
      serviceListStatus:vars.maintenOrderMgmt.serviceListStatus,
      FaultTypeOptions: vars.maintenOrderMgmt.FaultTypeOptions,
      dialogAreaFormVisible: false,
      dialogAfterContractFormVisible: false,
      causeData: {
        FailureResonCode: "",
        FailureReson: ""
      },
      solutionData: {
        FailureSolutionCode: "",
        Solution: "",
        AttachmentList: []
      },
      dialogCauseStatus: "detail",
      dialogSolutionStatus: "detail",
      dialogCauseFormVisible: false,
      dialogSolutionFormVisible: false,
      dialogNewStatus: "create",
      failureDetailId: "",
      dialogFdVisible: false,
      dIndex: 0,
      xIndex: 0,
      outerData: null,
      msg: null,
      oData: false,
      dialogNewFormVisible: false,
      dialogPhenFormVisible: false,
      dialogReferFormVisible: false,
      msgReplace: null,
      dialogReplaceStatus: "create",
      leftListIndex: 0,
      rightListIndex: 0,
      structPartIndex: 0,
      dialogAcceFormVisible: false,
      tabsActive: "",
      workTypes: vars.maintenOrderMgmt.workTypes,
      handlingResultStatus: vars.maintenOrderMgmt.handlingResultStatus,
      oldDistrictId: null,
      isTip: true,
      listToTreeSelect,
      loading: false,
      disabledBtn: false,
      normalizer(node) {
        return {
          id: node.Id,
          label: node.RegionalName,
          children: node.children
        };
      },
      faultColumns: [
        {
          attr: {
            prop: "Phenomenon",
            label: "故障现象",
            renderHeader: this.renderHeader
          },
          slot: true
        },
        {
          attr: {
            prop: "CauseAnalysis",
            label: "原因分析",
            renderHeader: this.renderHeader
          },
          slot: true
        },
        {
          attr: {
            prop: "Solution",
            label: "解决方法",
            renderHeader: this.renderHeader
          },
          slot: true
        }
      ],
      replaceColumns: [
        {
          attr: { prop: "StructPartName", label: "配件名称", align: "center", showOverflowTooltip: true },slot:true
        },
        {
          attr: { prop: "ProcessModeName", label: "用途分类", align: "center" }
        },
        {
          attr: {prop: "SpecificationModel",label: "规格型号",align: "center", showOverflowTooltip: true },slot:true
        },
        {
          attr: {prop: "UnitPrice",label: "单价(元)",width: 150,align: "center"},slot: true
        },
        {
          attr: {prop: "Count",label: "数量",width: 150,align: "center"},slot: true
        },
        {
          attr: {prop: "TotalPrice",label: "总价(元)",align: "center"}
        },
        // {
        //   attr: { prop: "SupplierName", label: "供应商", align: "center", showOverflowTooltip: true },
        //   slot: true
        // }
      ],
      rules: {
        ReportTime: { fieldName: "报修时间", rules: [{ required: true }] },
        TroubleShooter: {
          fieldName: "故障报告人",
          rules: [{ required: true }]
        },
        ReportFailureRecord: {
          fieldName: "故障记录",
          rules: [{ required: true }]
        },
        // ReporterNumber: {
        //   fieldName: "报告人电话",
        //   rules: [{ required: true }, { reg: regs.phoneAndTel }]
        // },
        RegionalId: { fieldName: "安装地区", rules: [{ required: true }] },
        ReportEmployee: {
          fieldName: "记录人",
          rules: [{ required: true, trigger: "change" }]
        },
        MaintenanceEquipmentList: {
          fieldName: "设备列表",
          rules: [{ required: true, trigger: "change" }]
        },
        IsSignBill:{
          fieldName: "是否签单",
          rules: [{ required: true, trigger: "change" }]
        },
        // WorkingHours: { fieldName: "工时", rules: [{ required: true }] },
        // ServiceNo: { fieldName: "单号", rules: [{ required: true }] },
        // EntryTime: { fieldName: "录入日期", rules: [{ required: true }] },
        Phenomenon: {
          fieldName: "故障现象不能为空",
          rules: [{ required: true }]
        },
        CauseAnalysis: {
          fieldName: "原因分析不能为空",
          rules: [{ required: true }]
        },
        Solution: { fieldName: "解决方法不能为空", rules: [{ required: true }] }
      },
      formData: {
        RegionalPhone: "",
        areaName: "",
        Id: "", //项目ID
        ReportTime: new Date(), //报修时间
        ReportEmployee: [], //记录人
        TroubleShooter: "", //故障报告人
        ReporterNumber: "", //报告人电话
        RegionalId: "", // 安装地区
        ReportContent: '',//报修内容
        ReportFailureRecord: '',
        HandlerEmployeeList: [], //实施人员
        UseVehicle: "", //使用车辆
        OvertimeNightWork: 1, //加班/夜勤
        WorkingHours: 0, //工时
        ServiceNo: "", //单号
        EntryTime: "", //录入日期
        AgentEmployeeList: null,
        AgentIdsNames: [],
        TotalTime: '',
        MaintenanceEquipmentList: [],
        IsSignBill:1,
        SignBillRemark:'',
        AttachmentList:[],
        AttachmentIdList:[],//图片附件Id
        timeValue:'',
        StationSign:'',
        OtherExpenses:'',
        ArrivalTime:'',
        DepartureTime:'',
        ComplaintsSuggestions:'',
        AttitudeScore:'',
        TimelinessScore:'',
        SkillScore:'',
      },
      selectGroup: "",

      //创建订单
      dialogOrderCreateFormVisible: false,
      dialogOrderCreateStatus: "detail",

      paths: [],
      acceData: null,
      timeout: null,
      selectDatas: [],
      cSelectDatas: [],
      sSelectDatas: [],
      caseData: null,

      dialogRecordVisible: false,

    };
  },
  created() {
    this.rules = this.initRules(this.rules);

    const validateFaultPhenomenonList = (rule, value, callback) => {
      if (value.length > 0) {
        let fields = [
          { value: "Phenomenon", label: "故障现象" },
          { value: "CauseAnalysis", label: "原因分析" },
          { value: "Solution", label: "解决方法" }
        ];
        value = value.map(s => {
          !s.Phenomenon && (s.Phenomenon = "");
          !s.CauseAnalysis && (s.CauseAnalysis = "");
          !s.Solution && (s.Solution = "");
          return s;
        });
        let isErrors = value.some(
          s =>
            !s.Phenomenon.trim() ||
            !s.CauseAnalysis.trim() ||
            !s.Solution.trim()
        );
        if (isErrors) {
          callback(new Error(`故障现象、原因分析、解决方法均不能为空`));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    if (!this.rules["FaultPhenomenonList"])
      this.rules["FaultPhenomenonList"] = [];
    this.rules.FaultPhenomenonList.push({
      validator: validateFaultPhenomenonList,
      trigger: "change"
    });

    const validateReplaceAccessoryList = (rule, value, callback) => {
      if (value.length > 0) {
        let fields = [
          { value: "Name", label: "配件名称" },
          { value: "SpecificationModel", label: "规格型号" }
        ];
        value = value.map(s => {
          !s.Name && (s.Name = "");
          !s.SpecificationModel && (s.SpecificationModel = "");
          return s;
        });
        let isErrors = value.some(
          s => !s.Name.trim() || !s.SpecificationModel.trim()
        );
        if (isErrors) {
          callback(new Error(`配件名称、规格型号均不能为空`));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    if (!this.rules["ReplaceAccessoryList"])
      this.rules["ReplaceAccessoryList"] = [];
    this.rules.ReplaceAccessoryList.push({
      validator: validateReplaceAccessoryList,
      trigger: "change"
    });
  },

  mounted() {
    // if(this.dialogStatus == 'detail') {
      this.loadSelectData();
    // }
  },
  methods: {
    getDetailInfo() {
      customerRepair.detail({id: this.transId}).then(res => {
        this.formData.ReportTime = res.ReportTime //保修时间
        this.formData.TroubleShooter = res.ReportEmployee //报修人
        this.formData.ReporterNumber = res.ReporterNumber //报修电话
        this.formData.RegionalPhone = res.RegionalPhone //地区电话
        this.formData.ReportContent = res.ReportContent //报修内容
        this.formData.ReportFailureRecord = res.ReportContent
        if(res.OrderEquipmentDetailList && res.OrderEquipmentDetailList.length > 0) {
          res.OrderEquipmentDetailList.forEach(r => {
            this.formData.ReportFailureRecord += '\n'
            this.formData.ReportFailureRecord += r.Name
            this.formData.ReportFailureRecord += '，'
            this.formData.ReportFailureRecord += r.Description
            
          })
        }
        this.formData.AfterSaleMaintenanceId = res.AfterSaleMaintenanceId
        //表示用户是选择的地址（非手动输入）
        if(res && res.RegionalId) {
          this.formData.RegionalId = res.RegionalId
          this.formData.areaName = res.RegionalName
        }

        this.formData.MaintenanceEquipmentList = this.equsInit(res.OrderEquipmentDetailList || [])

      })
    },
    closeChangeDialog(){
      this.dialogChangeFormVisible = false;
    },
    handleChangeSaveSuccess(){
      this.tabsActive = 'repairInfo';
      this.getDetail();
      this.closeChangeDialog();
    },
    handleChangeDialog(row) {
        this.currentRow = row;
        this.dialogChangeStatus = "create";
        this.isShowHistories = false;
        this.dialogChangeFormVisible = true;
    },
    handleDeleteFile(index){
      this.imgsFilePath.splice(index,1);
      this.formData.AttachmentIdList.splice(index,1);
    },
    beforeAvatarUpload(file) {
      this.imgLoading=true;
        const isLt20M = file.size / 1024 / 1024 < 20;
        if (!isLt20M) {
          this.$message({
            message: '上传图片大小不能超过 20M!',
            type: "error"
          });
        }
        return isLt20M;
      },
      handleSuccess (res, file) {
        if(this.imgsFilePath.length<9){
          this.imgsFilePath.push(res.TMessageData.Path);
          this.formData.AttachmentIdList.push(res.TMessageData.Id);
        }
        this.imgLoading=false;
      },
      handleError(err, file, fileList){
        this.imgLoading=false;
      },
      disabledFn(data, nodeType) {
          //禁选一级节点
          if(data.level <= 1) {
              return true
          }
          return false
      },
      handlePickerChange(d,e){
          // if(e){
          //     d.timeValue=e;
          // }
          // this.$forceUpdate();
      },
    // handleChangeNum(d) {
    //   d.TotalMoney = 0;
    //   d.FaultPhenomenonList.forEach(v => {
    //     v.MaintenancStructPartList.forEach(v1 => {
    //       v1.TotalPrice=v1.UnitPrice*v1.Count;
    //       d.TotalMoney += v1.TotalPrice;
    //     });
    //   });
    //   // d.TotalMoney += d.OtherExpenses;
    //   this.$forceUpdate();
    // },
    handleAfterContractDialog() {
      this.dialogAfterContractFormVisible = true
    },
    electedAfterContractData(datas) {
      if (datas && datas.length > 0) {
        this.formData.AfterContractId = datas[0].Id;
        this.formData.AfterContractCode = datas[0].AfterContractCode;
      }else{
        this.formData.AfterContractId = null;
        this.formData.AfterContractCode = '';
      }
    },
    handleDialog() {
      this.dialogAreaFormVisible = true;
    },
    electedRegionalData(data) {
      if (data) {
        this.$refs.formData.clearValidate("RegionalId");
        this.formData.RegionalId = data.Id;
        //如果当前为“转工单”方式创建报修单，在选择地区时，不需要修改 地区电话、报修人、报修人电话 三属性。
        if(!this.transId) {
          this.formData.RegionalPhone = data.RegionalPhone;
          this.formData.TroubleShooter=data.RegionalContact;
          this.formData.ReporterNumber=data.RegionalMobile;
        }
        this.formData.areaName = data.ParentName;
      }else{
        this.formData.RegionalPhone = '';
        this.formData.RegionalId = null;
        this.formData.areaName = '';
        this.formData.TroubleShooter='';
        this.formData.ReporterNumber='';
      }
    },
    closeAreaDialog() {
      this.dialogAreaFormVisible = false;
    },
    closeCauseDialog() {
      this.dialogCauseFormVisible = false;
    },
    closeSolutionDialog() {
      this.dialogSolutionFormVisible = false;
    },
    seeSolutionDetail(d) {
      this.solutionData = {
        FailureSolutionCode: d.FailureSolutionCode,
        Solution: d.SolutionContent,
        AttachmentList: d.AttachmentList
      };
      this.dialogSolutionFormVisible = true;
    },
    seeCauseDetail(d) {
      this.causeData = {
        FailureResonCode: d.FailureResonCode,
        FailureReson: d.AnalysisContent
      };
      this.dialogCauseFormVisible = true;
    },
    seeDetail(d) {
      this.failureDetailId = d.FailureCaseId;
      this.dialogFdVisible = true;
    },
    closeFdDialog() {
      this.dialogFdVisible = false;
    },
    delPMsg(d) {
      d.Phenomenon = "";
      d.CauseAnalysisList = [];
      d.SolutionList = [];
    },
    delCMsg(d, index) {
      d.CauseAnalysisList.splice(index, 1);
    },
    delSMsg(d, index) {
      d.SolutionList.splice(index, 1);
    },
    handlePhenSaveSuccess(d) {
      this.formData.MaintenanceEquipmentList[this.dIndex].FaultPhenomenonList[
        this.xIndex
      ] = d;
      this.formData.MaintenanceEquipmentList[this.dIndex].FaultPhenomenonList[this.xIndex]["errPhenomenon"] = false;
    },
    handlepMore(d, dIndex, xIndex) {
      this.dIndex = dIndex;
      this.xIndex = xIndex;
      this.outerData = d;
      this.dialogPhenFormVisible = true;
    },
    handleCMore(d, dIndex, xIndex) {
      this.dIndex = dIndex;
      this.xIndex = xIndex;
      this.outerData = d;
      this.msg = this.selectDatas.find(s => s.Id == d.FailureCaseId);
      this.dialogReferFormVisible = true;
    },
    pPitchOn(d, index, d1) {
      this.$forceUpdate();
      let t = false;
      d1.FaultPhenomenonList.forEach((v, i) => {
        if (i != index) {
          if (v.Phenomenon == d.pValue) {
            // this.$message.error('故障现象不可重复!');
            d.errPhenomenon = true;
            d.errPhenomenonMsg = "故障现象不可重复!";
            t = true;
          }
        }
      });
      if (t) return;
      if (d.pValue != d.Phenomenon) {
        d.SolutionList = [];
        d.CauseAnalysisList = [];
      }
      let a = this.selectDatas.find(v => v.FailureSymptom == d.pValue);
      if (a) {
        d.IsNew = false;
        d.FailureCaseId = a.Id;
        failurecase.getSubitem({ Id: a.Id }).then(res => {
          this.cSelectDatas = res.FailureAnalysiseList;
          this.sSelectDatas = res.FailureSolutionList;
        });
      } else {
        d.IsNew = true;
        d.FailureCaseId = "";
        this.cSelectDatas = [];
        this.sSelectDatas = [];
      }
      d.Phenomenon = d.pValue;
      d.pValue = "";
    },
    cPitchOn(d) {
      d.errCauseAnalysis = false;
      let data = {
        Id: "",
        AnalysisContent: "",
        IsNew: false,
        FailureCaseAnalysisId: "",
        FailureResonCode: ""
      };
      let a = this.cSelectDatas.find(v => v.FailureReson == d.CauseAnalysis);
      if (a) {
        data.FailureCaseAnalysisId = a.Id;
        data.AnalysisContent = a.FailureReson;
        data.FailureResonCode = a.FailureResonCode;
      } else {
        data.IsNew = true;
        data.AnalysisContent = d.CauseAnalysis;
      }
      for (var i = 0; i < d.CauseAnalysisList.length; i++) {
        if (d.CauseAnalysisList[i].AnalysisContent == d.CauseAnalysis) {
          let index = i;
          d.CauseAnalysisList.splice(index, 1); //存在即删除
        }
      }
      d.CauseAnalysis = "";
      d.CauseAnalysisList.unshift(data);
      this.$forceUpdate();
    },
    sPitchOn(d) {
      d.errSolution = false;
      let data = {
        Id: "",
        SolutionContent: "string",
        IsNew: false,
        FailureCaseSolutionId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        FailureSolutionCode: "",
        AttachmentList: []
      };
      let a = this.sSelectDatas.find(v => v.Solution == d.Solution);
      if (a) {
        data.FailureCaseSolutionId = a.Id;
        data.SolutionContent = a.Solution;
        data.FailureSolutionCode = a.FailureSolutionCode;
        data.AttachmentList = a.AttachmentList;
      } else {
        data.IsNew = true;
        data.SolutionContent = d.Solution;
      }
      for (var i = 0; i < d.SolutionList.length; i++) {
        if (d.SolutionList[i].SolutionContent == d.Solution) {
          let index = i;
          d.SolutionList.splice(index, 1); //存在即删除
        }
      }
      d.Solution = "";
      d.SolutionList.unshift(data);
      this.$forceUpdate();
    },
    loadSelectData() {
      failurecase
        .getListByCondition({ failureCaseCode: "", failureSymptom: "" })
        .then(res => {
          res.forEach(v => {
            v.value = v.FailureSymptom;
          });
          this.selectDatas = res;
        });
    },
    handleNewCase(d, d1) {
      d.MaintenanceEquipmentId = d1.mainKey;
      this.caseData = d;
      this.dialogNewFormVisible = true;
    },

    remoteChange(val, d) {
      this.$set(d, d.Phenomenon, val);
    },
    handleSelect(item, d) {
      this.$set(d, d.Phenomenon, item.val);
    },
    handleBlur(val, d) {
      d.pShow = false;
      this.$forceUpdate();
    },
    handleFocus(d) {
      d.pShow = true;
      this.$forceUpdate();
      if (d.pValue.length > 0) {
        var restaurants = this.selectDatas;
        var results = d.pValue
          ? restaurants.filter(this.createStateFilter(d.pValue, 0))
          : restaurants;
        d.options = results;
      } else {
        d.options = this.selectDatas;
      }
      if (d.options.length > 0) {
        d.pShow = true;
      } else {
        d.pShow = false;
      }
    },
    handleSBlur(d) {
      d.sShow = false;
      this.$forceUpdate();
    },
    handleCBlur(d) {
      d.cShow = false;
      this.$forceUpdate();
    },
    handleSFocus(d) {
      this.$forceUpdate();
      d.sShow = true;
      if (d.Solution.length > 0) {
        var restaurants = this.sSelectDatas;
        var results = d.Solution
          ? restaurants.filter(this.createStateFilter(d.Solution, 2))
          : restaurants;
        d.sOptions = results;
      } else {
        d.sOptions = this.sSelectDatas;
      }
      if (d.sOptions.length > 0) {
        d.sShow = true;
      } else {
        d.sShow = false;
      }
    },
    handleCFocus(d) {
      this.$forceUpdate();
      d.cShow = true;
      if (d.CauseAnalysis.length > 0) {
        var restaurants = this.cSelectDatas;
        var results = d.CauseAnalysis
          ? restaurants.filter(this.createStateFilter(d.CauseAnalysis, 1))
          : restaurants;
        d.cOptions = results;
      } else {
        d.cOptions = this.cSelectDatas;
      }
      if (d.cOptions.length > 0) {
        d.cShow = true;
      } else {
        d.cShow = false;
      }
    },
    handleSInput(d) {
      d.sShow = true;
      this.$forceUpdate();
      if (d.Solution.length > 0) {
        var restaurants = this.sSelectDatas;
        var results = d.Solution
          ? restaurants.filter(this.createStateFilter(d.Solution, 2))
          : restaurants;
        d.sOptions = results;
      } else {
        d.sOptions = this.sSelectDatas;
      }
      if (d.sOptions.length > 0) {
        d.sShow = true;
      } else {
        d.sShow = false;
      }
    },
    handleCInput(d) {
      d.cShow = true;
      this.$forceUpdate();
      if (d.CauseAnalysis.length > 0) {
        var restaurants = this.cSelectDatas;
        var results = d.CauseAnalysis
          ? restaurants.filter(this.createStateFilter(d.CauseAnalysis, 1))
          : restaurants;
        d.cOptions = results;
      } else {
        d.cOptions = this.cSelectDatas;
      }
      if (d.cOptions.length > 0) {
        d.cShow = true;
      } else {
        d.cShow = false;
      }
    },
    remoteMethod(val, d) {
      d.pShow = true;
      d.errPhenomenon = false;
      this.$forceUpdate();
      if (val.length > 0) {
        var restaurants = this.selectDatas;
        var results = val
          ? restaurants.filter(this.createStateFilter(val, 0))
          : restaurants;
        d.options = results;
      } else {
        d.options = this.selectDatas;
      }
      if (d.options.length > 0) {
        d.pShow = true;
      } else {
        d.pShow = false;
      }
    },
    createStateFilter(queryString, type) {
      return state => {
        if (type === 0) {
          return (
            state.FailureSymptom.toLowerCase().indexOf(
              queryString.toLowerCase()
            ) === 0
          );
        } else if (type === 1) {
          return (
            state.FailureReson.toLowerCase().indexOf(
              queryString.toLowerCase()
            ) === 0
          );
        } else {
          return (
            state.Solution.toLowerCase().indexOf(queryString.toLowerCase()) ===
            0
          );
        }
      };
    },
    handleChoiceSelect(item, d) {
      d.pValue = item.FailureSymptom;
      d.pShow = false;
      d.errPhenomenon = false;
      this.$forceUpdate();
    },
    handleSChoice(item, d) {
      d.Solution = item.Solution;
      d.sShow = false;
      this.$forceUpdate();
    },
    handleCChoice(item, d) {
      d.CauseAnalysis = item.FailureReson;
      d.cShow = false;
      this.$forceUpdate();
    },
    intPhenomenonChange(d) {
      if (d.Phenomenon) d.errPhenomenon = false;
      else d.errPhenomenon = true;
    },
    intCauseAnalysisChange(d) {
      if (d.CauseAnalysis) d.errCauseAnalysis = false;
      else d.errCauseAnalysis = true;
    },
    intSolutionChange(d) {
      if (d.Solution) d.errSolution = false;
      else d.errSolution = true;
    },
    handleChangeInput(a) {
      
      // this.formData.MaintenanceEquipmentList[a].TotalMoney = 0;
      this.formData.MaintenanceEquipmentList[a].FaultPhenomenonList.forEach(
        v => {
          v.MaintenancStructPartList.forEach(v1 => {
            v1.TotalPrice=v1.UnitPrice*v1.Count;
            v1.TotalPrice=Math.round(v1.TotalPrice * 100) / 100;
            // this.formData.MaintenanceEquipmentList[a].TotalMoney +=v1.TotalPrice;
          });
        }
      );
      // this.formData.MaintenanceEquipmentList[a].TotalMoney +=this.formData.MaintenanceEquipmentList[a].OtherExpenses;
    },
    closePhenDialog() {
      this.dialogPhenFormVisible = false;
    },
    closeReferDialog() {
      this.dialogReferFormVisible = false;
    },
    saveRefSuccess() {
      this.closeReferDialog();
    },
    closeDialog() {
      this.dialogAcceFormVisible = false;
    },
    closeNewDialog() {
      this.dialogNewFormVisible = false;
    },
    handleNewSaveSuccess() {
      this.getDetail();
      this.closeNewDialog();
    },
    multip(prams1, prams2) {
      let temp1 = new Dec(prams1);
      let temp2 = new Dec(prams2);
      return temp1.mul(temp2).toNumber();
    },
    resetFormData() {
      let temp = {
        areaName: "",
        Id: "", //项目ID
        ReportTime: new Date(), //报修时间
        ReportEmployee: [], //记录人
        TroubleShooter: "", //故障报告人
        ReporterNumber: "", //报告人电话
        RegionalId: "", // 安装地区
        AgentEmployeeList: [],
        AgentIdsNames: [],
        TotalTime: '',
        HandlerEmployeeList: [], //实施人员
        UseVehicle: "", //使用车辆
        OvertimeNightWork: 1, //加班/夜勤
        IsSignBill:1,
        SignBillRemark:'',
        AttachmentList:[],
        AttachmentIdList:[],
        WorkingHours: 0, //工时
        ServiceNo: "", //单号
        EntryTime: null, //录入日期
        ReportFailureRecord: '',

        MaintenanceEquipmentList: [],
        timeValue:'',
        StationSign:'',
        OtherExpenses:'',
        ArrivalTime:'',
        DepartureTime:'',
        // Approval: {//审批信息
        //     ApprovalEmployeeList: [[]],
        //     ApprovalType: 1,
        //     ApprovalOperatorEmployeeList: [], //已审批人员
        //     NoApprovalEmployeeList: [], //未审批人员
        //     CCEmployeeList: [], //抄送人
        //     ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
        //     ApprovalState: 1, //1: 进行中; 2: 已完成
        //     ApprovalResult: 1, //1: 通过； 2：不通过
        // },
      };
      this.imgsFilePath=[];
      this.formData = Object.assign({}, this.formData, temp);
    },
    handleChangeOwnerUsers(users) {
      if (users && users.length > 0) {
        this.formData.ReportEmployee = [users[0]];
      } else {
        this.formData.ReportEmployee = [];
      }
    },
    //报修单处理审批
    // handleApproval() {
    //     let postData = this.$refs.approvalDetail.getData()
    //     postData.BusinessId = this.id
    //     let approvalLabel = projVars.vars.approvalResult.find(s => s.value == postData.ApprovalResultState).label

    //     this.$confirm(`是否确认 ${approvalLabel} 当前审批?`, '提示', {
    //         confirmButtonText: '确定',
    //         cancelButtonText: '取消',
    //         type: 'warning'
    //     }).then(() => {
    //         //项目创建审批
    //         this.disabledBtn = true
    //         mom.createApproval(postData).then(res => {
    //             this.disabledBtn = false
    //             this.$notify({
    //                 title: "提示",
    //                 message: "审批成功",
    //                 type: "success",
    //                 duration: 2000
    //             });
    //             this.$refs.appDialogRef.createData()
    //         }).catch(err => {
    //             this.disabledBtn = false
    //         })
    //     })
    // },
    //验证某一项设备的故障现象的必填项
    tErrors(a) {
      this.selectGroup = this.formData.MaintenanceEquipmentList[a[0]].Code;
    },
    //根据验证错误，定位到对应的设备列表中的某一项设备
    trackErrors(a) {
      this.$refs.formData.validate((res, errors) => {
        //当”设备列表“下有多条记录时，需要根据错误提示切换到不同的”设备两目“下，方便用户看到错误提示信息
        //当所有非”设备列表“下的验证都通过后，才执行逻辑
        let errFields = JSON.parse(JSON.stringify(errors));
        let errorKeys = Object.keys(errFields);

        let isToggle =
          errorKeys.length > 0 &&
          errorKeys.every(
            s =>
              s !== "MaintenanceEquipmentList" &&
              s.indexOf("MaintenanceEquipmentList") > -1
          );
        if (isToggle) {
          //存在错误的”设备列表“索引
          let errorKeysIdx = errorKeys.map(s => {
            s = s.substring(s.indexOf(".") + 1).substring(0, s.indexOf("."));
            return parseInt(s);
          });
          errorKeysIdx = Array.from(new Set(errorKeysIdx)); //去重
          //当前选中的设备索引
          let idx = this.formData.MaintenanceEquipmentList.findIndex(
            s => s.Code == this.selectGroup
          );
          //如果当前选中的设备中不存在校验失败的字段，那么就需要切换到第一组存在错误的设备条目中
          if (errorKeysIdx.findIndex(s => s === idx) == -1 && a[0] != idx) {
            if (errorKeysIdx.length > 0) {
              this.selectGroup = this.formData.MaintenanceEquipmentList[
                errorKeysIdx[0]
              ].Code;
            }
          }
        }
      });
    },
    /**
     * optType: 1: 保存（不校验数据）；2：提交：
     */
    createData(optType) {
      this.formData.WorkingHours = this.workingHour
      
      if(this.formData.MaintenanceEquipmentList && this.formData.MaintenanceEquipmentList.length > 0) {
        this.formData.MaintenanceEquipmentList.forEach(s => {
          if(s.FaultPhenomenonList && s.FaultPhenomenonList.length > 0) {
            s.FaultPhenomenonList.forEach(n => {
              if(n.CauseAnalysis) {
                n.CauseAnalysisList = [{
                  "Id": "",
                  "AnalysisContent": n.CauseAnalysis,
                  "IsNew": true,
                  "FailureCaseAnalysisId": "",
                  "FailureResonCode": ""
                }]
              }

              if(n.Solution) {
                n.SolutionList = [{
                  "SolutionContent": n.Solution,
                  "FailureCaseSolutionId": "",
                  "FailureSolutionCode": "",
                  "AttachmentList": [],
                  "Id": "",
                  "IsNew": true
                }]
              }
            })
          }
        })
      }


      if(this.formData.IsSignBill == 0) this.formData.IsSignBill='';
      let flagValid = new Promise((resolve, reject) => {
        return resolve(true);
      });
      let validList = [flagValid];

      let validate = this.$refs.formData.validate();

      if (this.dialogStatus == "create" || this.dialogStatus == "update") {
        validList.push(validate);
      }
      if (optType == 2 && this.dialogStatus == "handle") {
        validList.push(validate);

        //如果是”处理“报修单，需要验证”审批“区块信息
        // if(this.dialogStatus == 'handle') {
        //     let approvalPanelValidate = this.$refs.approvalPanel.validate()
        //     validList.push(approvalPanelValidate)
        // }
      }
      let goPass = true,
        errArr = [];
      // 验证某一项设备的故障现象的必填项
      if (optType == 2 && this.dialogStatus == "handle") {
        if (
          this.formData.MaintenanceEquipmentList &&
          this.formData.MaintenanceEquipmentList.length > 0
        ) {
          for (
            var i = 0;
            i < this.formData.MaintenanceEquipmentList.length;
            i++
          ) {
            if (
              this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList &&
              this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList
                .length > 0
            ) {
              for (
                var i1 = 0;
                i1 <
                this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList
                  .length;
                i1++
              ) {
                // for(let key in this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[i1]){
                if (
                  this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[
                    i1
                  ].Phenomenon === ""
                ) {
                  errArr.push(i);
                  this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[
                    i1
                  ]["errPhenomenon"] = true;
                  this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[
                    i1
                  ]["errPhenomenonMsg"] = "故障现象不能为空!";
                  goPass = false;
                }
                // if(this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[i1].CauseAnalysisList.length<=0){
                //     errArr.push(i);
                //     this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[i1]['errCauseAnalysis']=true;
                //     goPass=false;
                // }
                // if(this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[i1].SolutionList.length<=0){
                //     errArr.push(i);
                //     this.formData.MaintenanceEquipmentList[i].FaultPhenomenonList[i1]['errSolution']=true;
                //     goPass=false;
                // }
                // }
              }
            }
          }
        }
        this.$forceUpdate();
      }
      Promise.all(validList)
        .then(valid => {
          if (goPass) {
            if(this.dialogStatus == "handle" && this.formData.IsSignBill == 2){
              this.formData.timeValue='';
              this.formData.ArrivalTime='';
              this.formData.DepartureTime='';
              this.formData.OvertimeNightWork=1;
              this.formData.WorkingHours= 0; //工时
              this.formData.ServiceNo= ""; //单号
              this.formData.EntryTime= ""; //录入日期
              this.formData.StationSign='';
              this.formData.OtherExpenses=0;
            }
            let postData = JSON.parse(JSON.stringify(this.formData));
            postData.SaveOrSubmit = optType; // ”保存“或”提交“ 操作

            // if(!this.isHandle) {
            //     postData.Approval = this.$refs.approvalPanel.getData() //审批层区块
            //     postData.Approval.ApprovalEmployeeIdList = postData.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
            //     postData.Approval.CCEmployeeIdList = postData.Approval.CCEmployeeList.map(s => s.EmployeeId)
            // }
            postData.ReportEmployeeId = postData.ReportEmployee[0].EmployeeId;
              if(postData.timeValue && postData.timeValue.length>0){
                  postData.ArrivalTime = postData.timeValue[0];
                  postData.DepartureTime = postData.timeValue[1];
              }
            if (postData.MaintenanceEquipmentList) {
              postData.MaintenanceEquipmentList.forEach(item => {
                if (
                  item.FaultPhenomenonList &&
                  item.FaultPhenomenonList.length > 0
                ) {
                  item.FaultPhenomenonList.forEach(v => {
                    if (v.AttachmentList) {
                      v.AttachmentIdList = v.AttachmentList.map(s => s.Id);
                    }
                  });
                }
              });
            }
            //因为编辑获取详细时，id 被换成了 设备id，而主键id保存到 mainKey（所以需要还原）
            if (
              postData.MaintenanceEquipmentList &&
              postData.MaintenanceEquipmentList.length > 0
            ) {
              postData.MaintenanceEquipmentList = postData.MaintenanceEquipmentList.map(
                s => {
                  s.OrderEquipmentDetailId = s.Id; //此时的 Id 为设备id
                  s.Id = s.mainKey;
                  return s;
                }
              );
            }
            if (
              optType == 2 &&
              this.dialogStatus == "handle" &&
              this.formData.MaintenanceEquipmentList.some(
                s => s.HandlingResultStatus == 1
              )
            ) {
              this.$confirm(`设备列表中包含未开始的设备，是否提交?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
              })
                .then(() => {
                  this.disabledBtn = true;
                  mom
                    .dealWithAsync(postData)
                    .then(res => {
                      this.disabledBtn = false;
                      this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                      });
                      this.$refs.appDialogRef.createData();
                    })
                    .catch(err => {
                      this.disabledBtn = false;
                    });
                })
                .catch(err => {
                  this.disabledBtn = false;
                });
            } else {
              let result = null;
              this.disabledBtn = true;
              if (this.dialogStatus == "create") {
                delete postData.Id;
                postData.ReportTime=this.formatterDate(new Date(this.formData.ReportTime));
                result = mom.add(postData);
              } else if (this.dialogStatus == "update") {
                result = mom.edit(postData);
              } else if (this.dialogStatus == "handle") {
                result = mom.dealWithAsync(postData);
              }

              result
                .then(res => {
                  this.disabledBtn = false;
                  this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                  });
                  this.$refs.appDialogRef.createData();
                })
                .catch(err => {
                  this.disabledBtn = false;
                });
            }
          } else {
            this.tErrors(errArr);
          }
        })
        .catch(err => {
          this.trackErrors(errArr);
        });
    },
    formatterDate(val) {
      let f = this.$options.filters["dateFilter"];
      return f(val, "YYYY-MM-DD HH:mm:ss");
      // return f(row.ReleaseTime, 'YYYY-MM-DD HH:mm:ss')
    },
    filterName(d) {
      if (d == "Phenomenon") {
        return "故障现象";
      } else if (d == "CauseAnalysis") {
        return "原因分析";
      } else {
        return "解决方法";
      }
    },
    renderHeader(h, { column }) {
      return h("span", [
        h("span", column.label),
        h(
          "span",
          {
            style: "color: red"
          },
          " *"
        )
      ]);
    },
    getDetail() {
      this.loading = true;
      mom
        .detail({
          id: this.id
          // , approvalId: this.approvalId
        })
        .then(res => {

          this.loading = false;
          this.isTip = false;
          let temp = Object.assign({}, this.formData, res);
          temp.RegionalPhone = temp.RegionalMobile
         
          temp.ReportEmployee = [temp.ReportEmployee];
          temp.MaintenanceEquipmentList = this.equsInit(
            temp.MaintenanceEquipmentList,
            true
          );
          if (temp.OvertimeNightWork == 0) {
            temp.OvertimeNightWork = 1;
          }
          temp.MaintenanceEquipmentList.forEach(v => {
            v.FaultPhenomenonList.forEach(v1 => {
              if (v1.FaultType === 0) {
                v1.FaultType = null;
              }
            });
            // v.StationSign=v.StationSign ? v.StationSign : '无';
          });
          // if(this.dialogStatus == 'handle' && !temp.Approval) {
          //     temp.Approval = {//审批信息
          //         ApprovalEmployeeList: [[]],
          //         ApprovalType: 1,
          //         ApprovalOperatorEmployeeList: [], //已审批人员
          //         NoApprovalEmployeeList: [], //未审批人员
          //         CCEmployeeList: [], //抄送人
          //         ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
          //         ApprovalState: 1, //1: 进行中; 2: 已完成
          //         ApprovalResult: 1, //1: 通过； 2：不通过
          //     }
          // }
          this.formData = temp;
          this.formData.RegionalId = res.RegionalId;
          this.formData.areaName = res.RegionalName;
          if (
            this.formData.AgentEmployeeList &&
            this.formData.AgentEmployeeList.length > 0
          ) {
            this.formData.AgentIdsNames = [];
            this.formData.AgentEmployeeList.forEach(v => {
              this.formData.AgentIdsNames.push(v.Name);
            });
            this.formData.AgentIdsNames = this.formData.AgentIdsNames.join(
              "、"
            );
          }
          let s = null,
            m = {
              options: [],
              cOption: [],
              sOption: [],
              pShow: false,
              cShow: false,
              sShow: false,
              pValue: "",
              errPhenomenon: false,
              errPhenomenonMsg: "故障现象不能为空!",
              errCauseAnalysis: false,
              errSolution: false
            };
            this.formData.AttachmentList.forEach(v => {
              this.imgsFilePath.push(v.Path);
              this.formData.AttachmentIdList.push(v.Id);
            })
              if(this.formData.ArrivalTime){
                  this.formData.timeValue = [this.formData.ArrivalTime, this.formData.DepartureTime];
              }else{
                  this.formData.timeValue='';
              }

          this.formData.MaintenanceEquipmentList.forEach(n => {
            if(n.FaultPhenomenonList && n.FaultPhenomenonList.length > 0 && n.CauseAnalysisList && n.CauseAnalysisList.length > 0) { 
              n.CauseAnalysis = n.FaultPhenomenonList[0].AnalysisContent
            }

            if(n.FaultPhenomenonList && n.FaultPhenomenonList.length > 0 && n.SolutionList && n.SolutionList.length > 0) { 
              n.Solution = n.SolutionList[0].SolutionContent
            }
          })
          
          //“处理”默认显示当天，切不可修改；
          if(this.dialogStatus == "handle") {
            this.formData.EntryTime = new Date()
          }

          this.formData.MaintenanceEquipmentList.forEach((v1,index1) => {
            if (v1.FaultPhenomenonList && v1.FaultPhenomenonList.length > 0) {
              v1.FaultPhenomenonList.forEach(v2 => {
                v2 = Object.assign(v2, m);
                // v2.IsNew=false;
                //v2.FailureCaseId='';
                if (
                  v2.MaintenancStructPartList &&
                  v2.MaintenancStructPartList.length > 0
                ) {
                  v2.MaintenancStructPartList.forEach(v3 => {
                    s = vars.handling.find(v4 => v4.value == v3.ProcessMode);
                    v3.ProcessModeName = s.label;
                  });
                }
              });
            }else{
              v1.FaultPhenomenonList.push({
                FaultType: null,
                options: [],
                cOption: [],
                sOption: [],
                pShow: false,
                cShow: false,
                sShow: false,
                pValue: "",
                errPhenomenon: false,
                errPhenomenonMsg: "故障现象不能为空!",
                errCauseAnalysis: false,
                errSolution: false,
                id: "",
                Phenomenon: "",
                CauseAnalysis: "",
                Solution: "",
                MaintenancStructPartList: [],
                AttachmentList: [],
                IsNew: false,
                FailureCaseId: "",
                CauseAnalysisList: [],
                SolutionList: []
              });
            }
          });
        })
        .then(err => {
          this.loading = false;
        });
    },
    /**
     * 订单创建
     */
    handleOrderCreateDialog() {
      this.dialogOrderCreateFormVisible = true;
    },
    closeOrderCreateDialog() {
      this.dialogOrderCreateFormVisible = false;
    },
    handleOrderCreateSaveSuccess() {
      this.getList();
      this.closeOrderCreateDialog();
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    //isFirstLoad: true， 如果是首次（如：详细界面）加载，需要吧id换成 设备id（不然无法选中已选择的项目）
    equsInit(datas, isFirstLoad = false) {
      if (datas && datas.length > 0) {
        this.selectGroup = datas[0].Code;
        return datas.map(s => {
          s.label = s.Name;
          s.value = s.Code;
          // EquipmentId: s.OrderEquipmentDetailId,
          // Id: s.OrderEquipmentDetailId,
          // OrderEquipmentDetailId: s.Id,

          if (isFirstLoad) {
            s.mainKey = s.Id; // 将主键保存起来，提交的时候在还原 （在选择报修单时，需要默认选中，所有需要将 Id 属性当作 设备Id 来使用）
            s.Id = s.OrderEquipmentDetailId;
          }
          return s;
          // s.label = s.Name
          // s.value = s.Code
          // s.EquipmentId=s.OrderEquipmentDetailId
          // s.OrderEquipmentDetailId = s.Id
          // return s
        });
      }
      return [];
    },

    handleChange(rows) {
      let list = this.equsInit(rows);

      this.formData.MaintenanceEquipmentList = list;
      if (!this.selectGroup && list && list.length > 0) {
        this.selectGroup = list[0].value;
      }
    },
    handleRemove(idx) {
      let delObj = JSON.parse(
        JSON.stringify(this.formData.MaintenanceEquipmentList[idx])
      );
      this.formData.MaintenanceEquipmentList.splice(idx, 1);

      if (delObj.Code == this.selectGroup) {
        if (this.formData.MaintenanceEquipmentList.length > 0) {
          this.selectGroup = this.formData.MaintenanceEquipmentList[0].Code;
        } else {
          this.selectGroup = "";
        }
      }
    },
    handleAddReplace(item, i, i1) {
      this.acceData = item;
      this.leftListIndex = i;
      this.rightListIndex = i1;
      this.dialogReplaceStatus = "create";
      this.dialogAcceFormVisible = true;
    },
    handleDetailReplace(d, item) {
      this.acceData = item;
      this.msgReplace = d;
      this.dialogReplaceStatus = "detail";
      this.dialogAcceFormVisible = true;
    },
    handleEditReplace(d, i, i1, i2, item) {
      this.acceData = item;
      this.leftListIndex = i;
      this.rightListIndex = i1;
      this.structPartIndex = i2;
      this.dialogReplaceStatus = "edit";
      this.msgReplace = d;
      this.dialogAcceFormVisible = true;
    },
    handleSaveSuccess(t, d, d1) {
      if (!t) {
        this.dialogAcceFormVisible = false;
      }
      if (d1) {
        d1.ByReplaceMaintenancStructPart = d;
        this.formData.MaintenanceEquipmentList[
          this.leftListIndex
        ].FaultPhenomenonList[
          this.rightListIndex
        ].MaintenancStructPartList.push(d1);
      } else {
        this.formData.MaintenanceEquipmentList[
          this.leftListIndex
        ].FaultPhenomenonList[
          this.rightListIndex
        ].MaintenancStructPartList.push(d);
      }
    },
    saveEdit(d, d1) {
      this.dialogAcceFormVisible = false;
      if (d1) {
        d1.ByReplaceMaintenancStructPart = d;
        this.formData.MaintenanceEquipmentList[
          this.leftListIndex
        ].FaultPhenomenonList[this.rightListIndex].MaintenancStructPartList[
          this.structPartIndex
        ] = Object.assign(
          this.formData.MaintenanceEquipmentList[this.leftListIndex]
            .FaultPhenomenonList[this.rightListIndex].MaintenancStructPartList[
            this.structPartIndex
          ],
          d1
        );
      } else {
        this.formData.MaintenanceEquipmentList[
          this.leftListIndex
        ].FaultPhenomenonList[this.rightListIndex].MaintenancStructPartList[
          this.structPartIndex
        ] = Object.assign(
          this.formData.MaintenanceEquipmentList[this.leftListIndex]
            .FaultPhenomenonList[this.rightListIndex].MaintenancStructPartList[
            this.structPartIndex
          ],
          d
        );
      }
    },
    handleRemoveReplace(item, idx, a) {
      item.MaintenancStructPartList.splice(idx, 1);
      // this.formData.MaintenanceEquipmentList[a].TotalMoney = 0;
      // this.formData.MaintenanceEquipmentList[a].FaultPhenomenonList.forEach(
      //   v => {
      //     v.MaintenancStructPartList.forEach(v1 => {
      //       v1.TotalPrice=v1.UnitPrice*v1.Count;
      //       this.formData.MaintenanceEquipmentList[a].TotalMoney += v1.TotalPrice;
      //     });
      //   }
      // );
      // this.formData.MaintenanceEquipmentList[a].TotalMoney +=this.formData.MaintenanceEquipmentList[a].OtherExpenses;
    },
    handleAddFault(item) {
      item.FaultPhenomenonList.push({
        FaultType: null,
        options: [],
        cOption: [],
        sOption: [],
        pShow: false,
        cShow: false,
        sShow: false,
        pValue: "",
        errPhenomenon: false,
        errPhenomenonMsg: "故障现象不能为空!",
        errCauseAnalysis: false,
        errSolution: false,
        id: "",
        Phenomenon: "",
        CauseAnalysis: "",
        Solution: "",
        MaintenancStructPartList: [],
        AttachmentList: [],
        IsNew: false,
        FailureCaseId: "",
        CauseAnalysisList: [],
        SolutionList: []
      });
    },
    handleRemoveFault(item, idx, a) {
      item.FaultPhenomenonList.splice(idx, 1);
      // this.formData.MaintenanceEquipmentList[a].TotalMoney = 0;
      // this.formData.MaintenanceEquipmentList[a].FaultPhenomenonList.forEach(
      //   v => {
      //     v.MaintenancStructPartList.forEach(v1 => {
      //       this.formData.MaintenanceEquipmentList[a].TotalMoney +=
      //         v1.UnitPrice;
      //     });
      //   }
      // );
      // this.formData.MaintenanceEquipmentList[a].TotalMoney +=this.formData.MaintenanceEquipmentList[a].OtherExpenses;
    },
    handleFilesUpChange(item, files) {
      item.AttachmentList = files;
    },

    getTotalPrice(list) {
      if (list && list.length > 0) {
        return list.reduce((prev, curr) => {
          return prev + this.multip(curr.UsageAmount, curr.UnitPrice);
        }, 0);
      }
      return 0;
    },
    handleShowRecord() {
      this.dialogRecordVisible = true
    },
    closeRecordDialog() {
      this.dialogRecordVisible = false
    },
  }
};
</script>

<style lang="css" scoped>
.upDiv >>> .el-icon-circle-close{
  color:white;
}
.elUpload >>> .el-upload-list{
  display:none;
}
.divLeft >>> .el-card__body{
  padding-bottom:0;
}
.p10 >>> .el-card__body{
    padding:10px!important;
}
.tMoney >>> .el-input {
  width: 100% !important;
}
.elNumber >>> .el-input {
  width: 100% !important;
}
.btn-wrapper >>> .el-button {
  padding-top: 0;
  padding-bottom: 0;
}

.cus-textarea-wrapper >>> .el-form-item__content,
.case-wrapper >>> .el-form-item__content,
.list-wrapper2 >>> .el-form-item__content {
  margin-left: 0 !important;
}

.right-wrapper >>> .el-tabs__header {
  margin-bottom: 0;
}

.wrapper >>> .el-card__body{
  padding: 10px;
}

.list-wrapper2>>> .list-wrapper{
  padding-right: 0;
}

.wrapper >>> .el-progress-bar {
  margin-right: -60px;
  padding-right: 55px;
}

.box-card-sp >>> .el-form-item{
  margin-bottom: 0;
}
</style>

<style lang="scss" scoped>
.upDiv{
  height:100%;
  ul{
    height: calc(100% - 86px);
    width:calc(100% - 40px);
    position: absolute;
    overflow-y: auto;
    li{
      width:98%;
      height:450px;
      position: relative;
      margin:5px;
      overflow: hidden;
      background:#282828;
      >i{
        display:none;
        z-index: 10;
        color:#ccc;
      }
      .elImg{
        width:100%;
        height:100%;
        // height:auto;
        // position:absolute;
        // top:50%;
        // left:50%;
        // transform:translate(-50%,-50%);
      }
    }
    li:hover{
      >i{
        display:block;
        position: absolute;
        right:0;
        top:0;
      }
    }
  }
}
.elUpload{
  float: right;
  padding-right:10px;
}
.divLeft{
  width:100%;
}
.cLeft{
  width:900px;
}
.cRight{
  position: absolute;
  right: 0;
  padding-bottom: 10px;
  width:340px;
  height:100%;
  .el-card{
    height:100%;
    overflow-y:auto;
  }
}
.boxCard{
    margin:10px;
}
.div-popover {
  padding: 6px 0;
  position: absolute;
  background: #fff;
  min-width: 250px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  z-index: 2000;
  color: #606266;
  line-height: 1.4;
  text-align: justify;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  word-break: break-all;
  margin-top: 6px;
  > div {
    padding: 0 20px;
    cursor: pointer;
    height: 34px;
    line-height: 34px;
  }
  > div:hover {
    background: #f5f7fa;
  }
}
.div-popover:before {
  box-sizing: content-box;
  width: 0px;
  height: 0px;
  position: absolute;
  top: -16px;
  left: 41px;
  padding: 0;
  border-bottom: 8px solid #ffffff;
  border-top: 8px solid transparent;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  display: block;
  content: "";
  z-index: 12;
}
.div-popover:after {
  box-sizing: content-box;
  width: 0px;
  height: 0px;
  position: absolute;
  top: -18px;
  left: 40px;
  padding: 0;
  border-bottom: 9px solid #ebeef5;
  border-top: 9px solid transparent;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  display: block;
  content: "";
  z-index: 10;
}

.elCol {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
.elFormUl {
  li {
    width: 96%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > div:nth-child(1) {
      display: flex;
      width: 690px;
      align-items: center;
      span:nth-child(2) {
        display: inline-block;
        margin-left: 6px;
      }
    }
  }
}
.nSpan {
  display: inline-block;
  border: 1px solid #f59a23;
  color: #f59a23;
  padding: 0 6px;
  border-radius: 6px;
  height: 18px;
  line-height: 18px;
}
.el-icon-more {
  position: absolute;
  right: 40px;
  top: 6px;
  font-size: 18px;
  transform: rotate(90deg);
  z-index: 100;
  color: #409eff;
  background: white;
  cursor: pointer;
}
.el-icon-success {
  position: absolute;
  right: 6px;
  top: 4px;
  font-size: 20px;
  z-index: 100;
  color: #04d919;
  cursor: pointer;
}
.elP {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  position: absolute;
  top: 18px;
  left: 82px;
}
// .tMoney {
//   padding: 10px;
//   border-bottom: 1px solid #dcdfe6;
//   margin-bottom: 10px;
// }
.box-card {
  margin-bottom: 10px;
  .el-icon-delete {
    color: red;
    float: right;
    margin: 8px;
    font-size: 18px;
  }
}

.card-info{

/deep/.el-card__body{
  padding: 0px;
}

}

.box-card-info{
  padding: 10px;
  /deep/.el-tabs__nav-wrap{
     overflow: hidden;
     margin-bottom: -1px;
     position: unset;
  }

  /deep/.el-tabs__header{
    padding: 0;
    margin: 0 0 15px;
    position: unset;
  }

  /deep/.el-tabs__nav-wrap::after{
    content: "";
    position: unset;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
  }
}

// .wrapper {
//   border-bottom: 1px solid #dcdfe6;
//   margin-bottom: 10px;
// }
.poEl {
  position: relative;
  > i:first-child {
    position: absolute;
    left: 4px;
    top: 4px;
    color: red;
  }
}
.required {
  color: red;
}

// .list-wrapper2{
//     box-sizing: border-box;
//     border-right: 1px solid #DCDFE6;
// }

.panel-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 4px;
  padding-left: 6px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
}
.right-wrapper {
  flex: 1;
  box-sizing: border-box;
 // margin-left: 10px;
  // .tab-content-wrapper {
  //   border-left: 1px solid #e4e7ed;
  // }
}
.title {
  font-weight: bold;
  padding-bottom: 4px;
  font-size: 14px;
  margin-left: 15px;
}

.section {
  padding: 10px;
  .col-wrapper {
    margin-bottom: 2px;
  }
  .field-title-right {
    display: inline-block;
    width: 100px;
    text-align: right;
  }
}

// .section-first {
//   border-bottom: 1px solid #dcdfe6;
// }

.status-1 {
  background-color: red;
}

.status-2 {
  background-color: #409eff;
}

.status-3 {
  background-color: #00b050;
}

.item-status {
  color: #fff;
  padding: 2px 4px;
  border-radius: 10%;
}

.item{
  margin-top: 20px;
}

.bottom-item{
  display: flex;
}

.state1{
  width: 28%;
  margin-bottom: 20px;
}

.state2{
  width: 0%;
  display: none;
  margin-bottom: 20px;
}


.state3{
  width: 72%;
  margin-bottom: 20px;
}

.state4{
  width: 100%;
  margin-bottom: 20px;
}


.from-tab{
  display: flex;
  overflow-x: auto;
}

.equipment-list{
  /deep/.el-card{
    border-radius: 0px;
  }
}

/deep/.sp-card{
  border: none!important;
}

/deep/.sp-card2{
  border-left: 1px solid #EBEEF5!important;
}

</style>
