<template>
    <div>
        <app-dialog
            title="事项管理"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :maxHeight='600'
            :width='600'
        >
            <template slot="body">
                <el-form ref="formData" :hide-required-asterisk='true' :rules="rules" :model="formData" label-position="right" :label-width="labelWidth">
                    <div class="wrapper" v-loading='loading'>
                        <div class="btn-wrapper">
                            <el-button size="mini" type="primary" @click="handleAdd">添加实施事项</el-button>
                        </div>
<!--
                        <div class="item" v-for="(item, idx) in formData.list" :key="idx">
                            <div class="item-title-wrapper">
                                <div class="item-title">
                                    <el-col :span="24" class="cus-textarea-wrapper">
                                        <el-form-item :prop="'list.' + idx + '.Name'" :rules="{required: true, message: '名称不能为空', trigger: 'blur'}">
                                            <span slot="label">{{ idx + 1 }}、<span style="color: red; margin-right: 4px;">*</span></span>
                                            <el-input maxlength="200" v-model="formData.list[idx].Name" clearable :disabled="!editable"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </div>
                                <div class="item-btns">
                                    <i v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></i>
                                    <i v-show="idx < formData.list.length - 1" class="el-icon-bottom" title="下移" @click="move('down', idx)"></i>
                                    <i class="el-icon-delete-solid" title="删除" @click="handleRemove(item)"></i>
                                </div>
                            </div>
                            <el-row class="item-content">

                            </el-row>
                        </div> -->

                        <el-card class="box-card" v-for="(item, idx) in formData.list" :key="idx">
                            <div slot="header">
                                <div class="item-title-wrapper">
                                    <div class="item-title">事项{{ idx + 1 }}</div>
                                    <div class="item-btns">
                                        <svg-icon icon-class="arrow-circle-up" v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></svg-icon>
                                        <svg-icon icon-class="arrow-circle-down" v-show="idx < formData.list.length - 1" title="下移" @click="move('down', idx)"></svg-icon>
                                        <i class="el-icon-delete-solid" style="font-size: 20px; color: red; margin-left: 4px;" title="删除" @click="handleRemove(item)"></i>
                                    </div>
                                </div>
                            </div>
                            <el-row class="item-content">
                                <el-col :span="24" class="cus-textarea-wrapper">
                                    <el-form-item
                                        label="事项名称"
                                        :prop="'list.' + idx + '.Name'"
                                        :rules="{required: true, message: '事项名称不能为空', trigger: 'blur'}"
                                    >
                                        <el-input maxlength="50" v-model="formData.list[idx].Name" clearable :disabled="!editable"></el-input>
                                    </el-form-item>
                                    <el-form-item
                                        label="重点关注"
                                        :prop="'list.' + idx + '.IsFoucs'"
                                    >
                                        <el-switch v-model="formData.list[idx].IsFoucs"></el-switch>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-card>

                        <!-- <div class="item" v-for="(item, idx) in formData.list" :key="idx">
                            <div class="item-title-wrapper">
                                <div class="item-title">事项{{ idx + 1 }}</div>
                                <div class="item-btns">
                                <i v-show="idx > 0" class="el-icon-top" title="上移" @click="move('up', idx)"></i>
                                <i
                                    v-show="idx < formData.list.length - 1"
                                    class="el-icon-bottom"
                                    title="下移"
                                    @click="move('down', idx)"
                                ></i>
                                <i class="el-icon-delete-solid" title="删除" @click="handleRemove(item)"></i>
                                </div>
                            </div>
                            <el-row class="item-content">
                                <el-col :span="24" class="cus-textarea-wrapper">
                                    <el-form-item
                                        label="事项名称"
                                        :prop="'list.' + idx + '.Name'"
                                        :rules="{required: true, message: '事项名称不能为空', trigger: 'blur'}"
                                    >
                                        <el-input maxlength="50" v-model="formData.list[idx].Name" clearable :disabled="!editable"></el-input>
                                    </el-form-item>
                                    <el-form-item
                                        label="重点关注"
                                        :prop="'list.' + idx + '.IsFoucs'"
                                    >
                                        <el-switch v-model="formData.list[idx].IsFoucs"></el-switch>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div> -->


                    </div>
                </el-form>
            </template>
            <template slot="footer">
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>
import * as impMgmt from "@/api/implementation/impManagement2"
  export default {
    name: "proces-mgmt",
    directives: {
    },
    components: {
    },
    props: {
        dialogStatus: { //create、update、detail
            type: String
        },
        //实施地区id（新增必传）
        id: {
            type: String,
            required: true
        },
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail" && this.dialogStatus != 'approval'
        },
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '工序管理'
            }else if(this.dialogStatus == 'update') {
                return '工序管理'
            }else if(this.dialogStatus == 'detail') {
                return '工序管理'
            }
        },
    },
    watch: {
        '$attrs.dialogFormVisible'(val) {
            if(val) {
                this.resetFormData()
                if(this.id) {
                    this.getProceItems()
                }

            }
        },
    },
    created() {
        this.rules = this.initRules(this.rules)

    },
    data() {
        return {
            rules: {

            },
            loading: false,
            disabledBtn: false,
            labelWidth: '100px',
            formData: {
                list: [

                ],
            },
        };
    },
    methods: {
        handleAdd() {
            this.formData.list.push({
                Name: '',
                ImplementationProcedureId: this.id,
            })
        },
        move(direction, currIdx) {
            if((direction == 'up' && currIdx > 0) || direction == 'down' && currIdx < this.formData.list.length - 1) {
                let currRow = JSON.parse(JSON.stringify(this.formData.list[currIdx]))
                let targetIdx = direction == 'up' ? currIdx - 1 : currIdx + 1
                this.formData.list.splice(currIdx, 1)
                this.formData.list.splice(targetIdx, 0, currRow)
            }
        },
        handleRemove(row) {
            this.$confirm("是否确认删除当前工序?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                var index = this.formData.list.indexOf(row);
                if (index !== -1) {
                    this.formData.list.splice(index, 1);
                }
            });
        },
        resetFormData() {
            let temp = {
                // Id: '', //项目ID
                formData: {
                    list: [],
                },
            }
            this.formData.list = Object.assign({}, this.formData.list, temp)
        },
        handleChangeUsers(row, users) {
            row.EmployeeList = users
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let tempPostData = JSON.parse(JSON.stringify(this.formData.list));

                    tempPostData = tempPostData.map((s, idx) => {
                        s.Name = s.Name.trim()
                        s.OrderIndex = (idx + 1) * 10
                        return s
                    })

                    let names = tempPostData.map(s => s.Name)
                    if((new Set(names)).size != names.length) {
                        this.$message({
                            message: '事项名称不能重复',
                            type: 'error'
                        })
                        return false
                    }

                    let postData = {
                        ImplementationProcedureId: this.id,
                        ImplementationItemEditRequestModels: tempPostData
                    }


                    this.disabledBtn = true
                    impMgmt.editImpItems(postData).then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData()
                    }).catch(err => {
                        this.disabledBtn = false
                    })
                }
            });
        },
        //获取工序列表
        getProceItems() {
            this.loading = true
            impMgmt.getImpItems({ImplementationProcedureId: this.id}).then(res => {
                this.loading = false
                this.formData.list = res
            })
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }
};
</script>

<style scoped>
.wrapper >>> .el-card:not(:last-child){
    margin-bottom: 10px;
}

.wrapper >>> .el-card__body{
    padding: 10px;
}
</style>

<style lang="scss" scoped>
.wrapper{
    min-height: 300px;
    .btn-wrapper{
        margin-bottom: 10px;
    }
    // .item {
    //     border: 1px solid #ebeef5;
    //     border-radius: 10px;
    //     margin-bottom: 10px;
        // padding-top: 10px;
        .item-title-wrapper {
            display: flex;
            height: 34px;
            line-height: 34px;
            // margin-bottom: 4px;
            padding: 0 10px;
            // border-bottom: 1px solid #ebeef5;
            .item-title {
                flex: 1;
                margin-bottom: 4px;
            }
            .item-btns{
                display: flex;
                align-items: center;
                justify-content: flex-end;
                svg {
                    cursor: pointer;
                    font-size: 20px;
                    vertical-align: middle;
                    margin-left: 4px;
                }
            }
        }
        .item-content {
            padding-right: 10px;
        }
    // }
}

</style>
