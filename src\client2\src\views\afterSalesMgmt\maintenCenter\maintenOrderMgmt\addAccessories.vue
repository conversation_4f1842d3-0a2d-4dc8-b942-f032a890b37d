<template>
  <div class="addAccessories">
    <app-dialog
      :title="pageTitle"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="800"
      :maxHeight="600"
    >
      <template slot="body">
        <div class="temBody" v-loading="loading6">
          <div class="cl" style="margin-top:10px;">
            <span class="fl">用途分类：</span>
            <el-radio-group
              :disabled="editable"
              class="fl"
              v-model="radio"
              @change="handleRadioChange"
            >
              <el-radio v-for="(r, idx) in handling" :key="idx" :label="r.value">{{ r.label }}</el-radio>
              <!-- <el-radio :label="4">更换</el-radio>
              <el-radio :label="1">维修</el-radio>
              <el-radio :label="2">拆除</el-radio>
              <el-radio :label="5">新增</el-radio> -->
            </el-radio-group>
          </div>
          <!-- <p v-if="radio == 4" style="margin-top:8px;">原结构配件</p> -->
          <el-form
            :rules="rules"
            ref="formData"
            :model="formData"
            label-position="right"
            label-width="110px"
          >
            <div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="结构配件名称" prop="StructPartName">
                    <span style="display:inline-block;max-width:80%;" class="omit fl" :title="formData.StructPartName">{{formData.StructPartName}}</span>
                    &nbsp;<el-button type="text" @click="handleStruct" :disabled="detailShow">选择</el-button>
                    <!-- <el-select
                      class="sel-ipt"
                      style="width:100%"
                      placeholder
                      clearable
                      :disabled="detailShow"
                      v-model="formData.StructPartName"
                      @change="handleChange"
                      @clear="handleClear"
                    >
                      <el-option
                        v-for="item in taskList"
                        :key="item.Id"
                        :label="item.Name || item.StructPartName"
                        :value="item.Id"
                      ></el-option>
                    </el-select> -->
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="规格型号" prop="SpecificationModel">
                    <span style="display:inline-block;width:100%;line-height:12px;" class="omit" :title="formData.SpecificationModel ? formData.SpecificationModel : '暂无数据'">{{formData.SpecificationModel ? formData.SpecificationModel : '暂无数据'}}</span>
                    <!-- <el-select
                      :disabled="radio != 5 || detailShow"
                      class="sel-ipt"
                      style="width:100%"
                      placeholder
                      clearable
                      v-model="formData.SpecificationModel"
                      @change="handleChange1"
                    >
                      <el-option
                        v-for="item in tableDatas"
                        :key="item.Id"
                        :label="item.SpecificationModel"
                        :value="item.Id"
                      ></el-option>
                    </el-select> -->
                  </el-form-item>
                </el-col>
                
                
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="供应商" prop="SupplierName">
                    <span style="display:inline-block;width:100%;" class="omit" :title="formData.SupplierName ? formData.SupplierName : '无'">{{formData.SupplierName ? formData.SupplierName : '无'}}</span>
                    <!-- <el-input
                      v-model="formData.SupplierName"
                      disabled
                      maxlength="30"
                      placeholder="暂无数据"
                    ></el-input> -->
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生产批次" prop="ProductionBatch">
                    <el-input
                      :disabled="detailShow"
                      v-model.trim="formData.ProductionBatch"
                      maxlength="30"
                      placeholder=""
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="生产时间" prop="ProductionTime">
                    <el-date-picker
                      :disabled="detailShow"
                      style="width: 100%;"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="formData.ProductionTime"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="投产时间" prop="CommissionTime">
                    <el-date-picker
                      :disabled="detailShow"
                      style="width: 100%;"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="formData.CommissionTime"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
          <!-- <p v-if="radio == 4">新结构配件</p> -->
          <!-- <el-form
            v-if="radio == 4"
            :rules="rules"
            ref="formData3"
            :model="formData3"
            label-position="right"
            label-width="110px"
          >
            <div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="结构配件名称" prop="StructPartName">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      placeholder
                      clearable
                      :disabled="detailShow"
                      v-model="formData3.StructPartName"
                      @change="handleChange2"
                    >
                      <el-option
                        v-for="item in taskList3"
                        :key="item.Id"
                        :label="item.Name"
                        :value="item.Id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="规格型号" prop="SpecificationModel">
                    <el-select
                      class="sel-ipt"
                      style="width:100%"
                      placeholder
                      clearable
                      :disabled="detailShow"
                      v-model="formData3.SpecificationModel"
                      @change="handleChange3"
                    >
                      <el-option
                        v-for="item in tableDatas3"
                        :key="item.Id"
                        :label="item.SpecificationModel"
                        :value="item.Id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="供应商" prop="SupplierName">
                    <el-input
                      v-model="formData3.SupplierName"
                      disabled
                      maxlength="30"
                      placeholder="暂无数据"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生产批次" prop="ProductionBatch">
                    <el-input
                      :disabled="detailShow"
                      v-model.trim="formData3.ProductionBatch"
                      maxlength="30"
                      placeholder=""
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生产时间" prop="ProductionTime3">
                    <el-date-picker
                      style="width: 100%;"
                      :disabled="detailShow"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="formData3.ProductionTime3"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="投产时间" prop="CommissionTime3">
                    <el-date-picker
                      style="width: 100%;"
                      :disabled="detailShow"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="formData3.CommissionTime3"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form> -->
        </div>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <el-button
          @click="handleClose"
          size="mini"
          v-show="dialogStatus == 'detail'"
          >关闭</el-button
        >
        <el-button
          @click="handleClose"
          size="mini"
          v-show="dialogStatus != 'detail'"
          >取消</el-button
        >
        <app-button
          @click="handleSuccess"
          :buttonType="1"
          :disabled="disabledBtn"
          v-show="dialogStatus != 'detail'"
        ></app-button>
      </template>
    </app-dialog>
    <add-structural
    :disabled="detailShow"
      @closeDialog="closeStructDialog"
      :dialogFormVisible="dialogStructFormVisible"
      :dialogStatus="dialogStructStatus"
      @handleChange="handleChange"
      :taskList='taskList'
    ></add-structural>
  </div>
</template>
<script>
import * as accessories from "@/api/afterSalesMgmt/accessories";
import addStructural from "./addStructural";
import { vars } from '../common/vars'
export default {
  name: "addAccessories",
  // mixins: [indexPageMixin],
  components: {
    addStructural
  },
  props: {
    //开始、结束操作弹框
    dialogStatus: {
      type: String,
      default: "create"
    },
    // id: {
    //     type: String,
    //     default: ''
    // },
    acceData: {
      type: Object,
      default: null
    },
    rightIndex: {
      type: Number,
      default: 0
    },
    msgReplace: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      handling: vars.handling,
      dialogStructFormVisible:false,
      dialogStructStatus:'create',
      radio: 1,
      disabledBtn: false,
      goOn: false,
      loading6: false,
      formData: {
        StructPartId: "",
        StructPartName: "",
        StructPartsSpecificationId: "",
        SpecificationModel: "",
        SupplierId: "",
        SupplierName: "",
        ProductionBatch: "",
        ProductionTime: "",
        CommissionTime: "",
        MaintenanceEquipmentId: "",
        ReplaceId: null,
        ViewModelDataId: null,
        ProcessMode: 1,
        ProcessModeName: "维修更换",
        UnitPrice: 0,
        Count:1,
        TotalPrice:0,
        Id: "",
        OrderEquipmentDetailStructPartId: null
      },
      // formData3: {
      //   StructPartId: "",
      //   StructPartName: "",
      //   StructPartsSpecificationId: "",
      //   SpecificationModel: "",
      //   SupplierId: "",
      //   SupplierName: "",
      //   ProductionBatch: "",
      //   ProductionTime: "",
      //   CommissionTime: "",
      //   ProductionTime3: "",
      //   CommissionTime3: "",
      //   MaintenanceEquipmentId: "",
      //   ReplaceId: null,
      //   ViewModelDataId: null,
      //   ProcessMode: 1,
      //   ProcessModeName: "更换",
      //   UnitPrice: 0,
      //   Id: "",
      //   OrderEquipmentDetailStructPartId: null
      // },
      rules: {
        StructPartName: [
          { required: true, message: "结构配件名称不能为空", trigger: "change" }
        ],
        SpecificationModel: [
          { required: true, message: "规格型号不能为空", trigger: "change" }
        ],
        ProductionTime: [
          { required: false, message: "生产时间不能为空", trigger: "change" }
        ],
        CommissionTime: [
          { required: false, message: "投产时间不能为空", trigger: "change" }
        ],
        // ProductionTime3: [
        //   { required: true, message: "生产时间不能为空", trigger: "change" }
        // ],
        // CommissionTime3: [
        //   { required: true, message: "投产时间不能为空", trigger: "change" }
        // ]
      },
      taskList: [],
      taskList3: [],
      tableDatas: [],
      tableDatas3: [],
      id: null
    };
  },
  filters: {},
  watch: {
    "$attrs.dialogFormVisible": {
      handler(val) {
        if (val) {
          this.goOn = false;
          this.rules.ProductionTime[0].required = false;
          this.rules.CommissionTime[0].required = false;
          this.taskList=[];
          this.formData = {
            StructPartId: "",
            StructPartName: "",
            StructPartsSpecificationId: "",
            SpecificationModel: "",
            SupplierId: "",
            SupplierName: "",
            ProductionBatch: "",
            ProductionTime: "",
            CommissionTime: "",
            MaintenanceEquipmentId: this.acceData.Id,
            ReplaceId: null,
            ViewModelDataId: this.guid(),
            ProcessMode: 1,
            ProcessModeName: "维修更换",
            UnitPrice: 0,
            Count:1,
            TotalPrice:0,
            Id: "",
            OrderEquipmentDetailStructPartId: null
          };
          if (this.dialogStatus == "create") {
            this.radio = 1;
            // this.getBusiness();
            this.getList();
          } else {
            console.log(4444,this.msgReplace)
            // this.radio = this.msgReplace.ProcessMode;
            // if (this.radio == 4) {
            //   // this.formData3 = {
            //   //   StructPartId: "",
            //   //   StructPartName: "",
            //   //   StructPartsSpecificationId: "",
            //   //   SpecificationModel: "",
            //   //   SupplierId: "",
            //   //   SupplierName: "",
            //   //   ProductionBatch: "",
            //   //   ProductionTime: "",
            //   //   CommissionTime: "",
            //   //   ProductionTime3: "",
            //   //   CommissionTime3: "",
            //   //   MaintenanceEquipmentId: "",
            //   //   ReplaceId: null,
            //   //   ViewModelDataId: null,
            //   //   ProcessMode: 1,
            //   //   ProcessModeName: "更换",
            //   //   UnitPrice: 0,
            //   //   Id: "",
            //   //   OrderEquipmentDetailStructPartId: null
            //   // };
            //   // this.formData3 = Object.assign(this.formData3, this.msgReplace);
            //   // this.formData3.ProductionTime3 = this.formData3.ProductionTime;
            //   // this.formData3.CommissionTime3 = this.formData3.CommissionTime;
            //   this.formData = Object.assign(this.formData,this.msgReplace.ByReplaceMaintenancStructPart);
            // } else {
            //   }
              this.formData = Object.assign(this.formData, this.msgReplace);
            // if (this.radio == 1 || this.radio == 2) {
            //   this.getBusiness();
            // } else if (this.radio == 4) {
            //   this.getBusiness();
            //   this.getList(true);
            // } else {
            //   this.rules.ProductionTime[0].required = true;
            //   this.rules.CommissionTime[0].required = true;
            // }
            if (this.radio == 5) {
              this.rules.ProductionTime[0].required = true;
              this.rules.CommissionTime[0].required = true;
            }
            this.getList();
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    handDatas() {
        let a=[];
        this.acceData.FaultPhenomenonList.forEach(v => {
            a=a.concat(v.MaintenancStructPartList);
        })
        return JSON.parse(JSON.stringify(a));
    //   return JSON.parse(
    //     JSON.stringify(
    //       this.acceData.FaultPhenomenonList[this.rightIndex]
    //         .MaintenancStructPartList
    //     )
    //   );
    },
    pageTitle() {
      if (this.dialogStatus == "create") {
        return "添加";
      } else if (this.dialogStatus == "edit") {
        return "编辑";
      } else if (this.dialogStatus == "detail") {
        return "详情";
      }
    },
    //不等于详情页面可编辑
    editable() {
      //详情和审批模式都不可编辑
      return this.dialogStatus != "create";
    },
    detailShow() {
      return this.dialogStatus == "detail";
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleStruct(){
      this.dialogStructFormVisible=true;
    },
    closeStructDialog(){
      this.dialogStructFormVisible=false;
    },
    saveStructSuccess(){

    },
      handleClear(){
          if(this.radio != 5 && this.dialogStatus == "edit"){
              this.taskList.unshift(this.msgReplace);
          }
      },
    getBusiness() {
      this.taskList=[];
      this.loading6 = true;
      let postData = {
        pageIndex: 1,
        pageSize: 100000000,
        businessId: this.acceData.OrderEquipmentDetailId
      };
      accessories
        .getBusinessRelationPage(postData)
        .then(res => {
          // conosole.log(111111111111111,res)
          this.loading6 = false;
          if (res.Items && res.Items.length > 0) {
            this.taskList = JSON.parse(JSON.stringify(res.Items));
            let a = false;
            this.taskList = this.taskList.filter(v => {
              a = this.handDatas.some(s => {
                if (s.ByReplaceMaintenancStructPart) {
                  return s.ByReplaceMaintenancStructPart.Id == v.Id;
                } else {
                  return v.Id == s.Id;
                }
              });
              return !a;
            });
          } else {
            this.taskList = [];
          }
        })
        .catch(err => {
          this.loading6 = false;
        });
    },
    //生成随机 GUID 数
    guid() {
      function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
      }
      return (
        S4() +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        S4() +
        S4()
      );
    },
    handleRadioChange(d) {
      if (d == 5) {
        this.rules.ProductionTime[0].required = true;
        this.rules.CommissionTime[0].required = true;
      } else {
        this.rules.ProductionTime[0].required = false;
        this.rules.CommissionTime[0].required = false;
      }
      let a = this.guid();
      this.formData = {
        StructPartId: "",
        StructPartName: "",
        StructPartsSpecificationId: "",
        SpecificationModel: "",
        SupplierId: "",
        SupplierName: "",
        ProductionBatch: "",
        ProductionTime: "",
        CommissionTime: "",
        MaintenanceEquipmentId: this.acceData.Id,
        ReplaceId: null,
        ViewModelDataId: a,
        ProcessMode: d,
        ProcessModeName: this.filterRadioName(d),
        UnitPrice: 0,
         Count:1,
         TotalPrice:0,
        Id: "",
        OrderEquipmentDetailStructPartId: null
      };
      this.$refs["formData"].resetFields();

      // this.formData3 = {
      //   StructPartId: "",
      //   StructPartName: "",
      //   StructPartsSpecificationId: "",
      //   SpecificationModel: "",
      //   SupplierId: "",
      //   SupplierName: "",
      //   ProductionBatch: "",
      //   ProductionTime: "",
      //   CommissionTime: "",
      //   ProductionTime3: "",
      //   CommissionTime3: "",
      //   MaintenanceEquipmentId: this.acceData.Id,
      //   ReplaceId: a,
      //   ViewModelDataId: a,
      //   ProcessMode: d,
      //   ProcessModeName: "更换",
      //   UnitPrice: 0,
      //   Id: "",
      //   OrderEquipmentDetailStructPartId: null
      // };
      // if (this.$refs["formData3"]) {
      //   this.$refs["formData3"].resetFields();
      // }

      // if (d == 1 || d == 2) {
      //   this.getBusiness();
      // } else if (d == 4) {
      //   this.getBusiness();
      //   this.getList(true);
      // } else {
      //   this.getList();
      // }
    },
    filterRadioName(d) {
      let obj = this.handling.find(s => s.value == d)
      if(obj) {
        return obj.label
      }
      return '无'

      // let a = "维修";
      // switch (d) {
      //   case 1:
      //     a = "维修";
      //     break;
      //   case 2:
      //     a = "拆除";
      //     break;
      //   case 4:
      //     a = "更换";
      //     break;
      //   case 5:
      //     a = "新增";
      //     break;
      // }
      // return a;
    },
    handleChange(d,structData) {
      if (d) {
        let a = this.taskList.find(s => s.Id == d);
        // if (this.radio == 5) {
        //   this.id = d;
        //   this.formData.StructPartsSpecificationId = "";
        //   this.formData.SpecificationModel = "";
        //   this.getPartList();
        //   this.formData.StructPartId = d;
        //   this.formData.StructPartName = a.Name;
        //   this.formData.Id = a.Id;
        //   this.formData.OrderEquipmentDetailStructPartId = null;
        // } else {
        //   this.formData.StructPartId = a.StructPartId;
        //   this.formData.StructPartName = a.StructPartName;
        //   this.formData.StructPartsSpecificationId =
        //     a.StructPartsSpecificationId;
        //   this.formData.SpecificationModel = a.SpecificationModel;
        //   this.formData.SupplierId = a.SupplierId;
        //   this.formData.SupplierName = a.SupplierName;
        //   this.formData.Id = a.Id;
        //   this.formData.OrderEquipmentDetailStructPartId = a.Id;
        // }
        this.formData.StructPartId = a.Id;
          this.formData.StructPartName = a.Name;
          this.formData.StructPartsSpecificationId =
            structData.StructPartId;
          this.formData.SpecificationModel = structData.SpecificationModel;
          this.formData.SupplierId = structData.SupplierId;
          this.formData.SupplierName = structData.SupplierName;
          this.formData.Id = a.Id;
          this.formData.OrderEquipmentDetailStructPartId = a.Id;
          this.formData.UnitPrice = structData.UnitPrice;
      }
    },
    handleChange1(d) {
      if (d) {
        let a = this.tableDatas.find(s => s.Id == d);
        this.formData.StructPartsSpecificationId = d;
        this.formData.SpecificationModel = a.SpecificationModel;
        this.formData.SupplierId = a.SupplierId;
        this.formData.SupplierName = a.SupplierName;
        this.formData.Id = a.Id;
      }
    },
    handleChange2(d) {
      if (d) {
        this.id = d;
        this.getPartList(true);
        this.formData3.StructPartsSpecificationId = "";
        this.formData3.SpecificationModel = "";
        let a = this.taskList3.find(s => s.Id == d);
        this.formData3.StructPartId = d;
        this.formData3.StructPartName = a.Name;
        this.formData3.Id = a.Id;
      }
    },
    handleChange3(d) {
      if (d) {
        let a = this.tableDatas3.find(s => s.Id == d);
        this.formData3.StructPartsSpecificationId = d;
        this.formData3.SpecificationModel = a.SpecificationModel;
        this.formData3.SupplierId = a.SupplierId;
        this.formData3.SupplierName = a.SupplierName;
        this.formData3.Id = a.Id;
      }
    },
    getList(t) {
      this.taskList=[];
      this.loading6 = true;
      let postData = {
        pageIndex: 1,
        pageSize: 1000000,
        name: null,
        isPivotal: null,
        structPartRisk: null
      };
      accessories
        .getStructuralList(postData)
        .then(res => {
          this.loading6 = false;
          if (res.Items && res.Items.length > 0) {
            let a = false;
            // if (t) {
            //   this.taskList3 = JSON.parse(JSON.stringify(res.Items));
            // } else {
              this.taskList = JSON.parse(JSON.stringify(res.Items));
            // }
          } 
          // else {
            // if (t) {
            //   this.taskList3 = [];
            // } else {
            // }
          // }
        })
        .catch(err => {
          this.loading6 = false;
        });
    },
    getPartList(t) {
      this.loading6 = true;
      let postData = {
        pageIndex: 1,
        pageSize: 1000000,
        structPartId: this.id
      };
      accessories
        .getPartList(postData)
        .then(res => {
          this.loading6 = false;
          if (t) {
            this.tableDatas3 = res.Items;
          } else {
            this.tableDatas = res.Items;
          }
        })
        .catch(err => {
          this.loading6 = false;
        });
    },
    handleSuccess() {
      this.disabledBtn = true;
      let listResult = this.$refs.formData.validate();
      // let listResult1 = JSON.parse(JSON.stringify(listResult));
      // if (this.radio == 4) {
      //   listResult1 = this.$refs.formData3.validate();
      // }
      // Promise.all([listResult, listResult1])
      Promise.all([listResult])
        .then(valid => {
          this.disabledBtn = false;
          let a = JSON.parse(JSON.stringify(this.formData));
            // b = JSON.parse(JSON.stringify(this.formData3));
          // b.ProductionTime = b.ProductionTime3;
          // b.CommissionTime = b.CommissionTime3;
          if (this.goOn) {
            let c = this.guid();
            this.formData = {
              StructPartId: "",
              StructPartName: "",
              StructPartsSpecificationId: "",
              SpecificationModel: "",
              SupplierId: "",
              SupplierName: "",
              ProductionBatch: "",
              ProductionTime: "",
              CommissionTime: "",
              MaintenanceEquipmentId: this.acceData.Id,
              ReplaceId: c,
              ViewModelDataId: c,
              ProcessMode: this.radio,
              ProcessModeName: a.ProcessModeName,
              UnitPrice: 0,
              Count:1,
              TotalPrice:0,
              Id: "",
              OrderEquipmentDetailStructPartId: null
            };
            this.$refs["formData"].resetFields();
            // if (this.radio == 4) {
            //   this.formData3 = {
            //     StructPartId: "",
            //     StructPartName: "",
            //     StructPartsSpecificationId: "",
            //     SpecificationModel: "",
            //     SupplierId: "",
            //     SupplierName: "",
            //     ProductionBatch: "",
            //     ProductionTime: "",
            //     CommissionTime: "",
            //     ProductionTime3: "",
            //     CommissionTime3: "",
            //     MaintenanceEquipmentId: this.acceData.Id,
            //     ReplaceId: c,
            //     ViewModelDataId: c,
            //     ProcessMode: this.radio,
            //     ProcessModeName: "",
            //     UnitPrice: 0,
            //     Id: "",
            //     OrderEquipmentDetailStructPartId: null
            //   };
            //   this.$refs["formData3"].resetFields();
            // }
            // if (this.radio != 5) {
            //   this.handDatas.push(a);
            //   let f = false;
            //   this.taskList = this.taskList.filter(v => {
            //     f = this.handDatas.some(s => {
            //       if (s.ByReplaceMaintenancStructPart) {
            //         return s.ByReplaceMaintenancStructPart.Id == v.Id;
            //       } else {
            //         return v.Id == s.Id;
            //       }
            //     });
            //     return !f;
            //   });
            // }
          }
          if (this.radio == 4) {
            // console.log(66666,a)
            // b.ProcessMode = 4;
            a.ProcessMode = 4;
            a.ProcessModeName = "更换";
          }
          if (this.dialogStatus == "create") {
            // if (this.radio == 4) {
            //   this.$emit("saveSuccess", this.goOn, a, b);
            // } else {
            //   this.$emit("saveSuccess", this.goOn, a, null);
            // }
            this.$emit("saveSuccess", this.goOn, a, null);
          } else if (this.dialogStatus == "edit") {
            // if (this.radio == 4) {
            //   this.$emit("saveEdit", a, b);
            // } else {
            //   this.$emit("saveEdit", a, null);
            // }
            this.$emit("saveEdit", a, null);
          }
        })
        .catch(err => {
          this.disabledBtn = false;
        });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>
<style lang="scss" scoped>
.temBody {
  padding: 0px 10px;
  > p {
    font-weight: 700;
    margin: 0;
  }
  > div:nth-child(1) {
    padding-bottom: 8px;
    border-bottom: 1px solid #dcdfe6;
  }
}
</style>