<template>
<div class="wrapper">
    <div class="card-container">
        <!-- 当前日期/星期 -->
        <!-- <div class="oneRowClass">
            <div class="content-wrapper2">
                <svg-icon icon-class="calendar"></svg-icon>
                <span>
                    {{currentDate}} &nbsp;&nbsp;
                </span>
            </div>
            <div style="height: 100%; color: #5b9cff;">
                <img :src="arrowRight" alt="">
            </div>
        </div> -->

        <div class="body-content">
            <div class="row">
                <!-- 新闻动态 -->
                <div class="lft">
                    <el-card class="box-card" shadow="hover">
                        <div class="content-news-tags-wrapper" v-loading="newsTrendsListLoading">
                            <div>
                                <div style="width:100%; height:120px; text-align: center; margin-bottom: 20px;">
                                    <img style="width:80px; height:80px" src="../../../../assets/images/news_type_icon.png">
                                    <h3>新闻动态</h3> 
                                </div>  
                                <el-tabs :tab-position="tabNewsPosition" style="height: 50%;" :items='newsTrendsGroupTypes' v-model="newsTrendsListQuery.NewsType">
                                    <el-tab-pane v-for="(itemTab,index) in newsTrendsGroupTypes" :name='itemTab.value' :label="itemTab.label" :key="index"></el-tab-pane>
                                </el-tabs>
                            </div>
                            <div>
                                <div class="tab-body" style="height:385px;width:710px;overflow:auto;">
                                    <no-data v-if="(!newsTrendsDatas || newsTrendsDatas.length == 0) && !newsTrendsListLoading"></no-data>
                                    <ul v-infinite-scroll="loadNews" :infinite-scroll-disabled="disabledNews">
                                        <li v-for="(item,index) in newsTrendsDatas" :key="index">
                                            <el-row class="newsRowClass">
                                                <el-col class="newsRowSpanClass">
                                                    <a @click="handleNewsTrendsReview(item)">  
                                                        <el-tag type="" effect="plain" v-if="item.IsChoiceness">精选</el-tag>
                                                        <span v-if="!item.IsRead" :title="item.NewsTitle" style="margin-left: 5px; display: inline-block; width: calc(100% - 50px);  font-size: 14px;" class="text-ellipsis">
                                                            {{item.NewsTitle}}
                                                        </span>
                                                        <span v-else :title="item.NewsTitle" style="margin-left: 5px; color:silver; display: inline-block; width: calc(100% - 50px);  font-size: 14px;" class="text-ellipsis">
                                                            {{item.NewsTitle}}
                                                        </span>
                                                    </a>
                                                </el-col>
                                                <el-col :span="24" style="margin-top:10px; margin-left:5px;">
                                                    <span style="color:#C0C0C0; margin-right:5px;">
                                                        {{item.Author }}
                                                        <!-- {{item.CreateEmployee | nameFilter}} -->
                                                    </span>
                                                    <span style="color:#C0C0C0;">
                                                        {{item.CreateTime | dateFilter('YYYY-MM-DD HH:mm')}}
                                                    </span>
                                                </el-col>

                                                <!-- <el-col :span="5" style="margin-top:10px;">
                                                    <span style="color:#C0C0C0">
                                                        {{item.CreateTime | dateFilter('YYYY-MM-DD HH:mm')}}
                                                    </span>
                                                </el-col> -->
                                                <el-col style="margin-top:10px;">
                                                    <div style="width:95%; height:1px; background-color:#CCC0C0C0; margin-right:100px;"></div>
                                                </el-col>
                                            </el-row>
                                        </li>
                                    </ul>
                                    <p v-if="newsTrendsListLoading" style="text-align: center;">加载中...</p>
                                    <p v-if="noMoreNews" style="text-align: center;">没有更多了</p>
                                </div>
                            </div>

                            <!-- <div class="tab-title">
                                <tags :items='newsTrendsGroupTypes' v-model="newsTrendsListQuery.NewsType">
                                    <template v-for="t in newsTrendsGroupTypes" :slot="t.value">
                                        {{ t.label }}
                                    </template>
                                </tags>
                            </div> -->
                            <!-- <div class="tab-body">
                                <no-data v-if="(!newsTrendsDatas || newsTrendsDatas.length == 0) && !newsTrendsListLoading"></no-data>
                                <el-row class="table-title">
                                    <el-col :span="1">
                                        <div style="width: 40px;">序号</div>
                                    </el-col>
                                    <el-col :span="12">标题</el-col>
                                    <el-col :span="3">类型</el-col>
                                    <el-col :span="3">发布人</el-col>
                                    <el-col :span="5">发布时间</el-col>
                                </el-row>
                                <el-row class="newsRowClass" v-for="(item,index) in newsTrendsDatas" :key="index">
                                    <el-col :span="1">
                                        {{index + 1}}
                                    </el-col>
                                    <el-col :span="12" class="newsRowSpanClass">
                                        <a @click="handleNewsTrendsReview(item)">
                                            <el-tag type="" effect="plain" v-if="item.IsChoiceness">精选</el-tag>
                                            <span v-if="!item.IsRead" :title="item.NewsTitle" style="margin-left: 5px; display: inline-block; width: calc(100% - 50px);" class="text-ellipsis">
                                                {{item.NewsTitle}}
                                            </span>
                                            <span v-else :title="item.NewsTitle" style="margin-left: 5px; color:silver; display: inline-block; width: calc(100% - 50px);" class="text-ellipsis">
                                                {{item.NewsTitle}}
                                            </span>
                                        </a>
                                    </el-col>
                                    <el-col :span="3">
                                        {{ newsTrendsGroupTypes.find(s=>s.value == item.NewsType).label }}
                                    </el-col>
                                    <el-col :span="3">
                                        {{item.CreateEmployee | nameFilter}}
                                    </el-col>
                                    <el-col :span="5">
                                        {{item.CreateTime | dateFilter('YYYY-MM-DD HH:mm')}}
                                    </el-col>
                                </el-row>
                            </div> -->
                            <!-- <div class="pagination">
                                <el-pagination layout="prev, pager, next" v-show="newsTrendsTotal>0" :total="newsTrendsTotal" :current-page.sync="newsTrendsListQuery.PageIndex" :page-size.sync="newsTrendsListQuery.PageSize" @current-change="handleNewsTrendsCurrentChange">
                                </el-pagination>
                            </div> -->
                        </div>
                    </el-card>
                </div>

                <!-- 流程制度 -->
                <div class="rht">
                    <el-card class="box-card" shadow="hover">
                        <div class="content-xinwendongtai-tags-wrapper-guizhangzhidu-tags-wrapper">
                           <div>
                                <div style="width:100%; height:120px; text-align: center; margin-bottom: 20px;">
                                    <img style="width:80px; height:80px" src="../../../../assets/images/process_system_icon.png">
                                    <h3>流程制度</h3> 
                                </div>  
                                <el-tabs :tab-position="tabNewsPosition" style="height: 50%;" :items='regulations' v-model="currentRegulation">
                                    <el-tab-pane v-for="(itemTab,index) in regulations" :name='itemTab.value' :label="itemTab.label" :key="index"></el-tab-pane>
                                </el-tabs>
                           </div>

                            <div>

                                <div class="tab-body">

                                    <template v-if="currentRegulation == 2">
                                        <no-data v-if="(!regulationDatas || regulationDatas.length == 0) && !regulationLoading"></no-data>
                                        <div v-loading="regulationLoading" style="padding:10px; height:385px;width:100%;overflow:auto;">

                                            <ul v-infinite-scroll="handleRegulationCurrentChange" :infinite-scroll-disabled="disabledRegulation">
                                                
                                                <li v-for="(item,index) in regulationDatas" :key="index">

                                                    <el-row class="newsRowClass">
                                                        <el-col :span="12">
                                                            <a @click="handletrainsReview(item, 2)">
                                                                <span :title="item.Name" style="display: inline-block; width: 100%; font-size: 14px;" class="newsRowSpanClass text-ellipsis">
                                                                    {{item.Name}}
                                                                </span>
                                                            </a>
                                                        </el-col>
                                                        <el-col :span="12">
                                                            <div style="padding-right: 25px; display: flex;  justify-content: flex-end;" :title="item.ClassifyName">{{item.ClassifyName || '无'}}</div>
                                                        </el-col>
                                                    </el-row>
                                                </li>
                                            </ul>

                                            <p v-if="regulationLoading" style="text-align: center;">加载中...</p>
                                            <p v-if="noMoreRegulation" style="text-align: center;">没有更多了</p>
                                            
                                        </div>
                                    </template>

                                    <template v-else-if="currentRegulation == 3">
                                        <div style="text-align: center;height: 266px;line-height: 266px;" v-if="(!regulationDatas || regulationDatas.length == 0) && !regulationLoading">
                                            暂无数据
                                        </div>
                                        <div v-else v-loading="regulationLoading" style="padding:10px; height:385px;width:100%;overflow:auto;">

                                            <ul v-infinite-scroll="handleRegulationCurrentChange" :infinite-scroll-disabled="disabledRegulation">
                                                <li v-for="(item,index) in regulationDatas" :key="index">
                                                <el-row class="newsRowClass">
                                                <el-col :span="12">
                                                    <a @click="handletrainsReview(item, 3)">
                                                        <span :title="item.Name" style="display: inline-block; width: 100%; font-size: 14px;" class="newsRowSpanClass text-ellipsis">
                                                            {{item.Name}}
                                                        </span>
                                                    </a>
                                                    </el-col>
                                                <el-col :span="12">
                                                    <div style="padding-right: 25px; display: flex;  justify-content: flex-end;" :title="item.ClassifyName">{{item.ClassifyName || '无'}}</div>
                                                </el-col>
                                                </el-row>
                                                </li>
                                            </ul>
                                        
                                            <p v-if="regulationLoading" style="text-align: center;">加载中...</p>
                                            <p v-if="noMoreRegulation" style="text-align: center;">没有更多了</p>
                                        </div>
                                    </template>

                                </div>

                            </div>


                            <!-- <div class="tab-body">
                                <template v-if="currentRegulation == 2">
                                    <div style="text-align: center;height: 266px;line-height: 266px;" v-if="(!regulationDatas || regulationDatas.length == 0) && !regulationLoading">
                                        暂无数据
                                    </div>
                                    <div v-else v-loading="regulationLoading">
                                        <el-row class="table-title">
                                            <el-col :span="3">
                                                <div style="width: 40px;">序号</div>
                                            </el-col>
                                            <el-col :span="13">标题名称</el-col>
                                            <el-col :span="8">类型名称</el-col>
                                        </el-row>
                                        <el-row class="newsRowClass" v-for="(item,index) in regulationDatas" :key="index">
                                            <el-col :span="3">
                                                {{index + 1}}
                                            </el-col>
                                            <el-col :span="13">
                                                <a @click="handletrainsReview(item, 2)">
                                                    <span :title="item.Name" style="display: inline-block; width: 100%;" class="newsRowSpanClass text-ellipsis">
                                                        {{item.Name}}
                                                    </span>
                                                </a>
                                            </el-col>
                                            <el-col :span="8">
                                                <div style="padding: 0 5px;" class="omit" :title="item.ClassifyName">{{item.ClassifyName || '无'}}</div>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </template>

                                <template v-else-if="currentRegulation == 3">
                                    <div style="text-align: center;height: 266px;line-height: 266px;" v-if="(!regulationDatas || regulationDatas.length == 0) && !regulationLoading">
                                        暂无数据
                                    </div>
                                    <div v-else v-loading="regulationLoading">
                                        <el-row class="table-title">
                                            <el-col :span="3">
                                                <div style="width: 40px;">序号</div>
                                            </el-col>
                                            <el-col :span="13">标题名称</el-col>
                                            <el-col :span="8">类型名称</el-col>
                                        </el-row>
                                        <el-row class="newsRowClass" v-for="(item,index) in regulationDatas" :key="index">
                                            <el-col :span="3">
                                                {{index + 1}}
                                            </el-col>
                                            <el-col :span="13">
                                                <a @click="handletrainsReview(item, 3)">
                                                    <span :title="item.Name" style="display: inline-block; width: 100%;" class="newsRowSpanClass text-ellipsis">
                                                        {{item.Name}}
                                                    </span>
                                                </a>
                                            </el-col>
                                            <el-col :span="8">
                                                <div style="padding: 0 5px;" class="omit" :title="item.ClassifyName">{{item.ClassifyName || '无'}}</div>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </template>

                            </div>
                            <div class="pagination">
                                <el-pagination v-if="currentRegulation == 2" layout="prev, pager, next" v-show="regulationTotal>0" :total="regulationTotal" :current-page.sync="regulationListQuery.PageIndex" :page-size.sync="regulationListQuery.PageSize" @current-change="handleRegulationCurrentChange">
                                </el-pagination>

                                <el-pagination v-if="currentRegulation == 3" layout="prev, pager, next" v-show="regulationTotal>0" :total="regulationTotal" :current-page.sync="regulationListQuery.PageIndex" :page-size.sync="regulationListQuery.PageSize" @current-change="handleRegulationCurrentChange">
                                </el-pagination>
                            </div> -->
                        </div>
                    </el-card>
                </div>
            </div>

            <!-- 企业文化墙 -->
            <div class="row">
                <!-- 企业文化墙 -->
                <div class="lft">
                    <el-card class="box-card" shadow="hover">
                        <!-- <div slot="header" class="clearfix">
                            <span class="twoRowSpanClass qiyewenhua">
                                <svg-icon icon-class="企业文化"></svg-icon>
                                <span>企业文化墙</span>    
                            </span>
                        </div> -->
                        <div class="content-xinwendongtai-tags-wrapper" v-loading="systemNewReportLoading">
                            <!-- <div class="tab-title">
                                <tags :items="types" v-model="riskType">
                                    <template v-for="t in types" :slot="t.value">
                                        {{t.label}}
                                    </template>
                                </tags>
                            </div> -->

                            <div>

                               <div style="width:100%; height:120px; text-align: center; margin-bottom: 20px;">
                                 <img style="width:80px; height:80px" src="../../../../assets/images/corporate_culture_wall_icon.png">
                                 <h3>企业文化墙</h3> 
                                 </div>  
                                
                                 <el-tabs :tab-position="tabNewsPosition" style="height: 50%;" :items='types' v-model="riskType">
                                  <el-tab-pane v-for="(itemTab,index) in types" :name='itemTab.value' :label="itemTab.label" :key="index"></el-tab-pane>
                                 </el-tabs>

                            </div>

                            <div>

                                
                            <div class="tab-body">
                                <!-- 新人报道 -->
                                <div v-if="riskType==1">
                                    <no-data v-if="(!systemNewReportDatas || systemNewReportDatas.length == 0) && !systemNewReportLoading"></no-data>
                                    <div v-else v-loading='systemNewReportLoading' style="height:385px;width:100%;overflow:auto;">
                                        <!-- <ul class="list">
                                            <li class="card-wrapper" v-for="c in systemNewReportDatas" :key="c.NewReportId">

                                            
                                            </li>
                                        </ul> -->

                                        <el-row class="list" v-infinite-scroll="handleSystemNewReportCurrentChange" :infinite-scroll-disabled="disabledNewReport">
                                               <el-col :span="12" class="card-wrapper"  v-for="c in systemNewReportDatas" :key="c.NewReportId">
                                                 <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                                                    <div @click="handleSystemNewReportUpdate(c, 'detail')" class="elMain">
                                                        <el-row>
                                                            <el-col :span="9" style="height:100%;">

                                                                <div style="height:100%; width:100%; display:flex; align-items: center; justify-content: center;">
                                                                    <div style="width:110px; height: 90px; overflow: hidden; margin-left:20px;">
                                                                        <img style="width:110px; height: 90px; object-fit: cover;" :src="c.CoverPath">
                                                                    </div>
                                                                </div>
                                                                 
                                                            </el-col>
                                                            
                                                            <el-col :span="15" style="height:100%">

                                                                <div style="height:100%; display: flex; flex-direction: column;  justify-content: center; margin-left:15px">

                                                                    <div>
                                                                      <span style=" display: inline-block; margin-top:5px; font-weight:bold; color: #1D2129; font-size: 14px;">{{c.Name}}</span>
                                                                    </div>

                                                                    <div>
                                                                      <span style="display: inline-block; margin-top:15px; width:90%" class="text-ellipsis" :title="c.DepartmentName">{{c.DepartmentName}}</span>
                                                                    </div>

                                                                     <div>
                                                                        <span style=" display: inline-block; margin-top:5px;" class="text-ellipsis">{{c.EntryTime | dateFilter('YYYY-MM-DD')}}入职</span> 
                                                                      </div>

                                                                </div>
                                                                  
                                                                
                                                                <!-- {{c.Sex | sexFilter}} &nbsp; -->
                                                               
                                                            </el-col>
                                                           
                                                        </el-row>
                                                    </div>
                                                 </el-card>
                                               </el-col>
                                         </el-row>

                                          <p v-if="systemNewReportLoading" style="text-align: center;">加载中...</p>
                                          <p v-if="disabledNewReport" style="text-align: center;">没有更多了</p>

                                    </div>
                                </div>
                                <!-- 公司团建 -->
                                <div v-if="riskType==2">
                                    <no-data v-if="(!systemLeagueConstructionDatas || systemLeagueConstructionDatas.length == 0) && !systemLeagueConstructionLoading"></no-data>
                                    <div v-else style="height:385px;width:100%;overflow:auto;">

                                        <el-row class="list" v-loading='systemLeagueConstructionLoading' v-infinite-scroll="handlesystemLeagueConstructionCurrentChange" :infinite-scroll-disabled="disabledLeagueConstruction">
                                            <el-col :span="12" class="card-wrapper" v-for="c in systemLeagueConstructionDatas" :key="c.LeagueConstructionId">
                                                <el-card shadow="hover" :body-style="{ padding: '0' }" class="card">
                                                    <div @click="handleSystemLeagueConstructionUpdate(c, 'detail')" class="elMain">
                                                        <div>
                                                            <img :src="c.ActivityCoverPath">
                                                            <span class="text-ellipsis" :title="c.ActivityTitle" style="line-height: 30px; font-size: 16px;">{{c.ActivityTitle}}</span>
                                                        </div>
                                                    </div>
                                                </el-card>
                                            </el-col>
                                        </el-row>

                                         <p v-if="systemLeagueConstructionLoading" style="text-align: center;">加载中...</p>
                                         <p v-if="disabledLeagueConstruction" style="text-align: center;">没有更多了</p>

                                    </div>
                                </div>
                            </div>

                            </div>

                            <!-- <div class="pagination">
                                <el-pagination v-if="riskType==1" layout="prev, pager, next" v-show="systemNewReportTotal>0" :total="systemNewReportTotal" :current-page.sync="systemNewReportListQuery.PageIndex" :page-size.sync="systemNewReportListQuery.PageSize" @current-change="handleSystemNewReportCurrentChange"></el-pagination>
                                <el-pagination v-if="riskType==2" layout="prev, pager, next" v-show="systemLeagueConstructionTotal>0" :total="systemLeagueConstructionTotal" :current-page.sync="systemLeagueConstructionListQuery.PageIndex" :page-size.sync="systemLeagueConstructionListQuery.PageSize" @current-change="handlesystemLeagueConstructionCurrentChange"></el-pagination>
                            </div> -->
                        </div>
                    </el-card>
                </div>

                <!-- 岗位技能 -->
                <div class="rht">
                    <el-card class="box-card" shadow="hover">
                        <!-- <div slot="header" class="clearfix">
                            <span class="twoRowSpanClass peixunzhongxin">
                                <svg-icon icon-class="培训中心"></svg-icon>
                                <span>岗位技能</span>
                            </span>
                        </div> -->

                        <!-- 岗位技能 -->
                        <div class="content">
                            <div>
                                <div class="content-skill-tags-wrapper" style="height: 100%;" v-loading="trainsListLoading">
                                    <div>
                                        <div style="width:100%; height:120px; text-align: center; margin-bottom: 20px;">
                                        <img style="width:80px; height:80px" src="../../../../assets/images/skill_icon.png">
                                        <h3>岗位技能</h3> 
                                        </div>  
                                    </div>
                                    <div>
                                        <div class="tab-title">
                                        <el-input style="width: 50%;" @clear="trainsListHandleFilter" v-antiShake='{time: 300,callback: () => {trainsListHandleFilter()}}' clearable
                                        v-model.trim="trainsListQuery.KeyWords" placeholder="搜索岗位/类型名称"></el-input>
                                        </div>

                                        <no-data v-if="(!trainsDatas || trainsDatas.length == 0) && !trainsListLoading"></no-data>

                                        <div class="tab-body" style="height:355px;width:100%;overflow:auto;">

                                            <ul v-infinite-scroll="handleTrainsCurrentChange" :infinite-scroll-disabled="disabledSkill">
                                                <li v-for="(item,index) in trainsDatas" :key="index">

                                                     <el-row class="newsRowClass">
             
                                                     <el-col :span="12">
                                                     <a @click="handletrainsReview(item, 1)">
                                                     <span :title="item.Name" style="display: inline-block; width: 100%; font-size: 14px" class="newsRowSpanClass text-ellipsis">
                                                      {{item.Name}}
                                                     </span>
                                                     </a>
                                                     </el-col>
                                                    <el-col :span="12">
                                                      <div style=" display: flex;  justify-content: flex-end;"  :title="item.ClassifyName">{{item.ClassifyName || '无'}}</div>
                                                    </el-col>
                                                    </el-row>

                                                </li>
                                            </ul>

                                                 <p v-if="trainsListLoading" style="text-align: center;">加载中...</p>
                                                 <p v-if="noMoreSkill" style="text-align: center;">没有更多了</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="pagination">
                                <el-pagination layout="prev, pager, next" :total="trainsTotal" :current-page.sync="trainsListQuery.PageIndex" :page-size.sync="trainsListQuery.PageSize" @current-change="handleTrainsCurrentChange">
                                </el-pagination>
                            </div> -->
                        </div>
                    </el-card>
                </div>

            </div>
        </div>

    </div>

    <!-- 新闻动态详情页面 -->
    <newsTrends-detail @closeDialog="closeNewsTrendsDialog" @saveSuccess="handleNewsTrendsSaveSuccess" :dialogFormVisible="dialogNewsTrendsFormVisible" :id="newsTrendsId"></newsTrends-detail>

    <!-- 员工手册 -->
    <createHandbook @closeDialog="closeSystemNoticesDialog" @saveSuccess="() => {}" :isShowRange='false' :dialogFormVisible="dialogSystemNoticesFormVisible" :id="systemNoticesId" dialogStatus='detail'></createHandbook>

    <!-- 新人报道详情页面 -->
    <systemNewReport-detail @closeDialog="closeSystemNewReportDialog" @saveSuccess="handleSystemNewReportSaveSuccess" :dialogFormVisible="dialogSystemNewReportFormVisible" :id="systemNewReportId"></systemNewReport-detail>

    <!-- 公司团建详情页面 -->
    <systemLeagueConstruction-detail @closeDialog="closeSystemLeagueConstructionDialog" @saveSuccess="handleSystemLeagueConstructionSaveSuccess" :dialogFormVisible="dialogSystemLeagueConstructionFormVisible" :id="systemLeagueConstructionId"></systemLeagueConstruction-detail>
    
    <app-dialog v-if='dialogFormVisible' :title="isPostMgmtType == 1 ? '岗位技能' : isPostMgmtType == 2 ? '公司制度' : '管理流程'" ref="appDialogRef3" @closeDialog="closeTrainsDialog" :dialogFormVisible="dialogFormVisible" :maxHeight="750" :width="1200">
        <template slot="body">
            <div style="min-height: 750px; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: -10px; bottom: 0; right: -10px; overflow: hidden;">
                    <detailIndex :isPostMgmtType='isPostMgmtType' :selectedNodeKeyId='selectedNodeKeyId'></detailIndex>
                </div>
            </div>
        </template>
        <template slot="footer">
            <div>
            <!-- 取消 -->
            <app-button @click="closeTrainsDialog" :buttonType="2"></app-button>
            </div>
        </template>
    </app-dialog>
    
</div>
</template>

<script>
import {
    regs
} from '@/utils/regs'
import elDragDialog from '@/directive/el-dragDialog'
import indexPageMixin from '@/mixins/indexPage'
import * as systemNewReport from '@/api/personnelManagement/systemNewReport'
import * as systemLeagueConstruction from '@/api/personnelManagement/systemLeagueConstruction'
import * as newsTrends from '@/api/informationCenter/newsTrends'
import newsTrendsDetail from "./newsTrendsDetail";
import createHandbook from "../../../informationCenter/empHandbook/create";
import systemNewReportDetail from "./systemNewReportDetail";
import systemLeagueConstructionDetail from "./systemLeagueConstructionDetail";
import NoData from "@/views/common/components/noData";
import * as empHandbookClassification from "@/api/informationCenter/empHandbookClassification";
import { listToTreeSelect } from '@/utils'
import * as foldersFile from '@/api/foldersFile'
import {
    mapGetters
} from 'vuex'
import detailIndex from '../../../informationCenter/postMgmt/index'

export default {
    components: {
        newsTrendsDetail,
        // createPage,
        createHandbook,
        systemNewReportDetail,
        NoData,
        systemLeagueConstructionDetail,
        detailIndex,
    },
    directives: {
        elDragDialog
    },
    mixins: [indexPageMixin],
    computed: {
        ...mapGetters(['autoOpenDialogList']),
    },
    filters: {
        sexFilter(value) {
            if (value == 2) {
                return "女";
            }
            if (value == 1) {
                return "男";
            }
        },
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
    },
    watch: {
        'newsTrendsListQuery.NewsType'() {
            this.getNewsTrendsList()
        },
        currentRegulation: {
            handler(val) {
                this.regulationListQuery.PageIndex = 1
                if(val == 2) {
                    this.getRegulations()
                }else if(val == 3) {
                    this.getRegulations()
                }
            },
            immediate: true
        },
    },
    data() {
        return {

            tabNewsPosition: 'left',
            disabledNews: false,
            noMoreNews: false,
            disabledRegulation:false,
            noMoreRegulation:false,
            disabledNewReport:false,
            disabledLeagueConstruction:false,
            disabledSkill:false,
            noMoreSkill:false,

            listToTreeSelect,
            // dialogStatus: "detail",
            // selectTypeId: '', //当前选中的类型ID

            types: [{
                    value: "1",
                    label: "新人报道"
                },
                {
                    value: "2",
                    label: "公司团建"
                }
            ],
            riskType: "1",
            newsTrendsGroupTypes: [{
                    value: '0',
                    label: '全部新闻'
                },
                {
                    value: '1',
                    label: '公司新闻'
                },
                {
                    value: '2',
                    label: '行业新闻'
                },
                {
                    value: '3',
                    label: '公司动态'
                }
            ],

            dialogSystemNewReportFormVisible: false,
            systemNewReportId: '',
            systemNewReportTotal: 0,
            systemNewReportDatas: [],
            systemNewReportLoading: false,
            systemNewReportListQuery: {
                PageSize: 6
            },

            dialogSystemLeagueConstructionFormVisible: false,
            systemLeagueConstructionId: '',
            systemLeagueConstructionTotal: 0,
            systemLeagueConstructionDatas: [],
            systemLeagueConstructionLoading: false,
            systemLeagueConstructionListQuery: {
                PageSize: 6
            },

            newsTrendsId: "",
            // trainsId: "",
            dialogFormVisible: false,
            dialogNewsTrendsFormVisible: false,
            newsTrendsTotal: 0,
            newsTrendsDatas: [],
            newsTrendsListLoading: false,
            newsTrendsListQuery: {
                NewsType: '0',
                IsShow: 1,
                PageSize: 6,
                WebOrApp: "HomePage"
            },

            systemNoticesId: "",
            dialogSystemNoticesFormVisible: false,
            systemNoticesTotal: 0,
            trainsTotal: 0,
            systemNoticesDatas: [],
            trainsDatas: [],
            trainsListLoading: false,
            systemNoticesListLoading: false,
            systemNoticesListQuery: { // 查询条件
                NoticeTitle: '',
                StartDateTime: null,
                EndDateTime: null,
                NoticeStatus: 1,
                PageSize: 6,
                WebOrApp: 'HomePage',
                Type: 1
            },
            trainsListQuery: { // 查询条件
                KeyWords: '',
                TrainsClassificationId: '',
                TrainsName: '',
                EmployeeName: "",
                IsShow: 1,
                PageSize: 6,
            },
            // defavatar1: require("../../../../assets/images/u320.png"),
            // defavatar2: require("../../../../assets/images/u321.png"),
            // defavatar3: require("../../../../assets/images/u322.png"),
            arrowRight: require("../../../../assets/images/arrow-right.svg"),

            currentDate: "",

            treeLoading: false,
            defaultTreeNode: {
                Id: "00000000-0000-0000-0000-000000000000",
                ParentId: null,
                Name: "全部课程",
            },
            treeDatas: [],

            treeDatasClass: [],
            checkedNodeHandbook: null,

            regulations: [
                // {value: 1, label: '员工手册'},
                {value: "2", label: '公司制度'},
                {value: "3", label: '管理流程'},
            ],
            currentRegulation: "2",
            regulationDatas: [],
            regulationTotal: 0,
            regulationLoading: [],
            regulationListQuery: { // 查询条件
                PageSize: 6,
                PageIndex: 1
            },
            isPostMgmtType: -1,
            selectedNodeKeyId: '',

        }
    },
    created() {
        this.getHandbookTreeDatas()
        this.getTrainsList()
    },
    mounted() {
        this.getCurrentDate();
        this.getNewsTrendsList();
        this.getSystemNewReportList()
        this.getSystemLeagueConstructionList()
    },
    methods: {
        getHandbookTreeDatas() {
            this.treeLoading = true;
            empHandbookClassification
            .getAllClassifications({})
            .then(res => {
                this.treeLoading = false;
                if (!res) {
                    res = [];
                }

                let defaultTreeNode = {
                    Id: "00000000-0000-0000-0000-000000000000",
                    ParentID: null,
                    Name: "全部",
                }
                res.splice(0, 0, defaultTreeNode);

                this.treeDatasClass = listToTreeSelect(res,undefined,undefined,{key: 'Id',parentKey: 'ParentID'}, 'Name');
                
                this.$nextTick(() => {
                    if(this.$refs.treeHandbook) {
                        this.$refs.treeHandbook.setCurrentKey(defaultTreeNode.Id);
                    }
                    this.checkedNodeHandbook = defaultTreeNode
                })
            })
            .catch(err => {
                this.treeLoading = false;
            });
        },

        /////////////////////////////////////////公司团建/////////////////////////////////////////
        //获取公司团建列表
        getSystemLeagueConstructionList() {
            this.systemLeagueConstructionLoading = true
            systemLeagueConstruction.getList(this.systemLeagueConstructionListQuery).then(res => {
                this.systemLeagueConstructionLoading = false
                this.systemLeagueConstructionDatas = res.Items
                this.systemLeagueConstructionTotal = res.Total
                if(this.systemLeagueConstructionDatas.length === this.systemLeagueConstructionTotal){
                    this.disabledLeagueConstruction = false;
                }else{
                    this.disabledLeagueConstruction = true;
                }

            })
        },
        handlesystemLeagueConstructionCurrentChange(val) {
            this.systemLeagueConstructionListQuery.PageIndex = val
            this.systemLeagueConstructionListQuery.PageSize += 10
            this.getSystemLeagueConstructionList()
        },

        // 弹出公司团建详情
        handleSystemLeagueConstructionUpdate(row) {
            this.systemLeagueConstructionId = row.LeagueConstructionId
            this.dialogSystemLeagueConstructionFormVisible = true;
        },

        closeSystemLeagueConstructionDialog() {
            this.dialogSystemLeagueConstructionFormVisible = false;
        },
        handleSystemLeagueConstructionSaveSuccess() {
            this.getSystemLeagueConstructionList();
            this.closeSystemLeagueConstructionDialog();
        },

        /////////////////////////////////////////新人报道/////////////////////////////////////////
        //获取新人报道列表
        getSystemNewReportList() {
            this.systemNewReportLoading = true
            systemNewReport.getList(this.systemNewReportListQuery).then(res => {
                this.systemNewReportLoading = false
                this.systemNewReportDatas = res.Items
                this.systemNewReportTotal = res.Total
                if(this.systemNewReportDatas.length === this.systemNewReportTotal){
                   this.disabledNewReport = true
                }else{
                   this.disabledNewReport = false
                }

            })
        },
        handleSystemNewReportCurrentChange(val) {
            this.systemNewReportListQuery.PageIndex = val
            this.systemNewReportListQuery.PageSize += 10
            this.getSystemNewReportList()
        },

        // 弹出新人报道详情
        handleSystemNewReportUpdate(row) {
            this.systemNewReportId = row.NewReportId
            this.dialogSystemNewReportFormVisible = true;
        },
        closeSystemNewReportDialog() {
            this.dialogSystemNewReportFormVisible = false;
        },
        handleSystemNewReportSaveSuccess() {
            this.getSystemNewReportList();
            this.closeSystemNewReportDialog();
        },

        /////////////////////////////////////////新闻动态/////////////////////////////////////////
        //获取新闻动态
        getNewsTrendsList() {
            this.newsTrendsListLoading = true
            newsTrends.getList(this.newsTrendsListQuery).then(response => {
                this.newsTrendsDatas = response.Items
                this.newsTrendsTotal = response.Total
                this.newsTrendsListLoading = false
                if(this.newsTrendsDatas.length === this.newsTrendsTotal){
                    this.noMoreNews = true;
                    this.disabledNews = true;
                }else{
                    this.noMoreNews = false;
                    this.disabledNews = false;
                }
            })
        },
        handleNewsTrendsCurrentChange(val) {
            this.newsTrendsListQuery.PageIndex = val
            this.getNewsTrendsList()
        },

        loadNews(){
          this.newsTrendsListQuery.PageSize+=10;
          this.handleNewsTrendsCurrentChange(1);
        },

        //查看详情
        handleNewsTrendsReview(row) {
            this.newsTrendsId = row.Id;
            this.dialogNewsTrendsFormVisible = true;
        },
        closeNewsTrendsDialog() {
            this.getNewsTrendsList();
            this.dialogNewsTrendsFormVisible = false;
        },
        handleNewsTrendsSaveSuccess() {
            this.getNewsTrendsList();
            this.closeNewsTrendsDialog();
        },

        /////////////////////////////////////////培训中心/////////////////////////////////////////

        trainsListHandleFilter() {
            this.trainsListQuery.PageIndex = 1
            this.trainsListQuery.PageSize += 10
            this.getTrainsList()
        },
        getTrainsList() {
            console.log("getTrainsList___________________")
            
            let postDatas = JSON.parse(JSON.stringify(this.trainsListQuery))
            postDatas.FoldersOrFilesType = 2
            postDatas.FoldersFilesBusinessType = 1 //1: 岗位技能; 2: 公司制度
            this.trainsListLoading = true
            foldersFile.getListPage(postDatas).then(res => {
                this.trainsListLoading = false
                this.trainsDatas = res.Items
                this.trainsTotal = res.Total

                if(this.trainsDatas.length === this.trainsTotal || this.trainsDatas.length == 0){
                 this.noMoreSkill = true
                 this.disabledSkill = true
                 this.trainsListQuery.PageSize = this.trainsTotal
                }else{
                 this.noMoreSkill = false
                 this.disabledSkill = false
                }  

            }).catch(err => {
                this.trainsListLoading = false
            })
        },
        handleTrainsCurrentChange(val) {
            this.trainsListQuery.PageIndex = 1
            this.trainsListQuery.PageSize += 10
            this.getTrainsList()
        },
        //查看详情
        handletrainsReview(row, type) {
            this.isPostMgmtType = type
            this.selectedNodeKeyId = row.Id;
            this.dialogFormVisible = true;
        },
        closeTrainsDialog() {
            this.dialogFormVisible = false;
            // this.getTrainsList();
        },
        //查看详情
        handleSystemNoticesReview(row, type) {
            this.isPostMgmtType = type
            this.systemNoticesId = row.Id;
            this.dialogSystemNoticesFormVisible = true;
        },
        closeSystemNoticesDialog() {
            this.dialogSystemNoticesFormVisible = false;
        },
        /////////////////////////////////////////当前日期/////////////////////////////////////////
        getCurrentDate() {
            var myDate = new Date();
            var year = myDate.getFullYear(); //年
            var month = myDate.getMonth() + 1; //月
            var day = myDate.getDate(); //日
            var days = myDate.getDay();
            switch (days) {
                case 1:
                    days = '星期一';
                    break;
                case 2:
                    days = '星期二';
                    break;
                case 3:
                    days = '星期三';
                    break;
                case 4:
                    days = '星期四';
                    break;
                case 5:
                    days = '星期五';
                    break;
                case 6:
                    days = '星期六';
                    break;
                case 0:
                    days = '星期日';
                    break;
            }
            var str = year + "年" + month + "月" + day + "日  " + days;
            this.currentDate = str;
        },
        getRegulations() {
            let postDatas = JSON.parse(JSON.stringify(this.regulationListQuery))
            postDatas.FoldersOrFilesType = 2
            postDatas.FoldersFilesBusinessType = this.currentRegulation //1: 岗位技能; 2: 公司制度 3: 管理流程
            this.regulationLoading = true
            foldersFile.getListPage(postDatas).then(res => {
                this.regulationLoading = false
                this.regulationDatas = res.Items
                this.regulationTotal = res.Total
                
                if(this.regulationDatas.length === this.regulationTotal){
                    this.disabledRegulation = true;
                    this.noMoreRegulation = true;
                }else{
                    this.disabledRegulation = false;
                    this.noMoreRegulation = false;
                }

            }).catch(err => {
                this.regulationLoading = false
            })
        },
        handleRegulationCurrentChange(val) {
            this.regulationListQuery.PageIndex = val
            this.regulationListQuery.PageSize += 5;
            this.getRegulations()
        },
        
    }
}
</script>

<style scoped>
.wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    background-color: #ffffff;
}

.card-container>>>.el-card__header{
    padding-left: 0!important;
}
.card-container>>>.el-card__body{
    padding: 10px;
    box-sizing: border-box;
    height: calc(100% - 26px);
}

.card-container>>>.xinwendongtai{
    background-color: #74c8f3;
}

.card-container>>>.gonggaotongzhi{
    background-color: #f06b60;
}

.card-container>>>.qiyewenhua{
    background-color: rgba(64, 158, 255, 1);
}

.card-container>>>.peixunzhongxin{
    background-color: #78c32a;
}



.card-container>>>.list-wrapper .tag {
    display: inline-block;
    padding: 4px;
    cursor: pointer;
    border-radius: 5px;
    margin: 2px;
    margin-right: 10px;
    padding-left: 10px;
    padding-right: 10px;
    font-size: 13px;
    color: #409eff;
}

.card-container>>>.list-wrapper .tag.active {
    color: white;
    background: #409eff !important;
}

.xinwendongtai-tags-wrapper >>> .list-wrapper .tag{
    color: #74c8f3;
}

.xinwendongtai-tags-wrapper >>>.list-wrapper .tag.active {
    background: #74c8f3 !important;
}

.guizhangzhidu-tags-wrapper >>> .list-wrapper .tag{
    color: #f06b60;
}
.guizhangzhidu-tags-wrapper>>>.list-wrapper .tag.active {
    background: #f06b60 !important;
}

.table-title{
    font-weight: 700;
}



.elTree >>> .el-tree{
    height: 100%;
}

.xinwendongtai-tags-wrapper >>> .tag.active::after, .card-container >>> .tag.active::after{
    height: 0;
}

.sp-dialog >>> .el-dialog__body{
    padding: 0!important;
}
</style>

<style lang="scss" scoped>

.card-container{
    display: flex;
    flex-direction: column;
    height: 100%;
    // .oneRowClass {
    //     height: 40px;
    //     margin-bottom: 10px;
    //     // line-height: 50px;
    //     font-size: 20px;
    //     color: #409eff;
    //     box-shadow: none;
    //     font-family: "Arial Negreta", "Arial Normal", "Arial";
    //     font-weight: 700;
    //     font-style: normal;
    //     display: flex;
    //     align-items: center;
    // }
    .body-content{
        flex: 1;
        overflow-y: hidden;
        .row{
            height: 50%;
            overflow-y: hidden;
            display: flex;
            >div{
                flex: 1;
                overflow-y: hidden;
                flex-shrink: 0;
                box-sizing: border-box;
                .box-card{
                    height: 100%;
                    margin-bottom: 0;
                    // height: calc(100% - 10px);
                    // margin-bottom: 10px;
                }
            }
        }
        // .lft{
        //     margin-right: 5px;
        // }
        // .rht{
        //     margin-left: 5px;
        // }
        .lft, .rht{
            padding: 10px;
        }

    }

}

// .content-wrapper2{
//     background: #5b9cff;
//     display: flex;
//     align-items: center;
//     color: #ffffff;
//     padding: 8px;
//     height: 40px;
// }
// .content-wrapper2 span:last-child{
//     margin-left: 10px;
    
// }


.twoRowClass {
    font-size: 16px;
    color: #409eff;
    box-shadow: none;
    font-family: "Arial Negreta", "Arial Normal", "Arial";
    // font-weight: 700;
    font-style: normal;
}

.twoRowSpanClass {
    font-family: "Arial Negreta", "Arial Normal", "Arial";
    font-weight: 700;
    font-style: normal;
    font-size: 16px;
    color: #ffffff;
    text-align: left;
    display: flex;
    // justify-content: center;
    align-items: center;
    padding-left: 10px;
}

.twoRowSpanClass span:last-child{
    margin-left: 5px;
}



.newsRowClass {
    margin-top: 20px;
    margin-left: 5px;
    margin-right: 5px;
}

.newsRowSpanClass {
    
    height: 20px;
    line-height: 18px;

    span {
        vertical-align: middle;
    }
}

.span {
    position: relative;
    padding: 5px;
}

.pagination {
    white-space: nowrap;
    color: #303133;
    font-weight: 700;
    text-align: center;
    text-align: center;
    width: 100%;
}

.elMain {
    width: 100%;
    height: 140px;
    cursor: pointer;
    // display: flex;
    // justify-content: space-between;
    position: relative;

    >div:first-child {
        position: relative;
        width: 100%;
        height: 100%;
       // text-align: center;
        overflow: hidden;

        >img {
            //   width: auto;
            //   height: auto;
            //   max-width: 100%;
            //   max-height: 100%;
            width: 100%;
            height: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        >span {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 45px;
            background: rgba(70, 70, 70, 0.7);
            color: white;
            text-align: left;
            padding-top: 7px;
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    >div:last-child {
        width: 100%;
        color: #909399;
    }
}


.list {
    padding-bottom: 50px;

     .card-wrapper {
        padding-top: 10px;
        padding-right: 5px;
        padding-left: 5px;
        display: inline-block;
        cursor: pointer;

        // .card {
        //     width: 100%;
        //     height: auto;
        //     border: 0px;
        // }

        // .item-wrapper {
        //     cursor: pointer;
        //     padding: 14px;
        //     padding-bottom: 2px;

        //     .item {
        //         padding-bottom: 4px;

        //         div {
        //             display: inline-block;
        //         }

        //         .item-title {
        //             width: 90px;
        //         }
        //     }
        // }
    }

    // .card-wrapper {
    //     padding-top: 10px;
    //     padding-right: 5px;
    //     padding-left: 5px;
    //     display: inline-block;
    //     width: 33.3%;
    //     cursor: pointer;

    //     .card {
    //         width: 100%;
    //         height: auto;
    //         border: 0px;
    //     }

    //     .item-wrapper {
    //         cursor: pointer;
    //         padding: 14px;
    //         padding-bottom: 2px;

    //         .item {
    //             padding-bottom: 4px;

    //             div {
    //                 display: inline-block;
    //             }

    //             .item-title {
    //                 width: 90px;
    //             }
    //         }
    //     }
    // }
}



.text-ellipsis{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
}

.box-card{
    height: 50%;
    box-sizing: border-box;
    ::v-deep .content{
        display: flex;
        flex-direction: column;
        height: 100%;
        .tab-title{
            margin-bottom: 10px;
        }
        .tab-body{
            flex: 1;
            overflow-y: auto;
            padding: 0 10px;
        }
    }
}

.content-news-tags-wrapper{
    display: grid;
    grid-template-columns: 100px auto;
    grid-template-rows: 385px;
}

.content-xinwendongtai-tags-wrapper-guizhangzhidu-tags-wrapper{
    display: grid;
    grid-template-columns: 100px auto;
    grid-template-rows: 385px;
}

.content-xinwendongtai-tags-wrapper{
    display: grid;
    grid-template-columns: 100px auto;
    grid-template-rows: 385px;
}

.content-skill-tags-wrapper{
    display: grid;
    grid-template-columns: 100px auto;
    grid-template-rows: 385px;
}

h3{
    font-size: 15px!important;
}



</style>
