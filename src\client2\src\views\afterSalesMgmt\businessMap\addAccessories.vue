<template>
  <div class="addAccessories">
    <app-dialog
      title="添加配件"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="1000"
      v-loading="loading"
    >
      <template slot="body">
        <div class="temBody">
          <div class="wl-transfer transfer">
            <div class="transfer-left">
              <h3 class="transfer-title">
                <span>结构配件列表</span>
              </h3>
              <div class="transfer-main">
                <el-input
                 prefix-icon="el-icon-search"
                placeholder="输入关键字进行过滤"
                v-model="leftFilterText">
                </el-input>
                <el-tree
                class="filter-tree"
                :data="leftData"
                :props="defaultProps"
                default-expand-all
                show-checkbox
                node-key="tId"
                :filter-node-method="filterNode"
                @check-change="handleLeftCheck"
                ref="leftTree">
                </el-tree>
              </div>
            </div>
            <div class="transfer-center">
              <p class="transfer-center-item">
                  <el-button type="primary" :disabled="disabledAdd" @click="getLfetCheckedNodes">添加 <i class="el-icon-arrow-right"></i></el-button>
              </p>
              <p class="transfer-center-item">
                  <el-button type="primary" :disabled="disabledDel" @click="getRightCheckedNodes"><i class="el-icon-arrow-left"></i> 删除</el-button>
              </p>
            </div>
            <div class="transfer-right">
              <h3 class="transfer-title">
                <span>已选中配件({{chioceN}})</span>
              </h3>
              <div class="transfer-main">
                <el-input
                 prefix-icon="el-icon-search"
                placeholder="输入关键字进行过滤"
                v-model="rightFilterText">
                </el-input>
                <el-tree
                class="filter-tree"
                :data="rightData"
                :props="defaultProps"
                default-expand-all
                show-checkbox
                node-key="tId"
                :filter-node-method="filterNode"
                ref="rightTree">
                </el-tree>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template slot="footer">
        <el-button @click="handleClose">取消</el-button>
        <app-button @click="handleSuccess" :buttonType="1" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>
<script>
import elDragDialog from "@/directive/el-dragDialog";
import treeTransfer from "el-tree-transfer";
export default {
  name: "addAccessories",
  // mixins: [indexPageMixin],
  components: {
    treeTransfer
  },
  props: ["fromData"],
  directives: {
    elDragDialog
  },
  data() {
    return {
        disabledAdd:false,
        disabledDel:false,
        leftFilterText:'',
        rightFilterText:'',
        leftData:[],
        rightData:[],
      nowData: [],
      disabledBtn: false,
      loading: false,
      title: ["结构配件列表", "已选中配件"],
      buttonText: ["添加", "删除"],
      mode: "transfer", // transfer addressList
      toData: [],
      chioceN: 0,
      defaultProps: {
          children: 'children',
          label: 'label'
        },
    };
  },
  filters: {},
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.leftFilterText="";
        this.rightFilterText="";
        this.checkKeys=[];
        this.leftData = this.fromData;
        this.rightData=[];
        // this.nowData=JSON.parse(JSON.stringify(this.fromData));
        this.toData = [];
        this.chioceN = 0;
      }
    },
    leftFilterText(val) {
    this.$refs.leftTree.filter(val);
    },
    rightFilterText(val) {
    this.$refs.rightTree.filter(val);
    }
  },
  computed: {
    // nowData(){
    //     return this.fromData;
    // }
  },
  created() {},
  mounted() {},
  methods: {
      handleLeftCheck(a,b,c){
          console.log(666,a,b,c)
      },
      getRightCheckedNodes(){
        this.chioceN=0;
          let a=JSON.parse(JSON.stringify(this.$refs.rightTree.getCheckedNodes())),b=JSON.parse(JSON.stringify(this.$refs.rightTree.getHalfCheckedNodes()));
          let c=a.filter(v => (v.children && v.children.length>0));
          let d=a.filter(v => (v.children == null || v.children == undefined));
          for(let i=this.rightData.length-1; i>=0; i--){
              if(c.some(s => (s.tId == this.rightData[i].tId))){
                  this.rightData.splice(i,1);
              }
          }
          this.rightData.forEach(v => {
            for(let i=v.children.length-1; i>=0; i--){
                if(d.some(s => (s.tId == v.children[i].tId))){
                    v.children.splice(i,1);
                    this.$nextTick(() => {
                      this.$refs.rightTree.setChecked(v.tId,false);
                    })
                }
            }
          })
          this.toData=this.rightData;
          this.rightData.forEach(v => {
            this.chioceN+=v.children.length;
          })
      },
      getLfetCheckedNodes() {
        this.chioceN=0;
        let a=JSON.parse(JSON.stringify(this.$refs.leftTree.getCheckedNodes())),b=JSON.parse(JSON.stringify(this.$refs.leftTree.getHalfCheckedNodes()));
        let c=a.filter(v => (v.children && v.children.length>0));
        c.forEach((v,i) => {
            v.tId=this.guid();
            v.children.forEach((v1,i1) => {
                v1.tId=this.guid();
            })
        })
        this.rightData=this.rightData.concat(c);
        b.forEach(v => {
            for(let i=v.children.length-1; i>=0; i--){
                if(!a.some(s => (s.id == v.children[i].id))){
                    v.children.splice(i,1);
                }
            }
        })
        b.forEach((v,i) => {
            v.tId=this.guid();
            v.children.forEach((v1,i1) => {
                v1.tId=this.guid();
            })
        })
        this.rightData=this.rightData.concat(b);
        
        this.toData=this.rightData;
        this.rightData.forEach(v => {
          this.chioceN+=v.children.length;
          })
        this.$nextTick(() => {
            this.$refs.leftTree.setCheckedKeys([])
        });
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      //生成随机 GUID 数
        guid() {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }
            return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
        },
    // 监听穿梭框组件添加
    add(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的        {keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      // console.log("fromData:", fromData);
      // console.log("toData:", toData);
      // console.log("obj:", obj);
      this.toData = toData;
      this.nowData = JSON.parse(JSON.stringify(this.fromData));
      this.getNum(toData);
    },
    // 监听穿梭框组件移除
    remove(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      // console.log("fromData:", fromData);
      // console.log("toData:", toData);
      // console.log("obj:", obj);
      this.toData = toData;
      this.nowData = JSON.parse(JSON.stringify(this.fromData));
      this.getNum(toData);
    },
    getNum(d) {
      let n = 0;
      if (d.length > 0) {
        d.forEach(v => {
          if (v.children.length > 0) {
            n += v.children.length;
          }
        });
        this.chioceN = n;
      } else {
        this.chioceN = 0;
      }
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
    handleSuccess() {
      if (this.toData.length > 0) {
        this.$emit("saveSuccess", this.toData);
      } else {
        this.$message({
          message: "请先添加配件",
          type: "warning"
        });
      }
    }
  }
};
</script>
<style scoped>
  .filter-tree > .el-tree-node > .el-tree-node__content .el-checkbox{
    display: none;
  }
</style>
<style lang="scss" scoped>
.wl-transfer{
    width: 100%;
    height: 540px;
    position: relative;
    overflow: hidden;
}
.wl-transfer .transfer-left, .wl-transfer .transfer-right{
        border: 1px solid #ebeef5;
    width: 40%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 5px;
    vertical-align: middle;
}
.wl-transfer .transfer-left {
    position: absolute;
    top: 0;
    left: 0;
}
.wl-transfer .transfer-center {
    position: absolute;
    top: 50%;
    left: 40%;
    width: 20%;
    transform: translateY(-50%);
    text-align: center;
}
.wl-transfer .transfer-right {
    position: absolute;
    top: 0;
    right: 0;
}
.wl-transfer .transfer-title {
    border-bottom: 1px solid #ebeef5;
    padding: 0 15px;
    height: 40px;
    line-height: 40px;
    color: #333;
    font-size: 16px;
    background-color: #f5f7fa;
    margin:0;
}
.wl-transfer .transfer-main {
    padding: 10px;
    height: calc(100% - 41px);
    box-sizing: border-box;
    overflow: auto;
}
.wl-transfer .transfer-center-item {
    padding: 10px;
    overflow: hidden;
}
.temBody {
  position: relative;
  .dNum {
    position: absolute;
    top: 10px;
    left: 716px;
    z-index: 1;
  }
}
</style>