<template>
    <div class="supplierDetail">
        <app-dialog 
            title="关联人员" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='1200'
            :max-height='630'
            v-loading="loading"
        >
            <template slot="body">
                <div>
                    <el-form :rules="rules" ref="formData" :model="formData" label-width="85px" label-position="right">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="评分主管" prop="GradingSupervisorEmployeeList">
                                    <emp-selector
                                        :showType="2"
                                        :multiple="false"
                                        :list="formData.GradingSupervisorEmployeeList"
                                        :beforeConfirm='handleBeforeConfirm'
                                        @change="handleChangeOwnerUsers"
                                    ></emp-selector>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div style="padding: 10px 0;">
                    <el-button @click="handleRelation" type="primary">添加人员</el-button>
                    <el-button @click="handleRemoveAll" type="danger">删除人员</el-button>
                </div>
                <app-table
                    ref="mainTable"
                    :tab-columns="tabColumns"
                    :tab-datas="formData.EmployeeList"
                    :tab-auth-columns="tabAuthColumns"
                    :isShowAllColumn="true"
                    :loading="loading"
                    :isShowOpatColumn="true"
                    :startOfTable="startOfTable"
                    @rowSelectionChanged="rowSelectionChanged"
                    :multable="true"
                    :isShowBtnsArea="false"
                    :isShowConditionArea="false"
                >
                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button :type="3" @click="handleRemove(scope.index - 1)"></app-table-row-button>
                    </template>
                </app-table>
                <!-- <pagination
                    v-show="total > 0"
                    :total="total"
                    :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize"
                    @pagination="handleCurrentChange"
                    @size-change="handleSizeChange"
                /> -->
            </template>
            <template slot="footer">
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>

        <emp-selector
            v-show="false"
            ref="empSelector"
            :readonly="true"
            :showType="2"
            :multiple="false"
            :list="formData.EmployeeList || []"
            @change="handleChange"
        ></emp-selector>
    </div>
</template>
<script>
import indexPageMixin from "@/mixins/indexPage";
import * as employeePointsRule from "@/api/personnelManagement/employeePointsRule";
import empSelector from "@/views/common/empSelector";

export default{
    name:'supplierDetail',
    mixins: [indexPageMixin],
    components: {   
        empSelector,
    },
    props:{
        id:{
            type: String,
            default:''
        },
    },
    data(){
        return{
            loading: false,
            disabledBtn: false,
            rules: {
                GradingSupervisorEmployeeList: { fieldName: "评分主管", rules: [{ required: true }] },
            },
            multipleSelection: [],
            tabColumns: [
                {
                  attr: { prop: "Name", label: "姓名", }
                },
                {
                  attr: { prop: "Number", label: "工号", },
                },
                {
                  attr: { prop: "DepartmentName", label: "部门", },
                },
            ],
            formData: {
                GradingSupervisorEmployeeList: [],
                EmployeeList: [],
            },
            
            // listQuery: {
            //     PageIndex:1,
            //     PageSize:20,
            // },
            // total: 0,

        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList();
                }
            },
            immediate: true
        }
    },
    created(){
        this.rules = this.initRules(this.rules);
    },
    mounted(){
        
    },
    methods:{
        getList() {
            this.loading = true;
            employeePointsRule.detail({id: this.id}).then(res => {
                this.loading = false
                this.formData.GradingSupervisorEmployeeList = res.GradingSupervisorEmployeeList
                this.formData.EmployeeList = res.EmployeeList;
            }).catch(err => {
                this.loading = false
            })
        },

        // handleCurrentChange(val) {
        //     this.listQuery.PageIndex = val.page;
        //     this.listQuery.PageSize = val.size;
        //     this.getList();
        // },
        // handleSizeChange(val) {
        //     this.listQuery.PageSize = val.size;
        //     this.getList();
        // },
        createData() {
            this.$refs.formData.validate((valid) => {
                if(valid) {
                    let postDatas = {
                        Id: this.id, 
                        EmployeeIdList: this.formData.EmployeeList.map(s => s.EmployeeId),
                        GradingSupervisorEmployeeIdList: this.formData.GradingSupervisorEmployeeList.map(s => s.EmployeeId),
                    }
                    this.disabledBtn = true
                    employeePointsRule.editEmployee(postDatas).then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "成功",
                            message: "设置成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData();
                    }).catch(err => {
                        this.disabledBtn = false
                    })
                }
            })
        },
        handleBeforeConfirm(users) {
            if (users && users.length > 1) {
                this.$message({
                    message: '评分主管不得超过1人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleChangeOwnerUsers(users) {
            this.formData.GradingSupervisorEmployeeList = users
            this.$refs["formData"].validateField("GradingSupervisorEmployeeList");
        },
        //查看详情
        handleRelation(row) {
            this.$refs.empSelector.handleShow()
        },
        handleChange(users) {
            this.formData.EmployeeList = users
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleRemove(index) {
            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.formData.EmployeeList.splice(index, 1);
            });
        },
        handleRemoveAll() {
            if(this.multipleSelection.length == 0) {
                this.$message({
                    message: '请选择要删除的行',
                    type: 'error'
                })
                return false
            }

            this.$confirm("是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                for(let i = 0; i < this.multipleSelection.length; i++) {
                    let idx = this.formData.EmployeeList.findIndex(s => s.EmployeeId == this.multipleSelection[i].EmployeeId)
                    if(idx > - 1) {
                        this.formData.EmployeeList.splice(idx, 1)
                    }
                }
            });
        },
        handleClose(){
            this.$refs.appDialogRef.handleClose();
        },

    }

}
</script>
<style lang="scss" scoped>

</style>