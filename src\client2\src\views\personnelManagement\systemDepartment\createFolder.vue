<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='700' :width='600'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="部门名称" prop="DepartmentName">
                                <el-input maxlength="50" :disabled="!editable" v-model="formData.DepartmentName"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <app-button @click="createData" :buttonType='1' v-show="editable"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as systemDepartment from '@/api/personnelManagement/systemDepartment'

export default {
    name: "systemDepartment-createFolder",
    directives: {},
    components: {

    },
    mixins: [],
    props: {

        dialogStatus: { //create、update、detail
            type: String,
            default: 'create'
        },
        //操作的节点，如果是新增，则为父节点；编辑为当前节点
        node: {
            type: Object,
            required: true
        },
    },
    watch: {
        '$attrs.dialogFormVisible'(val) {
            if (val) {
                this.resetFormData()
                if (this.dialogStatus != 'create') {
                    this.getDetail()
                }
            }
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail"
        },
        pageTitle() {
            if (this.dialogStatus == 'create') {
                return '添加部门信息'
            } else if (this.dialogStatus == 'update') {
                return '编辑部门信息'
            }
        },

    },
    mounted() {
        this.resetFormData()
        if (this.dialogStatus != 'create') {
            this.getDetail()
        }
    },

    created() {
        this.rules = this.initRules(this.rules)
    },

    data() {

        return {
            rules: {
                DepartmentName: {
                    fieldName: "部门名称",
                    rules: [{
                        required: true,
                        max: 100
                    }]
                },
            },
            labelWidth: '100px',
            formData: {
                Id: '',
                DepartmentName: '',
                DepartmentLevel: 0
            },
        };
    },
    methods: {
        resetFormData() {
            this.formData = {
                Id: '',
                DepartmentName: '',
                DepartmentLevel: 0
            }
        },
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));

                    let result = null

                    if (this.dialogStatus === "create") {

                        delete postData.Id
                        postData.ParentId = this.node.Id
                        postData.DepartmentLevel = this.node.level
                        result = systemDepartment.add(postData)
                    } else if (this.dialogStatus === "update") {

                        postData.DepartmentId = this.node.Id
                        postData.DepartmentLevel = this.node.DepartmentLevel
                        result = systemDepartment.edit(postData)
                    }

                    result.then(res => {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        this.$refs.appDialogRef.createData({dialogStatus: this.dialogStatus, data: this.dialogStatus == 'update' ? postData : res})
                    })
                }
            });
        },
        getDetail() {
            this.formData = Object.assign({}, this.formData, this.node);
            this.formData.DepartmentName = this.formData.DepartmentName;
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }
};
</script>

<style lang="scss" scoped>
</style>
