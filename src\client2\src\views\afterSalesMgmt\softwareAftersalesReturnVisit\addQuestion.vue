<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight="750">
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <div class="lft">
                        <el-row>
                            <!-- <el-col :span="24">
                                <el-form-item label="站点信息" prop="RegionalBusinessRelationId">
                                    <div class="_regional_detail_wrapper">
                                        <div class="btn_wrapper">
                                            <el-button :disabled="!editable" type="text" @click="handleRegionDialog">选择</el-button>
                                        </div>
                                        <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                        <div class="close_wrapper" v-show="formData.RegionalName && editable">
                                            <div class="i_wrapper">
                                                <el-button icon="el-icon-close" class="btn" circle @click="electedRegionalData(null)"></el-button>
                                            </div>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col> -->
                            <el-col :span="24">
                                <el-form-item label="问题标题" prop="Title">
                                    <el-input maxlength="50" :disabled='!editable || isHandle' :title="!editable || isHandle?formData.Title:''" v-model="formData.Title"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="问题类型" prop="SalesAfterQuestionTypeId">
                                    <!-- <el-select
                                    :disabled="!editable || isHandle"
                                    class="sel-ipt"
                                    style="width:100%"
                                    placeholder=""
                                    clearable
                                    v-model="formData.SalesAfterQuestionTypeId"
                                    >
                                    <el-option v-for="item in treeDatas" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select> -->

                                    <span v-if="!editable || isHandle">{{ formData.ClassTypeName }}</span>
                                    <treeselect key='type2'
                                        v-else
                                        class="treeselect-common"
                                        :append-to-body="true"
                                        :normalizer="unitNormalizer"
                                        v-model="formData.SalesAfterQuestionTypeId" 
                                        :default-expand-level="3"
                                        :options="treeDatas" :multiple="false" placeholder='' :show-count="true"
                                        :noResultsText='noResultsTextOfSelTree'
                                        :disabled="!editable || isHandle"
                                        :noOptionsText="noOptionsTextOfSelTree"
                                        zIndex='9999'
                                        :flat='true'
                                        @input="hadnleChangeCustomerUnitId"
                                        >
                                    </treeselect>

                                    
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="问题描述" prop="Describe">
                                    <el-input type='textarea' rows="4" :disabled='!editable || isHandle' v-model="formData.Describe" maxlength="1000"></el-input>
                                </el-form-item>
                            </el-col>
                  
                            <el-col :span="24" v-if="dialogStatus == 'detail'">
                                <el-form-item label="创建人" prop="CreateEmployee">
                                    <emp-selector
                                        :readonly="true"
                                        key="ccusers"
                                        :showType="2"
                                        :multiple="false"
                                        :list="formData.CreateEmployee ? [formData.CreateEmployee] : []"
                                        >
                                    </emp-selector>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item label="处理人" prop="ProcessEmployeeList">
                                    <emp-selector
                                        :readonly="!editable || isHandle"
                                        key="ccusers"
                                        :showType="2"
                                        :multiple="false"
                                        :list="formData.ProcessEmployeeList"
                                        @change="handleChangeUsers"
                                        :beforeConfirm='handleBeforeConfirm'
                                        >
                                    </emp-selector>
                                </el-form-item>
                            </el-col>



                            <el-col :span="24" v-if="isHandle">
                                <el-form-item label="状态" prop="SalesAfterQuestionStatus">
                                    <el-radio v-model="formData.SalesAfterQuestionStatus" :label="2">处理中</el-radio>
                                    <el-radio v-model="formData.SalesAfterQuestionStatus" :label="3">已处理</el-radio>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24" v-if="dialogStatus == 'detail'">
                                <el-form-item label="状态" prop="SalesAfterQuestionStatus">
                                    <span class="item-status" v-if="formData.SalesAfterQuestionStatus"
                                    :style="{backgroundColor: getQuestionStatusObj(formData.SalesAfterQuestionStatus).bgColor,
                                            color: getQuestionStatusObj(formData.SalesAfterQuestionStatus).color}">
                                        {{ getQuestionStatusObj(formData.SalesAfterQuestionStatus).label }}
                                    </span>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24" v-if="dialogStatus != 'create' && dialogStatus != 'update'">
                                <el-form-item label="处理结果" prop="ProcessResult">
                                    <el-input type='textarea' :disabled='!editable && (formData.SalesAfterQuestionStatus == 2 || formData.SalesAfterQuestionStatus == 3)' rows="4" v-model="formData.ProcessResult" maxlength="1000"></el-input>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item label="备注" prop="Remark">
                                    <el-input type='textarea' rows="4" :disabled='!editable' v-model="formData.Remark" maxlength="1000"></el-input>
                                </el-form-item>
                            </el-col>

                        </el-row>
                    </div>
                    <div class="rht">
                        <div class="row-title">
                            <tags :items="optTypes" v-model="currentOptType">
                                <template v-for="t in optTypes" :slot="t.value">
                                    <i :class="t.value == 1 ? 'el-icon-time' : 'el-icon-paperclip'" :key="t.value"></i> {{ t.label }}
                                </template>
                            </tags>
                        </div>

                        <div class="rht-content">
                            <div v-show="currentOptType == 1">
                                <noData v-if="activities.length == 0"></noData>
                                
                                <el-timeline v-loading='activitiesLoading' style="padding: 10px 0;">
                                    <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.CreateTime.split('.')[0]">
                                        <div class="elTimeDiv cl">
                                        <!-- 拜访记录 -->
                                        <!-- <span style="padding-right: 20px; color: red;">{{ activity.Type }} (当前行开发完成后删除) </span> -->
                                        <template>
                                            <div style="display: flex;">
                                            <div class="avatar">
                                                <img class="img-avatar-shadow" :src="activity.CreateEmployee.Avatar" alt="" srcset="">
                                            </div>
                                            <div style="flex: 1;">
                                                <div v-for='(rows, idx) in activity.CommentContent' :key="`row${idx}`">
                                                <span v-for="(content, idx) in rows" :key="idx">
                                                    <span class="content" :key="'dynamicContent-'+index+'-' +idx" :style="content.IsHighLight?'color:#409EFF':''">&nbsp;{{content.Content}}&nbsp;</span>
                                                </span>
                                                </div>
                                            </div>
                                            <div>
                                                <app-table-row-button v-if="activity.Type == 16 && activity.CreateEmployee && activity.CreateEmployee.EmployeeId == getUserInfo().employeeid" @click="handleRemoveRecord(activity)" :type='3' text='删除'></app-table-row-button>
                                            </div>
                                            </div>
                                        </template>
                                        <div v-viewer class="img-wrapper" v-if="activity.AttachmentList && activity.AttachmentList.length > 0">
                                            <img v-for="(p, idx3) in activity.AttachmentList" :key="'pic_' + idx3" style="width: 24%; height: 60px" :src="p.Path" />
                                        </div>
                                        </div>
                                    </el-timeline-item>
                                </el-timeline>
                            </div>
                            <div v-show="currentOptType == 2" class="elTimeBox-wrapper">
                                <app-uploader ref="appUploaderRef" style="padding:10px;" accept="all" :fileType="3" :max="10000" :readonly="!editable" :value="formData.AttachmentList" :fileSize="1024 * 1024 * 500" :minFileSize="100 * 1024" @change="handleFilesUpChange"></app-uploader>
                            </div>
                        </div>
                    </div>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <span class="fl m-r-50" v-if="dialogStatus == 'create'">
                <el-checkbox v-model="isContinue">继续添加</el-checkbox>
            </span>
            
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2'></app-button>
            <!-- 确认 -->
            <app-button @click="createData" :buttonType='1' v-show="editable" :disabled='disabledBtn'></app-button>
        </template>
    </app-dialog>

    <regionalSelector
        @closeDialog="closeRegionDialog"
        @saveSuccess="electedRegionalData"
        :dialogFormVisible="dialogRegionFormVisible"
        :checked="formData.RegionalBusinessRelationId ? {value: formData.RegionalBusinessRelationId, label: formData.RegionalName} : null"
    ></regionalSelector>


</div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import noData from "@/views/common/components/noData";
import regionalSelector from "./regionalSelector";
import * as salesAfterQuestion from "@/api/afterSalesMgmt/salesAfterQuestion";
import * as salesAfterQuestionType from "@/api/afterSalesMgmt/salesAfterQuestionType";
import * as comment from '@/api/projectDev/projectMgmt/comment'
import { vars } from './enum'
import empSelector from '@/views/common/empSelector'

export default {
    name: "addQuestion",
    directives: {},
    components: {
        noData,
        regionalSelector,
        empSelector,
        // tabs,
        // tags,
        // relationOrder,
        

    },
    mixins: [],
    props: {
        dialogStatus: {
            //create、update、detail、handle（处理）
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        checkedRegional: {
            type: Object,
            default: null
        },
    },
    watch: {
        currentOptType: {
            handler(val) {
                if(val == 1) {
                    this.getAllComment()
                }
            },
            immediate: true
        },
        "$attrs.dialogFormVisible": {
            
            handler(val) {

                if (!val) {
                this.isContinue = false;
                }
                
                if (val) {
                    this.resetFormData();

                    if (this.dialogStatus != "create" && this.id) {
                        this.getDetail();
                    }

                    if(this.dialogStatus == 'create' || this.dialogStatus == 'update') {
                        this.currentOptType = 2 //跟踪记录
                    }else{
                        this.currentOptType = 1 //附件
                    }

                    if(this.dialogStatus == 'create' && this.checkedRegional) {
                        this.formData.RegionalBusinessRelationId = this.checkedRegional.value
                        this.formData.RegionalName = this.checkedRegional.label
                    }

                }
            },
            immediate: true
        },
    },
    computed: {
        //是否未处理
        isHandle() {
            return this.dialogStatus == "handle";
        },
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail";
        },
        pageTitle() {
            let mainTitle = '问题'
            if (this.dialogStatus == "create") {
                return `创建${mainTitle}`
            } else if (this.dialogStatus == "update") {
                return `编辑${mainTitle}`
            } else if (this.dialogStatus == "detail") {
                return `${mainTitle}详情`
            } else if (this.dialogStatus == "handle") {
                return `${mainTitle}处理`
            } 
        },
        optTypes() {
            let opts = [{ value: 2, label: '附件' }]
            if(this.dialogStatus == 'create' || this.dialogStatus == 'update') {
                return opts
            }else{
                let temp = { value: 1, label: '处理记录' }
                opts.splice(0, 0, temp)
                return opts
            }
            
        }
    },
    created() {
        if(this.dialogStatus == 'create') {
            this.getTreeDatas().then(res => {
                this.initTreeDatas(res)
            })
        }
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            unitNormalizer(node) {
                // treeselect定义字段
                if(node.QuestionType == 1){
                    node['isDisabled'] = true
                }else{
                    node['isDisabled'] = false
                }
                return {
                    id: node.Id,
                    label: node.Name,
                    children: node.children
                }
            },
            questionStatus: vars.questionStatus,
            currentOptType: 2,
            
            isContinue: false,
            treeDatas: [],
            loading: false,
            disabledBtn: false,
            rules: {
                Title: { fieldName: "问题标题", rules: [{ required: true }] },
                SalesAfterQuestionTypeId: { fieldName: "问题类型", rules: [{ required: true }] },
                Describe: { fieldName: "问题描述", rules: [{ required: true }] },
                RegionalBusinessRelationId: { fieldName: "站点信息", rules: [{ required: true }] },
                SalesAfterQuestionStatus: { fieldName: "状态", rules: [{ required: true }] },
                ProcessResult: { fieldName: "处理结果", rules: [{ required: true }] },

                

            },
            labelWidth: "100px",
            formData: {
                Id: "", //
                Title: "",
                Describe: "",
                SalesAfterQuestionTypeId: null,//类型
                ProcessEmployeeList: [],
                RegionalBusinessRelationId: null,
                RegionalName: '',
                SalesAfterQuestionStatus: 1,
                ProcessResult: '',
                AttachmentList: [],

            },

            dialogRegionFormVisible: false,
            activities: [],
            activitiesLoading: false,


        };
    },

    methods: {
        getAllComment() {
            // let postData = {
            //     CurrentBusinessId: this.id,
            //     CurrentParentBusinessId: this.id,
            //     Type: 32
            // }
            this.activitiesLoading = true
            comment.geByParentBusinessId({id: this.id}).then(res => {
                this.activitiesLoading = false

                this.activities = res.map(s => {
                    // 最后一个元素为创建时间，不需要
                    let comments = JSON.parse(s.CommentContent)
                    // if (comments && comments.length > 0) {
                    //     comments.pop()
                    // }
                    let type = s.Type

                    let result = {
                        Id: s.Id,
                        CurrentBusinessId: s.CurrentBusinessId,
                        Type: type,
                        CreateTime: s.CreateTime,
                        CommentContent: [],
                        AttachmentList: s.AttachmentList,
                        CreateEmployee: s.CreateEmployee
                    }

                    let rows = []
                    for (let i = 0; i < comments.length; i++) {
                        if(!comments[i].IsBr) {
                            rows.push(comments[i])
                            if(i == comments.length - 1) {
                                result.CommentContent.push(rows)    
                            }
                        }else{
                            result.CommentContent.push(rows)
                            rows = []
                        }
                    }

                    return result
                })


            }).catch(err => {
                this.activitiesLoading = false
            })
        },
        getQuestionStatusObj(val) {
            return this.questionStatus.find(s => s.value == val) || {}
        },
        handleFilesUpChange(files) {
            this.formData.AttachmentList = files
        },
        getTreeDatas() {
            return salesAfterQuestionType.getTreeDatas({})
        },
        initTreeDatas(list) {
            
            let _this = this

            _this.treeDatas = listToTreeSelect(list)

            if(this.dialogStatus == 'create' && this.formData.RegionalBusinessRelationId && !!list.find(s => s.Id == this.formData.RegionalBusinessRelationId)) {
                this.formData.SalesAfterQuestionTypeId = this.formData.RegionalBusinessRelationId
            }
        },
        hadnleChangeCustomerUnitId() {
            this.$refs.formData.validateField("SalesAfterQuestionTypeId");
        },
        handleBeforeConfirm(users) {
            if (users && users.length > 1) {
                this.$message({
                    message: '处理人不得超过1人',
                    type: 'error'
                })
                return false
            }
            return true
        },
        handleChangeUsers(users) {
            this.formData.ProcessEmployeeList = users
        },
        clearValidateInfo() {
            this.$nextTick(() => {
                this.$refs["formData"].clearValidate();
            })
        },
        resetFormData() {
            let temp = {
                Id: "", //
                Title: "",
                Describe: "",
                SalesAfterQuestionTypeId: null,//类型
                ProcessEmployeeList: [],
                Remark: "",
                AttachmentList: [],

            };
            
            if(this.$refs.appUploaderRef) {
                this.$refs.appUploaderRef.clearFiles();
            }

            this.formData = Object.assign({}, this.formData, temp);
        },
        async getDetail() {
            let m1 = this.getTreeDatas()
            let m2 = salesAfterQuestion.detail({id: this.id})
            this.loading = true
            Promise.all([m1, m2]).then(res => {

                this.loading = false
                let res1 = res[0] ? res[0] : []
                let res2 = res[1]

                this.initTreeDatas(res1)
                this.formData = Object.assign({}, this.formData, res2);

                //如果当前是处理弹框
                if(this.dialogStatus == 'handle') {
                    this.formData.SalesAfterQuestionStatus = 2 //处理中
                }

            }).catch(err => {
                this.loading = false
            });

        },

        //地区选择
        closeRegionDialog() {
          this.dialogRegionFormVisible = false;
        },
        handleRegionDialog(){
            this.dialogRegionFormVisible=true;
        },
        electedRegionalData(data){
            this.$refs.formData.clearValidate('RegionalBusinessRelationId');
            if(data){
                this.formData.RegionalBusinessRelationId=data.value;
                this.formData.RegionalName=data.label;
            }else{
                this.formData.RegionalBusinessRelationId='';
                this.formData.RegionalName='';
            }
            this.closeRegionDialog()
        },
        createData() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                //提交数据保存

                postData = Object.assign({}, this.formData);
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                }

                if(postData.AttachmentList && postData.AttachmentList.length > 0) {
                    postData.AttachmentIdList = postData.AttachmentList.map(s => s.Id) || []
                }
                delete postData.AttachmentList

                if(postData.ProcessEmployeeList && postData.ProcessEmployeeList.length > 0) {
                    postData.ProcessEmployeeIdList = postData.ProcessEmployeeList.map(s => s.EmployeeId)
                }
                delete postData.ProcessEmployeeList

                this.disabledBtn = true
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = salesAfterQuestion.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = salesAfterQuestion.edit(postData);
                } else if (this.dialogStatus == 'handle') {
                    let temp = {
                        Id: postData.Id,
                        SalesAfterQuestionStatus: postData.SalesAfterQuestionStatus,
                        ProcessResult: postData.ProcessResult,
                        Remark: postData.Remark,
                        AttachmentIdList: postData.AttachmentIdList
                    }
                    result = salesAfterQuestion.handle(temp)
                }

                result.then(res => {
                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.disabledBtn = false
                    if (this.isContinue) {
                        this.resetFormData();
                        this.$emit("reload");
                    } else {
                        this.$refs.appDialogRef.createData();
                    }
                }).catch(err => {
                    this.disabledBtn = false
                });
            });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>



<style lang='scss' scoped>

.wrapper{
    display: flex;
    height: 540px;
    .lft, .rht{
        box-sizing: border-box;
    }
    .lft{
        padding-right: 10px;
        border-right: 1px solid #e3e3e3;
        flex: 1;
        overflow-y: auto;
    }
    .rht{
        width: 380px;
        padding-left: 10px;
        display: flex;
        flex-direction: column;
        .row-title {
            border-bottom: 1px solid #e3e3e3;
        }
        .rht-content{
            flex: 1;
            overflow-y: auto;
            >div{
                height: 100%;
            }
        }
    }
}

.item-status {
    color: #fff;
    padding: 2px 4px;
    border-radius: 10%;
}

.elTimeDiv {
  margin-bottom: 6px;
  // height: 20px;
  // line-height: 20px;
  // > span:first-child {
  //     margin-right: 100px;
  //     width: 320px;
  //     > i:last-child {
  //     max-width: 250px;
  //     }
  // }
  .img-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    img {
      margin-right: 4px;
      margin-bottom: 4px;
    }
  }
}

.avatar {
  margin-right: 10px;
  img {
    width: 25px;
    height: 25px;
    border-radius: 50%;
  }
}
</style>
