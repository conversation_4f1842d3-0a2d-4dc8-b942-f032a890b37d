<template>
<div>
    <app-dialog title="批量导入" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :height="400" :width='600'>
        <template slot="body">
            <el-form :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth">
                <div class="wrapper" v-loading='loading'>
                    <div v-loading='uploadLoading' element-loading-text="导入中...">
                        <el-row>
                            <div style="margin-bottom: 10px;">
                                <span style="margin-left: 15px;">提示：请下载导入模板，按格式修改后导入</span>
                                <el-button style="margin-left: 15px;" @click="downloadTemplate">下载模板</el-button>
                            </div>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="上传文件" prop="AttachmentList">
                                    <el-upload :action="'string'" class="upload-demo" ref="upload" :on-change="handleChange" :on-exceed="handleExceed" accept=".xls,.xlsx" :on-remove="handleRemove" :limit="1" :file-list="formData.AttachmentList" :auto-upload="false">
                                        <el-button type="primary" :disabled='uploadLoading'>选择文件</el-button>
                                        <el-button @click.stop="handleImport" :disabled='uploadLoading' type="success">开始导入</el-button>
                                        <div slot="tip" class="el-upload__tip">仅支持xls、xlsx格式文件</div>
                                    </el-upload>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>

                    <div class="import-result" v-show="showImportResult">
                        <div class="import-result-content">
                            <svg-icon style="font-size: 60px; color: #79c32a;" v-if="importResult" icon-class="正确"></svg-icon>
                            <svg-icon style="font-size: 60px; color: #e76b6d;" v-if="!importResult" icon-class="错误"></svg-icon>

                            <div style="text-align: center;" v-if="importResult">
                                {{ importResultText }}
                            </div>

                            <div style="text-align: center;" v-if="!importResult">
                                {{ importResultText }}
                            </div>
                            <el-button type="primary" @click="() => { showImportResult = false }" v-if="!importResult">继续上传</el-button>
                        </div>
                    </div>
                </div>
            </el-form>
        </template>
        <template slot="footer">
            <!-- 取消 -->
            <app-button @click="handleClose" :buttonType='2' :disabled='uploadLoading'></app-button>
            <!-- 确认 -->
            <!-- <app-button @click="createData" :buttonType='1' :disabled='uploadLoading'></app-button> -->
        </template>
    </app-dialog>
</div>
</template>

<script>

import * as materialDataApi from "@/api/procureInventory/materialData";
import { downloadFile } from '@/utils/index'
export default {
    name: "batch-import",
    props: {
        //已经存在的表格记录总条数
        rowLength: {
            type: Number,
            default: 0
        },
        maxLength: {
            type: Number,
            default: 999
        }
    },
    watch: {
        "$attrs.dialogFormVisible"(val) {
            if (val) {
                this.resetFormData();

            }
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            loading: false,
            uploadLoading: false,
            rules: {
                AttachmentList: { fieldName: "上传文件", rules: [{ required: true }] },
            },
            labelWidth: "80px",
            formData: {
                AttachmentList: [],
            },

            showImportResult: false, //显示导入结果
            importResult: true, //导入结果正确还是错误
            importResultText: '', //导入结果提示
        };
    },
    methods: {
        downloadTemplate() {
            let tempUrl = '/template/材料资料导入模板.xlsx'
            downloadFile(tempUrl)
        },
        resetFormData() {
            let temp = {
                AttachmentList: [],
            };
            this.formData = Object.assign({}, this.formData, temp);
        },
        createData() {
            let self = this,validate = self.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let formData = new FormData();
                formData.append('file', self.formData.AttachmentList[0])
                console.log(formData)
                self.uploadLoading = true
                materialDataApi.importFile(formData).then(res => {
                    
                    self.uploadLoading = false
                    self.showImportResult = true

                    // if(self.rowLength + res.length > self.maxLength) {
                    //     self.importResult = false
                    //     self.importResultText = `导入模板错误<br/>请下载制定模板后再次上传`
                    // }else{
                        self.importResult = true
                        self.importResultText = `成功导入${res.length}条数据`
                        self.$refs.appDialogRef.createData(res);
                    // }
                }).catch(err => {
                    self.uploadLoading = false

                    if(err.messageCode == 130) {
                        self.showImportResult = true
                        self.importResult = false
                        self.importResultText = err.errorMessage
                    }
                });
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
        handleExceed() {
            this.$message.warning(`只能选择一个excel`);
        },
        handleChange(file) {
            this.formData.AttachmentList = [file.raw]
            this.$refs.formData.validateField('AttachmentList')
        },
        handleRemove(file, fileList) {
            this.formData.AttachmentList = []
            this.$refs.formData.validateField('AttachmentList')
        },
        handleImport() {
            this.createData()
        },

        
    }
};
</script>

<style lang="scss" scoped>
.wrapper{
    height: 200px;
    position: relative;
}
.panel-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 4px;
    padding-left: 6px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 10px;
}

.import-result{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #fff;
    z-index: 999;
    .import-result-content{
        width: 100%; 
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        padding: 30px;

    }
}
</style>
