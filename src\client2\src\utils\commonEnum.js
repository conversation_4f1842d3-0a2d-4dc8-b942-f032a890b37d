
export const maps = [
  {list: ['xls', 'xlsx'], suffix: 'excel'},
  {list: ['doc', 'docx'], suffix: 'word'},
  {list: ['ppt', 'pptx'], suffix: 'ppt'},
  {list: ['zip', '7z'], suffix: 'zip'},
  {list: ['txt'], suffix: 'txt'},
  {list: ['png', 'jpg', 'jpeg', 'svg'], suffix: 'image'},
  {list: ['gif'], suffix: 'gif'},
  {list: ['pdf'], suffix: 'pdf'},
  {list: ['avi', 'mp4', 'mov'], suffix: 'video'},
  {list: ['mp3', 'wav', 'ape', 'aac'], suffix: 'music'},
]


/**变更类型 */
export const projectManagementChangeType = [
    { label: '里程碑', value: 1 },
    { label: '团队成员', value: 2 },
    { label: '需求', value: 3 },
]
export const projectManagementChangeTypeLabel =
{
    label1: '里程碑',
    label2: '团队成员',
    label3: '需求'
}


/**审批类型 */
export const approvalType = [
    { label: '会签', value: 2 },
    { label: '单签', value: 3 },
]
export const approvalTypeLabel =
{
    label2: '会签',
    label3: '单签'
}


/**审批状态 */
export const stateCollectionType = [
    { label: '草稿', value: 2 },
    { label: '驳回待提交', value: 3 },
    { label: '待审批', value: 4 },
    { label: '审批中', value: 5 },
    { label: '驳回', value: 6 },
    { label: '其他人 驳回', value: 7 },
    { label: '转审', value: 8 },
    { label: '审批同意', value: 9 },
    { label: '其他人 审批同意', value: 10 },
    { label: '完全审批通过', value: 11 },
]

export const stateCollectionTypeLabel =
{
    label2: '草稿',
    label3: '驳回待提交',
    label4: '待审批',
    label5: '审批中',
    label6: '驳回',
    label7: '其他人 驳回',
    label8: '转审',
    label9: '审批同意',
    label10: '其他人 审批同意',
    label11: '完全审批通过',
}


/**项目管理-里程碑基准类型 */
export const milestoneDatumType = [
    { label: '立项', value: 1 },
    { label: 'PCR1', value: 2 },
    { label: 'PCR2', value: 3 },
    { label: 'PCR3', value: 4 },
    { label: 'PCR4', value: 5 },
    { label: 'PCR5', value: 6 },
    { label: 'PCR6', value: 7 },
    { label: 'PCR7', value: 8 },
    { label: 'PCR8', value: 9 },
    { label: 'PCR9', value: 10 },
    { label: 'PCR10', value: 11 },
]
export const milestoneDatumTypeLabel =
{
    label1: '立项',
    label2: 'PCR1',
    label3: 'PCR2',
    label4: 'PCR3',
    label5: 'PCR4',
    label6: 'PCR5',
    label7: 'PCR6',
    label8: 'PCR7',
    label9: 'PCR8',
    label10: 'PCR9',
    label11: 'PCR10'
}



/**项目管理-关联变更 */
export const relatedChangeType = [
    { value: 1, label: "引起任务变更" },
    { value: 2, label: "引起版本计划变更" },
]
export const relatedChangeTypeLabel =
{
    label1: "引起任务变更",
    label2: "引起版本计划变更"
}



/**是否影响进度 */
export const isImpactProgressType = [
    { value: 1, label: "是" },
    { value: 2, label: "否" },
    { value: 3, label: "待商讨" },
]
export const isImpactProgressTypeLabel =
{
    label1: "是",
    label2: "否",
    label3: "待商讨"
}

/**里程碑所属版本/迭代 */
export const isVersionPlanType = [
    { value: 1, label: "非版本/迭代" },
    { value: 2, label: "版本" },
    { value: 3, label: "迭代" },
]

/**参数分类 */
export const ParamType = [
    { value: 1, label: "环保" },
    { value: 2, label: "生产" },
    { value: 4, label: "安全" },
    { value: 8, label: "节能" },
]

/**状态 */
export const StatusType = [
    { value: 1, label: '未开始', color: 'red' },
    { value: 2, label: '进行中', color: '#00cc00' },
    { value: 3, label: '已完成', color: '#409EFF' },
]

/**设备状态 */
export const PatrolTaskEquipmentStatusType = [
    { value: 1, label: '未检', },
    { value: 2, label: '已检', },
    { value: 3, label: '异常', },
]

/**员工选择列表源类型 */
export const EmployeeSourceType = [
    { value: 1, label: '业务员管理', },
    { value: 2, label: '客服管理', },
]

/**员工选择列表源类型 */
export const ImplementationQuestionStatusType = [
    { value: 1, label: '待处理', },
    { value: 2, label: '处理中', },
    { value: 3, label: '已处理', },
]

/************************************************** 商机 **************************************************/
/**商机来源 */
export const BusinessOpportunitySourceType = [
    { value: 1, label: '自主挖掘', },
    { value: 2, label: '客户介绍', },
    { value: 3, label: '电话来访', },
    { value: 4, label: '其他', },
]
/**商机权重 */
export const BusinessOpportunityWeightType = [
    { value: 1, label: '重要', },
    { value: 2, label: '一般', },
]

// 优先级
export const BusinessOpportunityPriority = [
    { value: 1, label: 'A', },
    { value: 2, label: 'B', },
    { value: 3, label: 'C', },
]

/**商机阶段状态 （机会点列表有改动状态描述，太多地方使用这个枚举  所以复制了一份来改动） */
export const BusinessOpportunityStatus = [
    { value: 1, label: '未开始', },
    { value: 2, label: '持续跟进', },
    { value: 3, label: '已转订单', },
    { value: 4, label: '已终止', },
]
/**商机阶段状态 */
export const BusinessOpportunityPhaseStatus = [
    { value: 1, label: '未开始', },
    { value: 2, label: '进行中', },
    { value: 3, label: '已终止', },
    { value: 4, label: '已完成', },
]
/**商机需求优先级 */
export const BusinessOpportunityDemandPriority = [
    { value: 1, label: '高', },
    { value: 2, label: '中', },
    { value: 3, label: '低', },
]
/**商机立项评估状态 */
export const BusinessOpportunityProjectStatus = [
    { value: 1, label: '前期准备', },
    { value: 2, label: '提交评审', },
    { value: 3, label: '评审通过', },
    { value: 4, label: '评审不通过', },
]
/**商机方案制定类型 */
export const BusinessOpportunitySchemeType = [
    { value: 1, label: '技术方案', },
    { value: 2, label: '施工方案', },
]
/**商机方案制定状态 */
export const BusinessOpportunitySchemeStatus = [
    { value: 1, label: '方案设计', },
    { value: 2, label: '待评审', },
    { value: 3, label: '评审通过', },
    { value: 4, label: '评审不通过', },
]

//左侧树通用枚举
export const businessTreeTypes = [
    { value: 1, label: '实施常见问题' },
    { value: 2, label: '软件实施指南' },
    { value: 3, label: '维修故障案例' },
    { value: 4, label: '工作汇报模板' },
    { value: 5, label: '设备参数模板' },
    { value: 6, label: '回访记录模板' }
]

//业务百科-状态
export const StatusList = [
    { value: 1, label: '有效', color: '#70B603' },
    { value: 2, label: '无效', color: '#FF0000' },
]

/**状态 */
export const StatusClue = [
    { value: 3, label: '已终止', color: 'red' },
    { value: 1, label: '持续跟进', color: '#00cc00' },
    { value: 2, label: '已转化', color: '#409EFF' },
]
