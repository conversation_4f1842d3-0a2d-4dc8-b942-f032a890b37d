<template>
<div class="app-container">
    <div class="bg-white">
        <!-- <page-title title="在线培训" :subTitle="['在线培训管理页面']"></page-title> -->
        <div class="pageWrapper">
            <div class="product-list">
                <div class="btn-wrapper" v-if="btnAddClassification == 'btnAddClassification'">
                    <el-button type="primary" @click="addClassification">创建课程类型</el-button>
                </div>
                <div class="treeBox" v-loading="treeLoading">
                    <el-tree class="elTree" ref="tree" :data="treeDatas" node-key="Id" :filter-node-method="filterNode" :props="defaultProps" default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="data => (checkedNode = data)">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <span class="node-title" :title="node.label" :style="{
                    width:
                      node.level == 1
                        ? '151px'
                        : node.level == 2
                        ? '133px'
                        : '115px'
                  }">{{ node.label }}</span>
                            <span class="node-btn-area">
                                <el-dropdown trigger="click" v-if="data.Id != defaultTreeNode.Id" style="float: right; padding-right: 9px;" @command="handleCommand($event, node, data)">
                                    <span class="el-dropdown-link">
                                        <i class="el-icon-more"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown" v-if="btnAddClassification == 'btnAddClassification'">
                                        <el-dropdown-item v-show="node.level < 3" command="add">添加子级</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="update">编辑</el-dropdown-item>
                                        <el-dropdown-item v-show="node.level <= 3" command="delete">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </span>
                        </span>
                    </el-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading"
                    :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="true" :layoutMode='layoutMode' :isShowBtnsArea='false'
                    @sortChagned="handleSortChange" :optColWidth="160" @rowSelectionChanged="rowSelectionChanged">
                        <template slot="LogoPath" slot-scope="scope">
                            <img :src="scope.row.LogoPath" width="45px" height="45px" style="padding-top: 8px;" />
                        </template>
                        <template slot="IsShow" slot-scope="scope">
                            <span class="item-status" :class="`status-${scope.row.IsShow}`">
                                {{ scope.row.IsShow | isShowFilter }}
                            </span>
                        </template>
                        <template slot="PrincipalEmployeeList" slot-scope="scope">
                            <span v-if="scope.row.PrincipalEmployeeList">{{scope.row.PrincipalEmployeeList.map(s => s.Name).join(",")}}</span>
                        </template>
                        <template slot="ViewRange" slot-scope="scope">{{ getViewRangeObj(scope.row.ViewRange).label || '无' }}</template>
                        <!-- <template slot="ExamType" slot-scope="scope">
                          <span :style="`color:${getExamTypeObj(scope.row.ExamType).color}`">
                            {{ getExamTypeObj(scope.row.ExamType).label || '无' }}
                          </span>
                        </template> -->
                        <template slot="IsWillLearn" slot-scope="scope">
                          <span :style="`color:${getIsWillLearnObj(scope.row.IsWillLearn).color}`">
                            {{ getIsWillLearnObj(scope.row.IsWillLearn).label || '无' }}
                          </span>
                        </template>
                        <template slot="Period" slot-scope="scope">{{ scope.row.Period || '无' }}</template>
                        <template slot="ExamTypeValueName" slot-scope="scope">{{ scope.row.ExamTypeValueName || '无' }}</template>
                        <template slot="Integral" slot-scope="scope">{{ scope.row.Integral || '无' }}</template>
                        <!-- <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm') }}</template> -->
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                          <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch"
                          :layoutMode='layoutMode'>
                            <template slot="KeyWords">
                              <el-input style="width: 100%;" @clear="handleFilter" v-antiShake='{time: 300,callback: () => {handleFilter()}}' clearable
                                v-model.trim="listQuery.KeyWords" placeholder="搜索课程名称/关联试卷/负责人"></el-input>
                            </template>
                            <!-- 是否必学 -->
                            <template slot="IsWillLearn">
                              <el-select v-model="listQuery.IsWillLearn" placeholder="请选择">
                                <!-- <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option> -->
                                <el-option label="非必学" :value="1"></el-option>
                                <el-option label="必学" :value="2"></el-option>
                              </el-select>
                            </template>
                            <!-- 是否必考 -->
                            <!-- <template slot="ExamType">
                              <el-select v-model="listQuery.ExamType" placeholder="请选择">
                                <el-option v-for="item in examTypeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                              </el-select>
                            </template> -->
                            <!-- 可见范围 -->
                            <template slot="ViewRange">
                              <el-select v-model="listQuery.ViewRange" placeholder="请选择">
                                <el-option v-for="item in viewRangeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                              </el-select>
                            </template>
                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <permission-btn :filterBtn='handleFilterBtn' v-on:btn-event="onBtnClicked">
                                    <el-dropdown slot="customDomId" trigger="click" slot-scope="scope" style="margin-left:4px" @command="onBtnClicked">
                                        <el-button type="primary">
                                            {{scope.data.Name}}<i class="el-icon-arrow-down el-icon--right"></i>
                                        </el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="batchEditClassify">修改分类</el-dropdown-item>
                                            <el-dropdown-item command="batchVisibleTime">设置时段</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </permission-btn>
                            </template>
                          </app-table-form>
                        </template>

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <!-- 查看学习记录 -->
                            <app-table-row-button @click="handleReview(scope.row)" :type="1" text="学习记录"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnSetIsShow')" @click="handleSetIsShow(scope.row)" :type="scope.row.IsShow ? 3 : 4" :text="scope.row.IsShow ? '停用' : '启用'"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit')" @click="handleTableUpdate(scope.row)" :type="1"></app-table-row-button>

                            <app-table-row-button v-if="rowBtnIsExists('btnDel')" v-show="!scope.row.IsShow" @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>

                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 创建分类 -->
    <create-folder-page v-if="currentOptNode" @closeDialog="closeFolderDialog" @saveSuccess="handleFolderSaveSuccess" :dialogFormVisible="dialogFolderFormVisible" :dialogStatus="dialogFolderStatus" :node="currentOptNode">
    </create-folder-page>

    <!-- 创建一级分类 -->
    <create-classification-page @closeDialog="closeClassificationDialog" @saveSuccess="handleClassificationSaveSuccess" :dialogFormVisible="dialogClassificationFormVisible">
    </create-classification-page>

    <!-- 添加/修改  -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id" @reload="getList" :selectTypeId="selectTypeId"></create-page>

    <learning-records  @closeDialog="learningRecordsVisible=false" :dialogFormVisible="learningRecordsVisible" :checked-node="learningRecordsRow" />
    
    <!-- 批量修改分类/时段  -->
    <batch-classify @closeDialog="batchClassifyVisible=false" @saveSuccess="handleBatchClassifySuccess" :component-type="componentType"
        :dialogFormVisible="batchClassifyVisible" :ids="multipleSelectionIds"></batch-classify>

</div>
</template>

<script>
import {
    listToTreeSelect
} from "@/utils";
import indexPageMixin from "@/mixins/indexPage";
import * as train from "@/api/informationCenter/train";
import * as trainsClassification from "@/api/informationCenter/trainsClassification";
import createFolderPage from "./createFolder";
import createPage from "./create";
import createClassificationPage from "./createClassification";
import learningRecords from "./learningRecords";
import batchClassify from "./batchClassify";

import { vars } from '../common/vars'

export default {
    name: "knowledge-train",
    mixins: [indexPageMixin],
    components: {
        createFolderPage,
        createPage,
        createClassificationPage,
        learningRecords,
        batchClassify
    },
    props: {},
    filters: {
        nameFilter(creator) {
            if (creator) {
                return creator.Name;
            }
            return "";
        },
        isShowFilter(isShow) {
            if (isShow) {
                return "有效";
            }
            return "无效";
        },
    },
    computed: {
    },
    watch: {
        checkedNode: {
            handler(val) {
                if (val) {
                    this.listQuery.PageIndex = 1;
                    this.listQuery.TrainsClassificationId = val.Id;
                    this.getList();
                }
            },
            immediate: true
        }
    },
    created() {
        this.gettrainsClassification();
        this.btnTextValue();
    },
    data() {
        return {
            // 列表多选  id集合
            multipleSelectionIds: [],

            // 批量修改分类
            batchClassifyVisible: false,
            componentType: 1,

            // 学习记录
            learningRecordsVisible: false,
            learningRecordsRow: {},
            

            viewRangeTypes: vars.trainEnum.viewRangeTypes, // 可见范围 1:所有人 2:按部门 3:按入职时间 4:自定义
            // examTypeTypes: vars.trainEnum.examTypeTypes, // 是否必考 1:不考 2:必考 3:选考
            isWillLearnTypes: vars.learningRecordsEnum.isWillLearnTypes, // 是否必学 1: 非必学 , 2: 必学
            btnAddClassification: "",
            //   btnAddChildren: '',
            btnAdd: "",
            treeDatas: [],
            defaultProps: {
                //树默认结构
                children: "children",
                label: "TrainsClassificationName"
            },
            tableSearchItems: [
                { prop: "KeyWords", label: "", mainCondition: true },
                { prop: "IsWillLearn", label: "是否必学" },
                // { prop: "ExamType", label: "是否必考" },
                { prop: "ViewRange", label: "可见范围" },
            ],

            checkedNode: null, //当前单击选中的节点
            classificationListQuery: {
                TrainsClassificationName: ""
            },

            dialogFolderFormVisible: false,
            dialogFolderStatus: "create",
            currentOptNode: null, //当前操作的文件夹节点（新增、编辑、删除）
            selectTypeId: "", //当前选中的类型ID

            dialogClassificationFormVisible: false,

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            listLoading: false,
            layoutMode: 'simple',
            tabColumns: [
              {
                    attr: {
                        prop: "LogoPath",
                        label: "课程封面",
                        align: "center", width: '100'
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "TrainsName",
                        label: "课程名称",
                        showOverflowTooltip: true, width: '210'
                    }
                },
             
                {
                    attr: {
                        prop: "IsShow",
                        label: "状态",
                        // sortable: "custom"
                    },
                    slot: true
                },
                {
                    attr: {
                        prop: "TrainsClassificationName",
                        showOverflowTooltip: true,
                        label: "课程分类",
                        //    sortable: 'custom'
                    }
                },

                {
                    attr: {
                        prop: "PrincipalEmployeeList",
                        showOverflowTooltip: true,
                        label: "课程负责人",
                    },
                    slot: true
                },
                {attr: {prop: "ViewRange",label: "可见范围"},slot: true},
                {attr: {prop: "Period",label: "学时"},slot: true},
                {attr: {prop: "ExamTypeValueName",label: "关联试卷", showOverflowTooltip: true,},slot: true},
                {attr: {prop: "IsWillLearn",label: "是否必学"},slot: true},
                // {attr: {prop: "ExamType",label: "是否必考"},slot: true},
                {attr: {prop: "Integral",label: "积分"},slot: true},
            ],
            listQuery: {
                TrainsClassificationId: "",
                KeyWords: "",
                IsWillLearn: "",
                // ExamType: "",
                ViewRange: "",
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,
            defaultTreeNode: {
                Id: "00000000-0000-0000-0000-000000000000",
                ParentId: null,
                TrainsClassificationName: "全部课程"
            }
        };
    },
    methods: {
        getViewRangeObj(val) {
          return this.viewRangeTypes.find(
              s => s.value == val
          ) || {};
        },
        // 问题类型 文字转换
        getExamTypeObj(val) {
            return this.examTypeTypes.find(
                s => s.value == val
            ) || {};
        },
        // 问题类型 文字转换
        getIsWillLearnObj(val) {
            return this.isWillLearnTypes.find(
                s => s.value == val
            ) || {};
        },
        
        btnTextValue() {
            let btns = this.topBtns;
            btns.forEach(item => {
                if (item["DomId"] == "btnAdd") {
                    this.btnAdd = "btnAdd";
                }
                if (item["DomId"] == "btnAddClassification") {
                    this.btnAddClassification = "btnAddClassification";
                }
            });
        },
        handleFilterBtn(btns) {
            if (btns && btns.length > 0) {
                return btns.filter(s => s.DomId != 'btnAddClassification')
            }
            return []
        },
        onResetSearch() {
            this.listQuery.KeyWords = "";
            this.listQuery.IsWillLearn = "";
            // this.listQuery.ExamType = "";
            this.listQuery.ViewRange = "";
            this.getList(); //刷新列表
        },

        //查看详情
        handleReview(row, optType = "detail") {
            //   this.$router.push(`/informationCenter/productCenter/productPresentations/detail/${row.Id}?productClassificationId=${row.ProductClassificationId}`)

            // this.id = row.Id;
            // this.dialogStatus = optType;
            // this.dialogFormVisible = true;
            this.learningRecordsRow = row;
            this.learningRecordsVisible = true
        },
        handleSortChange({
            column,
            prop,
            order
        }) {
            this.sortObj = {
                prop,
                order
            };
            this.getList();
        },
        //获取列表
        getList() {
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            if (this.checkedNode && this.checkedNode.Id != this.defaultTreeNode.Id) {
                postData.TrainsClassificationId = this.checkedNode.Id;
            }
            postData = this.assignSortObj(postData);
            this.listLoading = true;
            train
                .getList(postData)
                .then(res => {
                    this.listLoading = false;
                    this.tabDatas = res.Items;
                    this.total = res.Total;
                })
                .catch(err => {
                    this.listLoading = false;
                });
        },
        onBtnClicked: function (domId) {
            switch (domId) {
                //添加分类
                case "btnAddClassification":
                    this.addClassification();
                    break;
                    //添加
                case "btnAdd":
                    this.handleDialog("create");
                    break;
                    // 修改分类
                case "batchEditClassify":
                    this.handleClassify(1);
                    break;
                    // 修改时段
                case "batchVisibleTime":
                    this.handleClassify(2);
                    break;
                    
                // 批量删除
                // case "btnBatchDel":
                //     if (this.multipleSelection.length < 1) {
                //         this.$message({
                //             message: "至少删除一个",
                //             type: "error"
                //         });
                //         return;
                //     }
                //     this.handleTableDelete(this.multipleSelection);
                //     break;
                default:
                    break;
            }
        },
        handleClassify(type){
            if (this.multipleSelection.length>0) {
                this.multipleSelectionIds = this.multipleSelection.map(s=>{return s.Id});
                this.componentType = type;
                this.batchClassifyVisible=true;
            } else {
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        //弹出添加框
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
            this.selectTypeId = this.listQuery.TrainsClassificationId;
        },

        // 弹出编辑框
        handleTableUpdate(row, optType = "update") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },

        //设置有效/无效
        handleSetIsShow(rows) {
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }

            var isShow = rows.IsShow;
            var message = isShow ? "停用" : "启用";
            this.$confirm("是否确认" + message + "?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                train.setIsShow(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "操作成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },

        // 多行删除
        handleTableDelete(rows) {
            let ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id);
            } else {
                ids.push(rows.Id);
            }

            this.$confirm("删除后将同时删除相关学习记录，是否继续删除?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                train.del(ids).then(() => {
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    this.getList();
                });
            });
        },
        // 表格行选择
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },
        handleBatchClassifySuccess(_formData) {
            this.getList();
            this.batchClassifyVisible = false
        },

        /***************************************左侧树菜单操作***************************************/

        listToTreeSelect,

        //按关键字过滤树菜单
        filterNode(value, data) {
            if (!value) return true;
            return data.TrainsClassificationName.indexOf(value) !== -1;
        },

        //获取左侧树菜单
        gettrainsClassification() {
            this.treeLoading = true;
            trainsClassification
                .getListByCondition(this.classificationListQuery)
                .then(res => {
                    this.treeLoading = false;
                    if (!res) {
                        res = [];
                    }
                    res.splice(0, 0, this.defaultTreeNode);

                    this.treeDatas = listToTreeSelect(res);
                    //如果首次加载问价夹树（没有选中），默认选中根节点
                    if (!this.checkedNode) {
                        this.setDefaultChecked();
                    }
                })
                .catch(err => {
                    this.treeLoading = false;
                });
        },
        //设置默认选中第一项（首次加载文件夹树 或 删除文件夹树节点为当前选中的节点）
        setDefaultChecked() {
            this.$nextTick(() => {
                if (this.treeDatas && this.treeDatas.length > 0) {
                    let rootNode = this.treeDatas[0];
                    this.$refs.tree.setCurrentKey(rootNode.Id);
                    this.checkedNode = rootNode;
                }
            });
        },

        //左侧树操作菜单
        handleCommand(optType, node, data) {
      
            this.currentOptNode = node;
            if (optType == "add") {
                this.handleCreateFolder(data);
            } else if (optType == "update") {
                this.handleCreateFolder(data, "update");
            } else if (optType == "delete") {
                this.handleDelete(node, data);
            }
        },

        //添加父分类
        addClassification() {
            this.dialogClassificationFormVisible = true;
            //   this.$prompt('', '添加产品分类', {
            //     confirmButtonText: '确认',
            //     cancelButtonText: '取消',
            //     inputPlaceholder: '请输入分类名称',
            //     inputPattern: /^[\u4e00-\u9fffa-zA-Z0-9]{1,10}$/,
            //     inputErrorMessage: '只能输入汉字、字母、数字，并且不能超过10个字符'
            //   }).then(({ value }) => {
            //     var obj = {
            //       ProductClassificationName: value,
            //       ParentId: null,
            //       Level: 1
            //     };
            //     productClassification.add(obj).then(res => {
            //       this.$notify({
            //         title: '成功',
            //         message: '添加成功',
            //         type: 'success',
            //         duration: 2000
            //       })
            //       this.getProductClassification()
            //     })
            //   }).catch(() => { });
        },

        // 添加/修改 子分类
        handleCreateFolder(data, optType = "create") {
            this.dialogFolderStatus = optType;
            //this.currentOptNode = data
            this.dialogFolderFormVisible = true;
        },

        //删除分类
        handleDelete(node, data) {
            this.$confirm(
                `是否确认删除${data.TrainsClassificationName}所包含的课程吗?`,
                "提示", {
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            ).then(() => {
                trainsClassification.del([data.Id]).then(res => {
                    if (this.checkedNode && this.checkedNode.Id == data.Id) {
                        this.checkedNode = null;
                    }
                    this.gettrainsClassification();
                    this.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                });
            });
        },

        //关闭树菜单的操作层
        closeFolderDialog() {
            this.dialogFolderFormVisible = false;
        },

        //树菜单操作保存
        handleFolderSaveSuccess() {
            this.gettrainsClassification();
            this.closeFolderDialog();
        },

        closeClassificationDialog() {
            this.dialogClassificationFormVisible = false;
        },

        handleClassificationSaveSuccess() {
            this.gettrainsClassification();
            this.closeClassificationDialog();
        }
    }
};
</script>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    flex: 1;
    padding-bottom: 10px;
    overflow-y: auto;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        overflow: auto;
        margin-top: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

/* 有效 */
.status-true {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.2);
}

/* 无效 */
.status-false {
  color: #FF0000;
  background: rgba(255, 0, 0, 0.2);
}

.item-status {
    padding: 2px 4px;
    border-radius: 10%;
}

.btn-wrapper {
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
