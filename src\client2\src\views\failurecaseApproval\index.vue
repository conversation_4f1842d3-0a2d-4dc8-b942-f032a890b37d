<template>
    <div>
        <div class="app-container">
            <div class="bg-white">
                <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas"
                    :tab-auth-columns='tabAuthColumns' :isShowAllColumn='isShowAllColumn' :loading="listLoading"
                    @rowSelectionChanged="rowSelectionChanged" :multable='false' :isShowOpatColumn='rowBtns.length > 0'
                    :startOfTable='startOfTable'>
                    <!-- 表格查询条件区域 -->
                    <template slot="conditionArea">
                        <app-table-form style="padding-top: 10px;" :label-width="'100px'" :items='tableSearchItems'
                            @onSearch='handleFilter' @onReset='onResetSearch'>
                            <template slot="Symptom">
                                <el-input style="width: 100%;" v-model="listQuery.Symptom"></el-input>
                            </template>
                        </app-table-form>
                    </template>
                    <template slot='StatusId' slot-scope="scope">
                        {{ getRepTofcStatus(scope.row.StatusId) }}
                    </template>
                    <!-- 表格批量操作区域 -->
                    <template slot="btnsArea">
                        <permission-btn moduleName="repairorder" v-on:btn-event="onBtnClicked"></permission-btn>
                    </template>
                    <!-- <template slot="tableTopAres">
                        <div style="padding: 4px;">
                            <el-radio-group v-model="listQuery.Status" @change="getList()">
                                <el-radio-button v-for="(r, idx) in radioStatus" :key="idx" :label="r.value">{{ r.text }}</el-radio-button>
                            </el-radio-group>
                        </div>
                    </template> -->
                    <!-- 表格行操作区域 -->
                    <template slot-scope="scope">
                        <app-table-row-button
                            v-show="rowBtnIsExists('btnEdit') && isShowEdit(scope.row.MakerList ,scope.row.CreateEmployee ? scope.row.CreateEmployee.EmployeeId : '', scope.row.StatusId)"
                            @click="handleUpdate(scope.row)" :type='1'></app-table-row-button>
                        <app-table-row-button v-show="rowBtnIsExists('btnDetail')"
                            @click="handleUpdate(scope.row, 'detail')" :type='2'></app-table-row-button>
                        <app-table-row-button v-show="deltable(scope.row)" v-if="rowBtnIsExists('btnDel')"
                            @click="handleDelete(scope.row)" :type='3'></app-table-row-button>
                    </template>
                </app-table>

                <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageIndex"
                    :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>

            <!--编辑维修单转案例库记录-->
            <!-- //点击编辑按钮，如果状态为3（待审批）——不能编辑，所以为detail -->
            <create-dialog @close='handleClosed' @saveSuccess='handSaveSuccess'
                :dialogStatus="dialogStatus == 'update' && detailObj.StatusId == 3 ? 'detail' : dialogStatus"
                :detail='detailObj' :visible='createDialogVisible'>
                <template slot='other-button-area'>
                    <el-button v-show="auditable" type='primary' @click="handleAssign('assign')">转审</el-button>
                    <el-button v-show="auditable" type='primary' @click="handleAssign('audit')">审批</el-button>
                    <el-button v-show="revokable" type='primary' @click="handleRevoke">撤回</el-button>
                </template>
            </create-dialog>

            <!--转审弹框-->
            <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" :title="auditTypes[auditType]"
                :visible.sync="dialogAssignFormVisible" :close-on-click-modal='false' :append-to-body='true'>
                <el-form :rules="assignRules" ref="dataAssignForm" :model="assignTemp" label-position="right"
                    label-width="100px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="审批意见" prop='Description'>
                                <el-input v-model="assignTemp.Description" maxlength="500" clearable type="textarea"
                                    :rows="5"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="转审人员" prop='MakerList'>
                                <emp-selector key='service-users'
                                    :list='assignTemp.MakerList' @change='handleChangeAssignUser'></emp-selector>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <div slot="footer">
                    <el-button type='primary' @click="handleSaveAssign">确认</el-button>
                    <el-button size="mini" @click="handleCloseAssignDialog">取消</el-button>
                </div>
            </el-dialog>

            <!--审批弹框-->
            <el-dialog v-el-drag-dialog class="dialog-mini" width="600px" :title="auditTypes[auditType]"
                :visible.sync="dialogAuditFormVisible" :close-on-click-modal='false' :append-to-body='true'>
                <el-form :rules="auditRules" ref="dataAuditForm" :model="auditTemp" label-position="right"
                    label-width="100px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="" prop='MakerList'>
                                <el-radio-group v-model="auditTemp.Taged" size="mini">
                                    <el-radio label="1" border>同意</el-radio>
                                    <el-radio label="2" border>驳回</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="审批意见" prop='Description'>
                                <el-input v-model="auditTemp.Description" clearable maxlength="500" type="textarea"
                                    :rows="5"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer">
                    <el-button type='primary' @click="handleSaveAssign">确认</el-button>
                    <el-button size="mini" @click="handleCloseAssignDialog">取消</el-button>
                </div>
            </el-dialog>

            <!--审批记录弹框-->
            <!-- <el-dialog
                v-el-drag-dialog
                class="dialog-mini"
                width="600px"
                title="审批记录"
                :visible.sync="dialogHistoryFormVisible"
                :close-on-click-modal='false'
                :append-to-body='true'
            >
                <el-timeline style="max-height: 600px; overflow-y: scroll;">
                    <el-timeline-item
                        v-for="(activity, idx) in FlowOperationhistories"
                        :key="idx"
                        :timestamp="getTimestamp(activity)" placement="top">
                        <el-card class="audit-detail">
                            <h4>{{ activity.Result }}</h4>
                            <p>{{ activity.Content }}</p>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </el-dialog> -->
        </div>
    </div>
</template>

<script>
    import indexPageMixin from '@/mixins/indexPage'
    import elDragDialog from '@/directive/el-dragDialog'
    import { getUserInfo } from '@/utils/auth'
    import * as reptofc from '@/api/repairToFailurecase'
    import CreateDialog from '../common/reptofailurecase/create'
    import EmpSelector from '../common/empSelector'
    
    import { repTofcStatus } from '../common/reptofailurecase/status'


    export default {
        name: 'failurecaseapproval',
        mixins: [indexPageMixin],
        directives: {
            elDragDialog
        },
        components: {
            CreateDialog,
            EmpSelector,
        },
        watch: {
            dialogAssignFormVisible(val) {
                if (!val) {
                    this.resetAssignTemp()
                    if (this.$refs['dataAssignForm']) {
                        this.$nextTick(() => {
                            this.$refs['dataAssignForm'].clearValidate()
                        })
                    }
                }
            },
            dialogAuditFormVisible(val) {
                if (!val) {
                    this.resetAuditTemp()
                    if (this.$refs['dataAuditForm']) {
                        this.$nextTick(() => {
                            this.$refs['dataAuditForm'].clearValidate()
                        })
                    }
                }
            }
        },
        created() {

            this.assignRules = this.initRules(this.assignRules)
            this.auditRules = this.initRules(this.auditRules)
            const validateRequired = (rule, value, callback) => {
                if (value.length <= 0 && this.auditTemp.Taged == '2') {
                    callback(new Error(`驳回时审批意见不能为空`))
                } else {
                    callback()
                }
            }
            if (!this.auditRules['Description'])
                this.auditRules['Description'] = []
            this.auditRules.Description.push({ validator: validateRequired, trigger: 'blur' })

            this.getApprovalUser()
            this.getList()
        },
        computed: {
            // auditRecord() { //审批记录条数
            //     return this.FlowOperationhistories.length
            // },
            //可撤回
            revokable() {
                if (this.detailObj) {
                    let status = this.detailObj.StatusId
                    let isRevokable = status == 3 && this.detailObj.CreateEmployee && getUserInfo().employeeid == this.detailObj.CreateEmployee.CreateEmployeeID
                    return isRevokable && this.dialogStatus == 'update'
                }
                return false
            },
            //可转审、审批
            auditable() {
                //3 待审批
                if (this.detailObj) {
                    let status = this.detailObj.StatusId
                    let makerList = this.detailObj.MakerList.map(p => p.EmployeeId)
                    let isAuditable = status == 3 && makerList.findIndex(p => p == getUserInfo().employeeid) > -1
                    return isAuditable && this.dialogStatus == 'update'
                }
                return false
            },
            editable() {
                //撤回的 :4; 驳回的  :2
                if (this.detailObj) {
                    let status = this.detailObj.StatusId
                    if (this.dialogStatus == 'create' || ((status == 4 || status == 2) && this.detailObj.CreateEmployee && this.detailObj.CreateEmployee.CreateEmployeeID == getUserInfo().employeeid && this.dialogStatus == 'update')) {
                        return false
                    }
                }
                return true
            },
        },
        data() {
            return {
                tabColumns: [
                    {
                        attr: { prop: 'Symptom', label: '故障现象', showOverflowTooltip: true },
                    },
                    {
                        attr: { prop: 'OrderNumber', label: '维修单号' },
                    },
                    {
                        attr: { prop: 'CreateUserName', label: '创建人' },
                    },
                    {
                        attr: { prop: 'CreateDateTime', label: '创建时间' },
                    },
                    {
                        //（1：已提交；2：已撤回；3：已驳回；4：已拒绝；5：已通过）
                        attr: { prop: 'StatusId', label: '状态' },
                        slot: true
                    },
                ],
                total: 0,
                tabDatas: [],
                listLoading: false,
                multipleSelection: [],
                tableSearchItems: [
                    { prop: 'Symptom', label: '故障现象' },
                ],
                AuditUsers: [],//审批人员
                auditTypes: {
                    assign: '转审',
                    audit: '审批',
                },
                auditType: '',//审批类型： assign 转审；audit：审批
                assignTemp: { //转审表单
                    Description: '',//审批意见
                    MakerList: [], //转审人员
                },
                textMap: {
                    update: '编辑',
                    create: '添加'
                },
                dialogFormVisible: false,
                createDialogVisible: false,//转换页面弹框
                dialogAssignFormVisible: false, //转审弹框
                dialogAuditFormVisible: false,//审批弹框
                dialogHistoryFormVisible: false, //审批记录弹框
                detailObj: null,//当前编辑维修单转案例库记录
                //转审表单
                assignRules: {
                    Description: { fieldName: '审批意见', rules: [{ required: true }, { max: 500 }] },
                    MakerList: { fieldName: '转审人员', rules: [{ required: true }] },
                },
                //审批表单
                auditRules: {

                },
                //审批
                auditTemp: {
                    RepairOrderToFailureCasesSymptomsId: '',
                    Taged: '1', //1：通过；2：驳回
                    Description: '',//审批意见，通过可以不填，驳回必填
                    NodeRejectStep: '',//空着
                },
            }
        },
        methods: {
            rowSelectionChanged(rows) {
                this.multipleSelection = rows;
            },
            onResetSearch() {
                this.resetSearch()
            },
            onBtnClicked: function (domId) {
                // console.log('you click:' + domId)
                switch (domId) {
                    case 'btnAdd':
                        // this.handleCreate()
                        break
                    case 'btnEdit':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行编辑',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0])
                        break
                    case 'btnDetail':
                        if (this.multipleSelection.length !== 1) {
                            this.$message({
                                message: '只能选中一个进行查看',
                                type: 'error'
                            })
                            return
                        }
                        this.handleUpdate(this.multipleSelection[0], 'detail')
                        break;
                    case 'btnDel':
                        if (this.multipleSelection.length < 1) {
                            this.$message({
                                message: '请选择要删除的行',
                                type: 'error'
                            })
                            return
                        }

                        if (this.multipleSelection.length > 1) {
                            this.$message({
                                message: '不支持批量删除',
                                type: 'error'
                            })
                            return
                        }
                        this.handleDelete(this.multipleSelection[0])

                        break
                    default:
                        break
                }
            },
            deltable(row) {
                return (row.StatusId == 2 || row.StatusId == 4) && row.CreateEmployee && getUserInfo().employeeid == row.CreateEmployee.CreateEmployeeID
            },
            handleClosed() {
                this.createDialogVisible = false
            },
            handleUpdate(row, optType = 'update') { // 弹出编辑框
                // let id = row.Id
                // let temp = this.tabDatas.find(o => o.Id == id)


                //             this.createDialogVisible = true
                //             this.dialogStatus = optType

                //             this.detailObj = {
                //                 // Id: '', //当前转换记录主键
                //                 // RepairOrderId: det.RepairOrderId, //维修单主键
                //                 // RepairOrderSymptomId: sym.Symptom.Id,//维修单对应故障案例表主键（当前转换故障现象记录行主键——目前只有inputModel为1（手动收入）可以转换）
                //                 // Symptom: sym.Symptom.Text,//故障现象
                //                 // SymptomInputModel: sym.Symptom.InputMode, //故障现象输入模式；1：输入；2：选择
                //                 // Solutions: sym.Solutions,
                //                 // Analysis: sym.Analysis,
                //                 // Images: det.Images,
                //                 // Videos: det.Videos,
                //                 // EquipmentTypeSubtypeRelationIds: det.EquipmentTypeRelationIds,
                //                 // Keywords: [],//关键字
                //                 // PointPositionParams: [],//影响点位参数
                //                 // AssCompAlarmParams: [],//关联报警参数
                //                 CreateUserId: temp.CreateUserId,
                //                 StatusId: temp.StatusId,
                //             }


                reptofc.detail({ repairOrderToFailureCasesSymptomsId: row.Id }).then(res => {
                    this.createDialogVisible = true
                    this.dialogStatus = optType
                    this.detailObj = Object.assign({}, res)



                    // if(this.temp.Videos && this.temp.Videos.length > 0){
                    //     this.temp.Videos = this.temp.Videos.map(v => {
                    //         return {
                    //             Id: v.Id,
                    //             Path: v.Path,
                    //             FileMd5value: v.FileMd5value,
                    //             FileName: v.FileName
                    //         }
                    //     })
                    // }
                    // this.FlowStatus = res.FlowStatus
                    // this.AuditUsers = res.AuditUsers || []
                    // this.CreateUserId = res.CreateUserId
                    // this.CcList = res.CcUsers || []
                    // this.FlowOperationhistories = res.FlowOperationhistories || []
                    // this.$set(this.temp, 'ContractRange', [])
                    // if(this.temp.ServiceStartTime){
                    //     this.temp.ContractRange.push(this.temp.ServiceStartTime)
                    //     this.temp.ContractRange.push(this.temp.ServiceEndTime)
                    // }
                    // if(this.temp.CarInformation){
                    //     this.temp.CarsInformation = this.temp.CarInformation.split(',')
                    // }
                    // this.dialogFormVisible = true
                    // // this.selectOrgs = this.temp.ParentID
                    // this.$nextTick(() => {
                    //     this.$refs['dataForm'].clearValidate()
                    // })
                })

            },
            handSaveSuccess() {
                this.detailObj.StatusId = 3
                this.getList()
            },
            /**
             * 是否可撤回
             * @param epmloyeeId 维修单创建人id
             * @param statusId 维修单状态
             */
            isShowEdit(makerList, epmloyeeId, statusId) {

                if(!makerList) {
                    makerList = []
                }
                let empId = getUserInfo().employeeid
                return (statusId == 3 && empId == epmloyeeId) || //可撤回
                    (statusId == 3 && makerList.findIndex(p => p == empId) > -1) || ////可转审、审批
                    ((statusId == 4 || statusId == 2) && epmloyeeId == empId) //可编辑
            },
            getApprovalUser() {
                reptofc.getApprovalUser().then(res => {
                    this.AuditUsers = res
                })
            },
            handleDelete(row) { // 多行删除
                this.$confirm('是否确认删除选中项?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    reptofc.del({ id: row.Id }).then(() => {
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.getList()
                    })
                })
            },
            getList() {
                this.listLoading = true
                let condition = JSON.parse(JSON.stringify(this.listQuery))
                reptofc.getList(condition).then(res => {
                    this.tabDatas = res.Items
                    this.total = res.Total

                    this.listLoading = false
                }).catch(err => {
                    this.listLoading = false
                })
            },
            handleCurrentChange(val) {
                this.listQuery.PageIndex = val.page
                this.listQuery.PageSize = val.size
                this.getList()
            },
            handleSizeChange(val) {
                this.listQuery.PageSize = val.size
                this.getList()
            },
            //转审、审批
            handleAssign(auditType) {
                this.auditType = auditType
                if (auditType == 'assign') {
                    this.dialogAssignFormVisible = true
                } else if (auditType == 'audit') {
                    this.dialogAuditFormVisible = true
                }
            },
            //重置转审表单
            resetAssignTemp() {
                this.assignTemp = {
                    Description: '',//审批意见
                    MakerList: [], //转审人员
                }
            },
            resetAuditTemp() {
                this.auditTemp = {
                    RepairOrderToFailureCasesSymptomsId: '',
                    Taged: '1', //1：通过；2：驳回
                    Description: '',//审批意见，通过可以不填，驳回必填
                    NodeRejectStep: '',//空着
                }
            },
            handleFilter() {
                // this.listQuery.PageIndex = 1
                // this.listQuery.PageSize = 20
                this.getList()
            },
            handleDialog() {
                this.dialogFormVisible = false
            },
            getRepTofcStatus(statusId) {
                return repTofcStatus[statusId]
            },
            //撤回
            handleRevoke() {
                this.$confirm('是否确认撤回该维修单流程?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    reptofc.revoke({
                        id: this.detailObj.Id
                    }).then(res => {
                        this.$notify({
                            title: '成功',
                            message: '撤回成功',
                            type: 'success',
                            duration: 2000
                        })
                        this.handleDialog()
                        this.handleClosed()
                        this.getList()
                    })
                })
            },
            handleCloseAssignDialog() {
                if (this.auditType == 'assign') {
                    this.dialogAssignFormVisible = false
                } else if (this.auditType == 'audit') {
                    this.dialogAuditFormVisible = false
                }
            },
            handleChangeAssignUser(users) {
                this.assignTemp.MakerList = users
                this.$refs["dataAssignForm"].validateField("MakerList");
            },
            //确认转审
            handleSaveAssign() {
                //提示转审批成功

                let self = this;
                let formName = self.auditType == 'audit' ? 'dataAuditForm' : 'dataAssignForm'

                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        let formData = null

                        if (self.auditType == 'assign') {
                            formData = JSON.parse(JSON.stringify(self.assignTemp))
                            formData.RepairOrderToFailureCasesSymptomsId = this.detailObj.Id
                            formData.MakerList = this.assignTemp.MakerList.map(p => {
                                return p.EmployeeId
                            })
                        } else if (self.auditType == 'audit') {
                            formData = JSON.parse(JSON.stringify(self.auditTemp))
                            formData.RepairOrderToFailureCasesSymptomsId = this.detailObj.Id
                        }

                        let result = null
                        if (self.auditType == 'assign') {
                            result = reptofc.assign(formData)
                        } else if (self.auditType == 'audit') {
                            result = reptofc.audit(formData)
                        }

                        result.then(res => {
                            self.$notify({
                                title: '成功',
                                message: self.auditTypes[self.auditType] + '成功',
                                type: 'success',
                                duration: 2000
                            })

                            if (self.auditType == 'assign') { //转审
                                self.detailObj.MakerList = self.assignTemp.MakerList
                                self.handleCloseAssignDialog()
                                self.handleDialog()
                                self.getList()
                            } else if (self.auditType == 'audit') {//审批
                                self.detailObj.StatusId = 1 //审批通过
                                self.handleClosed()
                                self.handleCloseAssignDialog()
                                self.handleDialog()
                                self.getList()
                            }
                        })
                    }
                })

            },
        },
    }
</script>
