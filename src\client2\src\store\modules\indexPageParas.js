const indexPageParas = {
    state: {
        //列表页查询条件对象
        //作用：列表页跳转到详情页，详情页再返回列表页，用户回填查询参数
        //格式：key——当前列表页路由；value——当前列表页查询参数组成的对象。
        listPageParas: {
            // 'xxx': '111', 
            // 'yyy': '22222',
        }
    },
    mutations: {
        SAVE_LIST_PAGE_PARAS: (state, parasObj) => {
            state.listPageParas[parasObj.path] = parasObj.paras
        }
    }
}

export default indexPageParas