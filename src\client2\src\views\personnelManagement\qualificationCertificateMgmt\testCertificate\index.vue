<template>
  <!-- 型式试验证书 -->
  <div class="app-container">
    <div class="bg-white __dynamicTabContentWrapper">
      <div class="pageWrapper __dynamicTabWrapper">
        <app-table
          ref="mainTable"
          :layoutMode="layoutMode"
          :multable="true"
          :isShowOpatColumn="true"
          :isShowBtnsArea="false"
          :isShowAllColumn="true"
          :optColWidth="120"
          :loading="listLoading"
          :tab-columns="tabColumns"
          :tab-datas="tabDatas"
          :tab-auth-columns="tabAuthColumns"
          :startOfTable="startOfTable"
          @rowSelectionChanged="rowSelectionChanged"
          @sortChagned="handleSortChange"
        >
          <!-- 表格查询条件区域 -->
          <template slot="conditionArea">
            <app-table-form
              :label-width="'100px'"
              :items="tableSearchItems"
              @onSearch="handleFilter"
              @onReset="onResetSearch"
              :layoutMode="layoutMode"
            >
              <template slot="Keywords">
                <el-input
                  style="width: 100%;"
                  placeholder="搜索产品名称/产品型号/证书编号"
                  @clear="getList"
                  v-antiShake="{
                    time: 300,
                    callback: () => {
                      getList();
                    },
                  }"
                  clearable
                  v-model.trim="listQuery.Keywords"
                ></el-input>
              </template>
              <template slot="ClassifyId">
                <el-select
                  style="width: 100%;"
                  clearable
                  v-model="listQuery.ClassifyId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in ClassifyList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  ></el-option>
                </el-select>
              </template>
              <template slot="TimeEffectiveState">
                <el-select
                  style="width: 100%;"
                  clearable
                  v-model="listQuery.TimeEffectiveState"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in statusTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
              <template slot="ReportBookNumber">
                <el-input
                  style="width: 100%;"
                  v-model="listQuery.ReportBookNumber"
                  placeholder="请输入"
                ></el-input>
              </template>
              <template slot="CoverTypeText">
                <el-input
                  style="width: 100%;"
                  v-model="listQuery.CoverTypeText"
                  placeholder="请输入"
                ></el-input>
              </template>
              <template slot="TestingTime">
                <el-date-picker
                  v-model="listQuery.TestingTime"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder
                  end-placeholder
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%;"
                ></el-date-picker>
              </template>
              <template slot="ValidDate">
                <el-date-picker
                  v-model="listQuery.ValidDate"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="-"
                  start-placeholder
                  end-placeholder
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%;"
                ></el-date-picker>
              </template>
              <!-- 表格批量操作区域 -->
              <template slot="btnsArea">
                <permission-btn v-on:btn-event="onBtnClicked">
                  <el-dropdown
                    slot="customDomId"
                    trigger="click"
                    slot-scope="scope"
                    style="margin-left:4px"
                    @command="onBtnClicked"
                  >
                    <el-button type="primary">
                      {{ scope.data.Name }}
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="batchTermOfValidity">修改有效期</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-dropdown
                    slot="exportBtn"
                    trigger="click"
                    slot-scope="scope"
                    style="margin-left:4px"
                    @command="onBtnClicked"
                  >
                    <el-button type="primary">
                      {{ scope.data.Name }}
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="exportData">导出数据</el-dropdown-item>
                      <el-dropdown-item command="exportAttachments">导出附件</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </permission-btn>
              </template>
            </app-table-form>
          </template>
          <template slot="ClassifyId" slot-scope="scope">
            {{ classifyIdToName(scope.row.ClassifyId) }}
          </template>
          <template slot="TestingTime" slot-scope="scope">
            {{ scope.row.TestingTime | dateFilter("YYYY-MM-DD") }}
          </template>
          <template slot="ValidDate" slot-scope="scope">
            <span
              :style="{
                color:
                  scope.row.TimeEffectiveState == 2 || scope.row.TimeEffectiveState == 3
                    ? getStatusObj(scope.row.TimeEffectiveState).color
                    : '',
              }"
            >
              {{ scope.row.ValidDate | dateFilter("YYYY-MM-DD") }}
            </span>
          </template>
          <template slot="TimeEffectiveState" slot-scope="scope">
            <span
              class="item-status"
              v-if="!!scope.row.TimeEffectiveState"
              :style="{
                backgroundColor: getStatusObj(scope.row.TimeEffectiveState).bgColor,
                color: getStatusObj(scope.row.TimeEffectiveState).color,
              }"
            >
              {{ getStatusObj(scope.row.TimeEffectiveState).label }}
            </span>
            <template v-else>无</template>
          </template>
          <template slot="CoverTypeText" slot-scope="scope">
            {{ scope.row.CoverTypeText | emptyFilter }}
          </template>
          <template slot="ParameterSpecification" slot-scope="scope">
            {{ scope.row.ParameterSpecification | emptyFilter }}
          </template>
          <template slot="Remark" slot-scope="scope">{{ scope.row.Remark | emptyFilter }}</template>

          <!-- 表格行操作区域 -->
          <template slot-scope="scope">
            <!-- 详情 -->
            <app-table-row-button
              @click="handleUpdate(scope.row, 'detail')"
              text="详情"
              :type="2"
            ></app-table-row-button>
            <!-- 编辑 -->
            <app-table-row-button
              v-if="rowBtnIsExists('btnEdit')"
              @click="handleUpdate(scope.row, 'update')"
              :type="1"
            ></app-table-row-button>
            <!-- 删除 -->
            <app-table-row-button
              v-if="rowBtnIsExists('btnDel')"
              @click="handleDelete(scope.row)"
              :type="3"
            ></app-table-row-button>
          </template>
        </app-table>
      </div>
      <pagination
        :total="total"
        :page.sync="listQuery.PageIndex"
        :size.sync="listQuery.PageSize"
        @pagination="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 创建/修改 列表 -->
    <create-page
      v-if="createDialogFormVisible"
      :ClassifyList="ClassifyList"
      :id="selectId"
      :dialogStatus="createDialogStatus"
      :dialogFormVisible="createDialogFormVisible"
      @closeDialog="createDialogFormVisible = false"
      @saveSuccess="createSaveSuccess"
    ></create-page>
    <!-- 批量修改有效期 -->
    <term-of-validity
      v-if="batchTermOfValidityVisible"
      :component-type="4"
      :ids="multipleSelectionIds"
      :dialogFormVisible="batchTermOfValidityVisible"
      @closeDialog="batchTermOfValidityVisible = false"
      @saveSuccess="termOfValiditySaveSuccess"
    ></term-of-validity>
    <!-- 分类管理 -->
    <classify-page
      v-if="batchclassifyVisible"
      :component-type="4"
      :dialogFormVisible="batchclassifyVisible"
      @closeDialog="classifySaveSuccess"
      @saveSuccess="classifySaveSuccess"
    ></classify-page>
    <!-- 修改有效时段（提醒设置） -->
    <effective-period
      v-if="batchEffectivePeriodVisible"
      :component-type="4"
      :dialogFormVisible="batchEffectivePeriodVisible"
      @closeDialog="batchEffectivePeriodVisible = false"
    ></effective-period>
    <!--导出数据弹窗-->
    <exportDataDialog
      v-if="exportDataVisible"
      :dialogFormVisible="exportDataVisible"
      @closeDialog="exportDataVisible = false"
      :tabColumns="tabColumns"
      :listQueryParams="listQuery"
      :multipleSelection="multipleSelection"
    />
  </div>
</template>
<script>
import * as TestCertificateApi from "@/api/personnelManagement/TestCertificate";
import indexPageMixin from "@/mixins/indexPage";
import createPage from "./create";
import classifyPage from "./classify";
import termOfValidity from "../common/termOfValidity";
import * as classify from "@/api/classify";
import {downloadFile, listToTreeSelect} from "@/utils";
import effectivePeriod from "../common/effectivePeriod";
import exportDataDialog from "./exportDataDialog";
import { vars } from "../common/vars";

export default {
  name: "test-certificate",
  mixins: [indexPageMixin],
  components: {
    createPage,
    classifyPage,
    termOfValidity,
    effectivePeriod,
    exportDataDialog,
  },
  filters: {},
  created() {
    this.getClassifyList();
    this.getList();
  },
  data() {
    return {
      /**修改提醒设置 */
      batchEffectivePeriodVisible: false,

      batchclassifyVisible: false,

      ClassifyList: [], // 产品类型集合
      statusTypes: vars.statusTypes, // 列表中状态  s

      selectId: "",
      createDialogStatus: "create",
      createDialogFormVisible: false,
      exportDataVisible: false,
      batchTermOfValidityVisible: false,
      statusList: [],
      total: 0,
      listQuery: {},
      layoutMode: "simple",
      listLoading: false,
      tableSearchItems: [
        { prop: "Keywords", label: "", mainCondition: true },
        { prop: "ClassifyId", label: "产品类型" },
        { prop: "TimeEffectiveState", label: "状态" },
        { prop: "ReportBookNumber", label: "报告书编号" },
        { prop: "CoverTypeText", label: "覆盖型号" },
        { prop: "TestingTime", label: "试验时间" },
        { prop: "ValidDate", label: "有效期至" },
      ],
      multipleSelection: [],
      tabDatas: [],
      tabColumns: [
        { attr: { prop: "Name", label: "产品名称", showOverflowTooltip: true } },
        { attr: { prop: "ClassifyId", label: "产品类型", width: "120" }, slot: true },
        {
          attr: { prop: "TimeEffectiveState", label: "状态", sortable: "custom", width: "100" },
          slot: true,
        },
        { attr: { prop: "ProductModelNumber", label: "产品型号", showOverflowTooltip: true } },
        { attr: { prop: "CertificateNumber", label: "证书编号", showOverflowTooltip: true } },
        { attr: { prop: "ReportBookNumber", label: "报告书编号", showOverflowTooltip: true } },
        {
          attr: { prop: "TestingTime", label: "试验时间", sortable: "custom", width: "110" },
          slot: true,
        },
        {
          attr: { prop: "ValidDate", label: "有效期至", sortable: "custom", width: "110" },
          slot: true,
        },
        {
          attr: {
            prop: "CoverTypeText",
            label: "覆盖型号",
            showOverflowTooltip: true,
            renderHeader: this.renderHeader,
          },
          slot: true,
        },
        {
          attr: {
            prop: "ParameterSpecification",
            label: "参数说明",
            showOverflowTooltip: true,
            renderHeader: this.renderHeader,
          },
          slot: true,
        },
        {
          attr: {
            prop: "Remark",
            label: "备注",
            showOverflowTooltip: true,
            renderHeader: this.renderHeader,
          },
          slot: true,
        },
      ],
    };
  },
  methods: {
    renderHeader(h, { column }) {
      let colObj = this.tabColumns.find(s => s.attr.prop == column.property);
      return (
        <span>
          <span>{column.label}</span>
          <el-button
            style="margin-left: 4px;"
            type="text"
            size="small"
            on-click={() =>
              (colObj.attr["showOverflowTooltip"] = !colObj.attr["showOverflowTooltip"])
            }
          >
            {colObj.attr["showOverflowTooltip"] ? "展开" : "收起"}{" "}
            <i
              class={
                colObj.attr["showOverflowTooltip"] ? "el-icon-arrow-right" : "el-icon-arrow-down"
              }
            ></i>
          </el-button>
        </span>
      );
    },
    classifyIdToName(Id) {
      let self = this;
      if (self.ClassifyList.length == 0) return "";
      return self.ClassifyList.find(s => s.Id == Id)
        ? self.ClassifyList.find(s => s.Id == Id).Name
        : "";
    },
    // 获取分类列表
    getClassifyList() {
      let self = this;
      let paramData = {
        PageSize: 10000,
        PageIndex: 1,
        BusinessType: 8,
      };
      classify.getListPage(paramData).then(response => {
        self.ClassifyList = listToTreeSelect(response.Items);
      });
    },
    // 获取页面列表数据
    getList() {
      let self = this;
      self.listLoading = true;
      let postDatas = JSON.parse(JSON.stringify(self.listQuery));
      postDatas = self.assignSortObj(postDatas);
      if (postDatas.TestingTime && postDatas.TestingTime.length == 2) {
        postDatas.StartTestingTime =
          postDatas.TestingTime[0] == postDatas.TestingTime[1]
            ? postDatas.TestingTime[0] + " 00:00:00"
            : postDatas.TestingTime[0];
        postDatas.EndTestingTime =
          postDatas.TestingTime[0] == postDatas.TestingTime[1]
            ? postDatas.TestingTime[1] + " 23:59:59"
            : postDatas.TestingTime[1];
      }
      if (postDatas.ValidDate && postDatas.ValidDate.length == 2) {
        postDatas.StartValidDate =
          postDatas.ValidDate[0] == postDatas.ValidDate[1]
            ? postDatas.ValidDate[0] + " 00:00:00"
            : postDatas.ValidDate[0];
        postDatas.EndValidDate =
          postDatas.ValidDate[0] == postDatas.ValidDate[1]
            ? postDatas.ValidDate[1] + " 23:59:59"
            : postDatas.ValidDate[1];
      }
      delete postDatas.TestingTime;
      delete postDatas.ValidDate;
      TestCertificateApi.getList(postDatas)
        .then(res => {
          self.listLoading = false;
          self.tabDatas = res.Items;
          self.total = res.Total;
        })
        .catch(err => {
          self.listLoading = false;
        });
    },
    // 列表排序切换 搜索
    handleSortChange({ column, prop, order }) {
      this.sortObj = {
        prop,
        order,
      };
      this.getList();
    },
    // 头部搜索 重置
    onResetSearch() {
      // this.listQuery.Keywords = "";
      this.listQuery = this.$options.data().listQuery;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageIndex = 1;
      this.getList();
    },
    // 表格顶部按钮点击事件
    onBtnClicked(domId) {
      switch (domId) {
        case "btnAdd":
          this.handleUpdate(null, "create");
          break;
        case "batchTermOfValidity":
          this.handleTermOfValidity();
          break;
        case "btnClassify":
          this.batchclassifyVisible = true;
          break;
        case "btnEffectivePeriod":
          this.handleEffectivePeriod();
          break;
        case "exportData":
          this.exportDataVisible = true;
          break;
        case "exportAttachments":
          this.exportAttachment();
          break;
        default:
          break;
      }
    },
    /** 弹出 导出附件*/
    exportAttachment() {
      if(!this.multipleSelection.length){
        this.$message.error('请选择需要导出的数据')
        return
      }
      let innerHtml = `
         <i class="el-message-box__status el-icon-warning"></i>
         <div class="el-message-box__message">
           <span>是否导出所选列表数据的附件</span></br>
           <span style="color:#C9CDD4">注意：每一条数据会形成一个文件包，文件包的命名规则是【产品名称】+【证书编号】
           </span>
         </div>
         `;
      this.$alert(innerHtml, "提示", {
        dangerouslyUseHTMLString: true,
        showCancelButton:true,
      }).then(() => {
        const params = {
          BusinesIdList:this.multipleSelection.map(t=>t.Id)
        }
        TestCertificateApi.packDownloadTestCertificate(params).then((res)=>{
          downloadFile(res.Path);
        })
      });
    },
    /** 弹出 编辑有效时段 */
    handleEffectivePeriod() {
      this.multipleSelectionIds = this.multipleSelection.map(s => {
        return s.Id;
      });
      this.batchEffectivePeriodVisible = true;
    },
    /** 弹出 批量编辑编辑有效期 */
    handleTermOfValidity() {
      if (this.multipleSelection.length > 0) {
        this.multipleSelectionIds = this.multipleSelection.map(s => {
          return s.Id;
        });
        this.batchTermOfValidityVisible = true;
      } else {
        this.$message({
          message: "至少选择一条",
          type: "error",
        });
      }
    },
    /** 弹出编辑框 row = Obj ; optType = create 、 update 、 detail */
    handleUpdate(row, optType = "update") {
      this.selectId = optType === "create" ? "" : row.Id;
      this.createDialogStatus = optType;
      this.createDialogFormVisible = true;
    },
    /** 编辑框 点确定后 关闭 并刷新列表 */
    createSaveSuccess(d) {
      console.log("d", d);
      if (!d) {
        this.createDialogFormVisible = false;
      }
      // this.listQuery.PageIndex = 1;
      this.getList();
    },
    termOfValiditySaveSuccess() {
      this.batchTermOfValidityVisible = false;
      this.getList();
    },
    // 关闭分类弹窗 更新列表的分类数据
    classifySaveSuccess() {
      this.batchclassifyVisible = false;
      this.getClassifyList();
    },
    /** 删除 */
    handleDelete(rows) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        TestCertificateApi.del([rows.Id]).then(() => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      });
    },
    /**分页页大小变更 */
    handleSizeChange(val) {
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    /**分页页码变更 */
    handleCurrentChange(val) {
      this.listQuery.PageIndex = val.page;
      this.listQuery.PageSize = val.size;
      this.getList();
    },
    rowSelectionChanged(rows) {
      this.multipleSelection = rows;
    },
    getStatusObj(val) {
      return vars.statusTypes.find(s => s.value == val) || {};
    },
  },
};
</script>
