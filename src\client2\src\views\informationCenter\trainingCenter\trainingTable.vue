<template>
    <div class="seleEmpCon">
        <div class="lft">
            <div class="input-wrapper">
                <el-input class="elInput" style="margin:10px; width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            </div>
            <el-tree class="tree" :data="treeDatas" node-key="Id" :default-expanded-keys="defaultExpandedKey" :filter-node-method="filterNode" :props="defaultProps" ref="treeRef"  default-expand-all :expand-on-click-node="false" :highlight-current="true" :check-on-click-node="true" @node-click="handleNodeClick">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="node-title" :title="node.label">{{ node.label }}</span>
                </span>
            </el-tree>
        </div>

        <div class="rht">
            <div class="input-wrapper">
                <el-input class="elInput" style="margin:10px; width:calc(100% - 20px);" prefix-icon="el-icon-search" placeholder="搜索课程名称/负责人" v-model="filterText2"></el-input>
            </div>
            <div class="tab-wrapper">
                <el-table class="empTab" height="540" fit :data="tabDatas" name="CommonEmpTab" @select-all="selectAll" @select="select" v-loading="listLoading" ref="mainTable2">
                    <el-table-column type="selection" width="55" align="left" :selectable="checkSelectable"></el-table-column>

                    <el-table-column prop="LogoPath" width="80" label="课程封面">
                        <template slot-scope="scope">
                            <img :src="scope.row.LogoPath" style="width:50px;height:50px;border-radius: 4px">
                        </template>
                    </el-table-column>

                    <el-table-column prop="TrainsName" label="课程名称" align="left" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="TrainsClassificationName" label="培训类型" align='left'></el-table-column>
                    <el-table-column prop="PrincipalEmployeeList" label="负责人" align="left" show-overflow-tooltip>
                        <template slot-scope="scope">
                            {{ scope.row.PrincipalEmployeeList.map(s => s.Name).join('、') }}
                        </template>
                    </el-table-column>

                    <el-table-column prop="IsShow" label="状态" width="60">
                        <template slot-scope="scope">
                            <span class="item-status" :class="`status-${scope.row.IsShow}`">
                                {{ scope.row.IsShow | isShowFilter }}
                            </span>
                        </template>
                    </el-table-column>

                    
                    <el-table-column prop="Period" label="学时" width="80"></el-table-column>


                    <el-table-column prop="ExamTypeValueName" label="关联试卷">
                        <template slot-scope="scope">
                            {{ scope.row.ExamTypeValueName || "无" }}
                        </template>
                    </el-table-column>

                    <el-table-column prop="Integral" label="积分" width="80">
                        <template slot-scope="scope">
                            {{ scope.row.Integral || "无" }}
                        </template>
                    </el-table-column>

                    <el-table-column prop="IsWillLearn" label="是否必学" width="100">
                        <template slot-scope="scope">
                            <span :style="`color:${getIsWillLearnObj(scope.row.IsWillLearn).color}`">
                            {{getIsWillLearnObj(scope.row.IsWillLearn).label || "无"}}
                            </span>
                        </template>
                    </el-table-column>
                    

                </el-table>
            </div>
            <pagination
                :total="total"
                :page.sync="listQuery.PageIndex"
                :size.sync="listQuery.PageSize"
                @pagination="handleCurrentChange"
                @size-change="handleSizeChange"
            />
        </div>
    </div>
</template>

<script>
import { listToTreeSelect } from '@/utils'
import * as train from "@/api/informationCenter/train";
import * as trainsClassification from "@/api/informationCenter/trainsClassification";
import {vars} from "@/views/knowledge/common/vars";

export default {
    name: 'emp-table',
    props: {
        existsUsers: { //父级组件传入和当前已选人员
            type: Array,
            default: () => {
                return []
            }
        },
        multiple: { //父级附件设置:multiple="false" ，即可设置为单选
            type: Boolean,
            default: true
        },
        visible: { //当前组件展示与否
            type: Boolean,
            default: false
        },
        condition: {
            type: Object,
            default: null
        },
        disabledList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    filters: {
        isShowFilter(isShow) {
            if (isShow) {
                return "有效";
            }
            return "无效";
        },
    },
    created() {
        var _this = this; //暂时没有找到更好的办法来判断展示单选提示和全选按钮，如果有更好的办法请联系曹思平，谢谢。
        _this.$nextTick(function () { //初次打开组件在渲染结束后初始化
            $('div[name="CommonEmpTab"] th .el-checkbox').parent().attr('name', 'checkboxDiv'); //给全选框上级元素增加标识
            _this.elCheckbox = $('div[name="CommonEmpTab"] th .el-checkbox'); //默认加载全选框，将全选框存入变量重复加载时根据选择模式进行dom操作
            if (!_this.multiple)
                $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //将全选框改成单选提示
        })
    },
    watch: {
        filterText(val) {
            this.$refs.treeRef.filter(val);
        },
        filterText2(val) {
            let _this = this
            _this.filterNodes({
                vm: _this,
                val
            })
        },
        visible: { //展示当前组件刷新列表并根据父组件传入的已选人员进行页面选中
            handler() {
                var _this = this;
                if (_this.multiple) //展示当前组件后判断单选和多选模式
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html(_this.elCheckbox); //将全选框放入table表头
                else
                    $('div[name="CommonEmpTab"] div[name="checkboxDiv"]').html('单选'); //修改单选框提示
                _this.checkedUsers = JSON.parse(JSON.stringify(_this.existsUsers)) || [];
                _this.getList();
            },
            immediate: true
        },

    },
    data() {
        return {
            /**树筛选内容 */
            filterText: "",
            filterText2: "",
            total: 0,
            elCheckbox: null,
            checkedUsers: JSON.parse(JSON.stringify(this.existsUsers)) || [],
            treeDatas: [], //部门树
            defaultProps: { //树默认结构
                children: 'children',
                label: 'label'
            },
            defaultExpandedKey: [], //部门树默认展开节点
            employeeIds: [], //员工IS
            tabDatas: [], //员工列表
            listQuery: { // 查询条件
                Keywords: '',
            },
            listLoading: false,
            checkedNode: null
        }
    },
    methods: {
        // 问题类型 文字转换
        getIsWillLearnObj(val) {
        return vars.learningRecordsEnum.isWillLearnTypes.find(
            s => s.value == val
        ) || {};
        },
        filterNodes: _.debounce(({vm, val}) => {
            let _this = vm
            _this.listQuery.Keywords = val
            _this.getList()
        }, 150),
        /**按关键字过滤树菜单 */
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },
        handleNodeClick(node) {
            this.checkedNode = node
            this.getList()
        },
        setCheckRow() { //根据父级传入的已选人员设置选中
           var _this = this;
            if (_this.checkedUsers && _this.checkedUsers.length > 0) {
                var checkedUsers = _this.tabDatas.filter(s => _this.checkedUsers.map(u => u.Id).some(o => o == s.Id)) || []

                checkedUsers.forEach(u => {
                    _this.$nextTick(() => {
                        if (_this.$refs.mainTable2)
                            _this.$refs.mainTable2.toggleRowSelection(u);
                    })
                })
                
            } else {
                _this.$refs.mainTable2.clearSelection();
            }
        },
        getSelectedList() {
            return JSON.parse(JSON.stringify(this.checkedUsers || []))
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        // valueChange(e) { //查询框值改变
        //     var _this = this;
        //     if (e == '' && _this.treeHide) { //如查询框清空内容
        //         _this.treeHide = false; //展示树形菜单
        //         _this.getList(); //刷新人员列表  
        //     }
        // },
        selectHandel(row) { //选择框处理
            var _this = this;

            var tmpRow = _this.checkedUsers.some(m => m.Id == row.Id);
            if (tmpRow) { //选中的数据是否已包含在_this.checkedUsers内，包含则移除，不包含则添加
                _this.checkedUsers = _this.checkedUsers.filter(m => m.Id != row.Id);
            } else {
                _this.checkedUsers.push(row);
            }
        },
        selectAll(rows) { //全选框事件
            var _this = this;
            let oldSelectedList = JSON.parse(JSON.stringify(this.checkedUsers)) //已选中的
            // 已选中的，且不在当前列表中（不管怎么操作都不会被影响）
            oldSelectedList = oldSelectedList.filter(s => this.tabDatas.findIndex(n => n.Id == s.Id) == -1)
            // 合并上当前选中的
            oldSelectedList = oldSelectedList.concat(rows)
            //最后结果
            this.checkedUsers = oldSelectedList

            // _this.tabDatas.forEach(row => {//循环当前table数据做选择框事件相应处理
            //   if(this.disabledList.findIndex(s => s == row.CommodityId) == -1) {
            //     _this.selectHandel(row);
            //   }
            // });
        },
        select(selection, row) { //单选框事件
            var _this = this;
            if (!_this.multiple) {
                //单选处理
                _this.$refs.mainTable2.clearSelection();
                _this.$refs.mainTable2.toggleRowSelection(row, true);
                _this.checkedUsers = [row];
            } else {
                //多选处理
                _this.selectHandel(row);
            }
        },
        getOrgTree() { //查询并加载部门树

            var _this = this;

            trainsClassification
                .getListByCondition({}).then(response => { //调用公共组件API获取部门数据

                response = response.map(s => {
                    s.Name = s.TrainsClassificationName
                    return s
                })

                response.unshift({
                    Id: "",
                    Name: "全部",
                    Level: 0,
                    ParentId: null
                });
                

                _this.list = response.map(function (item) {
                    return {
                        Id: item.Id,
                        label: item.Name,
                        ParentId: item.ParentId
                    }
                })
                var orgstmp = JSON.parse(JSON.stringify(_this.list));
                var tempOrgsTree = listToTreeSelect(orgstmp); //将部门数据转换成树形结构
                // _this.defaultExpandedKey.push(tempOrgsTree[0]['Id']); //设置默认展开
                
                _this.treeDatas = tempOrgsTree;

                if (_this.treeDatas && _this.treeDatas.length > 0) {
                    if (
                        !(
                            _this.checkedNode &&
                            response.find(t => {
                                return t.Id == _this.checkedNode.Id;
                            })
                        )
                    ) {
                        _this.checkedNode = _this.treeDatas[0];
                    }
                } else {
                    _this.checkedNode = null;
                }
                if (_this.checkedNode) {
                    _this.$nextTick(() => {
                        if(_this.$refs.treeRef) {
                            _this.$refs.treeRef.setCurrentKey(_this.checkedNode.Id);
                        }
                    });
                }
            })
        },
        // query() { //查询按钮单击事件
        //     var _this = this;
        //     if (this.listQuery.Name) {
        //         _this.treeHide = true; //隐藏部门树
        //         _this.$refs.treeDatas.setCheckedKeys([]);
        //         _this.getList();
        //     }
        // },
        getList() {
            var _this = this;
            _this.listLoading = true;
            var result = null;
            
            let postData = JSON.parse(JSON.stringify(_this.listQuery))
            if (this.condition) {
                postData = Object.assign({}, postData, this.condition)
            }

            if (_this.checkedNode && _this.checkedNode.Id) {
                postData.TrainsClassificationId = _this.checkedNode.Id;
            } else {
                delete postData.TrainsClassificationId;
            }
            postData.IsShow = true
            result = train.getList(postData); //根据勾选部门或者输入框调用公共组件API获取人员
            result.then(response => {
                _this.total = response.Total
                _this.tabDatas = response.Items.map(s => {
                    // if(s.Id) {
                    //     s.CommodityId = s.Id
                    //     delete s.Id
                    // }
                    return s
                });
                _this.listLoading = false;
                _this.setCheckRow(); //根据已选人员设置tableRow默认选中
            })
        },
        checkSelectable(row) {
            if (this.disabledList && this.disabledList.length > 0) {
                return this.disabledList.findIndex(s => s == row.Id) == -1
            }
            return true
        },
    },
    mounted() {
        this.getOrgTree();
    }
}
</script>

<style scoped>
/* .treehead>>>.el-input-group__append {
    background: red;
    color: #fff;
}

.input-wrapper>>>.el-input__inner {
    border-radius: 4px 0 0 4px;
} */
</style>

<style lang='scss' scoped>


.seleEmpCon {
    height: 650px;
    // border: 1px solid #eee;
    display: flex;
    
    .lft{
        width: 250px;
        border-right: 1px solid #dcdfe6;
        display: flex;
        flex-direction: column;
        .input-wrapper{
            // padding: 10px;
        }
        .tree{
            flex: 1;
            overflow-y: auto;
        }
    }
    .rht{
        flex: 1;
        padding: 0;
        width: 100%;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .tab-wrapper{
            flex: 1;
            overflow-y: auto;
        }

        .dialog{
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: .5;
            background: $text-primary;
            z-index: 888;
        }
        .panel-selected{
            position: absolute;
            top: 30px;
            bottom: 30px;
            right: -400px;
            width: 400px;
            z-index: 999;
            background: #fff;
            transition: .3s ease-in-out right;
            border-top: 1px solid #dcdfe6;
            border-left: 1px solid #dcdfe6;
            border-bottom: 1px solid #dcdfe6;
            &.active{
                right: 0;
            }

            .panel-content{
                width: 100%;
                height: 100%;
                position: relative;
                .btn{
                    cursor: pointer;
                    position: absolute;
                    width: 40px;
                    height: 100px;
                    left: -40px;
                    top: 50%;
                    transform: translateY(-50%);
                    border-radius: 20px 0 0 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    align-items: center;
                    border-top: 1px solid #dcdfe6;
                    border-left: 1px solid #dcdfe6;
                    border-bottom: 1px solid #dcdfe6;
                    padding: 12px 0;
                    z-index: 999;
                    background: #fff;
                    span:not(:last-child) {
                        font-size: 16px;
                    }
                    .num{
                        display: block;
                        background: #F59A23;
                        text-align: center;
                        padding: 4px;
                        border-radius: 50%;
                        color: #fff;
                        font-size: 12px;
                        height: 22px;
                        width: 22px;
                    }
                }
            }

            .tab-wrapper{
                display: flex;
                flex-direction: column;
                height: 100%;
                .tab-title{
                    display: flex;
                    align-items: center;
                    height: 36px;
                    padding: 0 10px;
                    box-sizing: border-box;
                    border-bottom: 1px solid #dcdfe6;
                    .num{
                        flex: 1;
                    }
                    .btn-clear{
                        
                    }
                }
                .tab-content{
                    overflow-y: auto;
                }
            }
        }
    }
}

.custom-tree-node {
    display: block;
    width: calc(100% - 30px);
    position: relative;
    box-sizing: border-box;
    // padding-right: 10px;

    .node-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

}
.status-true {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.2);
}

/* 无效 */
.status-false {
  color: #FF0000;
  background: rgba(255, 0, 0, 0.2);
}

</style>
