<template>
<div>
    <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width='1000' :maxHeight="800" >
        <template slot="body">
            <div v-loading="formLoading">
                <div v-if="dialogStatus == 'review'" class="section-title">一、系统判断</div>
                <el-form v-if="dialogStatus != 'review'" :rules="rules" ref="formData" :model="formData" label-position="right" :label-width="labelWidth" style="margin-right: 2%;">
                    <el-form-item label="规则名称" prop="RuleName">
                        <el-input :disabled="!editable" maxlength="100" type="text" v-model="formData.RuleName"></el-input>
                    </el-form-item>
                </el-form>
                <ruleTable ref="ruleTable" :readonly="!editable" :values="formData.RuleList || []"></ruleTable>

                <template v-if="dialogStatus == 'review'">
                    <div class="section-title">二、主管判断</div>
                    <div>上述中其他的部分，由主管酌情评分，参照以下标准：</div>
                    <ul>
                        <li>友商信息：竞争对手分析和跟进。  没有维护友商信息， 扣2分</li>
                        <li>上下游工作推进，反馈等。每收到一次上下游投诉扣5分，每发现一次没推进扣2分</li>
                        <li>市场调研和分析：完成设备大普查分析，市场调研和分析，市场需求分析</li>
                        <li>跟进客户规划分析，挖掘机会点，丢第一次，扣50分</li>
                    </ul>
                </template>
            </div>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" :buttonType="2"></app-button>
            <app-button @click="handleSave" text="保存" v-if="editable" :disabled="disabledBtn"></app-button>
        </template>
    </app-dialog>

</div>
</template>

<script>

import ruleTable from './ruleTable'
import * as employeePointsRule from "@/api/personnelManagement/employeePointsRule";

export default {
    name: "employeeDataSetting-create",
    components: {
        ruleTable,
    },
    mixins: [],
    props: {
        dialogStatus: {
            type: String
        },
        id: {
            type: String,
            default: ""
        },
        empId: {
            type: String,
            default: ''
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                    if (this.dialogStatus != "create" && (this.id || this.empId)) {
                        this.getDetail();
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        //不等于详情页面可编辑
        editable() {
            return this.dialogStatus != "detail" && this.dialogStatus != "review";
        },
        pageTitle() {
            if (this.dialogStatus == "create") {
                return "创建规则";
            } else if (this.dialogStatus == "update") {
                return "编辑规则";
            } else if (this.dialogStatus == "detail") {
                return "规则详情";
            } else if (this.dialogStatus == "review") {
                return "评分标准";
            }
        }
    },

    created() {
        this.rules = this.initRules(this.rules);
    },
    data() {
        return {
            formLoading: false,
            disabledBtn: false,
            rules: {
                RuleName: { fieldName: "规则名称", rules: [{ required: true }] },
            },
            labelWidth: "100px",
            formData: {
                Id: "",
                RuleName: "",
                RuleList: [],
                
            },
            
        };
    },
    methods: {
        
        resetFormData() {
            let temp = {
                Id: "",
                RuleName: "",
                RuleList: []
            };
            this.formData = Object.assign({}, this.formData, temp);
        },

        getDetail() {
            let self = this;
            self.formLoading = true;
            let result = null
            if(this.id) {
                result = employeePointsRule.detail({id: self.id})
            }else if(this.empId) {
                result = employeePointsRule.getDetailsByEmployeeId({employeeId: self.empId})
            }
            result.then(res => {
                self.formLoading = false;
                self.formData = Object.assign({}, self.formData, res);
                if (self.formData.LogoPath) {
                    self.formData.CoverFileList = [{
                        Id: res.Logo,
                        Path: res.LogoPath,
                    }]
                }
                
            })
            .catch(err => {
                self.formLoading = false;
            });
        },

        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },

        //保存
        handleSave() {
            let validate = this.$refs.formData.validate();
            Promise.all([validate]).then(valid => {
                let postData = JSON.parse(JSON.stringify(this.formData));
                postData.RuleList = this.$refs.ruleTable.getDatas()

                //提交数据保存
                let result = null;
                if (this.dialogStatus == "create") {
                    delete postData.Id;
                    result = employeePointsRule.add(postData);
                } else if (this.dialogStatus == "update") {
                    result = employeePointsRule.edit(postData);
                }
                this.disabledBtn = true;
                result.then(res => {
                    this.disabledBtn = false;
                    // if (this.goOn) {
                    //     this.resetFormData();
                    //     this.$refs.appUploaderRef.clearFiles();
                    //     this.$refs.formData.resetFields();
                    //     this.formData.AttachmentList = []
                    //     this.$emit("reload");
                    // } else {
                        
                    // }

                    this.$notify({
                        title: "提示",
                        message: "保存成功",
                        type: "success",
                        duration: 2000
                    });
                    this.$refs.appDialogRef.createData();
                }).catch(err => {
                    this.disabledBtn = false;
                });
            });
        },

    }
};
</script>
<style lang="scss" scoped>

.section-title{
    color: #606266;
    font-weight: 700;
    padding: 10px 0;
}

ul{
    padding-left: 20px;
    margin: 10px;
    li{
        list-style-type: square;
    }
}
</style>
