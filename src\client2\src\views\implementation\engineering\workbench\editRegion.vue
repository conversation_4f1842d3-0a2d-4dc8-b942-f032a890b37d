<template>
    <div>
        <app-dialog
            title="编辑地区"
            ref="appDialogRef"
            v-bind="$attrs"
            v-on="$listeners"
            :maxHeight='600'
            :width='600'
        >
            <template slot="body">
                <el-form ref="formData" :rules="rules" :model="formData" label-position="right" :label-width="labelWidth">
                    <div class="wrapper" style="min-height: 330px;">
                        <el-row class="item-content">
                            <el-col :span="24">
                                <el-form-item label="地区详情" prop="RegionalId">
                                    <!-- <treeselect :normalizer="normalizer" key='type2'
                                        class="treeselect-common"
                                        :zIndex='99999999'
                                        :disable-branch-nodes="true"
                                        v-model="formData.RegionalId" :default-expand-level="3"
                                        :options="listToTreeSelect(treedata)" :multiple="false" placeholder='' :show-count="true"
                                        :noResultsText='noResultsTextOfSelTree' :noOptionsText="noOptionsTextOfSelTree">
                                    </treeselect> -->
                                    
                                    <!-- <el-button type="text" @click="handleDialog">选择</el-button>
                                    {{ formData.RegionalName }} -->

                                    <div class="_regional_detail_wrapper">
                                        <div class="btn_wrapper">
                                            <el-button type="text" @click="handleDialog">选择</el-button>
                                        </div>
                                        <div class="regional_text" :title="formData.RegionalName">{{ formData.RegionalName }}</div>
                                        <div class="close_wrapper" v-show="formData.RegionalName">
                                            <div class="i_wrapper">
                                                <el-button icon="el-icon-close" class="btn" circle @click="handleSaveSuccess(null)"></el-button>
                                            </div>
                                        </div>
                                    </div>

                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="站点负责人" prop="EmployeeList">
                                    <emp-selector :isShowClearButton='true' :sourceType="'onlyAuthUsers'" :multiple='true' :showType='2' key='service-users' :list='formData.EmployeeList' @change='handleChangeUsers'></emp-selector>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </template>
            <template slot="footer">

                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>

            </template>
        </app-dialog>

        <v-area-choose
            v-if="dialogFormVisible"
            @closeDialog="closeDialog"
            @electedRegionalData="handleSaveSuccess"
            :dialogFormVisible="dialogFormVisible"
            :condition='{RegionalId: topRegionalId}'
            :multiple='false'
            :checkedList="formData.RegionalId ? [formData.RegionalId] : []"
            :disabledFn="disabledFn"
            :defaultExpandLevel='1'
        ></v-area-choose>
        <!-- 
             -->
    </div>
</template>

<script>
import * as impMgmt from "@/api/implementation/impManagement2"
// import * as regionalManagement from '@/api/systemManagement/regionalManagement'
import empSelector from "../../../common/empSelector"
import vAreaChoose from "../../../afterSalesMgmt/businessMap/common/areaChoose";
// import { listToTreeSelect } from '@/utils'

export default {
    name: "edit-region",
    props: {
        topRegionalId: {
            type: String,
            required: true
        },
        region: {
            type: Object,
            required: true
        },
        disabledList: {
            type: Array,
            default: () => {
                return []
            }
        },
    },
    computed: {
        
    },
    components: {
        empSelector,
        vAreaChoose,
    },
    watch: {
        '$attrs.dialogFormVisible'(val) {
            if(val) {
            }
        },
        region: {
            handler(val) {
                this.formData = Object.assign({}, this.formData, JSON.parse(JSON.stringify(val)))
                this.oldRegionId = this.formData.RegionalId
            },
            // deep: true,
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
        // this.getRegions()
    },
    data() {
        return {
            oldRegionId: '',
            // listToTreeSelect,
            normalizer(node) {
                if(node.RegionalLevel == 0){
                    if(!node.children || node.children.length == 0){
                        node['isDisabled'] = true    
                    }
                }
                // if(node.RegionalLevel != 3){
                //     node['isDisabled'] = true;
                // }
                return {
                    id: node.Id,
                    label: node.RegionalName,
                    children: node.children
                };
            },
            rules: {
                EmployeeList: { fieldName: "站点负责人", rules: [{ required: true }]},
                RegionalId: { fieldName: "地区详情", rules: [{ required: true }] }
            },
            disabledBtn: false,
            labelWidth: '100px',
            formData: {
                Id: '',
                RegionalId: undefined, //地区ID
                RegionalName: '', //地区完整路径
                EmployeeList: [], 
            },
            // treedata: [],

            dialogFormVisible: false,

        };
    },
    methods: {
        createData() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    let temp = this.formData
                    let postData = {
                        Id: temp.Id,
                        RegionalId: temp.RegionalId, //地区ID
                        EmployeeIdList: temp.EmployeeList.map(s => s.EmployeeId), 
                        ImplementationTemplateId: temp.ImplementationTemplateId, //实施模板ID
                    }

                    this.disabledBtn = true
                    impMgmt.editImpRegion(postData).then(res => {
                        this.disabledBtn = false
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000
                        });
                        //构建父页需要的 regional 对象（直接替换）
                        let result = JSON.parse(JSON.stringify(this.formData))

                        result.value = this.formData.Id //实施地区主键
                        
                        let tempPath = this.formData.RegionalName
                        if(tempPath.indexOf('/') > -1) {
                            tempPath = tempPath.substring(tempPath.indexOf('/') + 1)
                        }
                        result.label = tempPath //地区路径
                        result.RegionalName = tempPath //地区路径

                        // let regionalObj = this.treedata.find(s => s.Id == result.RegionalId)
                        // if(regionalObj) {
                        //     let tempPath = regionalObj.ParentName
                        //     //去掉跟节点
                        //     if(tempPath.indexOf('/') > -1) {
                        //         tempPath = tempPath.substring(tempPath.indexOf('/') + 1)
                        //     }
                        // }

                        this.$refs.appDialogRef.createData(result)
                    }).catch(err => {
                        this.disabledBtn = false
                    })
                }
            })

        },
        handleChangeUsers(users) {
            this.formData.EmployeeList = users
        },
        // getRegions() {
        //     regionalManagement.getAllChildrenByRegionalId({regionalId: this.topRegionalId}).then(res => {
        //         this.treedata = res.map(s => {
        //             //不能选择功能中已存在的地区（需要保证可以选择到自己）
        //             s.isDisabled = this.disabledList.findIndex(r => r == s.Id) > -1 && s.Id != this.oldRegionId
        //             return s
        //         })
        //     })
        // },
        disabledFn(data, nodeType) {
            if(this.disabledList.findIndex(r => r == data.Id) > -1 && data.Id != this.oldRegionId) {
                return true
            }
            //只能选择第2、3、4、5级
            if(data.level <= 1) {
                return true
            }
            return false
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },

        /**
         * 地区选择器弹框
         */
        handleDialog(row, optType = "update") {
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(node) {
            
            this.$refs.formData.clearValidate('RegionalId');
            if(node) {
                this.formData.RegionalId = node.Id
                this.formData.RegionalName = node.ParentName
            }else{
                this.formData.RegionalId = ''
                this.formData.RegionalName = ''
            }
            this.closeDialog();
        },
    }
};
</script>

