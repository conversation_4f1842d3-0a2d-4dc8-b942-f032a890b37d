export const vars = {
	//实施工程状态
	implementationSatus: [
		{ value: 1, label: '未开始', color: '#909399' },
		{ value: 2, label: '进行中', color: '#32B372' },
		// { value: 3, label: '已交付', color: '#409EFF' },
		// { value: 4, label: '已终止', color: '#FF9900' },
		// { value: 5, label: '创建待审批', color: '#FF2131' },
		// { value: 6, label: '验收待审批', color: '#FF2131' },
		// { value: 7, label: '创建不通过', color: '#FF9900' },
		// { value: 8, label: '验收不通过', color: '#FF9900' },
		// { value: 9, label: '变更待审批', color: '#FF2131' },
		// { value: 10, label: '变更不通过', color: '#FF9900' },
		{ value: 11, label: '已完成', color: '#267FEB' },
	],
	acceptTypes: [
		{ value: 1, label: '交付' },
		{ value: 2, label: '终止' }
	],
	//地区状态
	regionalStatus: [
		{ value: 1, label: '未开始', color: '#F53F3F' },
		{ value: 2, label: '进行中', color: '#32B372' },
		{ value: 3, label: '已完成', color: '#267FEB' },
	],
	//设备状态
	equStatus: [
		{ value: 1, label: '未开始', color: '#F53F3F' },
		{ value: 2, label: '进行中', color: '#32B372' },
		{ value: 3, label: '已完成', color: '#267FEB' },
	],
	processStatus: [
		{ value: 1, label: '未开始', color: '#F53F3F' },
		{ value: 2, label: '进行中', color: '#32B372' },
		{ value: 3, label: '已完成', color: '#267FEB' },
	],
	//审批结果
	approvalResult: [
		{ value: 1, label: '通过', color: '#32B372' },
		{ value: 2, label: '不通过', color: '#F53F3F' },
	],
	//设备审批结果
	equApprovalStatus: [
		{ value: 1, label: '添加待审批' },
		{ value: 2, label: '添加已通过' },
		{ value: 3, label: '添加不通过' },
		{ value: 4, label: '变更待审批' },
		{ value: 5, label: '变更已通过' },
		{ value: 6, label: '变更不通过' },
	],
	approvalStatuObj: {
		approvalStatus: [
			{ value: 1, label: '待审批', color: '#FFAA00' },
			{ value: 2, label: '已审批', color: '#32B372' },
			{ value: 3, label: '不通过', color: '#F53F3F' }
		],
	},
	//设备审批结果
	questionStatus: [
		{ value: 1, label: '待处理', color: '#F53F3F' },
		{ value: 2, label: '处理中', color: '#32B372' },
		{ value: 3, label: '已处理', color: '#267FEB' },
	],
}