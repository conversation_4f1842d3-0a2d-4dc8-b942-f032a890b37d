<template>
<div>
    <app-dialog title="关联人员" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :maxHeight='600' :width='800'>
        <template slot="body">
            <div class="wrapper __dynamicTabContentWrapper" style="height:500px">
                <div class="content __dynamicTabWrapper" >
                    <app-table ref="mainTable" :tab-columns="tabColumns" :tab-datas="tabDatas" :isShowAllColumn="true" :loading="listLoading"
                    :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="true" :layoutMode='layoutMode' :isShowBtnsArea='false'
                    @rowSelectionChanged="rowSelectionChanged"
                    :optColWidth="80">
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                          <app-table-form :label-width="'100px'" :items="[]" :layoutMode='layoutMode'>
                            <!-- 表格批量操作区域 -->
                            <template slot="btnsArea">
                                <emp-selector
                                    key="ccusers"
                                    :showType="2"
                                    :multiple="true"
                                    :list="tabDatas"
                                    @change="handleChangeUsers"
                                    :beforeConfirm='handleBeforeConfirm'
                                    >
                                    <!-- :condition="{SourceType:2}" -->
                                    <el-button slot="reference" type="primary">添加人员</el-button>
                                </emp-selector>
                                <el-button type="danger" @click="batchDel" style="margin-left:10px;">删除人员</el-button>
                            </template>
                          </app-table-form>
                        </template>
                        <template slot="DepartmentName" slot-scope="scope">{{scope.row.DepartmentName||'无'}}</template>
                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleDel(scope.row)" :type="3" text="删除"></app-table-row-button>
                        </template>
                    </app-table>

                </div>
                <!-- <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" /> -->
            </div>
        </template>
        <template slot="footer">
            <app-button @click="handleClose" type='2' text="关闭"></app-button>
        </template>
    </app-dialog>
</div>
</template>

<script>
import * as businessRoleApi from "@/api/businessRole";

import empSelector from '@/views/common/empSelector'
import indexPageMixin from "@/mixins/indexPage";
export default {
    name: "purchaseFollow-related-personnel",
    mixins: [indexPageMixin],
    components: {
        empSelector
    },
    props: {
        id: {
            type: String,
            default: ""
        },
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.getList();
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    created() {
    },
    data() {
        return {
            // 创建/编辑/详情
            dialogStatus: '',
            dialogFormVisible: false,
            selectId: '',


            // 关联人员
            dialogRelationVisible: false,
            pers: [],
            EditEmployeeLoading: false,

            listLoading: false,
            layoutMode: 'simple',
            tabColumns: [
                {attr: {prop: "Name",label: "姓名", showOverflowTooltip: true}},
                {attr: {prop: "Number",label: "工号", showOverflowTooltip: true}},
                {attr: {prop: "DepartmentName",label: "部门", showOverflowTooltip: true},slot: true},
            ],
            listQuery: {
            },
            tabDatas: [], //原始数据
            total: 0,
            multipleSelection: []
        };
    },
    methods: {
        rowSelectionChanged(rows) {
            console.log(rows)
            this.multipleSelection = rows;
        },
        handleBeforeConfirm(users){
            console.log('handleBeforeConfirm', users)
            return true
        },
        /**人员变更 */
        handleChangeUsers(users) {
            console.log('handleChangeUsers', users)
            let self = this;
            self.EditEmployeeLoading = true;
            businessRoleApi.EditEmployee({
                BusinessId: self.id,
                EmployeeIdList: users.map(s=>s.EmployeeId)
            }).then(res => {
                self.EditEmployeeLoading = false
                self.$notify({
                    title: "成功",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                self.getList();
                self.$emit("reload");
            })
            .catch(err => {
                self.EditEmployeeLoading = false
            });
        },
        getList() {
            let self = this;
            self.listLoading = true;
            businessRoleApi.detail({id: self.id}).then(res => {
                self.listLoading = false;
                self.tabDatas = res.EmployeeList || [];
            })
            .catch(err => {
                self.listLoading = false;
            });
        },
        batchDel(){
            if (this.multipleSelection.length>0) {
                this.handleDel(this.multipleSelection)
            }else{
                this.$message({
                    message: "至少选择一条",
                    type: "error"
                });
            }
        },
        handleDel(rows){
            let self=this,ids = [];
            if (_.isArray(rows)) {
                ids = rows.map(u => u.EmployeeId);
            } else {
                ids.push(rows.EmployeeId);
            }
            self.$confirm("确定要删除吗?", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                businessRoleApi.DeleteEmployee({
                    BusinessId: self.id,
                    EmployeeIdList: ids
                }).then(() => {
                    self.$notify({
                        title: "成功",
                        message: "删除成功",
                        type: "success",
                        duration: 2000
                    });
                    self.getList();
                    self.$emit("reload");
                });
            });
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },
    }
};
</script>

<style lang="scss" scoped>
</style>
