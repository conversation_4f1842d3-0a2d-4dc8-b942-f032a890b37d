<!--试验证书编辑-->
<template>
    <div>
        <!--组件内容区-->
        <app-dialog :title="pageTitle" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="400">
            {{ dialogStatus }}
            <template slot="body">
                <el-form
                    :rules="formRules"
                    ref="formRef"
                    :model="formModel"
                    label-position="right"
                    label-width="120px"
                    style="padding-right:20px"
                >
                    <el-form-item label="产品类型名称" prop="Name">
                        <el-input :disabled="!editable" maxlength="30" type="text" v-model="formModel.Name"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template slot="footer">
                <app-button :buttonType="2" @click="handleClose"></app-button>
                <app-button :buttonType="1" :loading="buttonLoading" @click="handleButtonClick"></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<!--组件脚本区-->
<script>
/**引用区 */
import * as classify from '@/api/classify'

export default {
    /**名称 */
    name: "test-certificate-classify-create",
    /**组件声明 */
    components: {},
    /**参数区 */
    props: {
        dialogStatus: { //create、update、detail
            type: String
        },
        node: {
            type: Object,
            required: true
        }
    },
    /**数据区 */
    data() {
        return {
            loading:false,
            buttonLoading: false,

            /**表单模型 */
            formModel: {
                Id: null,
                Name: "",
                Level: 0,
                BusinessType: 8
            },
            /**表单规则 */
            formRules: {
                Name: { fieldName: "产品类型名称", rules: [{ required: true }] },
            }
        };
    },
    /**计算属性---响应式依赖 */
    computed: {
        pageTitle() {
            if(this.dialogStatus == 'create') {
                return '创建产品类型'
            }else if(this.dialogStatus == 'update') {
                return '编辑产品类型'
            }else if(this.dialogStatus == 'detail') {
                return '产品类型详情'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail"
        },
    },
    /**监听 */
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                let self = this;
                self.formModel = self.$options.data().formModel
                if(val && self.dialogStatus != 'create' && self.node) {
                    // let temp = {
                    //     Id: "",
                    //     Level: self.node.Level ? self.node.Level + 1 : 1,
                    //     ParentId: self.node.Id,
                    //     Name: ""
                    // };
                    self.formModel = Object.assign({}, self.formModel, self.node);
                }
            },
            immediate: true
        }
    },
    /**渲染前 */
    created() {
        this.formRules = this.initRules(this.formRules);
    },
    /**渲染后 */
    mounted() {},
    /**方法区 */
    methods: {
        /**提交方法 */
        handleButtonClick() {
            let self = this;
            self.$refs.formRef.validate(valid => {
                if (valid) {
                    let result = null;
                    self.buttonLoading = true;

                    if(this.dialogStatus == 'create') {
                        result = classify.add(this.formModel)
                    }else if(this.dialogStatus == 'update') {
                        result = classify.edit(this.formModel)
                    }

                    result.then(response => {
                        self.buttonLoading = false;
                        self.$emit('saveSuccess');
                    }).catch(err => {
                        self.buttonLoading = false;
                    });
                }
            });
        },
        getDetail() {
            // this.loading = true
            // classify.detail({id: this.id}).then(response => {
            //     this.loading = false
            //     this.formModel = Object.assign({}, this.formModel, response)
            // }).catch(err => {
            //     this.loading = false
            // })
        },
        /**关闭 */
        handleClose() {
            this.$refs.appDialogRef.handleClose();
        },
    }
};
</script>

<!--组件样式区-->
<style lang="scss" scoped>
.el-tag{
    cursor: pointer;
    +.el-tag{margin-left: 15px;}
}
.contentBox{
    display: flex;
    &_left{
        flex: 1;
        padding-right: 10px;
    }
    &_right{
        width: 34%;
        border-left: 1px solid #eee;
        padding: 13px 5px;
    }
}
</style>
