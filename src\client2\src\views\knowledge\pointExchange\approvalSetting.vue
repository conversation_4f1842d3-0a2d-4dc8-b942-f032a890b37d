<template>
  <div>
    <app-dialog title="兑换审批设置" ref="appDialogRef" v-bind="$attrs" v-on="$listeners" :width="1008">
      <template slot="body">
        <el-form
          :rules="rules"
          ref="formData"
          :model="formData"
          label-position="right"
          label-width="100px"
        >
          <div style="height: 260px;" v-loading='loading'>
            <el-form
                :rules="rules"
                ref="formData"
                :model="formData"
                label-position="right"
                label-width="110px"
            >
                <el-form-item label="审批方式" prop="ApprovalSet">
                    <el-radio-group v-model="formData.ApprovalSet">
                        <el-radio :label="ps.label" v-for="(ps,psI) in processSet" :key="psI">{{ps.value}}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div v-if="formData.ApprovalSet != 1">
                <!-- <div class="panel-title">审批</div> -->
                <div>
                    <approval-panel ref="approvalPanel" :approvalPanelObj='formData.Approval'></approval-panel>
                </div>
            </div>
          </div>
        </el-form>
      </template>

      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType="2"></app-button>
        <!-- 确认 -->
        <app-button @click="createData" :buttonType="1" :disabled="disabledBtn"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
// import empSelector from "../../common/empSelector";
import approvalPanel from '../../projectDev/projectMgmt/common/approvalPanel'
import * as prod from "@/api/projectDev/productMgmt";
import * as pointExchange from "@/api/knowledge/pointExchange";
import { vars } from '../../workbench/myWorkbench/vars'

export default {
  name: "",
  directives: {},
  components: {
    // empSelector,
    approvalPanel,

  },
  mixins: [],
  computed: {
    //不等于详情页面可编辑
    // editable() {
    //   return this.dialogStatus != "detail";
    // },
  },
  props: {
    //编辑还是新增(create: 新增; update: 编辑; detail：详情)
    // dialogStatus: {
    //   required: true,
    //   type: String
    // },
    // id: {
    //   type: String,
    //   default: ""
    // }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.resetFormData();
        this.getDetail();
      }
    }
  },
  created() {
    this.rules = this.initRules(this.rules);
  },
  data() {
    return {
      disabledBtn: false,
      loading: false,
      rules: {
        
      },
      processSet:vars.processSet,
      formData: {
        ApprovalSet:1,
        Approval: {//审批信息
            ApprovalEmployeeList: [[]],
            ApprovalType: 1,
            ApprovalOperatorEmployeeList: [], //已审批人员
            NoApprovalEmployeeList: [], //未审批人员
            CCEmployeeList: [], //抄送人
            ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
            ApprovalState: 1, //1: 进行中; 2: 已完成
            ApprovalResult: 1, //1: 通过； 2：不通过
        },
      }
    };
  },
  methods: {
    resetFormData() {
      this.formData = {
        ApprovalSet:1,
        Approval: {//审批信息
            ApprovalEmployeeList: [[]],
            ApprovalType: 1,
            ApprovalOperatorEmployeeList: [], //已审批人员
            NoApprovalEmployeeList: [], //未审批人员
            CCEmployeeList: [], //抄送人
            ApprovalNodeList: [], //审批结果（审批结束，审批人员审批详情）
            ApprovalState: 1, //1: 进行中; 2: 已完成
            ApprovalResult: 1, //1: 通过； 2：不通过
        },
      }
    },
    createData() {
        this.disabledBtn=true;
        let validate = this.$refs.formData.validate()
        let approvalPanelValidate = new Promise((resolve, reject) => { resolve(true) });
        if(this.formData.ApprovalSet == 2){
            approvalPanelValidate = this.$refs.approvalPanel.validate()
        }

        Promise.all([validate, approvalPanelValidate]).then(valid => {
            let postDatas = JSON.parse(JSON.stringify(this.formData))

            if(postDatas.ApprovalSet == 2){ //预设
                postDatas.Approval = this.$refs.approvalPanel.getData() //审批层区块
                postDatas.Approval.ApprovalEmployeeIdList = postDatas.Approval.ApprovalEmployeeList.map(s => s.map(e => e.EmployeeId))
                postDatas.Approval.CCEmployeeIdList = postDatas.Approval.CCEmployeeList.map(s => s.EmployeeId)
            }else{ //自选
                postDatas.Approval.ApprovalEmployeeIdList = [[]]
                postDatas.Approval.CCEmployeeIdList = []
            }

            delete postDatas.Approval.ApprovalEmployeeList
            delete postDatas.Approval.CCEmployeeList

            pointExchange.setApprovalPrestore(postDatas).then(res => {
                this.$notify({
                    title: "提示",
                    message: "保存成功",
                    type: "success",
                    duration: 2000
                });
                this.$emit('saveSuccess');
                this.disabledBtn=false;
            }).catch(err => {
                this.disabledBtn=false;
            })
            
        }).catch(e => {
            this.disabledBtn=false;
        })
    },
    getDetail() {
      this.loading = true
      pointExchange.getApprovalPrestore({}).then(res => {
        this.loading = false
        if(res) {
            this.formData = Object.assign({}, this.formData, res);
        }
      }).catch(err => {
        this.loading = false
      });
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    }
  }
};
</script>

<style lang='scss' scoped>

</style>