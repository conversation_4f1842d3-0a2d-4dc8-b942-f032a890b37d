<template>
    <div class="step-wrapper">
        <div class="step-main">
            <el-form ref="formData" :model="formData" :rules="rules" label-position="right" label-width="100px">
                <div>
                    <!-- <el-row>
                        <el-col :span="24">
                            <el-form-item label="整体评价" prop="OverallEvaluation">
                                <el-input :disabled='!editable' :rows='4' maxlength="500" type="textarea" v-model="formData.OverallEvaluation"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="相关附件" prop="OverallEvaluationAttachmentList">
                                <template v-if="!editable && formData.OverallEvaluationAttachmentList.length == 0">
                                    无
                                </template>
                                <template v-else>
                                    <app-uploader
                                        :readonly="!editable"
                                        accept='all'
                                        :fileType='3'
                                        :max='10000'
                                        :value='formData.OverallEvaluationAttachmentList'
                                        :fileSize='1024 * 1024 * 500'
                                        :minFileSize='100 * 1024'
                                        @change='handleFilesUpChange'
                                    ></app-uploader>
                                </template>
                            </el-form-item>
                        </el-col>
                    </el-row> -->
                    <div style="display: flex; padding: 10px; border-bottom: 1px solid #DCDFE6;">
                        <span style="color: #606266; font-weight: 900; margin-right: 20px;">考核人员列表</span>
                        <span style="color: #c1c1c1;">
                              <el-popover
                                placement="bottom-start"
                                title="推荐评定"
                                width="280"
                                trigger="hover"
                                >
                                <div>
                                    <div class="row-wrapper" v-for="(t, idx) in types" :key="idx">
                                        <div class="popo-label">{{ t.label }}</div>
                                        <div class="popo-desc">{{ t.desc }}</div>
                                        <div class="popo-prop">
                                            <span v-show="t.proportion">比例：</span>
                                        </div>
                                        <div class="popo-prop-val">{{ t.proportion }}</div>
                                    </div>
                                </div>
                                <span slot="reference">
                                    <span style="display: flex; align-items: center;">
                                        <svg-icon icon-class="question"></svg-icon>推荐评定
                                    </span>
                                </span>
                            </el-popover>
                        </span>
                        <span style="flex: 1;"></span>
                        <span style="color: #409EFF;">已评：{{ evaluated }}人</span>
                        <span style="color: #f56c6c; margin-left: 20px;">未评：{{ evaluate }}人</span>
                    </div>
                    <el-table fit class="orderList" :data="formData.PersonalList" style="width: 100%" v-loading='listLoading'>
                        <el-table-column label="考核对象" width="150px">
                            <template slot-scope="scope">
                                <el-form-item :prop="'PersonalList.' + scope.$index + '.SelfRatingEmployee'">
                                    <span v-if="scope.row.SelfRatingEmployee">
                                        {{ scope.row.SelfRatingEmployee.Name }}
                                    </span>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="自评" width="120px">
                            <template slot-scope="scope">
                                <el-form-item :prop="'PersonalList.' + scope.$index + '.SelfRatingEvaluate'">
                                    <span v-if="scope.row.SelfRatingEvaluate">
                                        {{ scope.row.SelfRatingEvaluate }}
                                    </span>
                                    <span v-else>
                                        无
                                    </span>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="集体评议" width="120px">
                            <template slot-scope="scope">
                                <el-form-item :prop="'PersonalList.' + scope.$index + '.TeamEvaluate'">
                                    <el-select v-if="editable" v-model="scope.row.TeamEvaluate" placeholder="评定">
                                        <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <span v-else>
                                        {{ scope.row.TeamEvaluate }}
                                    </span>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="评定说明">
                            <template slot-scope="scope">
                                <el-form-item v-if="editable" :prop="'PersonalList.' + scope.$index + '.TeamExplain'">
                                    <el-input type='textarea' rows="2" maxlength="500" v-model="scope.row.TeamExplain" clearable></el-input>
                                </el-form-item>
                                <span v-else>
                                    {{ scope.row.TeamExplain }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="自评详情" width="80px">
                            <template slot-scope="scope">
                                <app-table-row-button v-if="scope.row.SelfRatingEvaluate" @click="handleShowDetail(scope.row)" :type="2"></app-table-row-button>
                                <!-- <app-table-row-button v-if="scope.row.SelfRatingEvaluate" @click="handleShowDetail(scope.row)" :type="2" text='重新评定'></app-table-row-button> -->
                            </template>
                        </el-table-column>
                    </el-table>


                    <!-- <app-table-core ref="mainTable" 
                        :tab-columns="tabColumns" 
                        :tab-datas="formData.PersonalList" 
                        :tab-auth-columns="tabAuthColumns" 
                        :isShowAllColumn="true" 
                        :loading="listLoading" 
                        :isShowOpatColumn="true" 
                        :startOfTable="startOfTable"
                        :multable='false'
                        >

                        <template slot="SelfRatingEmployee" slot-scope="scope">
                            <span v-if="scope.row.SelfRatingEmployee">{{ scope.row.SelfRatingEmployee.Name }}</span>
                        </template>


                        <template slot-scope="scope">
                            <app-table-row-button @click="handleShowDetail(scope.row)" :type="2"></app-table-row-button>
                        </template>
                    </app-table-core> -->
                </div>
            </el-form>
        </div>
        <div class="btn-wrapper" v-if="editable">
            <!-- <el-button type="primary" style="width: 180px;" :loading="loading" :disabled='loading' @click="handleSave">完成考核评估，下一步</el-button> -->
            <el-button type="primary" style="width: 100px;" :loading="loading" :disabled='loading' @click="handleSave">保存</el-button>
        </div>
    </div>
</template>


<script>
import indexPageMixin from "@/mixins/indexPage";
import empSelector from '@/views/common/empSelector'
// import { appraisePromiseStatusEnum } from "../enum"
import * as ach from "@/api/personnelManagement/achievementMgmt"
import * as myAch from "@/api/myAchievements"

export default {
    name: "step4",
    mixins: [indexPageMixin],
    components: {
        empSelector,
    },
    computed: {
        editable() {
            if(this.isFollow && this.detail && this.progressStatus >= 4 && this.progressStatus < 6) {
                return true
            }
            return false
        },
        evaluated() {
            return this.formData.PersonalList.filter(s => s.TeamEvaluate).length
        },
        evaluate() {
            return this.formData.PersonalList.filter(s => !s.TeamEvaluate).length
        }
    },
    props: {
        //跟进 才 可编辑；详细不可以
        isFollow: {
            type: Boolean,
            default: false
        },
        detail: {
            type: Object,
            required: true
        },
        progressStatus: {
            type: Number,
            required: true
        },
        // isStart: {
        //     type: Boolean,
        //     default: false
        // }
    },
    watch: {
        detail: {
            handler(val) {
                this.initDatas()
                this.getList()
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        this.rules = this.initRules(this.rules);
        
    },
    mounted() {
    },
    filters: {
        // appraisePromiseStatusFilter(val) {
        //     let obj = appraisePromiseStatusEnum.find(s => s.value == val)
        //     if(obj) {
        //         return obj.label
        //     }
        //     return ''
        // },
    },
    data() {
        return {
            types: [
                {value: 'A', label: 'A', desc: '杰出贡献', proportion: '5-10%'},
                {value: 'B+', label: 'B+', desc: '高于平均贡献', proportion: '75-85%'},
                {value: 'B', label: 'B', desc: '扎实贡献', proportion: ''},
                {value: 'C', label: 'C', desc: '较低贡献', proportion: '5-10%'},
                {value: 'D', label: 'D', desc: '不满意', proportion: '5%'},
            ],
            loading: false,
            listLoading: false,
            tabDatas: [],
            formData: {
                Id: '',
                // OverallEvaluation: '',
                // OverallEvaluationAttachmentList: [],
                PersonalList: [],
            },
            // tabColumns: [{
            //         attr: {
            //             prop: "SelfRatingEmployee",
            //             label: "考核对象",
            //         },
            //         slot: true,
            //     },
            //     {
            //         attr: {
            //             prop: "SelfRatingEvaluate",
            //             label: "自评",
            //         },
            //         slot: true,
            //     },
            //     {
            //         attr: {
            //             prop: "TeamEvaluate",
            //             label: "集体评议",
            //         },
            //         slot: true,
            //     },
            //     {
            //         attr: {
            //             prop: "TeamExplain",
            //             label: "集体评议说明",
            //         },
            //         slot: true,
            //     },
            // ],
            rules: {
                // OverallEvaluation: { fieldName: "整体评价", rules: [{ required: true, max: 100 }]}
            }
        };
    },
    methods: {
        handleSave() {
            this.$refs.formData.validate(valid => {
                if(valid) {
                    // if(this.formData.PersonalList.some(s => !s.TeamEvaluate)) { 
                    //     this.$confirm(`检测到有未评人员是否继续公示结果？`, '提示', {
                    //         confirmButtonText: '确定',
                    //         cancelButtonText: '取消',
                    //         type: 'warning'
                    //     }).then(() => {
                    //         this.saveData()
                    //     })
                    // }else{
                    //     this.$confirm(`完成评估即将公示结果，是否继续？`, '提示', {
                    //         confirmButtonText: '确定',
                    //         cancelButtonText: '取消',
                    //         type: 'warning'
                    //     }).then(() => {
                    //         this.saveData()
                    //     })
                    // }
                    this.saveData()
                }
            });
        },
        saveData() {
            this.loading = true
            let postData = JSON.parse(JSON.stringify(this.formData))
            // postData.OverallEvaluationAttachmentIdList = postData.OverallEvaluationAttachmentList.map(s => s.Id)
            // delete postData.OverallEvaluationAttachmentList
            
            ach.completeAndPublicResult(postData).then(res => {
                this.loading = false

                ach.detailPlan({Id: this.detail.Id}).then(res => {
                    //需要同步“附件”、“整体评价”属性、第四步操作完成的日期（需要通过详细接口获取）
                    this.$emit('forwardSuccess', {
                        // OverallEvaluationAttachmentList: this.formData.OverallEvaluationAttachmentList, 
                        // OverallEvaluation: this.formData.OverallEvaluation,
                        AutoEndTime: res.AutoEndTime
                    })
                    this.$emit('reload')
                    this.$notify({
                        title: '成功',
                        message: '保存成功',
                        type: 'success',
                        duration: 2000
                    })
                }).catch(err => {
                    this.loading = false
                })
            }).catch(err => {
                this.loading = false
            })
        },
        getList() {
            let postData = {
                PageSize: 10000,
                PageIndex: 1,
                AppraisePlanId: this.detail.Id,
                LookStatus: 1, //只查询继续考核的员工（终止考核不显示）
            }
            this.listLoading = true
            myAch.getList(postData).then(res => {
                this.listLoading = false
                this.formData.PersonalList = res.Items.map(s => {
                    return {
                        Id: s.Id,
                        SelfRatingEmployee: s.SelfRatingEmployee,
                        SelfRatingEvaluate: s.SelfRatingEvaluate,
                        TeamEvaluate: s.TeamEvaluate,
                        TeamExplain: s.TeamExplain
                    }
                }) || []
            }).catch(err => {
                this.listLoading = false
            })
        },
        initDatas() {
            let data = JSON.parse(JSON.stringify(this.detail))
            this.formData.Id = data.Id
            // this.formData.OverallEvaluation = data.OverallEvaluation
            // this.formData.OverallEvaluationAttachmentList = data.OverallEvaluationAttachmentList
        },
        handleShowDetail(row) {
            //1 表示中期；2：表示已自评
            this.$emit('showDetail', {Id: row.Id, Type: 2})
        },
        // handleFilesUpChange(files) {
        //     this.formData.OverallEvaluationAttachmentList = files
        // },
    },
};
</script>

<style scoped>
.orderList >>> .el-form-item__content {
    margin-left: 0 !important;
}

.orderList >>> .el-form-item{
    margin-bottom: 0 !important;
}
</style>


<style lang="scss" scoped>
@import "./step.css";
.row-wrapper{
    display: flex;
    .popo-label{
        width: 30px;
    }
    .popo-desc{
        width: 110px;
    }
    .popo-prop{
        width: 45px;
    }
    .popo-prop-val{
        width: 52px;
        text-align: right;
    }
    .popo-prop, .popo-prop-val{
        color: #c1c1c1;
    }
}
</style>