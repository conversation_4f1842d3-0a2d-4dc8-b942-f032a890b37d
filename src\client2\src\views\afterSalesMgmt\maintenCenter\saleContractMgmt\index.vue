<template>
<div class="app-container">
    <div class="bg-white">
        <div class="pageWrapper">
            <div class="product-list">
                <div class="treeBox">
                    <v-tree @changeNode='changeTreeNode' :isAll='true' :isSubset='false'></v-tree>
                </div>
            </div>
            <div class="content-wrapper __dynamicTabContentWrapper">
                <div class="content __dynamicTabWrapper">
                    <app-table ref="mainTable" :isShowBtnsArea='false' :tab-columns="tabColumns" :tab-datas="tabDatas" :tab-auth-columns="tabAuthColumns" :isShowAllColumn="true" :loading="listLoading" @rowSelectionChanged="rowSelectionChanged" :isShowOpatColumn="true" :startOfTable="startOfTable" :multable="false" @sortChagned="handleSortChange" :layoutMode='layoutMode'>
                    
                        <template slot="AfterContractStatus" slot-scope="scope">
                            <span :style="{color: getStatusObj(scope.row.AfterContractStatus).color}">
                                {{ getStatusObj(scope.row.AfterContractStatus).label }}
                            </span>
                        </template>
                        <template slot="AfterContractProgress" slot-scope="scope">
                            <span v-if="scope.row">
					      		<el-progress :percentage="scope.row.AfterContractProgress" :color="scope.row.AfterContractProgress > 100 ? getProgressTypeObj('>').color : scope.row.AfterContractProgress == 100 ? getProgressTypeObj('=').color : getProgressTypeObj('<').color"></el-progress>
					      	</span>
                        </template>
                        <template slot="AfterTaxAmount" slot-scope="scope">
                            {{ scope.row.AfterTaxAmount }}
                        </template>
                        <template slot="RemainingAmount" slot-scope="scope">
                            <span :style="{color: scope.row.RemainingAmount < 0 ? 'red' : ''}">
                                {{ scope.row.RemainingAmount }}
                            </span>
                        </template>
                        <template slot="ServiceCharge" slot-scope="scope">
                            {{ scope.row.ServiceCharge || '无' }}
                        </template>
                        
                        
                        <!-- 表格查询条件区域 -->
                        <template slot="conditionArea">
                            <app-table-form :label-width="'100px'" :items="tableSearchItems" @onSearch="handleFilter" @onReset="onResetSearch" :layoutMode='layoutMode'>
                                <template slot="Keywords">
                                    <el-input style="width: 100%;" 
                                        placeholder="搜索售后合同编号/名称/报修单号"
                                        @clear='getList'
                                        v-antiShake='{
                                            time: 300,
                                            callback: () => {
                                                getList()
                                            }
                                        }' 
                                        clearable 
                                        v-model="listQuery.Keywords"
                                    ></el-input>
                                </template>

                                <template slot="AfterContractStatus">
                                    <div class="month-range-wrapper">
                                        <div class="start-month">
                                            <el-select style="width: 100%;" clearable v-model="listQuery.AfterContractStatus" placeholder="">
                                                <el-option
                                                    v-for="item in status"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                >
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </template>
                                <!-- 表格批量操作区域 -->
                                <template slot="btnsArea">
                                    <permission-btn v-if="!isOut" moduleName="xxx" v-on:btn-event="onBtnClicked"></permission-btn>
                                </template>
                            </app-table-form>
                        </template>

                        

                        <!-- 表格行操作区域 -->
                        <template slot-scope="scope">
                            <app-table-row-button @click="handleReview(scope.row)" :type="2"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnOrder') && !isOut" @click="handleReviewOrder(scope.row)" text='服务单'></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnEdit') && !isOut" @click="handleReview(scope.row, 'update')" :type="1"></app-table-row-button>
                            <app-table-row-button v-if="rowBtnIsExists('btnDel') && !isOut" @click="handleTableDelete(scope.row)" :type="3"></app-table-row-button>
                        </template>
                    </app-table>
                </div>
                <pagination :total="total" :page.sync="listQuery.PageIndex" :size.sync="listQuery.PageSize" @pagination="handleCurrentChange" @size-change="handleSizeChange" />
            </div>
        </div>
    </div>

    <!-- 查看/修改 -->
    <create-page @closeDialog="closeDialog" @saveSuccess="handleSaveSuccess" v-if="dialogFormVisible" :dialogFormVisible="dialogFormVisible" :dialogStatus="dialogStatus" :id="id"></create-page>

    <order-page @closeDialog="closeOrderDialog" v-if="dialogOrderFormVisible && currentRow" :dialogFormVisible="dialogOrderFormVisible" dialogStatus="detail" :row="currentRow" @reload='getList'></order-page>
</div>
</template>

<script>
import indexPageMixin from "@/mixins/indexPage";
import * as saleContractMgmt from "@/api/maintenanceCenter/saleContractMgmt";
import createPage from "./create";
import orderPage from "./order"
import { vars } from './vars'
import vTree from '../../../afterSalesMgmt/businessMap/common/tree'

export default {
    name: "sale-contract-mgmt",
    mixins: [indexPageMixin],
    components: {
        createPage,
        orderPage,
        vTree,
    },
    props: {
        //只为控制权限
        isOut: {
            type: Boolean,
            default: false
        },
    },
    filters: {
    },
    computed: {
        fildids() {
            return this.multipleSelection.map((s) => s.Id) || [];
        },
    },
    watch: {

    },
    created() {
    },
    data() {
        return {
            status: vars.status,
            progressTypes: vars.progressTypes,
            layoutMode: 'simple',

            tableSearchItems: [{
                    prop: "Keywords",
                    label: "",
                    mainCondition: true
                },{
                    prop: "AfterContractStatus",
                    label: "服务状态"
                }
            ],

            id: "",
            dialogStatus: "create",
            dialogFormVisible: false,

            currentRow: null,

            dialogOrderStatus: 'detail',
            dialogOrderFormVisible: false,

            listLoading: false,
            tabColumns: [{
                    attr: {
                        prop: "AfterContractCode",
                        label: "售后合同编号",
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "AfterContractName",
                        label: "售后合同名称",
                        showOverflowTooltip: true
                    },
                },
                {
                    attr: {
                        prop: "AfterContractStatus",
                        label: "服务状态",
                        width: '120',
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "RegionalName",
                        label: "服务地区",
                    },
                },
                {
                    attr: {
                        prop: "AfterContractProgress",
                        label: "服务进度",
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "RemainingAmount",
                        label: "剩余额度（元）",
                        width: '150',
                        sortable: "custom",
                    },
                    slot: true,
                },
                {
                    attr: {
                        prop: "ServiceCharge",
                        label: "服务费",
                    },
                    slot: true,
                },
            ],
            listQuery: {
                RegionalId: "",
                Keywords: '',
                AfterContractStatus: null,
            },
            multipleSelection: [],
            tabDatas: [], //原始数据
            total: 0,
        };
    },
    methods: {
        getStatusObj(val) {
            return this.status.find(s => s.value == val) || {}
        },
        getProgressTypeObj(val) {
            return this.progressTypes.find(s => s.value == val) || {}
        },
        onBtnClicked: function(type) {
            switch (type) {
                //添加
                case "btnAdd":
                    this.handleDialog("create");
                break;
                default:
                break;
            }
        },
        changeTreeNode(d) {
            if (d.Id == -1) {
                this.listQuery.RegionalId = null;
            } else {
                this.listQuery.RegionalId = d.Id;
            }
            this.listQuery.PageIndex = 1
            this.getList();
        },
        handleSortChange({column, prop, order}) {
            this.sortObj = {
                prop,
                order,
            };
            this.getList();
        },

        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSaveSuccess(_formData) {
            this.getList();
            this.closeDialog();
        },
        
        //获取成员列表
        getList() {
            this.listLoading = true;
            let postData = JSON.parse(JSON.stringify(this.listQuery));
            postData = this.assignSortObj(postData);

            saleContractMgmt.getList(postData).then((res) => {
                this.listLoading = false;
                this.tabDatas = res.Items
                this.total = res.Total;
            })
            .catch((err) => {
                this.listLoading = false;
            });
        },
        handleTableDelete(rows) {
            let ids = []
            if (_.isArray(rows)) {
                ids = rows.map(u => u.Id)
            } else {
                ids.push(rows.Id)
            }

            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                saleContractMgmt.del(ids).then(() => {
                    this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                })
            })
        },
        closeOrderDialog() {
            this.dialogOrderFormVisible = false;
        },
        handleReviewOrder(row, optType = "detail") {
            this.currentRow = row;
            this.dialogOrderStatus = optType;
            this.dialogOrderFormVisible = true;
        },
        handleDialog(activeName) {
            this.dialogStatus = activeName;
            this.dialogFormVisible = true;
        },
        //弹出详情框
        handleReview(row, optType = "detail") {
            this.id = row.Id;
            this.dialogStatus = optType;
            this.dialogFormVisible = true;
        },
        onResetSearch() {
            this.listQuery.Keywords = ''
            this.listQuery.AfterContractStatus = null
            this.getList();
        },
        rowSelectionChanged(rows) {
            this.multipleSelection = rows;
        },
        handleFilter() {
            this.listQuery.PageIndex = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.listQuery.PageSize = val.size;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.PageIndex = val.page;
            this.listQuery.PageSize = val.size;
            this.getList();
        },

    },
};
</script>

<style scoped>
.pageWrapper >>> .el-progress-bar {
  margin-right: -70px;
  padding-right: 65px;
}
</style>

<style lang="scss" scoped>
.treeBox {
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;

    .elInput {
        width: 230px;
        margin-left: 10px;
    }

    .elTree {
        height: calc(100% - 38px);
        overflow: auto;
        margin-top: 10px;
        padding-bottom: 10px;
    }
}

.pageWrapper {
    display: flex;
    position: absolute;
    left: 0;
    // top: 40px;
    top: 0;
    right: 0;
    bottom: 0;

    .product-list {
        width: 250px;

        border-right: 1px solid #dcdfe6;
        // >div:first-child{
        //     display: flex;
        //     justify-content: space-between;
        //     align-items:center;
        //     padding:0 10px;
        // }
    }

    .content-wrapper {
        width: calc(100% - 200px);
        flex: 1;
        overflow-y: auto;

        .content {
            // padding: 10px;
            // padding-right: 0;

            .opt-wrapper {
                box-sizing: border-box;
                border-bottom: 1px solid #dcdfe6;
                padding-bottom: 10px;
            }
        }
    }

    .custom-tree-node {
        display: block;
        width: 100%;
        position: relative;
        box-sizing: border-box;
        padding-right: 24px;

        .node-title {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .node-btn-area {
            position: absolute;
            right: 0;
            top: 0;
            width: 23px;
            height: 16px;
        }
    }
}

.month-range-wrapper{
    display: flex;
    .start-month, .end-month{
        flex: 1
    }
    .month-separator{
        padding: 0 6px;
    }
}

</style>
