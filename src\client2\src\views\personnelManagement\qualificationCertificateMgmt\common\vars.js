export const vars = {
    // 列表中状态 
    statusTypes: [
        {   value: 1,   label: '正常', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
        {   value: 2,   label: '即将到期', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' }, // 橙色
        {   value: 3,   label: '已过期', color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)' }, // 红色
    ],
    termOfValidityTypes: [
        {   value: 1,   label: '1年有效', number: 1 },
        {   value: 2,   label: '3年有效', number: 3  },
        {   value: 3,   label: '4年有效', number: 4  },
        {   value: 4,   label: '5年有效', number: 5  },
        {   value: 5,   label: '10年有效', number: 10 },
        {   value: 6,   label: '20年有效', number: 20 },
    ],
    // 著作类型
    patentWorkTypes: [
        {   value: 1,   label: '授权发明'  },
        {   value: 2,   label: '实用新型'  },
        {   value: 3,   label: '外观设计'  },
        {   value: 4,   label: '软件著作登记' },
        {   value: 5,   label: '其他' },
    ],
}