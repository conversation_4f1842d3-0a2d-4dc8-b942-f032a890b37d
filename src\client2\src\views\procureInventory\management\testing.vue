<template>
    <div>
        <app-dialog :title="title" 
            ref="appDialogRef" 
            v-bind="$attrs" 
            v-on="$listeners"
            :width='600'
        >
            <template slot="body">
                <el-form :rules="rules" ref="formData" :model="formData" label-position="right" label-width="80px">
                    <div class="wrapper" v-loading='loading'>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="物料编码">
                                    <span>{{formData.MaterialCode}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="物料名称">
                                  <span>{{formData.MaterialName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="规格型号">
                                    <span>{{formData.Specifications}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="仓库">
                                   <span>{{formData.MaterialStockName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                            <el-col :span="12">
                                <el-form-item label="退料人">
                                    <span>{{formData.MaterialEmployeeList | nameFilter}}</span>
                                </el-form-item>
                            </el-col>
                             <el-col :span="12">
                                <el-form-item label="退料数量">
                                    <span>{{formData.MaterialReturnCount}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                         <el-row>
                             <el-col :span="24">
                                <el-form-item label="检测结果" prop="TestingResult">
                                     <el-radio
                                          :disabled='!editable'
                                          v-model="formData.TestingResult"
                                          v-for="(t, idx) in resultType"
                                          :key="idx"
                                          :label="t.value"
                                          >
                                            {{ t.label }}
                                        </el-radio>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="说明" prop="TestingExplain">
                                    <el-input type="textarea" :rows="6" maxlength="500" v-model="formData.TestingExplain"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>


                        <el-row style="margin-top:5px">
                                <el-col :span="24">
                                    <el-form-item label="附件" prop="AttachmentList">
                                <app-uploader
                                    ref="appUploaderRef"
                                    :readonly="!editable"
                                    accept="all"
                                    :fileType="3"
                                    :max="10000"
                                    :value="formData.AttachmentList"
                                    :fileSize="1024 * 1024 * 500"
                                    :minFileSize="100 * 1024"
                                    @change="handleFilesUpChange"
                                ></app-uploader>
                                    </el-form-item>
                                </el-col>
                            </el-row> 

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="备注" prop="TestingRemark">
                                    <el-input type="textarea" :rows="6" maxlength="500" v-model="formData.TestingRemark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </div>
                </el-form>

            </template>

            <template slot="footer">
        
                <!-- 取消 -->
                <app-button @click="handleClose" :buttonType='2'></app-button>
                <!-- 确认 -->
                <app-button @click="createData" :buttonType='1' :disabled='disabledBtn'></app-button>
            </template>
        </app-dialog>
    </div>
</template>

<script>

import * as materialTransferApi from "@/api/personalInventoryMgmt/materialTransfer";
import { vars } from "../common/vars";

  export default {
    name: "testing-create",
    computed: {
        title() {
            if(this.dialogStatus == 'create') {
                this.getDetail();
                return '检测'
            }else if(this.dialogStatus == 'update') {
                return '编辑检测'
            }
        },
        //不等于详情页面可编辑
        editable() {
            //详情和审批模式都不可编辑
            return this.dialogStatus != "detail";
        },
    },
    props: {
        dialogStatus: {
            //create、update、detail
            type: String
        },
        id: {
            type: String,
            default: ""
        }
    },
    watch: {
        "$attrs.dialogFormVisible": {
            handler(val) {
                if (val) {
                    this.resetFormData();
                }
            },
        }

    },
    filters: {
     
    },
    created() {
        this.rules = this.initRules(this.rules)
    },
    data() {
        return {
            ReturnedNum:0,
            disabledBtn: false,
            rules: {
                AttachmentList: { fieldName: "附件", rules: [{ required: true }] },
                TestingResult: { fieldName: "检测结果", rules: [{ required: true }] },
                TestingExplain: { fieldName: "说明", rules: [{ required: true }] },
            },
            loading: false,
            resultType:vars.resultType,
            formData: {
               AttachmentList: [],
               TestingResult:2,
               TestingRemark:'',
               TestingExplain:'',
            },
        };
    },
    filters:{
        nameFilter(list){
        if(list && list.length > 0){
            let result =  list.map(v => v.Name)   
            return result.toString()
        }
        return "无";     
    },
    },
    methods: {
        resetFormData() {
            this.formData = {
            
            }
        },

         handleFilesUpChange(files) {
            this.$refs.formData.clearValidate('AttachmentList');
            this.formData.AttachmentList = files
        },

        getDetail(){
              materialTransferApi.returnMaterialDetails({'id':this.id})
             .then(res => {
                this.formData = Object.assign({}, this.formData, res);
                this.formData.TestingResult = 2
                this.loading = false       
              }).catch(err => {
                this.loading = false         
              });
        },
        
        createData() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData));
                    postData.AttachmentIdList = postData.AttachmentList && postData.AttachmentList.map(s => s.Id)
                    this.disabledBtn = true
                    if (this.dialogStatus == "create") {
                        materialTransferApi.testing(postData).then(res => {
                            this.disabledBtn = false
                            this.$notify({
                                title: "提示",
                                message: "保存成功",
                                type: "success",
                                duration: 2000
                            });
                            this.$refs.appDialogRef.createData()
                        }).catch(err => {
                            this.disabledBtn = false
                        });
                    }
        
                } 
            });
        },
        handleClose() {
            this.$refs.appDialogRef.handleClose()
        },   
    }
};
</script>
