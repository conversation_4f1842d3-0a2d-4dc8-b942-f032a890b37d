<template>
  <div class="specifications">
    <app-dialog
      :title="title"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width="600"
      v-loading="loading"
    >
      <template slot="body">
        <div class="temBody">
          <el-form
            :rules="rules"
            ref="formData"
            :model="formData"
            label-position="right"
            label-width="110px"
          >
            <div class="firstDiv">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="结构配件名称" prop="Name">
                    <div style="line-height:28px;">{{ formData.Name }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row>
                                <el-col :span="24">
                                    <el-form-item label="是否关键配件" prop="IsPivotaler">
                                        <div style="line-height:28px;">{{formData.IsPivotaler}}</div>
                                    </el-form-item>
                                </el-col>
                            </el-row> -->
            </div>
            <div>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="规格型号" prop="specificationModel">
                    <el-input
                      v-model.trim="formData.specificationModel"
                      :disabled="!editable"
                      maxlength="150"
                      placeholder=""
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="物资编号" prop="materialNo">
                    <el-input
                      v-model.trim="formData.materialNo"
                      :disabled="!editable"
                      maxlength="20"
                      placeholder=""
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="价格" prop="UnitPrice">
                    <el-input-number
                      v-model="formData.UnitPrice"
                      :precision="2"
                      :disabled="!editable"
                      :min="0"
                    ></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="供应商" prop="supplierId">
                    <div class="cl">
                      <el-button
                        class="fl"
                        type="primary"
                        size="mini"
                        :disabled="!editable"
                        @click="choiceSupplier()"
                      >
                        选择
                      </el-button>
                      <span class="omit fl" :title="formData.SupplierName">
                        {{ formData.SupplierName }}
                      </span>
                      <el-button
                        v-show="
                          (dialogStatus == 'create' || dialogStatus == 'edit') &&
                            formData.SupplierName
                        "
                        style="padding:4px;"
                        icon="el-icon-close"
                        circle
                        @click="closeOrder"
                      ></el-button>
                    </div>
                    <!-- <el-select v-model="formData.supplierId" :disabled="!editable" placeholder="请选择供应商">
										    <el-option
										      v-for="item in supplierList"
										      :key="item.Id"
										      :label="item.Name"
										      :value="item.Id">
										    </el-option>
										 </el-select> -->
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      :disabled="!editable"
                      type="textarea"
                      maxlength="50"
                      :autosize="{ minRows: 2, maxRows: 4 }"
                      placeholder="请输入内容"
                      v-model="formData.remark"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <div class="fDiv fl m-r-50" v-show="dialogStatus == 'create'">
          <el-checkbox v-model="goOn">继续添加</el-checkbox>
        </div>
        <el-button @click="handleClose" size="mini">取消</el-button>
        <app-button
          v-show="editable"
          @click="handleSuccess"
          :buttonType="1"
          :disabled="disabledBtn"
        ></app-button>
      </template>
    </app-dialog>
    <supplier-list
      @closeDialog="closeDialog"
      @saveSuccess="handleSaveSuccess"
      :dialogFormVisible="dialogFormSuppVisible"
      :id="supId"
    ></supplier-list>
  </div>
</template>
<script>
import supplierList from "./supplierList";
import * as accessories from "@/api/afterSalesMgmt/accessories";
import { vars } from "./vars";
export default {
  name: "specifications",
  // mixins: [indexPageMixin],
  components: {
    supplierList,
  },
  props: {
    //开始、结束操作弹框
    dialogStatus: {
      type: String,
      default: "create",
    },
    id: {
      type: String,
      default: "",
    },
    taskData: {
      type: Object,
      defult: null,
    },
  },
  data() {
    return {
      supId: "",
      dialogFormSuppVisible: false,
      goOn: false,
      disabledBtn: false,
      riskCategories: vars.riskCategories,
      loading: false,
      formData: {
        Name: "",
        UnitPrice:0,
        IsPivotaler: "",
        structPartId: "",
        specificationModel: "",
        materialNo: "",
        supplierId: "",
        SupplierName: "",
        remark: "",
      },
      rules: {
        specificationModel: [{ required: true, message: "规格型号不能为空" }],
        // supplierId: [{ required: true, message: '请选择供应商', trigger: 'change'}],
      },
      options: [],
      supplierList: [],
    };
  },
  filters: {},
  watch: {
    "$attrs.dialogFormVisible"(val) {
      if (val) {
        this.goOn = false;
        this.supId = "";
        if (this.dialogStatus == "equipmentDetail") {
          this.getDetail();
        } else {
          this.formData.Name = this.taskData.Name;
          this.formData.IsPivotaler = this.taskData.IsPivotal ? "是" : "否";
          this.formData.structPartId = this.taskData.Id; //结构配件id
          this.formData.specificationModel = "";
          this.formData.materialNo = "";
          this.formData.supplierId = "";
          this.formData.SupplierName = "";
          this.formData.remark = "";
          this.formData.UnitPrice = 0;
          if (this.dialogStatus == "detail" || this.dialogStatus == "edit") {
            this.getDetail();
          }
        }
      }
    },
  },
  computed: {
    title() {
      if (this.dialogStatus == "create") {
        return "添加规格型号";
      } else if (this.dialogStatus == "edit") {
        return "编辑规格型号";
      } else if (this.dialogStatus == "equipmentDetail") {
        return "结构配件详情";
      } else if (this.dialogStatus == "detail") {
        return "规格型号详情";
      } else {
        return "";
      }
    },
    editable() {
      return this.dialogStatus != "detail" && this.dialogStatus != "equipmentDetail";
    },
  },
  created() {},
  mounted() {},
  methods: {
    choiceSupplier() {
      this.dialogFormSuppVisible = true;
    },
    closeDialog() {
      this.dialogFormSuppVisible = false;
    },
    closeOrder() {
      this.formData.supplierId = "";
      this.formData.SupplierName = "";
      this.supId = "";
    },
    handleSaveSuccess(d) {
      this.formData.supplierId = d.Id;
      this.formData.SupplierName = d.Name;
      this.supId = d.Id;

      this.$refs.formData.clearValidate("supplierId");
      this.closeDialog();
    },
    getDetail() {
      accessories.detailPart({ id: this.id }).then(res => {
        this.formData.Name = res.Name;
        this.formData.IsPivotaler = res.IsPivotal ? "是" : "否";
        this.formData.specificationModel = res.SpecificationModel;
        this.formData.materialNo = res.MaterialNo;
        this.formData.supplierId = res.SupplierId;
        this.formData.SupplierName = res.SupplierName;
        this.formData.UnitPrice = res.UnitPrice;
        this.supId = res.SupplierId;
        this.formData.remark = res.Remark;
      });
    },
    handleSuccess() {
      if (this.dialogStatus == "detail") {
        this.handleClose();
      } else {
        this.disabledBtn = true;
        let listResult = this.$refs.formData.validate();
        Promise.all([listResult])
          .then(valid => {
            if (this.dialogStatus == "create") {
              accessories
                .addPart(this.formData)
                .then(res => {
                  this.disabledBtn = false;
                  this.$notify({
                    title: "成功",
                    message: "创建成功！",
                    type: "success",
                  });
                  if (this.goOn) {
                    this.formData.specificationModel = "";
                    this.formData.materialNo = "";
                    this.formData.supplierId = "";
                    this.formData.SupplierName = "";
                    this.formData.UnitPrice = 0;
                    this.formData.remark = "";
                    this.$refs["formData"].resetFields();
                  }
                  this.$emit("saveSuccess", this.goOn);
                })
                .catch(err => {
                  this.disabledBtn = false;
                });
            } else if (this.dialogStatus == "edit") {
              this.formData.id = this.id; //规格型号id
              accessories
                .editPart(this.formData)
                .then(res => {
                  this.disabledBtn = false;
                  this.$notify({
                    title: "成功",
                    message: "编辑成功！",
                    type: "success",
                  });
                  this.$emit("saveSuccess", false);
                })
                .catch(err => {
                  this.disabledBtn = false;
                });
            }
          })
          .catch(err => {
            this.disabledBtn = false;
          });
      }
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  },
};
</script>
<style lang="scss" scoped>
.firstDiv {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 20px;
}
.omit {
  max-width: 300px;
  margin-left: 6px;
}
</style>
