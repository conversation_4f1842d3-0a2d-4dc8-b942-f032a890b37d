<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" id="appContainer"></router-view>
      </keep-alive>
      <!-- <keep-alive v-if="cachedViews">
        <router-view :key="key"></router-view>
      </keep-alive>
      <router-view v-if="!cachedViews"></router-view>
      -->
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews

      // const cacheds = this.$store.state.tagsView.cachedViews
      // if(cacheds && cacheds.length > 0){
      //   const cachedIdx = cacheds.findIndex(i => this.$route.name == i)
      //   return cachedIdx > -1
      // }
      // return false
    },
    key() {
      return this.$route.fullPath
    }
  }
}
</script>

<style scoped>
.app-main {
    /*84 = navbar + tags-view = 50 +34 */
    /* min-height: calc(100vh - 100px); */
    position: relative;
    overflow: auto; 
    background-color: #e8eaee;
    min-height: 100%;
    height: 100%;
}
#appContainer{
  position: relative;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
</style>
