<template>
  <div>
    <listSelector
      :checkedData="listSelectorCheckedData"
      :getListUrl="listSelectorUrl"
      :multiple="listSelectorMultiple"
      :pageTitle="listSelectorTitle"
      :topMessage="listSelectorTopMessage"
      :selectKeyName="listSelectorKeyName"
      :condition="listQueryParams"
      :columnData="listSelectorColumnData"
      :dialogFormVisible="listSelectorDialogFormVisible"
      @closeDialog="listSelectorCloseDialog"
      @saveSuccess="listSelectorSaveSuccess"
      ref="listSelector"
    >
      <template slot="conditionArea">
        <app-table-form style="padding-top: 10px;" :label-width="'110px'" :items='tableSearchItems' @onSearch='handleFilter' @onReset='onResetSearch'>
          <template slot='DemandName'>
            <el-input style="width: 100%;" v-model="listQueryParams.DemandName" placeholder=""></el-input>
          </template>
          <template slot='DemandNumber'>
            <el-input style="width: 100%;" v-model="listQueryParams.DemandNumber" placeholder=""></el-input>
          </template>
          <template slot='Priority'>
            <el-select class="filter-item" style="width:100%" v-model="listQueryParams.Priority" placeholder="" clearable>
                <el-option v-for="item in demandPriorities" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </template>
        </app-table-form>
      </template>

      <template slot="Priority" slot-scope="scope"><span>{{ scope.row.Priority | priorityFilter }}</span></template>
      <template slot="CreateEmployee" slot-scope="scope">
          <span v-if="scope.row.CreateEmployee">{{ scope.row.CreateEmployee.Name }}</span>
      </template>
      <template slot="CreateTime" slot-scope="scope">{{ scope.row.CreateTime | dateFilter('YYYY-MM-DD HH:mm:ss') }}</template>
    </listSelector>
  </div>
</template>

<script>
import listSelector from "./listSelector";
import { serviceArea } from "@/api/serviceArea";
import { vars } from "../projectDev/common/vars";

export default {
  name: "demand-selector",
  components: {
    listSelector,
  },
  filters: {
    nameFilter(creator) {
      if (creator) {
        return creator.Name;
      }
      return "";
    },
    priorityFilter(val) {
      const obj = vars.demandPriorities.find(
        (s) => s.value == val
      );
      if (obj) {
        return obj.label;
      }
      return "";        
    },
    // receivedPayment(status) {
    //   const statusObj = vars.orderMgmt.remittanceStatus.find(
    //     (s) => s.value == status
    //   );
    //   if (statusObj) {
    //     return statusObj.label;
    //   }
    //   return "";
    // },
    // stateFilter(status) {
    //   const statusObj = vars.orderMgmt.approvalStatus.find(
    //     (s) => s.value == status
    //   );
    //   if (statusObj) {
    //     return statusObj.label;
    //   }
    //   return "";
    // },
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    condition: {
      type: Object,
      default: null,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    // if(this.condition) {
    //   this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
    // }
  },
  watch: {
    
    isShow(val) {
      this.listSelectorDialogFormVisible = val;
    },
    checkedList: {
        handler(val) {
            if (val && val.length > 0) {
                this.listSelectorCheckedData = JSON.parse(JSON.stringify(val));
            }else{
                this.listSelectorCheckedData = []
            }
        },
        deep: true
    },
    condition: {
        handler(val) {
            this.listQueryParams = Object.assign({}, this.listQueryParams, this.condition)
        },
        deep: true,
        immediate: true
    }
  },
  data() {
    return {
      demandPriorities: vars.demandPriorities,
      listQueryParams: {
        DemandName: '',
        DemandNumber: '',
        Priority: null,
        // IsSelectList: true
      },
      tableSearchItems: [
        { prop: "DemandName", label: "需求名称" },
        { prop: "DemandNumber", label: "需求编号" },
        { prop: "Priority", label: "优先级" },
      ],
      listSelectorCheckedData: [],
      listSelectorUrl: serviceArea.business + "/Demand/GetListPage",
      listSelectorMultiple: this.multiple,
      listSelectorCondition: this.condition ? this.condition : {},
      listSelectorTitle: "添加关联需求",
      listSelectorTopMessage: "",
      listSelectorKeyName: "Id",
      listSelectorColumnData: [
        {
          attr: { prop: "DemandName", label: "需求名称" },
        },
        {
          attr: { prop: "DemandNumber", label: "需求编号" },
        },
        {
          attr: { prop: "Priority", label: "优先级" },
          slot: true,
        },
        {
          attr: { prop: "CreateEmployee", label: "创建人" },
          slot: true,
        },
        {
          attr: { prop: "CreateTime", label: "创建时间" },
          slot: true,
        }
      ],
      listSelectorDialogFormVisible: false,
    };
  },
  methods: {
    handleFilter() {
      this.$refs.listSelector.getList()
    },
    onResetSearch() {
      // this.listQueryParams.PageIndex = 1
      this.listQueryParams.DemandName = ''
      this.listQueryParams.DemandNumber = ''
      this.listQueryParams.Priority = null
      // this.listQuery.EmployeeId = ''
      // this.listQuery.Employee = []
      this.handleFilter()
    },
    listSelectorCloseDialog() {
      this.onResetSearch()
      this.listSelectorDialogFormVisible = false;
      this.$emit("closed", this.listSelectorDialogFormVisible);
    },
    listSelectorSaveSuccess(data) {
      let list =
        data.map((s) => {
          s.Code = s.OrderNumber;
          return s;
        }) || [];
      this.$emit("changed", JSON.parse(JSON.stringify(list)));
      this.listSelectorCloseDialog();
    },
    // handleChangeUsers(users) {
    //   if (users && users.length > 0) {
    //     this.listQuery.Employee = [users[0]];
    //     this.listQuery.EmployeeId = users[0].EmployeeId;
    //   } else {
    //     this.listQuery.Employee = [];
    //     this.listQuery.EmployeeId = '';
    //   }
    // },
  },
};
</script>
